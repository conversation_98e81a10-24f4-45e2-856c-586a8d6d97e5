{"Request": {"Number": "861360", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 437, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004868602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=26CB280EA9420B6DDE6E3CB3B6A8B2EF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "861360"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.07.2005"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "861360 - BP_TR2: Consistency check in RFTBUP10_BP000"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You execute the business partner conversion of the phase II.<br />Before you execute the report RFTBUP10_BP000, you switch off the consistency check (check of the unique assignment of a SAP business partner for several property business partners) on the selection screen.<br />After you execute the report, the entries are no longer in the table or the entries are incomplete.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FTBU, consistency check, business partner, conversion, UPART02</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You switched off the consistency check on the selection screen. You did not carry out a manual check for the BP000 table with Transaction SE16.<br />However, there are inconsistencies in the BP000 table (for example, there are several entries for which the BP000-PARTNER field is empty).<br />If an error occurs, data in the BP000 table can be lost.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the correction specified below using the Note Assistant or import the Support Package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5029681"}, {"Key": "Processor                                                                                           ", "Value": "D027661"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "858123", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Table BP000 is deleted by RFTBUP10_BP000", "RefUrl": "/notes/858123"}, {"RefNumber": "762061", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Deactivate consistency check for table BP000", "RefUrl": "/notes/762061"}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}, {"RefNumber": "858123", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Table BP000 is deleted by RFTBUP10_BP000", "RefUrl": "/notes/858123 "}, {"RefNumber": "762061", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Deactivate consistency check for table BP000", "RefUrl": "/notes/762061 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "463_20", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 110", "SupportPackage": "SAPKGPFA21", "URL": "/supportpackage/SAPKGPFA21"}, {"SoftwareComponentVersion": "EA-FINSERV 200", "SupportPackage": "SAPKGPFB10", "URL": "/supportpackage/SAPKGPFB10"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ28", "URL": "/supportpackage/SAPKIPBJ28"}, {"SoftwareComponentVersion": "EA-FINSERV 500", "SupportPackage": "SAPKGPFC10", "URL": "/supportpackage/SAPKGPFC10"}, {"SoftwareComponentVersion": "EA-FINSERV 600", "SupportPackage": "SAPKGPFD01", "URL": "/supportpackage/SAPKGPFD01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 4, "URL": "/corrins/**********/201"}, {"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 1, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "516621 ", "URL": "/notes/516621 ", "Title": "SAP BP conversion phase II: Report RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "858123 ", "URL": "/notes/858123 ", "Title": "BP_TR2: Table BP000 is deleted by RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "516621 ", "URL": "/notes/516621 ", "Title": "SAP BP conversion phase II: Report RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "858123 ", "URL": "/notes/858123 ", "Title": "BP_TR2: Table BP000 is deleted by RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "858123 ", "URL": "/notes/858123 ", "Title": "BP_TR2: Table BP000 is deleted by RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "858123 ", "URL": "/notes/858123 ", "Title": "BP_TR2: Table BP000 is deleted by RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "600", "ValidTo": "600", "Number": "858123 ", "URL": "/notes/858123 ", "Title": "BP_TR2: Table BP000 is deleted by RFTBUP10_BP000", "Component": "FS-BP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}