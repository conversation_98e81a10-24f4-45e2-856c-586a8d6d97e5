{"Request": {"Number": "826357", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 243, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015863282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=AC669C99D6A8469F09B76260F146039D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "826357"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 55}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "EC-PCA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Profit Center Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Enterprise Controlling", "value": "EC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Profit Center Accounting", "value": "EC-PCA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-PCA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "826357 - Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to know</p>\r\n<ul>\r\n<ul>\r\n<li>how Profit Center Accounting is mapped in General Ledger Accounting (new),</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>to what extent classic Profit Center Accounting can be used in parallel when you use General Ledger Accounting (new)</li>\r\n</ul>\r\n</ul>\r\n<p><br />For information about Profit Center Accounting in the universal journal in SAP S/4HANA, on-premise edition, see SAP Note 2425255.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>General Ledger Accounting (new) FI-GL (new) profit center PCA document splitting document split T8A30 SAPF180 SAPF180A SAPF100 RCOPCA49 IAOM 028 KECRMPCA 001</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want more information about using Profit Center Accounting.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For Release mySAP ERP, the Profit Center Accounting was integrated into General Ledger Accounting (new). The solution is as follows:</p>\r\n<ul>\r\n<ul>\r\n<li>SAP delivers the 'Profit Center' and the 'Partner Profit Center' as fixed characteristics that are posted on the original FI postings. It is no longer necessary to update the data in another ledger as in classic Profit Center Accounting.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>As a result of the integration of the Profit Center Accounting into General Ledger Accounting (new), new functions are available, such as the one for document splitting. Using the 'Document Splitting' function (online document split), you can create balance sheets for company codes as well as for other entities such as the profit center. The balance is then set to 0 for each document for the profit center.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Integrating classic General Ledger Accounting and classic Profit Center Accounting into the one application also removes the time and effort required.</li>\r\n</ul>\r\n</ul>\r\n<p><br />We recommend the following to all new customers: For the introduction of General Ledger Accounting (new) in Release SAP ERP, map Profit Center Accounting by activating the scenario FIN_PCA (profit center update) in General Ledger Accounting (new). The parallel activation of classic Profit Center Accounting in addition to updating parallel data volumes does not make sens, for reasons that will be discussed in the following section.<br /><br />Detailed information about configuring Profit Center Accounting in General Ledger Accounting (new):</p>\r\n<ul>\r\n<ul>\r\n<li>Define the update of the characteristics 'Profit Center' and 'Partner Profit Center' in the ledger by selecting the scenario 'Profit center update' (Customizing: Financial Accounting (New) -&gt; Financial Accounting Basic Settings (New) -&gt; Ledgers -&gt; Ledger -&gt; Assign Scenarios and Customer-Defined Fields to Ledgers).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you want to use the document splitting, you can define the 'Profit center' field as a splitting characteristic in the document splitting (Customizing: Financial Accounting (New) -&gt; General Ledger Accounting (New) -&gt; Business Transactions -&gt; Document Splitting -&gt; Define Document Splitting Characteristics for General Ledger Accounting). Set the 'Zero balance' indicator again for the added field 'Profit Center'. You can now create balance sheets on the profit center. You must also activate the Mandatory Field check to ensure that the profit center is set in all postings. If you want to display balance sheet items at profit center level (for example, receivables and payables) but you do not require complete balance sheets, we recommend that you do not set the 'Zero balance' and 'Mandatory Field check' indicators.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br />If you already used classic Profit Center Accounting as an SAP R/3 customer but now want to use Profit Center Accounting in General Ledger Accounting (new), you can continue to use classic Profit Center Accounting for a transition period parallel to the profit center update scenario  in General Ledger Accounting (new). However, we do not recommend you do this on a long-term basis due to the increased data volume and the increased time and effort required, and in the case of active document splitting, the update response changes.<br />If classic Profit Center Accounting should continue to play a significant role in your system in spite of everything, we recommend that you not activate the document splitting in General Ledger Accounting (new), not even for other entities like the segment. A reason for this is that classic Profit Center Accounting uses certain functions of the classic general ledger that may no longer be available with active document splitting (for example, calculating the balance sheet adjustment; see SAP Note 981775). Another reason is that when you use transactions such as 3KEH, FAGL3KEH or FI substitution to set proposal profit centers, this prevents the setting of profit centers due to document splitting (for example, when reading a profit center from the source document or reference document).<br /><br />You can find details for deviating functions between Profit Center Accounting in General Ledger Accounting (new) and in the classic PCA and details about the effects of General Ledger Accounting (new) on the posting behavior in classic Profit Center Accounting in the following executions. Profit Center Accounting always takes place within a controlling area, even in the case of mapping to the General Ledger Accounting (new). SAP does not support cross-controlling area PCA.</p>\r\n<p>&#x00A0;</p>\r\n<p>Overview of the following topics:</p>\r\n<p>1. Derivation of the default profit center<br />2. Derivation of the partner profit center<br />3. Displaying receivables and payables for each profit center<br />4. Periodic transfers of asset portfolios to classic Profit Center Accounting<br />5. Dummy profit center<br />&#x00A0;&#x00A0;&#x00A0; a) Dummy profit center on profit and loss accounts (P+L accounts)<br />&#x00A0;&#x00A0;&#x00A0; b) Dummy profit center on cost elements/revenue elements<br />6. PCA additional rows<br />7. Substitution of profit centers in sales orders<br />8. Reporting<br />9. Transfer prices (multiple valuations)<br />10. Creating the profit center standard hierarchy<br />11. Using the dummy profit center<br />12. Compare G/L Accounts in FI with Profit Center Accounting (Transaction KE5T)<br />13. Real-time integration of CO postings into General Ledger Accounting (new)<br />14. Migration to General Ledger Accounting (new)<br />15. CRM integration<br />16. ALE scenarios<br />17. Elimination profit center (field EPRCTR)<br />18. Origin object type (field RHOART)<br />19. Manual transfer postings<br />20. Differentiation of the data (field RRCTY - field AWTYP)<br />21. Performance<br />22. Unvaluated material processes (KSTAT = U)<br />23. Tables<br />23.1 T882</p>\r\n<p>&#x00A0;</p>\r\n<p>1. Derivation of the default profit center</p>\r\n<ul>\r\n<li>Classic Profit Center Accounting start situation:<br /><br />There are various options for setting a default profit center in the SAP system: Manual entry (if the transaction supports this), FI substitution, PCA transactions 3KEH/3KEI The derived proposal profit center is also updated in the line item table of classic general ledger accounting, but is not significant due to non-existing evaluation options. Transactions 3KEH or 3KEI have an additional meaning for classic Profit Center Accounting. They control if certain balance sheet accounts and retained earnings accounts are updated in the tables of classic Profit Center Accounting.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li>General Ledger Accounting (new) without document splitting:<br /><br />There are various options for setting a default profit center: Manual entry (if the transaction permits this), FI substitution, implementation of the BAdI AC_DOCUMENT (for postings using the accounting interface only, for example, MM, SD postings), PCA transactions 3KEH or 3KEI, the new transaction FAGL3KEH, and the BAdI FAGL_3KEH_DEFPRCTR.<br /><br />Transactions 3KEH and 3KEI also exist in mySAP ERP2004 and function in the same way as in R/3 (in other words, classic Profit Center Accounting must be active as a prerequisite). You can then use the entry in transaction 3KEH to control the update of the balance sheet and profit and loss accounts in classic Profit Center Accounting, and the transactions set a proposal profit center where required, which is returned to the posting interface. Keep in mind that the profit center information is therefore affected in General Ledger Accounting (new) by settings in classic Profit Center Accounting.<br /><br />As of Release mySAP ERP2005, transactions 3KEH and 3KEI do NOT return the derived proposal profit center to the posting interface any longer. The entries of transaction 3KEH control the transfer of line items to classic Profit Center Accounting. The profit center from transactions 3KEH and 3KEI is only relevant for the document in classic Profit Center Accounting if no profit center is delivered from the interface. This may result in a different profit center in the FI document in the PCA document.<br /><br />Therefore, we recommend using the new transaction FAGL3KEH and the BAdI FAGL_3KEH_DEFPRCTR to maintain the default profit centers or to move the derivations from 3KEH/3KEI to transaction FAGL3KEH or BAdI FAGL_3KEH_DEFPRCTR. You can use these new functions to determine a proposal profit center depending on the company code and the account. Note that this proposal profit center does not appear on the input screen; it is derived only when you post the document. The proposal profit center is used if the line item does not contain a CO account assignment and if the profit center was not already determined elsewhere.<br /><br />! Note the changes in the logic for the following SAP Notes:<br />1241741 Problems w/ 3KEH/FAGL3KEH, particularly in migration phase 1<br />2093490 FAGL3KEH: Derivation of default profit center for cost elements</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li>General Ledger Accounting (new) with document splitting:<br /><br />The options described under the \"General Ledger Accounting (new) without document splitting\" section are also available to you. However, depending on the account, the use of the function is critical and must be well considered. As these proposal profit center derivations (and also segment derivations) take place before the \"document splitting\" (online document split), document splitting is sometimes prevented. For this reason, NEVER set a proposal profit center IN ACCOUNTS in which you expect an account assignment by document splitting.  These accounts include in particular OI-managed accounts, that is customers/vendors and also OI-managed G/L accounts, for example, goods receipt accounts or invoice receipt clearing accounts (GR/IR accounts). Note that document splitting also overwrites an actual dummy profit center. Use a default profit center instead, which you create with the normal default transaction for profit centers.<br /><br />! Note the changes in the logic for the following SAP Notes:<br />1241741 Problems w/ 3KEH/FAGL3KEH, particularly in migration phase 1<br />2093490 FAGL3KEH: Derivation of default profit center for cost elements</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p>2. Derivation of the partner profit center</p>\r\n<ul>\r\n<li>Release SAP ERP 2004:<br /><br />The profit center scenario in General Ledger Accounting (new) is active, classic Profit Center Accounting is not active: Transactions 8KER/8KES are no longer available. Notes 997925 and 1087350 provide the functions from transaction OCCL. Alternatively, you can use the BAdI AC_DOCUMENT to set the partner profit center.<br />The profit center scenario in General Ledger Accounting (new) and classic PCA is active: Transactions 8KER/8KES and OCCL (reading purchase order/sales order for affiliated companies) are active.  However, we recommend that you no longer use transaction 8KER or 8KES. Partner profit centers derived using these transactions are available in both classic Profit Center Accounting and in General Ledger Accounting (new) only if the line is relevant in classic Profit Center Accounting.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li>Release SAP ERP 2005:<br /><br />The profit center scenario in General Ledger Accounting (new) is active, classic Profit Center Accounting is not active: Transactions 8KER/8KES are no longer available. Notes 997925 and 1087350 provide the functions from transaction OCCL. Alternatively, you can use the BAdI AC_DOCUMENT or the new BAdI FAGL_DEFPPRCTR (enhancement spot FAGL_LEDGER_CUST_DEFPRCTR) with the method SET_DEFAULT_PART_PRCTR to set the partner profit center.<br />The profit center scenario in General Ledger Accounting (new) and classic PCA is active: Transactions 8KER/8KES and OCCL are active. However, we recommend that you no longer use transaction 8KER or 8KES because partner profit centers derived using these transactions are available in both classic Profit Center Accounting and in General Ledger Accounting (new) only if the line is relevant in classic Profit Center Accounting. Instead, if required, you should use the BAdI FAGL_DEFPPRCTR to set the partner profit center. A partner profit center determined in this way is always updated both in General Ledger Accounting (new) and in classic Profit Center Accounting.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p>3. Displaying receivables and payables for each profit center</p>\r\n<ul>\r\n<li>Document splitting is active<br /><br />The detailed information from the general ledger view about receivables and payables split online from the document splitting is NOT available for classic Profit Center Accounting.<br />If the business area is not defined as a document splitting characteristic, you can use Note 981775 to execute the reports for balance sheet adjustment (SAPF180*). This means that you can use transaction 1KEK to transfer receivables and payables to classic Profit Center Accounting. However, the old foreign currency valuation function (transaction F.05, the report SAPF100) is no longer available if General Ledger Accounting (new) is active. As a result, transaction 1KEK copies only the original receivables/payables, independently of transaction 2KEM \"Account Valuation Differences\"; in other words, the original data is not corrected by the valuation differences.<br />The new foreign currency valuation of the OIs (the report FAGL_FC_VALUATION) can only be performed with valuation areas. However, if you have set document splitting by profit centers, the report FAGL_FC_VALUATION posts with the operational profit centers. The balance sheet adjustment account can then be transferred to the classic Profit Center Accounting using transaction 3KEH.<br />The profit and loss adjustment (report SAPF181, transaction F.50) is not possible because CO objects are adjusted that can be directly or indirectly (for example, using the profit center) adjusted by means of document splitting also. Follow-up costs split according to source can be transferred online to the classic Profit Center Accounting because these are already available in the data entry view.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li>Document splitting is not active<br /><br />In this case, you CANNOT display the receivables and payables according to source at profit center level within General Ledger Accounting (new). However, you can use the old split of the receivables and payables within the classic Profit Center Accounting (transaction F.5D) as well as of the follow-up costs (transaction F.50), and you can use the periodic transfer of receivables and payables using transaction 1KEK. However, you can execute the new report for the foreign currency valuation of the open items (report FAGL_FC_VALUATION) with depreciation areas only, which means that the documents are no longer updated (valuation difference not updated in BSEG-BDIFF). As a result, transaction 1KEK copies only the original receivables/payables, independently of transaction 2KEM 'Account Valuation Differences'; in other words, the original data is not corrected by the valuation differences.<br />You can use the standard report groups 8A98 and 8A99 to display the open receivables and payables in classic Profit Center Accounting.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p>4. Periodic transfers of asset portfolios to classic Profit Center Accounting</p>\r\n<p>As of Release 4.7, it is possible to map a parallel reporting mapped in FI (for example, parallel accounts) for parallel depreciation areas in Asset Accounting by using particular settings (defining an accounting principle). You must stop the execution of transaction 1KEI because it would result in duplicated data in PCA because of postings to the same accounts. You must also stop transaction 1KEI with a 'different company code' or a 'different depreciation area in the different company code' because the data cannot be transferred correctly. Transaction 1KEI terminates with the error message KM 764. As of Release SAP ERP, if General Ledger Accounting (new) is active, the system issues the message FAGL_LEDGER_CUST 076.</p>\r\n<p>&#x00A0;</p>\r\n<p>5. Dummy profit centers</p>\r\n<p>a) Dummy profit center on profit and loss accounts (P+L accounts)</p>\r\n<p>You use transactions 3KEH and 3KEI to firstly try to determine a proposal profit center in classic Profit Center Accounting for document line items with a P+L account (no cost element) and without a profit center account assignment. If the system does not find a proposal profit center, the dummy profit center is set for some activities (primarily from Logistics). If General Ledger Accounting (new) is active AND if at least one of the two characteristics 'Profit Center' and 'Segment' is used in the document splitting, the routine for setting the dummy profit center will no longer run (see Note 820121 and 832776). Otherwise, the document splitting would either split a document incorrectly or not at all in the case of a set dummy profit center. The system must therefore find and set the profit center that is valid for the process using the document splitting or another derivation possibility. If this is not the case, the document line item will not be updated in the classic Profit Center (document line items with initial Profit Center are not allowed in the classic Profit Center Accounting and are therefore not updated).<br /><br />! See the changes from SAP Note 1241741.</p>\r\n<p>b) Dummy profit center on cost elements/revenue elements</p>\r\n<p>If classic Profit Center Accounting is active, an actual dummy profit center is maintained (table field TKA01-DPRCT). The dummy profit center continues to be set for cost elements/revenue elements if the system recognizes a true account assignment, but the profit center is initial after the derivation of the profit center from the account assignment object.<br />Example: Function module K_COBL_CHECK &#x2013;&gt; subroutine SUBST_FROM_REAL_OBJECT</p>\r\n<p>&#x00A0;</p>\r\n<p>6. PCA additional rows</p>\r\n<p>If you map Profit Center Accounting in General Ledger Accounting (new) in mySAP ERP, you can update known PCA additional lines from classic Profit Center Accounting using consulting note 937872 in General Ledger Accounting (new) as well.</p>\r\n<p>If you use the functions of the transfer prices, you do not require SAP Note 937872. This is because the PCA additional lines are then structured in a technically true manner and are posted automatically to General Ledger Accounting (new) if the proper settings are maintained in transaction 0KEK.</p>\r\n<p>&#x00A0;</p>\r\n<p>7. Substitution of profit centers in sales orders</p>\r\n<p>Transactions 0KEL and 0KEM are available both in classic Profit Center Accounting and in General Ledger Accounting (new) (Customizing: Financial Accounting (New) -&gt; General Ledger Accounting (New) -&gt; Tools -&gt; Validation/Substitution)</p>\r\n<p>&#x00A0;</p>\r\n<p>8. Reporting</p>\r\n<ul>\r\n<li>Line item reporting in General Ledger Accounting (new)<br /><br />Release SAP ERP 2004: Even if document splitting is set with the characteristic Profit Center, only one restricted line item reporting to profit centers is available in this release at present. When you use the G/L account line item list of FI, you can limit profit centers for line item settlement G/L accounts that are not relevant for the document splitting. As of Support Package 10, line item reporting to profit centers and segments is available.<br />Release SAP ERP 2005: Line item reporting according to profit centers and segments is available.<br />&#x00A0;</li>\r\n</ul>\r\n<ul>\r\n<li>Ledger reporting in General Ledger Accounting (new)<br /><br />Release SAP ERP 2004: Even if the document splitting is set with the characteristic profit center or segment, no current account reporting to profit centers and segments is available up to Support Package 10.  With Support Package 10, current account reporting according to profit centers and segments is available. Also see the detailed explanations for Release SAP ERP 2005.<br />Release SAP ERP 2005: Current account reporting according to profit centers and segments is available. It replaces the standard report groups 8A98/8A99 in earlier releases. However, the difference is that the foreign currency valuation correction is no longer displayed for each item because no update of the valuation in items occurs through the foreign currency valuation in General Ledger Accounting (new) (no BDIFF/BDIFF2 update). It is a key date-related valuation (mostly for the period end).<br />&#x00A0;</li>\r\n</ul>\r\n<ul>\r\n<li>Report transfer from Classic Profit Center Accounting to General Ledger Accounting (new)<br /><br />Enhancement Package 3 provides the option to copy the Report Painter summary reports and Report Writer summary reports to a library in General Ledger Accounting (new). Transaction FAGL_RMIGR is provided for this purpose. See the following note about this topic:<br />1490811 FAGL_RMIGR not for line item reports.<br /><br />The following reports are also available in the menu for General Ledger Accounting (new):<br />Menu -&gt; Accounting -&gt; Financial Accounting -&gt; General Ledger -&gt; Information System -&gt; General Ledger Reports -&gt; Reports for Profit Center Accounting</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p>9. Transfer prices (multiple valuations)</p>\r\n<p>The functions for the parallel valuations (profit center valuation/transfer prices and group valuation) is available when you use General Ledger Accounting (new) in Release SAP ERP 2005. In SAP ERP 2004, you can use the functions of the transfer prices/parallel valuation only if you have activated the classic General Ledger and classic Profit Center Accounting.</p>\r\n<p><br />10. Creating the profit center standard hierarchy</p>\r\n<ul>\r\n<li>Release SAP ERP 2004: You must create the highest node of the standard hierarchy in the Customizing of the classic Profit Center Accounting (transaction 0KE5), even if you are not using classic Profit Center Accounting. </li>\r\n</ul>\r\n<ul>\r\n<li>Release SAP ERP 2005: To create the highest node of the standard hierarchy, use transaction SM30 with the maintenance view V_FAGL_PC_STHR. </li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p>11. Using the dummy profit center</p>\r\n<ul>\r\n<li>Classic Profit Center Accounting is active (regardless of whether classic General Ledger Accounting or General Ledger Accounting (new) is active):<br /><br />If the classic Profit Center Accounting is active, you must create a dummy profit center to avoid postings with an initial profit center in the database tables of the classic PCA. <br />If the General Ledger Accounting (new) is also active AND if you are using at least one of the two characteristics 'Profit Center' and 'Segment' in the document splitting, in Release mySAP ERP 2004, you have to ensure that SAP Notes 820121 and 832776 are included. In Release SAP ERP 2005, the changed posting logic is included from the beginning.  Note that the update of document line items in classic Profit Center Accounting is omitted because of this.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li>Classic Profit Center Accounting is not active, General Ledger Accounting (new) is active, and you are using at least one of the two characteristics 'Profit Center' and 'Segment' in document splitting:<br /><br />You do not have to create and use a dummy profit center.  Using the dummy profit center can cause situations you want to avoid: For example, the system splits receivables/payables to the dummy profit center because of the document splitting (you cannot transfer them manually), or a document line item with dummy profit center account assignment is not split by the document splitting.  To ensure that a profit center is assigned in all rows, set the profit center as mandatory field in the Customizing of the document splitting.  However, note that this can also lead to terminations while posting, if a profit center assignment is missing. <br />Due to Note 817587, the actual dummy profit center was overwritten by the document splitting. If you work with the document splitting and intentionally want to use dummy profit centers as profit center assignments, we recommend that you do NOT use the actual dummy profit center. Use a default profit center instead; this means you should use the normal profit center (created using transaction KE51), which is recognized as default owing to its description. The dummy profit center should only indicate postings that are incorrect (such as account assignments that are not made using standard transactions, substitutions, and so on).</li>\r\n</ul>\r\n<p>If the dummy profit center is not found, the reason may be that the function module COPCA_ACTIVE_ACT, which checks Profit Center Accounting for activity, returns the information that PCA is not active. To ensure that PCA is recognized as active, the active indicator must be set in transaction 0KE5, AND transaction 1KEF must contain a valid entry for the fiscal year. Message KM 586 can be set in Customizing.</p>\r\n<p>&#x00A0;</p>\r\n<p>12. Compare G/L Accounts in FI with Profit Center Accounting (Transaction KE5T)</p>\r\n<p>In classic Profit Center Accounting, transaction KE5T is used to compare account balances in PCA and FI. In transaction KE5T, the ledgers to be compared are fixed. If General Ledger Accounting (new) is active in your system, use the more general transaction GCAC. You can enter any base ledger and any comparison ledger.</p>\r\n<p>There is currently no replacement for transaction KE5U used in the Euro conversion. To correct minor differences due to the Euro conversion, you must enter manual postings in PCA.</p>\r\n<p>&#x00A0;</p>\r\n<p>13. Real-time integration of CO postings into General Ledger Accounting (new)</p>\r\n<p>When you use real-time integration in General Ledger Accounting (new), you cannot transfer the intercompany clearing accounts to classic Profit Center Accounting (as described in consulting note 309987).</p>\r\n<p><br />14. Migration to General Ledger Accounting (new)</p>\r\n<p>If you are in a migration, see the following notes:</p>\r\n<p>1241741 Problems with 3KEH/FAGL3KEH, in particular migration phase 1&#x00A0;&#x00A0;&#x00A0;&#x00A0; !!! This SAP note replaces, for example, Notes 1138325 and 1133659.<br />1138325 3KEH in phase 1 (migration)<br />1138208 Profit center derivation in phase 1 (migration)<br />1133659 Deriving default profit center in phase 1 (migration)</p>\r\n<p>15. CRM integration</p>\r\n<p>See SAP Note 386391 - \"CRM: Determination of profit center\".<br /><br />A real dummy profit center (table field TKA01-DPRCT) must be maintained only if classic Profit Center Accounting is active. This is necessary for reasons of consistency for the tables in classic Profit Center Accounting.&#x00A0;If you exclusively use Profit Center Accounting in the General Ledger Accounting (new) or Profit Center Accounting in the universal journal, you no longer need to create a dummy profit center. If you still receive an error message because no dummy profit center can be found, contact us in a customer incident.</p>\r\n<p>16. ALE scenarios</p>\r\n<p>If you map Profit Center Accounting in General Ledger Accounting (new), the system also updates the profit center information in the tables of General Ledger Accounting (new). This means that when a FI-ALE scenario is used, the system automatically also distributes the profit center information.  Therefore, there are no more separate profit center-ALE scenarios in this case, for example, there is no decentralized Profit Center Accounting. </p>\r\n<p>If you use General Ledger Accounting (new), but continue to map Profit Center Accounting using only classic Profit Center Accounting (that is, you have not assigned the profit center scenario or segment scenario to any new G/L ledger), use one of the two profit center-ALE scenarios for the distribution of the profit center data. We do not recommend an update of the FI IDocs (FIDCC1/FIDCC2) in classic Profit Center Accounting (see Note 114814). </p>\r\n<p>If you use Profit Center Accounting in General Ledger Accounting (new) and classic Profit Center Accounting in parallel for a transition period and you use ALE scenarios, you must use the FI and the PCA-ALE scenario in parallel during this transition period. Depending on the scenarios used, it may be difficult to reconcile the data of the two components, and restrictions may apply.</p>\r\n<p>&#x00A0;</p>\r\n<p>17. Elimination profit center (field EPRCTR)</p>\r\n<p>The Elimination Profit Center field allows you to perform an elimination of data per profit center group in reporting. This type of elimination can be used for a PCA management reporting, but is not suitable as a logic for the legal consolidation tools.</p>\r\n<p>17.1 Fill field EPRCTR</p>\r\n<p>In classic Profit Center Accounting, the derivation rules for the elimination profit center are defined fixed in source code. There were repeated queries from customers who wanted to fill the elimination profit center for the same type of posting in different ways. For this reason, no fixed logic was implemented by SAP for General Ledger Accounting (new); however, a BAdI was provided, which customers can use to implement their own logic. If the elimination profit center is set, it must always be identical to the partner profit center. You can also decide whether the elimination profit center is to be filled automatically in all posting items in which a partner profit center is set, or only in a subset of these posting items. This depends on the individual reporting requirements. The following lists the previous source code positions for actual postings that can serve as a guide.</p>\r\n<p>AC Interface postings (for example, SD/MM):<br />For cross-company postings, the partner profit center (SPRCTR) and the elimination profit center (EPRCTR) from the affiliated company are determined and set. This is done using the read function for partner profit centers (transaction OCCL/function module COPCA_PARTNER_GSBER_PRCTR).<br />In addition, the derivation function for the partner profit center (transactions 8KES and 8KER or function module COPCA_PARTNER_PRCTR_GET) is used to set the elimination profit center). In the function module COPCA_DOCUMENT_CHECK, the partner profit center is transferred to the elimination profit center after the function module COPCA_PARTNER_PRCTR_GET is processed.</p>\r\n<p>FI postings:<br />When the function module COPCA_PARTNER_PRCTR_GET is processed in the function module COPCA_DOCUMENT_CHECK, an existing customer number or vendor number is used to determine whether it is a posting with an affiliated company. In the subsequent subroutine set_elim_prctr, invoices posted in FI with an affiliated company and transaction RFBU receive the elimination profit center if the partner profit center is filled. For the affiliated company, the VBUND derived by the system from the function module COPCA_PARTNER_PRCTR_GET is used.</p>\r\n<p>CO postings: In the include LGIN1F80, the elimination profit center (EPRCTR) is set from the partner profit center (PPRCTR); however, for some transactions, the elimination profit center is NOT set because there should be no elimination for these transactions. For example, this applies to the following transactions:</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;KAZI Actual cost center accrual<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;KAZP Plan cost center accrual<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RKP1 Planning primary costs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RKP2 Planning activities<br />&#x00A0;&#x00A0;&#x00A0; RKP5 Plan Revenue Types<br />&#x00A0;&#x00A0;&#x00A0; RKP6 Plan. act-dep. costs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RKP8 Planning Settlement Costs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RKP9 Plan. act-dep.settlement costs</p>\r\n<p><br />The elimination profit center is available in General Ledger Accounting (new) with Software Release 6.0 Enhancement Package 3, and after you activate the business function FIN_GL_CI_1 for actual data. The BAdI FAGL_DERIVE_EPRCTR is called from the function module FAGL_ELEM_PRCTR_SET. <br />Note that the new field for the elimination profit center (for example, ZZEPRCTR) still has to be assigned to the ledger so that the information of the elimination profit center is updated in the tables. To do this, choose the following path in Customizing: \"Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Ledgers -&gt; Ledger -&gt; Assign Scenarios and Customer Fields to Ledgers\".<br />Since the elimination profit center (for example, ZZEPRCTR) is not contained in the totals record table FAGLFLEXT, it must first be included in the totals record table (e.g. FAGLFLEXT) as an additional field. To do this, choose the following path in Customizing: \"Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Ledgers -&gt; Fields -&gt; Customer-Specific Fields -&gt; Include Fields to Totals Table (transaction FAGL_GINS). Activate the new field in a period in which no FI postings are carried out. If you use a different totals record table, check whether this step is required.<br />Additional information: Since the elimination profit center is a category 2 field in accordance with SAP Note 923687, you do not have to include it in the source code block.</p>\r\n<p>If you use transaction OCCL, also see SAP Notes 1719120 1826940, and 2733583. As a result, the elimination profit center in the function module COPCA_PARTNER_GSBER_PRCTR is deleted if the elimination profit center is set according to your own rules using the BAdI FAGL_DERIVE_EPRCTR.</p>\r\n<p><span style=\"text-decoration: underline;\">Plan:</span> SAP Note 1390569 enables the function module FAGL_ELEM_PRCTR_SET during the CO planning data transfer to General Ledger Accounting (new) in which the BAdI you have already maintained for the actual data exists. When you execute the program listed in SAP Note 1390570, the source code is processed. If you perform manual planning in Profit Center Accounting, the elimination profit center must be entered manually. This must still be done in General Ledger Accounting (new). If you execute plan assessments in General Ledger Accounting (new), you can use the BAdI FAGL_ALLO_SUBSTITUTE to set the elimination profit center.</p>\r\n<p>17.2 Reporting<br />The table T804C controls which fields are used for the sender-receiver relationship for elimination of IU sales. The corresponding field (sender) is defined as EPRCTR for the table GLPCT for the profit center (field name RPRCTR).<br />For the new general ledger tables FAGLFLEXT/FAGLFLEXR, the corresponding field (sender) PPRCTR is defined for the profit center (field name PRCTR) in the standard system because there was no standard field for the elimination profit center until the introduction of the business function FIN_GL_CI_1 (with the field ZZEPRCTR). As a result, in reports of General Ledger Accounting (new) with active elimination of IU sales for the \"Profit Center\" field, more rows are eliminated than expected. If the elimination profit center field is not used in General Ledger Accounting (new), the PC elimination of IU sales can therefore not be mapped completely. After introducing a field for the elimination PC, you must therefore use transaction GRCT to define the new field name (for example, ZZEPRCTR) for the table FAGLFLEXT/FAGLFLEXR for the field name PRCTR in the \"Corresponding field (sender)\" field.</p>\r\n<p>Table T804C - TA GRCT -&gt; SPRO (IMG):&#x00A0;Financial Accounting (New) -&gt; General Ledger Accounting (New) -&gt; Information System -&gt; Report Writer/Report Painter Reports -&gt; Transfer of Reports from Profit Center Accounting -&gt; Set Elimination PrCtr for Reports with Elimination of Internal Bus. Volume.</p>\r\n<p>18. Origin object type (field RHOART)</p>\r\n<p>The origin object type is available in General Ledger Accounting (new) with Software Release 6.0 Enhancement Package 3, and after you activate the business function FIN_GL_CI_1 for actual data.<br />Note that the field HOART or ZZHOART still has to be assigned to the ledger so that the information of the origin object type is updated in the tables. To do this, choose the following path in Customizing: \"Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Ledgers -&gt; Ledger -&gt; Assign Scenarios and Customer Fields to Ledgers\".<br />As the origin object type (field HOART or ZZHOART) is not contained in the totals record table FAGLFLEXT, it must first be included in the totals record table as an additional field. To do this, choose the following path in Customizing: \"Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Ledgers -&gt; Fields -&gt; Customer-Specific Fields -&gt; Include Fields to Totals Table (transaction FAGL_GINS). Activate the new field in a period in which no FI postings are carried out. If you use a different totals record table, check whether this step is required.<br />Additional information: Because the origin object is a category 2 field in accordance with SAP Note 923687, you do not need to include it in the source code block.</p>\r\n<p><br />19. Manual transfer postings</p>\r\n<p>In classic Profit Center Accounting, manual transfer postings to profit center level are can be made using transaction 9KE0. In General Ledger Accounting (new), manual postings can also be made using transaction FB01. However, these must now adhere to the rules of general ledger accounting. For example, this means that one-sided postings at profit center level, which were possible using transaction 9KE0, are no longer possible in Profit Center Accounting within General Ledger Accounting (new).</p>\r\n<p>&#x00A0;</p>\r\n<p>20. Differentiation of the data (field RRCTY - field AWTYP)</p>\r\n<p>In classic Profit Center Accounting, the data is differentiated using the record type (field RRCTY):<br /><br /></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>0</td>\r\n<td>Actual</td>\r\n</tr>\r\n<tr>\r\n<td>1</td>\r\n<td>Plan</td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>Assessment/Distribution Actual</td>\r\n</tr>\r\n<tr>\r\n<td>3</td>\r\n<td>Assessment/Distribution Plan</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In General Ledger Accounting (new), to differentiate between the data, the reference transaction (field AWTYP) can be used (for example: for actual records and plan allocations, it is AWTYP = GLAL0). For a more precise differentiation, you can also use the business transaction (field ACTIV).</p>\r\n<p>Sample allocation:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>GLA0</td>\r\n<td>Actual assessment</td>\r\n</tr>\r\n<tr>\r\n<td>GLA1</td>\r\n<td>Plan assessment</td>\r\n</tr>\r\n<tr>\r\n<td>GLD0</td>\r\n<td>Actual distribution</td>\r\n</tr>\r\n<tr>\r\n<td>GLD1</td>\r\n<td>Plan distribution</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p>\r\n<p>21. Performance in General Ledger Accounting (new)</p>\r\n<p>See SAP Note 820495: \"General Ledger Accounting (new): Data volume, performance, and parallel ledgers\".</p>\r\n<p><br />22. Unvaluated material processes (KSTAT = U)</p>\r\n<p>In classic Profit Center Accounting, documents with KSTAT = U that have initial values but a filled quantity, are transferred (include LGIN1F03 / subroutine process_rwbeleg_pca -&gt; perform quantity_relevant). Whether a quantity is set depends, among other things, on the set quantity indicator (XMFRW) in Logistics (SD/MM) and can be influenced by the central quantity module AC_DOCUMENT_QUANTITY_GET.<br />On the other hand, documents with KSTAT = U that have initial values but a filled quantity are not posted in Financial Accounting in ECC.<br /><br /></p>\r\n<p>23. Tables<br />23.1 T882<br />Table T882 is no longer relevant for fixed ledgers of the FI table GLT0 if General Ledger Accounting (new) is active (SAP Note 2954084). But table T882 is still required for the ledger 8A of classic Profit Center Accounting (as well as for ledgers used in FI-SL). However, only the field VTRHJ is used for the ledger 8A. In the field VTRHJ, the carryforward year of the last execution of transaction 2KES (balance carryforward PCA) is set. It is important for the correct execution of transaction 2KES. All other fields are irrelevant for processing in classic PCA. The local currency (HSL) is always derived from the information of the company code (table T001). The PCA local currency (KSL) is always derived from the information of the controlling area (table TKA01).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D025743)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D025743)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "997925", "RefComponent": "FI-GL-FL", "RefTitle": "Read partner profit center in NewGL", "RefUrl": "/notes/997925"}, {"RefNumber": "971364", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/971364"}, {"RefNumber": "966000", "RefComponent": "CA-JVA", "RefTitle": "mySAP new general ledger and Joint Venture Accounting", "RefUrl": "/notes/966000"}, {"RefNumber": "937872", "RefComponent": "FI-GL-GL", "RefTitle": "General Ledger Accounting (new): Internal revenue between profit centers", "RefUrl": "/notes/937872"}, {"RefNumber": "895923", "RefComponent": "CO-PA-MD", "RefTitle": "Active indicator of PCA in CO-PA derivation", "RefUrl": "/notes/895923"}, {"RefNumber": "853169", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATION: Actual cycles can only be executed periodically", "RefUrl": "/notes/853169"}, {"RefNumber": "820121", "RefComponent": "FI-GL", "RefTitle": "Document splitting for segment does not work", "RefUrl": "/notes/820121"}, {"RefNumber": "817587", "RefComponent": "FI-GL-FL", "RefTitle": "Online splitter: Check for obligatory characteristics", "RefUrl": "/notes/817587"}, {"RefNumber": "764841", "RefComponent": "RE-FX", "RefTitle": "No profit center derivation for active flexible GL", "RefUrl": "/notes/764841"}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146"}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821"}, {"RefNumber": "217338", "RefComponent": "EC-PCA-MD", "RefTitle": "Number of profit centers", "RefUrl": "/notes/217338"}, {"RefNumber": "2093490", "RefComponent": "FI-GL-FL", "RefTitle": "FAGL3KEH: Derivation of default profit center for cost elements", "RefUrl": "/notes/2093490"}, {"RefNumber": "1719120", "RefComponent": "EC-PCA-ACT", "RefTitle": "Elimination profit center in General Ledger Accounting (new) as of EHP 3", "RefUrl": "/notes/1719120"}, {"RefNumber": "1280060", "RefComponent": "FI-GL", "RefTitle": "PCA in FI-GL (new) or classic PCA?", "RefUrl": "/notes/1280060"}, {"RefNumber": "1241741", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Problems w/ 3KEH/FAGL3KEH, particularly in migration phase 1", "RefUrl": "/notes/1241741"}, {"RefNumber": "1157912", "RefComponent": "EC-PCA-TP", "RefTitle": "KM 140 during order settlement", "RefUrl": "/notes/1157912"}, {"RefNumber": "114814", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCCP01/02 and FAGLDT01/FAGLST01: Questions and problems in FI distribution", "RefUrl": "/notes/114814"}, {"RefNumber": "1138325", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "3KEH in phase 1 (migration)", "RefUrl": "/notes/1138325"}, {"RefNumber": "1138208", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Profit center derivation in phase 1 (migration)", "RefUrl": "/notes/1138208"}, {"RefNumber": "1133659", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Deriving default profit center in phase 1 (migration)", "RefUrl": "/notes/1133659"}, {"RefNumber": "1087350", "RefComponent": "FI-GL-GL", "RefTitle": "NewGL: Reading partner profit center in reversal", "RefUrl": "/notes/1087350"}, {"RefNumber": "1039346", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: RCIPE00/RPCIPE01 - distribution of liabilities", "RefUrl": "/notes/1039346"}, {"RefNumber": "1018065", "RefComponent": "FI-GL-GL", "RefTitle": "NewGL: activating \"segmentation\" scenario", "RefUrl": "/notes/1018065"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2911106", "RefComponent": "EC-PCA", "RefTitle": "After system upgrade to S/4HANA, PCA data can not be displayed in classic Profit Center report", "RefUrl": "/notes/2911106 "}, {"RefNumber": "1988311", "RefComponent": "FI-GL-FL", "RefTitle": "GLT2201 in materials management invoice with minor differences", "RefUrl": "/notes/1988311 "}, {"RefNumber": "2157482", "RefComponent": "FI-GL-IS", "RefTitle": "S_PCO_36000219 balance does not match with FBL1N", "RefUrl": "/notes/2157482 "}, {"RefNumber": "2060116", "RefComponent": "FI-GL-IS", "RefTitle": "S_PCO_36000218 balance does not match with FBL5N", "RefUrl": "/notes/2060116 "}, {"RefNumber": "2070061", "RefComponent": "FI-GL-IS", "RefTitle": "S_AC0_52000887 balance does not match with FBL5N", "RefUrl": "/notes/2070061 "}, {"RefNumber": "2559043", "RefComponent": "CO-PC-ACT", "RefTitle": "error message GLT2 201 in transaction CKMLCP/CKMLCPAVR", "RefUrl": "/notes/2559043 "}, {"RefNumber": "2425255", "RefComponent": "EC-PCA-ACT", "RefTitle": "Profit Center Accounting in the universal journal in SAP S/4HANA, on-premise and private cloud", "RefUrl": "/notes/2425255 "}, {"RefNumber": "2796842", "RefComponent": "RE-FX-LA-RA", "RefTitle": "RECEEP: Partner profit center and elimination profit center set incorrectly/unnecessarily (standard)", "RefUrl": "/notes/2796842 "}, {"RefNumber": "2093490", "RefComponent": "FI-GL-FL", "RefTitle": "FAGL3KEH: Derivation of default profit center for cost elements", "RefUrl": "/notes/2093490 "}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821 "}, {"RefNumber": "937872", "RefComponent": "FI-GL-GL", "RefTitle": "General Ledger Accounting (new): Internal revenue between profit centers", "RefUrl": "/notes/937872 "}, {"RefNumber": "1039346", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: RCIPE00/RPCIPE01 - distribution of liabilities", "RefUrl": "/notes/1039346 "}, {"RefNumber": "1280060", "RefComponent": "FI-GL", "RefTitle": "Classic PCA or PCA in General Ledger Accounting (new)? Classic PCA or PCA in the universal journal in SAP S/4HANA?", "RefUrl": "/notes/1280060 "}, {"RefNumber": "1719120", "RefComponent": "EC-PCA-ACT", "RefTitle": "Elimination profit center in General Ledger Accounting (new) as of EHP 3", "RefUrl": "/notes/1719120 "}, {"RefNumber": "997925", "RefComponent": "FI-GL-FL", "RefTitle": "Read partner profit center in NewGL", "RefUrl": "/notes/997925 "}, {"RefNumber": "1087350", "RefComponent": "FI-GL-GL", "RefTitle": "NewGL: Reading partner profit center in reversal", "RefUrl": "/notes/1087350 "}, {"RefNumber": "114814", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCCP01/02 and FAGLDT01/FAGLST01: Questions and problems in FI distribution", "RefUrl": "/notes/114814 "}, {"RefNumber": "1241741", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Problems w/ 3KEH/FAGL3KEH, particularly in migration phase 1", "RefUrl": "/notes/1241741 "}, {"RefNumber": "1138325", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "3KEH in phase 1 (migration)", "RefUrl": "/notes/1138325 "}, {"RefNumber": "966000", "RefComponent": "CA-JVA", "RefTitle": "mySAP new general ledger and Joint Venture Accounting", "RefUrl": "/notes/966000 "}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146 "}, {"RefNumber": "1133659", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Deriving default profit center in phase 1 (migration)", "RefUrl": "/notes/1133659 "}, {"RefNumber": "820121", "RefComponent": "FI-GL", "RefTitle": "Document splitting for segment does not work", "RefUrl": "/notes/820121 "}, {"RefNumber": "1224657", "RefComponent": "FI-GL-GL-D", "RefTitle": "FAGLL03 in ERP2004: Amounts displayed incorrectly", "RefUrl": "/notes/1224657 "}, {"RefNumber": "1157912", "RefComponent": "EC-PCA-TP", "RefTitle": "KM 140 during order settlement", "RefUrl": "/notes/1157912 "}, {"RefNumber": "1138208", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Profit center derivation in phase 1 (migration)", "RefUrl": "/notes/1138208 "}, {"RefNumber": "1018065", "RefComponent": "FI-GL-GL", "RefTitle": "NewGL: activating \"segmentation\" scenario", "RefUrl": "/notes/1018065 "}, {"RefNumber": "217338", "RefComponent": "EC-PCA-MD", "RefTitle": "Number of profit centers", "RefUrl": "/notes/217338 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "817587", "RefComponent": "FI-GL-FL", "RefTitle": "Online splitter: Check for obligatory characteristics", "RefUrl": "/notes/817587 "}, {"RefNumber": "895923", "RefComponent": "CO-PA-MD", "RefTitle": "Active indicator of PCA in CO-PA derivation", "RefUrl": "/notes/895923 "}, {"RefNumber": "853169", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATION: Actual cycles can only be executed periodically", "RefUrl": "/notes/853169 "}, {"RefNumber": "764841", "RefComponent": "RE-FX", "RefTitle": "No profit center derivation for active flexible GL", "RefUrl": "/notes/764841 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}