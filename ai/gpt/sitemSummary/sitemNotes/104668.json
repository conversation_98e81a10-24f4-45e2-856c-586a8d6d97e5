{"Request": {"Number": "104668", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1308, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014566912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000104668?language=E&token=014C9FD4BD1129B3B5764DC52A20F766"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000104668", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000104668/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "104668"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 55}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.06.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "104668 - Collective note: Subsequent settlement (Purch.) Release 4.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>-- Collective note: Problems and errors in&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;subsequent settlement (purchasing)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Version: December 20, 2001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--</p> <b>***********************************************************************</b><br /> <b>* European Monetary Union (important):&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 480984 Currency conversion, message MN 471</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(rebate arrangement currency cannot be converted)</b><br /> <b>*</b><br /> <b>* 439493 Incorrect document currency w/ billing via billing interface *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(incorrect posting in Financial Acctng, currency exchanged)&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 452365 Rebate arrangement currency not copied to condition records&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(undesired currency in condition record)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 437429 Message SG105 List settlement documents, setup incomes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Revenues in Financial Accounting and/or S074 incorrect)&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 400432 Expiring currencies, rebate arrangmnt maint. (purch., sales) *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>***********************************************************************</b><br /> <b>* Notes that you should definitely implement as a precaution since&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* the consequences may be difficult to correct:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 422649 Error in validity display (agrmt maintenance)- deletion&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This note must be implemented (even by customers not&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;affected by the currency changeover)!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 400893 Detailed statement, message MN 514&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 408521 MN 495 (Update simulation), MN 514 (detailed statement)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 394619 Provisions f income n cancelled if cancelling settlement doc *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 315769 Subsequent settlement: Subsequent business volume update&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Goods receipt pricing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 305388 Subs.Settlmnt: Subs.busi.vol.update in wrong period&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* In Release 4.0A/B, you must implement Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>* 303047 RMCENEUA - Error in selection by info structures&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Otherwise, the wrong revenues might be settled!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* In Release 4.0A/B, you must implement Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>* 175341 Subsq. Settlement: Multiple updates of income in&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* customer billing documents. Otherwise, the wrong incomes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* might be settled!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 180434 Sub.settlmt: Verifcatn o.rel docmnts befre archving&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 179864 PURCHIS - commitments - problems during IR or GR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 176433 Subsq. Settlement: Recompilation of income, incomes are&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;completely deleted&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 175160 PURCHIS - GR for several PO items with PO generation&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 175138 PURCHIS - GR with delivery generation - volume-based rebate&#x00A0;&#x00A0;*</b><br /> <b>* Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 160570 Subsequent settlement: Subs. update and price determination&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 160440 Subs. settlement: Wrong update of PI recompilation&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 153954 Subs. Settlement: incorrect subsequent update with value 0&#x00A0;&#x00A0; *</b><br /> <b>* Releases 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 153577 Subs. Settlement: Too many recompilations/subsequent updates *</b><br /> <b>* Releases 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 153054 Subsequent settlement: Recompilation of business volume data *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;deletes all business volume data&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* Release 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 151398 PURCHIS - double update for delivery costs for several&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;purchase orders&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>* Releases 4.0A und 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 104867 Sub. settlnt: Updating business volume data incorrect&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* Releases 4.0A and 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 120610 Subs. settl: Data loss vendor busn. volume comp.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 206060 Ind. 'Subseq. settlement', deviating vendor data&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see also Note 214146!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>***********************************************************************</b><br /> <p><br />This Note refers to Release 4.0B only. For Release 4.5B, refer to special Note 152725, for Release 4.6, Note 183379 as well as Note 40147 for Release 3.0A to 3.1I.<br /></p> <b>General information</b><br /> <p>Above all, this note should point out the most frequent problems and errors during subsequent settlement (purchasing). It should help to analyze frequently occuring problems and perhaps to solve them.<br />You find a list of all notes on subsequent settlement which refer to known problems in section \"Related notes\". All notes to correct program errors will be delivered with the next available Hot Package.</p> <b>Handling</b><br /> <p>If you have problems with subsequent settlement (purchasing), you can first try to solve the problem yourself using this note. Check whether one of these notes refers to your problem. Read the note and execute the checks specified there. In it you may find references to notes which may solve the errors.<br />Please note that you can access various informational consulting notes on the topics of purchasing, goods receipt, etc., using search criteria MM-PUR* (component) and FAQ (search text).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, collective note<br />Transactions MEB1, MEB2, MEB3, MEB4, MEB6, MEB8, MEB9, MCE+, MEBA<br />Programs SAPMV13A, SAPMNB01, RWMBON01, RWMBON03, RWMBON04, RWMBON06, RWMBON08, RMCE0900<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Notes on problem solution/consulting</OL> <p><br />167284 Subsequent settlement: general notes (Q&amp;As and Customizing)<br /><br />104683 Sub. settlement: changes in Release 4.0A/B<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Innovations/changes in Release 4.0A/B in the overview<br /><br />75655 Error message VK358: 'PurchOrg &amp; with CoCode &amp; deviates from the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CoCode in the arrangement'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Underlying problem:</p> <UL><UL><LI>You work with several company codes, that is, your purchasing organization is not permanently assigned to a company code. Also see Note 153694.</LI></UL></UL> <p><br />153694: Subsequent settlement: Credit-side or debit side<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;settlement type<br />Note on the importance of the settlement type.<br /><br />72199 Subsequent settlement: updating vendor business volumes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>No update of business volume data is executed for a document.</LI></UL></UL> <UL><UL><LI>For a document, an update of business volume data is executed but the scale basis value or condition basis has value 0.</LI></UL></UL> <p><br />112413 Subsequent settlement: units of measure<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Possible problems when using units of measure</LI></UL></UL> <p><br />113031 Subsequent settlement: Taxes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Tax handling (calculation) in the process of subsequent settlement</LI></UL></UL> <p><br />77258 Subsequent settlement: Required fields settlement doc.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>When settling an arrangement error messages</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;00055 \"Required entry not made\" or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;00344 \"No batch input data for screen SAPMM08R xxxx\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;are generated.<br /><br />80233 Debit-side settlement: Customizing, error message<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>Error messages of message class VF</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure during SD Customizing from the point of view of the subsequent settlement</LI></UL></UL> <p><br />73214 Subs.settl.: Retrospec. compltn/rcompltn o.bus.vol.data<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>With the retrospective compilation of vendor business volumes, no update is executed.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure when using the function</LI></UL></UL> <UL><UL><LI>Functional restrictions of the function</LI></UL></UL> <p><br />381831 Consignment processing and subsequent billing<br />Frequent symptoms:<br />No update of business volume for settlement documents in consignment processing<br /><br />333914 Subsequent settlement: Performance (composite note)<br /></p> <OL>2. Notes on known problems</OL> <p>See section \"Related notes\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023678)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000104668/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104668/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98843", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:00015(value of field is negative)", "RefUrl": "/notes/98843"}, {"RefNumber": "98648", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Abnormal termination for detailed statement", "RefUrl": "/notes/98648"}, {"RefNumber": "73214", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settl.:Retrospec.compltn/recompltn of busin.vol.data", "RefUrl": "/notes/73214"}, {"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199"}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040"}, {"RefNumber": "489318", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Verbuchungsabbruch beim Anlegen von Absprachen", "RefUrl": "/notes/489318"}, {"RefNumber": "485130", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Arrangement status for interim settlement set incorrectly", "RefUrl": "/notes/485130"}, {"RefNumber": "480984", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currency conversion, message MN 471", "RefUrl": "/notes/480984"}, {"RefNumber": "458695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Man. correction f. customer BillDoc income w/ partial settl.", "RefUrl": "/notes/458695"}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365"}, {"RefNumber": "445331", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currencies can be deleted in the mass maintenance", "RefUrl": "/notes/445331"}, {"RefNumber": "441313", "RefComponent": "LO-MD-RPC", "RefTitle": "Doc index generation: Variable key wrong for specific values", "RefUrl": "/notes/441313"}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493"}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324"}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429"}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199"}, {"RefNumber": "433752", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error when checking for release to accounting", "RefUrl": "/notes/433752"}, {"RefNumber": "425755", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Sel. rebate arrangement by arrangmt type, arrangmt calendar", "RefUrl": "/notes/425755"}, {"RefNumber": "425033", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem extending arrangements if arrangement contains EURO", "RefUrl": "/notes/425033"}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649"}, {"RefNumber": "408521", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN495 (update simulation), MN514 (detailed statement)", "RefUrl": "/notes/408521"}, {"RefNumber": "407972", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in check routine for EXIT_SAPLWN01_003", "RefUrl": "/notes/407972"}, {"RefNumber": "403734", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN242 upon settlement (different calculation rules)", "RefUrl": "/notes/403734"}, {"RefNumber": "400898", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation bus.vol. data, incomes: sel. by arrangemt type", "RefUrl": "/notes/400898"}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893"}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432"}, {"RefNumber": "399118", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msg NAA045 list output for arrangemts (RWMBON02, Trans.MEB5)", "RefUrl": "/notes/399118"}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739"}, {"RefNumber": "396955", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/396955"}, {"RefNumber": "396334", "RefComponent": "MM-PUR-VM", "RefTitle": "Selection according to document date in RMEBEIN3", "RefUrl": "/notes/396334"}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673"}, {"RefNumber": "394619", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/394619"}, {"RefNumber": "392066", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Maximum values cannot be entered per calculation rule", "RefUrl": "/notes/392066"}, {"RefNumber": "392055", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Search help attachment in report RMCE0900", "RefUrl": "/notes/392055"}, {"RefNumber": "388716", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "Value dates in the general billing interface", "RefUrl": "/notes/388716"}, {"RefNumber": "388626", "RefComponent": "MM-PUR-VM", "RefTitle": "Variant cannot be saved for reports", "RefUrl": "/notes/388626"}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044"}, {"RefNumber": "386665", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of document index S111", "RefUrl": "/notes/386665"}, {"RefNumber": "386157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Shrt dmp CONVT_OVERFLOW or BCD_FIELD_OVERFLOW durng settlmnt", "RefUrl": "/notes/386157"}, {"RefNumber": "386039", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination of report rwmbon08 with message MN 273", "RefUrl": "/notes/386039"}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006"}, {"RefNumber": "383053", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Update termination for final settlement with message MN248", "RefUrl": "/notes/383053"}, {"RefNumber": "382939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in agreement maintenance debit-side settlmt accounting", "RefUrl": "/notes/382939"}, {"RefNumber": "382829", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect repricing in subsequent settlement", "RefUrl": "/notes/382829"}, {"RefNumber": "382749", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Org. data of an arrangement not copied during creation", "RefUrl": "/notes/382749"}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831"}, {"RefNumber": "375119", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating provisions for accrued inc. of parked credit memos", "RefUrl": "/notes/375119"}, {"RefNumber": "371754", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Not possible to display invoice from list display", "RefUrl": "/notes/371754"}, {"RefNumber": "371737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Copy additional value days to credit memos", "RefUrl": "/notes/371737"}, {"RefNumber": "367511", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "structure MCKONA for LIS filled incompletely", "RefUrl": "/notes/367511"}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295"}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231"}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914"}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145"}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142"}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921"}, {"RefNumber": "325341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect exception processing of user exit LWBON003", "RefUrl": "/notes/325341"}, {"RefNumber": "324705", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msgs MN781 or MN164 with settlement of rebate arrangement", "RefUrl": "/notes/324705"}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279"}, {"RefNumber": "319666", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deadlock with subsequent business volume recompilation", "RefUrl": "/notes/319666"}, {"RefNumber": "316723", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error log for settlement run", "RefUrl": "/notes/316723"}, {"RefNumber": "316290", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance of document index recompilation", "RefUrl": "/notes/316290"}, {"RefNumber": "315769", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of bus.volume, price determtn goods receipt", "RefUrl": "/notes/315769"}, {"RefNumber": "306173", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect specification of period-specific condition", "RefUrl": "/notes/306173"}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806"}, {"RefNumber": "305388", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent business volume update in wrong period", "RefUrl": "/notes/305388"}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047"}, {"RefNumber": "302145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "RMCENEUA: PIS, business volume data updated repeatedly", "RefUrl": "/notes/302145"}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716"}, {"RefNumber": "215124", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Del.ind. of settled vol.rebate cond. can be changed", "RefUrl": "/notes/215124"}, {"RefNumber": "202036", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination TSV_TNEW_PAGE_ALLOC_FAILED when you settle", "RefUrl": "/notes/202036"}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492"}, {"RefNumber": "200859", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Terms of Payment not transferred", "RefUrl": "/notes/200859"}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703"}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188"}, {"RefNumber": "198340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incor bus.vol assgmt if doc.date not in arrgmt perd", "RefUrl": "/notes/198340"}, {"RefNumber": "197007", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Incor decimals in amount of condit.rec (II)", "RefUrl": "/notes/197007"}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134"}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902"}, {"RefNumber": "186133", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN019 in initial arrangement creation", "RefUrl": "/notes/186133"}, {"RefNumber": "184340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Euro Workbench: Incorrect input check of arrangements", "RefUrl": "/notes/184340"}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434"}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864"}, {"RefNumber": "176433", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income recompilation, income deleted completely", "RefUrl": "/notes/176433"}, {"RefNumber": "175526", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-b.rebate:Incor.dec.plcs.in amnt.of cond.recs", "RefUrl": "/notes/175526"}, {"RefNumber": "175341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple updating income when changing customer billing doc", "RefUrl": "/notes/175341"}, {"RefNumber": "175160", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS-GR for several PO items with PO generation", "RefUrl": "/notes/175160"}, {"RefNumber": "175138", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "PURCHIS-GR w/ generation of dely-volme-based rebate", "RefUrl": "/notes/175138"}, {"RefNumber": "171902", "RefComponent": "MM-PUR", "RefTitle": "Update of update BV data for return item incorrect", "RefUrl": "/notes/171902"}, {"RefNumber": "169540", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN371 when settling arrangement", "RefUrl": "/notes/169540"}, {"RefNumber": "169372", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Document items in subs. updating not edited", "RefUrl": "/notes/169372"}, {"RefNumber": "168242", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 4.0B", "RefUrl": "/notes/168242"}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284"}, {"RefNumber": "163400", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation needs too much main memory", "RefUrl": "/notes/163400"}, {"RefNumber": "162897", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect detailed statement", "RefUrl": "/notes/162897"}, {"RefNumber": "161595", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect data transfer for activated user exit", "RefUrl": "/notes/161595"}, {"RefNumber": "160440", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect update of provisions for accrued income recompiltn", "RefUrl": "/notes/160440"}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580"}, {"RefNumber": "154601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequnt settlmnt:external data transfr cancellatn", "RefUrl": "/notes/154601"}, {"RefNumber": "153954", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect subsequent update with value 0", "RefUrl": "/notes/153954"}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694"}, {"RefNumber": "153577", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Too many recompilation updates/subsequent updates", "RefUrl": "/notes/153577"}, {"RefNumber": "153054", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompltn of busnss volume data deletes all busnss vlme data", "RefUrl": "/notes/153054"}, {"RefNumber": "151611", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect updating of business volume for price determntn GR", "RefUrl": "/notes/151611"}, {"RefNumber": "151607", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect proposal dcmnt number for cancelltn of credit memo", "RefUrl": "/notes/151607"}, {"RefNumber": "151398", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS -double update for deliv.cst fr several POs", "RefUrl": "/notes/151398"}, {"RefNumber": "150738", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN607 or MN597", "RefUrl": "/notes/150738"}, {"RefNumber": "147939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance recompil.bus.vol.data/Subs.BV update", "RefUrl": "/notes/147939"}, {"RefNumber": "146739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Condition rate/percentage rate in document condtns incorrect", "RefUrl": "/notes/146739"}, {"RefNumber": "146092", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No reason for occurrence of message MN310 in settlement run", "RefUrl": "/notes/146092"}, {"RefNumber": "143301", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update with fixed amount calculation rule", "RefUrl": "/notes/143301"}, {"RefNumber": "141623", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of business volume processes discnt in kind", "RefUrl": "/notes/141623"}, {"RefNumber": "139148", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance problems when deleting PO items", "RefUrl": "/notes/139148"}, {"RefNumber": "136863", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "New DB secondary index AEB f.table EKBO", "RefUrl": "/notes/136863"}, {"RefNumber": "134210", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Perf. recompile bus. vol. data/suppl. updating of bus. vol.", "RefUrl": "/notes/134210"}, {"RefNumber": "133493", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Notes application area MM-PUR-VM-SET", "RefUrl": "/notes/133493"}, {"RefNumber": "132071", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for collectve purchase order", "RefUrl": "/notes/132071"}, {"RefNumber": "128507", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MM108: Subsequent updating of business volume", "RefUrl": "/notes/128507"}, {"RefNumber": "128279", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Automatic deletion business volume data during recompilation", "RefUrl": "/notes/128279"}, {"RefNumber": "128070", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: error message MN514", "RefUrl": "/notes/128070"}, {"RefNumber": "126764", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS - GR with PO generation and normal item", "RefUrl": "/notes/126764"}, {"RefNumber": "126220", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 when saving business volume comprsn and agrmnt", "RefUrl": "/notes/126220"}, {"RefNumber": "123487", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No change update of purchase order", "RefUrl": "/notes/123487"}, {"RefNumber": "123297", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message MN369", "RefUrl": "/notes/123297"}, {"RefNumber": "122727", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN177 with interim settlement", "RefUrl": "/notes/122727"}, {"RefNumber": "120610", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Data loss recompilation vendor volume data with short dump", "RefUrl": "/notes/120610"}, {"RefNumber": "118460", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for date in future or past", "RefUrl": "/notes/118460"}, {"RefNumber": "118087", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message M8001", "RefUrl": "/notes/118087"}, {"RefNumber": "118077", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Messages MN514, MN515, MN565 and MN566", "RefUrl": "/notes/118077"}, {"RefNumber": "117265", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Message MN589", "RefUrl": "/notes/117265"}, {"RefNumber": "114570", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Status arrangement if condition records/arrangements deleted", "RefUrl": "/notes/114570"}, {"RefNumber": "114111", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of incomes from settlement documents RWMBON07", "RefUrl": "/notes/114111"}, {"RefNumber": "113655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deviating tax calculation in credt memos", "RefUrl": "/notes/113655"}, {"RefNumber": "113315", "RefComponent": "SD-MD-CM", "RefTitle": "VK894: Screen generation error for rebate", "RefUrl": "/notes/113315"}, {"RefNumber": "113240", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Alternative UM for qty-dependent materl volume rebate: VK070", "RefUrl": "/notes/113240"}, {"RefNumber": "113031", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Taxes", "RefUrl": "/notes/113031"}, {"RefNumber": "112731", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Item text with debit-side settlement accounting", "RefUrl": "/notes/112731"}, {"RefNumber": "112413", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: units of measure", "RefUrl": "/notes/112413"}, {"RefNumber": "112407", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error MM108 for settlement statement statistic", "RefUrl": "/notes/112407"}, {"RefNumber": "111366", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN164 and MN310", "RefUrl": "/notes/111366"}, {"RefNumber": "108139", "RefComponent": "MM-IM-GR", "RefTitle": "GR reversed incorrectly wth negative delivery costs", "RefUrl": "/notes/108139"}, {"RefNumber": "107552", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Update from goods receipt", "RefUrl": "/notes/107552"}, {"RefNumber": "105895", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Crrncy trnsltin busnss vlme comprson+agremnt incor.", "RefUrl": "/notes/105895"}, {"RefNumber": "105687", "RefComponent": "MM-PUR-RFQ", "RefTitle": "ME41: Volume rebate indicator from vendor master", "RefUrl": "/notes/105687"}, {"RefNumber": "105333", "RefComponent": "MM-IM", "RefTitle": "GR to returns item, neg. delivery costs, M7050", "RefUrl": "/notes/105333"}, {"RefNumber": "104888", "RefComponent": "IS-R-LG-IM", "RefTitle": "Reading article master w. value-based inventry mgmt", "RefUrl": "/notes/104888"}, {"RefNumber": "104867", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating business volume data incorrect", "RefUrl": "/notes/104867"}, {"RefNumber": "104683", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: changes in Release 4.0", "RefUrl": "/notes/104683"}, {"RefNumber": "104662", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, messages MN546 or MN604", "RefUrl": "/notes/104662"}, {"RefNumber": "102392", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsqunt settlemnt:program termintn, missng exceptn", "RefUrl": "/notes/102392"}, {"RefNumber": "101199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Program termination RV13Ennn missing", "RefUrl": "/notes/101199"}, {"RefNumber": "100695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update during reversal", "RefUrl": "/notes/100695"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "388716", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "Value dates in the general billing interface", "RefUrl": "/notes/388716 "}, {"RefNumber": "113031", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Taxes", "RefUrl": "/notes/113031 "}, {"RefNumber": "386039", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination of report rwmbon08 with message MN 273", "RefUrl": "/notes/386039 "}, {"RefNumber": "371754", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Not possible to display invoice from list display", "RefUrl": "/notes/371754 "}, {"RefNumber": "367511", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "structure MCKONA for LIS filled incompletely", "RefUrl": "/notes/367511 "}, {"RefNumber": "407972", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in check routine for EXIT_SAPLWN01_003", "RefUrl": "/notes/407972 "}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006 "}, {"RefNumber": "382829", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect repricing in subsequent settlement", "RefUrl": "/notes/382829 "}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703 "}, {"RefNumber": "197007", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Incor decimals in amount of condit.rec (II)", "RefUrl": "/notes/197007 "}, {"RefNumber": "113315", "RefComponent": "SD-MD-CM", "RefTitle": "VK894: Screen generation error for rebate", "RefUrl": "/notes/113315 "}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434 "}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493 "}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284 "}, {"RefNumber": "485130", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Arrangement status for interim settlement set incorrectly", "RefUrl": "/notes/485130 "}, {"RefNumber": "489318", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Verbuchungsabbruch beim Anlegen von Absprachen", "RefUrl": "/notes/489318 "}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040 "}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199 "}, {"RefNumber": "371737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Copy additional value days to credit memos", "RefUrl": "/notes/371737 "}, {"RefNumber": "392066", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Maximum values cannot be entered per calculation rule", "RefUrl": "/notes/392066 "}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365 "}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694 "}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145 "}, {"RefNumber": "325341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect exception processing of user exit LWBON003", "RefUrl": "/notes/325341 "}, {"RefNumber": "403734", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN242 upon settlement (different calculation rules)", "RefUrl": "/notes/403734 "}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231 "}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432 "}, {"RefNumber": "133493", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Notes application area MM-PUR-VM-SET", "RefUrl": "/notes/133493 "}, {"RefNumber": "458695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Man. correction f. customer BillDoc income w/ partial settl.", "RefUrl": "/notes/458695 "}, {"RefNumber": "315769", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of bus.volume, price determtn goods receipt", "RefUrl": "/notes/315769 "}, {"RefNumber": "202036", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination TSV_TNEW_PAGE_ALLOC_FAILED when you settle", "RefUrl": "/notes/202036 "}, {"RefNumber": "441313", "RefComponent": "LO-MD-RPC", "RefTitle": "Doc index generation: Variable key wrong for specific values", "RefUrl": "/notes/441313 "}, {"RefNumber": "316290", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance of document index recompilation", "RefUrl": "/notes/316290 "}, {"RefNumber": "425033", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem extending arrangements if arrangement contains EURO", "RefUrl": "/notes/425033 "}, {"RefNumber": "480984", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currency conversion, message MN 471", "RefUrl": "/notes/480984 "}, {"RefNumber": "396955", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/396955 "}, {"RefNumber": "382749", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Org. data of an arrangement not copied during creation", "RefUrl": "/notes/382749 "}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142 "}, {"RefNumber": "445331", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currencies can be deleted in the mass maintenance", "RefUrl": "/notes/445331 "}, {"RefNumber": "161595", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect data transfer for activated user exit", "RefUrl": "/notes/161595 "}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324 "}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649 "}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429 "}, {"RefNumber": "306173", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect specification of period-specific condition", "RefUrl": "/notes/306173 "}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492 "}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902 "}, {"RefNumber": "186133", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN019 in initial arrangement creation", "RefUrl": "/notes/186133 "}, {"RefNumber": "184340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Euro Workbench: Incorrect input check of arrangements", "RefUrl": "/notes/184340 "}, {"RefNumber": "169540", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN371 when settling arrangement", "RefUrl": "/notes/169540 "}, {"RefNumber": "163400", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation needs too much main memory", "RefUrl": "/notes/163400 "}, {"RefNumber": "151607", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect proposal dcmnt number for cancelltn of credit memo", "RefUrl": "/notes/151607 "}, {"RefNumber": "150738", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN607 or MN597", "RefUrl": "/notes/150738 "}, {"RefNumber": "146739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Condition rate/percentage rate in document condtns incorrect", "RefUrl": "/notes/146739 "}, {"RefNumber": "146092", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No reason for occurrence of message MN310 in settlement run", "RefUrl": "/notes/146092 "}, {"RefNumber": "126220", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 when saving business volume comprsn and agrmnt", "RefUrl": "/notes/126220 "}, {"RefNumber": "122727", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN177 with interim settlement", "RefUrl": "/notes/122727 "}, {"RefNumber": "118077", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Messages MN514, MN515, MN565 and MN566", "RefUrl": "/notes/118077 "}, {"RefNumber": "114570", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Status arrangement if condition records/arrangements deleted", "RefUrl": "/notes/114570 "}, {"RefNumber": "113240", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Alternative UM for qty-dependent materl volume rebate: VK070", "RefUrl": "/notes/113240 "}, {"RefNumber": "112731", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Item text with debit-side settlement accounting", "RefUrl": "/notes/112731 "}, {"RefNumber": "112407", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error MM108 for settlement statement statistic", "RefUrl": "/notes/112407 "}, {"RefNumber": "111366", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN164 and MN310", "RefUrl": "/notes/111366 "}, {"RefNumber": "104662", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, messages MN546 or MN604", "RefUrl": "/notes/104662 "}, {"RefNumber": "98648", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Abnormal termination for detailed statement", "RefUrl": "/notes/98648 "}, {"RefNumber": "375119", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating provisions for accrued inc. of parked credit memos", "RefUrl": "/notes/375119 "}, {"RefNumber": "324705", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msgs MN781 or MN164 with settlement of rebate arrangement", "RefUrl": "/notes/324705 "}, {"RefNumber": "319666", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deadlock with subsequent business volume recompilation", "RefUrl": "/notes/319666 "}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188 "}, {"RefNumber": "134210", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Perf. recompile bus. vol. data/suppl. updating of bus. vol.", "RefUrl": "/notes/134210 "}, {"RefNumber": "128507", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MM108: Subsequent updating of business volume", "RefUrl": "/notes/128507 "}, {"RefNumber": "101199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Program termination RV13Ennn missing", "RefUrl": "/notes/101199 "}, {"RefNumber": "100695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update during reversal", "RefUrl": "/notes/100695 "}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673 "}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921 "}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806 "}, {"RefNumber": "305388", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent business volume update in wrong period", "RefUrl": "/notes/305388 "}, {"RefNumber": "176433", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income recompilation, income deleted completely", "RefUrl": "/notes/176433 "}, {"RefNumber": "175341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple updating income when changing customer billing doc", "RefUrl": "/notes/175341 "}, {"RefNumber": "169372", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Document items in subs. updating not edited", "RefUrl": "/notes/169372 "}, {"RefNumber": "160440", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect update of provisions for accrued income recompiltn", "RefUrl": "/notes/160440 "}, {"RefNumber": "153954", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect subsequent update with value 0", "RefUrl": "/notes/153954 "}, {"RefNumber": "153577", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Too many recompilation updates/subsequent updates", "RefUrl": "/notes/153577 "}, {"RefNumber": "153054", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompltn of busnss volume data deletes all busnss vlme data", "RefUrl": "/notes/153054 "}, {"RefNumber": "151611", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect updating of business volume for price determntn GR", "RefUrl": "/notes/151611 "}, {"RefNumber": "143301", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update with fixed amount calculation rule", "RefUrl": "/notes/143301 "}, {"RefNumber": "141623", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of business volume processes discnt in kind", "RefUrl": "/notes/141623 "}, {"RefNumber": "123487", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No change update of purchase order", "RefUrl": "/notes/123487 "}, {"RefNumber": "120610", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Data loss recompilation vendor volume data with short dump", "RefUrl": "/notes/120610 "}, {"RefNumber": "118460", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for date in future or past", "RefUrl": "/notes/118460 "}, {"RefNumber": "107552", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Update from goods receipt", "RefUrl": "/notes/107552 "}, {"RefNumber": "104867", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating business volume data incorrect", "RefUrl": "/notes/104867 "}, {"RefNumber": "388626", "RefComponent": "MM-PUR-VM", "RefTitle": "Variant cannot be saved for reports", "RefUrl": "/notes/388626 "}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044 "}, {"RefNumber": "383053", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Update termination for final settlement with message MN248", "RefUrl": "/notes/383053 "}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914 "}, {"RefNumber": "433752", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error when checking for release to accounting", "RefUrl": "/notes/433752 "}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739 "}, {"RefNumber": "400898", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation bus.vol. data, incomes: sel. by arrangemt type", "RefUrl": "/notes/400898 "}, {"RefNumber": "425755", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Sel. rebate arrangement by arrangmt type, arrangmt calendar", "RefUrl": "/notes/425755 "}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893 "}, {"RefNumber": "73214", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settl.:Retrospec.compltn/recompltn of busin.vol.data", "RefUrl": "/notes/73214 "}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864 "}, {"RefNumber": "200859", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Terms of Payment not transferred", "RefUrl": "/notes/200859 "}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716 "}, {"RefNumber": "386665", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of document index S111", "RefUrl": "/notes/386665 "}, {"RefNumber": "396334", "RefComponent": "MM-PUR-VM", "RefTitle": "Selection according to document date in RMEBEIN3", "RefUrl": "/notes/396334 "}, {"RefNumber": "408521", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN495 (update simulation), MN514 (detailed statement)", "RefUrl": "/notes/408521 "}, {"RefNumber": "399118", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msg NAA045 list output for arrangemts (RWMBON02, Trans.MEB5)", "RefUrl": "/notes/399118 "}, {"RefNumber": "316723", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error log for settlement run", "RefUrl": "/notes/316723 "}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831 "}, {"RefNumber": "382939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in agreement maintenance debit-side settlmt accounting", "RefUrl": "/notes/382939 "}, {"RefNumber": "139148", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance problems when deleting PO items", "RefUrl": "/notes/139148 "}, {"RefNumber": "136863", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "New DB secondary index AEB f.table EKBO", "RefUrl": "/notes/136863 "}, {"RefNumber": "112413", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: units of measure", "RefUrl": "/notes/112413 "}, {"RefNumber": "302145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "RMCENEUA: PIS, business volume data updated repeatedly", "RefUrl": "/notes/302145 "}, {"RefNumber": "132071", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for collectve purchase order", "RefUrl": "/notes/132071 "}, {"RefNumber": "113655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deviating tax calculation in credt memos", "RefUrl": "/notes/113655 "}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295 "}, {"RefNumber": "147939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance recompil.bus.vol.data/Subs.BV update", "RefUrl": "/notes/147939 "}, {"RefNumber": "128279", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Automatic deletion business volume data during recompilation", "RefUrl": "/notes/128279 "}, {"RefNumber": "386157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Shrt dmp CONVT_OVERFLOW or BCD_FIELD_OVERFLOW durng settlmnt", "RefUrl": "/notes/386157 "}, {"RefNumber": "168242", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 4.0B", "RefUrl": "/notes/168242 "}, {"RefNumber": "114111", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of incomes from settlement documents RWMBON07", "RefUrl": "/notes/114111 "}, {"RefNumber": "198340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incor bus.vol assgmt if doc.date not in arrgmt perd", "RefUrl": "/notes/198340 "}, {"RefNumber": "392055", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Search help attachment in report RMCE0900", "RefUrl": "/notes/392055 "}, {"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199 "}, {"RefNumber": "394619", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/394619 "}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580 "}, {"RefNumber": "154601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequnt settlmnt:external data transfr cancellatn", "RefUrl": "/notes/154601 "}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047 "}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279 "}, {"RefNumber": "215124", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Del.ind. of settled vol.rebate cond. can be changed", "RefUrl": "/notes/215124 "}, {"RefNumber": "171902", "RefComponent": "MM-PUR", "RefTitle": "Update of update BV data for return item incorrect", "RefUrl": "/notes/171902 "}, {"RefNumber": "126764", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS - GR with PO generation and normal item", "RefUrl": "/notes/126764 "}, {"RefNumber": "175138", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "PURCHIS-GR w/ generation of dely-volme-based rebate", "RefUrl": "/notes/175138 "}, {"RefNumber": "175160", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS-GR for several PO items with PO generation", "RefUrl": "/notes/175160 "}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134 "}, {"RefNumber": "175526", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-b.rebate:Incor.dec.plcs.in amnt.of cond.recs", "RefUrl": "/notes/175526 "}, {"RefNumber": "162897", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect detailed statement", "RefUrl": "/notes/162897 "}, {"RefNumber": "105333", "RefComponent": "MM-IM", "RefTitle": "GR to returns item, neg. delivery costs, M7050", "RefUrl": "/notes/105333 "}, {"RefNumber": "151398", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS -double update for deliv.cst fr several POs", "RefUrl": "/notes/151398 "}, {"RefNumber": "128070", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: error message MN514", "RefUrl": "/notes/128070 "}, {"RefNumber": "118087", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message M8001", "RefUrl": "/notes/118087 "}, {"RefNumber": "105895", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Crrncy trnsltin busnss vlme comprson+agremnt incor.", "RefUrl": "/notes/105895 "}, {"RefNumber": "104888", "RefComponent": "IS-R-LG-IM", "RefTitle": "Reading article master w. value-based inventry mgmt", "RefUrl": "/notes/104888 "}, {"RefNumber": "98843", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:00015(value of field is negative)", "RefUrl": "/notes/98843 "}, {"RefNumber": "117265", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Message MN589", "RefUrl": "/notes/117265 "}, {"RefNumber": "108139", "RefComponent": "MM-IM-GR", "RefTitle": "GR reversed incorrectly wth negative delivery costs", "RefUrl": "/notes/108139 "}, {"RefNumber": "105687", "RefComponent": "MM-PUR-RFQ", "RefTitle": "ME41: Volume rebate indicator from vendor master", "RefUrl": "/notes/105687 "}, {"RefNumber": "102392", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsqunt settlemnt:program termintn, missng exceptn", "RefUrl": "/notes/102392 "}, {"RefNumber": "123297", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message MN369", "RefUrl": "/notes/123297 "}, {"RefNumber": "104683", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: changes in Release 4.0", "RefUrl": "/notes/104683 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}