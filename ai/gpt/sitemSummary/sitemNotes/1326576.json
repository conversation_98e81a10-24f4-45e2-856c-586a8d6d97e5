{"Request": {"Number": "1326576", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 512, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016768282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001326576?language=E&token=F8AB1A8E4FDFC213F211346CFE2DE460"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001326576", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001326576/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1326576"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 41}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.07.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1326576 - SAP NetWeaver Systems Containing SAP ERP Software Components"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>General Solution Manager Configuration<br />You want to know how to configure your Product System in SMSY in Solution Manager for the SAP ERP Add-Ons on SAP NetWeaver (ERECRUIT, SEM-BW, FINBASIS or LSOFE).<br /><br />Scenario 1 (New installation on NW 7.0x, NW 7.3x, NW7.4, NW 7.5):<br />You are planning to install additional ERP software components (ERECRUIT, SEM-BW, FINBASIS or LSOFE) on a SAP NetWeaver 7.0x, 7.3x, 7.4, 7.5&#160;system and there is no predecessor installed for this components.<br /><br />Scenario 2 (Update to NW 7.0 EHP1):<br />You have installed additional ERP software components (ERECRUIT, SEM-BW, FINBASIS or LSOFE) on a SAP NetWeaver system, and you want to install Enhancement Package 1 for NetWeaver 7.0. During the installation, the ERP software components shall be updated to version 6.04 (EHP4).<br /><br />Scenario 3 (Update to NW 7.0 EHP2):<br />You have installed additional ERP software components (ERECRUIT, SEM-BW, FINBASIS or LSOFE) on a SAP NetWeaver system, and you want to install Enhancement Package 2 for NetWeaver 7.0. During the installation, the ERP software components shall be updated to version 6.05 (EHP5).<br /><br />Scenario 4 (Update to NW 7.0 EHP3):<br />You have installed additional ERP software components (ERECRUIT, SEM-BW, FINBASIS or LSOFE) on a SAP NetWeaver system, and you want to install Enhancement Package 3 for NetWeaver 7.0. During the installation, the ERP software components shall be updated to version 6.06 (EHP6).<br /><br />Scenario 5 (Upgrade to NW 7.3 EHP1):<br />You have already installed or updated additional ERP software components (ERECRUIT, SEM-BW, FINBASIS or LSOFE) in version EHP6 FOR SAP ERP 6.0 on EHP3 FOR SAP NETWEAVER 7.0 (NW 703).<br />The ABAP software component versions delivered with SAP EHP3 FOR SAP NETWEAVER 7.0 are identical to the ones delivered with SAP EHP1 FOR SAP NETWEAVER 7.3. However, you have the possibility to formally rewrite the system identity information covering product version and installed product instances, adjustments to new file system structure, and merge startprofile into instance profile to ensure working software lifecycle processes afterwards.<br /><br />Scenario 6 (Update to NW 7.4):<br />You have installed additional ERP software components (ERECRUIT, SEM-BW, FINBASIS or LSOFE) on a SAP NetWeaver system, and you want to upgrade to NetWeaver 7.4. During the upgrade, the ERP software components shall be updated to version 617/747 (EHP7).<br /><br /></p>\r\n<p>Scenario&#160;7 (Update to NW 7.5):<br />You have installed additional ERP software components (ERECRUIT, SEM-BW,&#160;FINBASIS or LSOFE) on a SAP NetWeaver system, and you want to upgrade to NetWeaver 7.5. During the upgrade, the ERP software components shall be updated to version 617/748 (EHP8).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SUM, update, Enhancement Packages, Multi-Product, SEM-BW, FINBASIS, LSOFE, ERECRUIT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>General Solution Manager Configuration</p>\r\n<p>For the standalone installation of ERECRUIT, FINBASIS, LSOFE and SEM-BW on a SAP NETWEAVER system (without ECC server) it is not longer required to configure the product system in Solution Manager as a ERP system.</p>\r\n<p><strong>Outdatet</strong><br />(If on the NetWeaver System the ERP Add-Ons SEM-BW, FINBASIS, LSOFE and or ERECRUIT are installed, the system must be configured on the Solution Manager in SMSY/LMDB as an SAP ERP system and SAP NETWEAVER system to be able to create a valid STACK.XML within MOPZ/Maintenance Planner&#160;for EHP Installation or Upgrade. Reason is that the above mentioned Add-Ons are delivered as part of the SAP ERP product versions, but are also installable standalone on a SAP NETWEAVER system (without ECC server).)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>General Solution Manager Configuration LMDB</strong><br />During the Enhancement Package Installation the calculation of the required software components is performed as part of the maintenance process in the Maintenance Optimizer (MOPZ) / Maintenance Planner.<br />The upgrade requires the file Stack.xml, which must be created using the MOPZ/MP. Due to the component structure of SEM-BW, FINBASIS, LSOFE, and ERECRUIT, the product system within LMDB on Solution Manager must be <strong>no longer</strong> modeled as an SAP ERP system if installed on standalone NetWeaver.</p>\r\n<p>Details you could find on Maintenance Planning Guide:&#160;<a target=\"_blank\" href=\"https://blogs.sap.com/2017/02/24/sap-solution-manager-special-cases-in-installation-and-upgrade/\">https://blogs.sap.com/2017/02/24/sap-solution-manager-special-cases-in-installation-and-upgrade/</a></p>\r\n<p><strong>Running ERECRUIT, FINBASIS, LSOFE and SEM-BW on standalone NetWeaver:</strong></p>\r\n<ul>\r\n<li>For SAP BW customers running&#160;the <strong>SAP SEM</strong> scenario a new process has been enabled: SAP Note <a target=\"_blank\" href=\"/notes/1927083\">1927083<br /></a>You can find a summary of all supported upgrade paths for<strong>&#160;</strong>SEM-BW in<strong>&#160; </strong><a target=\"_blank\" href=\"https://support.sap.com/content/dam/launchpad/en_us/pam/pam-essentials/TIP/Upgrade_BW_7 4x_ABAP_EHP8.pdf\">https://support.sap.com/content/dam/launchpad/en_us/pam/pam-essentials/TIP/Upgrade_BW_7%204x_ABAP_EHP8.pdf</a></li>\r\n<li>Customers running <strong>SAP E-RECRUITING</strong> standalone scenario a new process has been enabled: SAP Note&#160;<a target=\"_blank\" href=\"/notes/2418598\">2418598</a></li>\r\n<li>Customers running&#160;<strong>SAP FSCM&#160;</strong>standalone scenario a new process has been enabled: SAP Note&#160;<a target=\"_blank\" href=\"/notes/2418718\">2418718</a></li>\r\n<li>Customers running <strong>SAP LEARNING SOLUTION</strong> standalone scenario a new process has been enabled: SAP Note&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/notes /2418699\">2418699</a></li>\r\n</ul>\r\n<p><strong>Running ERECRUIT, FINBASIS, LSOFE and SEM-BW on ERP System (ECC Server):</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>SAP ERP RELEASE</td>\r\n<td>TECHNICAL USAGE</td>\r\n</tr>\r\n<tr>\r\n<td>SAP ERP 6.0</td>\r\n<td>\r\n<p>SAP E-Recruiting<br />SAP FSCM<br />SAP Learning Sol-Frontend ABAP<br />SAP SEM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>EHP2 FOR SAP ERP 6.0</td>\r\n<td>\r\n<p>Finbasis<br />Learning Solution&#160;<br />SAP SEM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>EHP3 FOR SAP ERP 6.0</td>\r\n<td>\r\n<p>SAP E-Recruiting<br />Finbasis<br />SAP Learning Sol-Frontend ABAP<br />SAP SEM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>EHP4 FOR SAP ERP 6.0</td>\r\n<td>\r\n<p>SAP E-Recruiting<br />Finbasis<br />SAP Learning Sol-Frontend ABAP<br />SAP SEM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>EHP5 FOR SAP ERP 6.0</td>\r\n<td>\r\n<p>SAP E-Recruiting<br />Finbasis<br />SAP Learning Sol-Frontend ABAP<br />SAP SEM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>EHP6 FOR SAP ERP 6.0</td>\r\n<td>\r\n<p>SAP E-Recruiting<br />Finbasis<br />Learning Solution - ABAP only<br />SAP SEM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>EHP7 FOR SAP ERP 6.0</td>\r\n<td>\r\n<p>E-Recruiting Standalone<br />Finbasis Standalone<br />Learning Solution Standalone<br />SAP SEM Standalone</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>EHP8 FOR SAP ERP 6.0</td>\r\n<td>\r\n<p>E-Recruiting Standalone<br />Finbasis Standalone<br />Learning Solution Standalone<br />SAP SEM Standalone</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br /><strong><br /></strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PE-LSO-TM (Training Management)"}, {"Key": "Other Components", "Value": "SV-SMG-LDB (Landscape Management Database (LMDB))"}, {"Key": "Other Components", "Value": "FIN-SEM (Strategic Enterprise Management)"}, {"Key": "Other Components", "Value": "XX-SER-REL (Business Suite and SAP S/4HANA Release Information)"}, {"Key": "Other Components", "Value": "PA-ER (E-Recruiting)"}, {"Key": "Other Components", "Value": "BC-UPG-MP (Maintenance Planning Tools)"}, {"Key": "Other Components", "Value": "SV-SMG-MAI (<PERSON><PERSON><PERSON> replaced by Maintenance Planner: BC-UPG-MP)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041903)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D039661)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001326576/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326576/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1857002", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Difference between Netweaver 7.0 EHP3 and 7.3 EHP1", "RefUrl": "/notes/1857002"}, {"RefNumber": "438520", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for FINBASIS add-on", "RefUrl": "/notes/438520"}, {"RefNumber": "1927083", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP NetWeaver Systems with SEM-BW", "RefUrl": "/notes/1927083"}, {"RefNumber": "186299", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for SEM-BW add-on", "RefUrl": "/notes/186299"}, {"RefNumber": "1816146", "RefComponent": "BC-UPG-MP", "RefTitle": "Correction of installed software information (CISI)", "RefUrl": "/notes/1816146"}, {"RefNumber": "1752914", "RefComponent": "SV-SMG-MAI", "RefTitle": "LMDB/LVSM: ERECRUIT, LSOFE, SEM-BW, FINBASIS installation", "RefUrl": "/notes/1752914"}, {"RefNumber": "1699482", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP SEM 634 - installation on SAP NetWeaver 7.30", "RefUrl": "/notes/1699482"}, {"RefNumber": "1681435", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1681435"}, {"RefNumber": "1681434", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade of SAP SEM 634 NW730 to SAP SEM 736 NW731", "RefUrl": "/notes/1681434"}, {"RefNumber": "1619800", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Netweaver Systems containing SAP ERP Software Components", "RefUrl": "/notes/1619800"}, {"RefNumber": "1531022", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Deployment options for SAP SEM based on SEM-BW 6.04", "RefUrl": "/notes/1531022"}, {"RefNumber": "1456663", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAINT: Enhancement Package 5 components on NW 7.02", "RefUrl": "/notes/1456663"}, {"RefNumber": "1347387", "RefComponent": "BC-EHP-INS-TLA", "RefTitle": "NW 7.0 EHP1: Error when including ERP components in EHPI", "RefUrl": "/notes/1347387"}, {"RefNumber": "1273591", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of Enhancement Package 2 for SAP NetWeaver 700", "RefUrl": "/notes/1273591"}, {"RefNumber": "1142832", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of Enhancement Package 1 for SAP NetWeaver 700", "RefUrl": "/notes/1142832"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2573471", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM stops in PHASE PREP_INPUT_CHECK/READDATA_EXP with error: \"Could not find suitable DVDs for product *NW* required for the procedure\"", "RefUrl": "/notes/2573471 "}, {"RefNumber": "2832459", "RefComponent": "BC-UPG-MP", "RefTitle": "Checks after phase PREP_INIT/VERSCHK_INI were negative!\".  \"Start Release configuration is not supported", "RefUrl": "/notes/2832459 "}, {"RefNumber": "2418699", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP NetWeaver Systems with LSOFE", "RefUrl": "/notes/2418699 "}, {"RefNumber": "2418718", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP NetWeaver Systems with FINBASIS", "RefUrl": "/notes/2418718 "}, {"RefNumber": "550993", "RefComponent": "PA-ER", "RefTitle": "Release strategy for ERECRUIT add-on", "RefUrl": "/notes/550993 "}, {"RefNumber": "438520", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for FINBASIS add-on", "RefUrl": "/notes/438520 "}, {"RefNumber": "1927083", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP NetWeaver Systems with SEM-BW", "RefUrl": "/notes/1927083 "}, {"RefNumber": "1816146", "RefComponent": "BC-UPG-MP", "RefTitle": "Correction of installed software information (CISI)", "RefUrl": "/notes/1816146 "}, {"RefNumber": "1456663", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAINT: Enhancement Package 5 components on NW 7.02", "RefUrl": "/notes/1456663 "}, {"RefNumber": "1752914", "RefComponent": "SV-SMG-MAI", "RefTitle": "LMDB/LVSM: ERECRUIT, LSOFE, SEM-BW, FINBASIS installation", "RefUrl": "/notes/1752914 "}, {"RefNumber": "1273591", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of Enhancement Package 2 for SAP NetWeaver 700", "RefUrl": "/notes/1273591 "}, {"RefNumber": "1681434", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade of SAP SEM 634 NW730 to SAP SEM 736 NW731", "RefUrl": "/notes/1681434 "}, {"RefNumber": "1142832", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of Enhancement Package 1 for SAP NetWeaver 700", "RefUrl": "/notes/1142832 "}, {"RefNumber": "1619800", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Netweaver Systems containing SAP ERP Software Components", "RefUrl": "/notes/1619800 "}, {"RefNumber": "1531022", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Deployment options for SAP SEM based on SEM-BW 6.04", "RefUrl": "/notes/1531022 "}, {"RefNumber": "1699482", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP SEM 634 - installation on SAP NetWeaver 7.30", "RefUrl": "/notes/1699482 "}, {"RefNumber": "186299", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for SEM-BW add-on", "RefUrl": "/notes/186299 "}, {"RefNumber": "1347387", "RefComponent": "BC-EHP-INS-TLA", "RefTitle": "NW 7.0 EHP1: Error when including ERP components in EHPI", "RefUrl": "/notes/1347387 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FINBASIS", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "LSOFE", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "ERECRUIT", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}