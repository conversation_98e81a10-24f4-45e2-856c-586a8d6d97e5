{"Request": {"Number": "1710236", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 294, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017426712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001710236?language=E&token=06E64DA52505E2311717B43DAE9FBED5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001710236", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001710236/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1710236"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.09.2019"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DP-DXC"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA Direct Extractor Connector"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Provisioning - please select one of the subcomponents", "value": "HAN-DP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Direct Extractor Connector", "value": "HAN-DP-DXC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DP-DXC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1710236 - SAP HANA DXC: DataSource Restrictions"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>There are a very small number of DataSources that are not supported for use with SAP HANA Direct Extractor Connection (DXC).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />DXC DEC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>Disclaimer: The feature which is mentioned in this note has been switched off with SAP HANA 2.0 SPS05. As an alternative Technology you may use e.g. HANA SDI to extract data from an ERP system into a SAP HANA Database.</strong></p>\r\n<p><br />1) SAP HANA does not presently provide functionality that is found in SAP BW for handling inventory movements data. Specifically, SAP BW includes special functionality known as \"non-cumulative key figures\" and \"non-cumulative cubes\". Data provided by certain DataSources (particularly ones related to inventory movements) requires the previously mentioned functionality in the receiving system (i.e. SAP BW) for correct data in reports. Therefore, since this required functionality is not presently available in SAP HANA, certain DataSources are not supported with DXC.</p>\r\n<p>2) Hierarchy DataSources (&#8220;Hierarchy Nodes (HIER)&#8221;, e.g. Business Content DataSources ending with &#8220;_HIER&#8221;) can&#8217;t be utilized directly by DXC.</p>\r\n<p>This is because of the two following reasons:</p>\r\n<p>- Hierarchy DataSources transfer hierarchies in separate tables / segments (hierarchy header, hierarchy nodes, &#8230;) and were originally designed with a BW InfoObject in mind as the target. As an example, the respective hierarchy node segment table doesn&#8217;t contain the hierarchy name that is loaded. In the BW InfoObject hierarchy loading services, this relation is made to finally store a coherent hierarchy on BW side. DXC doesn&#8217;t provide such functionality so that no coherent hierarchy transfer is possible.</p>\r\n<p>- The other aspect is that, when loading into BW, and performing recurring loads of the same hierarchy, new BW internal NODEIDs are created for every load; therefore a semantically identical node could be considered as duplicate if used directly by DXC. Only when loading hierarchy data into an InfoObject in SAP BW, the necessary information is added.</p>\r\n<p>Thus, in order to utilize hierarchy DataSources with DXC, the procedure would be to first perform the actions needed to activate the coresponding hierarchy InfoObject in the embedded BW (embedded inside the SAP Business Suite system). &#160;Next, this hierarchy InfoObject should be configured as an export DataSource, which in turn should be used as the DataSource for DXC. &#160;Thus, loading hierarchy data into SAP HANA using DXC would be a two-step process: first, load the hierachy InfoObject in the embedded BW, and then perform the DXC extraction from that same hierarchy InfoObject (export DataSource) into the SAP HANA In-Memory DSO (which was generated automatically by DXC). Also, please note that the hierarchy data is \"flattened\" in the SAP HANA In-Memory DSO. &#160;Each record contains data about the parent node relationship. This data can be utilized in data modeling in SAP HANA.</p>\r\n<p>As a prerequisite for this workaround, implementation of SAP Note&#160;2383689 is required in your system. Please follow then carefully the manual post-implementation steps as described in SAP Note 2383689 (see section \"Caution: You have to perform this manual post-implementation step manually and separately in each system after you have imported the Note to implement.\")</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br />The following DataSources are not supported for use with SAP HANA Direct Extractor Connection (DXC):<br /><br />0CFM_INIT_POSITIONS<br />0CML_INIT_BUDAT<br />0CML_INIT_DDISPO<br />0CML_INIT_DFAELL<br />0RE_1<br />0RE_3<br />0SCM_LIM_1<br />2LIS_02_SRV<br />2LIS_03_BX<br />2LIS_03_S197<br />2LIS_03_S198<br />2LIS_40_S278<br />2LIS_03_BF<br />0BV_BEV1_EM_CUSTSTO_BW_GET_TD<br />0RE_2<br />0RE_3<br />0RF_REASLOC_ATTR<br />0SCM_LIM_1<br />0SCM_LIM_2<br /><br /></p>\r\n<p>All DataSource of type &#8220;Hierarchy Nodes (HIER)&#8221;, e.g. Business Content DataSources ending with &#8220;_HIER&#8221;, see 2) above.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027954)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027026)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001710236/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001710236/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2383689", "RefComponent": "BW-WHM-DST", "RefTitle": "SP36: Enabling Hierarchy Export DataSource for Direct Extractor Connection (DXC)", "RefUrl": "/notes/2383689"}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125"}, {"RefNumber": "1501907", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulative cubes, validity interval,non-cumulative logic", "RefUrl": "/notes/1501907"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2812402", "RefComponent": "HAN-DB", "RefTitle": "Activating DSO in DXC Application Fails With \"Feature Not Supported\"", "RefUrl": "/notes/2812402 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125 "}, {"RefNumber": "1501907", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulative cubes, validity interval,non-cumulative logic", "RefUrl": "/notes/1501907 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "HDB", "From": "1.00", "To": "1.00", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}