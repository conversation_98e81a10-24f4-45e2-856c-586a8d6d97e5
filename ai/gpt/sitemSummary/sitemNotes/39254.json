{"Request": {"Number": "39254", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 241, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014406692017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000039254?language=E&token=3740EB1E526B19D7E162131EEC9AF47D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000039254", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000039254/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "39254"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-IV-IB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Intercompany Billing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Processing Billing Documents", "value": "SD-BIL-IV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-IV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Intercompany Billing", "value": "SD-BIL-IV-IB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-IV-IB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "39254 - Cross-company sales to external customer"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>How is the profit center and, if necessary, the partner profit center determined during cross-company code processing in the sales module SD?</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>0KEL 0KEM cross-company COPCA_PARTNER_GSBER_PRCTR</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Cross-company code sales</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>1. Sales order (in the selling company code)</strong></p>\r\n<p>The profit center in the sales order item belongs to the supplying company code. The profit center is proposed from the material master for the supplying plant but can be replaced by another profit center of the supplying company code (manually, actual account assignment, PCA substitution).&#x00A0;This profit center is used as the profit center in goods issue posting and for internal billing documents.</p>\r\n<p>When a profit center is entered manually, a check is performed to see whether the profit center entered belongs to the controlling area of the supplying company code. With profit center substitution (transactions 0KEL and 0KEM), both the 'Profit Center' field (PRCTR) and the 'Profit Center for Billing' field (PCTRF) are filled when you create the sales order.</p>\r\n<p>The functions of profit center substitution are described in SAP Note 167912.<br />Please see also SAP Notes 815972 ('PCA substitution for cross-company-code sale') and 916973 ('Incorr partner PC in intercompany billing before ext billing') for the determination of the field for the billing document profit center (PCTRF).</p>\r\n<p>&#x00A0;</p>\r\n<p>History:<br />In the case of cross-company sales, profit center substitution is not active when the system creates sales orders (in Release 3.0C - 4.0B). Substitution can derive a profit center from sales order data. This data belongs to the selling company code (for example, the customer number), and not to the supplying company code. Therefore, a substitution is not useful. Furthermore, when the system creates sales orders in the case of cross-company sales (in Releases 3.0C - 4.5A), profit center substitution does NOT contain internal billing document data (internal customer number, internal sales area) in addition to the data for external sales. These two functions can be implemented in the aforementioned releases using SAP Note 112974.</p>\r\n<p><br /><strong>2. Goods issue (in the supplying company code)</strong></p>\r\n<p>The goods issue copies the profit center from the sales order.</p>\r\n<p>History:<br />Up to Release 3.0B, see also SAP Note 37800.</p>\r\n<p><br /><br /><strong>3. Customer billing document (in the selling company code)</strong></p>\r\n<p>During the creation of the customer billing document in the cross-company-code scenario, the profit center (PRCTR) is always deleted and redetermined. The reason for this is that the profit center transferred from the sales order is the profit center of the supplying view, not the selling view. This means that it cannot be used for the billing document.</p>\r\n<p>To redetermine the profit center, maintain and activate a substitution rule in Customizing for Profit Center Accounting (transactions 0KEL and 0KEM). If this has already been done at the event 'Create Sales Order' and the field for the billing document profit center (VBAP-PCTRF) has therefore been filled, this profit center is copied to the external billing document item.</p>\r\n<p>However, you may want the profit center to be copied from the order item. Please see SAP Notes 106875 and 713228 in this regard. See also SD note 1532865 - FAQ: Profit center in billing document</p>\r\n<p><br /><br /><strong>4. Intercompany billing (in the supplying company code)</strong></p>\r\n<p>Intercompany billing copies the profit center from the sales order (VBAP-PRCTR), which corresponds to the profit center of the delivery view.</p>\r\n<p>To correctly determine the partner profit center for intercompany billing up to and including Release 4.6C, you need to create a customer billing document (or a customer credit memo) before intercompany billing. With Release 4.7, the 'Profit Center for Billing' field was added again to the table for order items (VBAP-PCTRF). Therefore, you no longer need to adhere to the time-based sequence of creating an external customer billing document before an internal customer billing document if the \"Profit Center for Billing\" field is already filled when you create the sales order. For more information, please see SAP Note 815972 and, depending on the release level, SAP Note 916973.<br /><br />To read the partner profit center for the intercompany billing, the preparations for consolidation must be maintained (OCCL, OCCI - see SAP Note 161277, too). If CRM is active, the R/3 routines for determining the partner profit center (transaction OCCL) do not work. For more information, see SAP Note 570567 as well.</p>\r\n<p>When the intracompany billing is saved, an IDoc is created. This is used as the basis for the posting of the invoice receipt in the selling company code. When the IDoc is created, transaction VOFC (change view 'Billing document: Switch for acc. elements of IV to CO') is executed. As a result, if the IDoc type INVOIC02 is used, the following account assignments are copied from the billing document:</p>\r\n<ul>\r\n<li>Cost center = KOSTL (segment E1EDP30 with qualifier 045)</li>\r\n</ul>\r\n<ul>\r\n<li>Profitability segment = PAOBJNR (segment E1EDP30 with qualifier 046)</li>\r\n</ul>\r\n<ul>\r\n<li>Work breakdown structure = PSPNR (segment E1EDP30 with qualifier 047)</li>\r\n</ul>\r\n<ul>\r\n<li>Profit center = PRCTR (segment E1EDP30 with qualifier 048)</li>\r\n</ul>\r\n<ul>\r\n<li>Business area = GSBER (segment E1EDP30 with qualifier 049)</li>\r\n</ul>\r\n<ul>\r\n<li>Partner profit center = PPRCTR (segment E1EDP30 with qualifier 078)</li>\r\n</ul>\r\n<p>In the customizing for intercompany billing under \"Automatic Posting to Vendor Account (SAP-EDI)\" (transaction VOFC), activate the process for copying account assignments from the external billing document. You must activate the billing type for the intracompany billing and the billing type for the reversal for the intracompany billing.</p>\r\n<p>&#x00A0;</p>\r\n<p>History:<br />If the external billing document is created before the internal billing document, the partner profit center is determined by reading the selling profit center from the customer billing document data (or the customer credit memo in the case of a return). However, the system does not read the table VBRP of the billing document items. Instead, it reads the PrintView table VBDPR that exists in SD. <br />To read the partner profit center for the intercompany billing, the preparations for consolidation must be maintained (OCCL, OCCI - see SAP Note 161277, too). <br />Up to and including Release 4.6C, the external billing document must be created before the internal billing document. Up to Release 4.5B, please refer to the program corrections contained in SAP Note 114954.<br />To handle cross-company sales without intercompany billing, please refer to SAP Note 69314 up to and including Release 4.0B.</p>\r\n<p><br /><br /><strong>5. EDI invoice receipt (in selling company code)</strong></p>\r\n<p>Profit center: As of Release 4.0, you can use transaction VOFC to make a setting so that account assignments are copied from the customer billing document for the automatic EDI invoice receipt. Transaction VOFC is executed when the intracompany billing is saved and is reflected in the IDoc, which is used to post the automatic EDI invoice receipt. (See also point 4 - 'Intercompany Billing'). For this, the customer billing document must be available before the internal billing document.<br />In the case of the posting of an invoice receipt with EDI where the intercompany billing has already been posted (document type IV) but the customer billing document has not (document type F2), please see SAP Note 607799.<br /><br />To set the partner profit center for EDI invoice receipt, note the following:&#x00A0;Automatic determination is not possible during the invoice receipt for intercompany billing. The partner profit center can then be set using other standard options.</p>\r\n<p>In addition, note that the field for the partner profit center in the field status variant of the accounts concerned must be ready for input (transactions OB14 and OB41).</p>\r\n<p>&#x00A0;</p>\r\n<p>History:<br />SAP Note 166462.<br />If, in the case of cross-company processing, the profit center is not copied from the customer billing document for the automatic EDI invoice receipt, refer to SAP Note 134656 (Releases 3.0D - 3.1I).</p>\r\n<p>&#x00A0;</p>\r\n<p><strong>Information about IDoc types 'INVOIC01' and 'INVOIC02'<br /></strong>As of Release 4.0B, a new IDoc type INVOIC02 is available in addition to the IDoc type INVOIC01. The difference between the IDoc types INVOIC02 and INVOIC01 is that the type INVOIC02 also contains the E1EDP30 segment for additional account assignments during intercompany billing.<br />If the IDoc type INVOIC02 is used (definition in the partner profiles of the sender), at present the account assignments for the project, profit center, cost center, profitability segment, and business area are automatically copied to the FI document from the customer billing document by transaction VOFC. Therefore, the advantage of this IDoc type in comparison with the IDoc type INVOIC01 is that the account assignments are already contained in the IDoc (and therefore in every process). Consequently, the standard customizing for the derivation of an additional account assignment in the table T076K (transaction OBCC) or the user EXIT_SAPLIEDI_002 may no longer be required at all if the six account assignments from the customer billing document (or the E1EDP30 segment) are sufficient.<br /><br />We recommend using the IDoc type INVOIC02 with the message INVOIC when you post intercompany billings with EDI or EDI invoices in FI. For account assignments in INVOIC02, see also SAP Note 170809.</p>\r\n<p><br />The account assignment options of INVOIC01 and INVOIC02 are compared below:<br /><br />INVOIC02<br />1. TC OBCC - Table T076K (with SAP Note 170809)<br />2. TC VOFC (additional account assignments INVOIC02, segment E1EDP30)<br />3. EXIT_SAPLIEDI_002 (with SAP Note 170809)<br /><br />INVOIC01<br />1. TC OBCC - Table T076K<br />2. EXIT_SAPLIEDI_002<br /><br />If you do not use the user exit and if both account assignments from segment E1EDP30 (only for INVOIC02) and Customizing account assignments from the table T076K exist, the relevant account assignment from the E1EDP30 segment has priority over the customizing account assignment. However, if no account assignment exists in the E1EDP30 segment but is defined in customizing in the table T076K, the customizing entry is transferred to the FI document.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EC-PCA (Profit Center Accounting)"}, {"Key": "Other Components", "Value": "SD-EDI-OM-IV (Outbound Messages Invoices)"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "TRANSAKTIONEN"}, {"Key": "Transaction codes", "Value": "IDOC"}, {"Key": "Transaction codes", "Value": "PCA"}, {"Key": "Transaction codes", "Value": "0KEL"}, {"Key": "Transaction codes", "Value": "OCCI"}, {"Key": "Transaction codes", "Value": "VOFC"}, {"Key": "Transaction codes", "Value": "0KEM"}, {"Key": "Transaction codes", "Value": "OCCL"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D025743)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D025743)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000039254/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000039254/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "916973", "RefComponent": "EC-PCA-ACT", "RefTitle": "Incorr partner PC in intercompany billing before ext billing", "RefUrl": "/notes/916973"}, {"RefNumber": "815972", "RefComponent": "EC-PCA-ACT", "RefTitle": "PCA substitution for cross-company-code sale", "RefUrl": "/notes/815972"}, {"RefNumber": "713228", "RefComponent": "SD-BIL-IV-IB", "RefTitle": "Cross-company: Profit center of customer billing doc. (2)", "RefUrl": "/notes/713228"}, {"RefNumber": "69314", "RefComponent": "SD-BIL-IV-IB", "RefTitle": "Cross-company without intercompany billing", "RefUrl": "/notes/69314"}, {"RefNumber": "659590", "RefComponent": "SD-EDI-OM-IV", "RefTitle": "EDI: Stock transfer and cross-company sales", "RefUrl": "/notes/659590"}, {"RefNumber": "607799", "RefComponent": "SD-EDI-OM-IV", "RefTitle": "Posting to vendor account: ICB before F2", "RefUrl": "/notes/607799"}, {"RefNumber": "570567", "RefComponent": "EC-PCA-ACT", "RefTitle": "CRM:Partner profit center with internal billing documents", "RefUrl": "/notes/570567"}, {"RefNumber": "48941", "RefComponent": "SD-BIL-IV", "RefTitle": "Profit center substitution for credit/debit memo", "RefUrl": "/notes/48941"}, {"RefNumber": "410532", "RefComponent": "SD-EDI-OM-IV", "RefTitle": "IDoc: Account assignment for order-related intercompany billing", "RefUrl": "/notes/410532"}, {"RefNumber": "37800", "RefComponent": "MM-IM", "RefTitle": "MMIM: Profit center not set", "RefUrl": "/notes/37800"}, {"RefNumber": "170809", "RefComponent": "FI-AP-AP-M", "RefTitle": "EDI/INVOIC: CO, PCA-, CO-PA accnt assignments incomplete", "RefUrl": "/notes/170809"}, {"RefNumber": "1693521", "RefComponent": "EC-PCA-ACT", "RefTitle": "FI invoice receipt: Partner profit center goes missing", "RefUrl": "/notes/1693521"}, {"RefNumber": "167912", "RefComponent": "EC-PCA", "RefTitle": "PCA substitution call-up points for sales order/billing document", "RefUrl": "/notes/167912"}, {"RefNumber": "1677974", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization: IV before F2", "RefUrl": "/notes/1677974"}, {"RefNumber": "161277", "RefComponent": "EC-PCA-BS", "RefTitle": "Customizing transaction OCCL: Automatic reading of partner profit center", "RefUrl": "/notes/161277"}, {"RefNumber": "1532865", "RefComponent": "SD-BIL-IV", "RefTitle": "FAQ: Profit center in billing document", "RefUrl": "/notes/1532865"}, {"RefNumber": "1529073", "RefComponent": "SD-BIL-RR", "RefTitle": "Cross-company: Profit center transfer during GI posting", "RefUrl": "/notes/1529073"}, {"RefNumber": "134656", "RefComponent": "EC-PCA", "RefTitle": "Profit center for EDI invoice receipt", "RefUrl": "/notes/134656"}, {"RefNumber": "121599", "RefComponent": "SD-SLS", "RefTitle": "Account assignment in Cross Company sales", "RefUrl": "/notes/121599"}, {"RefNumber": "112974", "RefComponent": "SD-SLS", "RefTitle": "Subst. Prof.ctr subst.,create KDAUFf.cross-CC sales", "RefUrl": "/notes/112974"}, {"RefNumber": "106875", "RefComponent": "SD-BIL-IV-IB", "RefTitle": "Cross-company: Profit center for cust.billing doc.", "RefUrl": "/notes/106875"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "692187", "RefComponent": "SD-EDI-OM-IV", "RefTitle": "IDOC INVOIC: Profit center of internal billing document", "RefUrl": "/notes/692187 "}, {"RefNumber": "607799", "RefComponent": "SD-EDI-OM-IV", "RefTitle": "Posting to vendor account: ICB before F2", "RefUrl": "/notes/607799 "}, {"RefNumber": "1532865", "RefComponent": "SD-BIL-IV", "RefTitle": "FAQ: Profit center in billing document", "RefUrl": "/notes/1532865 "}, {"RefNumber": "161277", "RefComponent": "EC-PCA-BS", "RefTitle": "Customizing transaction OCCL: Automatic reading of partner profit center", "RefUrl": "/notes/161277 "}, {"RefNumber": "916973", "RefComponent": "EC-PCA-ACT", "RefTitle": "Incorr partner PC in intercompany billing before ext billing", "RefUrl": "/notes/916973 "}, {"RefNumber": "1693521", "RefComponent": "EC-PCA-ACT", "RefTitle": "FI invoice receipt: Partner profit center goes missing", "RefUrl": "/notes/1693521 "}, {"RefNumber": "815972", "RefComponent": "EC-PCA-ACT", "RefTitle": "PCA substitution for cross-company-code sale", "RefUrl": "/notes/815972 "}, {"RefNumber": "1677974", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization: IV before F2", "RefUrl": "/notes/1677974 "}, {"RefNumber": "410532", "RefComponent": "SD-EDI-OM-IV", "RefTitle": "IDoc: Account assignment for order-related intercompany billing", "RefUrl": "/notes/410532 "}, {"RefNumber": "659590", "RefComponent": "SD-EDI-OM-IV", "RefTitle": "EDI: Stock transfer and cross-company sales", "RefUrl": "/notes/659590 "}, {"RefNumber": "713228", "RefComponent": "SD-BIL-IV-IB", "RefTitle": "Cross-company: Profit center of customer billing doc. (2)", "RefUrl": "/notes/713228 "}, {"RefNumber": "1529073", "RefComponent": "SD-BIL-RR", "RefTitle": "Cross-company: Profit center transfer during GI posting", "RefUrl": "/notes/1529073 "}, {"RefNumber": "69314", "RefComponent": "SD-BIL-IV-IB", "RefTitle": "Cross-company without intercompany billing", "RefUrl": "/notes/69314 "}, {"RefNumber": "167912", "RefComponent": "EC-PCA", "RefTitle": "PCA substitution call-up points for sales order/billing document", "RefUrl": "/notes/167912 "}, {"RefNumber": "106875", "RefComponent": "SD-BIL-IV-IB", "RefTitle": "Cross-company: Profit center for cust.billing doc.", "RefUrl": "/notes/106875 "}, {"RefNumber": "570567", "RefComponent": "EC-PCA-ACT", "RefTitle": "CRM:Partner profit center with internal billing documents", "RefUrl": "/notes/570567 "}, {"RefNumber": "170809", "RefComponent": "FI-AP-AP-M", "RefTitle": "EDI/INVOIC: CO, PCA-, CO-PA accnt assignments incomplete", "RefUrl": "/notes/170809 "}, {"RefNumber": "121599", "RefComponent": "SD-SLS", "RefTitle": "Account assignment in Cross Company sales", "RefUrl": "/notes/121599 "}, {"RefNumber": "134656", "RefComponent": "EC-PCA", "RefTitle": "Profit center for EDI invoice receipt", "RefUrl": "/notes/134656 "}, {"RefNumber": "112974", "RefComponent": "SD-SLS", "RefTitle": "Subst. Prof.ctr subst.,create KDAUFf.cross-CC sales", "RefUrl": "/notes/112974 "}, {"RefNumber": "48941", "RefComponent": "SD-BIL-IV", "RefTitle": "Profit center substitution for credit/debit memo", "RefUrl": "/notes/48941 "}, {"RefNumber": "37800", "RefComponent": "MM-IM", "RefTitle": "MMIM: Profit center not set", "RefUrl": "/notes/37800 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}