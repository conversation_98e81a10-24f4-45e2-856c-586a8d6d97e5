{"Request": {"Number": "432027", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 238, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015063522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000432027?language=E&token=0C671DB2728BCDDDB14062550431594D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000432027", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000432027/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "432027"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.12.2002"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-OCS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)", "value": "BC-UPG-OCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "432027 - Strategy for using SAP Support Packages"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to know:</p> <UL><LI>At which times you are supposed to import Support Packages.</LI></UL> <UL><LI>Which activities must be taken into account for this.</LI></UL> <UL><LI>How you can supply your system environment with Support Packages.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP Support Packages, strategy of Support Packages, SAP patches</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Why is the regular updating of the system environment with Support Packages important ?</b><br /> <p></p> <UL><LI>Every Support Package contains <B>all</B> error corrections which were carried out in a certain period (currently generally within a month). These are on average 500 to 800. In the course of the release cycle the number of corrections will reduce.</LI></UL> <UL><LI>If you are several Support Packages away from the current status in the Support Package delivery (example: current status <B>SP16</B><B>,</B> your status is <B>SP10</B>), this brings the following disadvantages:</LI></UL> <UL><UL><LI>You want to include a larger error correction (e.g. note 12345) that is only delivered with SP14. Your software status for <B>SP10</B><B>,</B> however, is relatively far from the status <B>SP14</B><B>.</B> If you want to implement this note, you must also implement all <B>dependent</B> notes.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> --&gt; The older your software status is, the larger and more diverse the dependencies.</B></p> <UL><UL><LI>The SAP Development Support is all the more difficult the further you move from the current Support Package level.</LI></UL></UL> <UL><LI>For this reason, we recommend you to regularly equip your system environment with the <B>current</B> Support Package status<B>.</B><B>Only</B> implement notes into your system in <B>exceptional cases.</B> The manual installation of notes is not<B> a replacement</B> for importing a Support Package. This procedure has the following <B>advantages</B> for you:</LI></UL> <UL><UL><LI> <B>All</B> corrections that were generated up to the correction freeze of the Support Package were generated are imported into your system. You no longer have to implement notes manually. Your system is always up to date.</LI></UL></UL> <UL><UL><LI>New errors that appear can be eliminated by the Development Support much faster since they are considerably closer to the status of the SAP correction system, due to the fact that your system is up to date.</LI></UL></UL> <UL><LI>Tests occur before a Support Package is delivered. The most important transactions and core processes are then tested. Due to the large number of processes, you still need to <B>test</B> your <B>customer-specific</B> processes as soon as you have imported a Support Package. SAP offers the test organizer as a test support (free in the scope of supply). With the aid of this software you can describe your most important customer-specific transactions and core processes and evaluate them after the test. To do this, create a <B>test catalog</B> with the most important functions. Where the scope of the catalog is concerned, pay attention also to the period you have available for Support Package tests. This catalog is the basis for generating a <B>test plan</B> per Support Package test. (The description of the functions of the test organizer can be found in the online documentation.)</LI></UL> <p><br /></p> <b>Recommendations using SAP Support Packages in the project phase (before productive operation)</b><br /> <p></p> <UL><LI>Import <B>every</B> Support Package released by SAP into your test system. Update also the Basis, ABA and BW components, if required.</LI></UL> <UL><LI> <B>Then </B><B>test</B> your most important customer-specific transactions or core processes in accordance with your created test plan.</LI></UL> <UL><LI>Before the production start update your systems to the <B>newest</B> status and again test your customer-specific transactions or core processes with a new test plan.</LI></UL> <p><br /></p> <b>Recommendations for using SAP Support Packages in the productive operation</b><br /> <p></p> <UL><LI>Define a <B>strategy</B> in which periods you want to import a Support Package into your production system. We recommend a period of <B>3 to 6 months</B>.</LI></UL> <UL><LI>Always import several Support Packages in a <B>queue</B> into your test system. If there are exceptions, we will notify you of these in the corresponding note for the Support Package. (When importing ADD ONS other instructions may be decisive).</LI></UL> <UL><LI>Then carry out the <B>test</B> while using your test plan in the test system.</LI></UL> <UL><LI>After the successful test supply your entire environment with the Support Packages. It makes sense to distribute the Support Packages for all additional systems <B>from the test system.</B> Also import the adjustments which you may have created in the test system.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>--&gt; Test system, quality assurance system and production system should always have the same Support Package</B><B>status</B><B>.</B></p> <UL><LI>While installing the Support Packages, run a comparison via the transactions <B>SPDD</B> and <B>SPAU</B><B>. </B><B>I</B>f you carried out <B>none </B><B>of your </B>own <B>modifications,</B> select <B>\"Back to the standard\"</B> if the system requires a response per object.</LI></UL> <p><br /></p> <b>Recommendations for using SAP Support Packages</b><br /> <p></p> <UL><LI> <B>Case 1:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Your system has the status <B>SP10</B>. You want to update your system to status <B>SP15</B><B>,</B> but you have already implemented notes in advance which are only eliminated with <B>SP16</B><B>.</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Important information:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you update your system to SP15, all notes implemented in advance for SP16 are regarded as modifications and deactivated. After the upgrade you then have a clean SP15 status. The inactive modifications are saved in the versions database.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Procedure:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Update your system with SP11 to SP15. During the <B>SPAU</B><B> comparison</B> indicate for each implemented advance note for SP16 that you want to retain the modification. This is then flagged as an active version in your system and is available again.</p> <UL><LI> <B>Case 2:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You want to implement a note in advance. Use the Note Assistant in this case. This tool supports you in selecting and simultaneously installing the dependent notes. You can download the Note Assistant from our Service Marketplace:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://SERVICE.SAP.COM/NOTEASSISTANT<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Here you will also find additional information on this tool. <br /></p> <UL><LI> <B>Case 3:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You want to know which notes are contained in a Support Package.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Procedure:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Log on to your OSS system. Select the --&gt; SAP Patch Service and then --&gt; SAP Support Package components. Under the component searched for and the desired release you will find the notes broken down by Support Package number.</p> <UL><UL><LI>Navigate on the SAP Service Marketplace (service.sap.com/patches) to the list of Support Packages relevant to you. After selecting a Support Package you can have the notes contained in this Support Package and further information displayed.</LI></UL></UL> <p><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D001405"}, {"Key": "Processor                                                                                           ", "Value": "D000325"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000432027/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000432027/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000432027/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000432027/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000432027/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000432027/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000432027/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000432027/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000432027/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97630", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Familiar problems with patches 3.1H-4.0B", "RefUrl": "/notes/97630"}, {"RefNumber": "97629", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with patches Rel. 4.5", "RefUrl": "/notes/97629"}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620"}, {"RefNumber": "782140", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/782140"}, {"RefNumber": "675156", "RefComponent": "SCM-TEC", "RefTitle": "SAP APO 3.0A SP26: Release and information note", "RefUrl": "/notes/675156"}, {"RefNumber": "645197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/645197"}, {"RefNumber": "641748", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 25 for APO Release 3.0A", "RefUrl": "/notes/641748"}, {"RefNumber": "618337", "RefComponent": "SCM-TEC", "RefTitle": "APO of Support Package 24 for APO Release 3.0A", "RefUrl": "/notes/618337"}, {"RefNumber": "590056", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 23 for APO Release 3.0A", "RefUrl": "/notes/590056"}, {"RefNumber": "560595", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 22 for APO Release 3.0A", "RefUrl": "/notes/560595"}, {"RefNumber": "556962", "RefComponent": "BC-UPG-OCS", "RefTitle": "FAQ OCS: General information on Support Packages", "RefUrl": "/notes/556962"}, {"RefNumber": "532619", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 21 for APO Release 3.0A", "RefUrl": "/notes/532619"}, {"RefNumber": "523987", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/523987"}, {"RefNumber": "517492", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 20 for APO Release 3.0A", "RefUrl": "/notes/517492"}, {"RefNumber": "491882", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 19 for APO Release 3.0A", "RefUrl": "/notes/491882"}, {"RefNumber": "459985", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 18 for APO Release 3.0A", "RefUrl": "/notes/459985"}, {"RefNumber": "448518", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 17 for APO Release 3.0A", "RefUrl": "/notes/448518"}, {"RefNumber": "447925", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/447925"}, {"RefNumber": "438337", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 16 for APO Release 3.0A", "RefUrl": "/notes/438337"}, {"RefNumber": "400280", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Pkgs in Basis Release 6.10", "RefUrl": "/notes/400280"}, {"RefNumber": "173814", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages for Release 4.6", "RefUrl": "/notes/173814"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1672817", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note", "RefUrl": "/notes/1672817 "}, {"RefNumber": "782140", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/782140 "}, {"RefNumber": "560595", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 22 for APO Release 3.0A", "RefUrl": "/notes/560595 "}, {"RefNumber": "675156", "RefComponent": "SCM-TEC", "RefTitle": "SAP APO 3.0A SP26: Release and information note", "RefUrl": "/notes/675156 "}, {"RefNumber": "641748", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 25 for APO Release 3.0A", "RefUrl": "/notes/641748 "}, {"RefNumber": "532619", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 21 for APO Release 3.0A", "RefUrl": "/notes/532619 "}, {"RefNumber": "618337", "RefComponent": "SCM-TEC", "RefTitle": "APO of Support Package 24 for APO Release 3.0A", "RefUrl": "/notes/618337 "}, {"RefNumber": "517492", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 20 for APO Release 3.0A", "RefUrl": "/notes/517492 "}, {"RefNumber": "590056", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 23 for APO Release 3.0A", "RefUrl": "/notes/590056 "}, {"RefNumber": "173814", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages for Release 4.6", "RefUrl": "/notes/173814 "}, {"RefNumber": "491882", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 19 for APO Release 3.0A", "RefUrl": "/notes/491882 "}, {"RefNumber": "400280", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Pkgs in Basis Release 6.10", "RefUrl": "/notes/400280 "}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620 "}, {"RefNumber": "97630", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Familiar problems with patches 3.1H-4.0B", "RefUrl": "/notes/97630 "}, {"RefNumber": "97629", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with patches Rel. 4.5", "RefUrl": "/notes/97629 "}, {"RefNumber": "447925", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/447925 "}, {"RefNumber": "438337", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 16 for APO Release 3.0A", "RefUrl": "/notes/438337 "}, {"RefNumber": "448518", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 17 for APO Release 3.0A", "RefUrl": "/notes/448518 "}, {"RefNumber": "459985", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 18 for APO Release 3.0A", "RefUrl": "/notes/459985 "}, {"RefNumber": "556962", "RefComponent": "BC-UPG-OCS", "RefTitle": "FAQ OCS: General information on Support Packages", "RefUrl": "/notes/556962 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}