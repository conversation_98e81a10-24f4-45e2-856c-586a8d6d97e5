{"Request": {"Number": "1703201", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 429, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017416542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001703201?language=E&token=44E3F78184C581DEB269E0E59F185E15"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001703201", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001703201/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1703201"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.03.2017"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CZ-PS"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-PS-CZ"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Czech Republic", "value": "XX-CSC-CZ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-PS-CZ", "value": "XX-CSC-CZ-PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1703201 - Rep.Frwk. - Reporting Framework - New Common Tool"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The purpose of the Reporting Framework is to deliver functionality that can easily be replaced or enhanced by customer solution.<br />The solution is technically based on events, in combination with Customizing settings. This concept allows you to split the program logic into separate blocks, and the scope of delivery is therefore variable and depends on the type of report and the legal or customer-specific requirements.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Reporting Framework; Transaction IDREPFW_REP; Report IDREPFW_REPORTING; Management of Events; Transaction IDREPFW_EVN_MGMT; Function Group IDREPFW_MGMT_EVENTS; Number Range IDREPFW_RUNINR; IMG Structure Settings for Reporting Framework; Switch FILOC_REP_FRWK_SFWS_01; Dynamic Selection Criteria; View Cluster IDREPFW_SELPA_VC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The whole solution is stand alone and has only prerequisite to install Software Component SAP_FIN (starting Enhacement Package 7).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Please install corresponding Support Pack.<br /><br />The framework consists of the following parts:</p>\r\n<ul>\r\n<li>Main program that controls the entire reporting process (starts with a selection screen and ends with data output)</li>\r\n</ul>\r\n<ul>\r\n<li>Dynamic definition of selection criteria (Customizing settings)</li>\r\n</ul>\r\n<ul>\r\n<li>Enhancement technique: events</li>\r\n</ul>\r\n<ul>\r\n<li>Database framework for storing data</li>\r\n</ul>\r\n<ul>\r\n<li>Output framework (ALV - predefined solution)</li>\r\n</ul>\r\n<ul>\r\n<li>XML generation (Customizing settings for mapping ) via XSLT</li>\r\n</ul>\r\n<ul>\r\n<li>Adobe output</li>\r\n</ul>\r\n<ul>\r\n<li>Customizing</li>\r\n</ul>\r\n<ul>\r\n<li>Cross-check values</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-SK-PS (use FI-LOC-PS-SK)"}, {"Key": "Other Components", "Value": "XX-CSC-HU-PS (use FI-LOC-PS-HU)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I044454)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I071485)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001703201/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Reporting_Framework_TechDocu_v1.4.zip", "FileSize": "1219", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000111382012&iv_version=0002&iv_guid=94761ED29803AE4990E11E161C52783B"}, {"FileName": "Reporting_Framework_TechDocu_v1.4.z01", "FileSize": "1423", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000111382012&iv_version=0002&iv_guid=D078E1257110824BB457B23B9072509C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1929420", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Activate Reporting Group", "RefUrl": "/notes/1929420"}, {"RefNumber": "1925534", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Change of DB Tables Delivery Class ('C' to 'E')", "RefUrl": "/notes/1925534"}, {"RefNumber": "1922181", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Enable Transaction Run", "RefUrl": "/notes/1922181"}, {"RefNumber": "1915455", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "Rep.Frwk. - Protect Dynamic Insertion and Selection", "RefUrl": "/notes/1915455"}, {"RefNumber": "1883961", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Import of Settings Report and Events 002x", "RefUrl": "/notes/1883961"}, {"RefNumber": "1720027", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "New Public Sector Reports Hungary - Reporting Framework", "RefUrl": "/notes/1720027"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2614678", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Deletion of Unique Index POS from DB Table IDREPFW_SEELM_C", "RefUrl": "/notes/2614678 "}, {"RefNumber": "2455914", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Selection Parameters Cleared w/ Variant Used", "RefUrl": "/notes/2455914 "}, {"RefNumber": "2436072", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Internal ATC Check: ILM Object - Description Missing and Condition Fields Not Registered", "RefUrl": "/notes/2436072 "}, {"RefNumber": "2420066", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Correction in Class IDREPFW_CL_DYNAMIC_SEL after SAP Basis Import", "RefUrl": "/notes/2420066 "}, {"RefNumber": "2400669", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Show Application Log; ALV Hotspot Click; Get Default Report Header", "RefUrl": "/notes/2400669 "}, {"RefNumber": "2385085", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Duplicated Display in Show History of Changes (Event 2060)", "RefUrl": "/notes/2385085 "}, {"RefNumber": "2348083", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Manual Activities for Cross Check Rule Identification w/ Description", "RefUrl": "/notes/2348083 "}, {"RefNumber": "2347409", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Cross Check Rule Identification w/ Description", "RefUrl": "/notes/2347409 "}, {"RefNumber": "2308379", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Copy Customizing Report: Copy Cross-Checks", "RefUrl": "/notes/2308379 "}, {"RefNumber": "2257829", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Allow Empty Lines in Cross-Checks", "RefUrl": "/notes/2257829 "}, {"RefNumber": "2239820", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - <PERSON><PERSON> Amounts in Cross-Checks", "RefUrl": "/notes/2239820 "}, {"RefNumber": "2159832", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Minor Improvements of Reporting Framework", "RefUrl": "/notes/2159832 "}, {"RefNumber": "2146972", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Cross-Checks with Regular Expressions", "RefUrl": "/notes/2146972 "}, {"RefNumber": "2123030", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Problems with <PERSON> Attributes, Comments as Long Texts, More Selection Screen Tabs", "RefUrl": "/notes/2123030 "}, {"RefNumber": "2106576", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Comment as Long Text, Delete Saved Run", "RefUrl": "/notes/2106576 "}, {"RefNumber": "2079735", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Public Sector Czech Republic Reporting transition to new reporting solution", "RefUrl": "/notes/2079735 "}, {"RefNumber": "2072437", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Archiving, Restrict Access to Personal Data, Cross-Checks", "RefUrl": "/notes/2072437 "}, {"RefNumber": "2067466", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "New Reports Czech Rep. - Reporting Framework (FIN 1-12 SF, REF 1-04 OSS, ROZP 1-01 SPO)", "RefUrl": "/notes/2067466 "}, {"RefNumber": "2067357", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Wrong Function Module Call in EhP5 and  EhP6", "RefUrl": "/notes/2067357 "}, {"RefNumber": "2009714", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "New Reports Czech Rep. - Reporting Framework (FIN 2-12 M)", "RefUrl": "/notes/2009714 "}, {"RefNumber": "2061750", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Read Only Fields Changeable By F4 Value Request", "RefUrl": "/notes/2061750 "}, {"RefNumber": "2054227", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Technical Check on TRANSLATE in a Multilingual Systems", "RefUrl": "/notes/2054227 "}, {"RefNumber": "2053281", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - RFBILA Enhancement Missing from ECC 600 to EhP6", "RefUrl": "/notes/2053281 "}, {"RefNumber": "2051807", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Reporting Framework - Implementation Guide", "RefUrl": "/notes/2051807 "}, {"RefNumber": "2044963", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Customer Usage Measure", "RefUrl": "/notes/2044963 "}, {"RefNumber": "2040078", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Customer Usage Measurement", "RefUrl": "/notes/2040078 "}, {"RefNumber": "2039433", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Wrong Function Module Call under EhP4", "RefUrl": "/notes/2039433 "}, {"RefNumber": "2030749", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Report Docu & Selection Parameters", "RefUrl": "/notes/2030749 "}, {"RefNumber": "2025682", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Data Element Change + FM Interface Change", "RefUrl": "/notes/2025682 "}, {"RefNumber": "2019102", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Reporting Framework - Downport to ECC 600", "RefUrl": "/notes/2019102 "}, {"RefNumber": "2008689", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Reporting Framework - Downport to EhP4", "RefUrl": "/notes/2008689 "}, {"RefNumber": "2005888", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Save Selection Parameters", "RefUrl": "/notes/2005888 "}, {"RefNumber": "2002083", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - ALV Tree Output", "RefUrl": "/notes/2002083 "}, {"RefNumber": "1996483", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "New Reports Czech Rep. - Reporting Framework (XML Package 23)", "RefUrl": "/notes/1996483 "}, {"RefNumber": "1995849", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Correction of Accessibility Issues", "RefUrl": "/notes/1995849 "}, {"RefNumber": "1994220", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "New Reports Czech Rep. - Reporting Framework (Part 3) - Correction of Adobe Forms for Closing Reports Annex 5 and PAP", "RefUrl": "/notes/1994220 "}, {"RefNumber": "1967631", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "New Reports Czech Rep. - Reporting Framework (Part 2)", "RefUrl": "/notes/1967631 "}, {"RefNumber": "1987712", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - New Event 1025 - Modify ALV Exception Quickinfo", "RefUrl": "/notes/1987712 "}, {"RefNumber": "1987452", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Incorrect Calculation Types in Data Connector", "RefUrl": "/notes/1987452 "}, {"RefNumber": "1979274", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Selection Screen Change for Enable Transaction Run", "RefUrl": "/notes/1979274 "}, {"RefNumber": "1963461", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "Rep.Frwk. - Change in Package Interface ID-REP_FRWK", "RefUrl": "/notes/1963461 "}, {"RefNumber": "1871746", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "PS CZ: New Reports Czech Rep. - Reporting Framework", "RefUrl": "/notes/1871746 "}, {"RefNumber": "1963308", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "PS CZ: New Reports Czech Rep. - Manual Activities", "RefUrl": "/notes/1963308 "}, {"RefNumber": "1704957", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Public Sector Hungary Localization", "RefUrl": "/notes/1704957 "}, {"RefNumber": "1933186", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Data Store Improvement - Compression", "RefUrl": "/notes/1933186 "}, {"RefNumber": "1946331", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Enable Report Group PSHU", "RefUrl": "/notes/1946331 "}, {"RefNumber": "1941077", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "Rep.Frwk. - Copy Customizing Report", "RefUrl": "/notes/1941077 "}, {"RefNumber": "1936189", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "Rep.Frwk. - Output in CSV Format", "RefUrl": "/notes/1936189 "}, {"RefNumber": "1922181", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Enable Transaction Run", "RefUrl": "/notes/1922181 "}, {"RefNumber": "1925534", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Change of DB Tables Delivery Class ('C' to 'E')", "RefUrl": "/notes/1925534 "}, {"RefNumber": "1929420", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Activate Reporting Group", "RefUrl": "/notes/1929420 "}, {"RefNumber": "1926226", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Data Connector for Reporting Framework Enhancement", "RefUrl": "/notes/1926226 "}, {"RefNumber": "1720027", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "New Public Sector Reports Hungary - Reporting Framework", "RefUrl": "/notes/1720027 "}, {"RefNumber": "1883961", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Import of Settings Report and Events 002x", "RefUrl": "/notes/1883961 "}, {"RefNumber": "1915455", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "Rep.Frwk. - Protect Dynamic Insertion and Selection", "RefUrl": "/notes/1915455 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}