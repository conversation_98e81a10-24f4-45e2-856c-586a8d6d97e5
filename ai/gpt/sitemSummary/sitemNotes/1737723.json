{"Request": {"Number": "1737723", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 393, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017465452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001737723?language=E&token=5954F526F3DF63D6B047888336A306F6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001737723", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001737723/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1737723"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 68}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1737723 - EHP3 for SAP SCM 7.0 SP Stacks - Release & Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to notes for applying Support Package (SP) Stacks of SAP Enhancement Package 3 for SAP SCM 7.0<br /><br /><strong>Note</strong>: This SAP Note is subject to change. Listed below are a few general points to keep in mind.<br /><br /><strong>GENERAL INFORMATION</strong><br /><br />Read this note completely BEFORE applying SP Stacks of SAP Enhancement Package 3 of SAP SCM 7.0 (SCM 7.03) and follow the instructions given&#160;below.</p>\r\n<ul>\r\n<li>Check this note for changes on a regular basis. All changes made after release of a Support Package (SP) Stack are documented in section \"Changes made after Release of SP Stack &lt;xx&gt;\".</li>\r\n<li>You will find general information about <strong>SP Stacks&#160;</strong>on SAP Support Portal at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/patches/stacks.html\">https://support.sap.com/software/patches/stacks.html</a>&#160;. The Schedule for SP Stacks&#160;could be found in the SAP Support Portal under Release, Upgrade&#160;&amp; Maintenance Info --&gt; Schedules for maintenance deliveries (Support Packages and SP Stacks) see <a target=\"_blank\" href=\"https://support.sap.com/release-upgrade-maintenance.html\">https://support.sap.com/release-upgrade-maintenance.html</a>&#160;</li>\r\n<li>In addition to the notes mentioned in this note, you should also take into account the list of side effects known for Support Packages, which is created especially for your situation (SP queue). You can request this list on SAP Service Marketplace at <a target=\"_blank\" href=\"http://support.sap.com/notes\">http://support.sap.com/notes</a>.</li>\r\n<li>\r\n<p><strong>NEW:</strong> SAP Solution Manager&#8217;s cloud-based <strong>Maintenance Planner</strong> is the successor of Maintenance Optimizer, Landscape Planner and Product System Editor. <em><strong>Maintenance Optimizer is no longer supported</strong></em><strong>.</strong> Maintenance planner helps you plan and maintain systems in your landscape. Please use the Maintenance planner to calculate a stack XLM file for a system maintenance and add-on installation. To access the tool go to <a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/mp\">https://apps.support.sap.com/sap/support/mp</a> . A <a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/download/attachments/*********/Maintenance_Planner_User_Guide.pdf\">Maintenance Planner User Guide</a> can be accessed from the home screen of Maintenance Planner. For the<strong> big picture of the Maintenance Planner</strong> and related tools, see <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-55363\">Landscape Management</a> @ the SCN</p>\r\n</li>\r\n</ul>\r\n<p><strong>SPAM Update</strong> - We strongly recommend that you apply the latest version of the Support Package Manager before you apply any other Support Packages.</p>\r\n<ul>\r\n<li>SPAM Updates are available on SAP Support Portal under Software Downloads -&gt; Support Packages and Patches --&gt; Tools to apply Support Packages at <a target=\"_blank\" href=\"https://support.sap.com/software/patches/support-packages.html\">https://support.sap.com/software/patches/support-packages.html</a>&#160;.&#160;&#160;For more information about Support Package Manager and Software Update Manager, see the System Maintenance information on the <a target=\"_self\" href=\"https://service.sap.com/sltoolset\">SL Toolset page</a>.</li>\r\n<li>For additional information, see the initial screen of transaction SPAM, choose the 'i' icon (online documentation: help -&gt; application help).</li>\r\n<li>When you import components using the Support Package Manager, ensure that no system activities occur in parallel and no background jobs are running</li>\r\n</ul>\r\n<p>Read the <strong>restrictions for scenarios</strong> in:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 Enhancement packages 3 in SAP Note <a target=\"_blank\" href=\"/notes/1873473\">1873473</a></li>\r\n</ul>\r\n<p><strong>Installation and Upgrade Information</strong></p>\r\n<ul>\r\n<li>Upgrades are possible from any Support Package level of the supported start release. It is not necessary to apply a higher Support Packages on the start release. An overview about the upgrade paths can be found in the <a target=\"_blank\" href=\"https://websmp104.sap-ag.de/&#126;sapidb/011000358700000525522013E\">PAM</a>.</li>\r\n<li>You can find information about browser support in SAP Note <a target=\"_blank\" href=\"/notes/1853989\">1853989</a>.</li>\r\n<li>For the required component versions, see SAP Note <a target=\"_blank\" href=\"/notes/1821798\">1821798</a>.</li>\r\n<li>Make sure to check the SAP NetWeaver Upgrade Master Guide of the SAP NW upgrade target release if you are planning a <strong>NetWeaver upgrade</strong>. It contains specific information about SAP NetWeaver prerequisites, upgrade paths and upgrade dependencies. You find the guides for the SAP NetWeaver Platform on the SAP Help Portal at <a target=\"_blank\" href=\"http://help.sap.com/netweaver\">help.sap.com/netweaver</a> .</li>\r\n<li>After installation/ upgrade&#160;the \"SAP System data\" box can show the Product Version \"- See Details-\". For more information&#160;see SAP Note <a target=\"_blank\" href=\"/notes/2122939\">2122939</a>.</li>\r\n</ul>\r\n<p><strong>Important restrictions</strong></p>\r\n<ul>\r\n<li>SAP Extended Warehouse Management as comprised instance of the product version&#160;for&#160;SAP enhancement package&#160;3 for SAP SCM 7.0&#160;is not released for productive usage. The productive usage of SAP Extended Warehouse Management ontop of&#160;SAP enhancement package&#160;3 for SAP SCM 7.0 can be achieved by installing the Add-On SAP EWM 9.1, or 9.2, or 9.3.</li>\r\n</ul>\r\n<p><strong>Important Considerations</strong></p>\r\n<ul>\r\n<li>Please be aware that by SAP Business Suite 7 Innovations 2013 (including EhP7 for SAP ERP 6.0, EhP3 for SAP CRM 7.0, EHP3 for SAP SCM 7.0 and EhP3 for SAP SRM 7.0)&#160;&#160;dual-stack is not supported anymore. For more information (including information about a dual stack split tool), see SAP Note <a target=\"_blank\" href=\"/notes/1816819\">1816819</a>&#160;&amp; <a target=\"_blank\" href=\"/notes/1655335\">1655335</a>.</li>\r\n<li>Please be aware that SAP Business Suite 7 Innovations 2013 has been <span style=\"text-decoration: underline;\">initially delivered</span> with SP01-stack (hence this release information note starts with SP01). Although at least SP stack 01 is required for productive usage, the initial upgrade and installation shipment contains SP stack 00.Therefore the required SP stack must be included into the upgrade or installed after the installation.</li>\r\n<li>Please note that as of SAP Business Suite 7 (including EhP4 for SAP ERP 6.0, SAP CRM 7.0, SAP SRM 7.0 and SAP SCM 7.0), you can <strong>no</strong> longer install <strong>Dual Stack</strong> Application Systems (ABAP+Java) see SAP Note <a target=\"_blank\" href=\"/notes/855534\">855534</a>.</li>\r\n<li>Be aware, that if you are currently using Enterprise Portal, Business Warehouse and/or Process Integration in conjunction with SAP Business Suite applications, you will have to upgrade those SAP NetWeaver Hubs to 7.30 or higher before you can upgrade SAP SCM to SAP Enhancement Package&#160;3 for SAP SCM 7.0. For more information about the Version Interoperability within the SAP Business Suite, see SAP Note <a target=\"_blank\" href=\"/notes/1388258\">1388258</a>.</li>\r\n<li>If you want to use Add-Ons with a specific Enhancement Package, then refer to SAP Note <a target=\"_blank\" href=\"/notes/1821797\">1821797</a>&#160;and <a target=\"_blank\" href=\"/notes/1896062\">1896062</a>.</li>\r\n<li>Please be informed that SAP Enhancement Package 3 for SAP SCM 7.0 will not be released for all <strong>platforms</strong> (OS/DB dependencies). Further information can be found at the SAP Product Availabilty Matrix (<a target=\"_blank\" href=\"http://support.sap.com/pam\">http://support.sap.com/pam</a>).</li>\r\n<li>To run <strong>embedded analytics</strong> you need to order the necessary SAP Business Objects Software. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1486885\">1486885</a>.</li>\r\n<li>If you use&#160;<strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum Revision&#160;85.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SCM 7.0; SCM 7; Enhancement Package 3; EHP3 Installation; EhP3 for SCM; SCM 7.03; SCM 713; Supply Change Management</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;27 (09/2023)</strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;27 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 25. For more information about SAP SCM 7.0 SPS25, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 30 (07/2023). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;27 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p><strong>SAP HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2404375\">2404375</a>&#160;(SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;24 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;27 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS27 -- SCM 7.0 EhP1 SPS22 -- SCM 7.0 SPS25 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;26 (03/2023)</strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;26 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 25. For more information about SAP SCM 7.0 SPS25, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;29 (01/2023). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;26 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p><strong>SAP HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2404375\">2404375</a>&#160;(SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;24 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;26 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS27 -- SCM 7.0 EhP1 SPS22 -- SCM 7.0 SPS25 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;25 (09/2022)</strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;25 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 24. For more information about SAP SCM 7.0 SPS24, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;28 (07/2022). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;25 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p><strong>SAP HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2404375\">2404375</a>&#160;(SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;24 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;25 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS26 -- SCM 7.0 EhP1 SPS21 -- SCM 7.0 SPS24 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;24 (03/2022)</strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;24 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 24. For more information about SAP SCM 7.0 SPS24, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;27 (01/2022). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;24 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p><strong>SAP HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2404375\">2404375</a>&#160;(SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;24 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;24 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS26 -- SCM 7.0 EhP1 SPS21 -- SCM 7.0 SPS24 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;23 (09/2021)</strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;23 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 23. For more information about SAP SCM 7.0 SPS23, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;26 (07/2021). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;23 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p><strong>SAP HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2404375\">2404375</a>&#160;(SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;23 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;23 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS25 -- SCM 7.0 EhP1 SPS20 -- SCM 7.0 SPS23 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;22 (03/2021)</strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;22 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 23. For more information about SAP SCM 7.0 SPS23, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;25 (12/2020). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;22 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p><strong>SAP HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2404375\">2404375</a>&#160;(SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;22 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;22 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS25 -- SCM 7.0 EhP1 SPS20 -- SCM 7.0 SPS23 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK&#160;21 (09/2020)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;21 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 22. For more information about SAP SCM 7.0 SPS22, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;24 (07/2020). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;21 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p><strong>SAP HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note <a target=\"_blank\" href=\"/notes/2404375\">2404375</a> (SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;21 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;21 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS24 -- SCM 7.0 EhP1 SPS19 -- SCM 7.0 SPS22 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<ul>\r\n<li><strong>2999417 - Error when reading product allocation characteristics from buffer</strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK&#160;20 (03/2020)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack&#160;20 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 22. For more information about SAP SCM 7.0 SPS22, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;23 (01/2020). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;20 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p><strong>SAP HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note <a target=\"_blank\" href=\"/notes/2404375\">2404375</a> (SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS)&#160;20 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS&#160;20 -- SCM 7.0 EhP2 on Hana SPS 14 -- SCM 7.0 EhP2 SPS24 -- SCM 7.0 EhP1 SPS19 -- SCM 7.0 SPS22 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 19 (09/2019)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 19 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 21. For more information about SAP SCM 7.0 SPS21, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;22 (07/2019). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 19 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p><strong>SAP HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note <a target=\"_blank\" href=\"/notes/2404375\">2404375</a> (SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 19 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 19 -- SCM 7.0 EhP2 on Hana SPS 13 -- SCM 7.0 EhP2 SPS23 -- SCM 7.0 EhP1 SPS18 -- SCM 7.0 SPS21 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 18 (03/2019)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 18 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 21. For more information about SAP SCM 7.0 SPS21, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;21 (01/2019). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 18 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p><strong>SAP HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note <a target=\"_blank\" href=\"/notes/2404375\">2404375</a> (SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 18 -- SCM 7.0 EhP2 on Hana SPS 13 -- SCM 7.0 EhP2 SPS23 -- SCM 7.0 EhP1 SPS18 -- SCM 7.0 SPS21 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;After the installation of the support package&#160;18,&#160;it is necessary to install note <strong>2760986</strong> too. The note corrects an error in the Service Fill Analysis deletion report /SAPAPO/SRVF_PROG_DELE_SFD.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160; Without note <strong>2760986</strong> the report may not work correctly and delete more data than intended.</p>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 17 (10/2018)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 17 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 20. For more information about SAP SCM 7.0 SPS20, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;20 (08/2018). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 17 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p><strong>SAP HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note <a target=\"_blank\" href=\"/notes/2404375\">2404375</a> (SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p>If SCM 7.13 SP17 is applied as part of SCM APO 7.13 ON ERP 6.0 EHP6, it is mandatory to have SAP note 1988903 (and all prerequisites) or SAPKB73113 as prerequisite &#8211; otherwise there will be syntax errors and dumps in some BAPIs.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 16 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 17 -- SCM 7.0 EhP2 on Hana SPS 12 -- SCM 7.0 EhP2 SPS22 -- SCM 7.0 EhP1 SPS17 -- SCM 7.0 SPS20 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 16 (03/2018)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 16 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 20. For more information about SAP SCM 7.0 SPS20, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 19 (02/2018). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 16 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p><strong>SAP HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package 3 for SAP SCM 7.0. This applies to all support package stack levels of SAP&#160;SCM 7.0 EhP 3. The support for SAP HANA 2.0 is optional, SAP HANA 1.0 continues to be supported in SAP&#160;SCM 7.0 EhP 3. For general information regarding SAP HANA 2.0, please refer to SAP Note <a target=\"_blank\" href=\"/notes/2404375\">2404375</a> (SAP HANA Platform 2.0 SPS 01 Release Note).</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 16 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 16 -- SCM 7.0 EhP2 on Hana SPS 12 -- SCM 7.0 EhP2 SPS21 -- SCM 7.0 EhP1 SPS17 -- SCM 7.0 SPS20 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li>After implementing SP 16 (03/2018), customers using SNP should implement note 2635746.</li>\r\n</ul>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 15 (01/2018)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 15 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 19. For more information about SAP SCM 7.0 SPS19, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 18 (10/2017). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 15 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . SAP HANA database 2.0 is not released for Enhancement Package&#160;3 for SAP SCM 7.0.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 15 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 15 -- SCM 7.0 EhP2 on Hana SPS 11 -- SCM 7.0 EhP2 SPS20 -- SCM 7.0 EhP1 SPS16 -- SCM 7.0 SPS19 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 14 (07/2017)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 14 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 19. For more information about SAP SCM 7.0 SPS19, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 17 (05/2017). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 14 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . SAP HANA database 2.0 is not released for Enhancement Package&#160;3 for SAP SCM 7.0.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 14 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 14 -- SCM 7.0 EhP2 on Hana SPS 11 -- SCM 7.0 EhP2 SPS19 -- SCM 7.0 EhP1 SPS16 -- SCM 7.0 SPS19 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 13 (01/2017)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 13 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 18. For more information about SAP SCM 7.0 SPS18, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 16 (11/2016). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 13 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . SAP HANA database 2.0 is not released for Enhancement Package&#160;3 for SAP SCM 7.0.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 13 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 13 -- SCM 7.0 EhP2 on Hana SPS10 -- SCM 7.0 EhP2 SPS18 -- SCM 7.0 EhP1 SPS15 -- SCM 7.0 SPS18 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 12 (07/2016)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 12 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 18. For more information about SAP SCM 7.0 SPS18, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 15 (16/2016). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 12 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;85</strong>&#160;of SAP HANA platform software SP stack 08. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Optimizer calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 12 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 12 -- SCM 7.0 EhP2 on Hana SPS10 -- SCM 7.0 EhP2 SPS17 -- SCM 7.0 EhP1 SPS15 -- SCM 7.0 SPS18 -- SCM 5.1 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong><strong><strong>___________________________________________________________________________________________________</strong></strong></strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 11 (01/2016)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 11 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 17. For more information about SAP SCM 7.0 SPS17, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 13 (11/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 11 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;85</strong>&#160;of SAP HANA platform software SP stack 08. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a>&#160;concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note&#160;<a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 11 -- SCM 7.0 EhP2 on Hana SPS09 -- SCM 7.0 EhP2 SPS16 -- SCM 7.0 EhP1 SPS14 -- SCM 7.0 SPS17 -- SCM 5.1 SPS22</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 10 (10/2015)</span></strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 10 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 17. For more information about SAP SCM 7.0 SPS17, see SAP Note <a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 12 (08/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 10 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP <strong>Fiori</strong> for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a> concerning interoperability.</p>\r\n<p>With SPS 10 the new instance &#8216;SAP Hana LiveCache&#8217; was added to product version EHP3 for SAP SCM 7.0. Further information could be found in note <a target=\"_blank\" href=\"/notes/2223318\">2223318</a>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 08 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 10 -- SCM 7.0 EhP2 on Hana SPS09 -- SCM 7.0 EhP2 SPS15 -- SCM 7.0 EhP1 SPS14 -- SCM 7.0 SPS17 -- SCM 5.1 SPS22</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td width=\"463\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><strong>HANA LCAPPS</strong></p>\r\n</td>\r\n<td width=\"463\"></td>\r\n</tr>\r\n<tr>\r\n<td width=\"166\">\r\n<p><a target=\"_blank\" href=\"/notes/2223318\">2223318</a></p>\r\n</td>\r\n<td width=\"463\">\r\n<p>SAP HANA LCAPPS as part of SAP EhP 3 and EhP 4 for SAP Supply Chain Management 7.0</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong><strong><span style=\"text-decoration: underline;\"><strong>___________________________________________________________________________________________________</strong></span></strong></strong></span></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 09 (07/2015)</span></strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 09 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 17. For more information about SAP SCM 7.0 SPS17, see SAP Note <a target=\"_blank\" href=\"/notes/2065499\">2065499</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 11 (06/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 09 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP <strong>Fiori</strong> for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 08 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 09 -- SCM 7.0 EhP2 on Hana SPS09-- SCM 7.0 EhP2 SPS15 -- SCM 7.0 EhP1 SPS14 -- SCM 7.0 SPS17 -- SCM 5.1 SPS22</p>\r\n<p><strong>No additional notes to be applied as part of this stack.</strong></p>\r\n<p><strong><strong><span style=\"text-decoration: underline;\"><strong>___________________________________________________________________________________________________</strong></span></strong></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 08 (04/2015)</span></strong></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 08 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 16. For more information about SAP SCM7.0 SPS16, see SAP Note <a target=\"_blank\" href=\"/notes/2005596\">2005596</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 10 (03/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 08 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version SAP <strong>Fiori</strong> for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 08 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7.0 EhP3 SPS 08 -- SCM 7.0 EhP2 on Hana SPS08-- SCM 7.0 EhP2 SPS14 -- SCM 7.0 EhP1 SPS13 -- SCM 7.0 SPS16 -- SCM 5.1 SPS21</p>\r\n<p><strong>No additional notes to be applied as part of this stack.</strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong>___________________________________________________________________________________________________</strong></span></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 07 (01/2015)</strong></span></strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 07 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 16. For more information about SAP SCM7.0 SPS16, see SAP Note <a target=\"_blank\" href=\"/notes/2005596\">2005596</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 09 (11/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 07 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>Update to ERP 6.0 EHP7 SP07 is possible both via SUM and SPAM. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>. Only the SP update from SP06 to SP07 is possible via SPAM.</p>\r\n<p>The calculation of a maintenance stack (MOPZ) for a system that contains the software component SAP NW 7.31 BI CONT ADDON 7.47 does not deliver any Support Packages for BI_CONT. As a workaround you can either download the corresponding BI_CONT 7.47 SPs manually from the Service Marketplace or select SAP NW 7.40 BI CONT ADDON 7.57 under \"Alternative Selection\" in the step \"Select OS/DB - Dependent files&#8221;. The upgrade package and the corresponding SPs for BI_CONT 7.57 are then calculated correctly.</p>\r\n<p>In case you use the product version SAP <strong>Fiori</strong> for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 07 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SCM 7. 0 EhP3 SPS 07 -- SCM 7.0 EhP2 on Hana SPS08-- SCM 7.0 EhP2 SPS14 -- SCM 7.0 EhP1 SPS13 -- SCM 7.0 SPS16 -- SCM 5.1 SPS21</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong><strong>SAP notes</strong></strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><em><strong>Update/Upgrade</strong></em></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1751237\">1751237</a></td>\r\n<td>Add. info about the update/upgrade to SAP NetWeaver 7.4 (incl. SPs and SRs)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\"><strong>___________________________________________________________________________________________________</strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT RELEASE 2 (12/2014)</strong></span></p>\r\n<p><br />SR2: Enhancement Package&#160;3 for SAP&#160;SCM 7.0 with Support Release&#160;2 includes the support packages up to SP06. If you perform an update with target release&#160;EHP3 for SAP&#160;SCM 7.0 SR2, your system goes immediately to EHP3 for SCM 7.0 SP06 and SAP NetWeaver 7.40 SP08.</p>\r\n<p>With the availability of Support Release 2, the DVDs of the predecessor Support Release 1 have been archived. If you have already started upgrading a system with the DVDs of Support Release 1, you can still select the corresponding valid Support Package Stacks (03 &#8211; 05) in Maintenance Planner for a transition phase.</p>\r\n<p>___________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 06 (10/2014)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 06 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 16. For more information about SAP SCM7.0 SPS16, see SAP Note <a target=\"_blank\" href=\"/notes/2005596\">2005596</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 08 (09/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 06 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP08</strong> requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The implementation of SP Stack 06 is only possible via <strong>SUM</strong> tool and not as for previous support packages via SPAM as well. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>.</p>\r\n<p>Before performing a <strong>system copy</strong> check that&#160;you use the correct SAP Kernel medium for the target system installation. For more details please refere to SAP note&#160;<a target=\"_blank\" href=\"/notes/1738258\">1738258</a>. If your system is still non-unicode and you have updated it to SP Stack 06 of EHP3 for SAP&#160;SCM 7.0, it is currently not possible to do a <strong>heterogeneous </strong>system copy (i.e. changing the database product). For details see SAP note <a target=\"_blank\" href=\"/notes/2054965\">2054965</a>.</p>\r\n<p>In case you use the product version SAP <strong>Fiori</strong> for SAP&#160;SCM 1.0, please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2001729\">2001729</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>Be aware that the SAP Solution Manager - Maintenace Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 06 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SCM 7. 0 EhP3 SPS 06 -- SCM 7.0 EhP2 on Hana SPS08-- SCM 7.0 EhP2 SPS13 -- SCM 7.0 EhP1 SPS13 -- SCM 7.0 SPS16 -- SCM 5.1 SPS21</p>\r\n<p><strong>Changes made after Release of SP Stack 06:</strong></p>\r\n<p>The <strong>EHP update</strong> is released since 04.11.2014.</p>\r\n<p>The <strong>upgrade </strong>to Suite7i2013 SPS06 is released since 04.12.2014.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>*********************************************************************************************************</strong></span></p>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 05 (07/2014)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 05 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 15. For more information about SAP SCM7.0 SPS15, see SAP Note <a target=\"_blank\" href=\"/notes/1972685\">1972685</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 07 (06/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>To run embedded analytics you need to order the necessary SAP Business Objects Software. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1486885\">1486885</a>.</p>\r\n<p>Before update to SP Stack 05 of Enhancement Package&#160;3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision 82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"color: #0000ff;\">2047977</span></span></span></a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em><span style=\"color: #0000ff;\">SAP HANA revision and maintenance strategy</span></em></a>documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"color: #0000ff;\">2021789</span></span></span></a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>Be aware that the SAP Solution Manager - Maintenace Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 05 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SCM 7. 0 EhP3 SPS 05 -- SCM 7.0 EhP2 on Hana SPS07-- SCM 7.0 EhP2 SPS12 -- SCM 7.0 EhP1 SPS12 -- SCM 7.0 SPS15 -- SCM 5.1 SPS21 -- SCM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to processes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP notes</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Update/Upgrade</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2037827\">2037827</a></td>\r\n<td>SQL1754 error during XPRA phase of update to SCM 713 SP05 using SPAM on DB6</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2042004\">2042004</a></td>\r\n<td>SQL1754 error during infoprovider or Planning Object Structure activation on DB6 on SCM 713 SP05</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2035350\">2035350</a></td>\r\n<td>DB6: Wrong long tablespaces for new or modified tables during SAP system upgrade to 7.40</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2039085\">2039085</a></td>\r\n<td>upgrade fails with Retcode 1: SQL-error -2048-column store error PARCONV_UP</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2040866\">2040866</a></td>\r\n<td>Update R3load for Software Provisioning Manager 1.0</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2016248\">2016248</a></td>\r\n<td>Activation of DDL source terminates due to missing namespace</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Systemcopy</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2031638\">2031638</a></td>\r\n<td>R3load terminates during creation of an index for a field of the type LOB</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 05:</strong></p>\r\n<p>Before update to SP Stack 05 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision 82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"color: #0000ff;\">2047977</span></span></span></a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em><span style=\"color: #0000ff;\">SAP HANA revision and maintenance strategy</span></em></a>documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"color: #0000ff;\">2021789</span></span></span></a> .</p>\r\n<p><strong>-------------------------------------------------------------------------------------------------------------------------------------------------------------</strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 04 (04/2014)</strong></span></p>\r\n<p><strong>I<strong>nstallation Requirements: </strong></strong></p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 04 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 15. For more information about SAP SCM7.0 SPS15, see SAP Note <a target=\"_blank\" href=\"/notes/1972685\">1972685</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 06 (03/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>To run embedded analytics you need to order the necessary SAP Business Objects Software. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1486885\">1486885</a>.</p>\r\n<p>Before update to SP Stack 04 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimal <strong>revision 74</strong> of SP stack 07 of SAP HANA database (see SAP note <a target=\"_blank\" href=\"/notes/2003736\">2003736</a>). Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. For more details, please refer to the <em>SAP HANA revision strategy</em> document on SAP Service Marketplace at <a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">https://service.sap.com/&#126;sapidb/011000358700001182742013</a>&#160;.</p>\r\n<p><strong>SP Equivalence Levels:</strong></p>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>Be aware that the SAP Solution Manager - Maintenace Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 04 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SCM 7. 0 EhP3 SPS 04 -- SCM 7.0 EhP2 on Hana SPS06-- SCM 7.0 EhP2 SPS11 -- SCM 7.0 EhP1 SPS12 -- SCM 7.0 SPS15 -- SCM 5.1 SPS21 -- SCM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to processes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong><strong>SAP Note</strong></strong></p>\r\n</td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Demand Planning</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1969092</td>\r\n<td>Exception when launching master data maintenance for some InfoObjects; additional option for navigating to application log</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SNC</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1991427</td>\r\n<td>Corrections to the Enhanced SNC Alert Engine - DDIC Changes and Generated Objects</td>\r\n</tr>\r\n<tr>\r\n<td>1992058</td>\r\n<td>Exit Added for Sending Notification for Alerts in Enhanced SNC Alert Engine</td>\r\n</tr>\r\n<tr>\r\n<td>1991149</td>\r\n<td>Corrections to the Enhanced SNC Alert Engine</td>\r\n</tr>\r\n<tr>\r\n<td>1992003</td>\r\n<td>GUIDs in Notification Email for Alerts Managed in Enhanced SNC Alert Engine</td>\r\n</tr>\r\n<tr>\r\n<td>1980439</td>\r\n<td>Parallel quick view selection not working for alert types managed by the Enhanced SNC Alert Engine</td>\r\n</tr>\r\n<tr>\r\n<td>1967886</td>\r\n<td>UI Framework: Make max. screen width controllable</td>\r\n</tr>\r\n<tr>\r\n<td>2004122</td>\r\n<td>Alert Notification Dump with Enhanced SNC Alert Engine</td>\r\n</tr>\r\n<tr>\r\n<td>1993918</td>\r\n<td>Personalize link is not visible on the Quick View screen</td>\r\n</tr>\r\n<tr>\r\n<td>2002786</td>\r\n<td>POWL: Table is rendered blank on switching between queries</td>\r\n</tr>\r\n<tr>\r\n<td>1975709</td>\r\n<td>New SNC Alert Engine</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SPP</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1967886</td>\r\n<td>UI Framework: Make max. screen width controllable</td>\r\n</tr>\r\n<tr>\r\n<td>1964225</td>\r\n<td>SPP Deployment: Virtual Child Location Demand Horizon</td>\r\n</tr>\r\n<tr>\r\n<td>1950581</td>\r\n<td>Inventory Balancing: inventory balancing areas share locations</td>\r\n</tr>\r\n<tr>\r\n<td>1960451</td>\r\n<td>DRP Projected Stock Keyfigure</td>\r\n</tr>\r\n<tr>\r\n<td>1944915</td>\r\n<td>BADI for controlling the trigger for Stocking Realignment service when replenishment indicator is changed</td>\r\n</tr>\r\n<tr>\r\n<td>1967148</td>\r\n<td>Stocking/Destocking: Ability to Disregard Stability Period</td>\r\n</tr>\r\n<tr>\r\n<td>1704361</td>\r\n<td>PSM Service for Audit Trail reorganization</td>\r\n</tr>\r\n<tr>\r\n<td>1991187</td>\r\n<td>Input help: Display of SELECT-OPTIONS documentation in input help window</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>__________________________________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 03 (01/2014)</strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>I<strong>nstallation Requirements: </strong></strong></span></p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 03 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 14. For more information about SAP SCM7.0 SPS14, see SAP Note <a target=\"_blank\" href=\"/notes/1789401\">1789401</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 05 (12/2013). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p>To run embedded analytics you need to order the necessary SAP Business Objects Software. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1486885\">1486885</a>.</p>\r\n<p>The <strong>upgrade, <strong>EHP update </strong></strong>and the<strong><strong> installation</strong> </strong>to Suite7i2013 SPS03 is released with Support Release 1 (SR01).</p>\r\n<p>The implementation of SP Stack 03 is only possible via <strong>SUM</strong> tool and not as for previous support packages via SPAM as well. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>.</p>\r\n<p>Before update to SP Stack 03 of Enhancement Package 3 for SAP SCM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimal <strong>revision&#160;70</strong> of SP stack 07 of SAP HANA database. Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>Be aware that the SAP Solution Manager - Maintenace Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 03 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SCM 7. 0 EhP3 SPS 03 -- SCM 7.0 EhP2 on Hana SPS05-- SCM 7.0 EhP2 SPS10 -- SCM 7.0 EhP1 SPS11 -- SCM 7.0 SPS14 -- SCM 5.1 SPS20 -- SCM 5.0 SPS24</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to processes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong><strong>SAP Note</strong></strong></p>\r\n</td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Demand Planning</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1914046</td>\r\n<td>POWL: Detail area is wrong on refresh and lead selection</td>\r\n</tr>\r\n<tr>\r\n<td>1912693</td>\r\n<td>POWL: Clear the table selection for index -2 set from applic</td>\r\n</tr>\r\n<tr>\r\n<td>1945143</td>\r\n<td>POWL Dump on Detail area and Edit row data is not reflected upon lead selection</td>\r\n</tr>\r\n<tr>\r\n<td>1966342</td>\r\n<td>Planner Homepage Resource view raises UNCAUGHT_EXCEPTION, LEADSEL does not exists dump.</td>\r\n</tr>\r\n<tr>\r\n<td>1953480</td>\r\n<td>Column view generation after upgrade to BW 7.40 SP5</td>\r\n</tr>\r\n<tr>\r\n<td>1964028</td>\r\n<td>Planner Homepage Demand Planning view raises UNCAUGHT_EXCEPTION, LEADSEL does not exists dump</td>\r\n</tr>\r\n<tr>\r\n<td><strong>HANA Optimization</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1959529</td>\r\n<td>SNP Heuristics not considering Version Independent quota</td>\r\n</tr>\r\n<tr>\r\n<td>1966479</td>\r\n<td>Incorrect quota negative buffering</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Planners Homepage</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1964385</td>\r\n<td>CX_SY_REF_IS_INITIAL dump raised in Planners Homepage</td>\r\n</tr>\r\n<tr>\r\n<td>1969518</td>\r\n<td>Alert icon is not displayed in Location Product CHIP</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 03:</strong></p>\r\n<p>The <strong>upgrade, <strong>EHP update </strong></strong>and the<strong><strong> installation</strong> </strong>to Suite7i2013 SPS03 is released with Support Release 1 (SR01). <br />The restriction about the EHP update and upgrade are lifted see SAP note <a target=\"_blank\" href=\"/notes/1949802\">1949802</a>.(March 17, 2014)</p>\r\n<p>__________________________________________________________________________________________________________________________</p>\r\n<p>&#160;</p>\r\n<p><strong>SUPPORT PACKAGE STACK 02 (10/2013)</strong></p>\r\n<p><br />I<strong>nstallation Requirements: </strong></p>\r\n<p>Enhancement Package 3 for SAP SCM SP Stack 02 is based on:</p>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 14. For more information about SAP SCM7.0 SPS14, see SAP Note <a target=\"_blank\" href=\"/notes/1789401\">1789401</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a><strong>.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 04 (10/2013). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n<p>To run embedded analytics you need to order the necessary SAP Business Objects Software. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1486885\">1486885</a>.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>Be aware that the SAP Solution Manager - Maintenace Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 02 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SCM 7. 0 EhP3 SPS 02 -- SCM 7.0 EhP2 on Hana SPS04-- SCM 7.0 EhP2 SPS09 -- SCM 7.0 EhP1 SPS11 -- SCM 7.0 SPS14 -- SCM 5.1 SPS20 -- SCM 5.0 SPS24</p>\r\n<p>&#160;<strong>Notes to be applied as part of this Stack sorted according to processes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Process /Scenario</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>General</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1913944</td>\r\n<td>\r\n<p>View generation correction for SCIC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1929495</td>\r\n<td>POWL:Export button disabled after the change in selection</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Demand Planning</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1902251</td>\r\n<td>Detail area not hidden when there is no lead</td>\r\n</tr>\r\n<tr>\r\n<td>1927562</td>\r\n<td>ITAB_ILLEGAL_SORT_ORDER dump while forwarding alerts</td>\r\n</tr>\r\n<tr>\r\n<td>1914046</td>\r\n<td>POWL: Detail area is wrong on refresh and lead selection</td>\r\n</tr>\r\n<tr>\r\n<td>1912693</td>\r\n<td>POWL: Clear the table selection for index -2 set from application</td>\r\n</tr>\r\n<tr>\r\n<td>1924256</td>\r\n<td>APO Planner's Homepage My Object cannot be opened in new window</td>\r\n</tr>\r\n<tr>\r\n<td>1928691</td>\r\n<td>Unwanted \"Data will be lost\" popup window appears for alert</td>\r\n</tr>\r\n<tr>\r\n<td>1930797</td>\r\n<td>Alerts not displayed when alert profile is selected</td>\r\n</tr>\r\n<tr>\r\n<td>1931711</td>\r\n<td>Internal error 500 occured when creating new alert worklist query</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>PP/DS</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1768102</td>\r\n<td>ICF service for Web Dynpro application not delivered</td>\r\n</tr>\r\n<tr>\r\n<td>1923015</td>\r\n<td>PP/DS optimizer: Runtime error in /SAPAPO/CDPS_OPT_GET_DBCON: CX_SY_RANGE_OUT_OF_BOUNDS in an SCM on SAP HANA system</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SNC</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1889434</td>\r\n<td>Demand Planning: repeated regeneration of programs</td>\r\n</tr>\r\n<tr>\r\n<td>1922303</td>\r\n<td>Runtime error COMMIT_IN_POSTING in CVC creation</td>\r\n</tr>\r\n<tr>\r\n<td>1924792</td>\r\n<td>POSTING_ILLEGAL_STATEMENT error for SNC POS</td>\r\n</tr>\r\n<tr>\r\n<td>1929441</td>\r\n<td>ODM Update issue</td>\r\n</tr>\r\n<tr>\r\n<td>1913944</td>\r\n<td>View generation correction for SCIC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>__________________________________________________________________________________________________________________________</p>\r\n<p><br /><strong>SUPPORT PACKAGE STACK 01 (08/2013)</strong><br /><br />As a special case, SP Stack 01 has to be applied with the installation<br />of Enhancement Package 3 for SAP SCM 7.0.</p>\r\n<ul>\r\n<li>I<strong>nstallation Requirements: </strong>Enhancement Package 3 for SAP SCM SP Stack 01 is based on:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP SCM 7.0 SP Stack 13. For more information about SAP SCM7.0 SPS13, see SAP Note <strong>1789400</strong>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/ehp-inst\">http://service.sap.com/ehp-inst</a> <strong>.</strong></li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 03 (07/2013). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/installnw74\">http://service.sap.com/installnw74</a></li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To run embedded analytics you need to order the necessary SAP Business Objects Software. For more information, see SAP Note <strong>1486885</strong>.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>SP Equivalence Levels</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenace Planner calculates the correct Support Packages for you.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The SP equivalence levels for EhP3 Support Package Stack (SPS) 01 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SCM 7. 0 EhP3 SPS 01 -- SCM 7.0 EhP2 on Hana SPS02-- SCM 7.0 EhP2 SPS07 -- SCM 7.0 EhP1 SPS10 -- SCM 7.0 SPS13 -- SCM 5.1 SPS19 -- SCM 5.0 SPS23</p>\r\n<ul>\r\n<li><strong>Notes to be applied as part of this Stack sorted according to processes:</strong></li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP Note Number</td>\r\n<td>Description</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>General Notes (process independent)</strong></td>\r\n</tr>\r\n<tr>\r\n<td>1856415</td>\r\n<td>F4 help: Dump TABLE_INVALID_INDEX for personal 1857237</td>\r\n<td>FPM List UIBB: Dump in Wire Model Maintenance</td>\r\n</tr>\r\n<tr>\r\n<td>1857240</td>\r\n<td>FPM List UIBB: Dump in Filter from Filter</td>\r\n</tr>\r\n<tr>\r\n<td>1858431</td>\r\n<td>WD ABAP ALV : Dump when saving the [standard</td>\r\n</tr>\r\n<tr>\r\n<td>1859065</td>\r\n<td>View Maintenance showing records from all</td>\r\n</tr>\r\n<tr>\r\n<td>1862698</td>\r\n<td>Update termination in function module</td>\r\n</tr>\r\n<tr>\r\n<td>1865096</td>\r\n<td>Update termination in function module/II</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Process dependent Notes:</strong></td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>Backorder Processing:</td>\r\n</tr>\r\n<tr>\r\n<td>1878229</td>\r\n<td>EBOPI: Performance issues while reading items</td>\r\n</tr>\r\n<tr>\r\n<td>1892716</td>\r\n<td>EBOPI: Inconsistent structure</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>Demand Planning:</td>\r\n</tr>\r\n<tr>\r\n<td>1875079</td>\r\n<td>Runtime error when opening selection</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>LiveCache:</td>\r\n</tr>\r\n<tr>\r\n<td>1796966</td>\r\n<td>HDB adjustments for SCM 7.12 and SCM 7.13</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>PP/DS:</td>\r\n</tr>\r\n<tr>\r\n<td>1859508</td>\r\n<td>List ATS component: Setting default for drop</td>\r\n</tr>\r\n<tr>\r\n<td>1872373</td>\r\n<td>Unexpected and incompleted messages in EPP</td>\r\n</tr>\r\n<tr>\r\n<td>1876292</td>\r\n<td>Stock Overview: short dump occurs with single</td>\r\n</tr>\r\n<tr>\r\n<td>1888157</td>\r\n<td>Unwanted message while changing the simple</td>\r\n</tr>\r\n<tr>\r\n<td>1888701</td>\r\n<td>Navigation from alert reloads planning segment</td>\r\n</tr>\r\n<tr>\r\n<td>1888921</td>\r\n<td>Element list is emptied when second level</td>\r\n</tr>\r\n<tr>\r\n<td>1889237</td>\r\n<td>Unwanted message while changing the simple</td>\r\n</tr>\r\n<tr>\r\n<td>1889477</td>\r\n<td>Refresh navigation panel in EPP upon selection 1889915</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1895180</td>\r\n<td>Planned order doesn't get fixed</td>\r\n</tr>\r\n<tr>\r\n<td>1895844</td>\r\n<td>Unwanted success message while modifying date</td>\r\n</tr>\r\n<tr>\r\n<td>1895851</td>\r\n<td>Unwanted message while loading the simple</td>\r\n</tr>\r\n<tr>\r\n<td>1923015</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>PPDS (Upgrade):</td>\r\n</tr>\r\n<tr>\r\n<td>1880215</td>\r\n<td>System dumps while scheduling Planning Run</td>\r\n</tr>\r\n<tr>\r\n<td>1892203</td>\r\n<td>ALV-Grid: Runtime error</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SPP (Upgrade):</td>\r\n</tr>\r\n<tr>\r\n<td>1869169</td>\r\n<td>NDM: Parser error when reading notes from DB in HANA system</td>\r\n</tr>\r\n<tr>\r\n<td>1894607</td>\r\n<td>Empty Stockholding Location when OEMMI product</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I065382)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I036507)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737723/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1873473", "RefComponent": "SCM-APO-FCS", "RefTitle": "Implementation recommendations for DP in EHP 3 of SAP SCM 7.0 and successor EhPs", "RefUrl": "/notes/1873473"}, {"RefNumber": "1853989", "RefComponent": "XX-SER-REL", "RefTitle": "Main Browser Note for SAP Business Suite", "RefUrl": "/notes/1853989"}, {"RefNumber": "1833801", "RefComponent": "BC-DB-LCA", "RefTitle": "Improvements for the implementation of HDB", "RefUrl": "/notes/1833801"}, {"RefNumber": "1825774", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite Powered by SAP HANA - Multi-Node Support", "RefUrl": "/notes/1825774"}, {"RefNumber": "1821798", "RefComponent": "XX-SER-REL", "RefTitle": "Enhancement package 3 for SAP SCM 7.0: Required SWC", "RefUrl": "/notes/1821798"}, {"RefNumber": "1821797", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Enhancement Package 3 for SAP SCM 7.0: Compatible Add-on", "RefUrl": "/notes/1821797"}, {"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819"}, {"RefNumber": "1803986", "RefComponent": "BC-UPG-RDM", "RefTitle": "Rules to use SUM or SPAM/SAINT to apply SPs for ABAP stacks", "RefUrl": "/notes/1803986"}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566"}, {"RefNumber": "1655335", "RefComponent": "BC-INS-DSS", "RefTitle": "Use Cases for Splitting Dual-Stack Systems", "RefUrl": "/notes/1655335"}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2420699", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Release of SAP HANA Database 2.0 for older SAP Versions", "RefUrl": "/notes/2420699 "}, {"RefNumber": "1914052", "RefComponent": "BC-UPG-PRP", "RefTitle": "Minimal HANA and MaxDB platform requirements for NetWeaver 7.40 Support Packages", "RefUrl": "/notes/1914052 "}, {"RefNumber": "1833801", "RefComponent": "BC-DB-LCA", "RefTitle": "Improvements for the implementation of HDB", "RefUrl": "/notes/1833801 "}, {"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819 "}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258 "}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566 "}, {"RefNumber": "1803986", "RefComponent": "BC-UPG-RDM", "RefTitle": "Rules to use SUM or SPAM/SAINT to apply SPs for ABAP stacks", "RefUrl": "/notes/1803986 "}, {"RefNumber": "1825774", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite Powered by SAP HANA - Multi-Node Support", "RefUrl": "/notes/1825774 "}, {"RefNumber": "1873473", "RefComponent": "SCM-APO-FCS", "RefTitle": "Implementation recommendations for DP in EHP 3 of SAP SCM 7.0 and successor EhPs", "RefUrl": "/notes/1873473 "}, {"RefNumber": "1821797", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Enhancement Package 3 for SAP SCM 7.0: Compatible Add-on", "RefUrl": "/notes/1821797 "}, {"RefNumber": "1853989", "RefComponent": "XX-SER-REL", "RefTitle": "Main Browser Note for SAP Business Suite", "RefUrl": "/notes/1853989 "}, {"RefNumber": "1655335", "RefComponent": "BC-INS-DSS", "RefTitle": "Use Cases for Splitting Dual-Stack Systems", "RefUrl": "/notes/1655335 "}, {"RefNumber": "1821798", "RefComponent": "XX-SER-REL", "RefTitle": "Enhancement package 3 for SAP SCM 7.0: Required SWC", "RefUrl": "/notes/1821798 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCM", "From": "713", "To": "713", "Subsequent": ""}, {"SoftwareComponent": "SCM_BASIS", "From": "713", "To": "713", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}