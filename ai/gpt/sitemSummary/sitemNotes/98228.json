{"Request": {"Number": "98228", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000371502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000098228?language=E&token=E0443C6D95F5EE6E8301CE0825A90AC4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000098228", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000098228/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "98228"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 46}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.11.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Monitors for Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Monitors for Oracle", "value": "BC-CCM-MON-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "98228 - Transp. after database upgrade to Oracle 8.0 and 8.1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>1) An upgrade to Oracle 8.0 or 8.1 has been excecuted.<br /><br />In the course of the database upgrade to Oracle 8.0.x or 8.1.x with R/3 Releases 3.0D, 3.0E, 3.0F, 3.1G and 3.1H, the transport described in this note must be imported.<br />(In the case of Oracle 8.1.x, pay attention to the R/3 release matrix.)<br />For Release 3.1I NO actions are required. The changes are already contained in the standard system.<br /><br />&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; &gt;&gt;&gt;&lt;&lt; &lt; &lt;&lt;&lt; &lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;<br />ATTENTION: Do NOT import the transports during an R3 upgrade,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; especially not between the upgrade phases<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RUN_RDDYT2NT and JOB_DBDIF_40B<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Otherwise, the system is brought into an inconsistent<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; status that you can only resolve by resetting the system.<br />&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; &gt;&gt;&gt;&gt;&lt;&lt; &lt; &lt; &lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;<br /><br /><br /><br />2) This transport corrects errors such as:<br />- DBIF_DSQL2_SQL_ERROR in SAPLSTD1 (Transaction ST04)<br />- DBIF_DSQL2_SQL_ERROR in RSORA060 (Transaction DB02)<br />- DBIF_DSQL2_SQL_ERROR in RSPERF10 (Transaction AL07)<br />- DBIF_DSQL2_SQL_ERROR in RSORA850 (Transaction DB12)<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Oracle8, Oracle, 8.0, 8.0.4, 8.0.5, 8.1.x, ST04, DB02, AL07, DB12, SAPLSTD1, RSORA060, RSPERF10, RSORA850, DBIF_DSQL2_SQL_ERROR<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Changes in the structure of the V$Tables with Oracle 8.0.4 and use of<br />the same structures in Oracle 8.1.x.<br /><br />With R/3 3.0D - 3.1H, three Oracle releases can be used:<br /> - Oracle 7.2.3 (no longer officially supported)<br /> - Oracle 7.3.3 -&gt; see Note 84638<br /> - Oracle 8.0.4 -&gt; For the release date &amp; conditions, see Note 104549<br /> - Oracle 8.0.5 -&gt; For the release date &amp; conditions, see Note 23875<br /><br />After the upgrade to Oracle 7.3.3 or Oracle 8.0.4, corresponding transports have to be imported.<br /><br />Changes in the structure of the V$Tables between Oracle7 and Oracle8 can be considerable. Note that only the most important corrections are contained in this note. For a complete monitoring of Oracle8 and in particular new functions, you need the redevelopments for R/3 Release 3.1I.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the following correction after the database upgrade. Download the files from the server sapservX (X = 3, 4, 5, 6, 7) in the binary mode of ftp. Proceed as follows:<br /><br />1. The required files are in the attachment in the respective releases.<br />Previously:<br />(Download the required files from sapservX: These can be found in the following directories:<br />&#x00A0;&#x00A0; In Release 3.0D: /general/R3server/abap/note.0098228/30d<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.0E: -&gt; advance corrections<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.0F: /general/R3server/abap/note.0098228/30f<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.1G and 3.1H:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/general/R3server/abap/note.0098228/31gh<br /><br />&#x00A0;&#x00A0; (/usr/SUPPORT/ftp/home/<USER>/general/R3server/abap/note.0098228/###))<br /><br /><br />2a. (3.0D, 3.0F, 3.1G, 3.1H):<br />&#x00A0;&#x00A0; Implement the corresponding corrections with tp.<br />&#x00A0;&#x00A0; In Release 3.0D:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; C11K000003<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.0F:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;F38K000009<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.1G and 3.1H:&#x00A0;&#x00A0;&#x00A0;&#x00A0;F38K000011<br /><br /><br />2b. (3.0E)<br />&#x00A0;&#x00A0;Carry out the required corrections manually:<br />&#x00A0;&#x00A0;The required corrections are in<br />&#x00A0;&#x00A0; --&gt; Note management --&gt; corrections<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --&gt; select \"SAPXXXXXXX\" --&gt; advance corrections<br />&#x00A0;&#x00A0;Change the programs LSTD1F01 and LSTD1U04.<br />&#x00A0;&#x00A0;If the program LSTD1U15 exists in your system, adjust this also (if it<br />&#x00A0;&#x00A0;does not exist in the system, then ignore the correction LSTD1U15).<br /><br /><br /><br /><br />&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; &gt;&gt;&gt;&lt;&lt; &lt; &lt;&lt;&lt; &lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;<br />ATTENTION: Do NOT import the transports during an R3 upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; especially not between the upgrade phases<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RUN_RDDYT2NT and JOB_DBDIF_40B<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Otherwise, the system is brought into an inconsistent<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;state that you can only get out of by resetting the system.<br />&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; &gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&lt;&lt; &lt; &lt; &lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;<br /><br />See Note 13719 for further help on importing.<br /><br />3. For all Releases (3.0D - 3.1I):<br />Next you have to replace Report RSORA850 in the system by uploading the following: attachment:abap.zipThis version contains additional corrections that refer to Year 2000 specific problems for instance.<br /></p> <b>Note:</b><br /> <p>To avoid longer runtimes of the Exclusive Lockwaits Monitor (Transaction DB01) implement the corrections described in Note 126676.<br /></p> <b>Questions and answers</b><br /> <p><br />1) How can I check whether the corresponding correction was imported into a system?<br /><br />Answer:<br /> - ABAP/4 Editor (SE38) -&gt; Execute program RSORACOR.<br />&#x00A0;&#x00A0; (If the program does not exist, a correction is not imported.)<br /> - The result of the program appears as follows:<br /><br />&gt;&#x00A0;&#x00A0;Corrections for R/3 3.1G/H and Oracle 8.0.4 have been imported.<br />&gt;&#x00A0;&#x00A0;R/3 Release:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;30F<br />&gt;&#x00A0;&#x00A0;Oracle-release (R/3): ORA800<br />&gt;&#x00A0;&#x00A0;Oracle-release (V$Version):<br />&gt;&#x00A0;&#x00A0;Oracle8 Enterprise Edition Release *******.0 - Production<br /><br />2) After the import, an error can occur in the reports RSSTAT60, RSSTAT80, SAPLSTUW or similar reports, since the form 'ADD_COMPLETE_STATISTIC_TO' no longer exists (only in Release 3.0D).<br /><br />Solution:<br />This error occurs if the transport outlined in Note 82486 was imported to the system before the upgrade. To correct the error, import the transport as described in Note 82486.<br /><br /><br />3) You display V$Tables with<br />&#x00A0;&#x00A0; ST04 -&gt; Detail analysis menu -&gt; Display V$Tables<br />&#x00A0;&#x00A0; but some V$Tables receive the error message:<br />&#x00A0;&#x00A0; \"E:No Further Information Available\".<br /><br />Solution:<br />It is important to observe that this note only contains corrections for the most important V$tables. As of Release 3.1I, you can monitor all V$Tables completely.<br />All V$Tables can be displayed with the SVRMGR on the database level (that is, from Oracle).<br /><br /><br />4) As well as V$Tables, GV$Tables are contained in some releases in &#x00A0;&#x00A0; ST04 -&gt; Detail analysis menu -&gt; Display V$Tables.<br /><br />Solution:<br />These GV$Tables are relevant for the Oracle parallel server only, so ignore otherwise.<br /><br />5) Error message COMPUTE_INT_TIMES_OVERFLOW appears when you display v$rollstat:<br />The error COMPUTE_INT_TIMES_OVERFLOW may occur in some cases in Program RSORA008 when v$rollstat is displayed. Manually change Program RSORA008 to correct the error. See note management for the corresponding correction in: Corrections \"SAPYYYYYYY\".<br /><br />6) V$Database cannot be displayed:<br />Choosing \"ST04 -&gt; Detail analysis menu -&gt; Display V$Tables -&gt; V$DATABASE\" leads to the message \"E:No further information available\".<br />Solution: Display table V$Database directly with Report RSORA135.<br /> - To do so you need to start Transaction SE38.<br /> - Enter the report name \"RSORA135\".<br /> - Excecute Report RSORA135 (click the button \"Execute\").<br />See the contents of the table V$Database for the results of the display.<br /><br />7) In R/3 Release 3.0F, calling Transaction DB02 -&gt; Detailed Analysis<br />&#x00A0;&#x00A0;-&gt;Selection for table (for example REGUH) ANALYSIS -&gt; Structure<br />&#x00A0;&#x00A0; results in a termination with error message \"ORA-01722: Invalid<br />&#x00A0;&#x00A0; Number.\"<br />&#x00A0;&#x00A0; Solution:<br />&#x00A0;&#x00A0; To correct this error, change Program RSORAT0F manually. The<br />&#x00A0;&#x00A0; corresponding correction is in the \"Assigned correction instruction\"<br />&#x00A0;&#x00A0; section of this note.<br /><br />8) In R/3 Release 3.0E a termination with error message \"ORA-01722:<br />&#x00A0;&#x00A0; Invalid Number\" occurs when you call Transaction DB02&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Detailed analysis -&gt; Selection for table (for example REGUH)&#x00A0;&#x00A0;&#x00A0;&#x00A0;ANALYSIS -&gt; Extents.<br />&#x00A0;&#x00A0; Solution: To correct this error, change Program RSORAT0F manually.<br />&#x00A0;&#x00A0; The corresponding correction is in the \"Assigned correction<br />&#x00A0;&#x00A0; instructions\" section of this note.<br /><br />9) In R/3 Release 3.0E, Transaction DB02 -&gt; Detailed Analysis<br /> -&gt; Selection for table (for example REGUH) Choose for Analysis<br />&#x00A0;&#x00A0;-&gt; Table and its indexes<br />&#x00A0;&#x00A0; terminates with the error message \"ORA-01722: Invalid Number\".<br />&#x00A0;&#x00A0;Solution:<br />&#x00A0;&#x00A0;To correct this error, change Program RSORAT0F manually. The<br />&#x00A0;&#x00A0;corresponding correction is in the \"Assigned correction<br />&#x00A0;&#x00A0;instructions\" section of this note.<br /><br />10) When you call Transaction ST04 -&gt; Detail Analysis menu<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Display V$Tables -&gt; V$LOG_HISTORY<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;a termination may occur because the field ARCHIVE_NAME is no<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;longer a component of the named V$Table.<br />&#x00A0;&#x00A0; Solution:<br />&#x00A0;&#x00A0; To correct this error, change Program RSORA136 manually. The<br />&#x00A0;&#x00A0; corresponding correction is in the \"Assigned corrections<br />&#x00A0;&#x00A0; instructions\" section of this note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Database System", "Value": "ORACLE  8.0"}, {"Key": "Database System", "Value": "ORACLE  8.1.6"}, {"Key": "Database System", "Value": "ORACLE  8.1.5"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D028369)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036430)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000098228/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000098228/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098228/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098228/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098228/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098228/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098228/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098228/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098228/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "98228_31gh.zip", "FileSize": "149", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008296102001&iv_version=0046&iv_guid=0245D02CC7DD3B49913141188D60B76E"}, {"FileName": "98228_30D.zip", "FileSize": "1039", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008296102001&iv_version=0046&iv_guid=7678202C97179048B1FE8D1E7B173E0D"}, {"FileName": "98228_30F.zip", "FileSize": "462", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008296102001&iv_version=0046&iv_guid=07577BD586BFF84881BC3571FACF93D2"}, {"FileName": "abap.ZIP", "FileSize": "10", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008296102001&iv_version=0046&iv_guid=FC15E1725683DE44A0BAA330042305A5"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98507", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions for ORACLE Migration/Upgrade to 8.0.4", "RefUrl": "/notes/98507"}, {"RefNumber": "84638", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Trnsprt after DB upgrade to Oracle 7.3.3 & 7.3.4.", "RefUrl": "/notes/84638"}, {"RefNumber": "154912", "RefComponent": "BC", "RefTitle": "Year 2000 information for 3.1I and 4.0B kernels", "RefUrl": "/notes/154912"}, {"RefNumber": "138208", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions ORACLE-Migr./Upgrade to 8.0.4: WinNT", "RefUrl": "/notes/138208"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "134576", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB12: incorrect date in the Redo log status display", "RefUrl": "/notes/134576"}, {"RefNumber": "126676", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems when displaying locks", "RefUrl": "/notes/126676"}, {"RefNumber": "121327", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/121327"}, {"RefNumber": "114327", "RefComponent": "BC-OP-NT-CLU", "RefTitle": "MSCS installation R/3 3.1I with Oracle 8.0", "RefUrl": "/notes/114327"}, {"RefNumber": "112325", "RefComponent": "BC-DB-ORA", "RefTitle": "End of \"Cust. Care Support\" ORACLE 7.3.*", "RefUrl": "/notes/112325"}, {"RefNumber": "104549", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 8 for R/3 Releases 3.x", "RefUrl": "/notes/104549"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "84638", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Trnsprt after DB upgrade to Oracle 7.3.3 & 7.3.4.", "RefUrl": "/notes/84638 "}, {"RefNumber": "112325", "RefComponent": "BC-DB-ORA", "RefTitle": "End of \"Cust. Care Support\" ORACLE 7.3.*", "RefUrl": "/notes/112325 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "134576", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB12: incorrect date in the Redo log status display", "RefUrl": "/notes/134576 "}, {"RefNumber": "126676", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems when displaying locks", "RefUrl": "/notes/126676 "}, {"RefNumber": "138208", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions ORACLE-Migr./Upgrade to 8.0.4: WinNT", "RefUrl": "/notes/138208 "}, {"RefNumber": "98507", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions for ORACLE Migration/Upgrade to 8.0.4", "RefUrl": "/notes/98507 "}, {"RefNumber": "114327", "RefComponent": "BC-OP-NT-CLU", "RefTitle": "MSCS installation R/3 3.1I with Oracle 8.0", "RefUrl": "/notes/114327 "}, {"RefNumber": "154912", "RefComponent": "BC", "RefTitle": "Year 2000 information for 3.1I and 4.0B kernels", "RefUrl": "/notes/154912 "}, {"RefNumber": "104549", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 8 for R/3 Releases 3.x", "RefUrl": "/notes/104549 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31H", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 5, "URL": "/corrins/0000098228/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}