{"Request": {"Number": "1645590", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 397, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017331282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001645590?language=E&token=FFE4ED6EB0EC9CB88F3FC15089D396B5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001645590", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001645590/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1645590"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.10.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-WJR"}, "SAPComponentKeyText": {"_label": "Component", "value": "BEx Web Java Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BEx Web Java Runtime", "value": "BW-BEX-ET-WJR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-WJR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1645590 - Java/Server SPs dependencies (and SupportDeskTool)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The Support Desk Tool for checking the installation of the BI Java patches provides a yellow status.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BI Java Patches, Support Packages, ABAP-Server</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Dependencies between BI Java and BW-Server</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Starting with Stack 6 of SAP NetWeaver 7.01 the dependency between SAP NW BW (ABAP) and SAP NW BI JAVA is more flexible.<br /><br />In general it is possible that the BW (ABAP) System is updated independently of the BI JAVA System, e.g. BW 7.01 (or BW 7.30) can use BI Java 7.01 but also with higher Java versions, e.g. BI Java 7.30 (with one exception, see below remark 1).<br />The other way round is also possible in most cases (see remark 2), e.g. BI Java 7.01 (or BI Java 7.30 or BI Java 7.40) can be used with BW ABAP 7.01 but also with BW ABAP 7.02, BW ABAP 7.30. or BW ABAP 7.03/7.31.<br />These combinations are supported.<br /><br /><br />It is strongly recommended to use SPs which are up-to-date (on Java - and ABAP as well). <br /><br /></p>\r\n<p>But the usage of BI JAVA&#160;has some flexibly. When a patch of BI Java is used it is required that the SPS-Level (&#8220;Stack-Level&#8221;)&#160;of the BI Java System is close to the patch level else the Support Desk Tool will show RED or YELLOW checks.</p>\r\n<p>For detailed understanding of this flexibly please refer the note 2586530. <br /><br />Remarks:</p>\r\n<ul>\r\n<li>Remark 1: There is, however, one exception: as in 730 (and following releases)&#160;the Planning Modeller is offered in ABAP and not in Java any more upgrading the BI Java System without an upgrade of the BW cannot be recommended when integrated planning is used. See note 1787619.</li>\r\n</ul>\r\n<ul>\r\n<li>Remark 2: BW ABAP 7.40 will not work with BI Java 7.01 (or 7.02). If BI Java is used it is required that a BI Java is at least BI Java 7.30 (SP/Patch level &gt;= SP09 Patch 20 (or SP08 Patch 30) and corresponding levels for BI Java 703/731 (e.g. for 7.31 SP level &gt;= SP08 (or at least Patch&#160;7.31 SP07 Patch 20)). If BI Java 7.40 is used&#160;SP Level must be at least SP3.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026714)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026714)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001645590/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645590/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1961111", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BW ABAP/JAVA SPS dependencies for different Netweaver release and BI JAVA patch updating relevant", "RefUrl": "/notes/1961111"}, {"RefNumber": "2581475", "RefComponent": "BW", "RefTitle": "Support Packages for SAP NetWeaver 7.0x Business Warehouse in 2018 - 2020", "RefUrl": "/notes/2581475"}, {"RefNumber": "1787619", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Planning Modeller Runtime as of BW 7.30", "RefUrl": "/notes/1787619"}, {"RefNumber": "1666670", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP BW powered by SAP HANA - Landscape Deployment Planning", "RefUrl": "/notes/1666670"}, {"RefNumber": "1648480", "RefComponent": "XX-SER-REL", "RefTitle": "Maintenance for SAP Business Suite 7 Software including SAP NetWeaver", "RefUrl": "/notes/1648480"}, {"RefNumber": "1512988", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Frequently Asked Questions:BI Java 7.30/7.31/7.40 SP's/patches", "RefUrl": "/notes/1512988"}, {"RefNumber": "1512356", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 7.30/7.31/7.40/7.50  BI Java Support Package & Patches", "RefUrl": "/notes/1512356"}, {"RefNumber": "1512355", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW 7.30/7.31/7.40/7.50 : Schedule for BI Java Patch Delivery and BICS SP Delivery", "RefUrl": "/notes/1512355"}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722"}, {"RefNumber": "1327345", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 7.01  BI Java Support Package", "RefUrl": "/notes/1327345"}, {"RefNumber": "1163789", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0: BI Java Synchronized Patch Delivery Strategy", "RefUrl": "/notes/1163789"}, {"RefNumber": "1013369", "RefComponent": "BW", "RefTitle": "SAP NetWeaver 7.0 BI - intermediate Support Packages", "RefUrl": "/notes/1013369"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2141196", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Common performance issues occurring while executing Queries and Web Templates in Java Web", "RefUrl": "/notes/2141196 "}, {"RefNumber": "2581475", "RefComponent": "BW", "RefTitle": "Support Packages for SAP NetWeaver 7.0x Business Warehouse in 2018 - 2020", "RefUrl": "/notes/2581475 "}, {"RefNumber": "1648480", "RefComponent": "XX-SER-REL", "RefTitle": "Maintenance for SAP Business Suite 7 Software including SAP NetWeaver", "RefUrl": "/notes/1648480 "}, {"RefNumber": "1947943", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Central Note for BI Java NW730 SP11", "RefUrl": "/notes/1947943 "}, {"RefNumber": "1512355", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW 7.30/7.31/7.40/7.50 : Schedule for BI Java Patch Delivery and BICS SP Delivery", "RefUrl": "/notes/1512355 "}, {"RefNumber": "1666670", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP BW powered by SAP HANA - Landscape Deployment Planning", "RefUrl": "/notes/1666670 "}, {"RefNumber": "1512356", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 7.30/7.31/7.40/7.50  BI Java Support Package & Patches", "RefUrl": "/notes/1512356 "}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722 "}, {"RefNumber": "1787619", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Planning Modeller Runtime as of BW 7.30", "RefUrl": "/notes/1787619 "}, {"RefNumber": "1163789", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0: BI Java Synchronized Patch Delivery Strategy", "RefUrl": "/notes/1163789 "}, {"RefNumber": "1512988", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Frequently Asked Questions:BI Java 7.30/7.31/7.40 SP's/patches", "RefUrl": "/notes/1512988 "}, {"RefNumber": "1013369", "RefComponent": "BW", "RefTitle": "SAP NetWeaver 7.0 BI - intermediate Support Packages", "RefUrl": "/notes/1013369 "}, {"RefNumber": "1327345", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 7.01  BI Java Support Package", "RefUrl": "/notes/1327345 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "701", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}