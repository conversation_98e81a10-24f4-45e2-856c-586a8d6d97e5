{"Request": {"Number": "623723", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 263, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015695562017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000623723?language=E&token=61A135D0AC0C264780F2388A1649562A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000623723", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000623723/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "623723"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 69}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.08.2012"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-IMS-UPGR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Application Specific Upgrade Tools"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installed Base Development Projects", "value": "XX-PROJ-IMS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-IMS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Application Specific Upgrade Tools", "value": "XX-PROJ-IMS-UPGR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-IMS-UPGR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "623723 - Upgrade: Application-specific problems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>******************************************************************<br />For ERP and BW systems, this note is obsolete.<br />For these systems, refer to Note 1000009 with the<br />current version of ASU Toolbox 2008. <br />******************************************************************<br /><br /><br /><br />******************************************************************<br /><br />You have completed the technical part of your SAP system upgrade (R/3, APO, CRM and so on). When you test the new system, you encounter application problems such as:</p> <OL>1. Application transactions no longer work as expected (or they work in a different way than before).</OL> <OL>2. The system terminates or displays error messages when you start application transactions because data conversions are missing, for example.</OL> <OL>3. You may no longer be able to use or display background variants for programs. (see Notes 153865, 65343, 114675, 626408)</OL> <OL>4. You can no longer use display variants of reports or they are not available in the new transactions.</OL> <OL>5. Batch input sessions no longer work because the SAP programs have changed.</OL> <OL>6. Your customer exits have syntax errors because the structure of SAP tables has changed.</OL> <OL>7. For new functions, the upgrade IMG does not specify that Customizing settings are required.</OL> <OL>8. ...</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Upgrade, put, application, application problems, upgrade problems, XPRA,<br />XPRAs,<br />ASU, ASU toolbox, Application Specific Upgrade<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>We always attempt to create downward compatibile programs. This ensures that you can still execute the old functions and programs in the new release.<br />However, continuous product enhancements may require data conversions.  If possible, the system automatically executes these data conversions during the upgrade in the XPRA phase. To keep the upgrade runtime as short as possible, some programs are not delivered as XPRA and must be started manually in the new system as a result.&#x00A0;&#x00A0;However, it has not previously been possible to obtain a collection of all of these programs at a central location.<br />For other problems (such as inconsistent background variants), there was previously no satisfactory solution. Once you added new functions to the interface of a program, you could no longer use its variants (to solve this problem, see Note 626408).<br />By changing to more user-friendly interfaces (keyword Enjoy), you may no longer be able to use the customer-specific display variants of the old transactions with the new interfaces.<br />New functions may require you to make manual changes, which may not be described in the release notes or in the upgrade guide.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>We provide a free service that helps you to automatically eliminate some of the upgrade-related problems described above.<br />In the following, this is referred to as ASU (Application Specific Upgrade) or ASU toolbox.<br />Read the relevant application-specific upgrade guides attached to this note. These guides contain a detailed description of known problems, errors and changes that may occur after an upgrade and affect your production operation.<br />The ASU upgrade guides only describe known problems and how to correct them. In contrast, the ASU toolbox (see below) contains correction reports that you should start or can start after the upgrade.<br />For HR-specific upgrade problems, refer to http://service.sap.com/hrupgrade or the upgrade information at http://service.sap.com/erp-hcm on SAP Service Marketplace.<br />You will find other upgrade-relevant notes on SAP Service Marketplace at http://service.sap.com/notes if you choose the notes with the category 'U' - Upgrade Information. These notes were not included in the ASU upgrade guide.<br /></p> <b>Note:</b><br /> <p>You cannot download the attachments from the note database. To download notes, go to SAP Service Marketplace:</p> <UL><LI>When you use the \"/notes\" alias to display a note, the system displays the tab \"Attachment\" after you click '&gt;&gt;'.</LI></UL> <UL><LI>You can download a file using the 'Save Target As...' option in the browser.</LI></UL> <p><br />Note 13719 provides a more detailed description about how to import a transport request into your system. However, ensure that you import the transfers into the relevant clients (for example, specify client 000 during the import).<br />For technical reasons, ASU program texts are only available in English. To start the ASU toolbox, log on in English.<br /><br />The upgrade guides are not exhaustive, but are constantly changed and updated. If errors occur when you upgrade or test a new system and these errors are not described in the upgrade guides, report this in a customer message. If the problem you describe is relevant for all customers, we will include it in the upgrade guides.<br /><br />The following section lists the names of the attachments and the date on which we last updated them:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Benefits_of_ASU.ppt or Benefits_of_ASU.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0; June 20, 2003</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A short description of the functions and advantages associated with using the ASU toolbox<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>How_To_Guide.ppt or How_To_Guide.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; June 27, 2003</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Operating instructions for the ASU toolbox with explanatory screenshots<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_FI_EC.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_FI_EC.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;August 27, 2007<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide for Financial Accounting and Enterprise Controlling<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_CO.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_CO.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;8/21/2007<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide f&#x00FC;r Controlling<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_PLM.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_PLM.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;October 17, 2006<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide for Product Lifecycle Management<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_PP.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_PP.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;June 22, 2005<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide for Production Planning and Control<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_SCM_LE.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_SCM_LE.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;October 06, 2004<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide f&#x00FC;r Supply Chain Management - LE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_SCM_LIS.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_SCM_LIS.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;October 06, 2004<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide f&#x00FC;r Supply Chain Management - LIS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_SCM_MM.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_SCM_MM.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;November 03, 2005<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide for Supply Chain Management - MM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_SD.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_SD.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;November 30, 2006<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide for Sales and Distribution<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_CRM.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_CRM.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;October 12, 2004<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade guide for Customer Relationship Management<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>Upgrade_Guide_BC_CA.zip or</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade_Guide_BC_CA.pdf&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;April 23, 2007</p> <UL><LI>Upgrade guide for Cross-Application Components</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************<br /><br /></p> <UL><LI>AXIK016961.ZIP, source release 3.1I or lower (August 06, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for source release 3.1I or lower. For more information, see Note 716345.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>AX4K036441.ZIP, source release 4.0A/B (August 06, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for source release 4.0A/B<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>AX5K017885.ZIP, source release 4.5A/B (August 06, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for source release 4.5A/B<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Only for target release 4.70 and lower<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>AX7K015978.ZIP, source release 4.6A/B (August 06, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for source release 4.6A/B<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Only for target release 4.70 and lower<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>AX8K025400.ZIP source and target release 4.6C (November 25, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for source and target release 4.6C<br />This is a component-independent part of the ASU toolbox:&#x00A0;&#x00A0;you must import it. For more information, see Note 767038.</p> <UL><LI>AX8K023517.ZIP source and target release 4.6C (July 31, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This is a special source code for the SAP_APPL software component. You may only implement this source code if you have installed this component in your system.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>AXAK019666.ZIP target release 4.70 (November 25, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for target release 4.70.<br /> This is a component-independent part of the ASU toolbox:&#x00A0;&#x00A0;you must import it. For more information, see Note 767038.</p> <UL><LI>AXAK017421.ZIP target release 4.70 (July 31, 2003)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for target release 4.70.<br /> This is a special source code for the SAP_APPL software component. You may only implement this source code if you have installed this component in your system.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>When upgrading with target release 5.00 (ERP 2004) and source releases as of 4.5B, you may not import any transports before the upgrade. The part that is component-independent (variant restorer) is integrated with the tool import of the upgrade (program RASUVAR1), but you must also import the corrections (fix buffer) for the tool import. For more information, see Notes 663240 and 689574.<br />NOTE: If you upgrade to ERP 2004 (or higher) from Release 4.5B (or higher), you may not execute the program RASUVCRE or RASUVADM. In this case (source release 4.5B or higher - target release ERP 2004), the system queries whether the program RASUVAR1 should be started during the phase SAVE_VAR_CHK of the upgrade. If you confirm, the upgrade programs RASUVAR1 and RASUVAR2 are started automatically.</LI></UL> <UL><LI>AXAK026587.ZIP target release ERP 2004 (April 07, 2005)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for target release ERP 2004<br /> This is a special source code for the SAP_APPL software component. You may only implement this source code if you have installed this component in your system. It also contains the automatic correction and conversion reports.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>When you upgrade to SAP ERP 2005, you may not import a transport before the upgrade. The ASU variant restorer is part of the tool import in PREPARE. When you select JOB_RASUVAR1(2) in the phase SAVE_VAR_CHECK, you can activate the variant restorer without restricting the source release of the upgrade.</LI></UL> <UL><LI>AXAK028295.ZIP target release ERP 2005 (September 01, 2006)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport file for target release ERP 2005<br /> This is a special source code for the SAP_APPL software component. You may only implement this source code if you have installed this component in your system. It also contains the automatic correction and conversion reports.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>When you upgrade a system other than ERP or R/3 (for example, CRM, SCM or BW systems), you can use the variant restorer if the target release uses SAP Web AS 640 and the source release uses at least SAP_BASIS 4.5B. In this case, the comments for upgrading to ECC 5.00 (ERP 2004) are valid. You do not require an additional transport. The variant restorer is integrated in the tool import and you can activate it in the phase SAVE_VAR_CHK. The programs RASUVAR1 or RASUVAR2 are then automatically started by the upgrade. For more information, see Notes 663240 and 689574.</LI></UL> <UL><LI>When you upgrade a system other than ERP or R/3 to SAP Web AS 700 or higher, you can use the variant restorer of the ASU toolbox without restricting the source release. No separate transport is required since the variant restorer is integrated in the tool import of PREPARE. You can activate it in the phase SAVE_VAR_CHECK by selecting JOB_RASUVAR1(2).</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************</p> <UL><LI>When you upgrade to NW2004s (or higher), the system may display '3PETG447' messages for the tables TASUVCAT, TASUVEXT, TASUVSVD, and TASUVSVO in the log file of the phase CHK_POSTUP. The message is: 'Table and runtime object \"TASUVxxx\" exist without DDIC reference (\"Transp. table\")'. You can ignore this message. You can delete the tables from the database since they are no longer required.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*************************************************************<br /><br />The following section describes the functions and sections of the ASU toolbox.<br /></p> <OL>1. All problems that can be solved automatically are started in the ASU toolbox.</OL> <OL>2. All problems that require manual settings or work are described in the upgrade guides.</OL> <OL>3. Additional conversion programs (for example, variant restorer) are started in the ASU additional tools.</OL> <p><br />To import the ASU toolbox into your system, proceed as follows:</p> <OL>1. First, read the How_To_Guide.PPTslides, which are attached to this note.</OL> <OL>2. If you want to use the variant restorer, import the first partl <B>BEFORE</B> the upgrade (see the above list of transport requests by source release).</OL> <OL>3. After you complete the upgrade, import the second part of the ASU toolbox.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The second part of the ASU toolbox consists of the following transport requests: <UL><UL><LI>A component-independent request that you must always import (except with target release ERP2004).</LI></UL></UL> <UL><UL><LI>Transports for individual software components (currently only for SAP_APPL). Import only the transports whose components are installed in your system (this information is available in the table CVERS).</LI></UL></UL> <p></p> <b>Technical information</b><br /> <p>The transports contain an encapsulated tool, which is delivered with its own development classes:<br />ASU1 :&#x00A0;&#x00A0;General tool<br />ASU_APPL : Source code for the component SAP_APPL<br />The development classes are delivered with the 'Space' transport layer. This means that, if required, you can modify the objects contained in your system, but you cannot transport them.&#x00A0;&#x00A0; If you must make changes to the programs, we provide a new transport request for the modified objects. Therefore, internal transports in the customer system are not required. You can always use the current SAP standard transport and import it into all of your SAP systems.<br />Note that the programs convert data that is client-specific. This means that you must start this tool separately in each relevant client.<br /><br />The following is a description of how you can use the individual tools.<br /></p> <OL>1. Variant restorer:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For more information, see Note 626408. <OL>2. Automatic repair toolbox:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This toolbox contains all programs that do not require any entries, and the programs carry out repairs that are required for the application transactions to run smoothly in your system. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To execute the programs, you require the authorization object 'S_ADMI_FCD' with the following instances <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;id&#x00A0;&#x00A0;&#x00A0;&#x00A0;= 'S_ADMI_FCD' <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;field = 'RSET' <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Start the program RASUTGR1. This is the central initial screen for all programs that run in the ASU toolbox. The system lists all software components that are installed in your system, and you should confirm this selection. Only those programs for components that you selected are later executed. After you select the components, you see the central initial screen for all repair programs. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you select the function 'Execute mass post processing reports', the system navigates to the initial screen of the program RASUTARG. <OL><OL>a) RASUTARG: Mass run of repair or conversion programs</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This program starts all programs that are relevant for the software components you have installed and the release upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following section provides an overview of the program RASUTARG. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>'Processing'</B><br />You can process the programs one after the other in a consecutive series. We recommend that you do this only if you use a very small system with a low number of batch processes. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The option of processing the jobs at the same time results in a shorter runtime, but involves a greater workload. You can select the maximum number of jobs that you want to run simultaneously. The main job runs in an endless loop until all scheduled programs have terminated. In this loop, the status of each scheduled program is checked after a certain period of time. You can specify this time (in seconds) to activate a program run for this loop. (A short time means a higher computer load due to the main job. If the number of seconds selected is too high, this may result in a queue time that is too long.) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>'Release Notes'</B><br />The 'Release Notes' section displays the data that is read from your system. If this data is incorrect, you can overwrite it. The system executes only those programs that are required for the release upgrade that you performed. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>'Maintenance of programs to be executed'</B><br /> You can now determine the number of programs that you want the system to execute. This allows you to distribute the workload generated by the programs. The list displays both the priority and expected runtime of the programs. If possible, run programs with priority 1 directly after the upgrade. You can later run programs with priority 2 and 3 according to their expected runtime. The information is stored in the table TASUTREP. The indicator ACTIVE determines whether a program is to be started the next time the toolbox runs. The settings that you make are NOT part of the background variant in this program. Only the current version of the table TASUTREP is valid. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For detailed background information about the program, see the relevant upgrade guide. This guide describes the symptoms that the reports correct. The program functions are described in a note that is displayed in the program list. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>'List Display'</B><br />You can use \"List Display\" in the Schedule Manager Monitor to analyze all programs that have been started (including spool lists). <OL><OL>b) \"Individual Reports\"</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Programs referred to in the upgrade guide are listed under \"Individual Reports\". If one of the notes in the upgrade guide applies to your system, you do not have to create the Z report manually in your customer namespace. The reports have been transported into your system along with the ASU toolbox. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Note that not all programs are included in the ASU toolbox.&#x00A0;&#x00A0;The programs that are not contained in the ASU should only run after you have checked your data. For security reasons, the ASU toolbox only contains programs that have no special prerequisites.) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To start one of these programs, simply choose the key for the program you require and proceed according to the description provided in the relevant note. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The contents of the toolbox are constantly changed and updated. The latest version is attached to this note as a transport request. If your upgrade phase lasts for several weeks (since you usually carry out a test run in both test and development systems). we recommend that you always import the latest version of the toolbox into the system that you want to upgrade. <OL><OL>c) Adding customer-defined programs</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can also use the ASU toolbox to start customer-defined programs that adjust your specialized data. The advantage of doing this is that the Schedule Manager then automatically analyzes your runs. The toolbox also provides you with the option of automatic parallel processing (taking into account dependencies). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To do so, the programs have to be distinguished by the presence or absence of an initial screen. You have the following three options: <UL><UL><LI>The program has no initial screen and you can start it automatically. In this case, leave the SELSCR field empty.</LI></UL></UL> <UL><UL><LI>The program has an initial screen but you can create a variant for it, and the program should be started automatically with this variant. Leave the field SELSCR empty, enter an 'X' in the field SELSCR_VARI and the variant name in the field VARI.</LI></UL></UL> <UL><UL><LI>The program has no initial screen and you can only start it manually. Enter 'X' in the field SELSCR.</LI></UL></UL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the table TASUTREP, use transaction SE16 or SE16N to create new entries for your program. You must fill the following fields: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>REPNAME:</B><br />Your program name <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>SELSCR:</B><br />The program is started automatically (enter space) or manually (enter X). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>UPGSTEP:</B><br />Execution stage (only stage 2 is supported) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>DESCRIPTION:</B><br />Description of the program (to do this, you must use quotation marks such as \" ) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>SSCRREP:</B><br />Describe CLEARLY, count back from POSR999 (in uppercase) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>TEXT:</B><br />Describe CLEARLY, count back from POST999 (in uppercase) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>RELFROM:</B><br />Enter the source release as of which the report is required. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>RELTO:</B><br />Enter the target release as of which the report is required. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>DEPREP:</B><br />Dependent on (you must have already executed the report that is entered here) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>SEVERITY:</B><br />Importance of the program (optional) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>RUNTIME:</B><br />Expected runtime (optional) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>APPLICATION:</B><br />Application to which the report belongs <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>COMPONENT:</B><br />Software component <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>EXECUTED:</B><br />Has already been executed (this is set by the toolbox) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>EXETIME:</B><br />Time of execution (this is set by the toolbox) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>EXEDATE:</B><br />Date of execution (this is set by the toolbox) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>EXEUSER:</B><br />User of execution (this is set by the toolbox) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>ACTIVE:</B><br />Report is executed when you start the next time <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>NOTE:</B><br />Enter relevant note number (if available) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>SELSCR_VARI:</B><br />Program is statrted with a variant (X) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>VARI:</B><br />Variant name, if SELSCR_VARI is set to X <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-RDM (README: Upgrade Supplements)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023370)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023370)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000623723/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000623723/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Benefits_of_ASU.ppt", "FileSize": "131", "MimeType": "application/vnd.ms-powerpoint", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=003AD9B019FCCD43B6D56F83039A55F8"}, {"FileName": "Upgrade_Guide_PLM.ZIP", "FileSize": "662", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=097AB869A63FCF4B91B7D1FA839790E5"}, {"FileName": "AXIK016961.ZIP", "FileSize": "33", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=0C249D23C6CE2E4BA9E4C6524A7303C2"}, {"FileName": "AX4K036441.ZIP", "FileSize": "33", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=15C72A00C93DA548880E736F6DEC1FC8"}, {"FileName": "Upgrade_Guide_FI_EC.pdf", "FileSize": "237", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=1F815B0EF64062439877CC48B1F411CD"}, {"FileName": "Upgrade_Guide_PP.ZIP", "FileSize": "634", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=21B34BA8B4351E44B7DD77F1945EC71D"}, {"FileName": "Upgrade_Guide_CO.zip", "FileSize": "633", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=23DC915952082D42A9F1FFD5FA2FA8FE"}, {"FileName": "AXAK019666.zip", "FileSize": "135", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=2E34B55111E16D43A767F269AB8708F1"}, {"FileName": "Upgrade_Guide_SCM_MM.pdf", "FileSize": "224", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=3A8A299D88ABEC4491E9040B0807B3CB"}, {"FileName": "Upgrade_Guide_CO.pdf", "FileSize": "283", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=40D7B5D0FCAE7B468D545C125A38ACDD"}, {"FileName": "Upgrade_Guide_FI_EC.ZIP", "FileSize": "613", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=41064828F7568C4B9AABB097D3786BD2"}, {"FileName": "AXAK017421.ZIP", "FileSize": "183", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=545A883A64FA1C459C77FD693A0C982E"}, {"FileName": "Upgrade_Guide_SCM_MM.ZIP", "FileSize": "617", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=636CC0D54C1F7D4CAD911E5E8F51751C"}, {"FileName": "Upgrade_Guide_PLM.pdf", "FileSize": "347", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=76F0F62C32ECE44183A2AD64C2084655"}, {"FileName": "Upgrade_Guide_CRM.pdf", "FileSize": "207", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=79F9996226C2A14795CCFE07B238C64B"}, {"FileName": "AXAK026587.ZIP", "FileSize": "253", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=7B7F0BA3A29537458A1E73224FBBC457"}, {"FileName": "AX8K023517.ZIP", "FileSize": "184", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=8CB414F8AF3E004A8472A403C46577DF"}, {"FileName": "HowToGuide.ppt", "FileSize": "497", "MimeType": "application/vnd.ms-powerpoint", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=954CEB99D591B4489A3DE7EA09E1E116"}, {"FileName": "Upgrade_Guide_SCM_LE.zip", "FileSize": "613", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=9EFEFE8CC56C4B45BFDCB16336207872"}, {"FileName": "AX8K025400.zip", "FileSize": "130", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=A3F786C98616E44B8ACC8C3DEB391E8E"}, {"FileName": "Upgrade_Guide_SCM_LIS.zip", "FileSize": "609", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=A59B3EA38E24AA49A332C6DAA7BD96E3"}, {"FileName": "Upgrade_Guide_SCM_LIS.pdf", "FileSize": "192", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=A5FC5B074916B64C99034C7351A8C66D"}, {"FileName": "AX7K015978.ZIP", "FileSize": "41", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=AC6C0E0FEBEBBF49B5DBB650C1F257EC"}, {"FileName": "Upgrade_Guide_SD.ZIP", "FileSize": "603", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=B01EB574A68B354D91A2DF2354C62DB4"}, {"FileName": "Upgrade_Guide_BC_CA.pdf", "FileSize": "194", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=B2B5643CCB404147A368C507CD643CD7"}, {"FileName": "Upgrade_Guide_SD.pdf", "FileSize": "194", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=BA82BFEE7E6DEE4185CE0E7F23A01204"}, {"FileName": "Upgrade_Guide_SCM_LE.pdf", "FileSize": "203", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=D7B25CABAF3B004E9DE68D393E58E0B7"}, {"FileName": "Upgrade_Guide_CRM.zip", "FileSize": "614", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=E82DAE05A7500E488E0705F98F4B069D"}, {"FileName": "AX5K017885.ZIP", "FileSize": "30", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=E9032C0CB5B7144293A49911072638A4"}, {"FileName": "HowToGuide.pdf", "FileSize": "404", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=EBD21371630D12448491D50B3E23F5FA"}, {"FileName": "Upgrade_Guide_BC_CA.ZIP", "FileSize": "603", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=EC38DF0ECC1A1147A14295402E31AE18"}, {"FileName": "Benefits_of_ASU.pdf", "FileSize": "119", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=F5C1898575F91F4AA10E14A045DC8B78"}, {"FileName": "AXAK028295.zip", "FileSize": "249", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=FD21C2C92264674DAD569ED877D0E428"}, {"FileName": "Upgrade_Guide_PP.pdf", "FileSize": "273", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000431072003&iv_version=0069&iv_guid=FE015642EB87764FB50E148724BE04D4"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "849317", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/849317"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "758375", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "ASU: VARID-AEDAT not filled after restore", "RefUrl": "/notes/758375"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "733519", "RefComponent": "CO-OM-OPA-F", "RefTitle": "Settlement: No list output for reports RKO7xxxx", "RefUrl": "/notes/733519"}, {"RefNumber": "716345", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/716345"}, {"RefNumber": "712297", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Short dumps occur if you restore variants when upgrading", "RefUrl": "/notes/712297"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "689192", "RefComponent": "XX-CSC-US-WE-WM", "RefTitle": "WEC Release 1: General Fixes # 66", "RefUrl": "/notes/689192"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "626408", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Upgrade: Restoring variants during the release upgrade", "RefUrl": "/notes/626408"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "1000009", "RefComponent": "SV-SMG-ASU", "RefTitle": "ASU Toolbox 2008", "RefUrl": "/notes/1000009"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1000009", "RefComponent": "SV-SMG-ASU", "RefTitle": "ASU Toolbox 2008", "RefUrl": "/notes/1000009 "}, {"RefNumber": "712297", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Short dumps occur if you restore variants when upgrading", "RefUrl": "/notes/712297 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "626408", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Upgrade: Restoring variants during the release upgrade", "RefUrl": "/notes/626408 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "758375", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "ASU: VARID-AEDAT not filled after restore", "RefUrl": "/notes/758375 "}, {"RefNumber": "733519", "RefComponent": "CO-OM-OPA-F", "RefTitle": "Settlement: No list output for reports RKO7xxxx", "RefUrl": "/notes/733519 "}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240 "}, {"RefNumber": "689192", "RefComponent": "XX-CSC-US-WE-WM", "RefTitle": "WEC Release 1: General Fixes # 66", "RefUrl": "/notes/689192 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "716345", "RefTitle": "Upgrade: Error when you save the variants", "RefUrl": "/notes/0000716345"}, {"RefNumber": "767038", "RefTitle": "ASU toolbox: Changeability after restore", "RefUrl": "/notes/0000767038"}]}}}}}