{"Request": {"Number": "1119735", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 389, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016425152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001119735?language=E&token=7244BD1A930A5628E0C8F84D74EE800E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001119735", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001119735/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1119735"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2013.04.10"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON"}, "SAPComponentKeyText": {"_label": "Component", "value": "CCMS Monitoring & Alerting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1119735 - CCMS agents: Upgrade of monitored systems from 7.0 to 7.1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You monitor systems of Release SAP NetWeaver 7.0 as part of the CCMS monitoring infrastructure using CCMS agents. You require information about the steps that you have to perform during the upgrade of the monitored systems to SAP NetWeaver 7.1 with regard to the CCMS agents.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>For a system, you want to upgrade from SAP NetWeaver 7.0 to SAP NetWeaver 7.1. This SAP Note describes the things you must take into account with regard to monitoring using CCMS agents, since the relevant infrastructure was further developed with the upgrade from 7.0 to 7.1. The following system types are covered:</p> <UL><LI>pure ABAP systems</LI></UL> <UL><LI>pure Java systems</LI></UL> <UL><LI>Double stack systems (ABAP + Java)</LI></UL> <UL><LI>TREX</LI></UL> <b>Background information:</b><br /> <p>Between the central and the monitored system, the communications protocol was converted from RFC to Web Services in the kernel 710.<br /><br />The functions of the stand-alone CCMS agents sapccm4x and sapccmsr were integrated into the sapstartsrv as a static library. This integrated CCMS monitoring function is called an integrated CCMS agent.<br /><br />The integrated CCMS agent is started automatically by default. You can prevent the automatic start of the integrated CCMS agent by adding the profile parameter \"ccms/enable_agent = 0\" in the start profile used.<br /><br />The monitoring functions are executed as a separate thread within sapstartsrv. The thread connects to the monitoring segment of the monitored instance. Applications can access the sapstartsrv monitoring functions using a Web Service interface. This interface replaces the RFC server part of the stand-alone CCMS agents.<br /><br />The advantage of this change is that each kernel update refreshes the CCMS agent automatically. This prevents errors that occurred in the past due to the incompatibility of the shared memory format used by the kernel and the CCMS agents.<br />(http://help.sap.com/saphelp_nwpi71/helpdata/en/44/86b3b2cc8812d2e10000000a422035/frameset.htm).<br /></p> <b>Double stack systems</b><br /> <UL><LI>Up to Kernel Release 7.01, the Java part is monitored by the stand-alone CCMS agent sapccmsr -j2ee. This agent creates its own monitor segment. To be able to see this segment in transaction RZ20, register this CCMS agent with the ABAP part (locally). This way, the system can be monitored as one single unit. In transaction RZ21 under \"Segment Overview\", you can display both segments of the instance: the segment of the ABAP part in the form &lt;SID&gt;_&lt;host name&gt;_&lt;instance number&gt; and the segment of the Java part in the form &lt;SID&gt;_&lt;host name&gt;_&lt;instance number&gt;_X.</LI></UL> <UL><LI>As of Kernel Release 7.10, both parts of the instance use a common monitoring segment that has the form &lt;SID&gt;_&lt;host name&gt;_&lt;instance number&gt;. This is a segment of the type of the CCMS agent sapccm4x that is integrated in sapstartsrv and that is therefore started by default. You must no longer use the stand-alone CCMS agent sapccmsr -j2ee for this instance.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Preface:<br />When you proceed as described below, you must move certain directory contents, since some of the CCMS agent directories have changed between SAP NetWeaver Version 7.0 and 7.1. These directories are located under the following paths:</p> <UL><LI>sapccmsr -j2ee (for monitored Java systems and TREX systems)</LI></UL> <UL><UL><LI>Working directory:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NW 7. 0 (Unix): /usr/sap/ccms/&lt;SysID&gt;_&lt;SysNr&gt;/sapccmsr<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NW 7. 0 (Windows): [drive]:\\usr\\sap\\ccms\\&lt;SysID&gt;_&lt;InstNr&gt;\\sapccmsr<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NW 7. 1: $DIR_LOGGING\\sapccmsr, by default &lt;instance directory&gt;\\log\\sapccmsr</p> <UL><UL><LI>Logmon directory:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NW 7. 0 (Unix): /usr/sap/ccms/&lt;SysID&gt;_&lt;InstNr&gt;/logmon<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NW 7. 0 (Windows): [drive]:\\usr\\sap\\ccms\\&lt;SysID&gt;_&lt;InstNr&gt;\\logmon<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NW 7. 1: $DIR_LOGGING\\logmon, by default &lt;instance directory&gt;\\log\\logmon</p> <UL><LI>sapccm4x (for monitored ABAP systems)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following directories do not change during the upgrade from SAP NetWeaver 7.0 to 7.1:</p> <UL><UL><LI>Working directory: $DIR_LOGGING\\sapccm4x, by default &lt;instance directory&gt;\\log\\sapccm4x</LI></UL></UL> <UL><UL><LI>Logmon directory: $DIR_LOGGING\\logmon, by default &lt;instance directory&gt;\\log\\logmon</LI></UL></UL> <p><br />Depending on the release of the central monitoring system (CEN) there are different procedures:<br /></p> <b>I. No CEN</b><br /> <p><br />You currently do not use central monitoring as part of the CCMS monitoring infrastructure, that is, you did not register a CCMS agent in CEN for the system that you want to upgrade. Depending on the system type of the monitored system, you must perform the following steps:</p> <UL><LI>pure ABAP system/pure Java system/TREX</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You do not need to perform any further steps with regard to the CCMS agents.</p> <UL><LI>Double stack system</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For a double-stack system up to SAP NetWeaver 7. 0, the stand-alone CCMS agent sapccmsr -j2ee should always be registered to the local ABAP stack, even without central monitoring. As of SAP NetWeaver 7.1, you no longer require a local agent. Therefore, deregister the stand-alone agent in the Visual Administrator of AS Java before the upgrade:</p> <OL><OL>a) In the navigation bar, choose \"Cluster --&gt; &lt;SysID&gt; --&gt; Dispatcher --&gt; Services --&gt; Monitoring\".</OL></OL> <OL><OL>b) Choose the tab \"CCMS Agent Configuration\" and enter the passwords for the specified users.</OL></OL> <OL><OL>c) Choose \"Unregister\".</OL></OL> <p></p> <b>II. CEN with Release SAP NetWeaver 7.0</b><br /> <p><br />For the central monitoring, you use a system with Release SAP NetWeaver 7.0 (without enhancement package; for example, if you use a previous Solution Manager). Therefore, you must also use the stand-alone CCMS agents for central monitoring after the upgrade of your monitored system to SAP NetWeaver 7.1 (see SAP Note 1368389).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that, in this scenario, you always require the stand-alone CCMS agent sapccm4x for monitoring an ABAP system or a double-stack system with Release SAP NetWeaver 7.1. This means that a registration of the monitored ABAP stack using only RFC destinations (transaction RZ21, menu \"Technical infrastructure -&gt; Configure Central System -&gt; Create remote monitoring entry\") is not sufficient.<br /><br />Before the upgrade, use the command \"-stop\" to stop all CCMS agents of the monitored system. The executables are located in the directory DIR_EXECUTABLE. Further information is available on the SAP Help Portal at: http://help.sap.com/saphelp_nw70/helpdata/en/8b/f64352e1bfbd4eb43e432860504a1c/frameset.htm<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can determine all agents that are registered to a CEN for a monitored system by calling the topology browser in CEN. For this, call transaction RZ21 and, in the frame \"Topology\" select \"Agents for Remote Systems\" and then choose \"Display Overview\".<br /><br />For a double-stack system, also deregister the agent sapccmsr -j2ee the same way as described above for double-stack systems without CEN.<br />Depending on the system type of the monitored system, you must also perform the following steps:<br /></p> <UL><LI>pure ABAP system</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After the upgrade, you can restart the agent with the command \"-DCCMS\". The executable is located in the same place as before the upgrade. Further information is available at the link mentioned above.</p> <UL><LI>pure Java system</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Perform the following steps:</p> <OL><OL>a) The working directory and the standard directory for the Logmon templates change for the CCMS agent sapccmsr -j2ee from NW 7.0 to 7.1. You must move the entire directory contents from the old directories to the new directories (see path above). If these directories do not exist for SAP NetWeaver 7.1, create them manually.</OL></OL> <OL><OL>b) After the upgrade, you can restart the agent with the command \"-DCCMS\". The executable is located in the same place as before the upgrade.</OL></OL> <UL><LI>Double stack system</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Perform the following steps:</p> <OL><OL>a) Move the content of the Logmon directory of the agent sapccmsr -j2ee (NW 7.0) to the Logmon directory of the agent sapccm4x (NW 7.1). If the target directory does not exist, create it manually under the path specified.</OL></OL> <OL><OL>b) The functions of sapccmsr -j2ee are executed by sapccm4x after the upgrade. If you have made manual settings in the configuration file sapccmsr.ini of sapccmsr -j2ee (NW 7.0), you must transfer these entries manually to the configuration file sapccmsr.ini of sapccm4x (NW 7.1). The configuration file sapccmsr.ini is located in the working directory of the relevant agent.</OL></OL> <OL><OL>c) After the upgrade and the transfer of the above contents, you can restart the agent sapccm4x with the command \"-DCCMS\". The executable is located in the same place as before the upgrade.</OL></OL> <UL><LI>TREX</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Proceed as described in the following documentation: http://help.sap.com/saphelp_nw70/helpdata/en/46/cbefbb63a668dfe10000000a114a6b/frameset.htm<br /></p> <b>III. CEN with a SAP NetWeaver Release 7.1 or SAP NetWeaver Release 7.0 with Enhancement Package 1</b><br /> <p><br />You use a system with Release SAP NetWeaver 7.1 or SAP NetWeaver 7.0 with Enhancement Package 1 for central monitoring. Therefore, you no longer require a stand-alone CCMS agent for this system after upgrading the monitored system. Perform the following steps:</p> <OL>1. Before the upgrade, deregister all stand-alone CCMS agents of the relevant monitored system.</OL> <OL>2. As of SAP NetWeaver kernel 7.10 (including SAP NetWeaver systems 7.0 with Enhancement Package 2 because they use kernel 7.20 as a downward-compatible kernel), all of the functions of these agents are integrated into the SAP NetWeaver management agents (sapstartsrv). For this reason, you no longer have to use the CCMS agents as stand-alone executables as of this release. However, the SAP NetWeaver Management agents take the information in the specified working directories and Logmon directories of the CCMS agents into account. Therefore, move the contents of these directories as described in the scenario \"CEN with Release SAP NetWeaver 7.0\" for the relevant system type.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Further information about SAP NetWeaver Management agents is available on the SAP Help Portal at: http://help.sap.com/saphelp_nwpi71/helpdata/en/44/86b14bcc8812d2e10000000a422035/frameset.htm <OL>3. Since you deregistered the stand-alone CCMS agents, you cannot restart them. Therefore, reregister the monitored system in CEN after the upgrade by calling transaction RZ21 in CEN and choosing \"Technical Infrastructure -&gt; Configure Central System -&gt; Create remote monitoring entry\" from the menu.</OL> <OL>4. As component type, choose the system type of the monitored system (ABAP, Java, double stack, or TREX). Further information is available on the SAP Help Portal at: http://help.sap.com/saphelp_nwpi71/helpdata/en/44/893e933dc912d3e10000000a422035/frameset.htm</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I047532)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D056056)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001119735/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119735/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "954980", "RefComponent": "BC-CCM-MON", "RefTitle": "RZ20: CEN NW04s monitors SAP NetWeaver 7.1 system", "RefUrl": "/notes/954980"}, {"RefNumber": "1760861", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR data in double stack with SAP_BASIS 7.00", "RefUrl": "/notes/1760861"}, {"RefNumber": "1667336", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Monitoring with Kernel 7.20 (DCK)", "RefUrl": "/notes/1667336"}, {"RefNumber": "1616488", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR statistics", "RefUrl": "/notes/1616488"}, {"RefNumber": "1547201", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Start and stop standalone agents", "RefUrl": "/notes/1547201"}, {"RefNumber": "1503112", "RefComponent": "BC-CCM-MON", "RefTitle": "Obsolete MTE nodes in CCMS monitoring segment (Error-253)", "RefUrl": "/notes/1503112"}, {"RefNumber": "1373897", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: No Customizing destination for J2EE segment", "RefUrl": "/notes/1373897"}, {"RefNumber": "1368389", "RefComponent": "BC-CCM-MON", "RefTitle": "Re-activating RFC-communication for CCMS Agents", "RefUrl": "/notes/1368389"}, {"RefNumber": "1368387", "RefComponent": "BC-CCM-MON", "RefTitle": "A guideline to interoperability of different CEN generations", "RefUrl": "/notes/1368387"}, {"RefNumber": "1304555", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: New registration of monitored components", "RefUrl": "/notes/1304555"}, {"RefNumber": "1136330", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agent in sapstartsrv: <PERSON><PERSON> 2008", "RefUrl": "/notes/1136330"}, {"RefNumber": "1093900", "RefComponent": "BC-TRX", "RefTitle": "Upgrade from TREX 7.00 to TREX 7.10", "RefUrl": "/notes/1093900"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2203184", "RefComponent": "BC-CCM-MON-SHM", "RefTitle": "CCMS: agents fail to display permitted file content, logical directory or profile, wrong measurement of ResponseTimeDialogRFC", "RefUrl": "/notes/2203184 "}, {"RefNumber": "1787260", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Mutual activity control of agents", "RefUrl": "/notes/1787260 "}, {"RefNumber": "1453112", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agent and kernel patches", "RefUrl": "/notes/1453112 "}, {"RefNumber": "1368389", "RefComponent": "BC-CCM-MON", "RefTitle": "Re-activating RFC-communication for CCMS Agents", "RefUrl": "/notes/1368389 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1616488", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR statistics", "RefUrl": "/notes/1616488 "}, {"RefNumber": "1760861", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR data in double stack with SAP_BASIS 7.00", "RefUrl": "/notes/1760861 "}, {"RefNumber": "1503112", "RefComponent": "BC-CCM-MON", "RefTitle": "Obsolete MTE nodes in CCMS monitoring segment (Error-253)", "RefUrl": "/notes/1503112 "}, {"RefNumber": "1304555", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: New registration of monitored components", "RefUrl": "/notes/1304555 "}, {"RefNumber": "1667336", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Monitoring with Kernel 7.20 (DCK)", "RefUrl": "/notes/1667336 "}, {"RefNumber": "1547201", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Start and stop standalone agents", "RefUrl": "/notes/1547201 "}, {"RefNumber": "1373897", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: No Customizing destination for J2EE segment", "RefUrl": "/notes/1373897 "}, {"RefNumber": "1136330", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agent in sapstartsrv: <PERSON><PERSON> 2008", "RefUrl": "/notes/1136330 "}, {"RefNumber": "1368387", "RefComponent": "BC-CCM-MON", "RefTitle": "A guideline to interoperability of different CEN generations", "RefUrl": "/notes/1368387 "}, {"RefNumber": "954980", "RefComponent": "BC-CCM-MON", "RefTitle": "RZ20: CEN NW04s monitors SAP NetWeaver 7.1 system", "RefUrl": "/notes/954980 "}, {"RefNumber": "1093900", "RefComponent": "BC-TRX", "RefTitle": "Upgrade from TREX 7.00 to TREX 7.10", "RefUrl": "/notes/1093900 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}