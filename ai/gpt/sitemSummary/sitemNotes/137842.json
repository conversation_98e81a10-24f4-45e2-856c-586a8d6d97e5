{"Request": {"Number": "137842", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 457, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014644882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000137842?language=E&token=3FD2FEF5D058FC3C7DC4951E0FBFEF77"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000137842", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000137842/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "137842"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.01.2002"}, "SAPComponentKey": {"_label": "Component", "value": "FI-BL-PT-BA"}, "SAPComponentKeyText": {"_label": "Component", "value": "bank statement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Bank-Related Accounting", "value": "FI-BL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-BL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payment Transactions", "value": "FI-BL-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-BL-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "bank statement", "value": "FI-BL-PT-BA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-BL-PT-BA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "137842 - Automatic account statement Austria"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>A detailed delivery of payment information within the account statement was impossible with most Austrian banks. This detailed information was only provided on returned data media (in V2 format).<br />After the entire posting logic is ready in account statement processing, the following solution has been implemented:<br />The banks deliver two files, the first file in MT940 format and the second in V2 format for returned data media. The account statement (first file) contains collective posting information, and the second file delivers the corresponding detailed information.This format now contains all detailed information (structured field 86) and can be imported by the SAP standard programs (RFEBKA00).<br /><br />The newly created report is called RFIDATEB00 (in Release 3.1I it is called RFIDATEB). As described already, this report creates a file in the swift MT940 format which is read either by directly executing report RFEBKA00 or which you can transfer to report RFEBKA00 in the background after a suitable selection of the selection parameters of report RFIDEBKA00.<br />However, in both cases you must call report RFIDAT<PERSON>B00 first and execute it. You can only execute report RFEBKA00 after that with the result of report RFIDATEB00, either directly or as described above by calling it in the background after the completion of report RFIDATEB00.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Automatic account statement Austria, V2, V3, Cremul, Debmul, MT940<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The V2 data medium format is replaced by the V3 format, valid as of January 1st, 2002.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Use the Support Package versions listed below:<br />Release&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Support Package &#x00A0;&#x00A0;46C&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;28<BR/> &#x00A0;&#x00A0;46B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;36<BR/> &#x00A0;&#x00A0;45B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;49<BR/> &#x00A0;&#x00A0;40B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;70<BR/> &#x00A0;&#x00A0;31I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;95<br /><br />Report RFIDATEB00 (or RFIDATEB in Release 3.1I) is also available via SAPSERV3 in the directory /general/R3server/abap/note.0137842.<br /><br />Release 3.1I<br />&#x00A0;&#x00A0; - Files: K053930.P3I, R053930.P3I<br />&#x00A0;&#x00A0; - Transport: P3IK053930<br /><br />Release 4.0B<br />&#x00A0;&#x00A0; - Files: K185336.P4B, R185336.P4B<br />&#x00A0;&#x00A0; - Transport:&#x00A0;&#x00A0;P4BK185336<br /><br />Release 4.5B<br />&#x00A0;&#x00A0; - Files: K182440.P4D, R182440.P4D<br />&#x00A0;&#x00A0; - Transport:&#x00A0;&#x00A0;P4DK182440<br /><br />Release 4.6B<br />&#x00A0;&#x00A0; - Files: K198672.P9B, R198672.P9B<br />&#x00A0;&#x00A0; - Transport:&#x00A0;&#x00A0;P9BK198672<br /><br />Release 4.6C<br />&#x00A0;&#x00A0; - Files: K220757.P9C, R220757.P9C<br />&#x00A0;&#x00A0; - Transport:&#x00A0;&#x00A0;P9CK220757<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Responsible                                                                                         ", "Value": "I000190"}, {"Key": "Processor                                                                                           ", "Value": "I000190"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000137842/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000137842/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "513563", "RefComponent": "FI-BL-PT-BA", "RefTitle": "RFIDATEB00: enhancement of note to payee", "RefUrl": "/notes/513563"}, {"RefNumber": "434551", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/434551"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "513563", "RefComponent": "FI-BL-PT-BA", "RefTitle": "RFIDATEB00: enhancement of note to payee", "RefUrl": "/notes/513563 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I91", "URL": "/supportpackage/SAPKH31I91"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I95", "URL": "/supportpackage/SAPKH31I95"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I91", "URL": "/supportpackage/SAPKE31I91"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B70", "URL": "/supportpackage/SAPKH40B70"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B69", "URL": "/supportpackage/SAPKH40B69"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B48", "URL": "/supportpackage/SAPKH45B48"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B49", "URL": "/supportpackage/SAPKH45B49"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B47", "URL": "/supportpackage/SAPKH45B47"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B46", "URL": "/supportpackage/SAPKH45B46"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B45", "URL": "/supportpackage/SAPKH45B45"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B35", "URL": "/supportpackage/SAPKH46B35"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B34", "URL": "/supportpackage/SAPKH46B34"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B33", "URL": "/supportpackage/SAPKH46B33"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C28", "URL": "/supportpackage/SAPKH46C28"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C23", "URL": "/supportpackage/SAPKH46C23"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C27", "URL": "/supportpackage/SAPKH46C27"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C24", "URL": "/supportpackage/SAPKH46C24"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C26", "URL": "/supportpackage/SAPKH46C26"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}