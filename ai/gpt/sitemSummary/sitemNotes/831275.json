{"Request": {"Number": "831275", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 589, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004528282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000831275?language=E&token=6E1B7AD2E1CC52EABB5F9F60307B3C87"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000831275", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000831275/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "831275"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.05.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "831275 - SEFIP: collective agreement and other enhancements"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>- The system does not support the collecting codes 650 and 660;<br />- Fields number 6 and 7 from record type 12, related with income from sponsorship and sport events, are not handled by the system;<br />- Employee name is truncated in record type 30;<br />- The program is not able to properly generate a file with more than one company<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Brazil legal reports INSS FGTS HBRSEF00 diss&#x00ED;dio collective agreement sponsorship sport events<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Missing features.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>From now on three views must be maintained with transaction SM30:<br /><br />V_T7brb7 - sponsorship/sport events data<br />V_t7brb8 - other data reported in record type 12. In this one you might leave the branch field as blank so that the data are assumed to all branches of that company. In case of a different set of data for some branches, those must be informed individually.<br />V_t7br07 - You must customize the company code and responsible branch entries in&#x00A0;&#x00A0;if you want to use several companies. When there are data in this view, the program use them; otherwise the selection screen parameters are taken into account.<br /><br />This solution will be provided via support package.<br /><br />As an alternative, you can install one of the attached files according to your system release. Details on how to install the file can be found in notes 480180, 212876 and 13719. Refer to the correction instructions for details about program code changes.<br /><br />In order to carry out that procedure one should have at least the status provided by notes 909085 and 913024.<br /><br />Following objects were changed/added:<br /><br />- New tables: T7brb7, T7brb8 and t7br07<br />- New maintenance views: V_t7brb7 and V_t7brb8<br />- Data elements: PBR_IMPTA, PBR_INFPF, PBR_INFPI, PBR_IRECE, PBR_OINFV, PBR_OIPAN, PBR_OUINP, PBR_CATY, PBR_EMPRE, PBR_FILIAC, PBR_INEMP, PBR_SIMPL, PBR_SINPS, PBR_SINRE, PBR_SINTY<br />- Domains: PBR_IRECE, PBR_INEMP, PBR_SIMPL, PBR_SINPS, PBR_SINRE, PBR_SINTY, PBR_CATY<br />- Structures HBRXXXX0<br />- Function module: HR_BR_CURRENCY<br />- Message class HRPAYBR99<br />- Programs: HBRSEF00, PC2RXBR0, PCSF3BR0, PCSF4BR1, PCACCBR0<br /><br /><B><B>IMPORTANT</B></B>: The data element PBR_CATY is created by this note for the following releases:<br />- 46C<br />- 470<br />- 600<br /><br />For release 500, the data element is delivery in the note 917854.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D036108)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I811676)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000831275/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L7DK020649_600_first.CAR", "FileSize": "213", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000014852005&iv_version=0028&iv_guid=5A6A170D7302A04DB551C1A0EAC13B2A"}, {"FileName": "L6BK088347.CAR", "FileSize": "223", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000014852005&iv_version=0028&iv_guid=1E03866250A4E3459ADC9D2AD9F65A91"}, {"FileName": "L9CK184117.CAR", "FileSize": "209", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000014852005&iv_version=0028&iv_guid=211D31C6B2A6B942B203D64ACC4CC78F"}, {"FileName": "L7DK021188_600_second.CAR", "FileSize": "3", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000014852005&iv_version=0028&iv_guid=6B8AE1BCCF4CCE49BCCDA76AEF6432D7"}, {"FileName": "L6BK097093.CAR", "FileSize": "152", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000014852005&iv_version=0028&iv_guid=AF671ED513B8F8469B950BD70D2401FD"}, {"FileName": "L6DK047808.CAR", "FileSize": "216", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000014852005&iv_version=0028&iv_guid=9F4B3957DFE14A41A3BFCAB4A0590F90"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "970127", "RefComponent": "PY-BR", "RefTitle": "SEFIP: View V_T7BR07 not accesible through SM30", "RefUrl": "/notes/970127"}, {"RefNumber": "920095", "RefComponent": "PY-BR", "RefTitle": "HBRGPS00 & HBRSEF00: Compensation - New functionality", "RefUrl": "/notes/920095"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1053058", "RefComponent": "PY-BR", "RefTitle": "HBRSEF00 - record 13 with code 426 wrongly generated (updt)", "RefUrl": "/notes/1053058"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "920095", "RefComponent": "PY-BR", "RefTitle": "HBRGPS00 & HBRSEF00: Compensation - New functionality", "RefUrl": "/notes/920095 "}, {"RefNumber": "970127", "RefComponent": "PY-BR", "RefTitle": "SEFIP: View V_T7BR07 not accesible through SM30", "RefUrl": "/notes/970127 "}, {"RefNumber": "1053058", "RefComponent": "PY-BR", "RefTitle": "HBRSEF00 - record 13 with code 426 wrongly generated (updt)", "RefUrl": "/notes/1053058 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CB3", "URL": "/supportpackage/SAPKE46CB3"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47058", "URL": "/supportpackage/SAPKE47058"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50024", "URL": "/supportpackage/SAPKE50024"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60001", "URL": "/supportpackage/SAPKE60001"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60006", "URL": "/supportpackage/SAPKE60006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 4, "URL": "/corrins/0000831275/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 79, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "959871 ", "URL": "/notes/959871 ", "Title": "SEFIP: Actualization of document number ( IT0465 )", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "211535 ", "URL": "/notes/211535 ", "Title": "SEFIP - Alter. para nova versao", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "216877 ", "URL": "/notes/216877 ", "Title": "SEFIP - Correções para a versão 4.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "302086 ", "URL": "/notes/302086 ", "Title": "SEFIP - Acerto para dedução rubrica estorno FGTS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "312839 ", "URL": "/notes/312839 ", "Title": "HBRSEF00 - Acerto para geração do registro 13", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "336571 ", "URL": "/notes/336571 ", "Title": "HBRSEF00 - Acerto para status de ocupação", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "350858 ", "URL": "/notes/350858 ", "Title": "HBRSEF00 - Acerto cálculo do FGTS e base INSS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "370079 ", "URL": "/notes/370079 ", "Title": "Campos 22 e 23 do reg 30 do SEFIP(inf. 13o) não preenchidos", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "831335 ", "URL": "/notes/831335 ", "Title": "HBRSEF00 - \"Data de movimento\" earlier than hire date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "833669 ", "URL": "/notes/833669 ", "Title": "HBRSEF00 record type 30 Allowance Base", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "695827 ", "URL": "/notes/695827 ", "Title": "SEFIP prints wrong value in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "705318 ", "URL": "/notes/705318 ", "Title": "HBRSEF00 doesn't fill in field Percentual of Philanthropy", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "813885 ", "URL": "/notes/813885 ", "Title": "HBRSEF00 and Values related to the pre notice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "764696 ", "URL": "/notes/764696 ", "Title": "SEFIP: CTPS data in record type 13 / 13th salary difference", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "842526 ", "URL": "/notes/842526 ", "Title": "SEFIP - maternity leave return", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "855485 ", "URL": "/notes/855485 ", "Title": "Employee transference in SEFIP", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "600", "Number": "864181 ", "URL": "/notes/864181 ", "Title": "SEFIP: employee without FGTS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "600", "Number": "886077 ", "URL": "/notes/886077 ", "Title": "HBRSEF00 Movement code Q4 not considered for field 20 rec30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "500", "Number": "725084 ", "URL": "/notes/725084 ", "Title": "HBRSEF00: Special Charaters are not converted in all fields", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "433360 ", "URL": "/notes/433360 ", "Title": "Some employees is not considered in report HBRSEF00", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "435847 ", "URL": "/notes/435847 ", "Title": "Payroll stops because there's no FGTS's Bank Account", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "439595 ", "URL": "/notes/439595 ", "Title": "Legal Change SEFIP: new version SEFIP 5.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "452785 ", "URL": "/notes/452785 ", "Title": "Treatment for special characters in brazilian reports", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "483830 ", "URL": "/notes/483830 ", "Title": "HBRSEF00:/123 (negative values) and INSS's Basis with errors", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "490568 ", "URL": "/notes/490568 ", "Title": "Remuneration should not be generated when employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "524750 ", "URL": "/notes/524750 ", "Title": "HBRSEF00 shows positive values for negative basis", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "525497 ", "URL": "/notes/525497 ", "Title": "HBRSEF00 must create 2 records type 32 for movements", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "529879 ", "URL": "/notes/529879 ", "Title": "HBRSEF00 does not fill in Admission Date for Emp.Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "538420 ", "URL": "/notes/538420 ", "Title": "Values in SEFIP for competence 12", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "548186 ", "URL": "/notes/548186 ", "Title": "SEFIP sends an Error Message: Error Reading Infotype 0006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "555883 ", "URL": "/notes/555883 ", "Title": "HBRSEF00 prints error message for Work Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "568150 ", "URL": "/notes/568150 ", "Title": "HBRSEF00 does not create record type 30 and 32 for code=150", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "580408 ", "URL": "/notes/580408 ", "Title": "HBRSEF00 does not print INSS's values", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "623156 ", "URL": "/notes/623156 ", "Title": "HBRSEF00 does not select rehired employee", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "628497 ", "URL": "/notes/628497 ", "Title": "HBRSEF00 creates record type 32 with wrong movement code", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "639150 ", "URL": "/notes/639150 ", "Title": "HBRSEFIP does not create record type 32", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "656009 ", "URL": "/notes/656009 ", "Title": "Small changes in HBRSEF00", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "731819 ", "URL": "/notes/731819 ", "Title": "HBRSEF00: /116 FGTS reversal basis negative not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "738390 ", "URL": "/notes/738390 ", "Title": "HBRSEF00: Rehired employee is rejected", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "739363 ", "URL": "/notes/739363 ", "Title": "HBRSEF00: Cod. Caixa Trab. not taken from IT0009", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "742630 ", "URL": "/notes/742630 ", "Title": "HBRSEF00: Backg.proc. not print. Pernr/T90 not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "779775 ", "URL": "/notes/779775 ", "Title": "HBRSEF00 and maternity average /T85", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "790449 ", "URL": "/notes/790449 ", "Title": "HBRSEF00 business place and centralizer branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "852636 ", "URL": "/notes/852636 ", "Title": "HBRSEF00: Removal from special characters", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "865473 ", "URL": "/notes/865473 ", "Title": "HBRSEF00: Single Absence records", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "875109 ", "URL": "/notes/875109 ", "Title": "SEFIP: code updates", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "910847 ", "URL": "/notes/910847 ", "Title": "SEFIP: fields 20, 22 and 23 of record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "926763 ", "URL": "/notes/926763 ", "Title": "HR-BR: SEFIP - record 32 for competence before firing month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "931624 ", "URL": "/notes/931624 ", "Title": "SEFIP: corrections in records 12 and 30 - maternity leave", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "470", "Number": "611406 ", "URL": "/notes/611406 ", "Title": "HBRSEF00 does not print hire date in record type 14", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "470", "Number": "748388 ", "URL": "/notes/748388 ", "Title": "SEFIP - incorrect leaving date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "500", "Number": "833613 ", "URL": "/notes/833613 ", "Title": "SEFIP - Remuneration Without Christmas Allowance", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "500", "Number": "913024 ", "URL": "/notes/913024 ", "Title": "HBRSEF00 8.0 fixes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "500", "Number": "921993 ", "URL": "/notes/921993 ", "Title": "SEFIP: CBO change in record type 13", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "548186 ", "URL": "/notes/548186 ", "Title": "SEFIP sends an Error Message: Error Reading Infotype 0006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "555883 ", "URL": "/notes/555883 ", "Title": "HBRSEF00 prints error message for Work Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "568150 ", "URL": "/notes/568150 ", "Title": "HBRSEF00 does not create record type 30 and 32 for code=150", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "580408 ", "URL": "/notes/580408 ", "Title": "HBRSEF00 does not print INSS's values", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "623156 ", "URL": "/notes/623156 ", "Title": "HBRSEF00 does not select rehired employee", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "628497 ", "URL": "/notes/628497 ", "Title": "HBRSEF00 creates record type 32 with wrong movement code", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "639150 ", "URL": "/notes/639150 ", "Title": "HBRSEFIP does not create record type 32", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "656009 ", "URL": "/notes/656009 ", "Title": "Small changes in HBRSEF00", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "731819 ", "URL": "/notes/731819 ", "Title": "HBRSEF00: /116 FGTS reversal basis negative not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "739363 ", "URL": "/notes/739363 ", "Title": "HBRSEF00: Cod. Caixa Trab. not taken from IT0009", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "742630 ", "URL": "/notes/742630 ", "Title": "HBRSEF00: Backg.proc. not print. Pernr/T90 not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "779775 ", "URL": "/notes/779775 ", "Title": "HBRSEF00 and maternity average /T85", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "790449 ", "URL": "/notes/790449 ", "Title": "HBRSEF00 business place and centralizer branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "831335 ", "URL": "/notes/831335 ", "Title": "HBRSEF00 - \"Data de movimento\" earlier than hire date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "910847 ", "URL": "/notes/910847 ", "Title": "SEFIP: fields 20, 22 and 23 of record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "913024 ", "URL": "/notes/913024 ", "Title": "HBRSEF00 8.0 fixes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "926763 ", "URL": "/notes/926763 ", "Title": "HR-BR: SEFIP - record 32 for competence before firing month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "738390 ", "URL": "/notes/738390 ", "Title": "HBRSEF00: Rehired employee is rejected", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "833669 ", "URL": "/notes/833669 ", "Title": "HBRSEF00 record type 30 Allowance Base", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "931624 ", "URL": "/notes/931624 ", "Title": "SEFIP: corrections in records 12 and 30 - maternity leave", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "739363 ", "URL": "/notes/739363 ", "Title": "HBRSEF00: Cod. Caixa Trab. not taken from IT0009", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "742630 ", "URL": "/notes/742630 ", "Title": "HBRSEF00: Backg.proc. not print. Pernr/T90 not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "748388 ", "URL": "/notes/748388 ", "Title": "SEFIP - incorrect leaving date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "779775 ", "URL": "/notes/779775 ", "Title": "HBRSEF00 and maternity average /T85", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "790449 ", "URL": "/notes/790449 ", "Title": "HBRSEF00 business place and centralizer branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "831335 ", "URL": "/notes/831335 ", "Title": "HBRSEF00 - \"Data de movimento\" earlier than hire date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "865473 ", "URL": "/notes/865473 ", "Title": "HBRSEF00: Single Absence records", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "875109 ", "URL": "/notes/875109 ", "Title": "SEFIP: code updates", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "910847 ", "URL": "/notes/910847 ", "Title": "SEFIP: fields 20, 22 and 23 of record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "926763 ", "URL": "/notes/926763 ", "Title": "HR-BR: SEFIP - record 32 for competence before firing month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "953990 ", "URL": "/notes/953990 ", "Title": "HBRSEF00 - record 13 with code 426 wrongly generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "926763 ", "URL": "/notes/926763 ", "Title": "HR-BR: SEFIP - record 32 for competence before firing month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "931624 ", "URL": "/notes/931624 ", "Title": "SEFIP: corrections in records 12 and 30 - maternity leave", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}