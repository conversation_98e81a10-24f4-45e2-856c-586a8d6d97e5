{"Request": {"Number": "1005772", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 305, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005990952017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001005772?language=E&token=07EB5FA1AF55907C893B190971E47634"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001005772", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001005772/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1005772"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2007.07.11"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analyzing Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1005772 - Formula variables with replacement from key"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to replace a formula variable with a replacement path before the aggregation by using the corresponding characteristic from the characteristic key. Due to Note 993286, the system displays 'X' for the result.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>REPPATH, FLAGR, 1ATTRKEY<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In BW 3.x this formula variable is replaced before the aggregation in calculated key figures and it is replaced after the aggregation in formulas. In BI 7.0 the replacement always occurs after the aggregation. As a result, the calculated key figure and the formula deliver correct values and the values in the results rows are correct. For further information about problems in BW 3.x, see Note <B>379832</B>.<br />However, in BI 7.0 there is a gap as a result of this. This means that after the upgrade a corresponding calculated key figure still delivers only value 'X'.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>After you implement this correction, you can use program <B>RSR_VAR_REPPATH_AGGREGATION</B> to determine the calculation time for a variable before the aggregation.<br /><br />As an alternative to using these corrections and the corresponding program, you can make the setting in the Query Designer as of Frontend Patch 15. You must create the variable in question as a formula variable with replacement from the attribute \"Characteristic key\" (= 1ATTRKEY).<br /><br />Frontend Patch 15 is available when Note 991095 \"SAPBINews BI 7.0 Support Package 15\", which describes this Support Package in more detail, is released for customers.<br /></p> <UL><LI>SAP NetWeaver 2004s BI</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 12 for SAP NetWeaver 2004s BI (BI Patch 12 or <B>SAPKW70012</B>) into your BI system. The Support Package is available when <B>Note 914306 </B>\"SAPBINews BI 7.0 Support Package 12\", which describes this Support Package in more detail, is released for customers.<br /><br />In urgent cases, you can implement the correction instructions as an advance correction. The correction instructions do not contain any texts so only replacement texts are shown.<br /><br /><B>You must first implement Notes 932065, 935140, 948389, 964580, 969846 and 975510,</B> <B>which provide information about transaction SNOTE. Otherwise, problems and syntax errors may occur when you deimplement certain notes. </B><br /><br />To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br />Before you implement an advance correction (if one exists and you want to implement it), see Note 875986. This contains notes regarding the SAP Note Assistant and these notes prevent problems during the implementation.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-OLAP-VAR (Using Variables)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027464)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D041044)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001005772/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001005772/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005772/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005772/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005772/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005772/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005772/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005772/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005772/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "993288", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Composite note: Formula vars. w/ replacement path BI7.0 SP09", "RefUrl": "/notes/993288"}, {"RefNumber": "993286", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Replacement path from key not before the aggregation", "RefUrl": "/notes/993286"}, {"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990"}, {"RefNumber": "914305", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 11", "RefUrl": "/notes/914305"}, {"RefNumber": "1465034", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: dimensions date (time) for formula variable", "RefUrl": "/notes/1465034"}, {"RefNumber": "1385580", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How does a formula variable with a replacement path work?", "RefUrl": "/notes/1385580"}, {"RefNumber": "1228378", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems w/ formula exception aggregation in NetWeaver 2004s", "RefUrl": "/notes/1228378"}, {"RefNumber": "1151957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Interesting facts about the OLAP Processor/Analytic Engine", "RefUrl": "/notes/1151957"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1151957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Interesting facts about the OLAP Processor/Analytic Engine", "RefUrl": "/notes/1151957 "}, {"RefNumber": "1385580", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How does a formula variable with a replacement path work?", "RefUrl": "/notes/1385580 "}, {"RefNumber": "1465034", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: dimensions date (time) for formula variable", "RefUrl": "/notes/1465034 "}, {"RefNumber": "1228378", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems w/ formula exception aggregation in NetWeaver 2004s", "RefUrl": "/notes/1228378 "}, {"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990 "}, {"RefNumber": "914305", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 11", "RefUrl": "/notes/914305 "}, {"RefNumber": "1072356", "RefComponent": "BW-BCT-ERC", "RefTitle": "Warning when you execute a query", "RefUrl": "/notes/1072356 "}, {"RefNumber": "993288", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Composite note: Formula vars. w/ replacement path BI7.0 SP09", "RefUrl": "/notes/993288 "}, {"RefNumber": "993286", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Replacement path from key not before the aggregation", "RefUrl": "/notes/993286 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70012", "URL": "/supportpackage/SAPKW70012"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 5, "URL": "/corrins/0001005772/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 24, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "964756 ", "URL": "/notes/964756 ", "Title": "Formula variables with replacement from difference in BI 7.0", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "967228 ", "URL": "/notes/967228 ", "Title": "X299: System error in SAPLRRI2 and FST_VAR_01-01-", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "970755 ", "URL": "/notes/970755 ", "Title": "Unauthorized termination Brain A206", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "971295 ", "URL": "/notes/971295 ", "Title": "A222 Brain after upgrade to 7.0", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "971611 ", "URL": "/notes/971611 ", "Title": "Formula variable in CKF before or after the aggregation", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "973505 ", "URL": "/notes/973505 ", "Title": "Calculation before the aggregation and formula variables", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "975021 ", "URL": "/notes/975021 ", "Title": "Special handling for InfoSets during aggregation", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "975208 ", "URL": "/notes/975208 ", "Title": "System error in SAPLRRK0 and MEGA_SORT_14-01-", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "976550 ", "URL": "/notes/976550 ", "Title": "Correction of the validation of the exception aggregation", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "977214 ", "URL": "/notes/977214 ", "Title": "GETWA_NOT_ASSIGNED in SAPLRRK0, Form sx_to_fdata_04", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "977917 ", "URL": "/notes/977917 ", "Title": "Error for formulas with attribute variables and constants", "Component": "BW-BEX-OT-OLAP-HIER"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "979398 ", "URL": "/notes/979398 ", "Title": "BRAIN 299 in program SAPLRRI2 and form FEMZ_FUELLEN-98-", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "982328 ", "URL": "/notes/982328 ", "Title": "Time variable with replacement path is not replaced", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "984134 ", "URL": "/notes/984134 ", "Title": "Syst error in program SAPLRR<PERSON>0 and form LRECH_AGGR_ELSE-03-", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "985763 ", "URL": "/notes/985763 ", "Title": "Formulas w/ non-cumulatives, normal key figures + variables", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "987996 ", "URL": "/notes/987996 ", "Title": "X299: System error in SAPLRRI2 and LRECH_F_VAR2-02-", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "992008 ", "URL": "/notes/992008 ", "Title": "Double currency shift if replacement from amount attribute", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "992364 ", "URL": "/notes/992364 ", "Title": "System error in SAPLRRK0 and FBDATA_TO_FDATA-01-", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "992567 ", "URL": "/notes/992567 ", "Title": "X299: System error in SAPLRRI2 and CHF_DUPL_BASIS-01-", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "993045 ", "URL": "/notes/993045 ", "Title": "Stocks and formula variables with replacement path", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "993286 ", "URL": "/notes/993286 ", "Title": "Replacement path from key not before the aggregation", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "1001134 ", "URL": "/notes/1001134 ", "Title": "Incorrect data for normal exception aggregation and formulas", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "1005772 ", "URL": "/notes/1005772 ", "Title": "Formula variables with replacement from key", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "700", "ValidTo": "700", "Number": "978327 ", "URL": "/notes/978327 ", "Title": "Calculating with hierarchy attrib plus/minus sign reversal", "Component": "BW-BEX-OT-OLAP-VAR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}