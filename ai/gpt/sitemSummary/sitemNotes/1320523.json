{"Request": {"Number": "1320523", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 718, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016758742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001320523?language=E&token=956999F1D73EE6178F5C08F79E8B157F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001320523", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001320523/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1320523"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.12.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-XI"}, "SAPComponentKeyText": {"_label": "Component", "value": "NetWeaver Process Integration (PI)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "NetWeaver Process Integration (PI)", "value": "BC-XI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-XI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1320523 - XI 30 Support Package Stack (SPS) 24"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to import SAP Exchange Infrastructure (XI) 3.0 Support Package<br />Stack 24 using the Support Package Stack Guide.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP Exchange Infrastructure, XI 30, SAPXITOOL, SAPXIAF, SAPXIAFC,<br />SAPXICONS, SAPXIPCK, SPS24, Support Package Stack 24</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Support Package 24 contains all of the attached XI corrections. We have corrected some functions, as well as made the corrections mentioned in the SAP Notes.<br />Import Support Package Stack 24 for SAP Exchange Infrastructure using the Support Package Stack Guide. The guide can be found on the service market place as described in SAP Note 952402.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037120)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D049116)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001320523/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320523/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1309542", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Several open connections noticed in DB2", "RefUrl": "/notes/1309542"}, {"RefNumber": "1301864", "RefComponent": "BC-XI-IS-SLD", "RefTitle": "XI-Runtime: Error when registering AAE in SLD", "RefUrl": "/notes/1301864"}, {"RefNumber": "1299599", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Checking parameters for send steps", "RefUrl": "/notes/1299599"}, {"RefNumber": "1297495", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "File adapter: NegativeArrayException occurs while Archiving", "RefUrl": "/notes/1297495"}, {"RefNumber": "1297288", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI/PI CCMS Alert Monitor: Navigation to Message Monitor", "RefUrl": "/notes/1297288"}, {"RefNumber": "1297282", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "MTE class 'SXMBSysErrorAlerts' - Incorrect alert severity", "RefUrl": "/notes/1297282"}, {"RefNumber": "1296819", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Configuring Maximum Message Size Limits for OOM Error", "RefUrl": "/notes/1296819"}, {"RefNumber": "1296610", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Restriction of display in RWB performance monitor", "RefUrl": "/notes/1296610"}, {"RefNumber": "1295746", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Error in event-driven message processing", "RefUrl": "/notes/1295746"}, {"RefNumber": "1295430", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "First File Adapter message fails after a MS restart", "RefUrl": "/notes/1295430"}, {"RefNumber": "1294312", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI alerting: SXMS_TO_ADAPTER_ERRTXT is truncated", "RefUrl": "/notes/1294312"}, {"RefNumber": "1294122", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/PI7.x: Archiving the tables IDXSNDPOR and IDXRCVPOR", "RefUrl": "/notes/1294122"}, {"RefNumber": "1290443", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Audit logs missing for async messages", "RefUrl": "/notes/1290443"}, {"RefNumber": "1286336", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "SWIA: New mass-capable ‘Logically Delete’ function", "RefUrl": "/notes/1286336"}, {"RefNumber": "1286041", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Writing the receiver for acknowledgement msgs", "RefUrl": "/notes/1286041"}, {"RefNumber": "1283701", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Component Monitoring: Javascript error on page", "RefUrl": "/notes/1283701"}, {"RefNumber": "1283325", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "Receiver file adapter thread hanging", "RefUrl": "/notes/1283325"}, {"RefNumber": "1283145", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "JDBC message fails after Messaging System restarts.", "RefUrl": "/notes/1283145"}, {"RefNumber": "1283089", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "BLOB, CLOB Null value check is not performed", "RefUrl": "/notes/1283089"}, {"RefNumber": "1283061", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Incomplete payload received at IE from AE", "RefUrl": "/notes/1283061"}, {"RefNumber": "1282745", "RefComponent": "BC-XI-IS", "RefTitle": "X13.0: RSXMB_DEL_TO_ARCH does not find messages", "RefUrl": "/notes/1282745"}, {"RefNumber": "1281584", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: HTTP protocol checks", "RefUrl": "/notes/1281584"}, {"RefNumber": "1272854", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Synchronous message with the same ID", "RefUrl": "/notes/1272854"}, {"RefNumber": "1272512", "RefComponent": "BC-XI-IS", "RefTitle": "XI mapping: Endless loop in split mapping", "RefUrl": "/notes/1272512"}, {"RefNumber": "1268238", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Duplicate segment fields in XML", "RefUrl": "/notes/1268238"}, {"RefNumber": "1265534", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "Archive faulty files does not work with add time stamp mode", "RefUrl": "/notes/1265534"}, {"RefNumber": "1265222", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "endSeparator field is not removed while content coversion", "RefUrl": "/notes/1265222"}, {"RefNumber": "1263224", "RefComponent": "BC-XI-IBC-MAP", "RefTitle": "Mapping of Fault Messages", "RefUrl": "/notes/1263224"}, {"RefNumber": "1262006", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: HTTP response with request GUID", "RefUrl": "/notes/1262006"}, {"RefNumber": "1261933", "RefComponent": "BC-XI-IBD-MAP", "RefTitle": "Context Caching Function behaviour change since 1158485", "RefUrl": "/notes/1261933"}, {"RefNumber": "1260178", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Status shows a red \"LED\" after activation of indexing", "RefUrl": "/notes/1260178"}, {"RefNumber": "1259515", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "'clusterSyncMode' is available as service property", "RefUrl": "/notes/1259515"}, {"RefNumber": "1259120", "RefComponent": "BC-XI-IS", "RefTitle": "XI30/PI70x/71x: Performance for deletion of history data", "RefUrl": "/notes/1259120"}, {"RefNumber": "1257633", "RefComponent": "BC-XI-IBC-MAP", "RefTitle": "Header Fields in ABAP XSLT Mappings called in the ccBPM", "RefUrl": "/notes/1257633"}, {"RefNumber": "1257135", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Message Overview: numbers for selected time period wrong", "RefUrl": "/notes/1257135"}, {"RefNumber": "1255110", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Adapter status of processed message is not final", "RefUrl": "/notes/1255110"}, {"RefNumber": "1250500", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Performance Monitoring: no data for Value Help", "RefUrl": "/notes/1250500"}, {"RefNumber": "1250136", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: System acknowledgements with B2B hop list", "RefUrl": "/notes/1250136"}, {"RefNumber": "1249370", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN:Synchronous message proxies are deleteable too early", "RefUrl": "/notes/1249370"}, {"RefNumber": "1247703", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Message performance alerts: \"0\" is always reported", "RefUrl": "/notes/1247703"}, {"RefNumber": "1246809", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "AuditLog entries not getting deleted", "RefUrl": "/notes/1246809"}, {"RefNumber": "1246084", "RefComponent": "BC-XI-CON-MSG", "RefTitle": "Error while loading system status from messaging system", "RefUrl": "/notes/1246084"}, {"RefNumber": "1243262", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Inconsistency between Message Overview & Message Monitoring", "RefUrl": "/notes/1243262"}, {"RefNumber": "1240862", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Incorrect password returned when ispassword=false in AMD", "RefUrl": "/notes/1240862"}, {"RefNumber": "1237883", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Monitored Services do not appear in Monitoring Service/CCMS", "RefUrl": "/notes/1237883"}, {"RefNumber": "1235902", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-MON: SWF_XI_SWPR: Incorr response for multiple selection", "RefUrl": "/notes/1235902"}, {"RefNumber": "1235555", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: SWWL dumps if rdisp/max_wprun_time is not numeric", "RefUrl": "/notes/1235555"}, {"RefNumber": "1228675", "RefComponent": "BC-XI-CON-MSG", "RefTitle": "Missing <PERSON><PERSON> Log entries", "RefUrl": "/notes/1228675"}, {"RefNumber": "1225693", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "Corrupt Japanese chars in ftp mode and j2ee hang with ftp", "RefUrl": "/notes/1225693"}, {"RefNumber": "1178403", "RefComponent": "BC-XI-IS-IEN", "RefTitle": "Archiving: Large Messages with too many versions correction", "RefUrl": "/notes/1178403"}, {"RefNumber": "1170710", "RefComponent": "BC-XI", "RefTitle": "XI 30 Support Package Stack (SPS) 23", "RefUrl": "/notes/1170710"}, {"RefNumber": "1162450", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/7.00: F4 help archiving in application systems", "RefUrl": "/notes/1162450"}, {"RefNumber": "1153602", "RefComponent": "BC-XI-CON-MSG", "RefTitle": "Improved performance at high message loads", "RefUrl": "/notes/1153602"}, {"RefNumber": "1131655", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "MessageTransformBean: SimpleXML2Plain encoding parameter", "RefUrl": "/notes/1131655"}, {"RefNumber": "1131651", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "FTP receiver adapter deletes file with append mode", "RefUrl": "/notes/1131651"}, {"RefNumber": "1113757", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/7.0x/7.1x: Clean-up of the history table", "RefUrl": "/notes/1113757"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1259120", "RefComponent": "BC-XI-IS", "RefTitle": "XI30/PI70x/71x: Performance for deletion of history data", "RefUrl": "/notes/1259120 "}, {"RefNumber": "1283145", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "JDBC message fails after Messaging System restarts.", "RefUrl": "/notes/1283145 "}, {"RefNumber": "1113757", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/7.0x/7.1x: Clean-up of the history table", "RefUrl": "/notes/1113757 "}, {"RefNumber": "1296610", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Restriction of display in RWB performance monitor", "RefUrl": "/notes/1296610 "}, {"RefNumber": "1259515", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "'clusterSyncMode' is available as service property", "RefUrl": "/notes/1259515 "}, {"RefNumber": "1261933", "RefComponent": "BC-XI-IBD-MAP", "RefTitle": "Context Caching Function behaviour change since 1158485", "RefUrl": "/notes/1261933 "}, {"RefNumber": "1299599", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Checking parameters for send steps", "RefUrl": "/notes/1299599 "}, {"RefNumber": "1290443", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Audit logs missing for async messages", "RefUrl": "/notes/1290443 "}, {"RefNumber": "1153602", "RefComponent": "BC-XI-CON-MSG", "RefTitle": "Improved performance at high message loads", "RefUrl": "/notes/1153602 "}, {"RefNumber": "1228675", "RefComponent": "BC-XI-CON-MSG", "RefTitle": "Missing <PERSON><PERSON> Log entries", "RefUrl": "/notes/1228675 "}, {"RefNumber": "1246809", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "AuditLog entries not getting deleted", "RefUrl": "/notes/1246809 "}, {"RefNumber": "1283061", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Incomplete payload received at IE from AE", "RefUrl": "/notes/1283061 "}, {"RefNumber": "1297495", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "File adapter: NegativeArrayException occurs while Archiving", "RefUrl": "/notes/1297495 "}, {"RefNumber": "1265222", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "endSeparator field is not removed while content coversion", "RefUrl": "/notes/1265222 "}, {"RefNumber": "1265534", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "Archive faulty files does not work with add time stamp mode", "RefUrl": "/notes/1265534 "}, {"RefNumber": "1295746", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Error in event-driven message processing", "RefUrl": "/notes/1295746 "}, {"RefNumber": "1240862", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Incorrect password returned when ispassword=false in AMD", "RefUrl": "/notes/1240862 "}, {"RefNumber": "1257135", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Message Overview: numbers for selected time period wrong", "RefUrl": "/notes/1257135 "}, {"RefNumber": "1255110", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Adapter status of processed message is not final", "RefUrl": "/notes/1255110 "}, {"RefNumber": "1286336", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "SWIA: New mass-capable ‘Logically Delete’ function", "RefUrl": "/notes/1286336 "}, {"RefNumber": "1237883", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Monitored Services do not appear in Monitoring Service/CCMS", "RefUrl": "/notes/1237883 "}, {"RefNumber": "1309542", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Several open connections noticed in DB2", "RefUrl": "/notes/1309542 "}, {"RefNumber": "1296819", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Configuring Maximum Message Size Limits for OOM Error", "RefUrl": "/notes/1296819 "}, {"RefNumber": "1225693", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "Corrupt Japanese chars in ftp mode and j2ee hang with ftp", "RefUrl": "/notes/1225693 "}, {"RefNumber": "1131651", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "FTP receiver adapter deletes file with append mode", "RefUrl": "/notes/1131651 "}, {"RefNumber": "1283089", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "BLOB, CLOB Null value check is not performed", "RefUrl": "/notes/1283089 "}, {"RefNumber": "1283325", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "Receiver file adapter thread hanging", "RefUrl": "/notes/1283325 "}, {"RefNumber": "1295430", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "First File Adapter message fails after a MS restart", "RefUrl": "/notes/1295430 "}, {"RefNumber": "1243262", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Inconsistency between Message Overview & Message Monitoring", "RefUrl": "/notes/1243262 "}, {"RefNumber": "1170710", "RefComponent": "BC-XI", "RefTitle": "XI 30 Support Package Stack (SPS) 23", "RefUrl": "/notes/1170710 "}, {"RefNumber": "1283701", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Component Monitoring: Javascript error on page", "RefUrl": "/notes/1283701 "}, {"RefNumber": "1249370", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN:Synchronous message proxies are deleteable too early", "RefUrl": "/notes/1249370 "}, {"RefNumber": "1250500", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Performance Monitoring: no data for Value Help", "RefUrl": "/notes/1250500 "}, {"RefNumber": "1131655", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "MessageTransformBean: SimpleXML2Plain encoding parameter", "RefUrl": "/notes/1131655 "}, {"RefNumber": "1246084", "RefComponent": "BC-XI-CON-MSG", "RefTitle": "Error while loading system status from messaging system", "RefUrl": "/notes/1246084 "}, {"RefNumber": "1178403", "RefComponent": "BC-XI-IS-IEN", "RefTitle": "Archiving: Large Messages with too many versions correction", "RefUrl": "/notes/1178403 "}, {"RefNumber": "1297288", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI/PI CCMS Alert Monitor: Navigation to Message Monitor", "RefUrl": "/notes/1297288 "}, {"RefNumber": "1301864", "RefComponent": "BC-XI-IS-SLD", "RefTitle": "XI-Runtime: Error when registering AAE in SLD", "RefUrl": "/notes/1301864 "}, {"RefNumber": "1297282", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "MTE class 'SXMBSysErrorAlerts' - Incorrect alert severity", "RefUrl": "/notes/1297282 "}, {"RefNumber": "1294122", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/PI7.x: Archiving the tables IDXSNDPOR and IDXRCVPOR", "RefUrl": "/notes/1294122 "}, {"RefNumber": "1294312", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI alerting: SXMS_TO_ADAPTER_ERRTXT is truncated", "RefUrl": "/notes/1294312 "}, {"RefNumber": "1281584", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: HTTP protocol checks", "RefUrl": "/notes/1281584 "}, {"RefNumber": "1286041", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Writing the receiver for acknowledgement msgs", "RefUrl": "/notes/1286041 "}, {"RefNumber": "1282745", "RefComponent": "BC-XI-IS", "RefTitle": "X13.0: RSXMB_DEL_TO_ARCH does not find messages", "RefUrl": "/notes/1282745 "}, {"RefNumber": "1272512", "RefComponent": "BC-XI-IS", "RefTitle": "XI mapping: Endless loop in split mapping", "RefUrl": "/notes/1272512 "}, {"RefNumber": "1250136", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: System acknowledgements with B2B hop list", "RefUrl": "/notes/1250136 "}, {"RefNumber": "1260178", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Status shows a red \"LED\" after activation of indexing", "RefUrl": "/notes/1260178 "}, {"RefNumber": "1272854", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Synchronous message with the same ID", "RefUrl": "/notes/1272854 "}, {"RefNumber": "1268238", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Duplicate segment fields in XML", "RefUrl": "/notes/1268238 "}, {"RefNumber": "1263224", "RefComponent": "BC-XI-IBC-MAP", "RefTitle": "Mapping of Fault Messages", "RefUrl": "/notes/1263224 "}, {"RefNumber": "1257633", "RefComponent": "BC-XI-IBC-MAP", "RefTitle": "Header Fields in ABAP XSLT Mappings called in the ccBPM", "RefUrl": "/notes/1257633 "}, {"RefNumber": "1262006", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: HTTP response with request GUID", "RefUrl": "/notes/1262006 "}, {"RefNumber": "1247703", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Message performance alerts: \"0\" is always reported", "RefUrl": "/notes/1247703 "}, {"RefNumber": "1162450", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/7.00: F4 help archiving in application systems", "RefUrl": "/notes/1162450 "}, {"RefNumber": "1235555", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: SWWL dumps if rdisp/max_wprun_time is not numeric", "RefUrl": "/notes/1235555 "}, {"RefNumber": "1235902", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-MON: SWF_XI_SWPR: Incorr response for multiple selection", "RefUrl": "/notes/1235902 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_XITOOL", "From": "3.0", "To": "3.0", "Subsequent": ""}, {"SoftwareComponent": "SAP_XIAF", "From": "3.0", "To": "3.0", "Subsequent": ""}, {"SoftwareComponent": "SAP_XIPCK", "From": "3.0", "To": "3.0", "Subsequent": ""}, {"SoftwareComponent": "SAP-XIAFC", "From": "3.0", "To": "3.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}