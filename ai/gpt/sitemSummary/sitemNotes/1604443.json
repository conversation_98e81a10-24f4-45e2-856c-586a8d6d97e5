{"Request": {"Number": "1604443", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 612, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009527562017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=77CE6E763F33FB05B5049B2221310E18"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1604443"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.06.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-EIM-ODP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Operational Data Provisioning (ODP) in Search&Analytics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enterprise Information Management", "value": "BC-EIM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-EIM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operational Data Provisioning (ODP) in Search&Analytics", "value": "BC-EIM-ODP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-EIM-ODP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1604443 - ETL interface: Deleting CX_SY_OPEN_SQL_DB for subscription"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Deleting a subscription on a DataSource terminates with an exception condition of the type CX_SY_OPEN_SQL_DB. The reason for this is duplicate data records during an INSERT on the table ROOSPRMSF.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem occurs if more than one delta initialization exists for the subscription and if the extraction ended with errors for at least one of these initializations.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the Support Package that is relevant for your PI_BASIS release or, in exceptional cases, implement the attached correction instructions using the Note Assistant (transaction SNOTE).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EIM-DS-ODP (SAP ERP Extractors)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D003286)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D003286)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1556037", "RefComponent": "BC-EIM-ODP", "RefTitle": "ETL interface: Termination during delta extraction w/ sel.", "RefUrl": "/notes/1556037"}, {"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883 "}, {"RefNumber": "1556037", "RefComponent": "BC-EIM-ODP", "RefTitle": "ETL interface: Termination during delta extraction w/ sel.", "RefUrl": "/notes/1556037 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI_BASIS", "From": "2005_1_700", "To": "2005_1_700", "Subsequent": ""}, {"SoftwareComponent": "PI_BASIS", "From": "2006_1_700", "To": "2006_1_700", "Subsequent": ""}, {"SoftwareComponent": "PI_BASIS", "From": "701", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "PI_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "PI_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI_BASIS 2005_1_700", "SupportPackage": "SAPKIPYJ7Q", "URL": "/supportpackage/SAPKIPYJ7Q"}, {"SoftwareComponentVersion": "PI_BASIS 2006_1_700", "SupportPackage": "SAPKIPYM16", "URL": "/supportpackage/SAPKIPYM16"}, {"SoftwareComponentVersion": "PI_BASIS 701", "SupportPackage": "SAPK-70111INPIBASIS", "URL": "/supportpackage/SAPK-70111INPIBASIS"}, {"SoftwareComponentVersion": "PI_BASIS 702", "SupportPackage": "SAPK-70210INPIBASIS", "URL": "/supportpackage/SAPK-70210INPIBASIS"}, {"SoftwareComponentVersion": "PI_BASIS 730", "SupportPackage": "SAPK-73004INPIBASIS", "URL": "/supportpackage/SAPK-73004INPIBASIS"}, {"SoftwareComponentVersion": "PI_BASIS 731", "SupportPackage": "SAPK-73101INPIBASIS", "URL": "/supportpackage/SAPK-73101INPIBASIS"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "PI_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/**********/292"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "PI_BASIS", "ValidFrom": "2005_1_700", "ValidTo": "701", "Number": "1556037 ", "URL": "/notes/1556037 ", "Title": "ETL interface: Termination during delta extraction w/ sel.", "Component": "BC-EIM-ODP"}, {"SoftwareComponent": "PI_BASIS", "ValidFrom": "702", "ValidTo": "730", "Number": "1556037 ", "URL": "/notes/1556037 ", "Title": "ETL interface: Termination during delta extraction w/ sel.", "Component": "BC-EIM-ODP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1556037", "RefTitle": "ETL interface: Termination during delta extraction w/ sel.", "RefUrl": "/notes/0001556037"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}