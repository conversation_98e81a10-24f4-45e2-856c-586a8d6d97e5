{"Request": {"Number": "852235", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 379, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016005122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000852235?language=E&token=26E4A9A266936AFE46C2D42E0C448BC6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000852235", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000852235/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "852235"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 165}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.11.2014"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "852235 - Release Restrictions for SAP ERP 6.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note provides information about the limitations in SAP ERP 6.0.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Release, limitations, release limitations<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>When SAP ERP 6.0 was released, there were some limitations concerning the productive use of certain functions. These functions are detailed below.<br /><br /><strong>Note:</strong> SAP ERP 6.0 is new name of the application formerly known as mySAP ERP 2005.<br /><br /><br />Dear customer,<br /><br />We wish to inform you about the following limitations concerning the use in production operation of SAP ERP 6.0. The status of the following information corresponds to the last date on which this note was changed.<br /><br />E-mail the person responsible indicated in the note text, particularly in those cases where consultation with SAP is explicitly required. Continue to enter problem messages on SAP Service Marketplace at service.sap.com.<br /><br />In addition to this note, refer to the following SAP Notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>SAP Note</th><th></th><th>Description</th></tr>\r\n<tr>\r\n<td>741821</td>\r\n<td>&nbsp;</td>\r\n<td>Release Restrictions for mySAP ERP 2004</td>\r\n</tr>\r\n<tr>\r\n<td>852008</td>\r\n<td>&nbsp;</td>\r\n<td>Release Restrictions for SAP NetWeaver</td>\r\n</tr>\r\n<tr>\r\n<td>863532</td>\r\n<td>&nbsp;</td>\r\n<td>mySAP SRM</td>\r\n</tr>\r\n<tr>\r\n<td>874967</td>\r\n<td>&nbsp;</td>\r\n<td>SAP for Retail</td>\r\n</tr>\r\n<tr>\r\n<td>886261</td>\r\n<td>&nbsp;</td>\r\n<td>SAP for Banking in Business Suite 2005</td>\r\n</tr>\r\n<tr>\r\n<td>886262</td>\r\n<td>&nbsp;</td>\r\n<td>SAP for Banking in Business Suite 2004</td>\r\n</tr>\r\n<tr>\r\n<td>943319</td>\r\n<td>&nbsp;</td>\r\n<td>BCA (SAP Deposits): Extended ERP 2005 ramp-up</td>\r\n</tr>\r\n<tr>\r\n<td>901070</td>\r\n<td>&nbsp;</td>\r\n<td>General information on the performance of mySAP ERP 2005</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ol><ol>1.</ol></ol>\r\n<p><strong>Platforms</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For an overview on supported combinations of operating system version with database version, refer to our Product Availability Matrix at service.sap.com/pam.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Further details on supported platforms can be found on SAP Service Martkeplace at service.sap.com/platforms.</p>\r\n<ol><ol>2.</ol></ol>\r\n<p><strong>Compatibility of Components</strong></p>\r\n<ol><ol>a) SAP NetWeaver components</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- The installation of SAP NetWeaver components/usage types BW/BI, XI/PI, EP, MI, and DI together with SAP ECC in the same instance (the same SID) is restricted. For a detailed description, see <strong>SAP Note 855534</strong>.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- For some operating systems, performance restrictions apply to SAP Internet Pricing and Configurator (IPC). For more information, see <strong>S</strong><strong><strong>AP Note 853050</strong></strong>.</p>\r\n<ol><ol>b) Case Management (CM)</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Currently, it is not possible to operate various CM-based applications on a client if these applications are in different areas.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Public Sector Records Management is in a separate area and therefore does not use other CM-based applications, such as Basis CM, Case CRM, or Dispute Management.</p>\r\n<ol><ol>c) SRM Add-On on ERP</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The usage of SAP SRM - as a server or as an addon to ECC 6.0 - within SAP ERP 6.0 is limited in several aspects. Details can be found in the SAP SRM release note <strong>863532</strong>.</p>\r\n<ol><ol>3.</ol></ol>\r\n<p><strong>Migration</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAP has adapted the following solutions in such a way that data must be migrated in the medium term at least when you upgrade to SAP ERP 6.0:</p>\r\n<ol><ol>a) SAP Treasury and Risk Management</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For more information about data migration for SAP Treasury and Risk Management, from release SAP R/3 Enterprise Extension Set 1.10 and earlier releases, see <strong>SAP Note 706952</strong>.</p>\r\n<ol><ol>b) New General Ledger</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If you request for extended support for multiple GAAP accounting or segment reporting, you must consider migrating to the New General Ledger. In this case, you must also take into account the limitations described for New General Ledger in this note.The implementation of the New General Ledger is a project by itself and the technical migration of data from the classic general ledger to the new general ledger is a central part of such a project. Depending on each customer's individual scenario, this migration could mean a significant change to the live database, which must be safeguarded to ensure that historical data is kept intact.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;To guarantee maximum security, SAP actively supports every migration project with a migration service. This technical basic service is based on standard migration scenarios and is provided in the form of migration packages for a fixed price. After you have commissioned this service the migration functions are released by allocating a license key.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For more information, see <strong>SAP note 812919</strong>.</p>\r\n<ol><ol>c) SAP Real Estate Management</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For more information about data migration for SAP Real Estate Management, see <strong>SAP Note 828160</strong>.</p>\r\n<ol><ol>d) SAP Travel Management - BIBE connectivity</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;With SAP ERP 6.0 the BIBE interfaces in SAP Travel Management have been migrated to the SAP Exchange Infrastructure (XI). The new infrastructure replaces the former SAP Business Connector interface. Additional information can be found in <strong>SAP Note 901972</strong>.</p>\r\n<ol><ol>e) SAP Mill Products</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Cable Solution Add-on 4.6C customers upgrading to ERP 6.0 need to follow special procedures. This information is described in \"Migration Cable Solution V46C.1A -&gt; DIMP / ECC-DIMP\" guide. For more information, see <strong>SAP Note 664712</strong>.</p>\r\n<ol><ol>4.</ol></ol>\r\n<p><strong>Country and Language Releases</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Information on country and language-specific releases is available on SAP Service Marketplace at service.sap.com/globalization. For more information about SAP ERP 6.0, see <strong>SAP Note 883072</strong>.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If applications are used in countries where they are not released, SAP cannot ensure that the country-specific legal requirements are fulfilled.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAP uses and recommends Unicode as the standard technology to fulfill multilingual language requirements. MDMP (Multi-Display Multi-Processing) is fully supported by R/3 only. With the availability of Unicode, we recommend that you do not use MDMP due to the limitations and risks that currently exist.</p>\r\n<ol><ol>a) Multi-Display Multi-Processing (MDMP)</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ESS/MSS - Scenarios with ERP-MDMP backend:&#160;&#160;Take note of the prerequisites&#160;&#160;described in the document \"ESS/MSS - MDMP - Scenarios: Challenges and Workarounds\" available on SAP Service Marketplace at service.sap.com/unicode -&gt; Media Library.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For more information, see SAP Service Marketplace at service.sap.com/unicode -&gt; Media Library OR use the following link: http://service.sap.com/&#126;sapidb/011000358700004513892004E. Also refer to <strong>SAP note 896144</strong>. If you have any further questions, contact <EMAIL>.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;MDMP support: SAP uses and recommends Unicode as the standard technology to cover multilingual language requirements. Up to now, only SAP R/3 fully supported MDMP. Due to the existing limitations or risks, as of SAP ERP 6.0 SAP no longer supports MDMP.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Scenario 1) New Installation of ECC MDMP - not supported: SAP recommends that you use Unicode. As of SAP ERP 6.0 2005 SAP no longer supports new MDMP installations.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Scenario 2) SAP R/3 MDMP -&gt; SAP ECC Upgrade: An upgrade from an existing SAP R/3 MDMP to SAP ECC requires a system conversion to Unicode. If you want to operate MDMP productively on SAP ERP 6.0 for a very limited period of time, you can only do this after you sign the relevant \"disclaimer\" and agree to undertake the project with SAP Consulting.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For more information, see:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- SAP Service Marketplace at service.sap.com/unicode<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- SAP Notes 79991, 540911, 745030, and 73606.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If you have any questions, contact <EMAIL>.</p>\r\n<ol><ol>b) Unicode</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If you want to change large Unicode-based installations (&gt; ca. 1 TB) with a minimum downtime, which is less than one weekend, contact <EMAIL>.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For more information about individual applications, see <strong>SAP Note 540911</strong>. The SAP GUI configuration for Unicode is described in <strong>SAP Note 508854</strong>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b><B>Open Limitation(s)</B></b><br /> <p></p> <b><B>AP-PPE-CMP</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Restricted Use: Specific Function of Product Structure Maintenance</B><br />The Focus in iPPE function within the product structure maintenance is available only after prior approval from SAP. For further consultation, contact Stefan <NAME_EMAIL><br />( Changed at 22.02.2006 )<br /><br /></p> <b><B>CA-DMS-EUI</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Functional Restrictions with SAP Easy Document Management Release 6.0 SP01</B><br />Listed below are the functional restrictions for SAP Easy Document Management: -&#x00A0;&#x00A0;The offline functionality is disabled in the Citrix environment -&#x00A0;&#x00A0;In case of a published document, DisableaAutomatic Checkin will not work. In other scenarios, a user must check in all the documents before closing the session, otherwise the changes/data will be lost -&#x00A0;&#x00A0;Default characteristcs for CURRENCY and NUMERIC types are not supported -&#x00A0;&#x00A0;Search help is not available for all the objects<br />( Changed at 22.02.2006 )<br /><br /><B>Communication Problems with SAP Easy Document Management</B><br />There are communication problems between the backend system and Easy Document Management. For various reasons, the connection between the backend and frontend breaks, and the frontend reports any of the following errors: -&#x00A0;&#x00A0;\"Connection to partner broken\" -&#x00A0;&#x00A0;\"Error while checking in and storing...\" This could result in data loss. We recommend that you save the data on your local machine to prevent any loss of data. An RFC trace should also be generated. For more information about the generation of a trace, see SAP Note 661600. Some additional steps also need to be taken, for this refer to SAP Note 900076. This error occurs with Release 4.6C and is solved with Release 4.7 and higher releases. For more information, see SAP Note 810391.<br />( Changed at 22.02.2006 )<br /><br /></p> <b><B>CA-ESS</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Approving another Absence or Working Time is Not Possible</B><br />While trying to approve a leave request or the working hours of an employee through UWL, the link &#x0093;Approve another absence/working time&#x0094; in  Manager Self Service&#x0094; does not work properly if the link triggers the application to start in a new window. The application is expected to restart, but this does not work currently.<br />( Changed at 19.04.2007 )<br /><br /></p> <b><B>EP-PCT-EXP</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Express Planning: Bidirectional Scripts for RTF Documents are Not Supported</B><br />Express Planning does not support bidirectional scripts such as, Arabic or Hebrew while creating RTF documents. RTF documents are used to download a summary of entered values in Express Planning.<br />( Changed at 22.02.2006 )<br /><br /></p> <b><B>EP-PCT-MGR-HR</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Portal: Performance</B><br />The performance of SAP Portal is limited, especially for low-power front-end terminals (lower than 500 MHZ).<br />( Changed at 11.09.2007 )<br /><br /><B>MSS: Interactive Forms</B><br />Interactive Forms that are used with the business package for Manager Self-Service mySAP ERP 2004 (60.1) have the following limitations: (1) For the Business Package for Manager Self-Service mySAP ERP 2004 (60 .1), only some of the forms delivered are available for productive use as interactive forms in both the change requests for employee data workset and the recruitment workset. With ERP 2004 Support Package Stack 04, the following forms were released for productive use: Change Employee Group/Employee Subgroup (SPEG) Change Working Time (SPWT) Change Personnel Area/Personnel Subarea (SPPA) Change Position (SPPS) Request Separation (SPSE) Request Separation - Enhanced (SPSD) and Simple Requisition Form (SRQ1). (2) In ERP 2004 Support Package Stack 05, there are some dependencies between PI Support Package 05 and the interactive forms of the Business Package for Manager Self-Service mySAP ERP 2004 (60.1). In ERP 2004 Support Package Stack 05, you cannot call interactive forms integrated with the Internal Service Request (ISR) - such as change requests for employee data and requisitions for hiring new employees. The front-end corrections made for these interactive forms in ERP 2004 Support Package Stack 05 required a new function module in the PI back-end system. You can use PI Support Package 05 to import the new module. For more information, see Note 800288. (3) With the ERP 2004 Support Package Stack 05 in combination with Adobe&#x00A0;&#x00A0;Reader 6.0.2, you cannot use list fields (dropdown boxes) that are linked to one or more empty key values in your forms. For example, employee subgroups contain several employee groups. If you use the key value \"\" for a default value (for example, \"main location\"), you cannot use any dropdown boxes in your form. This problem should be solved with Adobe Reader 7.0. (4) Until Support Package 09, you cannot define or use any forms containing dynamically displayed or hidden fields. For more information,&#x00A0;&#x00A0;see Note 863308. (5) SAP NetWeaver Support Package Stack 11 (the package underlying the mySAP ERP 2004 Support Package Stack 07) supports forms in the master language only. The master language is German in the case of change requests for employee data. Therefore, after you import mySAP ERP Support Package Stack 7, the forms only work when you call them in German. Translations delivered in earlier mySAP ERP Support Package stacks are no longer automatically available. You can access missing translations manually by implementing Note 831053. As of mySAP ERP Support Package Stack 08, translations are automatically made available again in more languages. (6) In mySAP ERP 2004, you cannot define any forms that contain search helps. The OVS (Object Value Selector) function is unavailable. This means that&#x00A0;&#x00A0;SAP does not release the following forms in mySAP ERP 2004 as sample forms for your implementation: Request Transfer (SPTR) and Request Transfer - Enhanced (SPTD). For more information about a succeeding solution for interactive forms, see SAP Notes 952693 and 972322.<br />( Changed at 11.09.2007 )<br /><br /></p> <b> <B>No release</B></b><br /> <p><B>MSS: Reporting (my Staff)</B><br />The combination of SAP NetWeaver Support Package Stack 10 and ERP 2004 Support Package Stack 06 results in an error in the Business Package for Manager Self-Service (mySAP ERP 2004) 60.1 for the reporting workset: The system does not issue a separate window displaying the report result to the user. If you use this reporting, you should not import the combination of SAP NetWeaver Support Package Stack 10 and ERP&#x00A0;&#x00A0;2004 Support Package Stack 06.<br />( Changed at 02.11.2009 )<br /><br /><B>Interactive Forms for Transfer Scenarios Not Released</B><br />The Object Value Selector (OVS) input help function is currently not available. As a consequence, the following Interactive Forms are not released: - Request for Transfer (SPTR) - Request for Transfer (enhanced) (SPTD) For more information, see SAP Note 952693.<br />( Changed at 31.10.2006 )<br /><br /></p> <b><B>FI-AR-CR</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>SAP Credit Management and mySAP CRM</B><br />If you want to use SAP Credit Management in conjunction with SAP CRM, you must consult with SAP. For more details contact Helge Meyer <NAME_EMAIL><br />( Changed at 22.02.2006 )<br /><br /></p> <b><B>FI-GL</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Use of Partial Payment in license sales conflicts with New GL</B><br />1. What is the limitation: As a prerequiste for the partial payment functionality a special purpose ledger has to be used. If you want to define a manual split of the incoming payment to individual titles or rights dimensions this split is&#x00A0;&#x00A0;posted to the special purpose ledger but is not transferred to the ledger in NewGL. 2. Is a workaround possible: No workaround known.<br />( Changed at 22.11.2007 )<br /><br /></p> <b><B>FIN-CGV-MIC</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Management of Internal Controls: Compatibility with SAP Enterprise Portal</B><br />The Management of Internal Controls component cannot be run as an iView in the Enterprise Portal of SAP NetWeaver. However, it can run as a stand-alone application in a web browser. In addition, both Management of Internal Controls and Public Sector Records Management cannot run on the same client.<br />( Changed at 22.02.2006 )<br /><br /></p> <b><B>FIN-FSCM-BD-AR</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>EBPP/Payments: Limitations with a Large Number of Open Items</B><br />The following limitations apply to the functions: 'Open items', 'Create payment advice notes', 'Display payment advice notes', 'Balances and line items', 'Balance comparison'. If you activate these functions in the Internet for end customers for whom you manage a very large number of open items in your customer accounts (hundreds of items not yet cleared), you must first contact SAP. For such a large number of open items, a lot of memory and CPU resources are consumed on the J2EE Server, when Internet pages containing these line items are loaded. Before you use these pages productively with the customer group specified above, open a customer message under the component FIN-FSCM-BD-AR. In the message, indicate the number of open items that you expect.<br />( Changed at 11.09.2007 )<br /><br /></p> <b> <B>Release with restrictions</B></b><br /> <p><B>EBPP: Language Availability</B><br />Electronic Bill Presentment &amp; Payment is not translated into the following languages: - Chinese (simplified) - Chinese (traditional) - Danish - Polish - Korean - Russian<br />( Changed at 11.09.2007 )<br /><br /></p> <b><B>FIN-FSCM-COL</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>SAP Collections Management: Upgradeability in Subsequent Releases</B><br />In the subsequent release SAP Collections Management 6.0, a master data replication between the SAP business partner and the customer is required for the new function. In the standard system, this replication is fully supported in SAP Collections Management 6.0 if the following two prerequisites are fulfilled: (1) SAP Collections Management is used in a 1-system or 2-system scenario. However, it is not possible to integrate several accounts receivable accounting systems with a Financial Basis system for Collections Management (2) ECC 6.0 exists either in the accounts receivable accounting system or in the system in which Financial Basis is installed. If one of these two prerequisites is not fulfilled, a lot of time and effort is required to implement this master data replication in the customer project and to integrate it in SAP Collections Management 6.0. To avoid this situation and to avoid problems during the upgrade to SAP Collections Management 6.0, the following two recommendations are already valid for the installation of SAP Collections Management 3.0: (1) We recommend that you integrate only one customer accounting system with a Financial Basis system for Collections Management. The customer-specific conditions (for example, the release of the customer accounting system) determine whether this happens in a 1-system or 2-system scenario. (2) We recommend that you install the Financial Basis 3.0 on ECC 5.0 and&#x00A0;&#x00A0;not on Netweaver 04 or CRM 5.0 if both of the following prerequisites apply to your situation: (a) You use SAP Collections Management 3.0 in a 2-system scenario, that is, you install Financial Basis 3.0 in a system other than your accounts receivable accounting system (b) you want to upgrade to SAP Collections Management 6.0 and you want to use the new function of SAP Collections Management 6.0 before you upgrade to ECC 6.0 in your accounts receivable accounting system.<br />( Changed at 11.09.2007 )<br /><br /><B>Restricted Upgrade: SAP Collections Management from Release 3.0 to 6.0</B><br />This restricition only applies if you run SAP Collections Management 6.0  on a different system than Accounts Receivable Accounting (FI-AR). Upgrade of these systems is only possible when the FI-AR system is upgraded to SAP ECC 6.0 OR If the SAP Collections Management system already includes SAP ECC 5.0, then during an upgrade to SAP Collections Management 6.0, SAP ECC 5.0 is&#x00A0;&#x00A0;also upgraded to SAP ECC 6.0.<br />( Changed at 08.06.2007 )<br /><br /></p> <b><B>FIN-FSCM-CR</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>SAP Credit Management: Language Availability</B><br />SAP Credit Management is not translated into Polish and Russian.<br />( Changed at 11.09.2007 )<br /><br /></p> <b><B>FIN-SEM-BCS</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>SEM Business Consolidation: Interunit Profit Elimination of Transferred Assets</B><br />The following automatic functions do not exist in Interunit Profit Elimination of Transferred Assets: - Assets in local currency - Transfer chains including companies consolidated according to the equity method - Method change or partial divesture of a company included in an asset transfer - Option to post depreciate correction on the selling company.<br />( Changed at 02.11.2009 )<br /><br /></p> <b><B>FIN-SEM-CPM-BSC</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Specific Functions in SAP SEM Corporate Performance Monitor are Not Available</B><br />The following functions of the Corporate Performance Monitor (SEM-CPM) executive briefing book are not available any longer: - Perspective Overview - Strategy Map - Strategy Analysis. The web presentations of strategy map, perspective overview, drilldown, value driver tree, and BSC Wizard do not support bidirectional scripts&#x00A0;&#x00A0;such as, Arabic or Hebrew. The charts displayed in the balance scorecard, value driver tree, and in&#x00A0;&#x00A0;the Management Cockpit do not support right-to-left directions in web presentations or in SAP GUI.<br />( Changed at 08.06.2007 )<br /><br /></p> <b><B>IS-A</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Product Design Cost Estimate for Automotive Customers only</B><br />The functionality of Product Design Cost Estimate (PDCE, formerly also knows as Concurrent Costing) is released for use in scenarios of SAP for  Automotive only. Prior to Support Package 05 of mySAP ERP2005, the functionality is generally not released. If you request use of PDCE prior to Support Package 05 of mySAP ERP2005, please contact Markus Kerle @ markus. <EMAIL>.<br />( Changed at 16.05.2006 )<br /><br /></p> <b><B>MM-IM-ED</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Excise Duty: Language and Country Limitations</B><br />Application component: MM-IM-ED (with active extension EA-CP). Language limitation: MM-IM-ED is only available in the following languages: English, German, Spanish, French, Hungarian, Czech, Greek and&#x00A0;&#x00A0;Croatian. Country limitation: The \"Excise Duty\" function is limited to the following countries: Austria, Belgium, Denmark, Finland, France, Germany, Great Britain, Ireland, Italy, Luxembourg and the Netherlands. Country limitation: The function of Excise Duty is limited to the following countries: Austria, Belgium, Denmark, Finland, France, Germany, Great Britain, Ireland, Italy, Luxembourg and the Netherlands. Official reporting, such as tax returns, stock reports and stock settlements or similar, is localized only for the German beer, brandy, and sparkling wine tax. Country-specific procedures, rules, and forms deviating from this standard must be localized separately. A further limitation exists in the case where two or more different country versions are installed on one system and only one common material master record is managed for the same articles in these countries. Here, an excise duty settlement is not possible for articles with different excise duty types in the individual countries. For further restrictions, see SAP Note 1030452.<br />( Changed at 11.09.2007 )<br /><br /></p> <b><B>PA-CE</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Restricted Use: Concurrent Employment</B><br />The concurrent employment function is released only for specific customer groups. These functions are subject to prior approval from SAP. For more information, contact Michael <NAME_EMAIL>.<br />( Changed at 22.02.2006 )<br /><br /></p> <b><B>PA-EIC</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>mySAP ERP HCM: Employee Interaction Center Only Available on Interner Explorer</B><br />Employee Interaction Center of mySAP Human Capital Management can be accessed using Interaction Center WebClient, which uses only Internet Explorer as its web browser.<br />( Changed at 20.12.2006 )<br /><br /></p> <b><B>PA-ER</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>SAP E-Recruiting - mySAP ERP Human Capital Management System Compatibility</B><br />SAP E-Recruiting has a new XI interface, which allows you to transfer candidate data from the e-recruiting system to the mySAP ERP HCM system when an employee is hired.&#x00A0;&#x00A0;For this new XI interface to work, mySAP ERP HCM must be of ERP release 2005. Earlier releases of mySAP ERP HCM do not support this new XI interface. However, the earlier RFC interface, which was used to transfer data, is still available to customers using earlier versions of&#x00A0;&#x00A0;mySAP ERP HCM, and who do not upgrade to mySAP HCM 2005.<br />( Changed at 02.11.2009 )<br /><br /><B>SAP E-Recruiting - Manager Self-Service Scenario Compatibility</B><br />The Manager Self-Service (MSS) scenario for SAP E-Recruiting is based on  Web Dynpro technology. If you want to use the MSS scenario with SAP E-Recruiting, the mySAP HCM system must be of release 2004 or 2005. Additionaly, the MSS scenario will not work with a mySAP HCM release 4.7 system or with any earlier versions of mySAP HCM.<br />( Changed at 02.11.2009 )<br /><br /></p> <b><B>PA-GE</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Restricted Use: Global Employment</B><br />The management of global employees function is released only for specific customer groups. These functions are subject to prior approval from SAP. For more information, contact Michael <NAME_EMAIL>.<br />( Changed at 22.02.2006 )<br /><br /></p> <b><B>PLM-IFO</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>No Release - Guided Procedures for New Product Request/ Product Change Request</B><br />The Guided Procedures New Product Request and Product Change Request are not available with ERP 2004 and higher releases.<br />( Changed at 11.09.2007 )<br /><br /></p> <b><B>XX</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Support of MDMP</B><br />SAP uses and recommends Unicode as the standard technology to fulfill multilingual language requirements. MDMP (Multi-Display Multi-Processing) is fully supported by R/3 only. With the availability of Unicode, we recommend that you do not use MDMP due to the limitations  and risks that currently exist. -----Case 1: ECC MDMP New Installation - We recommend that you use Unicode. You should only use MDMP in justified, exceptional cases and only following consultation with SAP. -----Case 2: R/3 MDMP -&gt;ECC MDMP Upgrade -&#x00A0;&#x00A0;SAP supports the process of upgrading an existing R/3 MDMP to ECC MDMP, but we recommend that you convert to Unicode in the long term. If you operate an ECC MDMP, you should expect this to involve additional time and effort as well as some  limitations: - Operation of a shared MDMP ECC/BW installation is not supported. - Use of an ABAP-based ADD-ON is not supported. - Increased time and effort/limitations when integrated with non-Unicode&#x00A0;&#x00A0;and Unicode components, for example: (a) non-Unicode SAP BW, SAP Solution Manager and other mySAP Solutions (b) Unicode SAP EP, XI, MDM, BW and other mySAP Solutions ---- For more information, see Note 896144.<br />( Changed at 11.09.2007 )<br /><br /></p> <b><B>Fixed Limitation(s)</B></b><br /> <p></p> <b><B>BC-VMC-JIT</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>SAP Internet Pricing and Configurator: Operating Systems</B><br />The performance of the SAP Internet Pricing and Configurator (IPC) is not sufficient for productive use on the Linux on zArchitecture operating system. For more information, see SAP Note 853050 and the Product Availablity Matrix for NetWeaver 04S.</p> <b>This restriction is no longer valid.</b><br /> <p>( Changed at 30.03.2007 )<br /><br /></p> <b><B>CA-ESS</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Error in Employee Self-Service Personal Information Scenario - Personal Data for France</B><br />If French employees want to edit their personal data using the ESS Personal Data scenario, the service will cause a dump. This means that the localized French version for ESS Personal Data should not be implemented.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation is Fixed<br />( Changed at 08.05.2006 )<br /><br /><B>Error in Service - Change Office and Communication Data During call from ESS Address Book</B><br />The ESS Service Address Book (Who&#x0092;s Who) includes the 'Change office and communication data&#x0092; service. This service causes a dump after&#x00A0;&#x00A0;the end user tries to change and save the data from the service address&#x00A0;&#x00A0;book. This results in the following limitation: Usage of ESS Address Book (Who&#x0092;s Who) without integrated service &#x0091;Change&#x00A0;&#x00A0;office and communication data&#x0092;.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000924828<br />Refer to SAP Note 0000924829<br />Limitation Fixed with SP03<br />( Changed at 20.02.2006 )<br /><br /><B>Example Life and Work Events for ESS Based on Guided Procedures Does Not Work Properly</B><br />The self-service (XSS) wrapper used to integrate the localized ESS services in the respectice Life and Work Events does not work with mySAP  ERP 2005 SP03 and SAP NetWeaver04s SP06 anymore. The ESS Life and Work Events will be changed and used to integrate ESS services with callable objects that are directly bound to&#x00A0;&#x00A0;country-specific applications. Therefore, the localized ESS Services integrated with the following example of ESS Life and Work Events do not function properly: - My First Days - Birth/Adoption - Marriage - Divorce&#x00A0;&#x00A0;- Termination - Change Employment - Benefits. You must keep in mind that the example Life and Work Events for ESS, which is based on Guided Procedures is aligned to US requirements. In case you want to use life and work events for other countries, copy the example processes and exchange the corresponding ESS country services according to your country needs.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000929353<br />Refer to SAP Note 0000929447<br />Limitation Fixed with SP04<br />( Changed at 29.03.2006 )<br /><br /></p> <b><B>CA-GTF-CPE</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>CRM Commodity Pricing for Sales Documents in Sales and Distribution is Not Available</B><br />The sales process cycle for SD sales documents begins in SAP CRM. Commodity pricing for sales documents in SAP CRM is not available. Furthermore, the general limitations for SAP IPC operating systems also apply in this case. For more information, see Limitations for Operating Systems for SAP IPC.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with SP03<br />( Changed at 05.01.2006 )<br /><br /></p> <b><B>CA-GTF-TS-XSS</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Upgrade Restrictions: SAP Self Service and Portal Roles</B><br />If you use the self service scenario of mySAP ERP 2004 and have installed the Self-Services Component (XSS) 5.0 on a different J2EE engine other than the Enterprise Portal (EP) 6.0, then you cannot upgrade your current system landscape. An upgrade to XSS 6.0 (mySAP ERP 2005) includes an upgrade of the Enterprise Portal to NetWeaver 2004s usage type EP and is only possible if you have installed both XSS and Enterprise Portal on the same J2EE engine. For more information please refer to note 879335.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000879335<br />Limitation Fixed with Release SP06<br />( Changed at 22.09.2006 )<br /><br /><B>Performance Problems with Manager Self Service</B><br />Using Manager Self Services (MSS) with a large number of users can result in high memory consumption on the portal server running the XSS component. This in turn may lead to performance issues.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation is fixed with release SP04<br />( Changed at 20.04.2006 )<br /><br /></p> <b><B>CA-TS-IA-XS</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Employee Self-Service Record Working Time Approval with TS31000007 Does Not Function Properly</B><br />The Approval Workflow TS31000007 used in ESS Service Record Working time  (CATS) does not get triggered. Therefore, no support is provided for this workflow.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000929992<br />Limtation Fixed with SP04<br />( Changed at 03.04.2006 )<br /><br /></p> <b><B>EP-BC</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Federated Global Portal is Not Available</B><br />Implementation of a federated global portal to distribute a global Enterprise Portal and the Enterprise Portal that is necessary for SAP Self Service (XSS 6.0) to different servers (e.g. for load balancing reasons) is not possible at the beginning of the ramp-up phase. For more  information about the federated portal concept please refer to the SAP NetWeaver 2004s Master Guide.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000879335<br />Limitation Fixed wtih Release SP06<br />( Changed at 22.09.2006 )<br /><br /></p> <b><B>EP-PCT</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Portal Roles are Not Available</B><br />Portal roles will not be available at the begining of the ramp-up phase. Therefore functionality that is provided to business processes exclusively through portal roles is not available either. The following business processes are partially affected: - Travel Request and Pre-trip approval - Travel Planning / online Booking - Online Services for Travel Expense - Express Planning - Administering Employee HR Data However, customer projects can begin with the technical implementation of the portal immediately at availability of mySAP ERP2005 SP01.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP02<br />( Changed at 23.11.2005 )<br /><br /></p> <b><B>EP-PCT-FIN</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Key Figures Determined Using BW Queries Cannot Use Hierarchy Variables</B><br />The generic key figure monitor is used in the following roles in mySAP ERP 2005: -&#x00A0;&#x00A0;Plant Manager -&#x00A0;&#x00A0;Manager Self Service -&#x00A0;&#x00A0;Business Unit Analyst -&#x00A0;&#x00A0;Production Supervisor Key figures can be calculated using reports in ECC, BW queries, and external sources. If BW queries are used to fill the key figure, the report&#x00A0;&#x00A0;KFM_KF_REPORT_BW_QUERY is run with the appropriate selection parameter.&#x00A0;&#x00A0;These are filled using the BW variables appropriate to the key figure. It is currently not possible to call this report with hierarchy variables that are linked with other variables or characteristic values,&#x00A0;&#x00A0;such as the controlling area. Instead, queries must be chosen to fill the key figure that use only variables that can be filled directly, such&#x00A0;&#x00A0;as a list of cost centers or cost elements.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with Release SP07<br />( Changed at 21.12.2006 )<br /><br /></p> <b><B>EP-PCT-MAN</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Certain Web Dynpro Applications in Specific Roles Cannot be Used</B><br />Some Web Dynpro applications newly delivered with mySAP ERP 2005 cannot be used as yet. This results in the following limitations while using the these three roles: 1) Role \"Maintenance Technician\": The following Web Dynpro for ABAP applications have limitations and cannot be used: (a) Changing notifications from the work list might cause data loss due to missing locks and the missing work protect mode. (b) \"Display Documents\" from the work center \"Structure Display\" works only for documents that are not checked-out by other users. 2) Role \"Quality Inspector\": The Web Dynpro for ABAP applications Record Defects (non-conformity data) via the confirmation service cannot be used ----Note: The automatic generation of defect data through rejection of inspection characteristics in results recording is not affected, so this  function can be used.---- 3) Role \"Production Supervisor\": Defects Recording cannot be used within the new Web Dynpro for ABAP applications for confirmation. In particular in Customizing, do not assign the component for defects recording (technical name: RPLM_QIMT_DEFECT_COMP) to a layout. Layout settings: In the customizing transaction ORPS5 SAP IMG: Integration with Other SAP Components --&gt; Business Packages/Functional Packages --&gt; Production Scheduler (or Production Supervisor) --&gt; Define Settings for Confirmation --&gt; Define Confirmation Layouts).</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with SP04<br />( Changed at 15.05.2006 )<br /><br /><B>Translation Inconsistency in Forms for Roles: Manager, Employee, and HR Administrator</B><br />Certain forms in mySAP ERP 2005 have been changed in SP01, but the translation for these forms have not been adopted accordingly. This results in the following limitations. These inconsitencies occur in both  print and interactive forms For more information about the technical details of these print forms, see SAP Note 909511. For Interactive Forms: Note: The corresponding roles are indicated within parenthesis. 1) The following forms are only correct in German: -&#x00A0;&#x00A0;Hiring (HR Administrator) -&#x00A0;&#x00A0;Maternity leave (HR Administrator, Employee) -&#x00A0;&#x00A0;Birth of child (HR Administrator, Employee) -&#x00A0;&#x00A0;Termination (HR Administrator, Employee, Manager) -&#x00A0;&#x00A0;Organizational Change (HR Administrator, Employee, Manager) -&#x00A0;&#x00A0;Change Employee Group and Subgroup (Manager) -&#x00A0;&#x00A0;Change Personnel Area and Subarea (Manager) -&#x00A0;&#x00A0;Request for Internal Transfer (Enhanced)&#x00A0;&#x00A0;(Manager) -&#x00A0;&#x00A0;Request for Promotion (Manager) -&#x00A0;&#x00A0;Request for Internal Transfer (Manager) -&#x00A0;&#x00A0;Request for Discharge (Enhanced)  (Manager) -&#x00A0;&#x00A0;Request for Separation (Manager) -&#x00A0;&#x00A0;Request for Special Payment (Manager) -&#x00A0;&#x00A0;Change of Working Time (Manager) 2) The following forms are only correct in English: -&#x00A0;&#x00A0;Regular Employment - Long Form (Manager) -&#x00A0;&#x00A0;Regular Employment - Short Form (Manager)</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed wtih SP03<br />( Changed at 21.12.2005 )<br /><br /></p> <b><B>EP-PCT-MGR-CO</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Limitation on Internal Service Request for the Roles Business Unit Analyst and Manager Self Service</B><br />It is not possible to start any Internal Service Request (ISR) in the roles Manager Self Service and Business Unit Analyst. In addition, the iView 'Service Request: Status Overview' does not work. However, in both  cases the error message '500 Internal Server Error' is displayed.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000946972<br />Limitation is fixed with SP05<br />( Changed at 11.07.2006 )<br /><br /></p> <b><B>EP-PCT-MGR-HR</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>SAP Manager Self-Service and SAP E-Recruiting Integration Scenario Not Released</B><br />The SAP Manager Self-Service and SAP E-Recruiting integration scenario is currently not released. This means, in particular, that SAP does not release the following functionality: - Create New requisition Request - Create New Candidate Assessment - Recruiting Requisition: Status Overview - Recruiting Candidate Status Overview</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000949625<br />Limtation fixed with release SP07<br />( Changed at 20.12.2006 )<br /><br /><B>Personnel Change Request&#x00A0;&#x00A0;from Related Activities: Check and Error Message Missing</B><br />When you launch a personnel change request out of the Related Activities iView, some forms do not get initialized, but are displayed to the user. The check to verify whether these forms are successfully initialized is missing along with the error message.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP03<br />( Changed at 23.02.2006 )<br /><br /></p> <b><B>EP-PCT-SRM-IVC</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Documents Overview and Document Quick Access for the Role Supplier Does Not Function Properly</B><br />The iViews 'Documents Overview' and 'Document Quick Access' in the role 'Supplier' does not work properly. If one of the documents is called, an  error occurs by opening the corresponding document. It is possible to open a document if the user changes the URL every time the document is called. You can do this by replacing 'main.do&amp;' in the URL sequence with  'main.do?' (replace &amp; with ?). This limitation also affects the processes Service Procurement and Self-Service Procurement in Supplier Relationship Management (SRM).</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000932192<br />Limitation Fixed with SP04<br />( Changed at 29.03.2006 )<br /><br /></p> <b><B>EP-PIN</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Problems in Upgrading the Java of ERP 2004 to ERP 2005 SP Releases</B><br />Problems encountered while upgrading the ERP Java part of ERP 2004 to ERP 2005 SP08 and higher releases. For more information contact, Michal Zilcha @ <EMAIL>.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation has been fixed with SP10<br />( Changed at 17.08.2007 )<br /><br /></p> <b><B>EP-VC-ANA</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>Special System Setup for Analytical Applications Based on Visual Composer</B><br />In order to run analytical applications based on SAP NetWeaver Visual Composer, a separate instance based on SAP NetWeaver 2004 (instead of SAP NetWeaver 2004s) is required to run SAP NetWeaver Visual Composer.</p> <b>This restriction is no longer valid.</b><br /> <p>Customer must enlist in the Analytics Ramp Up to avail of this functionality<br />( Changed at 11.07.2006 )<br /><br /></p> <b><B>FI-AR-CR</B></b><br /> <p></p> <b> <B>No release</B></b><br /> <p><B>SAP Credit Management - SD Service Orders</B><br />SD Service Orders are not taken into account for exposure updates and credit limit checks.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed wtih SP03<br />( Changed at 21.12.2005 )<br /><br /></p> <b><B>FIN-BA</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Product Design Cost Estimate is Restricted to Automotive Supplier Use</B><br />The Product Design Cost Estimate (PDCE) is based on specific business content and generated from the generic Costing Engine. This business content is restricted to only address the needs of automotive suppliers. The business content contains information such as, the data model, the costing algorithm, valuation strategy, authorizations, and reporting data. Note: Certain elements of the content have been assigned to a protected SAP name space to avoid an exceedance of this restriction.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note **********<br />Limitation fixed with release SP05<br />( Changed at 08.06.2007 )<br /><br /></p> <b><B>FIN-CGV-MIC</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Management of Internal Controls - No Release Prior to Support Package 05</B><br />Corrections to Management of Internal Controls (MIC) provided by SAP after the release of Support Package 05 cannot be applied to Support Packages 04 and lower. MIC is therefore not released for productive use prior to Support Package 05. If you need to run MIC in productive use prior to upgrading to Support Package 05, please contact Gero <NAME_EMAIL>.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP05<br />( Changed at 11.07.2006 )<br /><br /></p> <b><B>IS-DFS-PDR</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Product Data Replication for Defense Scenarios</B><br />For the extension EA-DFPS the usage of the functionality of product data  replication is not released. For consultation, contact the person responsible directly by e-mail [<EMAIL>].</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note **********<br />( Changed at 12.01.2006 )<br /><br /></p> <b><B>KM-DOC</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Documentation Delayed</B><br />The documentation update as to ERP2005 for following business processes will be delayed. Please note that all the processes / roles listed below  are already available in ERP2004, and the according documentation is available. Only new features as per ERP2005 are not yet documented. mySAP ERP Financials: - Accounts Payable - Accounts Receivable - Accrual Accounting - Adjustment of Conditions - Business Unit Analyst - Central Incoming Payments (In-House Cash) - Central Payments (In-House Cash) - Classic General Ledger - External Payments - Electronic Tax Declaration - FI-AR: Collections Management - FI-AR: Credit Management - FI-AR: Dispute Resolution - Financial Statement Planning - Fixed Asset Accounting - Investment Management - Investment Planning - Local Close - Managing Sales-Based Lease - Overhead Cost and ABC/M Analytics - Overhead Cost Management and ABC/M - Parallel Valuation - Product and Service Cost Analytics - Product and Service Cost Management - Profit Center Accounting for Classic General Ledger - Service Charge Settlement - Strategic Planning - Tax Accounting - Transfer Pricing for Profit Center Accounting and Classic GL ---------- mySAP ERP Human Capital Management: - Employee Self-Service - HR Administrator - Manager Self-Service - Mobile Self Service for Travel Expenses - Online Self Service for Travel Expenses - Performance Measurement - Travel Planning: Online Booking - Travel Request and Pre-trip approval ---------- ERP Sales &amp; Service: - Incentive Plan Maintenance - Incentive Business Configuration in ERP --------- NOTE: - This documentation will be made available on SAP Service Marketplace. - The configuration documentation for Enterprise Commerce (ECO) ERP is part of the SAP Solution Manager content. If you use ECO ERP together with SAP R/3 4.6 C, 4.7 or mySAP ERP 2004 and do not use SAP Solution Manager, then you can download the solution manager content into a configuration document as done for CRM 4.0.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP03<br />( Changed at 21.12.2005 )<br /><br /></p> <b><B>MBA</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Special System Setup for Mobile Business Processes</B><br />Customers are advised to use a special system setup for Mobile Business Processes, such as SAP Mobile Time and Travel, SAP Mobile Asset Management, and Mobile Service Request. Those processess are only available after approval or consultation with SAP. Contact Thomas <NAME_EMAIL> for additional information. Note: The process Sales Order Management with ERP for Mobile Devices is not available with the current ERP 2005 release.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP07<br />( Changed at 20.12.2006 )<br /><br /></p> <b><B>PA-AS</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>HR Administrative Services: HCM Processes and Adobe Forms</B><br />HCM Processes and Forms, a sub-component of HR Administrative Services, uses Adobe Interactive Forms for form-based HCM processes. There are certain restrictions while using these HCM processes because of specific  restrictions for Adobe Interactive Forms. For more information, see SAP Note 894389.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with Release SP06<br />( Changed at 22.09.2006 )<br /><br /></p> <b><B>PA-ER</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>SAP E-Recruiting Fast Track Shipment</B><br />Rampup customers who want to implement the Succession and Talent Management functionality of SAP E-Recruiting Release 2005 must implement  the SAP E-Recruiting Fast Track Shipment before going live with mySAP ERP 2005. The fast rack shipment consists of a number of enhancements to SAP E-Recruiting and Succession Planning. It provides an improved UI for users, including a Recruiter Dashboard, quicker access to candidate and requisition screens, improved search function, ESA scenario for Resume Parsing integration, updated Succession Planning functionality, and a new Webdynpro and Workcenter-based HR role for Talent Management.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with Release SP06<br />( Changed at 22.09.2006 )<br /><br /></p> <b> <B>Release with restrictions</B></b><br /> <p><B>SAP E-Recruiting: Problems in Updating Candidate Data Using the XI Scenario</B><br />Problems have been reported with the processing of new hire actions using the new XI scenario for candidate data import in SAP E-Recruiting.  The problems only occur when both EREC and HCM are installed on a single instance. The XI scenario functions without problems when E-Recruiting is installed on a separate instance, as is often the case with E-Recruiting. Customers implementing SAP ERP 2005 E-Recruiting on a  single instance with HCM should avoid using the XI scenario for new hire data import until the problem is solved. However, in the meantime, if a customer running EREC on a single instance with HCM needs to go live, the alternative scenario for new hire data import that does not require XI technology can be used.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000982908<br />Limitation fixed with release SP07<br />( Changed at 20.12.2006 )<br /><br /><B>SAP E-Recruiting: Problems in Updating Candidate Data Using ESS Scenarios</B><br />Problems have been reported with the process of updating candidate data when personnel data is updated using ESS. The Internal Candidate data needs to be updated when specific-personnel data is maintained, for example via ESS. The problems occur when both EREC and ESS are installed  on a single instance. The update of candidate data, using ESS, functions without problems when E-Recruiting is installed on a separate instance, as is often the case with E-Recruiting. Customers who wish to go live with EREC and ESS need to take this limitation into consideration.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000982908<br />Limitation fixed with release SP07<br />( Changed at 20.12.2006 )<br /><br /></p> <b><B>PE-LSO</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Functionality Restrictions in the Course Follow-up Process of the SAP Learning Solution</B><br />The following functions are not available in the Follow-Up process: - Confirmation link in the Learning Portal is not available - FDA functionality in the backend is not available - Follow-Up process steps with entry 'SYSTEM' are not available - Follow-Up for curriculum is restricted - Supported combinations of entry &amp; process are changed</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 000 888227<br />Limitation Fixed with SP04<br />( Changed at 29.03.2006 )<br /><br /></p> <b><B>PE-LSO-TM</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Specific Functions of Course Follow-up in SAP Learning Solution Not Available</B><br />The process, Managing Enterprise Learning Stragtegies of the SAP Learning Solution 6.0 is redesigned. With this new functionality, the following functions are not available: 1. Conversion report from the existing to the new infotype is missing. In addition, the related documentation is not available. This information is critical for upgrade customers. 2. The existing infotype cannot be completely replaced by the new one; fields are yet to be transferred into the new infotype. Furthermore, the documentation is not available. This information is critical for upgrade and new customers 3. Exisiting functions are not available with the new functionality such as, direct access to detail screens. This information is critical for upgrade customers. Therefore, implementation of the SAP Learning Solution 6.00 is subject to prior approval from SAP. For more details, contact Simone <NAME_EMAIL>.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP02<br />( Changed at 02.02.2006 )<br /><br /></p> <b><B>PLM-CPR</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>SAP cProjects Suite: Financials Integration</B><br />If you want to use Multi-Level Controlling (Financial Integration) for cProjects 4.0, you have to contact SAP. To do this, please create a customer message using the component PLM-CPR-EXT-FIN.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with SP08 Release<br />( Changed at 19.04.2007 )<br /><br /></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Specific Functions of SAP cProject Suite Not Available</B><br />The following functions in SAP cProject Suite do not work properly: - Individual Approval using Adobe Interactive Forms - Project Status Report using Adobe Interactive Forms does not work, if electronic signatures are used - MS Project and XML import/export does not work with all features as in&#x00A0;&#x00A0;cProjects 3.1 - Scheduling function.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000874099<br />Limitation fixed with SP02<br />( Changed at 12.12.2005 )<br /><br /><B>SAP cProject Suite: SRM Integration</B><br />New process for external service procurement using SRM and XI functions does not work correctly.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with SP04<br />( Changed at 29.03.2006 )<br /><br /><B>SAP cProject Suite: Multi-level Controlling</B><br />The transfer of an already saved ad-hoc calculation for new multi-level controlling scenarios into an Easy Cost Planning (ECP) calculation is currently not supported.</p> <b>This restriction is no longer valid.</b><br /> <p>Refer to SAP Note 0000862198<br />Limitation Fixed with SP04<br />( Changed at 10.07.2006 )<br /><br /></p> <b><B>PLM-RM</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Restricted Use: Recipe Management</B><br />The delivery of Recipe Management (RM), including the new formula calculation functionality, is possible only with prior approval from Application Solution Management of SAP. For more information, contact Mark <NAME_EMAIL> Note: With the redesign of Recipe Management, the formula explosion modeling function is no longer available.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation Fixed with Release SP05<br />( Changed at 19.04.2006 )<br /><br /></p> <b><B>PPM-PRO-EXT-FIN</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Scenario Project Management</B><br />If you want to use Multi-Level Controlling (Financial Integration) for cProjects 4.0, you have to contact SAP. To do this, please create a customer message using the component PLM-CPR-EXT-FIN.</p> <b>This restriction is no longer valid.</b><br /> <p>Fixed with ERP 6.0 SP08<br />( Changed at 08.10.2009 )<br /><br /></p> <b><B>PSM-GPR</B></b><br /> <p></p> <b> <B>Release with approval of SAP or after consultation with SAP</B></b><br /> <p><B>Government Procurement</B><br />The usage of Government Procurement is only released with approval by SAP or following consultation with SAP. For consultation, contact the person responsible directly by e-mail [<EMAIL>].</p> <b>This restriction is no longer valid.</b><br /> <p>no correction needed<br />( Changed at 23.04.2007 )<br /><br /></p> <b><B>RE-FX-LC</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>SAP Real Estate Management: Compliance with Swiss and Austrian Legal Requirements</B><br />Specific changes to Condition Adjustment, as laid down by Swiss law, are  described below: 1. Any manual change in condition calculations or condition values are blocked after the first condition adjustment. 2. After providing notice to a contract, any change in calculation and condition values are blocked. ----------- Specific changes to Service Charge Settlement, as laid down by Austrian law, are described below: 1. Any changes in measurement is taken into consideration by historic values per time slot. A different settlement method, for example, settlement by valid measurements at the day of settlement is only possible when additional measurements for the settlement period are created in the real estate objects. 2. Advance payments (Akonti) are calculated for service charge settlements based on real payments. Other methods such as, calculations based on the average advance payments per square meter and the square meters at the day of the settlement must be implemented using BAdIs within the project. 3. Input tax for non-opting tenants is calculated and settled based on the assumption of time-equal distribution of service charges during the settlement period. Therfore, non-opting tenants will be charged with non-deductable input tax for their proportion of the service charges. Other calculation methods are not supported.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP02<br />( Changed at 07.12.2005 )<br /><br /></p> <b><B>XX</B></b><br /> <p></p> <b> <B>Release with restrictions</B></b><br /> <p><B>Usage Type Enterprise Portal Core Not Released for mySAP ERP 2005</B><br />The use of the new SAP NetWeaver usage type Enterprise Portal Core (EP Core) is not yet released for mySAP ERP 2005.</p> <b>This restriction is no longer valid.</b><br /> <p>Limitation fixed with SP08<br />( Changed at 08.06.2007 )<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D032778)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I059667)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000852235/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "972113", "RefComponent": "XX-SER-REL", "RefTitle": "mySAP ERP 2005 SP Stack 6 (09/2006) Release Information Note", "RefUrl": "/notes/972113"}, {"RefNumber": "971364", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/971364"}, {"RefNumber": "956271", "RefComponent": "PPM-PRO-EXT-FIN", "RefTitle": "cProjects Suite 4.00 notes on FIN integration after SP04", "RefUrl": "/notes/956271"}, {"RefNumber": "943319", "RefComponent": "IS-B-BCA", "RefTitle": "BCA (SAP Deposits): Release only after consultation", "RefUrl": "/notes/943319"}, {"RefNumber": "936782", "RefComponent": "PA-ER", "RefTitle": "HR_SYNC_PERSON: Updating an e-mail address", "RefUrl": "/notes/936782"}, {"RefNumber": "920016", "RefComponent": "PA", "RefTitle": "Contents & applying HR Support Packages ERP6.00", "RefUrl": "/notes/920016"}, {"RefNumber": "900310", "RefComponent": "PPM-PRO", "RefTitle": "FAQ cProjects 4.0: General information", "RefUrl": "/notes/900310"}, {"RefNumber": "896144", "RefComponent": "BC-I18", "RefTitle": "SAP ERP 6.0 Upgrade for R/3 or ERP MDMP Customers", "RefUrl": "/notes/896144"}, {"RefNumber": "891431", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/891431"}, {"RefNumber": "888227", "RefComponent": "PE-LSO-TM-RW", "RefTitle": "Correction of the follow-up process", "RefUrl": "/notes/888227"}, {"RefNumber": "886262", "RefComponent": "FS-CML", "RefTitle": "SAP for Banking in ERP2004", "RefUrl": "/notes/886262"}, {"RefNumber": "886261", "RefComponent": "FS-CML", "RefTitle": "SAP for Banking in ERP 6.0", "RefUrl": "/notes/886261"}, {"RefNumber": "885892", "RefComponent": "IS-OIL-DS-OGSD", "RefTitle": "Availability of OGSD in SAP ECC IS-Oil 6.0", "RefUrl": "/notes/885892"}, {"RefNumber": "874967", "RefComponent": "IS-R", "RefTitle": "Release restrictions for mySAP Retail", "RefUrl": "/notes/874967"}, {"RefNumber": "868403", "RefComponent": "IS-OIL", "RefTitle": "Release restrictions: SAP ECC Industry Extension Oil&Gas 6.0", "RefUrl": "/notes/868403"}, {"RefNumber": "863532", "RefComponent": "SRM-EBP-TEC-PM", "RefTitle": "Release Restrictions mySAP SRM 2005", "RefUrl": "/notes/863532"}, {"RefNumber": "853050", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/853050"}, {"RefNumber": "852008", "RefComponent": "BC", "RefTitle": "Release Restrictions for SAP NetWeaver 2004s", "RefUrl": "/notes/852008"}, {"RefNumber": "832393", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/832393"}, {"RefNumber": "774615", "RefComponent": "XX-SER-SWFL-SHIP", "RefTitle": "Support Package levels of ERP/ECC installations/upgrades", "RefUrl": "/notes/774615"}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821"}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016"}, {"RefNumber": "1413544", "RefComponent": "SCM-CA-PER", "RefTitle": "Implementation recommendations for SCM 5.0", "RefUrl": "/notes/1413544"}, {"RefNumber": "1358629", "RefComponent": "FI-TV-PL-BIB", "RefTitle": "Information of BIBE integration (new interfaces of 14.06.09)", "RefUrl": "/notes/1358629"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1156407", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhan.Pk.4 SP Stack 01(11/2008) - Release Info. Note", "RefUrl": "/notes/1156407"}, {"RefNumber": "1100189", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhan. Pk.1 SPS 04(12/2007) Release Information Note", "RefUrl": "/notes/1100189"}, {"RefNumber": "1096009", "RefComponent": "FIN-FSCM-CR", "RefTitle": "Restrictions in SAP Credit Management", "RefUrl": "/notes/1096009"}, {"RefNumber": "1081261", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP 6.0 SP Stack 11 (12/2007) Release Information Note", "RefUrl": "/notes/1081261"}, {"RefNumber": "1064635", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhancement Packages: SP Stacks Release Info Note", "RefUrl": "/notes/1064635"}, {"RefNumber": "1046863", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP 2005 SP Stack 09 (05/2007) Release Information Note", "RefUrl": "/notes/1046863"}, {"RefNumber": "1030452", "RefComponent": "IS-BEV", "RefTitle": "Composite SAP note for Beverage and EA-CP module", "RefUrl": "/notes/1030452"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2034609", "RefComponent": "PPM-PRO", "RefTitle": "Integration with Workforce Deployment (WFD) 1.0 (WFMCORE 200)", "RefUrl": "/notes/2034609 "}, {"RefNumber": "1064635", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhancement Packages: SP Stacks Release Info Note", "RefUrl": "/notes/1064635 "}, {"RefNumber": "774615", "RefComponent": "XX-SER-SWFL-SHIP", "RefTitle": "Support Package levels of ERP/ECC installations/upgrades", "RefUrl": "/notes/774615 "}, {"RefNumber": "874967", "RefComponent": "IS-R", "RefTitle": "Release restrictions for mySAP Retail", "RefUrl": "/notes/874967 "}, {"RefNumber": "863532", "RefComponent": "SRM-EBP-TEC-PM", "RefTitle": "Release Restrictions mySAP SRM 2005", "RefUrl": "/notes/863532 "}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821 "}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016 "}, {"RefNumber": "1413544", "RefComponent": "SCM-CA-PER", "RefTitle": "Implementation recommendations for SCM 5.0", "RefUrl": "/notes/1413544 "}, {"RefNumber": "832393", "RefComponent": "SCM-CA-PER", "RefTitle": "Release Restrictions for SCM 5.0", "RefUrl": "/notes/832393 "}, {"RefNumber": "862198", "RefComponent": "PPM-PRO", "RefTitle": "cProject Suite 4.00 release notes and information notes", "RefUrl": "/notes/862198 "}, {"RefNumber": "896144", "RefComponent": "BC-I18", "RefTitle": "SAP ERP 6.0 Upgrade for R/3 or ERP MDMP Customers", "RefUrl": "/notes/896144 "}, {"RefNumber": "886261", "RefComponent": "FS-CML", "RefTitle": "SAP for Banking in ERP 6.0", "RefUrl": "/notes/886261 "}, {"RefNumber": "1096009", "RefComponent": "FIN-FSCM-CR", "RefTitle": "Restrictions in SAP Credit Management", "RefUrl": "/notes/1096009 "}, {"RefNumber": "900310", "RefComponent": "PPM-PRO", "RefTitle": "FAQ cProjects 4.0: General information", "RefUrl": "/notes/900310 "}, {"RefNumber": "1156407", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhan.Pk.4 SP Stack 01(11/2008) - Release Info. Note", "RefUrl": "/notes/1156407 "}, {"RefNumber": "1100189", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhan. Pk.1 SPS 04(12/2007) Release Information Note", "RefUrl": "/notes/1100189 "}, {"RefNumber": "1081261", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP 6.0 SP Stack 11 (12/2007) Release Information Note", "RefUrl": "/notes/1081261 "}, {"RefNumber": "1046863", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP 2005 SP Stack 09 (05/2007) Release Information Note", "RefUrl": "/notes/1046863 "}, {"RefNumber": "972113", "RefComponent": "XX-SER-REL", "RefTitle": "mySAP ERP 2005 SP Stack 6 (09/2006) Release Information Note", "RefUrl": "/notes/972113 "}, {"RefNumber": "920016", "RefComponent": "PA", "RefTitle": "Contents & applying HR Support Packages ERP6.00", "RefUrl": "/notes/920016 "}, {"RefNumber": "1358629", "RefComponent": "FI-TV-PL-BIB", "RefTitle": "Information of BIBE integration (new interfaces of 14.06.09)", "RefUrl": "/notes/1358629 "}, {"RefNumber": "886262", "RefComponent": "FS-CML", "RefTitle": "SAP for Banking in ERP2004", "RefUrl": "/notes/886262 "}, {"RefNumber": "1030452", "RefComponent": "IS-BEV", "RefTitle": "Composite SAP note for Beverage and EA-CP module", "RefUrl": "/notes/1030452 "}, {"RefNumber": "852008", "RefComponent": "BC", "RefTitle": "Release Restrictions for SAP NetWeaver 2004s", "RefUrl": "/notes/852008 "}, {"RefNumber": "943319", "RefComponent": "IS-B-BCA", "RefTitle": "BCA (SAP Deposits): Release only after consultation", "RefUrl": "/notes/943319 "}, {"RefNumber": "868403", "RefComponent": "IS-OIL", "RefTitle": "Release restrictions: SAP ECC Industry Extension Oil&Gas 6.0", "RefUrl": "/notes/868403 "}, {"RefNumber": "956271", "RefComponent": "PPM-PRO-EXT-FIN", "RefTitle": "cProjects Suite 4.00 notes on FIN integration after SP04", "RefUrl": "/notes/956271 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "936782", "RefComponent": "PA-ER", "RefTitle": "HR_SYNC_PERSON: Updating an e-mail address", "RefUrl": "/notes/936782 "}, {"RefNumber": "888227", "RefComponent": "PE-LSO-TM-RW", "RefTitle": "Correction of the follow-up process", "RefUrl": "/notes/888227 "}, {"RefNumber": "891431", "RefComponent": "IS-M-AMC", "RefTitle": "Verfügbarkeit SAP Classified Advertising Management 2.0", "RefUrl": "/notes/891431 "}, {"RefNumber": "885892", "RefComponent": "IS-OIL-DS-OGSD", "RefTitle": "Availability of OGSD in SAP ECC IS-Oil 6.0", "RefUrl": "/notes/885892 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "VIRCC 520_640", "SupportPackage": "SP002", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005937&support_package=SP002&patch_level=000000"}, {"SoftwareComponentVersion": "VIRCC 520_700", "SupportPackage": "SP002", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005938&support_package=SP002&patch_level=000000"}, {"SoftwareComponentVersion": "VIRRE 520_640", "SupportPackage": "SP002", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005953&support_package=SP002&patch_level=000000"}, {"SoftwareComponentVersion": "VIRRE 520_700", "SupportPackage": "SP002", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005954&support_package=SP002&patch_level=000000"}, {"SoftwareComponentVersion": "VIRAE 520_700", "SupportPackage": "SP002", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005955&support_package=SP002&patch_level=000000"}, {"SoftwareComponentVersion": "VIRAE 520_640", "SupportPackage": "SP002", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005956&support_package=SP002&patch_level=000000"}, {"SoftwareComponentVersion": "SAP BUSINESS ONE 2005 A SP01", "SupportPackage": "SP001", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005354&support_package=SP001&patch_level=000000"}, {"SoftwareComponentVersion": "VIRCC 520_640", "SupportPackage": "SP001", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005937&support_package=SP001&patch_level=000000"}, {"SoftwareComponentVersion": "VIRCC 520_700", "SupportPackage": "SP001", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005938&support_package=SP001&patch_level=000000"}, {"SoftwareComponentVersion": "VIRFF 520_640", "SupportPackage": "SP001", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005947&support_package=SP001&patch_level=000000"}, {"SoftwareComponentVersion": "VIRFF 520_700", "SupportPackage": "SP001", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200005948&support_package=SP001&patch_level=000000"}, {"SoftwareComponentVersion": "SAP XPD 200", "SupportPackage": "SP011", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004357&support_package=SP011&patch_level=000000"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}