{"Request": {"Number": "3007466", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1136, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000126632021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003007466?language=E&token=2E12BFA40BD2AFF3578169DF96CBCE61"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003007466", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003007466/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3007466"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.05.2021"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-MGM-PRC-IMD"}, "SAPComponentKeyText": {"_label": "Component", "value": "IMDS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP EHS Management", "value": "EHS-MGM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Compliance Management", "value": "EHS-MGM-PRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IMDS", "value": "EHS-MGM-PRC-IMD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC-IMD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3007466 - IMDS v13 - Support For Functional Changes in AI Interface"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using the International Material Data System (IMDS) functionality provided by the Product Compliance component. For the IMDS Release 13 the Advanced Interface (AI) will be updated as described in document <strong><em>Preliminary IMDS Release 13 Information</em></strong> published by IMDS (https://public.mdsystem.com/documents/10906/16820/preliminary-release-information_13.0_en.pdf). This results in required adjustments within functionality Product Compliance as following:</p>\r\n<ul>\r\n<li>Chapter 2: Increased precision for portions</li>\r\n<li>Chapter 4: Support for multi-sourcing</li>\r\n<li>Chapter 5: Lower threshold of application code</li>\r\n<li>Chapter 13: Confidential substances in REACH and GADSL</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">&#160;&#160;</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n\r\n<p>v13, 13,&#160;SA<PERSON><PERSON>, SARDaily, Exemption, SVHC, EHPRC_CPM_IMPORT 235,&#160;EHPRC_CPM_IMPORT 236,&#160;EHPRC_CPM_IMPORT 237,&#160;EHPRC_CPM_IMPORT 238,&#160;EHPRC_CPM_IMPORT 239,&#160;EHPRC_CPM_IMPORT 240,&#160;EHPRC_CPM_IMPORT 241,&#160;EHPRC_CPM_IMPORT 242, EHPRC_CP_IMDS_RES 087, EHPRC_CP_IMDS_RES 032, EHPRC_CPM_IMDS 093, EHPRC_CPM_IMDS 149, EHPRC_CPM_IMDS 150, EHPRC_CPM_CHECK 093</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\">The symptom is caused by an interface enhancement.</p>\r\n<p>Prerequisites can be found in the relevant correction instructions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p id=\"\">Implement this SAP Note or import either the corresponding Support Packages for the component extension of SAP EHS Management or the Feature Packages Stacks for SAP S/4HANA.</p>\r\n\r\n<p>After implementation of this SAP Note the behavior changes as following.</p>\r\n<ul>\r\n<li><strong>Chapter 2: Increased precision for portions<br /><br /></strong>According to Chapter 2 of the release notes, the maximum number of floating-point digits for proportions and weights will be enhanced from 6 to 9 decimals. The smallest possible&#160;proportion&#160;in IMDS will be changed from currently 0.000 001% (10ppb) to 0.000 000 001% (0.01ppb) while the smallest possible weight will be changed from currently 0.000 001g (1&#956;g) to 0.000 000 001g (1ng). This SAP Note introduces the following enhancements in SAP EHSM Component Extension and SAP S/4HANA for Product Compliance which shall help you to handle the increased precision of decimal places for proportions and weights:<br /><br /></li>\r\n<ul>\r\n<li>\r\n<p style=\"display: inline !important;\"><span style=\"text-decoration: underline;\">No Change:</span> The database table definitions in SAP EHSM Component Extension and SAP S/4HANA for Product Compliance are not changed. If small proportion or weight values are imported the system will try to convert them into a smaller UoM. You must check and adjust the configuration for UoM for dimension Mass and Proportion in transcation CUNI as described in the manual post installation steps.</p>\r\n</li>\r\n<li><span style=\"text-decoration: underline;\">Changed:</span> IMDS weight Check logic and message (check criterion IMDS 2093 for smallest weight) has been enhanced to support a precision of up to 9 decimals (0.000000001g or (1ng)</li>\r\n<li><span style=\"text-decoration: underline;\">Changed:</span> IMDS 100 % check logic and messages have been enhanced to support a precision of up to 9 decimals 0.000 000 001% (0.01ppb) for the following 3 check critereons:</li>\r\n<ul>\r\n<li>Sum of basic materials not 100% (IMDS/018)</li>\r\n<li>Sum of bulks not 100% not 100% (IMDS/121)</li>\r\n<li>Sum of pure substances not 100% (IMDS/015)</li>\r\n<li>You must check and adjust the configuration for the environment parameters&#160;PDB_COMP_CALC_PURE_TOLERANCE and&#160;PDB_COMP_CALC_BASMAT_TOLERANCE&#160;as described in the manual post installation steps.</li>\r\n</ul>\r\n<li><span style=\"text-decoration: underline;\">Changed:</span> The XML generation to upload XMLs into IMDS has been enhanced to convert concentration and weights stored with UoM PPT or NG into % respectively G.</li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">&#160;</span></p>\r\n<ul>\r\n<li><strong>Chapter 4: Support for multi-sourcing<br /></strong>\r\n<p>This SAP Note introduces the following changes in SAP EHSM Component Extension and SAP S/4HANA for Product Compliance which shall enable multi-source usage for purchased components and for assemblies, that contain only one alternative group item per Bill of Material:</p>\r\n</li>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\"><span style=\"text-decoration: underline;\">Changed Data Model:</span> The ROOT node of the Business Object EHPRC_COMPLIANCE_DATA (Compliance Data) has been enhanced with the flag MULTI_SOURCED_IND in database table EHPRCD_COD_ROOT.</span></li>\r\n<li><span style=\"text-decoration: underline;\">Changed&#160;UI for IMDS Tab and Automated Change Processing:</span><span style=\"font-size: 14px;\"> The field Multi Sourced has been added to the IMDS tab of the Compliance Requirement IMDS and is disabled / editable according to the table below. Changing and saving the Multisource Indicator will trigger the automated change processing for IMDS and BASE check.<br /><br /></span>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"3\"></td>\r\n<td colspan=\"3\"><strong>Multisource Checkbox</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Case</strong></td>\r\n<td><strong>Material Category</strong></td>\r\n<td><strong>Indicators</strong></td>\r\n<td><strong>Value</strong></td>\r\n<td><strong>Internal</strong></td>\r\n<td><strong>External</strong></td>\r\n</tr>\r\n<tr>\r\n<td>1</td>\r\n<td rowspan=\"2\">MAT_PART</td>\r\n<td>ASSEMBLY=YES<br />or<br />PURCHASED=YES</td>\r\n<td>&lt;user input&gt;</td>\r\n<td>Enabled</td>\r\n<td rowspan=\"7\">Disabled</td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>ELSE</td>\r\n<td>FALSE</td>\r\n<td>Disabled</td>\r\n</tr>\r\n<tr>\r\n<td>3</td>\r\n<td>PACKAGING</td>\r\n<td>&nbsp;</td>\r\n<td>FALSE</td>\r\n<td>Disabled</td>\r\n</tr>\r\n<tr>\r\n<td>4</td>\r\n<td>BULK</td>\r\n<td rowspan=\"4\"></td>\r\n<td rowspan=\"4\">FALSE</td>\r\n<td rowspan=\"4\">Disabled</td>\r\n</tr>\r\n<tr>\r\n<td>5</td>\r\n<td>MATERIAL</td>\r\n</tr>\r\n<tr>\r\n<td>6</td>\r\n<td>SURFACE</td>\r\n</tr>\r\n<tr>\r\n<td>8</td>\r\n<td>SUBSTANCE</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<br /><br /></li>\r\n<li><span style=\"text-decoration: underline;\">Changed UI for Weight and Composition Tab:</span> If a component is marked as multi-sourced, the measured weight field is disabled, and both value and unit of measure are deleted, as a multi-sourced component in IMDS must not have a measured weight. Its weight is defined by the calculated/measured weight of the preferred alternative item.</li>\r\n<li><span style=\"text-decoration: underline;\">Changed Import Configuration and Logic:</span> If the system imports a CMS download file, where the Material Data Sheet (MDS) is marked as multi-sourced or the MDS contains multi-sourced components the system will</li>\r\n<ul>\r\n<li>set the flag MULTI_SOURCED_IND for the affected multi-sourced component and</li>\r\n<li>import the referenced alternatives of the multi-sourced component into table ESTVP with PREF_ALT = 01 by marking the preferred one with PREF_ALT = X</li>\r\n</ul>\r\n<li><span style=\"text-decoration: underline;\">Changed IMDS Check Logic:</span> If a component is marked as multi-sourced the system checks, that the multi-sourced component must have a preferred alternative and that the measured weights of the non-preferred alternatives do not deviate from the measured weight of the preferred alternative considering the allowed weight deviation / tolerance of the item.</li>\r\n<li><span style=\"text-decoration: underline;\">Changed IMDS Normalization:</span> The normalization has been enhanced to keep non-preferred alternative branches of a multi-sourced component. In case no multi-sourcing is used the normalization will remove all non-preferred alternatives as of today (Status quo).</li>\r\n<li><span style=\"text-decoration: underline;\">Changed XML Upload to IMDS:</span> In order to upload a purchased part as multi-sourced component to IMDS you have to manually set the multi-sourced indicator for the purchased part. This allows you to control, whether you want to disclose all Supplier Parts for your Purchased Part in IMDS or only the preferred Supplier Part.<br />The XML generation has been enhanced to support the export of multi-sourced components to IMDS. In case of Streamlining the system will create Datasheets for the alternative items and try to internally release them in IMDS, during the upload of the multi-sourced component.<br /><br /></li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong style=\"font-size: 14px;\">Chapter 5: Lower Threshold of Application Code<br /><br /></strong>With IMDS 13 the Substance Application Relations (SAR) download file will be enhanced with a lower limit percentage value. This SAP Note introduces the following changes in SAP EHSM Component Extension and SAP S/4HANA for Product Compliance:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><span style=\"text-decoration: underline;\">Changed:</span> Customizing for Incoming Template IMDS_SAR</li>\r\n<li><span style=\"text-decoration: underline;\">Changed:</span> SAR file import is enhanced to process both upper and lower limit percentage value for an application code/exemption</li>\r\n<li><span style=\"text-decoration: underline;\">No Change:</span> The Import will update the Exemption / Substance assignment for the Regulatory List Revision used by the IMDS Check, which is GADSL by default (See Environment Parameter&#160;&#160;IMDS_REG_LIST_IMDS_APPL)</li>\r\n<li><span style=\"text-decoration: underline;\">No Change:</span> The system uses the lower and upper limit concentration values to display only exemptions / applications in the value help matching the substance and concentration condition</li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\"><strong>&#160;</strong></span></p>\r\n<ul>\r\n<li><strong>Chapter 13: Confidential Substances in REACH and GADSL<br /><br /></strong>This SAP Note introduces the following changes in SAP EHSM Component Extension and SAP S/4HANA for Product Compliance which shall support you to deal with confidential substances, which have been added to EU REACH SVC and/or GADSL in IMDS:<br /><br /></li>\r\n<ul>\r\n<li><span style=\"text-decoration: underline;\">New:</span> Download type CSPMALL and CSPMDaily to download all confidential substances used in published datasheets or only those valid until a key date from IMDS</li>\r\n<li><span style=\"text-decoration: underline;\">New:</span> Download type CSRMALL and CSRMDaily to download all confidential substances used in received datasheets or only those valid until a key date from IMDS</li>\r\n<li><span style=\"text-decoration: underline;\">New:</span> Incoming Template Configuration IMDS_CSPM and IMDS CSRM to process the download files for confidential substances</li>\r\n<li><span style=\"text-decoration: underline;\">New:</span> Import Logic to replace confidential substances marked with Identifier NUM IMDS_NODE = -1 with generic substances as described in table \"Generic Substances\"</li>\r\n<li><span style=\"text-decoration: underline;\">New:</span> Generic Substance Handling</li>\r\n<ul>\r\n<li>Read and Existence Check</li>\r\n<li>Initial Creation including referenced Listed Substances</li>\r\n<li>Add generic Substances to Regulations GADSL and SVHC</li>\r\n</ul>\r\n<li><span style=\"text-decoration: underline;\">Changed:</span> New field CONF_SUB_HIGH_CONCERN (Confidential Substance of High Concern) has been added to the RP Segment configuration of the incoming template IMDS_MDS_I to process confidential information in exiting continuous CMS File import from IMDS</li>\r\n<li><span style=\"text-decoration: underline;\">Changed:</span> Processing Logic to replace/create confidential substances marked with Identifier NUM IMDS_NODE = -1 with generic substances as described in table xxxx.&#160;The system updates the component category of the generic substance with the component category for confidential substances, defined in Environment parameter PDB_COMP_TYPE_CONFIDENTIAL (CONFIDENT by default).</li>\r\n<li><span style=\"text-decoration: underline;\">No Change:</span> After the import of confidential substances has been processed successfully the system will,</li>\r\n<ul>\r\n<li>Mark the composition change of the basic material as relevant change</li>\r\n<li>Process the relevant change and Identify the next release relevant level where the basic material is contained (either the material itself or another CDO in ACP Determination)</li>\r\n<li>Mark the confidential substance in the compliance workbench with the corresponding icons (check result and declarable/prohibited flag) for Compliance Requirement IMDS and EU REACH SVHC</li>\r\n<li>Execute the compliance checks for the regulatory lists defined by IMG parameter value IMDS_REG_LIST_IMDS_APPL for GADSL and IMDS_REG_LIST_REACH_SVHC for REACH SVHC (ACP Execution)</li>\r\n<li>If the IMDS Check recognizes a confidential substance, in the substance composition of a basic material or surface material, the system will validate the 2 check criteria and set the check status of the affected parent Object to checked with error / warning and display an error/warning message based on the configuration of the check criterion:<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Check</strong></td>\r\n<td><strong>Criteria ID</strong></td>\r\n<td><strong>Criteria Description</strong></td>\r\n<td><strong>Active</strong></td>\r\n<td><strong>Reaction Type</strong></td>\r\n<td><strong>Message Class</strong></td>\r\n<td><strong>Message Number</strong></td>\r\n<td><strong>Message</strong></td>\r\n</tr>\r\n<tr>\r\n<td>IMDS</td>\r\n<td>2140</td>\r\n<td>Conf. Substance must have CAS or EINECS NO</td>\r\n<td>Yes</td>\r\n<td>Error</td>\r\n<td>EHPRC_CPM_IMDS</td>\r\n<td>140</td>\r\n<td>Confidential substance &amp;1 must have a CAS or an EINECS number</td>\r\n</tr>\r\n<tr>\r\n<td>IMDS</td>\r\n<td>2141</td>\r\n<td>Conf. Substance must not be listed in GADSL or SVHC</td>\r\n<td>Yes</td>\r\n<td>Error</td>\r\n<td>EHPRC_CPM_IMDS</td>\r\n<td>141</td>\r\n<td>Disclose confidential substance &amp;1</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<br />By default the affected object and parent objects are marked with error and will prevent that the Material Data Sheet (MDS) can be sent or proposed until the owner of the MDS has provided an updated MDS with the undisclosed confidential substance. As the generic confidential substance are not assigned to the substance group &#8220;Application relevant substances in IMDS&#8221; they will not be collected as declarable substances in the IMDS check.</li>\r\n<li>The EU REACH SVHC check will collect the newly regulated confidential substances in the declarable substances.<br /><br /></li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong>Generic Substances</strong><br /><br />The table below gives an overview of the additional 7 generic substances, that are introduced with this correction note. All confidential substances not otherwise to be declared are represented by the Joker Substance with Identifier NUM IMDS_NODE = -1 in IMDS. The import will identify the Joker substance in the in-house system and replace it with one of the 7 generic substances having an identifier NUM PURE = -&lt;Confidential Substance Flag from IMDS&gt;. The generic substances use the Pseudo Identifier NUM IMDS_NODE =-1-&gt;&lt; Confidential Substance Flag from IMDS&gt;.<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Language</strong></td>\r\n<td><strong>Substance</strong></td>\r\n<td><strong>Listed Substance ID</strong></td>\r\n<td><strong>Identifier</strong><br /><strong>NUM IMDS_NODE</strong></td>\r\n<td><strong>Identifier</strong><br /><strong>NUM IMDS_PURE</strong></td>\r\n<td><strong>Comment</strong></td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Confidential Substances</td>\r\n<td>&nbsp;</td>\r\n<td>-1</td>\r\n<td>&nbsp;</td>\r\n<td>Original IMDS</td>\r\n</tr>\r\n<tr>\r\n<td>EN</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;Confidential Substances - GADSL D&#65279;</span></td>\r\n<td rowspan=\"2\">Y*</td>\r\n<td rowspan=\"2\">-1-&gt;1</td>\r\n<td rowspan=\"2\">-1</td>\r\n<td rowspan=\"14\">\r\n<p>Generated by a new program/function module. The program is executed by the system</p>\r\n<p>a) at the beginning of CSPM/SCRM File Import, if not empty.</p>\r\n<p>b) at the end of SBALL File Import.</p>\r\n<p>c) at the beginning of CSM File Import, if not empty</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>DE</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Vertrauliche Reinstoffe - GADSL D</span></td>\r\n</tr>\r\n<tr>\r\n<td>EN</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Confidential Substances - GADSL P</span></td>\r\n<td rowspan=\"2\">Y*</td>\r\n<td rowspan=\"2\">-1-&gt;2</td>\r\n<td rowspan=\"2\">-2</td>\r\n</tr>\r\n<tr>\r\n<td>DE</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Vertrauliche Reinstoffe - GADSL P</span></td>\r\n</tr>\r\n<tr>\r\n<td>EN</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Confidential Substances - SVHC</span></td>\r\n<td rowspan=\"2\">Y*</td>\r\n<td rowspan=\"2\">-1-&gt;3</td>\r\n<td rowspan=\"2\">-3</td>\r\n</tr>\r\n<tr>\r\n<td>DE</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Vertrauliche Reinstoffe - SVHC</span></td>\r\n</tr>\r\n<tr>\r\n<td>EN</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Confidential Substances - GADSL D+P</span></td>\r\n<td rowspan=\"2\">Y*</td>\r\n<td rowspan=\"2\">-1-&gt;4</td>\r\n<td rowspan=\"2\">-4</td>\r\n</tr>\r\n<tr>\r\n<td>DE</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Vertrauliche Reinstoffe - GADSL D+P</span></td>\r\n</tr>\r\n<tr>\r\n<td>EN</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Confidential Substances - GADSL D / SVHC&#160;</span></td>\r\n<td rowspan=\"2\">Y*</td>\r\n<td rowspan=\"2\">-1-&gt;5</td>\r\n<td rowspan=\"2\">-5</td>\r\n</tr>\r\n<tr>\r\n<td>DE</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Vertrauliche Reinstoffe -&#160;GADSL D / SVHC</span></td>\r\n</tr>\r\n<tr>\r\n<td>EN</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Confidential Substances -&#160;GADSL P / SVHC</span></td>\r\n<td rowspan=\"2\">Y*</td>\r\n<td rowspan=\"2\">-1-&gt;6</td>\r\n<td rowspan=\"2\">-6</td>\r\n</tr>\r\n<tr>\r\n<td>DE</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Vertrauliche Reinstoffe -&#160;GADSL P / SVHC</span></td>\r\n</tr>\r\n<tr>\r\n<td>EN</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Confidential Substances -&#160;GADSL D+P / SVHC</span></td>\r\n<td rowspan=\"2\">Y*</td>\r\n<td rowspan=\"2\">-1-&gt;7</td>\r\n<td rowspan=\"2\">-7</td>\r\n</tr>\r\n<tr>\r\n<td>DE</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Vertrauliche Reinstoffe -&#160;GADSL D+P / SVHC</span></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\"><strong>&#160;</strong></span></p>\r\n<ul>\r\n<ul>\r\n<li><strong>Example<br /><br /></strong>Initial status is a published data sheet &#8220;IMDS Release 13.0 Confidential Substances&#8221;, which is used in an own product Housing. The Housing has been uploaded to IMDS already.<br /><br /></li>\r\n<ul>\r\n<li>Assembly <em>Housing</em></li>\r\n<ul>\r\n<li>Component <em>IMDS Release 13.0 Confidential Substance</em></li>\r\n<ul>\r\n<li>Basic Material <em>GADSL-D Material</em></li>\r\n<ul>\r\n<li>Substance <em>Iron</em></li>\r\n<li><em>Confidential Substances</em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-P Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substances</em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substances</em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-D/P Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance</em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-D REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance</em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-P REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance</em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-D/P REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance<br /><br /></em></li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<li>Next step is to process the confidential substance file. Log reports following messages:</li>\r\n<ul>\r\n<li>Validation of confidential substance of high concern (CSHC)</li>\r\n<li>Confidential substance of high concern records (segment 'CSHC')</li>\r\n<li>NodeID ...: Confidential substance in IMDS material ... changed</li>\r\n</ul>\r\n<li>As a result the system created the ACP relevant changes for the basic materials and marked the corresponding release relevant compliance data objects&#160;as pending (to be checked).<br /><br /></li>\r\n<ul>\r\n<li>Assembly&#160;<em>Housing</em></li>\r\n<ul>\r\n<li>Component&#160;<em>IMDS Release 13.0 Confidential Substance</em></li>\r\n<ul>\r\n<li>Basic Material&#160;<em>GADSL-D Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substances - GADSL D</em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-P Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substances<em>&#160;- GADSL P</em></em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substances<em>&#160;- GADSL SVHC</em></em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-D/P Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance<em>&#160;- GADSL D+P</em></em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-D REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance<em>&#160;- GADSL D / SVHC</em></em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-P REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance<em><em>&#160;- GADSL P / SVHC</em></em></em></li>\r\n</ul>\r\n<li>Basic Material&#160;<em>GADSL-D/P REACH SVHC Material</em></li>\r\n<ul>\r\n<li>Substance&#160;<em>Iron</em></li>\r\n<li><em>Confidential Substance<em><em>&#160;- GADSL D+P / SVHC<br /><br /></em></em></em></li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<li>Open the Compliance Data Object and process the checks for compliance requirement Automotive (IMDS) and REACH SVHC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<li>The IMDS Check issues an error message that the generic substance does not have a node id and as a consequence cannot be sent used in own data sheets unless the owner has provided an update.</li>\r\n<li>For EU REACH SVHC the declarable substances are checked and rolled up.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>Refer to&#160;<em>Preliminary IMDS Release 13 Information</em>&#160;chapter 2, 4, 5 and 13 for details.</p>\r\n<p><span style=\"font-size: 14px;\">&#160;&#160;</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054665)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003007466/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003007466/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3394838", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "IMDS Import fails", "RefUrl": "/notes/3394838 "}, {"RefNumber": "2147718", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 6.0 for SAP EHS Management: RIN", "RefUrl": "/notes/2147718 "}, {"RefNumber": "3084640", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "Composition Rounding or Field Overflow Error from IMDS Import", "RefUrl": "/notes/3084640 "}, {"RefNumber": "3007465", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "IMDS v13 - Support For Functional Changes in AI Interface", "RefUrl": "/notes/3007465 "}, {"RefNumber": "3017469", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "RefUrl": "/notes/3017469 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10111INS4CORE", "URL": "/supportpackage/SAPK-10111INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10209INS4CORE", "URL": "/supportpackage/SAPK-10209INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10307INS4CORE", "URL": "/supportpackage/SAPK-10307INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10405INS4CORE", "URL": "/supportpackage/SAPK-10405INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10503INS4CORE", "URL": "/supportpackage/SAPK-10503INS4CORE"}, {"SoftwareComponentVersion": "EHSM 400", "SupportPackage": "SAPK-40007INEHSM", "URL": "/supportpackage/SAPK-40007INEHSM"}, {"SoftwareComponentVersion": "EHSM 600", "SupportPackage": "SAPK-60007INEHSM", "URL": "/supportpackage/SAPK-60007INEHSM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 5, "URL": "/corrins/0003007466/19773"}, {"SoftwareComponent": "EHSM", "NumberOfCorrin": 2, "URL": "/corrins/0003007466/9587"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-10104INS4CORE - SAPK-10110INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-10205INS4CORE - SAPK-10208INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-10303INS4CORE - SAPK-10306INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10404INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 105&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10502INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Execute program R_EHPRC_NOTE_3007466. Activate and save all changes to transport request of SAP Note 3007466.<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 400&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-40006INEHSM - SAPK-40006INEHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60002INEHSM - SAPK-60006INEHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Execute program R_EHPRC_NOTE_3007466. Activate and save all changes to transport request of SAP Note 3007466.<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-10104INS4CORE - SAPK-10110INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-10205INS4CORE - SAPK-10208INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-10303INS4CORE - SAPK-10306INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10404INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 105&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10502INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/></P> <UL><LI>Execute program R_EHPRC_NOTE_3007466 again.</LI></UL> <UL><LI>Open IMG for editing in transaction SPRO.</LI></UL> <UL><LI>Navigate to IMG activity Product Safety and Stewardship -&gt; Product  Compliance for Discrete Industries -&gt; General Configuration -&gt; Data Exchange -&gt; Specify Outgoing and Incoming Templates</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Incoming: X <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template Name: IMDS Confid. subst. in published MDSs <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSRM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Incoming: X <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template Name: IMDS Confid. subst. in received MDSs <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG activity Product Safety and Stewardship -&gt; Product  Compliance for Discrete Industries -&gt; General Configuration -&gt; Data Exchange -&gt; Specify Incoming Templates.</LI></UL> <UL><LI>Add following new entry to the list of templates. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data Origin: IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Identification File: EHPRC_CP_IM52_IDENT_IMDS_CSPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Processing: EHPRC_CP_IM52_CS_IMPORT_START <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Date Format: 1 - DD.MM.YYYY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Based On M.Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Suppl. No. Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Document Type: DATA <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Applic. : DAT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No Checks: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attachment Type: IBDOC <UL><LI>Add following new entry to the list of templates. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSRM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data Origin: IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Identification File: EHPRC_CP_IM52_IDENT_IMDS_CSRM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Processing: EHPRC_CP_IM52_CS_IMPORT_START <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Date Format: 1 - DD.MM.YYYY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Based On M.Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Suppl. No. Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Document Type: DATA <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Applic. : DAT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No Checks: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attachment Type: IBDOC <UL><LI>Select Template IMDS_MDS_I.</LI></UL> <UL><LI>Navigate to Segment Fields.</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: RP <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field: CONF_SUB_HIGH_CONCERN <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Confidential Substance of High Concern <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 17 <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: ISMULTISOURCE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Is Multi-Sourced <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 9 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: PRODUCTION_IN_EU <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Production in European Union <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 10 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: SCIP_NO <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: SCIP Number <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 11 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: SCIP_SUBMISSION_NO <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: SCIP Submission Number <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 12 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: IS_AUTOM_SCIP_NO <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Is Automatic SCIP Number <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 13 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Save your changes.</LI></UL> <UL><LI>Select Template IMDS_SAR.</LI></UL> <UL><LI>Navigate to Segment Fields.</LI></UL> <UL><LI>Update following entry:</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: SAR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field: PERCENTAGE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Percentage Max <UL><LI>Add a new entry as following:</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: SAR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field: PERCENTAGE_MIN <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Percentage Min <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 5 <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG activity Product Safety and Stewardship -&gt; Product  Compliance for Discrete Industries -&gt; IMDS Compliance -&gt; Specify IMDS Download Types</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSPMAll <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: All confidential substances in published MDSs until key date <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSPMDaily <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: Confidential substances in published MDSs at key date <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSRMAll <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: All confidential substances in received MDSs until key date <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSRMDaily <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: Confidential substances in received MDSs at key date <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG Activity Product Safety and Stewardship -&gt; Product  Compliance for Discrete Industries -&gt; General Configuration -&gt;  Regulations and Compliance Requirements -&gt; Specify Checks for Compliance Requirement and Check Criteria</LI></UL> <UL><LI>Select Compliance Requirement IMDS</LI></UL> <UL><LI>Navigate to Define Criteria for Compliance Requirements</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria ID: 2147 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria: Multisource: Missing item <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Active: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Status: 0 - Error <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message ID: EHPRC_CPM_IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message: 149 <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria ID: 2148 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria: Multisource: Weight deviation <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Active: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Status: 0 - Error <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message ID: EHPRC_CPM_IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message: 150 <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG Activity Product Safety and Stewardship -&gt; Product  Compliance for Discrete Industries -&gt; General Configuration -&gt;  Environment Parameters -&gt; Specify Environment Parameters for Back-End Processes</LI></UL> <UL><LI>Change the values of following parameters:</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PDB_COMP_CALC_PURE_TOLERANCE: 0.000000001 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PDB_COMP_CALC_BASMAT_TOLERANCE: 0.000000001 <UL><LI>Save your changes.</LI></UL> <UL><LI>Open units of measure for dimension Mass in transaction CUNI.</LI></UL> <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: UG <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: ug <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: µg <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Microgramm, ug <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -9 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Decimal Rounding: 4 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: NG <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: ng <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ng <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Nanogramm, ng <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -12 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Decimal Rounding: 4 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Save your changes.</LI></UL> <UL><LI>Open units of measure for dimension Proportion in transaction CUNI.</LI></UL> <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: PPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: PPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ppm <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: part per million, ppm <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -6 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ALE/EDI - ISO code: 59 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: PPB <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: PPB <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ppb <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Part per billion, ppb <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -9 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: PPT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: PPT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ppt <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Part per trillion, ppt <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -12 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Save your changes.</LI></UL> <P><br/><br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 400&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-40006INEHSM - SAPK-40006INEHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60002INEHSM - SAPK-60006INEHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <UL><LI>Execute program R_EHPRC_NOTE_3007466 again.</LI></UL> <UL><LI>Open IMG for editing in transaction SPRO.</LI></UL> <UL><LI>Navigate to IMG activity SAP EHS Management -&gt; Product Compliance -&gt;  General Configuration -&gt; Data Exchange -&gt; Specify Outgoing and Incoming Templates</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Incoming: X <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template Name: IMDS Confid. subst. in published MDSs <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSRM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Incoming: X <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template Name: IMDS Confid. subst. in received MDSs <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG activity SAP EHS Management -&gt; Product Compliance -&gt;  General Configuration -&gt; Data Exchange -&gt; Specify Incoming Templates.</LI></UL> <UL><LI>Add following new entry to the list of templates. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data Origin: IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Identification File: EHPRC_CP_IM52_IDENT_IMDS_CSPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Processing: EHPRC_CP_IM52_CS_IMPORT_START <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Date Format: 1 - DD.MM.YYYY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Based On M.Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Suppl. No. Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Document Type: DATA <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Applic. : DAT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No Checks: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attachment Type: IBDOC <UL><LI>Add following new entry to the list of templates. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Template: IMDS_CSRM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data Origin: IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Identification File: EHPRC_CP_IM52_IDENT_IMDS_CSRM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Processing: EHPRC_CP_IM52_CS_IMPORT_START <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Date Format: 1 - DD.MM.YYYY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Based On M.Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Abort Suppl. No. Chk: N - Nothing <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Document Type: DATA <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Applic. : DAT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No Checks: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attachment Type: IBDOC <UL><LI>Select Template IMDS_MDS_I.</LI></UL> <UL><LI>Navigate to Segment Fields.</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: RP <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field: CONF_SUB_HIGH_CONCERN <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Confidential Substance of High Concern <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 17 <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: ISMULTISOURCE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Is Multi-Sourced <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 9 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: PRODUCTION_IN_EU <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Production in European Union <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 10 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: SCIP_NO <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: SCIP Number <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 11 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: SCIP_SUBMISSION_NO <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: SCIP Submission Number <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 12 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: C <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field: IS_AUTOM_SCIP_NO <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Is Automatic SCIP Number <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 13 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Material Category: PRODUCT <UL><LI>Save your changes.</LI></UL> <UL><LI>Select Template IMDS_SAR.</LI></UL> <UL><LI>Navigate to Segment Fields.</LI></UL> <UL><LI>Update following entry:</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: SAR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field: PERCENTAGE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Percentage Max <UL><LI>Add a new entry as following:</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment: SAR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field: PERCENTAGE_MIN <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Segment Field Name: Percentage Min <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sort: 5 <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG activity SAP EHS Management -&gt; Product Compliance -&gt;  IMDS Compliance -&gt; Specify IMDS Download Types</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSPMAll <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: All confidential substances in published MDSs until key date <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSPMDaily <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: Confidential substances in published MDSs at key date <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSRMAll <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: All confidential substances in received MDSs until key date <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Type: CSRMDaily <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description of Download Type: Confidential substances in received MDSs at key date <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG Activity SAP EHS Management -&gt; Product Compliance -&gt;  General Configuration -&gt; Regulations and Compliance Requirements -&gt;  Specify Checks for Compliance Requirement and Check Criteria</LI></UL> <UL><LI>Select Compliance Requirement IMDS</LI></UL> <UL><LI>Navigate to Define Criteria for Compliance Requirements</LI></UL> <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria ID: 2147 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria: Multisource: Missing item <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Active: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Status: 0 - Error <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message ID: EHPRC_CPM_IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message: 149 <UL><LI>Add a new entry as following. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria ID: 2148 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Criteria: Multisource: Weight deviation <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Active: X (checkbox selected) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Status: 0 - Error <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message ID: EHPRC_CPM_IMDS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message: 150 <UL><LI>Save your changes.</LI></UL> <UL><LI>Navigate to IMG Activity SAP EHS Management -&gt; Product Compliance -&gt;  General Configuration -&gt; Environment Parameters -&gt; Specify Environment Parameters for Back-End Processes</LI></UL> <UL><LI>Change the values of following parameters:</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PDB_COMP_CALC_PURE_TOLERANCE: 0.000000001 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PDB_COMP_CALC_BASMAT_TOLERANCE: 0.000000001 <UL><LI>Save your changes.</LI></UL> <UL><LI>Open units of measure for dimension Mass in transaction CUNI.</LI></UL> <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: UG <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: ug <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: µg <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Microgramm, ug <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -9 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Decimal Rounding: 4 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: NG <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: ng <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ng <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Nanogramm, ng <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -12 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Decimal Rounding: 4 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Save your changes.</LI></UL> <UL><LI>Open units of measure for dimension Proportion in transaction CUNI.</LI></UL> <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: PPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: PPM <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ppm <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: part per million, ppm <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -6 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ALE/EDI - ISO code: 59 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: PPB <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: PPB <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ppb <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Part per billion, ppb <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -9 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Create or update following unit of measure as necessary. Keep the default values of other attributes.</LI></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Internal UoM: PPT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Commercial: PPT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Display - Technical: ppt <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Measurement unit text: Part per trillion, ppt <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Numerator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Denominator: 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Conversion - Exponent: -12 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application Parameters: Commercial meas.unit selected <UL><LI>Save your changes.</LI></UL> <P><br/><br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 7, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 4, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 47, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "1923167 ", "URL": "/notes/1923167 ", "Title": "Data Migration: Performance Improvements in Determ/Validatio", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2344665 ", "URL": "/notes/2344665 ", "Title": "IMDS: New check criterion for not accepted components", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2360869 ", "URL": "/notes/2360869 ", "Title": "IMDS 11.0", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2371711 ", "URL": "/notes/2371711 ", "Title": "Corrections for IMDS Recommendation 019: Electric/Electronic components and assemblies", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2386161 ", "URL": "/notes/2386161 ", "Title": "REACH O5A Support for EHSM PRC and S4HANA OP PRC", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2399409 ", "URL": "/notes/2399409 ", "Title": "ACP: Prevent running during long running programs", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2406375 ", "URL": "/notes/2406375 ", "Title": "CDO Determination Dependency", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2418191 ", "URL": "/notes/2418191 ", "Title": "BO EHPRC_COMPLIANCE_DATA: Enable Actions for customer enhancements", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2431581 ", "URL": "/notes/2431581 ", "Title": "IMDS Upload XML: Polymer value is missing for carrier components", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2456301 ", "URL": "/notes/2456301 ", "Title": "Mixed compositions with basic materials and bulks are not handled correctly during IMDS upload", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2490561 ", "URL": "/notes/2490561 ", "Title": "IMDS SG Import: Add missing listed substances for GADSL list", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2522766 ", "URL": "/notes/2522766 ", "Title": "Calculated Weight change triggers ACP too often", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2537834 ", "URL": "/notes/2537834 ", "Title": "Availibility of PSA specific attributes in chapter 4 (Recipient Information)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2548161 ", "URL": "/notes/2548161 ", "Title": "IMDS: Import overwrites specific weight for semi-components", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2550161 ", "URL": "/notes/2550161 ", "Title": "IMDS: Error \"Calculation yields a rest of 0%!\" during release of a MDS", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2552910 ", "URL": "/notes/2552910 ", "Title": "IMDS: Performance and Locking issues when importing MDS in parallel", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2554357 ", "URL": "/notes/2554357 ", "Title": "IMDS Chapter 4 data gets deleted during the import of own datasheets", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2559380 ", "URL": "/notes/2559380 ", "Title": "Product Structure - Tree displays different order of components", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2559565 ", "URL": "/notes/2559565 ", "Title": "IMDS: Error when saving an MDS to IMDS", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2565095 ", "URL": "/notes/2565095 ", "Title": "IMDS: SB file import removes Listed Substance names", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2565885 ", "URL": "/notes/2565885 ", "Title": "IMDS Specific weight of semi-component gets transferred incorrectly", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2586727 ", "URL": "/notes/2586727 ", "Title": "Wrong calculation of confidential proportion during IMDS check", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2613244 ", "URL": "/notes/2613244 ", "Title": "IMDS improve hidden Substance handling for GADSL Regulatory List Revision", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2633390 ", "URL": "/notes/2633390 ", "Title": "Error at MDS import due to check criteria 2099", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2651629 ", "URL": "/notes/2651629 ", "Title": "Performance issue with Compliance Check", "Component": "EHS-MGM-PRC-CMP"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2707744 ", "URL": "/notes/2707744 ", "Title": "Bulk Like Packaging not supported in Compliance Workbench / Consistency Check", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2719016 ", "URL": "/notes/2719016 ", "Title": "Bulk Like Components are Considered as Part Like", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2764989 ", "URL": "/notes/2764989 ", "Title": "Compatibility with IMDS AI Interface v12", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2795340 ", "URL": "/notes/2795340 ", "Title": "IMDS - Recyclate information mandatory", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2813953 ", "URL": "/notes/2813953 ", "Title": "Compliance Check Fails Because of Inconsistent Data", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2917804 ", "URL": "/notes/2917804 ", "Title": "IMDS SBALL IMPORT dump - ITAB_DUPLICATE_KEY", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2918236 ", "URL": "/notes/2918236 ", "Title": "IMDS 12.2 - Support of Default Contact", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2922865 ", "URL": "/notes/2922865 ", "Title": "IMDS 12.2 - Support for DMV Files", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2945561 ", "URL": "/notes/2945561 ", "Title": "IMDS - Dump during Import of IMDS Supplier MDS", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2955783 ", "URL": "/notes/2955783 ", "Title": "DBSQL Error During IMDS Import and Check", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2988109 ", "URL": "/notes/2988109 ", "Title": "IMDS - import of listed substance not correct", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3007466 ", "URL": "/notes/3007466 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3017469 ", "URL": "/notes/3017469 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3018330 ", "URL": "/notes/3018330 ", "Title": "Turning off the 100% complete composition check is not possible", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3027451 ", "URL": "/notes/3027451 ", "Title": "IMDS Weight Does Not Report Too Low Weights of Bulk Components, Basic Material  or Surface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3035833 ", "URL": "/notes/3035833 ", "Title": "Compliance Workbench - Performance in Compliance Check", "Component": "EHS-MGM-PRC-CMP"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "600", "Number": "2350183 ", "URL": "/notes/2350183 ", "Title": "Corrections in IMDS check", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "600", "Number": "2606488 ", "URL": "/notes/2606488 ", "Title": "New IMDS Version is not retrieved from result file", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "600", "Number": "2667662 ", "URL": "/notes/2667662 ", "Title": "Wrong Calculated Weight Without Packaging", "Component": "EHS-MGM-PRC-MAT"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "600", "Number": "2738045 ", "URL": "/notes/2738045 ", "Title": "Tasks in Work Overview are missing Product Type identifier in the subject", "Component": "EHS-MGM-PRC-CWL"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2360869 ", "URL": "/notes/2360869 ", "Title": "IMDS 11.0", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2371711 ", "URL": "/notes/2371711 ", "Title": "Corrections for IMDS Recommendation 019: Electric/Electronic components and assemblies", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2456301 ", "URL": "/notes/2456301 ", "Title": "Mixed compositions with basic materials and bulks are not handled correctly during IMDS upload", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2537834 ", "URL": "/notes/2537834 ", "Title": "Availibility of PSA specific attributes in chapter 4 (Recipient Information)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2550161 ", "URL": "/notes/2550161 ", "Title": "IMDS: Error \"Calculation yields a rest of 0%!\" during release of a MDS", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2559380 ", "URL": "/notes/2559380 ", "Title": "Product Structure - Tree displays different order of components", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2565885 ", "URL": "/notes/2565885 ", "Title": "IMDS Specific weight of semi-component gets transferred incorrectly", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2918236 ", "URL": "/notes/2918236 ", "Title": "IMDS 12.2 - Support of Default Contact", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2922865 ", "URL": "/notes/2922865 ", "Title": "IMDS 12.2 - Support for DMV Files", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2955783 ", "URL": "/notes/2955783 ", "Title": "DBSQL Error During IMDS Import and Check", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "3007466 ", "URL": "/notes/3007466 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "3017469 ", "URL": "/notes/3017469 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "3017533 ", "URL": "/notes/3017533 ", "Title": "ANNEX Information at Exemption Missing in Regulatory List", "Component": "EHS-MGM-FND"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "103", "Number": "2738045 ", "URL": "/notes/2738045 ", "Title": "Tasks in Work Overview are missing Product Type identifier in the subject", "Component": "EHS-MGM-PRC-CWL"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2918236 ", "URL": "/notes/2918236 ", "Title": "IMDS 12.2 - Support of Default Contact", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2922865 ", "URL": "/notes/2922865 ", "Title": "IMDS 12.2 - Support for DMV Files", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "3007466 ", "URL": "/notes/3007466 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "3017469 ", "URL": "/notes/3017469 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2918236 ", "URL": "/notes/2918236 ", "Title": "IMDS 12.2 - Support of Default Contact", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2922865 ", "URL": "/notes/2922865 ", "Title": "IMDS 12.2 - Support for DMV Files", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "3007466 ", "URL": "/notes/3007466 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "3017469 ", "URL": "/notes/3017469 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2918236 ", "URL": "/notes/2918236 ", "Title": "IMDS 12.2 - Support of Default Contact", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2922865 ", "URL": "/notes/2922865 ", "Title": "IMDS 12.2 - Support for DMV Files", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "3007466 ", "URL": "/notes/3007466 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "3017469 ", "URL": "/notes/3017469 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2918236 ", "URL": "/notes/2918236 ", "Title": "IMDS 12.2 - Support of Default Contact", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2922865 ", "URL": "/notes/2922865 ", "Title": "IMDS 12.2 - Support for DMV Files", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "3007466 ", "URL": "/notes/3007466 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "3017469 ", "URL": "/notes/3017469 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "105", "ValidTo": "105", "Number": "3007466 ", "URL": "/notes/3007466 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "105", "ValidTo": "105", "Number": "3017469 ", "URL": "/notes/3017469 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface (Preparation)", "Component": "EHS-MGM-PRC-IMD"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3013456", "RefTitle": "IMDS v13 - Support For Functional Changes in AI Interface", "RefUrl": "/notes/0003013456"}]}}}}}