{"Request": {"Number": "116638", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 316, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000481612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000116638?language=E&token=435AB80C491A4E447E892B4722CB6DAD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000116638", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000116638/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "116638"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.05.2000"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-RB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rebate Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rebate Processing", "value": "SD-BIL-RB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-RB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "116638 - Upgrade of info. structure S060 in Release 4.*"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you install Release 4.*, if information structure S060 is updated with program SDBONTO4, bad performance can lead to long runtimes.<br />In addition no status information is issued in the background log to inform the user how many documents have been processed. The logging provided up to now is insufficient since it is only issued after the end of the update.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>S060, volume-based rebate, SDS060RB</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is a performance problem.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The error is corrected in Release 4.5B.</p> <UL><LI>Carry out the attached advance correction.</LI></UL> <UL><LI>By means of Transaction SE38 for program<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SDS060RB define the following text symbols :<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symbol&#x00A0;&#x00A0;Text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;P01&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number of records processed:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;P02&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number of selected records:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;and the following parameter text<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Parameter&#x00A0;&#x00A0;Text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;INITS060&#x00A0;&#x00A0;&#x00A0;&#x00A0;Initialize S060<br /></LI></UL> <UL><LI>In the production run, you should deactivate logging of processed records since with this the system performs unnecessary main storage operations.</LI></UL> <UL><LI>For the upgrade not report SDBONTO4 but report SDS060RB should be used.</LI></UL> <UL><LI>Program SDS060RB should be used so at the first start in the production run the indicator 'Initialize S060' is marked.In this first run, with the selection by billing date you should process newer documents .In following runs, with an appropriate selection you can process older documents.In these runs, the indicator 'Initialize S060' must not be marked.</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D002635)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000116638/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116638/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "417693", "RefComponent": "SD-BIL-RB", "RefTitle": "Statistics for volume-based rebate S060 are not updated", "RefUrl": "/notes/417693"}, {"RefNumber": "196145", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/196145"}, {"RefNumber": "179482", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/179482"}, {"RefNumber": "159436", "RefComponent": "SD-BIL-RB", "RefTitle": "Log for S060 reorganization", "RefUrl": "/notes/159436"}, {"RefNumber": "140741", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate:Data enhmnt.S060 Rel.4.* slow", "RefUrl": "/notes/140741"}, {"RefNumber": "140718", "RefComponent": "SD-BIL-RB", "RefTitle": "Log during statistical setup of S060", "RefUrl": "/notes/140718"}, {"RefNumber": "122863", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate:Data enhancement S060 in Rel. 4.* is slow", "RefUrl": "/notes/122863"}, {"RefNumber": "1060629", "RefComponent": "SD-BIL-RB", "RefTitle": "SDS060RB/ENH_REBATE_S469RB:Reorg. of data relevant to rebate", "RefUrl": "/notes/1060629"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1060629", "RefComponent": "SD-BIL-RB", "RefTitle": "SDS060RB/ENH_REBATE_S469RB:Reorg. of data relevant to rebate", "RefUrl": "/notes/1060629 "}, {"RefNumber": "196145", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/196145 "}, {"RefNumber": "417693", "RefComponent": "SD-BIL-RB", "RefTitle": "Statistics for volume-based rebate S060 are not updated", "RefUrl": "/notes/417693 "}, {"RefNumber": "179482", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/179482 "}, {"RefNumber": "159436", "RefComponent": "SD-BIL-RB", "RefTitle": "Log for S060 reorganization", "RefUrl": "/notes/159436 "}, {"RefNumber": "140718", "RefComponent": "SD-BIL-RB", "RefTitle": "Log during statistical setup of S060", "RefUrl": "/notes/140718 "}, {"RefNumber": "140741", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate:Data enhmnt.S060 Rel.4.* slow", "RefUrl": "/notes/140741 "}, {"RefNumber": "122863", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate:Data enhancement S060 in Rel. 4.* is slow", "RefUrl": "/notes/122863 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B10", "URL": "/supportpackage/SAPKH40B10"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B10", "URL": "/supportpackage/SAPKE40B10"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000116638/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}