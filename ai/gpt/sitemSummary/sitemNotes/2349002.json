{"Request": {"Number": "2349002", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 500, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018375552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002349002?language=E&token=7BA906369CC3D49DBC1C2FF52363D30D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002349002", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002349002/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2349002"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.08.2019"}, "SAPComponentKey": {"_label": "Component", "value": "CA-MDG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Master Data Governance"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data Governance", "value": "CA-MDG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2349002 - SAP S/4HANA Master Data Governance 1610: Release Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note&#160;contains important information for customers using SAP S/4HANA Master Data Governance 1610.<br />This note may be subject to changes. Make sure that you always use the latest version of the note. Changes that have been added after the first release of the note are contained in section: \"Changes after general availability of SAP S/4HANA Master Data Governance 1610\"</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Master Data Governance, MDG_APPL 801, MDG_UX 801, MDG_FND 801</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You implement SAP S/4HANA Master Data Governance 1610.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Please note that certain master data fields as well as values for master data fields have been disabled in SAP S/4HANA On Premise. Fields and values which have been disabled&#160;can neither be maintained in nor distributed from SAP Master Data Governance when implemented on SAP S/4HANA On Premise. For details on disabled fields and changes to allowed values in SAP S/4HANA please see : <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfa4322f56824ae221e10000000a4450e5/1610%20000/en-US/SIMPL_OP1610.pdf\">Simplification List for SAP S/4HANA, on-premise edition 1610.</a></p>\r\n<p>The SAP stylesheet theme \"Belize\" is not supported. This includes&#160;usage of the following business catalogs to create a Fiori Launchpad for core Master Data Governance applications: SAP_MDG_BC_BUPA_DATA, SAP_MDG_BC_CUSTOMER_DATA, SAP_MDG_BC_SUPPLIER_DATA, SAP_MDG_BC_MATERIAL_DATA, SAP_MDG_BC_CUSTOBJ_DATA, SAP_MDG_BC_FINACC_DATA and SAP_MDG_BC_FINCTR_DATA. It also includes the corresponding business catalog groups SAP_MDG_BCG_BUPA_DATA, SAP_MDG_BCG_CUSTOMER_DATA, SAP_MDG_BCG_SUPPLIER_DATA, SAP_MDG_BCG_MATERIAL_DATA, SAP_MDG_BCG_CUSTOBJ_DATA, SAP_MDG_BCG_FINACC_DATA and SAP_MDG_BCG_FINCTR_DATA. Until the restriction is removed, you can use the regular PFCG roles SAP_MDG* with SAP NetWeaver Business Client instead.</p>\r\n<p>For details concerning the Fiori frontend installation please do read note 2327935. Customers with UIMDC001 100 software component previously installed, must first proceed with uninstallation of the component (Note 2230429) BEFORE they proceed with installation of Product verion SAP FIORI FOR SAP S/4HANA 1610.</p>\r\n<p>In case of software component MDG_MDC was installed before:&#160;as of S/4HANA 1610 the MDG_MDC functionality is part of software component S4CORE 101 - software component entry MDG_MDC is removed from table CVERS and set to UPDSTATUS=\"-\" in table AVERS.</p>\r\n<p>Customers using Master Data Governance with MDG 9.0 with S/4 HANA On Premise should read the following notes and implement them after importing the support package.</p>\r\n<p>The following notes from <strong>Master Data Governance, Foundation:</strong></p>\r\n<p><strong>Note&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Component&#160;&#160;&#160;&#160;&#160;&#160; &#160;Priority</strong></p>\r\n<p>2340118&#160;&#160;&#160; CA-MDG-AF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 2</p>\r\n<p>2358583&#160;&#160;&#160; CA-MDG-APP-MM&#160; 2</p>\r\n<p>2366276&#160;&#160;&#160; CA-MDG-AF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 2</p>\r\n<p>2364361&#160;&#160;&#160; CA-MDG-AF-DM&#160;&#160;&#160; 2</p>\r\n<p>2362512&#160;&#160;&#160; CA-MDG-AF-WF&#160;&#160;&#160; 2</p>\r\n<p>2358934&#160;&#160;&#160; CA-MDG-AF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 2</p>\r\n<p>2353562&#160;&#160; &#160;CA-MDG-AF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 2</p>\r\n<p>The following notes for <strong>Master Data Governance, Business Partner, Supplier, Customer:</strong></p>\r\n<p><strong>Note&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Component&#160;&#160;&#160;&#160;&#160;&#160; &#160;Priority</strong></p>\r\n<p>2348524&#160;&#160;&#160; CA-MDG-APP-BP&#160;&#160;&#160;&#160; 2</p>\r\n<p>2300169&#160;&#160;&#160; CA-MDG-APP-SUP&#160;&#160; 2</p>\r\n<p>The following notes from <strong>Master Data Governance, Consolidation and Mass Processing:</strong></p>\r\n<p><strong>Note&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Component&#160;&#160;&#160;&#160;&#160;&#160; &#160;Priority</strong></p>\r\n<p>2351887&#160;&#160;&#160;&#160;CA-MDG-CMP-MM&#160;&#160;&#160; 2</p>\r\n<p>2340558&#160;&#160;&#160; CA-MDG-CMP-FIO&#160;&#160;&#160; 3</p>\r\n<p>The following notes are relevant for <strong>Master Data Governance for Financials</strong>:</p>\r\n<p><strong>Note&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Component&#160;&#160;&#160;&#160;&#160;&#160;&#160; Priority</strong></p>\r\n<p>2359449&#160;&#160;&#160;&#160;CA-MDG-APP-FIN&#160;&#160;&#160; 2</p>\r\n<p>2352719&#160;&#160;&#160; CA-MDG-APP-FIN&#160;&#160;&#160; 2</p>\r\n<p>2352481&#160;&#160;&#160; CA-MDG-APP-FIN&#160;&#160;&#160; 2</p>\r\n<p>2348932&#160;&#160;&#160; CA-MDG-APP-FIN&#160;&#160;&#160; 2</p>\r\n<p>The following notes are relevant for <strong>Master Data Governance for Material</strong>:</p>\r\n<p><strong>Note&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Component&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Priority</strong></p>\r\n<p>2368946&#160;&#160;&#160; CA-MDG-APP-MM&#160;&#160;&#160; 2</p>\r\n<p>2364102&#160;&#160;&#160; CA-MDG-APP-MM&#160;&#160;&#160; 2</p>\r\n<p>2360539&#160;&#160;&#160; CA-MDG-APP-MM&#160;&#160;&#160; 2</p>\r\n<p>2293414&#160;&#160;&#160; CA-MDG-APP-MM&#160;&#160;&#160; 2</p>\r\n<p>&#160;</p>\r\n<p>The following note are relevant for <strong>Fiori</strong>:</p>\r\n<p>&#160;<strong>Note&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Component&#160;&#160;&#160;&#160;Priority</strong></p>\r\n<p><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text41-__clone8\" style=\"text-align: left;\">2230429&#160;&#160;&#160;&#160;XX-SER-REL&#160;&#160;&#160; 2</span></p>\r\n<p>2356208&#160;&#160;&#160;&#160;XX-SER-REL&#160;&#160;&#160; 2</p>\r\n<p>2302823&#160;&#160;&#160; CA-MDG-CMP&#160; 2</p>\r\n<p><br /><strong><strong>Changes after general availability of SAP S/4HANA Master Data Governance 1610</strong></strong></p>\r\n<p>Customers upgrading to SAP S/4HANA 1610 from classical Release of SAP Master Data Governance (MDG 8.0) have software component UIMDC001 100 previously installed.&#160; This component must first be uninstalled (Note 2230429) BEFORE proceeding with installation of Product verion SAP FIORI FOR SAP S/4HANA 1610.&#160;Deinstallation of the higher version UIMDC001 200 is not mandatory, but possible after manual migration of the configuration from UIMDC001 to UIS4HOP1.</p>\r\n<p>If you plan to have both backend ERP installations (classical and S/4HANA)&#160;<strong>AND</strong>&#160;a central gateway Hub for Fiori deployment, then you must ensure that you have two separate clients on your gateway system for&#160;UI for MDG Consolidation and Mass Processing.&#160; Using the same client is not possible as the delivered apps use the same sematic object.</p>\r\n<p>The following note is relevant from Netweaver:</p>\r\n<p><strong>Note&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Component&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Priority</strong></p>\r\n<p>2800701&#160; &#160; &#160;BC-CUS-TOL-BCD&#160; &#160; &#160;3</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041268)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019437)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002349002/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349002/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2447538", "RefComponent": "CA-MDG-AF-HP", "RefTitle": "Recommendation for MDG Hierarchy with SAP Master Data Governance", "RefUrl": "/notes/2447538"}, {"RefNumber": "2392135", "RefComponent": "CA-MDG", "RefTitle": "SAP Master Data Governance on SAP S/4HANA (central governance) in combination with certain business functions for industries", "RefUrl": "/notes/2392135"}, {"RefNumber": "2327935", "RefComponent": "CA-UI5-COR", "RefTitle": "General Information: FIORI UI Infrastructure Components  for products on SAP Frontend Server 2.0", "RefUrl": "/notes/2327935"}, {"RefNumber": "2313368", "RefComponent": "CA-MDG-APP-CUS", "RefTitle": "Functional restrictions in MDG for Business Partner / Customer / Supplier with SAP Master Data Governance 9.0", "RefUrl": "/notes/2313368"}, {"RefNumber": "2284745", "RefComponent": "CA-MDG-APP-MM", "RefTitle": "Functional Restrictions in MDG for Material with SAP Master Data Governance 9.0", "RefUrl": "/notes/2284745"}, {"RefNumber": "2230429", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UIMDC001 100  from the Product Version SAP Fiori 1.0 for SAP Master Data Governance", "RefUrl": "/notes/2230429"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2568323", "RefComponent": "XX-PART-UGI", "RefTitle": "SAP Master Data Governance, enterprise asset management extension by Utopia 9.1 for S/4HANA Release Information Note", "RefUrl": "/notes/2568323 "}, {"RefNumber": "1685823", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for SAP MASTER DATA GOVERNANCE", "RefUrl": "/notes/1685823 "}, {"RefNumber": "2518699", "RefComponent": "XX-PART-UGI", "RefTitle": "SAP Asset Information Workbench by Utopia for S/4HANA 1.0 Release Information Note", "RefUrl": "/notes/2518699 "}, {"RefNumber": "2410441", "RefComponent": "XX-PART-UGI", "RefTitle": "SAP Master Data Governance, enterprise asset management extension by Utopia 7.3 for S/4HANA Release Information Note", "RefUrl": "/notes/2410441 "}, {"RefNumber": "2392135", "RefComponent": "CA-MDG", "RefTitle": "SAP Master Data Governance on SAP S/4HANA (central governance) in combination with certain business functions for industries", "RefUrl": "/notes/2392135 "}, {"RefNumber": "2333141", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1610: Restriction Note", "RefUrl": "/notes/2333141 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "MDG_FND", "From": "801", "To": "801", "Subsequent": ""}, {"SoftwareComponent": "MDG_APPL", "From": "801", "To": "801", "Subsequent": ""}, {"SoftwareComponent": "MDG_UX", "From": "801", "To": "801", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}