{"Request": {"Number": "46564", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 318, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014438672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=3B9CDA6115BFD3B25D0F49404B74ADB9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "46564"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Invoice Verification"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Invoice Verification", "value": "MM-IV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "46564 - Reversing an invoice/credit memo: Posting logic"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>When you reverse an invoice, the system posts a credit memo for the invoice data. The postings are made according to the posting logic for credit memos.<br />This means that the postings made in the invoice are not necessarily reversed. This applies in particular if an invoice is to be reversed for an ordering transaction for which several invoices have already been posted.<br />Similarly, when you reverse a credit memo, the system does not necessarily reverse the postings made in the credit memo.<br /><br />The following examples show two cases in which the postings that are made when the invoice is reversed are different to those that are made when the invoice is posted.<br /><br />Example 1:<br /><br />Purchase order: 100 pieces at USD 10/pc<br />Goods receipt: 50 pieces<br />Invoice 1:&#x00A0;&#x00A0;50 pieces at USD 10 = USD 500<br />Invoice 2:&#x00A0;&#x00A0;50 pieces at USD 11 = USD 550<br />Reversal for invoice 1<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IR1&#x00A0;&#x00A0;&#x00A0;&#x00A0;IR2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reversal for IR1<br />Stock account&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;500 +<br />GR/IR clearing account&#x00A0;&#x00A0;&#x00A0;&#x00A0;500 -&#x00A0;&#x00A0;500 +&#x00A0;&#x00A0; 550 +&#x00A0;&#x00A0;&#x00A0;&#x00A0; 550 -<br />Vendor account&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;500 -&#x00A0;&#x00A0; 550 -&#x00A0;&#x00A0;&#x00A0;&#x00A0; 500 +<br />Price difference account&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50 +<br /><br />Example 2:<br /><br />Purchase order: 100 pieces at USD 10/pc<br />Invoice 1:&#x00A0;&#x00A0;10 pieces at USD 10 = USD 100<br />Invoice 2:&#x00A0;&#x00A0;10 pieces at USD 20 = USD 200<br />Reversal/credit memo for invoice 2<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IR1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IR2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reversal for IR2<br />Stock account<br />GR/IR clearing account&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100 +&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200 +&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 150 -<br />Vendor account&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100 -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200 -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 200 +<br />Price difference account&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50 -<br /><br /><br /><br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MRHG, MR08, MR8M, MR11SHOW, WRX</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This is not an error.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D001423"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031610)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "783345", "RefComponent": "MM-IV-CA", "RefTitle": "MR11SHOW: Posting logic", "RefUrl": "/notes/783345"}, {"RefNumber": "740877", "RefComponent": "MM-IV", "RefTitle": "Cancellation of obsolete documents", "RefUrl": "/notes/740877"}, {"RefNumber": "509162", "RefComponent": "CO-OM-CEL-E", "RefTitle": "Budget overrun for invoice receipt cancellation", "RefUrl": "/notes/509162"}, {"RefNumber": "308008", "RefComponent": "MM-IV", "RefTitle": "FAQ: Posting logic: GR/IR clearing account", "RefUrl": "/notes/308008"}, {"RefNumber": "1174830", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "FAQ: Invoice cancellation for services", "RefUrl": "/notes/1174830"}, {"RefNumber": "116250", "RefComponent": "MM-IV", "RefTitle": "MR08/MR8M:Cancellation+exchange rate diffrnces(KDM)", "RefUrl": "/notes/116250"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3302259", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "MR8M: Error message FF769 - SAP ERP & SAP S/4HANA", "RefUrl": "/notes/3302259 "}, {"RefNumber": "1684542", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "MR8M: Common errors - SAP ERP & SAP S/4HANA", "RefUrl": "/notes/1684542 "}, {"RefNumber": "2714115", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "MR8M: Cancel Invoice Document with Invoice Reduction", "RefUrl": "/notes/2714115 "}, {"RefNumber": "2684816", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "Posting logic for the reversal of an Invoice - SAP ERP & S/4HANA", "RefUrl": "/notes/2684816 "}, {"RefNumber": "2678256", "RefComponent": "FI-GL-GL-F", "RefTitle": "MIRO - Tax entry not possible in this item (FF724)", "RefUrl": "/notes/2678256 "}, {"RefNumber": "2654403", "RefComponent": "MM-IV-INT-TAX", "RefTitle": "MR8M - Tax entry not possible in this item (FF724)", "RefUrl": "/notes/2654403 "}, {"RefNumber": "2463279", "RefComponent": "MM-IV-INT-TAX", "RefTitle": "General Tax determination in ERP MM – Guided Answers", "RefUrl": "/notes/2463279 "}, {"RefNumber": "1174830", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "FAQ: Invoice cancellation for services", "RefUrl": "/notes/1174830 "}, {"RefNumber": "792907", "RefComponent": "MM-IV-LIV", "RefTitle": "Posting logic mmiv", "RefUrl": "/notes/792907 "}, {"RefNumber": "783345", "RefComponent": "MM-IV-CA", "RefTitle": "MR11SHOW: Posting logic", "RefUrl": "/notes/783345 "}, {"RefNumber": "308008", "RefComponent": "MM-IV", "RefTitle": "FAQ: Posting logic: GR/IR clearing account", "RefUrl": "/notes/308008 "}, {"RefNumber": "740877", "RefComponent": "MM-IV", "RefTitle": "Cancellation of obsolete documents", "RefUrl": "/notes/740877 "}, {"RefNumber": "509162", "RefComponent": "CO-OM-CEL-E", "RefTitle": "Budget overrun for invoice receipt cancellation", "RefUrl": "/notes/509162 "}, {"RefNumber": "116250", "RefComponent": "MM-IV", "RefTitle": "MR08/MR8M:Cancellation+exchange rate diffrnces(KDM)", "RefUrl": "/notes/116250 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "22A", "To": "22J", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}