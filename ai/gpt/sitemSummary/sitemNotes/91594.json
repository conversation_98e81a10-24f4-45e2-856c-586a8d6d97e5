{"Request": {"Number": "91594", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 370, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014538002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=1C94A8328A215EF37447929112F8A190"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "91594"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.11.2001"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "91594 - Additional Info: Upgrading to Release 3.1I"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Errors in the guide or in the upgrade procedure.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Update, migration, release upgrade, release, maintenance level</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>*</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Caution: This note is only maintained by the software logistics<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; department!! This applies to both the German and English<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; versions. Do not enter anything in the note but send<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;change requests by mail to the user D022030.<br /><br /><br />*******************************************************************<br />Also read the upgrade notes which cover database-specific problems (see related notes as well):<br />&#x00A0;&#x00A0;ADABAS D&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100903<br />&#x00A0;&#x00A0;DB2/400&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 102950<br />&#x00A0;&#x00A0;DB2/390&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 104018<br />&#x00A0;&#x00A0;DB2/CS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;101139<br />&#x00A0;&#x00A0;INFORMIX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100991<br />&#x00A0;&#x00A0;MS SQL Server&#x00A0;&#x00A0;98026<br />&#x00A0;&#x00A0;ORACLE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;102208<br /><br />Also read the following notes:<br />&#x00A0;&#x00A0;101220 Current note on the language import 3.1I<br /><br />*******************************************************************<br /><br /><B>Caution: This note is updated frequently! You should</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;therefore read it again immediately before the</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; upgrade.</B><br /><br /><br />&#x00A0;&#x00A0; Changes listed by date/time (one line is inserted for each change)<br />&#x00A0;&#x00A0; ==================================================================<br /><br />Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Topic Short Description<br />----------------------------------------------------------------------<br />27/NOV/01 II/&#x00A0;&#x00A0; Import R3trans patch<br />18/APR/00 III/&#x00A0;&#x00A0;For HR customers<br />20/DEZ/99 VII/&#x00A0;&#x00A0;Warning/termination in PCON_31I for EWUPAK<br />15/DEC/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;HR+ LCP 8, 9 and 10: Avoid termination in ACT_31I<br />24/NOV/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;HR+ LCP 8, 9 or 10: Avoid termination in TABIM_31I<br />09/NOV/98 II/&#x00A0;&#x00A0;For HR customers on 2.2x: Avoiding langer runtimes<br />09/NOV/98 II/&#x00A0;&#x00A0;HR customers with LCPs HR: Read Notes 73510<br />05/AUG/98 VII/&#x00A0;&#x00A0;ACT_31I: table DBSTATC is created by sapdba<br />20/JUL/98 VI/&#x00A0;&#x00A0;Syntax error in SPDD and SPAU after tool import<br />14/JUL/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Backup of Legacy System Migration Workbench<br />14/JUL/98 III/&#x00A0;&#x00A0;SAPDBA terminates during export of tables<br />24/JUN/98 VII/&#x00A0;&#x00A0;ACT_31I: Upgrade stops with error AD458<br />16/JUN/98 VII/&#x00A0;&#x00A0;XPRAS_31I: Ignore error OL555<br />16/JUN/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Backup of R/2-R/3 migration tools<br />15/JUN/98 II/&#x00A0;&#x00A0;Upgrade documentation for 3.1I also on CD<br />04/JUN/98 III/&#x00A0;&#x00A0;FCS Upgrade: Unpack UNIX Frontend differently<br />20/MAY/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;ReliantUNIX:/install/os_version/ReliantUNIX=SINIX<br />19/MAY/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Checks of table T510Q before the upgrade<br />19/MAY/98 VIII/ Solaris 2.5/2.51: Follow Note 69159<br />15/MAY/98 III/&#x00A0;&#x00A0;3.1 FCS (3.10) not possible as source release<br />07/MAY/98 VIII/ Accounting: Actions from Note 16762 .....<br />07/MAY/98 VIII/ For serial numbers: follow Note 52994<br />07/MAY/98 VIII/ Exchanging or reinstalling SAPcomm software<br />07/MAY/98 VIII/ Reimporting lost Ber.profil docu.<br />07/MAY/98 VIII/ ReliantUNIX: HRTIME/HRVTIME at least 1000<br />07/MAY/98 VIII/ Comparison of Hot Packages and upgrade<br />07/MAY/98 VII/&#x00A0;&#x00A0;Phase JOB_GECAD_31I: SAPSQL_ARRAY_INSERT_DUPREC<br />07/MAY/98 VII/&#x00A0;&#x00A0;PCON_31I: Problems when converting the BSEG<br />07/MAY/98 VII/&#x00A0;&#x00A0;XPRAS_31I: Error GU093 in the XPRA RGZZGLUX<br />07/MAY/98 VII/&#x00A0;&#x00A0;XPRAS_31I: Error GU093 in the XPRA RGUXI30A<br />07/MAY/98 VII/&#x00A0;&#x00A0;Phase PCON_31I: SQL error 1024<br />07/MAY/98 VII/&#x00A0;&#x00A0;ACT_31I/SPDD: Transports QE1K900002, TCVK900029<br />07/MAY/98 VII/&#x00A0;&#x00A0;Termination in the phase ACT_31I or PCON_31I<br />07/MAY/98 VII/&#x00A0;&#x00A0;Only for NT: Work processes die in STARTR3_NBAS<br />07/MAY/98 VII/&#x00A0;&#x00A0;Phase RSVTR_TRANS: do not use during termination<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SE14<br />07/MAY/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;FI-SL: note field grouping codes + contents<br />07/MAY/98 VIII/ FI-SL: Maintain field movements<br />07/MAY/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Only for FI-SL customers: Note 40124<br />07/MAY/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Only when starting with 2.2: Check indexes of the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;E071K<br />07/MAY/98 V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;MM - Inventory Management: Customizing settings<br />07/MAY/98 II/&#x00A0;&#x00A0; BSEG: in the SPDD NOT back to the standard<br />23/DEC/97 I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3up key word<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The topics are<br /></STRONG><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3up key word<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;II/&#x00A0;&#x00A0; Important general information<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;III/&#x00A0;&#x00A0;Correction of the guides<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;IV/&#x00A0;&#x00A0; Errors on the CD-ROM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Checks before the upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;VI/&#x00A0;&#x00A0; Problems with Prepare<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;Problems in the individual upgrade phases<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; VIII/ Problems after the upgrade<br /><br /><br /><br /><br /><STRONG>I/ R3up key word<br /><br /></STRONG><br />------------------------&lt; D019132 23/DEC/97&gt;-------------------------<br />* R3up key word (enter during the first call of R3up): 130008<br />----------------------------------------------------------------------<br /><br /><br /><STRONG>II/ Important general information<br /></STRONG><br />------------------------&lt; D025323 27/NOV/01 &gt;-------------------------<br /><B>Import R3trans patch</B><br />To prevent the loss of join conditions during the upgrade, you have to replace the R3trans tool in the &lt;DIR_PUT&gt;/exe directory after PREPARE and before starting the upgrade.<br />For more information see <B>Note 444045</B>.<br /><br />------------------------&lt; D020781 09/NOV/98 &gt;-------------------------<br /><B>For HR customers only:</B></p> <UL><LI>When your source release contains Legal Change Patches HR (LCPs HR):<br /><br />In this case, an upgrade on 3.1I is only possible when the corresponding LCPs are also available in Release 3.1I.<br /><br />With source release 3.1H, you are explicitly warned from R3up (phase PATCH_CHK) if you need to include LCPs.<br />This warning does not appear with source release 3.0F. You must however always include the corresponding LCPs for 3.1I.<br />Informationen about corresponding LCPs is in <B>Note 73510.</B><br /><br />In order to avoid unnecessarily long runtimes in the upgrade phase XPRAS_31I we recommend that you suppress the generation of the included LCPs. Use the stop for the SPDD or as an alternative stop the upgrade before the phase ACT_31I (Note 48184, notice that the SPDD still does not work at this point).<br />For each included LCP, state the following commands from the subdirectory \"bin\" of the upgrade directory:<br /> tp&#x00A0;&#x00A0;modbuffer&#x00A0;&#x00A0;SAPKE31Inn&#x00A0;&#x00A0;&lt;SID&gt;&#x00A0;&#x00A0;mode=G0<BR/> tp&#x00A0;&#x00A0;modbuffer&#x00A0;&#x00A0;SAPKE31Inn&#x00A0;&#x00A0;mode=G0&#x00A0;&#x00A0;buffer=ACT31I.BUF<br />With that, nn stands for the number of the included LCP (01, 02, ...). &lt;SID&gt; stands for the system name. The generation of the program is suppressed by this manipulation.</LI></UL> <UL><LI>If your source release contains no Legal Change Patches HR (LCPs HR):<br /><br />In this case as well, R3up in the upgrade phase BIND_PATCH asks whether you want to include LCPs for the target release.<br />Ignore this question.<br /><br /></LI></UL> <p>------------------------&lt; D000212 09/NOV/98 &gt;------------------------<br />Only for <B>HR customers with source release 2.2x:</B><br /><br />Problem:<br />The program RPU30000 has <B>high runtimes</B> for the conversion of the HR master data of the table PREL in transparent tables.<br /><br />Solution:<br />You find a description in the <B>Note 53328 </B>, as to how you shift this program in the follow-up of the upgrade and with that, how you can shorten the downtime.<br />Obtain this note before the upgrade since you must carry out activities before starting the R3up.<br /><br />------------------------&lt; D019144 15/JUN/98 &gt;-------------------------<br /><B>3.1I Upgrade Documentation also on CD </B><br /><br />Title of CD:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REPORT LOAD (NUMBER R31I0013)<br />Directory&#x00A0;&#x00A0;:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOCU<br />Format&#x00A0;&#x00A0;&#x00A0;&#x00A0;:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PDF<br />More info&#x00A0;&#x00A0;:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\\DOCU\\README.TXT<br />Platforms&#x00A0;&#x00A0;:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Documentation for AS/400, Windows NT and UNIX<br /><br /><br />You can find the documentation for the installation of the PC SAPgui and the language transport on the CD REPORT LOAD and also on the following CDs:<br /><br />Installation of the PC SAPgui on the CD PRESENTATION (NUMBER R31I0001)<br />Language transport documentation on the CD LANGUAGE DISC (NUMBER L31I0012)<br /><br /><br />------------------------&lt; D019924 07/MAY/98&gt;-------------------------<br />If table BSEG was modified<br /><br />Problem: Do not return to the standard SAP System for table BSEG.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; The resulting conversion of table<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * causes a termination in phase PCON_31I in the case of<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the majority of DB systems.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;* is runtime-intensive for the other DB systems<br /><br />Solution:<B>Accept the default of the SPDD </B>for table BSEG<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; in phase ACT_31I.<br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><STRONG>III/ Correction of the guides<br /><br /><br />------------------------&lt; D001330 18/APR/00 &gt;-------------------------<br /><B>Only for HR customers.</B><br /><br />Concerning the guides<br />'Upgrading to Release 3.1I Windows NT' page 4-13<br />and<br />'Upgrading to Release 3.1I Unix'&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Page 4-14<br />Phase BIND_PATCH.<br /><br />Read the note text there as follows:<br /><br />\"You can only include LCPs if your source release already contains LCPs (3.0F and 3.1H) and if the system was set up for the import of LCPs. For information on how to set up LCPs, refer to Note 89089.<br />Ignore the question for patches to be included in this phase only if you do not have any LCPs in your source release (that is, your source release is not 3.0F or 3.1H).\"<br /><br />Also refer to the chapter II \"only for HR customers\".<br /><br /></STRONG><br />------------------------&lt; D020432 14/JUL/98 &gt;-------------------------<br /><B>Oracle Upgrade from 7.1 or 7.2 to 7.3</B><br /><br />In the guide 'Upgrade to Release 3.1I: Windows NT', attachment E, the<br />following steps must be added:<br />For source version Oracle 7.1: Page E-8, step 18<br />For source version Oracle 7.2: Page E-18, step 16<br /><br />To make the export of database tables possible, script CATEXP.SQL must be executed in addition to the specified scripts. Otherwise SAPDBA terminates with error messages ORA-0904 and ORA-1003 during the attempt to export a table.<br />The script can be found in directory<br />&lt;drive&gt;:\\ORANT\\RDBMS73\\ADMIN\\CATEXP.SQL<br />and must be executed as user internal in the server manager.<br />To do this, enter the following command:<br /><br />svrmgr23<br />svrmgr&gt; connect internal/&lt;password&gt;<br />svrmgr&gt; @:\\ORANT\\RDBMS73\\ADMIN\\CATEXP.SQL<br />svrmgr&gt; exit<br /><br />After executing the script, before the R/3 upgrade, check whether the export of tables is possible.<br /><br /><br />------------------------&lt; D023648 04/JUN/98 &gt;-------------------------<br />Only for the <B>FCS upgrade</B> to <B> UNIX </B>:<br /><br />On the CD presentation there is an R3up in Version 3.1H by mistake. If you directly call this 3.1H R3up from the CD presentation, as described in the upgrade guide, Chapter 3, section: <B>Upgrading Frontend Software</B>, the 3.1H R3up reports the following error:<br /> &#x00A0;&#x00A0; SEVERE ERROR: Unknown SAP Release \"31I\"<BR/> &#x00A0;&#x00A0; Most likely you called the wrong R3up.<BR/> &#x00A0;&#x00A0; This is R3up version 3.1H.<br />This problem can be corrected by using the 3.1I R3up from the upgrade directory. The call is then as follows:<br /><br />&#x00A0;&#x00A0; /usr/sap/put/bin/R3up readunixgui<br /><br /><br />------------------------&lt; D019144 15/MAY/98 &gt;-------------------------<br />Only for source release 3.1 FCS (3.10):<br />The upgrade to 3.1I is not supported for this source release.<br /><br />Correction in the 3.1I upgrade guide for AS/400, Windows NT and UNIX necessary:<br /><br /><B>Forget 3.1 FCS (3.10) as a possible source release!</B><br /><br />in Chapter 2, Section&#x00A0;&#x00A0; \"Software Requirements\"<br /><br />----------------------------------------------------------------------<br /><br /><STRONG>IV/ Errors on the CD-ROM<br /><br /></STRONG><br />----------------------------------------------------------------------<br /><br /><STRONG>V/ Checks before the upgrade<br /><br /></STRONG><br />---------changed--------&lt; D022562 15/DEC/98 &gt;------------------------<br />------------------------&lt; D021879 24/NOV/98 &gt;------------------------<br />Only for <B>HR customers who want to include LCPs up to LCP 8, 9 or 10 in the target release</B>.<br /></p> <OL>1. Problem:<br />The inclusion of the LCPs up to LCP 8, 9, or 10 in the upgrade leads to a <B>termination in the phase TABIM_31I</B>.<br />Solution to the first problem:<br />To avoid the termination, proceed as is described in <B>Note 127339</B>.<br /></OL> <OL>2. Problem:<br />The upgrade on 3.1I <B>terminates in the phase ACT_31I</B> if you jointly include LCP08, LCP09 and LCP10.<br />Solution to the second problem:<br />To avoid the termination, proceed as is described in <B>Note 130446 </B>.<br /></OL> <p><STRONG></STRONG><br />----------changed-------&lt; D021867 22/JUL/98 &gt;------------------------<br />----------changed-------&lt; D021943 14/JUL/98 &gt;------------------------<br />------------------------&lt; D021867 16/JUN/98 &gt;------------------------<br />Only if you have installed the <B>R/2-R/3 migration tools</B> and/or<br />the <B>Legacy System Migration Workbench</B>:<br /><br />Problem: 1. When upgrading to 3.1I, components of the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R/2-R/3 migration tools and/or the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Legacy System Migration Workbench are deleted.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2. The upgrade stops in phase ACT_31I because<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;data element MG_KTYPE and table TUMDO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;cannot be activated.<br /><br />Solution: 1. You must save the R/2-R/3 migration tools or the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Legacy System Migration Workbench before the upgrade.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; For more information see <B>Note 106799</B>.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2. You can ignore the error message in phase ACT_31I.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The symptom is described in more detail further down<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in this note (under \"Problems in the individual upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; phases\", phase ACT_31I).<br /><br />------------------------&lt; D024648 22/MAY/98 &gt;------------------------<br />Only for <B>ReliantUNIX:</B><br /><br />Before the upgrade make sure that the instance profiles of all R/3 instances contain the following lines:<br /><br />&#x00A0;&#x00A0; /install/os_version/ReliantUNIX = SINIX<br /><br />More information is contained in <B>Note 39745. </B><br /><br /><br />------------------------&lt; D024094 07/MAY/98 &gt;-------------------------<br />Only for MM customers (Inventory Management):<br />Only for source release 3.0x/3.1x:<br /><br />Problem:<br />Certain Customizing settings of movement types are lost during the upgrade. The settings are reset to the standard SAP settings.<br /><br />Solution: in Note 86627.<br /><br /><br />------------------------&lt; C5003135 07/MAY/98&gt;------------------------<br />Only for source release 2.2x<br /><br />Problem: In Release 2.2, table E071K was delivered without<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; technical settings. As a result, an overflow may occur<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; during the upgrade for the indexes E071K__0 and E071K__1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and this may cause a termination of the upgrade in phase<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DDIC_IMPORT.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; In the case of the ORACLE database, for example, error<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;message ORA-1632 would be displayed.<br /><br />Solution:Make sure that the indexes can increase during the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;upgrade to the following values:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Index E071K__0 by 15 MB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Index E071K__1 by 19 MB<br /><br /><br />------------------------&lt; D000700 07/MAY/98&gt;-------------------------<br />Only for FI-SL customers:<br /><br />You should obtain the following note before the upgrade and carry out the described actions described BEFORE the upgrade:<br /><br /> <B>Note 40124:</B> FI-SL: User exit programs in the action<br />&#x00A0;&#x00A0;described in the SAP namespace must be carried out BEFORE<br />&#x00A0;&#x00A0; the upgrade, otherwise programs may be lost.<br /><br /><br />------------------------&lt; D016371 07/MAY/98&gt;-------------------------<br />Only for source release 2.2<br />Only for FI-SL customers, who have defined additional account assignments for the coding block in Release 2.2 according to Note 22204.<br /><br />Short text: <B>Note Field grouping codes</B>(and their contents) before the <B>upgrade! </B><br /><br />Symptom: The above-mentioned additional account assignments are not &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; updated in the FI-SL (=special ledger, previously GLX = &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;extended general ledger) after the upgrade.<br /><br />Solution: To avoid <B>data loss</B>, proceed according to<br /> <B>Note 47482 </B> BEFORE the upgrade.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Maintain the data AFTER the upgrade.<br /><br />-------------------------&lt;D001634 19/MAY/98&gt;--------------------------<br />Only for source releases smaller than 3.1H:<br /><br />Problem: In field T510Q-BETRG, table T510Q may contain non-numerical<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;entries in clients that are not '000'. These would cause<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; a <B>termination of the upgrade in phase PCON_31I </B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; during the table conversion. The following runtime error<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; would appear: CONVT_NO_NUMBER.<br /><br />Solution: Before the upgrade obtain <B>Note 83728.</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Follow the instructions there to avoid the termination<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in the phase PCON_31I<br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><STRONG>VI/ Problems with Prepare<br /><br />-------------------------&lt;D019989 20/JUL/98&gt;--------------------------</STRONG><br />Syntax error in Transactions SPDD and SPAU<br /><br />Symptom: Transactions SPDD and SPAU terminate with a syntax error in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;program RSUMOTOP: 'Type pool \"TRWBO\" unknown' after the tool<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; import in PREPARE.<br /><br />Solution: is contained in <B>Note 110443 </B><br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><STRONG>VII/ Problems in the individual upgrade phases<br /><br /></STRONG><br />-------------------------&lt;D019141 12/NOV/97&gt;--------------------------<br />Phase <B>RSVTR_TRANS</B><br /><br />In the case of a termination in phase RSVTR_TRANS, the database utility (SE14, programs 'RADBAT01' and 'RDDGENBB') must not be used!<br /><br />Further information is contained in <B>Note 74142</B>.<br /><br /><br />------------------------&lt; D020847 07/MAY/98 &gt;-------------------------<br />Only for Windows NT:<br />Phase STARTR3_NBAS<br /><br />Symptom: When the R/3 kernel is first started after the kernel<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; swap, all work processes die. The following entry is in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the developer trace \\usr\\sap\\&lt;SID&gt;\\&lt;instance&gt;\\work\\dev_w0:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ERROR =&gt; CreateFileMapping(5,4224) failed with Err=5<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Access is denied.<br /><br />Solution: described in <B>Note 77384</B>.<br /><br /><br />---------changed--------&lt; D021867 22/JUL/98 &gt;------------------------<br />------------------------&lt; D021867 24/JUN/98 &gt;-------------------------<br />Phase <B>ACT_31I </B><br /><br />Symptom: The upgrade stops since the data element MG_KTYPE and table<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TUMDO cannot be activated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; The following error messages can be found in the log<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ACT31I.ELG in the upgrade directory:<br /> EEAD458 \"R3TR\" \"DTEL\" \"MG_KTYPE\" was not successfully activated (\"E-\")<BR/> EEAD458 \"R3TR\" \"TABL\" \"TUMDO\" was not successfully activated (\"E-DT005\")<br />Cause: The R/2-R/3 migration tools and/or the Legacy System<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Migration Workbench are installed (see also entry futher above<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; in this note under \"Checks before the upgrade\").<br /><br /><br />Solution: Ignore these error messages.<br /><br /><br />------------------------&lt; D021848 07/MAY/98 &gt;-------------------------<br />Phase ACT_31I <br />In addition, these problems can occur in phase <B>PCON_31I</B><br /></p> <OL>3. Symptom:<br />The work processes terminate with signal 11 and R3up does not record this. Signal 11 can be recognized in the system log.<br />There are two ways of determining which structure causes the termination:<br />* via the Select command, as described in Note 29159.<br />* find the command file that was last edited. In this, you must<br />&#x00A0;&#x00A0;find the last structure that was activated successfully.<br />&#x00A0;&#x00A0;The structure following it caused the termination.<br /></OL> <OL>4. Symptom:<br />The phase ends with return code 12, termination in the RDDMASGL:<br />1AETR012X Program terminated. See long text (job: \"RDDMASGL\", no:<br />\"&lt;Number of the cancelled job&gt;\").<br />Exit code 12<br />Here you see the command file directly. In this, you find the structure which caused the termination.<br /><br />For both symptoms, the structures of the application CO (operating concern) cause the termination, for example, CE7S001.<br /><br /><br />Solution: Proceed as described in Note 29159.<br /><br /><br />------------------------&lt; D019132 05/AUG/98 &gt;-------------------------<br />Phase <B>ACT_31I</B><br /><br />Symptom: Table DBSTATC exists on the database with an incorrect<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;structure. The error is only recognized later by the upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; process in <B>TABIM_31I</B> but can be prevented at the time<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;of the SPDD adjustment.<br /><br />Solution: Use Transaction SE14 to check the consistency of the database<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;table (16 fields must exist). If 3 fields are missing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;select the option \"Delete data\" and execute the function<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Activate and adapt database\".<br /><br /></OL> <p>------------------------&lt; D022085 07/MAY/98 &gt;-------------------------<br />Phase <B>ACT_31I </B><br />It can only occur with source release 3.0C, 3.0D or 3.0E:<br /><br />Symptom: In the SPDD, the transport QE1K900002 or TCVK900029<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;is displayed by mistake as a customer modification.<br /><br />Solution:&#x00A0;&#x00A0;described in <B>Note 74831</B>.<br /><br /><br />-----------------------&lt; D028953 20/DEZ/99 &gt;--------------------------<br />Phase <B>PCON_31I </B><br /><br />Problem: There may be warnings or terminations due to table EWUPAK<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (loss of customer data)<br /><br />Solution: Read <B>Note 190527 </B> for this<br /><br /><br />------------------------&lt; D021798 07/MAY/98 &gt;-------------------------<br />Phase <B>PCON_31I </B><br /><br />Problem: For table VBDATA or other tables the following SQL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; error occurs:<br /><br /> <B>SQL error 1024 </B>(database object exists)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; The error is caused by that fact that the table in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; question already exists as a database table although this<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;should not be the case according to meta information in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R/3 System.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Up to now, it has not been possible to determine how the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;database table was created.<br /><br />Solution:&#x00A0;&#x00A0;Proceed as described in <B>Note 40319 </B>.<br /><br /><br />------------------------&lt; D019141 07/MAY/98&gt;-------------------------<br />Phase <B>PCON_31I </B><br /><br />Symptom: <B>Problems when converting table BSEG.</B><br /><br />Solution:&#x00A0;&#x00A0; 1. Proceed as described in <B>Note 24864</B>.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2. Repeat the phase PCON_31I with repeat.<br /><br /><br />------------------------&lt; C1016615 16/JUN/98 &gt;------------------------<br />Phase <B>XPRAS_31I</B><br /><br />Symptom: The upgrade stops. Error message OL555 is issued<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (in the respective language):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OL555 'Object type '\"BUS1032\"' could not be generated'<br /><br />Solution: you can ignore the message. To continue, start the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;upgrade again in this phase with \"Repair Severe Errors\".<br /><br /><br />------------------------&lt; D000700 07/MAY/98&gt;-------------------------<br />Phase <B>XPRAS_31I</B><br /><br />Symptom:&#x00A0;&#x00A0;Error GU093 in the XPRA RGZZGLUX<br />Solution:&#x00A0;&#x00A0;Proceed according to <B>Note 40153</B>, otherwise<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;update terminations can occur in the case of postings<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; in accounting.<br /><br /><br />------------------------&lt; D000700 07/MAY/98&gt;-------------------------<br />Phase <B>XPRAS_31I</B><br />Only for FI-SL customers<br /><br />Symptom:&#x00A0;&#x00A0;Error GU093 in the XPRA RGUXI30A<br />Solution: Proceed according to <B>Note 40161</B>, otherwise,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;an incorrect data update may occur after the upgrade.<br /><br /><br />------------------------&lt; D019955 07/MAY/98 &gt;-------------------------<br />Phase <B> JOB_GECAD_31I </B><br /><br />Symptom:&#x00A0;&#x00A0;ABAP/4 runtime error SAPSQL_ARRAY_INSERT_DUPREC in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;program RSGESCAD.<br />Solution: Proceed as described in <B>Note 73804 </B>.<br /><br /><br />----------------------------------------------------------------------<br /><br /><STRONG>VIII/ Problems after the upgrade<br /><br /></STRONG><br />------------------------&lt; D019989 07/MAY/98 &gt;-------------------------<br />Only if the modification adjustment is made for the R/3 upgrade and Hot Packages together, with the goal of automatic comparison in later systems:<br /><br />In the initial system proceed as follows:</p> <OL>1. Ignore the request in Transaction SPAM for a modification adjustment.</OL> <OL>2. Then compare the objects from the upgrade and from the Hot Packages with Transaction SPAU.</OL> <OL>3. Choose the function \"Select transport\" in SPAU.<br /><br />In the later system you must proceed according to <B>Note 69990</B>.<br />Otherwise, the objects contained in the Hot Packages that are already compared are overwritten in the later system!<br /></OL> <p>------------------------&lt; D021250 19/MAY/98 &gt;-------------------------<br />Only for Solaris 2.5/2.51:<br /><br />After the R/3 upgrade proceed as described in <B>Note 69159</B>.<br /><br /><br />------------------------ &lt; C1016615 07/MAY/98&gt;------------------------<br /><B>Only for ReliantUNIX:</B><br /><br />Check the following kernel parameters for ReliantUNIX after the R/3 upgrade:<br />* HRTIME<br />* HRVTIME<br />Both parameters must have a minimum value of 1000!<br /><br /><br />------------------------&lt; D019955 07/MAY/98&gt;-------------------------<br />Only for source release 2.2:<br /><br />Symptom: After the upgrade customer-specific documentation on<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;authorization profiles (documentation ID UP) is missing.<br />Solution:&#x00A0;&#x00A0;described in <B>Note 76518</B>.<br /><br /><br /><br />-------------------------&lt;D021361 07/MAY/98&gt;--------------------------<br />For customers who want to exchange the SAPcomm software under UNIX or reinstall it:<br /><br />You find the SAPcomm software on the \"Presentation\" CD in the following directory:<br /> &#x00A0;&#x00A0; /&lt;CD-Dir&gt;/GUI/UNIX/&lt;OS&gt;/SAPCOMM.CAR<br />where you have to replace  with&#x00A0;&#x00A0;AIX, DEC, HPUX, RELIANT or SOLARIS.<br /><br />For the installation proceed in the same way as described in <B>Note 19584</B>.<br /><br />Unpack the software into a temporary directory beforehand with:</p> <OL>1. cd &lt;temporary directory&gt;</OL> <OL>2. /&lt;CD-Dir&gt;/GUI/UNIX//CAR&#x00A0;&#x00A0;-xvf &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/&lt;CD-Dir&gt;/GUI/UNIX/&lt;OS&gt;/SAPCOMM.CAR<br />Then copy the executables (executable files without extension), the SAPcomm grammar files ('*.grm'), the configuration examples ('*.cfg' and 'sapcomm.pfl'), as well as the SAPcomm-API files ('scmi.h','scmiux.o','scmitst.c') as described in Note 19584.<br /><br /></OL> <p>------------------------&lt; D016371 07/MAY/98&gt;-------------------------<br />Only for source release 2.2<br />Only for FI-SL customers who defined additional account assignments for the coding block in Release 2.1 / 2.2 according to Note 22204.<br /><br />Short text: <B>maintain field grouping codes</B>(and their<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;contents) <B>after the upgrade! </B><br /><br />If you noted field grouping codes before the upgrade, now maintain these according to <B>Note 47482</B>.<br /><br /><br />------------------------ &lt; D015998 07/MAY/98 &gt;-------------------------<br />Only for customers who have used deliveries as of Release 2.2 with serial numbers (logistics):<br /><br />Execute a report directly after the upgrade.<br />You find the guidelines in <B>Note 52994</B>.<br /><br />Otherwise, the display for the history of a serial number does not work correctly.<br /><br /><br />------------------------&lt; D000700 07/MAY/98 &gt;-------------------------<br />For all customers who are using an accounting component:<br /><br />After the upgrade, carry out the steps described in <B>Note 16762</B><br />\"GLX: Number ranges for object numbers and record numbers\".<br />Otherwise, update terminations can occur in the case of postings in accounting.<br /><br />----------------------------------------------------------------------<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022030)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98026", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I    MSSQL Server", "RefUrl": "/notes/98026"}, {"RefNumber": "86627", "RefComponent": "MM-IM", "RefTitle": "Movement types: Customizing during release upgrade", "RefUrl": "/notes/86627"}, {"RefNumber": "83728", "RefComponent": "PY-DE", "RefTitle": "Check table T510Q before the upgrade", "RefUrl": "/notes/83728"}, {"RefNumber": "77384", "RefComponent": "BC-UPG", "RefTitle": "Phase STARTR3_NBAS: Access is denied", "RefUrl": "/notes/77384"}, {"RefNumber": "76518", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/76518"}, {"RefNumber": "74831", "RefComponent": "BC-CCM-MON", "RefTitle": "SPDD:Transports QE1K900002, TCVK900029, EWSK900052", "RefUrl": "/notes/74831"}, {"RefNumber": "74142", "RefComponent": "BC-DWB-DIC", "RefTitle": "Problem in the upgrade phase RSVTR_TRANS", "RefUrl": "/notes/74142"}, {"RefNumber": "73804", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/73804"}, {"RefNumber": "73510", "RefComponent": "BC-UPG", "RefTitle": "Problems during upgrade of patched source releases", "RefUrl": "/notes/73510"}, {"RefNumber": "69990", "RefComponent": "BC-UPG", "RefTitle": "SPAU: \"Mark transport\" and Hot Packages", "RefUrl": "/notes/69990"}, {"RefNumber": "69159", "RefComponent": "BC-OP-SUN", "RefTitle": "Localization in 3.0F, 3.1G on Solaris 2.5, 2.5.1", "RefUrl": "/notes/69159"}, {"RefNumber": "53328", "RefComponent": "PA-PA", "RefTitle": "RPU30000 - long runtimes", "RefUrl": "/notes/53328"}, {"RefNumber": "52994", "RefComponent": "LO-MD-SN", "RefTitle": "Converting deliveries with serial numbers from 2.2", "RefUrl": "/notes/52994"}, {"RefNumber": "48550", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/48550"}, {"RefNumber": "48184", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: Stopping a running R3up or SAPup", "RefUrl": "/notes/48184"}, {"RefNumber": "47482", "RefComponent": "AC-COB", "RefTitle": "Upgrade: Recording acct assignment fields:FI-SL", "RefUrl": "/notes/47482"}, {"RefNumber": "444045", "RefComponent": "BC-CTS-TLS", "RefTitle": "Views after import/upgrade without join conditions", "RefUrl": "/notes/444045"}, {"RefNumber": "40319", "RefComponent": "BC-UPG", "RefTitle": "SQL error 1024 in upgrade phase PCON_<REL>", "RefUrl": "/notes/40319"}, {"RefNumber": "40161", "RefComponent": "FI-SL", "RefTitle": "FI-SL: Error when upgrading to 3.0 in RGUXI30A", "RefUrl": "/notes/40161"}, {"RefNumber": "40153", "RefComponent": "FI-SL", "RefTitle": "FI-SL: Error GU093 with upgrade 3.0 in RGZZGLUX", "RefUrl": "/notes/40153"}, {"RefNumber": "40124", "RefComponent": "FI-SL", "RefTitle": "FI-SL: User exit programs in the SAP name range", "RefUrl": "/notes/40124"}, {"RefNumber": "39745", "RefComponent": "BC-ABA-NL", "RefTitle": "setlocale on ReliantUNIX (SINIX) and table TCP0C", "RefUrl": "/notes/39745"}, {"RefNumber": "29159", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/29159"}, {"RefNumber": "24864", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "No conversion of the table BSEG", "RefUrl": "/notes/24864"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "19584", "RefComponent": "BC-SRV-COM", "RefTitle": "SAPcomm: getting the new version from SAPSERVx", "RefUrl": "/notes/19584"}, {"RefNumber": "190527", "RefComponent": "CA-EUR-CNV", "RefTitle": "EMU: Error/warning message w/ upgrade table EWUPAK", "RefUrl": "/notes/190527"}, {"RefNumber": "16762", "RefComponent": "FI-SL-SL-E", "RefTitle": "GLX: Number ranges for object no.s & record number", "RefUrl": "/notes/16762"}, {"RefNumber": "150540", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info: Upgrading to 3.1H,3.1I SQL Server 7.0", "RefUrl": "/notes/150540"}, {"RefNumber": "15023", "RefComponent": "BC-I18", "RefTitle": "Initializing table TCPDB (RSCP0004) (EBCDIC)", "RefUrl": "/notes/15023"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "130446", "RefComponent": "PY", "RefTitle": "Upgrade 3.1I terminated in phase ACT_31I", "RefUrl": "/notes/130446"}, {"RefNumber": "127339", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/127339"}, {"RefNumber": "121431", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Simplified Chinese", "RefUrl": "/notes/121431"}, {"RefNumber": "121430", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Traditional Chinese", "RefUrl": "/notes/121430"}, {"RefNumber": "121429", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Korean", "RefUrl": "/notes/121429"}, {"RefNumber": "121428", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Japanese", "RefUrl": "/notes/121428"}, {"RefNumber": "110443", "RefComponent": "BC-UPG-TLS", "RefTitle": "Syntax error SPDD and SPAU", "RefUrl": "/notes/110443"}, {"RefNumber": "106799", "RefComponent": "XX-MIG-TOOL", "RefTitle": "Backup of the R/2-R/3 migration tools / LSMW", "RefUrl": "/notes/106799"}, {"RefNumber": "104018", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104018"}, {"RefNumber": "102950", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I       DB2/400", "RefUrl": "/notes/102950"}, {"RefNumber": "102208", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I       ORACLE", "RefUrl": "/notes/102208"}, {"RefNumber": "101926", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101926"}, {"RefNumber": "101220", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on language import 3.1I (+ SR1)", "RefUrl": "/notes/101220"}, {"RefNumber": "101139", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I    DB2/CS", "RefUrl": "/notes/101139"}, {"RefNumber": "100991", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I       INFORMIX", "RefUrl": "/notes/100991"}, {"RefNumber": "100903", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/100903"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "15023", "RefComponent": "BC-I18", "RefTitle": "Initializing table TCPDB (RSCP0004) (EBCDIC)", "RefUrl": "/notes/15023 "}, {"RefNumber": "24864", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "No conversion of the table BSEG", "RefUrl": "/notes/24864 "}, {"RefNumber": "102950", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I       DB2/400", "RefUrl": "/notes/102950 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "98026", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I    MSSQL Server", "RefUrl": "/notes/98026 "}, {"RefNumber": "150540", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info: Upgrading to 3.1H,3.1I SQL Server 7.0", "RefUrl": "/notes/150540 "}, {"RefNumber": "48184", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: Stopping a running R3up or SAPup", "RefUrl": "/notes/48184 "}, {"RefNumber": "444045", "RefComponent": "BC-CTS-TLS", "RefTitle": "Views after import/upgrade without join conditions", "RefUrl": "/notes/444045 "}, {"RefNumber": "100991", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I       INFORMIX", "RefUrl": "/notes/100991 "}, {"RefNumber": "86627", "RefComponent": "MM-IM", "RefTitle": "Movement types: Customizing during release upgrade", "RefUrl": "/notes/86627 "}, {"RefNumber": "101220", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on language import 3.1I (+ SR1)", "RefUrl": "/notes/101220 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "121428", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Japanese", "RefUrl": "/notes/121428 "}, {"RefNumber": "121429", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Korean", "RefUrl": "/notes/121429 "}, {"RefNumber": "121430", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Traditional Chinese", "RefUrl": "/notes/121430 "}, {"RefNumber": "121431", "RefComponent": "BC-INS", "RefTitle": "Additional Info: R/3 3.1I Inst. Simplified Chinese", "RefUrl": "/notes/121431 "}, {"RefNumber": "47482", "RefComponent": "AC-COB", "RefTitle": "Upgrade: Recording acct assignment fields:FI-SL", "RefUrl": "/notes/47482 "}, {"RefNumber": "73510", "RefComponent": "BC-UPG", "RefTitle": "Problems during upgrade of patched source releases", "RefUrl": "/notes/73510 "}, {"RefNumber": "83728", "RefComponent": "PY-DE", "RefTitle": "Check table T510Q before the upgrade", "RefUrl": "/notes/83728 "}, {"RefNumber": "190527", "RefComponent": "CA-EUR-CNV", "RefTitle": "EMU: Error/warning message w/ upgrade table EWUPAK", "RefUrl": "/notes/190527 "}, {"RefNumber": "74831", "RefComponent": "BC-CCM-MON", "RefTitle": "SPDD:Transports QE1K900002, TCVK900029, EWSK900052", "RefUrl": "/notes/74831 "}, {"RefNumber": "110443", "RefComponent": "BC-UPG-TLS", "RefTitle": "Syntax error SPDD and SPAU", "RefUrl": "/notes/110443 "}, {"RefNumber": "101139", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I    DB2/CS", "RefUrl": "/notes/101139 "}, {"RefNumber": "39745", "RefComponent": "BC-ABA-NL", "RefTitle": "setlocale on ReliantUNIX (SINIX) and table TCP0C", "RefUrl": "/notes/39745 "}, {"RefNumber": "102208", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1I       ORACLE", "RefUrl": "/notes/102208 "}, {"RefNumber": "69990", "RefComponent": "BC-UPG", "RefTitle": "SPAU: \"Mark transport\" and Hot Packages", "RefUrl": "/notes/69990 "}, {"RefNumber": "106799", "RefComponent": "XX-MIG-TOOL", "RefTitle": "Backup of the R/2-R/3 migration tools / LSMW", "RefUrl": "/notes/106799 "}, {"RefNumber": "130446", "RefComponent": "PY", "RefTitle": "Upgrade 3.1I terminated in phase ACT_31I", "RefUrl": "/notes/130446 "}, {"RefNumber": "53328", "RefComponent": "PA-PA", "RefTitle": "RPU30000 - long runtimes", "RefUrl": "/notes/53328 "}, {"RefNumber": "77384", "RefComponent": "BC-UPG", "RefTitle": "Phase STARTR3_NBAS: Access is denied", "RefUrl": "/notes/77384 "}, {"RefNumber": "52994", "RefComponent": "LO-MD-SN", "RefTitle": "Converting deliveries with serial numbers from 2.2", "RefUrl": "/notes/52994 "}, {"RefNumber": "16762", "RefComponent": "FI-SL-SL-E", "RefTitle": "GLX: Number ranges for object no.s & record number", "RefUrl": "/notes/16762 "}, {"RefNumber": "40161", "RefComponent": "FI-SL", "RefTitle": "FI-SL: Error when upgrading to 3.0 in RGUXI30A", "RefUrl": "/notes/40161 "}, {"RefNumber": "40124", "RefComponent": "FI-SL", "RefTitle": "FI-SL: User exit programs in the SAP name range", "RefUrl": "/notes/40124 "}, {"RefNumber": "74142", "RefComponent": "BC-DWB-DIC", "RefTitle": "Problem in the upgrade phase RSVTR_TRANS", "RefUrl": "/notes/74142 "}, {"RefNumber": "40153", "RefComponent": "FI-SL", "RefTitle": "FI-SL: Error GU093 with upgrade 3.0 in RGZZGLUX", "RefUrl": "/notes/40153 "}, {"RefNumber": "69159", "RefComponent": "BC-OP-SUN", "RefTitle": "Localization in 3.0F, 3.1G on Solaris 2.5, 2.5.1", "RefUrl": "/notes/69159 "}, {"RefNumber": "40319", "RefComponent": "BC-UPG", "RefTitle": "SQL error 1024 in upgrade phase PCON_<REL>", "RefUrl": "/notes/40319 "}, {"RefNumber": "19584", "RefComponent": "BC-SRV-COM", "RefTitle": "SAPcomm: getting the new version from SAPSERVx", "RefUrl": "/notes/19584 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}