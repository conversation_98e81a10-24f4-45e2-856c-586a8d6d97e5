{"Request": {"Number": "486274", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 337, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015395042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000486274?language=E&token=C27B84E7CB392FA34A8F83867BA21AA3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000486274", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000486274/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "486274"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 35}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.10.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "486274 - Additional information on upgrading to Basis 6.20 - INFORMIX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>There are errors in the guide or import procedure.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Upgrade, INFORMIX<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note contains information about INFORMIX-specific problems only.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /></p> <b>Contents<br />------</b><br /> <OL>1. INFORMIX Dynamic Server Versions</OL> <OL>2. Upgrading the database software</OL> <OL>3. Actions before the upgrade</OL> <OL>4. Errors in the guide</OL> <OL>5. Problems in the individual upgrade phases</OL> <OL>6. Actions after the upgrade</OL> <OL>1. INFORMIX Dynamic Server Versions<br />---------------------------------</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The required version of the INFORMIX Dynamic Server, as well as some additional information, is contained in the following special supplementary notes:<br /><br />WAS 6.40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;491742<br />EBP 3.5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;494201<br />BW&#x00A0;&#x00A0;3.0x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;509309<br />SAP R/3 Enterprise 4.7&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;518768<br />SAP R/3 Enterprise 4.7 Extension Set 2.00&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;639099<br />CRM 3.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;521501<br />BW&#x00A0;&#x00A0;3.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;543651<br />SCM 4.01&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;601620<br />CRM 4.0 / EBP 4.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;599232 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must refer to Note 685767. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Pay particular attention to the correct use of the Informix front end software (Iconnect Software). You must always adapt it to the R3 kernel being used (for the list, see Note 115619). <p></p> <OL>2. Upgrading the database software<br />------------------------------</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The database software is upgraded before the PREPARE. Even if no general downward-compatible release exists for the new database software, you may also operate the new database software with the kernels 3.1I_COM, 4.0B_COM, 4.5B, 4.6C and 4.6D during the upgrade. <UL><LI>Only for the kernels 3.1I_COM, 4.0B_COM, 4.5B on NT/WINDOWS2000</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You are authorized to operate the kernels 3.1I_COM, 4. 0B_COM, 4.5B with WINDOWS2000 during the upgrade. In this case, use at least front-end version 2.50TC1X2.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For the database conversion, you must also perform all the required conversion steps. Check the logs in the online log of the database for this. <p></p> <UL><LI>If the POSTDBUP script ends with the message 'Conversion failed', check the online.log for the following message:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;External conversion aborted due to incompatible sysmaster database.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: The sysmaster database was not converted for the current database server version. An external conversion of the sysmaster database must be carried out.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Workaround: Execute the script ${INFORMIXDIR}/etc/buildsmi as the user informix and restart the database server. This will generate a new sysmaster database and perform an external conversion. Ensure that no SQL statements are running before you execute the buildsmi script using 'onstat -g sql'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the conversion fails for another reason, open a customer message under the business component BC-DB-INF, requesting that it be forwarded to Development Support.<br /></p> <UL><LI>For NLS databases only (R/3 installation with 2.x to 3.1I)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ensure that the DBNLS=1 environment variable is set for the user &lt;sid&gt;adm and informix.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <UL><LI>SAPDBA only</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Make sure that you always use the SAPDBA from the /usr/sap/&lt;SID&gt;/SYS/exe/run directory before the DB upgrade and the new SAPDBA from the /usr/sap/put/exe directory after the database upgrade.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If this directory is not available yet, you can unpack the SAPDBA from the kernel CD:<br /><br />SAPCAR -xvf &lt;Kernel CD&gt;/UNIX/&lt;PLATTFORM&gt;/DBATOOLS.SAR sapdba<br />or<br />SAPCAR -xvf &lt;Kernel CD&gt;:\\NT\\&lt;PLATTFORM&gt;\\DBATOOLS.SAR sapdba.exe<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <UL><LI>After the database upgrade, you cannot start your SAP R/3 system.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As the user &lt;sid&gt; adm, call the command 'R3trans -d', and then check the contents of the file trans.log. If the following error occurred:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BE version = IDS&#x00A0;&#x00A0;&#x00A0;&#x00A0;1.00<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*** ERROR =&gt; Invalid&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BE version: 1.00<br />you must exchange the Informix Database Library (LIB DBSL) in the directory /usr/sap/&lt;SID&gt;/SYS/exe/run.&#x00A0;&#x00A0;The most current library file is available on SAP Service Marketplace.</p> <OL>3. Actions before the upgrade<br />------------------------</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you are still using kernel 4. 6D or lower, you must set the INFORMIX_FE_VERSION to any value, for example 'X'. Otherwise, the R3trans may terminate with the message 'Invalid FE version' when you start the system after the database upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you are still using DBLS = 1 in the environment for the INFORMIX or &lt;sid&gt;adm user, change the value to DBLS = 2. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before the Prepare, the database must be converted to a TCP/IP connection. To do so, proceed as follows: <UL><LI>Change the database to TCP/IP</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As the \"root\" user, create a new service in the /etc/services file and call it \"sapinf&lt;SID&gt;a\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Important: Use a service number that has not been assigned yet).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Keep in mind that if NIS, NIS+ or a similar service is active, you must make the change in the relevant master file.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If your system is called C11, for example, you should call the new service \"sapinfC11a\". The section from the /etc/services would now appear as follows:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sapinfC11&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4461/tcp</TD></TR> <TR><TD>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sapinfC11a&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4462/tcp</TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As the \"informix\" user, change the entry for the Shared Memory link in the sqlhosts file ($INFORMIXSQLHOSTS).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If your system is called \"C11\", for example, and the database server is called \"dbhost\", the following entry would then appear as follows in the sqlhosts file:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbhostc11shm&#x00A0;&#x00A0;onipcshm&#x00A0;&#x00A0;dbhost&#x00A0;&#x00A0;sapinfC11</TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy the entry. The informix server must always have the option of a shared memory link. Change the name of the previous SHM server name:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbhostc11shm0&#x00A0;&#x00A0;onipcshm&#x00A0;&#x00A0;dbhost&#x00A0;&#x00A0;sapinfC11</TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Include the new server name as an additional alias in the $ONCONFIG (DBSERVERALIASES). In the copied line, replace the log type as follows:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD> HPUX/AIX/LINUX/DEC:&#x00A0;&#x00A0;onipcshm with onsoctcp</TD></TR> <TR><TD> SOLARIS:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; onipcshm with ontlitcp</TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Replace the service to be used with the newly created service, that is, \"sapinfC11\" with \"sapinfC11a\". The result should look as follows:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD> HPUX/AIX/LINUX/DEC: dbhostc11shm0&#x00A0;&#x00A0;onipcshm&#x00A0;&#x00A0;dbhost&#x00A0;&#x00A0;sapinfC11</TD></TR> <TR><TD>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbhostc11shm&#x00A0;&#x00A0;onsoctcp&#x00A0;&#x00A0;dbhost&#x00A0;&#x00A0;sapinfC11a</TD></TR> <TR><TD></TD></TR> <TR><TD> SOLARIS:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbhostc11shm0&#x00A0;&#x00A0;onipcshm&#x00A0;&#x00A0;dbhost&#x00A0;&#x00A0;sapinfC11</TD></TR> <TR><TD>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbhostc11shm&#x00A0;&#x00A0;ontlitcp&#x00A0;&#x00A0;dbhost&#x00A0;&#x00A0;sapinfC11a</TD></TR> <TR><TD></TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The entry for the 'Default' TCP connection in the $INFORMIXSQLHOSTS file (example: 'dbhostc11tcp') should remain unchanged.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After the conversion to TCP/IP, it may take several minutes to start the database using 'startsap' because the R3trans waits for a 'time out'. Therefore, you should first start the database as 'informix' user and then start the SAP application using 'startsap r3'. If you have switched back to the SHM connection after the upgrade, you can also use 'startsap' to restart the database without queue times. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before starting the first PREPARE run, make sure that at least 200 MB of free space is available in the temporary DBSpaces (TMPDBSx). 'tmpdbs' is contained in the CHECKS.LOG as a synonym for the total of all temporary DBSpaces. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before starting the upgrade, make sure that there is at least 1 GB of log files in the 'logdbs'. <p></p> <UL><LI>Source Release 4.0B only</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If it does not already exist, create a dbspace named psapuser1.<br /></p> <UL><LI>Source releases LOWER THAN 4.6B only</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before the PREPARE, set the following environment variable in the environment of the &lt;sid&gt;adm user:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv INFORMIX_AF_MAXROWS 1 (csh)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INFORMIX_AF_MAXROWS=1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (ksh)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;export INFORMIX_AF_MAXROWS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;set INFORMIX_AF_MAXROWS=1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(NT)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /></p> <UL><LI>Only source releases on 6.10 or higher</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you plan a distributed upgrade (that is, if the central instance and database instance appear on different servers), you must ensure that an /informix/&lt;SID&gt;/sapreorg directory also exists on the central instance.<br /></p> <UL><LI>MOCOD systems only</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MCOD systems are SAP applications that were installed on the same database server in addition to an existing SAP application. MCOD systems use dbspace names of the type psap&lt;dbsid&gt;xxx, for example, psaptstddic. If you wish to upgrade an MOCD system, you must set the R3LOAD_ADDDBSID environment variable in the user &lt;sid&gt;adm environment before you start the R3UP:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3LOAD_ADDDBSID=&lt;upper case dbsid&gt; , for example, R3LOAD_ADDDBSID=TST<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Otherwise, tables are loaded into incorrect dbspaces, for example, psapddic instead of psap&lt;dbsid&gt;ddic in the EU_IMPORTx phase.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to upgrade an MOCD system to WINDOWS, execute the steps described under Section 9 of Note 402376, if you have not already done this.<br /></p> <UL><LI>HP_UX only</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If your database uses NLS, then create a soft link from de_DE. iso88591 to german.iso88591.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to upgrade an MOCD system to WINDOWS, execute the steps described under Section 9 of Note 402376, if you have not already done this.<br /></p> <OL>4. Errors in the guide<br />-------------------</OL> <p></p> <OL>5. Problems in the individual upgrade phases<br />----------------------------------------</OL> <UL><LI>HP, SUN and Reliant only</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you execute the PREPARE with a 32-bit database version, you get the following entry in the CHECKS.LOG file in the 'General Checks' module:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ERROR&gt; OS type XXX plus DB type INFORMIX undefined<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This is due to the fact that 32-bit databases are no longer supported in the 6.20 upgrade after the PREPARE phase. You should therefore interpret this message as a request for the database upgrade. See the relevant supplementary SAP notes for the required database release.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If an SQL error -212/ISAM-142 occurs with 'Create Index', see Note 547034.</p> <UL><LI>See Note 695163.</LI></UL> <p></p> <OL>6. Actions after the upgrade<br />-------------------------</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you changed from an SHM connection to a TCP/IP connection before upgrade, you should now reset your system to the SHM connection. <p></p> <UL><LI>Source releases LOWER THAN 4.6B only</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After the SAP upgrade, you should always reset the INFORMIX_AF_MAXROWS environment variable and restart the SAP application. Otherwise, performance problems can occur. This problem was solved in the front-end versions that are bound statically to the Release 6.40 DBSL.<br /><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If problems occur with \"saposcol\" on HP11, proceed as described in Note 127404. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check your profile parameters and add the following entry to DEFAULT.PFL:<br /><br />rsdb/prefer_union_all&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= 1 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Restart the R/3 system (stopsap - startsap) to allow the changes made to the standard settings during the R3up upgrade to take effect again. <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D033056)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (C5029091)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000486274/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000486274/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "539519", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/539519"}, {"RefNumber": "534334", "RefComponent": "XX-SER-REL", "RefTitle": "Composite SAP note Install./Upgrade SAP R/3 Enterprise 47x11", "RefUrl": "/notes/534334"}, {"RefNumber": "534208", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/534208"}, {"RefNumber": "513536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/513536"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "534334", "RefComponent": "XX-SER-REL", "RefTitle": "Composite SAP note Install./Upgrade SAP R/3 Enterprise 47x11", "RefUrl": "/notes/534334 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}