{"Request": {"Number": "1736351", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 980, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010315672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001736351?language=E&token=71929A3B4B4C578EE74A611E33278CFB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001736351", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001736351/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1736351"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.04.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-DFS-OF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use IS-DFS-OF-FOR"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BI Content Defense & Public Security", "value": "BW-BCT-DFS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-DFS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use IS-DFS-OF-FOR", "value": "BW-BCT-DFS-OF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-DFS-OF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1736351 - InfoObject '0DF_FORCE':  Data type CHAR32 is incorrect"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The data element /BI0/OIDF_FORCE with the data type CHAR32 is assigned to the InfoObject 0DF_FORCE.<br/><br/>The data type CHAR32 of the data element is incorrect.<br/><br/>Since the InfoObject 0DF_FORCE represents the object HROBJID, the data type must be NUMC8.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>0DF_LMMIS_ATTR, ODF_LMMIS, 0DF_IS_DFS_18, 0DF_DS03</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This concerns the program design.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Import the relevant Support Package or implement the corrections manually as described below.</p>\r\n<p>Important:<br/>Implement SAP Note 2009062 as well.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-DFS-PM (BW only - BI Content DFPS Plant Maintenance)"}, {"Key": "Responsible                                                                                         ", "Value": "D033047"}, {"Key": "Processor                                                                                           ", "Value": "D033047"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001736351/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736351/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Z_ROOSOURCE_ROOSFIELD_1736351.pdf", "FileSize": "150", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000429132012&iv_version=0003&iv_guid=FA6E9F4FA5907D44A4016C5A083F7C38"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1601140", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Migration of BW 3.x based data flows.", "RefUrl": "/notes/1601140"}, {"RefNumber": "1502779", "RefComponent": "BW-BCT-DFS-PM", "RefTitle": "0DF_LMMIS_ATTR:Termination-RSDMD 191 \"Duplicate data record\"", "RefUrl": "/notes/1502779"}, {"RefNumber": "1052648", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Migration of transfer rules and update rules for BW7.x", "RefUrl": "/notes/1052648"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2009062", "RefComponent": "BW-BCT-DFS-OF", "RefTitle": "Error in include program RS_BCT_UPDATE_DFPS_IS18_DS03", "RefUrl": "/notes/2009062 "}, {"RefNumber": "1967460", "RefComponent": "BW-BCT-DFS-MM", "RefTitle": "Incorrect format of DFS objects", "RefUrl": "/notes/1967460 "}, {"RefNumber": "1601140", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Migration of BW 3.x based data flows.", "RefUrl": "/notes/1601140 "}, {"RefNumber": "1052648", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Migration of transfer rules and update rules for BW7.x", "RefUrl": "/notes/1052648 "}, {"RefNumber": "1502779", "RefComponent": "BW-BCT-DFS-PM", "RefTitle": "0DF_LMMIS_ATTR:Termination-RSDMD 191 \"Duplicate data record\"", "RefUrl": "/notes/1502779 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "706", "To": "706", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "707", "To": "707", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "736", "To": "736", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "746", "To": "746", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-DFPS 603", "SupportPackage": "SAPK-60311INEADFPS", "URL": "/supportpackage/SAPK-60311INEADFPS"}, {"SoftwareComponentVersion": "EA-DFPS 604", "SupportPackage": "SAPK-60412INEADFPS", "URL": "/supportpackage/SAPK-60412INEADFPS"}, {"SoftwareComponentVersion": "EA-DFPS 605", "SupportPackage": "SAPK-60509INEADFPS", "URL": "/supportpackage/SAPK-60509INEADFPS"}, {"SoftwareComponentVersion": "EA-DFPS 606", "SupportPackage": "SAPK-60605INEADFPS", "URL": "/supportpackage/SAPK-60605INEADFPS"}, {"SoftwareComponentVersion": "BI_CONT 706", "SupportPackage": "SAPK-70605INBICONT", "URL": "/supportpackage/SAPK-70605INBICONT"}, {"SoftwareComponentVersion": "BI_CONT 707", "SupportPackage": "SAPK-70702INBICONT", "URL": "/supportpackage/SAPK-70702INBICONT"}, {"SoftwareComponentVersion": "BI_CONT 736", "SupportPackage": "SAPK-73603INBICONT", "URL": "/supportpackage/SAPK-73603INBICONT"}, {"SoftwareComponentVersion": "BI_CONT 746", "SupportPackage": "SAPK-74603INBICONT", "URL": "/supportpackage/SAPK-74603INBICONT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BI_CONT", "NumberOfCorrin": 1, "URL": "/corrins/0001736351/384"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-DFPS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60310INEADFPS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60401INEADFPS - SAPK-60411INEADFPS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60501INEADFPS - SAPK-60508INEADFPS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60601INEADFPS - SAPK-60604INEADFPS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Due to the extensive manual activities, we recommend that you implement this SAP Note only with a Support Package.<br/><br/>If you still want to implement this SAP Note manually, you first have to  execute the following advance steps in the source systems and then carry out the manual advance steps in the BW system.<br/><br/>Changes in the source system:<br/>==========================</P> <OL>1. Create the database view /ISDFPS/V_LM_MIS:</OL> <OL><OL>a) Call transaction SE11. Select \"View\", enter the value  /ISDFPS/V_LM_MIS as the view name in the related input field and choose \"Create\".</OL></OL> <OL><OL>b) In the dialog box now displayed, select \"Database view\" and choose ENTER to copy the selection.</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The system displays the screen \"Dictionary: Maintain View\". <OL><OL>a) Enter the following text as the short description:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;LM Missions <OL><OL>a) Choose the \"Tables/Join Conditions\" tab page, and enter the following tables in the \"Tables\" screen area:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/LM_MIS<br/>/ISDFPS/FORCE <OL><OL>a) In the \"Join conditions\" area of the screen, enter the following data:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;Table&gt; &lt;Field name&gt; &lt;=&gt; &lt;Table&gt; &lt; Field name&gt;<br/> /ISDFPS/LM_MIS&nbsp;&nbsp;MANDT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/FORCE&nbsp;&nbsp;MANDT<br/> /ISDFPS/LM_MIS&nbsp;&nbsp;FORCE_ID&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=&nbsp;&nbsp;&nbsp;&nbsp; /ISDFPS/FORCE&nbsp;&nbsp;FORCE_ID<br/> /ISDFPS/LM_MIS&nbsp;&nbsp;FORCE_CNT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/FORCE&nbsp;&nbsp;FORCE_CNT <OL><OL>a) Choose the \"View Fields\" tab page and enter the following values:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;View field&gt;&nbsp;&nbsp; &lt;Table&gt;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;Field&gt;<br/> MANDT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MANDT<br/> MISSION&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MISSION<br/> FORCE_ID&nbsp;&nbsp;&nbsp;&nbsp; /ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;FORCE_ID<br/> FORCE_CNT&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;FORCE_CNT<br/> OBJID&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/FORCE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;OBJID<br/> STEXT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;STEXT<br/> CREADAT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CREADAT<br/> CREABY&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; /ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CREABY<br/> CHNGDAT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CHNGDAT<br/> CHNGBY&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; /ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CHNGBY<br/> LOGSYS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; /ISDFPS/LM_MIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;LOGSYS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Choose \"Enter\" so that the system adds the remaining data. <OL><OL>a) Save and activate the database view; enter the value /ISDFPS/BI_CONTENT as the package.</OL></OL> <P><br/></P> <OL>1. Change the extraction structure ROXXD90195:</OL> <OL><OL>a) Call transaction SE11. Select \"Data type\", enter the structure  ROXXD90195 into the related input field and choose \"Change\".</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The system displays the screen \"Dictionary: Maintain Structure\". <OL><OL>a) Select the row with the component STEXT and choose \"Insert Row\".</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The system inserts an empty row before the selected row. <OL><OL>a) Enter the following values into the new row:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;Component&gt;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;Component type&gt;<br/> OBJID&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;HROBJID <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Choose \"Enter\" so that the system adds the remaining data. <OL><OL>a) Save and activate the enhanced extraction structure.</OL></OL> <P><br/></P> <OL>1. Change the DataSource 0DF_LMMIS_ATTR:</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The following changes to the DataSource 0DF_LMMIS_ATTR are required: <OL><OL>a) The table /ISDFPS/LM_MIS defined as an extractor must be replaced  with the database view /ISDFPS/V_LM_MIS created under point 1.ff.</OL></OL> <OL><OL>b) In the extraction structure ROXXD90195, the field property of the  newly added field OBJID must be changed from the value 'A' to the value 'P' and the indicator NOTEXREL = 'Y must be set.</OL></OL> <OL><OL>c) In the extraction structure ROXXD90195, the field property of the  field STEXT must be changed from the value 'P' to the value 'A' and the indicator NOTEXREL must be removed.</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;For these changes, use the report Z_ROOSOURCE_ROOSFIELD_1736351 from the attachment of this SAP Note. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the report in your system as described below and execute it with F8. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Call transaction SE38, enter the program name Z_ROOSOURCE_ROOSFIELD_1736351 and choose \"Create\". <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the report with the following attributes:<br/>Title................: See SAP Note 1736351<br/>Type: 1 (Executable program)<br/>Application: I (Plant maintenance) Development class...: Local object <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Save and activate the report. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute the report. <P><br/></P> <OL>1. Change the DataSource 0DF_LMMIS_TEXT:</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The following changes to the DataSource 0DF_LMMIS_TEXT are required: <OL><OL>a) The table /ISDFPS/LM_MIS defined as an extractor must be replaced  with the database view /ISDFPS/V_LM_MIS created under point 1.ff.</OL></OL> <OL><OL>b) The extraction structure /ISDFPS/LM_MIS entered in the DataSource  must be replaced with the extraction structure ROXXD90195.</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;You must execute these changes with the report Z_ROOSOURCE_ROOSFIELD_1736351 at the same time as point 3.c. <P><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; BI_CONT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Business Intell...|<br/>| Release 706&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-70601INBICONT - SAPK-70604INBICONT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 707&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-70701INBICONT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Due to the extensive manual activities, we recommend that you implement this SAP Note only with a Support Package.<br/><br/>If you still want to implement this SAP Note, you must first carry out  the manual advance activities in the source systems before you implement  the correction instructions and then carry out the steps described below in the specified sequence in your BW system.<br/><br/>Changes in the BW system<br/>========================</P> <OL>1. Change the data type of the domain /BI0/ODF_FORCE:</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The domain /BI0/ODF_FORCE with the data type CHAR32 is assigned to the  data element /BI0/OIDF_FORCE. The data type of this domain must be changed to NUMC with length 8. <OL><OL>a) Call transaction SE11. Select \"Domain\", enter the name  /BI0/ODF_FORCE in the related input field, and choose \"Change\".</OL></OL> <OL><OL>b) Open the \"Definition\" tab page and change the data as follows in the \"Format\" screen area:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data type..........: NUMC<br/>No. characters....: 8 <OL><OL>a) Choose ENTER and activate the changed domain.</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Note the following:<br/> Due to the changed data type, dependent objects (usually tables) must  also be changed. The system automatically executes these changes during the activation where possible.<br/>If there are tables that the system cannot automatically adjust, the  domain is not activated and a dialog box is sent that can be used to  display a log. Tables that cannot be automatically adjusted are  displayed in red in the log and it is noted that these tables must be converted. <OL><OL>a) Open a second mode and call transaction SE14, enter the first table  name displayed in red in the log and choose the \"Edit\" pushbutton.</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The screen \"ABAP/4 Dictionary: Utility for Database Tables\" is displayed. <OL><OL>a) Since the data for the DataSource 0DF_LMMIS_ATTR must still be  implemented again after you implement this SAP Note, select the option \"Delete data\" and choose \"Activate and adjust database\".</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The successful conversion is displayed with a message in the status line. <OL><OL>a) Execute the conversion consecutively for all tables written in red in the log.</OL></OL> <OL><OL>b) If all tables were converted successfully, activate the changed  domain again and look at the log at the end of the activation again;  there should be no more red entries in the log this time and the domain is activated.</OL></OL> <P></P> <OL>1. Change the InfoObjects 0DF_FORCE and 0DF_LMMIS:</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;In the InfoObject 0DF_FORCE, the data type must be changed from CHAR32 to NUMC8. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Before this change is possible, the InfoObject 0DF_FORCE must be removed from the compounding of the InfoObject 0DF_LMMIS. <OL><OL>a) Call transaction RSD1, enter the InfoObject 0DF_LMMIS in the input field and choose \"Maintain\".</OL></OL> <OL><OL>b) Open the \"Compounding\" tab page, select the row with the InfoObject 0DF_FORCE and choose \"Delete\".</OL></OL> <OL><OL>c) Activate the changed InfoObject and return to the initial screen of transaction RSD1.</OL></OL> <OL><OL>d) Enter the InfoObject 0DF_FORCE and choose \"Maintain\".</OL></OL> <OL><OL>e) Open the \"General\" tab page and change the data type and the length as follows:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data Type: NUMC - Character string with only digits<br/>Length: 8 <OL><OL>a) Activate the changed InfoObject and return to the initial screen of transaction RSD1.</OL></OL> <OL><OL>b) Enter the InfoObject 0DF_LMMIS in the input field again and choose \"Maintain\".</OL></OL> <OL><OL>c) Open the \"Compounding\" tab page, select the row with the InfoObject 0DF_LMFOCNT and choose \"Insert Row\".</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The system adds a new row before the selected row. <OL><OL>a) Enter the InfoObject 0DF_FORCE into the newly added row and choose  ENTER to allow the system to add the rest of the data.</OL></OL> <OL><OL>b) Activate the changed InfoObject.</OL></OL> <P></P> <OL>1. Migration of data flow to 7.x for the following objects:</OL> <UL><UL><LI>DataSource 0DF_LMMIS_ATTR, 0DF_LMMIS_TEXT, 0DF_IS_DFS_18</LI></UL></UL> <UL><UL><LI>InfoSource 0DF_IS_DFS_18</LI></UL></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;In this context, refer also to SAP Note 1052648. <P></P> <OL>1. Change the transformation 'RSDS 0DF_LMMIS_ATTR -&gt; IOBJ 0DF_LMMIS':</OL> <OL><OL>a) Delete the mapping (the connection, arrow) from the source field  0DF_FORCE to the target InfoObject 0DF_FORCE and map the field OBJID with the target InfoObject 0DF_FORCE.</OL></OL> <OL><OL>b) Activate the transformation.</OL></OL> <P></P> <OL>2. Change the transformation 'RSDS 0DF_IS_DFS_18 -&gt; TRCS 0DF_IS_DFS_18':</OL> <OL><OL>a) Map the source field FORCE_ID with the target InfoObject 0DF_TECHID  and the field OBJID with the target InfoObject 0DF_FORCE.</OL></OL> <OL><OL>b) Check whether the following routines exist for the following rules:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT1_P: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_PERS<br/>Target field of the rule....: 0DF_TEXT1_P <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT1_P.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_PERS(50).<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT1_P <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT1_M: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_MAT<br/>Target field of the rule....: 0DF_TEXT1_M <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT1_M.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_MAT(50).<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT1_M <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT1_T: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_TRAI<br/>Target field of the rule....: 0DF_TEXT1_T <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT1_T.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_TRAI(50).<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT1_T <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT2_P: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_PERS<br/>Target field of the rule....: 0DF_TEXT2_P <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT2_P.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_PERS+50(50).<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT2_P <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT2_M: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_MAT<br/>Target field of the rule....: 0DF_TEXT2_M <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT2_M.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_MAT+50(50).<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT2_M <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT2_T: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_TRAI<br/>Target field of the rule....: 0DF_TEXT2_T <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT2_T.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_TRAI+50(50).<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT2_T <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT3_P: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_PERS<br/>Target field of the rule....: 0DF_TEXT3_P <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT3_P.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_PERS+100.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT3_P <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT3_M: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_MAT<br/>Target field of the rule....: 0DF_TEXT3_M <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT3_M.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_MAT+100.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT3_M <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_TEXT3_T: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source field of the rule...: TEXT_TRAI<br/>Target field of the rule....: 0DF_TEXT3_T <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_TEXT3_T.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = SOURCE_FIELDS-TEXT_TRAI+100.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_TEXT3_T <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0LOGSYS: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Target field of the rule....: 0LOGSYS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0LOGSYS.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;RESULT = p_r_request-&gt;get_logsys( ).<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0LOGSYS <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If the routines do not exist, create them and activate the transformation. <P></P> <OL>1. Change the transformation 'TRCS 0DF_IS_DFS_18 -&gt; ODSO 0DF_DS03':</OL> <OL><OL>a) Check whether the following start routine exists for the transformation:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD start_routine.<br/>&nbsp;&nbsp;FIELD-SYMBOLS:<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;SOURCE_FIELDS&gt;&nbsp;&nbsp;&nbsp;&nbsp;TYPE _ty_s_SC_1.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rstmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_START_DS03'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;it_data = SOURCE_PACKAGE.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"start_routine <OL><OL>a) If the start routine does not exist, create it.</OL></OL> <OL><OL>b) Check whether the following routines exist for the following rules:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_OPERATN: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0DF_OPERATN <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_OPERATN.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get address number<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'DF_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_OPERATN <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_CONTING: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0DF_CONTING <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_CONTING.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get contingent<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'DF_CONTING'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_CONTING <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_COMANDR: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0DF_COMANDR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_COMANDR.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get leading force element<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'DF_COMANDR'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_COMANDR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_PROVPER: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0DF_PROVPER <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_PROVPER.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get provider personel<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'DF_PROVPER'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_PROVPER <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_PROVMAT: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0DF_PROVMAT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_PROVMAT.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get provider material<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'DF_PROVMAT'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_PROVMAT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0ADDR_NUMBR: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0DF_NUMBR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0ADDR_NUMBR.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get address number<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'ADDR_NUMBR'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0ADDR_NUMBR <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0DF_ADRTYPE: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0DF_ADRTYPE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0DF_ADRTYPE.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get address type<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'DF_ADRTYPE'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0DF_ADRTYPE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0COUNTRY: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0COUNTRY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0COUNTRY.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get country<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'COUNTRY'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0COUNTRY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0POSTALCODE: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0POSTALCODE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0POSTALCODE.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get postal code<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'POSTALCODE'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0POSTALCODE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0CITY: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0CITY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0CITY.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get address number<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'CITY'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0CITY <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0LONGITUDE: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0LONGITUDE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0LONGITUDE.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get longitude<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'LONGITUDE'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0LONGITUDE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0LATITUDE: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0LATITUDE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0LATITUDE.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get latitude<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'LATITUDE'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0LATITUDE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0ALTITUDE: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0ALTITUDE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0ALTITUDE.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get altitude<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'ALTITUDE'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0ALTITUDE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0SRCID: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0SRCID <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0SRCID.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get source id of a geo location<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'SRCID'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0SRCID <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0PRECISID: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0PRECISID <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0PRECISID.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get precision id of a geo location<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'PRECISID'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0PRECISID <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&gt;Rule 0REGION: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Source fields of the rule...: 0DATEFROM, 0DATETO, 0DF_FORCE<br/>Target field of the rule......: 0REGION <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;METHOD compute_0REGION.<br/>&nbsp;&nbsp;DATA:<br/>&nbsp;&nbsp;&nbsp;&nbsp;MONITOR_REC&nbsp;&nbsp;&nbsp;&nbsp;TYPE rsmonitor.<br/>*$*$ begin of routine - insert your code only below this line<br/>* --- Get address number<br/>&nbsp;&nbsp;CALL FUNCTION 'RS_BCT_DFPS_GET_FORCE_OPERATN'<br/>&nbsp;&nbsp;&nbsp;&nbsp;EXPORTING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_force&nbsp;&nbsp;&nbsp;&nbsp;= SOURCE_FIELDS-df_force<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_datefrom = SOURCE_FIELDS-datefrom<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_dateto&nbsp;&nbsp;= SOURCE_FIELDS-dateto<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv_field&nbsp;&nbsp;&nbsp;&nbsp;= 'REGION'<br/>&nbsp;&nbsp;&nbsp;&nbsp;CHANGING<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cv_result&nbsp;&nbsp;= RESULT.<br/>* --- No problems<br/>&nbsp;&nbsp;sy-subrc = 0.<br/>*$*$ end of routine - insert your code only before this line<br/>ENDMETHOD.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"compute_0REGION <OL><OL>a) If the routines do not exist, create them.</OL></OL> <P><br/><br/>Use transaction SNOTE to implement the correction instructions contained in this SAP Note.<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}