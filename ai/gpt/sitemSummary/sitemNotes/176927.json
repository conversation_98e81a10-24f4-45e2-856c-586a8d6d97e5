{"Request": {"Number": "176927", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 316, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014729942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000176927?language=E&token=8BC9EA5CD4B28AF40E1C62D96760C070"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000176927", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000176927/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "176927"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.10.2001"}, "SAPComponentKey": {"_label": "Component", "value": "CA-BFA-TRA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transceiver"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Framework Architecture", "value": "CA-BFA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-BFA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transceiver", "value": "CA-BFA-TRA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-BFA-TRA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "176927 - Transceiver component becomes unnecessary"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The transceiver component becomes unnecessary.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Transceiver, KK1, KK2, KK3, KK4, KK5, PP-PDC, HR-PDC, PDC</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The transceiver is required for communication at the standard interfaces KK1 and KK2. Because these interfaces have been replaced by new BAPI-based interfaces, it is no longer necessary to use the transceiver. The delivery and maintenance of the transceiver is therefore discontinued for the next but one main 4.6 release. This means that the transceiver is still supported by SAP Basis release 6.10 but not with 6.20 and later.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This means no restriction because new interfaces (HR-PDC-4.5, PP-PDC-4.6) are available as of Releases 4.5 or 4.6, and these interfaces cover the functions of the old interfaces. Because the maintenance of the transceiver component is discontinued soon, it is no longer ported to LINUX; neither is there a UNICODE support. If you work with LINUX or UNICODE you can change to the new versions of the interface.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PP-PDC (Plant Data Collection)"}, {"Key": "Other Components", "Value": "PT-RC (Time Data Recording and Management)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027318)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000176927/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000176927/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "647145", "RefComponent": "PT-RC-EX", "RefTitle": "Setting up the HR-PDC interface", "RefUrl": "/notes/647145"}, {"RefNumber": "45694", "RefComponent": "CA-BFA-TRA", "RefTitle": "Transceiver installation", "RefUrl": "/notes/45694"}, {"RefNumber": "44103", "RefComponent": "CA-BFA-TRA", "RefTitle": "Setting up the PDC interface", "RefUrl": "/notes/44103"}, {"RefNumber": "406429", "RefComponent": "BC-MID-ALE", "RefTitle": "Setting up the PP-PDC interface", "RefUrl": "/notes/406429"}, {"RefNumber": "357212", "RefComponent": "PP-SFC-EXE-CON", "RefTitle": "PDC confirmation 4.6: Default values for services", "RefUrl": "/notes/357212"}, {"RefNumber": "211918", "RefComponent": "PT-RC-TE", "RefTitle": "KK1 is replaced by HR-PDC", "RefUrl": "/notes/211918"}, {"RefNumber": "103376", "RefComponent": "CA-BFA-TRA", "RefTitle": "Transceiver coupld.exe under NT 4.0 terminates", "RefUrl": "/notes/103376"}, {"RefNumber": "102484", "RefComponent": "PP-PDC", "RefTitle": "Problems with delta download CC2", "RefUrl": "/notes/102484"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2879911", "RefComponent": "BC-ABA", "RefTitle": "Package BALI is missing", "RefUrl": "/notes/2879911 "}, {"RefNumber": "406429", "RefComponent": "BC-MID-ALE", "RefTitle": "Setting up the PP-PDC interface", "RefUrl": "/notes/406429 "}, {"RefNumber": "44103", "RefComponent": "CA-BFA-TRA", "RefTitle": "Setting up the PDC interface", "RefUrl": "/notes/44103 "}, {"RefNumber": "45694", "RefComponent": "CA-BFA-TRA", "RefTitle": "Transceiver installation", "RefUrl": "/notes/45694 "}, {"RefNumber": "357212", "RefComponent": "PP-SFC-EXE-CON", "RefTitle": "PDC confirmation 4.6: Default values for services", "RefUrl": "/notes/357212 "}, {"RefNumber": "211918", "RefComponent": "PT-RC-TE", "RefTitle": "KK1 is replaced by HR-PDC", "RefUrl": "/notes/211918 "}, {"RefNumber": "647145", "RefComponent": "PT-RC-EX", "RefTitle": "Setting up the HR-PDC interface", "RefUrl": "/notes/647145 "}, {"RefNumber": "102484", "RefComponent": "PP-PDC", "RefTitle": "Problems with delta download CC2", "RefUrl": "/notes/102484 "}, {"RefNumber": "103376", "RefComponent": "CA-BFA-TRA", "RefTitle": "Transceiver coupld.exe under NT 4.0 terminates", "RefUrl": "/notes/103376 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}