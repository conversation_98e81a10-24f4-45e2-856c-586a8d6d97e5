{"Request": {"Number": "1600929", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 367, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009504642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001600929?language=E&token=9FAB5D442A82AAAA84C8ED6406A15001"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001600929", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001600929/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1600929"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 91}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.05.2021"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB-ENG-BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA BW Engine"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA DB Engines", "value": "HAN-DB-ENG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB-ENG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA BW Engine", "value": "HAN-DB-ENG-BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB-ENG-BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1600929 - SAP BW powered by SAP HANA DB: Information"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to migrate your SAP Business Information Warehouse (BW) system to the SAP HANA database (DB).<br />You are using an SAP BW system with SAP HANA as the database.<br />You are looking for additional SAP Notes regarding SAP BW with SAP HANA as the database.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Support Package, BW on HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>This note contains information about the following topics:</strong></p>\r\n<ol>\r\n<li>General Information about Migration of your BW system to HANA</li>\r\n<li>Recommendations with regards to BW Release and Revision</li>\r\n<li>Information relevant for SAP NW BW 7.50 on HANA</li>\r\n<li>Information relevant for SAP NW BW 7.40&#160;on HANA</li>\r\n<li>Information relevant for SAP NW BW 7.31 on HANA</li>\r\n<li>Information relevant for SAP NW BW 7.30 on HANA</li>\r\n<li>Additional important SAP Notes and known problems</li>\r\n</ol>\r\n<p>&#160;<strong><span style=\"text-decoration: underline;\">1. General Information about Migration of your BW system to HANA</span></strong></p>\r\n<p>Ensure the following:</p>\r\n<ol>\r\n<li>You are NOT using a dual stack system (dual stack: ABAP and JAVA have the same System ID (SID) in the same database). Any functions for splitting are provided using the dual-stack split tool in the Software Logistics (SL) Toolset.</li>\r\n<li>For information about the SL Toolset and its release schedule, see the central note for the SL Toolset 1.0 (Note 1563579).<br />Support Package 06 or higher is imported for your SAP in-memory DB. We recommend to use the latest available revision from SMP, please see also recommedation for the different releases below.</li>\r\n</ol>\r\n<p>Only a standard installation and a distributed installation with AS-ABAP is supported. For AS-ABAP and SAP HANA DB on one Server please refer to note 1953429. Other applications must not be installed on the database server apart from the preinstalled SAP HANA database (DB) and additional applications that it requires because these other applications may have a negative effect on performance.&#160;For details about the other supported combinations please see the notes 1661202 and 1681092.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">2. Recommedations with regards to BW Release and Revision</span></strong></p>\r\n<p>With the introduction of maintenance revisions SAP recommends the following revisions on different HANA SPS levels for BW powered by SAP HANA Systems as guidance for maintenance / update / upgrade planning:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>Productively used BW release</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Recommended minimum HANA SP/Revision</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>7.30</p>\r\n</td>\r\n<td>\r\n<p>Maintenance Revisions 69.0x or <br />Maintenance Revision 74.0x *) or<br />Revision 82 or higher (HANA SP08)<br />or&#160;Revision 96 or higher (HANA SP09)<br />or Revision 102 or higher (HANA SP10)<br />or Revision 112 or higher (HANA SP11)<br />or Revision 122 or higher (HANA SP12)<br />or HANA 2 - Revision 36 or higher (HANA 2 SP3) - please see pre-requisites for HANA 2 under ****</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>7.31</p>\r\n</td>\r\n<td>\r\n<p>Maintenance Revisions 69.0x or <br />Maintenance Revision 74.0x *)<br />or Revision 82 or higher&#160;(HANA SP08)**)<br />or Revision 96 or higher (HANA SP09)<br />or Revision 102 or higher (HANA SP10)<br />or Revision 112 or higher (HANA SP11)<br />or Revision 122 or higher (HANA SP12)<br />or HANA 2 - Revision 36 or higher (HANA 2 SP3)&#160;- please see pre-requisites for HANA 2 under ****</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SP02 &lt;= 7.40 &lt; SP05</p>\r\n</td>\r\n<td>\r\n<p>Maintenance Revisions 69.0x or <br />Maintenance Revision 74.0x *)<br />or Revision 82 or higher&#160;(HANA SP08)<br />or Revision 96 or higher (HANA SP09)<br />or Revision 102 or higher (HANA SP10)<br />or Revision 112 or higher (HANA SP11)<br />or Revision 122 or higher (HANA SP12)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SP05 &lt;= 7.40 &lt; SP08</p>\r\n</td>\r\n<td>\r\n<p>Maintenance Revision 74.0x<br />or Revision 82 or higher&#160;(HANA SP08)<br />or Revision 96 or higher (HANA SP09)<br />or Revision 102 or higher (HANA SP10)<br />or Revision 112 or higher (HANA SP11)<br />or Revision 122 or higher (HANA SP12)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>7.40 &gt;= SP08</p>\r\n</td>\r\n<td>\r\n<p>Revision 85 or higher (HANA SP08)<br />or Revision 96 or higher (HANA SP09)<br />or Revision 102 or higher (HANA SP10)<br />or Revision 112 or higher (HANA SP11)<br />or Revision 122 or higher (HANA SP12)<br />or HANA 2.0 Revision 11 or higher (HANA 2 SP1) **) ***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>7.50</p>\r\n</td>\r\n<td>\r\n<p>Revision 102 or higher (HANA SP10)<br />or Revision 112 or higher (HANA SP11)<br />or Revision 122 or higher (HANA SP12)<br />or HANA 2.0 Revision 1 or higher(HANA 2 SP0) **)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Releases 7.51 and higher</p>\r\n</td>\r\n<td>\r\n<p>These releases are no Netweaver releases. They are called Innovation Packages and basically build&#160;<br />to support ABAP development activities on partner and on customer side.</p>\r\n<p>A use of BW as a hub is not released for these releases. Of course the embedded use case of BW in S/4HANA&#160;<br />on Premise is supported (SAP_BW as component in S/4HANA).<br />With regards to to HANA Versions please have a look at the corresponding S/4HANA information.<br /><br />This means that BI_CONT cannot be installed on Systems installed as AS-ABAP Innovation Package.</p>\r\n<p><a target=\"_blank\" href=\"/notes/2372388\">https://launchpad.support.sap.com/#/notes/2372388</a> (7.51)<br /><a target=\"_blank\" href=\"/notes/2524430\">https://launchpad.support.sap.com/#/notes/2524430</a> (7.52)</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;<span style=\"text-decoration: underline;\">*) Remark:</span> With the availability of maintenance revision 69.07, 74.04 and 85.05 SAP stops the delivery of fixes based on Revision 69 and 74. For details and other SP's please see note <em>2021789 - SAP HANA Revision and Maintenance Strategy</em>.</p>\r\n<p>**) Remark: For details about HANA 2.0 please see note:&#160;2420699</p>\r\n<p>***) Remark: BW has different requirements to HANA therefore the HANA Version can differ to the version given by NW. The difference in the supported HANA 2 Version between SAP BW 7.50 and SAP BW 7.40 lays in the backwards release of HANA 2 for 7.40. HANA 2 SP0 was not in maintenance any more at this point in time.</p>\r\n<p>****) Remark: As SAP HANA was first released for BW only (BW 7.30 SP05 onwards, BW 7.31) and not for the full Netweaver stack, there is an inconsistency to note 2420699. The same holds true for the release of SAP HANA 2.0.</p>\r\n<ul>\r\n<li><strong>Pre-requistes for NW 7.3x and HANA 2.0:</strong></li>\r\n<ul>\r\n<li>implement note&#160;<a target=\"_blank\" href=\"/notes/2972853\">2972853 - Enhancement of TREX_EXT function modules for Netweaver 7.3x + HANA2</a></li>\r\n<li>set the following internal (and therefore not documented) parameters on HANA</li>\r\n<ul>\r\n<li>ALTER SYSTEM ALTER CONFIGURATION ('indexserver.ini','SYSTEM') SET<br /> ('global', 'activate_old_trexviadbsl_functions') = 'true'<br /> WITH RECONFIGURE;&#65279;</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p>Please keep the update Paths for Maintenance Revisions in mind. Details can be found in note <em>1948334 - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\r\n<p>Notes with regards to HANA Revisions:</p>\r\n<ul>\r\n<li>HANA SP12: 2298750 - SAP HANA Platform SPS 12 Release Note</li>\r\n<li>HANA SP11:&#160;2227464 - SAP HANA Platform SPS 11 Release Note</li>\r\n<li>HANA SP10: 2165826 - SAP HANA Platform SPS 10 Release Note</li>\r\n<li>HANA SP09: 2075266 &#8211; SAP HANA-Platform SPS 09 Release Note</li>\r\n<li>HANA SP08: 2004651 - SAP-HANA-Platform SPS 08: Release-Informationen</li>\r\n<li>HANA SP07: 1921675 - SAP HANA Platform SPS 07 Release Note</li>\r\n<li>HANA SP06: 1935871 - SAP-HANA-Database SPS 06 Maintenance Revisions</li>\r\n</ul>\r\n<p>Information about HANA 1 and Netweaver can be found in note:&#160;<strong>1914052</strong></p>\r\n<p>HANA Systems with <span style=\"text-decoration: underline;\">Dynamic Tiering only</span>:</p>\r\n<p style=\"padding-left: 30px;\">Upgrade from HANA SP09 to HANA SP10: Please see the upgrade guide for Dynamic Tiering as there are some manual activities for DT needed as Pre-Upgrade Tasks.<br />Please see also note:&#160;2179009 - Update of SAP HANA and Dynamic Tiering From SPS 09 Rev 90 to SPS 10 Fails</p>\r\n<p><strong><span style=\"text-decoration: underline;\">3. Information relevant for SAP NW BW 7.50 on HANA</span></strong></p>\r\n<p>3.1. General Information</p>\r\n<ul>\r\n<li>Please find BW specific information about timeline, scope and recommendations at&#160;http://scn.sap.com/docs/DOC-67635&#160;</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">3.2. Upgrade relevant Information</span></p>\r\n<ul>\r\n<li>Using BW 7.50 SP00 or higher: It is necessary to use HANA SP10 or higher in combination with BW 7.50. Revision 102 is the minimum revision for HANA SP10. We recommend to always use the latest available HANA SP10 revision.</li>\r\n<li>The technical upgrade requires HANA SP09 or higher. In case of a BW System we recommend to execute the upgrade on HANA SP10.</li>\r\n<li><em>If </em>your start release is &lt;= 7.40 SP02 please have a look at the following activites before the upgrade:</li>\r\n<ul>\r\n<li><strong>Please see note 1879618 for BW specific activities before the upgrade/update to SAP NW BW 7.5</strong></li>\r\n<li>Necessary changes in customer owned coding: see note 1823174</li>\r\n</ul>\r\n<li>If your start release is &lt;= 7.40 SP05 please have a look at the following activies after the upgrade:</li>\r\n<ul>\r\n<li>After upgrading your system you need to generate your ColumnViews/Indexes on HANA once again(RS_BW_POST_MIGRATION). Details can be found in note 1953480.</li>\r\n<li>After the upgrade you might get the message: \"Prerequisites for AMDP not met.\" Please add the privileges given in <a target=\"_blank\" href=\"/notes/1899222\">note 1899222</a> to your SAP&lt;SID&gt; DB-User.</li>\r\n</ul>\r\n<li>Start release independent activites</li>\r\n<ul>\r\n<li>For compatibility of the BI-JAVA Stack with SAP NW BW 7.40 please have a look at note 1645590</li>\r\n<li>If you are using the BW based generation of HANA models including the generation of HANA privileges from BW Reporting Authorizations you need to add privilege ROLE ADMIN to your SAP&lt;SID&gt; Database user. Details are given in note: 1956963.</li>\r\n<li>If you'd like to use HANA based Analysis Processes within your BW you need to activate the scriptserver in your HANA Database Instance</li>\r\n<li>About Add-On compatibility please see notes</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2156543\">2156543 - Add-on compatibility of SAP NetWeaver 7.5 - Java</a></li>\r\n<li><a target=\"_blank\" href=\"/notes/2156130\">2156130 - Add-on compatibility of SAP NetWeaver 7.5 - ABAP</a></li>\r\n</ul>\r\n<li>Information about SP Equivalence can be found here:&#160;<span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2187353\">2187353 - SP </a></span><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2187353\">Equivalence</a></span><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2187353\">for</a></span><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2187353\"> update/upgrade </a></span><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2187353\">to</a></span><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2187353\"> SAP NW 7.5</a></span></li>\r\n</ul>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">4. Information relevant for SAP NW BW 7.40 on HANA</span></strong></p>\r\n<p>4.1. General Information</p>\r\n<ul>\r\n<li>Please find BW specific information about timeline, scope and recommendations at http://scn.sap.com/docs/DOC-35096.</li>\r\n<li>For details about changes relevant for customer-specific programs please see <a target=\"_blank\" href=\"/notes/1823174\">note 1823174</a> - This note is also relevant for SAP SCM Customers.</li>\r\n<li>For general NW 7.40 Information please go to: http://scn.sap.com/docs/DOC-35002</li>\r\n<li>PAM Information can be found in note&#160;</li>\r\n</ul>\r\n<p>4.2. Upgrade Information</p>\r\n<ul>\r\n<li>Hints for upgrading/updating your system <span style=\"text-decoration: underline;\">from</span> 7.30, 7.31, 7.40 &lt; SP02 <span style=\"text-decoration: underline;\">to</span> 7.40 SP05 or higher</li>\r\n<ul>\r\n<li>These activities are also relevant for customers using SAP SCM.</li>\r\n<li>HANA Revision 82 is needed to avoid issues during downtime of the upgrade in case of the following situation:</li>\r\n<ul>\r\n<li>You are using an ODS with a characteristic in the key which is referencing to 0TCTIOBJVAL. This infoobject is enlarged to length 250 which leads to issues on the database (alter column in table in partition specification). Please check your system before. &#160;&#160;</li>\r\n</ul>\r\n<li><strong>Please see note 1879618 for BW specific activities before the upgrade/update to SAP NW BW 7.40</strong></li>\r\n<li>For compatibility of the BI-JAVA Stack with SAP NW BW 7.40 please have a look at <a target=\"_blank\" href=\"/notes/1645590\">note 1645590</a></li>\r\n<li>Necessary changes in customer owned coding: seen <a target=\"_blank\" href=\"/notes/1823174\">note 1823174</a></li>\r\n<li>After upgrading your system to SAP NW BW 7.40 SP05 or higher you need to generate your ColumnViews/Indexes on HANA once again(RS_BW_POST_MIGRATION). Details can be found in <a target=\"_blank\" href=\"/notes/1953480\">note 1953480</a>.</li>\r\n<li>After applying SP05 or higher you might get the message: \"Prerequisites for AMDP not met.\" Please add the privileges given in <a target=\"_blank\" href=\"/notes/1899222\">note 1899222</a> to your SAP&lt;SID&gt; DB-User.</li>\r\n<li>If you are using the BW based generation of HANA models including the generation of HANA privileges from BW Reporting Authorizations you need to add privilege ROLE ADMIN to your SAP&lt;SID&gt; Database user. Details are given in <a target=\"_blank\" href=\"/notes/1956963\">note: 1956963</a>.</li>\r\n<li>If you'd like to use HANA based Analysis Processes within your BW you need to activate the scriptserver in your HANA Database Instance</li>\r\n<li><a target=\"_blank\" href=\"/notes/1951491\">Note 1951491</a> Minimal DB system platform requirements for SAP NetWeaver 7.4 SP08 gives hints for pure NW upgrades</li>\r\n</ul>\r\n</ul>\r\n<p>4.3 Information for SP Update</p>\r\n<ul>\r\n<li>HANA BW 7.40 with Support Package &gt;= SP08</li>\r\n<ul>\r\n<li><em>Using BW 7.40 SP08 or higher: It is necessary to use HANA SP08 or higher in combination with BW 7.40 SP08 or higher. Revision 82(85 for some SP Updates) is the minimum revision for HANA SP08. We recommend to always use the latest available HANA SP08 revision. </em></li>\r\n<li>Kernel: We recommend to use at least PL&#160;14 (742_REL).</li>\r\n<li>With HANA BW SP08 we optimized the use of rowstore and columnstore for many tables in SAP Netweaver. Therefore we recommend a rowstore defragmentation run after you have imported SP08. Details are given in note 1813245 - SAP HANA DB: Row store reorganization</li>\r\n<li>For your convenience we have created a note collecting all important notes which are created after the final assembly of the Support Package has started. Please see note 1949273 for details about notes to be implemented after applying SP08 (and SP05, SP06, SP07). This note contains also information about Note Assistant enabled collective notes.</li>\r\n<li>To avoid issues with SPAU and/or note assisstant please implement the following notes in advance</li>\r\n<ul>\r\n<li>Note 1679201 - Short dump OPEN_DATASET_NO_AUTHORITY in SPAU/SPDD</li>\r\n<li>Note 2017638 - Corrections to SAP_UPDATE_DBDIFF and RS_BW_POST_MIGRATION</li>\r\n<li>Note 2065063 - UPD: Collective run can't be triggered; update requests appear in status V1 processed</li>\r\n<li>Note 2067561 - Cannot change component type with SNOTE</li>\r\n<li>Note 2069795 - Avoid deadlock on database table DDFTX</li>\r\n<li>Note 2073470 - RSWWWIDE: F1 Help for checkbox \"Top-Level Work Items Only\"</li>\r\n<li>Note 2077553 - Obsolete version Implemented notes - Automatic adjust. for manual activities in SPAU</li>\r\n<li>Note 2077587 - Change Matching Interfaces using SNOTE</li>\r\n<li>Note 2087437 - SNOTE: Deletion -&gt; Remove texts from generated programs</li>\r\n<li>Note 2088226 - SNOTE: Change to object with original in other system cannot be included in request</li>\r\n<li>Note 2092021 - SPAU: Enhancement Framework objects also appear in \"Other objects\"</li>\r\n<li>Note 2092114 - STRUST certificate response improvements</li>\r\n<li>Note 2099092 - SE95: Display of objects in notes</li>\r\n</ul>\r\n</ul>\r\n<li>HANA BW 7.40 with Support Package &gt;= SP05</li>\r\n<ul>\r\n<li><em>Using BW 7.40 SP05 or higher: It is necessary to use HANA SP07 or higher in combination with BW 7.40 SP05 or higher. Revision 70 is the minimum revision for HANA SP07. We recommend to always use the latest available HANA SP07 revision.</em></li>\r\n<li>Kernel: We recommend to use at least PL&#160;41 (741_REL)</li>\r\n<li>We&#160;<span style=\"text-decoration: underline;\">strongly recommend</span> to implement BW SP07 to your systems as there are many fixes included in this Support Package.</li>\r\n<li>For your convenience we have created a note collecting all important notes which are created after the final assembly of the Support Package has started. Please see note 1949273 for details about notes to be implemented after applying SP07 (and SP05, SP06). This note contains also information about Note Assistant enabled collective notes.</li>\r\n</ul>\r\n<li>HANA BW 7.40 with Support Package <strong>&lt;</strong> SP05</li>\r\n<ul>\r\n<li>HANA Revision: Please use SAP HANA SP06 (Revision 63) as minimum level for SAP NW BW 7.40 on HANA. We recommend to always use the latest available revision/maintenance revision.</li>\r\n<li>Kernel: We recommend to use at least PL 11(740_REL)</li>\r\n<li>Add-on Compatibilty SAP NW 7.40 please see note 1826531.</li>\r\n</ul>\r\n</ul>\r\n<p>4.4 other information</p>\r\n<ul>\r\n<li>For SAP SEM 747 / SEM-BW 747 see also notes: 1602357, 1491922</li>\r\n<li>Add-on Compatibility Suite on HANA: 1812713</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">5. Information relevant for SAP NW BW 7.31 on HANA<br /><br /></span></strong>We recommend importing Support Pack Stack&#160;09 or higher for your SAP NetWeaver (NW) BW Installation or Upgrade&#160;(ABAP) before migration/ after installation. Support Package Stack 05 is the minimum level required for migration. With Support Package Stack 04 migration is also possible after implementing important notes (see SAP Note 1712334), however, importing this large amount of notes is cumbersome and error-prone. Support package Stack 03 is not supported. If you plan a migration project we strongly recommend including&#160;the update&#160;to at least SPS 09 in the plan.<br />We recommend to always use the stack kernel fitting to your SP or a higher Patch level of the kernel.<br /><strong>ATTENTION!</strong> After Applying NW 7.30 SP09 / NW 7.31 SP07 you need to add the SQL privileges mentioned in note 1776186 to your SAP&lt;SID&gt; User on HANA if your ABAP System is running on kernel 721_EXT and your HANA DB is distributed(scale out).</p>\r\n<p>Prerequisite for installation&#160;using latest Kernel media 51045124 (BW 7.30) or 51045125 (BW 7.31) containing optimized R3load:&#160;Please ensure that you update to the latest kernel version after installation has been finished.</p>\r\n<p>The following source databases are supported for the migration(minimum releases):<br />Oracle 11.2<br />IBM DB2 LUW 9.7<br />MaxDB 7.9<br />MS SQL server 2008<br />DB2 for i61, 7.1<br />DB2 for z/OS V9, V10<br />SAP Sybase ASE 15.7<br /><br /><br />General:<br /><br />For system copy of systems based on SAP NetWEaver 7.3 EhP1 please check the \"SAP HANA database - specific Topics section\" in note 1620515.<br /><br />For application server IBM i again HANA DB please check note 1713523.<br /><br /><strong>The upgrade procedure for \"SAP NW BW 7.30 on HANA DB\" to \"SAP NW BW 7.31 on HANA DB\" is supported as of SL Toolset 1.0 SP5. For further information please have a look at notes 1702177 and 1680769<span style=\"text-decoration: underline;\">.</span></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">6. Information relevant for SAP NW BW 7.30 on HANA</span></strong><br /><br />We recommend importing Support Pack Stack 10 or higher for your SAP NetWeaver (NW) BW Installation (ABAP) before migration/ after installation. Support Package Stack 08 is the minimum level required for migration. With Support Package Stack 07 migration is also possible after implementing important notes (see SAP Note 1712334), however, importing this large amount of notes is cumbersome and error-prone. If you plan a migration project we strongly recommend including&#160;the update&#160;to at least SPS10 in the plan. We recommend to use at least the shipped stack kernel or higher for the migration.<br />We recommend to always use the stack kernel fitting to your SP or a higher Patch level of the kernel. <br /><strong>ATTENTION!</strong> After Applying NW 7.30 SP09 / NW 7.31 SP07 you need to add the SQL privileges mentioned in note 1776186 to your SAP&lt;SID&gt; User on HANA if your ABAP System is running on kernel 721_EXTand your HANA DB is distributed(scale out).<br />If you use the operating system RedHat5 for your application server, order the relevant kernel DVD using the material number 51042007 under the component XX-SER-SWFL-SHIP.<br /><br />Prerequisite for installation using latest Kernel media 51045124 (BW 7.30) or 51045125 (BW 7.31) containing optimized R3load: Please ensure that you update to the latest kernel version after installation has been finished.</p>\r\n<p>Detailed information about Migration /Systemcopy using SAP HANA DB with special view on performance improvements in R3Load: see note 1753759</p>\r\n<p>The following source databases are supported for the migration(minimum releases):<br />Oracle 11.2<br />IBM DB2 LUW 9.7<br />MaxDB 7.8<br />MS SQL server 2008<br />DB2 for i61, 7.1<br />DB2 for z/OS V9, V10<br />SAP Sybase ASE 15.7<br /><br />Please check if the following tables are part of the row store in the SAP HANA DB before you start to implement Support Packages or other Add-Ons like Business Content, SEM or BPC to ensure a performant import:<br />DOKIL, TADIR, E070, E071, PAT01. VRSD and SMODILOG.<br />If the tables are not in the row store you can use the SQL Tool which has been delivered with SAP HANA Studio to move the table within schema SAP&lt;SID&gt;:<br />alter table DOKIL row;<br />The tables can stay in the row store.<br />You can find alternative options and an exact description of the change process in SAP Note 1660125.<br />See SAP Note 1659383 for more information about which tables are located in the row store.<br /><br /><span style=\"text-decoration: underline;\">ABAP Add-ons:</span></p>\r\n<p>For now, all products and components of the SAP Business Suite are not released for BW 7.30 SP5(and higher) powered by SAP HANA DB.<br /><br />This restriction does not apply to the following:</p>\r\n<ul>\r\n<ul>\r\n<li>\r\n<p>In the area of SEM-BW, the component SEM-BCS is available on releases 6.34, 7.36 and higher, with support of productive use of SAP Netweaver Business Warehouse 7.30, 7.31 and higher powered by SAP HANA. For more details, see SAP Note 1648413.</p>\r\n</li>\r\n<li>For general Information about ERP Software Components and HANA Support please see note:&#160;1970828 - SAP NetWeaver Systems Containing SAP ERP Software Components: HANA DB support</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The components VIRSANH and GRCPINW of the SAP GRC Access Control Solution are released.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The components ST-A/PI and ST-PI are released.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Notes Management 10.0 HANA compatible. Details can be found in note 1783857</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SBOP PC 10.0 FOR SAP NW is released. For more details please see SAP note 1676242. BPC will offer BW 7.31 on HANA support end of Jan. 2013.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SPM 3.0 is supported on SAP BW 7.30 on HANA. For details please see SAP note 1718152</li>\r\n</ul>\r\n</ul>\r\n<p><br />Business Content Add-On:</p>\r\n<ul>\r\n<li>SAP POS Data Management on SAP Business Warehouse powered by SAP HANA</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In the BI Content Releases 7.35 and 7.36 on SAP Business Warehouse powered by SAP HANA (SAP BW 7.3 SP05 and HANA 1.0 SP03), there may be restrictions with regard to the processing, compression, and size of the TLOG data table for users of the POS Inbound Processing Engine (PIPE) as the central component of SAP POS Data Management.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For more information, see SAP Note 1648861 \"SAP POS Data Management on SAP Business Warehouse powered by SAP HANA\".</li>\r\n</ul>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">7. Additional important SAP Notes and known problems</span></strong><br />-----<br />A dump occurs while uploading data via DTP on an SLS 11 SP1 Linux Server due to a wrong timestamp.<br />To correct this error, install Linux kernel version ********* or higher<br />You can check which kernel version is installed on your system using the command \"cat /proc/version\".<br />-----<br />There is the following restriction with regard to the use of Embedded Search with TREX. At the moment this functionality is not released for the SAP HANA database. It is also not released if you want to have an existing TREX alongside it. This restriction has been removed: <a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw74/helpdata/en/3c/3d342317d24b7693333ea1d49e31a9/content.htm\">http://help.sap.com/saphelp_nw74/helpdata/en/3c/3d342317d24b7693333ea1d49e31a9/content.htm</a><br />-----<br />Please see note 1657994 for NW 7.30 SP06 passwords for SPAM update.<br />-----<br />Please have also a look at note 1678047 COMPUTE_INT_PLUS_OVERFLOW during activation.<br />-----</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-AWB (Data Warehousing Workbench)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024795)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024795)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001600929/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001600929/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "776186", "RefComponent": "MM-PUR-GF-EKBE", "RefTitle": "ME87: DBIF_RSQL_INVALID_RSQL", "RefUrl": "/notes/776186"}, {"RefNumber": "2036554", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1 SPS08 Revision 081.00", "RefUrl": "/notes/2036554"}, {"RefNumber": "2021789", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1.0 Revision and Maintenance Strategy", "RefUrl": "/notes/2021789"}, {"RefNumber": "1951491", "RefComponent": "BC-DB-DB6", "RefTitle": "Minimal DB system platform requirements for SAP NetWeaver 7.4 SP08", "RefUrl": "/notes/1951491"}, {"RefNumber": "1948334", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database Update Paths for SAP HANA Maintenance Revisions", "RefUrl": "/notes/1948334"}, {"RefNumber": "1935871", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1 SPS06 Revisions 069.01 - 069.07", "RefUrl": "/notes/1935871"}, {"RefNumber": "1921675", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Platform 1.0 SPS 07 Release Note", "RefUrl": "/notes/1921675"}, {"RefNumber": "1783857", "RefComponent": "EPM-NM", "RefTitle": "SAP Notes Management: Support Package Stack overview", "RefUrl": "/notes/1783857"}, {"RefNumber": "1775293", "RefComponent": "HAN-DB", "RefTitle": "Migration or system copy to SAP HANA using the latest software provisioning manager (SWPM 1.0)", "RefUrl": "/notes/1775293"}, {"RefNumber": "1753759", "RefComponent": "HAN-DB", "RefTitle": "Migration / system copy to SAP HANA SWPM 1.0 SP0", "RefUrl": "/notes/1753759"}, {"RefNumber": "1738258", "RefComponent": "BC-INS-MIG", "RefTitle": "System Copy for Systems Based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1738258"}, {"RefNumber": "1718152", "RefComponent": "EPM-SA", "RefTitle": "Spend Performance Management on SAP BW 7.30 Hana", "RefUrl": "/notes/1718152"}, {"RefNumber": "1714013", "RefComponent": "EPM-BPC-NW", "RefTitle": "BPC 7.53 NW on SAP BW 7.3 powered by HANA DB", "RefUrl": "/notes/1714013"}, {"RefNumber": "1712334", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Important Notes for SAP BW powered by HANA on SP7", "RefUrl": "/notes/1712334"}, {"RefNumber": "1702177", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1702177"}, {"RefNumber": "1681435", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1681435"}, {"RefNumber": "1672940", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SAP SEM-CPM 6.34 on SAP BW 7.3 powered by HANA DB", "RefUrl": "/notes/1672940"}, {"RefNumber": "167242", "RefComponent": "MM-PUR-GF-EKBE", "RefTitle": "Inconsistency in purchase order history material document", "RefUrl": "/notes/167242"}, {"RefNumber": "1667731", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1667731"}, {"RefNumber": "1660886", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1660886"}, {"RefNumber": "1660125", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA database: Table consistency check", "RefUrl": "/notes/1660125"}, {"RefNumber": "1659383", "RefComponent": "HAN-DB", "RefTitle": "RowStore list for SAP Netweaver 7.30/7.31 on SAP HANA database", "RefUrl": "/notes/1659383"}, {"RefNumber": "1651394", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1651394"}, {"RefNumber": "1648861", "RefComponent": "BW-BCT-ISR-PIP", "RefTitle": "SAP POS DM on SAP Business Warehouse powered by SAP HANA", "RefUrl": "/notes/1648861"}, {"RefNumber": "1648413", "RefComponent": "FIN-SEM-BCS", "RefTitle": "SAP SEM-BCS on SAP BW powered by HANA DB", "RefUrl": "/notes/1648413"}, {"RefNumber": "1620515", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1620515"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2427579", "RefComponent": "EPM-BPC-NW", "RefTitle": "Compatibility between HANA 2.0 and BPC NW 10.0 and BPC NW 10.1?", "RefUrl": "/notes/2427579 "}, {"RefNumber": "3346502", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP38 - covers no longer supported Operating Systems", "RefUrl": "/notes/3346502 "}, {"RefNumber": "3220901", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP35 - covers no longer supported CPU and operation system versions", "RefUrl": "/notes/3220901 "}, {"RefNumber": "2972853", "RefComponent": "HAN-DB-ENG-TRX", "RefTitle": "Enhancement of TREX_EXT function modules for Netweaver 7.3x + HANA2", "RefUrl": "/notes/2972853 "}, {"RefNumber": "1680045", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 (recommended: SWPM 1.0 SP40)", "RefUrl": "/notes/1680045 "}, {"RefNumber": "2463467", "RefComponent": "EPM-BPC-BW4", "RefTitle": "SAP Business Planning and Consolidation 11.0 SP01, version for SAP BW/4HANA Central Note", "RefUrl": "/notes/2463467 "}, {"RefNumber": "2465366", "RefComponent": "BW-PLA-IP", "RefTitle": "Limited Performance by planning on direct update DSO in SAP BW  7.40", "RefUrl": "/notes/2465366 "}, {"RefNumber": "2420699", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Release of SAP HANA Database 2.0 for older SAP Versions", "RefUrl": "/notes/2420699 "}, {"RefNumber": "1661202", "RefComponent": "HAN-LM-INS", "RefTitle": "Support multiple applications one SAP HANA database / tenant DB", "RefUrl": "/notes/1661202 "}, {"RefNumber": "2292232", "RefComponent": "EPM-BPC-NW", "RefTitle": "SAP BusinessObjects Planning & Consolidation 10.1 NW SP11 Central Note", "RefUrl": "/notes/2292232 "}, {"RefNumber": "2329005", "RefComponent": "BC-UPG-PRP", "RefTitle": "Minimal DB system platform requirements for NW AS ABAP 7.51 INNOVATION PKG", "RefUrl": "/notes/2329005 "}, {"RefNumber": "2178742", "RefComponent": "EPM-BPC-NW", "RefTitle": "SAP Business Planning & Consolidation 10.1 on SAP BW 7.5 SP00 Central Note", "RefUrl": "/notes/2178742 "}, {"RefNumber": "1666670", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP BW powered by SAP HANA - Landscape Deployment Planning", "RefUrl": "/notes/1666670 "}, {"RefNumber": "2158828", "RefComponent": "BC-UPG-PRP", "RefTitle": "Minimal database system platform requirements for SAP NetWeaver 7.5", "RefUrl": "/notes/2158828 "}, {"RefNumber": "2095862", "RefComponent": "EPM-BPC-NW", "RefTitle": "SAP Business Planning & Consolidation 10.1 NW SP06 Central Note", "RefUrl": "/notes/2095862 "}, {"RefNumber": "2103585", "RefComponent": "EPM-BPC-NW", "RefTitle": "Product Component Matrix for SAP Business Planning & Consolidation 10.1, version for Net Weaver 7.40/7.50, S/4HANA and Business Planning & Consolidation 11.x, 2021, version for BW/4 HANA", "RefUrl": "/notes/2103585 "}, {"RefNumber": "2121768", "RefComponent": "HAN-DB", "RefTitle": "Considerations with SAP HANA multitenant database containers, SAP BW, and SAP BW/4HANA", "RefUrl": "/notes/2121768 "}, {"RefNumber": "2023281", "RefComponent": "EPM-BPC-NW", "RefTitle": "SAP Business Planning & Consolidation 10.1 NW SP04 Central Note", "RefUrl": "/notes/2023281 "}, {"RefNumber": "2061669", "RefComponent": "BW-BCT-CO-PC", "RefTitle": "DU HCO_BI_CONT_CO_CFS import error", "RefUrl": "/notes/2061669 "}, {"RefNumber": "1943931", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation or upgrade for the ABAP add-on BI_CONT or BI_CONT_XT 757", "RefUrl": "/notes/1943931 "}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "1951491", "RefComponent": "BC-DB-DB6", "RefTitle": "Minimal DB system platform requirements for SAP NetWeaver 7.4 SP08", "RefUrl": "/notes/1951491 "}, {"RefNumber": "1914052", "RefComponent": "BC-UPG-PRP", "RefTitle": "Minimal HANA and MaxDB platform requirements for NetWeaver 7.40 Support Packages", "RefUrl": "/notes/1914052 "}, {"RefNumber": "1775293", "RefComponent": "HAN-DB", "RefTitle": "Migration or system copy to SAP HANA using the latest software provisioning manager (SWPM 1.0)", "RefUrl": "/notes/1775293 "}, {"RefNumber": "1923480", "RefComponent": "EPM-BPC-NW", "RefTitle": "Software component CPMBPC 7.54 on SAP BW 7.40 powered by HANA", "RefUrl": "/notes/1923480 "}, {"RefNumber": "1906096", "RefComponent": "EPM-BPC-NW", "RefTitle": "BPC 7.50/7.54 NW on SAP BW 7.31 powered by HANA", "RefUrl": "/notes/1906096 "}, {"RefNumber": "1753759", "RefComponent": "HAN-DB", "RefTitle": "Migration / system copy to SAP HANA SWPM 1.0 SP0", "RefUrl": "/notes/1753759 "}, {"RefNumber": "1648413", "RefComponent": "FIN-SEM-BCS", "RefTitle": "SAP SEM-BCS on SAP BW powered by HANA DB", "RefUrl": "/notes/1648413 "}, {"RefNumber": "1738258", "RefComponent": "BC-INS-MIG", "RefTitle": "System Copy for Systems Based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1738258 "}, {"RefNumber": "1659383", "RefComponent": "HAN-DB", "RefTitle": "RowStore list for SAP Netweaver 7.30/7.31 on SAP HANA database", "RefUrl": "/notes/1659383 "}, {"RefNumber": "1620515", "RefComponent": "BC-INS-MIG", "RefTitle": "OBSOLETE: System Copy for Syst. Based on SAP NW 7.3 EHP1", "RefUrl": "/notes/1620515 "}, {"RefNumber": "1712334", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Important Notes for SAP BW powered by HANA on SP7", "RefUrl": "/notes/1712334 "}, {"RefNumber": "1783857", "RefComponent": "EPM-NM", "RefTitle": "SAP Notes Management: Support Package Stack overview", "RefUrl": "/notes/1783857 "}, {"RefNumber": "1718152", "RefComponent": "EPM-SA", "RefTitle": "Spend Performance Management on SAP BW 7.30 Hana", "RefUrl": "/notes/1718152 "}, {"RefNumber": "1660125", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA database: Table consistency check", "RefUrl": "/notes/1660125 "}, {"RefNumber": "1714013", "RefComponent": "EPM-BPC-NW", "RefTitle": "BPC 7.53 NW on SAP BW 7.3 powered by HANA DB", "RefUrl": "/notes/1714013 "}, {"RefNumber": "1660886", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Important Notes for SAP BW powered by HANA on SP6", "RefUrl": "/notes/1660886 "}, {"RefNumber": "167242", "RefComponent": "MM-PUR-GF-EKBE", "RefTitle": "Inconsistency in purchase order history material document", "RefUrl": "/notes/167242 "}, {"RefNumber": "1672940", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SAP SEM-CPM 6.34 on SAP BW 7.3 powered by HANA DB", "RefUrl": "/notes/1672940 "}, {"RefNumber": "1651394", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Important Notes for SAP BW powered by HANA", "RefUrl": "/notes/1651394 "}, {"RefNumber": "1648861", "RefComponent": "BW-BCT-ISR-PIP", "RefTitle": "SAP POS DM on SAP Business Warehouse powered by SAP HANA", "RefUrl": "/notes/1648861 "}, {"RefNumber": "776186", "RefComponent": "MM-PUR-GF-EKBE", "RefTitle": "ME87: DBIF_RSQL_INVALID_RSQL", "RefUrl": "/notes/776186 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73119", "URL": "/supportpackage/SAPKW73119"}, {"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74016", "URL": "/supportpackage/SAPKW74016"}, {"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74011", "URL": "/supportpackage/SAPKW74011"}, {"SoftwareComponentVersion": "SAP_BW 750", "SupportPackage": "SAPK-75004INSAPBW", "URL": "/supportpackage/SAPK-75004INSAPBW"}, {"SoftwareComponentVersion": "SAP_BW 750", "SupportPackage": "SAPK-75005INSAPBW", "URL": "/supportpackage/SAPK-75005INSAPBW"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}