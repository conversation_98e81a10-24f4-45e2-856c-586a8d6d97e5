{"Request": {"Number": "492828", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 217, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015165352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000492828?language=E&token=EA91BB8A3BA33803FCACBBD37E5A2A15"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000492828", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000492828/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "492828"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2005-07-18"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-MM-IM"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Inventory Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Materials Management", "value": "BW-BCT-MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Inventory Management", "value": "BW-BCT-MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "492828 - Determining the transaction key for 2LIS_03_BF + 2LIS_03_UM"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This consulting note describes how to determine the transaction key (the BWVORG field in the plug-in, 0PROCESSKEY InfoObject in the BW)) for the extraction of material movements and revaluations (2LIS_03_BF and 2LIS_03_UM extractors) in the R/3 plug-in system.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MC03BF0, MC03UM0</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The following transaction keys are available in the standard system (see transaction MCB0) for the MM application component and Application 03 (Inventory Controlling).<br /></p> <b>Extractor 2LIS_03_BF</b><br /> <p>Transaction Description Receipt/Issue<br />000 Misc.<br />001 Goods receipt/vendor Receipt<br />004 Matl transfer posting/inbound Receipt<br />005 Stock adjustment inventory + Receipt<br />006 Stock adjustment other + Receipt<br />010 Receipt from stock transfer Receipt<br />issues Issue<br />101 Returns/vendor Issue<br />104 Matl transfer posting/outbound Issue<br />105 Stock adjustment inventory - Issue<br />106 Stock adjustment inventory - Issue<br />110 Issue from stock transfer Issue<br /></p> <b>Extractor 2LIS_03_UM</b><br /> <p>Transaction Description<br />revaluation +<br />051 Revaluation/price change +<br />052 Revaluation/invoice verification + + +<br />revaluation -<br />151 Revaluation/price change -<br />152 Revaluation/invoice verification - - -<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>Extractor 2LIS_03_BF</b><br /> <OL>1. Determine whether the transferred row of the 2LIS_03_BF extractor corresponds to a receipt or an issue.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Receipt: <UL><UL><LI>The debit/credit indicator of the movement type = DEBIT (T156-SHKZG = S) and the movement type is not defined as a reversal movement type (T156-XSTBW = BLANK).</LI></UL></UL> <UL><UL><LI>The debit/credit indicator of the movement type = CREDIT (T156-SHKZG = H) and the movement type is defined as a reversal movement type (T156-XSTBW = X).<br /><br />In this case, the transferred row is also determined as a receipt. However, the ROCANCEL = X (BW: 0STORNO) indicator is set. For this reason, the fields of the 2LIS_03_BF extractor, where the STORNO = X (ROOSFIELD table, STORNO field) reversal indicator is set, are transferred with a minus sign. This means that key figures in the BW are also updated with a minus sign (subtracted) if they are based on the fields described above.</LI></UL></UL> <UL><UL><LI>For more information, see note 352344.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Issue: <UL><UL><LI>The debit/credit indicator of the movement type = CREDIT (T156-SHKZG = H) and the movement type is not defined as a reversal movement type (T156-XSTBW = BLANK).</LI></UL></UL><UL><UL><LI>The debit/credit indicator of the movement type = DEBIT (T156-SHKZG = S.) and movement type is defined as a reversal movement type (T156-XSTBW = X).<br /><br />In this case, the transferred row is also determined as an issue. However, the ROCANCEL = X (BW: 0STORNO) indicator is set. For this reason, the fields of the 2LIS_03_BF extractor, where the STORNO = X (ROOSFIELD table, STORNO field) reversal indicator is set, are transferred with a minus sign. This means that key figures in the BW are also updated with a minus sign (subtracted) if they are based on the fields described above.</LI></UL></UL> <UL><UL><LI>For more information, see note 352344.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exceptions: <UL><UL><LI>Movement type 122 is defined as an issue even though it was defined as a reversal movement type in the T156 table. Movement type 123 is accordingly defined as an issue (with a minus sign).</LI></UL></UL><UL><UL><LI>If the movement type of the posting is flagged as a stock transfer movement type (table TMCA field UMLKZ = X), the system creates an additional line item as an offsetting entry (MSEG-XAUTO = X). This receives the same movement type, however the opposite debit/credit indicator. In this case, the debits/credit indicator does not correspond to the entry in the T156 table because it is determined during the creation of the material document.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following are possible transaction keys for a receipt: 000, 001, 004, 005, 006, 010 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;000, 001, 004, 005, 006 and 010 100, 101, 104, 105, 106, 110 100, 101, 104, 105, 106, 110<br /> <OL>2. Transaction key 004 or 104</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transaction key 004 or 104 is determined if the posting movement type is flagged as a stock transfer movement type (TMCA table, UMLKZ = X field) and an article transfer posting or material transfer posting exists (MSEG-UMMAT &lt; &gt; MSEG-MATNR). <OL>3. Transaction key 010 or 110</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transaction key 010 or 110 is determined if the posting movement type is flagged as a stock transfer movement type (TMCA table, UMLKZ = X field) and a stock transfer exists between different plants (MSEG-UMWRK &lt; &gt; MSEG-WERKS). <OL>4. Transaction key 005 or 105</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the posting movement type corresponds to a physical inventory (TMCA table, INVKZ = X field). <OL>5. Transaction key 006 or 106</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the posting movement type corresponds to a stock adjustment (TMCA table, KORR = X field). <OL>6. Transaction key 001 or 101</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the movement type with reference to a vendor (MSEG-LIFNR &lt;&gt; BLANK) and does not correspond to a stock transfer movement type, a physical inventory or a stock adjustment. <OL>7. Transaction key 000 or 100</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;All remaining goods movements.<br /> <b>Extractor 2LIS_03_UM</b><br /> <OL>1. Transaction key 51</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revaluation through a material price change (for example, with transaction MR21) and the debit/credit indicator = DEBIT. <OL>2. Transaction key 151</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revaluation through a material price change (for example, with transaction MR21) and the debit/credit indicator = CREDIT. <OL>3. Transaction key 52</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revaluation through invoice verification and the debit/credit indicator = DEBIT. <OL>4. Transaction key 152</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revaluation through invoice verification and the debit/credit indicator = CREDIT. <OL>5. Transaction key 50</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revaluations not caused by a material price change or invoice and the debit/credit indicator = DEBIT. <OL>6. Transaction key 150</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revaluations not caused by a material price change or invoice and the debit/credit indicator = CREDIT. <p></p> <b>Example:</b><br /> <UL><LI>Goods receipt (ten pieces of material 4711) with movement type 561. Then, goods issue (two pieces of material 4711) with movement type 562.<br />Key figure 'Overall stock adjustment (positive)/BasME' = 0 (see InfoCube 0CP_IC_C1).<br /></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Movement type 561 is processed as a receipt since it is defined as a regular 'debit' movement type (T156-SHKZG = S. and T156-XSTBW = BLANK).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The movement type corresponds to a stock adjustment (TMCA table, KORR = X field).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; transaction key = 006<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Movement type 562 is also processed as a receipt since it is defined as a reversed 'credit' movement type (T156-SHKZG = H and T156-XSTBW = X).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; transaction key = 006<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The ROCANCEL = X field is also set.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Key figure =&gt; 'Overall stock adjustment (positive)/BasME' = 8.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-IM (Inventory Management)"}, {"Key": "Transaction codes", "Value": "MR21"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D024084)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000492828/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000492828/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000492828/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000492828/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000492828/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000492828/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000492828/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000492828/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000492828/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "538770", "RefComponent": "XX-CSC-BR", "RefTitle": "Movement Types 861-864 in Table TMCA", "RefUrl": "/notes/538770"}, {"RefNumber": "399929", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Update of extractor 2LIS_03_BF different", "RefUrl": "/notes/399929"}, {"RefNumber": "382779", "RefComponent": "BC-BW", "RefTitle": "Cancellation field in the DataSource maintenance RSA6", "RefUrl": "/notes/382779"}, {"RefNumber": "352344", "RefComponent": "BW-BCT-MM", "RefTitle": "Process key + reversals in Inventory Management", "RefUrl": "/notes/352344"}, {"RefNumber": "1402894", "RefComponent": "BW-BCT-ISR", "RefTitle": "Determining transaction keys for component IS-R", "RefUrl": "/notes/1402894"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1402894", "RefComponent": "BW-BCT-ISR", "RefTitle": "Determining transaction keys for component IS-R", "RefUrl": "/notes/1402894 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "382779", "RefComponent": "BC-BW", "RefTitle": "Cancellation field in the DataSource maintenance RSA6", "RefUrl": "/notes/382779 "}, {"RefNumber": "538770", "RefComponent": "XX-CSC-BR", "RefTitle": "Movement Types 861-864 in Table TMCA", "RefUrl": "/notes/538770 "}, {"RefNumber": "352344", "RefComponent": "BW-BCT-MM", "RefTitle": "Process key + reversals in Inventory Management", "RefUrl": "/notes/352344 "}, {"RefNumber": "399929", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Update of extractor 2LIS_03_BF different", "RefUrl": "/notes/399929 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}