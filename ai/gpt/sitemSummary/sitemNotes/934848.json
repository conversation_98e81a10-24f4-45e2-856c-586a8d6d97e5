{"Request": {"Number": "934848", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 361, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016078342017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=1B1CFFFCFCD06B5B5DD29B343054622D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "934848"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 54}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.12.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-TCT"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW Only - Technical Content and BW Administration Cockpit"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW Only - Technical Content and BW Administration Cockpit", "value": "BW-BCT-TCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-TCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "934848 - Collective note: (FAQ) BI Administration Cockpit"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to install and use the BI Administration Cockpit and you need information on prerequisites and installation steps to be performed.<br /><br /><br /><strong>Email Notification</strong><br />You can subscribe to this note if an Email-address is added to your profile.Go to http://service.sap.com -&gt; MyProfile (in the header)-&gt; My personal data-&gt; e-mail.To subscribe to this note, click on the subscribe link in the top line of the note display.<br />If the note is changed you will get an email notification.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Technical Content,BI Content, BI Administrator, Monitoring, Performance, RSDDSTAT, BI Statistics, BW Statistic, Runtime, ST03, ST03N, SAP_BW_TCONT, SAP_BW_BI_ADMINISTRATOR, 0BWTC, 0TCT,message_x,<br />RSDDSTATOBJLEVEL.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />1.Software component SAP_BW has to be installed with minimum release of NW 7.0x.<br /><br />2.Software component BI_CONT has to be installed with 7.02 or 7.03 or 7.04 or 7.05 or&#160;7.06&#160;release on your SAP NetWeaver 2004s BI system .<br />If your sysem is on NW 7.30 or higher release, then BI_CONT Software Component is not required for installation of BI Admin Cockpit.<br /><br />3.Software component BW_AC is required if SAP_BW Software Component is on 7.03 release.<br /><br />Implement the latest Support Packages for BI ABAP, BI JAVA and BI CONT.<br /><br />If your system is not on the latest Support Package but still meeting the prerequisites as mentioned in the above points 1,2 &amp; 3, then refer to the section \"Important Corrections\" below which contains a list of required notes as per Support Package for successful behavior of BI admin Cockpit.</p>\r\n<ul>\r\n<li><strong>If BI Content(BI_CONT) is below 7.03(Release) SP7(Level) or on 7.02, Select only myself system in the TX:RSOR Source System Assignment(SHIFT+F7) before running the automatic installation report.</strong></li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>Follow the steps of this note only if your system is on NW 7.00 or NW 7.01 or NW 7.02 release. To know the procedure to setup BI Administration Cockpit on NW 7.30 system, please refer note 1543092.</strong><br /><br /><strong>Installation: General Comments</strong><br /><br />- Please be aware that as of SAP NetWeaver 2004s BI transaction ST03 is based on the Technical Content InfoProviders (opposed to the Technical Content tables of prior releases). Therefore, using transaction ST03 for BI Monitoring requires that the technical content should be activated and populated periodically with statistics data.<br /><br />- If you plan to use the Technical Content only (without using the additional functionality of the BI Administration Cockpit in the SAP NetWeaver Portal) steps 6 to 8 of the proceeding become optional.<br />Please refer to point 10 in that case.<br /><br />- For details and for performing (most of) the following&#160;&#160;implementation steps please call the implementation guide (IMG) in the SAP NetWeaver BI system (transaction SPRO) using the following path: SAP NetWeaver -&gt; Business Intelligence -&gt; Settings for BI Content -&gt;&#160;&#160;Business Intelligence -&gt; BI Administration Cockpit.<br /><br /><strong>Installation: Steps to be performed (see IMG and documentation for further details)</strong><br /><br />Step 5 need not be performed if note 1074455 is applied or BI_CONT is on 7.03 SP7 or above. If BI Content(BI_CONT) of your BI system is on 7.03 SP8 or above this, Step 4 can be ignored.<br /><br />1. Activate the new technical content in SAP NetWeaver BI<br /><br />We recommend using the automatic installation report which is available in the Implementation Guide (transaction SPRO). For prerequisites and additional info, please refer to note 965386 \"Activating the technical content for the BI admin cockpit\".<br /><br />2. Assign the BI Administrator Role<br />Please assign BI administrators (users) in transaction SU01 to the role SAP_BW_BI_ADMINISTRATOR.<br /><br />3. Check Updating of Statistics<br />Calling transaction RSDDSTAT, you can check the updating of statistics for InfoProviders, Queries and Data Transfer Process. Please consider that the system default is 'X' (= statistics turned on) and detail level '2' (= all details). If for certain objects no logging of performance statistics is needed please switch them off in this transaction in order to avoid unnecessary load on statistics data storage and analysis.<br /><br />4.Schedule Process Chains.<br />Please schedule the technical content process chains (0TCT*) in transaction RSPC. \"INIT\" process chains need to be scheduled once and \"DELTA\" process chains should be scheduled periodically.<br /><br />5.Activate direct access for virtual providers.<br />Please assign source system for all the virtual providers (0TCT_VC*).<br /><br />6. Connect BI System and Portal<br />With these IMG activities, you have to assign the portal in which you wish to call the BI Administration Cockpit to the BI system.<br /><br />7. Activate Business Package 'BI Administration 1.0'<br />Download the Business Package BI Administration 1.0 from the Portal Content Portfolio on http://sdn.sap.com or from http://service.sap.com/swdc and import it into your SAP NetWeaver 2004s Portal (minimum: Support Package Stack 5). Then assign the roles 'System Administrator' and 'BI Administrator'(or any Administrator role which contain these roles as a subset) to your BI administrator users in the portal.<br /><br />8. Set Up Call to BI Administration Cockpit<br />If you want to call the BI Administration Cockpit from BI transactions such as the Data Warehousing Workbench, you have to configure in the IMG the call to the BI Administration Cockpit.<br /><br />9. Define Importance (optional)<br />Within this optional IMG activity, you can assign importances to BI objects. You can take into account these importances in technical content queries during analysis (for example for filtering or sorting).<br /><br />10. Using the Technical Content without the SAP&#160;&#160;NetWeaver Portal.<br />Incase you wish to see the statistics as seen in the portal, the technical web templates could be run in the backend. It will produce the same results.<br />The statistics for short term trends, long term trends,total runtimes, deviations in runtimes of each of the following can be obtained from th tempaltes mentioned.<br />A)<strong>Query Runtime Statistics: </strong><br />A.1 - <strong>BI Application Statistics</strong> -&#160;&#160;0TPLI_0TCT_MC01_Q0111-114.<br />A.2 - <strong>BI Application object Statistics</strong> - 0TPLI_0TCT_MC01_Q0121-124.<br />A.3 - <strong>Infoprovider Statistics</strong> - 0TPLI_0TCT_MC01_Q0131-134.<br /><br />B)<strong>Data Load Statistics</strong><br />B.1 - <strong>Process Chain Statistics</strong>&#160;&#160;0TPLI_0TCT_MC21_Q0101-104.<br />B.2 - <strong>Process Statistics</strong> - 0TPLI_0TCT_MC21_Q0111-114.<br />B.3 - <strong>Infopackage Statistics</strong> - 0TPLI_0TCT_MC23_Q101-104.<br />B.4 - <strong>DTP Statistics</strong> -&#160;&#160;0TPLI_0TCT_MC22_Q101-104.<br /><br />C)<strong><strong>Data Load Status</strong></strong><br />The process status,process chain status,Master data request status, Infocube request status, Datastore object request status&#160;&#160;can be obtained from the following web templates.<br />C.1 - <strong>Process Status</strong> - 0TPLI_0TCT_MC12_Q0100,0TPLI_0TCT_MC12_Q0110.<br />C.2 - <strong>BI Object request Status</strong> - 0TPLI_0TCT_MC11_Q0120,0TPLI_0TCT_MC11_Q0130,0TPLI_0TCT_MC11_Q0140.<br />C.3 - <strong>Infoprovider request Status</strong> - 0TPLI_0TCT_MC11_Q0120, 0TPLI_0TCT_MC11_Q0130,0TPLI_0TCT_MC11_Q0140.</p>\r\n<p><strong><strong>Running the Technical Content and the BI Administration Cockpit</strong></strong></p>\r\n<p><br />Please be aware of the fact that query runtime statistics will generate high statistics data load on the BI Statistics Tables. Depending on the (customized) detail statistics level 20 -80&#160;&#160;records may be written per navigation step to the BI Statistics Tables. Therefore, loading query statistics data to the Technical Content (InfoCube 0TCT_C01 - 0TCT_C03) and deleting original data out of the BI Statistics Tables is urgently required.<br /><br />1. Automatic deletion during data load<br />- Per default, with each delta load for query runtime statistics (DataSource 0TCT_DS01 - 0TCT_DS03), data that is older than 14 days is deleted out of the BI Statistics Tables. This time frame can be customized using the TCT_KEEP_OLAP_DM_DATA_N_DAYS parameter in the RSADMIN table. The deletion mechanism has been changed since BW 7.0 SP16, and the default value has been changed from 14 days to 30 days. Please refer to Note 1095411 for details.<br />- Please see SAP Note 891740 \"Query runtime statistics: Corrections for extractors\" for more information.<br /><br />2. Manual deletion of statistics data<br />- In transaction RSDDSTAT. Dates to be deleted can be selected.<br />- Using program RSDDSTAT_DATA_DELETE</p>\r\n<p><strong>Important Corrections</strong></p>\r\n<p><br />A ) <strong>BI Technology:</strong><br />We recommend to implement atleast the Support Package Stack given in SAP Note 1055581 \"Recommendations for Support Package Stacks for BI&#160;&#160;7.0\" when installing and running the BI Administration Cockpit (section \"General recommendation for Support Package (Stack)\").<br /><br />In addition,the following important corrections should be applied in case your system does not contain the latest Support Package.</p>\r\n<p><strong>Corrections contained in BI ABAP SP12</strong></p>\r\n<p>1007064: Performance improvement for data source 0TCT_DS23<br />1007592: STEPTP bei Query-Laufzeit Statistik nicht korrekt<br />1009408: Master data text and attribute is not loaded for 0TCTBWOBJCT<br />1006468: Full upload data selection not proper for 21, 22, 23<br />1002393: Short dump COMPUTE_BCD_OVERFLOW in 0TCT_DS22 extractor<br />1011811: Deleting statistics data with an 'up to point X' tag<br />1007592: STEPTP incorrect for query runtime statistics</p>\r\n<p><strong>Corrections contained in BI ABAP SP13</strong></p>\r\n<p>1022904: SYSID for 0TCT_MC12 queries is blank<br />1021034: Dump 'GETWA_NOT_ASSIGNED' got for 0TCTBWOBJCT_ATTR<br />1015917: Query statistics are missing from&#160;&#160;0TCT_C0[123],0TCT_VC0[123]<br />1014146: Duration of Infopackage wrong in 0TCT_MC23.<br />1024908: Duration is wrong for 0TCT_MC21,22,23 statistics queries.<br />1025696: Duration is incorrect for DTP Statistics in 0TCT_MC22<br />1025373: Unassigned statistical data in BEx Analyzer 3.x.<br />1031744: Korrektur: 0TCT_MON_IS_01 referenziert 0TCTREQNUM.<br />1031916: Logging for statistics recording terminates.<br />1032151: Displaying default settings for objects in RSDDSTAT<br />1032822: UTC Time Stamp is incorrect in cubes 0TCT_C01,2,3<br />1033636: Unnecessary statistics data is written<br />1035028: 0TCTBWOBJCT_ATTR extracts wrong infoarea for ODSO<br />1036113: 0TCTBWOBJCT_ATTR extracts '0' records for few tlogos <br />1052621: Short dump \"OBJECTS_OBJREF_NOT_ASSIGNED\" while accessing RRI<br />1039071: P13:SDL:Content:InfoPackage activation terminates -RSM 142<br />1029354: P13:DSO activation terminates and changes manage req to red<br />1035477: Handling new templates TLOGO type as BTMP<br /><br /><strong><strong>Corrections contained in BI ABAP SP14</strong></strong><br />1051080: RSDRI runtime statistics contain caller<br /><br /><strong>Corrections contained in BI ABAP SP15</strong><br />1060282: Maximum value for Importance is incorrect in RSTCIMP<br />1060346: Query Counter in Runtime Statistics is wrong<br />1060712: Performance improvement of the queries on 0TCT_MC11<br />1061281: Delta Load fetches wrong no. of Record in 0TCT_C01,2,3<br />1061593: Calday selection is not Proper in 0TCT_DS21,22,23<br />1072693: Statistics data is not written</p>\r\n<p><strong>Corrections contained in BI ABAP SP16</strong></p>\r\n<p>1075331: Extraction performance Improvement for 0TCT_C23<br />1091010: BIAC: DataManager Time null for MDX queries on 0TCT_MC01<br />1089437: Time stamp field blank for the data source 0tct_Ds21<br />1088038: Missing objects for the 0TCTPRCSVAR master data<br />1090762: BIAC: Appearnce of $T in the template 0TPLI_0TCT_MC11_Q0120<br />1095573: Query Count for the Query runtime Statistics is incorrect.<br />1095411: BIAC: Reduction in Data volume in QT Statistcs Tables<br />1093121: Negative times in BI statistics: Event 13052<br />1093755: Statistics counter of OLAP cache not increased<br />1095411: BIAC: Reduction in Data volume in QT Statistcs Table<br /><br /></p>\r\n<p><strong>Corrections contained in BI ABAP SP17</strong></p>\r\n<p>1115531: Error in statistics recording in the Web<br />1117380: Implement statistics event for exit variable/variable screen<br />1123808: Displaying SQL and EXPLAIN in query statistics<br />1131031: BAC:CL_RSTCT_BIRS_OLAP_AGGR_EX - BIOBJ retained in next step</p>\r\n<p><strong>Corrections contained in BI ABAP SP18</strong></p>\r\n<p>1138874: BIAC: User wait times no longer included in Front End times<br />1139693: BIAC: Code for extractor criteria for 0TCT_DS21, DS22 &amp; DS23<br />1139791: Statistiken: Wartezeiten in F4 werden zu OLAP Init gez&#228;hlt<br />1141331: OLAP-Statistiks&#228;tze werden der falschen HandleID zugeordnet<br />1141850: \"Nicht zugeordnet\" Zeit aus Query-RUNTIME herausrechnen.<br />1152320: BIAC:Wrong OLAP time in query Runtime Statistics.<br /><br /><br /><strong>Corrections contained in BI ABAP SP19</strong><br />1228548: BIAC:Process Type X unknown error for 0TCTPRCSCHN_ATTR DS.<br />1229385: BIAC:Duplicate entries for 0TCTPRCVAR_TEXT in PSA.<br />1237091: Enhancements to Query Runtime Statistics of BI Admin Cockpit.<br />1254165: BI Content transfer of InfoCubes/MultiProviders.<br />1299269: BI Application object name does not appear in the output.<br />1302812: BI Application object name does not appear in the output.</p>\r\n<p><strong><strong>Corrections contained in BI ABAP SP20</strong></strong></p>\r\n<p>1264894: PSA table status unavailable.<br /><br /><strong>Corrections contained in BI ABAP SP21</strong><br />1328376: Extractor 0TCTBWOBJCT_ATTR not considering BWOBJCT<br />1317483: BI Application name missing for 3.X Workbook and Templates.<br />1323805: 0TCT_DS01 run time error -COMPUTE_INT_TIMES_OVERFLOW.<br /><br /><br />B ) <strong>BI Content:</strong><br />Corrections to BI Statistics Technical Content objects are shipped with BI_CONT Support Packages. Before installing the BI Statistics Technical Content you should always implement the latest BI_CONT Support Package. If the BI Statistics Technical Content is already installed in your system you might reactivate the Content after having implemented the latest BI_CONT Support Package. Alternatively, the following notes can be applied manually in order to fix known issues on BI Statistic Technical Content objects:<br /><br />1007430: Short dump in transaction ST03N<br />992805 : Overflow during the arithmetical operation (type P)<br />980285 : Error when activating the transer rules for cube 0TCT_VC12<br />979721 : Changes in the transfer rules for 0TCT_C21,0TCT_C22,0TCT_C23<br />984537 : Short dump 'ITAB_ILLEGAL_SORT_ORDER'<br />966964 : 0TCT_DS22 doesn't load DTP Statistics<br />1022666: Timestamps not transferred from 0TCT_VC11 to 0TCT_MC11<br />1018490: 0TCTWHMMAN and 0TCTWHMTFM are empty in 0TCT_C23<br />1039381: Error when activating the content Message no. RS062<br />1035476: Icon not displayed in the report 0TPLI_0TCT_MC11_Q0120<br />1027152: Mapping not proper for 0TCTDBSEL &amp; 0TCTDBTRANS<br />1062363: Icon not displayed in the report 0TPLI_0TCT_MC11_Q0120<br />1065919: Error while installing the Admin Cockpit Process chains<br />1070099: Changes in the transfer rule 0TCT_DS11<br />1074229: Application Log not displayed after Installation Report<br />1081355: Please ignore Queries 0TCT_MC*_Q02* in BI Admin Cockpit<br />1078465: Error during activation of process chain 0TCT_MD_C_FULL_P01<br />1065249: Admin Cockpit report does not install Process chain<br />1074455: Automate 'Activate Direct Access' for Virtual Providers<br />1110641: BIAC: Dump when running Admin Cockpit installation<br />1330801: Unit of measurement for 0TCTRECSIZE contains invalid entry.<br />1329034: Correcting inconsistency of DBSEL and DBTRANS values.<br />1484802: Missing transfer rule of DataSource 0TCTPRCSVAR_TEXT<br />1491897: RRIs are not Working properly</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5163478"}, {"Key": "Processor                                                                                           ", "Value": "C5163478"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "965386", "RefComponent": "BW-BCT-TCT", "RefTitle": "Activating the technical content for the BI admin cockpit", "RefUrl": "/notes/965386"}, {"RefNumber": "964418", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Adjusting ST03N to new BI-OLAP statistics in Release 7.0", "RefUrl": "/notes/964418"}, {"RefNumber": "931840", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: No check for obsolete release", "RefUrl": "/notes/931840"}, {"RefNumber": "930495", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P8: SDL: PC: Writing D versions from InfoPackages", "RefUrl": "/notes/930495"}, {"RefNumber": "922462", "RefComponent": "BW-BCT-TCT", "RefTitle": "RUNW04s: Techincal content not active", "RefUrl": "/notes/922462"}, {"RefNumber": "885918", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P29:P6: InfoPackage shadow creation in Content system", "RefUrl": "/notes/885918"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "1550968", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC while activate BI Admin Cockpit", "RefUrl": "/notes/1550968"}, {"RefNumber": "1543092", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "Upgrade of BP_BIADMIN 1.0 to BP_BIADMIN 3.0(BP BW ADMINISTRATION)", "RefUrl": "/notes/1543092"}, {"RefNumber": "1035990", "RefComponent": "BW-PLA-IP", "RefTitle": "GoingLive services for integrated planning - check tool", "RefUrl": "/notes/1035990"}, {"RefNumber": "1007430", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "Short dump in transaction ST03N", "RefUrl": "/notes/1007430"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2470847", "RefComponent": "BW-BEX-OT", "RefTitle": "How to Delete BW Statistics Data", "RefUrl": "/notes/2470847 "}, {"RefNumber": "2697030", "RefComponent": "BW-BCT-TCT", "RefTitle": "Transaction ST03 does not work", "RefUrl": "/notes/2697030 "}, {"RefNumber": "1035990", "RefComponent": "BW-PLA-IP", "RefTitle": "GoingLive services for integrated planning - check tool", "RefUrl": "/notes/1035990 "}, {"RefNumber": "965386", "RefComponent": "BW-BCT-TCT", "RefTitle": "Activating the technical content for the BI admin cockpit", "RefUrl": "/notes/965386 "}, {"RefNumber": "1543092", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "Upgrade of BP_BIADMIN 1.0 to BP_BIADMIN 3.0(BP BW ADMINISTRATION)", "RefUrl": "/notes/1543092 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1550968", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC while activate BI Admin Cockpit", "RefUrl": "/notes/1550968 "}, {"RefNumber": "964418", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Adjusting ST03N to new BI-OLAP statistics in Release 7.0", "RefUrl": "/notes/964418 "}, {"RefNumber": "1007430", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "Short dump in transaction ST03N", "RefUrl": "/notes/1007430 "}, {"RefNumber": "931840", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: No check for obsolete release", "RefUrl": "/notes/931840 "}, {"RefNumber": "930495", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P8: SDL: PC: Writing D versions from InfoPackages", "RefUrl": "/notes/930495 "}, {"RefNumber": "922462", "RefComponent": "BW-BCT-TCT", "RefTitle": "RUNW04s: Techincal content not active", "RefUrl": "/notes/922462 "}, {"RefNumber": "885918", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P29:P6: InfoPackage shadow creation in Content system", "RefUrl": "/notes/885918 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "711", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "702", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "BI_CONT", "From": "703", "To": "703", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "704", "To": "704", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70022", "URL": "/supportpackage/SAPKW70022"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70024", "URL": "/supportpackage/SAPKW70024"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70021", "URL": "/supportpackage/SAPKW70021"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70027", "URL": "/supportpackage/SAPKW70027"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70104", "URL": "/supportpackage/SAPKW70104"}, {"SoftwareComponentVersion": "BI_CONT 703", "SupportPackage": "SAPKIBIIQ6", "URL": "/supportpackage/SAPKIBIIQ6"}, {"SoftwareComponentVersion": "BI_CONT 703", "SupportPackage": "SAPKIBIIQ4", "URL": "/supportpackage/SAPKIBIIQ4"}, {"SoftwareComponentVersion": "BI_CONT 704", "SupportPackage": "SAPK-70404INBICONT", "URL": "/supportpackage/SAPK-70404INBICONT"}, {"SoftwareComponentVersion": "BI_CONT 704", "SupportPackage": "SAPK-70405INBICONT", "URL": "/supportpackage/SAPK-70405INBICONT"}, {"SoftwareComponentVersion": "BI_CONT 704", "SupportPackage": "SAPK-70406INBICONT", "URL": "/supportpackage/SAPK-70406INBICONT"}, {"SoftwareComponentVersion": "BI_CONT 704", "SupportPackage": "SAPK-70407INBICONT", "URL": "/supportpackage/SAPK-70407INBICONT"}, {"SoftwareComponentVersion": "SAP_BW 710", "SupportPackage": "SAPKW71007", "URL": "/supportpackage/SAPKW71007"}, {"SoftwareComponentVersion": "SAP_BW 710", "SupportPackage": "SAPKW71008", "URL": "/supportpackage/SAPKW71008"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71103", "URL": "/supportpackage/SAPKW71103"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71104", "URL": "/supportpackage/SAPKW71104"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71101", "URL": "/supportpackage/SAPKW71101"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71102", "URL": "/supportpackage/SAPKW71102"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}