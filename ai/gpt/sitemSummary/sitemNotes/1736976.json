{"Request": {"Number": "1736976", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 437, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010319782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001736976?language=E&token=869D5D5CDD6496974AEECFC45BF3A688"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001736976", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001736976/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1736976"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 92}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.07.2018"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-SIZING"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Portal - Sizing Tools"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Portal - Sizing Tools", "value": "XX-SER-SIZING", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SIZING*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1736976 - Sizing Report for BW on HANA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Enhancement of Function Module /SDF/HANA_TABLE_SIZE and new Report /SDF/HANA_BW_SIZING<br /><br />You intend to migrate an SAP NetWeaver BW system from any database platform to the SAP HANA In-Memory Database and need information on hardware sizing of the HANA platform, or you want to verify the sizing for an SAP NetWeaver BW system that has already been migrated to HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BW on HANA Sizing, Sizing, SAP In-Memory Database, SAP HANA, SAP In-Memory Computing, BW HANA Sizing, Orange sizing, scale-out</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Starting with version 7.30 SP5 you can run SAP Business Information Warehouse (SAP BW) on SAP HANA as database platform. This enables you to leverage the In-Memory capabilities of HANA and the SAP-HANA-optimized BW objects.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>PLEASE NOTE: This note will not be maintained any more. For later updates of the sizing report (version 2.2.0 and above) please apply SAP note 2296290.</strong></span></p>\r\n<p>IMPORTANT: Before applying this note you first have to upgrade to at least ST-PI 2008_1_700 SP 08. An upgrade of ST-PI to the latest SP is recommended in any case.</p>\r\n<p><strong>Latest version of BW on HANA Sizing Report:</strong>&#160;&#160;&#160;&#160;&#160; 2.1.8</p>\r\n<p>Major Changes 2.1.8:</p>\r\n<ul>\r\n<li>Updated 7.40 row store list</li>\r\n<li>Bug fix in handling of 'n/a' results</li>\r\n</ul>\r\n<p>Major Changes 2.1.7:</p>\r\n<ul>\r\n<li>Bug fix&#160;in Hybrid LOB handling</li>\r\n</ul>\r\n<p>Major Changes 2.1.6:</p>\r\n<ul>\r\n<li>Bug fix of size calculation for InfoCubes</li>\r\n<li>Bug fix&#160;in Hybrid LOB handling</li>\r\n</ul>\r\n<p>Major Changes 2.1.5:</p>\r\n<ul>\r\n<li>Bug fix of size calculation for InfoCubes</li>\r\n<li>Correction in Hybrid LOB handling</li>\r\n<li>Bug fix for calculation of non-active share of tables</li>\r\n<li>Haswell support (1.5TB / 3 TB)</li>\r\n</ul>\r\n<p>Major Changes 2.1.4:</p>\r\n<ul>\r\n<li>Bug fix of size calculation for InfoCubes</li>\r\n<li>Integration of Hybrid LOBs</li>\r\n<li>Persistence of sizing results in database tables</li>\r\n<li>Color coding if utilization of master or worker nodes exceed certain thresholds</li>\r\n<li>Elimination of duplicates in table details</li>\r\n</ul>\r\n<p>Major Changes 2.1.3:</p>\r\n<ul>\r\n<li>Bug fix of statistics table issue (only for MaxDB)</li>\r\n<li>Integration of HANA PAK sizing (first approach)</li>\r\n</ul>\r\n<p>Major Changes 2.1.2:</p>\r\n<ul>\r\n<li>Bug fix in length handling of tables with character fields with improved sizing for DSO objects</li>\r\n<li>For InfoCubes number of&#160;characteristics is now taken into account instead of number of dimensions</li>\r\n<li>Chose between 7.30 and 7.40 target release to determine correct set of rowstore tables</li>\r\n<li>Display DB host rather than Application Server host in System Information section</li>\r\n</ul>\r\n<p>Major Changes 2.1.1:</p>\r\n<ul>\r\n<li>Bug fix in length handling of tables with BLOBs</li>\r\n<li>In 7.40 based systems, row store tabls are determined by their DDIC attributes, not by hard coded list</li>\r\n<li>Dimension tables are deteremined by metadata rather than by naming conventions</li>\r\n</ul>\r\n<p>Major Changes 2.1:</p>\r\n<ul>\r\n<li>Revised row store sizing:</li>\r\n<ul>\r\n<li>Now considers indexes for row store tables</li>\r\n<li>Adapted compression factor for row store tables</li>\r\n</ul>\r\n<li>Bug fix to correctly handle dimension tables</li>\r\n<li>Added utilization percentage for master and worker nodes&#160;</li>\r\n</ul>\r\n<p>Major Changes 2.0.1:</p>\r\n<ul>\r\n<li>Support for various memory configurations</li>\r\n<li>Minimum / recommended memory configuration</li>\r\n<li>Adapted compression factor for row store tables</li>\r\n</ul>\r\n<p><br /><br />So far, to estimate the resource requirements of the SAP HANA In-Memory Database, a set of database-dependent scripts has been provided which analyze the database dictionary and compute the size of the relevant source database tables. Please refer to note 1637145 for more details. To simplify sizing of an SAP BW system that is supposed to be migrated, we now provide the database independent ABAP report /SDF/HANA_BW_SIZING which computes all information relevant to sizing a HANA database server. A first version of the report is available with this note. ST-PI 2008_1_7xx SP08 or higher is required as prerequisite. Improvements of the report are also shipped with this SAP Note, so please make sure that you always download the latest version of the note.<br /><br />Important: If you plan to migrate a BW system with significant size (e.g. 20+TB source system database size), please read SAP note 1855041.<br /><br />For a detailed description of the report, its input parameters, and how to interpret its output, please refer to the attached document \"SAP_BW_on_HANA_Sizing_Report.pdf\".<br /><br />The ABAP report now includes the following features:</p>\r\n<ul>\r\n<ul>\r\n<li>enhanced sizing precision by table type specific compression factors</li>\r\n<li>\"non-active data\" concept: optimized memory consumption by taking into account that specific tables are preferred for memory displacement (e.g. tables belonging to objects in Corporate Memory).</li>\r\n<li>Sizing of a Part of the scenarios can be done specificating a subset of BW Objects which have to be considered for sizing. Dependent BW Objects will be automatically taken into account.</li>\r\n<li>Extended Tables: Sizing information for Sybase IQ database to support extended tables.&#160;</li>\r\n</ul>\r\n</ul>\r\n<p>Note that for a stand-alone version of SAP HANA (i.e. HANA without BW) separate sizing information is available in note 1514966.<br /><br />Prerequisite for Implementing this Note is ST-PI 2008_1_700 SP 08 or ST-PI 2008_1_710 SP 08.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "HAN-DB-ENG-BW (SAP HANA BW Engine)"}, {"Key": "Other Components", "Value": "HAN-DB (SAP HANA Database)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036186)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026008)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001736976/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001736976/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736976/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736976/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736976/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736976/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736976/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736976/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001736976/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HANA_BW_Scale_Out_V1_3.pdf", "FileSize": "382", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000439532012&iv_version=0092&iv_guid=FAE0EC5BF2CE1B4FB340B8A85F6FBF45"}, {"FileName": "SAP_BW_on_HANA_Sizing_V2_1_6.pdf", "FileSize": "892", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000439532012&iv_version=0092&iv_guid=669C66A1100A9B4EB2B424FACCA82118"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "1909597", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP BW Migration Cockpit for SAP HANA", "RefUrl": "/notes/1909597"}, {"RefNumber": "1872170", "RefComponent": "XX-SER-SIZING", "RefTitle": "ABAP on HANA sizing report (S/4HANA, Suite on HANA...)", "RefUrl": "/notes/1872170"}, {"RefNumber": "1855041", "RefComponent": "HAN-DB", "RefTitle": "Sizing Recommendation for Master-Node in BW-on-HANA Scale-Out Configuration", "RefUrl": "/notes/1855041"}, {"RefNumber": "1829728", "RefComponent": "BW-WHM-TC", "RefTitle": "BW Housekeeping Task List", "RefUrl": "/notes/1829728"}, {"RefNumber": "1777465", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP HANA Feasibility Check for Netweaver BW on HANA", "RefUrl": "/notes/1777465"}, {"RefNumber": "1767880", "RefComponent": "BW-WHM", "RefTitle": "Non-active data concept for BW on SAP HANA DB", "RefUrl": "/notes/1767880"}, {"RefNumber": "1637145", "RefComponent": "HAN-DB", "RefTitle": "SAP BW on HANA: Sizing SAP In-Memory Database", "RefUrl": "/notes/1637145"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002 "}, {"RefNumber": "1903576", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Additional Main Memory in Exceptional Cases", "RefUrl": "/notes/1903576 "}, {"RefNumber": "2140959", "RefComponent": "HAN-DYT", "RefTitle": "SAP HANA Dynamic Tiering - Additional Information", "RefUrl": "/notes/2140959 "}, {"RefNumber": "2021372", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Sizing Report for BW on HANA (for BW 3.5 Systems)", "RefUrl": "/notes/2021372 "}, {"RefNumber": "1767880", "RefComponent": "BW-WHM", "RefTitle": "Non-active data concept for BW on SAP HANA DB", "RefUrl": "/notes/1767880 "}, {"RefNumber": "1829728", "RefComponent": "BW-WHM-TC", "RefTitle": "BW Housekeeping Task List", "RefUrl": "/notes/1829728 "}, {"RefNumber": "1855041", "RefComponent": "HAN-DB", "RefTitle": "Sizing Recommendation for Master-Node in BW-on-HANA Scale-Out Configuration", "RefUrl": "/notes/1855041 "}, {"RefNumber": "1637145", "RefComponent": "HAN-DB", "RefTitle": "SAP BW on HANA: Sizing SAP In-Memory Database", "RefUrl": "/notes/1637145 "}, {"RefNumber": "1777465", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP HANA Feasibility Check for Netweaver BW on HANA", "RefUrl": "/notes/1777465 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-PI", "From": "2008_1_700", "To": "2008_1_700", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2008_1_710", "To": "2008_1_710", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRD8", "URL": "/supportpackage/SAPKITLRD8"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDK", "URL": "/supportpackage/SAPKITLRDK"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRD7", "URL": "/supportpackage/SAPKITLRD7"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDJ", "URL": "/supportpackage/SAPKITLRDJ"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRD9", "URL": "/supportpackage/SAPKITLRD9"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDL", "URL": "/supportpackage/SAPKITLRDL"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDM", "URL": "/supportpackage/SAPKITLRDM"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLRE8", "URL": "/supportpackage/SAPKITLRE8"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREK", "URL": "/supportpackage/SAPKITLREK"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLRE7", "URL": "/supportpackage/SAPKITLRE7"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREJ", "URL": "/supportpackage/SAPKITLREJ"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLRE9", "URL": "/supportpackage/SAPKITLRE9"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREL", "URL": "/supportpackage/SAPKITLREL"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREM", "URL": "/supportpackage/SAPKITLREM"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74003INSTPI", "URL": "/supportpackage/SAPK-74003INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74002INSTPI", "URL": "/supportpackage/SAPK-74002INSTPI"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-PI", "NumberOfCorrin": 53, "URL": "/corrins/0001736976/212"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; ST-PI&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Support Tools&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 2008_1_700&nbsp;&nbsp; SAPKITLRD6 - SAPKITLRD7&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 2008_1_710&nbsp;&nbsp; SAPKITLRE6 - SAPKITLRE7&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 2008_1_710&nbsp;&nbsp; SAPKITLRE6 - SAPKITLRE7&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Please extend data structure /SDF/HANA_DB_SIZE by adding the following  five lines (using transaction SE11) as additional components at the end of the structure:<br/><br/>Component Component type Data Type Length Decimal  <br/><br/>TAB_TYPE      CHAR   4  0 <br/>BUCKET   INT2   INT2   5  0 <br/>UNLD_FACT     DEC   3  2 <br/>ABAP_SIZE     DEC   22  0 <br/>HANA_SIZE     DEC   22  0 <br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 53, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_700", "Number": "1736976 ", "URL": "/notes/1736976 ", "Title": "Sizing Report for BW on HANA", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_710", "Number": "1736976 ", "URL": "/notes/1736976 ", "Title": "Sizing Report for BW on HANA", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "740", "ValidTo": "740", "Number": "1736976 ", "URL": "/notes/1736976 ", "Title": "Sizing Report for BW on HANA", "Component": "XX-SER-SIZING"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}