{"Request": {"Number": "855514", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1145, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015911982017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000855514?language=E&token=CB2158335C3EDE36716BB2964DB4233D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000855514", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000855514/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "855514"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.07.2006"}, "SAPComponentKey": {"_label": "Component", "value": "IS-A-DBM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Dealer Business Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Automotive", "value": "IS-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Dealer Business Management", "value": "IS-A-DBM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A-DBM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "855514 - Current Release Restrictions for DBM 500 for Ramp Up"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want information about the release restrictions for release DBM 500. Customers are strongly advised to implement at least support package 04.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />Dear Customer,<br /><br />SAP announces the following restrictions regarding the use of the DBM 5.0 release. The status of the following statements corresponds to<br />the date when this note was last changed.<br /><br />SAP welcomes all questions, suggestions, or comments on the<br />productive use of the DBM 5.0 release or the individual functions.<br /><br />Sincerely,<br />PTU Industry Development- Quality Management<br /></p> <b>General Restrictions:</b><br /> <b></b><br /> <UL><LI>Please check all restrictions with respect to ECC 5.0 in note 741821.</LI></UL> <UL><LI>Please check all restrictions with respect to ECC DIMP 5.0 in note 731290.</LI></UL> <UL><LI>The following notes must be implemented before installing DBM 500 SP04: 893976, 891459, 888631, 884889, 870135, 852091, 850409, 849627, 837977, 203251, 901754, 901389, 907597,960321, 946495, 940053 and 937449.</LI></UL> <UL><LI>Ensure that SAP_BASIS support package 16 is implemented before installing SAP DBM 5.0 SP04.</LI></UL> <UL><LI>It is not possible to use the VMS and DBM functionality together in one client. Customers requiring further information on this, are invited to contact SAP via message accordingly.</LI></UL> <UL><LI>The business object /DBM/ORDER has been deleted with SP04 and has been replaced by BUS2400 for the DBM order. See note 960321.</LI></UL> <UL><LI>Those customers that implement SP04 are advised that the authorization object /DBM/ORDER has been changed and therefore it can result in authorization adjustments being necessary.</LI></UL> <UL><LI>Customers intending to install DBM 500 on a unicode enabled system must contact SAP via message before doing this.</LI></UL><UL><LI>Customers are advised that transaction /DBM/S_PACKET will be modified with DBM 600 and a migration of packages created in DBM 500 will not be supported.</LI></UL> <UL><LI>The term \"credit limit\" in DBM refers to \"open items\".</LI></UL> <UL><LI>Archiving: The following standard archiving functionality should not be used by customers who are working with the DBM application as this lead to data inconsistencies:</LI></UL> <UL><UL><LI>Standard programs for archiving of materials</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of customers</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of CO orders</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of warranty claims</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of FI documents</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of printing nast entries</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of MRES entries</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of MSEG entries</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of SD order entries</LI></UL></UL> <UL><UL><LI>Standard programs for archiving of SD invoice entries</LI></UL></UL> <p><br />The restrictions affect planned functionality, which should be released in one of the upcoming support packages or releases. This functionality is not part of the functionality which we have agreed to deliver with DBM500. The restrictions described below do not form a complete list and can be changed at any time.</p> <b></b><br /> <b>The Specific Functionality:</b><br /> <b></b><br /> <OL>1. Release only with the consent of SAP or release only with approval</OL> <p>of or after consultation with SAP. Please contact component IS-A-DBM via message.</p> <b></b><br /> <p>Service Process Extensions (Actions or Transactions)</p> <UL><LI>Additional Order type (cost center as settlement receiver)</LI></UL> <UL><LI>Quotation Process</LI></UL> <UL><LI>Additional Item types (external service with purchase order)</LI></UL> <UL><LI>Backorder processing (Parts)</LI></UL> <UL><LI>Cancellation process actions</LI></UL> <UL><LI>Pricing for service package (dynamic)</LI></UL> <UL><LI>Adjustment of Service Order after Reimbursement Posting (Warranty)<br /></LI></UL> <p>Vehicle Process Extensions</p> <UL><LI>Calculation sheet</LI></UL> <UL><LI>Model quotation with late vehicle assignment</LI></UL> <UL><LI>Automatic quotation cancellation</LI></UL> <UL><LI>Demo vehicle process (internal asset)</LI></UL> <UL><LI>Vehicle order credit memo</LI></UL> <UL><LI>Differential taxation</LI></UL> <UL><LI>Management of fixed assets</LI></UL> <UL><LI>Debit-Side Trade-In</LI></UL> <UL><LI>Transform existing new vehicle into used vehicle</LI></UL> <UL><LI>Vehicle transfer between dealers / subsidaries</LI></UL> <UL><LI>Foreign Trade processes may face some limitations due to country compliances in particular sanctioned party list screening and complaince control during the delivery process. We highly recommend to map the business processes against foreign trade requirements on a project base.</LI></UL> <p><br />Parts Process Extensions</p> <UL><LI>Collective invoicing</LI></UL> <UL><LI>Cancellation process actions</LI></UL> <UL><LI>Parts locator (in Order)</LI></UL> <UL><LI>Integration of purchase requisition</LI></UL> <UL><LI>Manufacturer Part Number</LI></UL> <p><br />Cross application processes</p> <UL><LI>Check Engine</LI></UL> <UL><LI>Business Warehouse</LI></UL> <p></p> <OL>1. Released with Restrictions:</OL> <UL><LI>Item split in transactions /dbm/order, /dbm/order01 and /dbm/order02.</LI></UL> <UL><LI>Creation of invoices in /dbm/order, /dbm/order01 and /dbm/order02.</LI></UL> <OL>2. Not available:</OL> <UL><LI>All brand specific data, interfaces and process extensions, example fields without logic</LI></UL> <UL><LI>Tyre Manager</LI></UL> <UL><LI>KBA Data (\"Kraftfahrbundesamt\" - for German market) integration</LI></UL> <UL><LI>Chained Business</LI></UL> <UL><LI>\"Parts Management\" power transaction</LI></UL> <p><br />Customers are strongly advised to implement at least support package 04.<br />Customers who do not implement support package 04 must contact SAP for guidence before installing Dealer Business Management.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037163)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D039477)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000855514/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364"}, {"RefNumber": "946495", "RefComponent": "CO-OM-OPA-A", "RefTitle": "DBM: Generating settlement rule automatically", "RefUrl": "/notes/946495"}, {"RefNumber": "940053", "RefComponent": "LO-WTY", "RefTitle": "'Partner role missing for partner'error message in warranty", "RefUrl": "/notes/940053"}, {"RefNumber": "937449", "RefComponent": "LO-WTY", "RefTitle": "Update Termination in WTY while saving the partner profile.", "RefUrl": "/notes/937449"}, {"RefNumber": "921445", "RefComponent": "IS-A-DBM", "RefTitle": "DBM Architectural Principles", "RefUrl": "/notes/921445"}, {"RefNumber": "907597", "RefComponent": "LO-WTY", "RefTitle": "Short dump on opening message log in WTY transaction.", "RefUrl": "/notes/907597"}, {"RefNumber": "901754", "RefComponent": "IS-A-DBM", "RefTitle": "Customizing settings for pricing", "RefUrl": "/notes/901754"}, {"RefNumber": "901389", "RefComponent": "IS-A-DBM", "RefTitle": "Customizing settings for the Action Handler", "RefUrl": "/notes/901389"}, {"RefNumber": "893976", "RefComponent": "IS-A-VMS", "RefTitle": "VMAS DBM AT SP01: create PO - manual price ignored!", "RefUrl": "/notes/893976"}, {"RefNumber": "891459", "RefComponent": "XX-TRANSL-EN", "RefTitle": "Setting up screen substitution in DBM order using ATAB", "RefUrl": "/notes/891459"}, {"RefNumber": "888631", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV filter: Empty cells cause F4 help to dump", "RefUrl": "/notes/888631"}, {"RefNumber": "884889", "RefComponent": "IS-A-VMS", "RefTitle": "Incorrect delivery address on purchase orders (Action ORD1)", "RefUrl": "/notes/884889"}, {"RefNumber": "870135", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV filter: Cannot filter columns of the type 'String'", "RefUrl": "/notes/870135"}, {"RefNumber": "852091", "RefComponent": "IS-A-DBM", "RefTitle": "Installation of DBM 500 on SAP ECC 500", "RefUrl": "/notes/852091"}, {"RefNumber": "850409", "RefComponent": "MM-PUR-PO-BAPI", "RefTitle": "EnjoySAP order BAPI: Messages without class and number", "RefUrl": "/notes/850409"}, {"RefNumber": "849627", "RefComponent": "IS-A-VMS", "RefTitle": "VELO03_GET_POSSIBLE_ACTIONS does not return internal actions", "RefUrl": "/notes/849627"}, {"RefNumber": "837977", "RefComponent": "CO-OM-CCA-E", "RefTitle": "KB61: No module for reposting", "RefUrl": "/notes/837977"}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821"}, {"RefNumber": "731290", "RefComponent": "IS-A", "RefTitle": "Current release restrictions for ECC DIMP 5.0", "RefUrl": "/notes/731290"}, {"RefNumber": "203251", "RefComponent": "CO-OM-OPA-A", "RefTitle": "K_ORDER_SRULE_ADD: incorrect values in distribution rule", "RefUrl": "/notes/203251"}, {"RefNumber": "1158878", "RefComponent": "IS-A-DBM", "RefTitle": "Restriction for using sales UoM in DBM", "RefUrl": "/notes/1158878"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821 "}, {"RefNumber": "731290", "RefComponent": "IS-A", "RefTitle": "Current release restrictions for ECC DIMP 5.0", "RefUrl": "/notes/731290 "}, {"RefNumber": "946495", "RefComponent": "CO-OM-OPA-A", "RefTitle": "DBM: Generating settlement rule automatically", "RefUrl": "/notes/946495 "}, {"RefNumber": "1158878", "RefComponent": "IS-A-DBM", "RefTitle": "Restriction for using sales UoM in DBM", "RefUrl": "/notes/1158878 "}, {"RefNumber": "852091", "RefComponent": "IS-A-DBM", "RefTitle": "Installation of DBM 500 on SAP ECC 500", "RefUrl": "/notes/852091 "}, {"RefNumber": "921445", "RefComponent": "IS-A-DBM", "RefTitle": "DBM Architectural Principles", "RefUrl": "/notes/921445 "}, {"RefNumber": "850409", "RefComponent": "MM-PUR-PO-BAPI", "RefTitle": "EnjoySAP order BAPI: Messages without class and number", "RefUrl": "/notes/850409 "}, {"RefNumber": "888631", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV filter: Empty cells cause F4 help to dump", "RefUrl": "/notes/888631 "}, {"RefNumber": "837977", "RefComponent": "CO-OM-CCA-E", "RefTitle": "KB61: No module for reposting", "RefUrl": "/notes/837977 "}, {"RefNumber": "884889", "RefComponent": "IS-A-VMS", "RefTitle": "Incorrect delivery address on purchase orders (Action ORD1)", "RefUrl": "/notes/884889 "}, {"RefNumber": "940053", "RefComponent": "LO-WTY", "RefTitle": "'Partner role missing for partner'error message in warranty", "RefUrl": "/notes/940053 "}, {"RefNumber": "891459", "RefComponent": "XX-TRANSL-EN", "RefTitle": "Setting up screen substitution in DBM order using ATAB", "RefUrl": "/notes/891459 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "870135", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV filter: Cannot filter columns of the type 'String'", "RefUrl": "/notes/870135 "}, {"RefNumber": "937449", "RefComponent": "LO-WTY", "RefTitle": "Update Termination in WTY while saving the partner profile.", "RefUrl": "/notes/937449 "}, {"RefNumber": "893976", "RefComponent": "IS-A-VMS", "RefTitle": "VMAS DBM AT SP01: create PO - manual price ignored!", "RefUrl": "/notes/893976 "}, {"RefNumber": "203251", "RefComponent": "CO-OM-OPA-A", "RefTitle": "K_ORDER_SRULE_ADD: incorrect values in distribution rule", "RefUrl": "/notes/203251 "}, {"RefNumber": "849627", "RefComponent": "IS-A-VMS", "RefTitle": "VELO03_GET_POSSIBLE_ACTIONS does not return internal actions", "RefUrl": "/notes/849627 "}, {"RefNumber": "907597", "RefComponent": "LO-WTY", "RefTitle": "Short dump on opening message log in WTY transaction.", "RefUrl": "/notes/907597 "}, {"RefNumber": "901754", "RefComponent": "IS-A-DBM", "RefTitle": "Customizing settings for pricing", "RefUrl": "/notes/901754 "}, {"RefNumber": "901389", "RefComponent": "IS-A-DBM", "RefTitle": "Customizing settings for the Action Handler", "RefUrl": "/notes/901389 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DBM", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}