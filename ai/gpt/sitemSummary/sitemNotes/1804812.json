{"Request": {"Number": "1804812", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1548, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010663592017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001804812?language=E&token=98705D817B46529FC0E8F920DFF8018D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001804812", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001804812/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1804812"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.12.2015"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IM-GF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Management", "value": "MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "MM-IM-GF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM-GF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1804812 - MB transactions: Limited maintenance/decommissioning"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The following transactions for entering and displaying goods movements (material documents) - called \"MB transactions\" below - have been replaced by the single-screen transaction MIGO since Release 4.6:</p>\r\n<ul>\r\n<li>MB01, MB02, MB03, MB04, MB05, MB0A, MB11, MB1A, MB1B, MB1C, MB31, MBNL, MBRL, MBSF, MBSL, MBST, MBSU</li>\r\n</ul>\r\n<p>For reasons relating to change protection for existing process configurations, the specified transactions are still available up to the ERP&#x00A0;6.xx releases, that is, also in the Enhancement Packages 7 and 8. However, they are subject to maintenance restrictions:</p>\r\n<ul>\r\n<ul>\r\n<li>Corrections in the MB transactions are carried out only if the equivalent process cannot be posted demonstrably using transaction MIGO or if serious errors occur that cause inconsistencies or other legally-relevant problems.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New functions are developed only for transaction MIGO.</li>\r\n</ul>\r\n</ul>\r\n<p>In the ERP&#x00A0;6.xx releases (for example in EhP7 = Release 6.17), the system message M7 499 refers to this situation. You can set the message category in Customizing (default: \"I\" Info). The system issues the information message once daily in non-production clients only. Message category \"E\" (from SAP Note 2029600 onwards) results in an error message in live clients as well.&#x00A0;When MB03 is called from other transactions, the calls are automatically forwarded to MIGO for document display, if the message category \"E\" is set.</p>\r\n<p>With batch input, the system does not generally issue message 7499 until further notice.<br /><br />In ERP in the following SAP products (S/4 HANA, S4CORE), the current plan is to decommission the MB transactions. This means that they are then no longer available. For current information about this, see SAP Note 2210569.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MIGO_GR, MIGO_GI, MIGO_ST, BAPI_GOODSMVT_CANCEL, M7499, M7-499, omcq</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The double-maintenance of the transaction families MIGO and MB* delays the processing of problems with MIGO and other transactions, and reduces the development capacity provided for continuous further development. Therefore, the discontinuation of the double-maintenance is also in the interest of all SAP customers.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You can still use the MB transactions in the ERP&#x00A0;6.xx releases. However, it is advisable to replace them with transaction MIGO.<br />The BAPI \"BAPI_GOODSMVT_CREATE\" is provided as a replacement for background processing via batch input.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022253)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022253)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001804812/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001804812/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "729408", "RefComponent": "LE-SHP-DL", "RefTitle": "Document flow: Material doc. disp. w/ MIGO instead of MB03", "RefUrl": "/notes/729408"}, {"RefNumber": "2210569", "RefComponent": "MM-IM-GF", "RefTitle": "Obsolete Material inventory management transactions", "RefUrl": "/notes/2210569"}, {"RefNumber": "1888722", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1888722"}, {"RefNumber": "1816363", "RefComponent": "MM-IM-GF", "RefTitle": "MIGO: Enhanced functions for Enhancement Package 7", "RefUrl": "/notes/1816363"}, {"RefNumber": "1810419", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MM-IM reports: Switch from MB03 to MIGO", "RefUrl": "/notes/1810419"}, {"RefNumber": "1808576", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1808576"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2304535", "RefComponent": "MM-IM-GF-BAPI", "RefTitle": "BAPI_GOODSMVT_CREATE - GMCODE for MIGO in table T158G - SAP ERP & S/4 HANA", "RefUrl": "/notes/2304535 "}, {"RefNumber": "2948885", "RefComponent": "QM-IM-UD", "RefTitle": "QA11: error M7328 (Deficit of Delivered quantity) during usage decision", "RefUrl": "/notes/2948885 "}, {"RefNumber": "2373499", "RefComponent": "MM-IM-GR-MIGO", "RefTitle": "Batch determination in MIGO behaves differently from MB01", "RefUrl": "/notes/2373499 "}, {"RefNumber": "2617192", "RefComponent": "QM-IM-UD", "RefTitle": "M7021 for Return Delivery in transaction QA11 while posting stock", "RefUrl": "/notes/2617192 "}, {"RefNumber": "3195748", "RefComponent": "MM-IM-GF-MB", "RefTitle": "Usage of transaction codes within interfaces and Fiori apps", "RefUrl": "/notes/3195748 "}, {"RefNumber": "3032090", "RefComponent": "IS-ADEC-SUB", "RefTitle": "DI_AD_SUBCON_MOVETOSTOCK: side effect of note 2947273", "RefUrl": "/notes/3032090 "}, {"RefNumber": "2945455", "RefComponent": "MM-IM-GF-COBL", "RefTitle": "MB1C : Error message AAPO169 for goods receipt without value", "RefUrl": "/notes/2945455 "}, {"RefNumber": "2827225", "RefComponent": "IS-ADEC-SUB", "RefTitle": "ADSUBCON: calling MIGO", "RefUrl": "/notes/2827225 "}, {"RefNumber": "2743194", "RefComponent": "MM-IM-GF-POVL", "RefTitle": "MAA - Missing EKBE and EKBE_MA entry for cancellation via MBST or reversal entry using MB01", "RefUrl": "/notes/2743194 "}, {"RefNumber": "2732639", "RefComponent": "MM-IM-GF-COBL", "RefTitle": "MB1A : Operation account assignment  is set as required entry", "RefUrl": "/notes/2732639 "}, {"RefNumber": "2663320", "RefComponent": "LE-WM-IFC", "RefTitle": "Error when displaying the material document.", "RefUrl": "/notes/2663320 "}, {"RefNumber": "2458058", "RefComponent": "IS-DFS-MM-PFEH", "RefTitle": "GAF: Material document is not displayed", "RefUrl": "/notes/2458058 "}, {"RefNumber": "2267834", "RefComponent": "MM-IM-GF-VAL", "RefTitle": "S4TWL - Material Ledger Obligatory for Material Valuation", "RefUrl": "/notes/2267834 "}, {"RefNumber": "2262437", "RefComponent": "CA-GBT", "RefTitle": "Display Material Document in source system using MIGO (GBT)", "RefUrl": "/notes/2262437 "}, {"RefNumber": "2262349", "RefComponent": "LO-BM-GBT", "RefTitle": "Display Material Document in source system using MIGO (ERP)", "RefUrl": "/notes/2262349 "}, {"RefNumber": "2224132", "RefComponent": "EHS-WA-PRO", "RefTitle": "Disposal processing: Double-click on follow-on document calls transaction MB03 with error message M7 062 (\"Document & does not exist in calendar year &\")", "RefUrl": "/notes/2224132 "}, {"RefNumber": "2213013", "RefComponent": "IS-ADEC-SUB", "RefTitle": "MIGO: transfer posting like in transaction MB1B", "RefUrl": "/notes/2213013 "}, {"RefNumber": "2091749", "RefComponent": "MM-PUR-GF-RE", "RefTitle": "Changeover: Navigation for displaying the material document to transaction MIGO", "RefUrl": "/notes/2091749 "}, {"RefNumber": "2031266", "RefComponent": "XX-CSC-TH-LO", "RefTitle": "Material document displayed in MB03 transaction instead of MIGO in J_1IHSTCD", "RefUrl": "/notes/2031266 "}, {"RefNumber": "1993407", "RefComponent": "PM-W<PERSON>", "RefTitle": "Document flow: Branching to material document using MB03 instead of MIGO", "RefUrl": "/notes/1993407 "}, {"RefNumber": "1964366", "RefComponent": "MM-IM-GF-ARC", "RefTitle": "SARE: Archived material documents now displayed using MIGO", "RefUrl": "/notes/1964366 "}, {"RefNumber": "1888722", "RefComponent": "MM-IM-GR-MIGO", "RefTitle": "MIGO: Change Material Document", "RefUrl": "/notes/1888722 "}, {"RefNumber": "1816363", "RefComponent": "MM-IM-GF", "RefTitle": "MIGO: Enhanced functions for Enhancement Package 7", "RefUrl": "/notes/1816363 "}, {"RefNumber": "1810419", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MM-IM reports: Switch from MB03 to MIGO", "RefUrl": "/notes/1810419 "}, {"RefNumber": "1808576", "RefComponent": "MM-IM-GR-MIGO", "RefTitle": "MIGO: Ändern Materialbeleg (~MB02)", "RefUrl": "/notes/1808576 "}, {"RefNumber": "729408", "RefComponent": "LE-SHP-DL", "RefTitle": "Document flow: Material doc. disp. w/ MIGO instead of MB03", "RefUrl": "/notes/729408 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60024", "URL": "/supportpackage/SAPKH60024"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60214", "URL": "/supportpackage/SAPKH60214"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60313", "URL": "/supportpackage/SAPKH60313"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60414", "URL": "/supportpackage/SAPKH60414"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60511", "URL": "/supportpackage/SAPKH60511"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60608", "URL": "/supportpackage/SAPKH60608"}, {"SoftwareComponentVersion": "SAP_APPL 616", "SupportPackage": "SAPKH61602", "URL": "/supportpackage/SAPKH61602"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0001804812/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60023&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 602&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60213&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60312&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60413&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60510&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60607&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH61601&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/></P> <OL>1. SE91<br/>Create system message: M7 499<br/>Short text: \"Transaction &amp;1 is obsolete (SAP Note 1804812)\"</OL> <OL>2. SE80<br/>Package MB<br/>Create SET/GET parameter: \"M7499\"<br/>Short text: \"Control message M7499\"</OL> <OL>3. SE16<br/>Table T160MVAL<br/>Create new entries for M7 499: MSGTP = ' ', 'E', 'I'</OL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2029600", "RefTitle": "MB Transactions: M7499 as Error Message", "RefUrl": "/notes/0002029600"}]}}}}}