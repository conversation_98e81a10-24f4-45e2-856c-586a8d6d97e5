{"Request": {"Number": "1639050", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 291, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017321002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001639050?language=E&token=0175F1DF9AAEF477EAC200BFDCA6E748"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001639050", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001639050/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1639050"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 61}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.06.2016"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Solution Documentation Assistant"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Solution Documentation Assistant", "value": "SV-SMG-SDA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1639050 - Collective Note for SDA on ST 710"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This is a collection of SAP Notes for Solution Documentation Assistant(SDA) based on software component ST 710.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SoDocA, Reverse Business Process Documentation(RBPD),</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note provides a reference to several other SAP Notes relevant in the context of the SDA and it contains some helpful information.<br /><span style=\"text-decoration: underline;\">Note:</span> This SAP Note itself is not implementable with transaction SNOTE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>It is recommended to implement the latest support package (ST, ST-PI) and to apply the remaining SAP Notes (example: implement first ST 710 SP08 and apply than SAP Notes with &gt;= S09).<br /><br /><strong>SAP Notes for each managed system (ST-PI):</strong><br />Number&#160;&#160;Val SAP Note Short Text<br />1282401 M02 SDA: Collective SAP Note for ST-PI 2008_1_46C to 2008_1_710<br />1373202 M02 SDA: Corrections for ST-PI 008_1_46C bis 2008_1_710<br />1471578 M03 SDA: 'Unknown Table Field' Occurs to SQL Execution 2<br />1501533 M04 Runtime error CONVT_OVERFLOW in /SDF/RBE_RECEIVE_SQL_DATA<br />1512351 M04 GETWA_NOT_ASSIGNED in /SDF/RBE_GET_OBJ_DESCRIPTION<br />1521760 M04 SQL check step is not executed<br />1553812 M05 Runtime error during SQL check step execution<br />1561656 M05 Runtime error GENERATE_SUBPOOL_DIR_FULL in SQL check step<br />1564831 M05 Runtime error CONVT_OVERFLOW in SQL check step execution<br />1596004 M05 Composite SAP note for SQL execution<br />1652640 M06 Memory problem during SQL check step execution<br />1652641 M06 Resource problem during SQL check step execution<br />1655551 M06 SQL-execution errors are not handled properly<br />1681430 M06 SDA: runtime error during SQL execution (release &lt; 6.20)<br />1706534 M07 SQL execution terminates with DBIF_REPO_PART_NOT_FOUND<br />1788056 M07 Unnecessary execution of period independent SQL Checks<br />2027052 M10 Performance issue during data collection<br />2079084&#160;M12&#160;Runtime error TSV_TNEW_PAGE_ALLOC_FAILED<br />2110635 M12 SDA: Incomplete Field Results for Descriptive SQL Check Steps<br />2316965&#160;M14 Performance issue during data collection<br /><br /><strong>SAP Notes for SolMan system (ST):</strong><br />Number&#160;&#160;Val SAP Note Short Text<br />1490032 S01 Solution reporting is incomplete<br />1573279 S02 SDA: Error \"Invalid operand type\" in Analyse table tree<br />1581547 S02 SoDocA: Failed to delete scheduled Analysis<br />1585468 S02 SoDocA: Adding BAdI of type \"Classic\" to Rule Database<br />1591028 S03 Error in node rating with informative Check Item<br />1595250 S03 Composite SAP Note for SDA Solution Manager 7.1 SP1<br />1598814 S03 SDA upload: search help log. components displays all entries<br />1599087 S02 SDA upload: table for logical components not correct<br />1606003 S03 DYNPRO_SEND_IN_BACKGROUND when displaying analysis<br />1612348 S03 Problem with BPR object assignments in a new SolMan project<br />1615280 S03 SDA: Dump MOVE_CAST_ERROR in analysis<br />1619702 S04 Problems w. SQL check steps/execution time display extension<br />1620018 S04 SDA Analysis Project Structure: node can not be expanded<br />1621881 S04 SDA Content Upload: Logical Components w/o checkstep visible<br />1623786 S04 More UPL data than necessary is determined<br />1631615 S04 Missing E2E data in object usage<br />1634673 S04 SDA: Logical component deleted by upload<br />1635217 S04 SDA: Missing object details for BI and UPL check steps<br />1639691 S04 Incorrect results when there is incorrect workload<br />1641805 S04 Limit Number of Objects not working correctly<br />1642943 S04 Specified task types are ignored<br />1643108 S04 Enhancing the optimization for EarlyWatch Alert data<br />1648215 S05 SDA Create Analysis: period table retains old values<br />1649934 S05 SDA content upload:logical components not assignable<br />1654756 S05 SDA: Incorrect analysis processing<br />1654767 S05 SDA Content Upload: Analysis Structure empty<br />1654792 S05 Workload Data not retrieved correctly via RFC<br />1662470 S05 Incorrect rating of workload check steps<br />1664819 S05 Wrong traffic lights shown in log table<br />1665238 S05 Wrong execution of Usage &amp; Procedure Logging check steps<br />1672379 S05 SoDocA: Error updating Solution Mananger project<br />1676483 S05 SoDocA: Parameter \"Descriptive Result Limit\"<br />1678298 S05 Rule database: search result does not match the criteria<br />1682411 S05 No workload data in EWA service sessions for system 'XXX'<br />1685193 S05 SoDocA: Missing check item name in an analysis<br />1687179 S05 Scheduled Job Start Time in Managed System<br />1689383 S05 Wrong node rating (3)<br />1715620 S06 Search result not in visible area for table tree<br />1715661 S06 Collective Note for SoDocA on ST 710 SP05<br />1715675 S06 Content upload: missing logical components<br />1719142 S06 Problems solved with ST 710 SP06<br />1729159 S05 Too many customer objects displayed as not used<br />1737106 S07 Analysis: Missing data when extended system ID used<br />1737108 S07 Missing data in E2E summary of an analysis<br />1745492 S07 Logical component in analysis project not changed<br />1748733 S07 Incorrect rating of a check step and a check item<br />1756629 S07 Errors uploading SoDocA content<br />1758181 S07 Content Upload performance improvements<br />1774648 S08 Analysis stops execution with runtime error<br />1787940 S08 Missing objects when requesting additional used objects<br />1790955 S09 UPL check steps can not be executed<br />1809391 S09 Problem with extended System ID<br />1847290 S09 Issue when copying an analysis project<br />1895748 S10 Missing log entry for workload data via RFC<br />1895860 S10 Workload data collector dumps after long runtime<br />1901630 S10 Runtime error CALL_FUNCTION_NOT_FOUND<br />1916746 S10 No objects listed \"Analysis\" tab<br />1920451 S10 Missing BI Workload data and wrong UPL result<br />1922468 S11 BI Workload data missing<br />1951482&#160;S11 No usage for Objects of type Function Module, Report or Transaction determined<br />2005310 S12 Wrong period in job log when using BW workload data<br />2115654 S14 BSP error when generating SDA analysis report<br />2142606 S14 SDA: BI statistics missing for some periods<br />2165537 S14 Click on a line of analysis project ends with a short dump due to an invalid value<br />2181833 S14 Problem with objects with same name and different types<br />2237455 S15 Open analyses from SDA Overview ends with a short dump due to an invalid value<br />2242519 S15 Duplicate object usages shown for different logical components in SDA analysis results<br />2318156 S15&#160;Dump \"Message type is unknown\" occurs when creating an existing project</p>\r\n<p><br /><span style=\"text-decoration: underline;\">Explanation of validity column (Val):</span><br />First letter:<br />S - SolMan system, software component ST release 710<br />M - managed system, software component ST-PI releases:<br />&#160;&#160;&#160;&#160;2008_1_46C, 2008_1_620, 2008_1_640, 2008_1_700, 2008_1_710<br />&#160;&#160;&#160;&#160;Solutions exist for SAP_BASIS starting with 46C and/or 620<br /><br />Two digit number:<br />support package level the solution is contained in for the software component and release defined with first letter<br /><br /><span style=\"text-decoration: underline;\">Examples:</span><br />S05 - solution delivered for SolMan system, software component ST release 710 support package level 05<br /><br />M06 - solution delivered for managed system, software component ST-PI, releases 2008_1_46C and/or 2008_1_620, 2008_1_640, 2008_1_700, 2008_1_710 with support package 06<br /><br />1. A managed system defined in SAP Solution Manager with at least one complete month (e.g. the last complete month) of workload statistics data per managed system is required to perform an analysis.<br /><br />2. The workload statistics data (transaction ST03N) from a managed system is provided via the EarlyWatch Alert processing framework. It is required to define the collection of EarlyWatch Alert data in the SAP Solution Manager in order to use Reverse Business Process Documentation for those managed systems.<br /><br />3.&#160;ST-PI 2008_1_46C, 2008_1_620 and&#160;2008_1_640&#160;have reached the end of maintenance (see attached SAP Note 539977).<br /><br />4. Information on SAP Solution Manager 7.1 SP14 (September 2015) can be found at <a target=\"_blank\" href=\"http://help.sap.com/saphelp_sm71_sp14/helpdata/en/45/51fbdbd4941803e10000000a1553f7/frameset.htm\">http://help.sap.com/saphelp_sm71_sp14/helpdata/en/45/51fbdbd4941803e10000000a1553f7/frameset.htm</a><br /><br />5. Further information on Reverse Business Process Documentation (RBPD) you can find via http://service.sap.com/rbpd.<br /><br />6. In case of business content related issues including the definition and combination of check rules applied, open a customer message on component SV-SMG-SDA-CNT. (Update: see attached SAP Note 1988717)&#160;<br /><br />7. In case of technical issues with the SDA functionality, open a customer message on component SV-SMG-SDA.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D020501"}, {"Key": "Processor                                                                                           ", "Value": "D020501"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001639050/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001639050/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "1988717", "RefComponent": "SV-SMG-SDA", "RefTitle": "Support for component SV-SMG-SDA-CNT discontinued", "RefUrl": "/notes/1988717"}, {"RefNumber": "1683647", "RefComponent": "SV-SMG-SDA", "RefTitle": "Technical Prerequisites for SDA in SolMan 7.1", "RefUrl": "/notes/1683647"}, {"RefNumber": "1643839", "RefComponent": "SV-SMG-IMP-PAD", "RefTitle": "Incorrect tab page selection in project administration", "RefUrl": "/notes/1643839"}, {"RefNumber": "1591505", "RefComponent": "SV-SMG-SDA", "RefTitle": "Content for Reverse Business Process Documentation", "RefUrl": "/notes/1591505"}, {"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1591505", "RefComponent": "SV-SMG-SDA", "RefTitle": "Content for Reverse Business Process Documentation", "RefUrl": "/notes/1591505 "}, {"RefNumber": "1683647", "RefComponent": "SV-SMG-SDA", "RefTitle": "Technical Prerequisites for SDA in SolMan 7.1", "RefUrl": "/notes/1683647 "}, {"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998 "}, {"RefNumber": "1643839", "RefComponent": "SV-SMG-IMP-PAD", "RefTitle": "Incorrect tab page selection in project administration", "RefUrl": "/notes/1643839 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}