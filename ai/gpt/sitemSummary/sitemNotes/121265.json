{"Request": {"Number": "121265", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 240, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014605242017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000121265?language=E&token=E24853F81BADAFB7D3580382D7220BA4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000121265", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000121265/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "121265"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.02.2001"}, "SAPComponentKey": {"_label": "Component", "value": "PY-US"}, "SAPComponentKeyText": {"_label": "Component", "value": "USA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "USA", "value": "PY-US", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-US*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "121265 - Payroll Outsourcing on SAPSERV3/4"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>New functionality for Generic Payroll Outsourcing for Pilot site testing. This new functionality will provide a generic payroll file for the US and Canada to be used by external Outsourcers set up for this process. It creates IDoc structures which will automatically transmit to designated server ports as per configuration.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Outsourcing, PU12, Generic, IDOC, U200, K200, Payroll, Interface, IDoc</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>It is important to NOTE, there are two transports here.&#x00A0;&#x00A0;One transport is a \"delta\" transport for those clients who have already uploaded previous full transports and have kept all delta(patch) transports up<br />to date.&#x00A0;&#x00A0;The other type of transport is a \"full\" transport which contains all payroll outsourcing development up to the date indicated after the transport version.&#x00A0;&#x00A0;The full transport must be used by clients who are new to payroll outsourcing or did not apply the previous payroll outsourcing full transports. Documentation on all transports up to and<br />including patch #1 and the corrections/enhancements made can be requested by emailing to: <EMAIL>. Other future transport patches and their documentation will be in related notes.<br /><br />The files on sapserv3/4 are in directory<br />general/R3server/abap/note.0121265 (R4server).<br /><br /><br />4.0B<br />FULL TRANSPORTS;<br />D900038.HN0, R900038.HN0, K900038.HN0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; January 1999<br />OUTSOURCING40B.CAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;June 1999<br />OUTSOURCING40BV1.CAR (contains Patch1)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;July 1999<br />OUTSOURCING40BV2.CAR (contains Patch 1&amp;2)&#x00A0;&#x00A0; August 1999<br /><br />DELTA/PATCH TRANPORTS:<br />OUTSOURCING40BPATCH1.CAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;July 1999<br />OUTSOURCING40BPATCH2.CAR (see note 165325)&#x00A0;&#x00A0;August 1999<br /><br /><br />Extract the archive with CAR. Please check that your system id is not X4B. Customers should observe note 13719 for implementation.<br /><br />PLEASE NOTE:&#x00A0;&#x00A0;after applying either the August full transport or<br />Patch2 of August,<br />there is a change required to one module.&#x00A0;&#x00A0;Due to<br />backporting, a PU12 module contained some modularization logic which<br />did not exist until 4.5+.&#x00A0;&#x00A0;Therefore a duplicate include exists.<br />For 4.0B clients ONLY:<br />For Report RPCIFM41<br />Comment out INCLUDE UP50QDAT and regenerate export programs only from<br />transaction PU12 for OTUS/OTCA, OTMU/OTMK<br />This should only apply to Patch2, check the program to ensure the<br />Report RPCIFM41 does not have this include before changing and<br />regenerating programs<br /><br />************************************************************************<br />3.1I<br />FULL TRANSPORTS:<br />D900540.H3I, R900540.H3I, K900540.H3I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; January 1999<br />OUTSOURCING31I.CAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;June 1999<br />OUTSOURCING31IV1.CAR&#x00A0;&#x00A0;(contains Patch 1)&#x00A0;&#x00A0;&#x00A0;&#x00A0;July 1999<br />OUTSOURCING31IV2.CAR&#x00A0;&#x00A0;(contains Patch 1&amp;2)&#x00A0;&#x00A0;August 1999<br /><br />DELTA/PATCH TRANSPORTS:<br />OUTSOURCING31IPATCH1.CAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;July 1999<br />OUTSOURCING31IPATCH2.CAR (see note 165325)&#x00A0;&#x00A0;August 1999<br /><br />Extract the archive with CAR. Please check that your system id is not<br />T3I.&#x00A0;&#x00A0;Customers should observe note 13719 for implementation.<br /><br />************************************************************************<br />Please note, Full Transports for June, must apply Patch1 and 2.<br />Full transports for July, must apply patch 2.&#x00A0;&#x00A0;All new clients to<br />Payroll Outsourcing should apply the most recent Full Transport.<br />************************************************************************<br /><br />For 4.5B clients, applying LCP06 will provide patch1 &amp; patch2<br />corrections/enhancements.<br />************************************************************************<br />For ALL 4.0B and 3.1I clients the following MUST occur:<br /><br />NOTE:&#x00A0;&#x00A0;Due to various LCP transports affecting this Outsourcing transport, it is recommended to copy RPCALCU0, RPCFDCU0 and SAPMP50C from your current LCP prior to applying this transport.&#x00A0;&#x00A0;Once the transport has been applied, recopy your original LCP copied version back into your environment and make the changes as defined below.<br />For 4.0B clients the transport development occurred in a 4.0B system which had up to LCP13 installed, so clients up to but NOT including LCP 13 do not have to do these procedures.&#x00A0;&#x00A0;Any LCP 13 and 13+ will have to.<br />PLEASE NOTE: for any future LCP upgrades it is necessary to follow these same steps as mentioned above (copy 3 programs and change)<br /><br />The Payroll Import process required alterations to RPCMAS09 and RPCFTB00 to add table T558E. There appears to be no affect with LCP's at this time, but it is recommended to copy these two as well and monitor any LCP changes made to them which may have overwritten the Outsourcing logic.<br /><br />Outbound parameters must be defined in PORTS HROT_US/HROT_CA/HROT_UM/ HROT_CM based on the Outsourcing version chosen.&#x00A0;&#x00A0;Use transaction WEDI to define these for the correct PORT designation as defined in the Payroll Outsourcing training materals or Payroll Outsourcing configuration document.<br /><br />========================================================================<br />FOR 3.1I and 4.0B do the following updates:<br />*$*$<br />*$*$ Programident:&#x00A0;&#x00A0;LIMU<br />*$*$ Object&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;:&#x00A0;&#x00A0;REPO<br />*$*$ Objectname&#x00A0;&#x00A0;:&#x00A0;&#x00A0;RPCALCU0<br />*$*$ For Release :&#x00A0;&#x00A0;3.1I and 4.0B<br />After line:<br />INCLUDE RPCMASU0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; \"Modules AS-FUNCTIONS US Tax/Gen<br />Insert:<br />INCLUDE RPCMASUO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Fill tax tables for Outsourcing<br /><br />========================================================================<br />FOR 3.1I and 4.0B do the following updates:<br />*$*$<br />*$*$ Programident: LIMU<br />*$*$ Object&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: REPO<br />*$*$ Objectname&#x00A0;&#x00A0;: RPCFDCU0<br />*$*$ For release ; 3.1I and 4.0B<br />------------------------------------------------------------------------<br />After line:<br />DATA: END&#x00A0;&#x00A0; OF ROUND_TXRT.<br />Insert:<br />*&#x00A0;&#x00A0;Operation UST<br />DATA: USTRING(6).<br />========================================================================<br />FOR 3.1I and 4.0B do the following updates:<br />NOTE:&#x00A0;&#x00A0;Due to LCP's the following code may disappear and needs to be<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;reapplied for Outsourcing needs.<br />*$*$<br />*$*$ Programident: LIMU<br />*$*$ Object&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: REPO<br />*$*$ Objectname&#x00A0;&#x00A0;: SAPMP50C<br />*$*$ For release : 3.1I and 4.0B<br />------------------------------------------------------------------------<br />After line:<br />when 'U2D '. tcode = 'PC4U'. perform next_transaction.<br />Insert for 4.0B:<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;Outsourcing menus US<br />when 'U2E '. repid = 'RPCEPYU0_CALL'. perform submit_report.<br />when 'U2F '. repid = 'RPCEMDU0_CALL'. perform submit_report.<br />when 'U2G '. repid = 'RPCALCU0'. perform submit_report.<br />when 'U2H '. repid = 'RPCEMDU0_COMBINED_CALL'. perform submit_report.<br />when 'U2I '. repid = 'RPCEPYU0_COMBINED_CALL'. perform submit_report.<br />Insert for 3.1:<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;Outsourcing menus US<br />when 'U2E '. repid = 'RPCEPYUC'. perform submit_report.<br />when 'U2F '. repid = 'RPCEMDUC'. perform submit_report.<br />------------------------------------------------------------------------<br />After line:<br />when 'K2D '. tcode = 'PC4K'. perform next_transaction.<br />Insert for 4.0B:<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;Outsourcing menus Canada<br />when 'K2E '. repid = 'RPCEPYK0_CALL'. perform submit_report.<br />when 'K2F '. repid = 'RPCEMDK0_CALL'. perform submit_report.<br />when 'K2G '. repid = 'RPCALCK0'. perform submit_report.<br />when 'K2H '. repid = 'RPCEMDK0_COMBINED_CALL'. perform submit_report.<br />when 'K2I '. repid = 'RPCEPYK0_COMBINED_CALL'. perform submit_report.<br />Insert for 3.1:<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;Outsourcing menus Canada<br />when 'K2E '. repid = 'RPCEPYKC'. perform submit_report.<br />when 'K2F '. repid = 'RPCEMDKC'. perform submit_report.<br />------------------------------------------------------------------------<br />After line:<br />when 'IE2D '. tcode = '5IE4'. perform next_transaction.&#x00A0;&#x00A0;\"for 4.0B<br />OR before references to '5B&#x00A0;&#x00A0;' in 3.1 versions.<br />Insert:<br />*&#x00A0;&#x00A0;Outsourcing calls for IDOCS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;when '4A&#x00A0;&#x00A0;'. tcode = 'WE05'. perform next_transaction.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;when '4B&#x00A0;&#x00A0;'. tcode = 'WE14'. perform next_transaction.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;when '4C&#x00A0;&#x00A0;'. repid = 'RSEINB00'. perform submit_report.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;when '4D&#x00A0;&#x00A0;'. repid = 'RBDAPP01'. perform submit_report.<br />------------------------------------------------------------------------<br />In the menu painter for SAPMP50C change statuses M10 and M07 so that they have the following included for Outsourcing:<br />Status M10:<br />Insert entry for Outsourcing between Subs. Activities and Environment.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Outsourcing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U2E&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Gross Payroll Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U2F&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Master Data Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Combined Payroll Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4A&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Display IDOCS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Process Outbound<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4C&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Process Inbound<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4A&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Display IDOCS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4D&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Release Idocs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U2G&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Payroll Import<br /><br />Insert the following two entries for Combined Payroll Export:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U2H&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Mini Master Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U2I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Combined Payroll Export<br /><br />Status M07:<br />Insert entry for Outsourcing between Subs. Activities and Environment.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Outsourcing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;K2E&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Gross Payroll Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;K2F&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Master Data Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Combined Payroll Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4A&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Display IDOCS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Process Outbound<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4C&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Process Inbound<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4A&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Display IDOCS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4D&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release Idocs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;K2G&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Payroll Import<br /><br />Insert the following two entries for Combined Payroll Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;K2H&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Mini Master Export<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;K2I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Combined Payroll Export<br /><br />For 3.1I clients the Combined process does not exist and the \"Payroll<br />Import\" should reference transaction 1A not U2G/K2G.<br /><br />NOTE!!!! Always check the above 3 program changes whenever an LCP is applied to your system to make sure the LCP has not overwritten this<br />code.<br />************************************************************************<br />For 4.0B, 3.1H and 3.1I clients the following table change is needed:<br />- To acknowledge infotype 491 an entry in table T582A must exist:<br />&#x00A0;&#x00A0;Add the following entries for infotype 491:<br /><br />Single Screen:&#x00A0;&#x00A0;&#x00A0;&#x00A0;2000&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Infotype view:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I (3.1)<br />List Screen:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3000&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Period:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; I (3.1)<br />Screen header:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;02&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Database name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A (3.1)<br />Time Constraint:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2<br />Choose org. assn:&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"check mark this\"<br />Access Auth:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"check mark this\"<br />Sort Sequence:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2<br />Select w/begin:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create w/o beg:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 (3.1)<br />Select w/end:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create w/o end:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 (3.1)<br />Select w/o date:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />Past entry all:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />Text allowed:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"check mark this\"<br /><br />The rest of the fields leave blank.<br /><br />Call the infotype \"Payroll Outsourcing\".<br />********************************************<br />The Next setup only involves release 4.0B as IT415 does not exist for<br />3.1H/I<br />******************************************************************<br />- To acknowledge infotype 415 an entry in table T582A must exist:<br />&#x00A0;&#x00A0;Add the following entries for infotype 415:<br /><br />Single Screen:&#x00A0;&#x00A0;&#x00A0;&#x00A0;2000<br />List Screen:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3000<br />Screen header:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;02<br />Time Constraint:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;B<br />Select w/o date:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />Copy infotype:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"check mark this\"<br />Stype req:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"check mark this\"<br /><br />The rest of the fields leave blank.<br /><br />Call the infotype \"Export Status\".<br />********************************************<br />&#x00A0;&#x00A0;Also add the following entries for table T777D and infotype 0415:<br />Dialog Module:&#x00A0;&#x00A0;RP_0415<br />Structure:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P0415<br />Database table: PA0415<br />Module Pool:&#x00A0;&#x00A0;&#x00A0;&#x00A0;MP041500<br />Infotype of:&#x00A0;&#x00A0;\"click on Personnel Administration\"<br />Subtype field:&#x00A0;&#x00A0;FLDID<br />Subtype table:&#x00A0;&#x00A0;T532A<br />Infotype/view:&#x00A0;&#x00A0;I<br />Period/key date: I<br />************* End of changes for 4.0B, 3.1H, AND 3.1I*****<br />===========================================================<br />The following represents changes required should a 3.1H client desire<br />to use the 3.1I transport.&#x00A0;&#x00A0;This most recent transport has tried to<br />rectify some of these situations, so you may not require all the<br />changes as mentioned below.&#x00A0;&#x00A0;It is important to still make note<br />of the possibility for problems.<br /><br />For 3.1H clients ONLY the following changes must be made.<br />1)&#x00A0;&#x00A0;Field doesn't exist in 3.1H.<br />In REPORT RPCOPFX0, in FORM CALC_ANSAL<br />Remove:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;move p0008-waers to l_ptbindbw-waers.<br /><br />In REPORT RPCOMFX0, in FORM CALC_ANSAL<br />Remove:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;move p0008-waers to l_ptbindbw-waers.<br /><br />2)&#x00A0;&#x00A0;Data Dictionary data element doesn't exist in 3.1H.<br />E1HRFM expects the existence of a data element called CHAR_40.<br />Please add a data element definition with the following<br />characteristics:<br />Data Element Name:&#x00A0;&#x00A0;CHAR_40<br />Short text:&#x00A0;&#x00A0;Textfeld<br />Domain name:&#x00A0;&#x00A0;CHAR_40<br />Text(10):&#x00A0;&#x00A0;&#x00A0;&#x00A0;Text&#x00A0;&#x00A0;or&#x00A0;&#x00A0;Name Field<br />Text(15):&#x00A0;&#x00A0;&#x00A0;&#x00A0;Text&#x00A0;&#x00A0;or&#x00A0;&#x00A0;Name Field<br />Text(20):&#x00A0;&#x00A0;&#x00A0;&#x00A0;Text&#x00A0;&#x00A0;or&#x00A0;&#x00A0;Name Field<br /><br />After creating the Data Element, please reactivate tables E1HRFM<br />E2HRFM and E3HRFM.<br /><br />3)&#x00A0;&#x00A0;Includes not existing in 3.1H<br />Data definition includes for countries US and Canada do not exist in<br />3.1H that are required for the Interface Toolkit to work.&#x00A0;&#x00A0;These need<br />to be created:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; a)&#x00A0;&#x00A0;COPY the contents of include RPCDRUU0 to RPC2RUUP.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; b)&#x00A0;&#x00A0;COPY the contents of include RPCDRKK0 to RPC2RKKP.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;c)&#x00A0;&#x00A0;Run RPCIFG00 for File Modifier OTUS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;d)&#x00A0;&#x00A0;Run RPCIFG00 for File Modifier OTCA<br />**************&#x00A0;&#x00A0;End of 3.1H Only Updates *****************<br />************************************************************************<br />For 3.1H and 3.1I Clients:<br />During backporting, Rules do not get transported.&#x00A0;&#x00A0;Therefore,<br />three Rules are missing for the US (one for Canada) that need to be<br />added before U200/K200 will check.<br />1) Missing Rules&#x00A0;&#x00A0;US ONLY:<br />&#x00A0;&#x00A0; a)&#x00A0;&#x00A0;Using transaction PE02 copy K014 to U014.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;change Attributes in Country Grouping from 07 to 10<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;change references in the rule from KMS0 to UMS0.<br />&#x00A0;&#x00A0; b)&#x00A0;&#x00A0;Copy KMS0 to UMS0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;change Attributes in Country Grouping from 07 to 10<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;change Operation KST to UST and KSETI to USETI<br />&#x00A0;&#x00A0;c)&#x00A0;&#x00A0;Run RPUCT300 for OP (operations) on country code 10 if the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; rules do not check.<br />2) Missing Rule&#x00A0;&#x00A0;US and Canada:<br />&#x00A0;&#x00A0; a)&#x00A0;&#x00A0;Create X2PR&#x00A0;&#x00A0;for PCR * Wtype ****<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;add operation in first operation stmt of&#x00A0;&#x00A0;ADDWTG*<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;copy the PCR * to PCR's 1,2,3 &amp; 4.<br />*************&#x00A0;&#x00A0;End of changes for both 3.1H and 3.1I ******</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PY-US-IE (obsolete please use PY-XX-IE)"}, {"Key": "Other Components", "Value": "PY-CA (Canada)"}, {"Key": "Other Components", "Value": "PY-CA-IE (obsolete please use PY-XX-IE)"}, {"Key": "Responsible                                                                                         ", "Value": "I003406"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000121265/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "350388", "RefComponent": "PY-XX-IE", "RefTitle": "OT: Transport Patch #5 - Payroll Outsourcing", "RefUrl": "/notes/350388"}, {"RefNumber": "300351", "RefComponent": "PY-US-IE", "RefTitle": "Transport Patch #4 - Payroll Outsourcing", "RefUrl": "/notes/300351"}, {"RefNumber": "186426", "RefComponent": "PY-US-IE", "RefTitle": "Transport Patch #3 - Payroll Outsourcing", "RefUrl": "/notes/186426"}, {"RefNumber": "165325", "RefComponent": "PY-US-IE", "RefTitle": "Payroll Outsourcing Customization User Exit-Patch2", "RefUrl": "/notes/165325"}, {"RefNumber": "163909", "RefComponent": "PY-US-IE", "RefTitle": "Missing T582A entry for Infotype 0491 - Outsourcing", "RefUrl": "/notes/163909"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "350388", "RefComponent": "PY-XX-IE", "RefTitle": "OT: Transport Patch #5 - Payroll Outsourcing", "RefUrl": "/notes/350388 "}, {"RefNumber": "300351", "RefComponent": "PY-US-IE", "RefTitle": "Transport Patch #4 - Payroll Outsourcing", "RefUrl": "/notes/300351 "}, {"RefNumber": "186426", "RefComponent": "PY-US-IE", "RefTitle": "Transport Patch #3 - Payroll Outsourcing", "RefUrl": "/notes/186426 "}, {"RefNumber": "163909", "RefComponent": "PY-US-IE", "RefTitle": "Missing T582A entry for Infotype 0491 - Outsourcing", "RefUrl": "/notes/163909 "}, {"RefNumber": "165325", "RefComponent": "PY-US-IE", "RefTitle": "Payroll Outsourcing Customization User Exit-Patch2", "RefUrl": "/notes/165325 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}