{"Request": {"Number": "2884137", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 538, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000097892020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002884137?language=E&token=C569F681B47A3A97DDA3CDECDB1D3858"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002884137", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2884137"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.01.2020"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-SUS-FND"}, "SAPComponentKeyText": {"_label": "Component", "value": "Sustainability Foundation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Sustainability", "value": "EHS-SUS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-SUS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Sustainability Foundation", "value": "EHS-SUS-FND", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-SUS-FND*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2884137 - Task migration dumps if there are actions with an invalid Due In value"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>It is recommended&#160;that you apply the&#160;corrections in this&#160;SAP Note before you start the post-conversion activities.</p>\r\n<p>You have a recurring action definition whose <em>Due In</em> value is negative. You run the&#160;<strong>R_EHFND_WFF_RESTART_PROCESSES&#160;</strong>report as part of the migration to the new task management. A short dump occurs.</p>\r\n<p>After applying the corrections in this SAP Note, such action definitions are skipped and an error describing the issue is added to the report log.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Task Management, Short Dump, Migration, Invalid Value, Negative Value, Restart Processes, Restart Workflow Processes</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n\r\n<p>Reason: Program error.</p>\r\n<p>Prerequisites: The validity of the correction is maintained in the correction instructions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n\r\n<p>The Support Packages that contain this correction are listed in the \"Support Packages\" section.<br />Alternatively, you can implement the correction instructions of this SAP Note.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I340525"}, {"Key": "Processor                                                                                           ", "Value": "I062105"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002884137/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884137/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2903938", "RefComponent": "EHS-SUS-FND", "RefTitle": "Task migration from 1809 to 1909 release in SAP EHS Management", "RefUrl": "/notes/2903938 "}, {"RefNumber": "2905234", "RefComponent": "EHS-SUS-FND", "RefTitle": "Task migration from 1709 (or lower) to 1809 release in SAP EHS Management", "RefUrl": "/notes/2905234 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10304INS4CORE", "URL": "/supportpackage/SAPK-10304INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10402INS4CORE", "URL": "/supportpackage/SAPK-10402INS4CORE"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002884137/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10303INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10401INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/><br/>In your development system, carry out the following steps:<br/><br/>1. Call up transaction SE91.<br/>2. Type in CM_EHFND_WFF_MIGR in the Message Class field. Type in 509 in  the Messages Number field.<br/>3. Choose Change.<br/>4. Add the following message in the Message Short Text field that opens:<br/>Due In value \"&amp;1\" of definition \"&amp;2\" with key \"&amp;3\" is  invalid.<br/>5. Save your entries.<br/>6. Select your workbench request.<br/>7. Transport your workbench request to other relevant systems in your landscape.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2698591 ", "URL": "/notes/2698591 ", "Title": "Migrated closed task instances have wrong title and owner", "Component": "EHS-SUS-FND"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2866912 ", "URL": "/notes/2866912 ", "Title": "Task migration dumps if there are actions with a Due In value larger than 255", "Component": "EHS-SUS-FND"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2884137 ", "URL": "/notes/2884137 ", "Title": "Task migration dumps if there are actions with an invalid Due In value", "Component": "EHS-SUS-FND"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2866912 ", "URL": "/notes/2866912 ", "Title": "Task migration dumps if there are actions with a Due In value larger than 255", "Component": "EHS-SUS-FND"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2884137 ", "URL": "/notes/2884137 ", "Title": "Task migration dumps if there are actions with an invalid Due In value", "Component": "EHS-SUS-FND"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}