{"Request": {"Number": "1448738", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 553, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016995042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001448738?language=E&token=EF5BD806D38029D30686B5E01BC1FD55"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001448738", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001448738/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1448738"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.08.2011"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-AJ"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rent Adjustment"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rent Adjustment", "value": "RE-FX-AJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-AJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1448738 - FAQ: Rent adjustment"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains references to consulting notes and troubleshooting notes for the rent adjustment in RE-FX.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Adjustment, rent adjustment, REAJPR, REAJCH</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>See the \"Reference to related Notes\" section.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002072)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001448738/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001448738/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "925641", "RefComponent": "RE-FX-AJ", "RefTitle": "Change the values from the standard adjustment", "RefUrl": "/notes/925641"}, {"RefNumber": "924965", "RefComponent": "RE-FX-AJ", "RefTitle": "Consulting note: BAdI rent adjustment REAJ_ADJUSTMENT", "RefUrl": "/notes/924965"}, {"RefNumber": "1828227", "RefComponent": "RE-FX-AJ", "RefTitle": "Enhancement of capping for condition adjustment", "RefUrl": "/notes/1828227"}, {"RefNumber": "1762265", "RefComponent": "RE-FX-AJ", "RefTitle": "Undo activated rental adjustments", "RefUrl": "/notes/1762265"}, {"RefNumber": "1693715", "RefComponent": "RE-FX-CN", "RefTitle": "Assignment of rental objects to adjustment measure", "RefUrl": "/notes/1693715"}, {"RefNumber": "1629875", "RefComponent": "RE-FX-AJ", "RefTitle": "Expiration date when continuing adjustments", "RefUrl": "/notes/1629875"}, {"RefNumber": "1629693", "RefComponent": "RE-FX", "RefTitle": "Influencing expiration date of RE-FX logs", "RefUrl": "/notes/1629693"}, {"RefNumber": "1629534", "RefComponent": "RE-FX", "RefTitle": "Ability to reproduce deletion of logs", "RefUrl": "/notes/1629534"}, {"RefNumber": "1603630", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting: Due date of follow-up postings", "RefUrl": "/notes/1603630"}, {"RefNumber": "1589848", "RefComponent": "RE-FX-AJ", "RefTitle": "Enhancement adjustment by dependent condition - level 2", "RefUrl": "/notes/1589848"}, {"RefNumber": "1585833", "RefComponent": "RE-FX-AJ", "RefTitle": "Advance payment adjustment in case of leap years", "RefUrl": "/notes/1585833"}, {"RefNumber": "1584252", "RefComponent": "RE-FX-AJ", "RefTitle": "Configuring message REAJME 099", "RefUrl": "/notes/1584252"}, {"RefNumber": "1579609", "RefComponent": "RE-FX-AJ", "RefTitle": "Editable fields for condition adjustment", "RefUrl": "/notes/1579609"}, {"RefNumber": "1573265", "RefComponent": "RE-FX-AJ", "RefTitle": "Object selection for API REAJ_API_REVERSE", "RefUrl": "/notes/1573265"}, {"RefNumber": "1550408", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment record despite decreasing or increasing index", "RefUrl": "/notes/1550408"}, {"RefNumber": "1513523", "RefComponent": "RE-FX-CN", "RefTitle": "Selection of rental objects for comparative group", "RefUrl": "/notes/1513523"}, {"RefNumber": "1481623", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment history for service charge settlement", "RefUrl": "/notes/1481623"}, {"RefNumber": "1460487", "RefComponent": "RE-FX-AJ", "RefTitle": "Filtering in REAJSH and REAJRV", "RefUrl": "/notes/1460487"}, {"RefNumber": "1458027", "RefComponent": "RE-FX-CP", "RefTitle": "Form choice when printing from the rent adjustment", "RefUrl": "/notes/1458027"}, {"RefNumber": "1454377", "RefComponent": "RE-FX-AJ", "RefTitle": "Hide and display functions in adjustment overview", "RefUrl": "/notes/1454377"}, {"RefNumber": "1450103", "RefComponent": "RE-FX-AJ", "RefTitle": "F4 help for adjustment runs and adjustment date", "RefUrl": "/notes/1450103"}, {"RefNumber": "1448666", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjusting rental objects from CEA to rep. list of rents", "RefUrl": "/notes/1448666"}, {"RefNumber": "1444031", "RefComponent": "RE-FX-AJ", "RefTitle": "CEA: Performance adjustment", "RefUrl": "/notes/1444031"}, {"RefNumber": "1441715", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1441715"}, {"RefNumber": "1381010", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment measure and percentage surcharge", "RefUrl": "/notes/1381010"}, {"RefNumber": "1368670", "RefComponent": "RE-FX-SR", "RefTitle": "Sales grading: No index adjustment, error in documentation", "RefUrl": "/notes/1368670"}, {"RefNumber": "1339671", "RefComponent": "RE-FX-AJ", "RefTitle": "Selection of occupied rental objects using contract selectn", "RefUrl": "/notes/1339671"}, {"RefNumber": "1331311", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment letter for CEA and relevant expenses", "RefUrl": "/notes/1331311"}, {"RefNumber": "1328330", "RefComponent": "RE-FX-AJ", "RefTitle": "External reversal of rent adjustments", "RefUrl": "/notes/1328330"}, {"RefNumber": "1326819", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment even if only reduction/increase allowed", "RefUrl": "/notes/1326819"}, {"RefNumber": "1306665", "RefComponent": "RE-FX-CN", "RefTitle": "Condition transfer: Enhancement of BADI_RECD_CONDITION", "RefUrl": "/notes/1306665"}, {"RefNumber": "1271659", "RefComponent": "RE-FX-AJ", "RefTitle": "\"New condition in target\" parameter in RESCAJ", "RefUrl": "/notes/1271659"}, {"RefNumber": "1167204", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment measure: Objects missing", "RefUrl": "/notes/1167204"}, {"RefNumber": "1141635", "RefComponent": "RE-FX-SC", "RefTitle": "Rent adjustment after SCS does not contain all objects", "RefUrl": "/notes/1141635"}, {"RefNumber": "1136088", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment: Missing modifications in adjustment process", "RefUrl": "/notes/1136088"}, {"RefNumber": "1080168", "RefComponent": "RE-FX", "RefTitle": "Additional authorization check for processes", "RefUrl": "/notes/1080168"}, {"RefNumber": "1057282", "RefComponent": "RE-FX-BD", "RefTitle": "Maint fxtres fttngs chars rep lst of rents relevant/irrlvnt", "RefUrl": "/notes/1057282"}, {"RefNumber": "1013171", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Austria: Stable value guarantee procedure as per MRG", "RefUrl": "/notes/1013171"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1585833", "RefComponent": "RE-FX-AJ", "RefTitle": "Advance payment adjustment in case of leap years", "RefUrl": "/notes/1585833 "}, {"RefNumber": "1584252", "RefComponent": "RE-FX-AJ", "RefTitle": "Configuring message REAJME 099", "RefUrl": "/notes/1584252 "}, {"RefNumber": "1579609", "RefComponent": "RE-FX-AJ", "RefTitle": "Editable fields for condition adjustment", "RefUrl": "/notes/1579609 "}, {"RefNumber": "1573265", "RefComponent": "RE-FX-AJ", "RefTitle": "Object selection for API REAJ_API_REVERSE", "RefUrl": "/notes/1573265 "}, {"RefNumber": "1550408", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment record despite decreasing or increasing index", "RefUrl": "/notes/1550408 "}, {"RefNumber": "1828227", "RefComponent": "RE-FX-AJ", "RefTitle": "Enhancement of capping for condition adjustment", "RefUrl": "/notes/1828227 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1481623", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment history for service charge settlement", "RefUrl": "/notes/1481623 "}, {"RefNumber": "1460487", "RefComponent": "RE-FX-AJ", "RefTitle": "Filtering in REAJSH and REAJRV", "RefUrl": "/notes/1460487 "}, {"RefNumber": "1458027", "RefComponent": "RE-FX-CP", "RefTitle": "Form choice when printing from the rent adjustment", "RefUrl": "/notes/1458027 "}, {"RefNumber": "1454377", "RefComponent": "RE-FX-AJ", "RefTitle": "Hide and display functions in adjustment overview", "RefUrl": "/notes/1454377 "}, {"RefNumber": "1450103", "RefComponent": "RE-FX-AJ", "RefTitle": "F4 help for adjustment runs and adjustment date", "RefUrl": "/notes/1450103 "}, {"RefNumber": "1444031", "RefComponent": "RE-FX-AJ", "RefTitle": "CEA: Performance adjustment", "RefUrl": "/notes/1444031 "}, {"RefNumber": "1762265", "RefComponent": "RE-FX-AJ", "RefTitle": "Undo activated rental adjustments", "RefUrl": "/notes/1762265 "}, {"RefNumber": "1326819", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment even if only reduction/increase allowed", "RefUrl": "/notes/1326819 "}, {"RefNumber": "1693715", "RefComponent": "RE-FX-CN", "RefTitle": "Assignment of rental objects to adjustment measure", "RefUrl": "/notes/1693715 "}, {"RefNumber": "1339671", "RefComponent": "RE-FX-AJ", "RefTitle": "Selection of occupied rental objects using contract selectn", "RefUrl": "/notes/1339671 "}, {"RefNumber": "1629875", "RefComponent": "RE-FX-AJ", "RefTitle": "Expiration date when continuing adjustments", "RefUrl": "/notes/1629875 "}, {"RefNumber": "1513523", "RefComponent": "RE-FX-CN", "RefTitle": "Selection of rental objects for comparative group", "RefUrl": "/notes/1513523 "}, {"RefNumber": "1589848", "RefComponent": "RE-FX-AJ", "RefTitle": "Enhancement adjustment by dependent condition - level 2", "RefUrl": "/notes/1589848 "}, {"RefNumber": "1448666", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjusting rental objects from CEA to rep. list of rents", "RefUrl": "/notes/1448666 "}, {"RefNumber": "1629693", "RefComponent": "RE-FX", "RefTitle": "Influencing expiration date of RE-FX logs", "RefUrl": "/notes/1629693 "}, {"RefNumber": "1629534", "RefComponent": "RE-FX", "RefTitle": "Ability to reproduce deletion of logs", "RefUrl": "/notes/1629534 "}, {"RefNumber": "1141635", "RefComponent": "RE-FX-SC", "RefTitle": "Rent adjustment after SCS does not contain all objects", "RefUrl": "/notes/1141635 "}, {"RefNumber": "1136088", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment: Missing modifications in adjustment process", "RefUrl": "/notes/1136088 "}, {"RefNumber": "1080168", "RefComponent": "RE-FX", "RefTitle": "Additional authorization check for processes", "RefUrl": "/notes/1080168 "}, {"RefNumber": "1271659", "RefComponent": "RE-FX-AJ", "RefTitle": "\"New condition in target\" parameter in RESCAJ", "RefUrl": "/notes/1271659 "}, {"RefNumber": "1306665", "RefComponent": "RE-FX-CN", "RefTitle": "Condition transfer: Enhancement of BADI_RECD_CONDITION", "RefUrl": "/notes/1306665 "}, {"RefNumber": "1328330", "RefComponent": "RE-FX-AJ", "RefTitle": "External reversal of rent adjustments", "RefUrl": "/notes/1328330 "}, {"RefNumber": "1331311", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment letter for CEA and relevant expenses", "RefUrl": "/notes/1331311 "}, {"RefNumber": "1381010", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment measure and percentage surcharge", "RefUrl": "/notes/1381010 "}, {"RefNumber": "1603630", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting: Due date of follow-up postings", "RefUrl": "/notes/1603630 "}, {"RefNumber": "1368670", "RefComponent": "RE-FX-SR", "RefTitle": "Sales grading: No index adjustment, error in documentation", "RefUrl": "/notes/1368670 "}, {"RefNumber": "1167204", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment measure: Objects missing", "RefUrl": "/notes/1167204 "}, {"RefNumber": "924965", "RefComponent": "RE-FX-AJ", "RefTitle": "Consulting note: BAdI rent adjustment REAJ_ADJUSTMENT", "RefUrl": "/notes/924965 "}, {"RefNumber": "925641", "RefComponent": "RE-FX-AJ", "RefTitle": "Change the values from the standard adjustment", "RefUrl": "/notes/925641 "}, {"RefNumber": "1057282", "RefComponent": "RE-FX-BD", "RefTitle": "Maint fxtres fttngs chars rep lst of rents relevant/irrlvnt", "RefUrl": "/notes/1057282 "}, {"RefNumber": "1013171", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Austria: Stable value guarantee procedure as per MRG", "RefUrl": "/notes/1013171 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}