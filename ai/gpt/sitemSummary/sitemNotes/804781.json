{"Request": {"Number": "804781", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 291, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015821762017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000804781?language=E&token=F40D933226706AA7765E1B0625D95669"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000804781", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000804781/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "804781"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-MM-BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - MM Content im BW System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Materials Management", "value": "BW-BCT-MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - MM Content im BW System", "value": "BW-BCT-MM-BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM-BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "804781 - 0IC_C03: Consumption key figures from LIS INVCO (0IC_C01)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The following LIS key figures from Inventory Controlling (INVCO) are missing in BW InfoCube 0IC_C03:<br />- Total consumption quantity/value<br />- Unplanned consumption quantity/value<br />- Number of material movements<br />These key figures are contained in the 0IC_C01/2 InfoCubes, for example.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>0IC_C03, 0IC_C01, 0IC_C02, consumptions, consumption, consumption quantity, consumption value, consumption quantities, consumption values, total consumption, unplanned consumption, unplanned consumptions, BCO, LIS, MGVBR, WGVBR, MUVBR, WUVBR, number of material movements</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The standard BW system does not provide these evaluations but you may want to use them in certain cases.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You can easily enhance the existing Business Content as follows:<br /><br />1. In the logistics cockpit (R/3 Transaction LBWE), add the fields MCMSEG-MGVBR, MCMSEG-WGVBR, MCMSEG-MUVBR and MCMSEG-WUVBR in the extract structure to the 2LIS_03_BF and 2LIS_03_UM DataSources (if they do not yet exist) and activate all corresponding objects.<br /><br />2. Create the corresponding key figures in the BW system (cumulative values with the 0LOC_CURRCY and 0BASE_UOM unit InfoObjects; number of material movements as the type \"Figure\") or use these delivered key figures:<br />0NO_MATMOVE - Number of material movements<br />0TOT_US_QTY - Total consumption quantity<br />0TOT_US_VAL - Total consumption value<br />0UNP_US_QTY - Unplanned consumption quantity<br />0UNP_US_VAL - Unplanned consumption value<br /><br />3. Add these key figures to the InfoSources, DataProviders and reporting objects you are using.<br /><br />4. Adjust the transfer and update rules.<br /><br />a) Field allocation in the transfer rules<br />MCMSEG-MGVBR -&gt; 0TOT_US_QTY - Total consumption quantity<br />MCMSEG-WGVBR -&gt; 0TOT_US_VAL - Total usage value<br />MCMSEG-MUVBR -&gt; 0UNP_US_QTY - Unplanned consumption quantity<br />MCMSEG-WUVBR -&gt; 0UNP_US_VAL - Unplanned consumption value<br /><br />b) Logic in the update rules<br />Use these update rules as a template to calculate the key figure value. You must adjust the source field from the InfoSource correspondingly (XXXXXXX).<br /><br />2LIS_03_BF:<br /><br />&#x00A0;&#x00A0;IF ( COMM_STRUCTURE-processkey EQ '100'&#x00A0;&#x00A0; \"Other Issues<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '101'&#x00A0;&#x00A0; \"Returns / Vendor<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '104'&#x00A0;&#x00A0; \"Material Transfer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '105'&#x00A0;&#x00A0; \"Stock Adjustment InvD<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '106'&#x00A0;&#x00A0; \"Stock Adjustment Other<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '110' ) \"Issues from Stock<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Transfers<br />&#x00A0;&#x00A0; AND COMM_STRUCTURE-bwapplnm EQ 'MM'<br />* only movements which are relevant for stock control<br />&#x00A0;&#x00A0; AND COMM_STRUCTURE-XXXXXXX &lt;&gt; 0.<br />* result value of the routine<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = -1 * COMM_STRUCTURE-XXXXXXX.<br />* if the returncode is zero, the result will be updated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RETURNCODE = 0.<br />&#x00A0;&#x00A0;ELSEIF ( COMM_STRUCTURE-processkey EQ '000'&#x00A0;&#x00A0; \"Other Receipts<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '001'&#x00A0;&#x00A0; \"Goods Receipt / Vendor<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '004'&#x00A0;&#x00A0; \"Material Transfer /<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Receipt<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '005'&#x00A0;&#x00A0; \"Stock Adjustment InvD<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '006'&#x00A0;&#x00A0; \"Stock Adjustment Other<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OR COMM_STRUCTURE-processkey EQ '010' ) \"Receipt from Stock<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Transfer<br />* only movements which are relevant for stock control<br />&#x00A0;&#x00A0; AND COMM_STRUCTURE-XXXXXXX &lt;&gt; 0.<br />* result value of the routine<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = COMM_STRUCTURE-XXXXXXX.<br />* if the returncode is zero, the result will be updated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RETURNCODE = 0.<br />&#x00A0;&#x00A0;ELSE.<br />* if the returncode is not equal zero, the result will not be updated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RETURNCODE = 4.<br />&#x00A0;&#x00A0;ENDIF.<br />* if abort is not equal zero, the update process will be canceled<br />&#x00A0;&#x00A0;ABORT = 0.<br /><br />2LIS_03_UM:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if ( comm_structure-processkey eq '050'\"Other revaluation +<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;or comm_structure-processkey eq '051'\"Revaluation/ price change +<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;or comm_structure-processkey eq '052' )\"revaluation/inv. verif. +<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;AND COMM_STRUCTURE-bwapplnm EQ 'MM'<br /> * only movements which are relevant for stock control<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;AND COMM_STRUCTURE-XXXXXXX &lt;&gt; 0.<br /> * result value of the routine<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; RESULT = 1 * COMM_STRUCTURE-XXXXXXX.<br /> * if the returncode is zero, the result will be updated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; RETURNCODE = 0.<br />&#x00A0;&#x00A0; ELSEIF ( comm_structure-processkey eq '150'\"Other revaluation -<br />&#x00A0;&#x00A0; or comm_structure-processkey eq '151'\"Revaluation/price change -<br />&#x00A0;&#x00A0; or comm_structure-processkey eq '152' )\"Revaluation/ invoice verif.-<br />&#x00A0;&#x00A0; AND COMM_STRUCTURE-bwapplnm EQ 'MM'<br /> * only movements which are relevant for stock control<br />&#x00A0;&#x00A0; AND COMM_STRUCTURE-XXXXXXX &lt;&gt; 0.<br /> * result value of the routine<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; RESULT = -1 * COMM_STRUCTURE-XXXXXXX.<br /> * if the returncode is zero, the result will be updated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; RETURNCODE = 0.<br />&#x00A0;&#x00A0; ELSE.<br /> * if the returncode is not equal zero, the result will not be updated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; RETURNCODE = 4.<br />&#x00A0;&#x00A0; ENDIF.<br /> * if abort is not equal zero, the update process will be canceled<br />&#x00A0;&#x00A0; ABORT = 0.<br /><br />Insert a routine with the required logic for the 0NO_MATMOVE key figure value calculation. We cannot propose a general procedure here since customer requirements may be different.<br />In general, the update should occur with +1 or -1.<br />Depending on the transaction key (0PROESSKEY), the reversal indicator (0RECORDMODE) or the debit/credit indicator (0DCINDIC), you must decide whether it should be updated positively or negatively.<br />Note: You cannot use the CPNOITEMS field in this scenario.<br /><br />Update the key figures<br />0NO_MATMOVE - Number of material movements<br />0TOT_US_QTY - Total consumption quantity<br />0UNP_US_QTY - Unplanned consumption quantity<br />with all characteristics that are provided by the two InfoSources.<br />Update the key figures<br />0UNP_US_VAL - Unplanned consumption value<br />0TOT_US_VAL - Total consumption value<br />with all characteristics that are provided by the two InfoSources (except 0STOCKTYPE, 0BATCH, 0STOR_LOC and 0GN_VENDOR).<br /><br />5. Additional information<br /><br />See also Note 541559 for information about slow-moving item reports.<br /><br />You can also derive these further key figures using this adjusted data model of the 0IC_C03 InfoCube:<br />0NO_VAL_ISS Number of issues of valuated stock<br />0NO_VAL_REC Number of receipts of valuated stock<br />0NO_CNS_ISS Number of issues of consignment stock<br />0NO_CNS_REC Number of receipts of consignment stock<br />0NO_REVERSE Number of cancellations<br />0NO_TOT_US Number of total consumptions<br />0NO_UNPL_US Number of unplanned consumptions<br />You can do this using restricted/calculated key figures in the BeX Analyzer or in the update rules.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-IS-IC (Inventory Controlling)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D025681)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022263)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000804781/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804781/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "898083", "RefComponent": "BW-BCT", "RefTitle": "Composite SAP note for Business Content 3.0B SP 29", "RefUrl": "/notes/898083"}, {"RefNumber": "818835", "RefComponent": "BW-BCT", "RefTitle": "Composite SAP note for BI Content 3.3 Add-On Patch 18", "RefUrl": "/notes/818835"}, {"RefNumber": "786286", "RefComponent": "BW-BCT", "RefTitle": "Composite SAP note for BI Content 3.5.3 Add-On Patch 07", "RefUrl": "/notes/786286"}, {"RefNumber": "591063", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Queries/Web templates InfoCubes 0IC_C01/0IC_C02/0IC_C03", "RefUrl": "/notes/591063"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "591063", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Queries/Web templates InfoCubes 0IC_C01/0IC_C02/0IC_C03", "RefUrl": "/notes/591063 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "898083", "RefComponent": "BW-BCT", "RefTitle": "Composite SAP note for Business Content 3.0B SP 29", "RefUrl": "/notes/898083 "}, {"RefNumber": "818835", "RefComponent": "BW-BCT", "RefTitle": "Composite SAP note for BI Content 3.3 Add-On Patch 18", "RefUrl": "/notes/818835 "}, {"RefNumber": "786286", "RefComponent": "BW-BCT", "RefTitle": "Composite SAP note for BI Content 3.5.3 Add-On Patch 07", "RefUrl": "/notes/786286 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}