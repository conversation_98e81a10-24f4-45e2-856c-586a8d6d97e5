{"Request": {"Number": "1369296", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1415, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016842442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001369296?language=E&token=DB8AD068388FF841A04B261465D564F7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001369296", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001369296/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1369296"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1369296 - SAPBWNews BW 7.01 ABAP SP 09"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note deals with ABAP Support Package 09 for BI Release 7.01 (NW 7.0 Enhancement Package 01).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.01, BW Patches, BW Support Packages, business intelligence, BI, BI 7.01, SAPBINews</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBWNews for ABAP Support Package 09 of BW Release 7.01 (part of NetWeaver 7.0 Enhancement Package 1).<br /><br />Here you can find a list of all notes describing the corrections or enhancements provided in Support Package 09. This note will be updated when other notes are added. <br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li><strong>Manual actions that may be necessary:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>General information:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected by this Support Package; Enhancements delivered with this Support Package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See the release and information notes.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>Before you import the Support Package, check your system for inactive objects.  Call transaction SE80, select \"Inactive Objects\", and enter \"*\" into the user field. If information is displayed in the SAP namespace in the \"Transportable Objects\" section, you cannot import any Support Package.  You must first activate the inactive objects.  You can restart a terminated note installation consistently by using SNOTE consulting Note 1131831.  This activates the inactive objects.  Do not delete inactive objects from the transport request.  Note that you are no longer allowed to delete objects from the transport request and doing so would cause extensive subsequent problems. Note 822379 describes the correction procedure. </li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Obtain information about the most recent SAP Notes that you should take into account and, if necessary, implement after you import this Support Package. In the search function in the notes database or on SAP Service Marketplace, use search term: 'SAPKW700xx' (xx = current Support Package +1), and under 'Search Criteria--&gt; Extended Search Criteria--&gt; Priority', select: \"Hot News\" and \"Correction with high priority\". You can also restrict the search to a specific component by entering the relevant specifications in the \"Applic. Area\" field, for example BW-WHM*.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When you import a Support Package, syntax errors may occur for technical reasons. This is because notes that were already installed have been partly overwritten. In this case, you must install the inconsistent notes using transaction SNOTE. You can then continue with Support Package Manager (SPAM) processing. After you have completely imported a Support Package queue, transaction SNOTE should not display any inconsistent notes. If there are inconsistent notes in your system, it will not be able to function correctly.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Import the SNOTE corrections first and read composite SAP Note 875986 , which contains important notes about SAP Note Assistant.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If documents have already been migrated to the portal, you may have to repeat the migration. For more information, see Note 950877.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you have questions about downloading Support Package stacks, see Note 911032 'FAQ - SAP Support Package Stack Download'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For minor revisions in the BI Accelerator, see Note 1079068.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Errors that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>During a source system restore after the BW was copied, you do not notice that the connection is already used for the original BW system in ERP and that as a result, the source system cannot be restored. The restore seems to be successful. However, no changes are made. Loading data is still not possible because connection data is missing in the ERP system. For more information, refer to the corrections in SAP Note 1711490.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The InfoObject component check for Business Content does not work: If you install BI_CONT 7.04 or 7.05, the system issues errors of the type R7 322 when the InfoObject is activated or checked. The errors refer to an InfoObject that does not exist and that belongs to technical names such as 0DB*. For more information, see Note 1601003.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Runtime error GETWA_NOT_ASSIGNED; field symbol &lt;L_S_DATA_WIDE&gt;;OBJECT_NOT_STRUCTURED This problem occurs only if you have implemented Note 1564900, or imported Support Package 26 of SAP NetWeaver BW 7.00 or Support Package 09 of SAP NetWeaver BW 7.01. For more information, see Note 1573605.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>MDX: Data types are inconsistent after implementing Note 1563943. See Note 1605894 for information about this.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Errors corrected in this Support Package: </strong><br /><strong>Enhancements delivered with this Support Package:</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS (Basis System and Installation)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001369296/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001369296/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1570810", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW 7.30 time check during data loading to non-cum. InfoCubes", "RefUrl": "/notes/1570810"}, {"RefNumber": "1570756", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Generic BW delta is incorrect for numeric pointer", "RefUrl": "/notes/1570756"}, {"RefNumber": "1570559", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "cache mode 5: statistics fault with ITAB_DUPLICATE_KEY", "RefUrl": "/notes/1570559"}, {"RefNumber": "1569733", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Formulas: Lowercase letters", "RefUrl": "/notes/1569733"}, {"RefNumber": "1569601", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO Explorer: Sorting according to 0FISCPER is unexpected", "RefUrl": "/notes/1569601"}, {"RefNumber": "1569174", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P27:Mon:DM init:Source request list contains E-tab request", "RefUrl": "/notes/1569174"}, {"RefNumber": "1568913", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Duplicates of query components created using Query Designer", "RefUrl": "/notes/1568913"}, {"RefNumber": "1568869", "RefComponent": "BW-BEX-ET", "RefTitle": "RRI jump to remote system: performance \"RFC_FUNCTION_SEARCH\"", "RefUrl": "/notes/1568869"}, {"RefNumber": "1568815", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Subsequent correction to Note 1563856", "RefUrl": "/notes/1568815"}, {"RefNumber": "1568704", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Master data access type for result values does not work", "RefUrl": "/notes/1568704"}, {"RefNumber": "1568625", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP: Repair report for corrupt RSBMNODES table", "RefUrl": "/notes/1568625"}, {"RefNumber": "1568515", "RefComponent": "BW-WHM-AWB", "RefTitle": "P27:DWWB:Auth.: Remodeling ends AWB with termination message", "RefUrl": "/notes/1568515"}, {"RefNumber": "1568383", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "7.30(SP3)Incorrect warning message during Aggregation check", "RefUrl": "/notes/1568383"}, {"RefNumber": "1568381", "RefComponent": "BW-EI-APD", "RefTitle": "Filter transformation - Variable <> does not exist actively", "RefUrl": "/notes/1568381"}, {"RefNumber": "1568379", "RefComponent": "BW-WHM-DST", "RefTitle": "P27; STATMAN: Deleting old entries from RSSTATMANREQMDEL", "RefUrl": "/notes/1568379"}, {"RefNumber": "1568318", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Text variable is not replaced", "RefUrl": "/notes/1568318"}, {"RefNumber": "1567806", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Consulting:Dealing with Error message 'PSA fields too long'", "RefUrl": "/notes/1567806"}, {"RefNumber": "1567679", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI jump shows variable screen although not expected", "RefUrl": "/notes/1567679"}, {"RefNumber": "1567660", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P27:PC: No mess. in PC log if error during req. generation", "RefUrl": "/notes/1567660"}, {"RefNumber": "1567657", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P27:SDL:DB connect:DEC input fields with more than 9 chars.", "RefUrl": "/notes/1567657"}, {"RefNumber": "1567608", "RefComponent": "BW-WHM-MTD", "RefTitle": "TLOGO type CompositeProviders not visible in BW Metadata Rep", "RefUrl": "/notes/1567608"}, {"RefNumber": "1567541", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P27:DSO: Poor performance when you start request activation", "RefUrl": "/notes/1567541"}, {"RefNumber": "1567445", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Property 'Calculate Results As' is not inherited", "RefUrl": "/notes/1567445"}, {"RefNumber": "1567435", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P27:MON: \"Refresh All\" does not read current requests", "RefUrl": "/notes/1567435"}, {"RefNumber": "1567394", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Consulting note for sort sequence", "RefUrl": "/notes/1567394"}, {"RefNumber": "1567267", "RefComponent": "BW-WHM-DST", "RefTitle": "Restricted Selections in Reconstruction causes a short dump.", "RefUrl": "/notes/1567267"}, {"RefNumber": "1567184", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Access to adapter without storage not from DataSource", "RefUrl": "/notes/1567184"}, {"RefNumber": "1567113", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "The CompositeProvider activation ends in an endless loop", "RefUrl": "/notes/1567113"}, {"RefNumber": "1566941", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in new MD deletion during initial check", "RefUrl": "/notes/1566941"}, {"RefNumber": "1566912", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP Load Monitor dumps when display Master Data request", "RefUrl": "/notes/1566912"}, {"RefNumber": "1566879", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P27:PC:All messages issued when InfoPackage starts", "RefUrl": "/notes/1566879"}, {"RefNumber": "1566828", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query generation takes a long time", "RefUrl": "/notes/1566828"}, {"RefNumber": "1566628", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730: mistaken default persistence mode for OLAP cache", "RefUrl": "/notes/1566628"}, {"RefNumber": "1566574", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Compression of BW InfoCube does not remove last partition", "RefUrl": "/notes/1566574"}, {"RefNumber": "1566548", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP:PC:Transport:Spread of DTPs in PC:2 source NWs", "RefUrl": "/notes/1566548"}, {"RefNumber": "1566522", "RefComponent": "BW-WHM", "RefTitle": "SP27: RSRV tests for master data switched off for THJ Iobjs.", "RefUrl": "/notes/1566522"}, {"RefNumber": "1566451", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP: Incorrect requests: Starting same DTPs in parallel", "RefUrl": "/notes/1566451"}, {"RefNumber": "1566356", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPSRKK0 and form FILL_SP1_02", "RefUrl": "/notes/1566356"}, {"RefNumber": "1566101", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Exception Handling - MD Extraction", "RefUrl": "/notes/1566101"}, {"RefNumber": "1566080", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Lowercase letters not possible as data separators or escape", "RefUrl": "/notes/1566080"}, {"RefNumber": "1565544", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "SQL error in the context of the master data provider", "RefUrl": "/notes/1565544"}, {"RefNumber": "1565460", "RefComponent": "BW-WHM-DST", "RefTitle": "P27: Additional logs when the QS init selection is deleted", "RefUrl": "/notes/1565460"}, {"RefNumber": "1565418", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections for transaction RSLIMO", "RefUrl": "/notes/1565418"}, {"RefNumber": "1565382", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP27:RSAU 509 Error occurs in TimeSplit in Transformation", "RefUrl": "/notes/1565382"}, {"RefNumber": "1565372", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Normalizing to overall amt for universal display hierarchy", "RefUrl": "/notes/1565372"}, {"RefNumber": "1565140", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Problem when you delete in BW InfoCubes with own name space", "RefUrl": "/notes/1565140"}, {"RefNumber": "1565083", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Database crash during aggregate rollup/InfoCube compr.", "RefUrl": "/notes/1565083"}, {"RefNumber": "1565036", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Manually maintained validities are ignored", "RefUrl": "/notes/1565036"}, {"RefNumber": "1565018", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in CL_RSD_DC_SUPPORT_INFOCUBE and form GET_REFRESH", "RefUrl": "/notes/1565018"}, {"RefNumber": "1564947", "RefComponent": "BW-WHM-DST", "RefTitle": "P27: Dump in RSSM_MON_START_2ND_PROCESS: Enqueue RSREQDONE", "RefUrl": "/notes/1564947"}, {"RefNumber": "1564941", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Full deletion causes dump in RSSM_UPDATE_RSBKREQUEST", "RefUrl": "/notes/1564941"}, {"RefNumber": "1564900", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The virtual characteristic 0Infoprov is not available", "RefUrl": "/notes/1564900"}, {"RefNumber": "1564866", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Variables and displayed hierarchy levels", "RefUrl": "/notes/1564866"}, {"RefNumber": "1564839", "RefComponent": "BW-WHM-DST", "RefTitle": "P27:IOBJ: Delete master data request: Lock on RSICCONT", "RefUrl": "/notes/1564839"}, {"RefNumber": "1564719", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions dump: OBJECTS_WA_NOT_COMPATIBLE", "RefUrl": "/notes/1564719"}, {"RefNumber": "1564498", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Export to Excel: ABAP Run time", "RefUrl": "/notes/1564498"}, {"RefNumber": "1564060", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "RSRCACHE: Admin. Parameters cannot be set properly", "RefUrl": "/notes/1564060"}, {"RefNumber": "1563943", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Prerequisite for Note 1605894 and Note 1634931", "RefUrl": "/notes/1563943"}, {"RefNumber": "1563893", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Migration error DTP in content system", "RefUrl": "/notes/1563893"}, {"RefNumber": "1563856", "RefComponent": "BW-BEX-ET", "RefTitle": "Check of the variable table RSZGLOBV", "RefUrl": "/notes/1563856"}, {"RefNumber": "1563841", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Incorrect RSRT display for plan buffer query", "RefUrl": "/notes/1563841"}, {"RefNumber": "1563631", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Data even though SUBSELECT does not find any data", "RefUrl": "/notes/1563631"}, {"RefNumber": "1563593", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "CUBE_SAMPLE_CREATE_NEW", "RefUrl": "/notes/1563593"}, {"RefNumber": "1563496", "RefComponent": "BW-WHM-DST", "RefTitle": "P27:RSISIPTVERS:RS_ISIP_AFTER_IMPORT: Duplicate InfoPackages", "RefUrl": "/notes/1563496"}, {"RefNumber": "1563475", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "SP26:RSAR 682 Syntax error in template RSTMPL9C", "RefUrl": "/notes/1563475"}, {"RefNumber": "1563453", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "OR filters are not evaluated correctly", "RefUrl": "/notes/1563453"}, {"RefNumber": "1563385", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSEC: Return parameter of BAdI for the log", "RefUrl": "/notes/1563385"}, {"RefNumber": "1563268", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP27:Deletion of PSA doesnot delete Maintenance structures", "RefUrl": "/notes/1563268"}, {"RefNumber": "1563211", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: RS_BW_POST_MIGRATION issues", "RefUrl": "/notes/1563211"}, {"RefNumber": "1563163", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Dump UNCAUGHT_EXCEPTION - CL_RSBK_DTP_N raises CX_RS_PROGRAM", "RefUrl": "/notes/1563163"}, {"RefNumber": "1563000", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Arithmetic overflow in form get_mdc_cells", "RefUrl": "/notes/1563000"}, {"RefNumber": "1562952", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Complete deletion:All requests set to \"Deleted\"", "RefUrl": "/notes/1562952"}, {"RefNumber": "1562895", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P26:DSO: Activation starts even though not all DPs are green", "RefUrl": "/notes/1562895"}, {"RefNumber": "1562182", "RefComponent": "BW-EI-APD", "RefTitle": "APD: The object name is not allowed to be empty", "RefUrl": "/notes/1562182"}, {"RefNumber": "1562173", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: bXML flattening unbalanced hierarchies", "RefUrl": "/notes/1562173"}, {"RefNumber": "1561966", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "7.30SP3:TRF having DSO and MD lookups cannot be activated", "RefUrl": "/notes/1561966"}, {"RefNumber": "1561672", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Global filter and SUBSELECT", "RefUrl": "/notes/1561672"}, {"RefNumber": "1561617", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Long runtime in FORM SELECT_1", "RefUrl": "/notes/1561617"}, {"RefNumber": "1561539", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error ASSIGN_TYPE_CONFLICT for zero suppression", "RefUrl": "/notes/1561539"}, {"RefNumber": "1561338", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01SP9:F4 in changelog deletion variant doesnt show all DSO", "RefUrl": "/notes/1561338"}, {"RefNumber": "1561337", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "711SP07:Referential Integrity settings not transported", "RefUrl": "/notes/1561337"}, {"RefNumber": "1561236", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index without key figures", "RefUrl": "/notes/1561236"}, {"RefNumber": "1560958", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Improved error handling for the CompositeProvider", "RefUrl": "/notes/1560958"}, {"RefNumber": "1560900", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning sequence when saving", "RefUrl": "/notes/1560900"}, {"RefNumber": "1560285", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP:Termination for green U state sets a request", "RefUrl": "/notes/1560285"}, {"RefNumber": "1560172", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Mails incorrectly displayed in RSPC_METADATA_CLEANUP", "RefUrl": "/notes/1560172"}, {"RefNumber": "1560075", "RefComponent": "BW-WHM-DST", "RefTitle": "730: Enhancements for flat InfoCube", "RefUrl": "/notes/1560075"}, {"RefNumber": "1560036", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Missing data in a very specific situation", "RefUrl": "/notes/1560036"}, {"RefNumber": "1559843", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in one of the processes of new MD deletion", "RefUrl": "/notes/1559843"}, {"RefNumber": "1559716", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: ABAP runtime error ITAB_ILLEGAL_SORT_ORDER", "RefUrl": "/notes/1559716"}, {"RefNumber": "1559590", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "CompositeProvider adjustment", "RefUrl": "/notes/1559590"}, {"RefNumber": "1559557", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Subtype is missing when the transformation is migrated", "RefUrl": "/notes/1559557"}, {"RefNumber": "1559158", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Characteristic relationships: Deleting a step", "RefUrl": "/notes/1559158"}, {"RefNumber": "1558387", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BExAnalyzer: Planning Values are not transferred correctly", "RefUrl": "/notes/1558387"}, {"RefNumber": "1558230", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (SP26) InfoSource returns incorrect object status", "RefUrl": "/notes/1558230"}, {"RefNumber": "1558093", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSEC: New import paramater for virtual authorizations", "RefUrl": "/notes/1558093"}, {"RefNumber": "1558066", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "WAD Metadata corrupt due to invalid entries in RSZWMDITEM", "RefUrl": "/notes/1558066"}, {"RefNumber": "1557928", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache: swapped data does not become deleted", "RefUrl": "/notes/1557928"}, {"RefNumber": "1557602", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when resetting an expanded hierarchy", "RefUrl": "/notes/1557602"}, {"RefNumber": "1557457", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Syntax error in type pool RSDU in the lean stack", "RefUrl": "/notes/1557457"}, {"RefNumber": "1557174", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Enhancement for program CL_RSR_MDX_RSTT_TRACE_EXTRACT", "RefUrl": "/notes/1557174"}, {"RefNumber": "1556887", "RefComponent": "BI-RA-BICS", "RefTitle": "Bundle of RFC - Optimization of WAN scenario", "RefUrl": "/notes/1556887"}, {"RefNumber": "1556869", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Overflow during conversion", "RefUrl": "/notes/1556869"}, {"RefNumber": "1556796", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Compressing BW non-cumulative cubes slow for marker", "RefUrl": "/notes/1556796"}, {"RefNumber": "1556730", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:AWB:DWWB:Dump w/jump DTP -> transport connection", "RefUrl": "/notes/1556730"}, {"RefNumber": "1556689", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "DTP monitor: InfoCube loading errors are not displayed", "RefUrl": "/notes/1556689"}, {"RefNumber": "1556443", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DTA:DSO:WO-DSO:PSA:PC:Dump in PSA process for WO DSO", "RefUrl": "/notes/1556443"}, {"RefNumber": "1556122", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Performance optimization during master data update", "RefUrl": "/notes/1556122"}, {"RefNumber": "1556030", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Performance: PSA status management is slow", "RefUrl": "/notes/1556030"}, {"RefNumber": "1556026", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:WO-DSO:RSREQICODS: Missing activation request entries", "RefUrl": "/notes/1556026"}, {"RefNumber": "1555941", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "70SP26:After import method fails during BI_CONT upgrade", "RefUrl": "/notes/1555941"}, {"RefNumber": "1555876", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BCD_OVERFLOW error in OLAP cache mode 5 statistics", "RefUrl": "/notes/1555876"}, {"RefNumber": "1555872", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Message type X dump in FM RSSTATMAN_GET_PARTTAB_PSA", "RefUrl": "/notes/1555872"}, {"RefNumber": "1555706", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA: <PERSON> Already Executed in Background Job", "RefUrl": "/notes/1555706"}, {"RefNumber": "1555614", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1555614"}, {"RefNumber": "1555531", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "REOPEN: Always probs generating after SP stack (499933/2010)", "RefUrl": "/notes/1555531"}, {"RefNumber": "1555524", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Conversion error: non-numeric values in formula variables", "RefUrl": "/notes/1555524"}, {"RefNumber": "1555430", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Optimization of virtual providers in the data manager", "RefUrl": "/notes/1555430"}, {"RefNumber": "1555413", "RefComponent": "BW-WHM-DST", "RefTitle": "Preparations for ODP access", "RefUrl": "/notes/1555413"}, {"RefNumber": "1555152", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Compressing is slow during deletion for F table", "RefUrl": "/notes/1555152"}, {"RefNumber": "1555066", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "DTP:SP26: PC Variant import fails with no message details", "RefUrl": "/notes/1555066"}, {"RefNumber": "1554973", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSEC: 'No authorisation' double occurence of external char.", "RefUrl": "/notes/1554973"}, {"RefNumber": "1554929", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Dummy", "RefUrl": "/notes/1554929"}, {"RefNumber": "1554841", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO Explorer: '*/*' is an invalid value", "RefUrl": "/notes/1554841"}, {"RefNumber": "1554780", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query object IP/!IP could not be collected...", "RefUrl": "/notes/1554780"}, {"RefNumber": "1554702", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:After import:T version overlaps; no message", "RefUrl": "/notes/1554702"}, {"RefNumber": "1554647", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:ORA: Performance: Incorrect parallel degree setting", "RefUrl": "/notes/1554647"}, {"RefNumber": "1554603", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Translated text are lost after activation of content query", "RefUrl": "/notes/1554603"}, {"RefNumber": "1554574", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Filter in Content version is deactivated", "RefUrl": "/notes/1554574"}, {"RefNumber": "1554561", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache mode 5 incomplete statistics", "RefUrl": "/notes/1554561"}, {"RefNumber": "1554508", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:SY-UCOMM is not cleared after execution", "RefUrl": "/notes/1554508"}, {"RefNumber": "1554385", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DWWB:Assigning InfoObject to an area: No required entry", "RefUrl": "/notes/1554385"}, {"RefNumber": "1554344", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1554344"}, {"RefNumber": "1554274", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Clean up class CL_RSBM_LOG_DATAPACKAGE", "RefUrl": "/notes/1554274"}, {"RefNumber": "1554272", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Long runtime if crossjoin is used", "RefUrl": "/notes/1554272"}, {"RefNumber": "1554217", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Save of filter is not cancelled in case of CTS error", "RefUrl": "/notes/1554217"}, {"RefNumber": "1554150", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (Support Package 26): Dump during migration", "RefUrl": "/notes/1554150"}, {"RefNumber": "1554108", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in New MD deletion where used checks", "RefUrl": "/notes/1554108"}, {"RefNumber": "1554098", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "GETWA_NOT_ASSIGNED when you execute RSPC_METADATA_CLEANUP", "RefUrl": "/notes/1554098"}, {"RefNumber": "1553927", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: High memory consumption; crossjoin, intersect, generate", "RefUrl": "/notes/1553927"}, {"RefNumber": "1553696", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Incorrect check during transport of content roles (ACGR)", "RefUrl": "/notes/1553696"}, {"RefNumber": "1553608", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Changelog Deletion Variant doesnt work in BW 7.0X-7.3X", "RefUrl": "/notes/1553608"}, {"RefNumber": "1553601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Adjustments for the input help", "RefUrl": "/notes/1553601"}, {"RefNumber": "1553368", "RefComponent": "BW-WHM", "RefTitle": "Lockwait situation for table NRIV", "RefUrl": "/notes/1553368"}, {"RefNumber": "1553334", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Msg. R3 416 instead of a relevant msg. during direct access", "RefUrl": "/notes/1553334"}, {"RefNumber": "1553333", "RefComponent": "BC-EIM-ODP", "RefTitle": "Release of DataSources for ODP", "RefUrl": "/notes/1553333"}, {"RefNumber": "1553204", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BWA: Delta property lost when index is recreated", "RefUrl": "/notes/1553204"}, {"RefNumber": "1553201", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.11SP07:RSAR_PSA_NEWDS_MAPPING_CHECK considers inactive PSA", "RefUrl": "/notes/1553201"}, {"RefNumber": "1553159", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems with initial value '#'", "RefUrl": "/notes/1553159"}, {"RefNumber": "1553144", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "No RFC authorization for function group RFC_METADATA", "RefUrl": "/notes/1553144"}, {"RefNumber": "1552825", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:DTP:Filter in Content Version of DTP is deleted", "RefUrl": "/notes/1552825"}, {"RefNumber": "1552762", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual characteristic 0INFOPROV is ignored", "RefUrl": "/notes/1552762"}, {"RefNumber": "1552741", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Performance Optimization :SID Flag Updates - Follow-up Note", "RefUrl": "/notes/1552741"}, {"RefNumber": "1552573", "RefComponent": "BW-BCT-FI-GL", "RefTitle": "Query with virtual provider shows incorrect data", "RefUrl": "/notes/1552573"}, {"RefNumber": "1552482", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Activation for delta DTP fails: Invalid parameter", "RefUrl": "/notes/1552482"}, {"RefNumber": "1552001", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "A query on a MultiProvider terminates", "RefUrl": "/notes/1552001"}, {"RefNumber": "1551985", "RefComponent": "BW-PLA-BPS", "RefTitle": "Program termination occurs when maintaining documents", "RefUrl": "/notes/1551985"}, {"RefNumber": "1551792", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Data mart: Deadlock on table RSBKREQUEST", "RefUrl": "/notes/1551792"}, {"RefNumber": "1551587", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: SUBSELECT and external hierarchies", "RefUrl": "/notes/1551587"}, {"RefNumber": "1551586", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Duplicated technical names of query components", "RefUrl": "/notes/1551586"}, {"RefNumber": "1551565", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P26: PC: PSA: Length of delay times reading PSA admin data", "RefUrl": "/notes/1551565"}, {"RefNumber": "1551528", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Issues with values when Selection option variable is used.", "RefUrl": "/notes/1551528"}, {"RefNumber": "1551463", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Optimization in the data manager", "RefUrl": "/notes/1551463"}, {"RefNumber": "1551354", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26:ASSERTION_FAILED in single rule test(Infoset as Source)", "RefUrl": "/notes/1551354"}, {"RefNumber": "1551352", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Various minor issues in the Planning Modeler", "RefUrl": "/notes/1551352"}, {"RefNumber": "1551289", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:TRF:After import: Error message unclear, TRF missing", "RefUrl": "/notes/1551289"}, {"RefNumber": "1551005", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:After-import:T versions are no longer deleted", "RefUrl": "/notes/1551005"}, {"RefNumber": "1550919", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Deadlock/lock wait for complete deletion of DTA content", "RefUrl": "/notes/1550919"}, {"RefNumber": "1550918", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P26:BAPI_IPAK_CHANGE undoes field for warning handling", "RefUrl": "/notes/1550918"}, {"RefNumber": "1550723", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "System error in program CL_RSR and form GET_COB_PRO-01-", "RefUrl": "/notes/1550723"}, {"RefNumber": "1550270", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "DTP-LOG; no LOG for authorization problems", "RefUrl": "/notes/1550270"}, {"RefNumber": "1550187", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Termination in CL_RSDDB_INDEX_M and BUILD_TH_JOIN", "RefUrl": "/notes/1550187"}, {"RefNumber": "1549726", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2 and form FAC_VARIABLES-03-", "RefUrl": "/notes/1549726"}, {"RefNumber": "1549724", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Incorrect data for filter on Thj leaf", "RefUrl": "/notes/1549724"}, {"RefNumber": "1549224", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Request generation also reads deleted requests", "RefUrl": "/notes/1549224"}, {"RefNumber": "1549127", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Formulas of function module calls", "RefUrl": "/notes/1549127"}, {"RefNumber": "1549040", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P26:DSO:Deleting activation queue when AQ empty", "RefUrl": "/notes/1549040"}, {"RefNumber": "1548635", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Corrections to the time service routines in BW", "RefUrl": "/notes/1548635"}, {"RefNumber": "1548619", "RefComponent": "BW-BEX", "RefTitle": "BW-IP: internal", "RefUrl": "/notes/1548619"}, {"RefNumber": "1548452", "RefComponent": "BW-BEX-OT", "RefTitle": "New Infocube Load : Memory Leak", "RefUrl": "/notes/1548452"}, {"RefNumber": "1548259", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORACLE: Compression of UNIQUE INDEX of PSA tables", "RefUrl": "/notes/1548259"}, {"RefNumber": "1548242", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW cube indexes incorrect if no time char. in aggregate", "RefUrl": "/notes/1548242"}, {"RefNumber": "1548170", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "RRMX launches Excel, but the connection is not established", "RefUrl": "/notes/1548170"}, {"RefNumber": "1547838", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:DWB: Search for DTPs in workbench is slow", "RefUrl": "/notes/1547838"}, {"RefNumber": "1547708", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: some Key Figures are not visible", "RefUrl": "/notes/1547708"}, {"RefNumber": "1547667", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Error DTP:No LOGSYS with p_r_request->get_logsys", "RefUrl": "/notes/1547667"}, {"RefNumber": "1547453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Explain: X message for explain for formula with variables", "RefUrl": "/notes/1547453"}, {"RefNumber": "1547419", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "<PERSON>um<PERSON> Missing in Bex Analyzer", "RefUrl": "/notes/1547419"}, {"RefNumber": "1547391", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data when node is expanded", "RefUrl": "/notes/1547391"}, {"RefNumber": "1547290", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "DTP is not generated -> two source systems/IOBJ target", "RefUrl": "/notes/1547290"}, {"RefNumber": "1547271", "RefComponent": "BW-WHM-AWB", "RefTitle": "Missing authorization check in RFC with call transaction", "RefUrl": "/notes/1547271"}, {"RefNumber": "1546975", "RefComponent": "BW-BEX-OT", "RefTitle": "Internal <-> external conversion to DECFLOAT34 key figures", "RefUrl": "/notes/1546975"}, {"RefNumber": "1546906", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Enqueue: Minor improvements for creation of request", "RefUrl": "/notes/1546906"}, {"RefNumber": "1546399", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error in program CL_RSR and form GET_CHANM-02", "RefUrl": "/notes/1546399"}, {"RefNumber": "1545971", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Incorrect value for property 'MEMBER_UNIQUE_NAME'", "RefUrl": "/notes/1545971"}, {"RefNumber": "1545857", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:PC:Process chain step set t.red when request deleted", "RefUrl": "/notes/1545857"}, {"RefNumber": "1545834", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Authorization check on S_RFC_ADM", "RefUrl": "/notes/1545834"}, {"RefNumber": "1545818", "RefComponent": "BW-BEX", "RefTitle": "Orange", "RefUrl": "/notes/1545818"}, {"RefNumber": "1545663", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:WO-DSO:RSBX_BIW_GET_ODSDATA:Myself extraction: Selection", "RefUrl": "/notes/1545663"}, {"RefNumber": "1545450", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2, form LRECH_F_CHFP_01-01-", "RefUrl": "/notes/1545450"}, {"RefNumber": "1544520", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No Authorization with hierarchy authorization + interval", "RefUrl": "/notes/1544520"}, {"RefNumber": "1544434", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Properties of query elements are not evaluated correctly", "RefUrl": "/notes/1544434"}, {"RefNumber": "1544249", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Dump DYNPRO_FIELD_CONVERSION in screen 104", "RefUrl": "/notes/1544249"}, {"RefNumber": "1543704", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "CompositeProvider corrections", "RefUrl": "/notes/1543704"}, {"RefNumber": "1542855", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Simulation:RSBKDTPREPAIR_MAXSIZE: No list", "RefUrl": "/notes/1542855"}, {"RefNumber": "1542810", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Consulting:MD Update when key fields are mapped", "RefUrl": "/notes/1542810"}, {"RefNumber": "1541274", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP26:Impact on transfer structure - Infoobject Activation", "RefUrl": "/notes/1541274"}, {"RefNumber": "1541216", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1541216"}, {"RefNumber": "1540840", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "730:DTP:Performance:<PERSON><PERSON><PERSON>. Achtung:Hinweis NIE freigeben", "RefUrl": "/notes/1540840"}, {"RefNumber": "1540733", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Data flow migration: Name of InfoSource is lost", "RefUrl": "/notes/1540733"}, {"RefNumber": "1540688", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 BRAIN CL_RSDRC_PROVRQ_SRVS GET_SELDR_FROM_PROV_RQDR-01-", "RefUrl": "/notes/1540688"}, {"RefNumber": "1540666", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Screen 101 of program SAPLRSSM_PROCESS delivered again", "RefUrl": "/notes/1540666"}, {"RefNumber": "1540583", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Missing restrictions via the label", "RefUrl": "/notes/1540583"}, {"RefNumber": "1540273", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Different Issues with selector on F4", "RefUrl": "/notes/1540273"}, {"RefNumber": "1540186", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: RSSM_EXPAND_REQUESTLIST: Expansion in wrong order", "RefUrl": "/notes/1540186"}, {"RefNumber": "1540010", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Subsequent corrections to Note 1531144", "RefUrl": "/notes/1540010"}, {"RefNumber": "1540008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems in the context of SAP Business ByDesign - Part 2", "RefUrl": "/notes/1540008"}, {"RefNumber": "1539990", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate w/time hierarchy is compressed (2)", "RefUrl": "/notes/1539990"}, {"RefNumber": "1539903", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Optimization of master data access to Teradata", "RefUrl": "/notes/1539903"}, {"RefNumber": "1539601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Exception \"NAME_ERROR\" in the class CL_RSMD_RS_TREX_QUERY", "RefUrl": "/notes/1539601"}, {"RefNumber": "1539445", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DTP:DataSource extractor:PSA extractor:Join/cleanup", "RefUrl": "/notes/1539445"}, {"RefNumber": "1539234", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No message if authorization is missing", "RefUrl": "/notes/1539234"}, {"RefNumber": "1539226", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP27:Deletion of the Infobject terminates with error R7 157", "RefUrl": "/notes/1539226"}, {"RefNumber": "1539178", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:RSBX_BIW_GET_ODSDATA:Memory overflow:For all entries", "RefUrl": "/notes/1539178"}, {"RefNumber": "1538876", "RefComponent": "BW-BEX", "RefTitle": "BRAIN X299 in class CL_RSR_CHABIT; form SET_BIT1-02-", "RefUrl": "/notes/1538876"}, {"RefNumber": "1538402", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query terminates during generation", "RefUrl": "/notes/1538402"}, {"RefNumber": "1538109", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error when PARTITIONMODE 2 or 3", "RefUrl": "/notes/1538109"}, {"RefNumber": "1537931", "RefComponent": "BW-BEX", "RefTitle": "MP + non-cumulative and multiple assgmt of characteristics", "RefUrl": "/notes/1537931"}, {"RefNumber": "1537570", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1537570"}, {"RefNumber": "1537314", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "FB RSNDI_SHIE_SUBTREE_DELETE: SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/1537314"}, {"RefNumber": "1537125", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "SP26:Transaction RSH1 doesnt work correctly in display mode", "RefUrl": "/notes/1537125"}, {"RefNumber": "1536816", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 Enhanced SP compression postcorrection", "RefUrl": "/notes/1536816"}, {"RefNumber": "1536468", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS job cancels during status confirmation of a process.", "RefUrl": "/notes/1536468"}, {"RefNumber": "1536334", "RefComponent": "BW-BEX", "RefTitle": "DTP loading; archive areas are not checked correctly", "RefUrl": "/notes/1536334"}, {"RefNumber": "1536212", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "OLAP cache and currency translation", "RefUrl": "/notes/1536212"}, {"RefNumber": "1535607", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error in RSRV text on InfoCube with referencing key figure", "RefUrl": "/notes/1535607"}, {"RefNumber": "1535604", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: DTP: Manage: Where-used: No reset for DM", "RefUrl": "/notes/1535604"}, {"RefNumber": "1535576", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26: NOP key figures in transformations", "RefUrl": "/notes/1535576"}, {"RefNumber": "1535548", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "WJR:THJ - Subsequent correction to note 1464244", "RefUrl": "/notes/1535548"}, {"RefNumber": "1535514", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:DSP:PC:Truncate:Locked act. queue end with act. red", "RefUrl": "/notes/1535514"}, {"RefNumber": "1535483", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables exit i_step = 3 is not executed", "RefUrl": "/notes/1535483"}, {"RefNumber": "1535124", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Endless loop for complete deletion of DTA contents", "RefUrl": "/notes/1535124"}, {"RefNumber": "1535070", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 enhanced compression on large cache objects", "RefUrl": "/notes/1535070"}, {"RefNumber": "1535008", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Delta DTP: Transport with BEx variables or routines", "RefUrl": "/notes/1535008"}, {"RefNumber": "1535006", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1535006"}, {"RefNumber": "1534889", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Short dump during master data update (second attempt)", "RefUrl": "/notes/1534889"}, {"RefNumber": "1534748", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Quantity conversion does not take place", "RefUrl": "/notes/1534748"}, {"RefNumber": "1534680", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Hierarchy date variable is not filled when you navigate", "RefUrl": "/notes/1534680"}, {"RefNumber": "1534298", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1534298"}, {"RefNumber": "1534285", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RSBBS: Problems during the administration of jump targets", "RefUrl": "/notes/1534285"}, {"RefNumber": "1534231", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the case of constant selection and compounding", "RefUrl": "/notes/1534231"}, {"RefNumber": "1534209", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Overflow dump in report SAP_INFOCUBE_DESIGNS (2)", "RefUrl": "/notes/1534209"}, {"RefNumber": "1533694", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Red, updated DP cannot be repaired", "RefUrl": "/notes/1533694"}, {"RefNumber": "1533515", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "New MasterData Deletion: Usage check extended to include NLS", "RefUrl": "/notes/1533515"}, {"RefNumber": "1533381", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:Too many messages in DTP monitor during MD update", "RefUrl": "/notes/1533381"}, {"RefNumber": "1533378", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BExAnalyzer: Saving of Workbooks by Multiple Users", "RefUrl": "/notes/1533378"}, {"RefNumber": "1533324", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "No 'Standard Text' for hierarchy node is displayed", "RefUrl": "/notes/1533324"}, {"RefNumber": "1533243", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CL_RSMD_RS_TREX_QUERY->IF_RSMD_RS_BUILD_QUERY~READ_DATA", "RefUrl": "/notes/1533243"}, {"RefNumber": "1533143", "RefComponent": "BW-SYS", "RefTitle": "SP28:RS_BW_POST_MIGRATION doesnt convert PSAs of new DS", "RefUrl": "/notes/1533143"}, {"RefNumber": "1533115", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message: \"TREX call with check_n and FEMS_N\"", "RefUrl": "/notes/1533115"}, {"RefNumber": "1532837", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW write-optimized DSO, LISTCUBE and selective deletion", "RefUrl": "/notes/1532837"}, {"RefNumber": "1532624", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: REQARCH: Endless loop in write report", "RefUrl": "/notes/1532624"}, {"RefNumber": "1532596", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Explorer: 2920:multiprovider schema is not consistent;", "RefUrl": "/notes/1532596"}, {"RefNumber": "1532278", "RefComponent": "BW-BEX", "RefTitle": "Read authorization check for virtual providers", "RefUrl": "/notes/1532278"}, {"RefNumber": "1532223", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search based on calendar month/year does not work", "RefUrl": "/notes/1532223"}, {"RefNumber": "1532061", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Dump in process chains for sequence during saving", "RefUrl": "/notes/1532061"}, {"RefNumber": "1531920", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Error during InfoPackage import for non-existing target", "RefUrl": "/notes/1531920"}, {"RefNumber": "1531678", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT:Incorrect results when F4 in variable screen java (WJR)", "RefUrl": "/notes/1531678"}, {"RefNumber": "1531519", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Subsequent correction to Note 1385580 and Note 1431226", "RefUrl": "/notes/1531519"}, {"RefNumber": "1531453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and constant selection", "RefUrl": "/notes/1531453"}, {"RefNumber": "1531437", "RefComponent": "BW-BEX-ET-WEB-ITM", "RefTitle": "Role Menu Item: Javascript error with special characters", "RefUrl": "/notes/1531437"}, {"RefNumber": "1531427", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Extended where-used list for query elements", "RefUrl": "/notes/1531427"}, {"RefNumber": "1530803", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error CX_SY_DYN_TABLE_ILL_COMP_VAL; in  _SORT_X", "RefUrl": "/notes/1530803"}, {"RefNumber": "1530624", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "RSRHIEDIR_OLAP inconsistency,Deleted entries of INFOAREAHIER", "RefUrl": "/notes/1530624"}, {"RefNumber": "1530546", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Filling of formula variable via report-report interface", "RefUrl": "/notes/1530546"}, {"RefNumber": "1530454", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: XML injection when an XMLA interface is used", "RefUrl": "/notes/1530454"}, {"RefNumber": "1530319", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01SP9:New PSA Del Var ignores PSA of Exp DS of T-DSO/W-DSO", "RefUrl": "/notes/1530319"}, {"RefNumber": "1530274", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjustments to the CompositeProvider", "RefUrl": "/notes/1530274"}, {"RefNumber": "1530271", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception condition \"INCONSISTENCY\" raised", "RefUrl": "/notes/1530271"}, {"RefNumber": "1530255", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW selective deletion w/ very large single record conditions", "RefUrl": "/notes/1530255"}, {"RefNumber": "1530165", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:SDL:PC:Enabling user entries for InfoPackages", "RefUrl": "/notes/1530165"}, {"RefNumber": "1530110", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "F4 help for Attribute change run in PCs works incorrect", "RefUrl": "/notes/1530110"}, {"RefNumber": "1529997", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "deficient OLAP cache mode 6 'Shared Objects'", "RefUrl": "/notes/1529997"}, {"RefNumber": "1529720", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No data for calculated member and metadata reference", "RefUrl": "/notes/1529720"}, {"RefNumber": "1529591", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Tracing für BI Content Extraction", "RefUrl": "/notes/1529591"}, {"RefNumber": "1529540", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "BW InfoObject with RDA, navigation attributes and aggregates", "RefUrl": "/notes/1529540"}, {"RefNumber": "1529488", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.30 (SP02) Falsche Meldung", "RefUrl": "/notes/1529488"}, {"RefNumber": "1529455", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Stammdaten Optimierungen", "RefUrl": "/notes/1529455"}, {"RefNumber": "1529440", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: No text for new DataSources in request list", "RefUrl": "/notes/1529440"}, {"RefNumber": "1529330", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Deadlock when DSO contents are deleted in parallel", "RefUrl": "/notes/1529330"}, {"RefNumber": "1529152", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Runtime error in MDXTEST with missing authorization", "RefUrl": "/notes/1529152"}, {"RefNumber": "1529080", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01 SP9:PSA/CLG deletion variant save doesnt work correctly", "RefUrl": "/notes/1529080"}, {"RefNumber": "1528921", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Queries regenerated each time during execution", "RefUrl": "/notes/1528921"}, {"RefNumber": "1528906", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS dispatcher does not remove nodes for deleted PC Log IDs", "RefUrl": "/notes/1528906"}, {"RefNumber": "1528875", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Incorrect authorization check f. jump destination activation", "RefUrl": "/notes/1528875"}, {"RefNumber": "1528869", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_MEMBERS, CP/NP, Prty Member_Caption", "RefUrl": "/notes/1528869"}, {"RefNumber": "1528864", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Query generation generates syntax error for non-cml. cubes", "RefUrl": "/notes/1528864"}, {"RefNumber": "1528374", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error GET_PRPTY_VALUE-02-", "RefUrl": "/notes/1528374"}, {"RefNumber": "1528332", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0(SP26) Fehlerhafte Anzeige InfoSource", "RefUrl": "/notes/1528332"}, {"RefNumber": "1528293", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Dump in report RSSM_SM50_ALT", "RefUrl": "/notes/1528293"}, {"RefNumber": "1528191", "RefComponent": "BW-BEX-OT", "RefTitle": "Incorrect payment in case of MultiProvider with delta pair", "RefUrl": "/notes/1528191"}, {"RefNumber": "1528052", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java View cannot be loaded in ABAP runtime", "RefUrl": "/notes/1528052"}, {"RefNumber": "1527970", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Log overflow during query processing", "RefUrl": "/notes/1527970"}, {"RefNumber": "1527930", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination in BW aggregates with COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/1527930"}, {"RefNumber": "1527868", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BW InfoCube with BIA displays compressed aggregates", "RefUrl": "/notes/1527868"}, {"RefNumber": "1527844", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "%_HINTS ORACLE not built correct in the code LRRSIF03", "RefUrl": "/notes/1527844"}, {"RefNumber": "1527808", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BexAnalyzer:Jump to an Transaction freezes Sap GUI window", "RefUrl": "/notes/1527808"}, {"RefNumber": "1527728", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Error handler: Endless loop during deletion", "RefUrl": "/notes/1527728"}, {"RefNumber": "1527726", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "730:New DS & hierarchy sel. w/o existing hierarchy headers", "RefUrl": "/notes/1527726"}, {"RefNumber": "1527063", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data with constant selection and compounding", "RefUrl": "/notes/1527063"}, {"RefNumber": "1527025", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Manage:Complete data target deletion and request list", "RefUrl": "/notes/1527025"}, {"RefNumber": "1527011", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_STREAMINFO & fixed fiscal variant", "RefUrl": "/notes/1527011"}, {"RefNumber": "1526867", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Query does not return navigation attribute in MultiProvider", "RefUrl": "/notes/1526867"}, {"RefNumber": "1526857", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Incorrect display of destination", "RefUrl": "/notes/1526857"}, {"RefNumber": "1526835", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:REQARCH: Corr. in report RSREQARCH_REMO_INCONSISTENCIES", "RefUrl": "/notes/1526835"}, {"RefNumber": "1526833", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P26: RDA: No F1 help in real-time fields in the scheduler", "RefUrl": "/notes/1526833"}, {"RefNumber": "1526821", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Hierarchy without unassigned node has incorrect totals", "RefUrl": "/notes/1526821"}, {"RefNumber": "1526816", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Not possible to skip processes that have errors", "RefUrl": "/notes/1526816"}, {"RefNumber": "1526642", "RefComponent": "BW-PLA-IP", "RefTitle": "Adding a free characteristic unnecessary (BRAIN 224)", "RefUrl": "/notes/1526642"}, {"RefNumber": "1526618", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Fields in PSA have no text", "RefUrl": "/notes/1526618"}, {"RefNumber": "1526599", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Wrong values filtered with manual input on the Var. Screen", "RefUrl": "/notes/1526599"}, {"RefNumber": "1526103", "RefComponent": "BW-WHM-DBA-MPRO", "RefTitle": "MultiProvider activation abends with x message", "RefUrl": "/notes/1526103"}, {"RefNumber": "1526045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Non-unique hierarchies, link nodes, and structures", "RefUrl": "/notes/1526045"}, {"RefNumber": "1525723", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Program termination in program RSPC_LOG_DELETE", "RefUrl": "/notes/1525723"}, {"RefNumber": "1525635", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Additional index for RSZWVIEW table", "RefUrl": "/notes/1525635"}, {"RefNumber": "1525546", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Destination transport RC = 8", "RefUrl": "/notes/1525546"}, {"RefNumber": "1525419", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate with time hierarchy is compressed", "RefUrl": "/notes/1525419"}, {"RefNumber": "1525127", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Filter for hierarchies and empty mode", "RefUrl": "/notes/1525127"}, {"RefNumber": "1519889", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP processing: \"Database selection was interrupted\"", "RefUrl": "/notes/1519889"}, {"RefNumber": "1519344", "RefComponent": "BW-WHM-DST", "RefTitle": "Syntax error in RSSM_REDUCE_REQUESTLIST", "RefUrl": "/notes/1519344"}, {"RefNumber": "1515371", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Currency translation does not work", "RefUrl": "/notes/1515371"}, {"RefNumber": "1487382", "RefComponent": "BW-BEX-ET-ER", "RefTitle": "Duplicated serach result in Report Designer", "RefUrl": "/notes/1487382"}, {"RefNumber": "1481835", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Performance of INTERSECT() function or Incorrect data", "RefUrl": "/notes/1481835"}, {"RefNumber": "1416952", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 BAdI", "RefUrl": "/notes/1416952"}, {"RefNumber": "1370227", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT : Hierarchy activation dump", "RefUrl": "/notes/1370227"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1958826", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "<PERSON>um<PERSON> Missing in Bex Analyzer", "RefUrl": "/notes/1958826 "}, {"RefNumber": "1534285", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RSBBS: Problems during the administration of jump targets", "RefUrl": "/notes/1534285 "}, {"RefNumber": "1416952", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 BAdI", "RefUrl": "/notes/1416952 "}, {"RefNumber": "1548452", "RefComponent": "BW-BEX-OT", "RefTitle": "New Infocube Load : Memory Leak", "RefUrl": "/notes/1548452 "}, {"RefNumber": "1567267", "RefComponent": "BW-WHM-DST", "RefTitle": "Restricted Selections in Reconstruction causes a short dump.", "RefUrl": "/notes/1567267 "}, {"RefNumber": "1530624", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "RSRHIEDIR_OLAP inconsistency,Deleted entries of INFOAREAHIER", "RefUrl": "/notes/1530624 "}, {"RefNumber": "1565083", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Database crash during aggregate rollup/InfoCube compr.", "RefUrl": "/notes/1565083 "}, {"RefNumber": "1537314", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "FB RSNDI_SHIE_SUBTREE_DELETE: SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/1537314 "}, {"RefNumber": "1531437", "RefComponent": "BW-BEX-ET-WEB-ITM", "RefTitle": "Role Menu Item: Javascript error with special characters", "RefUrl": "/notes/1531437 "}, {"RefNumber": "1540273", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Different Issues with selector on F4", "RefUrl": "/notes/1540273 "}, {"RefNumber": "1542855", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Simulation:RSBKDTPREPAIR_MAXSIZE: No list", "RefUrl": "/notes/1542855 "}, {"RefNumber": "1533515", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "New MasterData Deletion: Usage check extended to include NLS", "RefUrl": "/notes/1533515 "}, {"RefNumber": "1536816", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 Enhanced SP compression postcorrection", "RefUrl": "/notes/1536816 "}, {"RefNumber": "1557602", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when resetting an expanded hierarchy", "RefUrl": "/notes/1557602 "}, {"RefNumber": "1531678", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT:Incorrect results when F4 in variable screen java (WJR)", "RefUrl": "/notes/1531678 "}, {"RefNumber": "1556887", "RefComponent": "BI-RA-BICS", "RefTitle": "Bundle of RFC - Optimization of WAN scenario", "RefUrl": "/notes/1556887 "}, {"RefNumber": "1525127", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Filter for hierarchies and empty mode", "RefUrl": "/notes/1525127 "}, {"RefNumber": "1526816", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Not possible to skip processes that have errors", "RefUrl": "/notes/1526816 "}, {"RefNumber": "1553608", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Changelog Deletion Variant doesnt work in BW 7.0X-7.3X", "RefUrl": "/notes/1553608 "}, {"RefNumber": "1567806", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Consulting:Dealing with Error message 'PSA fields too long'", "RefUrl": "/notes/1567806 "}, {"RefNumber": "1555066", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "DTP:SP26: PC Variant import fails with no message details", "RefUrl": "/notes/1555066 "}, {"RefNumber": "1535070", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 enhanced compression on large cache objects", "RefUrl": "/notes/1535070 "}, {"RefNumber": "1569733", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Formulas: Lowercase letters", "RefUrl": "/notes/1569733 "}, {"RefNumber": "1519344", "RefComponent": "BW-WHM-DST", "RefTitle": "Syntax error in RSSM_REDUCE_REQUESTLIST", "RefUrl": "/notes/1519344 "}, {"RefNumber": "1570756", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Generic BW delta is incorrect for numeric pointer", "RefUrl": "/notes/1570756 "}, {"RefNumber": "1553333", "RefComponent": "BC-EIM-ODP", "RefTitle": "Release of DataSources for ODP", "RefUrl": "/notes/1553333 "}, {"RefNumber": "1535604", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: DTP: Manage: Where-used: No reset for DM", "RefUrl": "/notes/1535604 "}, {"RefNumber": "1553696", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Incorrect check during transport of content roles (ACGR)", "RefUrl": "/notes/1553696 "}, {"RefNumber": "1563943", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Prerequisite for Note 1605894 and Note 1634931", "RefUrl": "/notes/1563943 "}, {"RefNumber": "1528869", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_MEMBERS, CP/NP, Prty Member_Caption", "RefUrl": "/notes/1528869 "}, {"RefNumber": "1539234", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No message if authorization is missing", "RefUrl": "/notes/1539234 "}, {"RefNumber": "1547419", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "<PERSON>um<PERSON> Missing in Bex Analyzer", "RefUrl": "/notes/1547419 "}, {"RefNumber": "1553144", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "No RFC authorization for function group RFC_METADATA", "RefUrl": "/notes/1553144 "}, {"RefNumber": "1540688", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 BRAIN CL_RSDRC_PROVRQ_SRVS GET_SELDR_FROM_PROV_RQDR-01-", "RefUrl": "/notes/1540688 "}, {"RefNumber": "1532837", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW write-optimized DSO, LISTCUBE and selective deletion", "RefUrl": "/notes/1532837 "}, {"RefNumber": "1553601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Adjustments for the input help", "RefUrl": "/notes/1553601 "}, {"RefNumber": "1554973", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSEC: 'No authorisation' double occurence of external char.", "RefUrl": "/notes/1554973 "}, {"RefNumber": "1567394", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Consulting note for sort sequence", "RefUrl": "/notes/1567394 "}, {"RefNumber": "1549127", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Formulas of function module calls", "RefUrl": "/notes/1549127 "}, {"RefNumber": "1555413", "RefComponent": "BW-WHM-DST", "RefTitle": "Preparations for ODP access", "RefUrl": "/notes/1555413 "}, {"RefNumber": "1567660", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P27:PC: No mess. in PC log if error during req. generation", "RefUrl": "/notes/1567660 "}, {"RefNumber": "1532278", "RefComponent": "BW-BEX", "RefTitle": "Read authorization check for virtual providers", "RefUrl": "/notes/1532278 "}, {"RefNumber": "1540840", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "730:DTP:Performance:<PERSON><PERSON><PERSON>. Achtung:Hinweis NIE freigeben", "RefUrl": "/notes/1540840 "}, {"RefNumber": "1551528", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Issues with values when Selection option variable is used.", "RefUrl": "/notes/1551528 "}, {"RefNumber": "1567679", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI jump shows variable screen although not expected", "RefUrl": "/notes/1567679 "}, {"RefNumber": "1548619", "RefComponent": "BW-BEX", "RefTitle": "BW-IP: internal", "RefUrl": "/notes/1548619 "}, {"RefNumber": "1536468", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS job cancels during status confirmation of a process.", "RefUrl": "/notes/1536468 "}, {"RefNumber": "1531427", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Extended where-used list for query elements", "RefUrl": "/notes/1531427 "}, {"RefNumber": "1554272", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Long runtime if crossjoin is used", "RefUrl": "/notes/1554272 "}, {"RefNumber": "1553927", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: High memory consumption; crossjoin, intersect, generate", "RefUrl": "/notes/1553927 "}, {"RefNumber": "1529591", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Tracing für BI Content Extraction", "RefUrl": "/notes/1529591 "}, {"RefNumber": "1528191", "RefComponent": "BW-BEX-OT", "RefTitle": "Incorrect payment in case of MultiProvider with delta pair", "RefUrl": "/notes/1528191 "}, {"RefNumber": "1526642", "RefComponent": "BW-PLA-IP", "RefTitle": "Adding a free characteristic unnecessary (BRAIN 224)", "RefUrl": "/notes/1526642 "}, {"RefNumber": "1534889", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Short dump during master data update (second attempt)", "RefUrl": "/notes/1534889 "}, {"RefNumber": "1533143", "RefComponent": "BW-SYS", "RefTitle": "SP28:RS_BW_POST_MIGRATION doesnt convert PSAs of new DS", "RefUrl": "/notes/1533143 "}, {"RefNumber": "1527970", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Log overflow during query processing", "RefUrl": "/notes/1527970 "}, {"RefNumber": "1563000", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Arithmetic overflow in form get_mdc_cells", "RefUrl": "/notes/1563000 "}, {"RefNumber": "1563856", "RefComponent": "BW-BEX-ET", "RefTitle": "Check of the variable table RSZGLOBV", "RefUrl": "/notes/1563856 "}, {"RefNumber": "1528906", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS dispatcher does not remove nodes for deleted PC Log IDs", "RefUrl": "/notes/1528906 "}, {"RefNumber": "1568815", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Subsequent correction to Note 1563856", "RefUrl": "/notes/1568815 "}, {"RefNumber": "1529997", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "deficient OLAP cache mode 6 'Shared Objects'", "RefUrl": "/notes/1529997 "}, {"RefNumber": "1554108", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in New MD deletion where used checks", "RefUrl": "/notes/1554108 "}, {"RefNumber": "1549724", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Incorrect data for filter on Thj leaf", "RefUrl": "/notes/1549724 "}, {"RefNumber": "1568913", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Duplicates of query components created using Query Designer", "RefUrl": "/notes/1568913 "}, {"RefNumber": "1566451", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP: Incorrect requests: Starting same DTPs in parallel", "RefUrl": "/notes/1566451 "}, {"RefNumber": "1528921", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Queries regenerated each time during execution", "RefUrl": "/notes/1528921 "}, {"RefNumber": "1535124", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Endless loop for complete deletion of DTA contents", "RefUrl": "/notes/1535124 "}, {"RefNumber": "1528052", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java View cannot be loaded in ABAP runtime", "RefUrl": "/notes/1528052 "}, {"RefNumber": "1370227", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT : Hierarchy activation dump", "RefUrl": "/notes/1370227 "}, {"RefNumber": "1530110", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "F4 help for Attribute change run in PCs works incorrect", "RefUrl": "/notes/1530110 "}, {"RefNumber": "1538402", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query terminates during generation", "RefUrl": "/notes/1538402 "}, {"RefNumber": "1539226", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP27:Deletion of the Infobject terminates with error R7 157", "RefUrl": "/notes/1539226 "}, {"RefNumber": "1563268", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP27:Deletion of PSA doesnot delete Maintenance structures", "RefUrl": "/notes/1563268 "}, {"RefNumber": "1565382", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP27:RSAU 509 Error occurs in TimeSplit in Transformation", "RefUrl": "/notes/1565382 "}, {"RefNumber": "1541274", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP26:Impact on transfer structure - Infoobject Activation", "RefUrl": "/notes/1541274 "}, {"RefNumber": "1563841", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Incorrect RSRT display for plan buffer query", "RefUrl": "/notes/1563841 "}, {"RefNumber": "1553204", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BWA: Delta property lost when index is recreated", "RefUrl": "/notes/1553204 "}, {"RefNumber": "1554929", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Dummy", "RefUrl": "/notes/1554929 "}, {"RefNumber": "1519889", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP processing: \"Database selection was interrupted\"", "RefUrl": "/notes/1519889 "}, {"RefNumber": "1551985", "RefComponent": "BW-PLA-BPS", "RefTitle": "Program termination occurs when maintaining documents", "RefUrl": "/notes/1551985 "}, {"RefNumber": "1561617", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Long runtime in FORM SELECT_1", "RefUrl": "/notes/1561617 "}, {"RefNumber": "1566522", "RefComponent": "BW-WHM", "RefTitle": "SP27: RSRV tests for master data switched off for THJ Iobjs.", "RefUrl": "/notes/1566522 "}, {"RefNumber": "1561539", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error ASSIGN_TYPE_CONFLICT for zero suppression", "RefUrl": "/notes/1561539 "}, {"RefNumber": "1533381", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:Too many messages in DTP monitor during MD update", "RefUrl": "/notes/1533381 "}, {"RefNumber": "1569601", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO Explorer: Sorting according to 0FISCPER is unexpected", "RefUrl": "/notes/1569601 "}, {"RefNumber": "1565372", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Normalizing to overall amt for universal display hierarchy", "RefUrl": "/notes/1565372 "}, {"RefNumber": "1568704", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Master data access type for result values does not work", "RefUrl": "/notes/1568704 "}, {"RefNumber": "1569174", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P27:Mon:DM init:Source request list contains E-tab request", "RefUrl": "/notes/1569174 "}, {"RefNumber": "1562182", "RefComponent": "BW-EI-APD", "RefTitle": "APD: The object name is not allowed to be empty", "RefUrl": "/notes/1562182 "}, {"RefNumber": "1557457", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Syntax error in type pool RSDU in the lean stack", "RefUrl": "/notes/1557457 "}, {"RefNumber": "1564900", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The virtual characteristic 0Infoprov is not available", "RefUrl": "/notes/1564900 "}, {"RefNumber": "1563593", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "CUBE_SAMPLE_CREATE_NEW", "RefUrl": "/notes/1563593 "}, {"RefNumber": "1553159", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems with initial value '#'", "RefUrl": "/notes/1553159 "}, {"RefNumber": "1559716", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: ABAP runtime error ITAB_ILLEGAL_SORT_ORDER", "RefUrl": "/notes/1559716 "}, {"RefNumber": "1570559", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "cache mode 5: statistics fault with ITAB_DUPLICATE_KEY", "RefUrl": "/notes/1570559 "}, {"RefNumber": "1550919", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Deadlock/lock wait for complete deletion of DTA content", "RefUrl": "/notes/1550919 "}, {"RefNumber": "1557928", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache: swapped data does not become deleted", "RefUrl": "/notes/1557928 "}, {"RefNumber": "1570810", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW 7.30 time check during data loading to non-cum. InfoCubes", "RefUrl": "/notes/1570810 "}, {"RefNumber": "1568379", "RefComponent": "BW-WHM-DST", "RefTitle": "P27; STATMAN: Deleting old entries from RSSTATMANREQMDEL", "RefUrl": "/notes/1568379 "}, {"RefNumber": "1534231", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the case of constant selection and compounding", "RefUrl": "/notes/1534231 "}, {"RefNumber": "1526045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Non-unique hierarchies, link nodes, and structures", "RefUrl": "/notes/1526045 "}, {"RefNumber": "1567184", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Access to adapter without storage not from DataSource", "RefUrl": "/notes/1567184 "}, {"RefNumber": "1540010", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Subsequent corrections to Note 1531144", "RefUrl": "/notes/1540010 "}, {"RefNumber": "1568318", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Text variable is not replaced", "RefUrl": "/notes/1568318 "}, {"RefNumber": "1566941", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in new MD deletion during initial check", "RefUrl": "/notes/1566941 "}, {"RefNumber": "1565036", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Manually maintained validities are ignored", "RefUrl": "/notes/1565036 "}, {"RefNumber": "1560958", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Improved error handling for the CompositeProvider", "RefUrl": "/notes/1560958 "}, {"RefNumber": "1563163", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Dump UNCAUGHT_EXCEPTION - CL_RSBK_DTP_N raises CX_RS_PROGRAM", "RefUrl": "/notes/1563163 "}, {"RefNumber": "1563631", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Data even though SUBSELECT does not find any data", "RefUrl": "/notes/1563631 "}, {"RefNumber": "1551587", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: SUBSELECT and external hierarchies", "RefUrl": "/notes/1551587 "}, {"RefNumber": "1567541", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P27:DSO: Poor performance when you start request activation", "RefUrl": "/notes/1567541 "}, {"RefNumber": "1568625", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP: Repair report for corrupt RSBMNODES table", "RefUrl": "/notes/1568625 "}, {"RefNumber": "1568515", "RefComponent": "BW-WHM-AWB", "RefTitle": "P27:DWWB:Auth.: Remodeling ends AWB with termination message", "RefUrl": "/notes/1568515 "}, {"RefNumber": "1568381", "RefComponent": "BW-EI-APD", "RefTitle": "Filter transformation - Variable <> does not exist actively", "RefUrl": "/notes/1568381 "}, {"RefNumber": "1535483", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables exit i_step = 3 is not executed", "RefUrl": "/notes/1535483 "}, {"RefNumber": "1568869", "RefComponent": "BW-BEX-ET", "RefTitle": "RRI jump to remote system: performance \"RFC_FUNCTION_SEARCH\"", "RefUrl": "/notes/1568869 "}, {"RefNumber": "1559843", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in one of the processes of new MD deletion", "RefUrl": "/notes/1559843 "}, {"RefNumber": "1567657", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P27:SDL:DB connect:DEC input fields with more than 9 chars.", "RefUrl": "/notes/1567657 "}, {"RefNumber": "1567435", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P27:MON: \"Refresh All\" does not read current requests", "RefUrl": "/notes/1567435 "}, {"RefNumber": "1568383", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "7.30(SP3)Incorrect warning message during Aggregation check", "RefUrl": "/notes/1568383 "}, {"RefNumber": "1567113", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "The CompositeProvider activation ends in an endless loop", "RefUrl": "/notes/1567113 "}, {"RefNumber": "1566628", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730: mistaken default persistence mode for OLAP cache", "RefUrl": "/notes/1566628 "}, {"RefNumber": "1567608", "RefComponent": "BW-WHM-MTD", "RefTitle": "TLOGO type CompositeProviders not visible in BW Metadata Rep", "RefUrl": "/notes/1567608 "}, {"RefNumber": "1566912", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP Load Monitor dumps when display Master Data request", "RefUrl": "/notes/1566912 "}, {"RefNumber": "1564941", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Full deletion causes dump in RSSM_UPDATE_RSBKREQUEST", "RefUrl": "/notes/1564941 "}, {"RefNumber": "1567445", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Property 'Calculate Results As' is not inherited", "RefUrl": "/notes/1567445 "}, {"RefNumber": "1566879", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P27:PC:All messages issued when InfoPackage starts", "RefUrl": "/notes/1566879 "}, {"RefNumber": "1566356", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPSRKK0 and form FILL_SP1_02", "RefUrl": "/notes/1566356 "}, {"RefNumber": "1565544", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "SQL error in the context of the master data provider", "RefUrl": "/notes/1565544 "}, {"RefNumber": "1566828", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query generation takes a long time", "RefUrl": "/notes/1566828 "}, {"RefNumber": "1566080", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Lowercase letters not possible as data separators or escape", "RefUrl": "/notes/1566080 "}, {"RefNumber": "1565018", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in CL_RSD_DC_SUPPORT_INFOCUBE and form GET_REFRESH", "RefUrl": "/notes/1565018 "}, {"RefNumber": "1552762", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual characteristic 0INFOPROV is ignored", "RefUrl": "/notes/1552762 "}, {"RefNumber": "1551586", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Duplicated technical names of query components", "RefUrl": "/notes/1551586 "}, {"RefNumber": "1563385", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSEC: Return parameter of BAdI for the log", "RefUrl": "/notes/1563385 "}, {"RefNumber": "1563496", "RefComponent": "BW-WHM-DST", "RefTitle": "P27:RSISIPTVERS:RS_ISIP_AFTER_IMPORT: Duplicate InfoPackages", "RefUrl": "/notes/1563496 "}, {"RefNumber": "1564839", "RefComponent": "BW-WHM-DST", "RefTitle": "P27:IOBJ: Delete master data request: Lock on RSICCONT", "RefUrl": "/notes/1564839 "}, {"RefNumber": "1564947", "RefComponent": "BW-WHM-DST", "RefTitle": "P27: Dump in RSSM_MON_START_2ND_PROCESS: Enqueue RSREQDONE", "RefUrl": "/notes/1564947 "}, {"RefNumber": "1565460", "RefComponent": "BW-WHM-DST", "RefTitle": "P27: Additional logs when the QS init selection is deleted", "RefUrl": "/notes/1565460 "}, {"RefNumber": "1566548", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP:PC:Transport:Spread of DTPs in PC:2 source NWs", "RefUrl": "/notes/1566548 "}, {"RefNumber": "1560285", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P27:DTP:Termination for green U state sets a request", "RefUrl": "/notes/1560285 "}, {"RefNumber": "1566574", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Compression of BW InfoCube does not remove last partition", "RefUrl": "/notes/1566574 "}, {"RefNumber": "1559590", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "CompositeProvider adjustment", "RefUrl": "/notes/1559590 "}, {"RefNumber": "1566101", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Exception Handling - MD Extraction", "RefUrl": "/notes/1566101 "}, {"RefNumber": "1565418", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections for transaction RSLIMO", "RefUrl": "/notes/1565418 "}, {"RefNumber": "1565140", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Problem when you delete in BW InfoCubes with own name space", "RefUrl": "/notes/1565140 "}, {"RefNumber": "1563893", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Migration error DTP in content system", "RefUrl": "/notes/1563893 "}, {"RefNumber": "1564719", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions dump: OBJECTS_WA_NOT_COMPATIBLE", "RefUrl": "/notes/1564719 "}, {"RefNumber": "1564866", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Variables and displayed hierarchy levels", "RefUrl": "/notes/1564866 "}, {"RefNumber": "1555876", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BCD_OVERFLOW error in OLAP cache mode 5 statistics", "RefUrl": "/notes/1555876 "}, {"RefNumber": "1515371", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Currency translation does not work", "RefUrl": "/notes/1515371 "}, {"RefNumber": "1564498", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Export to Excel: ABAP Run time", "RefUrl": "/notes/1564498 "}, {"RefNumber": "1554647", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:ORA: Performance: Incorrect parallel degree setting", "RefUrl": "/notes/1554647 "}, {"RefNumber": "1561672", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Global filter and SUBSELECT", "RefUrl": "/notes/1561672 "}, {"RefNumber": "1563211", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: RS_BW_POST_MIGRATION issues", "RefUrl": "/notes/1563211 "}, {"RefNumber": "1563453", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "OR filters are not evaluated correctly", "RefUrl": "/notes/1563453 "}, {"RefNumber": "1564060", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "RSRCACHE: Admin. Parameters cannot be set properly", "RefUrl": "/notes/1564060 "}, {"RefNumber": "1563475", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "SP26:RSAR 682 Syntax error in template RSTMPL9C", "RefUrl": "/notes/1563475 "}, {"RefNumber": "1561338", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01SP9:F4 in changelog deletion variant doesnt show all DSO", "RefUrl": "/notes/1561338 "}, {"RefNumber": "1547391", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data when node is expanded", "RefUrl": "/notes/1547391 "}, {"RefNumber": "1562895", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P26:DSO: Activation starts even though not all DPs are green", "RefUrl": "/notes/1562895 "}, {"RefNumber": "1562952", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Complete deletion:All requests set to \"Deleted\"", "RefUrl": "/notes/1562952 "}, {"RefNumber": "1560075", "RefComponent": "BW-WHM-DST", "RefTitle": "730: Enhancements for flat InfoCube", "RefUrl": "/notes/1560075 "}, {"RefNumber": "1560900", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning sequence when saving", "RefUrl": "/notes/1560900 "}, {"RefNumber": "1560036", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Missing data in a very specific situation", "RefUrl": "/notes/1560036 "}, {"RefNumber": "1562173", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: bXML flattening unbalanced hierarchies", "RefUrl": "/notes/1562173 "}, {"RefNumber": "1556122", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Performance optimization during master data update", "RefUrl": "/notes/1556122 "}, {"RefNumber": "1561966", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "7.30SP3:TRF having DSO and MD lookups cannot be activated", "RefUrl": "/notes/1561966 "}, {"RefNumber": "1535548", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "WJR:THJ - Subsequent correction to note 1464244", "RefUrl": "/notes/1535548 "}, {"RefNumber": "1551352", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Various minor issues in the Planning Modeler", "RefUrl": "/notes/1551352 "}, {"RefNumber": "1559158", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Characteristic relationships: Deleting a step", "RefUrl": "/notes/1559158 "}, {"RefNumber": "1561337", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "711SP07:Referential Integrity settings not transported", "RefUrl": "/notes/1561337 "}, {"RefNumber": "1560172", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Mails incorrectly displayed in RSPC_METADATA_CLEANUP", "RefUrl": "/notes/1560172 "}, {"RefNumber": "1538109", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error when PARTITIONMODE 2 or 3", "RefUrl": "/notes/1538109 "}, {"RefNumber": "1561236", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index without key figures", "RefUrl": "/notes/1561236 "}, {"RefNumber": "1555531", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "REOPEN: Always probs generating after SP stack (499933/2010)", "RefUrl": "/notes/1555531 "}, {"RefNumber": "1559557", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Subtype is missing when the transformation is migrated", "RefUrl": "/notes/1559557 "}, {"RefNumber": "1556443", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DTA:DSO:WO-DSO:PSA:PC:Dump in PSA process for WO DSO", "RefUrl": "/notes/1556443 "}, {"RefNumber": "1556030", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Performance: PSA status management is slow", "RefUrl": "/notes/1556030 "}, {"RefNumber": "1556026", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:WO-DSO:RSREQICODS: Missing activation request entries", "RefUrl": "/notes/1556026 "}, {"RefNumber": "1532061", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Dump in process chains for sequence during saving", "RefUrl": "/notes/1532061 "}, {"RefNumber": "1558387", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BExAnalyzer: Planning Values are not transferred correctly", "RefUrl": "/notes/1558387 "}, {"RefNumber": "1556869", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Overflow during conversion", "RefUrl": "/notes/1556869 "}, {"RefNumber": "1557174", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Enhancement for program CL_RSR_MDX_RSTT_TRACE_EXTRACT", "RefUrl": "/notes/1557174 "}, {"RefNumber": "1556796", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Compressing BW non-cumulative cubes slow for marker", "RefUrl": "/notes/1556796 "}, {"RefNumber": "1547290", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "DTP is not generated -> two source systems/IOBJ target", "RefUrl": "/notes/1547290 "}, {"RefNumber": "1556730", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:AWB:DWWB:Dump w/jump DTP -> transport connection", "RefUrl": "/notes/1556730 "}, {"RefNumber": "1558066", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "WAD Metadata corrupt due to invalid entries in RSZWMDITEM", "RefUrl": "/notes/1558066 "}, {"RefNumber": "1558093", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSEC: New import paramater for virtual authorizations", "RefUrl": "/notes/1558093 "}, {"RefNumber": "1558230", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (SP26) InfoSource returns incorrect object status", "RefUrl": "/notes/1558230 "}, {"RefNumber": "1553368", "RefComponent": "BW-WHM", "RefTitle": "Lockwait situation for table NRIV", "RefUrl": "/notes/1553368 "}, {"RefNumber": "1546399", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error in program CL_RSR and form GET_CHANM-02", "RefUrl": "/notes/1546399 "}, {"RefNumber": "1543704", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "CompositeProvider corrections", "RefUrl": "/notes/1543704 "}, {"RefNumber": "1548170", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "RRMX launches Excel, but the connection is not established", "RefUrl": "/notes/1548170 "}, {"RefNumber": "1556689", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "DTP monitor: InfoCube loading errors are not displayed", "RefUrl": "/notes/1556689 "}, {"RefNumber": "1555941", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "70SP26:After import method fails during BI_CONT upgrade", "RefUrl": "/notes/1555941 "}, {"RefNumber": "1555430", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Optimization of virtual providers in the data manager", "RefUrl": "/notes/1555430 "}, {"RefNumber": "1555524", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Conversion error: non-numeric values in formula variables", "RefUrl": "/notes/1555524 "}, {"RefNumber": "1552573", "RefComponent": "BW-BCT-FI-GL", "RefTitle": "Query with virtual provider shows incorrect data", "RefUrl": "/notes/1552573 "}, {"RefNumber": "1537931", "RefComponent": "BW-BEX", "RefTitle": "MP + non-cumulative and multiple assgmt of characteristics", "RefUrl": "/notes/1537931 "}, {"RefNumber": "1555152", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Compressing is slow during deletion for F table", "RefUrl": "/notes/1555152 "}, {"RefNumber": "1554603", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Translated text are lost after activation of content query", "RefUrl": "/notes/1554603 "}, {"RefNumber": "1555872", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Message type X dump in FM RSSTATMAN_GET_PARTTAB_PSA", "RefUrl": "/notes/1555872 "}, {"RefNumber": "1554780", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query object IP/!IP could not be collected...", "RefUrl": "/notes/1554780 "}, {"RefNumber": "1554561", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache mode 5 incomplete statistics", "RefUrl": "/notes/1554561 "}, {"RefNumber": "1555706", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA: <PERSON> Already Executed in Background Job", "RefUrl": "/notes/1555706 "}, {"RefNumber": "1552741", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Performance Optimization :SID Flag Updates - Follow-up Note", "RefUrl": "/notes/1552741 "}, {"RefNumber": "1550270", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "DTP-LOG; no LOG for authorization problems", "RefUrl": "/notes/1550270 "}, {"RefNumber": "1553334", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Msg. R3 416 instead of a relevant msg. during direct access", "RefUrl": "/notes/1553334 "}, {"RefNumber": "1554841", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO Explorer: '*/*' is an invalid value", "RefUrl": "/notes/1554841 "}, {"RefNumber": "1554217", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Save of filter is not cancelled in case of CTS error", "RefUrl": "/notes/1554217 "}, {"RefNumber": "1551565", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P26: PC: PSA: Length of delay times reading PSA admin data", "RefUrl": "/notes/1551565 "}, {"RefNumber": "1554385", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DWWB:Assigning InfoObject to an area: No required entry", "RefUrl": "/notes/1554385 "}, {"RefNumber": "1551792", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Data mart: Deadlock on table RSBKREQUEST", "RefUrl": "/notes/1551792 "}, {"RefNumber": "1552482", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Activation for delta DTP fails: Invalid parameter", "RefUrl": "/notes/1552482 "}, {"RefNumber": "1554508", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:SY-UCOMM is not cleared after execution", "RefUrl": "/notes/1554508 "}, {"RefNumber": "1554574", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Filter in Content version is deactivated", "RefUrl": "/notes/1554574 "}, {"RefNumber": "1554702", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:After import:T version overlaps; no message", "RefUrl": "/notes/1554702 "}, {"RefNumber": "1527808", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BexAnalyzer:Jump to an Transaction freezes Sap GUI window", "RefUrl": "/notes/1527808 "}, {"RefNumber": "1554274", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Clean up class CL_RSBM_LOG_DATAPACKAGE", "RefUrl": "/notes/1554274 "}, {"RefNumber": "1554150", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (Support Package 26): Dump during migration", "RefUrl": "/notes/1554150 "}, {"RefNumber": "1552825", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:DTP:Filter in Content Version of DTP is deleted", "RefUrl": "/notes/1552825 "}, {"RefNumber": "1554098", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "GETWA_NOT_ASSIGNED when you execute RSPC_METADATA_CLEANUP", "RefUrl": "/notes/1554098 "}, {"RefNumber": "1553201", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.11SP07:RSAR_PSA_NEWDS_MAPPING_CHECK considers inactive PSA", "RefUrl": "/notes/1553201 "}, {"RefNumber": "1481835", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Performance of INTERSECT() function or Incorrect data", "RefUrl": "/notes/1481835 "}, {"RefNumber": "1552001", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "A query on a MultiProvider terminates", "RefUrl": "/notes/1552001 "}, {"RefNumber": "1550723", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "System error in program CL_RSR and form GET_COB_PRO-01-", "RefUrl": "/notes/1550723 "}, {"RefNumber": "1534680", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Hierarchy date variable is not filled when you navigate", "RefUrl": "/notes/1534680 "}, {"RefNumber": "1549224", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Request generation also reads deleted requests", "RefUrl": "/notes/1549224 "}, {"RefNumber": "1549040", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P26:DSO:Deleting activation queue when AQ empty", "RefUrl": "/notes/1549040 "}, {"RefNumber": "1550918", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P26:BAPI_IPAK_CHANGE undoes field for warning handling", "RefUrl": "/notes/1550918 "}, {"RefNumber": "1546975", "RefComponent": "BW-BEX-OT", "RefTitle": "Internal <-> external conversion to DECFLOAT34 key figures", "RefUrl": "/notes/1546975 "}, {"RefNumber": "1537570", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1537570 "}, {"RefNumber": "1533115", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message: \"TREX call with check_n and FEMS_N\"", "RefUrl": "/notes/1533115 "}, {"RefNumber": "1549726", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2 and form FAC_VARIABLES-03-", "RefUrl": "/notes/1549726 "}, {"RefNumber": "1551463", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Optimization in the data manager", "RefUrl": "/notes/1551463 "}, {"RefNumber": "1547453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Explain: X message for explain for formula with variables", "RefUrl": "/notes/1547453 "}, {"RefNumber": "1551354", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26:ASSERTION_FAILED in single rule test(Infoset as Source)", "RefUrl": "/notes/1551354 "}, {"RefNumber": "1551289", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:TRF:After import: Error message unclear, TRF missing", "RefUrl": "/notes/1551289 "}, {"RefNumber": "1551005", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:After-import:T versions are no longer deleted", "RefUrl": "/notes/1551005 "}, {"RefNumber": "1526835", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:REQARCH: Corr. in report RSREQARCH_REMO_INCONSISTENCIES", "RefUrl": "/notes/1526835 "}, {"RefNumber": "1527025", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Manage:Complete data target deletion and request list", "RefUrl": "/notes/1527025 "}, {"RefNumber": "1529330", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Deadlock when DSO contents are deleted in parallel", "RefUrl": "/notes/1529330 "}, {"RefNumber": "1533324", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "No 'Standard Text' for hierarchy node is displayed", "RefUrl": "/notes/1533324 "}, {"RefNumber": "1539990", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate w/time hierarchy is compressed (2)", "RefUrl": "/notes/1539990 "}, {"RefNumber": "1548242", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW cube indexes incorrect if no time char. in aggregate", "RefUrl": "/notes/1548242 "}, {"RefNumber": "1544434", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Properties of query elements are not evaluated correctly", "RefUrl": "/notes/1544434 "}, {"RefNumber": "1550187", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Termination in CL_RSDDB_INDEX_M and BUILD_TH_JOIN", "RefUrl": "/notes/1550187 "}, {"RefNumber": "1534748", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Quantity conversion does not take place", "RefUrl": "/notes/1534748 "}, {"RefNumber": "1548259", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORACLE: Compression of UNIQUE INDEX of PSA tables", "RefUrl": "/notes/1548259 "}, {"RefNumber": "1548635", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Corrections to the time service routines in BW", "RefUrl": "/notes/1548635 "}, {"RefNumber": "1539903", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Optimization of master data access to Teradata", "RefUrl": "/notes/1539903 "}, {"RefNumber": "1547708", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: some Key Figures are not visible", "RefUrl": "/notes/1547708 "}, {"RefNumber": "1547667", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Error DTP:No LOGSYS with p_r_request->get_logsys", "RefUrl": "/notes/1547667 "}, {"RefNumber": "1547838", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:DWB: Search for DTPs in workbench is slow", "RefUrl": "/notes/1547838 "}, {"RefNumber": "1545971", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Incorrect value for property 'MEMBER_UNIQUE_NAME'", "RefUrl": "/notes/1545971 "}, {"RefNumber": "1541216", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1541216 "}, {"RefNumber": "1544249", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Dump DYNPRO_FIELD_CONVERSION in screen 104", "RefUrl": "/notes/1544249 "}, {"RefNumber": "1545663", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:WO-DSO:RSBX_BIW_GET_ODSDATA:Myself extraction: Selection", "RefUrl": "/notes/1545663 "}, {"RefNumber": "1545857", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:PC:Process chain step set t.red when request deleted", "RefUrl": "/notes/1545857 "}, {"RefNumber": "1546906", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Enqueue: Minor improvements for creation of request", "RefUrl": "/notes/1546906 "}, {"RefNumber": "1540666", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Screen 101 of program SAPLRSSM_PROCESS delivered again", "RefUrl": "/notes/1540666 "}, {"RefNumber": "1535008", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Delta DTP: Transport with BEx variables or routines", "RefUrl": "/notes/1535008 "}, {"RefNumber": "1535514", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:DSP:PC:Truncate:Locked act. queue end with act. red", "RefUrl": "/notes/1535514 "}, {"RefNumber": "1545818", "RefComponent": "BW-BEX", "RefTitle": "Orange", "RefUrl": "/notes/1545818 "}, {"RefNumber": "1545834", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Authorization check on S_RFC_ADM", "RefUrl": "/notes/1545834 "}, {"RefNumber": "1535607", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error in RSRV text on InfoCube with referencing key figure", "RefUrl": "/notes/1535607 "}, {"RefNumber": "1545450", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2, form LRECH_F_CHFP_01-01-", "RefUrl": "/notes/1545450 "}, {"RefNumber": "1544520", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No Authorization with hierarchy authorization + interval", "RefUrl": "/notes/1544520 "}, {"RefNumber": "1542810", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Consulting:MD Update when key fields are mapped", "RefUrl": "/notes/1542810 "}, {"RefNumber": "1530274", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjustments to the CompositeProvider", "RefUrl": "/notes/1530274 "}, {"RefNumber": "1530319", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01SP9:New PSA Del Var ignores PSA of Exp DS of T-DSO/W-DSO", "RefUrl": "/notes/1530319 "}, {"RefNumber": "1540008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems in the context of SAP Business ByDesign - Part 2", "RefUrl": "/notes/1540008 "}, {"RefNumber": "1537125", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "SP26:Transaction RSH1 doesnt work correctly in display mode", "RefUrl": "/notes/1537125 "}, {"RefNumber": "1539445", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DTP:DataSource extractor:PSA extractor:Join/cleanup", "RefUrl": "/notes/1539445 "}, {"RefNumber": "1540186", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: RSSM_EXPAND_REQUESTLIST: Expansion in wrong order", "RefUrl": "/notes/1540186 "}, {"RefNumber": "1540733", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Data flow migration: Name of InfoSource is lost", "RefUrl": "/notes/1540733 "}, {"RefNumber": "1540583", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Missing restrictions via the label", "RefUrl": "/notes/1540583 "}, {"RefNumber": "1534298", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1534298 "}, {"RefNumber": "1539178", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:RSBX_BIW_GET_ODSDATA:Memory overflow:For all entries", "RefUrl": "/notes/1539178 "}, {"RefNumber": "1536212", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "OLAP cache and currency translation", "RefUrl": "/notes/1536212 "}, {"RefNumber": "1539601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Exception \"NAME_ERROR\" in the class CL_RSMD_RS_TREX_QUERY", "RefUrl": "/notes/1539601 "}, {"RefNumber": "1538876", "RefComponent": "BW-BEX", "RefTitle": "BRAIN X299 in class CL_RSR_CHABIT; form SET_BIT1-02-", "RefUrl": "/notes/1538876 "}, {"RefNumber": "1531453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and constant selection", "RefUrl": "/notes/1531453 "}, {"RefNumber": "1528875", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Incorrect authorization check f. jump destination activation", "RefUrl": "/notes/1528875 "}, {"RefNumber": "1528864", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Query generation generates syntax error for non-cml. cubes", "RefUrl": "/notes/1528864 "}, {"RefNumber": "1535576", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26: NOP key figures in transformations", "RefUrl": "/notes/1535576 "}, {"RefNumber": "1536334", "RefComponent": "BW-BEX", "RefTitle": "DTP loading; archive areas are not checked correctly", "RefUrl": "/notes/1536334 "}, {"RefNumber": "1535006", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1535006 "}, {"RefNumber": "1527844", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "%_HINTS ORACLE not built correct in the code LRRSIF03", "RefUrl": "/notes/1527844 "}, {"RefNumber": "1530255", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW selective deletion w/ very large single record conditions", "RefUrl": "/notes/1530255 "}, {"RefNumber": "1529540", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "BW InfoObject with RDA, navigation attributes and aggregates", "RefUrl": "/notes/1529540 "}, {"RefNumber": "1534209", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Overflow dump in report SAP_INFOCUBE_DESIGNS (2)", "RefUrl": "/notes/1534209 "}, {"RefNumber": "1533694", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Red, updated DP cannot be repaired", "RefUrl": "/notes/1533694 "}, {"RefNumber": "1530165", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:SDL:PC:Enabling user entries for InfoPackages", "RefUrl": "/notes/1530165 "}, {"RefNumber": "1526103", "RefComponent": "BW-WHM-DBA-MPRO", "RefTitle": "MultiProvider activation abends with x message", "RefUrl": "/notes/1526103 "}, {"RefNumber": "1533378", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BExAnalyzer: Saving of Workbooks by Multiple Users", "RefUrl": "/notes/1533378 "}, {"RefNumber": "1532624", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: REQARCH: Endless loop in write report", "RefUrl": "/notes/1532624 "}, {"RefNumber": "1533243", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CL_RSMD_RS_TREX_QUERY->IF_RSMD_RS_BUILD_QUERY~READ_DATA", "RefUrl": "/notes/1533243 "}, {"RefNumber": "1531519", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Subsequent correction to Note 1385580 and Note 1431226", "RefUrl": "/notes/1531519 "}, {"RefNumber": "1529080", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01 SP9:PSA/CLG deletion variant save doesnt work correctly", "RefUrl": "/notes/1529080 "}, {"RefNumber": "1532596", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Explorer: 2920:multiprovider schema is not consistent;", "RefUrl": "/notes/1532596 "}, {"RefNumber": "1532223", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search based on calendar month/year does not work", "RefUrl": "/notes/1532223 "}, {"RefNumber": "1531920", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Error during InfoPackage import for non-existing target", "RefUrl": "/notes/1531920 "}, {"RefNumber": "1529488", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.30 (SP02) Falsche Meldung", "RefUrl": "/notes/1529488 "}, {"RefNumber": "1530803", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error CX_SY_DYN_TABLE_ILL_COMP_VAL; in  _SORT_X", "RefUrl": "/notes/1530803 "}, {"RefNumber": "1530546", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Filling of formula variable via report-report interface", "RefUrl": "/notes/1530546 "}, {"RefNumber": "1530271", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception condition \"INCONSISTENCY\" raised", "RefUrl": "/notes/1530271 "}, {"RefNumber": "1529720", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No data for calculated member and metadata reference", "RefUrl": "/notes/1529720 "}, {"RefNumber": "1529152", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Runtime error in MDXTEST with missing authorization", "RefUrl": "/notes/1529152 "}, {"RefNumber": "1529440", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: No text for new DataSources in request list", "RefUrl": "/notes/1529440 "}, {"RefNumber": "1527930", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination in BW aggregates with COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/1527930 "}, {"RefNumber": "1527868", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BW InfoCube with BIA displays compressed aggregates", "RefUrl": "/notes/1527868 "}, {"RefNumber": "1529455", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Stammdaten Optimierungen", "RefUrl": "/notes/1529455 "}, {"RefNumber": "1526833", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P26: RDA: No F1 help in real-time fields in the scheduler", "RefUrl": "/notes/1526833 "}, {"RefNumber": "1528374", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error GET_PRPTY_VALUE-02-", "RefUrl": "/notes/1528374 "}, {"RefNumber": "1528293", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Dump in report RSSM_SM50_ALT", "RefUrl": "/notes/1528293 "}, {"RefNumber": "1528332", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0(SP26) Fehlerhafte Anzeige InfoSource", "RefUrl": "/notes/1528332 "}, {"RefNumber": "1525546", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Destination transport RC = 8", "RefUrl": "/notes/1525546 "}, {"RefNumber": "1526867", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Query does not return navigation attribute in MultiProvider", "RefUrl": "/notes/1526867 "}, {"RefNumber": "1527011", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_STREAMINFO & fixed fiscal variant", "RefUrl": "/notes/1527011 "}, {"RefNumber": "1527728", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Error handler: Endless loop during deletion", "RefUrl": "/notes/1527728 "}, {"RefNumber": "1527726", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "730:New DS & hierarchy sel. w/o existing hierarchy headers", "RefUrl": "/notes/1527726 "}, {"RefNumber": "1527063", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data with constant selection and compounding", "RefUrl": "/notes/1527063 "}, {"RefNumber": "1526821", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Hierarchy without unassigned node has incorrect totals", "RefUrl": "/notes/1526821 "}, {"RefNumber": "1526618", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Fields in PSA have no text", "RefUrl": "/notes/1526618 "}, {"RefNumber": "1526857", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Incorrect display of destination", "RefUrl": "/notes/1526857 "}, {"RefNumber": "1526599", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Wrong values filtered with manual input on the Var. Screen", "RefUrl": "/notes/1526599 "}, {"RefNumber": "1525723", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Program termination in program RSPC_LOG_DELETE", "RefUrl": "/notes/1525723 "}, {"RefNumber": "1525635", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Additional index for RSZWVIEW table", "RefUrl": "/notes/1525635 "}, {"RefNumber": "1525419", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate with time hierarchy is compressed", "RefUrl": "/notes/1525419 "}, {"RefNumber": "1487382", "RefComponent": "BW-BEX-ET-ER", "RefTitle": "Duplicated serach result in Report Designer", "RefUrl": "/notes/1487382 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "701", "To": "701", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}