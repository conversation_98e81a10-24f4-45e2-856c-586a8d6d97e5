{"Request": {"Number": "31515", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 326, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014376702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000031515?language=E&token=4BDEAF088375C1D3774F93554ECF1F8D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000031515", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000031515/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "31515"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.08.2015"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-NET-HTL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Problems with remote access from SAP to Customer system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network connection", "value": "XX-SER-NET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Problems with remote access from SAP to Customer system", "value": "XX-SER-NET-HTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET-HTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "31515 - Service connections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Creating and opening service connections in the SAP Support Portal</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Remote services, Early Watch, remote consulting, Telnet connection</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />Service connections<br />-------------------</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th></th></tr>\r\n</tbody>\r\n</table></div>\r\n<p>Maintenance of service connection data is possible only in the SAP Support Portal (SAP Service Marketplace). You can find the application on SAP Service Marketplace using the URL <a target=\"_blank\" href=\"https://support.sap.com/remote-support/remote-connections.html\">https://support.sap.com/remote-support/remote-connections.html</a></p>\r\n<p><br />The complete user documentation is also available using the URL:<br /><strong>http://service.sap.com/~sapidb/011000358700003071932006</strong><br /><br /><br />A prerequisite for the service connections is that a physical connection to SAP is set up (using ISDN, VPN, SNC, and so on). For this, see SAP Note 35010, which contains all required information.<br />All service connections between your system and SAP are established using your SAProuter. For this, a connection must ALWAYS be established from the customer system (a physical connection cannot be established from SAP) in order to prevent unauthorized access to your system.<br />To ensure that SAP can access the customer systems, sapserv(x) must have access to the host on which the SAProuter software is running at the location of the customer.<br /><br /><br /><strong><strong>Service Connector</strong></strong><br /><br />To open the physical connection to SAP, you must also install the Service Connector. For information about this, see:<br /><a target=\"_blank\" href=\"https://support.sap.com/remote-support.html\">https://support.sap.com/remote-support.html</a><br /><strong>--&gt; Service connection</strong><br /><strong>--&gt; Documentation</strong><br /><strong>--&gt; SAProuter -&gt; SAP Service Connector - Installation instructions</strong><br /><br /><br /><strong>System data maintenance</strong><br /><br />The application to display and maintain system data is available at the following URL:<br /><a target=\"_blank\" href=\"https://support.sap.com/system-data\">https://support.sap.com/system-data</a></p>\r\n<p><br /><br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>If you experience any problems with service connections, create a customer incident (https://support.sap.com/kb-incidents.html) under the component XX-SER-NET-HTL.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-SER-NET (Network connection)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5014315)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (C5225676)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000031515/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031515/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "984434", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/984434"}, {"RefNumber": "895754", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/895754"}, {"RefNumber": "834172", "RefComponent": "IS-M-AMC", "RefTitle": "IS-M/AMC: Prerequisite for SAP Support remote access", "RefUrl": "/notes/834172"}, {"RefNumber": "816973", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS 7.00 - preclarification and basic problem analysis", "RefUrl": "/notes/816973"}, {"RefNumber": "813652", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/813652"}, {"RefNumber": "812732", "RefComponent": "XX-SER-NET", "RefTitle": "R/3 support service connection", "RefUrl": "/notes/812732"}, {"RefNumber": "808347", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/808347"}, {"RefNumber": "800267", "RefComponent": "BC-XI", "RefTitle": "\"Integration Repository/directory\" service connections", "RefUrl": "/notes/800267"}, {"RefNumber": "79411", "RefComponent": "XX-SER-NET", "RefTitle": "AS/400: 5250 connection for AS/400 customers", "RefUrl": "/notes/79411"}, {"RefNumber": "73649", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/73649"}, {"RefNumber": "583100", "RefComponent": "CRM-CIC-COM-ITP", "RefTitle": "Remote support & required information for Genesys component", "RefUrl": "/notes/583100"}, {"RefNumber": "558430", "RefComponent": "CRM-ISA", "RefTitle": "ISA/IPC support  delay in message processing", "RefUrl": "/notes/558430"}, {"RefNumber": "550571", "RefComponent": "CRM-ISA", "RefTitle": "Internet Sales Support", "RefUrl": "/notes/550571"}, {"RefNumber": "494980", "RefComponent": "EP-PIN", "RefTitle": "Remote Support for an Enterprise Portal", "RefUrl": "/notes/494980"}, {"RefNumber": "385102", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/385102"}, {"RefNumber": "383270", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/383270"}, {"RefNumber": "37946", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/37946"}, {"RefNumber": "36819", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/36819"}, {"RefNumber": "367665", "RefComponent": "BC-FES-ITS", "RefTitle": "Entering ITS server in OSS for PCANYWHERE access", "RefUrl": "/notes/367665"}, {"RefNumber": "35010", "RefComponent": "XX-SER-NET", "RefTitle": "Service connections: Composite note (overview))", "RefUrl": "/notes/35010"}, {"RefNumber": "33953", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network provider for remote connection in EMEA", "RefUrl": "/notes/33953"}, {"RefNumber": "32500", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/32500"}, {"RefNumber": "301933", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "Remote connection package (rcPack) - SAP France", "RefUrl": "/notes/301933"}, {"RefNumber": "202344", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Set up SAP DB connection", "RefUrl": "/notes/202344"}, {"RefNumber": "200330", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200330"}, {"RefNumber": "1718597", "RefComponent": "XX-SER-NET", "RefTitle": "Service connection \"SAP NI Connection\"", "RefUrl": "/notes/1718597"}, {"RefNumber": "170102", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Automatic opening of a service connection", "RefUrl": "/notes/170102"}, {"RefNumber": "1698817", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in China", "RefUrl": "/notes/1698817"}, {"RefNumber": "169296", "RefComponent": "XX-SER-SAPSMP-SYS", "RefTitle": "Integrate service connectns in maintain system data", "RefUrl": "/notes/169296"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "161100", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/161100"}, {"RefNumber": "159085", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/159085"}, {"RefNumber": "139459", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/139459"}, {"RefNumber": "138229", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/138229"}, {"RefNumber": "137342", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/137342"}, {"RefNumber": "1178684", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"SNC processing failed\"", "RefUrl": "/notes/1178684"}, {"RefNumber": "1178631", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: connection to partner broken", "RefUrl": "/notes/1178631"}, {"RefNumber": "1178628", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: internal error", "RefUrl": "/notes/1178628"}, {"RefNumber": "1178624", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"Partner not reached\" (sapserv#)", "RefUrl": "/notes/1178624"}, {"RefNumber": "1178546", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: route permission denied (...)", "RefUrl": "/notes/1178546"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2699939", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Emergency Suitcase", "RefUrl": "/notes/2699939 "}, {"RefNumber": "2534487", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service Connector missing librfc32.dll", "RefUrl": "/notes/2534487 "}, {"RefNumber": "1787981", "RefComponent": "XX-SER-NET", "RefTitle": "Service connection \"HTTP-based Tools\"", "RefUrl": "/notes/1787981 "}, {"RefNumber": "1757001", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Troubleshooting Checklist for Analysis Office", "RefUrl": "/notes/1757001 "}, {"RefNumber": "33953", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network provider for remote connection in EMEA", "RefUrl": "/notes/33953 "}, {"RefNumber": "800267", "RefComponent": "BC-XI", "RefTitle": "\"Integration Repository/directory\" service connections", "RefUrl": "/notes/800267 "}, {"RefNumber": "195715", "RefComponent": "XX-SER-NET", "RefTitle": "Service connection types \"BW RFC\" and \"BW GUI\"", "RefUrl": "/notes/195715 "}, {"RefNumber": "834172", "RefComponent": "IS-M-AMC", "RefTitle": "IS-M/AMC: Prerequisite for SAP Support remote access", "RefUrl": "/notes/834172 "}, {"RefNumber": "1698817", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in China", "RefUrl": "/notes/1698817 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "138229", "RefComponent": "BW-BEX", "RefTitle": "Remote connection with BW Business Explorer", "RefUrl": "/notes/138229 "}, {"RefNumber": "1178684", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"SNC processing failed\"", "RefUrl": "/notes/1178684 "}, {"RefNumber": "1178631", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: connection to partner broken", "RefUrl": "/notes/1178631 "}, {"RefNumber": "1178628", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: internal error", "RefUrl": "/notes/1178628 "}, {"RefNumber": "1178624", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"Partner not reached\" (sapserv#)", "RefUrl": "/notes/1178624 "}, {"RefNumber": "1178546", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: route permission denied (...)", "RefUrl": "/notes/1178546 "}, {"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434 "}, {"RefNumber": "812732", "RefComponent": "XX-SER-NET", "RefTitle": "R/3 support service connection", "RefUrl": "/notes/812732 "}, {"RefNumber": "202344", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Set up SAP DB connection", "RefUrl": "/notes/202344 "}, {"RefNumber": "1530309", "RefComponent": "BC-FES-ITS", "RefTitle": "SAP NI service connection for WebGUI support", "RefUrl": "/notes/1530309 "}, {"RefNumber": "35010", "RefComponent": "XX-SER-NET", "RefTitle": "Service connections: Composite note (overview))", "RefUrl": "/notes/35010 "}, {"RefNumber": "816973", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS 7.00 - preclarification and basic problem analysis", "RefUrl": "/notes/816973 "}, {"RefNumber": "550571", "RefComponent": "CRM-ISA", "RefTitle": "Internet Sales Support", "RefUrl": "/notes/550571 "}, {"RefNumber": "170102", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Automatic opening of a service connection", "RefUrl": "/notes/170102 "}, {"RefNumber": "494980", "RefComponent": "EP-PIN", "RefTitle": "Remote Support for an Enterprise Portal", "RefUrl": "/notes/494980 "}, {"RefNumber": "796308", "RefComponent": "PA-ER", "RefTitle": "Remote connections and how to debug in customer systems", "RefUrl": "/notes/796308 "}, {"RefNumber": "169296", "RefComponent": "XX-SER-SAPSMP-SYS", "RefTitle": "Integrate service connectns in maintain system data", "RefUrl": "/notes/169296 "}, {"RefNumber": "558430", "RefComponent": "CRM-ISA", "RefTitle": "ISA/IPC support  delay in message processing", "RefUrl": "/notes/558430 "}, {"RefNumber": "79411", "RefComponent": "XX-SER-NET", "RefTitle": "AS/400: 5250 connection for AS/400 customers", "RefUrl": "/notes/79411 "}, {"RefNumber": "583100", "RefComponent": "CRM-CIC-COM-ITP", "RefTitle": "Remote support & required information for Genesys component", "RefUrl": "/notes/583100 "}, {"RefNumber": "301933", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "Remote connection package (rcPack) - SAP France", "RefUrl": "/notes/301933 "}, {"RefNumber": "367665", "RefComponent": "BC-FES-ITS", "RefTitle": "Entering ITS server in OSS for PCANYWHERE access", "RefUrl": "/notes/367665 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}