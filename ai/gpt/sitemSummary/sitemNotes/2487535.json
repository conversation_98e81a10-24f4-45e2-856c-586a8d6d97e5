{"Request": {"Number": "2487535", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 396, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019004542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002487535?language=E&token=E2E8A8188E8A198CD8EA6CD204BD7170"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002487535", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002487535/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2487535"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03/09/2023"}, "SAPComponentKey": {"_label": "Component", "value": "BW-B4H-CNV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Conversion to SAP BW/4HANA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP BW/4HANA Starter Add-On", "value": "BW-B4H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Conversion to SAP BW/4HANA", "value": "BW-B4H-CNV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H-CNV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2487535 - BW4SL & BWbridgeSL - Personalization"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You use Personalization in SAP Business Explorer (BEx) and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ODSO, DODS, 0PERS_VAR, 0PERS_VR1, 0PERS_BOD, 0PERS_WTE, RS_PERS_ACTIVATE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Run program&#160;RS_B4HANA_RC to determine which objects are available in or can be converted to SAP BW/4HANA.</p>\r\n<p>See node: Automated Cleanup --&gt;&#160;Adjustments</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Target SAP BW/4HANA:</strong></span></p>\r\n<p style=\"padding-left: 30px;\">Personalization in SAP Business Explorer (BEx) is not available in SAP BW/4HANA. The corresponding classic DataStore Objects (0PERS*) is not available.</p>\r\n<p style=\"padding-left: 30px;\">When executing the switch from Compatibility Mode to B4H Mode, the system will save the personalization settings (for example user-specific variable values) to transparent tables:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"width: 976px; height: 101px;\">\r\n<tbody>\r\n<tr>\r\n<td>Description</td>\r\n<td>DataStore Object (classic) in SAP BW</td>\r\n<td>Database Table in SAP BW/4HANA</td>\r\n</tr>\r\n<tr>\r\n<td>Data for Variable Values</td>\r\n<td>0PERS_VAR/0PERS_VR1</td>\r\n<td>RSPERS_VAR</td>\r\n</tr>\r\n<tr>\r\n<td>Data for BEx Open Dialog</td>\r\n<td>0PERS_BOD</td>\r\n<td>RSPERS_BOD</td>\r\n</tr>\r\n<tr>\r\n<td>Data for Web Templates</td>\r\n<td>0PERS_WTE</td>\r\n<td>RSPERS_WTE</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">Related Information:</span></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://help.sap.com/viewer/73e6551e26244281884fd2fa36cdb678/7.5.7/en-US/0142c73c13b0ce5be10000000a114084.html\">Personalization in BEx</a></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>Target SAP Datasphere, SAP BW bridge:</strong></span></p>\r\n<p style=\"padding-left: 30px;\">SAP Datasphere, SAP BW bridge acts as a staging layer and does not support OLAP engine and functionality dependent on the OLAP engine. Therefore, Personalization in SAP Business Explorer (BEx) is not available in SAP Datasphere, SAP BW bridge.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "DS-BWB (BW Bridge)"}, {"Key": "Other Components", "Value": "BW-BEX-OT-OLAP-VAR (Using Variables)"}, {"Key": "Responsible                                                                                         ", "Value": "I822646"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028847)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002487535/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487535/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2487597", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Conversion of DataSource Field into InfoObject", "RefUrl": "/notes/2487597"}, {"RefNumber": "2451013", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL - DataStore Objects (classic)", "RefUrl": "/notes/2451013"}, {"RefNumber": "2421930", "RefComponent": "BW-B4H-CNV", "RefTitle": "Simplification List for SAP BW/4HANA", "RefUrl": "/notes/2421930"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2487597", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Conversion of DataSource Field into InfoObject", "RefUrl": "/notes/2487597 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}