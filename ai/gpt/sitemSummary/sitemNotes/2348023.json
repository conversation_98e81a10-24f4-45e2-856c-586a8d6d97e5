{"Request": {"Number": "2348023", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 460, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018374002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002348023?language=E&token=A47CEA7398F4D101893D2EC204DDFD0B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002348023", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002348023/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2348023"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "IS-ADEC-MPN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Manufacturer Part Number"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Ind.-Spec.Comp. Aerospace&Defense / Engineering&Construction", "value": "IS-ADEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-ADEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Manufacturer Part Number", "value": "IS-ADEC-MPN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-ADEC-MPN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2348023 - S4TWL - Manufacturer Part Number and its System Conversion to SAP S/4HANA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<p>This SAP Note contains&#160;business process-related information of system conversion&#160;from ECC-DIMP to SAP S/4HANA on MPN.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP S/4HANA System Conversion, MPN, Manufacturer Part Number, S4_TRANSFORMATION</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description</strong></p>\r\n<p>The functionality for the Manufacturer Part Number (MPN) is not available&#160;in SAP S/4HANA 1511.</p>\r\n<p>Refer to SAP Note 2270836 for more information.</p>\r\n<p>The industry solution for the manufacturer part number&#160;is changed technically in SAP S/4HANA 1610.</p>\r\n<p><strong>The newly redesigned solution for Manufacturer Part Number will be available from SAP S/4HANA 1610 onwards.</strong></p>\r\n<p>The below points specify details about the changes at the technical level as well as the changes with respect to configuration of the MPN in SAP S/4HANA 1610:</p>\r\n<ul>\r\n<li><strong>Special characters '*', '&amp;' and ',,' (double-comma) are not allowed in material number</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">In S/4HANA it is not allowed to use the following special characters&#160;('*',&#160;'&amp;',&#160;',,') in the material number. If you have in table MATERIALID in the field MATNR_EXT such characters, the material needs to be renamed.</span></p>\r\n<ul>\r\n<li><strong>Leading space character not allowed in material number</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">In S/4HANA it is not allowed to use leading space(s) in the material number. If you have in table MATERIALID in the field MATNR_EXT such entries, the material number conversion will alter the S/4 material number (MATNR_LONG) to have leading '_' characters. Communication in external interfaces in a DIMP LAMA compatibility mode is not affected, as the original MATNR_EXT content remains intact during the conversion.&#160;</span></p>\r\n<ul>\r\n<li><strong>How MPN will be stored and rendered</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In SAP S/4HANA 1610 release, MPN has been technically redesigned. MPN material number will be stored as key (MATNR) in MARA table. Hence conversion exit, which was used in earlier releases, is no longer relevant.</p>\r\n<p style=\"padding-left: 60px;\">MPN Material number is the concatenation of Manufacturing Part number and External Manufacturer separated by the delimiter specified in&#160;<em>Logistics - General --&gt;&#160;Interchangeability of Parts --&gt;&#160;Specify Settings for Inventory-Managed MPN</em>.</p>\r\n<p style=\"padding-left: 60px;\">e.g.: Manuafcturing Part number = 'MPN123' External Manufacturer = 'MANF1' Delimiter = ':', then the MPN Material number formed will be 'MPN123:MANF1'.</p>\r\n<p style=\"padding-left: 60px;\">If Manufacturer Part number is a numerical Material number and no manufacturer is specified, then the part number will be suffixed with the delimiter specified in the customizing settings (This behavior is enabled only if lexicographical flag is switched off).</p>\r\n<p style=\"padding-left: 60px;\">e.g.: Manufacturer Part number = 1234 and External Manufacturer = ' ', then MPN Material number formed will be '1234:'.</p>\r\n<p style=\"padding-left: 60px;\">The above behavior is relevant only if 'Concatenated Material No.' option is selected for the material type of the MPN Material.</p>\r\n<ul>\r\n<li><strong>&#160;Concatenation checkbox in material types</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">MPN functionality&#160;is activated when you select 'Activate Inventory-Managed MPN' in Customizing.&#160;You can reach the new settings using the path: <em>Logistics - General --&gt;&#160;Interchangeability of Parts --&gt;&#160;Specify Settings for Inventory-Managed MPN</em>.</p>\r\n<p style=\"padding-left: 60px;\">From the SAP S/4HANA&#160;1610 release onwards, Inventory Managed MPN&#160;will be&#160;created as the material number (MARA-MATNR) itself.</p>\r\n<p style=\"padding-left: 60px;\">The MPN attributes like MFRPN and EMNFR are maintained in the initial screen of the material master.</p>\r\n<p style=\"padding-left: 60px;\">These fields are editable only if&#160;the following settings are enabled in the 'Material Type' Customizing by following the path: <em>Logistics-General --&gt;Material Master --&gt; Basic Settings --&gt; Material Types --&gt; Define Attributes of Material Types</em></p>\r\n<p style=\"padding-left: 60px;\">'Concatenated Material No.' field&#160;should be&#160;enabled.</p>\r\n<p style=\"padding-left: 60px;\">For more information related to the settings, refer to the documentation&#160;of the field 'Concatenated Material No.'.</p>\r\n<ul>\r\n<li><strong>MPN numbers can no longer be changed once created in SAP S/4HANA</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In the ECC-DIMP systems, MPN number&#160;can be&#160;maintained as an additional value for the material in the 'Basic Data1' tab&#160;as well as in 'Purchasing' view.</p>\r\n<p style=\"padding-left: 60px;\">With the help of the conversion exit, MPN values were displayed as the material number.</p>\r\n<p style=\"padding-left: 60px;\">In SAP S/4HANA, this approach is not available due to its shortcomings like performance impact and maintenance effort.</p>\r\n<p style=\"padding-left: 60px;\">Also, with the extension of material field length&#160;to 40 characters, it will be possible to use the field for storing the MPN material number.</p>\r\n<p style=\"padding-left: 60px;\">MPN materials will be stored and displayed as the material number itself. Hence the name of the MPN material cannot be changed once created.</p>\r\n<p style=\"padding-left: 60px;\">This behavior is enabled only if 'Concatenated Material No.' indicator is enabled in the respective 'Material Type' Customizing.</p>\r\n<ul>\r\n<li><strong>MPN data held in MIGO transaction will not be converted in SAP S/4HANA</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">MIGO transaction enables saving of MPN data for a user using the option 'Hold' so that it could be reused later in the application.</p>\r\n<p style=\"padding-left: 60px;\">During migration to SAP S/4HANA,&#160;this MPN data will not be converted.</p>\r\n<p style=\"padding-left: 60px;\">Hence MPN data thus saved before migration cannot be used in SAP S/4HANA.</p>\r\n<p style=\"padding-left: 60px;\">Customer&#160;needs to maintain the entry again.</p>\r\n<ul>\r\n<li><strong>Non-MPN materials that contain&#160;MFRPN (Manufacturer Part Number)&#160;will have different behavior in SAP S/4HANA</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In the <strong>ECC-DIMP</strong> system, if the DIMP 'Conversion exit' is activated, then the external material number&#160;stored in the table MATERIALID field MATNR_EXT will be displayed instead of the material number. The external material number will be populated in the case of LAMA and MPN.</p>\r\n<p style=\"padding-left: 60px;\">This behavior is enabled irrespective of the material type.&#160;For material type HALB, if the MPN information is maintained in the 'Purchasing' view tab in the material master and 'Conversion exit' is activated from the Customizing, the MPN material will be displayed instead of the generated material number.</p>\r\n<p style=\"padding-left: 60px;\">In <strong>SAP S/4HANA</strong>, the following&#160;behavior is different.&#160;If the&#160;MPN is activated and concatenation checkbox is enabled in the respective material type Customizing, MPN information maintained in the material master will be displayed and stored as the material number itself. Otherwise, the MPN information will behave as a normal attribute&#160;of a material.</p>\r\n<ul>\r\n<li><strong>Individual attributes of the material MFRPN (Manufacturer Part Number) and EMNFR (External Manufacturer)&#160;should not be updated in SAP S/4HANA</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If the concatenation checkbox is enabled for a material type, then the individual attributes MFRPN and EMNFR should not be updated manually. This will&#160;lead to inconsistencies in the master data.</p>\r\n<p style=\"padding-left: 60px;\">In SAP S/4HANA, MPN material number is derived by concatenating &#8216;Manufacturing Part Number&#8217; and &#8216;External Manufacturer&#8217; in the material master. Hence it is restricted to change these MPN attributes once the material is created through the UI as well as SAP-delivered APIs.</p>\r\n<p style=\"padding-left: 60px;\">This restriction is applicable only if MPN is enabled with the concatenation checkbox in Customizing.</p>\r\n<p style=\"padding-left: 60px;\">Customer should ensure that the MPN material name should&#160;not&#160;get modified in SAP S/4HANA.</p>\r\n<ul>\r\n<li><strong>Following online transaction&#160;will not work after system conversion with MPN materials in SAP S/4HANA</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Transactions MM17 and MMCC are not available for MPN materials in SAP S/4HANA.</p>\r\n<ul>\r\n<li><strong>Use of new APIs for interacting with&#160;SAP S/4HANA system from non-SAP S/4HANA system during material creation</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">While transferring a material created in the non-SAP S/4HANA to SAP&#160;S/4HANA system using any API, the RFC-enabled function module MPN01_MATNR_DERIVE should be called by giving destination as SAP S/4HANA system. This function module will return the MPN material number confirming with the SAP&#160;S/4HANA system.</p>\r\n<p style=\"padding-left: 60px;\">For more information, refer to the documentation of the function module MPN01_MATNR_DERIVE.</p>\r\n<ul>\r\n<li><strong>Report variants will be ignored during migration to SAP S/4HANA</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Screen variants that&#160;are created in ECC-DIMP system will be ignored during migration to SAP S/4HANA.</p>\r\n<p style=\"padding-left: 60px;\">Customer&#160;needs to create&#160;the variants again in the SAP S/4HANA.</p>\r\n<ul>\r\n<li><strong>Parts Interchangeability in SAP S/4HANA</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The Parts interchangeability functionality earlier part of the ECC-DIMP is now available as&#160;part of SAP S/4HANA 1610. The functionality is decoupled from MPN (Manufacturer Part Number) and works without an MPN material.</p>\r\n<p style=\"padding-left: 60px;\">Additionally, the ECC-DIMP fields of vendor master are available as part of business partner fields&#160;in SAP&#160;S/4HANA 1610.</p>\r\n<p style=\"padding-left: 60px;\">The Purchasing view of business partner role \"FLVN01 Vendor \" is enhanced with the Interchangeability tab.</p>\r\n<ul>\r\n<li><strong>Usage of MATERIALID table</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">MATERIALID table should not be used any more in the coding. For more information refer to the section 'Reference to MATERIALID table' of note&#160;2228241.</p>\r\n<ul>\r\n<li><strong><strong>Web services</strong></strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Web services will change incompatibly when conversion exit is used and MPN material without manufacturer is sent. In S/4HANA if the MPN material is numerical and external manufacturer is not mentioned, then the delimiter is added as suffix to the MPN&#160;Material number. For eg: MPN Material number, if no external manufacturer is provided, is displayed as shown below:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 61px; width: 515px;\">\r\n<tbody>\r\n<tr>\r\n<td><strong>DIMP LAMA</strong></td>\r\n<td><strong>S/4HANA</strong></td>\r\n</tr>\r\n<tr>\r\n<td>000123</td>\r\n<td>000123:</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li><strong>BI Content</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Proposed solution for DIMP LAMA anyway relies on BI content modification by the customer (FAQ note 735529 as proposal, BW note 906913)</p>\r\n<p style=\"padding-left: 60px;\">For S/4HANA connection customer shall adapt this existing modification.&#160;Replicate MATERIALID again (we have enhanced this extractor) and add a new transfer rule where needed when he uses the &#8220;old&#8221; solution</p>\r\n<p style=\"padding-left: 60px;\">May get a visible difference between MATNR in S/4HANA and in BW for MPN w/o manufacturer</p>\r\n<ul>\r\n<li><strong>Deprecated objects</strong></li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Object Type</strong></td>\r\n<td><strong>Object Name</strong></td>\r\n<td><strong>Remarks</strong></td>\r\n</tr>\r\n<tr>\r\n<td>FUNC&#160;</td>\r\n<td>MPN01_CHECK_MPN_CONVERSION</td>\r\n<td>It is no longer relevant as there is no conversion done for the MPN Material. MPN Material is stored directly in MARA-MATNR.</td>\r\n</tr>\r\n<tr>\r\n<td>DTEL</td>\r\n<td>MPN_OUTLEN</td>\r\n<td>Use the data element 'OUTPUTLEN' instead</td>\r\n</tr>\r\n<tr>\r\n<td>MSAG&#160;</td>\r\n<td>AD_MPN_MESS</td>\r\n<td>The messages contained in the message class is not relevant. Should you need it, please copy the message to a new message class.</td>\r\n</tr>\r\n<tr>\r\n<td>PROG&#160;</td>\r\n<td>FMPN30LCCD</td>\r\n<td>Not relevant. Remove the usage of this include from your code</td>\r\n</tr>\r\n<tr>\r\n<td>PROG&#160;</td>\r\n<td>\r\n<p>MPNUPGRADE01</p>\r\n<p>MPNUPGRADE02</p>\r\n<p>MPNUPGRADETOP</p>\r\n<p>RMPNAFTERUPGRADE</p>\r\n</td>\r\n<td>This is a reconciliation report after upgrade in EHP releases. Not required. There are separate conversion programs available for conversion to S/4HANA1610 release.</td>\r\n</tr>\r\n<tr>\r\n<td>PROG</td>\r\n<td>\r\n<p>RIXPRA_ISAD30</p>\r\n<p>RIXPRA_ISAD30_3</p>\r\n</td>\r\n<td>Conversions for older release in EHP. No longer relevant</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p style=\"padding-left: 30px;\">Refer to the below SAP Notes before&#160;performing the system conversion&#160;to&#160;SAP S/4HANA:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 135px; width: 538px;\">\r\n<tbody>\r\n<tr>\r\n<td>SAP Notes</td>\r\n<td>\r\n<p>2334012</p>\r\n<p>2334008</p>\r\n<p>2270836</p>\r\n<p>2360860</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I320905"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I037481)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002348023/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002348023/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2334012", "RefComponent": "IS-ADEC-MPN", "RefTitle": "Pre-checks for MPN Migration to SAP S/4HANA", "RefUrl": "/notes/2334012"}, {"RefNumber": "2334008", "RefComponent": "IS-ADEC-MPN", "RefTitle": "S4TC Pre-Transition Checks for MPN", "RefUrl": "/notes/2334008"}, {"RefNumber": "2270836", "RefComponent": "IS-ADEC-MPN", "RefTitle": "S4TWL - Manufacturer Part Number", "RefUrl": "/notes/2270836"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2914617", "RefComponent": "LO-MD-MM", "RefTitle": "BAPI_STDMATERIAL_GETINTNUMBER - Runtime error on CL_MATNR_CHK_MAPPER", "RefUrl": "/notes/2914617 "}, {"RefNumber": "2555447", "RefComponent": "IS-ADEC-MPN", "RefTitle": "Corresponding internal number not found in material mapping", "RefUrl": "/notes/2555447 "}, {"RefNumber": "3055463", "RefComponent": "IS-A-LMN", "RefTitle": "Leading space conversion of MPNs to S/4 HANA", "RefUrl": "/notes/3055463 "}, {"RefNumber": "3055691", "RefComponent": "IS-A-LMN", "RefTitle": "S4TC Correction for 2910131: MFLE Precheck corrections", "RefUrl": "/notes/3055691 "}, {"RefNumber": "2270836", "RefComponent": "IS-ADEC-MPN", "RefTitle": "S4TWL - Manufacturer Part Number", "RefUrl": "/notes/2270836 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "602", "To": "602", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "603", "To": "603", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "604", "To": "604", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "605", "To": "605", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "606", "To": "606", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "616", "To": "616", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "617", "To": "617", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "618", "To": "618", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}