{"Request": {"Number": "1685280", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 567, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017391012017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001685280?language=E&token=FF407181F824BC902F6CD5A428450E04"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001685280", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001685280/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1685280"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.04.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DBA-SPO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Semantically Partitioned Object"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Basis", "value": "BW-WHM-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Semantically Partitioned Object", "value": "BW-WHM-DBA-SPO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA-SPO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1685280 - SPO: Data conversion for SAP HANA-optimized partitions"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Transaction RSMIGRHANADB enables the conversion of data to the data format used for the SAP HANA-optimized InfoProvider. This is supported for InfoCubes and for DataStore objects (DSOs). However, if an InfoCube or a DataStore object is a partition of a semantically partitioned object (SPO), no data conversion is possible.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Semantically partitioned object, SPO, partitions, SAP HANA-optimized, SAP HANA, HANA, SAP HANA database, HDB, conversion, data, data conversion, migration, RSMIGRHANADB, RSDRI_CONVERT_CUBE_TO_INMEMORY, RSDRI_HDB_SPO, RSDRI_HDB 261, RSDRI_HDB261.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note is relevant only if SAP HANA is used as the database platform (SY-DBSYS = 'HDB').<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Transaction RSMIGRHANADB now also supports the conversion of semantically partitioned objects (SPOs) or its partitions. This is possible for InfoCube-based and for DataStore-base SPOs.<br /><br />If transaction RSMIGRHANADB is called for a DataStore-based SPO, the options with regard to the receipt of the change log table content are equally valid for all partitions of the SPO.<br /><br />Every conversion run for an SPO is logged. You can find the logs using transaction SLG1 in object RSDRI and subobject REFACTORING. If you convert an SPO, a higher-level log is created for this. This log contains the individual logs for the conversion of the SPO partitions. The individual logs can be displayed by using the button for the details.<br /><br />The conversion of an SPO consists of the following substeps:</p> <OL>1. Check the SPO.</OL> <OL>2. Save the SPO.</OL> <OL>3. Lock the SPO.</OL> <OL>4. Convert the partitions of the SPO.</OL> <OL>5. Unlock the SPO.</OL> <p><br />In the first substep, the system checks first if a conversion is allowed for the current SPO. For example, you are allowed to carry out a conversion only if the SPO can be executed and if the modified (M) version matches the active (A) version. In addition, the restrictions contained in SAP Note with regard to the inclusion of a BW 3.X data flow also apply in the same way for the partitions of an SPO.<br /><br />Before the conversion of the partitions of the SPO, the SPO is locked. This is done using an SPO activation that is deliberately terminated. This lock ensures, for example, that the data transfer processes (DTPs), which load the data into the partitions of the SPO, can no longer be started. If all partitions of the SPO are converted successfully, the lock is removed by activating the SPO again. After this, the conversion of the SPO is complete.<br /><br />The locking of the SPO is different, depending on whether an InfoCube-based SPO or a DSO-based SPO is locked. For a DSO-based SPO the activation that is started for the locking terminates only for the first partition that contains data. Empty partitions that were processed before this, need not be converted and are activated normally. In contrast, the activation for the locking of an InfoCube-based SPO terminates for the first partition independent of whether this partition contains data or not. Therefore, all InfoCube partitions are always converted even if they do not contain data.<br /><br />You can see the current status of SPO conversion in the table RSDRI_HDB_SPO. During the conversion of the SPO, the currently executed substep is saved to the field TASK. If the conversion is completed successfully, this field is initialized. If the conversion terminates, the field TASK displays during which substep the termination occurred. When you start the terminated conversion again, a new entry is made in table RSDRI_HDB_SPO, that is entries of older conversion runs are retained for further analyses. If the field RUNNING is \"X\", this means that the substep displayed in field TASK is either currently carried out or that this substep was terminated anomalously, for example, due to a runtime error or due to debugging.<br /><br />If an SPO is locked using an entry in the status table RSDRI_HDB_SPO, the SPO can no longer be activated externally. In this case, the activation of the SPO is terminated and the system displays error message RSDRI_HDB 261. Even an activation of the SPO during the after import phase after a transport is no longer possible. The SPO can be activated normally only after the successful completion of the SAP HANA conversion.</p> <UL><LI>SAP NetWeaver BW 7.30<br /><br />Import Support Package 8 for SAP NetWeaver BW 7.30 (SAPKW73008) into your BW system. The Support Package is available when <B>SAP Note 1680997</B> \"SAPBWNews NW BW 7.30 BW ABAP SP8\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.31 (SAP NW BW 7.0 Enhancement Package 3)<br /><br />Import Support Package 4 for SAP NetWeaver BW 7.31 (SAPKW73104) into your BW system. The Support Package is available when <B>SAP Note 1680998</B> \"SAPBWNews NW BW 7.31/7.03 ABAP SP4\", which describes this Support Package in more detail, is released for customers.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><B>You must first read SAP Note 875986, which provides information about transaction SNOTE.</B><br /><br />To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note contains the words \"Preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DBA-ICUB (InfoCubes)"}, {"Key": "Other Components", "Value": "BW-WHM-DBA-DSO (DataStore Object (classic))"}, {"Key": "Responsible                                                                                         ", "Value": "D037853"}, {"Key": "Processor                                                                                           ", "Value": "D037853"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001685280/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001685280/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1891529", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: Reconversion of SAP HANA-optimized DSO partitions", "RefUrl": "/notes/1891529"}, {"RefNumber": "1686378", "RefComponent": "BW-WHM-DBA", "RefTitle": "BW on SAP HANA DB: Compatibility for 3.x data flow", "RefUrl": "/notes/1686378"}, {"RefNumber": "1680998", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 04", "RefUrl": "/notes/1680998"}, {"RefNumber": "1680997", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 08", "RefUrl": "/notes/1680997"}, {"RefNumber": "1665322", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "Conversion for SAP HANA-optimized DataStore objects", "RefUrl": "/notes/1665322"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1891529", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: Reconversion of SAP HANA-optimized DSO partitions", "RefUrl": "/notes/1891529 "}, {"RefNumber": "1680998", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 04", "RefUrl": "/notes/1680998 "}, {"RefNumber": "1680997", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 08", "RefUrl": "/notes/1680997 "}, {"RefNumber": "1686378", "RefComponent": "BW-WHM-DBA", "RefTitle": "BW on SAP HANA DB: Compatibility for 3.x data flow", "RefUrl": "/notes/1686378 "}, {"RefNumber": "1665322", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "Conversion for SAP HANA-optimized DataStore objects", "RefUrl": "/notes/1665322 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "711", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73008", "URL": "/supportpackage/SAPKW73008"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73104", "URL": "/supportpackage/SAPKW73104"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}