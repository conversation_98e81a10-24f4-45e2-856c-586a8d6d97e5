{"Request": {"Number": "1163719", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 301, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016505162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001163719?language=E&token=44E039D9DF57145CE1FC1B23F6B136CE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001163719", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001163719/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1163719"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.10.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON"}, "SAPComponentKeyText": {"_label": "Component", "value": "CCMS Monitoring & Alerting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1163719 - CCMS: Incorrect version of the JMON library"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Due to an incorrect version of the JMON library, the following symptoms occur:<br />1. When you register or start the CCMS agent (sapccmsr -j2ee), the system issues error messages such as:<br />ERROR: Cannot open Monitoring Segment 40 rtc = 245<br />Last reported error: [249]&#x00A0;&#x00A0;CCMS monitoring segment has wrong EYE CATCH.<br /> It seems to be created as non-unicode segment.<br />This program runs as unicode. Please use non-unicode version<br /><br />2. When you start the Java instance, a jstart/jlaunch process terminates. The development traces dev_dispatcher and dev_server contain a message stating that the process received a signal. The stack dump of the terminated thread ends with the line:<br />Java_com_sap_mona_api_JMonAPI_openMonitoringSegment__ILjava_lang_String_2II().<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><PERSON><PERSON>20, sapccmsr, EYE CATCH, JMON.DLL, libjmon.so libjmon.sl</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>jmon.dll/libjmon.so and sapccmsr agent are incompatible.<br /><br />jmon.dll/libjmon.so and sapccmsr are delivered together with the other kernel binaries (jlaunch, jcontrol, and so on).<br /><br />In earlier NetWeaver Java version, the library jmon.dll/libjmon.so was also delivered using the jmon.sda, which is contained in the component SAPJTECHF. The version from jmon.sda fis located in the directory /usr/sap/&lt;SID&gt;/&lt;Instanz&gt;/j2ee/os_libs.<br />This procedure was abolished since it had the risk that libjmon.so did not match the other kernel binaries.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>jmon.dll/libjmon.so was removed from the SDA, and is now only delivered together with the kernel.<br /><br />In addition, jmon.dll/libjmon.so is part of the CCMS patch collection =&gt; Note 1129577.<br /><br />As of the following Support Packages, jmon.dll/libjmon.so is no longer part of jmon.sda:<br />NW04&#x00A0;&#x00A0;Support Package 23<br />NW700 Support Package 16<br />NW701 Support Package 00<br /><br /><B>Important</B><br />Since a deployment of a new jmon.sda version does not remove the jmon.dll/libjmon.so from the Java system, manual steps are required.<br /><br />If your system still contains a jmon.dll/libjmon.so in the os_libs directory although you use one of the Supprt Package levels specified above, you must perform the following steps:</p> <OL>1. Use SDM to undeploy the component sap.com / com.sap.mona.api. This also undeploys the dependent component sap.com / com.sap.util.monitor.jarm.ccms.</OL> <OL>2. To reinstall the components, you have two options: You can either use SDM to redeploy the components SAPJTECHF and SAPJTECHS or you install the complete Support Package stack again. You can also combine this with an installation of a new Java Support Package stack.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The components SAPJTECHF and SAPJTECHS are available on Software Marketplace. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For NW 7. 00 (and EHPs) they are available under: <UL><UL><LI>Support Packages and Patches</LI></UL></UL> <UL><UL><LI>SAP NetWeaver</LI></UL></UL> <UL><UL><LI>SAP NETWEAVER</LI></UL></UL> <UL><UL><LI>SAP NETWEAVER 7.0</LI></UL></UL> <UL><UL><LI>Entry by Component</LI></UL></UL> <UL><UL><LI>Application Server Java</LI></UL></UL> <UL><UL><LI>SAP TECH S 7.00 OFFLINE or&#x00A0;&#x00A0;SAP JAVA TECH SERVICES 7.00</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For NW 04 you require the file J2EERT&lt; SP&gt;_&lt;Patch number&gt;.SAR from: <UL><UL><LI>Support Packages and Patches</LI></UL></UL> <UL><UL><LI>SAP NetWeaver</LI></UL></UL> <UL><UL><LI>SAP NETWEAVER</LI></UL></UL> <UL><UL><LI>SAP NETWEAVER 04</LI></UL></UL> <UL><UL><LI>Entry by Component</LI></UL></UL> <UL><UL><LI>Application Server Java</LI></UL></UL> <UL><UL><LI>SAP J2EE ENGINE 6.40</LI></UL></UL> <UL><UL><LI>#OS independent</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Unpack these. SAPJTECHF is located in the subdirectory J2EE-RUNT-CD/J2EE-ENG/OFFLINE and SAPJTECHS in the subdirectory J2EE-RUNT-CD/J2EE-ENG/ONLINE. <p><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I047532)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027190)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001163719/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163719/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "785489", "RefComponent": "BC-OP-AS4-JSE", "RefTitle": "IBM i: Java doesn't start due to wrong libjmon.so", "RefUrl": "/notes/785489"}, {"RefNumber": "1707012", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Prepare Installation Kits for Usage with DCK Kernel", "RefUrl": "/notes/1707012"}, {"RefNumber": "1666481", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666481"}, {"RefNumber": "1611933", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1611933"}, {"RefNumber": "1489787", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1489787"}, {"RefNumber": "1407092", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Undeploying component com.sap.mona.api during EHPI", "RefUrl": "/notes/1407092"}, {"RefNumber": "1283141", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP1 SR1 Java", "RefUrl": "/notes/1283141"}, {"RefNumber": "1165462", "RefComponent": "BC-UPG-RDM", "RefTitle": "SAP Enhancement Package Installer 7.00: IBM DB2 for i", "RefUrl": "/notes/1165462"}, {"RefNumber": "1129631", "RefComponent": "BC-CCM-MON", "RefTitle": "iSeries: CCMS monitoring for AS Java (NW04 and NW04S)", "RefUrl": "/notes/1129631"}, {"RefNumber": "1129577", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agents and kernels: <PERSON><PERSON> 2008", "RefUrl": "/notes/1129577"}, {"RefNumber": "1033491", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS monitoring for AS Java (NetWeaver2004s, 7.00)", "RefUrl": "/notes/1033491"}, {"RefNumber": "1029770", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS monitoring for AS Java (NetWeaver2004, 6.40)", "RefUrl": "/notes/1029770"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1165462", "RefComponent": "BC-UPG-RDM", "RefTitle": "SAP Enhancement Package Installer 7.00: IBM DB2 for i", "RefUrl": "/notes/1165462 "}, {"RefNumber": "1707012", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Prepare Installation Kits for Usage with DCK Kernel", "RefUrl": "/notes/1707012 "}, {"RefNumber": "1283141", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP1 SR1 Java", "RefUrl": "/notes/1283141 "}, {"RefNumber": "1129577", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agents and kernels: <PERSON><PERSON> 2008", "RefUrl": "/notes/1129577 "}, {"RefNumber": "1033491", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS monitoring for AS Java (NetWeaver2004s, 7.00)", "RefUrl": "/notes/1033491 "}, {"RefNumber": "1029770", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS monitoring for AS Java (NetWeaver2004, 6.40)", "RefUrl": "/notes/1029770 "}, {"RefNumber": "1407092", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Undeploying component com.sap.mona.api during EHPI", "RefUrl": "/notes/1407092 "}, {"RefNumber": "785489", "RefComponent": "BC-OP-AS4-JSE", "RefTitle": "IBM i: Java doesn't start due to wrong libjmon.so", "RefUrl": "/notes/785489 "}, {"RefNumber": "1129631", "RefComponent": "BC-CCM-MON", "RefTitle": "iSeries: CCMS monitoring for AS Java (NW04 and NW04S)", "RefUrl": "/notes/1129631 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP-JEE", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "SAP-JEE", "From": "7.00", "To": "7.01", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}