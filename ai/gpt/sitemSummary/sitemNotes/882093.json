{"Request": {"Number": "882093", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 504, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016010792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000882093?language=E&token=DBC2627785BB7163679E0B34C2A33F20"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000882093", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000882093/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "882093"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "882093 - HBRRAIS0 to run on all companies at once"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The report HBRRAIS0 can only run for one branch at a time. This fact does not able to report all branches of a company in just one file.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>brazil brasil report HBRRAIS0&#x00A0;&#x00A0;BUKRS&#x00A0;&#x00A0;m&#x00FA;ltiplas empresas&#x00A0;&#x00A0;T7BR06</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The report was not prepared to run all branches belonging to one company.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Solution will be provided by Support Package. This new features are only available for release 4.6C and up.<br /><br />Advance delivery files are also available as follow:<br /></p> <UL><LI>For release 4.6C, please apply in the following order the attached files:</LI></UL> <UL><UL><LI>L9CK203602_46C_1.CAR</LI></UL></UL> <UL><UL><LI>L9CK203602_46C_2.CAR</LI></UL></UL> <p></p> <UL><LI>For release 4.70, please apply the attached file:</LI></UL> <UL><UL><LI>L6BK105008.CAR</LI></UL></UL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Some objects that are not relevant to the process were released<br />in a second Support Package (SAPKE47061), they will be installed when you update your Support Packages.</p> <UL><LI>For release 5.00, please apply the attached file:</LI></UL> <UL><UL><LI>L6DK041831_500.CAR</LI></UL></UL> <p><br />IMPORTANT:<br />Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br />It's possible to run the report HBRRAIS0 for several branches following the next steps<br /><br />Apart from the code changes listed, some dictionary (SE11) changes are needed too.<br /><br />Apply the following source code changes, or receive the changes &#x00A0;&#x00A0;automatically, when applying the next available Support Package.<br /><br />For each company code you have one responsible branch, for this it was created the following domain, data element and table.<br /><br />1)Domain<br />Create domain PBR_INEMP&#x00A0;&#x00A0;- Char 1 (Company type)<br />Pachage: PB37<br />Description: Indicador do porte da empresa<br />With the&#x00A0;&#x00A0;following fixed values:<br />M Microcompany<br />P Small size<br />S not classified<br /><br />2)Change data elements PBR_INEMP<br />From Domain:CHAR1<br />To Domain:&#x00A0;&#x00A0;PBR_INEMP<br /><br />3) Create the data elements below:<br /><br />Data Elem: PBR_SMPL<br />Pachage:&#x00A0;&#x00A0; PB37<br />Domain:&#x00A0;&#x00A0;&#x00A0;&#x00A0;XFELD<br />Description:&#x00A0;&#x00A0;Indicador op&#x00E7;&#x00E3;o simples<br /><br />Denominador de campo<br />Compr&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Denominador de campo<br />-----------------------------------<br />Breve&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SimOpInd<br />M&#x00E9;dio&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ind op&#x00E7;&#x00E3;o simples<br />longa&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Indicador op&#x00E7;&#x00E3;o simples<br />T&#x00ED;tulo&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Indicador op&#x00E7;&#x00E3;o simples<br /><br />Data Elem.: PBR_PAT<br />Pachage:&#x00A0;&#x00A0;&#x00A0;&#x00A0;PB37<br />Domain:&#x00A0;&#x00A0;&#x00A0;&#x00A0; XFELD<br /><br />Description: PAT participation<br /><br />Denominador de campo<br />Compr&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Denominador de campo<br />-----------------------------------<br />Breve&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PATpartici<br />M&#x00E9;dio&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PAT participation<br />longa&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PAT participation<br />T&#x00ED;tulo&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PAT participation<br /><br />4) Table t7br06 (type C - customizing table ) :<br />Technical Settings<br />Data class: APPL2<br />Size catagory:0<br />Buffering:Buffering switched on<br />Buffering type: Generic areas buffered<br /><br />Fields Key Init. Field&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data&#x00A0;&#x00A0; Length. Short Text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type<br />-----------------------------------------------------------------------<br />MANDT&#x00A0;&#x00A0; !&#x00A0;&#x00A0;&#x00A0;&#x00A0;!&#x00A0;&#x00A0; MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CLNT &#x00A0;&#x00A0; 3  &#x00A0;&#x00A0; Mandante<br />BUKRS&#x00A0;&#x00A0; !&#x00A0;&#x00A0;&#x00A0;&#x00A0;!&#x00A0;&#x00A0; BUKRS &#x00A0;&#x00A0; CHAR &#x00A0;&#x00A0; 4 &#x00A0;&#x00A0; Empresa<br />ENDDA&#x00A0;&#x00A0; !&#x00A0;&#x00A0;&#x00A0;&#x00A0;!&#x00A0;&#x00A0; ENDDA &#x00A0;&#x00A0; DATS &#x00A0;&#x00A0; 8 &#x00A0;&#x00A0; Fim da validade<br />BEGDA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!&#x00A0;&#x00A0; BEGDA &#x00A0;&#x00A0; DATS &#x00A0;&#x00A0; 8  &#x00A0;&#x00A0; In&#x00ED;cio da Validade<br />INEMP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_INEMP CHAR &#x00A0;&#x00A0; 1 &#x00A0;&#x00A0; Indicador do porte da empresa<br />SMPL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PBR_SMPL&#x00A0;&#x00A0; CHAR &#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Indicador op&#x00E7;&#x00E3;o simples<br />PAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_PAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;HAR &#x00A0;&#x00A0; 1 &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PAT participation<br /><br />5)The maintenance of the new tables was made with the views V_t7br06.<br />Package : PB37<br />Description: View par&#x00E2;metros RAIS para consolida&#x00E7;&#x00E3;o por grupo de empresas<br /><br />Table: T7BR06<br />view fields&#x00A0;&#x00A0;Data elem.<br />MANDT &#x00A0;&#x00A0;&#x00A0;&#x00A0; MANDT<br />BUKRS &#x00A0;&#x00A0;&#x00A0;&#x00A0; BUKRS<br />ENDDA &#x00A0;&#x00A0;&#x00A0;&#x00A0; ENDDA<br />BEGDA &#x00A0;&#x00A0;&#x00A0;&#x00A0; BEGDA<br />INEMP &#x00A0;&#x00A0;&#x00A0;&#x00A0; PBR_INEMP<br />SMPL &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PBR_SMPL<br />PAT &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PBR_PAT<br /><br />Access: read,change,delete and insert<br />Delivery Class: C<br />Data Browser/Table View Maint: X Display / maintenance Allowed<br /><br />6)Table Maint.Dialog Generat<br />Table/View&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V_T7BR06<br />Authorization Group&#x00A0;&#x00A0;PC<br />Authorization object S_TABU_DIS<br />Function group&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; HRPADBR18<br />Package&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PB37<br /><br />7)Create to selection texts BUKRSR&#x00A0;&#x00A0;= \"Empresa repons&#x00E1;vel\" in program HBRRAIS0<br /><br />8)You must customize the company code and responsible branch entries in V_t7br06 if you want to use many branches ( range ).<br /><br />9)Using transaction SM30, insert the following entries in view V_t7br06:<br /><br />When program uses table T7BR06:.<br />-&gt; If table T7BR06 is filled, the program use this table to use the INEMP, SMPL and PAT for each company code filled, anything else use program parameter.<br /><br />For range company code, Rais Program have one company code / filial responsible and both belongs range.<br /><br />10)If manually applied, it may be necessary to create<br />in message class HRPAYBR99 via tr. SE91, the two messages with the following text:<br />Message number 203<br />Filial Empresa respons&#x00E1;vel para empresa &amp;1 n&#x00E3;o encontrada.<br /><br />Message number 204<br />A empresa &amp;1 n&#x00E3;o consta na tabela T7BR06, verifique a configura&#x00E7;&#x00E3;o.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031094)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I810950)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000882093/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L6BK105008.CAR", "FileSize": "140", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000452742005&iv_version=0008&iv_guid=4372082C5B64734794A477E77D156520"}, {"FileName": "L6DK041831_500.CAR", "FileSize": "79", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000452742005&iv_version=0008&iv_guid=D51F8F2E827AD44AA9DB9B1D4124A194"}, {"FileName": "L9CK203976_46C_2.CAR", "FileSize": "37", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000452742005&iv_version=0008&iv_guid=187E69A69B094A46B5DE4CAA3BEA080D"}, {"FileName": "L9CK203602_46C_1.CAR", "FileSize": "32", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000452742005&iv_version=0008&iv_guid=FEA74CADCC72F340A16AF66852DFFA7D"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CA8", "URL": "/supportpackage/SAPKE46CA8"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47052", "URL": "/supportpackage/SAPKE47052"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47061", "URL": "/supportpackage/SAPKE47061"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50019", "URL": "/supportpackage/SAPKE50019"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60004", "URL": "/supportpackage/SAPKE60004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}