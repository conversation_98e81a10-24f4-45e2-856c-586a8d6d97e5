{"Request": {"Number": "207223", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 470, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014792462017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000207223?language=E&token=FA07EA152E705D1CA8654AA65DF21BA6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000207223", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000207223/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "207223"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 67}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "207223 - SAP EarlyWatch Alert Processed at SAP"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use the EarlyWatch Alert (EWA) service for an ABAP based system.<br />The&#160;EarlyWatch Alert can be processed in your own SAP Solution Manager system or at SAP.&#160;The EWA processed at SAP&#160;is the collaboration platform to work together on EWA data as described in the&#160;<a target=\"_blank\" href=\"https://d.dam.sap.com/a/gcNVUNU\">EWA_Workspace_as_a_Collaboration_Platform presentation</a>.</p>\r\n<p>In your&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/\">SAP One Support Launchpad</a>&#160;you find the apps for the EarlyWatch Alert: The&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaworkspace\">SAP EarlyWatch Alert Workspace</a>&#160;is the entry point to the current EWA of your whole system landscape, and additional content exclusively available on the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaworkspace\">SAP EarlyWatch Alert Workspace</a>. From here, you easily navigate to all other application like the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaviewer\">EarlyWatch Alert Reports</a>&#160;(also known as&#160;<em>EarlyWatch Alert Viewer</em>), the <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewasop/soopDetail\">EarlyWatch Alert Dashboard</a>, and the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewasolutionfinder\">EarlyWatch Alert Solution Finder</a>.</p>\r\n<p>The Solution Manager provides the results as a document (Word, PDF, html), and in the EarlyWatch Alert Viewer app. If the EarlyWatch Alert is processed at SAP, you can browse the results&#160;in the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaviewer\">SAP EarlyWatch Alert Reports</a>&#160;app.&#160;The EWA is also provided as a document, which can be downloaded from the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaviewer\">EarlyWatch Alert Reports</a>&#160;app or&#160;from the&#160;<em>Service Messages&#160;</em>app, both found in your&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/\">SAP One Support Launchpad</a>.</p>\r\n<p>To use the EWA processed at SAP, the EWA service data must be sent to SAP. The service data is collected in the&#160;<em>Service Data Control Center&#160;</em>(<em>SDCC</em>, transaction SDCCN) of the ABAP system, from where it can be sent to SAP, or to a Solution Manager, or to a <a target=\"_blank\" href=\"https://support.sap.com/en/alm/focused-solutions/focused-run.html\">Focused Run</a> (FRUN). This SAP Note is about the first scenario. It explains how to set up the EarlyWatch Alert processed at SAP.&#160;At SAP the&#160;EWA results&#160;are evaluated and you are contacted&#160;in case important issues are identified where SAP can support you.</p>\r\n<p>The Earlywatch Alert&#160;is a monitoring function in the SAP Solution Manager.&#160;On the Solution Manager, you can configure whether the EWA service data is&#160;sent to SAP or not. For production systems, the default setting is '<em>on</em>'.</p>\r\n<p>Focused Run is a platform allowing to administrate the EWA in large system landscapes. Focused Run&#160;provides central access to the EWA processed at SAP. For the systems administrated by FRUN, the FRUN sends all EWA service data for processing to SAP.</p>\r\n<p><a target=\"_blank\" href=\"/notes/1257308\">SAP Note 1257308</a>&#160;provides an overview. Section&#160;<a target=\"_blank\" href=\"/notes/1257308#SETUP\">Setup</a>&#160;provides the links to documentation how to setup EWA in Solution Manager and Focused Run. The entry point for more information&#160;on the EarlyWatch Alert is quick link&#160;<a target=\"_blank\" href=\"http://support.sap.com/ewa\">/ewa</a>&#160;on the&#160;<a target=\"_blank\" href=\"http://support.sap.com/ewa\">SAP Support Portal</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>EWA, EarlyWatch Alert, Remote Services,&#160;Automatic Session Manager, SDCC, SDCCN, SAP Solution Manager, Direct EWA, Z-EWA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP only processes the EarlyWatch Alert for ABAP systems configured as <em>productive </em>or <em>test </em>system (based on the client settings in transaction SCC4). Besides for ABAP, also service data of HANA systems can be sent to SAP. For other system types only Focused Run is capable to send service data to SAP.</p>\r\n<p>For ABAP based systems the EarlyWatch Alert processed at SAP and the EarlyWatch Alert processed in a Solution Manager are not identical, but share a very broad common core. The ST-SER code line processing at SAP is the one released with <em>Service Content Update </em>for all Solution Manager systems.</p>\r\n<p>The <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaworkspace\">SAP EarlyWatch Alert Workspace</a>&#160;provides additional content (e.g. regarding Fiori app performance and configuration or the predictive check when a SAP HANA database table may hit the 2 billion record limit) and functionality (e.g. 'time travel' across subsequent EWA reports in the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaviewer\">EarlyWatch Alert Reports</a>&#160;app and full text search with specific filters in the <em>Solution Finder</em>) which is not available in the Solution Manager.<br />Besides the service data collected in <em>SDCC </em>the Solution Manager evaluates&#160;data exclusively available in the Solution Manager and uses the tight integration with the managed system to configure optional EWA contents. Examples are:</p>\r\n<ul>\r\n<li>Business Key Figures (BKF) data collection for EWA can only be activated with the system connected to a Solution Manager. The activation is done in the Solution Manager.&#160;</li>\r\n<li>Among the checks for ABAP systems which are not part of the EWA processed at SAP because they evaluate SMD data are:</li>\r\n<ul>\r\n<li>The Solution Manager collects performance and workload data (accessible in the Root Cause Analysis [RCA] Work Center). This&#160;data is comparable to the data in the Workload Monitor (transaction ST03 on the ABAP system), but in some respect more detailed and retained over a longer period of time (with default settings).<br />Based on these data a more detailed performance evaluation is performed in the EarlyWatch Alert processed in the Solution Manager with a detection of bottleneck situations.</li>\r\n<li>A trend analysis over a short term (four weeks) and long term (one&#160;year) period is done based on this RCA data.</li>\r\n</ul>\r\n<li>Hardware Capacity checks make use of the more detailed RCA data for CPU and memory usage.</li>\r\n<li>Certain checks for CRM systems require Solution Manager.</li>\r\n<li>Only checks for ABAP systems are part of the EWA processed at SAP. No Java checks are included, even for double stack systems. (The FRUN supports also service data collection for Java and certain Business Objects systems.)&#160;</li>\r\n</ul>\r\n<p>For an ABAP system running on SAP HANA all HANA specific checks are also included in the EarlyWatch Alert for the ABAP system processed at SAP.<br /><br />It is recommended to&#160;only send service data to SAP for productive systems. For other systems, data&#160;is&#160;also accepted and processed. To create an EarlyWatch Alert in SDCCN or a development system requires ST-PI 740 SP23 (ST-PI 2008_1_7xx SP 33). Please only send EWA data to SAP if you plan to consume the EWA results in the <a target=\"_blank\" href=\"https://me.sap.com\">SAP for Me</a> apps.</p>\r\n<p>Please be aware that the&#160;EarlyWatch Alerts processed at SAP identify the system by the key&#160;&lt;SID&gt;, &lt;Installation Number&gt; and&#160;&lt;System Number&gt;. Please avoid sending service data of systems with identical keys to SAP. For example, if you run a productive system and an associated standby or backup system having the same &lt;Installation Number&gt; and &lt;SID&gt;,&#160;please make sure to have maintained different system numbers (this means a correct license) on the systems.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To be able to send service data to SAP, the ABAP based system must be connected to SAP Support Backbone. To configure and check the connection to SAP Support Backbone you may use task list&#160;'SAP_BASIS_CONFIG_OSS_COMM', see <a target=\"_blank\" href=\"/notes/2793641\">SAP Note&#160;2793641</a>. (In case the system is on SAP_BASIS 7.31 or below, please see <a target=\"_blank\" href=\"/notes/2837310\">SAP Note 2837310</a>&#160;instead.&#160;SAP_BASIS 7.00 is required as minimum release.) It is recommended to start from a current ST-PI support package. Otherwise you must check&#160;<a target=\"_blank\" href=\"/notes/763561\">SAP Note 763561</a>&#160;how this destination is created on your specific support package level.</p>\r\n<p>Instead of following this SAP Note, you may execute report <em><strong>RTCCTOOL</strong></em>&#160;and follow its instructions.<br /><br /><strong>RTCCTOOL - Service Preparation Check</strong><br />Please make sure that ST-A/PI (<a target=\"_blank\" href=\"/notes/69455\">SAP <strong>Note 69455</strong></a>) has been implemented in your system. This add-on includes a tool for service preparation: RTCCTOOL. Start the RTCCTOOL from the Service Data Control Center. Alternatively, you can also execute report RTCCTOOL in transactions SE38/SA38.<br />The report output an action list to prepare the system for a successful execution of all SAP remote services.</p>\r\n<p><strong>I.&#160;&#160;Activating the Earlywatch Alert processed at SAP in Service Data Control Center</strong></p>\r\n<p>The <em>Service Data Control Center</em> (transaction SDCCN) must be activated as per the documentation mentioned in <a target=\"_blank\" href=\"/notes/763561\">SAP Note 763561</a>&#160;(FAQ for <em>Service Data Control Center</em>).<br /><br />1. Call transaction SDCCN. Ensure that a 'Maintenance task' has been scheduled as per the above documentation, and that a Service Definition Refresh was executed recently.</p>\r\n<p>2. Data collection settings should be at default values. These settings are maintained and can be reset to default&#160;in SDCCN -&gt; Goto -&gt; Settings -&gt; Task specific-&gt; Session Data Collection -&gt; Data request -&gt; Settings -&gt; Periodic sessions.</p>\r\n<p>3. Create the relevant task, via: SDCCN -&gt; Task -&gt; Create -&gt; Request session data -&gt; Periodic -&gt; Earlywatch Alert -&gt; Continue</p>\r\n<p>'Schedule' the start date of the first session. A typical selection is Mondays at 4am. But for systems with large workload a later schedule can be appropriate: the daily ST03 aggregate for the Sunday before must already be created when the data collections runs. In a system with more than one ABAP application server,&#160;the weekly TOTAL aggregates for the foregoing week should already be calculated. The ST03 aggregates are created by&#160;the job <em>SAP Collector for Performance.&#160;</em>You can check collector protocols also in transaction ST03.</p>\r\n<p>4. Confirm the&#160;destination selected, which must point to SAP support backbone. There must always be <strong>at most</strong> one <em>active</em> destination pointing <em>to SAP</em> support backbone configured in&#160;SDCCN destination table (SDCCN -&gt; Goto -&gt; Settings -&gt; Task specific-&gt;&#160;Session Data Collection -&gt;&#160;RFC Destinations -&gt; Settings -&gt; table icon). In the destination table, use the <em>Check</em> function. In case the check fails or a <em>To SAP </em>destination does not yet exist, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/20f8ecd5028346a38fac89c2f3052bf6/SP5/en-US/loiob0605883e376454abce03682db18e39d_sps5.pdf\">Checklist</a> for https destination SAP-SUPPORT_PORTAL (used on ST-PI 740 and on ST-PI 2008_1_* only with&#160;<a target=\"_blank\" href=\"/notes/2837310\">SAP Note 2837310</a>).&#160;&#160;For more information about the SAP support backbone destination also see Q9 in <a target=\"_blank\" href=\"/notes/763561\">SAP Note 763561</a>.</p>\r\n<p>5. To stop sending Earlywatch Alert data to SAP you only have to delete the newest task of this type (on the <em>To do </em>tab).&#160;In the&#160;<em>Service Data Control Center</em>&#160;those sessions can be recognized by their download number containing a Z like 100Z000000123. The scenario where the ABAP system sends the data directly to SAP is also known as&#160;<em>direct EWA&#160;</em>or&#160;<em>Z-EWA</em>.</p>\r\n<p>6. If you need to make changes (to the day on which the Earlywatch Alert session is executed, or the frequency, etc.), you must delete the newest relevant task in tab 'To do'. Then you need to change the customizing of the task to suit your new requirements (see step 2). After this you can create a new periodic task.</p>\r\n<p>Should you experience problems during collecting or sending of the data these will be indicated through messages in the 'Detail log' of that task. In&#160;<a target=\"_blank\" href=\"/notes/763561\">SAP Note 763561</a> you can find additional information on the most frequent problems in <em>SDCC</em>.<br /><br /><strong>II. Access to the Earlywatch Alert Results</strong></p>\r\n<p>Your S-user requires certain authorizations to access EWA results. To request authorizations, contact one of the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/importantcontacts\" rel=\"noopener noreferrer\">administrators in your company</a>. Please request authorization \"<em>Service Reports and Feedback</em>\". There is a second special authorization \"<em>Display Security Alerts in SAP EarlyWatch Alert Workspace</em>\"&#160;to access to security-related content in the SAP EarlyWatch Alert Workspace. For details look up the <a target=\"_blank\" href=\"https://support.sap.com/user-admin-concept\">d</a><a target=\"_blank\" href=\"https://support.sap.com/user-admin-concept\">ocumentation</a>. Administrators find the authorizations in the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/user/management\">User Management&#160;application</a> listed under \"Reports\".<br /><br /><em>a) EarlyWatch Alert Workspace</em></p>\r\n<p>From the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaworkspace\">SAP EarlyWatch Alert Workspace</a>&#160;app&#160;in your&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/\">SAP One Support Launchpad</a> you can navigate to all other application like the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewaviewer\">EarlyWatch Alert Reports</a>, the EarlyWatch Alert Dashboard, and the <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/ewasolutionfinder\">EaryWatch Alert Solution Finder</a>.<br />With the introduction of&#160;<a target=\"_blank\" href=\"https://me.sap.com\">SAP for Me</a>&#160;the apps get new URLs (the old URLs being redirected):&#160;&#160;<a target=\"_blank\" href=\"https://me.sap.com/app/ewaworkspace\">SAP EarlyWatch Alert Workspace</a>,&#160;<a target=\"_blank\" href=\"https://me.sap.com/app/ewaviewer\">EarlyWatch Alert Reports</a>, the <a target=\"_blank\" href=\"https://me.sap.com/ewa/dashboard \">EarlyWatch Alert Dashboard</a>, and the&#160;<a target=\"_blank\" href=\"https://me.sap.com/ewa/solutionfinder\">EaryWatch Alert Solution Finder</a>.&#160;<a target=\"_blank\" href=\"https://me.sap.com/app/ewaworkspace\"><br /></a></p>\r\n<p><em>b) Earlywatch Alert reports in Service Messages</em></p>\r\n<p>After every EWA processed at SAP a service message named \"Session Report\" is created. This contains a link to the respective service report which can be downloaded&#160;via this link. You find the reports at <a target=\"_blank\" href=\"https://launchpad.support.sap.com/\">SAP ONE Support Launchpad</a> (<a target=\"_blank\" href=\"https://launchpad.support.sap.com/\">https://launchpad.support.sap.com/</a>) with the tile \"Service Messages&#160;- On Premise\".&#160;(For more information about service messages in Launchpad, refer to SAP Note <a target=\"_blank\" href=\"/notes/2319793\">2319793</a>.)</p>\r\n<p>Alternatively, on quick link&#160;<a target=\"_blank\" href=\"https://support.sap.com/servicemessages\">/servicemessages</a>&#160;-&gt; click \"Search for Service Messages\" (or use this <a target=\"_blank\" href=\"https://service.sap.com/&#126;form/handler?_APP=00200682500000002332&amp;_EVENT=DISPLAY&amp;ACTION=SRVMSGSEARCH\">direct link</a>).</p>\r\n<p>Please note that only the most current 12 EarlyWatch Alert reports are made available in the Service Messages.</p>\r\n<p>Beside Microsoft Word, there are many other applications capable of displaying the EarlyWatch Alert reports.</p>\r\n<p><br /><strong>III. Support for EarlyWatch Alert Processed at SAP</strong></p>\r\n<p>a) Incidents about the contents of the EarlyWatch Alert can be opened in component SV-SMG-SER-EWA.</p>\r\n<p>b) Incidents about problems with the <em>Service Data Control Center</em> (transaction SDCCN) can be opened in component SV-SMG-SDD.</p>\r\n<p><strong>Note for Installations On SAP HANA Database</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong><span style=\"text-decoration: underline;\">Note</span>:</strong><strong>&#160;</strong>After the installation of any new SAP ABAP system running on SAP HANA, you have to enable the SAP EarlyWatch Alert and send corresponding data to SAP &#8211; either by using SAP Solution Manager for SAP EarlyWatch Alert or by sending service data to SAP directly. On these systems (as of SAP NetWeaver 7.3),&#160;an automated procedure using&#160;automation framework ABAP Task Manager is provided. The automation task list Early Watch Alert to SAP Configuration sets up a periodical EWA data collection and the transfer of this data to SAP in Service Data Control Center (SDCCN). Basically the EWA activation is done by starting the ABAP Task Manager by calling transaction STC01, then&#160;choosing the task list /BDL/SDCCN_EWA_CONFIG and executing it.<br />This is documented in more detail in section <em>Post-Installation</em> of the guides &#8222;<em>Installation of SAP Systems based on the AS ABAP of SAP NetWeaver</em>\" on all OS platforms when running&#160;on SAP HANA database.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SER-EWA (EarlyWatch Alert)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028075)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D044039)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000207223/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207223/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2923799", "RefComponent": "XX-SER-NET", "RefTitle": "Final Shutdown of RFC Connections From Customer Systems to SAP", "RefUrl": "/notes/2923799"}, {"RefNumber": "2715504", "RefComponent": "SV-FRN-INF-SDA", "RefTitle": "Configure Early Watch <PERSON><PERSON>", "RefUrl": "/notes/2715504"}, {"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "883111", "RefComponent": "SV-SMG-SDD", "RefTitle": "Deactivating old EarlyWatchAlert (Transaction SCUI)", "RefUrl": "/notes/883111"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "2837310", "RefComponent": "SV-SMG-SDD", "RefTitle": "Connecting Legacy Systems Like Solution Manager 7.1 with https to SAP Support Backbone", "RefUrl": "/notes/2837310"}, {"RefNumber": "2793641", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "Automated Configuration of new Support Backbone Communication - Update 01", "RefUrl": "/notes/2793641"}, {"RefNumber": "2250709", "RefComponent": "SV-SMG-AUT", "RefTitle": "Solution Manager 7.2: End-User Roles and Authorizations Corrections as of SP09 and higher", "RefUrl": "/notes/2250709"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "1658306", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "'Illegal xml character.' When Opening EWA Report With Word", "RefUrl": "/notes/1658306"}, {"RefNumber": "1518015", "RefComponent": "SV-ES-SAC", "RefTitle": "Enterprise Support Prerequisites", "RefUrl": "/notes/1518015"}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308"}, {"RefNumber": "1095227", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1095227"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2685246", "RefComponent": "XX-SER-FORME", "RefTitle": "Kernel information is missing, incomplete, or incorrect - SAP for Me", "RefUrl": "/notes/2685246 "}, {"RefNumber": "3067195", "RefComponent": "SV-SCS-EWA", "RefTitle": "EarlyWatch Alert Dashboard: User Experience section is missing", "RefUrl": "/notes/3067195 "}, {"RefNumber": "2899458", "RefComponent": "SV-BO-REQ", "RefTitle": "Remote Service Delivery without SAP Solution Manager", "RefUrl": "/notes/2899458 "}, {"RefNumber": "2862417", "RefComponent": "SV-SMG-OST-FI", "RefTitle": "Kernel information fails to update in Maintenance section of Tactical Dashboard with Focused Insights ST-OST 200 Wave 3.1 and higher", "RefUrl": "/notes/2862417 "}, {"RefNumber": "2680554", "RefComponent": "SV-SCS-EWA", "RefTitle": "EWA send to SAP was not updated in launchpad", "RefUrl": "/notes/2680554 "}, {"RefNumber": "2630958", "RefComponent": "XX-SER-MCC", "RefTitle": "Red Early Watch Alert (EWA) report reviewed by SAP", "RefUrl": "/notes/2630958 "}, {"RefNumber": "2520319", "RefComponent": "SV-SCS-EWA", "RefTitle": "How to access the SAP EarlyWatch Alert apps in SAP for Me", "RefUrl": "/notes/2520319 "}, {"RefNumber": "3253487", "RefComponent": "SV-SCS-EWA", "RefTitle": "SAP HANA Migration Event in SAP EarlyWatch Alert Workspace", "RefUrl": "/notes/3253487 "}, {"RefNumber": "2616023", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI 2008_1_7xx SP19, ST-PI 740 SP09: Enhancements in functionality", "RefUrl": "/notes/2616023 "}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434 "}, {"RefNumber": "1518015", "RefComponent": "SV-ES-SAC", "RefTitle": "Enterprise Support Prerequisites", "RefUrl": "/notes/1518015 "}, {"RefNumber": "1658306", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "'Illegal xml character.' When Opening EWA Report With Word", "RefUrl": "/notes/1658306 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "883111", "RefComponent": "SV-SMG-SDD", "RefTitle": "Deactivating old EarlyWatchAlert (Transaction SCUI)", "RefUrl": "/notes/883111 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}