{"Request": {"Number": "1705431", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 400, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017419102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001705431?language=E&token=FBB8E6B20C1084C00E2D3ED32AE3325F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001705431", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001705431/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1705431"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2023"}, "SAPComponentKey": {"_label": "Component", "value": "EPM-BPC-NW"}, "SAPComponentKeyText": {"_label": "Component", "value": "NetWeaver Version"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Enterprise Performance Management", "value": "EPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Planning and Consolidation", "value": "EPM-BPC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EPM-BPC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "NetWeaver Version", "value": "EPM-BPC-NW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EPM-BPC-NW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1705431 - Business Planning and Consolidation - House keeping"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In a production environment of Business&#160;Planning and Consolidation, data volume might increase gradually over time. The increase in data volume might result in performance degration in certain function modules and system resources might be consumed excessively in some extreme cases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Data Archiving Strategy Archive PC BPC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note gives an overview of the current recommended data house keeping strategy for the BPC 10.X NetWeaver&#160;version, BPC for BW4/HANA version (include BPC 11.X and BPC 202X version).</p>\r\n<ul>\r\n<li>Transaction Data</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Business&#160;Planning and Conslidation stores transation data in SAP Data Warehouse(BW, BW4/HANA), it's recommend to use standard BW Information Lifecycle Mangement data aging stratgy.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Currently, only Data Archiving 3.X is supported by BPC 10.X NetWeaver&#160;version, and&#160;Data Archiving Process is supported&#160;by BPC&#160;for&#160;BW4/HANA version, refer to the <a target=\"_blank\" href=\"https://blogs.sap.com/2017/08/10/data-archiving-for-sap-business-planning-and-consolidation-11.0-version-for-sap-bw4hana/\">blog</a>&#160;for detail.</p>\r\n<p><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For SAP solution manager customer, please also refer to Data Volume Management function:&#160;https://support.sap.com/en/alm/solution-manager.html -&gt;Data Volume Management</p>\r\n<ul>\r\n<li>Audit Data</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Data change audit data volume grows the fatest when data auditing feature is enable. To keep the data audit report performance within a reasonable level, regular archiving on audit data is strongly recommended.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Audit data archiving can be started/scheduled with Data Manager Package by using process chain:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;/CPMB/ARCHIVE_DATA: Archive Data Change Audit<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;/CPMB/ARCHIVE_ACTIVITY: Archive Activity Audit<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For Data Manager related information, please refer to application help of EPM add-in section 36.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; To delete archived audit data physically, execute the program UJU_DELETE_AUDIT_DATA; To delete archived audit activity data, execute the program UJU_DELETE_AUDIT_DATA_2. Refer to KB <a target=\"_blank\" href=\"/notes/2031162\">2031162</a>&#160;for detail.</p>\r\n<ul>\r\n<li>Business Process Flow (BPF)</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Both Process Templates and Process Instances can be archived from the Web Admin Concole.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Process Instance can only be archived when the instance is suspended.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Process Template can only be archived when there is NO related active process instance.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;When Process Template is archvied, related Process instance will be archived automatically.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Please note that currently there is no easy way to restore or view the archived process templates or instances. Archived records only exist in the database.</p>\r\n<ul>\r\n<li>Data Manager</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Usually data manager data files( for both load transaction or master data from file) and rejected data files can be huge. Regular clean up for unused Data Manager files is recommended.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Execute program UJF_FILE_SERVICE_DLT_DM_FILES from transaction SA38, select environment and input desired file age, a list of data manager files older than defined age will be displayed, and user can choose files from the list to delete.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Data manager also generates temporary data during data load, the temporary data is store in table. If data load process aborted unexpectedly, junk data will be left in database without been cleaned. Execute program UJD_BACKEND_DATA_MAINTENANCE from transaction SA38, make selections, a list of junk data will be listed with advised action, user could choose to perform the action accordingly.</p>\r\n<ul>\r\n<li>File Service (UJFS)</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;With transaction UJFS, user will be able to delete files that are no longer needed(from context menu).</p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160;You can also execute program UJF_FILE_SERVICE_CLEAN_LOGS to delete logs from UJFS. Refer to note <a target=\"_blank\" href=\"/notes/1908533\">1908533</a>.</p>\r\n<p>Backup and Restore Tool (UJBR)</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Backup and restore tool is NOT recommended for data archiving purposes, but it could be used to backup all the data in case any unintended actions taken during data archiving activities.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EPM-BPC-BW4 (BPC/4)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I048930)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I048930)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001705431/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001705431/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1908533", "RefComponent": "EPM-BPC-NW-INF-FS", "RefTitle": "BPC File Service Cleanup Tool", "RefUrl": "/notes/1908533"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3414397", "RefComponent": "EPM-BPC-BW4-INS-BR", "RefTitle": "UJBR backup dump: CX_SY_ARITHMETIC_OVERFLOW", "RefUrl": "/notes/3414397 "}, {"RefNumber": "2796039", "RefComponent": "EPM-XLS", "RefTitle": "Clear Package Hangs, log says SUCCESS", "RefUrl": "/notes/2796039 "}, {"RefNumber": "1941503", "RefComponent": "EPM-BPC-NW-AR", "RefTitle": "How to trace the audit table growth in the backend BW system", "RefUrl": "/notes/1941503 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BPC4HANA", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "BPC4HANA", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "754", "Subsequent": ""}, {"SoftwareComponent": "CPMBPC", "From": "800", "To": "800", "Subsequent": ""}, {"SoftwareComponent": "CPMBPC", "From": "801", "To": "801", "Subsequent": ""}, {"SoftwareComponent": "CPMBPC", "From": "810", "To": "810", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}