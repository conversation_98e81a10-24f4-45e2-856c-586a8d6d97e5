{"Request": {"Number": "1079141", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 526, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006399202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001079141?language=E&token=A3475A6BB66C1F20D605A1B691D3013D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001079141", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1079141"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.07.2007"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-MI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Migration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Migration", "value": "RE-FX-MI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-MI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1079141 - Use of user exits in migration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Within migration, you want to be able to change specific data, or determine how something is converted, before the master data is converted.<br />You do not want to change the original data from RE Classic.<br /><br />We offer a number of user exits within migration. You can use these to change the following, for example:</p> <UL><LI>Contract text</LI></UL> <UL><LI>Status to be copied</LI></UL> <UL><LI>Change RE Classic conditions</LI></UL> <UL><LI>Change RE-FX conditions</LI></UL> <UL><LI>Change settlement unit text</LI></UL> <UL><LI>Change evaluation group for the external settlement</LI></UL> <UL><LI>Change sales-based rent agreement</LI></UL> <UL><LI>Change the frequency of a sales-based rent agreement</LI></UL> <UL><LI>Change the leading contract</LI></UL> <UL><LI>Change the organizational assignment</LI></UL> <UL><LI>Change the option rates</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>REMICL, REMICLBATCH, REMICL 098, CHANGE_OLD_CONDITION, CHANGE_CONDITION, CHANGE_OPTRATE<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Proceed as follows to implement the user exits used during the migration:</p> <OL>1. Define a user-defined class that is inherited from the class CL_REMI_MIG_ATTR_MODIFICATION. This class contains all user exits for the migration.</OL> <OL>2. Call transaction SE16 to enter the class name in the table TIVMISET, field IMPLCLNAME.</OL> <OL>3. Redefine required methods in the new class (see point 1 above).</OL> <p><br />Note that there is no support for the use of the user exits and the problems that may occur.<br /><br />We reserve the right to change existing interfaces, and to add additional interfaces on request.<br /><br />Attached find the new interface to change the option rate method (for business entity, building, land, settlement unit).&#x00A0;&#x00A0;You can change the option rate method for the contract in the user exit of the condition (CHANGE_CONDITION).<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028091)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028091)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001079141/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001079141/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "865444", "RefComponent": "RE-FX-MI", "RefTitle": "Migration from Classic RE", "RefUrl": "/notes/865444"}, {"RefNumber": "1878571", "RefComponent": "RE-FX-MI", "RefTitle": "Migration of general contracts: Distribution", "RefUrl": "/notes/1878571"}, {"RefNumber": "1436832", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: User exit, user fields", "RefUrl": "/notes/1436832"}, {"RefNumber": "1152641", "RefComponent": "RE-FX-MI", "RefTitle": "REMICL exit option rate: Detailed data missing", "RefUrl": "/notes/1152641"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1878571", "RefComponent": "RE-FX-MI", "RefTitle": "Migration of general contracts: Distribution", "RefUrl": "/notes/1878571 "}, {"RefNumber": "1436832", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: User exit, user fields", "RefUrl": "/notes/1436832 "}, {"RefNumber": "1152641", "RefComponent": "RE-FX-MI", "RefTitle": "REMICL exit option rate: Detailed data missing", "RefUrl": "/notes/1152641 "}, {"RefNumber": "865444", "RefComponent": "RE-FX-MI", "RefTitle": "Migration from Classic RE", "RefUrl": "/notes/865444 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD11", "URL": "/supportpackage/SAPKGPAD11"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60201INEAAPPL", "URL": "/supportpackage/SAPK-60201INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 2, "URL": "/corrins/0001079141/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1078122 ", "URL": "/notes/1078122 ", "Title": "User exit: Object assignment and VZGPO conversion", "Component": "RE-FX-MI"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1078122 ", "URL": "/notes/1078122 ", "Title": "User exit: Object assignment and VZGPO conversion", "Component": "RE-FX-MI"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}