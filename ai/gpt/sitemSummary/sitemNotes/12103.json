{"Request": {"Number": "12103", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 889, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014331022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000012103?language=E&token=A6A2EF4DB47C15AE19DE9D91DAFFA8B7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000012103", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000012103/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "12103"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 66}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.09.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-TUN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Workload Monitoring Tool"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workload Monitoring Tool", "value": "BC-CCM-MON-TUN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-TUN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "12103 - Contents of table TCOLL"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The job COLLECTOR_FOR_PERFORMANCEMONITOR or SAP_COLLECTOR_FOR_PERFMONITOR (report RSCOLL00) does not collect all the performance statistics data required or does not reorganize the entries in the MONI table.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Table TCOLL</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The entries in the TCOLL table differ from the specifications below, or RSCOLL00 report is not run hourly as a background job.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>BASIS Release 6.40: SAP Note 970449 describes the contents of the table TCOLL for Release 6.40.<br />BASIS Release 7.00: SAP Note 966309 describes the content of the table TCOLL for Release 7.00.<br />BASIS Release 7.10: SAP Note 966631 describes the content of the table TCOLL for Release 7.10.<br /><br />The SAP standard job SAP_COLLECTOR_FOR_PERFMONITOR must be scheduled hourly in each SAP system. The job must always be scheduled in client 000 with user DDIC or with a user with the same authorization (see Note 16083 for more information).<br />Each entry in the TCOLL table corresponds to a report that is run when executing the RSCOLL00 report (the COLLECTOR_FOR_PERFORMANCEMONITOR or the SAP_COLLECTOR_FOR_PERFMONITOR job). The reports are run at the specified times on the specified weekdays. The entry in the 'System' field controls where the report is to be executed. The following values are possible for<br />the 'System' field:<br /><br />up to Basis Release 6.20:</p>\r\n<ul>\r\n<li>'C': The report is started on the host with the database.</li>\r\n</ul>\r\n<ul>\r\n<li>'E': The report is started on the instance with the enqueue process.</li>\r\n</ul>\r\n<ul>\r\n<li>'*': The report is executed on all instances.</li>\r\n</ul>\r\n<p>The following indicators must be displayed in the 'System' field for reports that should only be started once for each collector run:<br /><br />'C' - If there is only one SAP instance on the database server, that is, the report is executed on this instance.<br /><br />'E' - If there are several SAP instances running on the database server and an SAP instance runs on the ENQ server, that is, the report is executed on the ENQ server.<br /><br />If there is no SAP instance on either the database server or the ENQ server, then the 'System' field must contain the indicator '%' and the 'Alternative System' must contain a server on which an SAP instance runs.<br /><br />The TCOLL table is maintained by using Transaction ST03 or ST03N<br />(ST03 &gt; Environment &gt; Data collector &gt; Collector frequency, or<br />ST03N &gt; 'Expert' mode &gt; Collector &amp; Database perf. &gt; Monitor-collector perf.<br />All of the entries in the TCOLL table are listed below.<br /><br />----------------------------------------------------------------------<br />I Report I Repeat factor I<br />I I System I<br />I I Altern. system I<br />I I Day of week I<br />I I Time of day I<br />----------------------------------------------------------------------<br />I RSDBPREV I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: I<br />----------------------------------------------------------------------<br />I RSHOSTDB I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />I RSHOSTPH I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : : : :X: : :X: : : : : : :X: : : : I<br />----------------------------------------------------------------------<br />I RSORA811 I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : : : : : : :X: : : : : : :X: : : : I<br />----------------------------------------------------------------------<br />I RSORAPAR I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : : :X: : : : :X: : : : : :X: : : : I<br />----------------------------------------------------------------------<br />I RSORATDB I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : :X: : : : : : : : : : : : :X: : : I<br />----------------------------------------------------------------------<br />I RSSTAT60 I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : :X: : :X: : :X: :X: : :X: :X: : :X: : :X: I<br />----------------------------------------------------------------------<br />I RSSTAT80 I 1 I<br />I (up to Rel.I * I<br />I &lt;4.6C) I X:X:X:X:X:X:X I<br />I see below. I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />I RSSTATPH I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : : : :X: : :X: : : : : :X: : : : : I<br />----------------------------------------------------------------------<br />I RSTUNE80 I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : : : :X: : : : :X: : : : : : : :X: I<br />----------------------------------------------------------------------<br /><br />The entries for RSORAPAR and RSORATDB no longer exist as of Basis Release 6.10. They are replaced by the corresponding RSDB_PAR and RSDB_TDB entries.<br /><br />In addition, entries of the following type CAN exist:<br />----------------------------------------------------------------------<br />I RSEFA350 I 1 I<br />I I C I<br />I I I<br />I I - any - I<br />I I - any - I<br />----------------------------------------------------------------------<br />I RSCUECRM I 1 I<br />I I C I<br />I I I<br />I I : :X: : : : I<br />I I : : : : :X: : : : : : : : : : : : : : : : : : I<br />----------------------------------------------------------------------<br />I RSXRPM I 1 I<br />I I C I<br />I I I<br />I I : :X: : : : I<br />I I : : : : :X: : : : : : : : : : : : : : : : : : I<br />----------------------------------------------------------------------<br />I RSRFCDLT I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />----------------------------------------------------------------------<br />I RSRFCDMN I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />Report RSEFA350 is no longer required as of the introduction of the new EarlyWatch alert.<br />No longer required. The entry can be removed from TCOLL.<br /><br />As of Release 3.0C, the following entry is also needed:<br />----------------------------------------------------------------------<br />I RSSTAT98 I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br /><br />I For systems on database platform DB2/6000, the following entry is also necessary as of Release 3.0C:<br />----------------------------------------------------------------------<br />I RSORAWDB I 1 I<br />I I C I<br />I I I<br />I I : : : : : :X I<br />I I : : : : : : : : : : :X: : : : : : : : : : : : I<br />----------------------------------------------------------------------<br />This entry no longer exists as of Basis Release 6.10 and will be replaced with the entry of the RSDB_WDB report.<br /><br /><br />As of SAP Release 3.0D, the following entry is also necessary:<br />----------------------------------------------------------------------<br />I RSSTAT90 I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: I<br />----------------------------------------------------------------------<br /><br />As of Release 4.0, there could be the following additional entries:<br />----------------------------------------------------------------------<br />I RSHOSTDC I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: I<br />----------------------------------------------------------------------<br />This entry is only required if there is no R/3 Instance on the database server.<br />----------------------------------------------------------------------<br />I RSAMON40 I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />The entry for RSAMON40 does not exist by default; it is used to create ST07 history data. If you want to use the ST07 history, add the following entry:<br /><br />As of Release 4.0:<br />either ----------------------------------------------------------------------<br />I RSSTAT80 I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />---------------------------------------------------------------------- or (EXCLUSIVE),<br />----------------------------------------------------------------------<br />I RSSTAT83 I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X: :X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />If for Release 4.0B you did not apply Hot Package 10 or the new version of RSSTAT83 from a transport from Note 127642, we recommend you use RSSTAT80 (this means delete the entry for RSSTAT83). Otherwise it would be better to use RSSTAT83 as it has a higher performance (this means delete the entry for RSSTAT80). For this subject also refer to Notes 127642 and 118956.<br />As of Release 4.6C, you can use only RSSTAT83. Scheduling RSSTAT80 is no longer possible as of Release 4.6C.<br /><br />As of Release 4.5B (RSSTAT82 also for earlier releases when using Note 127642) there could also be the following entries:<br />----------------------------------------------------------------------<br />I RSSTAT82 I 1 I<br />I (only I C I<br />I Rel. I I<br />I &lt; 4.6C) I X:X:X:X:X:X:X I<br />I I : : :X: : : : : : : : : : : : : : : : : : : : I<br />----------------------------------------------------------------------<br />I RSSTAT89 I 1 I<br />I I * I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: :X: I<br />----------------------------------------------------------------------<br />This entry for RSSTAT82 is only required when the TOTAL server of the workload analysis is not set up by RSSTAT80 (ST03 &gt; Goto &gt; Parameters &gt; Performance Database: 'Cumulate server statistics to a systemwide total statistic' is turned off, or if the RSSTAT83 runs instead of RSSTAT80).<br />The entry for RSSTAT82 is only necessary and only possible up to Release 4.6B. As of Release 4.6C, the calculation of the TOTAL data is performed by the RSSTAT83.<br /><br />The RSSTAT89 entry makes sense only if the application statistic is activated: that is, profile parameter stat/as_level = 1. Alternatively, instead of RSSTAT89, you can also schedule RSSTAT88 as follows (also refer to SAP Note 127642):<br />----------------------------------------------------------------------<br />I RSSTAT88 I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X: :X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br /><br />As of Release 4.6A:<br />Furthermore, you have the option, to move the load generated by the application collector to a later point in time (compared to the 'normal' workload collector). To do this, you have to enter the following report instead of RSSTAT88 or RSSTAT89:<br />----------------------------------------------------------------------<br />I RSSTAT87 I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X: :X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />You may only enter one of the three reports RSSTAT87, RSSTAT88 or RSSTAT89! (For more information about load reduction also refer to Note 127642).<br />As of Release 4.6C:<br />only the parallel collector RSSTAT83 can be used, it is no longer possible to schedule RSSTAT80.<br />----------------------------------------------------------------------<br />I RSSTAT83 I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br />If you want to create the TOTAL server of the workload analysis, this is automatically initiated from RSSTAT83. Therefore, it is no longer possible (and no longer necessary) to schedule RSSTAT82).<br /><br />As of Basis Release 6.10:<br />there is an additional entry in the TCOLL Table for R/3 systems with the Oracle database.<br />This is scheduled as follows:<br />(This entry also exists in systems (&gt;= 40B and &lt;=46D), in<br />which the basic transport for remote services (refer to Note 91488)<br />was implemented.<br /><br />Delete the entry for the RSORAVSH report from the TCOLL table.)<br />If you have imported the plug-in, the entry in <br />table TCOLL triggers an error message. Delete the entry <br />for report RSORAVSH from table TCOLL.)<br /><br />If, due to the fixed values in the domain COLL_RNAME, you cannot <br />implement this entry in the table TCOLL, <br />proceed according to Note 113270 and maintain<br />the fixed value for the report RSORAVSH in this<br />domain.<br />----------------------------------------------------------------------<br />I RSORAVSH I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br /><br />There are also the following new entries that were generated due to the renaming of the<br />reports RSORAPAR, RSORATDB and RSORAWDB. These reports have been renamed to indicate that  they can perform database-released activities for all database platforms.<br />----------------------------------------------------------------------<br />I RSDB_PAR I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : : :X: : : : :X: : : : : :X: : : : I<br />----------------------------------------------------------------------</p>\r\n<p>You can deactivate RSDB_PAR if SAP HANA is the local database and if there are no remote MaxDB databases, DB6 databases, or MSS databases integrated into DBACOCKPIT.</p>\r\n<p>---------------------------------------------------------------------<br />I RSDB_TDB I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I : : : : : : :X: : : : : : : : : : : : :X: : : I<br />----------------------------------------------------------------------<br />I RSDB_WDB I 1 I<br />I I C I<br />I I I<br />I I : : : : : :X I<br />I I : : : : : : : : : : :X: : : : : : : : : : : : I<br />----------------------------------------------------------------------<br /><br />As of Support Package 55, the following entry exists in Release 6.20:<br />----------------------------------------------------------------------<br />I RSORACOL I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br /><br />If you import the Support Packages described in Note 549174 into a sysstem that runs on a DB2/390 platform, you must delete the reports RSORAPAR (or RSDB_PAR as of Release 6.10), RSHOSTPH and RSSTATPH from table TCOLL. Otherwise, the collector terminates with the error DYNPRO_SEND_IN_BACKGROUND.<br /><br />As of Releases 4.6C Support Package 47, 4.6D Support Package 36, 6.10 Support Package 39, 6.20 Support Package 39:<br />New monitor functions are available for systems on the Oracle database platform (see Notes 705608 and 716000). For this, you must enter the RSORAHCL report:<br />----------------------------------------------------------------------<br />I RSORAHCL I 1 I<br />I I C I<br />I I I<br />I I X:X:X:X:X:X:X I<br />I I X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X:X I<br />----------------------------------------------------------------------<br /><br /><br />The reports listed in the TCOLL table perform the following tasks:</p>\r\n<ul>\r\n<li>Report RSDBPREV: Reads the current database status and stores it in the MONI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSEFA350: On Informix systems, an Update Statistics is executed on tables that have changed greatly. On Oracle platforms, a size analysis is performed on ALL R/3 tables. This takes a long time and loads the system heavily, and should only be executed when absolutely necessary.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSCUECRM: (Prepared) Triggers the collection of statistical application data in CRM systems.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSHOSTDB: Reads the system load from an operating system perspective and stores this data in the MONI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSHOSTDC: Reads the system load from an operating system perspective on a database server without an R/3 instance, and stores this data in the MONI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSHOSTPH: Reads the operating system kernel parameters and stores this data in the PAHI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSORA811: Reorganizes the BRBACKUP and BRARCHIVE logs.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSORAHCL: Updates Oracle monitor history data. The report is only relevant for systems with Oracle Release 9i or higher.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSORAPAR: Reads the database parameters and stores them in the PAHI table. As of Release 6.10, this report has been renamed RSDB_PAR.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSORATDB: Analyzes the tables and indexes and saves the results to the MONI table: As of Release 6.10, this report has been renamed RSDB_TDB.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSORAVSH: Analyzes wait situations on the Oracle database and stores this data in the MONI table (Oracle only).</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSORAWDB: Analyzes the database on DB2/6000 platforms. As of Release 6.10, this report has been renamed RSDB_WDB.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSORACOL: This is only relevant for ORACLE databases.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Reads the ORA_MON_DBCON table in databases to be monitored.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Reads the DB02_COLL_PLAN table to process the functions that are to be executed.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Executes the selected collector module.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>It is available for Release 6.20 as of Basis Support Package 55, for 6.40 as of Basis Support Package 14, and for 7.00 as of Basis Support Package 5.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT60: Reorganizes table MONI.</li>\r\n</ul>\r\n<ul>\r\n<li>As of Release 6.40, report RSSTAT61 replaces report RSSTAT60.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT62 enhances report RSSTAT61 as of Release 6.40. The report checks the MONI cluster table for defective entries.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT80 Reads the statistics records and then summarizes and stores this data in the MONI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT83: Like RSSTAT80, but several processes are started in parallel. Please see SAP Note 127642 for more information.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT82: Structures the TOTAL statistics of the workload analysis, if report RSSTAT80 does not do so, and stores them in table MONI.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT89: Reads the application statistical records, aggregates and stores them in table MONI.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT88: Like RSSTAT89, but several processes are started in parallel. Please see SAP Note 127642 for more information.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT87: Schedules a job that starts the RSSTAT88 report approximately 30 minutes after the SAP_COLLECTOR_FOR_PERFMONITOR.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT90: Reads the table access statistics and stores this data in the MONI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTAT98: Logs active instances in the SAPWLSERV table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSSTATPH: Reads the SAP profile parameters and stores this data in the PAHI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSTUNE80: Reads the buffer statistics and stores this data in the MONI table.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSRFCDLT: Reorganizes the RFC statistics table. In Releases 6.20 and 6.30, this report performs the tasks of RSICFDMN.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSRFCDMN: Reads the RFC statistics data from the file system and saves it to the database. Reorganizes the files in the file system.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSICFDLT: Reorganizes the ICF recorder entries in the database.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSICFDMN: Deletes the obsolete entries and deactivates the ICF analysis tool.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSICFJOB: Schedules a background job to execute RSICFDLT in 30 minutes.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSAMON40: Creates ST07 data.</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSDB_PAR: Reads the database parameters and stores them in the PAHI table. This report replaces report RSORAPAR as of Release 6.10</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSDB_TDB: Analyzes the tables and indexes and saves the results to the MONI table: This report replaces report RSORATDB as of Release 6.10</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSDB_WDB: Analyzes the database on DB2/6000 platforms. This report replaces report RSORAWDB as of Release 6.10</li>\r\n</ul>\r\n<ul>\r\n<li>Report RSXRPM: Triggers the collection of statistical application data in xRPM systems.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "SE16"}, {"Key": "Transaction codes", "Value": "ST03"}, {"Key": "Transaction codes", "Value": "ST07"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I052675)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I047806)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000012103/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012103/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "970449", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 640", "RefUrl": "/notes/970449"}, {"RefNumber": "966631", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 710", "RefUrl": "/notes/966631"}, {"RefNumber": "966309", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in SAP_BASIS 700 - 7.02 and 7.31 - 7.56", "RefUrl": "/notes/966309"}, {"RefNumber": "945279", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Workload Collector terminates due to memory problems", "RefUrl": "/notes/945279"}, {"RefNumber": "813452", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "ST04N: Oracle DB monitor with scheduling of RSORAHCL", "RefUrl": "/notes/813452"}, {"RefNumber": "79722", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/79722"}, {"RefNumber": "764684", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Incorrect instance selection with ST03/ST03N (2)", "RefUrl": "/notes/764684"}, {"RefNumber": "74283", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/74283"}, {"RefNumber": "73945", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Missing statistical data in database monitor", "RefUrl": "/notes/73945"}, {"RefNumber": "6833", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Deleting statistics file, size of statistics file", "RefUrl": "/notes/6833"}, {"RefNumber": "549174", "RefComponent": "BC-DB-DB2", "RefTitle": "Transaction DB03 no longer supported for DB2/390", "RefUrl": "/notes/549174"}, {"RefNumber": "529843", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Runtime problems in RSSTAT60", "RefUrl": "/notes/529843"}, {"RefNumber": "524816", "RefComponent": "BC-CST-EQ", "RefTitle": "Standalone enqueue server", "RefUrl": "/notes/524816"}, {"RefNumber": "523947", "RefComponent": "PP-MRP", "RefTitle": "Performance measurement of planned orders processing", "RefUrl": "/notes/523947"}, {"RefNumber": "168439", "RefComponent": "XX-SER-TCC-EW", "RefTitle": "Preparándose para una sesión de Early Watch ó GL", "RefUrl": "/notes/168439"}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083"}, {"RefNumber": "156198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156198"}, {"RefNumber": "1540278", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Poor performance in RSDB_TDB or RSDB4090", "RefUrl": "/notes/1540278"}, {"RefNumber": "144864", "RefComponent": "SV-SMG-SER", "RefTitle": "Setting Up the ABAP Workload Monitor", "RefUrl": "/notes/144864"}, {"RefNumber": "143550", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03/workload: User exit for individual processing", "RefUrl": "/notes/143550"}, {"RefNumber": "1394392", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 730", "RefUrl": "/notes/1394392"}, {"RefNumber": "1394391", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of the table TCOLL in SAP_BASIS 720", "RefUrl": "/notes/1394391"}, {"RefNumber": "136660", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "SAPLSTUW: High load due to parallel collectors", "RefUrl": "/notes/136660"}, {"RefNumber": "129779", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03: Data for 'TOTAL' miss. after Hot Package 10", "RefUrl": "/notes/129779"}, {"RefNumber": "118956", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syslog: No active link, CMSEND(A)", "RefUrl": "/notes/118956"}, {"RefNumber": "1057307", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1057307"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2774630", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "SAP_COLLECTOR_PERFMON_RSSTAT90 job cancelled with parameter rsdb/staton = 0", "RefUrl": "/notes/2774630 "}, {"RefNumber": "966309", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in SAP_BASIS 700 - 7.02 and 7.31 - 7.56", "RefUrl": "/notes/966309 "}, {"RefNumber": "1085937", "RefComponent": "SV-SMG-SER", "RefTitle": "Wait Event Analysis For SQL Server", "RefUrl": "/notes/1085937 "}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083 "}, {"RefNumber": "1540278", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Poor performance in RSDB_TDB or RSDB4090", "RefUrl": "/notes/1540278 "}, {"RefNumber": "1394392", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 730", "RefUrl": "/notes/1394392 "}, {"RefNumber": "1394391", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of the table TCOLL in SAP_BASIS 720", "RefUrl": "/notes/1394391 "}, {"RefNumber": "966631", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 710", "RefUrl": "/notes/966631 "}, {"RefNumber": "73945", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Missing statistical data in database monitor", "RefUrl": "/notes/73945 "}, {"RefNumber": "144864", "RefComponent": "SV-SMG-SER", "RefTitle": "Setting Up the ABAP Workload Monitor", "RefUrl": "/notes/144864 "}, {"RefNumber": "945279", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Workload Collector terminates due to memory problems", "RefUrl": "/notes/945279 "}, {"RefNumber": "529843", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Runtime problems in RSSTAT60", "RefUrl": "/notes/529843 "}, {"RefNumber": "764684", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Incorrect instance selection with ST03/ST03N (2)", "RefUrl": "/notes/764684 "}, {"RefNumber": "970449", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 640", "RefUrl": "/notes/970449 "}, {"RefNumber": "143550", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03/workload: User exit for individual processing", "RefUrl": "/notes/143550 "}, {"RefNumber": "524816", "RefComponent": "BC-CST-EQ", "RefTitle": "Standalone enqueue server", "RefUrl": "/notes/524816 "}, {"RefNumber": "1057307", "RefComponent": "CA-LT-ANA", "RefTitle": "SLO AS - Checklist for checking transaction monitor settings", "RefUrl": "/notes/1057307 "}, {"RefNumber": "549174", "RefComponent": "BC-DB-DB2", "RefTitle": "Transaction DB03 no longer supported for DB2/390", "RefUrl": "/notes/549174 "}, {"RefNumber": "813452", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "ST04N: Oracle DB monitor with scheduling of RSORAHCL", "RefUrl": "/notes/813452 "}, {"RefNumber": "779989", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Shortdump SYSTEM_LOAD_OF_PROGRAM_FAILED", "RefUrl": "/notes/779989 "}, {"RefNumber": "93325", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "No data for history in the ST07", "RefUrl": "/notes/93325 "}, {"RefNumber": "136660", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "SAPLSTUW: High load due to parallel collectors", "RefUrl": "/notes/136660 "}, {"RefNumber": "129779", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03: Data for 'TOTAL' miss. after Hot Package 10", "RefUrl": "/notes/129779 "}, {"RefNumber": "118956", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syslog: No active link, CMSEND(A)", "RefUrl": "/notes/118956 "}, {"RefNumber": "590297", "RefComponent": "BC-DB-MSS", "RefTitle": "Database error 50000, run only once a day", "RefUrl": "/notes/590297 "}, {"RefNumber": "523947", "RefComponent": "PP-MRP", "RefTitle": "Performance measurement of planned orders processing", "RefUrl": "/notes/523947 "}, {"RefNumber": "168439", "RefComponent": "XX-SER-TCC-EW", "RefTitle": "Preparándose para una sesión de Early Watch ó GL", "RefUrl": "/notes/168439 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62019", "URL": "/supportpackage/SAPKB62019"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70009", "URL": "/supportpackage/SAPKB70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}