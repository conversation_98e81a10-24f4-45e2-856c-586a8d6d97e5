{"Request": {"Number": "1227667", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 587, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016551742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001227667?language=E&token=67AF2737C38F6653CFEBA7A30FD3D8E6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001227667", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001227667/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1227667"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.10.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DST-TRF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transformation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Staging", "value": "BW-WHM-DST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transformation", "value": "BW-WHM-DST-TRF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST-TRF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1227667 - Expert routine consulting: Design rules"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note contains a template for design rules required to create a user-defined expert routine.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The functioning of any expert routine programmed by the customer remains the responsibility of the customer. This note provides guidance to help ensure that this will work successfully.<br /><br />Note the following design rules in particular, in order to ensure consistent runtime behavior in the case of a data-driven error situation. The default behavior of a data status that is not expected in the case of the expert routine is as follows: the data package that has already been processed is terminated, resulting in the termination of the entire request with an error status. The example below deals with the following topics:</p>\r\n<ul>\r\n<li><strong>Sending messages to the data transfer process (DTP) monitor.</strong></li>\r\n</ul>\r\n<ul>\r\n<li><strong>Creating reference relationships between the data records of the inbound data package SOURCE_PACKAGE and the outbound data package RESULT_PACKAGE. These are required for the functioning of the settings in DTP error handling.</strong></li>\r\n</ul>\r\n<p>In the following example, the error message XXX 900 is sent to the monitor if the value in the field 'a' is initial. The relevant data record is not transferred to RESULT_PACKAGE, and the 'skip' information is transferred to the monitor. The reference relationships between SOURCE_PACKAGE and RESULT_PACKAGE are therefore created without gaps.<br />The following source code is <strong>not executable</strong> and is only to be used as a template.<br /><br />METHOD expert_routine.<br /><br /><br /><br /> DATA: l_t_msg type rstr_ty_t_monitors,<br /> l_s_msg type rstmonitor.<br /><br /><br /> LOOP AT source_package ASSIGNING &lt;source_fields&gt;.<br /><br />*-- put the message to corresponding fields of structure l_s_msg<br />*- append l_s_msg to l_t_msg<br /><strong>*-- note: if you like to skip the inbound data record</strong><br /><strong>*- you have to set the skip flag SKIPPED in structure l_s_msg</strong><br /><strong>*- and don't append that record into the outbound RESULT_PACKAGE</strong><br /><strong>* The RECNO field in l_s_msg has to be filled by the record#</strong><br /><strong>* of the inbound data record of SOURCE_PACKAGE.</strong><br /> IF &lt;source_fields&gt;-'a' IS INITIAL.<br /><strong>*-- send message to monitor and skip record</strong><br /> l_s_msg-msgid = 'XXX'.<br /> l_s_msg-msgty = 'E'. \" Error<br /> l_s_msg-msgno = '900'.<br /> l_s_msg-msgv1, v2, v3, v4, <strong>\"set context according the definition</strong><br /> <strong>\"of the variables</strong><br /><strong>*-- source record needs to be set as erroneous and skipped</strong><br />l_s_msg-recno = &lt;source_fields&gt;-record<br /> l_s_msg-skipped = 'X'.<br /><br /> APPEND l_s_msg TO l_t_msg.<br /><br /> CALL METHOD cl_rstran_expert_rout_srv=&gt;send_message<br /> EXPORTING<br /> i_r_log = log<br /> i_rule_context = p_curr_rule<br /> i_seg_id = \"i_sc\"<br /> <strong> \"i_sc value depends on segment id of the SOURCE_PACKAGE</strong><br /><strong> \"is only &lt;&gt; 1 in case of a segmented DataSource</strong><br /><strong> \"in this case naming of source_package is like</strong><br /><strong> \"source_package_i i = segment ID</strong><br /> CHANGING<br /> c_t_msg = l_t_msg.<br /><br /> CONTINUE. \"continue with the next source packages record<br /><br /> ELSE.<br /> CALL METHOD new_record__expert_routine<br /> EXPORTING<br /> log = log<br /> source_segid = \"i_sc\" <strong>\"i_sc see above</strong><br /> source_record = &lt;source_fields&gt;-record<br /> target_segid = \"i_tg\"<br /> <strong>\"Release 7.0x i_tg is always '1' </strong><br /><strong> \"Release 7.3+ </strong><br /> <strong> \"i_tg value depends on segment id of the RESULT_PACKAGE</strong><br /><strong> \"is only &lt;&gt; 1 in case of InfoObject with Hierarchy</strong><br /><strong> \"in this case naming of result_package is like</strong><br /><strong> \"result_package_i i = segment ID  </strong><br /> IMPORTING<br /> record_new = result_fields-record.<br />*-- Put source_record to result_package<br /> APPEND result_fields TO result_package.<br /> ENDIF.<br /> ENDLOOP.<br /><br />ENDMETHOD. \"expert_routine</p>\r\n<p>Maintain the parameters in transaction SE91 if user-defined message IDs are used (starting with Z).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D002702"}, {"Key": "Processor                                                                                           ", "Value": "I309472"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001227667/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227667/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1349820", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Expert routine: Technical enhancements", "RefUrl": "/notes/1349820"}, {"RefNumber": "1258089", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Design rule: Adding records to the start routine", "RefUrl": "/notes/1258089"}, {"RefNumber": "1052648", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Migration of transfer rules and update rules for BW7.x", "RefUrl": "/notes/1052648"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1851875", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Consulting:DTP load terminates with error 'Package contains duplicate entries' in transformation processing in HANA DB", "RefUrl": "/notes/1851875 "}, {"RefNumber": "1052648", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Migration of transfer rules and update rules for BW7.x", "RefUrl": "/notes/1052648 "}, {"RefNumber": "1258089", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Design rule: Adding records to the start routine", "RefUrl": "/notes/1258089 "}, {"RefNumber": "1349820", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Expert routine: Technical enhancements", "RefUrl": "/notes/1349820 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "711", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}