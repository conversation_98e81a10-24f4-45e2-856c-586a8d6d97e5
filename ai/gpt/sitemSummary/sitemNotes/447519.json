{"Request": {"Number": "447519", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1387, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015119332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000447519?language=E&token=93218509B4C87DD15B0871602F5DED68"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000447519", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000447519/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "447519"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 307}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.03.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internationalization (I18N)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "447519 - Kernel patches for code pages, languages and locales"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />You encounter code page, language or locale problems in systems with Technology Basis 6.10 and/or higher.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />Locale, character set, code page, converter, CCC, CCC cache, R3load, R3ldctl, LOADTOOLS, R3trans, RFC library</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />- various problems -<br />This note provides an overview of the repairs contained in the kernel and other C and C++ programs.<br /><br />As of 1st September, 2012, kernel releases 700, 701, 710 and 711 are no longer maintained. Corrections will be available in kernel release 720, which is downward compatible.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Package 1</strong></p>\r\n<ul>\r\n<li>Unexpected internal errors are reported when you receive XML messages. The error depends on the message content.<br />For more details, see note 422313.</li>\r\n</ul>\r\n<ul>\r\n<li>The system required a very large cache in order to process code page 4105 (UCS-4 LE) on the input page.<br />For more details, see note 419519.</li>\r\n</ul>\r\n<p>6.10: Kernel patch 38<br />6.20: Repaired in Summer 2001.</p>\r\n<p><strong>Package 2</strong></p>\r\n<ul>\r\n<li>If strings (ABAP-type STRING) contain control characters such as carriage returns or line feeds, only # is displayed after an RFC shipping.<br />For more details, see note 435611.</li>\r\n</ul>\r\n<p>6.10: Kernel patch 193<br />6.20: Repaired before September 17, 2001</p>\r\n<p><strong>Package 3</strong></p>\r\n<ul>\r\n<li>The x'BA' byte in ISO-8859-2 should be the LATIN SMALL LETTER S WITH CEDILLA (U+015F) character, and not the LATIN SMALL LETTER LONG S (U+017F).<br />For more details, see note 436645.</li>\r\n</ul>\r\n<p>6.10: Kernel patch 296<br />6.20: Repaired before September 19, 2001</p>\r\n<p><strong>Package 4</strong></p>\r\n<ul>\r\n<li>Processing JIS as an output code page</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>T2 tables in the CCC cache receive attributes for ISO-2202.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New functions for escape sequences (STaRT and BACK).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Always use the JIS -&gt; SJIS converter, if possible.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>An RFC should quickly and correctly check the logon language.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New fields in the CCC cache to differentiate between languages that may be processed in the complete system and languages that in fact work on the dedicated application server.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New rscpGetPossibleLangList and rscpIsLangPossible functions.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A delay in reading the locales until they are required because RFC programs have no access to TCP0C.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ABAP also accesses the cache using the languages that actually work on the dedicated application server: CALL 'CUR_LCL' ID 'ASPLANGS'</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load and R3trans from MDMP to Unicode</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Many functions form the rscpMC API.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>include/rscpcpg.h deleted from krn/rscp/rscpgues.h.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Many minor repairs to the Guess package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Most test programs now have an -o-option.</li>\r\n</ul>\r\n<ul>\r\n<li>Even if the code page converter does not have to perform a conversion, it should return RSCPETOOLONG if the output buffer is too short.</li>\r\n</ul>\r\n<ul>\r\n<li>Special processing for in-place conversions now in krn/runt/abtral.c</li>\r\n</ul>\r\n<ul>\r\n<li>rscpCConvert() will be faster and rscpc_convert() will be a little slower.</li>\r\n</ul>\r\n<ul>\r\n<li>The output buffer is filled with blank characters if this was requested.</li>\r\n</ul>\r\n<ul>\r\n<li>If requested, checks are performed to see whether the input buffer only contains blank characters if the output buffer is full.</li>\r\n</ul>\r\n<ul>\r\n<li>Conversions from UTF-16 to old code pages may only write '#' if this was permitted.</li>\r\n</ul>\r\n<ul>\r\n<li>Error handling if the cache overflows when the system starts to load the control codes.</li>\r\n</ul>\r\n<ul>\r\n<li>The rscpeft__err_from_rtab, rscpefn__err_from_ntab, rscprti__rtab_info, rscpefq__err_from_sql and rscpefs__err_from_rsql functions are also available again in the non-DB rscperr.c variants.</li>\r\n</ul>\r\n<ul>\r\n<li>The RSCPEUNIQ error condition no longer writes a syslog entry immediately because (1) this has happened too often in the past and (2) most calling programs can now process this situation correctly.</li>\r\n</ul>\r\n<p>6.10: Kernel patch 296<br />6.20: Repaired before October 25, 2001<br /><br /></p>\r\n<p><strong>Package 5</strong></p>\r\n<ul>\r\n<li>UCS-2 and UCS-4 (&#126;=UTF-32) are included in the F5 memory.</li>\r\n</ul>\r\n<ul>\r\n<li>New converter object pairs for the correct conversion of text between ABAP and JavaScript.</li>\r\n</ul>\r\n<ul>\r\n<li>Minor corrections in the multi-object converter (for MDMP to Unicode migrations).</li>\r\n</ul>\r\n<p>6.10: Kernel patch 296<br />6.20: Repaired before November 19, 2001</p>\r\n<p><strong>Package 6</strong></p>\r\n<ul>\r\n<li>No process termination if the cache memory overflows when a code page rule is loaded.</li>\r\n</ul>\r\n<ul>\r\n<li>A switch in the cache for rules about the UTF-8-GUI logon.</li>\r\n</ul>\r\n<ul>\r\n<li>F1 memory processes UTF-8 as a 7-bit ASCII.</li>\r\n</ul>\r\n<ul>\r\n<li>In accordance with Unicode 3.1, UTF-8 contains a gap between U+FDD0 and U+FFEF.</li>\r\n</ul>\r\n<ul>\r\n<li>UTF-32 contains the U-0010FFFF character.</li>\r\n</ul>\r\n<ul>\r\n<li>The kernel is prepared for different UMGCCTL and TCP0F structures (It can handle different Support Package versions).</li>\r\n</ul>\r\n<ul>\r\n<li>There is no longer an overwriter in the CCC cache even if there are a lot of read errors in the NameTab.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Unicode systems ignore install/codepage/appl_server.</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>Kernel patch</td>\r\n<td>410</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch</td>\r\n<td>11</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>11</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans patch</td>\r\n<td>413</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>&nbsp;</td>\r\n<td>before January 30, 2002</td>\r\n</tr>\r\n<tr>\r\n<td>6.30</td>\r\n<td>&nbsp;</td>\r\n<td>before January 27, 2002</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 7:</strong></p>\r\n<ul>\r\n<li>F1 memory: Control characters for line feed and TAB are changed on AS/400 to the status from previous releases in the past years.</li>\r\n</ul>\r\n<ul>\r\n<li>F1 memory: Code page 1180 was added (type w_e7_europ). This is an \"ambiguous blended codepage\" containing Latin-1 and Latin-2 characters; the accents and other diacritic characters were deleted in the Latin-2 characters that are not in Latin-1.</li>\r\n</ul>\r\n<ul>\r\n<li>F1 memory: Uses 0100 for R/2 (not 0120).</li>\r\n</ul>\r\n<ul>\r\n<li>F1 memory: Modification due to 4110-1100 in front ends. UTF-8 treated as 7-bit US-ASCII. (Conversion is ideal only if there are no national special characters.)</li>\r\n</ul>\r\n<ul>\r\n<li>rscp converters can start without a profile and without being able to read from the database.</li>\r\n</ul>\r\n<ul>\r\n<li>The install/codepage/appl_server profile parameter should not be able to damage Unicode systems.</li>\r\n</ul>\r\n<ul>\r\n<li>If TCP00 data records were read from the F5 memory, you should also be able to find the segment data in the F5 memory.</li>\r\n</ul>\r\n<ul>\r\n<li>Six (6) missing pairs were added to the CCC converter:<br />UCS-2h -&gt; UCS-4 (l,h), UCS-4h -&gt; UCS-2 (l,h)<br />and UCS-4h -&gt; UTF-16(l,h)</li>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>kernel patch</td>\r\n<td>451</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>14</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>gui610 patch</td>\r\n<td>18</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch</td>\r\n<td>22</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>&#160;&#160;4</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>gui620 patch</td>\r\n<td>14</td>\r\n</tr>\r\n<tr>\r\n<td>6.30</td>\r\n<td>&nbsp;</td>\r\n<td>before February 14, 2002</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 8</strong></p>\r\n<ul>\r\n<li>CCC converter:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Implement a new cache for fast mapping of the SAP language onto the code page used. (rscpGetMBCodePageForLanguage, rscpGetCodePageForLanguage )</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Use this new cache during ROLL-IN.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction in rscpce1__copy_enlongating_t1<br />(Symptom: \"1/3\"(1146) -&gt; \"1\"(1100)).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add the 'rscpa0lib' library to prevent confusing differences between 6.20 and 6.30. In Release 6.20, however, the F1 memory is not in the libraries yet and rscpCSetUseF1() is not active yet.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction if libraries use the database and the F5 memory, but not the F4 memory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Also activate the interface between JAVASCRIPT and ABAP in MDMP systems.</li>\r\n</ul>\r\n<ul>\r\n<li>Activate sapcpm_codepages_mixed() in Unicode systems.</li>\r\n</ul>\r\n<ul>\r\n<li>Implement corrections if the system starts and the TCPDB table is still empty.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The install/codepage/db/transp and install/codepage/db/non_transp profile parameters should no longer be able to cause any damage.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>kernel patch</td>\r\n<td>508</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans patch</td>\r\n<td>524</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>lernel patch</td>\r\n<td>22</td>\r\n</tr>\r\n<tr>\r\n<td>6.30</td>\r\n<td>&nbsp;</td>\r\n<td>before 2002-03-20</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 9</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Also permits odd addresses if the conversion occurs from Unicode to Unicode (for example, UTF-16 to UTF-8).<br />(This solves problems in the spooler if Unicode systems want to print on Lexmark printers)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add the code page 1180.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add the SAP characters 01077, 01081, 01121, 01122, 01123 to the predefined TCP01.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.10 : Kernel patch 536<br />6.20 : Kernel patch 90<br />6.30 : before 2002-04-09</p>\r\n<p><strong>Package 10</strong></p>\r\n<ul>\r\n<li>Character width and line break</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The visible width of characters is now calculated, if possible, in the kernel and not in ABAP functions.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.10 : Kernel patch 551<br />6.20 : Kernel patch 171<br />6.30 : before 2002-05-08</p>\r\n<p><strong>Package 11</strong></p>\r\n<ul>\r\n<li>CCC converter:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Modification of the new cache from package #8 for fast mapping of the SAP language onto the code page used for cases where Unicode, but not ICU, is used.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.10 : Kernel patch 562<br />6.20 : Kernel patch ?<br />6.30 : before 2002-06-12</p>\r\n<p><strong>Package 12</strong></p>\r\n<ul>\r\n<li>Character width and line break</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More precise calculation of the width of character strings of Greek or Cyrillic letters if the application server uses Unicode and the output device use an Asian font.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>Kernel patch</td>\r\n<td>593</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>Kernel patch</td>\r\n<td>307</td>\r\n</tr>\r\n<tr>\r\n<td>6.30</td>\r\n<td>&nbsp;</td>\r\n<td>before 2002-05-28</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 13</strong></p>\r\n<ul>\r\n<li>F1 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add the pair 1804 -&gt; 0800 correctly (Before the correction, the F1 memory did not recognize the pair. EBCDIC is no longer frequently used in Release 6.xx, but it should still work).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add 6110, 6120, 6170, 6180, 6280, 6510 and 6570<br />(when you convert a system with an ambiguous blended code page to MDMP, you do not need to install *.CDP files on all front ends).</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>Kernel patch</td>\r\n<td>587</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>Kernel patch</td>\r\n<td>242</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>&#160;&#160;7</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC patch</td>\r\n<td>&#160;&#160;3</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>gui620 patch</td>\r\n<td>17</td>\r\n</tr>\r\n<tr>\r\n<td>6.30</td>\r\n<td>&nbsp;</td>\r\n<td>before 2002-05-28</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 14</strong></p>\r\n<ul>\r\n<li>Code page setting</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for NT for cases where the cache for code pages to be used is not accessible. (This can only occur in Release 6.10).</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.10 : Kernel patch 588<br />6.20 : Kernel patch ?<br />6.30 : Kernel patch before 2002-05-28<br /><br /></p>\r\n<p><strong>Package 15</strong></p>\r\n<ul>\r\n<li>CCC code page converter:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Use '#' again as a default replacement character even if the old procedures are called. (For the spool.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.10 : Kernel patch 596<br />6.20 : Kernel patch 242<br />6.30 : Repaired before July 5, 2002<br /><br /></p>\r\n<p><strong>Package 16</strong></p>\r\n<ul>\r\n<li>Small, special code page converters</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New Utf8nToUcnCP_4 and UcnToUtf8nCP_4 converters that contain special checks for RFC.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC code page converter:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>With UTF-8 as an input code page, the check for invalid bytes are more stringent. (Too few 10xx.xxxx follow-on bytes or 10xx.xxxx bytes without them following a suitable start byte.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A stricter distinction is now also made on the output side between those conversions that are to be converted with or without control codes. This prevents cases where data in Unicode systems contains incorrect control bytes and these were output unintentionally and unfiltered to a printer.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.10 : Kernel patch 593<br />6.20 : Kernel patch 242<br />6.30 : before 2002-06-10<br /><br /></p>\r\n<p><strong>Package 17</strong></p>\r\n<ul>\r\n<li>nlsfileu</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Repairs for two small, previously unreported, errors.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC code page converter and also TemSe administration</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Occasional termination of the work process with signal 11 on SUN if the unformatted trace for C functions is activated in the SAP trace.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>Kernel patch</td>\r\n<td>590</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>Kernel patch</td>\r\n<td>244</td>\r\n</tr>\r\n<tr>\r\n<td>6.30</td>\r\n<td>&nbsp;</td>\r\n<td>before 2002-07-18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 18</strong></p>\r\n<ul>\r\n<li>NLS Customizing:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Changed concept:<br />Unicode systems no longer use TCPDB.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When you log onto a Unicode system, UTF 16 GUIs are accepted and are the preferred choice (but there are no UTF 16 GUIs yet).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>\"CCC\" code page converter, functional changes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New switches to deactivate the conversion of SAP-specific characters.<br />(They are only effective for the user if they are used by higher software layers.)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;To maintain compatibility with older 6.10 and 6.20 kernels, the default for these switches is always 'SAPOwnChCv_try'.</p>\r\n<ul>\r\n<ul>\r\n<li>Useful substitute responses if the TCP00A table is missing.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New protection against missing memory in DIAG or elsewhere (there is now an error message instead of a work process termination).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>TCP01 is taken from the F5 memory (or F4 memory) if the third column is missing in the database.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error corrections in functions that import JIS (because the errors only occur with poor quality data, they were never observed by users and were only detected in SAP-internal tests after ten years).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Codepage converter \"CCC\", acceleration</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Accelerated conversions of old, byte-oriented code pages to Unicode by \"T1 shortcuts\". (The \"T1 tables\" are structures in the shared memory cache. They usually contain internal character identifiers. In suitable cases, copies of these are created and the output bytes are entered directly).</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;To do this, the converter must see the \"system code page\" attribute from the TCP00 table.</p>\r\n<ul>\r\n<ul>\r\n<li>Accelerated conversion of Unicode to old, byte-based code pages.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Nine new, more specialized converter methods:<br />&#160;&#160;&#160;&#160;SAPCCM_l_nu16be_get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_l_nu16get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_l_nu16le_get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_l_su16get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_ncu16get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_nu16be_get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_nu16get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_nu16le_get_tab<br />&#160;&#160;&#160;&#160;SAPCCM_su16get_tab</p>\r\n<ul>\r\n<li>rscpf_db, rscpf_a test tools:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New 'hs' and 'ho' commands so that the special commands do not make the normal help so unclear.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New 'be' command to enable you to enter long test data.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improvements to 'ya'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The file flag in the 'm' command now controls the creation of output files as well the reading of input files.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improvements to the layout of the output of the 't' and 'm' commands.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Other minor repairs not directly visible to the user</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The 'rscpsc7__store_in_cache_7' C function should only log something if the debugging mode was compiled.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A source analysis program detected three missing break commands in rscpc.c. They were inserted even though it has not yet been determined what errors might have been displayed earlier as a result.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some logging was deleted. (Rulebased__t1e__to__l_out__and_break and with T1 shortcuts)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Standalone programs now also use actual time stamps.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Unification of texts in logs. (rscpnmi__name_miss, rscpnf__name_fill, rscpnb__name_broken, rscpncc__name_CtrlCodeCv, rscpnoc__name_SAPOwnChCv and rscpnf1__name_useF1).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add Asian front-end code pages: 8004, 8304, 8404, 8505 and 8604.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add Hong Kong Chinese (HKSCS): 8340, 8341, 8344 and 8345.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>&nbsp;</td>\r\n<td>Use the 6.20 kernel.</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>Kernel patch</td>\r\n<td>543</td>\r\n</tr>\r\n<tr>\r\n<td>6.30</td>\r\n<td>&nbsp;</td>\r\n<td>October 18, 2002</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 19</strong></p>\r\n<ul>\r\n<li>Support for Hong Kong Chinese</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Details are contained in note 579747.<br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.10</td>\r\n<td>&nbsp;</td>\r\n<td>Use 6.20 kernel</td>\r\n</tr>\r\n<tr>\r\n<td>6.20</td>\r\n<td>Kernel patch</td>\r\n<td>589</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>&nbsp;</td>\r\n<td>January 01, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 20</strong></p>\r\n<ul>\r\n<li>F1 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add the separator (soft hyphen) to the '0100' code page.<br />See also note 563851, level 14.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>Kernel patch</td>\r\n<td>783</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>28</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 21</strong></p>\r\n<ul>\r\n<li>Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Death loop no longer in the hash processing in the R3load export.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>Kernel Patch</td>\r\n<td>827</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>lib_dbsl</td>\r\n<td>16</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>&nbsp;</td>\r\n<td>before May 19, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 22</strong></p>\r\n<ul>\r\n<li>HTTP names</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HTTP_names are also known without database access.<br />See also note 630503</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>itsWindows</td>\r\n<td>4</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>&nbsp;</td>\r\n<td>before October 6, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 23</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load now uses all words in the vocabulary.<br />(The problem only occurred during the Unicode export of an MDMP database.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load is twice as fast during the Unicode export of cluster tables.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The CCC converter can also convert between code page 4000 and Asian code pages.<br />(This function was omitted for Release 6.10, but is required in order to output PDF documents.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction of the conversion to and from code page 6200 (Asian Unification)</li>\r\n</ul>\r\n</ul>\r\n<p>Also see notes 633697, 633693, 633759<br />Repairs:<br />6.20 : in factory<br />6.40 : Repaired before June 20, 2003<br /><br /></p>\r\n<p><strong>Package 24</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>API was extended so that threads can use different languages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Acceleration of the GetCPInfo and rscpLengthOfBasicChar functions and simillar ones (as a result, the entire system is faster if you have to generate a lot of screens).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The \"old\" converter can be omitted if the program would otherwise be too large for the C optimizer (important for OS/2).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HTTP names and JAVA names of SAP code pages are now also in the F5 memory (see also package 22).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In many code pages, a separation was now made between SAP frame characters and SAP symbols on the one hand and Unicode characters that look alike on the other hand.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A special assignment of languages for code pages can be transferred using the UMGCPLANG table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improved error messages (with an RSCPE error description block)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Test utility programs</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The contents of the TCPDB table can be simulated even if the program does not log onto a database.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>988</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>22</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC SDK patch level</td>\r\n<td>9</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>&nbsp;</td>\r\n<td>before April 7, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 25</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Also filters unwanted control codes in the case of fast converters from UCS-2, UCS-4 and UTF-16 to UTF-8. (Otherwise, a UTF-8-GUI can suffer log errors due to poor data).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The length of SAP symbols (\"SAPding\") is also correctly determined in Unicode systems using the latest code pages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load for Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Languages that do not have any unique code page assignment can be listed in the UMGLNOCP table (for example, English in systems using SAP Unification, Asian Unification or SAP Diocletian).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F1 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add Diocletian front-end code pages: 6512, 6513, 6514, 6572, 6573 and 6574.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>987</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>21</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch level</td>\r\n<td>38</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC SDK patch level</td>\r\n<td>&#160;&#160;9</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>&nbsp;</td>\r\n<td>by July 17, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 26</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The CCC converter can also convert between code page 4000 and Asian code pages.<br />(This is an enhancement of package 23 especially for full width European characters in Japan (\"full-width Latin\")).</li>\r\n</ul>\r\n</ul>\r\n<p>See also note 633693.<br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1021</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>&nbsp;</td>\r\n<td>before August 12, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 27</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Unfortunately, package 24 generated an error, which is removed here. In the meantime, an interim step is required when you install a new system. . You must make an entry (\"1100\",\"1100\") in TCPDB before starting this type of kernel (if the TCPDB table is empty when you start the kernel, all logon attempts are rejected with a message indicating that code page 1100 is not released).</li>\r\n</ul>\r\n</ul>\r\n<p>See also note 653739.<br />Repairs:<br />6.20 : kernel patch level 1027<br />6.40 : Repaired before August 24, 2003<br /><br /></p>\r\n<p><strong>Package 28</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for converting characters from the private use area of UTF-16. Truncated characters from the private use area of UTF-16 were filtered during the conversion.<br />Depending on the configuration, the converter now generates an error or a replacement character.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for converting UCS-2 UCS-4 and UTF-16 to UTF-8. Control codes at the end of the converter input buffer caused an error message. Unwanted control codes are now filtered out correctly.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.20 :<br />6.40 :<br /><br /></p>\r\n<p><strong>Package 29</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CCC converter correction in rscpCConvertNeutral for UTF-16LE input data (you may observe errors if R3load on Intel or DEC hardware reads from a Unicode database).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If rscpCConvert is called with only one buffer, the error message is no longer written to the syslog but to the developer trace instead.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'rscpe_rstr_to_stderr' must not be a global variable (this is to avoid errors in shared libraries.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The new data type LINE_USING_PROC_WITH_CONTEXT is used (this remains hidden on the interface.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load-Export</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for NameTab export from a Unicode system.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Essential corrections have been made to the error messages and the warnings in the log files. This concerns exports from MDMP databases in particular.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:<br />6.20 : kernel patch level 1157<br />&#160;&#160;&#160;&#160;&#160;&#160;R3load patch level 29<br />6.40 : Kernel from October 30, 2003<br />&#160;&#160;&#160;&#160;&#160;&#160; R3load from October 30, 2003<br />7.00 : Before October 15, 2003<br /><br /></p>\r\n<p><strong>Package 30</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for ABAP list printing in Asian Unification systems. See also note 633697 level 2.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1200</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>&#160;&#160; 33</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC patch level</td>\r\n<td>&#160;&#160; 20</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;???</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>before November 14, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 31</strong></p>\r\n<ul>\r\n<li>R3load (Export with Unicode conversion)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load no longer terminates the export in the case of Unicode conversion problems that can be corrected using transaction SUMG. Instead of this, an order list is written for SUMG in XML format.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Screen sources can also be converted from a non-European non-Unicode system to Unicode.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction during the translation of characters with ambiguous targets. (Example: x'00A7' from 4102 to 6300. Code page 6300 contains both a West-European and a Japanese divide symbol).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New subcomponents for writing logs in XML format.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>With MC converter objects, the system no longer reports errors with many problems if a XML log could be written.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The system now issues warnings for words that were too short or too long for the vocabulary.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The converters for screen sources automatically fill gaps at the end with blank characters.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When determining module names, the error message functions are no longer irritated by the new Perforce date format.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&#160;&#160;R3load patch level</td>\r\n<td>&#160;&#160; 32</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>11</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>before November 15, 2003</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 32</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Adds the option of asking the CCC converter to use the F1 memory instead of the complete code page definitions.<br />(As a result, the behavior is the same as for non-Unicode RFC.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you enter a T1 shortcut into the cache and if the cache overflows, the system now reorganizes the cache (previously, the optimization was deactivated).</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1224</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>11</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>F1 memory:</td>\r\n<td>2002-03-01</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>November 11, 2003</td>\r\n<td>2003-11-11</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 33</strong></p>\r\n<ul>\r\n<li>RSCP library for the kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New ABAP calls:<br />UMG_WORD_SEP ====== rscpacWordSeparator<br />UMG_CP_FOR_TEXT === rscpacCodePageForText</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load export with Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Special processing for tables with ABAP sources.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Reads and uses the UMGSEP table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improvements to the generated XML files:<br />New: &lt;CAT&gt;<br />Comment instead of &lt;TYPE&gt; for key fields.<br />Comment instead of &lt;POOL_NAME&gt;<br />Comment instead of &lt;CLUSTER_NAME&gt;<br />Never '--' in a comment.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The number in the file name is increased to prevent existing XML files from being attached.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F2 memory (for TCP0C)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Preventing dumps on SUN.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Code page 1810 and area '4IL' added.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Code page 1810 added.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP symbols and SAP frame characters were separated from similar looking Unicode characters ('disunification').</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1311</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>&#160;&#160; 35</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Package 34</td>\r\n<td>1312</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>11</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>before January 06, 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 34</strong></p>\r\n<ul>\r\n<li>RSCP libraries</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New rscpCPFromHttp C function</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>rscpCPFromMS knows also MS-65001 == SAP-4110</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load export with Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for tables with long names in a pool with short name.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Do not write any control codes in key values in the XML file.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Do not write any control codes in comments in the XML file.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write lots of XML files so that no individual one is larger than 10 MB.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Use &lt;TAB_USED_CP&gt; if the code page refers to the complete table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some remote trace outputs.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ABAP interface for the CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If output strings are used and the F1 memory is being used, the output string is created with the same length as the input data. (For GUI_UPLOAD and GUI_DOWNLOAD)<br />See also note 695907.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ABAP interface for the Unicode migration</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CALL 'UMG_WORD_SEP' only returns words that contain at least one national character.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1336</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>36</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>11</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>before January 15 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Packet 35</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the cache converter is reorganized after an overflow, code pages that are not required are removed from the cache. A core dump could then be triggered by memcpyRChk under certain circumstances. The source code was changed in such a way that after a cache overflow, the function in which memcpyR was called will no longer run.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1319</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>21.01.2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 36</strong></p>\r\n<ul>\r\n<li>R3load export with Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Err.corr. because of truncated names of pooled tables.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't write control codes within values.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't write control codes within comments.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Split files, if they become too large.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write only a smaller part of the cache statistics.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write &lt;TAB_USED_CP&gt; and not &lt;USED_CP&gt; when a code page is used for a whole table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load is using a cache for UMGCCTL.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better XML data in case of errors with fields of type INT1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Output names of key fields in case in POOLs.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load never uses ACPR any longer. (Automatic code page recognition)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RscpGet8bitWord() no longer returns th word's position.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More and better comments at start and end of the XML-file.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Repeat table kind, when table is split over more than one XML-file.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improved layout of timestamps in XML-files.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better error message while Dynpro source is not finially corrected.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error corr. in resorting function for code pages within the Multi Code Page Converter Object.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Remove some traces</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ABAP interface to CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When output buffer is a string and when possibly the F1-memory is used, that string is started with a length exactly matching to the input buffer. (for GUI_UPLOAD, GUI_DOWNLOAD)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ABAP interface to Unicode migration</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CALL 'UMG_WORD_SEP', rscpacWordSeparator and nlsPickFirstWord return only words which have at least one character of interest. (currently: with 8th bit on.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Modules for ACPR and vocabulary lookup moved to other libraries.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Regenerate all dependency rules in makefile.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1336</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>&#160;&#160; 36</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans patch level</td>\r\n<td>1337</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>???</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>kernel patch level</td>\r\n<td>???</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>07.02.2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Paket 37</strong></p>\r\n<ul>\r\n<li>R3load-Export with Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Read three more tables: UMGSETTING, UMGSEPCP, UMGCOMCHAR .</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Common character set can now be larger than 7-bit-US-ASCII.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>UMGCCTL-GUESS == 1 is now an error and will not try to call ACPR any longer.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>UMGSTAT-MINWORDLEN is obsolte now. Look at UMGSETTING-MINWORDLN.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improvement of error handling for ABAP sources:</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Count lines of XML file.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correct nesting of XML in case of problems in pool tables.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Corr.: Never write to a non-open XML file.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Put sign at end of negative integer key field values.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't drop code pages from TCPDB any longer. Even if it seems to be not used by any language.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Reorganize modules to separate vocabulary functions from ACPR from testing functions</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some smaller corrections in vacabulary functions for special cases (depending on order of the languages used.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Language dependent Dynpro-migration non-Unicode &lt;-&gt; Unicode</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Create error message with key value, when the source language of dynpro sources is not known.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC code page converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction: Release semaphore, after a a F1 mamping table has been found. ( Error in BIN since 07.03.2002 and in 6.20&#160;&#160; since 13.11.2003.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The dump function for converter objects outputs the last table name and the header of the vocabulary also.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CALL 'UMG_CP_FOR_TEXT' not only calls the vocabulary function, but now it tests what the mlti code page converter in the R3load would do.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow CALL 'UMG_WORD_SEP' to flush caches.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel&#160;&#160;patch level</td>\r\n<td>1406</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load&#160;&#160;patch level</td>\r\n<td>&#160;&#160; 38</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans patch level</td>\r\n<td>1405</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel&#160;&#160;patch level</td>\r\n<td>&#160;&#160; 13</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load&#160;&#160;patch level</td>\r\n<td>&#160;&#160;&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans patch level</td>\r\n<td>&#160;&#160; 13</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc&#160;&#160;patch level</td>\r\n<td>&#160;&#160;&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>12.03.2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 38</strong></p>\r\n<ul>\r\n<li>CCC Codepage Converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Gets a new functionality which can be used to filter out characters during codepage conversion.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add code pages 1810, 1824, 1614, 6180</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update the unambiguous blended code pages: undefined bytes are mapped to distinct code points in the private use area.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F1 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add code pages 1810, 1824</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel&#160;&#160;patch level</td>\r\n<td>1422</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load&#160;&#160;patch level</td>\r\n<td>41</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC&#160;&#160;&#160;&#160; patch level</td>\r\n<td>26</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc&#160;&#160;patch level</td>\r\n<td>55</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel&#160;&#160;patch level</td>\r\n<td>&#160;&#160;13</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load&#160;&#160;patch level</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans patch level</td>\r\n<td>&#160;&#160;13</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>19.03.2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 39</strong></p>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Remove segment CTHB0 from codepage 1800. RTL/LTR control marks are no longer filtered out, but are replaced by '#' symbols.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1422</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load patch level</td>\r\n<td>41</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC&#160;&#160;&#160;&#160;patch level</td>\r\n<td>26</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch level</td>\r\n<td>55</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>14</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 40</strong></p>\r\n<ul>\r\n<li>R3load - Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If a following word has a different code page assigned to than the first word, this can be ignored, if all characters of that following word are members of the common character set of these two code pages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add a cache for all rows of table UMGCOMCHAR.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In a Unicode system I don't need UMGLANGCP nor UMGLNOCP and I will not read them.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If there is at least one row in UMGLANGCP, then only UMGLANGCP and UMGLNOCP shall be looked at (and no longer TCP0C/0D/DB).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some important switches like 'rscp/on_error' can be set thru environment also. Reason: R3load does not read nomal profile files.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write all warnings, which meight happen during DYNPRO conversion, into XML file now.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Illegal language keys are now reported also, when happening in pooled tables.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>All problems about choosing the right code page for conversion create entries into the corresponding XML files. But now most problems no long write duplicate information into R3load's LOG files. Now there LOG files look like:<br />(EXP) TABLE: \"UMG_TEST_4\"<br />(EXP) TABLE: \"UMG_TEST_5\"<br />(EXP) TABLE: \"UMG_TEST_6\"<br />(EXP) TABLE: \"UMG_TEST_6B\"<br />(EXP) Look also at 170 lines in XML file<br />(EXP) TABLE: \"UMG_TEST_7\"<br />(EXP) Look also at 123 lines in XML file<br />(EXP) TABLE: \"UMG_TEST_8\"</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>C-CALLs UMG_WORD_SEP and UMG_CP_FOR_TEXT: Some parts of the conversion software of R3load is also in the kernel to enable ABAP programs&#160;&#160;to see in advance, what R3load would do.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel</td>\r\n<td>1484</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;46</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 4</td>\r\n</tr>\r\n<tr>\r\n<td>6.40 NT</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;19</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 3</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;19</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>2004-04-22</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 41</strong></p>\r\n<ul>\r\n<li>R3load - Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the R3load repair log (the XML files) contain a problem report for a whole table (&lt;TAB_USED_CP&gt;), then we don't want a report for the first record of that table also.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>R3load</td>\r\n<td>46 (!)</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>Kerenl</td>\r\n<td>18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>4</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>2004-04-23</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 42</strong></p>\r\n<ul>\r\n<li>Kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>UTF-16 contains low-surrogates between U+DC00 and U+DFFF</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel</td>\r\n<td>currently not planned</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>2004-04-05</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 43</strong></p>\r\n<ul>\r\n<li>CCC converter and ABAP interface</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A new option was introduced to control the character filter. Depending on this option, characters with the filter flag are treated as non-convertable characters, are replaced by spaces, or are removed.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction and better error handling in conversions between 6300, 8000, 8100, 8200, 8210, 8211, 8220</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Increased ranges for multi-byte characters in case of direct conversion from one Asian code page into an other related Asian code page. (This is effective only in some cases, because most often the conversion is done with the help of Unicode.)<br />See also note 745721.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1540</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>2004-05-23</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 44</strong></p>\r\n<ul>\r\n<li>General database interface</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Step 1: Data types and constants to enable the distinction between ASCII-based databases and UTF-8 databases for external tools.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1542</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;21</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>June 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 45</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Support the input of JIS. See also note 743764.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Split broken_stop into broken_stop_finally and broken_stop_restartable</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Err. corr. for the combination of input buffer == output buffer and some of the fast conversion methods.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Never use the F1-memory when handling UTF-8.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction of 8200 (JIS)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Codepage utilities</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add rscpUnderstandCodePageNumber</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1541</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>June 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 46</strong></p>\r\n<ul>\r\n<li>CCC</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add JIS (and ISO-2022) shifted single byte codes on input side.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Avoid dump. Don't search in an empty UMGCOMCHAR cache. (Visible in R3load)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update code pages Update 1800, 1824, 82xx</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP in kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>C-CALL can give details of ISO-2022 rules. Visible in transaction SCP.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1588</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;50</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160;69</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;12</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>July 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 47</strong></p>\r\n<ul>\r\n<li>F1-memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't tell IMPORT, that you will convert, if you would not do yourself. (There shall be no code page conversion errors, when IMPORT in a 1810-system reads data, which has been written on an 1100-system.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.20</td>\r\n<td>kernel patch level</td>\r\n<td>1575</td>\r\n</tr>\r\n<tr>\r\n<td>6.40</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;12</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>&nbsp;</td>\r\n<td>July 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 48</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the converter is tuned to substitute non-convertable characters by '#' and invalid JIS data (codepage 8200) are converted to Unicode, then the remaing part of the converter output consists of '#' characters.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1587</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>July 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 49</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Introduce runtime rules to handle ranges. That reduces the shared memory consumption of 8401 on the input side from 100 MB to 1.3 MB.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Introduce runtime rules to handle ranges. That reduces the shared memory consumption of 8401 on the output side from 14 MB to 4.5MB.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow rules with (small) attribute values not only in T3 entries but also in T2 entries. That further reduces the shared memory consumption of 8401 on the output side from 4.5 MB to 0.2 MB.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5-memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction in 8401 (GB-18030)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP in kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Call CUR_LCL: ASPLANGS may be a string also.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More selection criteria, when putting the contents of a rule into an internal table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Can display contents of ISO-2022 rules.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Know the corresponding multi-byte code page also for languages, which are not active. (Fixes a problem with IMPORT FROM DATABASE in Unicode systems.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1640</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160; 76</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160; 33</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160;&#160;&#160;9</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>August 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 50</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Codepage converter supports two escape sequences to switch back from multibyte mode to ASCII, i.e. ESC ( B and ESC ( J.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction in conversion from Asian multibyte&#160;&#160;codepages to JIS, i.e. 8000, 6300, 8100 -&gt; 8200.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160; 1622</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160; 33</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>August 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 51</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add function for CCC cache self checking. (In rscpf_...ccf use 'T'.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CCC statistics: Make percentage_bar() immune agains bad data.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Typing error in rscp__IterUTF8_4Next: (some unused characters of UTF-8 handled incorrectly)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>sapiconv</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>First roll-out. See also note 752859.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1663</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160;77</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>use 6.40</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;35</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc patch</td>\r\n<td>&#160;&#160; 9</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv patch</td>\r\n<td>&#160;&#160; 1</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>September 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 52</strong></p>\r\n<ul>\r\n<li>R3trans</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Look into UMGLANGCP and UMGLNOCP only if export file code page is Unicode or if UMGSETTING tells me to do so.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Look into UMGLANGCP and UMGLNOCP only if export file code page is Unicode or if UMGSETTING tells me to do so.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Only when input is ASCII and not a enforce single code page, then the Multi-Converter and rscpsumg shall be activated.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The UMGSETTING cache and the UMGCCTL cache are not loaded, when the executable is a Unicode-Executable.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Handle code apge 1810 like 1800. This means, that Walldorf exports, which had been done using a 1100-system are imported into an 1810-system without a real code page conversion.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Test drivers</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow to switch errtrace (\"rscp/on_error\").</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Implement 'exec' command.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhance 'e' command: checks for RFC Lang/CP-List handling.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add switch, whether memory shall be protected. With profile variable \"rscp/ccc/protect\".</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Publish rscpep2_line_using_proc_wrapper.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Byte counters are 64 bit long now. Don't write noof_inbytes and noof_outbytes unprotected into shared memory any longer.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Even \"strong\" rules are less stong than \"strong\" characters from a mapping segment.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Call rscpqc_quality_check() after each reorg.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5-memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update code page 8200</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1663</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>1662</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;55</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;40</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;38</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;15</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>September 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 53</strong></p>\r\n<ul>\r\n<li>R3load and rscpMC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Vocabulary word's lengths shall be &lt;= 30 and not &lt; 30.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If vocabulary look-up ended with good result but with warning, the database row is reported. But SUMG needs also the code page, which was used.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Find words also if only the first character is a national one.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't warn too short international words, even when they follow a too short national word.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for cases for more than one problem while writing a single line of XML.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add R3LOAD_TRACE. (Active, only if software is recompiled with this switch on.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>rscpf test drivers</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add new variable \"show_time\".</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add new commands 'br0' .. 'br9' .</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Command 'bro' is like old 'br'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Prefix of command name can be 'rscp_a_' and 'rscp_db_' also.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A lonely command line parameter will be used as command file also without '-i'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>sapiconv</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP's propietary characters are not searched, as long as you don't use the new option '-s' . .</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow more than 8 KB on stdin.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC converter (internal enhancements)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add rscpCInit11WithDiag, rscpCActivateWithDiag.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>rscpa29_create_CObj() with new param ERRMSGS</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Withdraw some functions from public eyes.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;56</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;17</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>October 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 54</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Fix the propagation of the vocabulary look-up result from a cluster row into the common fields of the next cluster row.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load, R3load test kernel calls</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correct code page used in XML file in case of problems in clusters.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction with cache of UMGCCTL.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Modified handling of rejected code page switching within one row.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1707</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;57</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;21</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>October 2004, 96</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 55</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Export of cluster with conversion: In case of conversion problems key, which are written to the XML file are no longer written into the R3load log also.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Export of pools with conversion: In case of conversion problems key, which are written to the XML file are no longer written into the R3load log also.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;61</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;21</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>October 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 56</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Put a write protection over the shared memory segment (\"CCCC\") as long as no updates are really neccessary. This depends on profile parameter values and other switches. This may not be active on every platform. See note 788567 for details.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Also count converted bytes, when one of the fast converters have been used.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't destroy byte counters when writing substitution characters.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow DIAGRAPH to catch control bytes (with rscpCSwitchMiss). See also note 785959.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1728</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;45</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>November 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 57</strong></p>\r\n<ul>\r\n<li>CCC converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add semaphore locking to rscpCConvBaseChar and rscpCConvOneChar. Loading code pages into CCC cache will proceed with the correct access permission.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<li>Repairs:</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1745</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>45</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>November 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 58</strong></p>\r\n<ul>\r\n<li>R3load - Export from Unicode DB</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If \"-dbcodepage\" not given and TCPDB is empty, it picks correct 4102 or 4103 itself.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load - Import into Unicode DB</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If \"-dbcodepage auto\" given and TCPDB is empty, it picks correct 4102 or 4103 itself.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If DB code page given or derived and it is not the expected 4102 or 4103, then output warning.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>R3load - Export with Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors in transparent fields of a table cluster shall not reported for the table cluster itself but for each of the clustered tables.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Look at each physical line of a table cluster separtely, even when TABCAT is 3.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Also the the GUESS value of a table cluster does not say details about the embeded logical tables.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Do not popagate \"too short\" error into next row of a pooled table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CCC Converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Recover from a CCC cache overfolw, where only the T07-area had been flooded.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>rscpeo4__enter_out_4code sets error info now.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Replace rscpCSwitchMiss with rscpCPreSetSubstU4.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some internal enhancements<br />Add field 'latin1only'.<br />Pick the right sap5lib.<br />Even \"strong\" rules are less stong than \"strong\" characters from a mapping segment.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP general</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Accept additional fields in UMGPMIG.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Accept additional fields in UMGSETTING.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update dummy module rscpexdu.c</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1764</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;64</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;50</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;23</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>Dec. 1st, 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 59</strong></p>\r\n<ul>\r\n<li>R3load - Export from Unicode DB</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancement in calculation of length of logical record within a table cluster item.</li>\r\n</ul>\r\n</ul>\r\n<p>Some more details in internal note 808081.<br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>Jan. 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 60</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Warning message in case of strange -dbcodepage&#160;&#160;values.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>command line option -dbcodepage is stronger than contents of table UMGSETTING</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP &amp; R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow table UMGPMIG with 24 or 25 fields</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Function rscpMCInit needs to know whether the UMG*-tables shall be read or not.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New NLSMAJOR number '25' for GB-2312-80-EUC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F1 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Make separtion: GBK vs. GB-2312-80-EUC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New code page 8450 (GB-2312-80-EUC)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HTTP names and Java names for 8x04.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some internal enhancements<br />More details checked in test scripts for SAP-Unification printer code pages.<br />Test driver output line numbers.<br />Three printf-formats enhanced, which have been found by new ccQ.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel patch level</td>\r\n<td>1800</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;67</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel patch level</td>\r\n<td>&#160;&#160;56</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;26</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>Jan.12th, 2004</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 61</strong></p>\r\n<ul>\r\n<li>ABAP Engine</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>EXPORT TO ...</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The result of an EXPORT TO DATABASE ( or to somewhere else ) is a compressed data record together with a transport header. This transport header contains a code page attribute. Normally the currently active code page is copied into that header. When there is a profile entry<br />i18n/export_to/code_page_attr = migrate<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;then the currently active language is taken and the corresonding code page is taken from UMGLANGCP. By that it is possible to write values which do not create problems because of ambiguous code page numbers.</p>\r\n<ul>\r\n<ul>\r\n<li>IMPORT FROM ... of tables with language key in a Unicode system when the data is still non-Unicode</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For each row the language key is used to find the corresponding byte-oriented code page ( rscpGetMBCodePageForLanguage). When that fails, the code page number from the transport header is used.</p>\r\n<ul>\r\n<ul>\r\n<li>IMPORT FROM ... (other cases)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The code page attribute in the transport header is compared to the current system code page and if a code page conversion is neccessary, that code page number is used.</p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New function rscpGetCPForImportFrom to support ABAP engine.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The list of code page mappings is now cached in shared memory.</li>\r\n</ul>\r\n</ul>\r\n<p>Some more details in internal note xx.<br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>1797</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;56</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>&nbsp;</td>\r\n<td>Jan. 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 62</strong></p>\r\n<ul>\r\n<li>DIAG codepage converter</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Missing spauptr addresses cause core dumps after ending an ABAP session.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If an unsupported character is fed into a Dynpro field, the field is filled up with spaces. Wrong substitution (on_miss) switches in the DIAG converter are responsible for this behaviour.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Err. correction in usage of new converter API in DIAG. Reset input/ouput buffer before reusing&#160;&#160;rscpCConvert... .</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>1859</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;63</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160; 1</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Mar. 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 63</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add functions to find best code page for communication with a browser.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Split&#160;&#160;SAPCCNLS_simplCh into SAPCCNLS_2312_80_Ch and SAPCCNLS_GBK_Ch.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>(only 6.40 and higher:) TCP02, TCP03 and TCP07 are no longer used. Because of that: F4-memory no longer used.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP test dirvers</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add command \"TABinfo\".</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP (for R3load export)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add field EINDX to UMGSETTING.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add UMGSETTINGS.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add UMGCONTAINER and the container handling.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>1891</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;72</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;69</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Mar. 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 64</strong></p>\r\n<ul>\r\n<li>RSCP (for R3load export)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better reaction in case of missing table UMGSETTINGS.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If an alternate NameTab can be simulated, create a soft error only.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better syslog message and better error messages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhance container_info_t, add container_infos_t.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhance bufferPair_t.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Remove all container cache row numbers from UMGCCTL cache again. StartTab is too late for the cluster modules because of non-text fields. But then I don't need any linkage between UMGCCTL and UMGCONTAINER any longer.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Move the re-mapping from field counters to buffer counters also from CLU to rscpexmc.c.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write less into the XML repair log, when a container's type could not be recognized.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP test driver</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Corr.:&#160;&#160;set proc = rscpCShortcutTableConv</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>1891</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;72</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;69</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>April 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 65</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Recreate spaces in rules for JIS, which are surpressed in F5-memory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enable JIS -&gt; UTF-8.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP (for R3load export)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In release 6.20 and 6.40 the mechanism for containers is only active, when the process's environment contains: R3LOAD_USE_CONTAINER=1 .</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>SAPLIB</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add sapi64 to sap0lib.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>UXPORT</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Build uxprtrlib.o also.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>1891</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;72</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>1890</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160;86</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;69</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;69</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160;21</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>May 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 66</strong></p>\r\n<ul>\r\n<li>R3load export</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Continue on missing table UMGCCTL. (So R3load can read 4.6C databases.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>...</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>April 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 67</strong></p>\r\n<ul>\r\n<li>RSCP F5-memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update attributes of code page 1610</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update some code page names</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Handle missing UMGCCTL table when using 6.20 R3load at a 4.6-database.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>1933</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;74</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>1932</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;77</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;37</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;77</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>May 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 68</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Intorduce six table groups and allow to switch on and off access to each group individually. The first user is R3trans for following scenario: A upgrade, where the start release is &lt;= 4.6D and the target release is &gt;= 6.20 and where the upgrade includes transports, which came from a Unicode system originally. In such a case texts from non-Latin-1 languages could be damaged. (That could happen with IS-OIL.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Create new 'L'-objects for fast&#160;&#160; lengths informations. (The first user will be SAPGUI for JAVA environment.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>1933</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;74</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>1932</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;77</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;37</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;77</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>May 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 69</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Since package 56 it is possible to put a write protection on the shared memory cache and open it only while the special semaphore is hold. Now the write-permission has been de-coupled form the semaphore. The semaphore is requested when atomic operations on the cache are started. As opening and closing the shared memory is on some platforms is expensive, the write-permission is only requested when updates to the shared memory are neccessary.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>(not planned)</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;77</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;36</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;77</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;18</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>May 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 70</strong></p>\r\n<ul>\r\n<li>RSCP for kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Statistics was too long when USAG2 and MBCPM and RFCMA and USAGE are space.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load does not require environment variable R3LOAD_USE_CONTAINER any longer.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Modify the interface to krn/dbcnv/ in a way that less details are visible outside of RSCP.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>rscpGetPossibleLangListLen and rscpGetPossibleLangList and functions like rscpLangCPListGetSize survives, even when rscpi_init was not called.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Test drivers now call rscpi_init later to enable testing of hooks.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>kernel</td>\r\n<td>out of maintainance</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;83</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;39</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;20</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 1</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Jul 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 71</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Dynamically increase size of cache for UMGCCTL. Until now the limit was 45.000 .</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If there is a vocabulary problem with words from the transparent fields of a table cluster, the code page chosen to convert those fields are used for all rows of all clustered tables, that share this key.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When replacing doubble-hypen in XML files, use underscore and no longer sybilliac hyphen. That one did not display nicely in vi, more, InternetExplorer,..</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When doing R3load-Import for Unicode with byte-swapping, don't look into UMGCONTAINER and don't cry about NameTabs not yet imported. Both is not needed for byte-swapping.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>&nbsp;</td>\r\n<td>out of maintainance</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;39</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;20</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 1</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Jul 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 72</strong></p>\r\n<ul>\r\n<li>RSCP for Kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In a Unicode system the locales of the C-libraries are not used since a while. So they should not be checked during Roll-In.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>&nbsp;</td>\r\n<td>out of maintainance</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;89</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3trans</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;22</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Jul 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 73</strong></p>\r\n<ul>\r\n<li>RSCP for Kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add dummy function to trigger export of rscpllen.c module functions.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>620</td>\r\n<td>&nbsp;</td>\r\n<td>out of maintainance</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;84</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Jul 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 74</strong></p>\r\n<ul>\r\n<li>RSCP for Kernel</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Implementation of a new internal converter state \"reinitialize\". This state defines a converter with a temporarily limited functinality which is expected to be restored at some future point in time. The converter will switch into the new \"reinitialize\" state if the work process is in reconnect state and</p>\r\n<ul>\r\n<ul>\r\n<li>the converter cache is build up first time</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>the converter tries to&#160;&#160;reorganize the converter cache</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>the converter tries to load a new codepage from database</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In that phase, any conversion call using rscpCConvert with an invalid converter object will fail. If the work process could successfully reconnect to the database the next initialization attempt via rscpCInit will reinitialize the converter. database</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>Repairs:</td>\r\n</tr>\r\n<tr>\r\n<td>620</td>\r\n<td>&nbsp;</td>\r\n<td>out of maintainance</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>86</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>24</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Jul 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 75</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some versions of R3load aborted in the middle of their job while exporting the table EDI40, when it contained more than 30 different document types. See note 872124 how to handle, when R3load patch is not available.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<li>In case of containers some of the NameTab checking is done, which will be done for other kinds of tables with package 76.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>Repairs:</td>\r\n</tr>\r\n<tr>\r\n<td>620</td>\r\n<td>&nbsp;</td>\r\n<td>out of maintainance</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;42</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 3</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Aug 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 76</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add mechanism which check that the alternate NameTab is not older than the normal NameTab at time of database export. As the NameTabs are switched during import, the originally label \"alternate NameTab\" becomes active and is vital for the system.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<li>If you have good reasons to enforce an export even when these checks fail, then set an environment variable:<br />setenv I18N_NAMETAB_TIMESTAMPS IGNORE<br />setenv I18N_NAMETAB_TIMESTAMPS LOG</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>Repairs:</td>\r\n</tr>\r\n<tr>\r\n<td>620</td>\r\n<td>&nbsp;</td>\r\n<td>out of maintainance</td>\r\n</tr>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Aug 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 77</strong></p>\r\n<ul>\r\n<li>RSCP for kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A new mechanism was added to enforce automatic frontend codepage detection via TCP0F on the backend side. This behaviour can be controlled by the TCP0I entry \"utf8gui\". If utf8gui is set to automatic the support bit option /SUPPORTBIT_OFF=AUTO_CODEPAGE will be ignored on backend side. Furthermore, if the system is Unicode and GUI is able to use UTF-8 the frontend codepage is set to UTF-8 (4110) independent of any codepage setting in SAPLogon.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;24</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>Aug 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 78</strong></p>\r\n<ul>\r\n<li>F1 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add code pages 8341 and 8345</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160;31</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160; 5</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Aug 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 79</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Be able to handle problems with unrecognizable structure names for containers also during conversion from single code page to Unicode. (See also note 876984.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;42</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 3</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Aug 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 80</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Re-do handling of containers, which cannot be subdivided.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Environment variable I18N_CONTAINER_NO_STRUCT says, if number of problems is reported only or if each case is reported in Log.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Correction to make sure, that no information about containers within one cluster is reused when other clusters are read. (See also note 889513.)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Into R3load Repair Log only comments are written.</p>\r\n<ul>\r\n<ul>\r\n<li>Look at DDNTT and DDNTT_CONV_UC and complain about mismatches.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Environment variable I18N_NAMETAB_TIMESTAMPS says, if error aborts R3load or if problems are reported only or if no checks are done at all.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Don't kill export because of NameTab read error, when NameTab is read for logging purposes only.</p>\r\n<ul>\r\n<ul>\r\n<li>Check NameTab entries also for each pooled table, which is seen while exporting a table pool.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP for kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Call 'CUR_LCL' can handle paramter 'UUSEMBCP' also when it has type 'N'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP internaly</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New API: \"rscpDA\" for dynamic arrays in main memory.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;45</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>kernel</td>\r\n<td>100</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 5</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Sept 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 81</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Overhaul the commiting and the releasing of memory in the CCC cache. (There had been instabilities, when a workprocess was used without a running database.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Back from '# ' to '##'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add rscp/mbc_copy = 3 . But it shall not be used for productive systems.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction in optimzation of rscpCConvertNeutral.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't try to write statistics from a cache, which does not exist.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>sapiconv</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New option '-C' to replace an Asian double-byte character with '##'. The old option '-c' uses always '#'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Special character \"DBC replacement\" into many Unicode code pages</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Plain code page 6200 has SJIS level 1 now. (Normally 62x0 is used.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>101</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>use 7.00 non-Unicode</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;47</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;32</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160; 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>use non-Unicode</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 6</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Nov 2005</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 82</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When converting from 8000,8300,8400.. to 6200 the source code page shall be stronger than the user's language.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A couple of new features for the test drivers rscpf_a and rscpf_db.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP F5 memory</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add code page 6101. (It is used as fall back code pages for Englisch users of code page 6200.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>101</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160;36</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160; 4</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>or use 7.00 non-Unicode</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;33</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160; 1</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>use non-Unicode</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>&#160;&#160; 9</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Nov 2005</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 83</strong></p>\r\n<ul>\r\n<li>RSCP for R3load-export</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New algorithm also in SPUMG: If a word is too long, we take the leading part only. Silently. The database row is no longer reported into the R3load Repair Log, as we think, that it will not happen that we change our mind about the language assigment, if we see also the bytes behind the first 30 bytes.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If a row contains words, which are too long or too short, SPUMG writes the global fallback code page into the reprocess log. Older R3loads tried to find \"the best\" code page. Now R3load uses the table-specific fallback code page. As the table-specific fallback code page is normally the same as the global fallback code page, this makes behaviour of SUMG less confusing.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Restructure collision resolution: Old: Two levels: 1st find common code page for all words of a field. 2nd find common answer for all fields. (may rescan whole fields) New: 1st collect all words from all fields and find a coomon code page for all words which are visible to an rscpMCConvert..M call.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction for case, that UMGLANGCP contains additional languages. (Could core dump on AIX.) Details in note 913996.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Read UMGSETTINGS[COLLRES].</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Be more precise when code pages does not match, but the characters are from common set.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If during export of a cluster the transparent part has a vocabulary on its own, then use it.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Handle unlimited number of entries in UMGCONTAINER.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Raise MAX_PARALLEL_CODE_PAGES from 15 to 30.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Drop code of 'ACPR' package. This shall not have any visible effect.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Remove limitation on number of words per database row.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More control of checks on NameTabs thru environment variables<br />I18N_NAMETAB_TIMESTAMPS<br />I18N_NAMETAB_NORM_ALLOW<br />I18N_NAMETAB_NORM_LOG<br />I18N_NAMETAB_ALT_ALLOW<br />I18N_NAMETAB_ALT_LOG<br />I18N_NAMETAB_OLD_ALLOW<br />I18N_NAMETAB_OLD_LOG</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements on warnings and error messages:<br />Don't talk about Unicode and UMG*-tables, when R3load is used for a different purpose.<br />Don't cry about RADCUCNT too often.<br />Don't talk about UMGSETTING, when UMGSETTINGS was used.<br />Don't talk about internal problems of the start-up of rscpMC, when the activation had a final success.<br />Don't output the RSCP error description block for cluster problems, when I think, that I have written enough details already.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write each common charcter set to R3load Repair Log when used for the first time.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't read UMGSEPCP and UMGCOMCHAR, when UMGSETTING or UMGSETTINGS have told me, that I shall use 7-bit range as common character set.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>No longer two syslog messages about language '' and code page 0000 during startup.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP error handling</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Mark each error description block with an individual number.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>113</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;56</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;45</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160; 9</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Jan 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 84</strong></p>\r\n<ul>\r\n<li>RSCP for kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal CCC state reinitialize: Transition only in case of failed reorg attempts.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Versioning of internal work process cache in GetCPInfo.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>113</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;53</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Jan 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 85</strong></p>\r\n<ul>\r\n<li>RSCP for kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New function function rscpUcnSubstBrokenDBC to be used to circumvent an error in Internet Explorer.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See also note 720738,</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>114</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160; ?</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Feb 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 86</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Also low level functions like conversion between UTF-16 and UTF-8 get connection to the RSCP error reporting mechanisms. That allows for example better error messages within RFC.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>120</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>58</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>53</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>Feb 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 87</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Handle packed-decimal fields, which are hidden within containers. See also note 936117.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;*** This correction is not good. Please don't use R3loads which have package 87 but don't have package 91. ***</p>\r\n<ul>\r\n<ul>\r\n<li>(7.00 only) Modify DbSl statement for NameTab checks to cooperate with ORACLE-version of 7.00 DbSl. See also note 935971.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP for RFC with UTF-8</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Look also at GUI's code page when trying to resolve an ambiguous conversion 6200 &lt;--&gt; 4110.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>122</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>(59)</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>53</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>(14)</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>March 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 88</strong></p>\r\n<ul>\r\n<li>RSCP and IMPORT FROM</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Since many, many years the ABAP statements of the IMPORT FROM family knows about MDMP and don't convert between different ASCII based system code pages. By that a user can read data independent of the logon language of the user, who has written it. (This is needed only for special cases, as normally there should be no national characters in data without a language field.) With this patch the ABAP statements of the IMPORT FROM family handle within a MDMP setup also those code pages like system code pages, which are stored as global fall back code page in UMGSETTING or in UMGSETTINGS or which are attached to languages in UMGLANGCP.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>122</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>59</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>March 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 89</strong></p>\r\n<ul>\r\n<li>R3load export with Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When R3load exports the NameTabs, it also checks that the alternate NameTab is up to date. The SELECT statements used for that must be different depending on DBMS. See also note 936533.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs: See package 91<br /><br /></p>\r\n<p><strong>Package 90</strong></p>\r\n<ul>\r\n<li>RSCP in kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>During each creation of the CCC cache the kernel checks, if table TCP00A seems to have an reasonable content. If it in doubt, it writes a Syslog message CP6 pointing to note 938736.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>At the same time it checks, whether one of the system code pages is a blended code page. If so, it writes a Syslog message CP6 pointing to note 938737 ot to SAP note 938738.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>129</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>59</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>April 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 91</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for package 87. On some platforms in some cases some fields of clustered tables have not been convertered. See also note 936117.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The time-stamp check for the alternate NameTab now looks at CREATE-time-stamp and no longer at ABAP-time-stamp. When preparation of Unicode conversion is started during an Upgrade, the old rules don't hold any longer.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The existence check for the alternate NameTab executed very slow on DB2/390. Now you can set a new environment variable to switch to a different SELECT statement:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I18N_NAMETAB_SELECT_TYPE = A or B<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A is fast on most DBMS<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;B is much faster DB2/390<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;See also note 936533.</p>\r\n<ul>\r\n<li>RSCP for some smaller executables</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't need to like rslg0lib nor rstr0lib any longer.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>129</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>62</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>59</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>16</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>May 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 92</strong></p>\r\n<ul>\r\n<li>RSCP for kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Legacy code page mechanism for SNC library. The legacy code page parameter is associated with the system code page used by the-Unicode system in the pre-conversion era. This mechanism is a workaround for SNC to overcome the problems caused by the fact that current external SNC products are not Unicode enabled. There are two modes to set this parameter.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TCP0I parameter:&#160;&#160;&#160;&#160;&#160;&#160; legacy_cp<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Environment variable:&#160;&#160;SNC_LEGACY_SAP_CP<br /><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>129</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>59</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>&nbsp;</td>\r\n<td>May 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 93</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Convert all fields of a row of a pooled table within one conversion call.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Implement handling of containers for pooled tables.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Convert all fields of a row of a transparent table within one conversion call, with the exception of LOBs.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Implement handling of containers for transparent tables, as long as they don't contain LOBs.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>(Separte distinct APIs into different header files)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Cut rscpMC-API out of rscp.h and put it into a separate header file: \"rscpmc.h\".<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Cut rscpMC-API out of rscpi.h and put it into a separate header file: \"rscpmci.h\".<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Cut rscpsumg-API out of rscp.h and put it into a separate header file: \"rscpsumg.h\".</p>\r\n<ul>\r\n<ul>\r\n<li>New API: rscpdabp<br />Dynamic arrays of buffer pairs.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add rscpncsf__name_charsize_factor.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>145</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>69</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>76</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>23</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>July 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 94</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Since 05.05.2006 R3ld/R3load/R3ldexp.c looks at the CRTIMESTMP and no longer at ABTIMESTMP. krn/rscp and krn/dbcnv shall do the same.<br />See also note 978244.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some code clean up.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More precise syslog messages in case of damaged code pages.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>149</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>71</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>79</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>26</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>Sept 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 95</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't die on container structures, which have no sub-fields.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't cry about strange language fields, when a single code page system is exported. This is activated, when the table UMGSETTINGS contains a row<br />'SINGLECP' , 'X'</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Solve problem of huge memory consumption and long runtime, when exporting a pool, where a large (logical) table does not contain any national characters.<br />See also note 988224.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add a couple of new statistics.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>76</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>29</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>Nov 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 96</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add a cache for NameTab read misses. Searching again and again was too expensive.<br />See also note 991831.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>79</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>30</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>Nov 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 97</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction for special case:<br />R3load export with Unicode conversion<br />and active container handling<br />and the current container has binary fields<br />and the last field is binary and has an odd length<br />and that last field has also an odd offset, because it is behind an other binary field with an odd length.<br />Without the correction R3load stops with an error message:<br />code:&#160;&#160;512&#160;&#160;RSCPEUNIQ<br />spaces don't fit into rest<br />module: rscpc&#160;&#160;&#160;&#160;no:&#160;&#160;234</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>34</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>Nov 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 98</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction in error messages in case of conversion problems of LOBs.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction when writing key values of logical cluster tables to Repair Log in a Unicode executable. (function was not yet used)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When container handling is used and when containers have binary fields, then do not try to lookup binary values in the vocabulary.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When container handling is used and when containers have binary fields and when the transport file code page is UTF-16LE, then binary fields must be exported byte-swaped</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't loose memory, when a alterante NameTab is missing and cannot be simulated either, because the strucure has binary fields.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Flush the R3load Repair Log whenever a (physical) table is finished. By that the XML files are better even when R3load is canceled.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Statistics talks also about DBMCPL_m_defCP and DBMCPL_n_defCP.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>34</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>Nov 2006</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 99</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Output converted key value to XML file instead of input key value when container handling happened on a key field.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>During start-up make a distinction between real warnings and information messages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add rscpCDump2ndObject to make comment at end of XML file shorter and clearer.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>39</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>Feb 2007</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 100</strong></p>\r\n<ul>\r\n<li>R3load and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Do not write any text about RSCP-Error into LOG file, when error was not detected within the RSCP library.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better selection of what to write inot the LOG file when an error is detected within the RSCP library.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This change omits possibly misleading error information, adds error messages close to the point of error. Existing error tests were left unchanged in order to preserve the validity of references in documentation, trouble shooting guides, Notes, CSN messages.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Before this change an \"RSCP Error\" box with messages was written on every R3load error exit; this error box is omitted and replaced by messages at, or moved to the error location. Set environment variable R3LDCPCV_CON_ERR to get the original error box at its original location.<br /><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>39</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>Feb 2007</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 101</strong></p>\r\n<ul>\r\n<li>R3load and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Support releasemix scenarios: Usage of R3load 6.40 against databases with SAP release 4.6d, 4.5b, 4.0b, 3.1i. In this special case code pages and code page information is not taken from the database because tables like TCPSEG do not exist in those old databases.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>(not planed)</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>(not planed)</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>(not planed)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 102</strong></p>\r\n<ul>\r\n<li>R3load and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't test alternate NameTab, when there is no code page conversion and also not when the code page conversion does not change the size of a character.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In error messages about pools or clusters there shall be no dirt-characters behind the physical or logical table names.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write timestamps of cluster items in case of problems with cluster items.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In diagnostic mode R3load tries to continue after initial cluster items.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP for kernel</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction when selecting details of RSCP statistics from ABAP.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>89</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>39</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>..?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 103</strong></p>\r\n<ul>\r\n<li>R3load and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add check: SPUMG writes test data, R3load verifies them.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add check: inactive nametabs should be empty when starting Unicode conversion:<br />DDXTT<br />DDXTF<br />DDXTT_CONV_UC<br />DDXTF_CONV_UC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add check: Unicode conversion export produces data in endiannes of host.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add check: for Unicode conversion export the widths of character fields in the Unicode nametab should be twice the non-Unicode widths.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 765475 gives additional info on the meaning of these checks, and what to do when they indicate a problem.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Do not abort, when messages about table IMPREDOC need to be written. See also SAP note 1062305.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Check profile parameter value of rsts/ccc/cachesize to be in valid range. See also SAP note 1078824.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs (prepared 2007-06-21):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>96 (2007-08-22)</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>kernel</td>\r\n<td>197 (2007-08-24)</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load w/o IMPREDOC</td>\r\n<td>53 (2007-08-07)</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>67 (2007-11-21)</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>kernel</td>\r\n<td>123 (2007-08-10)</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>in production.</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>after 2007-06-21</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 104</strong></p>\r\n<ul>\r\n<li>R3trans and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add-on to package 103: Don't look for table UMGCOMCHAR, when a R3trans of higher release is used to import transport requests into a 4.6c system. (This is needed for Upgrade preparation).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add matching feature to the test driver 'rscpf_db tchk'.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3trans</td>\r\n<td>203</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3trans</td>\r\n<td>123</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3trans</td>\r\n<td>&#160;&#160;7</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3trans</td>\r\n<td>in production.</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3trans</td>\r\n<td>in production.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 105</strong></p>\r\n<ul>\r\n<li>R3load and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add a check that data of pooled tables fit into the target pool table. (This shall stop customers to execute UMG_POOL_TABLE too late.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Modify an error message into a warning:<br />I18N_NAMETAB_ALT_ALLOW&#160;&#160;= ...<br />See also note 1088816.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Learn new container types 'E' and 'J'<br />But they work like 'D' and 'I' in the moment.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>99</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>78</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>in production.</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>in production.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 106</strong></p>\r\n<ul>\r\n<li>R3load and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improve R3load conversion log (in .xml files, for consumption by transaction SUMG, in the conversion target system) for incremental conversion exports where steps ran into errors and needed repetition.<br />Note 1012025 describes the problem; this change dispenses of manual intervention.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Fix broken .xml log files left over after R3load crashes.<br />Note 994909 describes the problem.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>99</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>63 (2007-10-26.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&#160;&#160;&#160;&#160; PR:5115445 2007)</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>in production. (2007-10-3)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 107</strong></p>\r\n<ul>\r\n<li>R3load and RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't dump when R3load is used without command line parameter '-datacodepage'. That could happen when you export from a non-Unicode database without any code page conversion. See note 1090368 for details.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>97</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>58</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>3</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 108</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction in 'rscpConvOneChar' for combinations of Asian double byte characters and illegal bytes.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>Feb.2008</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>78</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;4</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>Oct.2007</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 109</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction in makefiles to enable compilation of module in any order. (Needed for LINUX.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction in F1-memory- (Needed for OS/390.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>..?</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;4</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>Oct.2007</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 110</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load aborts if it does not support specific features which are requested by transaction SPUMG. (This shall protect a Unicode conversion to be damaged because a out-dated R3load is used.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load writes error messages, when transaction SPUMG is too old.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>99</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>78</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>Oct.2007</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 111</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Do not abort when reading from a 6.20 database which is on service package level of 2004 as long as no Unicode conversion is requested.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>99</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>78</td>\r\n</tr>\r\n<tr>\r\n<td>701&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>..?</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 112</strong></p>\r\n<ul>\r\n<li>RSCP and R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't crash in following special case:&#160;&#160; 1. R3load is executed and writes 1 or more XML files.&#160;&#160;&#160;&#160;2. R3load is executed a second time, checks the last XML file and writes one more XML file.&#160;&#160; 3. R3load closes that XML file (as it is longer then 10 MB) and create even one more XML file.<br />(See Note 1132837 for details.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Do not look for words with national characters in fields with a type, where such words should not exist (Date, Time, Numeric)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Handle containers of type 'J'. I such a case one database column contains a container and an other column contains a structure name. The container contents is divided into sub-fields by using the structure name but using only the key fields of that structure.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Read UMGSETTINGS before UMGCONTAINER an if UMGSETTINGS does not talk about 'J' then interpret CDPOS+'I' as if there was a 'J'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When key is not at the left or missing, the container decomposition is switched off in case of 'J' or 'E'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't write \"UMGCOMCHAR read check, skip: no data found; probably old SPUMG\" when R3load is used without Unicode conversion.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Special error message for the combination MDMP customer trying to convert during import and version of R3load, which doesn't recognize its own trick with 'MDMP' instead of a code page number.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Test drivers can test rscpMC-Objects with single input code page.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction in rscpDAJoinPairs (to resolve possible conflicts in UMGCONTAINER).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Use 'rscpsc2' instead of 'rscpuc2' in some error messages.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>101</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>78</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>..?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>Jan.2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 113</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't abort R3load on pool tables, which are too short in every case. When table should not be in the pool, only a warning given.<br />Example: old data from TCP07 in pool ATAB, though TCP07 is not a pooled table.<br />The R3load 6.40 pl. 99 up to 105 (Feb 22th - May 15th) and the R3load 7.00 pl. 78 up to 85 (Mar 13th - Apr 25th) on some platforms aborted with a signal; on most platforms they stopped with an error messages.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>106</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>86</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>Apr.2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 114</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Try to use fast 7-bit-ASCII to UTF-16 conversion before starting expensive code page selection.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Some error message texts enhanced.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal cleanup of rscpexmc.c</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add two new fast conversion methods to code page converter: 7-bit-US-ASCII to UTF-16BE and to UTF-16LE.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal: Remove code which enabled re-activation of old code page converter. (Was not used since 6.10.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add 6280 to F5 memory</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal: Remove include code page numbers from F5 memory.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>233</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>106</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>? perhaps 80 ?</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>86</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;4</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>Feb.2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 115</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Corr. on mixture of word-too-short and words which select a code page unambiguously.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't cry about age of SPUMG when R3load is used without a Unicode conversion. (Do the check on the features of UMGSETTINGS later.) For details see SAP note 1148541.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow environment variables to rename database tables on the fly. (This is mainly used to test compatibility to older database releases.)</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>233</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>106</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>80</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>86</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;4</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>March 2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 116</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If UMGCCTL-GUESS = 2 then even a UMGSTAT-DICCNT = 0 shall not switch off the vocabulary reading.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't use language field when value of UMGCCTL-LANGFLDPOS is not safe.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correct automatic switching from 'I' to 'J' for CDPOS. And now also for DBTABLOG.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Check against missing UMGCCTL-LANGFLDPOS.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Check for inconsistencies between UMGLANGCP and UMGLNOCP.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better SysLog messages in case somebody tries to use Unicode conversion tools in a Unicode system.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements in trace facility for the vocabulary loader.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements of the code page convert to be able to handle a code page as complicated as GB18030-2005.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update '1409' and '1614' in the F5-memory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add '8402' to F5-memory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements in filtering illegal chracter combinations in UTF-16</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction: The code page converter could damage input (!) buffer, when a character of length 4 was recognized by an input runtime rule. (Was possible with code page 8401, but was never reported.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Set version of RSCP's CALL interface to 20 to indicate enhancements which have been neccessary to support SAP-8402 which is GB-18030-2005.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>rscpGetASLoginLangList and rscpIsLangASLogin now check if a language is also in table TCP0I, field processed_languages. (But downwards compatible.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Write own and perants process identifier into shared memory cache.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More details in language list.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhance test driver to monitor input buffer.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>268</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>115</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>192</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>97</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160;4</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>25</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;9</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160;1</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>kernel</td>\r\n<td>21</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;6</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>&#160;&#160;1</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>May 2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 117</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add Utf8nToUcnCP_5 and UcnToUtf8nCP_5 and add &#126;SubstAll functions for XML parser.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better handling of errors from port-lib.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>268</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>192</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>kernel</td>\r\n<td>21</td>\r\n</tr>\r\n<tr>\r\n<td>develop</td>\r\n<td>kernel</td>\r\n<td>2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 118</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Error correction for command line option '-v'.<br />In release 7.01 this was damaged during modifications 105..115.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>not needed</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>not needed</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;6</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>not needed</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>not needed</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><br /></p>\r\n<p><strong>Package 119</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More precise control whether the data in fields of specific data type can or cannot contain national characters. (As an example we will not search for national characters in client fields nor in date fields.)</li>\r\n</ul>\r\n</ul>\r\n<p><br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>115</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>97</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;9</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;6</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>R3load</td>\r\n<td>Oct.2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 120</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the RSCP library was initialized too early in the kernel, then clean the desk and start over. Within the kernel the code page converter cache is kept in shared memory. When the library is needed too early, it starts with a preliminary cache in malloc-memory and replaces that later, when the propper intialization is executed.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Increase 'reorg_version' number when switching from malloc memory to shared memory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CCC cache shall have different labels when characters are 8 or 16 bit wide to prevent wrong tools from attaching.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal: Correction in function for legacy code page in non-Unicode systems.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal: Testdriver enhancements: New commands \"finish\" and \"ss\". New variables \"use_shm\" and \"legacy_cp\". And the test driver got new mechanism to set different values for Unicode-tests and non-Unicode-tests.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal: Remove remaining code about F4 memory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Internal: Code clean-up near SNC_LEGACY_SAP_CP. Unused functions removed.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>268</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>192</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>25</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>kernel</td>\r\n<td>21</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>kernel</td>\r\n<td>Nov.2008</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 121</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Disallow overlong UTF-8 sequences in many more cases. See also note 1294430.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Catch control codes during conversion from UTF-8 to UTF-16 when they are not wanted. (Important for SAPGUI input.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>More details are in SAP note 1270827. That note will also show the patch numbers.</li>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>290</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>199</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>45</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC</td>\r\n<td>19</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>34</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>10</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>157</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>RFC</td>\r\n<td>16</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>librfc</td>\r\n<td>22</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>kernel</td>\r\n<td>46</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>kernel</td>\r\n<td>&#160;&#160;Jan.2009</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 122</strong></p>\r\n<ul>\r\n<li>RSCP for R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't die when the control table UMGCONTAINER is talking about database tables and structures and for some of the tables or structures the alternate NameTab entries are missing.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>More details are in SAP note 1291072. That note will also show the patch numbers.</li>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;100</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;Jan 2009</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 123</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Implicitly rectify wrong endian -dbcodepage parameter in Unicode import. For details see the \"Unicode Conversion Trouble Shooting Guide\", Note 765475.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>119 (2009-06-04)</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>104 (2009-06-05)</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>14 (2009-06-05)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 124</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Don't abort while exporting DBTABLOG or CDCLS when the key fields of structures are set, which are mentioned in UMGCONTAINER themselves. (Nickname: container-in-container) For details see SAP Note 1353690.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>123</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>107</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>18</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 125</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More trace entries and more details written in case of problems during reconnection to a database, which was offline for a while. For details SAP Note 1354197.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>kernel</td>\r\n<td>309</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>kernel</td>\r\n<td>233</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>kernel</td>\r\n<td>69</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>kernel</td>\r\n<td>177</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>kernel</td>\r\n<td>64</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 126</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better control of the substitution with replacement characters, when one byte of a possible double-byte Asian non-Unicode character is at the end of a buffer.<br />(See also note 1372230)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>RFC</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>RFC</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>6</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>RFC</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>sapiconv</td>\r\n<td>3</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>sapiconv</td>\r\n<td>10</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>sapiconv</td>\r\n<td>3</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>sapiconv</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 127</strong></p>\r\n<ul>\r\n<li>R3load for Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>It could happen that national words did not show up in the working list of transaction SUMG, when the data field contained NULL-byte on the left side of such a word. (See note 1373578 for more details.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>6.40</td>\r\n<td>R3load</td>\r\n<td>124</td>\r\n</tr>\r\n<tr>\r\n<td>7.00</td>\r\n<td>R3load</td>\r\n<td>111</td>\r\n</tr>\r\n<tr>\r\n<td>7.01</td>\r\n<td>R3load</td>\r\n<td>21</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 128</strong></p>\r\n<ul>\r\n<li>R3load for Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For change documents, which are created during the system has executed a kernel of release 6.10 or higer: Enhanced adjustments of Asian data in fields like CDPOS-TABKEY, CDPOS_UID-TABKEY, CDPOS_STR-TABKEY already within R3load. After that there is less processing in the fresh Unicode system needed. (See also note 1337256.)</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>128</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>113</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>26</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 129</strong></p>\r\n<ul>\r\n<li>R3load for Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R3load no longer write false warnings \"Cannot get memory to read NameTab\".<br />Further details are in note 1413098.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>With tag &lt;NUVALUE&gt; R3load writes additional information into the R3load-Log(XML) to make it easier for transaction SUMG to match R3load-Log(XML) with Repair-Log.<br />Further details are in note 1417713.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>128</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>115 / 116</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>?</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>R3load</td>\r\n<td>35</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 130</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New versions of the code page converter library have an additional process-local cache in front of the shared memory cache. If a converter object can be created form that process-local cache, that can be done without using the semaphore 12. And when the semaphore 12 is locked less often, it will have shorter waiting queues.<br />Further details are in note 1413455.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>314</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel</td>\r\n<td>237</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel</td>\r\n<td>74</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>Kernel</td>\r\n<td>182</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>Kernel</td>\r\n<td>68</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>?</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Because of negative side effects to the spooler it is much better to wait and apply packages 130 and 132 together. There is also package 134, but the side effects removed there are less dramatical.<br /><br /><br /></p>\r\n<p><strong>Package 131</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>More traces when code page converter reconnects to database.<br />Further details are in note 1354197.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>309</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel</td>\r\n<td>233</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel</td>\r\n<td>69</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>Kernel</td>\r\n<td>177</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>Kernel</td>\r\n<td>64</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>?</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><br /></p>\r\n<p><strong>Package 132</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The enhancement of package 130 was corrected as it could cause severe problems in the spooler.<br />Further details are in note 1425811.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>316</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel</td>\r\n<td>239</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel</td>\r\n<td>77</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>Kernel</td>\r\n<td>184</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>Kernel</td>\r\n<td>71</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 133</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Add code page 1146 to the F5-memory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Update code page 1155 in the F5-memory. (See also note 1062237.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Remove code page 6114 from the F5-memory.</li>\r\n</ul>\r\n</ul>\r\n<p>See also note 1423651.<br />Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>sapiconv</td>\r\n<td>9</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>sapiconv</td>\r\n<td>7</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>sapiconv</td>\r\n<td>4</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>sapiconv</td>\r\n<td>3</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>sapiconv</td>\r\n<td>4</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>sapiconv</td>\r\n<td>2</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 134</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The enhancement of package 130 was corrected as it could cause problems in class CL_ABAP_CONV_OUT_CE.<br />Further details are in note 1439009.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The enhancement of package 130 was corrected as it could cause problems when non-standard substitution characters are used. If a re-org of the CCC cache happens in some cases such a conversion character is used also be other converter objects instead of '#'. (Not jet seen in customer systems.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When converting a sequence of input buffers from JIS to Unicode and when the CCC cache happens to be re-organized while switching from on input buffer to the next input buffer, the shift-status of the input data stream can be lost. (Not jet seen in customer systems.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A new profile parameter \"rscp/CActivate/cache/size\" can be used to control the size of the process locla cache, which was introduced with package 130.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 135</strong></p>\r\n<ul>\r\n<li>R3load for Unicode conversion</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Better handling of alignment gaps in containers, when a structure was used which contains binary fields.<br />Further details are in note 1463669.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>R3load</td>\r\n<td>135</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>R3load</td>\r\n<td>&#160;&#160;?</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>R3load</td>\r\n<td>34</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>R3load</td>\r\n<td>47</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 136</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Allow more combinations with EUC-JP (SAP-8100) for code page conversion.<br />Further details are in note 1448734.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>328</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel</td>\r\n<td>255</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel</td>\r\n<td>91</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>Kernel</td>\r\n<td>198</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>Kernel</td>\r\n<td>84</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>46</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>Package 136</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction of macros that deals with conversions from UTF-16 to other code pages. More checks to surrogate pairs have been added.<br />Further details are in note 1476582.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>334</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel</td>\r\n<td>260</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel</td>\r\n<td>98</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>Kernel</td>\r\n<td>204</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>Kernel</td>\r\n<td>90</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>53</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>Package 137</strong></p>\r\n<ul>\r\n<li>R3load</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improvement of messages that appear when table UMGSETTINGS contains an unknown feature such as FEATURE_CUUC_ACTIVE. These messages are exclusively used by R3load.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel and R3load</td>\r\n<td>382</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel and R3load</td>\r\n<td>310</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel and R3load</td>\r\n<td>152</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel and R3load</td>\r\n<td>96</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>Package 138</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Correction in rscpcls__codepage_and_locale_set() so that a buffer overrun does not happen in sapclcl_cur_lang_cp_loc().</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>407</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>Kernel</td>\r\n<td>348</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>Kernel</td>\r\n<td>192</td>\r\n</tr>\r\n<tr>\r\n<td>710</td>\r\n<td>Kernel</td>\r\n<td>277</td>\r\n</tr>\r\n<tr>\r\n<td>711</td>\r\n<td>Kernel</td>\r\n<td>164</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>312</td>\r\n</tr>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>&#160;16</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>Package 139</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>F5-memory has been updated. The main change is on codepage 8402. It existed in F5-memory but it did not exist in the database. There were some other minor discrepancies between F5-memory and the database. These discrepancies have been corrected in the new F5-memory.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>412</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>317</td>\r\n</tr>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>&#160;26</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>Package 140</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In a rare case the CCC returns an error after a DB has gone offline and then reconnected. Detail will be found in note 1781218.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>417</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>413</td>\r\n</tr>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>&#160;35</td>\r\n</tr>\r\n<tr>\r\n<td>738</td>\r\n<td>Kernel</td>\r\n<td>&#160;&#160;3</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>Package 141</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The order of syslog entries written by rscpep3_err_print3() in krn/rscp/rscperr.c was not right. It is corrected now.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>411</td>\r\n</tr>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>&#160;35</td>\r\n</tr>\r\n<tr>\r\n<td>738</td>\r\n<td>Kernel</td>\r\n<td>&#160; 4</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 142</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>There have been some complaints from customers that there were excessive accesses to table TCP00A. It is understood that the reason is findNameInTCP00A() in krn/rscp/rscpcpn.c. In order to reduce the table access a cache has been created.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>640</td>\r\n<td>Kernel</td>\r\n<td>423</td>\r\n</tr>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>417</td>\r\n</tr>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>110</td>\r\n</tr>\r\n<tr>\r\n<td>740</td>\r\n<td>Kernel</td>\r\n<td>&#160; 6</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 143</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>When one codepage segment contains more than 100 subsegments, rscpdl.c writes an error message but keeps reading subsegments, which causes a segment violation. Although there is no such a codepage, it is better to be corrected.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>720</td>\r\n<td>Kernel</td>\r\n<td>433</td>\r\n</tr>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>123</td>\r\n</tr>\r\n<tr>\r\n<td>740</td>\r\n<td>Kernel</td>\r\n<td>22</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 144</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Added a new command \"dgex\" in rscpf_a, rscpf_db, etc.</li>\r\n</ul>\r\n</ul>\r\n<p>Enhancement: rscpf_db exists in SAPEXE.SAR only. The program is for testing and diagnosing purpose.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>721</td>\r\n<td>in SAPEXE.SAR</td>\r\n<td>patch level not determined</td>\r\n</tr>\r\n<tr>\r\n<td>741</td>\r\n<td>in SAPEXE.SAR</td>\r\n<td>patch level not determined</td>\r\n</tr>\r\n<tr>\r\n<td>742</td>\r\n<td>in SAPEXE.SAR</td>\r\n<td>patch level not determined</td>\r\n</tr>\r\n<tr>\r\n<td>743</td>\r\n<td>in SAPEXE.SAR</td>\r\n<td>patch level not determined</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;<strong>Package 145</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>A request from R3load: enhanced so that some shadow table names be accepted during an export from a non-Unicode system on a low release (4.6C, etc). Downport from CGK.</li>\r\n</ul>\r\n</ul>\r\n<p>Enhancement:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>411</td>\r\n</tr>\r\n<tr>\r\n<td>741</td>\r\n<td>Kernel</td>\r\n<td>210</td>\r\n</tr>\r\n<tr>\r\n<td>742</td>\r\n<td>Kernel</td>\r\n<td>\r\n<p>29</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>743</td>\r\n<td>Kernel</td>\r\n<td>4</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Package 146</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>TCP0C.DAT has been updated in 7.21 and above.</li>\r\n</ul>\r\n</ul>\r\n<p>&#65279;<strong>Package 147</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Add an error text when a return code from kernel call ASIAN_CHAR_WIDTH is 3.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>721</td>\r\n<td>Kernel</td>\r\n<td>411</td>\r\n</tr>\r\n<tr>\r\n<td>741</td>\r\n<td>Kernel</td>\r\n<td>210</td>\r\n</tr>\r\n<tr>\r\n<td>742</td>\r\n<td>Kernel</td>\r\n<td>\r\n<p>32</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>743</td>\r\n<td>Kernel</td>\r\n<td>7</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;<strong>Package 148</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Correction to a buffer length miscalculation in rscpr0D__read_TCP0D() in rscpdl.c.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs: 721, 722, 742, 744, 745, 746, 747, 748.</p>\r\n<p><strong>Package 149</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Correction to error checking steps in rscpa24_f4_access() in rscpac1.c.</li>\r\n</ul>\r\n</ul>\r\n<p>Repairs: 721, 722, 742, 744, 745, 746, 747, 748.</p>\r\n<p><strong>Package 150</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Creation of Utf16nToUtf8nSubst().</li>\r\n</ul>\r\n</ul>\r\n<p>Development: 721, 722, 742,&#160;745, 746, 747, 748.</p>\r\n<p><strong>Package 151</strong></p>\r\n<ul>\r\n<li>SAPU16</li>\r\n<ul>\r\n<li>Making memcpy_sU16(), memmove_sU16(), sprintf_sU16(), strcat_sU16(), strcpy_sU16(), strncat_sU16() and strncpy_sU16() public (requested by RFC Library team).</li>\r\n</ul>\r\n</ul>\r\n<p>Development:&#160;722, 742,&#160;745,&#160;748, 749.</p>\r\n<p><strong>Package 152</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Remove a codepage check logic from rscpa23_check_locale() because it is out-of-date. The changes are within a #ifndef SAPwithUNICODE block&#160;and they affect non-Unicode kernels only.</li>\r\n</ul>\r\n</ul>\r\n<p>Repair: 722, 742,&#160;745,&#160;749, 750, 751, 752.</p>\r\n<p><strong>Package 153</strong></p>\r\n<ul>\r\n<li>SAPU16</li>\r\n<ul>\r\n<li>Making snprintfU16, snprintf_sU16, strnlenU16, strtollU16, strtoullU16, strdupU16, strerror_rU16, mkstempU16 and gets_sU16 public (requested by RFC Library team). strerror_rU16 and mkstempU16 are on Unix only.</li>\r\n</ul>\r\n</ul>\r\n<p>Development:&#160;753, 776.</p>\r\n<p><strong>Package 154</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Correction to the length of a buffer in rscpgf5.c. rscpgf5.c is a standalone test program and is not shipped to customers.</li>\r\n</ul>\r\n</ul>\r\n<p>Repair: 721. 722.</p>\r\n<p><strong>Package 155</strong></p>\r\n<ul>\r\n<li>&#160;RSCP</li>\r\n<ul>\r\n<li>Write more trace information when a CCC reorg happens. Messages will be written when the trace level is 3. ( The messages can be analysed by developers only.)</li>\r\n</ul>\r\n</ul>\r\n<p>Development: 749, 753, CGK (779).</p>\r\n<p><strong>Package 156</strong></p>\r\n<ul>\r\n<li>RSCP</li>\r\n<ul>\r\n<li>Write more information when&#160;sapslcl_set_lang_cp_loc() in krn/rscp/saplcp.c is called. An error is reported at a customers non-Unicode Japanese single code page system but there is no clue. Messages will not be written in a Unicode system.</li>\r\n</ul>\r\n</ul>\r\n<p>Development: 753.</p>\r\n<p><strong>General remarks</strong></p>\r\n<p>Please note that, from the summer 2012, <strong>SAP no longer provides any patches for kernel releases 7.00, 7.01, 7.02, 7,10 or 7.11. Kernel releases 7.20 and 7.21 are downward compatible and older releases are now replaced by 7.2x.</strong> Notes 1629598 and 163625 have detail explanation and explain how to replace an old kernel.<br /><br /></p>\r\n<p><strong>Problems in LOADTOOLS:</strong></p>\r\n<p>You are running a Unicode Conversion with SWPM, the export aborts with a message similar to the one below:</p>\r\n<p>A1WECTL 001 Logic table 'XXXX' has dbtabpos flag set within&#160; nametab only - using displacement.<br />A1EECTL 000 (CTL) Failed to process tables<br />A1EECTL 000 (TBL) Error with table 'XXXX': Table 'XXXX': displacement 10 and dbtabpos 0 do not coincide for logic key field 'YYYY' (alternate nametab)</p>\r\n<p>The issue will be fixed with a SWPM patch. The note will be updated as soon as the patch is available on SMP. If you should run into the issue you may open an incident on component BC-INS-MIG-TLA.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "0100"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021965)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021965)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000447519/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447519/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "988224", "RefComponent": "BC-I18-UNI", "RefTitle": "Performace problems R3load export of Pool with Unicode conv.", "RefUrl": "/notes/988224"}, {"RefNumber": "984965", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load crashes on container with structure without fields", "RefUrl": "/notes/984965"}, {"RefNumber": "978244", "RefComponent": "BC-I18-UNI", "RefTitle": "CU&UC and NameTab errors for pools or clusters", "RefUrl": "/notes/978244"}, {"RefNumber": "977372", "RefComponent": "LO-VC-DEP", "RefTitle": "Dependencies corrupt after unicode conversion", "RefUrl": "/notes/977372"}, {"RefNumber": "952208", "RefComponent": "BC-I18-UNI", "RefTitle": "SNC-Names with non-ASCII characters in SAP WebAS Unicode", "RefUrl": "/notes/952208"}, {"RefNumber": "938738", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using ambiguous blended code page", "RefUrl": "/notes/938738"}, {"RefNumber": "938737", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using blended code page", "RefUrl": "/notes/938737"}, {"RefNumber": "936533", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion: NameTab check long duration", "RefUrl": "/notes/936533"}, {"RefNumber": "936117", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion and binary data hidden in containers", "RefUrl": "/notes/936117"}, {"RefNumber": "935971", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion: invalid DBSL data type: 12", "RefUrl": "/notes/935971"}, {"RefNumber": "913996", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load-unload core dump after reading UMGLANGCP", "RefUrl": "/notes/913996"}, {"RefNumber": "901827", "RefComponent": "BC-I18", "RefTitle": "R3load: UMGSETTINGS says: RADCUCNT not succesful", "RefUrl": "/notes/901827"}, {"RefNumber": "892119", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load unload 6200 for Unicode and message <USED_CP>", "RefUrl": "/notes/892119"}, {"RefNumber": "872124", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load with Unicode conversion abort during EDI40", "RefUrl": "/notes/872124"}, {"RefNumber": "862009", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion and R3load cannot read end of UMGCCTL", "RefUrl": "/notes/862009"}, {"RefNumber": "837442", "RefComponent": "BC-I18-UNI", "RefTitle": "Some dynpros with strange attributes after Unicode convers.", "RefUrl": "/notes/837442"}, {"RefNumber": "785959", "RefComponent": "BC-I18", "RefTitle": "empty screen and \"codepage converter error rc 512\"", "RefUrl": "/notes/785959"}, {"RefNumber": "775114", "RefComponent": "BC-I18", "RefTitle": "Problems during transport into a Unicode system", "RefUrl": "/notes/775114"}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475"}, {"RefNumber": "752859", "RefComponent": "BC-I18", "RefTitle": "sapiconv - a tool for converting the encoding of files", "RefUrl": "/notes/752859"}, {"RefNumber": "741427", "RefComponent": "BC-I18", "RefTitle": "Short dump occured during sending an email.", "RefUrl": "/notes/741427"}, {"RefNumber": "738858", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion", "RefUrl": "/notes/738858"}, {"RefNumber": "720738", "RefComponent": "BC-ABA-SC", "RefTitle": "Enhancements for the ITS", "RefUrl": "/notes/720738"}, {"RefNumber": "716200", "RefComponent": "BC-I18", "RefTitle": "Can characters disappear during code page conversion", "RefUrl": "/notes/716200"}, {"RefNumber": "695907", "RefComponent": "BC-I18", "RefTitle": "Syslog CP7 ... rscpc 180", "RefUrl": "/notes/695907"}, {"RefNumber": "687334", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in the function FindMyChars()", "RefUrl": "/notes/687334"}, {"RefNumber": "685023", "RefComponent": "BC-I18-BID", "RefTitle": "Material numbers must be left-to-right", "RefUrl": "/notes/685023"}, {"RefNumber": "653739", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/653739"}, {"RefNumber": "635461", "RefComponent": "BC-ABA-SC", "RefTitle": "Displayable characters and UTF8 DIAG protocol", "RefUrl": "/notes/635461"}, {"RefNumber": "634007", "RefComponent": "BC-I18", "RefTitle": "rscpLengthOfBasicChar locks semaphore 12 too often", "RefUrl": "/notes/634007"}, {"RefNumber": "621992", "RefComponent": "BC-ABA-SC", "RefTitle": "DIAG protocol permits several code pages", "RefUrl": "/notes/621992"}, {"RefNumber": "579747", "RefComponent": "BC-I18", "RefTitle": "Support of HongKong Chinese", "RefUrl": "/notes/579747"}, {"RefNumber": "563769", "RefComponent": "BC-I18", "RefTitle": "Do no core dump, when some pointers are missing in memory", "RefUrl": "/notes/563769"}, {"RefNumber": "536913", "RefComponent": "BC-I18", "RefTitle": "Control codes are not converted to '#' as required", "RefUrl": "/notes/536913"}, {"RefNumber": "511655", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/511655"}, {"RefNumber": "507083", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/507083"}, {"RefNumber": "493791", "RefComponent": "BC-I18", "RefTitle": "RFC with deep structures in an MDMP system", "RefUrl": "/notes/493791"}, {"RefNumber": "487923", "RefComponent": "BC-I18", "RefTitle": "Undesired special charac. 6.10 GUI at AS/400", "RefUrl": "/notes/487923"}, {"RefNumber": "484651", "RefComponent": "BC-I18", "RefTitle": "Lots of unnecessary F40 entries in the syslog", "RefUrl": "/notes/484651"}, {"RefNumber": "453638", "RefComponent": "BC-I18", "RefTitle": "Basis Release 6.xx and JIS printer support", "RefUrl": "/notes/453638"}, {"RefNumber": "436645", "RefComponent": "BC-I18", "RefTitle": "incorrect mapping ISO-2+ others --> Unicode", "RefUrl": "/notes/436645"}, {"RefNumber": "435611", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/435611"}, {"RefNumber": "432922", "RefComponent": "BC-I18", "RefTitle": "Support of GB-18030    (SAP-8401)", "RefUrl": "/notes/432922"}, {"RefNumber": "422313", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/422313"}, {"RefNumber": "419519", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/419519"}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252"}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598"}, {"RefNumber": "1476582", "RefComponent": "BC-I18", "RefTitle": "Inappropriate handling of a broken surrogate pair", "RefUrl": "/notes/1476582"}, {"RefNumber": "1463669", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load fills binary alignment gaps in containers with x'00'", "RefUrl": "/notes/1463669"}, {"RefNumber": "1439009", "RefComponent": "BC-I18", "RefTitle": "Code page converter using wrong substitution character", "RefUrl": "/notes/1439009"}, {"RefNumber": "1423651", "RefComponent": "BC-I18", "RefTitle": "Code pages 1146 and 1155 in 'F5-memory'", "RefUrl": "/notes/1423651"}, {"RefNumber": "1417713", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion: SUMG and container in key fields", "RefUrl": "/notes/1417713"}, {"RefNumber": "1413098", "RefComponent": "BC-I18", "RefTitle": "R3load give false warning \"Cannot get memory to read NameTab", "RefUrl": "/notes/1413098"}, {"RefNumber": "1373578", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP to Unicode conversion: SUMG does not offer some words", "RefUrl": "/notes/1373578"}, {"RefNumber": "1372230", "RefComponent": "BC-I18", "RefTitle": "In separate programs: broken Asian multi-byte character", "RefUrl": "/notes/1372230"}, {"RefNumber": "1354197", "RefComponent": "BC-I18", "RefTitle": "More traces when code page converter reconnects to database", "RefUrl": "/notes/1354197"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1294430", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1294430"}, {"RefNumber": "1291072", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load: rscpGetUmgContainer*Line,3587, l_result = 128", "RefUrl": "/notes/1291072"}, {"RefNumber": "1270827", "RefComponent": "BC-I18", "RefTitle": "Control Codes not filtered during input", "RefUrl": "/notes/1270827"}, {"RefNumber": "1266393", "RefComponent": "BC-I18", "RefTitle": "Code page converter cache always 6.000.000 bytes?", "RefUrl": "/notes/1266393"}, {"RefNumber": "1148541", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load export: RSCPECALL ... UMGSETTINGS: SPUMG is too old", "RefUrl": "/notes/1148541"}, {"RefNumber": "1132837", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load failure in Unicode Conversion with .xml files", "RefUrl": "/notes/1132837"}, {"RefNumber": "1122673", "RefComponent": "BC-I18", "RefTitle": "R3load, R3trans fail: Syslog F6S: Al_info->key_len too small", "RefUrl": "/notes/1122673"}, {"RefNumber": "1088816", "RefComponent": "BC-I18-UNI", "RefTitle": "(RSCP) ERROR: I18N_NAMETAB_ALT_ALLOW = ...", "RefUrl": "/notes/1088816"}, {"RefNumber": "1078824", "RefComponent": "BC-I18", "RefTitle": "Code page converter writes zero-bytes / damaged box chars.", "RefUrl": "/notes/1078824"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "432922", "RefComponent": "BC-I18", "RefTitle": "Support of GB-18030    (SAP-8401)", "RefUrl": "/notes/432922 "}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475 "}, {"RefNumber": "738858", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion", "RefUrl": "/notes/738858 "}, {"RefNumber": "752859", "RefComponent": "BC-I18", "RefTitle": "sapiconv - a tool for converting the encoding of files", "RefUrl": "/notes/752859 "}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598 "}, {"RefNumber": "579747", "RefComponent": "BC-I18", "RefTitle": "Support of HongKong Chinese", "RefUrl": "/notes/579747 "}, {"RefNumber": "1354197", "RefComponent": "BC-I18", "RefTitle": "More traces when code page converter reconnects to database", "RefUrl": "/notes/1354197 "}, {"RefNumber": "685023", "RefComponent": "BC-I18-BID", "RefTitle": "Material numbers must be left-to-right", "RefUrl": "/notes/685023 "}, {"RefNumber": "436645", "RefComponent": "BC-I18", "RefTitle": "incorrect mapping ISO-2+ others --> Unicode", "RefUrl": "/notes/436645 "}, {"RefNumber": "1476582", "RefComponent": "BC-I18", "RefTitle": "Inappropriate handling of a broken surrogate pair", "RefUrl": "/notes/1476582 "}, {"RefNumber": "1439009", "RefComponent": "BC-I18", "RefTitle": "Code page converter using wrong substitution character", "RefUrl": "/notes/1439009 "}, {"RefNumber": "1266393", "RefComponent": "BC-I18", "RefTitle": "Code page converter cache always 6.000.000 bytes?", "RefUrl": "/notes/1266393 "}, {"RefNumber": "1417713", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion: SUMG and container in key fields", "RefUrl": "/notes/1417713 "}, {"RefNumber": "1463669", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load fills binary alignment gaps in containers with x'00'", "RefUrl": "/notes/1463669 "}, {"RefNumber": "952208", "RefComponent": "BC-I18-UNI", "RefTitle": "SNC-Names with non-ASCII characters in SAP WebAS Unicode", "RefUrl": "/notes/952208 "}, {"RefNumber": "1423651", "RefComponent": "BC-I18", "RefTitle": "Code pages 1146 and 1155 in 'F5-memory'", "RefUrl": "/notes/1423651 "}, {"RefNumber": "1413098", "RefComponent": "BC-I18", "RefTitle": "R3load give false warning \"Cannot get memory to read NameTab", "RefUrl": "/notes/1413098 "}, {"RefNumber": "1270827", "RefComponent": "BC-I18", "RefTitle": "Control Codes not filtered during input", "RefUrl": "/notes/1270827 "}, {"RefNumber": "1372230", "RefComponent": "BC-I18", "RefTitle": "In separate programs: broken Asian multi-byte character", "RefUrl": "/notes/1372230 "}, {"RefNumber": "1373578", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP to Unicode conversion: SUMG does not offer some words", "RefUrl": "/notes/1373578 "}, {"RefNumber": "936117", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion and binary data hidden in containers", "RefUrl": "/notes/936117 "}, {"RefNumber": "720738", "RefComponent": "BC-ABA-SC", "RefTitle": "Enhancements for the ITS", "RefUrl": "/notes/720738 "}, {"RefNumber": "1291072", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load: rscpGetUmgContainer*Line,3587, l_result = 128", "RefUrl": "/notes/1291072 "}, {"RefNumber": "621992", "RefComponent": "BC-ABA-SC", "RefTitle": "DIAG protocol permits several code pages", "RefUrl": "/notes/621992 "}, {"RefNumber": "1148541", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load export: RSCPECALL ... UMGSETTINGS: SPUMG is too old", "RefUrl": "/notes/1148541 "}, {"RefNumber": "563769", "RefComponent": "BC-I18", "RefTitle": "Do no core dump, when some pointers are missing in memory", "RefUrl": "/notes/563769 "}, {"RefNumber": "1088816", "RefComponent": "BC-I18-UNI", "RefTitle": "(RSCP) ERROR: I18N_NAMETAB_ALT_ALLOW = ...", "RefUrl": "/notes/1088816 "}, {"RefNumber": "493791", "RefComponent": "BC-I18", "RefTitle": "RFC with deep structures in an MDMP system", "RefUrl": "/notes/493791 "}, {"RefNumber": "487923", "RefComponent": "BC-I18", "RefTitle": "Undesired special charac. 6.10 GUI at AS/400", "RefUrl": "/notes/487923 "}, {"RefNumber": "1132837", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load failure in Unicode Conversion with .xml files", "RefUrl": "/notes/1132837 "}, {"RefNumber": "1122673", "RefComponent": "BC-I18", "RefTitle": "R3load, R3trans fail: Syslog F6S: Al_info->key_len too small", "RefUrl": "/notes/1122673 "}, {"RefNumber": "1078824", "RefComponent": "BC-I18", "RefTitle": "Code page converter writes zero-bytes / damaged box chars.", "RefUrl": "/notes/1078824 "}, {"RefNumber": "936533", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion: NameTab check long duration", "RefUrl": "/notes/936533 "}, {"RefNumber": "988224", "RefComponent": "BC-I18-UNI", "RefTitle": "Performace problems R3load export of Pool with Unicode conv.", "RefUrl": "/notes/988224 "}, {"RefNumber": "984965", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load crashes on container with structure without fields", "RefUrl": "/notes/984965 "}, {"RefNumber": "978244", "RefComponent": "BC-I18-UNI", "RefTitle": "CU&UC and NameTab errors for pools or clusters", "RefUrl": "/notes/978244 "}, {"RefNumber": "935971", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion: invalid DBSL data type: 12", "RefUrl": "/notes/935971 "}, {"RefNumber": "977372", "RefComponent": "LO-VC-DEP", "RefTitle": "Dependencies corrupt after unicode conversion", "RefUrl": "/notes/977372 "}, {"RefNumber": "938737", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using blended code page", "RefUrl": "/notes/938737 "}, {"RefNumber": "938738", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using ambiguous blended code page", "RefUrl": "/notes/938738 "}, {"RefNumber": "913996", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load-unload core dump after reading UMGLANGCP", "RefUrl": "/notes/913996 "}, {"RefNumber": "901827", "RefComponent": "BC-I18", "RefTitle": "R3load: UMGSETTINGS says: RADCUCNT not succesful", "RefUrl": "/notes/901827 "}, {"RefNumber": "687334", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in the function FindMyChars()", "RefUrl": "/notes/687334 "}, {"RefNumber": "837442", "RefComponent": "BC-I18-UNI", "RefTitle": "Some dynpros with strange attributes after Unicode convers.", "RefUrl": "/notes/837442 "}, {"RefNumber": "892119", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load unload 6200 for Unicode and message <USED_CP>", "RefUrl": "/notes/892119 "}, {"RefNumber": "872124", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load with Unicode conversion abort during EDI40", "RefUrl": "/notes/872124 "}, {"RefNumber": "862009", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion and R3load cannot read end of UMGCCTL", "RefUrl": "/notes/862009 "}, {"RefNumber": "484651", "RefComponent": "BC-I18", "RefTitle": "Lots of unnecessary F40 entries in the syslog", "RefUrl": "/notes/484651 "}, {"RefNumber": "453638", "RefComponent": "BC-I18", "RefTitle": "Basis Release 6.xx and JIS printer support", "RefUrl": "/notes/453638 "}, {"RefNumber": "536913", "RefComponent": "BC-I18", "RefTitle": "Control codes are not converted to '#' as required", "RefUrl": "/notes/536913 "}, {"RefNumber": "785959", "RefComponent": "BC-I18", "RefTitle": "empty screen and \"codepage converter error rc 512\"", "RefUrl": "/notes/785959 "}, {"RefNumber": "695907", "RefComponent": "BC-I18", "RefTitle": "Syslog CP7 ... rscpc 180", "RefUrl": "/notes/695907 "}, {"RefNumber": "716200", "RefComponent": "BC-I18", "RefTitle": "Can characters disappear during code page conversion", "RefUrl": "/notes/716200 "}, {"RefNumber": "634007", "RefComponent": "BC-I18", "RefTitle": "rscpLengthOfBasicChar locks semaphore 12 too often", "RefUrl": "/notes/634007 "}, {"RefNumber": "775114", "RefComponent": "BC-I18", "RefTitle": "Problems during transport into a Unicode system", "RefUrl": "/notes/775114 "}, {"RefNumber": "741427", "RefComponent": "BC-I18", "RefTitle": "Short dump occured during sending an email.", "RefUrl": "/notes/741427 "}, {"RefNumber": "635461", "RefComponent": "BC-ABA-SC", "RefTitle": "Displayable characters and UTF8 DIAG protocol", "RefUrl": "/notes/635461 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SOFTWARE_PROVISIONING_MANAGER", "From": "1.0", "To": "1.0", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "4.6DEXT", "To": "4.6DEX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "6.20", "To": "6.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "6.20", "To": "6.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "4.6DEXT", "To": "4.6DEX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "6.20", "To": "6.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.38", "To": "7.38", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.43", "To": "7.43", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.44", "To": "7.44", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.45", "To": "7.45", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.46", "To": "7.46", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.47", "To": "7.47", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.48", "To": "7.48", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.50", "To": "7.50", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.51", "To": "7.51", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.52", "To": "7.52", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.70", "To": "7.70", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.71", "To": "7.71", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.72", "To": "7.72", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.73", "To": "7.73", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.74", "To": "7.74", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.75", "To": "7.75", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.76", "To": "7.76", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EX2", "To": "7.22EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.77", "To": "7.77", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "6.20", "To": "6.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.2L", "To": "7.2L", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.00", "To": "8.00", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.02", "To": "8.02", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.03", "To": "8.03", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.04", "To": "8.04", "Subsequent": "X"}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.38", "To": "7.38", "Subsequent": "X"}, {"SoftwareComponent": "KRNL64UC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.43", "To": "7.43", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.44", "To": "7.44", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.45", "To": "7.45", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.46", "To": "7.46", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.47", "To": "7.47", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.48", "To": "7.48", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.50", "To": "7.50", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.51", "To": "7.51", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.52", "To": "7.52", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.70", "To": "7.70", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.71", "To": "7.71", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.72", "To": "7.72", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.73", "To": "7.73", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.74", "To": "7.74", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.75", "To": "7.75", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.76", "To": "7.76", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EX2", "To": "7.22EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.77", "To": "7.77", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.78", "To": "7.78", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.79", "To": "7.79", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "800", "To": "804", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "750", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "764", "To": "764", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.21", "To": "7.22", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "8.04", "To": "8.04", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.38", "To": "7.38", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.41", "To": "7.41", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.42", "To": "7.42", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.43", "To": "7.43", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.44", "To": "7.44", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.45", "To": "7.45", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.46", "To": "7.46", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.47", "To": "7.47", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.48", "To": "7.48", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.49", "To": "7.49", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.50", "To": "7.50", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.51", "To": "7.51", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.52", "To": "7.52", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.73", "To": "7.73", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.74", "To": "7.74", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.75", "To": "7.75", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.76", "To": "7.76", "Subsequent": ""}, {"SoftwareComponent": "MMC", "From": "7.11", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "MMC", "From": "7.20", "To": "7.20", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70001", "URL": "/supportpackage/SAPKB70001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP333", "SupportPackagePatch": "000333", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP338", "SupportPackagePatch": "000338", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP347", "SupportPackagePatch": "000347", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP348", "SupportPackagePatch": "000348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP351", "SupportPackagePatch": "000351", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP253", "SupportPackagePatch": "000253", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP268", "SupportPackagePatch": "000268", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP276", "SupportPackagePatch": "000276", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP277", "SupportPackagePatch": "000277", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP333", "SupportPackagePatch": "000333", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP338", "SupportPackagePatch": "000338", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP347", "SupportPackagePatch": "000347", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP348", "SupportPackagePatch": "000348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP351", "SupportPackagePatch": "000351", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP333", "SupportPackagePatch": "000333", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP338", "SupportPackagePatch": "000338", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP347", "SupportPackagePatch": "000347", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP348", "SupportPackagePatch": "000348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP351", "SupportPackagePatch": "000351", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP333", "SupportPackagePatch": "000333", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP338", "SupportPackagePatch": "000338", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP347", "SupportPackagePatch": "000347", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP348", "SupportPackagePatch": "000348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP351", "SupportPackagePatch": "000351", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP253", "SupportPackagePatch": "000253", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP268", "SupportPackagePatch": "000268", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP276", "SupportPackagePatch": "000276", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP277", "SupportPackagePatch": "000277", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP253", "SupportPackagePatch": "000253", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP268", "SupportPackagePatch": "000268", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP276", "SupportPackagePatch": "000276", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP277", "SupportPackagePatch": "000277", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP253", "SupportPackagePatch": "000253", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP268", "SupportPackagePatch": "000268", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP276", "SupportPackagePatch": "000276", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP277", "SupportPackagePatch": "000277", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 8.02 64-BIT UNICODE", "SupportPackage": "SP041", "SupportPackagePatch": "000041", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200015012&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 8.03 64-BIT UNICODE", "SupportPackage": "SP021", "SupportPackagePatch": "000021", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200016159&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 8.04 64-BIT UNICODE", "SupportPackage": "SP008", "SupportPackagePatch": "000008", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200018078&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP382", "SupportPackagePatch": "000382", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP394", "SupportPackagePatch": "000394", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP405", "SupportPackagePatch": "000405", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP407", "SupportPackagePatch": "000407", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP408", "SupportPackagePatch": "000408", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP412", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP413", "SupportPackagePatch": "000413", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP423", "SupportPackagePatch": "000423", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000057", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP152", "SupportPackagePatch": "000152", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP168", "SupportPackagePatch": "000168", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP175", "SupportPackagePatch": "000175", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP181", "SupportPackagePatch": "000181", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP190", "SupportPackagePatch": "000190", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP192", "SupportPackagePatch": "000192", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000057", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP152", "SupportPackagePatch": "000152", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP168", "SupportPackagePatch": "000168", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP175", "SupportPackagePatch": "000175", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP181", "SupportPackagePatch": "000181", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP190", "SupportPackagePatch": "000190", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP192", "SupportPackagePatch": "000192", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000057", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP152", "SupportPackagePatch": "000152", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP168", "SupportPackagePatch": "000168", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP175", "SupportPackagePatch": "000175", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP181", "SupportPackagePatch": "000181", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP190", "SupportPackagePatch": "000190", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP192", "SupportPackagePatch": "000192", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000057", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP152", "SupportPackagePatch": "000152", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP168", "SupportPackagePatch": "000168", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP175", "SupportPackagePatch": "000175", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP181", "SupportPackagePatch": "000181", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP190", "SupportPackagePatch": "000190", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP192", "SupportPackagePatch": "000192", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP140", "SupportPackagePatch": "000140", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP151", "SupportPackagePatch": "000151", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP154", "SupportPackagePatch": "000154", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP140", "SupportPackagePatch": "000140", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP151", "SupportPackagePatch": "000151", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP154", "SupportPackagePatch": "000154", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP140", "SupportPackagePatch": "000140", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP151", "SupportPackagePatch": "000151", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP154", "SupportPackagePatch": "000154", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP140", "SupportPackagePatch": "000140", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP151", "SupportPackagePatch": "000151", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP154", "SupportPackagePatch": "000154", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP096", "SupportPackagePatch": "000096", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP097", "SupportPackagePatch": "000097", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP118", "SupportPackagePatch": "000118", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP210", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP312", "SupportPackagePatch": "000312", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP317", "SupportPackagePatch": "000317", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP320", "SupportPackagePatch": "000320", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP433", "SupportPackagePatch": "000433", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP096", "SupportPackagePatch": "000096", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP097", "SupportPackagePatch": "000097", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP118", "SupportPackagePatch": "000118", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP210", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP312", "SupportPackagePatch": "000312", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP317", "SupportPackagePatch": "000317", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP320", "SupportPackagePatch": "000320", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP433", "SupportPackagePatch": "000433", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP096", "SupportPackagePatch": "000096", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP097", "SupportPackagePatch": "000097", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP118", "SupportPackagePatch": "000118", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP210", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP312", "SupportPackagePatch": "000312", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP317", "SupportPackagePatch": "000317", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP320", "SupportPackagePatch": "000320", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP433", "SupportPackagePatch": "000433", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP096", "SupportPackagePatch": "000096", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP097", "SupportPackagePatch": "000097", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP118", "SupportPackagePatch": "000118", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP210", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP310", "SupportPackagePatch": "000310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP312", "SupportPackagePatch": "000312", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP317", "SupportPackagePatch": "000317", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP320", "SupportPackagePatch": "000320", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP433", "SupportPackagePatch": "000433", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP006", "SupportPackagePatch": "000006", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP006", "SupportPackagePatch": "000006", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP006", "SupportPackagePatch": "000006", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP006", "SupportPackagePatch": "000006", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP026", "SupportPackagePatch": "000026", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP028", "SupportPackagePatch": "000028", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP035", "SupportPackagePatch": "000035", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP123", "SupportPackagePatch": "000123", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.38 64-BIT UNICODE", "SupportPackage": "SP004", "SupportPackagePatch": "000004", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019649&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT UNICODE", "SupportPackage": "SP014", "SupportPackagePatch": "000014", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019652&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT UNICODE", "SupportPackage": "SP022", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019652&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP000", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023698&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023700&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.43 64-BIT UNICODE", "SupportPackage": "SP004", "SupportPackagePatch": "000004", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200023955&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP004", "SupportPackagePatch": "000004", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.47 64-BIT UNICODE", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200004074&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 2.0", "SupportPackage": "SP009", "SupportPackagePatch": "000004", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=73554900100200006091&support_package=SP009&patch_level=000004"}, {"SoftwareComponentVersion": "SAP KERNEL 7.47 64-BIT UNICODE", "SupportPackage": "SP008", "SupportPackagePatch": "000008", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200004074&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.48 64-BIT UNICODE", "SupportPackage": "SP008", "SupportPackagePatch": "000008", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200004573&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.43 64-BIT UNICODE", "SupportPackage": "SP007", "SupportPackagePatch": "000007", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200023955&V=MAINT"}, {"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 2.0", "SupportPackage": "SP010", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=73554900100200006091&support_package=SP010&patch_level=000000"}, {"SoftwareComponentVersion": "SAP KERNEL 7.46 64-BIT UNICODE", "SupportPackage": "SP016", "SupportPackagePatch": "000016", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200003922&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.48 64-BIT UNICODE", "SupportPackage": "SP018", "SupportPackagePatch": "000018", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200004573&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.47 64-BIT UNICODE", "SupportPackage": "SP019", "SupportPackagePatch": "000019", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200004074&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.46 64-BIT UNICODE", "SupportPackage": "SP021", "SupportPackagePatch": "000021", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200003922&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "", "SupportPackagePatch": "000115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "", "SupportPackagePatch": "000626", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000412", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT", "SupportPackage": "SP210", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023698&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT UNICODE", "SupportPackage": "SP210", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023700&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP029", "SupportPackagePatch": "000029", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP029", "SupportPackagePatch": "000029", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP029", "SupportPackagePatch": "000029", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP029", "SupportPackagePatch": "000029", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP111", "SupportPackagePatch": "000111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP111", "SupportPackagePatch": "000111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP032", "SupportPackagePatch": "000032", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP032", "SupportPackagePatch": "000032", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.46 64-BIT UNICODE", "SupportPackage": "SP032", "SupportPackagePatch": "000032", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200003922&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP410", "SupportPackagePatch": "000410", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP212", "SupportPackagePatch": "000212", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP212", "SupportPackagePatch": "000212", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP810", "SupportPackagePatch": "000810", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP810", "SupportPackagePatch": "000810", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP221", "SupportPackagePatch": "000221", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.44 64-BIT UNICODE", "SupportPackage": "SP038", "SupportPackagePatch": "000038", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001667&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP621", "SupportPackagePatch": "000621", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP411", "SupportPackagePatch": "000411", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP319", "SupportPackagePatch": "000319", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP319", "SupportPackagePatch": "000319", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP620", "SupportPackagePatch": "000620", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP620", "SupportPackagePatch": "000620", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP725", "SupportPackagePatch": "000725", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP725", "SupportPackagePatch": "000725", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP442", "SupportPackagePatch": "000442", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP442", "SupportPackagePatch": "000442", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}