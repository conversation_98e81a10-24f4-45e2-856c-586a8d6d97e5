{"Request": {"Number": "1076804", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 331, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016344862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001076804?language=E&token=C188450C3B356B6307BF6B561A2ABFA5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001076804", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001076804/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1076804"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "HotNews"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.04.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-UP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Update"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Update", "value": "BC-CST-UP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-UP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1076804 - Incomplete update records"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Update requests are incompletely executed. The tables VBMOD and VBDATA contain entries that also exist in the table VBHDR, but with a different VBKEY.<br /><br />Among other things, this may become apparent when posting documents are missing or when they are incomplete even though these documents are displayed as posted when the user executes a transaction.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>VBMOD, VBDATA, SM13, update<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>As a result of an error in the kernel, the VBKEY changes during an SAP LUW, which means that the entries in the tables VBMOD, VBDATA and VBHDR are written with different values for VBKEY. As a result, only some of the function modules that belong to the update request are executed.<br /><br />This error can occur only up to the following kernel patch numbers:</p> <UL><LI>4.6D: 2334</LI></UL> <UL><LI>6.40:&#x00A0;&#x00A0;193</LI></UL> <UL><LI>7.00:&#x00A0;&#x00A0;120</LI></UL> <UL><LI>7.10:.. 65</LI></UL> <p><br />In addition, this problem can occur only if a trace (for example, an authorization trace or an SQL trace, but not a developer trace) is or was activated in the system.<br /><br />To check whether your system has been affected, use the report attached to Note 1280546. If this report detects orphans, create a message under component BC-CST-UP and attach the output of the report this message.<br /><br />Note the following: When you use an Oracle database, the same symptoms may also occur due to the problem described in Note 1107700. Note 1132350 describes a problem with the same symptom. Follow the measures describes there. A detailed description of the reasons and prerequisites is contained in Note 1107700. Another error is described in Note 1150058.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The error in the kernel that is described in this note is corrected with the following patch levels:<br /><br />4.6D: 2335<br />6.40:&#x00A0;&#x00A0;194<br />7.00:&#x00A0;&#x00A0;121<br />7.10:&#x00A0;&#x00A0; 66<br /><br />If you are using a lower patch level, you must import the current kernel patch. Until you do this, you can and should ensure that no trace is activated in your system. Since this trace is activated by transactions ST01 (system trace) and ST05 (performance analysis), you should ensure that nobody executes these transactions.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023620)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023620)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001076804/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001076804/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001076804/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001076804/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001076804/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001076804/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001076804/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001076804/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001076804/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "972262", "RefComponent": "BC-INS-UNX", "RefTitle": "OBSOLETE: Inst.NW 7.0(2004s)SR2/Business Suite 2005 SR2-UNIX", "RefUrl": "/notes/972262"}, {"RefNumber": "819641", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB performance", "RefUrl": "/notes/819641"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1594405", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-SER 2010_1: Maintenance of SAP Kernel issues", "RefUrl": "/notes/1594405"}, {"RefNumber": "1280546", "RefComponent": "BC-CST-UP", "RefTitle": "Shadow tables in update", "RefUrl": "/notes/1280546"}, {"RefNumber": "1223994", "RefComponent": "BC-CST-UP", "RefTitle": "Composite SAP note: Incomplete update records", "RefUrl": "/notes/1223994"}, {"RefNumber": "1177859", "RefComponent": "IS-H", "RefTitle": "IS-H: Inconsistencies due to incomplete update requests", "RefUrl": "/notes/1177859"}, {"RefNumber": "1150058", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1150058"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}, {"RefNumber": "1143576", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "SM13 Canceled update requests with <OBJECT>_WRITE_DOCUMENT", "RefUrl": "/notes/1143576"}, {"RefNumber": "1132350", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1132350"}, {"RefNumber": "1107700", "RefComponent": "BC-DB-ORA", "RefTitle": "10.2.0.2: Execution of SQL statements in wrong schema", "RefUrl": "/notes/1107700"}, {"RefNumber": "1076241", "RefComponent": "BC-CST", "RefTitle": "CST patch collection 29 2007", "RefUrl": "/notes/1076241"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "972262", "RefComponent": "BC-INS-UNX", "RefTitle": "OBSOLETE: Inst.NW 7.0(2004s)SR2/Business Suite 2005 SR2-UNIX", "RefUrl": "/notes/972262 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1594405", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-SER 2010_1: Maintenance of SAP Kernel issues", "RefUrl": "/notes/1594405 "}, {"RefNumber": "819641", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB performance", "RefUrl": "/notes/819641 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "1223994", "RefComponent": "BC-CST-UP", "RefTitle": "Composite SAP note: Incomplete update records", "RefUrl": "/notes/1223994 "}, {"RefNumber": "1116400", "RefComponent": "SD-SLS", "RefTitle": "Sales documents are not or only partially updated", "RefUrl": "/notes/1116400 "}, {"RefNumber": "1508039", "RefComponent": "PSM-FM-BCS-AC", "RefTitle": "Report to detect FMAVCT inconsistencies", "RefUrl": "/notes/1508039 "}, {"RefNumber": "1280546", "RefComponent": "BC-CST-UP", "RefTitle": "Shadow tables in update", "RefUrl": "/notes/1280546 "}, {"RefNumber": "1132350", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1132350 "}, {"RefNumber": "1143576", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "SM13 Canceled update requests with <OBJECT>_WRITE_DOCUMENT", "RefUrl": "/notes/1143576 "}, {"RefNumber": "1177859", "RefComponent": "IS-H", "RefTitle": "IS-H: Inconsistencies due to incomplete update requests", "RefUrl": "/notes/1177859 "}, {"RefNumber": "1150058", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1150058 "}, {"RefNumber": "1076241", "RefComponent": "BC-CST", "RefTitle": "CST patch collection 29 2007", "RefUrl": "/notes/1076241 "}, {"RefNumber": "1107700", "RefComponent": "BC-DB-ORA", "RefTitle": "10.2.0.2: Execution of SQL statements in wrong schema", "RefUrl": "/notes/1107700 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}