{"Request": {"Number": "1307871", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 462, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007683962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001307871?language=E&token=F6B215E8ED5EC791FBE3D45AEF0EAD97"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001307871", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001307871/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1307871"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.02.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DST-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorizations"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Staging", "value": "BW-WHM-DST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorizations", "value": "BW-WHM-DST-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1307871 - DTP Extraction: No Authorization when no KeyFigures"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Authorization check of a DTP extraction fails when no KeyFigures get extracted.<br /><br />Error: When the DTP extraction tries to extract no KeyFigures at all, then the Authorization Check checks not on <B>no</B> KeyFigures, but <B>all(!)</B> KeyFigures.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RS_EXCEPTION 301 RSBK 242 Error while extracting from source &lt;xyz&gt; (type &lt;abc&gt;) LRSDRC_SERVICESF03 SAPLRSDRC_SERVICES DBMAN 305 Error<br />reading the data of InfoProvider &lt;xyz&gt;<br /> </p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Normally in a DTP extraction some KeyFigures are extracted. Then the Authorization Check checks for exactly these KeyFigures.<br />Error: When the DTP extraction tries to extract no KeyFigures at all, then the check is not on no KeyFigures, but all(!) KeyFigures.<br /><br />The Authorization Protocol shows for the technical characteristic 0TCAKYFNM (=KeyFigures) a check \"0TCAKYFNM LIKE *\". This check is not authorized in most cases.<br />See note 1234567 for detailes about the Authorization Protocol.<br /><br />This note is only relevant if no KeyFigures are extracted by DTP.<br /><br />Please also check note 1291204 on possible issues with DTP authorizations.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Workaround 1: If the Business scenario allows, the extracting user should get a full '*' authorization for KeyFigures (0TCAKYFNM).<br /><br />Workaround 2: Change the DTP extraction in such a way that at least one authorized KeyFigure gets extracted.<br /><br />After applying the correction, the check does (correctly) <B>not</B> check for any KeyFigure Authorizations when <B>no</B> KeyFigures are extracted by DTP.<br /><br /></p> <UL><LI>SAP NetWeaver BI 7.00</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 21 for SAP NetWeaver BI 7. 00(SAPKW70021) into your BI system. The Support Package will be available when <B>note 1270629</B> with the short text \"SAPBINews NW BI 7.0 ABAP SP21\", describing this Support Package in more detail, is released for customers.</p> <UL><LI>SAP NetWeaver BI 7.01 (SAP NW BI7.0 EnhP 1)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 04 for SAP NetWeaver BI 7. 01 (SAPKW70104) into your BI system. The Support Package will be available when <B>note 1227876</B> with the short text \"SAPBINews NW7.01 BI ABAP SP04\", describing this Support Package in more detail, is released for customers.<br /></p> <UL><LI>SAP NetWeaver BI 7.10</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 08 for SAP NetWeaver BI 7. 10 BI (SAPKW71008) into your BI system. The Support Package will be available when <B>note 1260071</B> with the short text \"SAPBINews NW 7.10 BI SP08\", describing this Support Package in more detail, is released for customers.</p> <UL><LI>SAP NetWeaver BI 7.11</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 02 for SAP NetWeaver BI 7. 11 (SAPKW71102) into your BI system. The Support Package will be available when <B>note 1260072</B> with the short text \"SAPBINews NW7.11 BI SP02\", describing this Support Package in more detail, is released for customers.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><br />In urgent cases the correction instructions can be used.<br /><br />Beforehand please definitely check the note 875986 for transaction SNOTE.<br /><br />This note may already be available before the Support Package is released. However, the short text will still contain the words \"preliminary version\" in this case.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-OLAP-AUT (Authorizations)"}, {"Key": "Other Components", "Value": "BW-WHM-DST-DTP (Data Transfer Process)"}, {"Key": "DownPort/UpPort-WF", "Value": "UpPort check necessary"}, {"Key": "Responsible                                                                                         ", "Value": "D031457"}, {"Key": "Processor                                                                                           ", "Value": "D031457"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001307871/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001307871/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1291204", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Loading with DTP: Failed authorization check on 0BI_ALL", "RefUrl": "/notes/1291204"}, {"RefNumber": "1270629", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 21", "RefUrl": "/notes/1270629"}, {"RefNumber": "1260072", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.11 ABAP SP 02", "RefUrl": "/notes/1260072"}, {"RefNumber": "1260071", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.10 ABAP SP 08", "RefUrl": "/notes/1260071"}, {"RefNumber": "1234567", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The authorization log RSECADMIN", "RefUrl": "/notes/1234567"}, {"RefNumber": "1227876", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 04", "RefUrl": "/notes/1227876"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1260071", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.10 ABAP SP 08", "RefUrl": "/notes/1260071 "}, {"RefNumber": "1260072", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.11 ABAP SP 02", "RefUrl": "/notes/1260072 "}, {"RefNumber": "1270629", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 21", "RefUrl": "/notes/1270629 "}, {"RefNumber": "1227876", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 04", "RefUrl": "/notes/1227876 "}, {"RefNumber": "1234567", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The authorization log RSECADMIN", "RefUrl": "/notes/1234567 "}, {"RefNumber": "1291204", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Loading with DTP: Failed authorization check on 0BI_ALL", "RefUrl": "/notes/1291204 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "711", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70021", "URL": "/supportpackage/SAPKW70021"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 701", "SupportPackage": "SAPK-70105INVCBWTECH", "URL": "/supportpackage/SAPK-70105INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70104", "URL": "/supportpackage/SAPKW70104"}, {"SoftwareComponentVersion": "SAP_BW 710", "SupportPackage": "SAPKW71008", "URL": "/supportpackage/SAPKW71008"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71103", "URL": "/supportpackage/SAPKW71103"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 3, "URL": "/corrins/0001307871/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}