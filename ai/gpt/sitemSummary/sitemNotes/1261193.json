{"Request": {"Number": "1261193", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 481, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016644752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001261193?language=E&token=24694129FF4E907668823AEF95904A68"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001261193", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1261193"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.10.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorization"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorization", "value": "BC-SEC-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1261193 - Composite SAP Note : HCM Authorizations Documentation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You would like to have a consolidated view of must read OSS notes when<br />deealing with HCM Authorizations. This list is not exhaustive and contains useful notes. It is provided as a courtesy.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>HCM Authorizations Security Personnel Administration Personnel Development</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Documentation.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Read the documentation listed in this note.<br />Attend standard SAP class HR940 - SAP HCM Authorizations for HCM for hands-on experience.<br /><br />List of related transaction codes for HCM Authorizations (non exhaustive, for information purpose only)<br /><br />SU01 = User Administration<br />SU01D = Display User Administration<br />SU3 = Maintain User profile (incl. parameters)<br />PFCG = Profile Configurator (as of release 4.6)<br />SU02 = Profile Creation - Manual process (prior release 4.6)<br />SU03 = List of authorization objects<br />SU53 = Authorization checks (displays missing authorization in the profile)<br />HRUSER = Check user mapping between SAP userid and personnel number (through infotype 0105 - Communication - Subtype 0001)<br />SUIM = Authorization Reporting Tree<br />OOAC = Enable AUTSW switches in table T77S0<br />SE93 = Maintain transaction codes<br />SE37 = Function modules<br />OOSP = Display Personnel Development Profiles<br />SLG1 = Application Troubleshooting<br />SU56 = Authorization buffer for end user<br />SU21 = Create specific authorization object<br /><br />List of related transaction codes for HCM when dealing with authorizations (non exhaustive, for information purpose only)<br /><br />PPMDT = Manager Desktop<br />SP01  = Spool Request<br />SU3  = Maintain own user paramaters<br />SM31  = Access Table<br />PA30  = Maintain HR Master Data<br />PE03  = HR Features (Decision Trees) like PINCH<br />KS03  = Display Cost Center<br />PPOME = Maintain Org. Structure (new interface)<br />PPOM_OLD = Maintain Org. Structure ('old' interface)<br />PTARQ = Test Leave Request (includes a program to identify the line manager).<br />SWU_OBUF = Refresh Org. Management Buffer<br />SE18  = Business Add In's (BAdI's)<br /><br />List of tables<br />T77S0 = HR Switches main tables - group AUTSW regarding authorizations<br />USERS_SSM = Deactivate SAP standard menu and force user menus<br />V_T591C = Enable long field (241) form infotype 0105 - Communication<br />TRESC = Allowed naming space lie Z* or Y* convention for SAP<br />tables<br />V_T582A = Infotypes attributes<br />T526  = Administrators<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PA-PA-XX (General Parts)"}, {"Key": "Other Components", "Value": "PA-BC (Authorization and HCM Basis)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5076882)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5076882)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261193/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "959358", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS HTTP administration is not possible", "RefUrl": "/notes/959358"}, {"RefNumber": "944221", "RefComponent": "BC-SRV-FP", "RefTitle": "Error analysis for problems in form processing", "RefUrl": "/notes/944221"}, {"RefNumber": "939412", "RefComponent": "CA-ESS-WD", "RefTitle": "Missing Permissions for the Enduser : Everyone", "RefUrl": "/notes/939412"}, {"RefNumber": "935354", "RefComponent": "PA-BC", "RefTitle": "Authorization error in transaction PA20 or PA30", "RefUrl": "/notes/935354"}, {"RefNumber": "93254", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC short dump RFC_NO_AUTHORITY", "RefUrl": "/notes/93254"}, {"RefNumber": "902000", "RefComponent": "PA-BC", "RefTitle": "Analyzing HR authorizations", "RefUrl": "/notes/902000"}, {"RefNumber": "87861", "RefComponent": "BC-BMT-OM", "RefTitle": "Log function for infotypes of personnel planning", "RefUrl": "/notes/87861"}, {"RefNumber": "853878", "RefComponent": "BC-MID-ICF", "RefTitle": "HTTP WhiteList Check (security)", "RefUrl": "/notes/853878"}, {"RefNumber": "836478", "RefComponent": "PA-BC", "RefTitle": "HR authorizations: Displaying the data in the INDX", "RefUrl": "/notes/836478"}, {"RefNumber": "820317", "RefComponent": "CO-OM", "RefTitle": "CO-OM tools: SE16N as a display transaction with views", "RefUrl": "/notes/820317"}, {"RefNumber": "820183", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "New authorization concept in BI", "RefUrl": "/notes/820183"}, {"RefNumber": "798967", "RefComponent": "EP-PCT-MGR-HR", "RefTitle": "MSS: Authorizations and roles for WD services in ERP 2004", "RefUrl": "/notes/798967"}, {"RefNumber": "785345", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Copying authorization default values for services", "RefUrl": "/notes/785345"}, {"RefNumber": "723236", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/723236"}, {"RefNumber": "704604", "RefComponent": "BC-WD-JAV", "RefTitle": "Business graphics, GeoMaps are not displayed in WD Java", "RefUrl": "/notes/704604"}, {"RefNumber": "66056", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/66056"}, {"RefNumber": "628962", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "JCo: Java Connector tracing in SAP NetWeaver AS Java 7.50", "RefUrl": "/notes/628962"}, {"RefNumber": "612585", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "New: Authorization default values for ext. services", "RefUrl": "/notes/612585"}, {"RefNumber": "597117", "RefComponent": "CO-OM", "RefTitle": "CO-OM tools: SE16N as pure display transaction II", "RefUrl": "/notes/597117"}, {"RefNumber": "570161", "RefComponent": "PA-BC", "RefTitle": "BAdI: Time logic in PA authorization check", "RefUrl": "/notes/570161"}, {"RefNumber": "537138", "RefComponent": "PY-XX-TL", "RefTitle": "Deactivating RPUDEL20 and other reports in the production system", "RefUrl": "/notes/537138"}, {"RefNumber": "514841", "RefComponent": "BC-FES-IGS", "RefTitle": "Troubleshooting when a problem occurs with the IGS", "RefUrl": "/notes/514841"}, {"RefNumber": "503274", "RefComponent": "CO-OM", "RefTitle": "CO-OM tools: SE16N as pure display transaction", "RefUrl": "/notes/503274"}, {"RefNumber": "496993", "RefComponent": "PT-SP", "RefTitle": "Authorization objects of shift planning", "RefUrl": "/notes/496993"}, {"RefNumber": "452904", "RefComponent": "BC-SEC-AUT", "RefTitle": "Loss of authorization after profile generation", "RefUrl": "/notes/452904"}, {"RefNumber": "44206", "RefComponent": "PA-PA-XX-BS-EV", "RefTitle": "Creation of infotype change documents when changing date", "RefUrl": "/notes/44206"}, {"RefNumber": "385635", "RefComponent": "PA-BC", "RefTitle": "Authorization check with employee subgroup change", "RefUrl": "/notes/385635"}, {"RefNumber": "30724", "RefComponent": "BC-SEC", "RefTitle": "Data protection and security in SAP Systems", "RefUrl": "/notes/30724"}, {"RefNumber": "26909", "RefComponent": "BC-DWB-UTL-BRD", "RefTitle": "SE16 - Security", "RefUrl": "/notes/26909"}, {"RefNumber": "216036", "RefComponent": "CA-ESS-ITS", "RefTitle": "ESS and Authorizations", "RefUrl": "/notes/216036"}, {"RefNumber": "155650", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization problems", "RefUrl": "/notes/155650"}, {"RefNumber": "148495", "RefComponent": "PA-PA-XX", "RefTitle": "Setting main authorization switches", "RefUrl": "/notes/148495"}, {"RefNumber": "139418", "RefComponent": "BC-SEC", "RefTitle": "Logging of user actions (ABAP server)", "RefUrl": "/notes/139418"}, {"RefNumber": "138706", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization problems, analysis preparations", "RefUrl": "/notes/138706"}, {"RefNumber": "138526", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization check in reports incorrect", "RefUrl": "/notes/138526"}, {"RefNumber": "13202", "RefComponent": "BC-ABA-LA", "RefTitle": "Security aspects in ABAP programming", "RefUrl": "/notes/13202"}, {"RefNumber": "130035", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization check with P_PERNR incorrect.", "RefUrl": "/notes/130035"}, {"RefNumber": "124949", "RefComponent": "PA-PA", "RefTitle": "Logical database SAPDBPNP does not check P_PERNR", "RefUrl": "/notes/124949"}, {"RefNumber": "1241162", "RefComponent": "BC-BMT-OM", "RefTitle": "RHCDOC_DISPLAY in Background: Dump RAISE_EXCEPTION", "RefUrl": "/notes/1241162"}, {"RefNumber": "1228518", "RefComponent": "PA-PA-XX", "RefTitle": "RPUAUD00: Enhancement of the documentation (field \"Action\")", "RefUrl": "/notes/1228518"}, {"RefNumber": "1226183", "RefComponent": "PA-PA-XX", "RefTitle": "HR Admin: No authorization when Infotype 0001 is created", "RefUrl": "/notes/1226183"}, {"RefNumber": "1154828", "RefComponent": "BC-SEC", "RefTitle": "Cross-site scripting (XSS) attack using control sequences", "RefUrl": "/notes/1154828"}, {"RefNumber": "1154323", "RefComponent": "BC-CST-LL", "RefTitle": "Authorization trace of applications from the portal", "RefUrl": "/notes/1154323"}, {"RefNumber": "1136041", "RefComponent": "PA-EC-AD", "RefTitle": "Compensation Planning: incorrect authorization messages", "RefUrl": "/notes/1136041"}, {"RefNumber": "1126052", "RefComponent": "PA-PA-XX", "RefTitle": "Additional check triggers error message PG038", "RefUrl": "/notes/1126052"}, {"RefNumber": "1116744", "RefComponent": "PA-PA-XX", "RefTitle": "Additional check for retroactive change of entry date", "RefUrl": "/notes/1116744"}, {"RefNumber": "1115700", "RefComponent": "CO-OM", "RefTitle": "CO-OM Tools: SE16N: Authorization logic", "RefUrl": "/notes/1115700"}, {"RefNumber": "1109298", "RefComponent": "BC-SRV-NUM", "RefTitle": "SNUM: You are not authorized to use this function", "RefUrl": "/notes/1109298"}, {"RefNumber": "1067396", "RefComponent": "BC-BMT-OM", "RefTitle": "RHCDOC_DISPLAY: Runtime error \"DATA_LENGTH_TOO_LARGE\"", "RefUrl": "/notes/1067396"}, {"RefNumber": "1052242", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BI Analysis authorization: Generation", "RefUrl": "/notes/1052242"}, {"RefNumber": "1037230", "RefComponent": "BC-SRV-NUM", "RefTitle": "Authorization check not as documented", "RefUrl": "/notes/1037230"}, {"RefNumber": "1033149", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "General improvements: Transactions SU53 and SU56", "RefUrl": "/notes/1033149"}, {"RefNumber": "1012708", "RefComponent": "PA-PA-XX-CE", "RefTitle": "PA40: Message \"Person is already being processed by user\"", "RefUrl": "/notes/1012708"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "93254", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC short dump RFC_NO_AUTHORITY", "RefUrl": "/notes/93254 "}, {"RefNumber": "944221", "RefComponent": "BC-SRV-FP", "RefTitle": "Error analysis for problems in form processing", "RefUrl": "/notes/944221 "}, {"RefNumber": "704604", "RefComponent": "BC-WD-JAV", "RefTitle": "Business graphics, GeoMaps are not displayed in WD Java", "RefUrl": "/notes/704604 "}, {"RefNumber": "503274", "RefComponent": "CO-OM", "RefTitle": "CO-OM tools: SE16N as pure display transaction", "RefUrl": "/notes/503274 "}, {"RefNumber": "44206", "RefComponent": "PA-PA-XX-BS-EV", "RefTitle": "Creation of infotype change documents when changing date", "RefUrl": "/notes/44206 "}, {"RefNumber": "1109298", "RefComponent": "BC-SRV-NUM", "RefTitle": "SNUM: You are not authorized to use this function", "RefUrl": "/notes/1109298 "}, {"RefNumber": "1037230", "RefComponent": "BC-SRV-NUM", "RefTitle": "Authorization check not as documented", "RefUrl": "/notes/1037230 "}, {"RefNumber": "514841", "RefComponent": "BC-FES-IGS", "RefTitle": "Troubleshooting when a problem occurs with the IGS", "RefUrl": "/notes/514841 "}, {"RefNumber": "26909", "RefComponent": "BC-DWB-UTL-BRD", "RefTitle": "SE16 - Security", "RefUrl": "/notes/26909 "}, {"RefNumber": "820317", "RefComponent": "CO-OM", "RefTitle": "CO-OM tools: SE16N as a display transaction with views", "RefUrl": "/notes/820317 "}, {"RefNumber": "1154323", "RefComponent": "BC-CST-LL", "RefTitle": "Authorization trace of applications from the portal", "RefUrl": "/notes/1154323 "}, {"RefNumber": "612585", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "New: Authorization default values for ext. services", "RefUrl": "/notes/612585 "}, {"RefNumber": "30724", "RefComponent": "BC-SEC", "RefTitle": "Data protection and security in SAP Systems", "RefUrl": "/notes/30724 "}, {"RefNumber": "939412", "RefComponent": "CA-ESS-WD", "RefTitle": "Missing Permissions for the Enduser : Everyone", "RefUrl": "/notes/939412 "}, {"RefNumber": "1226183", "RefComponent": "PA-PA-XX", "RefTitle": "HR Admin: No authorization when Infotype 0001 is created", "RefUrl": "/notes/1226183 "}, {"RefNumber": "1228518", "RefComponent": "PA-PA-XX", "RefTitle": "RPUAUD00: Enhancement of the documentation (field \"Action\")", "RefUrl": "/notes/1228518 "}, {"RefNumber": "820183", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "New authorization concept in BI", "RefUrl": "/notes/820183 "}, {"RefNumber": "1136041", "RefComponent": "PA-EC-AD", "RefTitle": "Compensation Planning: incorrect authorization messages", "RefUrl": "/notes/1136041 "}, {"RefNumber": "1241162", "RefComponent": "BC-BMT-OM", "RefTitle": "RHCDOC_DISPLAY in Background: Dump RAISE_EXCEPTION", "RefUrl": "/notes/1241162 "}, {"RefNumber": "836478", "RefComponent": "PA-BC", "RefTitle": "HR authorizations: Displaying the data in the INDX", "RefUrl": "/notes/836478 "}, {"RefNumber": "452904", "RefComponent": "BC-SEC-AUT", "RefTitle": "Loss of authorization after profile generation", "RefUrl": "/notes/452904 "}, {"RefNumber": "1052242", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BI Analysis authorization: Generation", "RefUrl": "/notes/1052242 "}, {"RefNumber": "1033149", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "General improvements: Transactions SU53 and SU56", "RefUrl": "/notes/1033149 "}, {"RefNumber": "1116744", "RefComponent": "PA-PA-XX", "RefTitle": "Additional check for retroactive change of entry date", "RefUrl": "/notes/1116744 "}, {"RefNumber": "1126052", "RefComponent": "PA-PA-XX", "RefTitle": "Additional check triggers error message PG038", "RefUrl": "/notes/1126052 "}, {"RefNumber": "216036", "RefComponent": "CA-ESS-ITS", "RefTitle": "ESS and Authorizations", "RefUrl": "/notes/216036 "}, {"RefNumber": "902000", "RefComponent": "PA-BC", "RefTitle": "Analyzing HR authorizations", "RefUrl": "/notes/902000 "}, {"RefNumber": "537138", "RefComponent": "PY-XX-TL", "RefTitle": "Deactivating RPUDEL20 and other reports in the production system", "RefUrl": "/notes/537138 "}, {"RefNumber": "1067396", "RefComponent": "BC-BMT-OM", "RefTitle": "RHCDOC_DISPLAY: Runtime error \"DATA_LENGTH_TOO_LARGE\"", "RefUrl": "/notes/1067396 "}, {"RefNumber": "798967", "RefComponent": "EP-PCT-MGR-HR", "RefTitle": "MSS: Authorizations and roles for WD services in ERP 2004", "RefUrl": "/notes/798967 "}, {"RefNumber": "1012708", "RefComponent": "PA-PA-XX-CE", "RefTitle": "PA40: Message \"Person is already being processed by user\"", "RefUrl": "/notes/1012708 "}, {"RefNumber": "935354", "RefComponent": "PA-BC", "RefTitle": "Authorization error in transaction PA20 or PA30", "RefUrl": "/notes/935354 "}, {"RefNumber": "785345", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Copying authorization default values for services", "RefUrl": "/notes/785345 "}, {"RefNumber": "570161", "RefComponent": "PA-BC", "RefTitle": "BAdI: Time logic in PA authorization check", "RefUrl": "/notes/570161 "}, {"RefNumber": "13202", "RefComponent": "BC-ABA-LA", "RefTitle": "Security aspects in ABAP programming", "RefUrl": "/notes/13202 "}, {"RefNumber": "87861", "RefComponent": "BC-BMT-OM", "RefTitle": "Log function for infotypes of personnel planning", "RefUrl": "/notes/87861 "}, {"RefNumber": "597117", "RefComponent": "CO-OM", "RefTitle": "CO-OM tools: SE16N as pure display transaction II", "RefUrl": "/notes/597117 "}, {"RefNumber": "385635", "RefComponent": "PA-BC", "RefTitle": "Authorization check with employee subgroup change", "RefUrl": "/notes/385635 "}, {"RefNumber": "496993", "RefComponent": "PT-SP", "RefTitle": "Authorization objects of shift planning", "RefUrl": "/notes/496993 "}, {"RefNumber": "130035", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization check with P_PERNR incorrect.", "RefUrl": "/notes/130035 "}, {"RefNumber": "138526", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization check in reports incorrect", "RefUrl": "/notes/138526 "}, {"RefNumber": "124949", "RefComponent": "PA-PA", "RefTitle": "Logical database SAPDBPNP does not check P_PERNR", "RefUrl": "/notes/124949 "}, {"RefNumber": "155650", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization problems", "RefUrl": "/notes/155650 "}, {"RefNumber": "139418", "RefComponent": "BC-SEC", "RefTitle": "Logging of user actions (ABAP server)", "RefUrl": "/notes/139418 "}, {"RefNumber": "148495", "RefComponent": "PA-PA-XX", "RefTitle": "Setting main authorization switches", "RefUrl": "/notes/148495 "}, {"RefNumber": "138706", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization problems, analysis preparations", "RefUrl": "/notes/138706 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}