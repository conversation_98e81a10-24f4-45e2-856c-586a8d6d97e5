{"Request": {"Number": "1083533", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 822, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016353862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001083533?language=E&token=45ECBD1940CB2FBD25EBC36E61E9EF13"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001083533", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001083533/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1083533"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 36}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.06.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1083533 - Installation of Enhancement Package 3 on SAP ERP 6.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use transaction SAINT to update and enhance existing software components using Enhancement Package 3 for the SAP ERP Central Component (referred to here as SAP ECC 600).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Updating and enhancing existing software components,<br />Enhancement package, CD51033040, SAINT, 603, SAPK-603DHIN<PERSON><PERSON><PERSON>, SAPK-603DHINEADFPS, SAPK-603DHINEAFINSRV<br />SAPK-603DHINEAGLTRAD, SAPK-603DHIN<PERSON>H<PERSON>, SAPK-603<PERSON><PERSON><PERSON><PERSON><PERSON>, SAPK-603DHINEARETAIL, SAPK-603DHINECCDIMP, SAPK-60300INECCVPACK, SAPK-603DIINERECRUIT, SAPK-603<PERSON><PERSON><PERSON><PERSON><PERSON>, SAPK-603D<PERSON><PERSON>FICAX, SAPK-603<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SAPK-603<PERSON><PERSON><PERSON><PERSON><PERSON>RANC, SAPK-603D<PERSON><PERSON>ISCW<PERSON>, SAPK-603<PERSON><PERSON><PERSON>ISH, SAPK-603DHINISM, SAPK-603DHINISOIL, SAPK-603DHINISPSCA, SAPK-603DHINISUT, SAPK-603DIINLSOFE, SAPK-603DHINSAPAPPL, SAPK-603DIINSEMBW, SAPK-603AIINERECRUIT, SAPK-603AIINFINBASIS, SAPK-603AIINLSOFE, SAPK-603AIINSEMBW<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You plan an update and enhancement of existing software components (this is referred to here as \"installation\").</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>This note is updated on a regular basis. Make sure you have the current version of this note before you start the installation.</B><br /><br />Contents<br />&#x00A0;&#x00A0;1. Change history<br />&#x00A0;&#x00A0;2. Prerequisites for the installation<br />&#x00A0;&#x00A0;3. Preparing for the installation<br />&#x00A0;&#x00A0;4. Executing the installation<br />&#x00A0;&#x00A0;5. Errors during the installation<br />&#x00A0;&#x00A0;6. After the installation<br />&#x00A0;&#x00A0;7. Language support<br />&#x00A0;&#x00A0;8. Passwords<br /></p> <b>1. Change history</b><br /> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Topic</TH><TH ALIGN=LEFT> Short Description</TH></TR> <TR><TD>29.07.2008</TD><TD> 2.</TD><TD> Release EHP3 with HR-CEE</TD></TR> <TR><TD>03.06.2008</TD><TD> 5.</TD><TD> Conflict check with KAYAK 120</TD></TR> <TR><TD>08.04.2008</TD><TD> 2.</TD><TD> Release EHP3 with CEEISUT and CEERE</TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> </TABLE> <b>2. Prerequisites for the installation</b><br /> <UL><LI>Uninstalling is not possible.<br />Before you install the update and enhancement of existing components from Enhancement Package 3, note that you cannot uninstall an Enhancement Package.</LI></UL> <UL><LI>Component selection<br />Before the installation, check which components of Enhancement Package 3 you require. The Maintenance Optimizer supports you here with the \"Technical Usages\" selection option (useful bundling of EHP components). For more information about selecting EHP components, see Note 1083576 (SAP enhancement package 3 for SAP ERP 6.0 - required SWC).</LI></UL> <UL><LI>Required release:<br />You require SAP ERP Central Component 600 (SAP ECC 600). You can install the components FINBASIS, SEM-BW, LSOFE and ERECRUIT on SAP NetWeaver 7.0.</LI></UL> <UL><LI><B>Compatibility of add-ons and Enhancement Packages</B><br /><B>Note 1117309 provides information about the released combinations of add-ons for ERP and ERP enhancement packages.</B></LI></UL> <UL><UL><LI>The add-on HR-CEE 110_600 was retroactively declared to be compatible with the component EA-HR 603. If you have installed this add-on, download the attached ACP (ACP_EAHR603.SAR) before you install EA-HR 603.</LI></UL></UL> <UL><UL><LI>Retroactively, the add-on CEEISUT 600 was declared to be compatible with the component IS-UT 603. If you have installed this add-on, download the attached ACP (ACP_ISUT603.SAR) before you install IS-UT 603.</LI></UL></UL> <UL><UL><LI>The add-on CEERE 110_600 was retroactively declared to be compatible with the component SAP_APPL 603. If you have installed this add-on, download the attached ACP (ACP_SAPAPPL603.SAR) before you install SAP_APPL 603.</LI></UL></UL> <UL><UL><LI>In the Support Package Manager (transaction SPAM), you can then use the function \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Front End\" to load this package into the system, where it is processed automatically.<br />See Note 1119856 for further information about ACPs.</LI></UL></UL><UL><LI>Import the latest SPAM/SAINT update.<br />Make sure that you have installed the latest SPAM/SAINT update on your system. If a newer version is available on SAP Service Marketplace, import the new SPAM/SAINT update (at least Version 0025).</LI></UL> <UL><LI>Import the latest R3trans and tp.<br />Ensure that you have imported the latest kernel version into your system. If a newer version is available on SAP Service Marketplace, import the latest kernel. You must have an R3trans version from at least June 11, 2007.</LI></UL> <UL><LI>Obtain the following notes before you begin the installation:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Problems with transaction SAINT:</TD><TD> </TD><TD> 822380</TD></TR> <TR><TD>Language import and deletion of dictionary objects</TD><TD> :</TD><TD> 1022755</TD></TR> <TR><TD>Error when upgrading ECC-VPACK 601:</TD><TD> </TD><TD> 1053777</TD></TR> <TR><TD>Simultaneous activation of view append and appending view:</TD><TD> </TD><TD> 791984</TD></TR> <TR><TD>Termination of XPRA phase RSA2_DSOURCE_AFTER_IMPORT:</TD><TD> </TD><TD> 908642</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <p></p> <UL><LI>Updates</LI></UL> <UL><UL><LI>Process your V3 update entries before you carry out the installation. Otherwise, there is a risk that you may no longer be able to update the update entries if changes are introduced into the interface structures of the V3 update modules by an Enhancement Package (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the installation, process your entries in the extraction queues. Otherwise, there is a risk that you may no longer be able to update these entries if changes are introduced into the interface structures of the qRFC function modules by an Enhancement Package (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the installation, delete your entries in the reconstruction tables for the logistics extraction applications. Otherwise, there is a risk that you may no longer be able to use these entries if changes to the extraction structures are introduced by an Enhancement Package (see Note 328181).</LI></UL></UL> <UL><LI>Components and Support Package requirements</LI></UL> <UL><UL><LI>For Enhancement Package 3, you require the Support Packages of ERP 6.0 Stack 08 <B>or</B> an installed Enhancement Package 2. In the second case, all EHP2 components that are installed in the system must be upgraded to EHP3. A mixture of EHP2 and EHP3 components is not permitted.</LI></UL></UL> <UL><UL><LI>ERECRUIT 603: For ERECRUIT 603, include at least Support Package 01 in the queue to avoid subsequent errors in the application.</LI></UL></UL> <UL><UL><LI>FINBASIS and SEM-BW 603: There are some missing workflow templates with FINBASIS and SEM-BW 603 AOI and AOU packages. These objects are shipped with respective support package 01 of the components. You should import these Support packages to get these missing workflow templates.</LI></UL></UL> <UL><UL><LI>If you have not already installed the required Component Support Packages in your system, we recommend that you import them in the same queue with the EHP3 components.<br />For more information, see Note 83458.<br /><br /><B>Exception 1:</B> BEFORE the installation of IS-OIL 603, you must import SAP_BASIS 700 Support Package 12 (SAPKB70012) and Notes 1118861 and 1089194 or SAP_BASIS 700 Support Package 13 (SAPKB70013) into your system. It is not sufficient to include these Support Packages in the installation queue.<br /><br /><B>Exception 2:</B> BEFORE you install FINBASIS 603, you must either implement Note 1022755 or import SAP_BASIS 700 Support Package 16 (SAPKB70016)&#x00A0;&#x00A0;to avoid errors in the FINBASIS DDIC activation.</LI></UL></UL> <UL><UL><LI>If you cannot integrate the required component Support Packages because the import conditions are not met (for example, because you have already installed SAP_APPL 603 although SAP_APPL 600 is the prerequisite), you must update the import conditions. When you try to install EHP3 packages using the EHP installer (EHPI), the following error occurs in SPDA_EHP_INCLUSION.LOG: \"OCS package SAPKxxx does not match the current software component vector\" Ths system displays the same error message when you try to install packages using transaction SAINT. Proceed as described in Note 1118803.</LI></UL></UL> <UL><UL><LI>Caution: The component <B>ECC-VPACK 603</B> is an uninstall package (SAPK-60300INECCVPACK). You require this uninstall package only if you have installed Enhancement Package 1 (ECC-VPACK 601). Otherwise, you must not include it in the queue.</LI></UL></UL> <UL><UL><LI>You can also install the components SEM-BW 603 (SAPK-603AIINSEMBW), FINBASIS 603 (SAPK-603AIINFINBASIS), ERECRUIT 603 (SAPK-603AIINERECRUIT) and LSOFE 603 (SAPK-603AIINLSOFE) on SAP NetWeaver 7.0. The prerequisites are:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Release</TH><TH ALIGN=LEFT> Support Package</TH></TR> <TR><TD>SAP_BASIS</TD><TD> 700</TD><TD> SAPKB70013</TD></TR> <TR><TD>SAP_ABA</TD><TD> 700</TD><TD> SAPKA70013</TD></TR> <TR><TD>SAP_BW</TD><TD> 700</TD><TD> SAPKW70015</TD></TR> <TR><TD></TD></TR> </TABLE></UL></UL> <p></p> <b>3. Preparing for the installation</b><br /> <UL><LI>Making packages available<br />Use the Maintenance Optimizer of SAP Solution Manager to load the installation packages in your system. Information about the procedure is available on SAP Service Marketplace at service.sap.com/solutionmanager and in Note 772755.</LI></UL> <UL><LI>A DVD for Enhancement Package 3 is not automatically sent to customers. If you cannot use the Maintenance Optimizer for the download, request the DVD with the material number 51033040 from your local subsidiary, or download the DVD from SAP Service Marketplace.</LI></UL> <UL><LI>If you do not load the installation packages using Maintenance Optimizer, but instead load manually from the DVD:</LI></UL> <UL><UL><LI>Log on as user:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>&lt;sid&gt;adm</TD><TD> on UNIX</TD></TR> <TR><TD>&lt;SID&gt;OFR</TD><TD> on AS/400</TD></TR> <TR><TD>&lt;SID&gt;adm</TD><TD> on Windows</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Switch to the directory &lt;DIR_EPS_ROOT&gt; of your SAP system (usually /usr/sap/trans/EPS). The directory &lt;DIR_EPS_ROOT&gt; is also displayed under DIR_EPS_ROOT after you execute the report RSPFPAR.</LI></UL></UL> <UL><UL><LI>Go to the higher-level directory of &lt;DIR_EPS_ROOT&gt;.</LI></UL></UL> <UL><UL><LI>Unpack the SAR archive on the DVD with the following statement:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>SAPCAR -xvf \"/&lt;DVD_DIR&gt;/ABAP/&lt;Software component &gt;603/DATA/&lt;ARCHIV&gt;.SAR,\"</TD><TD> on UNIX</TD> </TR> <TR><TD></TD></TR> <TR><TD>SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/ABAP/&lt;Software component &gt;603/DATA/&lt;ARCHIV&gt;.SAR'</TD><TD> on AS/400</TD></TR> <TR><TD></TD></TR> <TR><TD>SAPCAR -xvf &lt;DVD_DRIVE&gt;:\\ABAP\\&lt;Software component &gt;603\\DATA\\&lt;ARCHIV&gt;.SAR</TD><TD> on Windows</TD></TR> </TABLE></UL></UL> <p>For the components that are to be installed (a subset of the following list), the relevant files should be located in the directory &lt;DIR_EPS_ROOT&gt;/in:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> File name</TH></TR> <TR><TD>SAP_APPL 603</TD><TD> CSN0120061532_0029007.PAT</TD></TR> <TR><TD>EA_APPL 603</TD><TD> CSN0120061532_0028793.PAT</TD></TR> <TR><TD>EA-HR 603</TD><TD> CSN0120061532_0028999.PAT</TD></TR> <TR><TD>EA-GLTRADE 603</TD><TD> CSN0120061532_0028758.PAT</TD></TR> <TR><TD>EA-DFPS 603</TD><TD> CSN0120061532_0028757.PAT</TD></TR> <TR><TD>EA-RETAIL 603</TD><TD> CSN0120061532_0028763.PAT</TD></TR> <TR><TD>EA-FINSERV 603</TD><TD> CSN0120061532_0028769.PAT</TD></TR> <TR><TD>EA-PS 603</TD><TD> CSN0120061532_0028760.PAT</TD></TR> <TR><TD>ECC-DIMP 603</TD><TD> CSN0120061532_0029001.PAT</TD></TR> <TR><TD>IS-CWM 603</TD><TD> CSN0120061532_0028777.PAT</TD></TR> <TR><TD>IS-H 603</TD><TD> CSN0120061532_0028771.PAT</TD></TR> <TR><TD>IS-M 603</TD><TD> CSN0120061532_0028774.PAT</TD></TR> <TR><TD>IS-UT 603</TD><TD> CSN0120061532_0028831.PAT</TD></TR> <TR><TD>IS-OIL 603</TD><TD> CSN0120061532_0028951.PAT</TD></TR> <TR><TD>INSURANCE 603</TD><TD> CSN0120061532_0028761.PAT</TD></TR> <TR><TD>IS-PS-CA 603</TD><TD> CSN0120061532_0028765.PAT</TD></TR> <TR><TD>FI-CA 603</TD><TD> CSN0120061532_0028796.PAT</TD></TR> <TR><TD>FI-CAX 603</TD><TD> CSN0120061532_0028832.PAT</TD></TR> <TR><TD>ERECRUIT 603</TD><TD> CSN0120061532_0029037.PAT</TD></TR> <TR><TD>FINBASIS 603</TD><TD> CSN0120061532_0029012.PAT</TD></TR> <TR><TD>SEM-BW 603</TD><TD> CSN0120061532_0028823.PAT</TD></TR> <TR><TD>LSOFE 603</TD><TD> CSN0120061532_0028805.PAT</TD></TR> <TR><TD>ECC-VPACK 603</TD><TD> CSN0120061532_0028414.PAT</TD></TR> <TR><TD></TD></TR> <TR><TH>For the installation on SAP NetWeaver 7.0:</TH></TR> <TR><TD>ERECRUIT 603</TD><TD> CSN0120061532_0028830.PAT</TD></TR> <TR><TD>FINBASIS 603</TD><TD> CSN0120061532_0028787.PAT</TD></TR> <TR><TD>SEM-BW 603</TD><TD> CSN0120061532_0028824.PAT</TD></TR> <TR><TD>LSOFE 603</TD><TD> CSN0120061532_0028806.PAT</TD></TR> <TR><TD></TD></TR> </TABLE> <p></p> <b>4. Executing the installation</b><br /> <UL><LI>User to be used.<br />Log on to your SAP system in client 000 as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.</LI></UL> <UL><LI>Make installation packages visible<br />Call transaction SAINT and choose \"Start\" and \"Load\". After the list of uploaded packages is displayed, you can return to the initial screen of transaction SAINT by choosing either F3 or \"Back\".</LI></UL><UL><LI>Number of parallel processes during the installation<br />You can reduce the duration of the installation by using parallel processing for several R3trans processes. Five parallel R3trans processes are known to be effective. We do not recommend several background processes for parallel processing of the method execution (otherwise, it may result in a termination as described in section 5). Ensure that you use the current versions for R3trans and tp (for more information, see Note 1110690).</LI></UL> <UL><LI>Starting the installation<br />Call transaction SAINT and choose 'Start'. Select the components that you want to install, and choose 'Continue'. If all of the necessary conditions for importing the components have been fulfilled, the system will now display the relevant queue. This queue comprises the installation package and it may also contain Support Packages.</LI></UL> <UL><LI>EHP3 queue installation<br />We recommend that you import all required components of Enhancement Package 3 together in one queue.<br />To start the installation process, choose \"Continue\". For more information, call transaction SAINT and choose \"Info\" on the application toolbar.<br />You will be requested to enter a password for each component you select. These passwords are provided below.</LI></UL> <p></p> <b>5. Errors during the installation</b><br /> <UL><LI>Reference to Support Packages to be included</LI></UL> <UL><UL><LI>If you receive the warning stating that additional Support Packages (level 0001) must be included in the installation of Enhancement Package 3, check the Support Package level of the relevant component. You can ignore the warning in the following cases:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;All components (Release 600) except EA-HR 600: Support Package level 0011<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EA-HR 600: Support Package level 0026</p> <UL><UL><LI>Note the following: In the case of a higher Support Package level (at least Support Package level 0012 or Support Package level 0026 for EA-HR), you must NOT ignore this warning.</LI></UL></UL> <UL><LI>Error in the add-on conflict calculation</LI></UL> <UL><UL><LI>If you have already installed Enhancement Package 1 (component ECC-VPACK 601), the system displays conflicts with SAP_APPL 603, EA-APPL 603, EA-PS 603 or EA-RETAIL 603.<br /><br />Solution: Reset the queue and integrate the deinstallation package ECC-VPACK 603 (along with SAP_APPL 603, EA-APPL 603, EA-PS 603, and EA-RETAIL 603).</LI></UL></UL> <UL><UL><LI>If you installed the add-on KAYAK 120 and integrate the deinstallation package in the queue, conflicts may occur between KAYAK and SAP_APPL.<br /><br />Solution: Reset the queue and load the attached ACP (ACP_KAYAK603_new.SAR) on your PC. Load the ACP into your system using the function \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Front End\" and restart the installation. See Note 1119856 for further information about ACPs.</LI></UL></UL> <UL><LI>DDIC Activation</LI></UL> <UL><UL><LI>Activation errors may occur that can be solved by repeating the activation phase.&#x00A0;&#x00A0;If errors occur, try to correct them.</LI></UL></UL> <UL><UL><LI>The following activation errors cannot be solved by repeating the activation phase. For these, proceed as follows:<br /><br />\"Indexes 001 and KEY for table PFO_GO_00IS have identical fields.\"<br />In transaction SE11, call the table PFO_GO_00IS in change mode, and choose the menu 'Go to -&gt; Indexes'. In the dialog box that is then displayed, select the index 'KEY' and choose 'Delete Index'. Do not activate Index 001 or the table, but continue with the import in transaction SAINT. A corrected version of this table is delivered with EA-APPL 603 Support Package 01.</LI></UL></UL> <UL><LI>Error during the import</LI></UL> <UL><UL><LI>When you upgrade from ECC-DIMP 602 to ECC-DIMP 603 in a non-Unicode system, the import of SAPK-603DTINECCDIMP terminates and the system issues the error message \"duplicate key error during insert into table SALRTCATCT occured\".<br /><br />Solution: First, ignore the error message by copying the attached file A-603DTINECCDIMP.SAP into the directory /usr/sap/trans/cofiles and then repeat the import. In addition, see Note 1233569.</LI></UL></UL> <UL><LI>Errors in the method execution</LI></UL> <UL><UL><LI>When you upgrade from Enhancement Package 1 to Enhancement Package 3 (ECC-VPACK 603), method FDT_AFTER_IMPORT terminates with return code 0012.<br /><br />Solution: Implement Note 1053777 and then continue the installation.</LI></UL></UL> <UL><UL><LI>When you import SAP_APPL 603, the method execution of SAPK-603DZINSAPAPPL may terminate with an error in the method SWO_OBJTYPE_AFTER_IMPORT (\"Object type 'BUS2081' could not be generated\").<br /><br />Solution: The error occurs only if several background processes run in parallel during the installation. When the phase XPRA_EXECUTION is repeated, the error no longer occurs.</LI></UL></UL> <UL><UL><LI>When you import SAP_APPL 603, the method execution of SAPH60004 may terminate with an error in the method SWO_OBJTYPE_AFTER_IMPORT (\"Object type 'BUS2081' could not be generated\") if this Support Package (SAPH60004) was integrated in the queue.<br /><br />Solution: Copy the attached file AH60004.SAP into the directory /usr/sap/trans/cofiles and repeat this phase.</LI></UL></UL> <UL><UL><LI>If the method execution terminates with the error message B! 039 (\"Names EA-*, PI-*,IS-PS and JVA are reserved for structure packages\"), implement Note 1073405 and repeat this phase.</LI></UL></UL> <UL><LI>Generation errors:</LI></UL> <UL><UL><LI>INSURANCE 603:<br />Program SAPLVKKN, include LVKKNU27: Syntax error line 000034<br />INCLUDE report 'ISCDGEN_ACC_OP' not found.<br /><br />Solution: After you install INSURANCE 603, execute the report SAPRGEN_CD to generate the includes ISCDGEN_ACC_OP, ISCDGEN_ACC_OPK and ISCDGEN_COPA.<br /></LI></UL></UL> <UL><UL><LI>INSURANCE 603:<br />Program CL_IMP_ICL_REGREP_SHOW_MESSAGECP, Include CL_IMP_ICL_REGREP_SHOW_MESSAGECM001: Syntax error in line 000029<br />The transformation 'ICL_REGREP_ROES_SHOW_MESSAGE' does not have an active version.<br /><br />Solution: The error is corrected with INSURANCE 603 Support Package 01. Note 1110541 contains an advance correction.</LI></UL></UL> <UL><UL><LI>SEM-BW 603:<br />Program SAPLSEM_SRM, Include LSEM_SRMTOP: Syntax error in line 000012<br />INCLUDE report 'LSEM_SRME81A' not found.<br /><br />Solution: The error can be ignored.</LI></UL></UL> <UL><UL><LI>EA-PS 603:<br />Program CL_FMFG_CCR_PROCESS_SAMPLE====CP, include CL_FMFG_CCR_PROCESS_SAMPLE====CM001: Syntax error in line 000047.<br />Field 'ES_MASTER_DATA-CENTRAL_DATA-CENTRAL-DATA-PSON1' is unknown. It is neither in one of the speci[...]<br />Program CL_FMFG_CCR_PROCESS_XML_SAMPLECP, include CL_FMFG_CCR_PROCESS_XML_SAMPLECM003: Syntax error in line 000047.<br />Field 'ES_MASTER_DATA-CENTRAL_DATA-CENTRAL-DATA-PSON1' is unknown. It is neither in one of the speci[...]<br /><br />Solution: The syntax error occurs only if you have not activated SAP R/3 Enterprise Extension EA-PS. To solve this error, activate the business function. If you do not use the EA-PS function, you can ignore the error.</LI></UL></UL> <UL><UL><LI>IS-PS-CA 603:<br />Program SAPLHRPIQUS_PSUS_BUD0, Include LHRPIQUS_PSUS_BUD0TOP: Syntax error in line 000125&#x00A0;&#x00A0;Field 'XPIQDB_US_BPSPCHK' is unknown....<br /><br />Solution: Implement Note 1110703 in your system. The error is solved with IS-PS-CA 603 Support Package 01.</LI></UL></UL> <UL><UL><LI>IS-H 603:<br />Program CL_IM_ISH_NL_NCIR_PBO=========CP: Syntax error in line 000005<br />INCLUDE report 'CL_IM_ISH_NL_NCIR_PBO=========CL'not found.Program CL_IM_NL_ISH_NV2000_PAI=======CP: Syntax error in line 000005<br />INCLUDE report 'CL_IM_NL_ISH_NV2000_PAI=======CL'not found.<br />Program CX_N2DOC_RPLYEXCEPTION00======CP: Syntax error in line 000009<br />INCLUDE report 'CX_N2DOC_RPLYEXCEPTION00======CU'not found.<br />Program CX_N2DOC_SNDEXCEPTION00=======CP: Syntax error in line 000009<br />INCLUDE report 'CX_N2DOC_SNDEXCEPTION00=======CU'not found.<br /><br />Solution: The errors are caused by not completely deleting classes and can be ignored. IS-H 603 Support Package 01 corrects the errors.</LI></UL></UL> <UL><UL><LI>IS-OIL 603:<br />Program SAPLOIJ_NOM_UPDATE, include LOIJ_NOM_UPDATEF01: Syntax error in line 000021<br />The type 'KOMWB_WF_DATA_ITAB' is unknown.<br /><br />Solution: Implement note 1109813.</LI></UL></UL> <UL><UL><LI>IS-OIL 603:<br />Program SAPLOI0_BAPI_GOODS_MOVEMENT, include LOI0_BAPI_GOODS_MOVEMENTU04: Syntax error in line 000143<br />Field 'E1GOODSMOVEMENTOIL_CREATEFR' is unknown. It is neither in one of the specified tables nor def<br /><br />Solution: The error can be ignored.<br /></LI></UL></UL> <b>6. After the installation</b><br /> <UL><LI>Activation switch</LI></UL> <UL><UL><LI>To be able to use the new function of the Enhancement Packages, you must activate the relevant business functions. For more information about which business functions are required for which applications, see Note 1083576 (SAP enhancement package 3 for SAP ERP 6.0 - required SWC).</LI></UL></UL> <UL><UL><LI>In transaction SFW5, you will find all industry-independent business functions of the installed Enhancement Packages under the node \"Enterprise Business Functions\". New business functions for an industry solution are available directly under the node of a selected business function set.</LI></UL></UL> <UL><UL><LI>To activate a new business function, set the planned status to \"On\" and choose \"Check Changes\". If no error is reported, you can activate the new functions by choosing \"Activate Changes\".</LI></UL></UL> <UL><UL><LI>If the activation job terminates with the error message B! 039 (\"Names EA-*, PI-*,IS-PS and JVA are reserved for structure packages\"), implement Note 1073405 and repeat the activation. Call transaction SFW5 and choose 'System Settings' -&gt; 'Activate Restart'.</LI></UL></UL> <UL><UL><LI>If the activation of the industry \"Catch Weight Management\" terminates with errors in the reports WZRE_RMCSBWXP_COM and /CBAD/SD_RMCSBWXP_COM (\"Error while changing structure MC03BF1\"), you must implement Note 1112089. You can then start the activation again.</LI></UL></UL><UL><LI>Importing languages after the installation<br />If you intend to install additional languages, we recommend that you install them <B>before</B> you import further Support Packages. Refer to the \"Language support\" section.</LI></UL> <UL><LI>Delivery Customizing<br />Delivery Customizing is imported only into client 000. If you want to copy Customizing into existing clients, read Note 337623.<br /></LI></UL> <b>7. Language support</b><br /> <UL><LI>Enhancement Package 3 supports all the languages of ECC 600.<br />The languages are part of the installation packages, and you do not need to import any additional language packages.</LI></UL> <UL><LI>For information about subsequently installing further languages in your system, see Note 195442.<br /></LI></UL> <b>8. Passwords</b><br /> <UL><LI>Before the installation, you will be asked to enter a password for each component you selected. This password is:<br /></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>SAP_APPL 603 (SAPK-603DHINSAPAPPL)</TD><TD> <B>81B7D132ED</B></TD></TR> <TR><TD>EA-APPL 603 (SAPK-603DHINEAAPPL)</TD><TD> <B>90B7DB5EFC</B></TD></TR> <TR><TD>EA-DFPS 603 (SAPK-603DHINEADFPS)</TD><TD> <B>86B7C45EF9</B></TD></TR> <TR><TD>EA-FINSERV 603 (SAPK-603DHINEAFINSRV)</TD><TD> <B>89A9C42C8D</B></TD></TR> <TR><TD>EA-HR 603 (SAPK-603DHINEAHR)</TD><TD> <B>92C7B75EF5</B></TD></TR> <TR><TD>EA-RETAIL 603 (SAPK-603DHINEARETAIL)</TD><TD> <B>85B3D63783</B></TD></TR> <TR><TD>EA-GLTRADE 603 (SAPK-603DHINEAGLTRAD)</TD><TD> <B>8CB3C53F9E</B></TD></TR> <TR><TD>EA-PS 603 (SAPK-603DHINEAPS)</TD><TD> <B>93C7B75EED</B></TD></TR> <TR><TD>ECC-DIMP 603 (SAPK-603DHINECCDIMP)</TD><TD> <B>84AEDA2CFE</B></TD></TR> <TR><TD>IS-OIL 603 (SAPK-603DHINISOIL)</TD><TD> <B>89ABBB4CF2</B></TD></TR> <TR><TD>FI-CA 603 (SAPK-603DHINFICA)</TD><TD> <B>81C7B456FE</B></TD></TR> <TR><TD>FI-CAX 603 (SAPK-603DHINFICAX)</TD><TD> <B>81BFB456FE</B></TD></TR> <TR><TD>IS-M 603 (SAPK-603DHINISM)</TD><TD> <B>C0E79B6CD0</B></TD></TR> <TR><TD>IS-UT 603 (SAPK-603DHINISUT)</TD><TD> <B>94C7BB4CE8</B></TD></TR> <TR><TD>IS-CWM 603 (SAPK-603DHINISCWM)</TD><TD> <B>97AABB4CFE</B></TD></TR> <TR><TD>IS-PS-CA 603 (SAPK-603DHINISPSCA)</TD><TD> <B>93A4DA4CED</B></TD></TR> <TR><TD>INSURANCE 603 (SAPK-603DHININSURANC)</TD><TD> <B>95B5DA3F8D</B></TD></TR> <TR><TD>IS-H 603 (SAPK-603DHINISH)</TD><TD> <B>C0E79B6CD5</B></TD></TR> <TR><TD>ERECRUIT 603 (SAPK-603DIINERECRUIT)</TD><TD> <B>83B5C2248D</B></TD></TR> <TR><TD>ERECRUIT 603 (SAPK-603AIINERECRUIT)</TD><TD> <B>83B5C2218D</B></TD></TR> <TR><TD>FINBASIS 603 (SAPK-603DIINFINBASIS)</TD><TD> <B>82A6C73F81</B></TD></TR> <TR><TD>FINBASIS 603 (SAPK-603AIINFINBASIS)</TD><TD> <B>82A6C73A81</B></TD></TR> <TR><TD>SEM-BW 603 (SAPK-603DIINSEMBW)</TD><TD> <B>82B0A15AF1</B></TD></TR> <TR><TD>SEM-BW 603 (SAPK-603AIINSEMBW)</TD><TD> <B>82B0A15FF1</B></TD></TR> <TR><TD>LSOFE 603 (SAPK-603DIINLSOFE)</TD><TD> <B>86A2BE4CF3</B></TD></TR> <TR><TD>LSOFE 603 (SAPK-603AIINLSOFE)</TD><TD> <B>86A2BE49F3</B></TD></TR> <TR><TD>ECC-VPACK 603 (SAPK-60300INECCVPACK)</TD><TD> <B>96B7D64BED</B></TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> </TABLE></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037264)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033006)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001083533/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001083533/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083533/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083533/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083533/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083533/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083533/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083533/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083533/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "A-603DTINECCDIMP.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000483512007&iv_version=0036&iv_guid=369234DA27802E4E8EDC21B410705C53"}, {"FileName": "ACP_EAHR603.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000483512007&iv_version=0036&iv_guid=8604C86C6385064FA52F623B3B21AD0D"}, {"FileName": "ACP_KAYAK603_new.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000483512007&iv_version=0036&iv_guid=5D0AB08695E1074BA16183F58EEAB849"}, {"FileName": "ACP_ISUT603.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000483512007&iv_version=0036&iv_guid=4F24DD568D198F41BCE482E92CBED220"}, {"FileName": "ACP_SAPAPPL603.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000483512007&iv_version=0036&iv_guid=B7CC5DCC5617A245974933FA7F2DE52C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "551147", "RefComponent": "PA-ER", "RefTitle": "Overview: Notes for add-on ERECRUIT", "RefUrl": "/notes/551147"}, {"RefNumber": "529063", "RefComponent": "PE-LSO-TM", "RefTitle": "Overview: Notes for the Learning Solution add-on", "RefUrl": "/notes/529063"}, {"RefNumber": "438520", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for FINBASIS add-on", "RefUrl": "/notes/438520"}, {"RefNumber": "186299", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for SEM-BW add-on", "RefUrl": "/notes/186299"}, {"RefNumber": "1371027", "RefComponent": "BC-CTS-LAN", "RefTitle": "Delivery of TERM and GLOSSARY Texts in SAP_BASIS 7x Products", "RefUrl": "/notes/1371027"}, {"RefNumber": "1262124", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1262124"}, {"RefNumber": "1225752", "RefComponent": "IS-AFS", "RefTitle": "BAPIs valid for usage with SAP Apparel and Footwear 6.3", "RefUrl": "/notes/1225752"}, {"RefNumber": "1225060", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to IS-M 603 stops due to conflict with EXX-SE 602", "RefUrl": "/notes/1225060"}, {"RefNumber": "1170324", "RefComponent": "PE-LSO-CP", "RefTitle": "Java components of LSO and Enhancement Packages", "RefUrl": "/notes/1170324"}, {"RefNumber": "1158886", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP REAL ESTATE CEE 110_600 add-on and ERP Enhancement Pack", "RefUrl": "/notes/1158886"}, {"RefNumber": "1150868", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP IS-UT CEE 600 add-on and ERP Enhancement Packages", "RefUrl": "/notes/1150868"}, {"RefNumber": "1150349", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC-SE 604 Add-On Installation / Delta update", "RefUrl": "/notes/1150349"}, {"RefNumber": "1140663", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-PS-XT 6.0 and ERP Enhancement Packages", "RefUrl": "/notes/1140663"}, {"RefNumber": "1118803", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC 600 Support Packages and ERP enhancement packages", "RefUrl": "/notes/1118803"}, {"RefNumber": "1117309", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Enhancement Package 2 & 3 for ERP 6.0:Compatible Add-ons", "RefUrl": "/notes/1117309"}, {"RefNumber": "1112089", "RefComponent": "IS-CWM", "RefTitle": "Error when activating the CWM EHP3 switch", "RefUrl": "/notes/1112089"}, {"RefNumber": "1110690", "RefComponent": "BC-UPG-OCS", "RefTitle": "Deadlock when importing Support Package", "RefUrl": "/notes/1110690"}, {"RefNumber": "1109562", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Termination UNCAUGHT_EXCEPTION in transaction SFW5", "RefUrl": "/notes/1109562"}, {"RefNumber": "1109483", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of ESOADOCU 100", "RefUrl": "/notes/1109483"}, {"RefNumber": "1100022", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.600: Conflicts when importing Enhancement Package", "RefUrl": "/notes/1100022"}, {"RefNumber": "1095233", "RefComponent": "XX-SER-REL", "RefTitle": "Enhancement Package 3 for SAP ERP 6.0: required Support Pack", "RefUrl": "/notes/1095233"}, {"RefNumber": "1083576", "RefComponent": "XX-SER-REL", "RefTitle": "SAP enhancement package 3 for SAP ERP 6.0 - required SWC", "RefUrl": "/notes/1083576"}, {"RefNumber": "1082916", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade AIO 1.2 to Enhancement Package for SAP ERP 6.0", "RefUrl": "/notes/1082916"}, {"RefNumber": "1081630", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC-SE 603 Add-On Installation / Delta upgrade", "RefUrl": "/notes/1081630"}, {"RefNumber": "1022755", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Language import and deletion of dictionary objects", "RefUrl": "/notes/1022755"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "438520", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for FINBASIS add-on", "RefUrl": "/notes/438520 "}, {"RefNumber": "1150349", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC-SE 604 Add-On Installation / Delta update", "RefUrl": "/notes/1150349 "}, {"RefNumber": "1371027", "RefComponent": "BC-CTS-LAN", "RefTitle": "Delivery of TERM and GLOSSARY Texts in SAP_BASIS 7x Products", "RefUrl": "/notes/1371027 "}, {"RefNumber": "1170324", "RefComponent": "PE-LSO-CP", "RefTitle": "Java components of LSO and Enhancement Packages", "RefUrl": "/notes/1170324 "}, {"RefNumber": "551147", "RefComponent": "PA-ER", "RefTitle": "Overview: Notes for add-on ERECRUIT", "RefUrl": "/notes/551147 "}, {"RefNumber": "529063", "RefComponent": "PE-LSO-TM", "RefTitle": "Overview: Notes for the Learning Solution add-on", "RefUrl": "/notes/529063 "}, {"RefNumber": "1100022", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.600: Conflicts when importing Enhancement Package", "RefUrl": "/notes/1100022 "}, {"RefNumber": "186299", "RefComponent": "FIN-SEM", "RefTitle": "Overview: SAP Notes for SEM-BW add-on", "RefUrl": "/notes/186299 "}, {"RefNumber": "1083576", "RefComponent": "XX-SER-REL", "RefTitle": "SAP enhancement package 3 for SAP ERP 6.0 - required SWC", "RefUrl": "/notes/1083576 "}, {"RefNumber": "1117309", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Enhancement Package 2 & 3 for ERP 6.0:Compatible Add-ons", "RefUrl": "/notes/1117309 "}, {"RefNumber": "1081630", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC-SE 603 Add-On Installation / Delta upgrade", "RefUrl": "/notes/1081630 "}, {"RefNumber": "1022755", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Language import and deletion of dictionary objects", "RefUrl": "/notes/1022755 "}, {"RefNumber": "1158886", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP REAL ESTATE CEE 110_600 add-on and ERP Enhancement Pack", "RefUrl": "/notes/1158886 "}, {"RefNumber": "1140663", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-PS-XT 6.0 and ERP Enhancement Packages", "RefUrl": "/notes/1140663 "}, {"RefNumber": "1109483", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of ESOADOCU 100", "RefUrl": "/notes/1109483 "}, {"RefNumber": "1095233", "RefComponent": "XX-SER-REL", "RefTitle": "Enhancement Package 3 for SAP ERP 6.0: required Support Pack", "RefUrl": "/notes/1095233 "}, {"RefNumber": "1118803", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC 600 Support Packages and ERP enhancement packages", "RefUrl": "/notes/1118803 "}, {"RefNumber": "1225752", "RefComponent": "IS-AFS", "RefTitle": "BAPIs valid for usage with SAP Apparel and Footwear 6.3", "RefUrl": "/notes/1225752 "}, {"RefNumber": "1225060", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to IS-M 603 stops due to conflict with EXX-SE 602", "RefUrl": "/notes/1225060 "}, {"RefNumber": "1082916", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade AIO 1.2 to Enhancement Package for SAP ERP 6.0", "RefUrl": "/notes/1082916 "}, {"RefNumber": "1150868", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP IS-UT CEE 600 add-on and ERP Enhancement Packages", "RefUrl": "/notes/1150868 "}, {"RefNumber": "1110690", "RefComponent": "BC-UPG-OCS", "RefTitle": "Deadlock when importing Support Package", "RefUrl": "/notes/1110690 "}, {"RefNumber": "958245", "RefComponent": "BC-UPG-ADDON", "RefTitle": "DFPS: Release strategy", "RefUrl": "/notes/958245 "}, {"RefNumber": "1091671", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add on upgrade DFPS 613 on SAP ECC 600", "RefUrl": "/notes/1091671 "}, {"RefNumber": "1109562", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Termination UNCAUGHT_EXCEPTION in transaction SFW5", "RefUrl": "/notes/1109562 "}, {"RefNumber": "1112089", "RefComponent": "IS-CWM", "RefTitle": "Error when activating the CWM EHP3 switch", "RefUrl": "/notes/1112089 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-M", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-OIL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "FINBASIS", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-GLTRADE", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "FI-CAX", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "ERECRUIT", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "FI-CA", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "ECC-VPACK", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "KAYAK", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-CWM", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-PS-CA", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}