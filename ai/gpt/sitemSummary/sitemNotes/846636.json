{"Request": {"Number": "846636", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 331, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015897802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000846636?language=E&token=651C4C6A9C29DFF5389D6A087A899F17"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000846636", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000846636/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "846636"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.03.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-XX-CE"}, "SAPComponentKeyText": {"_label": "Component", "value": "Concurrent Employment - International Payroll"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payroll: General Parts", "value": "PY-XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Concurrent Employment - International Payroll", "value": "PY-XX-CE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-XX-CE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "846636 - CE-MPSD: Function Multiple Period Start Days"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>As part of the CE payroll you could previously only run a combined payroll for contracts that are within the same payroll area. With this enhancement, it is possible to run the payroll for contracts from different payroll areas.<br /><br />You have to take the following restrictions into account:</p> <UL><LI>After a payroll for MPSD was carried out, the switch MPSDS of the CCURE group in the V_T77S0 view may no longer be deleted or set to SPACE (= no).</LI></UL> <UL><LI>In addition, you must not change the grouping rule XXMP for grouping XXPY in the V_T7CCE_GPASG view.</LI></UL> <UL><LI>The payroll areas for which a combined payroll run is carried out must be assigned to one payroll area grouping (view V_T549AGA).</LI></UL> <UL><LI>A payroll area must only be assigned to one payroll area grouping.</LI></UL> <UL><LI>The payroll area groupings, for which the combined payroll run is carried out, must</LI></UL> <UL><UL><LI>either be all relevant for payroll or all not relevant for payroll.</LI></UL></UL> <UL><UL><LI>have the same payment date and the same time unit; the allocated period modifiers (PERMO) may, however, have a different period start.</LI></UL></UL> <UL><LI>You may only add an additional payroll area to a payroll area grouping if NO payroll result exists for it. (*)</LI></UL> <UL><LI>In the live system, you must not delete payroll areas from a payroll area grouping.</LI></UL> <UL><LI>The splitting of payroll results, in accordance with table T530, is not supported.</LI></UL> <UL><LI>For an organizational reassignment to another payroll area or the switch to another area of any grouping, you must create a new employment contract (therefore a new personnel number), or use an already existing contract (with the new payroll area or the new grouping).(**)</LI></UL> <UL><LI>This function is currently only for the Canadian payroll. If you require this enhancement for another country, contact the responsible product manager of the subsidiary.</LI></UL> <p><br />(*) This means in particular that no payroll results must exist for the first payroll area of a payroll area grouping. Subsequently, you must create new payroll areas for the MPSD function and assign these to the (also new) payroll area groupings.<br /><br />(**) This means in particular that the existing grouping values (for all groupings) of a contract must not be changed (that means that a contract may not be assigned to other areas). Subsequently, you must create new contracts (that are within the new payroll areas (see *)) for all existing employees at the start of the MPSD function.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Multiple Period Start Days, MPSD, Concurrent Employment, CE<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use the Multiple Period Start Days (MPSD) function in your environment for concurrent employment (CE).<br /><br />The above prerequisites are required to avoid problems with the retroactive accounting and the analyzing reports. These apply even if you already used concurrent employment.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note represents a special development, which is delivered with the HR Support Package specified below. Since this is not an error correction, correction instructions are not included.<br /><br />To activate the MPSD function, proceed as follows:</p> <UL><LI>See Note 520965 for information concerning the release of the concurrent employment function.</LI></UL> <UL><LI>In the V_T77S0 view for the CCURE group, enter the switch MPSDS with value X (= yes). This switch must not be deactivated.</LI></UL> <UL><LI>In the V_T7CCE_GPASG view, enter the grouping reason XXPY for grouping rule XXMP. You must not change this rule.</LI></UL> <UL><LI>Maintain your payroll area groupings with the view V_T549AG.</LI></UL> <UL><LI>Assign the payroll areas to the payroll area groupings with the view V_T549AGA.</LI></UL> <p><br />You find these steps also in the IMG of the payroll for Canada in the suboption Concurrent Employment -&gt; Multiple Period Start Days.<br /><br />Note:<br />The terms grouping and grouping reason are synonyms. This also applies to group and grouping value.<br />The net grouping, for example, is the grouping with grouping reason XXNT. A contract, for example, belongs to the group CA01 of the payroll area grouping XXPY if the payroll area of the contract belongs to the payroll area grouping CA01, and if this value is returned by the grouping rule XXMP (that is, by the linked method in accordance with table T7CCE_GPRULE) as grouping value (group =).<br />Consider that the payroll area grouping represents a summary of payroll areas, and that this is not related to the grouping mentioned above (for example, payroll grouping). To be precise, we should use the term contract grouping, however, in the concurrent employment environment this is usually not the case.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PY-CA-CE (Concurrent Employment - Payroll Canada)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023306)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023306)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000846636/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846636/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "857768", "RefComponent": "PY-XX-DT", "RefTitle": "RPCIPE00_CE: Missing enhancement for posting MPSD", "RefUrl": "/notes/857768"}, {"RefNumber": "855149", "RefComponent": "PY-XX-FO", "RefTitle": "Wage Type Reporter for CE with multiple periods start dates", "RefUrl": "/notes/855149"}, {"RefNumber": "839201", "RefComponent": "PY-XX-FO", "RefTitle": "CE-enabled payroll journal for USA and Canada", "RefUrl": "/notes/839201"}, {"RefNumber": "520965", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release restrictions R/3 Enterprise 4.70 / 1.10 Add-Ons", "RefUrl": "/notes/520965"}, {"RefNumber": "518520", "RefComponent": "PY-XX-CE", "RefTitle": "Concurrent Employment - Payroll", "RefUrl": "/notes/518520"}, {"RefNumber": "518490", "RefComponent": "PA-CE", "RefTitle": "Concurrent Employment - Grouping Personnel Assignments", "RefUrl": "/notes/518490"}, {"RefNumber": "1846655", "RefComponent": "PY-XX-CE", "RefTitle": "CE: Check for a change of payroll area is incomplete (4)", "RefUrl": "/notes/1846655"}, {"RefNumber": "1274704", "RefComponent": "PY-XX-CE", "RefTitle": "CE: Inconsistencies due to changes of payroll area", "RefUrl": "/notes/1274704"}, {"RefNumber": "1036365", "RefComponent": "PY-XX-CE", "RefTitle": "MPSD: Off-Cycle pay type 'C'", "RefUrl": "/notes/1036365"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1846655", "RefComponent": "PY-XX-CE", "RefTitle": "CE: Check for a change of payroll area is incomplete (4)", "RefUrl": "/notes/1846655 "}, {"RefNumber": "1274704", "RefComponent": "PY-XX-CE", "RefTitle": "CE: Inconsistencies due to changes of payroll area", "RefUrl": "/notes/1274704 "}, {"RefNumber": "1036365", "RefComponent": "PY-XX-CE", "RefTitle": "MPSD: Off-Cycle pay type 'C'", "RefUrl": "/notes/1036365 "}, {"RefNumber": "518490", "RefComponent": "PA-CE", "RefTitle": "Concurrent Employment - Grouping Personnel Assignments", "RefUrl": "/notes/518490 "}, {"RefNumber": "178618", "RefComponent": "PY-XX-CE", "RefTitle": "CE: Error when instantiating evaluation/legacy data class", "RefUrl": "/notes/178618 "}, {"RefNumber": "839201", "RefComponent": "PY-XX-FO", "RefTitle": "CE-enabled payroll journal for USA and Canada", "RefUrl": "/notes/839201 "}, {"RefNumber": "855149", "RefComponent": "PY-XX-FO", "RefTitle": "Wage Type Reporter for CE with multiple periods start dates", "RefUrl": "/notes/855149 "}, {"RefNumber": "857768", "RefComponent": "PY-XX-DT", "RefTitle": "RPCIPE00_CE: Missing enhancement for posting MPSD", "RefUrl": "/notes/857768 "}, {"RefNumber": "520965", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release restrictions R/3 Enterprise 4.70 / 1.10 Add-Ons", "RefUrl": "/notes/520965 "}, {"RefNumber": "518520", "RefComponent": "PY-XX-CE", "RefTitle": "Concurrent Employment - Payroll", "RefUrl": "/notes/518520 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-HR", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-HR 200", "SupportPackage": "SAPKGPHB26", "URL": "/supportpackage/SAPKGPHB26"}, {"SoftwareComponentVersion": "EA-HR 200", "SupportPackage": "SAPKGPHB25", "URL": "/supportpackage/SAPKGPHB25"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47044", "URL": "/supportpackage/SAPKE47044"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47045", "URL": "/supportpackage/SAPKE47045"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47043", "URL": "/supportpackage/SAPKE47043"}, {"SoftwareComponentVersion": "EA-HR 500", "SupportPackage": "SAPKGPHC18", "URL": "/supportpackage/SAPKGPHC18"}, {"SoftwareComponentVersion": "EA-HR 600", "SupportPackage": "SAPKGPHD04", "URL": "/supportpackage/SAPKGPHD04"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1036365", "RefTitle": "MPSD: Off-Cycle pay type 'C'", "RefUrl": "/notes/0001036365"}]}}}}}