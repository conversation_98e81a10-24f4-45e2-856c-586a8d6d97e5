{"Request": {"Number": "1010167", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 268, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006021212017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001010167?language=E&token=DACBD6BC42ABF97E96646FC228CD66E5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001010167", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001010167/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1010167"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.04.2007"}, "SAPComponentKey": {"_label": "Component", "value": "LO-SPM-INB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Inbound Process"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Parts Management", "value": "LO-SPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-SPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inbound Process", "value": "LO-SPM-INB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-SPM-INB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1010167 - Controlling take over of handling unit identification"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>If shipping notifications are received by the vendor in the form of IDocs in enhanced inbound delivery processing, the external identification (EXIDV) of the handling units is always replaced during processing by a newly assigned internal number. The external identification provided originally by the vendor is saved in this case as additional identification (EXDIV2). The reassignment of the HU number takes place even if the vendor assigned a unique HU number (SSCC18).<br /><br />In addition, the external identification is always replaced by a newly assigned internal number during completion confirmation for an inbound delivery from Extended Warehouse Management (EWM), meaning that the process is no longer transparent.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EXIDA, SSCC18, TVSHP-EXIDV_UNIQUE<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Currently there is no way of controlling whether the external identification should be retained or replaced by an internal number when the HU identification is transferred.<br /><br />When handling units are transferred from EWM during the completion confirmation, the HU numbers assigned by EWM should always be retained.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note provides a Customizing activity that can be used in future to control the transfer of the external HU identification.<br /><br />To make this activity available, proceed as follows:</p> <OL>1. Use transaction SE11 to create the new data element /SPE/EXIDV_TAKEOVER with the following features.</OL> <UL><UL><LI>Package /SPE/VL</LI></UL></UL> <UL><UL><LI>Domain&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;XFELD</LI></UL></UL> <UL><UL><LI>Description: Take Over External Handling Unit Identifications</LI></UL></UL> <UL><UL><LI>Field label:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Length</TH><TH ALIGN=LEFT> Description</TH></TR> <TR><TD>10</TD><TD> Ext. HU Id</TD></TR> <TR><TD>15</TD><TD> Take Ext. HU Id</TD></TR> <TR><TD>20</TD><TD> Take Over Ext. HU Id</TD></TR> <TR><TD>40</TD><TD> Take Over External HU Identification</TD></TR> </TABLE></UL></UL> <p></p> <OL>2. Use transaction SE11 to add the new field SPE_EXIDV_TAKEOV to the TVSHP table. Use the new data element /SPE/EXIDV_TAKEOVER as the data type. Activate the table.</OL> <OL>3. Use transaction SE11 to add the new field SPE_EXIDV_TAKEOV to view /SPE/V_TVSHP_HU. To do this, go to the \"View Fields\" tab in view maintenance and choose \"Table Fields\". Select the SPE_EXIDV_TAKEOV field in the list of available fields so that the field appears in the list of the view fields after transfer. Activate the view.</OL> <OL>4. Regenerate the maintenance dialog for the /SPE/V_TVSHP_HU view. To do this, call transaction SE54 and enter view name /SPE/V_TVSHP_HU. Select the \"Generated Objects\" subobject and choose \"Create/Change\". On the \"Generation Environment\" screen, select \"Change\". In the next dialog box, select \"New field/sec. table in structure\" as the change reason. After you confirm, a further dialog box is displayed in which you can select the elements to be generated. Select the single screen 310 and the attribute \"Normal field\" here. After you confirm this selection, the maintenance dialog is regenerated (you can ignore the warning that screen 310 is deleted and created again).</OL> <OL>5. Implement the source code corrections from the correction instructions.</OL> <p><br />After implementing the correction, you can control the system response when the external handling unit identification is transferred by using the following IMG activity: Logistics Execution -&gt; Service Parts Management (SPM) -&gt; Cross-Process Settings -&gt; Handling Unit Management (SPM) -&gt; Configure Additional Global Shipping Dates for HU Management.</p> <UL><LI>If the \"Take Over External HU Identification\" indicator is not set (default setting), the external HU identification transferred by IDoc is saved as additional identification (EXDIV2) as before, and the HU is given a new internal number as the external identification. Exception: If the external HU number is qualified as SSCC18, this unique number is transferred, independently of whether the \"Take Over External HU Identification\" is set.</LI></UL> <UL><LI>If the \"Take Over External HU Identification\" indicator is set, the external handling unit identification transferred by IDoc is transferred as such; no new internal number is assigned. If you use this setting together with the \"Unique HU Number Assignment\" (TVSHP-EXIDV_UNIQUE) switch, you may require organizational measures to ensure that your vendors transfer only unique HU numbers to you.</LI></UL> <p><br />The settings affect only IDoc inbound for enhanced inbound delivery processing (inbound module /SPE/IDOC_INPUT_DESADV1). In future, transfer of the external HU identification will be independent of the global setting for whether unique HU numbers are to be used.<br /><br />IDoc inbound processing using the standard module IDOC_INPUT_DESADV1 for customers with a standard system is not affected by the new switch. During this inbound processing, the external HU identification transferred by IDoc is always transferred. Until you implement Note 989511 or import Support Package SAPKH60007, the inbound processing in DIMP systems uses the IDOC_INPUT_DESADV1 module to branch to the /SPE/IDOC_INPUT_DESADV1 inbound function module, and due to this, Customizing settings for taking over the HU identification also become effective here.<br /><br /><U>Remark:</U> In the delivery of this correction by means of a Support Package, the unnecessary switch TVSHP-SPE_PACKPOS_DEL is removed from the /SPE/V_TVSHP_HU view and the related IMG documentation is updated.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-A-GR (Goods Receipt Process)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021667)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001010167/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001010167/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611"}, {"RefNumber": "872772", "RefComponent": "IS-A-GR", "RefTitle": "BORES SSCC number is copied to EXIDV2 of the HU", "RefUrl": "/notes/872772"}, {"RefNumber": "1802404", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound delivery replication to EWM does not sent EXIDV", "RefUrl": "/notes/1802404"}, {"RefNumber": "1092914", "RefComponent": "IS-A-GR", "RefTitle": "IDOC with nested HUs can not be processed (HUFUNCTIONS 004)", "RefUrl": "/notes/1092914"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1802404", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound delivery replication to EWM does not sent EXIDV", "RefUrl": "/notes/1802404 "}, {"RefNumber": "1591235", "RefComponent": "LO-SPM-X", "RefTitle": "Cross delivery HUs not transferred during STO", "RefUrl": "/notes/1591235 "}, {"RefNumber": "872772", "RefComponent": "IS-A-GR", "RefTitle": "BORES SSCC number is copied to EXIDV2 of the HU", "RefUrl": "/notes/872772 "}, {"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611 "}, {"RefNumber": "1092914", "RefComponent": "IS-A-GR", "RefTitle": "IDOC with nested HUs can not be processed (HUFUNCTIONS 004)", "RefUrl": "/notes/1092914 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60008", "URL": "/supportpackage/SAPKH60008"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0001010167/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1056526", "RefTitle": "Warning message when activating BC set /SPE/COMPLETE", "RefUrl": "/notes/0001056526"}]}}}}}