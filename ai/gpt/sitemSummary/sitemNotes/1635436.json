{"Request": {"Number": "1635436", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 402, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009709442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001635436?language=E&token=C0180891CF855C303E5FA4F8C2A81B38"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001635436", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001635436/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1635436"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.05.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-PPM-PFM"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Portfolio Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portfolio and Project Management", "value": "BW-BCT-PPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-PPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Portfolio Management", "value": "BW-BCT-PPM-PFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-PPM-PFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1635436 - BW Role and Role Relationship Delta Extractor-II"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b>Improvement Request:</b><br /> <p>PPM: Provide extractors with capability to do delta extraction of role demand data and staffing data for projects.<br /></p> <b>Request Reason:</b><br /> <p>Long runtime for full data extraction of role demand data and staffing data for projects as the current extractors 0RPM_ROLE_D and 0RPM_RELATE_D do not have the functionality of delta extraction.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CustConn, Customer Connect, BW delta extractors, 0RPM_ROLE_D, 0RPM_RELATE_D, 0RPM_ROLE_D_DELTA, 0RPM_RELATE_D_DELTA</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Interface Note 1627940 is pre-requisite.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please implement the relevant support package SAPK-50008INCPRXRPM to get the extractors.</p> <b>Improvement:</b><br /> <p>Two new extractors with capability of delta extraction have been developed. During the save in the application the delta changes (if any) will be posted to the tRFC queue (RSA7) against the following extractors.<br />(1)0RPM_ROLE_D_DELTA.<br />(2)0RPM_RELATE_D_DELTA.<br /></p> <b>Benefit:</b><br /> <p>This will enable faster load of data to BI as compared with full extraction which results in long runtime.<br /></p> <b>Important:</b><br /> <p>With this improvement request only extractors are delivered and &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; no BI content to consume the same.Customers implementing this note will have to develop the required BI content to consume this.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PPM-PFM (Portfolio Management)"}, {"Key": "Other Components", "Value": "PPM-PRO (Project Management)"}, {"Key": "Improvement Note", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I035150)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I057672)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001635436/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001635436/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1631964", "RefComponent": "PPM-PRO", "RefTitle": "CustConn PPM: Composite SAP Note", "RefUrl": "/notes/1631964"}, {"RefNumber": "1627940", "RefComponent": "PPM-PFM", "RefTitle": "BW Role and Role Relationship Delta Extractor-I", "RefUrl": "/notes/1627940"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2340116", "RefComponent": "PPM-PRO", "RefTitle": "SAP Portfolio and Project Management SAP S/4HANA: Restrictions", "RefUrl": "/notes/2340116 "}, {"RefNumber": "2026424", "RefComponent": "PPM-PRO", "RefTitle": "SAP Portfolio and Project Management 6.1: Restrictions", "RefUrl": "/notes/2026424 "}, {"RefNumber": "1631964", "RefComponent": "PPM-PRO", "RefTitle": "CustConn PPM: Composite SAP Note", "RefUrl": "/notes/1631964 "}, {"RefNumber": "1627940", "RefComponent": "PPM-PFM", "RefTitle": "BW Role and Role Relationship Delta Extractor-I", "RefUrl": "/notes/1627940 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CPRXRPM", "From": "500_702", "To": "500_702", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CPRXRPM 500_702", "SupportPackage": "SAPK-50008INCPRXRPM", "URL": "/supportpackage/SAPK-50008INCPRXRPM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKH50001&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual activity separately in each system into which you transport the Note for  implementation.<br/><br/>Please follow the steps belwo in order to activate the changes.<br/>1. Go to transaction code SPRO in PPM system.<br/>2. Click on \"SAP REFERENCE IMG\" button.<br/>3. Click on 'SAP Portfolio and Project Management'.<br/>4. Under that go to node Global Customizing.<br/>5. Under that go to Global Settings.<br/>6. In that execute \"Override Default Global Settings\"<br/>7. Click on new entry<br/>8 Now make entry in following fields:<br/>&nbsp;&nbsp; Area: 0007<br/>&nbsp;&nbsp; Name: 0030<br/>&nbsp;&nbsp; Settings Value : 'X'.<br/>9. Save the changes.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}