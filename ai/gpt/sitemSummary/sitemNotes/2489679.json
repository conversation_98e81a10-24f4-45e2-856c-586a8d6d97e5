{"Request": {"Number": "2489679", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 185, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002489679?language=E&token=EEBA026B3417E1C30800D2699FF151CD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002489679", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002489679/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2489679"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "19.06.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-NA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Note Assistant"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2489679 - How to identify TCI Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This document helps to&#160; :</p>\r\n<p>1. Differentiate between TCI and non-TCI Note.</p>\r\n<p>2. Differentiate between NA-Bootstrap Note (For TCI) and TCI Note.</p><h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3><p>SNOTE / SPAM / SAP Service Market Place / SAP one Launchpad</p><h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3><p>To check the Note details.</p><h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3><p>This document helps to identify the type of Note.</p><h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3><p>1. TCI Note is a Note which contains ABAP corrections in a package.</p>\r\n<p>&#160;&#160;&#160; Example : COMM SAPK70000LCPSAPBASIS</p>\r\n<p>&#160;&#160;&#160; Non-TCI Note is a Note which contains ABAP corrections to a object / new object .</p>\r\n<p>&#160;&#160;&#160; Example : REPS RSBDCOS0</p>\r\n<p>&#160;&#160;&#160; In-order identify the kind of Note , you need to check the delivered correction type.</p>\r\n<p>2.&#160; NA Bootstrapping Note is a Note&#160;which enables TCI . Example : <a target=\"_blank\" href=\"/notes/1995550\">1995550</a> , which needs to be installed via SPAM (version 61 or higher).</p>\r\n<p>&#160;&#160;&#160; Bootstrap Note needs to be applied in every system in client 000, if the delivering Basis SP is not yet installed.</p>\r\n<p>&#160;&#160;&#160; Refer to Note <a target=\"_blank\" href=\"/notes/2187425\">2187425</a>&#160;- Attached PDF describes Implementation steps.</p>\r\n<p>&#160;&#160;&#160; Example for Bootstrap Note : <a target=\"_blank\" href=\"/notes/1995550\">1995550</a></p>\r\n<p>&#160;&#160;&#160; The TCI Notes should be installed like normal notes in client &lt;&gt; 000.</p>\r\n<p>&#160;&#160;&#160; The Objects are recorded in a TR and can be consolidated like any other note correction.</p>\r\n<p>&#160;&#160;&#160;&#160; Example for TCI Note : <a target=\"_blank\" href=\"/notes/2444993\">2444993</a></p><h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3><p><a target=\"_blank\" href=\"https://help.sap.com/viewer/9d6aa238582042678952ab3b4aa5cc71/7.51.0/en-US/81a0376ed9b64194b8ecff6f02f32652.html\">https://help.sap.com/viewer/9d6aa238582042678952ab3b4aa5cc71/7.51.0/en-US/81a0376ed9b64194b8ecff6f02f32652.html</a></p><h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3><p>TCI</p>\r\n<p>SNOTE</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-NV (Note Validation)"}, {"Key": "Responsible                                                                                         ", "Value": "I058623"}, {"Key": "Processor                                                                                           ", "Value": "I058623"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489679/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2187425", "RefComponent": "BC-UPG-NA", "RefTitle": "Information about SAP Note Transport based Correction Instructions (TCI)", "RefUrl": "/notes/2187425"}, {"RefNumber": "1995550", "RefComponent": "BC-UPG-NA", "RefTitle": "Enabling SNOTE for transport based correction instruction", "RefUrl": "/notes/1995550"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543372", "RefComponent": "BC-UPG-NA", "RefTitle": "How to implement a Transport-based Correction Instruction", "RefUrl": "/notes/2543372 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "2 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 3.33, "Quality-Votes": 3, "RatingQualityDetails": {"Stars-1": 1, "Stars-2": 0, "Stars-3": 0, "Stars-4": 1, "Stars-5": 1}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}