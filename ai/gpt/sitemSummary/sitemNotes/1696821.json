{"Request": {"Number": "1696821", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 261, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017408302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001696821?language=E&token=FFF3CE92995A34446AE181D8D8CF7628"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001696821", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001696821/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1696821"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.10.2018"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-IMS-UPGR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Application Specific Upgrade Tools"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installed Base Development Projects", "value": "XX-PROJ-IMS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-IMS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Application Specific Upgrade Tools", "value": "XX-PROJ-IMS-UPGR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-IMS-UPGR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1696821 - Technical documentation: ASU variant restorer functionality"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Technical documentation for the ASU variant restorer</strong></p>\r\n<p>In general, the ASU variant restorer is executed during the technical upgrade. During the PREPARE phase (before an upgrade), the system queries whether the variant restorer is to be executed. If you do not want this, you can deselect the option here. Otherwise, the technical upgrade starts the program RASUVAR1 in JOB_RASUVAR1 before the upgrade, and after the upgrade then automatically JOB_RASUVAR2 with the program RASUVAR2. If the runtime of RASUVAR2 is too long after the upgrade, you can skip this step as described in SAP Note 1052664. You can later start RASUVAR2 (or alternatively the program RASUVAR_FINISH - see below) again manually as a background job to complete the conversion of the variants. For this, you should preferably select a time after the generation of the programs (SGEN). This significantly reduces the runtime of RASUVAR2 (or RASUVAR_FINISH). The programs RASUVAR1 and RASUVAR2 are delivered using the tool import during the PREPARE - this delivery is also part of the technical upgrade.</p>\r\n<p>If you want to use the variant restorer outside of the upgrade (for example, with a Support Package or when implementing a modifying add-on), you can do this using SAP Note 1160685. A transport file is attached to this SAP Note, which contains the programs RASUVAR_START (corresponds to the program RASUVAR1) and RASUVAR_FINISH (corresponds to the program RASUVAR2). The programs RASUVAR_START and RASUVAR_FINISH from SAP Note 1160685 have identical functions to the programs RASUVAR1 and RASUVAR2 from the technical upgrade. The only difference is that selection options exist for the restriction and that the execution can be parallelized if required.</p>\r\n<p>Technically, the ASU variant restorer works as follows:<br />The program RASUVAR1 (or RASUVAR_START) imports selection screen parameters for all programs with at least one variant. This information is then stored in the table TASUVAR1. The program RASUVAR_START works identically, and can also be used to evaluate the system variants (&amp;SAP...).&#x00A0;In the case of an upgrade, the conversion of system variants (SAP&amp;...) is not required because these variants are always part of the delivery.</p>\r\n<p>The program RASUVAR2 (or RASUVAR_FINISH) reads all reports from the table TASUVAR1 and then tries to import all existing variants for these reports. If an error occurs for a variant, the system imports the parameters individually for this variant. If a parameter cannot be read, the system generates an ABAP using the information for this parameter from TASUVAR1 and uses it to read the data. The imported value is then transferred to the current structure. Once all parameters have been processed, the data of the variant in the current structure is exported to the database again. This ensures that this variant can be imported without errors in the future.</p>\r\n<p>The logs of the programs RASUVAR1 and RASUVAR2 are stored as a file with the upgrade logs on the application server. You can usually view the log files in the relevant directory using transaction AL11. The name is the name of the program (that is, RASUVAR1 or RASUVAR2) followed by a dot and the system name. For the programs RASUVAR_START and RASUVAR_FINISH, the logs can additionally be printed as a list.</p>\r\n<p>To check the results after the conversion of the variants using the programs RASUVAR1 and RASUVAR2 (or RASUVAR_START and RASUVAR_FINISH), the program RASUVAR_DETECT (see SAP Note 1384051) is available in addition to the log files. The log files are often relatively large and unclear. As a better option for analyzing the selection variants, the program RASUVAR_DETECT is provided with SAP Note 1384051. Detailed documentation for the program RASUVAR_DETECT is also available in SAP Note 1384051. This should make it relatively easy to determine which variants still require manual processing after the conversion.</p>\r\n<p>Runtimes of the programs:<br />The runtimes of the programs RASUVAR1 and RASUVAR_START are usually not critical. These programs run before the downtime. The processing logic is also relatively straightforward and therefore quick. The current version of RASUVAR1 also attempts to perform the parallel processing using a server group, if such a server group has been defined in the system. In the program RASUVAR_START, you can select parallel processing using a server group on the selection screen.<br />It is relatively difficult to estimate the runtime of the program RASUVAR2. This depends largely on how many variants in total have been defined in the system (across all clients). Another significant influence is whether the reports for the variants have already been generated or need to be generated by the program RASUVAR2. The runtime also increases in line with the number of variants to be adjusted. The current version of RASUVAR2 also uses parallel processing via a server group if a server group exists in the system. This can reduce the runtime considerably. As already described above, you should terminate RASUVAR2 if excessively long runtimes occur in the upgrade (see SAP Note 1052664). In this case, variants that have already been processed are retained and do not need to be adjusted again. To convert the remaining variants, you should execute the program RASUVAR2 (or RASUVAR_FINISH - with parallel processing, if applicable) after SGEN</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ASU, variants, variant restorer, RASUVAR1, RASUVAR2, RASUVAR_START, RASUVAR_FINISH, RASUVAR_DETECT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-ASU (Application Specific Upgrade)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023370)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023370)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001696821/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001696821/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "712297", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Short dumps occur if you restore variants when upgrading", "RefUrl": "/notes/712297"}, {"RefNumber": "1384051", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "ASU variant restorer: Consistency check after upgrade", "RefUrl": "/notes/1384051"}, {"RefNumber": "1164985", "RefComponent": "EC-EIS-DD", "RefTitle": "Error message DB 634 for variants with period/year", "RefUrl": "/notes/1164985"}, {"RefNumber": "1160685", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Using the ASU variant restorer outside of the technical upgrade", "RefUrl": "/notes/1160685"}, {"RefNumber": "1052664", "RefComponent": "BC-UPG", "RefTitle": "Performance during Upgrade - JOB_RASUVAR2", "RefUrl": "/notes/1052664"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2267140", "RefComponent": "CA-FLE-MAT", "RefTitle": "S4TWL - Material Number Field Length Extension", "RefUrl": "/notes/2267140 "}, {"RefNumber": "2185009", "RefComponent": "PS-IS-LOG", "RefTitle": "Variants of the project info system after upgrade to ECC 617 or later", "RefUrl": "/notes/2185009 "}, {"RefNumber": "1160685", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Using the ASU variant restorer outside of the technical upgrade", "RefUrl": "/notes/1160685 "}, {"RefNumber": "1384051", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "ASU variant restorer: Consistency check after upgrade", "RefUrl": "/notes/1384051 "}, {"RefNumber": "1416105", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Technische Doku: Funktionsweise ASU Variantenretter (intern)", "RefUrl": "/notes/1416105 "}, {"RefNumber": "1052664", "RefComponent": "BC-UPG", "RefTitle": "Performance during Upgrade - JOB_RASUVAR2", "RefUrl": "/notes/1052664 "}, {"RefNumber": "712297", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Short dumps occur if you restore variants when upgrading", "RefUrl": "/notes/712297 "}, {"RefNumber": "1164985", "RefComponent": "EC-EIS-DD", "RefTitle": "Error message DB 634 for variants with period/year", "RefUrl": "/notes/1164985 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}