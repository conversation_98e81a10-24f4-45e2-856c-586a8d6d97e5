{"Request": {"Number": "1137431", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 503, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006775792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001137431?language=E&token=2801A9992D95DC2F29AB46CD727626B5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001137431", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1137431"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.04.2008"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "RE-FX-LC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1137431 - IRE Status Enh.(syncronization w. notice/renewal changes)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>a) In the real estate contracts which were noticed or renewed the recalculation of the IRE data is not triggered.<br /><br />Therefore it is necessary to go the IRE tab page in order to trigger the recalculation of the IRE data after creation or reversal of a notice or after a renewal.<br /><br />b) The minimum IRE amount (of currently 67 Euro) is applied for the status 'Intermediate' too.<br /><br />c) In case of renewals, the field in the IRE folder 'Term' indicating the number of periods until the next IRE record is '1'.<br /><br />d) an IRE record 'N' is created even if the notice date is the same as the regular contract end date,<br /><br />e) in case of notices the payback amount is the same as for the IRE records 'Intermediate' (I)<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, Italy, Localization, IRE, Imposta di Registro, Registration Tax, Contract Status, Notice, Transfer of Ownership, Renewal, Synchronization</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reasons:<br />a) In the legal change IRE Status Enhancement (Note 1129215) the synchronization between notices/renewals and the recalculation of the IRE records was missing.<br /><br />b) minimum IRE amount was not taken into account for renewals<br /><br />c) the number of terms was incorrect for renewals, and therefore also IRE amount was incorrect<br /><br />d) 'N' record is not needed if the notice period is on the contract end date (either original end date or the new end date after renewal). This is the case if the notice is a 'dummy notice' just to terminate an automatic renewal.<br /><br />e) incorrect payback amounts were calculated for notices<br /><br />Prerequisites:<br />Either Support Package SAPKGPAD12, SAPK-60202INEAAPPL or SAPK-60301INEAAPPL<br />or<br />the note 1129215 has to be already installed.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>a) This correction provides the automatic recalculation of the IRE data by:<br />- notice creation/activation/reversal,<br />- renewal rule creation and deletion.<br /><br />This means that after installing this note, it will not be necessary to go to the IRE screen after these changes in the contract just in order to trigger the IRE data recalculation. The change in the notices or renewals will trigger the recalculation directly, so no 'double saving' is needed anymore.<br /><br />b) After installing this note, the minimum IRE amount is considered for renewals<br /><br />c) After installing this note, the number of terms is calculated correctly just like the IRE amount.<br /><br />d) After installing this note, 'N' record will only be created in case of notice if the notice period is before the actual contract end date (original or new end data after renewal). If the notice date is on this end date, no extra 'N' record is created.<br /><br />e) After installing this note the correct payback amount is calculated.<br /><br />Carry out the correction instruction</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}, {"Key": "Processor                                                                                           ", "Value": "D021316"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137431/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301"}, {"RefNumber": "1494103", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: N record is not calculated", "RefUrl": "/notes/1494103"}, {"RefNumber": "1491557", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:amount for advance resolution is incorrectly calculated", "RefUrl": "/notes/1491557"}, {"RefNumber": "1473525", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:N record (notice) is not created-->corr for note 1468460", "RefUrl": "/notes/1473525"}, {"RefNumber": "1468460", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: the system does not create the new N (notice) record", "RefUrl": "/notes/1468460"}, {"RefNumber": "1454091", "RefComponent": "RE-FX-LC-IT", "RefTitle": "'N' type IRE record for posted payback amounts", "RefUrl": "/notes/1454091"}, {"RefNumber": "1399920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback amount calculation incorrectly before rounding", "RefUrl": "/notes/1399920"}, {"RefNumber": "1129215", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enhancements (notice, transfer of ownership)", "RefUrl": "/notes/1129215"}, {"RefNumber": "1122680", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE - Incorrect IRE status by renewal before Save + payback", "RefUrl": "/notes/1122680"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1494103", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: N record is not calculated", "RefUrl": "/notes/1494103 "}, {"RefNumber": "1491557", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:amount for advance resolution is incorrectly calculated", "RefUrl": "/notes/1491557 "}, {"RefNumber": "1473525", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:N record (notice) is not created-->corr for note 1468460", "RefUrl": "/notes/1473525 "}, {"RefNumber": "1468460", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: the system does not create the new N (notice) record", "RefUrl": "/notes/1468460 "}, {"RefNumber": "1129215", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enhancements (notice, transfer of ownership)", "RefUrl": "/notes/1129215 "}, {"RefNumber": "1454091", "RefComponent": "RE-FX-LC-IT", "RefTitle": "'N' type IRE record for posted payback amounts", "RefUrl": "/notes/1454091 "}, {"RefNumber": "1399920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback amount calculation incorrectly before rounding", "RefUrl": "/notes/1399920 "}, {"RefNumber": "1122680", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE - Incorrect IRE status by renewal before Save + payback", "RefUrl": "/notes/1122680 "}, {"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD13", "URL": "/supportpackage/SAPKGPAD13"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60203INEAAPPL", "URL": "/supportpackage/SAPK-60203INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60302INEAAPPL", "URL": "/supportpackage/SAPK-60302INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 3, "URL": "/corrins/0001137431/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "979757 ", "URL": "/notes/979757 ", "Title": "RE-FX Italy: IRE enhancements", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1122467 ", "URL": "/notes/1122467 ", "Title": "IRE Overview Data records interrupted by contract renewal", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1129215 ", "URL": "/notes/1129215 ", "Title": "IRE Status Enhancements (notice, transfer of ownership)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1129215 ", "URL": "/notes/1129215 ", "Title": "IRE Status Enhancements (notice, transfer of ownership)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1129215 ", "URL": "/notes/1129215 ", "Title": "IRE Status Enhancements (notice, transfer of ownership)", "Component": "RE-FX-LC-IT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}