{"Request": {"Number": "828160", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1318, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015865182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000828160?language=E&token=6AA9774D146C2070563613A8B2125A70"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000828160", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000828160/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "828160"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 24}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2016"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "828160 - Migration from Classic RE to RE-FX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Migration tools</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Classic RE, RE-FX, migration<br />FAQ</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Migration from Classic RE to RE-FX</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>As of Release SAP ERP 6.0 (previously SAP ERP 2005 / SAP ECC 6.00, Financials Extension, EA-FIN 600), you can migrate your data from Classic RE to RE-FX. SAP provides migration programs to help you convert Customizing and application data to the new tables in RE-FX, if a 1:1 conversion is possible. For more information, see the presentation attached to this SAP Note.<br /><br />Migrating data from Classic RE to RE-FX is not a simple technical conversion. Similar to a legacy data transfer, the migration must be planned as a project and executed. In this sense, the migration programs are the technical support for the data transfer project. A migration project can be more or less time consuming depending on how complex your data is, the capacity in which you work with customer developments and the extent to which you use the RE-FX functions.<br /><br />For this reason, you have to carefully plan the migration from Classic RE to RE-FX, particularly with regard to the following points:</p>\r\n<ul>\r\n<li>Familiarizing yourself with the structures and concepts of the new application RE-FX</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>relevant training of the project team and (later) the user, and</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>testing the RE-FX functions</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>comparing the processes used currently by the customer in Classic RE with the processes in RE-FX</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Changing the correspondence letters. Note that you can no longer print SAP script forms from RE-FX. This means that you are required to convert all RE forms and RE-specific dunning forms into PDF-based forms or SAP Smart Forms.</li>\r\n</ul>\r\n<ul>\r\n<li>Changing customer developments and modifications so that the functions covered by this are also available in the migrated system</li>\r\n</ul>\r\n<ul>\r\n<li>Note that the migration programs do not currently convert all data. Some functions are not included in the standard migration; these include:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Land use management add-on (LUM), see SAP Note 977295 for more information </li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Condominium owner administration add-on (WEG)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Corporate Real Estate Management add-on (CRE)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Quarter days add-on</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Input tax correction data (correction items, correction object), see SAP Note 1043260 for more information.</li>\r\n</ul>\r\n</ul>\r\n<p>You must read the attached PDF document. This document describes how you should proceed and the errors that can occur. Print out this file and read it before you start the migration.<br /><br />Before you make the first test migration:</p>\r\n<ul>\r\n<li>Import the most up-to-date Support Package status (at least ECC 6.00 Support Package 10) and the SAP Notes based on this status about component RE-FX-MI.</li>\r\n</ul>\r\n<p><br />To test the actual conversion:</p>\r\n<ul>\r\n<li>Call transaction REMICL. In the background, you can use transaction REMICLBATCH instead. The steps that require manual actions (for example, settings in the IMG) must always be carried out online.</li>\r\n</ul>\r\n<ul>\r\n<li>Call the individual migration points step by step. Read the information. These explain how each step works. Check the log after every step.</li>\r\n</ul>\r\n<ul>\r\n<li>If errors occur during migration and if it is not immediately clear to you how you should resolve them, search the attached documentation for the error number. However, note that the documentation does not describe every possible error.<br />If, after having read the documentation and the error message long text, you are still unable to resolve the error, contact SAP Support.</li>\r\n</ul>\r\n<p><strong>Experiences and recommendations from previous migration projects</strong></p>\r\n<ul>\r\n<li>User exits in the migration: see SAP Note 1079141</li>\r\n</ul>\r\n<ul>\r\n<li>Business partners, and addresses:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The FS04 pushbutton in the BPCONSTANTS table must be set to 1: see also SAP Note 1065388.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To avoid problems during the address transfer, deactivate the address check: see SAP Note 1032896.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>You are using the General Real Estate Contract, and have assigned a customer or a vendor in the conditions. In this case, use a customer-specific program to ensure that the TR business partner can be created for these accounts before the migration. See also SAP Note 1090827.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>You have created customer and vendor accounts for business partners. These both have bank details with the same ID (for example 0001), but have different account data. In this case, use a customer-specific program before the migration to ensure that different bank details for the same business partner also have different account IDs. You can use a naming convention to help, for example for the account ID 0001 of the vendor account, you can create the account ID K001 in the business partner. During the migration, ensure that the account ID 0001 is replaced with K001 for vendor conditions. You can do this by using the user exit in SAP Note 1079141.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Accrual/deferral: See SAP Note 980104.</li>\r\n</ul>\r\n<ul>\r\n<li>Service charge settlement: Advance payments posted for non-billed periods are transferred to the \"Advance payments legacy data\" table. This data can only be transferred if the relevant contracts have a corresponding settlement participation, that is, the billing structure must be fully created before the migration so that advance payments are recognized correctly.</li>\r\n</ul>\r\n<ul>\r\n<li>In some cases, inconsistenices can occur in the occupancy history. See SAP Note 1111447 with regards to this.</li>\r\n</ul>\r\n<ul>\r\n<li>To migrate conditions that do not occur in the contract validity period, see SAP Note 997076.</li>\r\n</ul>\r\n<ul>\r\n<li>To improve performance.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>During the transfer, you must keep the database indexes of RE tables up to date. </li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CO settlement rules: See SAP Note 946523.</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p><strong>SAP S/4 HANA release upgrade</strong></p>\r\n<p>Classic RE cannot be used under SAP S/4HANA and is not released. Existing Classic RE customers must migrate to RE-FX before upgrading to the solution \"SAP S/4HANA, on-premise edition\". Note that the display and call of Classic RE data in SAP S/4 HANA are no longer possible.</p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-BD (Basic Data)"}, {"Key": "Other Components", "Value": "RE-FX-MI (Migration)"}, {"Key": "Other Components", "Value": "RE-BD (Basic Data)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D044055)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002072)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000828160/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000828160/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000828160/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000828160/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000828160/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000828160/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000828160/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000828160/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000828160/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "AttachmentHinweisMigrationV4_5_DE.pdf", "FileSize": "378", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000248012005&iv_version=0024&iv_guid=E6882804E520DB4BAE8901472C4C7183"}, {"FileName": "Migration_RE_Classic2RE_FX_DE.ppt", "FileSize": "686", "MimeType": "application/vnd.ms-powerpoint", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000248012005&iv_version=0024&iv_guid=D2285CA46E02594487776341F2B1ABAD"}, {"FileName": "AttachmentHinweisMigrationV4_5_EN.pdf", "FileSize": "447", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000248012005&iv_version=0024&iv_guid=24ED1C55C63CC84F97A1C1F41954A351"}, {"FileName": "Migration_RE_Classic2RE_FX_EN.ppt", "FileSize": "678", "MimeType": "application/vnd.ms-powerpoint", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000248012005&iv_version=0024&iv_guid=7CC8AE76B1D5864E8EC1E3AA1E820B30"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "977295", "RefComponent": "RE-BD", "RefTitle": "Migration LUM von RE-Classic nach RE-FX", "RefUrl": "/notes/977295"}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "865444", "RefComponent": "RE-FX-MI", "RefTitle": "Migration from Classic RE", "RefUrl": "/notes/865444"}, {"RefNumber": "739860", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/739860"}, {"RefNumber": "628208", "RefComponent": "RE-FX", "RefTitle": "Procedures for activating RE Extension in SAP R/3 Enterprise", "RefUrl": "/notes/628208"}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311"}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}, {"RefNumber": "1578783", "RefComponent": "RE-FX", "RefTitle": "Customer: Alternative search helps by real estate", "RefUrl": "/notes/1578783"}, {"RefNumber": "1503676", "RefComponent": "RE-FX-MI", "RefTitle": "Archiving: Deleting \"RE classic\" data under RE-FX", "RefUrl": "/notes/1503676"}, {"RefNumber": "1501199", "RefComponent": "RE-BD", "RefTitle": "Archiving: Deleting \"Classic RE\" data in RE-FX", "RefUrl": "/notes/1501199"}, {"RefNumber": "1449490", "RefComponent": "RE-FX-MI", "RefTitle": "Table VICAINTRENO is not filled after migration", "RefUrl": "/notes/1449490"}, {"RefNumber": "1448446", "RefComponent": "RE-FX-MI", "RefTitle": "Unnecessary system statuses in rental objects and contracts", "RefUrl": "/notes/1448446"}, {"RefNumber": "1410237", "RefComponent": "RE-FX-LC-CH", "RefTitle": "Central note for REAL ESTATE (CLASSIC & FX) Swiss spec.", "RefUrl": "/notes/1410237"}, {"RefNumber": "1407804", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Exit for calculation rule", "RefUrl": "/notes/1407804"}, {"RefNumber": "1357750", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Limited partner assignments", "RefUrl": "/notes/1357750"}, {"RefNumber": "1286751", "RefComponent": "RE-FX-MI", "RefTitle": "Long texts for error messages from migration", "RefUrl": "/notes/1286751"}, {"RefNumber": "116283", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for XIS-RE-C", "RefUrl": "/notes/116283"}, {"RefNumber": "1111447", "RefComponent": "RE-FX-MI", "RefTitle": "MIgration: incorrect occupancy history", "RefUrl": "/notes/1111447"}, {"RefNumber": "1043260", "RefComponent": "RE-FX-IT", "RefTitle": "Input tax correction: Data transfer from Classic RE", "RefUrl": "/notes/1043260"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2270550", "RefComponent": "RE-FX", "RefTitle": "S4TWL - Real Estate Classic", "RefUrl": "/notes/2270550 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "1410237", "RefComponent": "RE-FX-LC-CH", "RefTitle": "Central note for REAL ESTATE (CLASSIC & FX) Swiss spec.", "RefUrl": "/notes/1410237 "}, {"RefNumber": "1407804", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Exit for calculation rule", "RefUrl": "/notes/1407804 "}, {"RefNumber": "1357750", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Limited partner assignments", "RefUrl": "/notes/1357750 "}, {"RefNumber": "1111447", "RefComponent": "RE-FX-MI", "RefTitle": "MIgration: incorrect occupancy history", "RefUrl": "/notes/1111447 "}, {"RefNumber": "1449490", "RefComponent": "RE-FX-MI", "RefTitle": "Table VICAINTRENO is not filled after migration", "RefUrl": "/notes/1449490 "}, {"RefNumber": "1448446", "RefComponent": "RE-FX-MI", "RefTitle": "Unnecessary system statuses in rental objects and contracts", "RefUrl": "/notes/1448446 "}, {"RefNumber": "1501199", "RefComponent": "RE-BD", "RefTitle": "Archiving: Deleting \"Classic RE\" data in RE-FX", "RefUrl": "/notes/1501199 "}, {"RefNumber": "1578783", "RefComponent": "RE-FX", "RefTitle": "Customer: Alternative search helps by real estate", "RefUrl": "/notes/1578783 "}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311 "}, {"RefNumber": "1503676", "RefComponent": "RE-FX-MI", "RefTitle": "Archiving: Deleting \"RE classic\" data under RE-FX", "RefUrl": "/notes/1503676 "}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}, {"RefNumber": "1286751", "RefComponent": "RE-FX-MI", "RefTitle": "Long texts for error messages from migration", "RefUrl": "/notes/1286751 "}, {"RefNumber": "1043260", "RefComponent": "RE-FX-IT", "RefTitle": "Input tax correction: Data transfer from Classic RE", "RefUrl": "/notes/1043260 "}, {"RefNumber": "865444", "RefComponent": "RE-FX-MI", "RefTitle": "Migration from Classic RE", "RefUrl": "/notes/865444 "}, {"RefNumber": "628208", "RefComponent": "RE-FX", "RefTitle": "Procedures for activating RE Extension in SAP R/3 Enterprise", "RefUrl": "/notes/628208 "}, {"RefNumber": "977295", "RefComponent": "RE-BD", "RefTitle": "Migration LUM von RE-Classic nach RE-FX", "RefUrl": "/notes/977295 "}, {"RefNumber": "116283", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for XIS-RE-C", "RefUrl": "/notes/116283 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}