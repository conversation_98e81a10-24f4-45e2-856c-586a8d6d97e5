{"Request": {"Number": "1620576", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 393, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017292142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001620576?language=E&token=2A43AF4BA3A92A880A9F01443F823F84"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001620576", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001620576/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1620576"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.03.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-BUS-HTM"}, "SAPComponentKeyText": {"_label": "Component", "value": "HTML"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Netweaver Business Client", "value": "BC-FES-BUS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-BUS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "HTML", "value": "BC-FES-BUS-HTM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-BUS-HTM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1620576 - NWBC for HTML and Runtime: Prerequisites and restrictions"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains a summary of the prerequisites and restrictions for live operation of SAP NetWeaver Business Client (NWBC) for HTML and NWBC Runtime.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>NWBC for HTML, zero footprint, runtime</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>These aspects apply on top of the prerequisites and restrictions stated for SAP Business Client. For more information, see SAP Note 2446515, in particular, for the current Version 6.5.<br />For more information about the NWBC, see Note 900000.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Restrictions</strong></p>\r\n<ul>\r\n<li>NWBC for HTML/Runtime 3.0 is no longer supported as of JAN/01/2015. The successor version 3.5 replaces the stated predecessor version as of the following SP levels:<br />[SAP_BASIS] 700:SP28; 701:SP12; 702:SP12; 711:SP11; 730:SP09; 731:SP00;<br />No support is undertaken for older release levels,<br /><br /></li>\r\n<li>NWBC for HTML/Runtime 3.5 is no longer supported as of SAP_UI 753. The successor version 3.6 replaces the previous named version.<br />For more information, see SAP Note 2561642.<br /><br /></li>\r\n<li>The business client supports service users as of ABAP Runtime Patch Level 37 only.<br /><br /></li>\r\n<li>The following SAP frameworks are supported by NWBC for HTML:</li>\r\n<ul>\r\n<li>Web Dynpro ABAP and Floorplan Manager (FPM)</li>\r\n<li>SAP GUI for HTML</li>\r\n<li>BSP 2008</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In the case of the usage of other frameworks or applications, the relevant component must be released.</p>\r\n<ul>\r\n<li>In NWBC for HTML, only HTML applications can be displayed. The display of PDF files and other non-HTML file types, for example, is not supported.<br /><br />&#x00A0;</li>\r\n<li>HTML applications that are not from SAP are not supported. However, if these types of applications do work, you can use them but we will not provide any guarantees for them. We do not actively support these types of scenarios. In particular, restrictions apply to HTML-based applications that open new browser windows: The communication between the various browser windows does not work.<br /><br /></li>\r\n<li>The embedding of NWBC for HTML in the SAP NetWeaver Portal is not supported:<br />NWBC for HTML can be integrated in external portals in an embedded mode - that is, without a navigation frame - using the addition \"/~canvas;window=embedded\". If it is integrated in an SAP NetWeaver Portal, problems occur in particular with object-based navigation (OBN), for example, as it used in power worklists (POWL).<br /><br /></li>\r\n<li>The portal eventing functionality is offered only in scenarios in which NWBC for HTML is <span style=\"text-decoration: underline;\">not</span> embedded using iFrame. In particular, this means that eventing is not supported in FLP scenarios in which NWBC is embedded (invisibly).<br /><br /></li>\r\n<li>Suspend/resume scenarios are supported in NWBC for HTML only in application windows. In main windows (even those for which certain areas are hidden with the ~canvas notation), suspend/resume is not supported.<br /><br /></li>\r\n<li>The initial window size and position cannot be affected. User settings for the size and position of the browser window are not restored when you open the window again.<br /><br /></li>\r\n<li>Customer themes are supported in the following versions and releases:</li>\r\n<ul>\r\n<li>NWBC for HTML 3.6 in UIAddOn Version 1 as of SP16</li>\r\n<li>NWBC for HTML 3.6 in UIAddOn Version 2 as of SP04</li>\r\n<li>NWBC for HTML 3.6&#x00A0;as of Release 7.40 SP04</li>\r\n<li>NWBC for HTML 3.5 as of Release 7.50</li>\r\n<li>In older SP levels, NWBC for HTML 3.6 supports customer themes only for service maps.&#x00A0;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>If you use NWBC for HTML in accessibility mode, only two themes are supported: \"sap_tradeshow_plus\" and \"sap_hcb\". If a different theme is selected, NWBC for HTML reverts to \"sap_tradeshow_plus\". In particular, no customer themes are supported in this scenario.<br /><br /></li>\r\n<li>Generally, NWBC for HTML is not called with the \"sap-ie\" parameter because otherwise the NWBC-side heuristic for determining the rendering mode would be overwritten.<br />The only exceptions are:<br />- NWBC is embedded in an iFrame. Here, the correct specification of the \"sap-ie\" parameter is mandatory.<br />- For test purposes<br />Moreover, NWBC for HTML cannot be used in \"Enterprise\" mode, since this also leads to an incorrect determination of the rendering mode by NWBC.<br /><br /></li>\r\n<li>URLs for so-called application windows cannot be executed again. This means that they can also not, for example, be stored in the form of a bookmark or in another form and then processed again later on. The background is that application windows are always opened by the NWBC using the HTTP-POST method. Thus, URL parameters are always missing from the URL in the address line of the browser.&#x00A0;</li>\r\n</ul>\r\n<ul>\r\n<li>Application parameters for NWBC URLs with the addition \"~canvas\" are not saved. If navigation to the root node takes place here, as is the case, for example, for a restart from the logoff confirmation popup, the application determined using the ~canvas notation is called without application parameters.&#x00A0;</li>\r\n</ul>\r\n<ul>\r\n<li>NWBC for HTML cannot have any influence on unsaved data in SAP GUI for Windows/SAP GUI transactions. If there is unsaved data in transactions, it is not possible for NWBC for HTML to display a corresponding warning message when a browser window is closed, for example.&#x00A0;</li>\r\n</ul>\r\n<p><strong>Warnings and additional information</strong></p>\r\n<ul>\r\n<li>Remote SAP GUI for Windows calls cannot be supported and are converted into remote SAP Web GUI calls, unless the administrator explicitly prevents this using Customizing. In particular, you must be careful with the PFCG indicator \"Force SAP GUI in HTML\" when defining OBN targets if you want to ensure that NWBC for HTML is fully supported by the maintained role.</li>\r\n</ul>\r\n<p><strong>Supported browsers</strong></p>\r\n<p>For general information about browser support, see the Product Availability Matrix (PAM = see SAP Note 900000).<br />The SAP NetWeaver release determines which browser is supported for the NWBC for HTML.</p>\r\n<p><em>Additional restrictions:</em><br /><br />Microsoft Edge (Windows 10): Firefox in extended support release cycle</p>\r\n<ul>\r\n<li>\r\n<p>NWBC for HTML 3.5 as of SAP NetWeaver Release 7.40 SP17,&#x00A0;7.50 SP4, or 7.51 SP0 with Patch Level 53 (SAP Note 2302129)</p>\r\n</li>\r\n<li>\r\n<p>NWBC for HTML 3.6 in SAP NetWeaver Release 7.40 SP17 with UI Add-On 1.0 SP17 and SAP NetWeaver Release 7.50 SP4 or 7.51 SP0 in Release 7.50 with Patch Level 53 (SAP Note 2302129)</p>\r\n</li>\r\n</ul>\r\n<p>Note that these aspects of browser support relate only to shell rendering; no statements are made about the canvas (application).<br /><br />With regard to Safari, there is a distinction between the shell and the actual application in the case of NWBC for HTML:</p>\r\n<ul>\r\n<li>The shell supports Safari from the back end.</li>\r\n</ul>\r\n<ul>\r\n<li>For Web Dynpro and SAP GUI for HTML applications, a minimum SAP_BASIS release of 7.02 SP04 is required.&#x00A0;For more information, see the PAM and the following SAP Note:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>1446519 Safari Limitations for Unified Rendering and ACF</li>\r\n</ul>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D043277)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D043277)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001620576/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001620576/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "900000", "RefComponent": "BC-FES-BUS", "RefTitle": "Netweaver Business Client - FAQ", "RefUrl": "/notes/900000"}, {"RefNumber": "2561642", "RefComponent": "BC-FES-BUS", "RefTitle": "NWBC ABAP Runtime 3.5 is obsolete", "RefUrl": "/notes/2561642"}, {"RefNumber": "2446515", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "SAP Business Client 6.5: Prerequisites and restrictions", "RefUrl": "/notes/2446515"}, {"RefNumber": "2227396", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "SAP Business Client 6.0: Prerequisites and restrictions", "RefUrl": "/notes/2227396"}, {"RefNumber": "2066345", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC for Desktop 5.0: Prerequisites and restrictions", "RefUrl": "/notes/2066345"}, {"RefNumber": "1885000", "RefComponent": "SCM-EWM-ANA", "RefTitle": "Implementation recommendations for SAP EWM 9.1", "RefUrl": "/notes/1885000"}, {"RefNumber": "1754946", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC for Desktop 4.0: Prerequisites and restrictions", "RefUrl": "/notes/1754946"}, {"RefNumber": "1728946", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Browser Support Strategy for NetWeaver", "RefUrl": "/notes/1728946"}, {"RefNumber": "1672817", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note", "RefUrl": "/notes/1672817"}, {"RefNumber": "1620514", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC 3.5 for Desktop: Prerequisites and restrictions", "RefUrl": "/notes/1620514"}, {"RefNumber": "1583947", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1583947"}, {"RefNumber": "1446519", "RefComponent": "BC-WD-UR", "RefTitle": "Safari Restrictions for Unified Rendering and ACF", "RefUrl": "/notes/1446519"}, {"RefNumber": "1029940", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "SAP NWBC 3.0 prerequisites and restrictions", "RefUrl": "/notes/1029940"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1864151", "RefComponent": "BC-FES-BUS-RUN", "RefTitle": "How to determine the version and Patch Level of NWBC Runtime Environment", "RefUrl": "/notes/1864151 "}, {"RefNumber": "2602570", "RefComponent": "EP-PIN-NAV-FFP", "RefTitle": "OBN Navigations do not work correctly using Remote Content via FLP@EP", "RefUrl": "/notes/2602570 "}, {"RefNumber": "2591827", "RefComponent": "BC-FES-BUS-RUN", "RefTitle": "Business Client on mobile devices or tablets", "RefUrl": "/notes/2591827 "}, {"RefNumber": "2571553", "RefComponent": "BC-FES-BUS-HTM", "RefTitle": "Unexpected error during Suspend / Resume scenarios using Business Client", "RefUrl": "/notes/2571553 "}, {"RefNumber": "2841669", "RefComponent": "BC-FES-BUS-HTM", "RefTitle": "Using SRM and Catalog Scenarios within FLP", "RefUrl": "/notes/2841669 "}, {"RefNumber": "1852400", "RefComponent": "CA-UI2-THD", "RefTitle": "UI theme designer (main note)", "RefUrl": "/notes/1852400 "}, {"RefNumber": "1353538", "RefComponent": "BC-FES-BUS", "RefTitle": "NWBC -Patch Collection- SERVER SIDE (ABAP)+NWBC for HTML", "RefUrl": "/notes/1353538 "}, {"RefNumber": "2114256", "RefComponent": "XX-PART-MFS-LOR", "RefTitle": "Scripting NWBC for SAP Loadrunner by Micro Focus", "RefUrl": "/notes/2114256 "}, {"RefNumber": "2052665", "RefComponent": "SCM-EWM-ANA", "RefTitle": "Implementation recommendations for SAP EWM 9.2", "RefUrl": "/notes/2052665 "}, {"RefNumber": "1599159", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: IE: Internet Explorer 9 Release Note", "RefUrl": "/notes/1599159 "}, {"RefNumber": "1885000", "RefComponent": "SCM-EWM-ANA", "RefTitle": "Implementation recommendations for SAP EWM 9.1", "RefUrl": "/notes/1885000 "}, {"RefNumber": "1672817", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note", "RefUrl": "/notes/1672817 "}, {"RefNumber": "1754946", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC for Desktop 4.0: Prerequisites and restrictions", "RefUrl": "/notes/1754946 "}, {"RefNumber": "1728946", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Browser Support Strategy for NetWeaver", "RefUrl": "/notes/1728946 "}, {"RefNumber": "1826387", "RefComponent": "PPM-PRO", "RefTitle": "SAP Portfolio and Project Management 6.0 : Supported Browsers, Java versions, etc.", "RefUrl": "/notes/1826387 "}, {"RefNumber": "900000", "RefComponent": "BC-FES-BUS", "RefTitle": "Netweaver Business Client - FAQ", "RefUrl": "/notes/900000 "}, {"RefNumber": "1620514", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC 3.5 for Desktop: Prerequisites and restrictions", "RefUrl": "/notes/1620514 "}, {"RefNumber": "1029940", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "SAP NWBC 3.0 prerequisites and restrictions", "RefUrl": "/notes/1029940 "}, {"RefNumber": "1446519", "RefComponent": "BC-WD-UR", "RefTitle": "Safari Restrictions for Unified Rendering and ACF", "RefUrl": "/notes/1446519 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}