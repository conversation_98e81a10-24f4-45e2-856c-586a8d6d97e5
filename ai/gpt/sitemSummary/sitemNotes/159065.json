{"Request": {"Number": "159065", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 451, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000771282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000159065?language=E&token=9E36CBD7EF02A25F3BBF33D0DC83FA58"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000159065", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000159065/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "159065"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.07.1999"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BMT-WFM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Workflow"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Management", "value": "BC-BMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Workflow", "value": "BC-BMT-WFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT-WFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "159065 - Data selection for work item archiving"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The data selection of the data for archiving takes an extremely long time for the archiving object or the archiving class workitem within the archiving program RSWWARCA.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ADK, archives development kit, performance, work item managers, WIM, workflow</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When determining the chosen work items that are to be archived via the selection screen of the report RSWWARCA, first all the columns of table SWWWIHEAD are read even though at this point, this is not necessary for the section of the workitem to be activated.<br />It is more sensible first to read the columns from table SWWWIHEAD, via which the conditions inside the program are formulated or which can be used later for a sensible statistical output.<br />Note that all data (that is, all columns in table SWWWIHEAD) must, of course, also be read for the actual writing of data in the archive in function module WORKITEM_ARCHIVE_OBJECT. However, this occurs at a later time after the amount of work items to be archived has already been determined.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>First implement the structure SWWARCHEAD (that means, PRIOR TO the attached corrections) (for example, in the development class SWW). This structure must contain the fields (in this sequence)</p> <UL><LI>WI_ID</LI></UL> <UL><LI>WI_TYPE</LI></UL> <UL><LI>WI_TEXT</LI></UL> <UL><LI>WI_CD</LI></UL> <UL><LI>WI_AED</LI></UL> <UL><LI>WI_AAGENT</LI></UL> <UL><LI>WI_RH_TASK<br /></LI></UL> <p>from table SWWWIHEAD.<br />Implement the attached corrections after this. Note that also the table parameters of the interface must be changed for function module SWW_WI_LIST_ARCHIVE.<br />As of Release 4.6C, the procedure is contained in the standard system.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D019512"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000159065/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000159065/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159065/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159065/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159065/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159065/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159065/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159065/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159065/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "573656", "RefComponent": "BC-BMT-WFM", "RefTitle": "Collective note relating to Archiving in workflow", "RefUrl": "/notes/573656"}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622"}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285"}, {"RefNumber": "304597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to Release 4.6C  FCS", "RefUrl": "/notes/304597"}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949"}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373"}, {"RefNumber": "155674", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Release 4.6A", "RefUrl": "/notes/155674"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "573656", "RefComponent": "BC-BMT-WFM", "RefTitle": "Collective note relating to Archiving in workflow", "RefUrl": "/notes/573656 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285 "}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622 "}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373 "}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371 "}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949 "}, {"RefNumber": "155674", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Release 4.6A", "RefUrl": "/notes/155674 "}, {"RefNumber": "304597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to Release 4.6C  FCS", "RefUrl": "/notes/304597 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31G", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0000159065/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}