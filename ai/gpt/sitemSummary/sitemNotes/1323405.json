{"Request": {"Number": "1323405", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 307, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016764102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001323405?language=E&token=B06EAAA1F649B94515841DAA356403B2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001323405", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001323405/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1323405"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 38}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1323405 - Technical Preparation of a CQC BPPO service"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>A Business Process Performance Optimization (BPPO) session has been scheduled. You are asked to prepare your system(s) for the service.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BPPO, CQC, GSS Perf</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>During a CQC BPPO service session the SAP service engineer analyses the data processing of your SAP software applications. He checks the performance by performing a trace analysis during the execution of the application.<br /><br />Note:</p>\r\n<ul>\r\n<li>For a most effective service you need to tell the SAP service engineer which business process steps are&#160;not working in an optimal way.</li>\r\n</ul>\r\n<ul>\r\n<li>For a reliable analysis of the application you should have finished all related developments including customizing. Optimally this service is performed at the end of Integration Testing phase or in operation phase.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In order for the service to go ahead successfully, we recommend that you review all the points in this Note in order to ensure that your system is ready for the delivery of the service.<br /><br />Please ensure that the basic prerequisites from SAP Note 91488 are fulfilled.<br /><br />This note provides you with information about the additional preparation steps specific to BPPO service delivery.<br /><br /><br /><strong>Content:</strong><br /><strong>General Requirements</strong><br /><strong>Customer Development Activities</strong><br /><strong>&#160;&#160;Customizing</strong><br /><strong>&#160;&#160;Data Preparation</strong><br /><strong>Execution of Business Process Steps</strong><br /><strong>Requirements for Analysed Systems</strong><br /><strong>&#160;&#160;Required SAP Kernel release</strong><br /><strong>&#160;&#160;Authorisation for Tools in Analysed System</strong><br /><strong>&#160;&#160;Current Service Tools</strong><br /><strong>&#160;&#160;System Settings for Service Tools</strong><br /><strong>&#160;&#160;&#160;&#160;STAD - Statistical Records</strong><br /><strong>&#160;&#160;&#160;&#160;ST05 - SQL trace</strong><br /><strong>&#160;&#160;&#160;&#160;ST03N - Workload Monitor</strong><br /><strong>&#160;&#160;&#160; SMICM - ICM Monitor</strong><br /><strong>Requirements for Delivery Platforms</strong><br /><strong>&#160; Requirements for Service Delivery in SAP Internal systems</strong><br /><strong>&#160;&#160;Requirements for Service Delivery in Solution Manager</strong><br /><strong>Additional Information for Specific Applications</strong><br /><br /><br /><strong><strong><span style=\"text-decoration: underline;\">General Requirements</span></strong></strong></p>\r\n<ol>\r\n<li><strong>Customer Development Activities<br /></strong>Any changes to objects that will be checked as part of the SAP Session must be completed before this session starts. To ensure the Optimization Session runs efficiently, your transactions and programs must run without errors.</li>\r\n<ol></ol>\r\n<li><strong>Customizing<br /></strong>Any changes to Customizing parameters must be completed before this session starts or frozen for the duration of the session.<br /><br />The analysis session should be performed prior to the Optimization Session. This analysis examines the Basis parameters of your production system and provides recommendations for improving the performance and availability of your SAP system. Some of these parameters affect performance significantly and we therefore recommend that you implement all recommendations before a performance analysis is executed for single business process steps or interfaces.</li>\r\n<li><strong>Data Preparation<br /></strong>The data required for this session must represent your normal daily operations. The transactions and data to be used for this session must be suitable for a trial run of all the functions you use in your production environment. All transactions can be executed several times and you should therefore prepare a reusable scenario.</li>\r\n<li><strong>Execution of Business Process Steps<br /></strong>To test the transactions and programs, we will ask you to execute them while we are performing traces or to provide us with a test script which we could execute ourselves. While executing a test script the SAP service engineer will post data in the system.</li>\r\n</ol>\r\n<p><br /><strong><span style=\"text-decoration: underline;\">Requirements for Analysed Systems</span></strong></p>\r\n<ol>\r\n<li><strong>Required SAP Kernel release<br /></strong>Please ensure that the SAP Kernel release is suitable for performance traces. Please check the following SAP Notes, implement a Kernel patch level with the corrections if necessary.</li>\r\n<ul>\r\n<li>SAP Notes 981919 (affected kernel 6.40,7.00), 1076804&#160;(affected kernel 4.6D - 7.10) and 1132350 (affected kernel 7.00,701,710,711).</li>\r\n<li>SAP Note 1795612 (affected Kernel&#160;&#160;7.20 patch levels 321-399 and 7.01 patch levels 193-...)</li>\r\n<li>SAP Note 1726888 (affected Kernels are 7.10 to 7.38).&#160;</li>\r\n<li>SAP Note&#160;3309953 (affected Kernels 7.22, 7.53, 7.54, 7.77, 7.81, 7.85, 7.89 - 7.92)&#160;</li>\r\n</ul>\r\n<li><strong>Authorisation for Tools in Analysed System<br /></strong>To perform a system analysis SAP requires users who are equipped with suitable authorization profiles. If due to security concerns a user with \"SAP_ALL\" authorization can not be given, a user RSDUSER can be constructed, which is described in SAP Note 1405975.</li>\r\n<li><strong>Current Service Tools<br /></strong>Use report RTCCTOOL as per SAP Note 69455 to ensure that the newest versions of add-ons ST-PI and ST-A/PI are implemented. Among other things the add-ons contain function modules and data collectors which ensure the quality of the service data collection. Important analysis tools like transaction ST12 and tools available via transaction ST13 are also part of ST-A/PI. For more detailed information see SAP Note 69455 (ST-A/PI) and 539977 (ST-PI).&#160;<br />In case you are not able to implement the latest version of ST-A/PI, be informed about the minimum requirements for ST-A/PI:<br />-&#160;for satellite systems except BW: 01N<br />- for BW WHM processes (Data loading): 01Q<br />- for BW Reporting processes: 01R<br />- IMPORTANT: for the satellite systems with SAP Basis 7.50 the version of ST-A/PI &gt;= 01S* SP00 is needed in order to switch on SQL trace.</li>\r\n<li><strong>System Settings for Service Tools</strong></li>\r\n<ul style=\"list-style-type: lower-alpha;\">\r\n<li><strong>STAD - Statistical Records<br /></strong>For individual analyses DB table access statistics need to be activated. Set the value of variable STAT/TABREC to 10. It can be activated online and no restart of an instance is necessary.&#160;&#160;You can do this via transaction ST03 -&gt; Online Parameters -&gt; Dialog step statistics.<br />You should activate the parameter only temporarily because, otherwise, performance problems and memory problems may occur in the statistics collector.&#160;</li>\r\n<li><strong>ST05 - SQL trace<br /></strong>Since basis 6.40: To avoid SQL traces from being overwritten, extend the parameter rstr/max_filesize_MB to 100 MB via transaction ST01 -&gt; GoTo -&gt; Administration. This parameter needs to be set on each instance. It can be activated online. After service delivery set the parameter to default file size 16 MB back again.</li>\r\n<li><strong>ST03N - Workload Monitor<br /></strong>The following is very useful ONLY for WebDynpro ABAP and BSP Applications.<br />Check if the resolution of the URL in the transaction profile of ST03N is set to 'Break-Down by URL' or 'Breakdown by Application.</li>\r\n<ul>\r\n<li>In a system with SAP NW 7.0 (7.00,&#160;&#160;7.01, 7.02 etc.) run report SWNC_CONFIG_URL (see SAP note 992474) and execute it with setting 'Display Current Configuration'. If the result is 'SAPMHTTP as Transaction Name in Transaction Profile' all Web Application will show up as SAPMHTTP in the transaction profile. Run the report again with setting 'Break-Down by URL' to switch the resolution.</li>\r\n<li>In a system with SAP NW &lt; 7.0 (e.g. 6.40 or 7.10) you can find the setting in ST03N -&gt; Collector and Performance DB -&gt; Workload Collector -&gt; Statistics to be created. Check the settings for 'Web Application Server Statistics'. Change the setting to 'Breakdown by Application' if necessary.</li>\r\n</ul>\r\n<li><strong>SMICM - ICM Monitor<br /></strong>The following is very useful for WebDynpro ABAP, BSP and Web UI applications.<br />Check if the ICM HTTP Server Log is activated and written correctly. Go to transaction SMICM. Use the menu path 'Goto -&gt; HTTP Log -&gt; Server -&gt; Display Entries' to check if the log is written at all. Use menu path 'Goto -&gt; Parameters -&gt; Display' to check profile parameter 'icm/HTTP/logging_*' (* is usually 0 but could be any consecutive number)<br />Below Kernel 7.00 PL 118 or 7.10 SP4 set value of parameter icm/HTTP/logging_* to PREFIX=/, LOGFORMAT=SAPSMD, LOGFILE=icmhttp.log, MAXSIZEKB=10240, SWITCHTF=day, FILEWRAP=on.<br />With Kernel 7.01 and 7.02 and starting with Kernel 7.00 PL 118 or 7.10 SP4 it is possible to restrict the amount of entries written into the http log file by a filter. By setting the parameter icm/HTTP/logging_* to PREFIX=/, LOGFILE=icmhttph.log, FILTER=SAPSMD, LOGFORMAT=SAPSMD2, MAXSIZEKB=10240,FILEWRAP=on, SWITCHTF=month <br />Only requests containing the X-CorrelationId are written into that file. After parameter change the ICM alone needs to be restarted. There is no need to restart the instance. The activated log file has no impact on system performance. The content of the file will be deleted automatically after some time. Therefore you can keep these settings for later services. For further details see note 1252944.</li>\r\n</ul>\r\n</ol>\r\n<p><br /><strong><span style=\"text-decoration: underline;\">Requirements for Delivery Platforms</span></strong></p>\r\n<p><span style=\"font-size: 14px;\">BPPO session can be delivered on SAP internal systems or customer solution manager. The preferred delivery model would be through SAP internal systems, which greatly reduces preparation time for customers. However, we can still deliver through a customer&#8217;s SAP Solution Manager upon request. Please see </span><a target=\"_blank\" href=\"https://me.sap.com/notes/2899458\">SAP Note 2899458- Remote Service Delivery without SAP Solution Manager</a><span style=\"font-size: 14px;\"> for additional details.</span></p>\r\n<ol start=\"1\" style=\"list-style-type: decimal;\">\r\n<li><strong>Requirements for the delivery through SAP internal systems:</strong></li>\r\n<ul>\r\n<li>Managed system is sending EWA data periodically to SAP.</li>\r\n</ul>\r\n</ol><ol start=\"2\" style=\"list-style-type: decimal;\">\r\n<li>\r\n<p><strong>Requirements for the delivery through customer's SAP Solution Manager:</strong></p>\r\n</li>\r\n</ol>\r\n<ul><ol style=\"list-style-type: lower-alpha;\">\r\n<li><strong>Technical Requirements for Service Delivery in Solution Manager</strong></li>\r\n<ul>\r\n<li>Refer to&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2253047\">SAP Note 2253047- Technical prerequisites for service delivery in SAP Solution Manager 7.2</a></li>\r\n<li>In case you are not able to implement the latest version of ST-A/PI, the minimum requirements for ST-A/PI in Solution Manager: 01P</li>\r\n</ul>\r\n</ol></ul>\r\n<p><strong><span style=\"text-decoration: underline;\">Additional Information for Specific Applications</span></strong></p>\r\n<p>Further application specific information:</p>\r\n<ul>\r\n<li>SAP Business Objects IDD / EIM</li>\r\n<li>For the (CQC) BPPO service delivery for SAP BusinessObjects IDD / EIM refer to the prerequisites listed in the SAP Note 1646341 - EarlyWatch Alert for SAP BusinessObjects IDD / EIM.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP BI, SAP SCM<br />For the analysis of batch job chains ST-A/PI version 01Q (or higher) is required in the managed system.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I029788)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I055494)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001323405/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001323405/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "872800", "RefComponent": "SV-SMG-SER", "RefTitle": "Roles for SAP Solution Manager: SAP service provider", "RefUrl": "/notes/872800"}, {"RefNumber": "2065631", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-SER 701_2010_1 SP25: Correction for failing Report Generation of BPPO Sessions", "RefUrl": "/notes/2065631"}, {"RefNumber": "1941181", "RefComponent": "SV-SMG-SER", "RefTitle": "ST12 traces cannot be fetched into Performance analysis checks (BPPO TIC service)", "RefUrl": "/notes/1941181"}, {"RefNumber": "1726888", "RefComponent": "BC-ABA-LA", "RefTitle": "SE30: Semaphore 51 blocks processes", "RefUrl": "/notes/1726888"}, {"RefNumber": "1646341", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert for SAP BusinessObjects IDD / EIM", "RefUrl": "/notes/1646341"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1601515", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Incorrect decimal values in tables after save", "RefUrl": "/notes/1601515"}, {"RefNumber": "1498779", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing Support Services for Systems with a Java Stack", "RefUrl": "/notes/1498779"}, {"RefNumber": "1405975", "RefComponent": "SV-SMG-SER", "RefTitle": "Minimum Authorization Profile for Remote Service Delivery", "RefUrl": "/notes/1405975"}, {"RefNumber": "1295142", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP SRM", "RefUrl": "/notes/1295142"}, {"RefNumber": "1274287", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics 7.0 EHP1 SP18 to SP30", "RefUrl": "/notes/1274287"}, {"RefNumber": "1258585", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Remote Services for SAP CRM", "RefUrl": "/notes/1258585"}, {"RefNumber": "1252944", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E Trace: Problem retrieving data from managed system.", "RefUrl": "/notes/1252944"}, {"RefNumber": "1172939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1172939"}, {"RefNumber": "1126859", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics SP15-SP17", "RefUrl": "/notes/1126859"}, {"RefNumber": "1010428", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics", "RefUrl": "/notes/1010428"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2770247", "RefComponent": "SV-PERF", "RefTitle": "SV-PERF: Guidelines when opening a Customer Incident", "RefUrl": "/notes/2770247 "}, {"RefNumber": "144864", "RefComponent": "SV-SMG-SER", "RefTitle": "Setting Up the ABAP Workload Monitor", "RefUrl": "/notes/144864 "}, {"RefNumber": "1646341", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert for SAP BusinessObjects IDD / EIM", "RefUrl": "/notes/1646341 "}, {"RefNumber": "1525473", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Long Processing Time For EWA Due to BW Access", "RefUrl": "/notes/1525473 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "1498779", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing Support Services for Systems with a Java Stack", "RefUrl": "/notes/1498779 "}, {"RefNumber": "1726888", "RefComponent": "BC-ABA-LA", "RefTitle": "SE30: Semaphore 51 blocks processes", "RefUrl": "/notes/1726888 "}, {"RefNumber": "1295142", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP SRM", "RefUrl": "/notes/1295142 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "1258585", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Remote Services for SAP CRM", "RefUrl": "/notes/1258585 "}, {"RefNumber": "1601515", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Incorrect decimal values in tables after save", "RefUrl": "/notes/1601515 "}, {"RefNumber": "1491461", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Dump TSV_TNEW_PAGE_ALLOC_FAILED When Processing EWA", "RefUrl": "/notes/1491461 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}