{"Request": {"Number": "826402", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2092, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004471792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000826402?language=E&token=540B2452971CC1C11CC6C639CEF29519"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000826402", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "826402"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Currentness", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.03.2005"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "826402 - IS-H AT: Changes to processing of intensive care data"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=826402&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/826402/D\" target=\"_blank\">/notes/826402/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Austria (AT).<br />New indicators must be added so that the system can differentiate between data entered and data that has not been entered for intensive care data.<br />The system takes these into account accordingly in the BAPIs and in intensive care scoring (that is, fields that have not been entered are reported as &quot;blank&quot;).<br />Note that the BAPI interfaces have been changed and therefore you must adjust the calling programs accordingly.<br />For more information, see the BAPI documentation.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>NWATPHYS, NWATTRISS, physiological data, entry of traumatological patients, intensive care documentation</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>See symptom.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>This SAP Note contains correction instructions and an attachment. You must implement this before you can implement the changes from the correction instructions in your system.</p> <OL>1. Unpack the attached file HW826402_463B.ZIP for Release 4.63B AOP 01 - 25 or HW826402_472.ZIP for Release 4.72 AOP 01 - 08.</OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the unpacked requests into your system.</OL> <OL>3. Then implement the correction instructions in your system.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (C5008711)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000826402/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW826402_463B.ZIP", "FileSize": "21", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000228322005&iv_version=0003&iv_guid=82A0C71FB8D9964884C3D8F162912C32"}, {"FileName": "HW826402_472.ZIP", "FileSize": "22", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000228322005&iv_version=0003&iv_guid=E239B79A8A16154AA24FE8FDE9784AE6"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD26", "URL": "/supportpackage/SAPKIPHD26"}, {"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF09", "URL": "/supportpackage/SAPKIPHF09"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0000826402/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "544056 ", "URL": "/notes/544056 ", "Title": "IS-H AT: Scoring - Time-Dependent Upper Limits", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "556295 ", "URL": "/notes/556295 ", "Title": "IS-H AT: SCORING - Creation of WIKRAF-V Record", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "586189 ", "URL": "/notes/586189 ", "Title": "IS-H AT: SCORING - Convert External Institute Number", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "588943 ", "URL": "/notes/588943 ", "Title": "IS-H AT: SCORING - Creation of WIKRAF-V Record (Without NKSA)", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "680335 ", "URL": "/notes/680335 ", "Title": "IS-H AT: SCORE - V Record Creation Without IV Confirmations", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "707273 ", "URL": "/notes/707273 ", "Title": "IS-H AT: Scoring - Postal Code and Canceled Cases", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "780001 ", "URL": "/notes/780001 ", "Title": "AT: Changes in APIs/BAPIs for Intensive Care Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "781068 ", "URL": "/notes/781068 ", "Title": "IS-H AT: Scoring Model 2004 (BAdI Intensive Care Data)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "801331 ", "URL": "/notes/801331 ", "Title": "IS-H AT: Scoring - Adjustments or Bug fixes", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "805347 ", "URL": "/notes/805347 ", "Title": "IS-H AT: Scoring - Data for Intensive Care Data BAdI", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "821647 ", "URL": "/notes/821647 ", "Title": "IS-H AT: Changes in Processing of Intensive Care Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "780001 ", "URL": "/notes/780001 ", "Title": "AT: Changes in APIs/BAPIs for Intensive Care Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "801331 ", "URL": "/notes/801331 ", "Title": "IS-H AT: Scoring - Adjustments or Bug fixes", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "805347 ", "URL": "/notes/805347 ", "Title": "IS-H AT: Scoring - Data for Intensive Care Data BAdI", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "821647 ", "URL": "/notes/821647 ", "Title": "IS-H AT: Changes in Processing of Intensive Care Data", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=826402&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/826402/D\" target=\"_blank\">/notes/826402/D</a>."}}}}