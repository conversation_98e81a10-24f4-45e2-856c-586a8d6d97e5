{"Request": {"Number": "1678780", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 326, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017380622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001678780?language=E&token=553FA0DA28CCA583ED372D64475EF2F8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001678780", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001678780/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1678780"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 51}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1678780 - Installation or upgrade BI_CONT/BI_CONT_XT 707/737/747"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Add-on installation or upgrade</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAINT, SAPehpi, add-on, BI_CONT, BI_CONT_XT, 707, 737, 747, SAPK-707AHINBICONT, SAPK-707BHINBICONT, SAPK-707G<PERSON>INBICONT, SAPK-737AHINBICONT, SAPK-737BHINBICONT, SAPK-737GHINBICONT, SAPK-747AHINBICONT, SAPK-747BHINBICONT, SAPK-747GHINBICONT, SAPK-707AGINBICONTXT, SAPK-707BGINBICONTXT, SAPK-737AGINBICONTXT, SAPK-737BGINBICONTXT, SAPK-737GGINBICONTXT, SAPK-747AGINBICONTXT, SAPK-747BGINBICONTXT, SAPK-747GGINBICONTXT, 51043514, 51043516, 51043517</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You must install Business Intelligence Content (BI_CONT 707, 737, or 747) and/or Business Intelligence Extension (BI_CONT_XT 707, 737, or 747).<br />You must perform a Basis upgrade or an SAP NetWeaver enhancement package installation with a simultaneous upgrade of BI_CONT and, if required, BI_CONT_XT.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>This SAP Note is updated on a regular basis. Make sure you have the current version of this SAP Note before you start the installation.</strong><br /><br />Contents<br /> 1. Change history<br /> 2. Important general information<br /> 3. Prerequisites for BI_CONT / BI_CONT_XT 707/737/747<br /> 4. Required packages / passwords<br /> 5. Information about SAP POS Data Management (RTLPOSDM)<br /> 6. Known errors<br /> 7. After the installation or the upgrade of BI_CONT / BI_CONT_XT 707/737/747<br /> 8. Language support<br /><br /></p>\r\n<p><strong>1. Change history</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Date</th><th>Section</th><th>Short description</th></tr>\r\n<tr>\r\n<td>3/4/2014</td>\r\n<td>6.</td>\r\n<td>Section \"Conflicts with ANASCPM\" added</td>\r\n</tr>\r\n<tr>\r\n<td>6/16/2013</td>\r\n<td>3.</td>\r\n<td>Section \"BI_CONT and SAP Solution Manager\" amended, since the restriction no longer exists</td>\r\n</tr>\r\n<tr>\r\n<td>5/13/2013</td>\r\n<td>3.</td>\r\n<td>SAP NetWeaver 7.4 is inserted as a supported SAP NetWeaver release.</td>\r\n</tr>\r\n<tr>\r\n<td>9/11/2012</td>\r\n<td>3./6.</td>\r\n<td>SPAM 47 required</td>\r\n</tr>\r\n<tr>\r\n<td>10/9/2012</td>\r\n<td>1./7.</td>\r\n<td>Important, required post-processing steps were added</td>\r\n</tr>\r\n<tr>\r\n<td>9/19/2012</td>\r\n<td>3.</td>\r\n<td>Import prerequisite for BI_CONT_EXT 707 was added</td>\r\n</tr>\r\n<tr>\r\n<td>8/6/2012</td>\r\n<td>4.</td>\r\n<td>Required package for SCM was corrected</td>\r\n</tr>\r\n<tr>\r\n<td>8/2/2012</td>\r\n<td>3.</td>\r\n<td>Section \"BI_CONT and SAP Solution Manager\" was inserted</td>\r\n</tr>\r\n<tr>\r\n<td>7/12/2012</td>\r\n<td>2./6.</td>\r\n<td>Preventing TABIM_UPG errors in the upgrade to SAP NetWeaver 7.30</td>\r\n</tr>\r\n<tr>\r\n<td>10/1/2012</td>\r\n<td>4.</td>\r\n<td>Source release BI_CONT 711 for upgrade to BI_CONT 747</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>2. Important general information</strong></p>\r\n<ul>\r\n<li>You cannot uninstall BI_CONT or BI_CONT_XT. Before you install BI_CONT or BI_CONT_XT, note that it is not possible to uninstall ABAP add-ons. Further restrictions concerning the upgrade and maintenance of your SAP system that are caused by the installation of an add-on are described in release strategy note 153967.</li>\r\n</ul>\r\n<ul>\r\n<li>When you install SAP NetWeaver 7.0 Enhancement Package 1 or 2 and perform a simultaneous upgrade of BI_CONT, SOFTWARE UPDATE MANAGER 1.0 Support Package 04 with patch level 2 or higher is essential. If you use the SAP EHP INSTALLER or a lower version of the SOFTWARE UPDATE MANAGER, errors may occur during the upgrade and these may result in the upgrade being reset.</li>\r\n</ul>\r\n<ul>\r\n<li>When you upgrade to SAP NetWeaver 7.0, only SAP NetWeaver 7.0 SR3 is supported. You are not permitted to use lower SAP NetWeaver 7.0 SR deliveries.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Upgrade to SAP NetWeaver 7.3</strong>: The package SAPK-737GHINBICONT must be included in the upgrade (you will find more descriptions in chapter 6).</li>\r\n</ul>\r\n<ul>\r\n<li>See SAP Note 1853775 for restrictions and limitations for BI_CONT 747 on SAP NetWeaver 7.40 Support Package stack 02.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>You must see the post-implementation steps in chapter 7.</strong></li>\r\n</ul>\r\n<ul>\r\n<li><strong>qRFC version</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note that the qRFC version must be 45 or higher (see Note 0498484).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Updates</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Process your V3 update entries before you carry out an upgrade. Otherwise, there is a risk that you may no longer be able to update entries if changes are introduced into the interface structures of the V3 update modules by the patch or upgrade (see Note 328181).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Before the upgrade, process your entries in the extraction queues. Otherwise, there is a risk that you may no longer be able to update these entries if changes to the interface structures of the qRFC function modules are introduced by the patch or the upgrade (see Note 328181).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Before the upgrade, delete your entries in the reconstruction tables for the logistics extraction applications. Otherwise, there is a risk that you may no longer be able to use these entries if changes to the extraction structures are introduced by the patch or the upgrade (see Note 328181).</li>\r\n</ul>\r\n</ul>\r\n<p><strong>3. Prerequisites for BI_CONT / BI_CONT_XT 707/737/747</strong></p>\r\n<ul>\r\n<ul>\r\n<li>Obtain the following SAP Notes before you begin the installation.</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>Add-ons: Conditions:</td>\r\n<td>&nbsp;</td>\r\n<td>70228</td>\r\n</tr>\r\n<tr>\r\n<td>Overview note:</td>\r\n<td>&nbsp;</td>\r\n<td>1000822</td>\r\n</tr>\r\n<tr>\r\n<td>Release strategy note:</td>\r\n<td>&nbsp;</td>\r\n<td>153967</td>\r\n</tr>\r\n<tr>\r\n<td>Problems with transaction SAINT:</td>\r\n<td>&nbsp;</td>\r\n<td>822380</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li>Required release<br />BI_CONT 707 requires SAP NetWeaver 7.0 (optional: with Enhancement Package 1 or 2).<br />BI_CONT_XT 707 requires SAP Netweaver 7.0 with Enhancement Package 2.<br />BI_CONT / BI_CONT_XT 737 requires SAP NetWeaver 7.3.<br />BI_CONT / BI_CONT_XT 747 requires:<br />SAP Enhancement Package 1 for SAP NetWeaver 7.3 or<br />SAP Enhancement Package 3 for SAP NetWeaver 7.0 or<br />SAP NetWeaver 7.40.</li>\r\n</ul>\r\n<ul>\r\n<li>Required SPAM version<br />BI_CONT 707, 737, 747 require SPAM Version 46 or higher.<br /><strong><strong>To prevent the error regarding the incorrect BI_CONT queue calculation described in Chapter 6 from occurring during the upgrade to SAP NetWeaver 7.3, you require SPAM 47 </strong></strong><strong>or higher </strong><strong><strong>. Before you begin the upgrade, make sure that SPAM 47 </strong></strong><strong>or higher </strong><strong><strong>is already installed.</strong></strong></li>\r\n</ul>\r\n<ul>\r\n<li>Required components and Support Packages<br /><br /><span style=\"text-decoration: underline;\"><strong><strong>BI_CONT 707:</strong></strong></span></li>\r\n</ul>\r\n<p><br />SAP NetWeaver 7.0 Support Package stack 21:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>700</td>\r\n<td>SP 21</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>700</td>\r\n<td>SP 21</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BW</td>\r\n<td>700</td>\r\n<td>Support Package 23</td>\r\n</tr>\r\n<tr>\r\n<td>PI_BASIS</td>\r\n<td>2005_1_700</td>\r\n<td>SP 21</td>\r\n</tr>\r\n<tr>\r\n<td>or</td>\r\n<td>2006_1_700</td>\r\n<td>Support Package 10</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>or</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>SAP EHP1 for SAP NetWeaver 7.0 Support Package stack 06:</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>701</td>\r\n<td>SP 06</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>701</td>\r\n<td>SP 06</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BW</td>\r\n<td>701</td>\r\n<td>SP 06</td>\r\n</tr>\r\n<tr>\r\n<td>PI_BASIS</td>\r\n<td>701</td>\r\n<td>SP 06</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>or</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP EHP2 for SAP NetWeaver 7.0 Support Package stack 03:</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BW</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>PI_BASIS</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p> <strong><span style=\"text-decoration: underline;\">BI_CONT_XT 707</span></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>BI_CONT</td>\r\n<td>707</td>\r\n<td>SP 00</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BW</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>PI_BASIS</td>\r\n<td>702</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p> <strong><span style=\"text-decoration: underline;\">BI_CONT 737</span></strong><br /> SAP NetWeaver 7.3 Support Package stack 07:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>730</td>\r\n<td>SP 07</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>730</td>\r\n<td>SP 07</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BW</td>\r\n<td>730</td>\r\n<td>SP 07</td>\r\n</tr>\r\n<tr>\r\n<td>PI_BASIS</td>\r\n<td>730</td>\r\n<td>SP 07</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p> <strong><span style=\"text-decoration: underline;\">BI_CONT_XT 737</span></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>BI_CONT</td>\r\n<td>737</td>\r\n<td>SP 00</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p> <strong><span style=\"text-decoration: underline;\">BI_CONT 747</span></strong><br /> SAP EHP3 for SAP NetWeaver 7.0 Support Package stack 03 /</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>SAP Enhancement Package 1 for SAP NetWeaver 7.3 Support Package stack 03:</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>731</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>731</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BW</td>\r\n<td>731</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>PI_BASIS</td>\r\n<td>731</td>\r\n<td>SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>or</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP NetWeaver 7.40 Support Package stack 00:</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>740</td>\r\n<td>SP 00</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>740</td>\r\n<td>SP 00</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BW</td>\r\n<td>740</td>\r\n<td>SP 00</td>\r\n</tr>\r\n<tr>\r\n<td>PI_BASIS</td>\r\n<td>740</td>\r\n<td>SP 00</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>IMPORTANT:</strong> See SAP Note 1853775 for restrictions and limitations for BI_CONT 747 on SAP NetWeaver 7.40 Support Package stack 02.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong><span style=\"text-decoration: underline;\">BI_CONT_XT 747</span></strong></td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>BI_CONT</td>\r\n<td>747</td>\r\n<td>SP 00</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p> If you have not yet installed the required Support Packages, you can include them in the installation or upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<ul>\r\n<li>Additional Component Support Packages</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>The add-ons BI_CONT and BI_CONT_XT do not contain modifications. You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section 'Required Components and Support Packages'.<br /><br />BI_CONT and SAP Solution Manager<br />BI_CONT 707 is released only for SAP Solution Manager 7.1 Support Package stack 10 and higher. You need at least ST-BCO 710 Support Package 08 in order to be able to upgrade to BI_CONT 707.<br />BI_CONT 701 is NOT released for SAP Solution Manager 7.0 Enhancement Package 1.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>4. Required packages / passwords</strong></p>\r\n<p>The following section lists the different implementation cases and the package and password that are required for them. You can obtain the packages either from DVD 51043514 (BI_CONT 707) or DVD 51043516 (BI_CONT 737) or 51043517 (BI_CONT 747), by downloading them from the software marketplace, or by downloading them within a maintenance optimizer (MOPZ) transaction.<br /><br />When you upgrade or install an SAP NetWeaver enhancement package, the system requests an entry in the phase IS_SELECT for BI_CONT and, if required, BI_CONT_XT if you did not select a target version for the components within the maintenance optimizer. In this case, select \"Upgrade with SAINT Package\" and the required target release.<br /><br /><br /> <strong><strong>4.a) Installing BI_CONT 707</strong></strong><br /> Package: SAPK-707AHINBICONT<br /> Password: E16227D341<br /><br /><br /> <strong>4.b) Installing BI_CONT_XT 707</strong><br /> Package: SAPK-707AGINBICONTXT<br /> Password: E16227AB3A<br /><br /><br /> <strong>4.c) Upgrading BI_CONT 707 </strong></p>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT 702 to 706</strong></strong><br />Package: SAPK-707BHINBICONT<br />SAINT password: E16227D041<br />IS keyword: 4368286<br /><br />For the delta upgrade of source release 705 or lower, in addition to the BI_CONT 707 delta upgrade package SAPK-707BHINBICONT, you must also make the following packages available and include them in the delta upgrade: Refer to the relevant SAP Notes:<br /><br />For Source Release BI_CONT 705:<br />SAPK-706BHINBICONT, SAP Note 1558363<br /><br />For Source Release BI_CONT 704:<br />SAPK-706BHINBICONT, SAP Note 1558363<br />SAPK-705DHINBICONT SAP Note 1321293<br />The BI_CONT 705 delta upgrade SAPK-705DHINBICONT requires BI_CONT 704 Support Package 03. If you start from a lower Support Package level, the missing Support Packages must also be made available and must be included in the upgrade.<br /><br />For Source Release BI_CONT 703:<br />SAPK-706BHINBICONT, SAP Note 1558363<br />SAPK-705DHINBICONT SAP Note 1321293<br />SAPK-70401INBICONT SAP Note 1254946<br />SAPK-70402INBICONT SAP Note 1254946<br />SAPK-70403INBICONT SAP Note 1254946<br />SAPK-704DHINBICONT SAP Note 1172899<br /><br />The BI_CONT 704 delta upgrade SAPK-704DHINBICONT requires BI_CONT 703 Support Package 09. If you start from a lower Support Package level, the missing Support Packages must also be made available and must be included in the upgrade.<br /><br /><strong>Exception:</strong><br />When you upgrade a CRM or SRM system with a source version lower than 7.0 and you perform a simultaneous upgrade of BI_CONT, you require the following package:<br />Package: SAPK-707GHINBICONT<br />Password: 4368286</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT 353</strong></strong><br />Package: SAPK-707GHINBICONT<br />Password: 4368286</li>\r\n</ul>\r\n</ul>\r\n<p> <strong>4.d) Upgrading BI_CONT_XT 707</strong></p>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT_XT 705,706</strong></strong><br />Package: SAPK-707BGINBICONTXT<br />Password: E16227A83A</li>\r\n</ul>\r\n</ul>\r\n<p> <strong><strong>4.e) Installing BI_CONT 737</strong></strong><br /> Package: SAPK-737AHINBICONT<br /> Password: E16127D341<br /><br /> <strong>4.f) Installing BI_CONT_XT 737</strong><br /> Package: SAPK-737AGINBICONTXT<br /> Password: E16127AB3A <br /><br /> <strong>4.g) Upgrading BI_CONT 737</strong></p>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT 735.736</strong></strong><br />Package: SAPK-737BHINBICONT<br />Password: E16127D041<br /><br />For the delta upgrade of source release 735, in addition to the BI_CONT 707 delta upgrade package SAPK-707BHINBICONT, you must also make the following package available and include it in the delta upgrade: Refer to the relevant SAP Note:<br /><br />SAPK-736BHINBICONT SAP Note 1578396</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT 353, 701 to 707</strong></strong><br />Package: SAPK-737GHINBICONT<br />Password: 3875453</li>\r\n</ul>\r\n</ul>\r\n<p> <strong>4.h) Upgrading BI_CONT_XT 737</strong></p>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT_XT 735,736</strong></strong><br />Package: SAPK-737BGINBICONTXT<br />Password: E16127A83A</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT_XT 705 to 707</strong></strong><br />Package: SAPK-737GGINBICONTXT<br />Password: 3905442</li>\r\n</ul>\r\n</ul>\r\n<p> <strong>4.i) Installing BI_CONT 747</strong><br /> Package: SAPK-747AHINBICONT<br /> Password: E16627D341<br /><br /> <strong>4.j) Installing BI_CONT_XT 747</strong><br /> Package: SAPK-747AGINBICONTXT<br /> Password: E16627AB3A<br /><br /> <strong>4.k) Upgrading BI_CONT 747</strong></p>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT 353,711,735 to 737</strong></strong><br />Package: SAPK-747GHINBICONT<br />Password: 3668936</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT 701 to 707</strong></strong><br />Package: SAPK-747BHINBICONT<br />Password: 3668936<br /><br />In addition to the BI_CONT 747 delta upgrade package SAPK-747BHINBICONT, you must make the following package available and include it in the upgrade. Refer to the relevant SAP Note:<br /><br />SAPK-746AHINBICONT SAP Note 1578396<br /><br /><strong>Exception:</strong><br />When you upgrade a CRM or SRM system with a source version lower than 7.0 and you perform a simultaneous upgrade of BI_CONT, you require the following package:<br />Package: SAPK-747GHINBICONT<br />Password: 3668936</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT 746</strong></strong><br />Package: SAPK-747BHINBICONT<br />Password (SAINT): E16627D041<br />IS keyword: 3668936</li>\r\n</ul>\r\n</ul>\r\n<p> <strong>4.l) Upgrading BI_CONT_XT 747</strong></p>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT_XT 735 to 737</strong></strong><br />Package: SAPK-747GGINBICONTXT<br />Password: 3781589</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT_XT 705 to 707</strong></strong><br />Package: SAPK-747BGINBICONTXT<br />Password: 3781589</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong>Source release BI_CONT_XT 746</strong></strong><br />Package: SAPK-747BGINBICONTXT<br />Password (SAINT): E16627A83A<br />IS keyword: 3781589</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>5. Information about SAP POS Data Management (RTLPOSDM)</strong></p>\r\n<ul>\r\n<li>The SAP POS Data Management function was previously delivered with the add-on BI_CONT. As of BI_CONT 707, 737, and 747, the SAP POS Data Management function is delivered with the separate add-on RTLPOSDM. Therefore, the system requests decisions in dialogs in the following cases.</li>\r\n</ul>\r\n<ul>\r\n<li>Upgrade with a target release of SAP_BASIS 701 SR1:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Source release SAP_BASIS 700: Phase ADDON_DYN<br />The system requests a decision in the phase ADDON_DYN.<br />In the BI_CONT source release, if you already used the SAP POS Data Management function or if you want to use it after the upgrade, select the option \"Target Release BI_CONT 707 or higher plus RTLPOSDM\". In this case, also refer to SAP Note 1683825.<br />In the BI_CONT source release, if you have not used the SAP POS Data Management function and you do not intend to use it after the upgrade either, select the option \"Keep Add-on BI_CONT\". Even if you select the option \"Keep Add-on BI_CONT\" in ADDON_DYN, an error may occur in BIND_PATCH during the complete queue calculation because of the missing RTLPOSDM (for example, if BI_CONT 707 is included and if the function POS DM was used in a lower BI_CONT version). Repeat the configuration module and correct the decision in ADDON_DYN.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Source release SAP_BASIS 640: Phase ADDON_SPEC3<br />The system requests a decision in the phase ADDON_SPEC3.<br />In the BI_CONT source release, if you have already used the SAP POS Data Management function or if you want to use it after the upgrade, select the option \"Change to BI_CONT plus RTLPOSDM\". In this case, also refer to SAP Note 1683826.<br />In the BI_CONT source release, if you have not used the SAP POS Data Management function and if you do not intend to use it after the upgrade either, select the option \"Stay on BI_CONT\". Even if you select the option \"Stay on BI_CONT\" in ADDON_SPEC3, an error may occur during the complete queue calculation because of the missing RTLPOSDM (for example, if BI_CONT 707 is included and if the function POS DM was used in a lower BI_CONT version). Repeat the configuration module and correct the decision in ADDON_SPEC3.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Upgrade with a target release of SAP_BASIS 700 (SR3): Phase IS_MOVEMENT<br /><br />The system requests a decision in the phase IS_MOVEMENT.<br />In the BI_CONT source release, if you have already used the SAP POS Data Management function or if you want to use it after the upgrade, select the option \"+POSDM\". In this case, also refer to SAP Note 1683826.<br />In the BI_CONT source release, if you have not used the SAP POS Data Management function and if you do not intend to use it after the upgrade either, select the option \"BICONT\". Even if you select the option \"BI_CONT\" in IS_MOVEMENT, an error may occur during the complete queue calculation because of the missing RTLPOSDM (for example, if BI_CONT 707 is included and if the function POS DM was used in a lower BI_CONT version). Repeat the configuration module and correct the decision in IS_MOVEMENT.</li>\r\n</ul>\r\n<ul>\r\n<li>Upgrade with target release SAP_BASIS 702 or higher or<br />Upgrading BI_CONT using transaction SAINT<br /><br />If you used the function POS DM in a lower BI_CONT version, the additional installation of RTLPOSDM is required in the upgrade. See SAP Note 1683825 and include RTLPOSDM in the upgrade.</li>\r\n</ul>\r\n<p><strong>6. Known errors</strong></p>\r\n<ul>\r\n<li>Incorrect BI_CONT queue in upgrade to SAP NetWeaver 7.3<br /><strong>Symptom:</strong><br />Error in phase MAIN_NEWBAS/TABIM_UPG<br />The system reports errors as follows:<br />/POSDW/LPA_EXDET\" does not exist in nametab.<br />or<br />Could not read nametab \"/POSDW/LPA_EXDET\"<br /><br /><strong>Solution:</strong><br />Make sure that the package SAPK-737GHINBICONT is included in the upgrade.<br />Delete BI_CONT 735 and 736 packages from the directory EPS/in.<br />Do not make a mount point for a BI_CONT 735 or 736 CD available to the upgrade.<br />After the 'Configuration' roadmap step, check that the package SAPK-737GHINBICONT is contained in the log TRQUEUE2HEAP.LOG.<br />If the error has occurred, the upgrade must be reset and started again from the beginning.<br />The system calculates the queue correctly in each case if you have SPAM 47 or higher in your initial state.</li>\r\n</ul>\r\n<ul>\r\n<li>Error in phase MAIN_SHDRUN/RUN_RSPTBFIL_DEST<br /><strong>Symptom:</strong><br />checks MAIN_SHDRUN/RUN_RSPTBFIL_DEST were negative<br />The system reports errors as follows:<br />Could not read nametab\"\"/POSDW/FD_PROF\"<br />It is not possible to publish a complete error list in this SAP Note. The affected objects start with /POSDW/.<br /><br /><strong>Solution:</strong><br />You are probably using the EHPI. As described in the \"2. Important general information\" section, it is essential that you use SOFTWARE UPDATE MANAGER 1.0 Support Package 04 with patch level 2 or higher. Reset the installation of the Enhancement Package and start again using SUM.</li>\r\n</ul>\r\n<ul>\r\n<li>Error during the import<br /><strong>Symptom:</strong><br />It is not possible to publish a complete error list in this SAP Note. The errors look as follows:<br />2EETW109 table \"/POSDW/LPA_EXDET\" does not exist in nametab.<br />The affected objects start with /POSDW/.<br /><br /><strong>Solution:</strong><br />Download the attached file Allowfiles.SAR and unpack it into your DIR_TRANS/cofiles if you are using SAINT or unpack it into the DIR_PUT/cofiles directory if you are executing an upgrade or an enhancement package installation.<br />This error no longer occurs in the packages SAPK-707BHINBICONT, SAPK-737BHINBICONT, or SAPK-747BHINBICONT that were registered on June 13, 2012 unless you upgrade or install an enhancement package on an SCM system or Solution Manager system. In the SCM case, the error no longer occurs if you use SUM Support Package 05 patch level 5 or higher or if you included the following corrections (available on SAP Service Marketplace under \"Additional Components\" -&gt; \"Upgrade Tools\" -&gt; \"CORRECTIONS FOR UPGRADE) in the upgrade:<br /><br />FIX_NW701 patch level 68 or higher<br />FIX_NW702 patch level 68 or higher<br />FIX_SCM50SR3 patch level 44 or higher</li>\r\n</ul>\r\n<ul>\r\n<li>Generation error when importing to SAP_BASIS 700 with a Support Package level lower than 23.<br /><strong>Symptom:</strong><br />If generation is active, the following generation errors occur when importing to SAP_BASIS 700 with a Support Package level lower than 23:<br />Program RS_BCT_PBPC_BRF_MAINT: Syntax error in line 000502<br />Type 'CL_ABAP_DYN_PRG' is unknown<br />Program SAPLRS_BCT_ME_EXITS, Include LRS_BCT_ME_EXITSU21: Syntax error in line 000073<br />Type 'CL_ABAP_DYN_PRG' is unknown<br />Program SAPLRS_BCT_ME_TOOLS, Include LRS_BCT_ME_TOOLSU01: Syntax error in line 000072<br />Type 'CL_ABAP_DYN_PRG' is unknown<br /><br /><strong>Solution:</strong><br />If you are using \"BW: Content BPS for Public Sector Management (English)\", implement SAP Note 1487337, which eliminates the syntax errors.<br />Otherwise, you can ignore the errors.</li>\r\n</ul>\r\n<ul>\r\n<li>Generation error when upgrading to BI_CONT 737 from 735 or 736<br /><strong>Symptom:</strong><br />If generation is active, generation errors occur when upgrading to BI_CONT 737 from 735 or 736. It is not possible to publish a complete error list in this SAP Note. The errors look as follows:<br /><br />Program /POSDW/CL_AGGREGATE===========CP: Syntax error in line 000009<br />INCLUDE report '/POSDW/CL_AGGREGATE===========CU'not found<br /><br />The affected objects start with /POSDW/CL_ or /POSDW/CX_.<br /><br /><strong>Solution:</strong><br />You can ignore these errors.<br /><br /></li>\r\n<li>Conflicts with ANASCPM<br /><br /><strong>Symptom:</strong><br />During an upgrade to BI_CONT, the system reports conflicts with the add-on ANASCPM.<br /><br /><strong>Solution:<br /></strong>Download the appropriate Attribute Change Package (ACP) from the SAP Service Marketplace and include it in the upgrade. The names of the ACPs appear in the following list.<br /><br />Upgrade to BI_CONT 707: BI_CONT===707 + ANASCPM===200_702<br />Upgrade to BI_CONT 737: BI_CONT===737 + ANASCPM===200_730<br />Upgrade to BI_CONT 747: BI_CONT===747&#x00A0;+ ANASCPM===200_731</li>\r\n</ul>\r\n<p><br /><strong>7. After the installation or the upgrade of BI_CONT / BI_CONT_XT 707/737/747</strong></p>\r\n<ul>\r\n<li><strong>Before installing objects of the component BI_CONT or BI_CONT_XT for the BW Releases BW 7.02, 7.30, 7.31, you must refer to SAP Note 1756034.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>After the installation or upgrade, refer to SAP Note 1540729 if you</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>use SAP Retail;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>use the BSP application RSBCT_RFASH_ALI (BSP application to download the article list to R/3);</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>want to use the cross-site request forgery (XSRF) protection mechanism for this BSP application.</li>\r\n</ul>\r\n</ul>\r\n<p> In all other cases, you can ignore Note 1540729.</p>\r\n<ul>\r\n<li>Delivery Customizing:<br />Delivery Customizing is imported into client 000 and may need to be copied to other clients. For more information, see Note 337623.</li>\r\n</ul>\r\n<p><strong>8. Language support</strong></p>\r\n<ul>\r\n<li>BI_CONT / BI_CONT_XT 707/737/747 supports the following languages:<br />Arabic<br />Bulgarian<br />Chinese<br />Trad. Chinese<br />Danish<br />Finnish<br />French<br />Greek<br />Hebrew<br />Italian<br />Japanese<br />Catalan<br />Korean<br />Croatian<br />Dutch<br />Norwegian<br />Polish<br />Portuguese<br />Romanian<br />Russian<br />Swedish<br />Slovakian<br />Slovenian<br />Spanish<br />Czech<br />Turkish<br />Hungarian<br /><br />The languages are contained in the add-on package, and you do not need to import any additional language packages.</li>\r\n</ul>\r\n<ul>\r\n<li>For information about subsequently installing further languages in your system, see SAP Note 195442. </li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020457)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D020457)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001678780/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678780/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Allowfiles.SAR", "FileSize": "60", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000096182012&iv_version=0051&iv_guid=34CD722C9654024EB40C74ACC31D9DB6"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1853775", "RefComponent": "BW-BCT-GEN", "RefTitle": "Errors when using BI Content 7.47 SP04 on SAP BW 7.40 SP02", "RefUrl": "/notes/1853775"}, {"RefNumber": "1825065", "RefComponent": "BW-BEX-OT", "RefTitle": "BW VirtualProvider interface - enhancement for hierarchies", "RefUrl": "/notes/1825065"}, {"RefNumber": "1683826", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to RTLPOSDM 100 in the system switch upgrade", "RefUrl": "/notes/1683826"}, {"RefNumber": "1683825", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of RTLPOSDM on SAP NetWeaver", "RefUrl": "/notes/1683825"}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822 "}, {"RefNumber": "1853775", "RefComponent": "BW-BCT-GEN", "RefTitle": "Errors when using BI Content 7.47 SP04 on SAP BW 7.40 SP02", "RefUrl": "/notes/1853775 "}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "1683826", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to RTLPOSDM 100 in the system switch upgrade", "RefUrl": "/notes/1683826 "}, {"RefNumber": "1683825", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of RTLPOSDM on SAP NetWeaver", "RefUrl": "/notes/1683825 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "707", "To": "707", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "737", "To": "737", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "747", "To": "747", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT_XT", "From": "747", "To": "747", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT_XT", "From": "737", "To": "737", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT_XT", "From": "707", "To": "707", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 1.0", "SupportPackage": "SP009", "SupportPackagePatch": "000001", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200013676&support_package=SP009&patch_level=000001"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}