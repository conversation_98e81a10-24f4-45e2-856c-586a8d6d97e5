{"Request": {"Number": "1737650", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 416, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017465292017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001737650?language=E&token=B52BC7497C5C9BE1D717F7B6EADE422B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001737650", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001737650/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1737650"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 132}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1737650 - EHP7 for SAP ERP 6.0 SP Stacks - Release & Information Note"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />This Release Information Note (RIN) contains information and references to notes for applying Support Package (SP) Stacks of SAP Enhancement Package 7 for SAP ERP 6.0.</p>\r\n<p><strong><strong>Note</strong>:</strong> This SAP Note is subject to change. Listed below are points to keep in mind:<br /><br /><strong>GENERAL INFORMATION</strong><br /><br />Read this note completely BEFORE applying SP Stacks of SAP Enhancement Package 7 of SAP ERP 6.0 and follow the instructions given below.</p>\r\n<ul>\r\n<li>\r\n<p>If you would like to know the minimum supported start release for an upgrade to the different ERP Enhancement Package releases please have a look on note <strong><a target=\"_blank\" href=\"/notes/2818442\" title=\"2818442  - Minimum supported start release for the upgrade to ERP Enhancement Packages\">2818442</a>.</strong></p>\r\n</li>\r\n<li>Check this note for changes on a regular basis. All changes made after release of a Support Package (SP) Stack are documented in section \"Changes made after Release of SP Stack &lt;xx&gt;\".</li>\r\n<li>You will find general information about <strong>SP Stacks&#160;</strong>on SAP Support Portal at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/patches/stacks.html\">https://support.sap.com/software/patches/stacks.html</a>&#160;. The Schedule for SP Stacks&#160;could be found in the SAP Support Portal under Release, Upgrade&#160;&amp; Maintenance Info --&gt; Schedules for maintenance deliveries (Support Packages and SP Stacks) see <a target=\"_blank\" href=\"https://support.sap.com/release-upgrade-maintenance.html\">https://support.sap.com/release-upgrade-maintenance.html</a>&#160;.</li>\r\n<li>In addition to the notes mentioned in this note, you should also take into account the list of side effects known for Support Packages, which is created especially for your situation (SP queue). You can request this list on SAP Service Marketplace at <a target=\"_blank\" href=\"http://support.sap.com/notes\">http://support.sap.com/notes</a></li>\r\n<li><strong>NEW:</strong> SAP Solution Manager&#8217;s cloud-based <strong>Maintenance Planner</strong> is the successor of Maintenance Optimizer, Landscape Planner and Product System Editor. <strong><em>Maintenance Optimizer is no longer supported</em>.</strong> Maintenance planner helps you plan and maintain systems in your landscape. Please use the Maintenance planner to calculate a stack XLM file for a system maintenance and add-on installation. To access the tool go to <a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/mp\">https://apps.support.sap.com/sap/support/mp</a> . A <a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/download/attachments/*********/Maintenance_Planner_User_Guide.pdf\">Maintenance Planner User Guide</a> can be accessed from the home screen of Maintenance Planner. For the<strong> big picture of the Maintenance Planner</strong> and related tools, see <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-55363\">Landscape Management</a> @ the SCN.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>United Kingdom leaving the EU</strong>: For information on how a &#8220;hard Brexit&#8221; (= a no-deal scenario) would impact your <em>SAP ERP </em>system, please see SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2749671\">2749671</a>.</strong></li>\r\n<li>\r\n<p>For more information regarding LEGAL VAT TAX CHANGE in Germany<strong> , please see SAP note </strong><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2934992\">2934992</a></span></p>\r\n</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>SPAM Update</strong> - We strongly recommend that you apply the latest version of the Support Package Manager before you apply any other Support Packages.</p>\r\n<ul>\r\n<li>SPAM Updates are available on SAP Support Portal under Software Downloads -&gt; Support Packages and Patches --&gt; Tools to apply Support Packages at <a target=\"_blank\" href=\"https://support.sap.com/software/patches/support-packages.html\">https://support.sap.com/software/patches/support-packages.html</a>&#160;.&#160;&#160;For more information about Support Package Manager and Software Update Manager, see the System Maintenance information on the <a target=\"_self\" href=\"https://service.sap.com/sltoolset\">SL Toolset page</a>.</li>\r\n<li>For additional information, see the initial screen of transaction SPAM, choose the 'i' icon (online documentation: help -&gt; application help).</li>\r\n<li>When you import components using the Support Package Manager, ensure that no system activities occur in parallel and no background jobs are running.</li>\r\n</ul>\r\n<p>Read the <strong>restrictions for scenarios</strong> in:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 Enhancement packages in SAP note <a target=\"_blank\" href=\"/notes/998833\">998833</a></li>\r\n<li>SAP ERP 6.0 in SAP note <a target=\"_blank\" href=\"/notes/852235\">852235</a></li>\r\n<li>ERP 6.0 DFPS Defense Forces &amp; Public Security in SAP note <a target=\"_blank\" href=\"/notes/1410106\">1410106</a></li>\r\n</ul>\r\n<p><strong>Installation and Upgrade Information</strong></p>\r\n<ul>\r\n<li>Upgrades are possible from any Support Package level of the supported start release. It is not necessary to apply a higher Support Packages on the start release. An overview about the upgrade paths can be found in the <a target=\"_blank\" href=\"https://websmp104.sap-ag.de/&#126;sapidb/011000358700000525522013E\">PAM</a>.</li>\r\n<li>You can find information about <strong>browser</strong> support in SAP note <a target=\"_blank\" href=\"/notes/1853989\">1853989</a>.</li>\r\n<li>You want to upgrade from SAP <strong>ERP 2004</strong> to EHP7 FOR SAP ERP 6.0 please see SAP note <a target=\"_blank\" href=\"/notes/1742750\">1742750</a><strong>.</strong></li>\r\n<li><strong>Technical Usage</strong> - You can select the Technical Usage required to install or upgrade your solution. For more information, see SAP note <a target=\"_blank\" href=\"/notes/1818596\">1818596</a>.</li>\r\n<li>You want to additionally install the <strong>Java</strong> components SAP-UCES or IS-R-SRS on an ERP Java system, please see SAP Note <a target=\"_blank\" href=\"/notes/1898253\">1898253</a>.</li>\r\n<li>Additional Information on <strong>upgrading</strong> to EHP7 for SAP ERP 6.0 can be found in SAP note <a target=\"_blank\" href=\"/notes/1771467\">1771467</a>.</li>\r\n<li>Make sure to check the SAP NetWeaver Upgrade Master Guide of the SAP NW upgrade target release if you are planning a <strong>NetWeaver upgrade </strong>(Very important if your start release is NetWeaver 7.00 or lower). It contains specific information about SAP NetWeaver prerequisites, upgrade paths and upgrade dependencies. You find the guides for the SAP NetWeaver Platform on the SAP Help Portal at <a target=\"_blank\" href=\"http://help.sap.com/netweaver\">help.sap.com/netweaver</a> .</li>\r\n<li>After installation/ upgrade&#160;the \"SAP System data\" box can show the Product Version \"- See Details-\". For more information&#160;see SAP Note <a target=\"_blank\" href=\"/notes/2122939\">2122939</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations</strong></p>\r\n<ul>\r\n<li>Please be aware that by SAP Business Suite 7 Innovations 2013 (including EhP7 for SAP ERP 6.0, EhP3 for SAP CRM 7.0, EHP3 for SAP SCM 7.0 and EhP3 for SAP SRM 7.0)&#160;dual-stack is not supported anymore. For more information (including information about a <strong>dual stack split tool</strong>), see SAP note <a target=\"_blank\" href=\"/notes/1816819\">1816819</a>&#160;&amp; <a target=\"_blank\" href=\"/notes/1655335\">1655335</a>.</li>\r\n<li>Please be aware that SAP Business Suite 7 Innovations 2013 has been <span style=\"text-decoration: underline;\">initially delivered</span> with SP01-stack (hence this release information note starts with SP01). Although at least SP stack 01 is required for productive usage, the initial upgrade and installation shipment contains SP stack 00.Therefore the required SP stack must be included into the upgrade or installed after the installation.</li>\r\n<li>Please note that as of SAP Business Suite 7 (including EhP4 for SAP ERP 6.0, SAP CRM 7.0, SAP SRM 7.0 and SAP SCM 7.0), you can no longer install Dual Stack Application Systems (ABAP+Java) see SAP note <a target=\"_blank\" href=\"/notes/855534\">855534</a>.</li>\r\n<li>Be aware, that if you are currently using Enterprise Portal, Business Warehouse and/or Process Integration in conjunction with SAP Business Suite applications, you will have to upgrade those <strong>SAP NetWeaver&#160;Systems</strong> to 7.30 or higher before you can upgrade SAP ERP to SAP Enhancement Package 7 for SAP ERP 6.0. For more information about the <strong>Version Interoperability</strong> within the SAP Business Suite, see SAP note <a target=\"_blank\" href=\"/notes/1388258\">1388258</a>&#160;and <a target=\"_blank\" href=\"/notes/1951805\">1951805</a>. You can find an overview picture in the <a target=\"_blank\" href=\"https://websmp104.sap-ag.de/&#126;sapidb/011000358700000024692014D/EHP7_InteropMatrix.pdf\">PAM </a>.</li>\r\n<li>If not all installed Java components of your start release are part of product version EHP4 FOR SAP ERP 6.0 or higher, please also consider note <a target=\"_blank\" href=\"/notes/1815263\">1815263</a></li>\r\n<li>If you want to use <strong>Add-Ons</strong> with a Enhancement Package 7, then refer to SAP note <a target=\"_blank\" href=\"/notes/1820906\">1820906</a>&#160;and <a target=\"_blank\" href=\"/notes/1896062\">1896062</a>.</li>\r\n<li>Information about the usage and release of <strong>SAP SRM Server on one client</strong> in SAP ERP can be found in SAP note <a target=\"_blank\" href=\"/notes/963000\">963000</a>.</li>\r\n<li>Please be informed that SAP Enhancement Package 7 for SAP ERP 6.0 will not be released for all<strong> platforms </strong>(OS/DB dependencies). Further information can be found at the SAP Product Availabilty Matrix (<a target=\"_blank\" href=\"https://support.sap.com/patches\">https://</a><a target=\"_blank\" href=\"https://support.sap.com/pam\">support.sap.com/pam</a>).</li>\r\n<li>With beginning of CRM 7.0 EHP3 and ERP 6.0 EHP7 the Web Event Capturing by using the 3rd party component <strong>TeaLeaf RealiTea</strong> isn't supported by the standard Web Channel Enablement solution anymore, see SAP note <a target=\"_blank\" href=\"/notes/1880643\">1880643</a>.</li>\r\n<li>With SAP enhancement package 7 for SAP ERP 6.0, SAP is enabling most areas of the existing SAP ERP functions for SAP HANA to utilize the <strong>in-memory technology</strong> and enable better performing business applications, see SAP note <a target=\"_blank\" href=\"/notes/1865866\">1865866</a>.</li>\r\n<li>Please read SAP note <a target=\"_blank\" href=\"/notes/1771467\">1771467 </a>before&#160;upgrading to SAP ERP 6.0&#160;EHP7 if you are using the Technical Usage \"<strong>Public Sector Accounting</strong>\" or/and \"SAP ECC Server Value Pack successor\".</li>\r\n<li><strong>CEE Travel management (CEE TM)</strong>: If you use CEE Travel Management for the Czech Republic, Poland, Russia, Slovakia <strong>or</strong> Ukraine in your source release system (&lt;= EA-HR 600), you must familiarize with the necessary migration steps described in SAP Note <a target=\"_blank\" href=\"/notes/1475582\">1475582</a> before the upgrade. Customers using project base solution of CEE Travel Management with source release EA-HR 602, EA-HR 603 or EA-HR 604 should contact SAP before upgrade.</li>\r\n<li>If you use&#160;<strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum Revision&#160;85.</li>\r\n<li>To run SAP Biller Direct within Financial Supply Chain Management (FSCM), you need to apply FSCM-BILLER DIRECT 7.50: \"FSCMBD00\" (SP-level #00, Patch-level #0). For additional information please have also a look under SAP note <a target=\"_blank\" href=\"/notes/2932373\">2932373</a> - Maintenance for SAP Biller Direct</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ERP 6.0; Enhancement Package 7; ERP 6.17; EhP7; EHP7 Installation; EHP7 Upgrade; ERP 6.07; SAP ERP; SAP enhancement package 7 for SAP ERP 6.0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In May 2016 starting with SP stack 11 the JAVA instances of EhP7 for ERP 6.0 were also released on SAP NetWeaver 7.50. For details please refer to SAP note <a target=\"_blank\" href=\"/notes/2304435\">2304435</a> .</p>\r\n<p>&#8220;If you are planning to update the underlying NW SP level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on ERP 6.07 SP12 or lower and the target NW release is to 740 SP17 or higher. In this case, please implement note 2391758.&#8221;</p>\r\n<p><strong>SUPPORT PACKAGE STACK 27 (09/2023)</strong></p>\r\n<p><strong>What's new in SP stack 27?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 27 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 35. For more information about SAP ERP 6.0 SP Stack 35, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 30 (07/2023). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 27 are: SAP HR 6.04 SP&#160;194 and EA-HR 607 SP145. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;26 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>The minimum required revision of&#160;SAP&#160;<strong>HANA database 2.0 is</strong>&#160;SP stack 02 Revision&#160;22. SAP generally recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance.&#160;For further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>&#160;and&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 27--&#160;EhP6 SPS32 -- EhP5 SPS22 -- EhP4 SPS25 -- EhP3 SPS24 -- EhP2 SPS25 -- SAP ERP 6.0 SPS35</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP145</li>\r\n<li>LSOFE 607 SP31 &amp; LSOFE 617 SP24</li>\r\n<li>MDG_FND 732 SP25 &amp; MDG_FND 747 SP27<br />MDG_APPL 607 SP25 &amp; MDG_APPL 617 SP27</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP30</li>\r\n<li>EA-PS-GLO 607 SP30</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations.</p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 19 (12/2019) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 24 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;21 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;16 (09/2019) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;21 (12/2020) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;33 (06/2023) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;30 (07/2023) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;28 (09/2023) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>SUPPORT PACKAGE STACK 26 (03/2023)</strong></p>\r\n<p><strong>What's new in SP stack 26?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 26 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 35. For more information about SAP ERP 6.0 SP Stack 35, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;29 (01/2023). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 26 are: SAP HR 6.04 SP&#160;188 and EA-HR 607 SP139. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;26 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>The minimum required revision of&#160;SAP&#160;<strong>HANA database 2.0 is</strong>&#160;SP stack 02 Revision&#160;22. SAP generally recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance.&#160;For further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>&#160;and&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 26--&#160;EhP6 SPS31 -- EhP5 SPS22 -- EhP4 SPS25 -- EhP3 SPS24 -- EhP2 SPS25 -- SAP ERP 6.0 SPS35</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP131</li>\r\n<li>LSOFE 607 SP29 &amp; LSOFE 617 SP22</li>\r\n<li>MDG_FND 732 SP25 &amp; MDG_FND 747 SP25<br />MDG_APPL 607 SP25 &amp; MDG_APPL 617 SP25</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP28</li>\r\n<li>EA-PS-GLO 607 SP28</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations.</p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 19 (12/2019) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 24 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;21 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;16 (09/2019) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;21 (12/2020) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;32 (12/2022) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;29 (01/2023) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;26 (03/2023) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>SUPPORT PACKAGE STACK 25 (09/2022)</strong></p>\r\n<p><strong>What's new in SP stack 25?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 25 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 34. For more information about SAP ERP 6.0 SP Stack 34, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;28 (07/2022). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 18 are: SAP HR 6.04 SP&#160;180 and EA-HR 607 SP131. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;25 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>The minimum required revision of&#160;SAP&#160;<strong>HANA database 2.0 is</strong>&#160;SP stack 02 Revision&#160;22. SAP generally recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance.&#160;For further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>&#160;and&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 25--&#160;EhP6 SPS30 -- EhP5 SPS21 -- EhP4 SPS24 -- EhP3 SPS23 -- EhP2 SPS24 -- SAP ERP 6.0 SPS34</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP131</li>\r\n<li>LSOFE 607 SP29 &amp; LSOFE 617 SP22</li>\r\n<li>MDG_FND 732 SP25 &amp; MDG_FND 747 SP25<br />MDG_APPL 607 SP25 &amp; MDG_APPL 617 SP25</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP28</li>\r\n<li>EA-PS-GLO 607 SP28</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations.</p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 19 (12/2019) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 24 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;21 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;16 (09/2019) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;21 (12/2020) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;31 (07/2022) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;28 (0<em>7</em>/2022) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;24 (06/2022) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>SUPPORT PACKAGE STACK 24 (03/2022)</strong></p>\r\n<p><strong>What's new in SP stack 24?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 24 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 34. For more information about SAP ERP 6.0 SP Stack 34, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;27 (01/2022). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 18 are: SAP HR 6.04 SP&#160;175 and EA-HR 607 SP126. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;23 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>The minimum required revision of&#160;SAP&#160;<strong>HANA database 2.0 is</strong>&#160;SP stack 02 Revision&#160;22. SAP generally recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance.&#160;For further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>&#160;and&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 24--&#160;EhP6 SPS29 -- EhP5 SPS21 -- EhP4 SPS24 -- EhP3 SPS23 -- EhP2 SPS24 -- SAP ERP 6.0 SPS34</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP126</li>\r\n<li>LSOFE 607 SP27 &amp; LSOFE 617 SP21</li>\r\n<li>MDG_FND 732 SP25 &amp; MDG_FND 747 SP24<br />MDG_APPL 607 SP25 &amp; MDG_APPL 617 SP24</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP27</li>\r\n<li>EA-PS-GLO 607 SP27</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 19 (12/2019) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 24 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;21 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;16 (09/2019) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;21 (12/2020) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;30 (01/2022) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;27 (01/2022) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;23 (03/2022) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>SUPPORT PACKAGE STACK 23 (09/2021)</strong></p>\r\n<p><strong>What's new in SP stack 23?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 23 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 33. For more information about SAP ERP 6.0 SP Stack 33, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;26 (07/2021). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 18 are: SAP HR 6.04 SP&#160;168 and EA-HR 607 SP118. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;23 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>The minimum required revision of&#160;SAP&#160;<strong>HANA database 2.0 is</strong>&#160;SP stack 02 Revision&#160;22. SAP generally recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance.&#160;For further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>&#160;and&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 23--&#160;EhP6 SPS28 -- EhP5 SPS20 -- EhP4 SPS23 -- EhP3 SPS22 -- EhP2 SPS23 -- SAP ERP 6.0 SPS33</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP118</li>\r\n<li>LSOFE 607 SP27 &amp; LSOFE 617 SP20</li>\r\n<li>MDG_FND 732 SP25 &amp; MDG_FND 747 SP23<br />MDG_APPL 607 SP25 &amp; MDG_APPL 617 SP23</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP26</li>\r\n<li>EA-PS-GLO 607 SP26</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;21 (12/2020) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;29 (07/2021)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;26 (07/2021) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;21 (06/2021) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>SUPPORT PACKAGE STACK 22 (03/2021)</strong></p>\r\n<p><strong>What's new in SP stack 22?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 22 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 33. For more information about SAP ERP 6.0 SP Stack 33, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;25 (12/2020). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 18 are: SAP HR 6.04 SP&#160;161 and EA-HR 607 SP112. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;17 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>The minimum required revision of&#160;SAP&#160;<strong>HANA database 2.0 is</strong>&#160;SP stack 02 Revision&#160;22. SAP generally recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance.&#160;For further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>&#160;and&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 22--&#160;EhP6 SPS27 -- EhP5 SPS20 -- EhP4 SPS23 -- EhP3 SPS22 -- EhP2 SPS23 -- SAP ERP 6.0 SPS33</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP112</li>\r\n<li>LSOFE 607 SP26 &amp; LSOFE 617 SP19</li>\r\n<li>MDG_FND 732 SP26 &amp; MDG_FND 747 SP22<br />MDG_APPL 607 SP26 &amp; MDG_APPL 617 SP22</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP25</li>\r\n<li>EA-PS-GLO 607 SP25</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;21 (12/2020) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;28 (12/2020)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;25 (12/2020) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;20 (03/2021) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Notes to be applied</strong></p>\r\n<p><strong>&#160;</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 22:</strong></p>\r\n<ul>\r\n<li></li>\r\n</ul>\r\n<p><strong>SUPPORT PACKAGE STACK 21 (09/2020)</strong></p>\r\n<p><strong>What's new in SP stack 21?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 21 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 32. For more information about SAP ERP 6.0 SP Stack 32, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;24 (07/2020). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 18 are: SAP HR 6.04 SP&#160;154 and EA-HR 607 SP105. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;17 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 02 Revision&#160;22 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 21--&#160;EhP6 SPS26 -- EhP5 SPS19 -- EhP4 SPS22 -- EhP3 SPS21 -- EhP2 SPS22 -- SAP ERP 6.0 SPS32</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP105</li>\r\n<li>LSOFE 607 SP25 &amp; LSOFE 617 SP18</li>\r\n<li>MDG_FND 732 SP25 &amp; MDG_FND 747 SP21<br />MDG_APPL 607 SP25 &amp; MDG_APPL 617 SP21</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP24</li>\r\n<li>EA-PS-GLO 607 SP24</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;20 (12/2019) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;27 (07/2020)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;24 (07/2020) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;19 (09/2020) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Notes to be applied</strong></p>\r\n<p><strong>&#160;</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 21:</strong></p>\r\n<ul>\r\n<li>To run SAP Biller Direct within Financial Supply Chain Management (FSCM), you need to apply FSCM-BILLER DIRECT 7.50: \"FSCMBD00\" (SP-level #00, Patch-level #0). For additional information please have also a look under SAP note <a target=\"_blank\" href=\"/notes/2932373\">2932373</a> - Maintenance for SAP Biller Direct</li>\r\n</ul>\r\n<p><strong>SUPPORT PACKAGE STACK 20 (03/2020)</strong></p>\r\n<p><strong>What's new in SP stack 20?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 20 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 32. For more information about SAP ERP 6.0 SP Stack 32, see SAP Note&#160;2882699. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;23 (01/2020). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 18 are: SAP HR 6.04 SP&#160;148 and EA-HR 607 SP99. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;17 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 02 Revision&#160;22 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 20--&#160;EhP6 SPS25 -- EhP5 SPS19 -- EhP4 SPS22 -- EhP3 SPS21 -- EhP2 SPS22 -- SAP ERP 6.0 SPS32</p>\r\n<p>EhP7 SPS 20 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP99</li>\r\n<li>LSOFE 607 SP24 &amp; LSOFE 617 SP17</li>\r\n<li>MDG_FND 732 SP24 &amp; MDG_FND 747 SP20<br />MDG_APPL 607 SP24 &amp; MDG_APPL 617 SP20</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP23</li>\r\n<li>EA-PS-GLO 607 SP23</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;20 (12/2019) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;26 (01/2020)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;23 (01/2020) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;17 (02/2020) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Notes to be applied</strong></p>\r\n<p><strong>&#160;</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 20:</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 19 (09/2019)</strong></p>\r\n<p><strong>What's new in SP stack&#160;19?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 19 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 31. For more information about SAP ERP 6.0 SP Stack 31, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2882699\">2882699</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;23 (01/2020). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The&#160;<strong>HR</strong>&#160;Components available with Enhancement Package 7 SP Stack 19 are: SAP HR 6.04 SP&#160;148 and EA-HR 607 SP99. For more information about the HR SP content, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;17 of&#160;SAP ERP EHP7 check if the SAP&#160;<strong>HANA</strong>&#160;<strong>database 1.0</strong>&#160;was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2021789</a>&#160;- SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 02 Revision&#160;20 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note&#160;<a target=\"_blank\" href=\"/notes/1022755\">1022755</a>&#160;where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 19--&#160;EhP6 SPS23 -- EhP5 SPS18 -- EhP4 SPS21 -- EhP3 SPS20 -- EhP2 SPS21 -- SAP ERP 6.0 SPS31</p>\r\n<p>EhP7 SPS 19 -- EhP6 on Hana SPS14</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note&#160;<a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP93</li>\r\n<li>LSOFE 607 SP23 &amp; LSOFE 617 SP16</li>\r\n<li>MDG_FND 732 SP23 &amp; MDG_FND 747 SP19<br />MDG_APPL 607 SP23 &amp; MDG_APPL 617 SP19</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP22</li>\r\n<li>EA-PS-GLO 607 SP22</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note&#160;<a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is&#160;<strong>greater or equal</strong>&#160;the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is&#160;<strong>below&#160;</strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;19 (12/2018) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;25 (07/2019)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;22 (07/2019) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;16 (09/2019) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Notes to be applied</strong></p>\r\n<p><strong>&#160;</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 19:</strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 18 (03/2019)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;18?</strong></p>\r\n<p>Information about new functionality can be found&#160;on <a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 18 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 31. For more information about SAP ERP 6.0 SP Stack 31, see SAP Note <a target=\"_blank\" href=\"/notes/2747632\">2747632</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;21 (01/2019). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 18 are: SAP HR 6.04 SP&#160;136 and EA-HR 607 SP87. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;17 of&#160;SAP ERP EHP7 check if the SAP <strong>HANA</strong> <strong>database 1.0</strong> was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2494263\">2021789</a> - SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 02 Revision&#160;20 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 18--&#160;EhP6 SPS23 -- EhP5 SPS18 -- EhP4 SPS21 -- EhP3 SPS20 -- EhP2 SPS21 -- SAP ERP 6.0 SPS31</p>\r\n<p>EhP7 SPS 18 -- EhP6 on Hana SPS13</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP87</li>\r\n<li>LSOFE 607 SP22 &amp; LSOFE 617 SP15</li>\r\n<li>MDG_FND 732 SP22 &amp; MDG_FND 747 SP18<br />MDG_APPL 607 SP22 &amp; MDG_APPL 617 SP18</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP21</li>\r\n<li>EA-PS-GLO 607 SP21</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;19 (12/2018) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;24 (01/2019)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;21 (01/2019) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;14 (03/2019) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Notes to be applied </strong></p>\r\n<p><strong>2746684 - Error in KPI Calculation after Upgrade of SP in SAP_BS_FND 747 SP11</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 18:</strong></p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 17 (10/2018)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;17?</strong></p>\r\n<p>Information about new functionality can be found&#160;on <a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 17 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 30. For more information about SAP ERP 6.0 SP Stack 30, see SAP Note <a target=\"_blank\" href=\"/notes/2594736\">2594736</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;20 (08/2018). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 14 are: SAP HR 6.04 SP&#160;129 and EA-HR 607 SP80. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;17 of&#160;SAP ERP EHP7 check if the SAP <strong>HANA</strong> <strong>database 1.0</strong> was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2494263\">2021789</a> - SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 17--&#160;EhP6 SPS22 -- EhP5 SPS17 -- EhP4 SPS20 -- EhP3 SPS19 -- EhP2 SPS20 -- SAP ERP 6.0 SPS30</p>\r\n<p>EhP7 SPS 16 -- EhP6 on Hana SPS12</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP80</li>\r\n<li>LSOFE 607 SP21 &amp; LSOFE 617 SP14</li>\r\n<li>MDG_FND 732 SP21 &amp; MDG_FND 747 SP17<br />MDG_APPL 607 SP21 &amp; MDG_APPL 617 SP17</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP20</li>\r\n<li>EA-PS-GLO 607 SP20</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;18 (01/2018) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;23 (08/2018)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;20 (08/2018) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;13 (09/2018) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Changes made after Release of SP Stack 17:</strong></p>\r\n<ul>\r\n<li></li>\r\n</ul>\r\n<p>&#160;_____________________________________________________________________________________</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 16 (03/2018)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;16?</strong></p>\r\n<p>Information about new functionality can be found&#160;on <a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 16 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 30. For more information about SAP ERP 6.0 SP Stack 30, see SAP Note <a target=\"_blank\" href=\"/notes/2594736\">2594736</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 19 (02/2018). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 14 are: SAP HR 6.04 SP&#160;124 and EA-HR 607 SP75. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;14 of&#160;SAP ERP EHP7 check if the SAP <strong>HANA</strong> <strong>database 1.0</strong> was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2494263\">2021789</a> - SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 16--&#160;EhP6 SPS21 -- EhP5 SPS17 -- EhP4 SPS20 -- EhP3 SPS19 -- EhP2 SPS20 -- SAP ERP 6.0 SPS30</p>\r\n<p>EhP7 SPS 16 -- EhP6 on Hana SPS12</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP75</li>\r\n<li>LSOFE 607 SP20 &amp; LSOFE 617 SP13</li>\r\n<li>MDG_FND 732 SP20 &amp; MDG_FND 747 SP16<br />MDG_APPL 607 SP20 &amp; MDG_APPL 617 SP16</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP19</li>\r\n<li>EA-PS-GLO 607 SP19</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;18 (01/2018) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;22 (02/2018)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 19 (02/2018) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;101(03/2018) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Changes made after Release of SP Stack 16:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>&#160;_____________________________________________________________________________________</p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 15 (01/2018)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;15?</strong></p>\r\n<p>Information about new functionality can be found&#160;on <a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 15 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 29. For more information about SAP ERP 6.0 SP Stack 29, see SAP Note <a target=\"_blank\" href=\"/notes/2347619\">2347619</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 18 (10/2017). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 14 are: SAP HR 6.04 SP&#160;121 and EA-HR 607 SP72. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;14 of&#160;SAP ERP EHP7 check if the SAP <strong>HANA</strong> <strong>database 1.0</strong> was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2494263\">2021789</a> - SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g. SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 15--&#160;EhP6 SPS20 -- EhP5 SPS16 -- EhP4 SPS19 -- EhP3 SPS18 -- EhP2 SPS19 -- SAP ERP 6.0 SPS29</p>\r\n<p>EhP7 SPS 15 -- EhP6 on Hana SPS12</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP72</li>\r\n<li>LSOFE 607 SP19 &amp; LSOFE 617 SP12</li>\r\n<li>MDG_FND 732 SP19 &amp; MDG_FND 747 SP15<br />MDG_APPL 607 SP19 &amp; MDG_APPL 617 SP15</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP18</li>\r\n<li>EA-PS-GLO 607 SP18</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;17 (04/2017) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;21 (10/2017)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 18 (10/2017) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;10 (11/2017) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 15:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>&#160;Note <strong>2738698 - Bank chains: determination of intermediary banks with unexpected results</strong> is relevant for systems with activated bank chains. Payment processes can be affected.</p>\r\n<p>_____________________________________________________________________________________</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 14 (07/2017)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;14?</strong></p>\r\n<p>Information about new functionality can be found&#160;on <a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 14 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 29. For more information about SAP ERP 6.0 SP Stack 29, see SAP Note <a target=\"_blank\" href=\"/notes/2347619\">2347619</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 17 (05/2017). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 14 are: SAP HR 6.04 SP&#160;114 and EA-HR 607 SP65. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;14 of&#160;SAP ERP EHP7 check if the SAP <strong>HANA</strong> <strong>database 1.0</strong> was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2494263\">2021789</a> - SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g. SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 14 --&#160;EhP6 SPS19 -- EhP5 SPS16 -- EhP4 SPS19 -- EhP3 SPS18 -- EhP2 SPS19 -- SAP ERP 6.0 SPS29</p>\r\n<p>EhP7 SPS 14 -- EhP6 on Hana SPS11</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP65</li>\r\n<li>LSOFE 607 SP18 &amp; LSOFE 617 SP11</li>\r\n<li>MDG_FND 732 SP17 &amp; MDG_FND 747 SP14<br />MDG_APPL 607 SP17 &amp; MDG_APPL 617 SP14</li>\r\n<li>EA-FI 100 SP16</li>\r\n<li>EA-APPL-GLO 607 SP17</li>\r\n<li>EA-PS-GLO 607 SP17</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;17 (04/2017) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;20 (05/2017)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 17 (05/2017) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;08 (07/2017) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 14:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>_______________________________________________________________</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 13 (01/2017)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;13?</strong></p>\r\n<p>Information about new functionality can be found&#160;on <a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 13 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 28. For more information about SAP ERP 6.0 SP Stack 28, see SAP Note <a target=\"_blank\" href=\"/notes/2301568\">2301568</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 16 (11/2016). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 12 are: SAP HR 6.04 SP&#160;103 and EA-HR 607 SP54. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>The ABAP Dictionary activation might fail due to duplicate&#160;indexes on table&#160;\"J_1BNFE_ACTIVE\". In that case delete indexes 008 and 009 in ABAP Dictionary and repeat the phase.<br /><br />In case the ABAP Dictionary activation fails due to some DB Prozedur Proxy objects beginning with PPH_V02_MRP please consider SAP Note 2274548.</p>\r\n<p>Before update to SP Stack&#160;13 of&#160;SAP ERP EHP7 check if the SAP <strong>HANA</strong> <strong>database 1.0</strong> was already updated to the required minimum Revision 122 of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"https://websmp103.sap-ag.de/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2494263\">2021789</a> - SAP HANA Revision and Maintenance Strategy.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 13 --&#160;EhP6 SPS17 -- EhP5 SPS15 -- EhP4 SPS18 -- EhP3 SPS17 -- EhP2 SPS18 -- SAP ERP 6.0 SPS28</p>\r\n<p>EhP7 SPS 13 -- EhP6 on Hana SPS10</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP54</li>\r\n<li>LSOFE 607 SP17 &amp; LSOFE 617 SP10</li>\r\n<li>MDG_FND 732 SP17 &amp; MDG_FND 747 SP13<br />MDG_APPL 607 SP17 &amp; MDG_APPL 617 SP13</li>\r\n<li>EA-FI 100 SP15</li>\r\n<li>EA-APPL-GLO 607 SP16<br />EA-PS-GLO 607 SP16</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;16 (09/2016) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 19 (11/2016)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 16 (11/2016) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;06 (01/2017) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Notes to be applied </strong></p>\r\n<p><strong><span style=\"font-size: 10pt; font-family: 'Arial',sans-serif; color: black; mso-ansi-language: DE; mso-fareast-font-family: SimSun; mso-fareast-theme-font: minor-fareast; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2397320: Architecture: 'Measurement Is Overwritten Manually' indicator removed</span></strong></p>\r\n<p><strong>Changes made after Release of SP Stack 13:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p><strong>&#160;</strong>&#160;<strong><span style=\"text-decoration: underline;\"><strong>&#160;<span style=\"text-decoration: underline;\">___________________________________________________________________________________________________</span></strong></span></strong></p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 12 (07/2016)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;12?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 12 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 28. For more information about SAP ERP 6.0 SP Stack 28, see SAP Note <a target=\"_blank\" href=\"/notes/2301568\">2301568</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 15 (06/2016). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 12 are: SAP HR 6.04 SP&#160;101 and EA-HR 607 SP52. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 12 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database 1.0was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance&#160;Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 12 --&#160;EhP6 SPS17 -- EhP5 SPS15 -- EhP4 SPS18 -- EhP3 SPS17 -- EhP2 SPS18 -- SAP ERP 6.0 SPS28</p>\r\n<p>EhP7 SPS 12 -- EhP6 on Hana SPS10</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP52</li>\r\n<li>LSOFE 607 SP16 &amp; LSOFE 617 SP09</li>\r\n<li>MDG_FND 732 SP16 &amp; MDG_FND 747 SP12<br />MDG_APPL 607 SP16 &amp; MDG_APPL 617 SP12</li>\r\n<li>EA-FI 100 SP14</li>\r\n<li>EA-APPL-GLO 607 SP15<br />EA-PS-GLO 607 SP15</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner (MP) will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;15 (06/2016) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 18 (06/2016)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 15 (06/2016) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;03 (04/2016) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Changes made after Release of SP Stack 12:</strong></p>\r\n<ul>\r\n<li>\r\n<p>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</p>\r\n</li>\r\n<li>\r\n<p>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</p>\r\n</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p><strong>&#160;</strong>&#160;<strong><span style=\"text-decoration: underline;\"><strong>&#160;<span style=\"text-decoration: underline;\">___________________________________________________________________________________________________</span></strong></span></strong></p>\r\n<p>&#160;<strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 11 (01/2016)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;11?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under&#160;&gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 11 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 27. For more information about SAP ERP 6.0 SP Stack 27, see SAP Note <a target=\"_blank\" href=\"/notes/2063325\">2063325</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 13 (11/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 11 are: SAP HR 6.04 SP&#160;93 and EA-HR 607 SP44. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 11 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database 1.0 was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance&#160;Planner (MP) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 11 --&#160;EhP6 SPS16 -- EhP5 SPS14 -- EhP4 SPS17 -- EhP3 SPS16 -- EhP2 SPS17 -- SAP ERP 6.0 SPS27</p>\r\n<p>EhP7 SPS 11 -- EhP6 on Hana SPS09</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP44</li>\r\n<li>LSOFE 607 SP15 &amp; LSOFE 617 SP08</li>\r\n<li>MDG_FND 732 SP15 &amp; MDG_FND 747 SP11<br />MDG_APPL 607 SP15 &amp; MDG_APPL 617 SP11</li>\r\n<li>EA-FI 100 SP13</li>\r\n<li>EA-APPL-GLO 607 SP14<br />EA-PS-GLO 607 SP14</li>\r\n<li>EA-GLTRADE-GLO 607 SP10</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance&#160;Planner will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack 01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;14 (10/2015) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 17 (11/2015)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 13 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n<li>SAP NetWeaver 7.50 SP Stack&#160;01 (11/2015) - If you are running your JAVA instance on SAP NetWeaver 7.50</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 11:</strong></p>\r\n<ul>\r\n<li>\r\n<p>17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</p>\r\n</li>\r\n<li>\r\n<p>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</p>\r\n</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>&#160;Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>&#160;</strong>&#160;<strong><span style=\"text-decoration: underline;\"><strong>&#160;<span style=\"text-decoration: underline;\">___________________________________________________________________________________________________</span></strong></span></strong></p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 10 (10/2015)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;10?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 10 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 27. For more information about SAP ERP 6.0 SP Stack 27, see SAP Note <a target=\"_blank\" href=\"/notes/2063325\">2063325</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 12 (08/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 10 are: SAP HR 6.04 SP&#160;91 and EA-HR 607 SP42. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 10 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database 1.0 was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance&#160;Planner (MP)&#160;calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 10 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 10 -- EhP6 on Hana SPS09 -- EhP6 SPS15 -- EhP5 SPS14 -- EhP4 SPS17 -- EhP3 SPS16 -- EhP2 SPS17 -- SAP ERP 6.0 SPS27</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP42</li>\r\n<li>LSOFE 607 SP14 &amp; LSOFE 617 SP07</li>\r\n<li>MDG_FND 732 SP14 &amp; MDG_FND 747 SP10<br />MDG_APPL 607 SP14 &amp; MDG_APPL 617 SP10</li>\r\n<li>EA-FI 100 SP12</li>\r\n<li>EA-APPL-GLO 607 SP13<br />EA-PS-GLO 607 SP13</li>\r\n<li>EA-GLTRADE-GLO 607 SP09</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;13 (05/2014) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 16 (04/2014)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 11 (06/2015) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>PI Basis</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2194584\">2194584</a></p>\r\n</td>\r\n<td>\r\n<p>DEV Jira: CM20DELTA: cannot capture exchange rates</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SAP BASIS</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2178368\">2178368</a></p>\r\n</td>\r\n<td>\r\n<p>URL generation for applications (My Inbox)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2175209\">2175209</a></p>\r\n</td>\r\n<td>\r\n<p>XI runtime: OBJECTS_OBJREF_NOT_ASSIGNED dump in class CL_XMS_MAIN</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2191005\">2191005</a></p>\r\n</td>\r\n<td>\r\n<p>Enhancement of interface IF_ECL_LAYER</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SAP UI</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2175397\">2175397</a></p>\r\n</td>\r\n<td>\r\n<p>Content type of files served from UI5 repository is broken</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2178862\">2178862</a></p>\r\n</td>\r\n<td>\r\n<p>IDA-ALV: Runtime error COMPUTE_INT_TIMES_OVERFLOW</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 10:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;&#160;<strong><span style=\"text-decoration: underline;\"><strong>&#160;<span style=\"text-decoration: underline;\">___________________________________________________________________________________________________</span></strong></span></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 09 (07/2015)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;09?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under&#160;&gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 09 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 27. For more information about SAP ERP 6.0 SP Stack 27, see SAP Note <a target=\"_blank\" href=\"/notes/2063325\">2063325</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 11 (06/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 09 are: SAP HR 6.04 SP&#160;88 and EA-HR 607 SP39. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 09 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>When the language Turkish is installed in a system the import may stop in the activation phase with error &#8220;Data element FSH_DELCR could not be activated&#8221;. Use the workaround described in note <a target=\"_blank\" href=\"/notes/1022755\">1022755</a> where in case of SPAM is used TRKORR is SAPKH61709, OBJECT is DTED and OBJ_NAME is FSH_DELCR. This workaround applies to releases of SAP Netweaver up to 7.40 .</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Optimizer (MOPz) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 09 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>EhP7 SPS 09 -- EhP6 on Hana SPS09 -- EhP6 SPS15 -- EhP5 SPS14 -- EhP4 SPS17 -- EhP3 SPS16 -- EhP2 SPS17 -- SAP ERP 6.0 SPS27</p>\r\n<p>For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP39</li>\r\n<li>LSOFE 607 SP14 &amp; LSOFE 617 SP07</li>\r\n<li>MDG_FND 732 SP13 &amp; MDG_FND 747 SP09<br />MDG_APPL 607 SP13 &amp; MDG_APPL 617 SP09</li>\r\n<li>EA-FI 100 SP12</li>\r\n<li>EA-APPL-GLO 607 SP12<br />EA-PS-GLO 607 SP12</li>\r\n<li>EA-GLTRADE-GLO 607 SP09</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;13 (05/2014) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 16 (04/2014)&#160;- If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 11 (06/2015) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Banking</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2159423\">2159423</a></p>\r\n</td>\r\n<td>\r\n<p>BP_SRV: Short dump ASSERTION_FAILED when creating a business partner using service in ERP</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Basis (NetWeaver)</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2145426\">2145426</a></p>\r\n</td>\r\n<td>\r\n<p>Open SQL error during data select for an Gateway Service EntitySet with a long name</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Commodity Management</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2147681\">2147681</a></p>\r\n</td>\r\n<td>\r\n<p>Short dump when using search help for contract maturity code</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>HCM Self-Services</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172276\">2172276</a></p>\r\n</td>\r\n<td>\r\n<p>CATS CALUI: Data gets saved to another day after UNDO operation</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172313\">2172313</a></p>\r\n</td>\r\n<td>\r\n<p>CATS CALUI: Issue with releasing of data</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>HCM Employee Self-Service</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2173978\">2173978</a></p>\r\n</td>\r\n<td>\r\n<p>Leave Request does not display the attachment pane</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Foundation - Social media</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172415\">2172415</a></p>\r\n</td>\r\n<td>\r\n<p>Social Intelligence API: Enable Social API OData Service</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong><strong>Changes made after Release of SP Stack 09:</strong></strong></strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>[Information on activation error with installed language Turkish added to important considerations</p>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong>&#160;<span style=\"text-decoration: underline;\">___________________________________________________________________________________________________</span></strong></span></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 08 (04/2015)</span></strong></p>\r\n<p><strong>What's new in SP stack&#160;08?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 08 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 26. For more information about SAP ERP 6.0 SP Stack 26, see SAP Note <a target=\"_blank\" href=\"/notes/2037564\">2037564</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 10 (03/2015). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">http://service.sap.com/maintenaceNW74</a>.</li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 08 are: SAP HR 6.04 SP&#160;81 and EA-HR 607 SP36. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 08 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>General Issue with MSSQL: When using SPAM to import ERP 6.0 EHP7 stack SP8&#160;make sure to import note <a target=\"_blank\" href=\"/notes/2125387\">2125387</a>&#160;before starting the stack&#160;import. SUM finishes but post-processing are required.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Optimizer (MOPz) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 08 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">EhP7 SPS 08 -- EhP6 on Hana SPS08 -- EhP6 SPS14 -- EhP5 SPS13 -- EhP4 SPS16 -- EhP3 SPS15 -- EhP2 SPS16 -- SAP ERP 6.0 SPS26</p>\r\n<p style=\"padding-left: 30px;\">For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n<ul>\r\n<li>EA-HR 607 SP36</li>\r\n<li>LSOFE 607 SP13 &amp; LSOFE 617 SP06</li>\r\n<li>MDG_FND 732 SP12 &amp; MDG_FND 747 SP08<br />MDG_APPL 607 SP12 &amp; MDG_APPL 617 SP08</li>\r\n<li>EA-FI 100 SP11</li>\r\n<li>EA-APPL-GLO 607 SP11<br />EA-PS-GLO 607 SP11</li>\r\n<li>EA-GLTRADE-GLO 607 SP08</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 12 (08/2014) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 14 (11/2014) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 10 (03/2015) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>SAP note</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>AP-MD-BP</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2111336\">2111336</a></p>\r\n</td>\r\n<td>\r\n<p>Importing parameter IV_REPL_TYPE should be optional parameter</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>IS-R</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2125132\">2125132</a></p>\r\n</td>\r\n<td>\r\n<p>RSBRAN03: Short dump CONNE_IMPORT_WRONG_FIELD_TYPE (part 2)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2144627\">2144627</a></p>\r\n</td>\r\n<td>\r\n<p>Possible conversion-downtime due to field length change in db table KONBBYPRQ</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>PP-PN</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2144344\">2144344</a></p>\r\n</td>\r\n<td>\r\n<p>Measurement Definition creates Short Dump</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2128672\">2128672</a></p>\r\n</td>\r\n<td>\r\n<p>Prevent Duplicate Enrtries for Mediums Oil, Water and Gas and characteristics in table GHO_MEAS_MEDIUM and GHO_MEASDT respectively</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>FI-CA</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146959\">2146959</a></p>\r\n</td>\r\n<td>\r\n<p>FKKBIX: error XI 166</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>IS-PS-CA</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2144381\">2144381</a></p>\r\n</td>\r\n<td>\r\n<p>Correction note for backend fixes</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 08:</strong></p>\r\n<ul>\r\n<li>\r\n<p>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</p>\r\n</li>\r\n<li>\r\n<p>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</p>\r\n</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>&#160;</strong><span style=\"text-decoration: underline;\"><strong>___________________________________________________________________________________________________</strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 07 (01/2015)</strong></span></p>\r\n<p><strong>What's new in SP stack&#160;07?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 07 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 26. For more information about SAP ERP 6.0 SP Stack 26, see SAP Note <a target=\"_blank\" href=\"/notes/2037564\">2037564</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 09 (11/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance74\">http://service.sap.com/maintenance74</a></li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 07 are: SAP HR 6.04 SP&#160;81 and EA-HR 607 SP32. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 07 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP08 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>Update to ERP 6.0 EHP7 SP07 is possible both via SUM and SPAM. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>&#160;. Only the SP update from SP06 to SP07 is possible via SPAM.</p>\r\n<p>In case you use the product version &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; (SAP <strong>Fiori</strong> for SAP ERP 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914499\">1914499</a> concerning interoperability.</p>\r\n<p>If you plan to use complex services (MM-SRV)&#160;within the EAM Maintenance Order&#160;in the Web User Interface (Enterprise Asset Management Simplicity (EAMS)),&#160;we recommend to switch to SAG GUI transactions IW31 / IW32. The mentioned restriction only apply for the Web Dynpro-based EAMS Web User Interface&#160;.</p>\r\n<p>General Issue with MSSQL: When using SPAM to import ERP 6.0 EHP7 stack SP7&#160;make sure to import note <a target=\"_blank\" href=\"/notes/2125387\">2125387</a>&#160;before starting the stack&#160;import. SUM finishes but post-processing are required.</p>\r\n<p>During factsheet app configuration (EAM-Maintenance), the connector preparation may end with an error. Then please execute the reports as per note 2106754, delete all existing connectors and recreate them.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Optimizer (MOPz) calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 07 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EhP7 SPS 07 -- EhP6 on Hana SPS08 -- EhP6 SPS14 -- EhP5 SPS13 -- EhP4 SPS16 -- EhP3 SPS15 -- EhP2 SPS16 -- SAP ERP 6.0 SPS26</p>\r\n<p style=\"padding-left: 60px;\">For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EA-HR 607 SP32<br />LSOFE 607 SP13 &amp; LSOFE 617 SP06<br />MDG_FND 732 SP11 &amp; MDG_FND 747 SP07<br />MDG_APPL 607 SP11 &amp; MDG_APPL 617 SP07 <br />EA-FI 100 SP11<br />EA-APPL-GLO 607 SP10<br />EA-PS-GLO 607 SP10</p>\r\n<p style=\"padding-left: 60px;\">EA-GLTRADE-GLO 607 SP08</p>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 12 (08/2014) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 14 (11/2014) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 09 (11/2014) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP note</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Update</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1751237\">1751237</a>&#160;</td>\r\n<td>Add. info about the update/upgrade to SAP NetWeaver 7.4 (incl. SPs and SRs)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/**********\" title=\"2129533\">2129533&#160;</a>&#160;</td>\r\n<td>SAPDBBRF: Syntax error after upgrade</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Account Statement</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2083346\">2083346</a>&#160;</td>\r\n<td>Technical changeover of the field FEBCL_BF-AGUMS</td>\r\n</tr>\r\n<tr>\r\n<td><strong>BOPF - Authority Check</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2105488\">2105488</a></td>\r\n<td>BOPF SADL dump in authority check</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Business Partner in FS-BP</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2062499\">2062499</a>&#160;</td>\r\n<td>No new entries in table-like data sets using collect module possible</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Case Management</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/*********\">2074409</a>&#160;</td>\r\n<td>Renumbering the steps of the process route template/ Workitem for a process route step not received in inbox and workflow shows to be broken</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2094748\">2094748</a>&#160;</td>\r\n<td>Renumbering of steps during template creation in process route causes error in functionality coupled with step numbers</td>\r\n</tr>\r\n<tr>\r\n<td><strong>EAM Simplicity</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2115706\">2115706</a><strong>&#160;</strong></td>\r\n<td>EAMS3: Table selection may lead to short dump</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2115849\">2115849</a>&#160;</td>\r\n<td>EAMS4: Selection state of task list operation is lost</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2116318\">2116318</a>&#160;</td>\r\n<td>Corrections for order and notification status (EAMS Web UI)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2116552\">2116552</a><a target=\"_blank\" href=\"/notes/2049890\">&#160;</a>&#160;</td>\r\n<td>Corrections for order lists and notification lists (POWLs and EAMS Web UI)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2118047\">2118047</a>&#160;&#160;</td>\r\n<td>Quickview task list not displayed in maintenance plan</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2119936\">2119936</a>&#160;</td>\r\n<td>EAMS: Error message when creating new suboperations in task list</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2119939\">2119939</a>&#160;&#160;</td>\r\n<td>Priority not displayed for order quickview and notification quickview</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2120079\">2120079</a>&#160;&#160;</td>\r\n<td>EAMS3: No actions can be maintained for confirmation of unplanned work</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2120501\">2120501</a>&#160;&#160;</td>\r\n<td>Order not technically completed with \"Save and Complete\"</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2120621\">2120621</a>&#160;</td>\r\n<td>EAMS3: Description not shown</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2122864\">2122864</a></td>\r\n<td>EAMS: Short dump when using \"Service Selection\" for copying services to an operation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2123493\">2123493</a><strong>&#160;</strong></td>\r\n<td>EAMS: Short dump when using click on 'Create Task List'</td>\r\n</tr>\r\n<tr>\r\n<td><strong>EAMS: Maintenance Processing</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2116318\">2116318</a>&#160;</td>\r\n<td>Corrections for order and notification status (EAMS Web UI)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2116552\">2116552</a>&#160;</td>\r\n<td>Corrections for order lists and notification lists (POWLs and EAMS Web UI)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2119307\">2119307</a>&#160;</td>\r\n<td>Syntax error in the methods QUERY_ORDOP_OOL and RAISE_ORD_NTF_STAT_DIALOG (EAMS Web UI)</td>\r\n</tr>\r\n<tr>\r\n<td><strong>FPM</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2074112\">2074112</a>&#160;</td>\r\n<td>List UIBB: Program abortion because of API contract violation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2067745\">2067745</a>&#160;</td>\r\n<td>TREE UIBB: Missing row actions</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Logistics Execution of Outbound Deliveries</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2115690\">2115690</a>&#160;</td>\r\n<td>Issue with quantity reduction for batch split item in deliveries</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Personalization in WD ABAP</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2067810\">2067810</a>&#160;</td>\r\n<td>Dump in Personalization when a new type of the UI element has changed</td>\r\n</tr>\r\n<tr>\r\n<td><strong>PLM Engineering Record</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2115662\">2115662</a>&#160;</td>\r\n<td>Solution validation bug fixing</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2117976\">2117976</a>&#160;</td>\r\n<td>Process Route Template without tasks should not be transported</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2119151\">2119151</a>&#160;</td>\r\n<td>Bug Fix GRPT - F4 for Agent ID</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2119735\">2119735</a>&#160;</td>\r\n<td>Issue with Authorization Check for Process Route while - Displaying the Engineering Record</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Variant configuration (LAMA)</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2077145\">2077145</a>&#160;</td>\r\n<td>Dump DATA_OFFSET_TOO_LARGE in program SAPLCLCV in systems with long material numbers (LAMA)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 07:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2125387\">2125387</a></td>\r\n<td>Activation of CDS view aborts on MSS</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/**********\" title=\"2129533\">2129533</a></td>\r\n<td>SAPDBBRF: Syntax error after upgrade</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Added information on factsheet app configuration (EAM-Maintenance) in \"important considerations\".</p>\r\n<p>___________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT RELEASE 2 (12/2014)</strong></span></p>\r\n<p><br />SR2: Enhancement Package 7 for SAP ERP 6.0 with Support Release&#160;2 includes the support packages up to SP06. If you perform an update with target release&#160;EHP7 for SAP ERP 6.0 SR2, your system goes immediately to EHP7 for ERP 6.0 SP06 and SAP NetWeaver 7.40 SP08.</p>\r\n<p>With the availability of Support Release 2, the DVDs of the predecessor Support Release 1 have been archived. If you have already started upgrading a system with the DVDs of Support Release 1, you can still select the corresponding valid Support Package Stacks (03 &#8211; 05) in Maintenance Optimizer for a transition phase.</p>\r\n<p>Technical information about the ABAP Support Package levels&#160;contained in the&#160;Support Release 2&#160;can be found in SAP note <a target=\"_blank\" href=\"/notes/774615\">774615</a>.</p>\r\n<p>___________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 06 (10/2014)</strong></span></p>\r\n<p><strong>What's new in SP stack&#160;06?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 06 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 26. For more information about SAP ERP 6.0 SP Stack 26, see SAP Note <a target=\"_blank\" href=\"/notes/2037564\">2037564</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 08 (09/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">http://</a><a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">service.sap.com/maintenanceNW74</a></li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 06 are: SAP HR 6.04 SP&#160;78 and EA-HR 607 SP29. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 06 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP08</strong> requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>&#160;</p>\r\n<p>The implementation of SP Stack 06 is only possible via <strong>SUM</strong> tool and not as for previous support packages via SPAM as well. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>.</p>\r\n<p>In case you use the product version &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; (SAP <strong>Fiori</strong> for SAP ERP 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914499\">1914499</a> concerning interoperability.</p>\r\n<p>This SP contains some <strong>scenario-based authorization checks (SACF)</strong> which require SACF BASIS framework implementation in your system landscape. If the basis requirements are not met, system errors might occur during the activation phase of this SP implementation. Ensure to follow the recommendations described in note <a target=\"_blank\" href=\"/notes/2054522\">2054522</a>. Detailed documentation on SACF is available on the <a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw74/helpdata/en/0d/4e0a72085a43a08d66e1e128365156/content.htm\">Help Portal</a>.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 06 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EhP7 SPS 06 -- EhP6 on Hana SPS08 -- EhP6 SPS13 -- EhP5 SPS13 -- EhP4 SPS16 -- EhP3 SPS15 -- EhP2 SPS16 -- SAP ERP 6.0 SPS26</p>\r\n<p style=\"padding-left: 60px;\">For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EA-HR 607 SP29<br />LSOFE 607 SP12 &amp; LSOFE 617 SP05<br />MDG_FND 732 SP10 &amp; MDG_FND 747 SP06<br />MDG_APPL 607 SP10 &amp; MDG_APPL 617 SP06 <br />EA-FI 100 SP10<br />EA-APPL-GLO 607 SP09<br />EA-PS-GLO 607 SP09<br />EA-GLTRADE-GLO 607 SP07</p>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 12 (08/2014) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 13 (09/2014) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 08 (09/2014) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP note</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>BRF plus</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2049928\">2049928</a></td>\r\n<td>BRF+: Issues with 731 SP13 and 740 SP08 or After Note 2049865 Implementation</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Commodity Management</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2053523\">2053523</a></td>\r\n<td>Used quotations are not displayed</td>\r\n</tr>\r\n<tr>\r\n<td><strong>MRP</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2076481\">2076481</a></td>\r\n<td>Runtime error DBSQL_DUPLICATE_KEY_ERROR when you maintain object dependencies</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2077128\">2077128</a></td>\r\n<td>Error in object dependency compilation</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SEPA</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2028970\">2028970</a></td>\r\n<td>SEPA mandate: Check for 36 months of non-use</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2049890\">2049890</a></td>\r\n<td>SEPA mandate: Check for 36 months of non-use II</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Visual Enterprise</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2079147\">2079147</a></td>\r\n<td>Short Dump when transferring PMI node</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 06:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n<li>[31.07.2017] APO 7.0 EHP3 ON ERP 6.0 EHP7 is released for HANA DB 2.0 SP01 Revision 11 or higher</li>\r\n<li>[04.11.2014] The <strong>EHP update</strong> is released.</li>\r\n<li>[04.12.2014] The <strong>upgrade </strong>to SP Stack 06 of Enhancement Package 7 for SAP ERP 6.0 is released.</li>\r\n</ul>\r\n<p><strong>Notes to be applied:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>&#160;********************************************************************************************************************</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 05 (07/2014)</strong></span></p>\r\n<p><strong>What's new in SP stack&#160;05?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 05 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 25. For more information about SAP ERP 6.0 SP Stack 25, see SAP Note <a target=\"_blank\" href=\"/notes/1894115\">1894115</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 07 (06/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">http://</a><a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">service.sap.com/maintenanceNW74</a></li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SP Stack 05 are: SAP HR 6.04 SP 75 and EA-HR 607 SP26. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Attention</span>: During the <strong>SP update</strong> using the SPAM tool, the import step \"Activate inactive runtime objects\" fails to create CDS views if the EKPO table is empty. <br />This issue occurs only on application running on MAXDB, DB2 and DB4 databases. To solve this issue, please run the report ZDDLSCREATE to create and reactivate the views. If this report is not available in your system, you can use SAP Note 2023690 to implement it. After the manual activation continue with the SPAM update</p>\r\n<p><span style=\"text-decoration: underline;\">Attention</span>: In case you use the product version &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; (SAP <strong>Fiori</strong> for SAP ERP 1.0), please be aware that the SP05-stack does only work with &#8216;&#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; SP03&#160;or SP02. The combination between EhP7 SP04 and &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; SP01 + SP00 is not supported.</p>\r\n<p>Before update to SP Stack 05 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>Activation of DDL sources fails during <strong>EHP update</strong> due to missing namespace not yet in the system . To work around this issue , ignore the error in SUM ( \"Accept non severe errors \" ) and proceed with the update . Post update , please implement the note 2016248 which activates the DDL sources.</p>\r\n<p>In case you are using <strong>DB2</strong>, SAP recommends to deploy SAP note <a target=\"_blank\" href=\"/notes/1832652\">1832652 </a>before updating to SP Stack 05 of Enhancement Package 7 for SAP ERP 6.0.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 05 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EhP7 SPS 05 -- EhP6 on Hana SPS07-- EhP6 SPS12 -- EhP5 SPS12 -- EhP4 SPS15 -- EhP3 SPS14 -- EhP2 SPS15 -- SAP ERP 6.0 SPS25</p>\r\n<p style=\"padding-left: 60px;\">For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EA-HR 607 SP26<br />LSOFE 607 SP11 &amp; LSOFE 617 SP04<br />MDG_FND 732 SP09 &amp; MDG_FND 747 SP05<br />MDG_APPL 607 SP09 &amp; MDG_APPL 617 SP05 <br />EA-FI 100 SP09<br />EA-APPL-GLO 607 SP08<br />EA-PS-GLO 607 SP08<br />EA-GLTRADE-GLO 607 SP07</p>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 11 (03/2014) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 12 (05/2014) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 07 (06/2014) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP note</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Update/Upgrade </strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2040866\">2040866</a></td>\r\n<td>Update R3load for Software Provisioning Manager 1.0</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2023690\">2023690</a></td>\r\n<td>CDS views are inactive</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2023690\">2039085</a></td>\r\n<td>upgrade fails with Retcode 1: SQL-error -2048-column store error PARCONV_UP</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Systemcopy</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2033982\">2033982</a></td>\r\n<td>SMIGR_CREATE_DDL: Basis corrections in 7.30 SP13, 7.31 SP14, and 7.40 SP9</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2031638\">2031638</a></td>\r\n<td>R3load terminates during creation of an index for a field of the type LOB</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Generell</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2002786\">2002786</a></p>\r\n</td>\r\n<td>POWL: Table is rendered blank on switching between queries</td>\r\n</tr>\r\n<tr>\r\n<td><strong><strong>Commodity Management</strong></strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2039049\">2039049</a></p>\r\n</td>\r\n<td>IMG: Free Characteristics Documentation - Logistics Part Missing</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2038634\">2038634</a></p>\r\n</td>\r\n<td>Free Characteristics not saved with a new created Logistics Document</td>\r\n</tr>\r\n<tr>\r\n<td><strong><strong>Claims Management</strong>&#160;</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2051746\">2051746</a><strong>&#160;</strong></p>\r\n</td>\r\n<td>Removing items with 0 value from the SD Invoice created from settlement to invoice</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Data Privacy Project (DPP)</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"http://service.sap.cm/sap/support/notes/2039087\">2039087</a></td>\r\n<td>Release Information for Simplified Data Deletion based on SAP ILM</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Enterprise Asset Mgmt</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1983540\">1983540</a></td>\r\n<td>List ATS Component: Freestyle value help, method initialize_guibb_vh not getting called</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1996553\">1996553</a></p>\r\n</td>\r\n<td>List ATS Component: Mass Edit Mode: Setting new template data does not work</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2002786\">2002786</a></p>\r\n</td>\r\n<td>POWL: Table is rendered blank on switching between queries</td>\r\n</tr>\r\n<tr>\r\n<td><strong>IDOC Processing</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2020879\">2020879</a></p>\r\n</td>\r\n<td>CHRMAS: Sequence of characteristic values</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Leave request</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2018826\">2018826</a></p>\r\n</td>\r\n<td>Leave Request UI5: Issues with Leave History and Planning tab</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2011468\">2011468</a></p>\r\n</td>\r\n<td>Incorrect shortfall when total wage is less than 1500</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Master data Governance </strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2014019\">2014019</a><strong>&#160;</strong></p>\r\n</td>\r\n<td>WD ABAP: Last line not displayed in some forms</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2022343\">2022343</a><strong>&#160;</strong></p>\r\n</td>\r\n<td>WD ABAP: Empty space between form groups</td>\r\n</tr>\r\n<tr>\r\n<td><strong><strong>MRP Cockpit</strong></strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2033330\">2033330</a></td>\r\n<td>MD01N: Purchasing group missing from purchase requisition</td>\r\n</tr>\r\n<tr>\r\n<td><strong>PLM</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2037767\">2037767</a></p>\r\n</td>\r\n<td>Corrections to Confirm Network Activity</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Pricing</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2019488\">2019488</a></p>\r\n</td>\r\n<td>Pricing always creates a trace when executing a BRFplus function</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Public Sector</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1990550\">1990550</a></p>\r\n</td>\r\n<td>Transient and tabbed UIBB</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 05:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<p>Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2051746\">2051746</a></p>\r\n</td>\r\n<td>Removing items with 0 value from the SD Invoice created from settlement to invoice</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Changed: Before update to SP Stack 05 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a> ). <strong>This SAP HANA database revision has been verified in SAP production systems.</strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .Changed SAP Hana</p>\r\n<p>&#160;_______________________________________________________________________________________________________</p>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 04 (04/2014)</strong></span></p>\r\n<p><strong>What's new in SP stack&#160;04?</strong></p>\r\n<p>Information about new functionality can be found&#160;on&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_ERP\">SAP Help Portal</a>&#160;under &gt; What's New - Release Notes.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 04 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 25. For more information about SAP ERP 6.0 SP Stack 25, see SAP Note <a target=\"_blank\" href=\"/notes/1894115\">1894115</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">http://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 06 (03/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">http://</a><a target=\"_blank\" href=\"http://service.sap.com/maintenaceNW74\">service.sap.com/maintenanceNW74</a></li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SPS03 are: SAP HR 6.04 SP&#160;72 and EA-HR 607 SP23. For more information about the HR SP content, see SAP note <a target=\"_blank\" href=\"/notes/1980053\">1980053</a>.</li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Attention: In case you use the product version &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; (SAP <strong>Fiori</strong> for SAP ERP 1.0), please be aware that the SP04-stack does only work with &#8216;&#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; SP02 or SP01. The combination between EhP7 SP04 and &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; SP00 is not supported.</p>\r\n<p>Before update to SP Stack 04 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimal <strong>revision 74</strong> of SP stack 07 of SAP HANA database (see SAP note <a target=\"_blank\" href=\"/notes/2003736\">2003736</a>). SAP in general recommends customers to implement the <strong>highest revision</strong> available to get all available fixes if facing an issue with their SAP HANA installation and&#160;unless otherwise recommended by their implementation partner. For more details, please refer to the <em>SAP HANA revision strategy</em> document on SAP Service Marketplace at <a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">https://service.sap.com/&#126;sapidb/011000358700001182742013</a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is released for SAP HANA database 2.</p>\r\n<p>In case you are&#160;using <strong>DB Sybase,&#160; </strong>SAP&#160;recommends to deploy SAP note <a target=\"_blank\" href=\"/notes/1929422\">1929422 </a>before updating to SP Stack 04 of Enhancement Package 7 for SAP ERP 6.0.</p>\r\n<p>In case you are using <strong>DB2</strong>, SAP recommends to deploy SAP note <a target=\"_blank\" href=\"/notes/1832652\">1832652 </a>before updating to SP Stack 04 of Enhancement Package 7 for SAP ERP 6.0.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 04 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EhP7 SPS 04 -- EhP6 on Hana SPS06-- EhP6 SPS11 -- EhP5 SPS12 -- EhP4 SPS15 -- EhP3 SPS14 -- EhP2 SPS15 -- SAP ERP 6.0 SPS25</p>\r\n<p style=\"padding-left: 60px;\">For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EA-HR 607 SP23<br />LSOFE 607 SP10 &amp; LSOFE 617 SP03<br />MDG_FND 732 SP08 &amp; MDG_FND 747 SP04<br />MDG_APPL 607 SP08 &amp; MDG_APPL 617 SP04 <br />EA-FI 100 SP08<br />EA-APPL-GLO 607 SP07<br />EA-PS-GLO 607 SP07<br />EA-GLTRADE-GLO 607 SP06</p>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 11 (03/2014) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 11 (03/2014) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 06 (03/2014) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP note</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Update </strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1929422\">1929422</a></td>\r\n<td>Performance improvement when changing defaults on Sybase (Recommended before u&#160;if you are using DB Sybase)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1832652\">1832652</a></td>\r\n<td>DB2-z/OS: Fix drop/create view</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Generell</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2009426\">2009426</a></td>\r\n<td>\r\n<p>HANA content getting deleted in table SNHI_DU_PROXY after \"Client Copy by Transport Request\" using transaction SCC1</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1981272\">1981272</a></td>\r\n<td>Type-Ahead: Dialog not working with SAP GUI 7.30 PL8</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1989550\">1989550</a></td>\r\n<td>Type-Ahead: Search dialog does not close</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Change Document</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1981491\">1981491</a></td>\r\n<td>Performance problem while viewing change documents for a DIR with plus character</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Document Builder</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1987309\">1987309</a></td>\r\n<td>CHECKMAN issue, remove unused method from interface</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1988004\">1988004</a></td>\r\n<td>ATC issue: Reading of Database Entries in Web Dynpro</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Financial</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1994161\">1994161</a></td>\r\n<td>Change WebDynpro configuration to fix leading selection change issue in \"My documents\" and \"To be verified documents\" pages</td>\r\n</tr>\r\n<tr>\r\n<td><strong>HCM</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1727067\">1727067</a></td>\r\n<td>CATS Calendar UI: Error when no target hours are available ???</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1970053\">1970053</a></td>\r\n<td>Incorrect rendering of lanes in Bluecrystal theme and new stylesheets related to payroll lane</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1984497\">1984497</a></td>\r\n<td>System Does not Allow More than 40 Characters of Text for Field TRLTXT in view T7UNOM_P_TITLE</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1986313\">1986313</a></td>\r\n<td>Search Lane - Facet filters are not working properly together with facet filter Search field</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1987421\">1987421</a></td>\r\n<td>LSO Social Learning - Structure corrections (Enh. Catgory)</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Hospital</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1967161\">1967161</a></td>\r\n<td>SOAP Eventing: dump during message processing in XI</td>\r\n</tr>\r\n<tr>\r\n<td><strong>MDG</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1940674\">1940674</a></td>\r\n<td>Inconsistent iDoc after CCTR replication (COSMAS01 basic type) - wrong or missing E1CSKTM segment</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1964220\">1964220</a></td>\r\n<td>Context based Value mapping</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1991042\">1991042</a></td>\r\n<td>Checkman: Adjustment of authorization proposal of WebDynpro Application Configuration MDG_BS_SUPPLIER_HOME_PFCG_03</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1984733\">1984733</a></td>\r\n<td>Syntax error in query method</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1982176\">1982176</a></td>\r\n<td>Application error and program termination when you copy client</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1983812\">1983812</a></td>\r\n<td>CheckMan errors occur.</td>\r\n</tr>\r\n<tr>\r\n<td><strong>NW Content Mgmt</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1953873\">1953873</a></td>\r\n<td>Certificate error while fetching the documents from the Content Server</td>\r\n</tr>\r\n<tr>\r\n<td><strong>PLM</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1593941\">1593941</a></td>\r\n<td>Specification search UI setup for EhP6</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1977483\">1977483</a></td>\r\n<td>Modification of search view for search of file content (file loader scenario)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2007013\">2007013</a></td>\r\n<td>Syntax error after upgrade</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Processing Commodity</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1979475\">1979475</a></td>\r\n<td>CPE: Additional corrections in PI_BASIS SP06</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1990135\">1990135</a></td>\r\n<td>Quotation period does not consider timing</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Manufacturing Execution</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2007293\">2007293</a></td>\r\n<td>Corrections in transaction CS11H, CS12H and CS13H</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2007394\">2007318</a></td>\r\n<td>Corrections for AMDP BOM procedures</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2007394\">2007394</a></td>\r\n<td>Parameter Effectivity Functionality Not Working For CS11h/CS12h/Cs13h</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2008052\">2008052</a></td>\r\n<td>Parameters for initial screen for CS11H not set</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2008056\">2008056</a></td>\r\n<td>Base Quantity for transaction CS13H is wrong and saving layout for tree view</td>\r\n</tr>\r\n<tr>\r\n<td><strong>MM Optimizations</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1942187\">1942187</a></td>\r\n<td>ESH: Snapshot search</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1955265\">1955265</a></td>\r\n<td>SNOTE: Remove non-ASCII characters while SW Comp. import</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1957352\">1957352</a></td>\r\n<td>Deletion of references for logical expressions can be activated</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Processing Commodity Sales Orders</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1966604\">1966604</a></td>\r\n<td>Correction of the view TBAV_BADI_PER_FW</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1939715\">1939715</a></td>\r\n<td>Preparatory SAP Note for the Commodity Pricing Engine</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1957938\">1957938</a></td>\r\n<td>Preparatory Note for Commodity Pricing Engine</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1981258\">1981258</a></td>\r\n<td>Customizing for quotation rules does not display derivative type</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1981258\">1981977</a></td>\r\n<td>Term type requires DKS-ID, MIC and exchange rate indicator</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1983392\">1983392</a></td>\r\n<td>Commodity curve is read on the incorrect date</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1990135\">1990135</a></td>\r\n<td>Quotation period does not consider timing</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2004654\">2004654</a></td>\r\n<td>Customizing: CPE Term Rule - Description Displays Interchang</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2008628\">2008628</a></td>\r\n<td>Agreed conversion factor is not applied2004654</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Production Planning</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1993505\">1993505</a></td>\r\n<td>Error \"Number range not maintain\" occurs in Transaction code MD01N(MRP Live)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2008708\">2008708</a></td>\r\n<td>Planned order reexplosion does not delete old reservations</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Standard Sales Order Processing</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1972305\">1972305</a></td>\r\n<td>Issues in type-ahead for domain-fixed values</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1981272\">1981272</a></td>\r\n<td>Type-Ahead: Dialog not working with SAP GUI 7.30 PL8</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1989550\">1989550</a></td>\r\n<td>Type-Ahead: Search dialog does not close</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1990861\">1990861</a></td>\r\n<td>Type-Ahead: Convert INT1 (and other) fields correctly</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2001788\">2001788</a></td>\r\n<td>Details for value-based to-scales in price list</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2003740\">2003740</a></td>\r\n<td>Quantity Field is Missing in Net Price List</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2005151\">2005151</a></td>\r\n<td>KOMK-VKBUR and KOMK-VKGRP fields not filled</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2005219\">2005219</a></td>\r\n<td>Type-Ahead: Duplicate columns for domain-fixed values</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2007261\">2007261</a></td>\r\n<td>Create Sales Order: \"Order Type\" Search-Help Type-Ahead shows inconsistent results</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2008127\">2008127</a></td>\r\n<td>Type-Ahead: Dialog inserts previously chosen values again</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 04:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT RELEASE 1 (03/2014)</strong></span></p>\r\n<p><br />SR1: Enhancement Package 7 for SAP ERP 6.0 with Support Release 1 includes the support packages 1 to 3. If you perform an update with target release&#160;EHP7 for ERP 6.0 SR1, your system goes immediately to EHP7 for ERP 6.0 SP03 and SAP NetWeaver 7.40 SP06.</p>\r\n<p>Technical information about the ABAP Support Package levels&#160;contained in the&#160;Support Release 2&#160;can be found in SAP note <a target=\"_blank\" href=\"/notes/774615\">774615</a>.</p>\r\n<p>________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 03 (01/2014)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 03 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 24. For more information about SAP ERP 6.0 SP Stack 24, see SAP Note <a target=\"_blank\" href=\"/notes/1992288\">1992288</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 06 (03/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://</a><a target=\"_blank\" href=\"https://service.sap.com/maintenaceNW74\">service.sap.com/maintenanceNW74</a></li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SPS04 are: SAP HR 6.04 SP&#160;72 and EA-HR 607 SP23. For more information about the HRSP content, see SAP note <a target=\"_blank\" href=\"/notes/1830914\">1830914</a>.</li>\r\n</ul>\r\n<p>Additional Information about <strong>HCM</strong> upgrade can be found in SAP note <a target=\"_blank\" href=\"/notes/1631422\">1631422</a>.</p>\r\n<p>The implementation of SP Stack 03 is only possible via <strong>SUM</strong> tool and not as for previous support packages via SPAM as well. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>.</p>\r\n<p>Attention: In case you use the product version &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; (SAP <strong>Fiori</strong> for SAP ERP 1.0), please be aware that the SP03-stack does only work with &#8216;&#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; SP01. The combination between EhP7 SP03 and &#8216;UI FOR EHP7 FOR SAP ERP 6.0&#8217; SP00 is not supported.</p>\r\n<p>Before update to SP Stack 03 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimal <strong>revision&#160;70</strong> of SP stack 07 of SAP HANA database. Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>. SAP in general recommends customers to implement the <strong>highest revision</strong> available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160; SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p>You run your SAP NetWeaver 7.40 <strong>Java</strong> system (with an application from EHP7) on Hana database. If you want to update your system to Enhancement Package 7 SP stack 03, your Hana DB shall not be on revision 69 or 70 but it shall be on <strong>revision 63</strong>. You can find further details in SAP note <a target=\"_blank\" href=\"/notes/1968009\">1968009</a>.</p>\r\n<p>The following is only relevant if you do <span style=\"text-decoration: underline;\">not use BW</span> in the system you want to update: <br />Please follow SAP note <a target=\"_blank\" href=\"/notes/1629923\">1629923</a> to avoid activation issues in the XPRA phase of the update.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP&#160;Maintenance Planner&#160;calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 03 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EhP7 SPS 03 -- EhP6 on Hana SPS05-- EhP6 SPS10 -- EhP5 SPS11 -- EhP4 SPS14 -- EhP3 SPS13 -- EhP2 SPS14 -- SAP ERP 6.0 SPS24</p>\r\n<p style=\"padding-left: 60px;\">For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EA-HR 607 SP20<br />LSOFE 607 SP09 &amp; LSOFE 617 SP02<br />MDG_FND 732 SP07 &amp; MDG_FND 747 SP03<br />MDG_APPL 607 SP07 &amp; MDG_APPL 617 SP03<br />EA-FI 100 SP07<br />EA-APPL-GLO 607 SP06<br />EA-PS-GLO 607 SP06<br />EA-GLTRADE-GLO 607 SP05</p>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 10 (09/2013) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack&#160;10 (01/2014) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 05 (12/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP note</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Update</span></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1973964\">1973964</a></td>\r\n<td>PP MRP Live Deployment units problems during upgrade/installation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1981346\">1981346</a></td>\r\n<td>SAP HANA activation error during XPRA phase</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1977583\">1977583 </a></td>\r\n<td>SUM:CORE SERVICE [TC&#126;BL&#126;DEPLOY_CONTROLLER] FAILED TO START</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Other</span></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1966604\">1966604</a></td>\r\n<td>Correction of the view TBAV_BADI_PER_FW</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1952725\">1952725</a></td>\r\n<td>SEPA: SEPA_MANDATE_FIELD_STATUS_GET revised</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1851339\">1851339</a></td>\r\n<td>Incorrect Prices in My Prebookings Tab of transaction PV8I</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1938575\">1938575</a></td>\r\n<td>Conversion of the authorization check for entries in the query table</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1952996\">1952996</a></td>\r\n<td>Search using multiple connectors does not work after you implement SAP Note 1938575</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1955220\">1955220</a></td>\r\n<td>UOM : Tab is reading wrong text for different tabs in network objects</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1955307\">1955307</a></td>\r\n<td>Overlapping UIBB's and blackout on screen on using F4 help</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1928298\">1928298</a></td>\r\n<td>FPM Search GUIBB: \"reset to default\" dumps</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1892492\">1892492</a></td>\r\n<td>SHDB: Development Tool-Classes NW7.30 SP11</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1873585\">1873585</a></td>\r\n<td>TSW Delta Extraction not considering full date time range</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1969994\">1969994</a></td>\r\n<td>Voucher Numbering: context block for RV_INVOICE_DOCUMENT_ADD</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1987259\">1987259</a></td>\r\n<td>FX and commodity OTC option cannot be posted via TBB1</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 03:</strong></p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n<li>The following is only relevant if you do <span style=\"text-decoration: underline;\">not use BW</span> in the system you want to update: <br />Please follow SAP note <a target=\"_blank\" href=\"/notes/1629923\">1629923</a>&#160; to avoid activation issues in the XPRA phase of the update.</li>\r\n<li>Direct <strong>upgrades and EHP updates&#160;</strong>to Suite7i2013 SPS03 are released now for all supported start releases.</li>\r\n</ul>\r\n<p>Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1977583\">1977583 </a></td>\r\n<td>SUM:CORE SERVICE [TC&#126;BL&#126;DEPLOY_CONTROLLER] FAILED TO START</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1981346\">1981346</a></td>\r\n<td>SAP HANA activation error during XPRA phase</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1987259\">1987259</a></td>\r\n<td>FX and commodity OTC option cannot be posted via TBB1</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>________________________________________________________________________________________________</p>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 02 (11/2013)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 7 for SAP ERP 6.0 SP Stack 02 is based on:</p>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 24. For more information about SAP ERP 6.0 SP Stack 24, see SAP Note <a target=\"_blank\" href=\"/notes/1894115\">1894115</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-ins\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 04 (10/2013). or more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">https://</a><a target=\"_blank\" href=\"http://service.sap.com/maintenance74\">service.sap.com/maintenanceNW74</a></li>\r\n<li>The <strong>HR</strong> Components available with Enhancement Package 7 SPS02 are: SAP HR 6.04 SP 66 and EA-HR 607 SP17. For more information about the HRSP content, see SAP note <a target=\"_blank\" href=\"/notes/1830914\">1830914</a>.</li>\r\n</ul>\r\n<p>Additional Information about <strong>HCM</strong> upgrade can be found in SAP note <a target=\"_blank\" href=\"/notes/1631422\">1631422</a>.</p>\r\n<p>In case you have deployed SAP <strong>LSOFE 607 SP 07 or SP08</strong> you cannot update to Enhancement Package 7 for SAP ERP 6.0 SP Stack 02.</p>\r\n<p>When applying SP Stack 02 of SAP Enhancement Package 7 for SAP ERP 6.0, <strong>Hana Content</strong> is not activated automatically. Your HANA DB version 1.0 should be at least <strong>revision 69</strong>, please ensure also that your version of the HANA Studio has the same level, see SAP note <a target=\"_blank\" href=\"/notes/1928220\">1928220</a>. SAP HANA database 2.0 is not released for Enhancement Package&#160;7 for SAP ERP 6.0.</p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;Please be aware that some add-ons are not yet released for SAP HANA database 2 (e.g.&#160;SAP SFINANCIALS 1.0).</p>\r\n<p>SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 02 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">EhP7 SPS 02 -- EhP6 on Hana SPS04-- EhP6 SPS09 -- EhP5 SPS11 -- EhP4 SPS14 -- EhP3 SPS13 -- EhP2 SPS14 -- SAP ERP 6.0 SPS24</p>\r\n<p style=\"padding-left: 60px;\">For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n</ul>\r\n<p style=\"padding-left: 120px;\">EA-HR 607 SP17<br />LSOFE 607 SP06 &amp; LSOFE 617 SP01<br />MDG_FND 732 SP06&#160; &amp; MDG_FND 747 SP02<br />MDG_APPL 607 SP06 &amp; MDG_APPL 617 SP02<br />EA-FI 100 SP06<br />EA-APPL-GLO 607 SP05<br />EA-PS-GLO 607 SP05<br />EA-GLTRADE-GLO 607 SP04</p>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP note <a target=\"_blank\" href=\"/notes/1620472\">1620472</a>.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack&#160;10 (09/2013) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 09 (10/2013) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 04 (10/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP note</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1973964\">1973964</a></td>\r\n<td>PP MRP Live Deployment units problems during upgrade/installation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1886428\">1886428</a></td>\r\n<td>Side panel: Enhancements in Support Package 2, EhP7</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1908692\">1908692</a></td>\r\n<td>Corrections to unified rendering up to SAP_UI 740/04 I</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1915974\">1915974</a></td>\r\n<td>End User Feedback Side Panel - Missing STRUST entry (CA-GTF-SP)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1917865\">1917865</a></td>\r\n<td>Encoding in HRHAP_PORTAL_URL_PARAMS_ERP</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1922791\">1922791</a></td>\r\n<td>ATC Error in trigger exit class</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1929281\">1929281</a></td>\r\n<td>Digital Signature PT: missing records in SIPT_VBRK table</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1929336\">1929336</a></td>\r\n<td>Syntax error in CL_CBESH_MM_HELPER: GET_VENDOR_ANNUAL_SPEND</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1926094\">1926094</a></td>\r\n<td>Unauthorized modification of displayed content in IC_BASE</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1893788\">1893788</a></td>\r\n<td>Conversions in SAP NetWeaver Gateway Framework</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1917237\">1917237</a></td>\r\n<td>Performance correction of CL_SLS_BUSINESSDOCFLOWERP001QR</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1855009\">1855009</a></td>\r\n<td>Infotypes 0022 &amp; 0023: Text fields are missing</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1918693\">1918693</a></td>\r\n<td>Checkman - Corrections</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1916154\">1916154</a></td>\r\n<td>OpenSQL: DBIF_RSQL_INVALID_RSQL during SELECT</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1928220\">1928220</a></td>\r\n<td>ERP EHP7 SP02: Hana Content Activation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1973964\">1973964</a></td>\r\n<td>PP MRP Live Deployment units problems during upgrade/installation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1987259\">1987259</a></td>\r\n<td>FX and commodity OTC option cannot be posted via TBB1</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after Release of SP Stack 02:</strong>&#65279;</p>\r\n<ul>\r\n<li>[17.07.2018] APO 7.0 EHP3 ON ERP 6.0 EHP7 is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP SRM Server on one client&#160;scenario is&#160;released for SAP HANA database 2</li>\r\n<li>[23.01.2018] SAP Simple Finance, on-premise edition 1503 is&#160;released for SAP HANA database 2</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1973964\">1973964</a></td>\r\n<td>PP MRP Live Deployment units problems during upgrade/installation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1987259\">1987259</a></td>\r\n<td>FX and commodity OTC option cannot be posted via TBB1</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2286922\">2286922</a></td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2290115\">2290115</a></td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>******************************************************************************************************</p>\r\n<p><br /><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 01 (08/2013)</strong></span></p>\r\n<p><strong>Installation Requirements</strong>: Enhancement Package 7 for SAP ERP 6.0 SP Stack 01 is based on:</p>\r\n<ul>\r\n<li>\r\n<div style=\"padding-left: 30px;\">SAP ERP 6.0 SP Stack 23. For more information about SAP ERP 6.0 SP Stack 23, see SAP Note 1824905. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://</a><a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">service.sap.com/erp-inst</a>.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">SAP NetWeaver 7.40 Support Package Stack 03 (07/2014). or more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http</a><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">://</a><a target=\"_blank\" href=\"http://service.sap.com/maintenance74\">service.sap.com/maintenanceNW74</a>.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">The HR Components available with Enhancement Package 7 SPS01 are: SAP HR 6.04 SP 62 and EA-HR 607 SP13. For more information about the HRSP content, see SAP note <a target=\"_blank\" href=\"/notes/1830914\">1830914</a>.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">Additional Information about HCM upgrade can be found in SAP note <a target=\"_blank\" href=\"/notes/1631422\">1631422</a>.</div>\r\n</li>\r\n</ul>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for EhP7 Support Package Stack (SPS) 01 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;EhP7 SPS 01 -- EhP6 on Hana SPS02-- EhP6 SPS07 -- EhP5 SPS10 -- EhP4 SPS13 -- EhP3 SPS12 -- EhP2 SPS13 -- SAP ERP 6.0 SPS23<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For more information about Enhancement Packages and equivalences, see SAP note <a target=\"_blank\" href=\"/notes/1064635\">1064635</a>.</p>\r\n<ul>\r\n<li>Apart from the above mentioned SAP ERP 6.0 and its subsequent enhancement packages, there are also other product versions (or rather software component versions) that might be considered in the course of update concerning equivalences:</li>\r\n</ul>\r\n<p style=\"padding-left: 120px;\">EA-HR 607 SP11<br />LSOFE 607 SP06 &amp; LSOFE 617 SP01<br />MDG_FND 732 SP04 &amp; MDG_FND 747 SP01<br />MDG_APPL 607 SP04 &amp; MDG_APPL 617 SP01<br />EA-FI 100 SP04<br />EA-APPL-GLO 607 SP03<br />EA-PS-GLO 607 SP03<br />EA-GLTRADE-GLO 607 SP02</p>\r\n<p><strong>SAP NetWeaver SP Stack Levels</strong></p>\r\n<p>In September 2011 SAP introduced the so called 'Minimum SP Stack Level' in order to simplify the maintenance process in bigger system landscapes. Based on SAP's version interoperability paradigm, decoupled software updates in system landscapes can now also be offered for SP stack implementations. Details about version interoperability and the supported software update strategies are described here:<br /><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy</a></p>\r\n<p>Based on the Landscape Pattern (HUB or SIDECAR) defined for the JAVA system, the Maintenance Optimizer will determine the corresponding SAP NetWeaver SP Stack. Please refer also to SAP Note 1620472.</p>\r\n<p>You do not need to apply a new SP Stack on the technical instance defined as HUB in case your JAVA instance is <strong>greater or equal</strong> the defined Minimum SP Stack Level outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 02 (03/2011) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 03 (04/2012) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 02 (05/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>If your JAVA instance is <strong>below </strong>the defined Minimum SP Stack Level, you need to apply the following Recommended SAP NetWeaver SP Stacks for instances in the cases outlined below:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.30 SP Stack 09 (03/2013) - If you are running your JAVA instance on SAP NetWeaver 7.30</li>\r\n<li>SAP NetWeaver 7.31 SP Stack 08 (07/2013) - If you are running your JAVA instance on SAP NetWeaver 7.31</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 03 (07/2013) - If you are running your JAVA instance on SAP NetWeaver 7.40</li>\r\n</ul>\r\n<p>For more information about the NW SP Stacks, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenance73\">http://service.sap.com/maintenance73</a>.</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to processes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th><strong>SAP Note Number</strong></th><th>Description</th></tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\"><strong>General Notes (process independent:</strong></span></td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1269853</td>\r\n<td>Correct ZF Message Class WRF_PCM Nr. 121</td>\r\n</tr>\r\n<tr>\r\n<td>1825653</td>\r\n<td>Checkman error with transaction HRMGE00POL</td>\r\n</tr>\r\n<tr>\r\n<td>1835975</td>\r\n<td>Checkman Error CL_LSO_BSP_EXT_SKIPOPTION</td>\r\n</tr>\r\n<tr>\r\n<td>1836783</td>\r\n<td>CALL_FUNCTION_NO_RECEIVER short dump in</td>\r\n</tr>\r\n<tr>\r\n<td>1846359</td>\r\n<td>Revision of ESS YEA Enhancement Phase 3</td>\r\n</tr>\r\n<tr>\r\n<td>1846498</td>\r\n<td>Downport errors</td>\r\n</tr>\r\n<tr>\r\n<td>1847139</td>\r\n<td>SAP_BS_FND 747</td>\r\n</tr>\r\n<tr>\r\n<td>1848269</td>\r\n<td>Connectivity protocol for XI runtime not</td>\r\n</tr>\r\n<tr>\r\n<td>1851171</td>\r\n<td>Error in OpenSQL</td>\r\n</tr>\r\n<tr>\r\n<td>1854562</td>\r\n<td>Open SQL: ORDER BY key1 ... keyN bypasses the</td>\r\n</tr>\r\n<tr>\r\n<td>1857806</td>\r\n<td>BP_SRV: Field APPL_RULE_VARIANT Renamed in Table BUTSORT</td>\r\n</tr>\r\n<tr>\r\n<td>1858284</td>\r\n<td>SU22: Default Authorization values for</td>\r\n</tr>\r\n<tr>\r\n<td>1859065</td>\r\n<td>View Maintenance showing records from all</td>\r\n</tr>\r\n<tr>\r\n<td>1861263</td>\r\n<td>Removal of Checkman errors</td>\r\n</tr>\r\n<tr>\r\n<td>1861560</td>\r\n<td>ALV-Grid: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED</td>\r\n</tr>\r\n<tr>\r\n<td>1861905</td>\r\n<td>FIN SA: Performance improvement of common</td>\r\n</tr>\r\n<tr>\r\n<td>1861916</td>\r\n<td>EhP3 SP00 Corr. Close: ATC Checks</td>\r\n</tr>\r\n<tr>\r\n<td>1861984</td>\r\n<td>Result set of select statement is not sorted</td>\r\n</tr>\r\n<tr>\r\n<td>1862003</td>\r\n<td>Result Set of select statement is not sorted</td>\r\n</tr>\r\n<tr>\r\n<td>1862665</td>\r\n<td>Result set of select statement is not sorted</td>\r\n</tr>\r\n<tr>\r\n<td>1862696</td>\r\n<td>MDG: Business partner replication terminates due to bank key</td>\r\n</tr>\r\n<tr>\r\n<td>1862698</td>\r\n<td>Update termination in function module</td>\r\n</tr>\r\n<tr>\r\n<td>1862722</td>\r\n<td>BP migration: Business partners are not migrated</td>\r\n</tr>\r\n<tr>\r\n<td>1862758</td>\r\n<td>Collective note for SQL-Select sorting (Reuse)</td>\r\n</tr>\r\n<tr>\r\n<td>1862985</td>\r\n<td>Short dump during change number switch</td>\r\n</tr>\r\n<tr>\r\n<td>1863011</td>\r\n<td>Resolving Code Inspector Issue in Report</td>\r\n</tr>\r\n<tr>\r\n<td>1863493</td>\r\n<td>Technical note</td>\r\n</tr>\r\n<tr>\r\n<td>1864074</td>\r\n<td>Collective note for SQL-Select sorting</td>\r\n</tr>\r\n<tr>\r\n<td>1864282</td>\r\n<td>New tariffs for postal orders since 01.04.2013</td>\r\n</tr>\r\n<tr>\r\n<td>1864310</td>\r\n<td>EhP3: Fixing Checkman Error</td>\r\n</tr>\r\n<tr>\r\n<td>1864606</td>\r\n<td>MB5T: ITAB_DUPLICATE_KEY in function group</td>\r\n</tr>\r\n<tr>\r\n<td>1864889</td>\r\n<td>Class CL_AXT_OLTP_CTX is obsolete and should</td>\r\n</tr>\r\n<tr>\r\n<td>1864934</td>\r\n<td>Solution for ABAP Code Scan Findings</td>\r\n</tr>\r\n<tr>\r\n<td>1865003</td>\r\n<td>PY-GB: RTI Display report DUMPs with old data</td>\r\n</tr>\r\n<tr>\r\n<td>1865176</td>\r\n<td>Serial numbers cannot be displayed in ME23N</td>\r\n</tr>\r\n<tr>\r\n<td>1866494</td>\r\n<td>Result Set of select statement is not sorted</td>\r\n</tr>\r\n<tr>\r\n<td>1868365</td>\r\n<td>Dump CONV_EXIT_FIELD_TOO_SHORT in TA KB21N</td>\r\n</tr>\r\n<tr>\r\n<td>1868660</td>\r\n<td>Checkman errors due to SAP_BASIS/SAP_BS_FND</td>\r\n</tr>\r\n<tr>\r\n<td>1870680</td>\r\n<td>DB6: SYSTEM_CORE_DUMPED in dsql_db6_fetch</td>\r\n</tr>\r\n<tr>\r\n<td>1875109</td>\r\n<td>LOPT: Error on Master Data Extraction and</td>\r\n</tr>\r\n<tr>\r\n<td>1875883</td>\r\n<td>Syntax errors after importing SAP_BASIS 740</td>\r\n</tr>\r\n<tr>\r\n<td>1875964</td>\r\n<td>Raw Exposures: Sum of all weightages must be</td>\r\n</tr>\r\n<tr>\r\n<td>1860537</td>\r\n<td>EhP3 ATC Checks</td>\r\n</tr>\r\n<tr>\r\n<td>1861916</td>\r\n<td>EhP3 SP00 Corr. Close: ATC Checks</td>\r\n</tr>\r\n<tr>\r\n<td>1858049</td>\r\n<td>WEBCUIF: View of BSP test application causes</td>\r\n</tr>\r\n<tr>\r\n<td>1860953</td>\r\n<td>Restrictions for Pricing in EHP7</td>\r\n</tr>\r\n<tr>\r\n<td>2286922</td>\r\n<td>Surcharge rule on quotation group item level used for all quotation group items</td>\r\n</tr>\r\n<tr>\r\n<td>2290115</td>\r\n<td>Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922</td>\r\n</tr>\r\n<tr>\r\n<td>2420699</td>\r\n<td>Release of SAP HANA Database 2.0 for older SAP Versions</td>\r\n</tr>\r\n<tr>\r\n<td>2494263</td>\r\n<td>Support for SAP HANA 2 in SAP ERP 6.0 Enhancement Package 7 - Status of application support for specific functionalities of SAP HANA 2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">NetWeaver</span></td>\r\n</tr>\r\n<tr>\r\n<td>1859065</td>\r\n<td>View Maintenance showing records from all</td>\r\n</tr>\r\n<tr>\r\n<td>1870680</td>\r\n<td>DB6: SYSTEM_CORE_DUMPED in dsql_db6_fetch</td>\r\n</tr>\r\n<tr>\r\n<td>1851171</td>\r\n<td>Error in OpenSQL</td>\r\n</tr>\r\n<tr>\r\n<td>1836783</td>\r\n<td>CALL_FUNCTION_NO_RECEIVER short dump in</td>\r\n</tr>\r\n<tr>\r\n<td>1854562</td>\r\n<td>Open SQL: ORDER BY key1 ... keyN bypasses the</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Process dependent notes:</strong></td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Accounts Receivables</span></td>\r\n</tr>\r\n<tr>\r\n<td>1784807</td>\r\n<td>SAP HANA enhancements to transaction F.30</td>\r\n</tr>\r\n<tr>\r\n<td>1797667</td>\r\n<td>SQLScript procedure activation</td>\r\n</tr>\r\n<tr>\r\n<td>1798895</td>\r\n<td>Add.information about manual ABAP on HANA</td>\r\n</tr>\r\n<tr>\r\n<td>1826007</td>\r\n<td>SAP HANA free text search FUZZY</td>\r\n</tr>\r\n<tr>\r\n<td>1871826</td>\r\n<td>AR CHIP : Key Figure : Future Outstanding</td>\r\n</tr>\r\n<tr>\r\n<td>1871914</td>\r\n<td>CHIP \"Customers New in dunning level\" dumps</td>\r\n</tr>\r\n<tr>\r\n<td>1876885</td>\r\n<td>CHIPs for Overdue-Analysis and Due Forecast</td>\r\n</tr>\r\n<tr>\r\n<td>1881281</td>\r\n<td>Business Suite on SAP HANA content activation</td>\r\n</tr>\r\n<tr>\r\n<td>1884627</td>\r\n<td>CHIP Customers per Risk Class shows incorrect</td>\r\n</tr>\r\n<tr>\r\n<td>1887433</td>\r\n<td>Correction of F.30 Due date analysis currency</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Billing of Energy Products</span></td>\r\n</tr>\r\n<tr>\r\n<td>1863909</td>\r\n<td>Translation of 'Execute Conditional Billing</td>\r\n</tr>\r\n<tr>\r\n<td>1874204</td>\r\n<td>Selection by rate category/schema with conditional billing</td>\r\n</tr>\r\n<tr>\r\n<td>1878274</td>\r\n<td>Error 151 (BIL)</td>\r\n</tr>\r\n<tr>\r\n<td>1879345</td>\r\n<td>CPP formula: Error during determ. of</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Creating and Managing Specifications (PLM)</span></td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1857795</td>\r\n<td>Runtime error in program</td>\r\n</tr>\r\n<tr>\r\n<td>1889960</td>\r\n<td>Error while performing MSDS upload without</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Developing Recipes and Formulas (PLM)</span></td>\r\n</tr>\r\n<tr>\r\n<td>1887877</td>\r\n<td>Calculation Parameters popup opens with error</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Fixed Asset Accounting</span></td>\r\n</tr>\r\n<tr>\r\n<td>1871895</td>\r\n<td>Error during fiscal year change - fixed assets not switched</td>\r\n</tr>\r\n<tr>\r\n<td>1889604</td>\r\n<td>Reporting - missing assets</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Global Label Management</span></td>\r\n</tr>\r\n<tr>\r\n<td>1883699</td>\r\n<td>Building block catalog - Functions executed</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Grouping, Pegging and Distribution</span></td>\r\n</tr>\r\n<tr>\r\n<td>99507</td>\r\n<td>Update terminations after upgrade/client copy</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">In-Store Merchandise and Inventory Management</span></td>\r\n</tr>\r\n<tr>\r\n<td>28683</td>\r\n<td>PERFORM_NOT_FOUND: VOFM routine is not active</td>\r\n</tr>\r\n<tr>\r\n<td>1873433</td>\r\n<td>Goods Receipt with reference to HU via Is-Store MIM</td>\r\n</tr>\r\n<tr>\r\n<td>1876797</td>\r\n<td>MIM, purchase order: button \"Edit\" does not work correctly</td>\r\n</tr>\r\n<tr>\r\n<td>1891355</td>\r\n<td>WRFMATCOPY incorrectly creates promotion prices</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Offer to Cash</span></td>\r\n</tr>\r\n<tr>\r\n<td>880749</td>\r\n<td>Patching of VMC based AP 7.00 engines</td>\r\n</tr>\r\n<tr>\r\n<td>1839668</td>\r\n<td>FKKBIX: Change of fld length for BIT classes</td>\r\n</tr>\r\n<tr>\r\n<td>1852374</td>\r\n<td>Profit center acct assgmt in Billing in</td>\r\n</tr>\r\n<tr>\r\n<td>1857532</td>\r\n<td>CL_THTMLB_CELLERATOR error in pop up.</td>\r\n</tr>\r\n<tr>\r\n<td>1878639</td>\r\n<td>WEBCUIF IE9-Standards mode</td>\r\n</tr>\r\n<tr>\r\n<td>1868128</td>\r\n<td>Provider Mass Run: Pie chart for Mass Creation Category</td>\r\n</tr>\r\n<tr>\r\n<td>1884145</td>\r\n<td>Release of provisional invoices from the clarification processing</td>\r\n</tr>\r\n<tr>\r\n<td>1885140</td>\r\n<td>Transferring sales organization data to the</td>\r\n</tr>\r\n<tr>\r\n<td>1885362</td>\r\n<td>FKKBIX: Changing field length for CIT classes</td>\r\n</tr>\r\n<tr>\r\n<td>1886399</td>\r\n<td>Master Agreemen: BTAdminI not bound in</td>\r\n</tr>\r\n<tr>\r\n<td>1888405</td>\r\n<td>SV O2C: More processes than ISTA available</td>\r\n</tr>\r\n<tr>\r\n<td>1889158</td>\r\n<td>Edit of Notes in Provider Order document not</td>\r\n</tr>\r\n<tr>\r\n<td>1892239</td>\r\n<td>FKK_VT: Derivation of prepaid accounts from reference does not find anything</td>\r\n</tr>\r\n<tr>\r\n<td>1870580</td>\r\n<td>FKKBIX: Create CIT and BIT in the future</td>\r\n</tr>\r\n<tr>\r\n<td>1860040</td>\r\n<td>FICA/SEPA: Mandate checks even though mandates</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Patient Administration and Services</span></td>\r\n</tr>\r\n<tr>\r\n<td>1887184</td>\r\n<td>Business partner migration: Error message for test migration</td>\r\n</tr>\r\n<tr>\r\n<td>1889679</td>\r\n<td>Phase 3 migration OM: Confirming downtime -&gt;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Production Planning (Discrete Manufacturing)</span></td>\r\n</tr>\r\n<tr>\r\n<td>1895924</td>\r\n<td>Not activating BF LOG_PPH_MDPSX_READ without</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Project Planning with PS</span></td>\r\n</tr>\r\n<tr>\r\n<td>1873506</td>\r\n<td>SNOTE and TABU transports</td>\r\n</tr>\r\n<tr>\r\n<td>1879988</td>\r\n<td>SNOTE: SW Component inclusion cannot be</td>\r\n</tr>\r\n<tr>\r\n<td>1889094</td>\r\n<td>IM_AVCHANA_ORD/WBS: Dumps DBSQL_SQL_ERROR,</td>\r\n</tr>\r\n<tr>\r\n<td>1892146</td>\r\n<td>Availability of Embedded Search on HANA</td>\r\n</tr>\r\n<tr>\r\n<td>1892217</td>\r\n<td>IM_AVCHANA_ORD/WBS: Dump SAPSQL_PARSE_ERROR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Simulation and Planning</span></td>\r\n</tr>\r\n<tr>\r\n<td>1813523</td>\r\n<td>No business function can be activated in SFW5.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Standard Sales Order Processing in ERP</span></td>\r\n</tr>\r\n<tr>\r\n<td>1549286</td>\r\n<td>Missing Event Type function OBP10_PACK</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Weighting</span></td>\r\n</tr>\r\n<tr>\r\n<td>1868148</td>\r\n<td>Missing table entries for weighing and sample drawing</td>\r\n</tr>\r\n<tr>\r\n<td>1873584</td>\r\n<td>Corrections: Weighing</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Application and SAP BS_FND</span></td>\r\n</tr>\r\n<tr>\r\n<td>1269853</td>\r\n<td>Correct ZF Message Class WRF_PCM Nr. 121</td>\r\n</tr>\r\n<tr>\r\n<td>1825653</td>\r\n<td>Checkman error with transaction HRMGE00POL</td>\r\n</tr>\r\n<tr>\r\n<td>1846359</td>\r\n<td>Revision of ESS YEA Enhancement Phase 3</td>\r\n</tr>\r\n<tr>\r\n<td>1846498</td>\r\n<td>Downport errors</td>\r\n</tr>\r\n<tr>\r\n<td>1848269</td>\r\n<td>Connectivity protocol for XI runtime not</td>\r\n</tr>\r\n<tr>\r\n<td>1858284</td>\r\n<td>SU22: Default Authorization values for</td>\r\n</tr>\r\n<tr>\r\n<td>1861905</td>\r\n<td>FIN SA: Performance improvement of common</td>\r\n</tr>\r\n<tr>\r\n<td>1862696</td>\r\n<td>MDG: Business partner replication terminates due to bank key</td>\r\n</tr>\r\n<tr>\r\n<td>1862722</td>\r\n<td>BP migration: Business partners are not migrated</td>\r\n</tr>\r\n<tr>\r\n<td>1862985</td>\r\n<td>Short dump during change number switch</td>\r\n</tr>\r\n<tr>\r\n<td>1864606</td>\r\n<td>MB5T: ITAB_DUPLICATE_KEY in function group</td>\r\n</tr>\r\n<tr>\r\n<td>1864282</td>\r\n<td>New tariffs for postal orders since 01.04.2013</td>\r\n</tr>\r\n<tr>\r\n<td>1864889</td>\r\n<td>Class CL_AXT_OLTP_CTX is obsolete and should</td>\r\n</tr>\r\n<tr>\r\n<td>1865003</td>\r\n<td>PY-GB: RTI Display report DUMPs with old data</td>\r\n</tr>\r\n<tr>\r\n<td>1865176</td>\r\n<td>Serial numbers cannot be displayed in ME23N</td>\r\n</tr>\r\n<tr>\r\n<td>1868365</td>\r\n<td>Dump CONV_EXIT_FIELD_TOO_SHORT in TA KB21N</td>\r\n</tr>\r\n<tr>\r\n<td>1868660</td>\r\n<td>Checkman errors due to SAP_BASIS/SAP_BS_FND</td>\r\n</tr>\r\n<tr>\r\n<td>1875883</td>\r\n<td>Syntax errors after importing SAP_BASIS 740</td>\r\n</tr>\r\n<tr>\r\n<td>1875964</td>\r\n<td>Raw Exposures: Sum of all weightages must be</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">NetWeaver</span></td>\r\n</tr>\r\n<tr>\r\n<td>1859065</td>\r\n<td>View Maintenance showing records from all</td>\r\n</tr>\r\n<tr>\r\n<td>1870680</td>\r\n<td>DB6: SYSTEM_CORE_DUMPED in dsql_db6_fetch</td>\r\n</tr>\r\n<tr>\r\n<td>1851171</td>\r\n<td>Error in OpenSQL</td>\r\n</tr>\r\n<tr>\r\n<td>1836783</td>\r\n<td>CALL_FUNCTION_NO_RECEIVER short dump in</td>\r\n</tr>\r\n<tr>\r\n<td>1854562</td>\r\n<td>Open SQL: ORDER BY key1 ... keyN bypasses the</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022712)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022712)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001737650/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2818442", "RefComponent": "XX-SER-REL", "RefTitle": "Minimum supported start release for the upgrade to ERP Enhancement Packages", "RefUrl": "/notes/2818442"}, {"RefNumber": "2494263", "RefComponent": "XX-SER-REL", "RefTitle": "Support for SAP HANA 2 in SAP ERP 6.0 Enhancement Package 7 - Status of application support for specific functionalities of SAP HANA 2", "RefUrl": "/notes/2494263"}, {"RefNumber": "2420699", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Release of SAP HANA Database 2.0 for older SAP Versions", "RefUrl": "/notes/2420699"}, {"RefNumber": "2391758", "RefComponent": "LO-AB-IDM", "RefTitle": "Structure WLFS_IDOC_LIST_OUT contains duplicate fields", "RefUrl": "/notes/2391758"}, {"RefNumber": "2304435", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SAP ERP for SAP NetWeaver 7.5 hub systems", "RefUrl": "/notes/2304435"}, {"RefNumber": "2021789", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1.0 Revision and Maintenance Strategy", "RefUrl": "/notes/2021789"}, {"RefNumber": "1865866", "RefComponent": "XX-SER-REL", "RefTitle": "SAP EHP 7 for SAP ERP 6.0 - Release Information", "RefUrl": "/notes/1865866"}, {"RefNumber": "1853989", "RefComponent": "XX-SER-REL", "RefTitle": "Main Browser Note for SAP Business Suite", "RefUrl": "/notes/1853989"}, {"RefNumber": "1830914", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of SAP enhancement package 7", "RefUrl": "/notes/1830914"}, {"RefNumber": "1825774", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite Powered by SAP HANA - Multi-Node Support", "RefUrl": "/notes/1825774"}, {"RefNumber": "1820906", "RefComponent": "XX-SER-REL", "RefTitle": "EHP7 for SAP ERP 6.0: Compatible Add-ons", "RefUrl": "/notes/1820906"}, {"RefNumber": "1818596", "RefComponent": "XX-SER-REL", "RefTitle": "Enhancement package 7 for SAP ERP 6.0: Required SWC", "RefUrl": "/notes/1818596"}, {"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819"}, {"RefNumber": "1803986", "RefComponent": "BC-UPG-RDM", "RefTitle": "Rules to use SUM or SPAM/SAINT to apply SPs for ABAP stacks", "RefUrl": "/notes/1803986"}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566"}, {"RefNumber": "1742750", "RefComponent": "XX-SER-REL", "RefTitle": "Upgrade from SAP ERP 2004 to EHP7 FOR SAP ERP 6.0 (Java)", "RefUrl": "/notes/1742750"}, {"RefNumber": "1655335", "RefComponent": "BC-INS-DSS", "RefTitle": "Use Cases for Splitting Dual-Stack Systems", "RefUrl": "/notes/1655335"}, {"RefNumber": "1631422", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 / EHP7 FOR SAP ERP 6.0 - Add. Information to HCM Upg", "RefUrl": "/notes/1631422"}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3036681", "RefComponent": "BC-UPG-MP", "RefTitle": "MP: \"Install or Maintain an Enhancement Package\" does not work for a higher EHP release for ERP 6.0", "RefUrl": "/notes/3036681 "}, {"RefNumber": "2596913", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "ACT_UPG: indexes for table \"J_1BNFE_ACTIVE\" have identical fields", "RefUrl": "/notes/2596913 "}, {"RefNumber": "2535751", "RefComponent": "BC-UPG-MP", "RefTitle": "Can not select the higher release for Netweaver in Maintenance Planner", "RefUrl": "/notes/2535751 "}, {"RefNumber": "3232855", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of SAP enhancement package 6", "RefUrl": "/notes/3232855 "}, {"RefNumber": "3232818", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of HR Renewal 1.0 / ECC 6.0 EhP7 for HCM", "RefUrl": "/notes/3232818 "}, {"RefNumber": "2818442", "RefComponent": "XX-SER-REL", "RefTitle": "Minimum supported start release for the upgrade to ERP Enhancement Packages", "RefUrl": "/notes/2818442 "}, {"RefNumber": "2647328", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of SAP enhancement package 6", "RefUrl": "/notes/2647328 "}, {"RefNumber": "2647100", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of HR Renewal 1.0 / ECC 6.0 EhP7 for HCM", "RefUrl": "/notes/2647100 "}, {"RefNumber": "2524661", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709 - SAP HANA Database Requirements", "RefUrl": "/notes/2524661 "}, {"RefNumber": "2491065", "RefComponent": "MM-PUR-HUB-REQ", "RefTitle": "Release Information Note for HUBERPI Integration Component in SAP S/4HANA for Central Procurement", "RefUrl": "/notes/2491065 "}, {"RefNumber": "2494263", "RefComponent": "XX-SER-REL", "RefTitle": "Support for SAP HANA 2 in SAP ERP 6.0 Enhancement Package 7 - Status of application support for specific functionalities of SAP HANA 2", "RefUrl": "/notes/2494263 "}, {"RefNumber": "2284655", "RefComponent": "SD-SLS", "RefTitle": "Incoterms 2010 Restriction: ERP-CRM Integration", "RefUrl": "/notes/2284655 "}, {"RefNumber": "2268727", "RefComponent": "XX-SER-REL", "RefTitle": "SAP EHP 8 for SAP ERP 6.0 - Release Information with respect to HDB", "RefUrl": "/notes/2268727 "}, {"RefNumber": "2211026", "RefComponent": "FI-CA-FIO", "RefTitle": "Release Information Note: SAP Fiori for FI-CA - Analyze Overdue Items", "RefUrl": "/notes/2211026 "}, {"RefNumber": "2117481", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance, on-premise edition 1503 - Release Information Note", "RefUrl": "/notes/2117481 "}, {"RefNumber": "2144627", "RefComponent": "IS-R-BD-BBY", "RefTitle": "SAP ERP 6.0 EHP7 SP08 or higher: possible conversion-downtime due to field length change in db table KONBBYPRQ", "RefUrl": "/notes/2144627 "}, {"RefNumber": "2142126", "RefComponent": "FI-CA-FIO", "RefTitle": "General Information Contract Accounts Receivable and Payable (FI-CA) including Convergent Invoicing", "RefUrl": "/notes/2142126 "}, {"RefNumber": "1068198", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for Travel Management for CEE countries", "RefUrl": "/notes/1068198 "}, {"RefNumber": "2070410", "RefComponent": "FI-CA-FIO", "RefTitle": "Release Information Note: SAP <PERSON>ori for FI-CA - Analyze Write-Offs", "RefUrl": "/notes/2070410 "}, {"RefNumber": "2016444", "RefComponent": "SCM-BAS-MD", "RefTitle": "Release Information Note for Fiori applications in Supply Chain Management - Fact Sheets", "RefUrl": "/notes/2016444 "}, {"RefNumber": "2027829", "RefComponent": "BC-CTS-HTC", "RefTitle": "Error during import/update of SAP HANA Transport Container objects (HTC/NHDU)", "RefUrl": "/notes/2027829 "}, {"RefNumber": "1940450", "RefComponent": "SD-SLS", "RefTitle": "SAP Fiori: Sales Quotation (Fact Sheet)", "RefUrl": "/notes/1940450 "}, {"RefNumber": "1940532", "RefComponent": "SD-SLS", "RefTitle": "SAP Fiori: Sales Group Contract (Fact Sheet)", "RefUrl": "/notes/1940532 "}, {"RefNumber": "1940527", "RefComponent": "SD-SLS", "RefTitle": "SAP Fiori: Sales Contract (Fact Sheet)", "RefUrl": "/notes/1940527 "}, {"RefNumber": "1940369", "RefComponent": "SD-SLS", "RefTitle": "SAP Fiori: Sales Order (Fact Sheet)", "RefUrl": "/notes/1940369 "}, {"RefNumber": "2014775", "RefComponent": "PP-SFC", "RefTitle": "Release Information Note for the Fiori application 'Release Production Order Operations'", "RefUrl": "/notes/2014775 "}, {"RefNumber": "2017245", "RefComponent": "MM-PUR-SOH", "RefTitle": "Release Information Note for the Fiori application My Purchasing Document Items", "RefUrl": "/notes/2017245 "}, {"RefNumber": "1931274", "RefComponent": "PP-SFC", "RefTitle": "Release Information Note for Fiori applications in Manufacturing - Fact Sheets", "RefUrl": "/notes/1931274 "}, {"RefNumber": "2000923", "RefComponent": "MM-IV-LIV", "RefTitle": "Release Information Note for the Fiori application Approve Supplier Invoices", "RefUrl": "/notes/2000923 "}, {"RefNumber": "1914052", "RefComponent": "BC-UPG-PRP", "RefTitle": "Minimal HANA and MaxDB platform requirements for NetWeaver 7.40 Support Packages", "RefUrl": "/notes/1914052 "}, {"RefNumber": "1997487", "RefComponent": "IS-R-BD-BBY", "RefTitle": "General Information: Bonus Buy Fact Sheet", "RefUrl": "/notes/1997487 "}, {"RefNumber": "1980053", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of HR Renewal 1.0 / ECC 6.0 EhP7 for HCM", "RefUrl": "/notes/1980053 "}, {"RefNumber": "1976496", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of SAP enhancement package 6", "RefUrl": "/notes/1976496 "}, {"RefNumber": "1968568", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/1968568 "}, {"RefNumber": "1931282", "RefComponent": "QM-QN-NM", "RefTitle": "General Information: QM", "RefUrl": "/notes/1931282 "}, {"RefNumber": "1931281", "RefComponent": "QM-IM", "RefTitle": "General Information: QM Fact Sheets", "RefUrl": "/notes/1931281 "}, {"RefNumber": "1931296", "RefComponent": "IS-R", "RefTitle": "General Information: Retail Fact Sheets", "RefUrl": "/notes/1931296 "}, {"RefNumber": "1937112", "RefComponent": "IS-R-BD-SIT", "RefTitle": "General Information: Site Fact Sheet", "RefUrl": "/notes/1937112 "}, {"RefNumber": "1937111", "RefComponent": "IS-R-BD-PR", "RefTitle": "General Information: Retail Promotion Fact Sheet", "RefUrl": "/notes/1937111 "}, {"RefNumber": "1937109", "RefComponent": "IS-R-PUR-AL", "RefTitle": "General Information: Allocation Table Fact Sheet", "RefUrl": "/notes/1937109 "}, {"RefNumber": "1936941", "RefComponent": "IS-R-BD-ART", "RefTitle": "General Information: Article Fact Sheet", "RefUrl": "/notes/1936941 "}, {"RefNumber": "1914499", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information Note: UI FOR EHP7 FOR SAP ERP 6.0", "RefUrl": "/notes/1914499 "}, {"RefNumber": "1820906", "RefComponent": "XX-SER-REL", "RefTitle": "EHP7 for SAP ERP 6.0: Compatible Add-ons", "RefUrl": "/notes/1820906 "}, {"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819 "}, {"RefNumber": "1865866", "RefComponent": "XX-SER-REL", "RefTitle": "SAP EHP 7 for SAP ERP 6.0 - Release Information", "RefUrl": "/notes/1865866 "}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258 "}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566 "}, {"RefNumber": "1803986", "RefComponent": "BC-UPG-RDM", "RefTitle": "Rules to use SUM or SPAM/SAINT to apply SPs for ABAP stacks", "RefUrl": "/notes/1803986 "}, {"RefNumber": "1825774", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite Powered by SAP HANA - Multi-Node Support", "RefUrl": "/notes/1825774 "}, {"RefNumber": "1742750", "RefComponent": "XX-SER-REL", "RefTitle": "Upgrade from SAP ERP 2004 to EHP7 FOR SAP ERP 6.0 (Java)", "RefUrl": "/notes/1742750 "}, {"RefNumber": "1818596", "RefComponent": "XX-SER-REL", "RefTitle": "Enhancement package 7 for SAP ERP 6.0: Required SWC", "RefUrl": "/notes/1818596 "}, {"RefNumber": "1631422", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 / EHP7 FOR SAP ERP 6.0 - Add. Information to HCM Upg", "RefUrl": "/notes/1631422 "}, {"RefNumber": "1853989", "RefComponent": "XX-SER-REL", "RefTitle": "Main Browser Note for SAP Business Suite", "RefUrl": "/notes/1853989 "}, {"RefNumber": "1655335", "RefComponent": "BC-INS-DSS", "RefTitle": "Use Cases for Splitting Dual-Stack Systems", "RefUrl": "/notes/1655335 "}, {"RefNumber": "1830914", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of SAP enhancement package 7", "RefUrl": "/notes/1830914 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}