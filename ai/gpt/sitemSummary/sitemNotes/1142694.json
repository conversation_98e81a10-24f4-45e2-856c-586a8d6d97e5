{"Request": {"Number": "1142694", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 279, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016467032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001142694?language=E&token=C9B7FA446DD299070C328928ACEC3CAB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001142694", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001142694/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1142694"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.07.2010"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-LAS"}, "SAPComponentKeyText": {"_label": "Component", "value": "License Auditing Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "License Auditing Services", "value": "XX-SER-LAS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-LAS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1142694 - FAQ: Transferring measurement results from USMM"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>[1] Question: Which version of SDCC(N) or SAP_BASIS am I using?<br /><br />[2] Which system prerequisites do I require to transfer the measurement results from transaction USMM online?<br /><br />[3] Question: When I attempt to transfer the measurement results to SAP, the system issues the error message: \"Internal error: could not create the SDCCN session\". Message number BV 055. What should I do?<br /><br />[4] Question: How can I remove invalid locks in transaction USMM?<br />I cannot start a new measurement. The system issues the error message: \"Measurement data is locked for transfer\"<br />Message number BV 007is displayed or the \"Transfer Status\" remains as \"Data Locked for Transfer\".<br />When I try to send the measurement results to SAP, the system issues the error message: \"Internal error while saving the transfer status in table TUSTAT.\" Message number BV 182<br />What should I do?<br /><br />[5] Question: How can I determine the session ID of the current transfer?<br /><br />[6] Question: When I perform a transfer, the system issues the following error message: \"Data has already been passed to SDCC for transport to SAP\". What should I do?<br /><br />[7] Question: When I start transaction USMM or when I use transaction USMM to send the measurement results to SAP, a  termination occurs. What should I do?<br /><br />[8] Question: In our system, an SDCC version lower than Version 2.1 is installed. What should I do?<br /><br />[9] Question: In transaction SDCCN, the system displays the exception \"VCM_GET_MEAS_DATA_FOR_TRANSFER :&#x00A0;&#x00A0;exception DATA_LOCKED\" in the log. What should I do?<br /><br />[10] Question: In transaction SDCCN, the system displays the exception \"VCM_GET_MEAS_DATA_FOR_TRANSFER :&#x00A0;&#x00A0;exception NO_VALID_DATA\". What should I do?<br /><br />[11] Question: Is there an alternative transfer method for the<br />data transfer other than via SDCC/N?<br /><br />[12] Question: The transfer of measurement results via SDCC terminates . In transaction SDCC, the status of the transfer is \"red\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; and, in transaction ST22, a short dump \"CALL_FUNCTION_PARM_MISSING\" is displayed. What should I do?<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><NOBR><B><B>FAQ, Q&amp;A, USMM, SDCC, SDCCN, measurement results, termination</B></B></NOBR></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><NOBR><B><B></B></B></NOBR></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>[1] Answer: In your system, check which versions of the software components SAP_BASIS or ST-PI you are using.<br />In the SAP GUI menu, choose System -&gt; Status. In the area \"SAP System data\", choose the pushbutton (Component Information). Here you can view the version.<br /><br />If SDCC is active in your system, you can determine the SDCC version that your system uses by calling transaction SDCC and choosing Information -&gt; Version of program.<br /><br /><br />[2] Answer:<br />We must differentiate between the different release and Support Package levels for USMM and their transfer functions (SDCC/SDCCN). Different constellations are provided depending on these versions.<br />The measurement results can be transferred accordingly only if both transaction USMM and the transfer function have the required Support Package level.<br /><br />Transaction USMM is SDCC-enabled as of the following Support Package levels:<br /><br />4.0B SAPKH40B59<br />4.5B SAPKH45B37<br />4.6B SAPKB46B23<br />4.6C SAPKB46C11<br />4.6D SAPKB46D03<br />6.20 SAPKB62000<br />6.40 SAPKB64000<br />7.00 SAPKB70000<br /><br />Transaction USMM can potentially be SDCCN-enabled as of the following Support Package levels by implementing Note 909626.<br /><br />4.6C SAPKB46C11<br />4.6D SAPKB46D03<br />6.20 up to SAPKB62061<br />6.40 up to SAPKB64020<br />7.00 SAPKB70001 up to SAPKB70011<br /><br />Transaction USMM can potentially be SDCCN-enabled as of the following Support Package levels:<br /><br />4.6C SAPKB46C54<br />6.20 SAPKB62062<br />6.40 SAPKB64021<br />7.00 SAPKB70012<br /><br /><br />The following data is relevant for transferring measurement results:<br /><br />SDCC version lower than 2.1:  SDCC transfer is not possible<br /><br />SDCC Version 2.1 or higher:  SDCC transfer is possible<br /><br />ST-PI 2005_1_* Support Package level 0: SDCCN transfer is not possible<br /><br />ST-PI 2005_1_* Support Package level 1 or higher: SDCCN transfer is possible<br /><br />Note that SDCC and SDCCN can be installed on a system at the same time. If SDCCN is installed, transaction SDCC is blocked. However, you can still technically use SDCC to transfer data.<br /><br />The following possibilities exist:<br /><br />a) If USMM or the transfer function are not SDCC-enabled, you cannot perform an online transfer under any circumstances.<br /><br />b) SDCCN is not installed (but you use SDCC Version 2.1 or higher).<br /><br />If SDCCN is not installed, the system uses SDCC to perform the transfer even if USMM is capable of using SDCCN.<br /><br />c) SDCCN is installed: ST-PI 2005_1_*&#x00A0;&#x00A0;&#x00A0;&#x00A0;Support Package level 0<br /><br />USMM SDCCN-enabled <br /><br /> NO Transfer not possible<br /> <br /> YES Transfer using SDCC (1)<br /><br /><br />d) SDCCN is installed: ST-PI 2005_1_*&#x00A0;&#x00A0;&#x00A0;&#x00A0;Support Package level 1 or higher<br /><br />USMM SDCCN-enabled <br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NO  use Note 909626 to perform the SDCCN transfer (2) <br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; YES  Use SDCCN to perform the transfer <br /><br />(1) If SDCCN (ST-PI 2005_1_*) with Support Package 00 is active, the system uses SDCC to transfer the measurement results even if you have implemented Note 909626. In this case, you cannot use transaction SDCC to check whether the system sent the measurement results to SAP. Instead, follow the instructions from Note <B>1153034</B> to obtain a confirmation of receipt by e-mail.<br /><br />(2) If SDCCN (ST-PI 2005_1_*) Support Package 01 or higher is active but the SAP_BASIS components do not have the relevant Support Package level (according to answer 1), implement Note <B>909626</B>. After you implement this note, the system uses SDCCN to transfer the measurement results to SAP. However, the system does not update the transfer status in transaction USMM. Implement Note <B>1153034</B> to obtain confirmation that your measurement results were received by SAP.<br /><br /><br />[3] Answer: The system issues this error message if the RFC destination to SAP is not maintained. The RFC destination to SAP must be maintained to send the measurement results to SAP. The RFC destination is called \"SDCC_OSS\" and you must maintain it in the RFC destination section of transaction SDCCN. For additional information about the correct configuration for data transfer using SDCCN, see Note <B><B>763561</B></B>. If this does not help, create a message under the component SV-SMG-SDD.<br /><br /><br />[4] Answer: Implement Note <B>1150840</B> to unlock transaction USMM.<br /><br />[5] Answer: In the measurement program (transaction USMM), display the list of all transfers. The first entry is the most recent. The session number is located in the column \"SDCC Session Number\", or, as of Release 6.20, in the second column.<br /><br /><br />[6] Answer: First, check which transfer variant is in use in your system (see question 2). Question 5 describes how to find the session ID.<br /></p> <UL><LI>SDCC completely active:</LI></UL> <UL><UL><LI>Use transaction USMM to determine the session ID of the locked transfer (the lock may expire).</LI></UL></UL> <UL><UL><LI>Use transaction SDCC to determine the status of the session (you may have to restart the transfer).</LI></UL></UL> <UL><UL><LI>If the session is no longer visible, you must reset transaction USMM (see question 8).</LI></UL></UL> <UL><LI>SDCCN completely active:</LI></UL> <UL><UL><LI>The procedure is the same as for transaction SDCC. Use transaction SDCCN.</LI></UL></UL> <UL><LI>SDCCN activated using Note 909626:</LI></UL> <UL><UL><LI>In this case, only the status display for transaction USMM does not work. You can use transaction SDCCN to edit the session.</LI></UL></UL> <UL><LI>SDCC when the installation of SDCCN is incomplete:</LI></UL> <UL><UL><LI>The status display for transaction USMM does not work. Since transaction SDCCN is locked, you cannot display a status here either. Note <B>1153034</B> is useful here.</LI></UL></UL> <p><br />The system may issue this error message because the configuration of the RFC link to SAP is incomplete. Check whether the RFC connections SAPOSS or SAPNET_RFC are correctly set up with passwords. For more information about this problem, see Note <B>216952</B>. <B>216952</B>.<br /><br /><br />[7] Answer: Implement the corrections contained in Note <B>1139194</B>. These corrections check whether the SDCCN function that transaction USMM requires is completely installed. If SDCCN is not completely installed, the system uses the SDCC transfer procedure to send the measurement results. This is possible because only transaction SDCC is locked but the send function using the SDCC transfer procedure is still available.<br /><br />[8] Answer: Follow the recommendations in Note <B>314672</B>. <B>314672</B>.<br /><br />[9] Answer: This exception may occur if you have already implemented Note 909626. Implement the most current version of this note and then send the information again.<br /><br />[10] Answer: This exception may occur if invalid entries exist in the table TUSTAT. To correct this problem, see the recommendations for question 4.<br /><br />[11] Answer: Alternatively, you can download the measurement results in a text file and then send them by e-mail.&#x00A0;&#x00A0;To do so, proceed in accordance with Note 1226944.<br /><br />[12] Answer: Follow the recommendations in Note: <B>559378.</B></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SDD (Service Data Download)"}, {"Key": "Responsible                                                                                         ", "Value": "D048768"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D034754)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001142694/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001142694/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "909626", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: SDCC replaced with SDCCN data transfer", "RefUrl": "/notes/909626"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "412391", "RefComponent": "XX-SER-LAS", "RefTitle": "System Measurement: Message \"Data locked for transfer\"", "RefUrl": "/notes/412391"}, {"RefNumber": "1226944", "RefComponent": "XX-SER-LAS", "RefTitle": "LAW/USMM: Downloading measurement files for e-mail transfer", "RefUrl": "/notes/1226944"}, {"RefNumber": "1153034", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Transfer status is not updated", "RefUrl": "/notes/1153034"}, {"RefNumber": "1150840", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Unlocking the USMM", "RefUrl": "/notes/1150840"}, {"RefNumber": "1139194", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement:TA USMM causes termination", "RefUrl": "/notes/1139194"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2352316", "RefComponent": "SV-FRN-INS", "RefTitle": "Focused RUN Usage", "RefUrl": "/notes/2352316 "}, {"RefNumber": "909626", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: SDCC replaced with SDCCN data transfer", "RefUrl": "/notes/909626 "}, {"RefNumber": "1226944", "RefComponent": "XX-SER-LAS", "RefTitle": "LAW/USMM: Downloading measurement files for e-mail transfer", "RefUrl": "/notes/1226944 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "1150840", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Unlocking the USMM", "RefUrl": "/notes/1150840 "}, {"RefNumber": "412391", "RefComponent": "XX-SER-LAS", "RefTitle": "System Measurement: Message \"Data locked for transfer\"", "RefUrl": "/notes/412391 "}, {"RefNumber": "1139194", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement:TA USMM causes termination", "RefUrl": "/notes/1139194 "}, {"RefNumber": "1153034", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Transfer status is not updated", "RefUrl": "/notes/1153034 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}