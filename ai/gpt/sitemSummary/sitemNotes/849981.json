{"Request": {"Number": "849981", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 242, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015903842017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000849981?language=E&token=E6F6BFDEA601F924BC455AC7E44D2CC1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000849981", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000849981/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "849981"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.08.2006"}, "SAPComponentKey": {"_label": "Component", "value": "IS-A-LMN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Long Material Number"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Automotive", "value": "IS-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Long Material Number", "value": "IS-A-LMN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A-LMN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "849981 - DIMP: Upgrade to ERP 2005"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains important information for DIMP customers who are upgrading to ERP 2005 or higher respectively who are using the Switch Framework with the Business Function (BF) or Business Function Set (BFS) for DIMP.<br /><br /><B>Especially, this note will inform you about the risk of dataloss and how to avoid it. Without reading this note before the upgrade and carrying out the below described actions, there is the risk of dataloss.</B><br /><br />This note requires knowledge about Switch Framework (SFW) and SPAU.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Long Materialnumber, LAMA, LMN, Automotive, Aerospace &amp; Defense, A&amp;D, MPN, Manufacturer Partnumber, Materialversions, MARA, Upgrade, ERP 2005, Switch Framework, Afterswitch Method, DDIC, Screen, DIMP, SFW5<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In case A or case B (described below), please use at least Support Package SP06. If you are not able to include SP06 or higher, you have to install the note 956192 after the upgrade respectively after switching on the BFS DIMP.<br /><br />In the subcase A1 or subcase B1 (described below), please use at least Support Package SP06. If you are not able to include SP06 or higher, you have to install the note 966997 after the upgrade respectively after switching on the BFS / BF's.<br /><br /><B>CASE A:</B><br />You will upgrade your system to ERP2005 or higher and you have used the industry solutions&#x00A0;&#x00A0;DI, DIMP, ECC-DIMP, Aerospace &amp; Defense, (Machinery) Engineering &amp; Construction, Mill Products, Automotive or High Tech in the release before the upgrade.<br /><br />SUBCASE A1:<br />You have used Long Materialnumbers (LMN) in the release before the upgrade (as Automotive customer). That means the following customizing setting is true: Table MGV_TLMNR has one entry. In this case, the system will switch on the BF MGV_LAMA automatically while the upgrade.<br /><br />Alternatively, you have used Manufacturer Part Number (MPN) in the release before the upgrade (as Aerospace &amp; Defense customer). That means the following customizing setting is true: Table MPNCNV has one entry 'X' in the field CONEXT. In this case, the system will switch on the BF IS_AD_MPN automatically while the upgrade. Please regard that the BF IS_AD_MPN will be switched on if in at least one client the customizing is set accordingly. This could be any client, e.g. a testclient. Therefore please check this table in all clients before the upgrade in order to avoid that the below mentioned afterswitch methods are performed unintentional.<br /><br />After the upgrade in case A1, please implement note 889740.<br /><br />SUBCASE A2:<br />You have neither used LMN nor MPN in the release before the upgrade. Both (above described) customizing settings are not valid in the release before the upgrade.<br /><br />REMARK to A1 &amp; A2:<br />If you will upgrade from ECC-DIMP 500, it could be possible that the customizing for MPN is set unintentional (see note 854523). This has the consequence that during the upgrade, the BF IS_AD_MPN is switched on, all Afterswitchmethods are executed and DDIC and Screens are changed (Details see below), besides you are not using MPN. Please decide whether you really require MPN and set the MPN customizing accordingly and like described in note 854523.<br /><br /><B>CASE B:</B><br />You already have an existing release ERP2005 or higher.<br />You are going to switch on manually the BFS for DIMP.<br /><br />SUBCASE B1:<br />You will switch on manually the BF MGV_LAMA or BF IS_AD_MPN, because you want to use LMN or MPN. In theses cases, you have to switch on BF MGV_LAMA or BF IS_AD_MPN always together with BF DIMP_SDUD (for technical reasons).<br /><br />Before switching on the BF in case B1, please implement note 890513.<br /><br />SUBCASE B2:<br />You will switch on manually the BFS DIMP,<br />and neither using LMN nor MPN (ie. you will switch on the BF DIMP_SDUD, but BF MGV_LAMA and BF IS_AD_MPN stays switched off).<br /><br />Please be aware that there are some further restrictions if you are using LMN. You are not allowed to use LMN without a special agreement with SAP (see e.g. note 831966).<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>=======================================================================<br />Introduction:<br />=======================================================================<br />In ERP2005, the BFS DIMP contains 3 BF: DIMP_SDUD, MGV_LAMA and IS_AD_MPN.<br /><br />A customer who neither uses LMN nor MPN, switches on only the BF DIMP_SDUD.<br /><br />A customer who uses LMN, switches on the BF MGV_LAMA and BF DIMP_SDUD together.<br /><br />A customer who uses MPN, switches on the BF IS_AD_MPN and BF DIMP_SDUD together.<br /><br />If BFS DIMP is switched on, one of the 3 BF must also be switched on. It is not possible to switch on any of these 3 BF if you are not using BFS DIMP. <B>Please remark in addition, that a BF and BFS can never be switched off. </B>While the upgrade to ERP2005 or higher, the BF and BFS is set automatically according to the customizing on the previous release.<br /><br />After the BFS DIMP is switched on, afterswitch methods are executed. The BADI definition of the afterswitch method is SFW_SWITCH_CHANGED, the enhancement implementation is LAMA_CHANGES.<br /><br />There is one afterswitch method for the BF DIMP_SDUD: BADI Class CL_IM_LAMACHANGES_FOR_DIMP. It contains the change of several DDIC elements.<br /><br />There is another afterswitch method for the BF MGV_LAMA respectively BF IS_AD_MPN: BADI Class CL_IM_LAMA_CHANGES. It contains the change of several DDIC elements as well as the change of the approximately 3000 LMN Screens. Details see below.<br /><br />The Afterswitch Methods are executed in every customer system (Development Systems, Test Systems, Productive System) automatically by the Switch Framework if the corresponding BFS / BF is switched on. The execution of Afterswitch methods can be triggered by switching on the BFS DIMP by two possibilities:<br /><br />- The BFS DIMP is switched on in the Development System and transported through the system landscape. I.e., in all other systems it is automatically switched on.<br /><br />- Alternatively, the BFS is switched on in every customer system manually.<br /><br /><B>Please remark, that the changes of the afterswitch methods can not be reversed.</B><br /><br />=======================================================================<br />Afterswitch method CL_IM_LAMACHANGES_FOR_DIMP (BF DIMP_SDUD)<br />=======================================================================<br /><br />This method runs in case A2 and B2.<br />This method contains the report MGV_DDIC_CHANGE_REPORT_DIMP that changes the following DDIC elements:<br /><br />(1) Domains: KKB_MATNR, KKEK_HERK2<br />(2) Data elements: MATSCH, SCHABLONE, BISSL20_L, VONSL20_L, KKB_HERKU,<br />KKEK_RESS, LEDSPD_EXIDV_MATNR, AZKTO, CO_OBJID, MGV_RFC_MATNR<br />(3) Structure field: HUMTREE-TEXT<br /><br />In releases below ERP2005, in several DIMP AddOns, these DDIC elements were modified compared to the SAP solution without Industry AddOn.<br />Within release ERP2005 or higher releases, these modifications are implemented by above mentioned report. These modifications are mostly a change of the technical length (No. of characters). Example: Data element MATSCH changed from CHAR18 to CHAR40. This length change is mostly performed by an exchange of the domain in the data elements (2). In the domains (1) and the structure (3), the change of the length is done in a direct way. Also, other small changes could be included (e.g. the Flag \"Lower Case\" in the Domain).<br /><br />=======================================================================<br />Remark: Changes done outside DIMP<br />=======================================================================<br />In releases below ERP2005, in several DIMP AddOns, the following DDIC elements were modified compared to the SAP solution without Industry AddOn. Within release ERP2005 or higher releases, these changes are taken over (\"retrofitted\") to the SAP solution without any Industry AddOn:<br /><br />(4)&#x00A0;&#x00A0;Data elements: KKP_PVALUE, KKP_VALUE, CMF_OBJECT<br /><br />=======================================================================<br />Afterswitch method CL_IM_LAMA_CHANGES (BF MGV_LAMA / IS_AD_MPN)<br />=======================================================================<br />This method runs in case A1 and B1. It contains 3 reports:<br /><br />(I) report MGV_DDIC_CHANGE_REPORT_DIMP<br />(II) report MGV_DDIC_CHANGE_REPORT_LAMA<br />(III) report MGV_LAMASCREENS2<br /><br />Report (I) is already described above.<br />Report (II) changes (only) the output lenght of the following domains: MATNR, CC_MATNR, CCMATNR, STOFF. Thereby, the output lenght is changed from 18 to 40 characters. Because the domain MATNR has many dependent objects, the activation could be very time consuming. Therefore, if the report (II) should be executed in the case of upgrade, the change and activation of these domains is done earlier in the upgrade process along with the activation of the complete DDIC (instead while the execution of the afterswitch methods).<br />The report MGV_LAMASCREENS2 (III) unpacks the data for approximately 3000 screens that include the field MATNR. If this report is executed, the screens are overwritten with screens that contain the long material number field. Afterwards the screens are activated. Then, like usual, the customer has the possibility to perform a SPAU (adjustment of the customer own screen changes).<br /><br />How to save time regarding SPAU of Screens?<br />If you plan to upgrade a DIMP system without using LMN / MPN and if you later decide to switch on LMN / MPN, you probably have to do a SPAU for several thousands screens twice: One time after upgrade and one time after switching on. Hence, we recommend to plan the usage of LMN / MPN in advance, switch it on directly after the upgrade and perform afterwards a common SPAU only one time. A similar problem occurs (without upgrade) if you first switch on BF DIMP_SDUD and later LMN / MPN: Also this could lead to a double SPAU effort. Therefore, plan to switch on both in one step and do a common SPAU to avoid the double effort.<br />In addition please remark that with support pack 02 (SP02), there are approximately 2000 LAMA screens delivered again. Hence, we recommend to implement at least SP02 (if already available) and do a common SPAU after it. Otherwise, you probably have to do a SPAU for a huge amount of screens twice.<br />=======================================================================<br /><STRONG>Risk of dataloss</STRONG><br />=======================================================================<br />If report (I) runs during upgrade (case A1 or A2), there is the risk of dataloss. In order to avoid data loss, please carry out the following measures:<br /><br />- The data elements MATSCH and SCHABLONE are used in one SAP customizing table: TMCNV. This table normally contains only one entry. MATSCH is used in field MASKE (\"Material no. template\") and SCHABLONE is used in field TZ (\"Editing characters\"). Please write down manually the content of these two fields before the upgrade. Please adjust the content of these two fields after the upgrade if it was cut off.<br /><br />- Please check whether the DDIC objects (1), (2) or (3) are used in any database table or database table append / include that is created by the customer. If yes, please ensure that you store the content of these tables before the upgrade and restore it after the upgrade. Therefore, please contact your development consultant.<br /><br />- Please also regard note 891828 and carry out the described actions to avoid dataloss.<br /><br />Background:&#x00A0;&#x00A0;Regard the DDIC objects (1), (2) and (3) that are changed by report (I): They are in the long state before the upgrade. During the upgrade to an ERP2005 DIMP system they will be activated in the short state. After the run of the afterswitch methods, they are again in the long state. If these DDIC objects are used in database tables, the content of the table fields will be cut off.<br /><br />=======================================================================<br />Further remarks<br />=======================================================================<br />Please also implement note 818758 if you are using LMN or MPN.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-MP (Mill Products)"}, {"Key": "Other Components", "Value": "IS-A (Automotive)"}, {"Key": "Other Components", "Value": "IS-ADEC-MPN (Manufacturer Part Number)"}, {"Key": "Other Components", "Value": "IS-ADEC (Ind.-Spec.Comp. Aerospace&Defense / Engineering&Construction)"}, {"Key": "Other Components", "Value": "IS-HT (Industry-specific Component High Tech)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031286)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D046328)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000849981/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849981/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "966997", "RefComponent": "IS-A-LMN", "RefTitle": "Release in the LAMA2ERP_FLAG table is filled incorrectly", "RefUrl": "/notes/966997"}, {"RefNumber": "956192", "RefComponent": "IS-A-LMN", "RefTitle": "Changes to Dictionary structures for ECC-DIMP in ERP2005", "RefUrl": "/notes/956192"}, {"RefNumber": "900723", "RefComponent": "IS-A-LMN", "RefTitle": "Logging function after DIMP/LAMA activation", "RefUrl": "/notes/900723"}, {"RefNumber": "891828", "RefComponent": "IS-ADEC-SSP", "RefTitle": "Customer stock in stock overview (MMBE)", "RefUrl": "/notes/891828"}, {"RefNumber": "890513", "RefComponent": "IS-A-LMN", "RefTitle": "Corrected report for activating DIMP with LAMA or MPN", "RefUrl": "/notes/890513"}, {"RefNumber": "889740", "RefComponent": "IS-A-LMN", "RefTitle": "Correction program for the LAMA/MPN screen after upgrade", "RefUrl": "/notes/889740"}, {"RefNumber": "874471", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additions for installing or activating ECC-DIMP on ECC 6.0", "RefUrl": "/notes/874471"}, {"RefNumber": "872197", "RefComponent": "IS-A", "RefTitle": "Additional info about upgrade to SAP ECC 6.0 w/ ECC-DIMP 6.0", "RefUrl": "/notes/872197"}, {"RefNumber": "854523", "RefComponent": "IS-ADEC-MPN-MD", "RefTitle": "MPN conversion exit is delivered as 'active' by default", "RefUrl": "/notes/854523"}, {"RefNumber": "831966", "RefComponent": "IS-A", "RefTitle": "Restrictions when Implementing the Long Material Number LMN", "RefUrl": "/notes/831966"}, {"RefNumber": "818758", "RefComponent": "IS-A-LMN", "RefTitle": "IS2ERP: Enhancement in Customer Include LXCKAF00 by LAMA", "RefUrl": "/notes/818758"}, {"RefNumber": "735529", "RefComponent": "IS-A-LMN", "RefTitle": "FAQ: DIMP/DI long material number and MPN", "RefUrl": "/notes/735529"}, {"RefNumber": "1597790", "RefComponent": "IS-A-LMN", "RefTitle": "Activation of Long Material Number in DIMP", "RefUrl": "/notes/1597790"}, {"RefNumber": "1132702", "RefComponent": "IS-A-LMN", "RefTitle": "Upgrade ECC-DIMP600 is not completed", "RefUrl": "/notes/1132702"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "831966", "RefComponent": "IS-A", "RefTitle": "Restrictions when Implementing the Long Material Number LMN", "RefUrl": "/notes/831966 "}, {"RefNumber": "735529", "RefComponent": "IS-A-LMN", "RefTitle": "FAQ: DIMP/DI long material number and MPN", "RefUrl": "/notes/735529 "}, {"RefNumber": "1597790", "RefComponent": "IS-A-LMN", "RefTitle": "Activation of Long Material Number in DIMP", "RefUrl": "/notes/1597790 "}, {"RefNumber": "872197", "RefComponent": "IS-A", "RefTitle": "Additional info about upgrade to SAP ECC 6.0 w/ ECC-DIMP 6.0", "RefUrl": "/notes/872197 "}, {"RefNumber": "1132702", "RefComponent": "IS-A-LMN", "RefTitle": "Upgrade ECC-DIMP600 is not completed", "RefUrl": "/notes/1132702 "}, {"RefNumber": "966997", "RefComponent": "IS-A-LMN", "RefTitle": "Release in the LAMA2ERP_FLAG table is filled incorrectly", "RefUrl": "/notes/966997 "}, {"RefNumber": "956192", "RefComponent": "IS-A-LMN", "RefTitle": "Changes to Dictionary structures for ECC-DIMP in ERP2005", "RefUrl": "/notes/956192 "}, {"RefNumber": "891828", "RefComponent": "IS-ADEC-SSP", "RefTitle": "Customer stock in stock overview (MMBE)", "RefUrl": "/notes/891828 "}, {"RefNumber": "900723", "RefComponent": "IS-A-LMN", "RefTitle": "Logging function after DIMP/LAMA activation", "RefUrl": "/notes/900723 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}