{"Request": {"Number": "1808526", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 342, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017585922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001808526?language=E&token=E320C6A9254F4A00815B3BD111536C01"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001808526", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001808526/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1808526"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.11.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-IAM-SL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use BC-IAM-SSO*"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Identity and Access Management", "value": "BC-IAM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-IAM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use BC-IAM-SSO*", "value": "BC-IAM-SL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-IAM-SL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1808526 - Release Note SAP NetWeaver Single Sign-On 2.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Release Notes for SAP NetWeaver Single Sign-On 2.0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Secure Login Client, Secure Login Server, Secure Login Library, Password Manager</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are using the components Secure Login Client, Secure Login Server and Secure Login Library or Password Manager of SAP NetWeaver Single Sign-On 2.0.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><span style=\"text-decoration: underline;\"><strong><strong>SECURE LOGIN</strong></strong></span><br />Secure Login is a client/server software system integrated with SAP software to facilitate single sign-on, alternative user authentication, and enhanced security for distributed SAP environments. The Secure Login solution includes three components:</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Secure Login Server</span></strong></p>\r\n<p>Central service running on SAP NetWeaver JAVA which provides X.509v3 certificates to users and application servers.</p>\r\n<ul>\r\n<li>Out-of-the-box PKI<br />Server Certificates (SNC/SSL) and Short Term User Certificates.<br />Integration to existing PKI.</li>\r\n</ul>\r\n<ul>\r\n<li>Several Authentication Server Connectors<br />SAP AS JAVA UME, SAP AS ABAP (RFC), LDAP or Microsoft Active Directory, and RADIUS.</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Web Client<br />Provide Certificate to SAP GUI and Internet Browser.<br />Secure Communication between SAP GUI and SAP Application Server.<br />Support of SAP GUI for Java.<br />No distribution of client software required (zero-footprint).<br />Apple Key Chain support on Mac OS X.<br />Enhanced Browser Support (Mozilla Firefox 17 ESR, Microsoft Internet Explorer 10).<br />Enhanced Platform Support (Windows 8, Windows Server 2012, Mac OS X 10.7/10.8)</li>\r\n</ul>\r\n<ul>\r\n<li>Web Adapter (Web Client interface to Secure Login Client)</li>\r\n</ul>\r\n<ul>\r\n<li>Reuse NetWeaver Portal Authentication<br />Seamless and silent integration of Web Client or Web Adapter into the Portal.</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Administration Console in WebDynpro<br />Completely reworked UI based on NetWeaver standards.<br />Additional language support (EN, DE, JP, FR, PT, ZH, RU).</li>\r\n</ul>\r\n<ul>\r\n<li>Deeper Integration into NetWeaver Stack<br />Integrate into NetWeaver key and certificate store.<br />Integrate into NetWeaver logs and traces.<br />Integrate into NetWeaver Configuration.<br />Benefit from standard NetWeaver tools and features like backup and restore, high availability and clustering, monitoring.</li>\r\n</ul>\r\n<ul>\r\n<li>Improved X.509 Attribute Configuration<br />Add multiple Subject Alternative Names (RFC822 Name / Principal Name).</li>\r\n</ul>\r\n<ul>\r\n<li>Improved User ID Mapping Configuration<br />Flexible user name retrieval and composition.</li>\r\n</ul>\r\n<ul>\r\n<li>X.509 User Certificate Propagation to UME<br />Store issued user certificates in NetWeaver UME entry of respective user.</li>\r\n</ul>\r\n<ul>\r\n<li>X.509 User Certificate Archiving Option<br />Store user certification requests and issued user certificates in file system.</li>\r\n</ul>\r\n<ul>\r\n<li>Enhanced Group Profile Configuration for Secure Login Client<br />Define arbitrary groups of client authentication profiles.</li>\r\n</ul>\r\n<ul>\r\n<li>Multiple Web Client Profiles<br />Define multiple independent Web Client or Web Adapter profiles with own authentication and user certificate configuration.</li>\r\n</ul>\r\n<ul>\r\n<li>PKI Migration Wizard<br />Import certificates and keys from Secure Login Server 1.0.</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">Secure Login Library</span></strong></p>\r\n<p>Cryptography and Security Library for SAP NetWeaver ABAP.</p>\r\n<ul>\r\n<li>SNC Library for SAP ABAP Application Server<br />Full SNC Compatibility with SAPCRYPTOLIB.</li>\r\n</ul>\r\n<ul>\r\n<li>X.509 and Kerberos Authentication in Parallel<br />Allow to configure server identities from PKI and Windows Domain in mixed mode to provide two user authentication protocols in parallel.</li>\r\n</ul>\r\n<ul>\r\n<li>SPNEGO for SAP AS ABAP<br />Windows Kerberos Authentication using Web Interface in SAP AS ABAP.</li>\r\n</ul>\r\n<ul>\r\n<li>FIPS 140-2 Certification for Crypto Kernel<br />Certification process is ongoing (final result expected in Q3/2013). Use URL http://csrc.nist.gov/ and search for SAP AG.</li>\r\n</ul>\r\n<ul>\r\n<li>INTEL AES Native Interface Support<br />Crypto Kernel makes use of INTEL AES-NI on Microsoft Windows and Linux platforms.</li>\r\n</ul>\r\n<ul>\r\n<li>Command Line Tools Reworked<br />With an extended SAPGENPSE utility, usability improvements and compatibility with SAPCRYPTOLIB are given.</li>\r\n</ul>\r\n<ul>\r\n<li>ABAP STRUST Compatibility<br />Better support of STRUST PSE files and credentials.</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">Secure Login Client</span></strong></p>\r\n<p>Client application which uses existing or provides new security tokens (Kerberos and X.509) for a variety of applications.</p>\r\n<ul>\r\n<li>SNC Client Library for SAP GUI Application<br />Strong Authentication, Communication Encryption and Single Sign-On</li>\r\n</ul>\r\n<ul>\r\n<li>Support for Digital Signatures in SAP Applications (SSF Interface)</li>\r\n</ul>\r\n<ul>\r\n<li>Security Token Management<br />Support for Smartcard, OTP Token, Kerberos, Microsoft Certificate Store, PKCS#11, Short Term Certificates provided by Secure Login Server and Integration to existing PKI.</li>\r\n</ul>\r\n<ul>\r\n<li>Enhanced Integration with SAP NetWeaver Business Client</li>\r\n</ul>\r\n<ul>\r\n<li>Installer Based on SAP Setup<br />Using SAP standard installation engine now.<br />Allow to integrate into SAP GUI installation packages.</li>\r\n</ul>\r\n<ul>\r\n<li>Enhanced Platform Support<br />Windows 8, Windows Server 2012 (WTS, CITRIX).</li>\r\n</ul>\r\n<ul>\r\n<li>Additional languages (EN, DE, JP, FR, PT, RU, ZH, ES)</li>\r\n</ul>\r\n<ul>\r\n<li>Accessability Support<br />High contrast, screen reader, keyboard navigation, tool tips.</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">SINGLE SIGN-ON EXTENSIONS LIBRARY</span></strong></p>\r\n<p><strong>Extension for Kerberos Constrained Delegation</strong></p>\r\n<ul>\r\n<li>This library provides support for Kerberos constrained delegation, which consists of a Service-for-User-to-Self (S4U2Self) extension and a Service-for-User-to-Proxy (S4U2Proxy) extension. This functionality can be used to obtain Kerberos service tickets on behalf of the currently authenticated user. For more information, see Extension for Kerberos Constrained Delegation Implementation Guide at <a target=\"_blank\" href=\"http://help.sap.com/nwsso\">http://help.sap.com/nwsso</a>.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong><strong><span style=\"text-decoration: underline;\">PASSWORD MANAGER</span></strong></strong><br /><strong>New Product Name</strong></p>\r\n<ul>\r\n<li>\"Enterprise Single Sign-On\" is now \"<strong>Password Manager\"</strong></li>\r\n</ul>\r\n<p><strong>Feature Enhancements</strong></p>\r\n<ul>\r\n<li>New UI design</li>\r\n<li>New categories of data that can be securely stored (Notes, Credit Card details, and Identities) including live search across all categories</li>\r\n<li>New mechanism for web site registration</li>\r\n<li>Basic authentication support, and support for more unusual login triggers</li>\r\n<li>New encryption mechanism and XML-based format for the password store</li>\r\n<li>Built-in Password generator</li>\r\n<li>SAP setup installer (attended/unattended installation)</li>\r\n<li>A New Exit Button for Terminating the Password Manager<br />You can exit the password manager any time by right-clicking the icon on your taskbar and choosing the Exit button.&#160; To start the manager again, follow the instructions in the Password Manager documentation under the section <em>Starting Password Manager</em><strong>.</strong></li>\r\n<li>No Restrictions for the Length of an Answer to a Security Question<br />Password Manager delivers a set of security questions. During installation, the installer prompts you to choose one question and enter an answer. The Password Manager uses the security question and answer to reset you master password in case you forget it. There are no restrictions for the length of the answer.</li>\r\n</ul>\r\n<p><strong>Enhanced Platform Support</strong></p>\r\n<ul>\r\n<li>Windows 8 (desktop/classic mode only)</li>\r\n</ul>\r\n<ul>\r\n<li>Enhanced Browser Support<br />Mozilla Firefox 17 ESR<br />Microsoft Internet Explorer 10 (Windows 8 only)</li>\r\n</ul>\r\n<ul>\r\n<li>Additional languages (EN, DE, JP, FR, PT, RU, ZH, ES)</li>\r\n</ul>\r\n<p><br /><br /></p>\r\n<p><strong>IMPORTANT GENERAL REMARKS</strong></p>\r\n<p>If you encounter any issues which are not documented, or you want to report an error, use http://service.sap.com and open a customer message on component <strong>BC-IAM-SL</strong> (Secure Login) or <strong>BC-IAM-PWM</strong> (Password Manager).</p>\r\n<p><strong>ONLINE MANUALS</strong></p>\r\n<p>See http://help.sap.com/nwsso20 for all available guides.</p>\r\n<p><strong>ONLINE COMMUNITY</strong></p>\r\n<p>The SAP Community Network (SCN) is an online community for developers, analysts, consultants, integrators, and administrators that provides a collection of technical content on SAP NetWeaver Single Sign-On topics. http://scn.sap.com/community/netweaver-sso.</p>\r\n<p><strong>KNOWN ISSUES</strong><br /><strong><span style=\"text-decoration: underline;\">Relevant SAP Notes</span></strong></p>\r\n<ul>\r\n<li>Note 1834979 - Error \"ERROR! could not access PKRoot\" in sapgenpse<br />https://service.sap.com/sap/support/notes/1834979</li>\r\n</ul>\r\n<ul>\r\n<li>Note 1841166 - Remotely initialize SLAC w/o HTTPs if using ReverseProxy<br />https://service.sap.com/sap/support/notes/1841166</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">Other Issues</span></strong></p>\r\n<ul>\r\n<li>Several components: GUI links to help.sap.com do not open /nwsso20 yet</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Web Client has no own local log file, but only those in the browser&#180;s Java Console</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Web Client fails with Client Error 0x06 if local PKI trust is missing; expected result for missing PKI trust is Client Error 0x04</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Web Client does not visualize longer server response times.</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Web Client does not display a hint in the browser if JRE is unavailable</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Client shows bogus profile tool tips</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Client reopens Certificate Viewer if it was closed with return key</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Client setup always installs a DLL for Secure Login Server support</li>\r\n</ul>\r\n<ul>\r\n<li>Secure Login Library creates a PSE file even after a bad command line and error code output</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC (Security - Read KBA 2985997 for subcomponents)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D055975)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D055975)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001808526/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001808526/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1912175", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "SAP Single Sign-On 2.0: Central Note", "RefUrl": "/notes/1912175"}, {"RefNumber": "1798979", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/1798979"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1798979", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/1798979 "}, {"RefNumber": "1912175", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "SAP Single Sign-On 2.0: Central Note", "RefUrl": "/notes/1912175 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SECURE_LOGIN_LIBRARY", "From": "2.0", "To": "2.0", "Subsequent": ""}, {"SoftwareComponent": "SECURE_LOGIN_CLIENT_32BIT", "From": "2.0", "To": "2.0", "Subsequent": ""}, {"SoftwareComponent": "SECURE_LOGIN_CLIENT_64BIT", "From": "2.0", "To": "2.0", "Subsequent": ""}, {"SoftwareComponent": "SECURE_LOGIN_SERVER", "From": "2.0", "To": "2.0", "Subsequent": ""}, {"SoftwareComponent": "ENTERPRISE_SSO", "From": "2.0", "To": "2.0", "Subsequent": ""}, {"SoftwareComponent": "SECURE_LOGIN_WEB_CLIENT", "From": "2.0", "To": "2.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}