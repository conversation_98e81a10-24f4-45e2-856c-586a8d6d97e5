{"Request": {"Number": "945787", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1127, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005558352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000945787?language=E&token=EEE9BEA245C17A27E6E8D998B2F01C07"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000945787", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000945787/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "945787"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.09.2006"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "945787 - HBRCAGED: Record type C and X - Layout changes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>1) Layout of record type X (and C) changed on January 2006 (law number<br />4923/65).<br /><br />2) TEMSE viewer \"Archive structure\" display option is not displaying the actual structure that was used to create the TEMSE file.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>HBRCAGED; CAGED; PCCG1BR0; PCCG2BR0; HBRUTMS5;<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>1) New info must be available (apprenctice) but also the already<br />existing should be more easy to interpret by the user on the TEMSE<br />viewer.<br />Fields \"N&#x00DA;MERO DA CARTEIRA DE TRABALHO\" and \"S&#x00C9;RIE DA CARTEIRA DE<br />TRABALHO\" changed size.<br /><br />2) Temse viewer doesn't consider the actual version of the TEMSE file, it assumes always 01.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>1) FILL7 was substituted by TYUPD; RMONT; RYEAR. FILL7 was already being filled with the correct information, this change will only allow on the TEMSE viewer the separation of the information.<br /><br />FIL14 was substituted by APPRE; FIL13. FIL13 is empty is empty but APPRE is filled with \"1\" if worker is an apprentice \"2\" if not.<br /><br />CTNUM and CTSER changed it's size, one more position on both fields.<br /><br />CTSTT (UF emissora da CTPS) mudou de posi&#x00E7;&#x00E3;o.<br /><br />(All these changes were made on layout C and X. They are equal)<br /><br />2) Version of the TEMSE file is retrieved from the TEMSE file itself.<br /><br /><STRONG>Important:</STRONG><br />It will be allowed to create and adjustment file (layout X) using<br />Temse files generated with the previous version.<br />Since the layouts are different some limitations apply:</p> <UL><UL><LI>the new field apprenctice can not be used when comparing with the old temse file.</LI></UL></UL> <UL><UL><LI>another limitation has to do with the record of layout X of update mode exclusion (Atualiza&#x00E7;&#x00E3;o: 1 - Exclus&#x00E3;o de registro). These records are created by copy of the old temse record, so all the new information that was not available on the old temse format (C) will not be on the adjustment file.</LI></UL></UL> <p><br />The correction described in this note will be included in an HR Support Package. The support package includes changes in:<br /></p> <UL><UL><LI>Report/Include: HBRUTMS5 (only from release 4.6C)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PCCG1BR0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PCCG2BR0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PUTMDBR0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PUTMMBR2<br /></LI></UL></UL> <UL><UL><LI>Domains:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_APPRE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_TYUPD<br /></LI></UL></UL> <UL><UL><LI>Data Elements:&#x00A0;&#x00A0;PBR_APPRE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_FIL13<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_TYUPD<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_CTNMR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_CTSRE<br /></LI></UL></UL> <UL><UL><LI>Structure:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HBRCAGX_03 (outdated)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HBRCAGC_04<br /></LI></UL></UL> <UL><UL><LI>Messages:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HRPAYBR99/217<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HRPAYBR99/218</LI></UL></UL> <p><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;****<br /><br />After the first release of a small correction was made regarding the comparation of files (when a file adjustment 'format X' is being created).<br />Also some corrections regarding note 972309 (from 4.6C upwards) were made on this note due to object locking. (Plese check note 972309).<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;****<br /><br />Special delivery files :<br />Rel. 4.5B -&gt; L4DK126376.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L4DK127428.CAR<br />Rel. 4.6B -&gt; L9BK135063.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L9BK136582.CAR<br />Rel. 4.6C -&gt; L9CK207350.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L9CK213336.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L9CK212314.CAR<br />Rel. 4.70 -&gt; L6BK114297.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L6BK121761.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L6BK120573.CAR<br />Rel. 5.00 -&gt; L6DK045398.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L6DK052724.CAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; L6DK051422.CAR<br /><br />Please note that when installing via special delivery files the corresponding .CAR file for note 972309 must be also downloaded/installed (assuming that note 972309 is also not installed).<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D043029)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D043029)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945787/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L9BK136582.CAR", "FileSize": "38", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=8F7360ED0419644491211C6347158988"}, {"FileName": "L9CK212314.CAR", "FileSize": "39", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=0060E230C93C234585FC4855310D4A20"}, {"FileName": "L6BK120573.CAR", "FileSize": "38", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=305943E3A061984F99E82C42250283D3"}, {"FileName": "L6BK121761.CAR", "FileSize": "4", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=324225C3D55E8347A712A9839B0E18CE"}, {"FileName": "L4DK127428.CAR", "FileSize": "31", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=461A88545ECA7D478922A1FD534926C7"}, {"FileName": "L9CK207350.CAR", "FileSize": "37", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=D61704588477C045A3A0858FFCFE6670"}, {"FileName": "L9CK213336.CAR", "FileSize": "3", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=50C30123CEA9C34B90B27A1E2E006EEA"}, {"FileName": "L6DK052724.CAR", "FileSize": "4", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=ABC2D4917B95714F90BC93469236FBF7"}, {"FileName": "L9BK135063.CAR", "FileSize": "37", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=3C966C8EB789E04DAC3C5CD33D20B7D3"}, {"FileName": "L6DK051422.CAR", "FileSize": "39", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=00FDA0DDC4A1044586E175DADDFD5BE1"}, {"FileName": "L6DK045398.CAR", "FileSize": "38", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=E2A922B9A5F0264DBB723B07200C44FB"}, {"FileName": "L6BK114297.CAR", "FileSize": "37", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=E35667DEF602924AAB0CD751B166D37F"}, {"FileName": "L4DK126376.CAR", "FileSize": "29", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000142072006&iv_version=0008&iv_guid=DF8A76738EC8C74893C23EC290EDE194"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "990258", "RefComponent": "PY-BR", "RefTitle": "HBRCAGED: Correction of adjustment-entries (Registro X)", "RefUrl": "/notes/990258"}, {"RefNumber": "972309", "RefComponent": "PY-BR", "RefTitle": "CAGED: CNPJ-number of headquarter differs from 0001 (rec. A)", "RefUrl": "/notes/972309"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "990258", "RefComponent": "PY-BR", "RefTitle": "HBRCAGED: Correction of adjustment-entries (Registro X)", "RefUrl": "/notes/990258 "}, {"RefNumber": "972309", "RefComponent": "PY-BR", "RefTitle": "CAGED: CNPJ-number of headquarter differs from 0001 (rec. A)", "RefUrl": "/notes/972309 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BD8", "URL": "/supportpackage/SAPKE45BD8"}, {"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BE2", "URL": "/supportpackage/SAPKE45BE2"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BC0", "URL": "/supportpackage/SAPKE46BC0"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BC4", "URL": "/supportpackage/SAPKE46BC4"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CB5", "URL": "/supportpackage/SAPKE46CB5"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CB1", "URL": "/supportpackage/SAPKE46CB1"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47060", "URL": "/supportpackage/SAPKE47060"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47056", "URL": "/supportpackage/SAPKE47056"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50026", "URL": "/supportpackage/SAPKE50026"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50022", "URL": "/supportpackage/SAPKE50022"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60005", "URL": "/supportpackage/SAPKE60005"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60009", "URL": "/supportpackage/SAPKE60009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 17, "URL": "/corrins/0000945787/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 17, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 56, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "409635 ", "URL": "/notes/409635 ", "Title": "HBRCAGED Entry/transfer date wrongly positioned", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "416935 ", "URL": "/notes/416935 ", "Title": "HBRCAGED: Employee is rejected if he does not have a cluster", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "417363 ", "URL": "/notes/417363 ", "Title": "HBRCAGED: Rem. Sal. is calculated wong.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "417935 ", "URL": "/notes/417935 ", "Title": "HBRCAGED: Code for school degree is wrong", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "419567 ", "URL": "/notes/419567 ", "Title": "HBRCAGED: Salaries are summed up within a month w. 2 changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "421770 ", "URL": "/notes/421770 ", "Title": "HBRCAGED: Log extensions", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "453254 ", "URL": "/notes/453254 ", "Title": "HBRCAGED: Register B is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "455016 ", "URL": "/notes/455016 ", "Title": "HBRCAGED: Number of active employees in register type B", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "457434 ", "URL": "/notes/457434 ", "Title": "HBRCAGED: School degree is always zero", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "485514 ", "URL": "/notes/485514 ", "Title": "HBRCAGED: <PERSON> missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "487085 ", "URL": "/notes/487085 ", "Title": "HBRCAGED: Hiredate, moveday inconsistency", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "514416 ", "URL": "/notes/514416 ", "Title": "HBRCAGED: reentrance to pay complementary termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "538821 ", "URL": "/notes/538821 ", "Title": "Record 30 in file CAGED does not show remuneration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "547114 ", "URL": "/notes/547114 ", "Title": "HBRCAGED: transference between companies", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "550100 ", "URL": "/notes/550100 ", "Title": "HBRCAGED: Sal. zeroed if employee is hired within month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "576495 ", "URL": "/notes/576495 ", "Title": "CAGED file does not show exit record", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "617388 ", "URL": "/notes/617388 ", "Title": "CAGED - wrong hiring date and missing employee", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "629664 ", "URL": "/notes/629664 ", "Title": "HBRCAGED: CBO not set in case of future P0001 entries", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "638957 ", "URL": "/notes/638957 ", "Title": "HBRCAGED doesn't fill in the CBO field", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "640494 ", "URL": "/notes/640494 ", "Title": "HBRCAGED: CBO missing for fired employees", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "658334 ", "URL": "/notes/658334 ", "Title": "HBRCAGED: CBO is not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "667174 ", "URL": "/notes/667174 ", "Title": "HBRCAGED: CBO blank when employee is transfered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "814288 ", "URL": "/notes/814288 ", "Title": "HBRCAGED record type X implementation", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "880223 ", "URL": "/notes/880223 ", "Title": "HBRCAGED establishments counter in record A", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "897636 ", "URL": "/notes/897636 ", "Title": "HBRCAGED adjust file is rejected by validator", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "945787 ", "URL": "/notes/945787 ", "Title": "HBRCAGED: Record type C and X - Layout changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "381758 ", "URL": "/notes/381758 ", "Title": "HBRUTMS no display for 2001 DIRF reports", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "197389 ", "URL": "/notes/197389 ", "Title": "HBRDIRF0 - acertos gerais", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "197875 ", "URL": "/notes/197875 ", "Title": "HBRCAGED - Acerto do horário de trabalho (IT0007)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "199355 ", "URL": "/notes/199355 ", "Title": "HBRCAGED - Acerto na leitura dos dados de mov. CAGED.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "204425 ", "URL": "/notes/204425 ", "Title": "HBRCAGED - Acerto do CBO para os desligados.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "211535 ", "URL": "/notes/211535 ", "Title": "SEFIP - Alter. para nova versao", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "302220 ", "URL": "/notes/302220 ", "Title": "CAGED - acerto de dados de endereço das filiais", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "302941 ", "URL": "/notes/302941 ", "Title": "HBRCAGED - Atualização para a versão 1.16", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "309617 ", "URL": "/notes/309617 ", "Title": "HBRCAGED - acertos campo atividade econômica", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "318319 ", "URL": "/notes/318319 ", "Title": "HBRCAGED - acerto para func. com demissão ult. dia mês", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "325245 ", "URL": "/notes/325245 ", "Title": "HBRCAGED - Acertos gerais p/ melhora de performance", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "381485 ", "URL": "/notes/381485 ", "Title": "HBRDIRF IDTAX preset wrong, third struct unnecessary", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "398947 ", "URL": "/notes/398947 ", "Title": "HBRCAGED data entrada, data emissao, PGM Valid. error", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "403026 ", "URL": "/notes/403026 ", "Title": "HBRCAGED data entrada, data emissao, no data...", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "407532 ", "URL": "/notes/407532 ", "Title": "HBRCAGED No. of employees wrong", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "600", "Number": "937340 ", "URL": "/notes/937340 ", "Title": "HBRCAGED record type X requires a record type B", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "600", "Number": "945610 ", "URL": "/notes/945610 ", "Title": "HBRCAGED doesn't fill the REM.SEMANAL field", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "MERCURY", "Number": "397917 ", "URL": "/notes/397917 ", "Title": "HBRCAGED Race numbers are different", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "409635 ", "URL": "/notes/409635 ", "Title": "HBRCAGED Entry/transfer date wrongly positioned", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "416935 ", "URL": "/notes/416935 ", "Title": "HBRCAGED: Employee is rejected if he does not have a cluster", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "417363 ", "URL": "/notes/417363 ", "Title": "HBRCAGED: Rem. Sal. is calculated wong.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "417935 ", "URL": "/notes/417935 ", "Title": "HBRCAGED: Code for school degree is wrong", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "419567 ", "URL": "/notes/419567 ", "Title": "HBRCAGED: Salaries are summed up within a month w. 2 changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "421770 ", "URL": "/notes/421770 ", "Title": "HBRCAGED: Log extensions", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "453254 ", "URL": "/notes/453254 ", "Title": "HBRCAGED: Register B is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "455016 ", "URL": "/notes/455016 ", "Title": "HBRCAGED: Number of active employees in register type B", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "457434 ", "URL": "/notes/457434 ", "Title": "HBRCAGED: School degree is always zero", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "485514 ", "URL": "/notes/485514 ", "Title": "HBRCAGED: <PERSON> missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "487085 ", "URL": "/notes/487085 ", "Title": "HBRCAGED: Hiredate, moveday inconsistency", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "514416 ", "URL": "/notes/514416 ", "Title": "HBRCAGED: reentrance to pay complementary termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "538821 ", "URL": "/notes/538821 ", "Title": "Record 30 in file CAGED does not show remuneration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "547114 ", "URL": "/notes/547114 ", "Title": "HBRCAGED: transference between companies", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "550100 ", "URL": "/notes/550100 ", "Title": "HBRCAGED: Sal. zeroed if employee is hired within month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "576495 ", "URL": "/notes/576495 ", "Title": "CAGED file does not show exit record", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "617388 ", "URL": "/notes/617388 ", "Title": "CAGED - wrong hiring date and missing employee", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "629664 ", "URL": "/notes/629664 ", "Title": "HBRCAGED: CBO not set in case of future P0001 entries", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "638957 ", "URL": "/notes/638957 ", "Title": "HBRCAGED doesn't fill in the CBO field", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "640494 ", "URL": "/notes/640494 ", "Title": "HBRCAGED: CBO missing for fired employees", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "658334 ", "URL": "/notes/658334 ", "Title": "HBRCAGED: CBO is not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "667174 ", "URL": "/notes/667174 ", "Title": "HBRCAGED: CBO blank when employee is transfered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "814288 ", "URL": "/notes/814288 ", "Title": "HBRCAGED record type X implementation", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "897636 ", "URL": "/notes/897636 ", "Title": "HBRCAGED adjust file is rejected by validator", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "945787 ", "URL": "/notes/945787 ", "Title": "HBRCAGED: Record type C and X - Layout changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46C", "Number": "880223 ", "URL": "/notes/880223 ", "Title": "HBRCAGED establishments counter in record A", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "380722 ", "URL": "/notes/380722 ", "Title": "HBRUTMS0 version handling is missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "409635 ", "URL": "/notes/409635 ", "Title": "HBRCAGED Entry/transfer date wrongly positioned", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "416935 ", "URL": "/notes/416935 ", "Title": "HBRCAGED: Employee is rejected if he does not have a cluster", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "417363 ", "URL": "/notes/417363 ", "Title": "HBRCAGED: Rem. Sal. is calculated wong.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "417935 ", "URL": "/notes/417935 ", "Title": "HBRCAGED: Code for school degree is wrong", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "419567 ", "URL": "/notes/419567 ", "Title": "HBRCAGED: Salaries are summed up within a month w. 2 changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "421770 ", "URL": "/notes/421770 ", "Title": "HBRCAGED: Log extensions", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "450498 ", "URL": "/notes/450498 ", "Title": "HBRUTMS5: TemSe viewer improving functionallity", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "453254 ", "URL": "/notes/453254 ", "Title": "HBRCAGED: Register B is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "455016 ", "URL": "/notes/455016 ", "Title": "HBRCAGED: Number of active employees in register type B", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "457434 ", "URL": "/notes/457434 ", "Title": "HBRCAGED: School degree is always zero", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "485514 ", "URL": "/notes/485514 ", "Title": "HBRCAGED: <PERSON> missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "487085 ", "URL": "/notes/487085 ", "Title": "HBRCAGED: Hiredate, moveday inconsistency", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "514416 ", "URL": "/notes/514416 ", "Title": "HBRCAGED: reentrance to pay complementary termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "538821 ", "URL": "/notes/538821 ", "Title": "Record 30 in file CAGED does not show remuneration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "547114 ", "URL": "/notes/547114 ", "Title": "HBRCAGED: transference between companies", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "550100 ", "URL": "/notes/550100 ", "Title": "HBRCAGED: Sal. zeroed if employee is hired within month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "576495 ", "URL": "/notes/576495 ", "Title": "CAGED file does not show exit record", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "617388 ", "URL": "/notes/617388 ", "Title": "CAGED - wrong hiring date and missing employee", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "629664 ", "URL": "/notes/629664 ", "Title": "HBRCAGED: CBO not set in case of future P0001 entries", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "638957 ", "URL": "/notes/638957 ", "Title": "HBRCAGED doesn't fill in the CBO field", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "640494 ", "URL": "/notes/640494 ", "Title": "HBRCAGED: CBO missing for fired employees", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "658334 ", "URL": "/notes/658334 ", "Title": "HBRCAGED: CBO is not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "667174 ", "URL": "/notes/667174 ", "Title": "HBRCAGED: CBO blank when employee is transfered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "742948 ", "URL": "/notes/742948 ", "Title": "HBRCAGED: <PERSON><PERSON><PERSON> not considered; Address conversion error", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "814288 ", "URL": "/notes/814288 ", "Title": "HBRCAGED record type X implementation", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "897636 ", "URL": "/notes/897636 ", "Title": "HBRCAGED adjust file is rejected by validator", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "945787 ", "URL": "/notes/945787 ", "Title": "HBRCAGED: Record type C and X - Layout changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "409635 ", "URL": "/notes/409635 ", "Title": "HBRCAGED Entry/transfer date wrongly positioned", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "526789 ", "URL": "/notes/526789 ", "Title": "HBRUTMS5 does not generate Excel file", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "550100 ", "URL": "/notes/550100 ", "Title": "HBRCAGED: Sal. zeroed if employee is hired within month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "576495 ", "URL": "/notes/576495 ", "Title": "CAGED file does not show exit record", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "617388 ", "URL": "/notes/617388 ", "Title": "CAGED - wrong hiring date and missing employee", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "622590 ", "URL": "/notes/622590 ", "Title": "CAGED - wrong firing and hiring date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "629664 ", "URL": "/notes/629664 ", "Title": "HBRCAGED: CBO not set in case of future P0001 entries", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "638957 ", "URL": "/notes/638957 ", "Title": "HBRCAGED doesn't fill in the CBO field", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "640494 ", "URL": "/notes/640494 ", "Title": "HBRCAGED: CBO missing for fired employees", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "658334 ", "URL": "/notes/658334 ", "Title": "HBRCAGED: CBO is not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "667174 ", "URL": "/notes/667174 ", "Title": "HBRCAGED: CBO blank when employee is transfered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "742948 ", "URL": "/notes/742948 ", "Title": "HBRCAGED: <PERSON><PERSON><PERSON> not considered; Address conversion error", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "814288 ", "URL": "/notes/814288 ", "Title": "HBRCAGED record type X implementation", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "880223 ", "URL": "/notes/880223 ", "Title": "HBRCAGED establishments counter in record A", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "897636 ", "URL": "/notes/897636 ", "Title": "HBRCAGED adjust file is rejected by validator", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "945787 ", "URL": "/notes/945787 ", "Title": "HBRCAGED: Record type C and X - Layout changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "742948 ", "URL": "/notes/742948 ", "Title": "HBRCAGED: <PERSON><PERSON><PERSON> not considered; Address conversion error", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "814288 ", "URL": "/notes/814288 ", "Title": "HBRCAGED record type X implementation", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "880223 ", "URL": "/notes/880223 ", "Title": "HBRCAGED establishments counter in record A", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "897636 ", "URL": "/notes/897636 ", "Title": "HBRCAGED adjust file is rejected by validator", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "945787 ", "URL": "/notes/945787 ", "Title": "HBRCAGED: Record type C and X - Layout changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "880223 ", "URL": "/notes/880223 ", "Title": "HBRCAGED establishments counter in record A", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "897636 ", "URL": "/notes/897636 ", "Title": "HBRCAGED adjust file is rejected by validator", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "945787 ", "URL": "/notes/945787 ", "Title": "HBRCAGED: Record type C and X - Layout changes", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}