{"Request": {"Number": "930110", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 348, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005449612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=A35D60AB2F82CD153A1C9A8F241482F3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "930110"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.04.2006"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-CO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Real Estate Controlling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Controlling", "value": "RE-FX-CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "930110 - Validation check on cost centers"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the case of internal rental, a cost center that you use is entered in the posting parameters during contract processing. The validation check performed to determine whether the cost center is valid happens with the current date, and not with the validity date of the cost center.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>KS210, cost center xy is not valid in controlling area xxxx from dd.mm.yyyy to dd.mm.yyy, RECN</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This error is corrected in a Support Package. For an advance correction, implement the attached source code corrections.<br />During contract processing, the start and end times of your assignment are used for a validation check on the cost centers in the posting data.<br />With this note, not all of the time-dependent checks on the cost center are cleaned up in the entire RE environment.<br /><br /><br />Before you implement the corrections, use Transaction SE80 to manually implement the following changes:</p> <UL><LI>In the 'CL_REEX_CO_SERVICES' class, create the new parameter 'IF_KEYDATE_CHECK' for the 'GET_COSTOBJECT' method: importing, associated type ABAP_BOOL, default value ABAP_TRUE, description 'Perform Random Check'.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save and activate the class.</p> <UL><LI>In the 'CL_REEX_CO_SERVICES' class, create the new parameter 'IF_KEYDATE_CHECK' for the 'GET_COST_CENTER' method: importing, associated type ABAP_BOOL, default value ABAP_TRUE, description 'Perform Random Check'.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save and activate the class.</p> <UL><LI>In the IF_RERA_ACC_SYSTEM interface, insert a new optional importing parameter in the GET_COSTOBJECT method:</LI></UL> <UL><UL><LI>IF_KEYDATE_CHECK; Importing; pass by value: 'X'; optional 'X'; typing method: TYPE; associated type: RECABOOL; default value: ABAP_TRUE; d escription: 'Time-Dependent Check'</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save and activate the interface.</p> <UL><LI>In the CL_RETM_PAYMENT_MNGR class, create a new private instance method _GET_CHECK_INTERVAL with the description 'Gets Time Interval for the Check'. For this method, specify the following parameters:</LI></UL> <UL><UL><LI>IO_BUSOBJ; Importing; pass by value: ' '; optional ' '; typing method: Type Ref To; associated type: IF_RECA_BUS_OBJECT; default value: ; description: 'Business object'</LI></UL></UL> <UL><UL><LI>ID_TERMVALIDFROM; Importing; pass by value: ' '; optional ' '; typing method: Type ; associated type: RETMTERMVALIDFROM; default value: ; description: 'Valid-From Date of Term'</LI></UL></UL> <UL><UL><LI>ID_TERMVALIDTO; Importing; pass by value: ' '; optional ' '; typing method: Type ; associated type: RETMTERMVALIDTO; default value: ; description: 'Valid-To Date of Term'</LI></UL></UL> <UL><UL><LI>ED_CHECKFROM; Exporting; pass by value: ' '; optional ' '; typing method : Type ; associated type: RETMTERMVALIDFROM; default value: ; description: 'Valid-From Date of Term'</LI></UL></UL> <UL><UL><LI>ED_CHECKTO; Exporting; pass by value: ' '; optional ' '; typing method: Type ; associated type: RETMTERMVALIDTO; default value: ; description: 'Valid-To Date of Term'</LI></UL></UL> <UL><UL><LI>EF_NO_DERIVATION; Exporting; pass by value: ' '; optional ' '; typing method: Type ; associated type: RECABOOL; default value: ; description: 'No Derivation Necessary'</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save and activate the class.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <UL><LI>Then implement the attached source code corrections.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-MI (Migration)"}, {"Key": "Other Components", "Value": "RE-FX-RA (Rental Accounting)"}, {"Key": "Other Components", "Value": "RE-FX-SC (Service Charge Settlement)"}, {"Key": "Responsible                                                                                         ", "Value": "D016550"}, {"Key": "Processor                                                                                           ", "Value": "D016550"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "865444", "RefComponent": "RE-FX-MI", "RefTitle": "Migration from Classic RE", "RefUrl": "/notes/865444"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "865444", "RefComponent": "RE-FX-MI", "RefTitle": "Migration from Classic RE", "RefUrl": "/notes/865444 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 200", "SupportPackage": "SAPKGPAB12", "URL": "/supportpackage/SAPKGPAB12"}, {"SoftwareComponentVersion": "EA-APPL 500", "SupportPackage": "SAPKGPAC13", "URL": "/supportpackage/SAPKGPAC13"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD05", "URL": "/supportpackage/SAPKGPAD05"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 3, "URL": "/corrins/**********/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 7, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "200", "ValidTo": "200", "Number": "645825 ", "URL": "/notes/645825 ", "Title": "Periodic posting log  branch to cost center not possible", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "200", "ValidTo": "200", "Number": "660021 ", "URL": "/notes/660021 ", "Title": "CO order in postng clause of rental objct cannot be assigned", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "200", "ValidTo": "200", "Number": "706464 ", "URL": "/notes/706464 ", "Title": "WBS element not displayed in posting log", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "200", "ValidTo": "200", "Number": "859092 ", "URL": "/notes/859092 ", "Title": "Posting term with master data summary for the rental object", "Component": "RE-FX-CP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "200", "ValidTo": "200", "Number": "888035 ", "URL": "/notes/888035 ", "Title": "Internal contract: Cost center missing, but no error msg", "Component": "RE-FX-CN"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "200", "ValidTo": "200", "Number": "888561 ", "URL": "/notes/888561 ", "Title": "RECN: Incorrect cost center in distribution", "Component": "RE-FX-CO"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "200", "ValidTo": "200", "Number": "912445 ", "URL": "/notes/912445 ", "Title": "RERAPP/RERAVP: CO objects in other company codes", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "859092 ", "URL": "/notes/859092 ", "Title": "Posting term with master data summary for the rental object", "Component": "RE-FX-CP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "888561 ", "URL": "/notes/888561 ", "Title": "RECN: Incorrect cost center in distribution", "Component": "RE-FX-CO"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "912445 ", "URL": "/notes/912445 ", "Title": "RERAPP/RERAVP: CO objects in other company codes", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "500", "ValidTo": "600", "Number": "888035 ", "URL": "/notes/888035 ", "Title": "Internal contract: Cost center missing, but no error msg", "Component": "RE-FX-CN"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "888561 ", "URL": "/notes/888561 ", "Title": "RECN: Incorrect cost center in distribution", "Component": "RE-FX-CO"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "912445 ", "URL": "/notes/912445 ", "Title": "RERAPP/RERAVP: CO objects in other company codes", "Component": "RE-FX-RA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}