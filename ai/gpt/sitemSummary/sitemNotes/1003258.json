{"Request": {"Number": "1003258", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 371, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016194742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=124152C2A27241DE5CF4497A86EF8472"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1003258"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2009"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rental Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rental Accounting", "value": "RE-FX-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1003258 - RE-FX integration with PSCD: Useful settings"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use the integration between RE-FX and PSCD (IS-PS-CA), as described in Note 1003497.<br /><br />This note describes</p> <UL><LI>specific settings in PSCD and RE-FX Customizing, which are required for the integration</LI></UL> <UL><LI>Business Add-Ins (BAdIs) whose implementation might be useful for a smooth integration.</LI></UL> <p><br />If required, additional information will be added to this note.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Accounting, customer, vendor, contract account, open item account<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Customizing</b><br /> <p>The specific settings for the integration with PSCD are contained in RE-FX Customizing in the following IMG paths:</p> <UL><LI>Contract -&gt; FI-CA Integration</LI></UL> <UL><LI>Accounting -&gt; FI-CA Integration</LI></UL> <p><br />These IMG nodes are only visible if the PSCD_REFX switch is active.<br /><br />In the following Customizing settings, there are some special features to be considered:</p> <UL><LI>Account determination value:<br />For technical reasons, the account determination IDs maintained for contract accounts in PSCD Customizing (in the IMG of contract accounts receivable and payable under \"Contract Accounts -&gt; Define Account Determination IDs for Contract Accounts\") must also be defined in the relevant RE-FX Customizing settings. Make these settings in the RE-FX IMG under \"Accounting -&gt; Automatically Generated Accounting Documents -&gt; Account Determination -&gt; Account Determination Values\".</LI></UL> <p><br />To ensure that the values KONTT and KONTL are not lost when the new reconciliation key is transferred make the following change in the Data Dictionary:<br /></p> <UL><LI>Up to Enhancement Package 3 (ECC 603): Add the fields KONTT and KONTL to the structure ACCIT: The customer include CI_FKKRW_ACCITA is in the append FKKRW_ACCITA. In this customer include add both fields KONTT (component type KONTT_FI) and KONTL (component type KONTL_FI).</LI></UL> <UL><LI>As of Enhancement Package 4 (ECC 604): The fields KONTT and KONTL exist in the standard SAP system in FKKRW_ACCITA. If you execute a release upgrade you must delete KONTT and KONTL from your customer include.</LI></UL> <p></p> <b>Business Add-Ins (BAdI)</b><br /> <p>For information about the general enhancement options of RE-FX, see Note 782947.</p> <UL><LI>BADI_REXA_CONTRACT_ACCOUNT<br />During the automatic generation of a contract account for a real estate contract, you can use this BAdI to preassign the master data of the new contract account.<br />Note that the system runs this BAdI only during the generation of the contract account.</LI></UL> <UL><LI>BADI_RECN_CONTRACT<br />This BAdI provides general methods in connection with the master data of the real estate contract.<br />The system always calls the method AFTER_STORE after you save changes of the real estate contract, and this method can be used, for example, to update the contract account data.</LI></UL> <UL><LI>BADI_REXA_FC_BAPI<br />The system calls this BAdI in all posting processes of RE-FX (periodic postings, service charge settlement) before calling the posting interface of PSCD, and this BAdI allows you to change the document data.</LI></UL> <UL><LI>BADI_REXA_DERIVE_FM_ACCT<br />The system runs this BAdI in all posting processes of RE-FX, and this BAdI allows you to change the derived FM account assignments. By default, the system derives the FM account assignments for a partner item and transfers the derivation to the related G/L account items. To allow a derivation of the commitment item from the G/L account, the relevant G/L account is specified during the derivation, instead of the reconciliation account of the partner.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028090)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002072)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "782947", "RefComponent": "RE-FX", "RefTitle": "Programming interfaces for RE-FX", "RefUrl": "/notes/782947"}, {"RefNumber": "1619040", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: PSCD (FI-CA) and RE-FX", "RefUrl": "/notes/1619040"}, {"RefNumber": "1003497", "RefComponent": "RE-FX", "RefTitle": "Release note: RE-FX/PSCD integration", "RefUrl": "/notes/1003497"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1003497", "RefComponent": "RE-FX", "RefTitle": "Release note: RE-FX/PSCD integration", "RefUrl": "/notes/1003497 "}, {"RefNumber": "1619040", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: PSCD (FI-CA) and RE-FX", "RefUrl": "/notes/1619040 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}