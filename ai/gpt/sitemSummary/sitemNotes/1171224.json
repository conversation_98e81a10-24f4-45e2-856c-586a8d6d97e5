{"Request": {"Number": "1171224", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 432, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007071252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001171224?language=E&token=6063B77C8112418E18861F712F6495F9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001171224", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1171224"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "RE-FX-LC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1171224 - ICI data recalculation after changes in City parameters"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><B>Changes in City parameters are not considered during recalculation of ICI records</B><br /><br />In the ICI cockpit and ICI master data screen there is no recalculation<br />based on possible changes in City parameters (like account and ICI collector) .<br />These parameters are only taken into account when first creating an ICI record.<br /><br />Two additional small issues were also discovered while solving the recalculation problem:</p> <UL><LI>In a very few cases there was a short dump when reading the City Rate table (it occured if the table contained a record for a city without category id, which actually should not have happened at all) (Note 1158197 - Performance on the ICI data screen)</LI></UL> <UL><LI>Because of a mistyping during a previous correction the splitting was not carried out. (checking for a wrong error code) (Note 1164233 - ICI cockpit does not update coefficients for D-Buildings)</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX Italy, REXCITICI, REDBAO, ICI Master Data screen, City parameters, recalculation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The ICI collector and account number are only reconsidered if it had not yet been read from database.<br />After installing this note, the City parameter changes are taken into account in change mode while recalculating each time a record is not locked.</p> <UL><LI>For <B>REDBAO</B> this means moving from one ICI record to another one triggers this recalculation if in change mode and the record is not locked and the city parameter changes are considered during this recalculation.</LI></UL> <UL><LI>For <B>REXCITICI</B> this means that for each for recalculation selected record the change in the city parameters are considered if the record is not locked.</LI></UL> <p><br />Also the above mentioned small issues are solved by this correction.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Carry out the correction instruction</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}, {"Key": "Processor                                                                                           ", "Value": "D021316"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171224/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301"}, {"RefNumber": "1476863", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: negative balance calculation", "RefUrl": "/notes/1476863"}, {"RefNumber": "1472920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect calculation for ICI record", "RefUrl": "/notes/1472920"}, {"RefNumber": "1468964", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect month of ownership in ICI record after recalc.", "RefUrl": "/notes/1468964"}, {"RefNumber": "1242056", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalc in AO after changes in City - rolling back", "RefUrl": "/notes/1242056"}, {"RefNumber": "1177390", "RefComponent": "RE-FX-LC-IT", "RefTitle": "REXCITICI: paymnt installm. date not updated in the AO", "RefUrl": "/notes/1177390"}, {"RefNumber": "1164233", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI cockpit does not update coefficients for D-buildings", "RefUrl": "/notes/1164233"}, {"RefNumber": "1158197", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Performance on the ICI Data screen", "RefUrl": "/notes/1158197"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1177390", "RefComponent": "RE-FX-LC-IT", "RefTitle": "REXCITICI: paymnt installm. date not updated in the AO", "RefUrl": "/notes/1177390 "}, {"RefNumber": "1476863", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: negative balance calculation", "RefUrl": "/notes/1476863 "}, {"RefNumber": "1472920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect calculation for ICI record", "RefUrl": "/notes/1472920 "}, {"RefNumber": "1468964", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect month of ownership in ICI record after recalc.", "RefUrl": "/notes/1468964 "}, {"RefNumber": "1242056", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalc in AO after changes in City - rolling back", "RefUrl": "/notes/1242056 "}, {"RefNumber": "1164233", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI cockpit does not update coefficients for D-buildings", "RefUrl": "/notes/1164233 "}, {"RefNumber": "1158197", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Performance on the ICI Data screen", "RefUrl": "/notes/1158197 "}, {"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD14", "URL": "/supportpackage/SAPKGPAD14"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60204INEAAPPL", "URL": "/supportpackage/SAPK-60204INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60303INEAAPPL", "URL": "/supportpackage/SAPK-60303INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 3, "URL": "/corrins/0001171224/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 7, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1017502 ", "URL": "/notes/1017502 ", "Title": "Incorrect change documents", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1138671 ", "URL": "/notes/1138671 ", "Title": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1138928 ", "URL": "/notes/1138928 ", "Title": "Unnecessary splits of ICI records", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1157680 ", "URL": "/notes/1157680 ", "Title": "Error messages with ICI records locked for recalculation", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1164233 ", "URL": "/notes/1164233 ", "Title": "ICI cockpit does not update coefficients for D-buildings", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "603", "Number": "1158197 ", "URL": "/notes/1158197 ", "Title": "Performance on the ICI Data screen", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "604", "Number": "1125593 ", "URL": "/notes/1125593 ", "Title": "ICI: historical objects, uninhabitable buildings, exemptions", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1138671 ", "URL": "/notes/1138671 ", "Title": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1138928 ", "URL": "/notes/1138928 ", "Title": "Unnecessary splits of ICI records", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1157680 ", "URL": "/notes/1157680 ", "Title": "Error messages with ICI records locked for recalculation", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1164233 ", "URL": "/notes/1164233 ", "Title": "ICI cockpit does not update coefficients for D-buildings", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1138671 ", "URL": "/notes/1138671 ", "Title": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1138928 ", "URL": "/notes/1138928 ", "Title": "Unnecessary splits of ICI records", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1157680 ", "URL": "/notes/1157680 ", "Title": "Error messages with ICI records locked for recalculation", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1164233 ", "URL": "/notes/1164233 ", "Title": "ICI cockpit does not update coefficients for D-buildings", "Component": "RE-FX-LC-IT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}