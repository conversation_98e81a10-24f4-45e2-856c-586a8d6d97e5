{"Request": {"Number": "781680", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 267, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015773292017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000781680?language=E&token=041653078AB051A97D9200A252988577"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000781680", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000781680/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "781680"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.11.2016"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "781680 - SDCC/ SDCCN - Problems with function modules"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>For the planned execution of a service session at SAP, you want to provide SAP with statistical data on the use of the R/3 System.<br />You have collected data for a service session with either SDCC or SDCCN. In the log for the data collection you found errors for some function modules.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SDCC, SDCCN, Data collection, Function module, SAP Solution Manager,<br />Earlywatch Alert, Service session</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>During a data collection for a service session some function modules could not collect data as expected.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Initial remarks (SDCCN):</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>a) Extended log:</strong></p>\r\n<p>SDCCN calls various function modules to collect data for service sessions.&#160;Sometimes errors are visible in the SDCCN log (they are in BLUE text).&#160;Known problems with specific function modules are listed in this note.&#160; The SDCCN log does not always show all function module called: it might only show the last module called successfully or not show any errors in BLUE.&#160;This can make analysis difficult, particularly for data collections which do not complete or run longer than can be reasonably expected.&#160;In cases where it is not possible to identify immediately which function module is causing problems, it can be helpful to switch on the extended log in SDCCN-&gt;GoTo-&gt;Settings-&gt;Task-Specific-&gt;Maitenance-&gt;Settings-&gt;Display&lt;-&gt;Change-&gt;set flag next to \"activate extended log\".&#160; After the extended log is activated, it should be easier to identify function modules which cause problems in the SDCCN log.&#160;Please be aware that the extended log is not a trace. It will just lead to some extra lines of text in the log.</p>\r\n<p><strong>&#160;b) Report pregeneration (XXXXXX) failed:</strong></p>\r\n<p>in 740 basis systems, you might come across errors like \"FUNCTION MODULE: Report pregeneration (XXXXXX) failed\" where FUNCTION MODULE can be replaced by any number of function modules and XXXXX is a number. Typically, this problem is the result of insufficient authorizations.&#160; Please refer to SAP note 763561, Q1 for information about authorizations required for SDCCN.&#160; A typical symptom of this problem is that EWA reports are generated but with a grey/gray rating.</p>\r\n<p><strong>Problems with specific function modules:</strong></p>\r\n<p>Different problems can occur for different function modules. Some of the most common problems are listed below. If you can not find a solution in this note, please open a message in component SV-SMG-SDD.</p>\r\n<ol><ol>1. Function Interface Differences</ol></ol><ol><ol>Q: When I collect data with the Service Data Control Centre ( SDCC or SDCCN), error messages show up, which indicate that a DIFFERENCE IN FUNCTION INTERFACE has occurred. How serious is this?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Data should generally not be collected before the report RTCCTOOL has been executed, and all status lights there are green. If all status lights in RTCCTOOL are green, and problems of this type still occur, check if the service definitions need to be updated, as described in notes 216952 ( SDCC) or 763561 (SDCCN).</ol></ol><ol><ol>If the DIFFERENCE IN FUNCTION INTERFACE&#160;&#160;messages remain after the next data collection you can allow an approximate data collection (i.e. even with interfaces that are not completely consistent). For R/3 versions &gt;= 4.0B this option is set by default.</ol></ol>\r\n<ul>\r\n<li>SDCC -&gt; Maintenance -&gt; Settings -&gt; General -&gt; Data collection with approximate interfaces -&gt; Allow dirty download</li>\r\n</ul>\r\n<ul>\r\n<li>SDCCN -&gt; Goto -&gt; Settings -&gt; Task specific settings -&gt; Data request -&gt; Attributes for session data collection -&gt; Allow approximate session data collection</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The advantage is that data for the inconsistent modules is collected as far as possible - however, with no guarantee for the accuracy or completeness of the data.<br /><br />Data collected through modules with interface inconsistencies are recorded in the log for the data collection with an indicator for a 'Warning' . If an 'approximate download could be done, the data will be sufficient for sessions.</p>\r\n<ol><ol>2. FUNCTION_MODULE_NOT FOUND</ol></ol><ol><ol>Q: When I collect data, error messages show up which indicate that a certain 'FUNCTION_MODULE_NOT FOUND'. What to do?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Make sure that RTCCTOOL returns only green status lights, i.e that the newest version of the Service Data Control Centre and any related support packages are implemented.</ol></ol><ol><ol>If all notes are green, but function modules are still reported missing ensure that you are executing an 'approximate data collection', as described above, in Q1.</ol></ol><ol><ol>If you are sure you are executing an' approximate data collection', but a function module is still reported missing, please open a message in component. SV-SMG-SDD</ol></ol><ol><ol>3. During a data collection errors of the type listed below occur ( also possible for other function modules). The newest version of the add on ST-PI has been implemented. Service Definitions can be refreshed from OSS. How can the below errors be resolved?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>CMO_QRFCQUEUES_READ : include generation of 001158 failed</ol></ol><ol><ol>Interface data missing: 001158 E</ol></ol><ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Page 9</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&gt; if_data_missing&#160;&#160;7</ol></ol><ol><ol>SSF_COLLECT_DATA_FOR_SDCC_INST : include generation of 083000 failed</ol></ol><ol><ol>Interface data missing: 083000</ol></ol><ol><ol>&gt; if_data_missing&#160;&#160;7</ol></ol><ol><ol>SSF_COLLECT_DATA_FOR_SDCC : include generation of 082000 failed</ol></ol><ol><ol>Interface data missing: 082000</ol></ol><ol><ol>&gt; if_data_missing&#160;&#160;7</ol></ol><ol><ol>SSF_COLLECT_DATA_FOR_SDCC_SERV : include generation of 082002 failed</ol></ol><ol><ol>Interface data missing: 082002</ol></ol><ol><ol>&gt; if_data_missing&#160;&#160;7</ol></ol><ol><ol>SSF_COLLECT_DATA_FOR_SDCC_DB : include generation of 082001 failed</ol></ol><ol><ol>Interface data missing: 082001 &gt; if_data_missing</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The service definitions will have to be completely replaced, as described in note 727998.</ol></ol><ol><ol>4. I have collected data for a session, but when I drill down in the download in the Service Data Viewer I get the error message</ol></ol><ol><ol>'No logical functional names found for current select'.</ol></ol><ol><ol>Have data been collected?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The Service Data Viewer in certain releases (see below) can&#160;&#160;not display download data if the server name contains an '_' (underscore). Please check the data collection log for this session. If there is no error message regarding the function module, the data was collected. In this case there should be no problem for the system were the session is performed. Another option is to rename the server, and collect data again when enough data has been accumulated under the new server name.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>This problem has been resolved with the new Service Data viewer 2.0.</ol></ol><ol><ol>This is contained in support package 30 (basis rel. 610) and&#160;&#160;SP20 (basis rel. 620 ). It is also contained in all ST-PI versions &gt;= ST-PI 2005.</ol></ol><ol><ol>5. Function module is inactive.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: Errors in data collection log:</ol></ol><ol><ol>\"&lt;Logfunc&gt; : function is inactive\",</ol></ol><ol><ol>\"&lt;Logfunc&gt; :an error occured during generation(&lt;function module&gt;)\".</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The ACTIVE flag is not set in database table ENLFDIR even though the function module is implemented in the system.</ol></ol><ol><ol>To solve this problem for R/3 Release &gt;= 4.6B:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Please run function module&#160;&#160;\"RS_FLIB_ENLFDIR_ACTIVE_CONSIST\"</ol></ol><ol><ol>via transaction SE37, with import parameters set to:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160;UPDATE_ENLFDIR = 'X'</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160;FUNCTION_GROUP = '&lt;function group of inactive function module&gt;'.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Please run above function module for each inactive function module listed in the data collection log ,&#160;&#160;to recreate consistency of selected modules. You can find the respective function group in database table TFDIR in field \"PNAME\",&#160;&#160;looking for selected function module in field \"FUNCNAME\".</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Note: In R/3 Releases 4.6B and 4. 6C function module</ol></ol><ol><ol>\"RS_FLIB_ENLFDIR_ACTIVE_CONSIST\" could have only one import parameter. The second import parameter \"FUNCTION_GROUP\" was added with R/3 release 4.6D. To fix the inconsistency in this case please run function module \"RS_FLIB_ENLFDIR_ACTIVE_CONSIST\" , setting import parameter UPDATE_ENLFDIR = 'X'.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>This will recreate consistency for all function groups. Depending on how many function groups have to be fixed, the run time could be too long for running in a dialog step. In that case we ask you to create a report which calls function module \"RS_FLIB_ENLFDIR_ACTIVE_CONSIST\" with import parameter UPDATE_ENLFDIR = 'X' and run th</ol></ol><ol><ol>6. Source code in test report /1CAGTF/WI&lt;CLUST_ID&gt; is out of date</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: Log of data collection shows \"&lt;logical function name&gt; : Too many parameters specified with PERFORM.\". Also ABAP runtime error</ol></ol><ol><ol>PERFORM_TOO_MANY_PARAMETERS occurs in ST22.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160;&#160;&#160; Or</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: Log of data collection shows \"&lt;logical function name&gt; :</ol></ol><ol><ol>Incorrect parameter with CALL FUNCTION.\". Also ABAP runtime error CALL_FUNCTION_PARM_MISSING occurs in ST22.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: In database table BDL_GENER ( or /BDL/_GENER) please delete all entries with an old time stamp (older than time stamp of data collection via SDCC). Then start data collection and transfer via transaction SDCC again</ol></ol><ol><ol>7. Q: The SDCCN log displays a message like \"&lt;Function Module Name&gt; : A type conflict occured when parameters were passed .\"</ol></ol><ol><ol>In ST22 this short dump occurs at the same time:</ol></ol><ol><ol>CALL_FUNCTION_UC_STRUCT (Exception&#160;&#160;CX_SY_DYN_CALL_ILLEGAL_TYPE).</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>In the \"What happened?\" section of the short dump the program that has been terminated is listed as \"/1CAGTF/WI&lt;xxxxxx&gt;\", where &lt;xxxxxx&gt; stands for a 6 digit number.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>The above error messages occurs during a data collection for a service session. ST-PI and support packages for ST-PI are up to date. Service definitions are also up to date.</ol></ol><ol><ol>What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: An old data collection report is used to collect the data for a function module. Please delete entry /1CAGTF/WIxxxxxx from table /BDL/_GENER. During the next data collection the necessary report is regenerated with up to date definitions.</ol></ol><ol><ol>8. Q: During the data collection the Service Data Control Center makes no attempt to collect some function modules. Because of this there are no error messages in the data collection log, the only symptom is that the function modules are missing from the data collection, and/or that an Earlywatch Alert report lists them as missing.</ol></ol><ol><ol>What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: There are different possible causes and fixes for this situation.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A1:The section \"Missing Function Modules\" in the chapter \"Service Preparation\" of the EarlyWatch Alert ( EWA) report lists missing downloadmodules for servers on which actually no SAP application server instance runs (for example, LiveCache server).</ol></ol><ol><ol>Please review SAP Note 1389277.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A2: Please check notes 712511 and 762696 to see if either of these situations apply. If you have checked these suggestions, and the function modules remain missing, please implement note 932861. This ensures that all reports needed to collect data for the function modules needed for a session are actually present in the system. This is particularly relevant where well known older function modules are missing, such as OCS_GET_INFO, SYSTEM_INFO or TH_SERVER_LIST.</ol></ol><ol><ol>9. Q: An Earlywatch Alert session was rated grey. Which function modules can cause this?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Note 762696 contains information about this problem.</ol></ol>\r\n<p>For many of the other exceptions that can occur helptexts have been maintained. You can access these by double clicking on the exception in the log. It is also advisable to perform a note search for the function module in question. Additionally some&#160;&#160;common problems are listed in the questions below.<br /><br /></p>\r\n<ol><ol>10. ARCH_OBJECTS_TOP_GET : no matching interface found</ol></ol><ol><ol>&gt; function module not found</ol></ol><ol><ol>&gt; ARCH_OBJECTS_TOP_GET : funcname /SDF/ARCH_OBJECTS_TOP_GET , module</ol></ol><ol><ol>000070</ol></ol><ol><ol>Q: The above error message is found in the data collection log. What can be done?</ol></ol>\r\n<p style=\"padding-left: 60px;\">`&#160;A: This function module is contained in SP4 for ST-PI 2005_1*.This support package should be implemented when it becomes available ( ca 12 June 2006). Until then the error should be ignored.</p>\r\n<p style=\"padding-left: 60px;\">11. DB_ORA_CRIT_OBJ___FREESPC_STAT&#160;&#160;: Approximate Download</p>\r\n<p style=\"padding-left: 60px;\">...Please check note 648439.</p>\r\n<p style=\"padding-left: 60px;\">12. DLD_SYB_SPINLOCKS : no matching interface found&#160;&#160; E &gt; function module not found</p>\r\n<p style=\"padding-left: 60px;\">Q: The above error message is found in the log. What can be done?</p>\r\n<p style=\"padding-left: 60px;\">A: Implement the newest version of ST-PI and its support packages, at least ST-PI 2008_1* SP12 / ST-PI 740 SP2</p>\r\n<ol><ol><ol></ol></ol></ol><ol><ol><ol><ol><ol></ol></ol></ol></ol><ol></ol><ol></ol><ol></ol><ol></ol><ol>12. DLD_SQL_STMTS_ORA : no matching interface founE &gt; function module not foundQ: The above error message is found in the log. What can be done?A: Implement the newest version of ST-PI and it's supportpackages, at least ST-PI 003C* (Note 597673) with SP2 ( note 539977).</ol><ol></ol><ol></ol><ol>13. Q: DOWNLOAD_SQL_CACHE_ORACLE : Internal error when executing EXEC SQL.</ol><ol></ol><ol></ol></ol>\r\n<p style=\"padding-left: 60px;\">During a data collection this error occurs in the log.</p>\r\n<ol><ol>What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please check if v$sysstat contains unusually large values, via</ol></ol><ol><ol>ST04 -&gt; Detail Analysis Menu -&gt; Display V$ values -&gt; v$sysstat</ol></ol><ol><ol>If the values are unusually large please check note 510497. If you can not resolve the problem with that note, a customer message should be opened in BC-DB-ORA</ol></ol><ol><ol>14. DOWNLOAD_SQL_CACHE_ORACLE</ol></ol><ol><ol>Q:&#160;&#160; DOWNLOAD_SQL_CACHE_ORACLE : Error when attempting to IMPORT object MY_MONI_GLOBAL</ol></ol><ol><ol>In the log for a data collection you find the above error message.</ol></ol><ol><ol>In addition a shortdump&#160;&#160;CONNE_IMPORT_WRONG_COMP_TYPE&#160;&#160;with exception with exception&#160;&#160;CX_SY_IMPORT_MISMATCH_ERROR occurs.</ol></ol><ol>How can this be fixed?</ol>\r\n<p><br />A: Please follow the instructions in note 877406.</p>\r\n<ol><ol>15. Q: DOWNLOAD_SQL_CACHE_ORACLE : Program /SDF/RSORADLD not found&#160;&#160;#</ol></ol><ol><ol>This error occurs during a data collection on a system where SP5 for ST-PI 2005* is already implemented. Service Definitions have been refreshed less than 7 days ago. How important is this?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The task of function module DOWNLOAD_SQL_CACHE_ORACLE has been taken over by a new function module, DLD_SQL_STMTS_ORA.</ol></ol><ol><ol>The filters in the service definitions can currently&#160;&#160;not be adjusted to avoid the call to DOWNLOAD_SQL_CACHE_ORACLE .</ol></ol><ol><ol>Any exceptions for this function module can be ignored if&#160;&#160;the report /sdf/rsoradld_new is available in the system.</ol></ol><ol><ol>With the implementation of SP 5 report /SDF/RSORADLD was deleted . The report was replaced by /SDF/RSORADLD_NEW&#160;&#160;which has been available for 2 years, with ST-PI shipment</ol></ol><ol><ol>16. Q: DOWNLOAD_SQL_CACHE_ORACLE:&#160;&#160;..Syntax error&#160;&#160;for RSORADLD ...</ol></ol><ol><ol>This error occurs during a data collection on a system where SP5 for ST-PI 2005* is already implemented. Service Definitions have been refreshed less than 7 days ago. The error also causes a shortdump.</ol></ol><ol><ol>What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol><ol>A: The service definitions need to be adjusted manually, to avoid a call to the obsolete report RSORADLD.Please follow the instruction&#160;&#160;in SAP Note 1016193</ol></ol></ol>\r\n<p>17. Q: DLD_SQL_STMTS_ORA : SQL error 904 occurred when executing EXEC SQL. This error occurs during a data collection on a system where ST5 for ST-PI 2005* is already implemented. Service Definitions have been refreshed less than 7 days ago. What can be done?</p>\r\n<ol><ol><ol>A: Please follow the instructions in note 1021537.</ol><ol></ol><ol></ol><ol></ol><ol></ol><ol></ol></ol></ol>\r\n<p style=\"padding-left: 90px;\">18.DVM_STPI_GET*</p>\r\n<p style=\"padding-left: 90px;\">Q: During a data collection with SDCCN you discover the below errors in the data collection log .<br />Report RTCCTOOL ( Service Preparation Check ) recommends SAP Note 2237911.&#160; What can be done ?</p>\r\n<p style=\"padding-left: 90px;\">02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; DVM_STPI_GET_PART_SNAPSHOT : no matching interface found<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &gt; function module not found<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &gt; DVM_STPI_GET_PART_SNAPSHOT : funcname DVM_STPI_GET_PART_SNAPSHOT , module 001313<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; DVM_STPI_GET_AGING_RUNS : no matching interface found<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &gt; function module not found<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &gt; DVM_STPI_GET_AGING_RUNS : funcname DVM_STPI_GET_AGING_RUNS , module 001312<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; DVM_STPI_GET_AGING_OBJECTS : no matching interface found<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &gt; function module not found<br />02.12.2015&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13:06:17&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &gt; DVM_STPI_GET_AGING_OBJECTS : funcname DVM_STPI_GET_AGING_OBJECTS , module 001311</p>\r\n<p style=\"padding-left: 90px;\"><br />A: &#160;Please review the information in SAP Note 2237911 . Implement the correction in the note . This enables the use of the above function modules.</p>\r\n<ol><ol><ol><ol>19. EWA_CHECK_OSMONQ: During a data collection the above logical function causes an error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol></ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol><ol></ol></ol><ol><ol>A: This logical function is delivered with SP 8 for ST-PI 2005_1* ( and equivalents). SAP Note 539977 documents the availability of the support packages.</ol><ol></ol><ol></ol></ol><ol>20. EWA_GET_PROCESSES</ol><ol>Q: During a data collection this above logical function causes an error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: This logical function is delivered with SP 8 for ST-PI 2005_1* ( and equivalents). SAP Note 539977 documents the availability of the support packages.</ol></ol><ol><ol>21. EWA_GET_CONFIG_FILES_G</ol></ol><ol><ol>EWA_GET_CONFIG_FILES_L</ol></ol><ol><ol>EWA_GET_CONFIG_FILES_R</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: During a data collection the above logical functions cause an error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: These are delivered with SP 8 for ST-PI 2005_1* ( and equivalents). SAP Note 539977 documents the availability of the support packages.</ol></ol><ol><ol>22. Q: EWA_GET_HARDWARE_INFO&#160;&#160;... Function Module not found</ol></ol><ol><ol>A: This function module is available from ST-PI 2005_1_700: Patch 0003 (and equivalent). It replaces SMON_READ_HARDWARE_DESCR_FILE .</ol></ol><ol><ol>SAP Note 539977 documents the availability of the support packages</ol></ol><ol><ol>23. Q: Logical function EWA_GET_PROCESSES (in SE37: /SDF/EWA_GET_PROCESSES) causes errors in the session collection log in SDCC(N). In ST22 you can see a corresponding short dump CONVT_NO_NUMBER.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A:This is due to a program error in the following support packages of ST-PI:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160;2005_1_46C&#160;&#160;SAPKITLQD8</ol></ol><ol><ol>&#160;&#160;2005_1_620&#160;&#160;SAPKITLQG8</ol></ol><ol><ol>&#160;&#160;2005_1_640&#160;&#160;SAPKITLQH8</ol></ol><ol><ol>&#160;&#160;2005_1_700&#160;&#160; SAPKITLQI6</ol></ol><ol><ol>&#160;&#160;2005_1_710&#160;&#160;SAPKITLQJ4</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>It can be fixed by implementing the correction in SAP Note 1140626.</ol></ol><ol><ol>24. EWA_GET_PARAMETER_DATA_D</ol></ol><ol><ol>EWA_GET_PARAMETER_DATA_I</ol></ol><ol><ol>Q: During a data collection the above logical functions cause an error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: These are delivered with SP 8 for ST-PI 2005_1* ( and equivalents).</ol></ol><ol><ol>SAP Note 539977 documents the availability of the support packages.</ol></ol><ol><ol>25. EWA_ICF_SERVICES : no matching interface found</ol></ol><ol><ol>&gt; function module not found</ol></ol><ol><ol>&gt; EWA_ICF_SERVICES : funcname /SDF/EWA_ICF_SERVICES , module 000173</ol></ol><ol><ol>Q:The SDCCN session log contains the above message, what can be done?</ol></ol><ol><ol>A:This function module becomes available with SP2 for ST-PI 2008_1*, after 30 April 2010. 'Service Preparation Check' ( from SDCCN) or report RTCCTOOL will recommend the new support package once it is available .</ol></ol><ol><ol>26. EWA_MAIN_BUSINESS_HOURS</ol></ol><ol><ol>Q: During a data collection the above logical functions cause an error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol><ol><ol>A: This function module is available from ST-PI 2008_1* ( 15 Dec 2008). SAP Note 539977 documents the availability.</ol></ol><ol><ol>27. Q: EWA_SAPOSCOL_B ... Function Module not found</ol></ol><ol><ol>A: Implement the newest support package for ST-PI 2005*.</ol></ol><ol><ol>28. Q: EWA_SAPOSCOL_B raises error&#160;&#160;FILE_G_ERROR</ol></ol><ol><ol>A: Install a current version of SAPOSCOL (at least version 20.93).</ol></ol><ol><ol>Details can be found in SAP note 19227.</ol></ol><ol><ol>29. EWA_SAPWORKLOAD_CONFIG : no matching interface found</ol></ol><ol><ol>&gt; function module not found</ol></ol><ol><ol>&gt; EWA_SAPWORKLOAD_CONFIG : funcname /SDF/EWA_SAPWORKLOAD_CONFIG , module 000174</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q:The SDCCN session log contains the above message, what can be done?</ol></ol><ol><ol>A:This function module becomes available with SP2 for ST-PI 2008_1*, after 30 April 2010. 'Service Preparation Check' ( from SDCCN) or report RTCCTOOL will recommend the new support package once it is available .</ol></ol><ol>30. Q: 2 errors occur during data collection</ol>\r\n<ul>\r\n<li>EWA_SCMEWM_STATISTICS : exception EWM_NOT_FOUND</li>\r\n</ul>\r\n<ul>\r\n<li>EWA_SCMEWM_ALERTS : exception EWM_NOT_FOUND</li>\r\n</ul>\r\n<p><br />These errors occur during a data collection on a system where the following conditions are met</p>\r\n<ul>\r\n<li>SP8 for ST-PI 2008* ( see SAP Note 539977) is implemented</li>\r\n</ul>\r\n<ul>\r\n<li>Component SCMEWM is not implemented</li>\r\n</ul>\r\n<p><br />A: Both function modules should only be called on systems where Component SCMEWM is implemented.<br />The errors can be disregarded for any other systems.<br />How to correct the errors:<br />An improved&#160;&#160;filter in the service definitions published by SAP has become available on 02 Oct 2013 .<br />Please refresh the service definitions, first in your SAP Solution Manager system, and then in the connected managed system.<br /><br /><br /><br /></p>\r\n<ol><ol>31. FILL_SNAPSHOT_DATA : SHARED_MEMORY_NOT_AVAILABLE</ol></ol><ol><ol>Q: In the SDCCN session log I find error messages of the type SHARED_MEMORY_NOT_AVAILABLE for FILL_SNAPSHOT_DATA and several other function modules. There are also corresponding short dumps in ST22.</ol></ol><ol><ol>What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A. Error messages of this type indicate that at the time of the data collection SAPOSCOL was not running. Various function modules attempt to read SAPOSCOL&#160;&#160;Shared Memory but fail because in that moment SAPOSCOL is not running correctly.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Recommendation:</ol></ol><ol><ol>Ensure that SAPOSCOL is running correctly and continuously. You can verify this in transaction ST06/ ST06N/ OS07N.</ol></ol><ol><ol>It is also recommended to install the latest SAPOSCOL. If there are problems messages can be opened in BC-CCM-MON-OS.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Affected function modules can be:</ol></ol><ol><ol>GET_POOL_SINGLE</ol></ol><ol><ol>GET_POOL_SUM</ol></ol><ol><ol>GET_DISK_SUM</ol></ol><ol><ol>GET_DISK_SINGLE</ol></ol><ol><ol>GET_CONFIG_DEFINED</ol></ol><ol><ol>GET_CONFIG_USED</ol></ol><ol><ol>GET_MEM_ALL</ol></ol><ol><ol>GET_CPU_ALL</ol></ol><ol><ol>FILL_SNAPSHOT_DATA</ol></ol><ol><ol>GET_FSYS_SINGLE</ol></ol><ol><ol>32. Q: GET_DB_ORA_TAB_GROWTH_BW</ol></ol><ol><ol>During a data collection the error below occurs in the log. ST-PI 003C is implemented, the Service Definitions can be refreshed without error.</ol></ol><ol><ol>/SDF/GET_DB_ORA_TAB_GROWTH_BW:an error occured during generation</ol></ol><ol><ol>/SDF/GET_DB_ORA_TAB_GROWTH_BW : function is inactive</ol></ol><ol><ol>What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Note 696016 explains how to activate the function module and how to repair function group /SDF/RI_ORACLE .</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>GET_ORA_SYS_TABLE_V_BUFF_STATS</ol></ol><ol><ol>Q: During a data collection this above logical function causes an error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: This logical function is delivered with SP 8 for ST-PI 2005_1* ( and equivalents). SAP Note 539977 documents the availability of the support packages.</ol></ol><ol><ol>33. GET_ORA_SYS_TABLE_V_INSTANCE : Type conflict when calling a function module .</ol></ol><ol><ol>An error of this type occurs during a data collection with SDCCN.</ol></ol><ol><ol>At the same type a shortdump of the type CALL_FUNCTION_CONFLICT_TYPE with exception&#160;&#160;CX_SY_DYN_CALL_ILLEGAL_TYPE&#160;&#160;occurs. This shortdump also contains&#160;&#160;the following information:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>'..The function module interface allows you to specify only fields of a particular type under \"QUERY_PATTERN\". The field \"%_IQUERY_PATTERN\" specified here has a different field type. .. '</ol></ol><ol><ol>How can this be resolved?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A:&#160;&#160;Please delete and replace the service definitions on the affected system.</ol></ol><ol><ol>In most cases this can be done using the SDCCN method described in SAP Note 727998.</ol></ol><ol><ol>Be aware that typically a SAP Solution Manager is the source of service definitions for managed systems.</ol></ol><ol><ol>In such cases, please&#160;&#160;delete and replace the service definitions in the SAP Solution Manager first, and then in the managed system itself.</ol></ol><ol><ol>34. GET_SYSTEM_INFO</ol></ol><ol><ol>Shortdump DBIF_DSQL2_OBJ_UNKNOWN (SAP releases &gt;= 4.6B) occurs in \"GET_SYSTEM_INFO\" in statement select max(lcheck_date) into :likey_date from mlicheck</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please implement the newest version of SDCC, at least version 2.3. The service definitions should be&#160;&#160;maintained as described in note 216952 ( FAQ SDCC) or note 763561 (FAQ SDCCN)</ol></ol><ol><ol>Also, IF SDCC was implemented via the transport recommended in note 560630 (NEVER in a system containing ST-PI) this transport should be re-implemented with unconditional mode u168.</ol></ol><ol><ol>35. LC_SEL_LOGLOG : Tabelle /SAPAPO/LCLOGLOG&#160;&#160;ist nicht im Dictionary verzeichnet&#160;&#160;&#160;&#160;or</ol></ol><ol><ol>LC_SEL_LOGLOG : Table /SAPAPO/LCLOGLOG is not listed in the ABAP/4 Dictionary</ol></ol><ol><ol>The above errors occur during a data collection. At the same time a shortdump DBIF_NTAB_TABLE_NOT_FOUND occurs. The newest version of ST-PI is implemented, service definitions can be refreshed. What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please advise the customer to completely replace the service definitions as described in note 727998.</ol></ol><ol><ol>36. MSQ_DB12_DOWNLOAD</ol></ol><ol><ol>Q: During a data collection SDCC shows the following error message</ol></ol><ol><ol>MSQ_DB12_DOWNLOAD : Unable to interpret Disk&#160;&#160;...</ol></ol><ol><ol>At the same time a&#160;&#160;shortdump CONVT_NO_NUMBER with the exception</ol></ol><ol><ol>CX_SY_CONVERSION_NO_NUMBER occurs. How can this be fixed?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please follow the instructions in note 758964</ol></ol><ol><ol>37. MSS_GROWING_TABLES</ol></ol><ol><ol>Q: Shortdump CONNE_IMPORT_WRONG_COMP_LENG occurs when calling logical download function MSS_GROWING_TABLES.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Solved with coding correction in ST-PI003C.See SAP note 636533</ol></ol><ol><ol>38. MSQ_SPINFO_DOWNLOAD</ol></ol><ol><ol>Q: A SQL Server based system writes Database Error 50000 to the System Log during data collection in SDCC.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: You are running R/3 release 6.20 or later on a MSSQL Server database.</ol></ol><ol><ol>During the data collection in SDCC function module MSQ_SPINFO_DOWNLOAD calls MSS_GET_TABLE_SIZE_INFO on table \"syscomments\" which does not exist in the current schema. The system log contains this error:</ol></ol><ol><ol>Database error 50000 has occurred during FET</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: The solution to this problem is described in note 836323.</ol></ol><ol><ol>39. MSQ_DBCC_DOWNLOAD</ol></ol><ol><ol>Q: During data collection logical function module MSQ_DBCC_DOWNLOAD collects data on the results of the DBCC checks. This function module uses the command master..xp_cmdshell. This may only be used by user 'dbo'.</ol></ol><ol><ol>In a 'schema system' where the data base user is not 'dbo' such a call will lead to ABAP runtime error DBIF_DSQL2_SQL_ERROR.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The solution for SAP release = 6.20 is described in note 594508.</ol></ol><ol><ol>The solution for SAP release &gt; 6.20 is described in SAP note 838581.</ol></ol><ol><ol>40. MSQ_DB02_DOWNLOAD</ol></ol><ol><ol>Q: MSQ_DB02_DOWNLOAD returns no historical data. The Earlywatch Alert report does not contain information regarding ' Database growth '.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please refer to SAP note 924108.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>MSS_WAITHIST</ol></ol><ol><ol>Q: During a data collection this above logical function causes an error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: This logical function is delivered with SP 8 for ST-PI 2005_1* ( and equivalents). SAP Note 539977 documents the availability of the support packages.</ol></ol><ol><ol>41. NT_PRFCLOG_DOWNLOAD</ol></ol><ol><ol>Errors for this function module can be ignored for all services based on the same data collection as EarlyWatch</ol></ol><ol><ol>42. PROFILE_FROM_PAHI/O</ol></ol><ol><ol>Errors for this function module can be ignored as the information is no longer used.</ol></ol><ol><ol>43. Q: PROFILE_FROM_PAHI/S : NO_PARAMS_FOUND_FOR_GIVEN_CRIT</ol></ol><ol>The above error message occurs during a data collection. How can this be fixed?</ol>\r\n<p><br />A:Errors for this function module can be ignored where&#160;&#160;it has been replaced by EWA_GET_PARAMETER_DATA_I , ie since ST-PI 2005_1_700: Patch 0005 ( and equivalent)<br />SAP Note 539977 documents the availability of the support packages</p>\r\n<ol><ol>44. READ_STAMP</ol></ol><ol><ol>Q: During a data collection the above logical function causesan error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: This function module is delivered with ST-PI 2008_1*</ol></ol><ol><ol>SAP Note 539977 documents the availability.</ol></ol><ol><ol>45. READ_RFC_LIST</ol></ol><ol><ol>Q: During a data collection the above logical function causesan error of the type&#160;&#160;FUNCTION_MODULE_NOT_FOUND</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: This function module is delivered with ST-PI 2008_1*</ol></ol><ol><ol>SAP Note 539977 documents the availability.</ol></ol><ol><ol>46. RRR_INSTALLATION_CHECK: function is inactive</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: The above message appears in the log during data collection. Is it important; what can be done about it?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: This module is in itself of no great priority for service delivery. But to resolve this issue you can ask the customer to go to transaction SE37, deactivate RRR_INSTALLATION_CHECK, and then activate it again.</ol></ol><ol><ol>47. RSCONEW_BICONTENT_ANALYSIS : no matching interface found</ol></ol><ol><ol>&gt; function module not found</ol></ol><ol><ol>Q: The above error message is found in a data collection log. What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: This function module is only available in systems with SAP_BW 30B&#160;&#160;SP 22 and higher. For all other systems this can be ignored</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>SAPWL* function modules</ol></ol><ol><ol>Q: During a data collection for a service session SAPWL* function modules exception NO_DATA_FOUND&#160;&#160;occurs.</ol></ol><ol><ol>This is happening on a system with basis rel 700, with a support package lower than 8</ol></ol><ol>In ST03 the folders 'week' for all instances contain data for the full calendar week prior to the session.</ol>\r\n<p><br />A: Please implement the solution described in note 917558</p>\r\n<ol><ol>48. SAPWL_TCODE_AGGREG_MONTH : exception NO_DATA_FOUND</ol></ol><ol><ol>Q: The above exception occurs in a data collection for an Earlywatch Alert for Solution Manager.</ol></ol><ol>What can be done?</ol>\r\n<p><br />A: These function modules are not used for the weekly Earlywatch Alert, they are only used in Service Level Reports. They have no influence on the rating of an individual Earlywatch Alert.<br /><br />Please check if in ST03 data is correctly accumulated for each instance, in folders #month#.<br />If&#160;&#160;this is not the case, please check if the performance collector has been set up correctly, as per note 12103.<br />If you need more help please open a message in BC-CCM-MON-TUN.<br /><br />If this occurs on Basis Rel 700, SP 0 - 6 please check the previous question as well.</p>\r\n<ol><ol>49. /SDF/SAPLRI_SQLSERVER</ol></ol><ol><ol>Q: An ABAP shortdump occurs in ABAP program /SDF/SAPLRI_SQLSERVER.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: For all ABAP shortdumps related to ABAP program /SDF/SAPLRI_SQLSERVER see SAP note 866576 for SQL Server 2000 and SAP note 934045 for for SQL Server 2005</ol></ol><ol><ol>50. SFW_SOLMAN_DATA : no matching interface found</ol></ol><ol><ol>&gt; function module not found</ol></ol><ol><ol>Q: The above message appears in the log during data collection. Is it important; what can be done about it?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A1: If the error message appears in the data collection of a satellite system, please implement Basis Support Package SAPKB70008</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A2: If the error message appears in the data collection of a SAP Solution Manager system which is monitoring itself, you can only implement&#160;&#160;SAPKB70008 after SAPKITL416 also has become available, on 14.08.2006. Until then this error can be ignored.</ol></ol><ol><ol>51. SYSTEM_SWITCH_GET</ol></ol><ol><ol>Q:During a data collection for a service session message&#160;&#160;' function module not found ' was raised for this function module.</ol></ol><ol><ol>What can be done?</ol></ol><ol><ol>A:This function module only exists in systems with SAP_BASIS 701 and lower. The service definitions at SAP will be adapted to reflect this, in Q4 2011, once this is done, the function module will not be called anymore in inappropriate systems.</ol></ol><ol><ol>Until then, please disregard this message for systems on SAP_BASIS 702 and higher .</ol></ol>\r\n<p><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SER (SAP Support Services)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D073660)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D073660)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000781680/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000781680/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "934045", "RefComponent": "SV-SMG-SDD", "RefTitle": "Composite note: SDCCN modules for MS SQL Server 2005", "RefUrl": "/notes/934045"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "866576", "RefComponent": "SV-SMG-SDD", "RefTitle": "Composite note: ST-PI modules for MS SQL Server", "RefUrl": "/notes/866576"}, {"RefNumber": "844607", "RefComponent": "BC-DB-MSS-CCM", "RefTitle": "FM MSQ_DBCC_DOWNLOAD returns empty results table", "RefUrl": "/notes/844607"}, {"RefNumber": "836323", "RefComponent": "BC-DB-MSS-CCM", "RefTitle": "MSQ_SPINFO_DOWNLOAD writes database error 50000 to syslog", "RefUrl": "/notes/836323"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "713674", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/713674"}, {"RefNumber": "712757", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712757"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "597323", "RefComponent": "SV-SMG-SDD", "RefTitle": "TCC basis tools in releases EARLIER than Release 4.0B!", "RefUrl": "/notes/597323"}, {"RefNumber": "560630", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI: Solution Tools plug-in - prerequisite not met!", "RefUrl": "/notes/560630"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "178631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178631"}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095"}, {"RefNumber": "1095227", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1095227"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "866576", "RefComponent": "SV-SMG-SDD", "RefTitle": "Composite note: ST-PI modules for MS SQL Server", "RefUrl": "/notes/866576 "}, {"RefNumber": "836323", "RefComponent": "BC-DB-MSS-CCM", "RefTitle": "MSQ_SPINFO_DOWNLOAD writes database error 50000 to syslog", "RefUrl": "/notes/836323 "}, {"RefNumber": "844607", "RefComponent": "BC-DB-MSS-CCM", "RefTitle": "FM MSQ_DBCC_DOWNLOAD returns empty results table", "RefUrl": "/notes/844607 "}, {"RefNumber": "934045", "RefComponent": "SV-SMG-SDD", "RefTitle": "Composite note: SDCCN modules for MS SQL Server 2005", "RefUrl": "/notes/934045 "}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095 "}, {"RefNumber": "597323", "RefComponent": "SV-SMG-SDD", "RefTitle": "TCC basis tools in releases EARLIER than Release 4.0B!", "RefUrl": "/notes/597323 "}, {"RefNumber": "560630", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI: Solution Tools plug-in - prerequisite not met!", "RefUrl": "/notes/560630 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}