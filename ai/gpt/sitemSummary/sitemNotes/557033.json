{"Request": {"Number": "557033", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 284, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015292422017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000557033?language=E&token=B4716206C00239DDB48FB84E31DA8B62"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000557033", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000557033/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "557033"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.11.2002"}, "SAPComponentKey": {"_label": "Component", "value": "PSM-FG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Federal Government Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Public Sector Management", "value": "PSM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Federal Government Functions", "value": "PSM-FG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM-FG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "557033 - Advance delivery of FACTS I/II reports with 2002 requirments"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to get an advance delivery of FACTS I/II reports using the<br />latest 2002 FACTS II attributes in the budgetary ledger.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RFACTS2_UPL_MAF<br />RFACTS2_FOOTNOTE<br />RFACTS2_FILE_SEND<br />RFACTS2_TB<br />RFACTS2_EDITS<br />RFACTS1_TB<br />RFACTS1_FILE_SEND<br />FACTS<br />MAF<br />Trial Balance</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are using the SAP US-Federal Government functionality.<br />You have alreay applied note 547156.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />Transport PI7K009576 contains the advance delivery. Please see attached note 13719 how to download and import the necessary files from sapserv.<br />The transport files are located on Sapserv in directory /home/<USER>/general/R3server/abap/note.557033</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PSM-FM-IS (Information System)"}, {"Key": "Responsible                                                                                         ", "Value": "I803615"}, {"Key": "Processor                                                                                           ", "Value": "I803615"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000557033/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "716457", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS II extract corrections", "RefUrl": "/notes/716457"}, {"RefNumber": "712819", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS II - Add edit 8 step to IMG", "RefUrl": "/notes/712819"}, {"RefNumber": "707903", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS II - Advance delivery of Edit 8", "RefUrl": "/notes/707903"}, {"RefNumber": "671454", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS I Attributes Exch Indicator & Custodial Indicator", "RefUrl": "/notes/671454"}, {"RefNumber": "547156", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of Report SF-224 and Enhancement of BL", "RefUrl": "/notes/547156"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "707903", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS II - Advance delivery of Edit 8", "RefUrl": "/notes/707903 "}, {"RefNumber": "716457", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS II extract corrections", "RefUrl": "/notes/716457 "}, {"RefNumber": "712819", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS II - Add edit 8 step to IMG", "RefUrl": "/notes/712819 "}, {"RefNumber": "671454", "RefComponent": "PSM-FG-IS", "RefTitle": "FACTS I Attributes Exch Indicator & Custodial Indicator", "RefUrl": "/notes/671454 "}, {"RefNumber": "547156", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of Report SF-224 and Enhancement of BL", "RefUrl": "/notes/547156 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-PS", "From": "462", "To": "462", "Subsequent": ""}, {"SoftwareComponent": "ISPSADIN", "From": "10A", "To": "10A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ISPSADIN 10A", "SupportPackage": "SAPK-10A08INISPSADIN", "URL": "/supportpackage/SAPK-10A08INISPSADIN"}, {"SoftwareComponentVersion": "IS-PS 462", "SupportPackage": "SAPKIPS409", "URL": "/supportpackage/SAPKIPS409"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}