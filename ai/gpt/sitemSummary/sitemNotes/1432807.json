{"Request": {"Number": "1432807", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 974, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016966172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001432807?language=E&token=33DC8C5DC73250AEFD6082727EEBCC78"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001432807", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001432807/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1432807"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.10.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1432807 - IBM i: Applying a saved kernel using APYSIDKRN"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Consulting question:<br />1. How do I apply a complete kernel that was saved by APYSIDKRN again using APYSIDKRN?<br />2. How do I apply an complete kernel from the individual SAR archives of SAP Service Marketplace (SMP) using APYSIDKRN?</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AS/400, OS400, system i, iSeries</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Consulting requirement - Documentation</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>If you use APYSIDKRN to import archives and do not assign the value *NONE to the parameter SAVSAR, the system saves the specified location of the complete kernel that is specified with SAVSAR (all under /sapmnt/&lt;sid&gt;/exe/). We recommend that you do not change the value assignment SAVSAR(*DEFAULT) (7.10 and 7.11) or SAVSAR(*STANDARD) (7.20). In this case, files with the name default_tttttttttt__ccc_ppp.SAR are written to the directory /sapmnt/&lt;sid&gt;/patches/saved and the symbols in the name mean the following: tttttttttt = time stamp (continuously increasing number), ccc = type of program (nuc, uc or jvm), ppp = platform (as400_pase_64, ntamd64, linuxppc64). All objects with the same timestamp tttttttttt TOGETHER form the complete kernel as it existed before you called APYSIDKRN (this may also just be a single file). Saving this is the first step when processing the command APYSIDKRN.<br />If, for whatever reason, you want to return to one of the saved kernels, you must process all of the parts for the same time stamp in one APYSIDKRN command, for example:<br />APYSIDKRN SID(&lt;sid&gt;) ARCHIVES('/sapmnt/&lt;sid&gt;/patches/saved/default_1090176543__nuc_as400_pase_64.SAR' '/sapmnt/&lt;sid&gt;/patches/saved/default_1090176543__uc_as400_pase_64.SAR' '...') CARPATH('...') MODE(*FULLY) ...<br />The parameter MODE(*FULLY) is permitted only in the context of this note.<br /></LI></UL> <UL><LI>It is not always possible to define exactly what a complete kernel is made up of because customers use different SAP software components. In general, an ABAP stack with the packages SAPEXE.SAR and SAPEXEDB.SAR (and also IGSEXE.SAR if required) is sufficient for as400_pase_64. For a Java stack, SAPJVM&lt;n&gt;.SAR, SAPEXE.SAR and IGSEXE.SAR are sufficient. Furthermore, for ABAP, you can also add components such as cryptography software (Note 758667 explains how to implement this in the kernel), or for Java, you can add the Java Cryptography Extension (JCE) files from Note 1240081. (We recommend that you save these files once in an SAR archive, and then, if required, import them again directly using SAPCAR.)<br />If you know exactly what SAR archives of the SMP your complete kernel is made up of (and only if this is the case), after you download the archive, you can use the following command to reconstruct a complete kernel:<br />APYSIDKRN SID(&lt;sid&gt;) ARCHIVES('/path and name of the archive' '..' '..') CARPATH(...) MODE(*FULLY).<br />The parameter MODE(*FULLY) is permitted only in the context of this note.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001432807/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432807/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755"}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608"}, {"RefNumber": "1177123", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Reconstructing an ILE kernel library from SAR archive", "RefUrl": "/notes/1177123"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "800791", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: In-place Code Page Conversion ASCII to Unicode", "RefUrl": "/notes/800791 "}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755 "}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608 "}, {"RefNumber": "1177123", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Reconstructing an ILE kernel library from SAR archive", "RefUrl": "/notes/1177123 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.21", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.45", "To": "7.45", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000030", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000030", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000316", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000316", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP024", "SupportPackagePatch": "000024", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}