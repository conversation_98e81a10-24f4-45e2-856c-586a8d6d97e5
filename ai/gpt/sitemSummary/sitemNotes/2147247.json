{"Request": {"Number": "2147247", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 225, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002147247?language=E&token=9DC0D818FC5E4E7D5D2EDA764A8D8654"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002147247", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002147247/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2147247"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 201}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.12.2023"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB-MON"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA Monitoring"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2147247 - FAQ: SAP HANA Statistics Server"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are interested in the SAP HANA statistics server.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p>SAP HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>1. <a target=\"_self\" href=\"#L1\">What is the SAP HANA statistics server?</a><br />2. <a target=\"_self\" href=\"#L2\">Where do I find more information about the SAP HANA statistics server?</a><br />3. <a target=\"_self\" href=\"#L3\">Which indications exist for problems in the area of the SAP HANA statistics server?</a><br />4. <a target=\"_self\" href=\"#L4\">How is the statistics server implemented?</a><br />5. <a target=\"_self\" href=\"#L5\">How can I determine details about the statistics server?</a><br />6. <a target=\"_self\" href=\"#L6\">What are the advantages of the embedded statistics server?</a><br />7. <a target=\"_self\" href=\"#L7\">How can the standalone statistics server be migrated to the embedded statistics server?</a><br />8. <a target=\"_self\" href=\"#L8\">How can the statistics server be configured?</a><br />9. <a target=\"_self\" href=\"#L9\">How can the memory requirements of the statistics server be minimized?</a><br />10. <a target=\"_self\" href=\"#L10\">How can the runtime and CPU requirements of the statistics server actions be analyzed and optimized?</a><br />11. <a target=\"_self\" href=\"#L11\">Why is the history collection for some tables inactive?</a><br />12. <a target=\"_self\" href=\"#L12\">Why are some statistics server actions disabled?</a><br />13. <a target=\"_self\" href=\"#L13\">How can I take care for disabled statistics server actions?</a><br />14. <a target=\"_self\" href=\"#L14\">How can I check for problems in the area of the statistics server?</a><br />15. <a target=\"_self\" href=\"#L15\">What is a good retention time for statistics server histories?</a><br />16. <a target=\"_self\" href=\"#L16\">What can I do if a statistics server action fails with \"301: unique constraint violated\"?</a><br />17. <a target=\"_self\" href=\"#L17\">Is it possible to mark an alert as acknowledged or handled?</a><br />18. <a target=\"_self\" href=\"#L18\">Are the default alert thresholds generally sufficient?</a><br />19. <a target=\"_self\" href=\"#L19\">How can the statistics server check intervals be adjusted?</a><br />20. <a target=\"_self\" href=\"#L20\">How can internal statistics server errors be analyzed?</a><br />21. <a target=\"_self\" href=\"#L21\">What happens if the STATISTICS_SCHEDULE table is manually modified in a wrong way?</a><br />22. <a target=\"_self\" href=\"#L22\">How can I analyze and resolve SAP HANA alerts?</a><br />23. <a target=\"_self\" href=\"#L23\">Is it possible to restart the statistics server?</a><br />24. <a target=\"_self\" href=\"#L24\">What is the difference of SNAPSHOT_ID and SERVER_TIMESTAMP in histories?</a><br />25. <a target=\"_self\" href=\"#L25\">Why do different statistics server actions run in the system database and tenants?</a><br />26. <a target=\"_self\" href=\"#L26\">How can I monitor and tackle issues with event acknowledgement?</a><br />27. <a target=\"_self\" href=\"#L27\">How can I check the state of the statistics server and restart it if required?</a><br />28. <a target=\"_self\" href=\"#L28\">Can Native Storage Extension (NSE) be used in context of the statistics server tables?</a><br />29. <a target=\"_self\" href=\"#L29\">Are there SQLScript version requirements for the statistics server?</a><br />30. <a target=\"_self\" href=\"#L30\">Do statistics server histories also contain information for remote system replication sites?</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L1\"></a>&#65279;1. What is the SAP HANA statistics server?</h3>\r\n<p>The SAP HANA statistics server (or: statistics service) is a central tool for monitoring the SAP HANA database. Among others it performs the following tasks:</p>\r\n<ul>\r\n<li>Regular check for critical situations and generation of alerts</li>\r\n<li>Regular history collection&#160;of monitoring information into&#160;histories located in the _SYS_STATISTICS schema.</li>\r\n</ul>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L2\"></a>&#65279;2. Where do I find more information about the SAP HANA statistics server?</h3>\r\n<p>See section \"The Statistics Service\" in the <a target=\"_blank\" href=\"http://help.sap.com/hana/SAP_HANA_Administration_Guide_en.pdf\">SAP HANA Administration Guide</a>&#160;for more information about the statistics server.</p>\r\n<p>The definition of views used by the embedded statistics server can be found in the <a target=\"_blank\" href=\"http://help.sap.com/saphelp_hanaplatform/helpdata/en/d2/34eedbd29510148efbf332391de7fd/content.htm\">Embedded Statistics Server Views Reference</a>.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L3\"></a>&#65279;3. Which indications exist for problems in the area of the SAP HANA statistics server?</h3>\r\n<p>The following SAP HANA alerts indicate problems in the SAP HANA statistics server area:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Alert</strong></td>\r\n<td style=\"text-align: center;\"><strong>Name</strong></td>\r\n<td style=\"text-align: center;\"><strong>SAP Note&#160;</strong></td>\r\n<td style=\"text-align: center;\"><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: right;\">0</td>\r\n<td>Internal statistics server problem</td>\r\n<td style=\"text-align: right;\"><a target=\"_blank\" href=\"/notes/2081609\">2081609</a></td>\r\n<td>Identifies internal statistics server problem.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><em>SQL: \"HANA_Configuration_MiniChecks\"</em> (SAP Notes <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>, <a target=\"_blank\" href=\"/notes/1999993\">1999993</a>) returns a potentially critical issue (C = 'X') for one of the following individual checks:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Check ID</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0710</td>\r\n<td>Open alerts (high priority)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0711</td>\r\n<td>Open alerts (error state)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0712</td>\r\n<td>Internal statistics server errors (last day)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0715</td>\r\n<td>Number of actions not executed as expected</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0716</td>\r\n<td>Number of statistics server worker threads</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0717</td>\r\n<td>Number of disabled actions</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0718</td>\r\n<td>Number of relevant inactive actions</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0719</td>\r\n<td>Number of actions with unknown state</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0720</td>\r\n<td>Events not acknowledged since &gt;= 1800 s</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0721</td>\r\n<td>Thread call stack collector inactive</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0730</td>\r\n<td>Pending e-mails older than 3 days</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0735</td>\r\n<td>Alerts older than 42 days</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0736</td>\r\n<td>Alerts reported frequently</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0740</td>\r\n<td>Time since statistics server run (s)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0744</td>\r\n<td>Total SQL cache share of statistics server (%)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0745</td>\r\n<td>Total size of statistics server tables (GB)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0746</td>\r\n<td>Total memory share of statistics server (%)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0747</td>\r\n<td>Number of zero entries in HOST_SQL_PLAN_CACHE</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0748</td>\r\n<td>History of M_CS_UNLOADS collected</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0749</td>\r\n<td>History of M_RECORD_LOCKS collected</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0750</td>\r\n<td>Stat. server tables with retention &lt; 42 days</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0751</td>\r\n<td>Historic thread samples save interval (s)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0752</td>\r\n<td>Historic thread call stacks interval (s)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0753</td>\r\n<td>Retention time of table disk sizes (days)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0755</td>\r\n<td>Embedded statistics server used</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0760</td>\r\n<td>Status of embedded statistics server migration</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0762</td>\r\n<td>Executed DDL statements (short-term)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0764</td>\r\n<td>Avg. manual stat. server sessions (short-term)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0765</td>\r\n<td>Log segment size of statisticsserver (MB)</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0766</td>\r\n<td>Workload class for statistics server in place</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0770</td>\r\n<td>Number of stat. server tables not on master</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0780</td>\r\n<td>Unknown entries in HOST_OBJECT_LOCK_STATISTICS</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">M0781</td>\r\n<td>Orphan entry for alert 32</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L4\"></a>&#65279;4. How is the statistics server implemented?</h3>\r\n<p>The statistics server is either implemented embedded (ESS) or standalone (SSS):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Statistics server type</strong></td>\r\n<td style=\"text-align: center;\"><strong>Shortcut</strong></td>\r\n<td style=\"text-align: center;\"><strong>Default</strong></td>\r\n<td style=\"text-align: center;\"><strong>Recommended</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Standalone statistics server</td>\r\n<td style=\"text-align: center;\">SSS</td>\r\n<td>Rev. &lt;= 1.00.92</td>\r\n<td>Rev. &lt;= 1.00.73</td>\r\n<td>\r\n<p>Old approach using a dedicated process (statisticsserver), various disadvantages:</p>\r\n<ul>\r\n<li>Increased memory requirements</li>\r\n<li>Unnecessary large history data collections (e.g. HOST_SQL_PLAN_CACHE)</li>\r\n<li>Important history data not collected (e.g. HOST_SERVICE_THREAD_SAMPLES)</li>\r\n<li>No shared resources with indexserver process, so increased risk of OOM and instabilities</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Embedded statistics server</td>\r\n<td style=\"text-align: center;\">ESS</td>\r\n<td>Rev. &gt;= 1.00.93</td>\r\n<td>Rev. &gt;= 1.00.74</td>\r\n<td>New approach embedded into indexserver process</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><em>SQL: \"HANA_TraceFiles_MiniChecks\"</em>&#160;(SAP Notes&#160;<a target=\"_blank\" href=\"/notes/1969700\">1969700</a>,&#160;<a target=\"_blank\" href=\"/notes/2380176\">2380176</a>) reports one of the following check IDs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Check ID</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">T0101</td>\r\n<td>Unique constraint violation</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">T0110</td>\r\n<td>Insert into HOST_SERVICE_THREAD_SAMPLES failed</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">T0112</td>\r\n<td>Error sending e-mail</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L5\"></a>&#65279;5. How can I determine details about the statistics server?</h3>\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>&#160;provides the following SQL statements for retrieving details about the statistics server:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>SQL statement</strong></td>\r\n<td style=\"text-align: center;\"><strong>Statistics server type</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_Alerts_Current\"</em></td>\r\n<td style=\"text-align: center;\">all</td>\r\n<td>Overview of currently reported alerts</td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_Alerts_Definition\"</em></td>\r\n<td style=\"text-align: center;\">all</td>\r\n<td>Available alerts including description and recommendation</td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_Alerts_History\"</em></td>\r\n<td style=\"text-align: center;\">all</td>\r\n<td>Overview of alerts reported in history</td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_Histories_RetentionTime\"</em></td>\r\n<td style=\"text-align: center;\">all</td>\r\n<td>Retention time and actual data of statistics server histories</td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_MailProcessing\"</em></td>\r\n<td style=\"text-align: center;\">ESS</td>\r\n<td>Overview of existing e-mail notifications, amount can increase considerably if e-mail is configured improperly, see SAP Note <a target=\"_blank\" href=\"/notes/2133799\">2133799</a></td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_PrimaryKeysOnHistoryTables\"</em></td>\r\n<td style=\"text-align: center;\">all</td>\r\n<td>Checks&#160;for primary keys are defined on statistics server histories, can be removed in exceptional situations as described in SAP Note <a target=\"_blank\" href=\"/notes/2143679\">2143679</a></td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_Properties\"</em></td>\r\n<td style=\"text-align: center;\">ESS</td>\r\n<td>General properties, can be used to check if migration to ESS was successful</td>\r\n</tr>\r\n<tr>\r\n<td><em>SQL: \"HANA_StatisticsServer_Schedule\"</em></td>\r\n<td style=\"text-align: center;\">ESS</td>\r\n<td>Schedule of statistics server actions (e.g. frequency, last run, runtime per hour)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Statistics server alerts can be monitored and configured in SAP HANA Studio -&gt; Administration -&gt; Alerts.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L6\"></a>&#65279;6. What are the advantages of the embedded statistics server?</h3>\r\n<p>The main advantages of the embedded statistics server are:</p>\r\n<ul>\r\n<li>Reduced memory footprint due to more efficient data processing</li>\r\n<li>More comprehensive history collections (e.g. HOST_SERVICE_THREAD_SAMPLES, see SAP Note <a target=\"_blank\" href=\"/notes/2114710\">2114710</a>)</li>\r\n<li>Elimination of instabilities and stuck situations caused by indexserver / statisticsserver process separation</li>\r\n<li>Updated alert checks</li>\r\n<li>Error-prone manual adjustments of parameters (see e.g. SAP Note <a target=\"_blank\" href=\"/notes/2084747\">2084747</a>) no longer required</li>\r\n<li>Prerequisite for specific SAP HANA features (e.g. keeping row store in memory during restart, see SAP Note <a target=\"_blank\" href=\"/notes/2159435\">2159435</a>)</li>\r\n<li>Extended retention time of 42 days for most tables</li>\r\n<li>Standalone statistics server is no longer maintained by SAP</li>\r\n</ul>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L7\"></a>&#65279;7. How can the standalone statistics server be migrated to the embedded statistics server?</h3>\r\n<p>The standalone statistics server is typically active when the following parameter is set:</p>\r\n<pre>statisticsserver.ini -&gt; [statisticsserver_general] -&gt; active = true</pre>\r\n<p>A manually triggered migration to the embedded statistics server can be performed based on SAP Notes <a target=\"_blank\" href=\"/notes/2092033\">2092033</a>&#160;and <a target=\"_blank\" href=\"/notes/1917938\">1917938</a>. See also SAP Note <a target=\"_blank\" href=\"/notes/1925684\">1925684</a>&#160;that describes prerequisites from SAP ABAP and Solution Manager perspective that need to be fulfilled.</p>\r\n<p>The main technical activation for the embedded statistics server is the following parameter setting:</p>\r\n<p>System DB, standalone DB:</p>\r\n<pre>nameserver.ini -&gt; [statisticsserver] -&gt; active = true</pre>\r\n<p>Tenant DB:</p>\r\n<pre>indexserver.ini -&gt; [statisticsserver] -&gt; active = true</pre>\r\n<p>When installing or upgrading to SAP HANA 1.00.93 or higher, the embedded statistics server is activated automatically.</p>\r\n<p>If the embedded statistics server disables itself automatically or doesn't collect data, see SAP Notes <a target=\"_blank\" href=\"/notes/2006652\">2006652</a>, <a target=\"_blank\" href=\"/notes/2036630\">2036630</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2091256\">2091256</a> for corrective measures. Additionally you can proceed according to the troubleshooting tree provided in SAP Note <a target=\"_blank\" href=\"/notes/2223237\">2223237</a>. In case of corruptions in statistics server objects you can also consider SAP Note <a target=\"_blank\" href=\"/notes/2343366\">2343366</a>&#160;for repair / re-creation steps.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L8\"></a>&#65279;8. How can the statistics server be configured?</h3>\r\n<p>Some alert settings can be configured in SAP HANA Studio (-&gt; \"Administration\" -&gt; \"Alerts\"). For more specific configurations the&#160;details depend on the type of the statistics server.</p>\r\n<p><span style=\"text-decoration: underline;\">Standalone statistics server:</span></p>\r\n<p>The configuration is done via SAP HANA parameters and should be handled with care. SAP Notes <a target=\"_blank\" href=\"/notes/2084747\">2084747</a>&#160;and <a target=\"_blank\" href=\"/notes/1929538\">1929538</a>&#160;for some examples.</p>\r\n<p><span style=\"text-decoration: underline;\">Embedded statistics server:</span></p>\r\n<p>The configuration is done based on changes of configuration tables. See SAP Notes&#160;<a target=\"_blank\" href=\"/notes/1991615\">1991615</a>&#160;and <a target=\"_blank\" href=\"/notes/2529478\">2529478</a>&#160;for details.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L9\"></a>&#65279;9. How can the memory requirements of the statistics server be minimized?</h3>\r\n<p>The statistics server may collect much more data than required. In order to minimize its memory footprint&#160;you should&#160;perform the following actions based on the type of the statistics server. All proposed changes have no impact on the analysis capabilities, but they can help to reduce the memory footprint significantly.</p>\r\n<p><span style=\"text-decoration: underline;\">General actions:</span></p>\r\n<ul>\r\n<li>Avoid retention times of more than 42 days in order to limit the size of the histories. You can check the currently configured retention times and the actual data retention via <em>SQL: \"HANA_StatisticsServer_Histories_RetentionTime\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>). Exception: Historic table disk sizes (ID 5010) should be retained for a significantly longer time as suggested in check ID M0753 (\"Retention time of table disk sizes (days)\") of the SAP HANA Mini Checks (SAP Note <a target=\"_blank\" href=\"/notes/1999993\">1999993</a>).</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Standalone statistics server:</span></p>\r\n<ul>\r\n<li>If you are at least on&#160;SAP HANA&#160;1.00.74&#160;it is recommended to switch to the embedded statistics server which will no longer use these allocators (see SAP Notes <a target=\"_blank\" href=\"/notes/1917938\">1917938</a>&#160;and <a target=\"_blank\" href=\"/notes/2092033\">2092033</a>&#160;for more information).</li>\r\n<li>If you are already in an OOM situation of the statisticsserver it might be difficult to perform the cleanup operations suggested below, because they fail due to the OOM situation (e.g. \"table update failed\"). In this case you can temporarily increase the SAP HANA parameter statisticsserver.ini -&gt; [memorymanager] -&gt; allocationlimit (e.g. to 10% or 20%) and perform the cleanup activities. Afterwards you should unset the allocationlimit parameter because the default should be sufficient when the&#160;below optimizations are implemented.</li>\r\n<li>Add \"WHERE EXECUTION_COUNT &gt; 0\" to the extraction command for HOST_SQL_PLAN_CACHE as described in SAP Note <a target=\"_blank\" href=\"/notes/2084747\">2084747</a>.</li>\r\n<li>Manually delete existing records with EXECUTION_COUNT = 0 from HOST_SQL_PLAN_CACHE:\r\n<pre>DELETE FROM _SYS_STATISTICS.HOST_SQL_PLAN_CACHE WHERE EXECUTION_COUNT = 0</pre>\r\n</li>\r\n<li>Disable the history collection of M_CS_UNLOADS in HOST_CS_UNLOADS as described in SAP Note <a target=\"_blank\" href=\"/notes/2084747\">2084747</a>.</li>\r\n<li>Check according to SAP Note <a target=\"_blank\" href=\"/notes/2084747\">2084747</a>&#160;if there are other unnecessarily large histories and adjust the data collection if required.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Embedded statistics server:</span></p>\r\n<ul>\r\n<li>Manually delete existing records with EXECUTION_COUNT = 0 from HOST_SQL_PLAN_CACHE:\r\n<pre>DELETE FROM _SYS_STATISTICS.HOST_SQL_PLAN_CACHE_BASE WHERE EXECUTION_COUNT = 0</pre>\r\n</li>\r\n<li>If you are below Revision 85, you should perform this DELETE operation on a regular basis (e.g. weekly).</li>\r\n<li>Once you have upgraded to Revision 85 or higher, the embedded statistics server will massively reduce the collected data of HOST_SQL_PLAN_CACHE and no manual intervention is required.</li>\r\n<li>Disable the data collection of M_CS_UNLOADS in HOST_CS_UNLOADS (if it is still active):\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET STATUS = 'Inactive' WHERE ID = 5035;\r\nTRUNCATE TABLE _SYS_STATISTICS.HOST_CS_UNLOADS_BASE</pre>\r\n</li>\r\n</ul>\r\n<ul>\r\n<li>Disable the data collection of M_RECORD_LOCKS in HOST_RECORD_LOCKS (if it is still active):\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET STATUS = 'Inactive' WHERE ID = 5033;\r\nTRUNCATE TABLE _SYS_STATISTICS.HOST_RECORD_LOCKS_BASE</pre>\r\n</li>\r\n</ul>\r\n<ul>\r\n<li>Check \"<a target=\"_self\" href=\"#L26\">How can I monitor and tackle issues with event acknowledgement?</a>\" in order to rule out an increased amount of non-acknowledged events.</li>\r\n</ul>\r\n<ul>\r\n<li>Make sure that there is no high amount of records for single alerts in table STATISTICS_ALERTS_BASE, e.g. via <em>SQL: \"HANA_StatisticsServerAlerts_FrequentAlerts_ESS\" (RETAINED_RECORDS_PER_ALERT = 500000)</em> available via SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>. Particularly high number of records can exist due to a bug with alert 65 that is fixed with Rev. ********** and 1.00.101.</li>\r\n<li>\r\n<p>On SAP HANA SPS 09 and below you can implement SAP Note <a target=\"_blank\" href=\"/notes/2186299\">2186299</a>&#160;in order to make sure that the size of heap allocator Pool/RowEngine/MonitorView doesn't grow unnecessarily.</p>\r\n</li>\r\n<li>\r\n<p>A large size of HOST_LOAD_HISTORY_SERVICE and HOST_LOAD_HISTORY_HOST can be caused by a small setting of parameter global.ini -&gt; [resource_tracking] -&gt; load_monitor_granularity. Make sure that at least a value of 1000 ms is used for that setting. See SAP Note <a target=\"_blank\" href=\"/notes/2222110\">2222110</a>&#160;for more information.</p>\r\n</li>\r\n<li>If HOST_OBJECT_LOCK_STATISTICS contains a very large number of entries, you can increase the collection interval of the related ID 5032, e.g. from one minute to 10 minutes. Starting with Rev. *********** the data collection is changed so that only long running lock waits (&gt;= 1 s) are&#160;historicized instead of all.</li>\r\n</ul>\r\n<ul>\r\n<li>The transactional lock history in HOST_OBJECT_LOCK_STATISTICS may contain a high amount of entries for OBJECT_NAME = '(unknown)'. They typically refer to already dropped temporary tables and can be deleted. This problem is fixed starting with SAP HANA 2.0 SPS 00. You can consider using SAP HANACleaner (SAP Note <a target=\"_blank\" href=\"/notes/2399996\">2399996</a>) to configure an automatic cleanup.\r\n<pre>DELETE FROM _SYS_STATISTICS.HOST_OBJECT_LOCK_STATISTICS_BASE WHERE OBJECT_NAME = '(unknown)'</pre>\r\n</li>\r\n</ul>\r\n<ul>\r\n<li>See SAP Note <a target=\"_blank\" href=\"/notes/2170779\">2170779</a>&#160;and make sure that table old records are deleted from table STATISTICS_ALERTS_BASE, e.g. by executing the following command. Starting with SAP HANA 1.0 SPS 11 the statistics server will automatically purge old alerts after 42 days:\r\n<pre>DELETE FROM _SYS_STATISTICS.STATISTICS_ALERTS_BASE WHERE ALERT_TIMESTAMP &lt; ADD_DAYS(CURRENT_TIMESTAMP, -42)</pre>\r\n</li>\r\n</ul>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L10\"></a>&#65279;10. How can the runtime and CPU requirements of the statistics server actions be analyzed and optimized?</h3>\r\n<p>The analysis of the database requests executed by the statistics server depends on its type.</p>\r\n<p><span style=\"text-decoration: underline;\">Standalone statistics server:</span></p>\r\n<p>You can determine the most expensive database requests on the statistics server schema by running <em>SQL: \"HANA_SQL_SQLCache\" (SCHEMA_NAME = '_SYS_STATISTICS')</em>. Based on the results you can optimize the statistics server configuration or tune the executed SQL statements (see SAP Note <a target=\"_blank\" href=\"/notes/2000002\">2000002</a>). For example,&#160;if the&#160;CATALOG READ privilege is not assigned to the _SYS_STATISTICS user, unnecessary long runtimes on dictionary tables like TABLES are possible.</p>\r\n<p><span style=\"text-decoration: underline;\">Embedded statistics server:</span></p>\r\n<p>All checks and collections triggered by the statistics server are executed with the following wrapper call (statement hash d6fd6678833f9a2e25e7b53239c50e9a):</p>\r\n<pre>call _SYS_STATISTICS.STATISTICS_SCHEDULABLEWRAPPER('Timer', ?, ?, ?, ?)</pre>\r\n<p>You can check its load key figures by executing <em>SQL: \"HANA_SQL_StatementHash_KeyFigures\" (STATEMENT_HASH = 'd6fd6678833f9a2e25e7b53239c50e9a')</em> available via SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>. If you observe a significant&#160;average runtime&#160;(e.g. time per execution &gt;= 500 ms), you can have a closer look at the individual actions by executing <em>SQL: \"HANA_StatisticsServer_Schedule\"</em> (ORDER_BY = 'TIME_PER_H') available via SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>. The following output illustrates a case where the collector for the HOST_BLOCKED_TRANSACTIONS histories is responsible for significant execution times of more than 600 seconds per hour:</p>\r\n<p><img class=\"img-responsive\" alt=\"actionsPerHour.JPG\" height=\"114\" id=\"6CAE8B27F6DB1ED4B4DC149842C6B278\" src=\"data:image/pjpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/4REKRXhpZgAATU0AKgAAAAgABAE7AAIAAAAVAAAISodpAAQAAAABAAAIYJydAAEAAAAqAAAQ2OocAAcAAAgMAAAAPgAAAAAc6gAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEZyYXVlbmRvcmZlciwgTWFydGluAAAABZADAAIAAAAUAAAQrpAEAAIAAAAUAAAQwpKRAAIAAAADMjIAAJKSAAIAAAADMjIAAOocAAcAAAgMAAAIogAAAAAc6gAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADIwMTU6MDM6MjUgMTI6MTc6NDYAMjAxNTowMzoyNSAxMjoxNzo0NgAAAEYAcgBhAHUAZQBuAGQAbwByAGYAZQByACwAIABNAGEAcgB0AGkAbgAAAP/hCydodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvADw/eHBhY2tldCBiZWdpbj0n77u/JyBpZD0nVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkJz8+DQo8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIj48cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPjxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSJ1dWlkOmZhZjViZGQ1LWJhM2QtMTFkYS1hZDMxLWQzM2Q3NTE4MmYxYiIgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIi8+PHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9InV1aWQ6ZmFmNWJkZDUtYmEzZC0xMWRhLWFkMzEtZDMzZDc1MTgyZjFiIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iPjx4bXA6Q3JlYXRlRGF0ZT4yMDE1LTAzLTI1VDEyOjE3OjQ2LjIyMjwveG1wOkNyZWF0ZURhdGU+PC9yZGY6RGVzY3JpcHRpb24+PHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9InV1aWQ6ZmFmNWJkZDUtYmEzZC0xMWRhLWFkMzEtZDMzZDc1MTgyZjFiIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iPjxkYzpjcmVhdG9yPjxyZGY6U2VxIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+PHJkZjpsaT5GcmF1ZW5kb3JmZXIsIE1hcnRpbjwvcmRmOmxpPjwvcmRmOlNlcT4NCgkJCTwvZGM6Y3JlYXRvcj48L3JkZjpEZXNjcmlwdGlvbj48L3JkZjpSREY+PC94OnhtcG1ldGE+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw/eHBhY2tldCBlbmQ9J3cnPz7/2wBDAAcFBQYFBAcGBQYIBwcIChELCgkJChUPEAwRGBUaGRgVGBcbHichGx0lHRcYIi4iJSgpKywrGiAvMy8qMicqKyr/2wBDAQcICAoJChQLCxQqHBgcKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKir/wAARCAB3BCoDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD3yG48QNrLRXGmaammbm23KajI0xHYmIwBQT3G/j3pllc+JHkuBqGk6VAixsYGg1SSUyP2DA267Qe5BbHoani1CWTxNd6cVTyYbOGdWwdxZ3lUgn0xGO3rXJzeO/EFous/avDumhtMurezxHq8jCWWZodvJthhQJgSeTlcYPWha6f12HbU6S2uvE7WFy93pGkxXa4+zRRarK8cnrvc26lMdsK2fagXXif+yTIdI0n+0fNwLf8AtWXyfLx97zfs+d2f4dmPem6Drl1qF7f6dq1jFY6jYGNpI4LkzxPHICUdXKITyrAgqMFT1GCdugRjT3XiddOtntdI0mS8bP2iGTVZUjj9Nji3JfPfKrj3pb258Sobf+ztJ0qcNGDcefqkkXlv3C4t23j3O3PoK2KztU1CWyvdKhiVCt5eGCQsDkL5Mj5HPXKD8M0dQ6Ec9x4gXWEjtdM02TTSV33EmoyJMB/EREICpx2+cZ9qIbjxA2stFcaZpqaZubbcpqMjTEdiYjAFBPcb+PesW58fw2fiDV9IubFoprEx/ZXeUBb0FYzJt44ZPNXK88EH1w7U/HX9napqtqNO8yKyspp7ecz7VuZoUV5IR8p24Dr83P8AHx8ppdvv/r+vIdunoatlc+JHkuBqGk6VAixsYGg1SSUyP2DA267Qe5BbHoaS2uvE7WFy93pGkxXa4+zRRarK8cnrvc26lMdsK2fasMfERZGl8nTCyw6LJqUpafGyVMbrc/L1G4ZbtkcVp+D/ABNL4osZbstorxIyqraRqxvgDjJVz5SbSMjjnr2p2/r5tfoK+z7lkXXif+yTIdI0n+0fNwLf+1ZfJ8vH3vN+z53Z/h2Y96J7rxOunWz2ukaTJeNn7RDJqsqRx+mxxbkvnvlVx71s0UAY97c+JUNv/Z2k6VOGjBuPP1SSLy37hcW7bx7nbn0FPnuPEC6wkdrpmmyaaSu+4k1GRJgP4iIhAVOO3zjPtWrRQBlQ3HiBtZaK40zTU0zc225TUZGmI7ExGAKCe438e9MsrnxI8lwNQ0nSoEWNjA0GqSSmR+wYG3XaD3ILY9DWxRQBjW114nawuXu9I0mK7XH2aKLVZXjk9d7m3UpjthWz7UC68T/2SZDpGk/2j5uBb/2rL5Pl4+95v2fO7P8ADsx71s0UAY0914nXTrZ7XSNJkvGz9ohk1WVI4/TY4tyXz3yq496W9ufEqG3/ALO0nSpw0YNx5+qSReW/cLi3bePc7c+grYooAyp7jxAusJHa6ZpsmmkrvuJNRkSYD+IiIQFTjt84z7UQ3HiBtZaK40zTU0zc225TUZGmI7ExGAKCe438e9atFAGPZXPiR5LgahpOlQIsbGBoNUklMj9gwNuu0HuQWx6GktrrxO1hcvd6RpMV2uPs0UWqyvHJ673NupTHbCtn2rZooAxhdeJ/7JMh0jSf7R83At/7Vl8ny8fe837Pndn+HZj3onuvE66dbPa6RpMl42ftEMmqypHH6bHFuS+e+VXHvWzRQBj3tz4lQ2/9naTpU4aMG48/VJIvLfuFxbtvHudufQU+e48QLrCR2umabJppK77iTUZEmA/iIiEBU47fOM+1atFAGVDceIG1lorjTNNTTNzbblNRkaYjsTEYAoJ7jfx70yyufEjyXA1DSdKgRY2MDQapJKZH7BgbddoPcgtj0NbFFAGNbXXidrC5e70jSYrtcfZootVleOT13ubdSmO2FbPtQLrxP/ZJkOkaT/aPm4Fv/asvk+Xj73m/Z87s/wAOzHvWzRQBjT3XiddOtntdI0mS8bP2iGTVZUjj9Nji3JfPfKrj3pb258Sobf8As7SdKnDRg3Hn6pJF5b9wuLdt49ztz6CtiigDKnuPEC6wkdrpmmyaaSu+4k1GRJgP4iIhAVOO3zjPtRDceIG1lorjTNNTTNzbblNRkaYjsTEYAoJ7jfx71q0UAY9lc+JHkuBqGk6VAixsYGg1SSUyP2DA267Qe5BbHoaS2uvE7WFy93pGkxXa4+zRRarK8cnrvc26lMdsK2fatmigDGF14n/skyHSNJ/tHzcC3/tWXyfLx97zfs+d2f4dmPeie68Trp1s9rpGkyXjZ+0QyarKkcfpscW5L575Vce9bNFAGPe3PiVDb/2dpOlThowbjz9Uki8t+4XFu28e5259BT57jxAusJHa6ZpsmmkrvuJNRkSYD+IiIQFTjt84z7Vq0UAZUNx4gbWWiuNM01NM3NtuU1GRpiOxMRgCgnuN/HvTLK58SPJcDUNJ0qBFjYwNBqkkpkfsGBt12g9yC2PQ1sUUAY1tdeJ2sLl7vSNJiu1x9mii1WV45PXe5t1KY7YVs+1AuvE/9kmQ6RpP9o+bgW/9qy+T5ePveb9nzuz/AA7Me9bNFAGNPdeJ1062e10jSZLxs/aIZNVlSOP02OLcl898quPelvbnxKht/wCztJ0qcNGDcefqkkXlv3C4t23j3O3PoK2KKAMqe48QLrCR2umabJppK77iTUZEmA/iIiEBU47fOM+1ENx4gbWWiuNM01NM3NtuU1GRpiOxMRgCgnuN/HvUlxqEsXiSx09VQxXFtPM7EHcCjRAY9v3hz+FYcHiHxXL4luNIPh/R1NvBFcPKNalOUkaRVwPsv3v3RyOnI5NAW0uatlc+JHkuBqGk6VAixsYGg1SSUyP2DA267Qe5BbHoaS2uvE7WFy93pGkxXa4+zRRarK8cnrvc26lMdsK2fasaLx+bnTbqe20sm40y1mm1W3kn2/Y5I1OIt207mYqSDgDZ8/dQc63+KTS6Fqeo/ZdHnWxjgcT2Os+faAyybNkk/kjy2XhiNrYUg9xSH/X/AADqhdeJ/wCyTIdI0n+0fNwLf+1ZfJ8vH3vN+z53Z/h2Y96J7rxOunWz2ukaTJeNn7RDJqsqRx+mxxbkvnvlVx707wzrEuu6Ot/I2luruwjfStQN7CwHGfM8tOc5BGOMda16poSMe9ufEqG3/s7SdKnDRg3Hn6pJF5b9wuLdt49ztz6Cnz3HiBdYSO10zTZNNJXfcSajIkwH8REQgKnHb5xn2rVopAZUNx4gbWWiuNM01NM3NtuU1GRpiOxMRgCgnuN/HvTLK58SPJcDUNJ0qBFjYwNBqkkpkfsGBt12g9yC2PQ1sUUAY1tdeJ2sLl7vSNJiu1x9mii1WV45PXe5t1KY7YVs+1AuvE/9kmQ6RpP9o+bgW/8Aasvk+Xj73m/Z87s/w7Me9bNFAGNPdeJ1062e10jSZLxs/aIZNVlSOP02OLcl898quPelvbnxKht/7O0nSpw0YNx5+qSReW/cLi3bePc7c+grYooAyp7jxAusJHa6ZpsmmkrvuJNRkSYD+IiIQFTjt84z7UQ3HiBtZaK40zTU0zc225TUZGmI7ExGAKCe438e9SXGoSxeJLHT1VDFcW08zsQdwKNEBjnp+8OfwrBj8Y6hdeKrnRbOz0dJYXdVgvdWeC7kVR/rBAIGzGT0YMRjnqCoA6GvZXPiR5LgahpOlQIsbGBoNUklMj9gwNuu0HuQWx6GktrrxO1hcvd6RpMV2uPs0UWqyvHJ673NupTHbCtn2rP0TxFr9/q2oW+q6PpVja6bN5N1cRatJKQfJSUFVa3QEYkUElhjnrjlNK8Vapruk3UumaNbR6hbThWs76+aIGFkDxyblicgsrD5dvB3DPy0novkHU0RdeJ/7JMh0jSf7R83At/7Vl8ny8fe837Pndn+HZj3onuvE66dbPa6RpMl42ftEMmqypHH6bHFuS+e+VXHvWVoni/VL/S9Fv8AU9Hs7SHWpIlthb6g07KrxPJlwYUwRtAwCep545i1fx7Jp1sZUs7KFFvp7R7nUr82trH5ZwC8wjfazH7qkYOCN2cAt6Oz/rb/ADQf1+f+TNu9ufEqG3/s7SdKnDRg3Hn6pJF5b9wuLdt49ztz6Cnz3HiBdYSO10zTZNNJXfcSajIkwH8REQgKnHb5xn2q5p0891psE92luksiBmW2nM0fPTa5Vdwx32irNPZi3RlQ3HiBtZaK40zTU0zc225TUZGmI7ExGAKCe438e9MsrnxI8lwNQ0nSoEWNjA0GqSSmR+wYG3XaD3ILY9DWxRSGY1tdeJ2sLl7vSNJiu1x9mii1WV45PXe5t1KY7YVs+1AuvE/9kmQ6RpP9o+bgW/8Aasvk+Xj73m/Z87s/w7Me9bNFAGNPdeJ1062e10jSZLxs/aIZNVlSOP02OLcl898quPelvbnxKht/7O0nSpw0YNx5+qSReW/cLi3bePc7c+grYooAyp7jxAusJHa6ZpsmmkrvuJNRkSYD+IiIQFTjt84z7UQ3HiBtZaK40zTU0zc225TUZGmI7ExGAKCe438e9atFAGPZXPiR5LgahpOlQIsbGBoNUklMj9gwNuu0HuQWx6GktrrxO1hcvd6RpMV2uPs0UWqyvHJ673NupTHbCtn2rZrida8ey6Hodvezw6aGuNYn04SX18bO3iVGmw7yFXxxEB05LdqQ7df62v8AobouvE/9kmQ6RpP9o+bgW/8Aasvk+Xj73m/Z87s/w7Me9E914nXTrZ7XSNJkvGz9ohk1WVI4/TY4tyXz3yq496w77x+9l4L0vXJbOwtW1C4EH+n6j9ntoxhyJPP8s5RgmVOwbgy8DNS6l4q1+z0Gw1Wy0nRL+C6aGMvDrchTdNKsaFHFsQ6fOpLcd8A45fW3nYS6Gxe3PiVDb/2dpOlThowbjz9Uki8t+4XFu28e5259BT57jxAusJHa6ZpsmmkrvuJNRkSYD+IiIQFTjt84z7VdsHvZLGNtUt4Le6IPmRW87TIvPGHZEJ4x/CK5W08b6hJeCW80a3h0h9Vk0pLqK/LzCRZWiVniMSgKzKBw7Ebhx1IfWwfZv/X9aG7DceIG1lorjTNNTTNzbblNRkaYjsTEYAoJ7jfx70yyufEjyXA1DSdKgRY2MDQapJKZH7BgbddoPcgtj0NVU8VPL4uudEisV2rC5trp58JcTIFLxY2nGBInPPR+PlNVdN8btqiOtppMz3VlFI2qWoky9pIoOIV4/eOxHy/dBXDZ5UGel/L+n/Xl3Q+v9f1/XkadtdeJ2sLl7vSNJiu1x9mii1WV45PXe5t1KY7YVs+1AuvE/wDZJkOkaT/aPm4Fv/asvk+Xj73m/Z87s/w7Me9UvCPii58SpPLImiiKEAMNO1VrqSJz/BKhhj8th6HnPGKjvPFGp2vw1i8TW2n2l5MliL25gkuWt12CMu2whJCTxgA469ab03CK5tEaM914nXTrZ7XSNJkvGz9ohk1WVI4/TY4tyXz3yq496W9ufEqG3/s7SdKnDRg3Hn6pJF5b9wuLdt49ztz6Csm68bTaPc6DD4g02K1XVFkM9xb3ZlhsyGRY9zMiEhjIq5wNrEDkc1DceL/EJbSn07QNMmg1Wc28DT6tJEyuEkc7lFuwAxE3IJ6jinsxLVX8jfnuPEC6wkdrpmmyaaSu+4k1GRJgP4iIhAVOO3zjPtRDceIG1lorjTNNTTNzbblNRkaYjsTEYAoJ7jfx71gp44um8aN4deHQYbmIxLLFNrZW4fdGJGMUPk5kAyQCSudp6VX8FfEW48Yq/wBn07TyfsX2pRZap9p8p84EM58pfKc54Hzfdb05Qf8AA/E6OyufEjyXA1DSdKgRY2MDQapJKZH7BgbddoPcgtj0NJbXXidrC5e70jSYrtcfZootVleOT13ubdSmO2FbPtUWneKoNSt9KlihCC9tnuZw8gH2VUADhjjBIchccdGPbFZ9j46a60G7vp9La2uLa8hgNq83zGOZo/Lkzt4ykgOMcEMueM0W6f1vYL9TVF14n/skyHSNJ/tHzcC3/tWXyfLx97zfs+d2f4dmPeie68Trp1s9rpGkyXjZ+0QyarKkcfpscW5L575Vce9bNc74u8USeGYbR0hstlw7K91qV6bS1hwOA8ojfazE4UEAHB5zgFNgi1e3PiVDb/2dpOlThowbjz9Uki8t+4XFu28e5259BT57jxAusJHa6ZpsmmkrvuJNRkSYD+IiIQFTjt84z7VE+r3kcWhGaK0WTUbjyphBMZ41XyZJAUfau77i8lRwTx3rOv8AxB4ntvFUOkWug6TMlzFNPBPJq8qExxtGp3KLY4Y+auACRwear7Vg6fL+vyNaG48QNrLRXGmaammbm23KajI0xHYmIwBQT3G/j3pllc+JHkuBqGk6VAixsYGg1SSUyP2DA267Qe5BbHoaw9Q8Y65p2taxaS6Dp72+mae2oecuqPvljPmhBs8jAYmLkbiFByC3SkHxEWRpfJ0wssOiyalKWnxslTG63Py9RuGW7ZHFTsr/ANdf8h2fNb+un+ZuW114nawuXu9I0mK7XH2aKLVZXjk9d7m3UpjthWz7UC68T/2SZDpGk/2j5uBb/wBqy+T5ePveb9nzuz/Dsx71W8H+JpfFFjLdltFeJGVVbSNWN8AcZKufKTaRkcc9e1WfFOvSeHdHF7FbxTEyrGz3M5hghB6ySyBHKIMfe2kAkZwMkOWm4o67BPdeJ1062e10jSZLxs/aIZNVlSOP02OLcl898quPelvbnxKht/7O0nSpw0YNx5+qSReW/cLi3bePc7c+grE1HxhrVn4UtNbtNL0TUI7ieOIm21t3ixJKkcbJILf5xl+eBgDjdU1945OkeIE0jWNPWCR9OW5W4juN8LXB8w/ZwxUHJETFWIG7B4BwCPTVjSb2Nie48QLrCR2umabJppK77iTUZEmA/iIiEBU47fOM+1ENx4gbWWiuNM01NM3NtuU1GRpiOxMRgCgnuN/HvWZo3ifVPENwJ9J0i0OlJL5M11cX7JJvX/WeXGsTBgpyuWZCSp4AwTBofjaXW/Fl5pEaaKi2k80UkS6wXvQI2K7zbeVwCcfx9CPpTs07E30ua9lc+JHkuBqGk6VAixsYGg1SSUyP2DA267Qe5BbHoaS2uvE7WFy93pGkxXa4+zRRarK8cnrvc26lMdsK2fam+MPEX/CKeF7jV/Jhm8mSJNtxceRGPMlWPc0m1tqjdknB4FZuneN3vfCkusyWNuwjvUtFazvPPt5w0iJ5kUuxd6jf/dHKsPektdv6/q43pZmoLrxP/ZJkOkaT/aPm4Fv/AGrL5Pl4+95v2fO7P8OzHvRPdeJ1062e10jSZLxs/aIZNVlSOP02OLcl898quPesjxB4/Tw/eyxz6bJNb297Fb3E0cnMUbQNM0xXHIQKcgHOMntg9DpGqpq8NxLCq+XFcPCjq+4SAYwwPoc0LW9v62/zB6Wv/W/+RWvbnxKht/7O0nSpw0YNx5+qSReW/cLi3bePc7c+gp89x4gXWEjtdM02TTSV33EmoyJMB/EREICpx2+cZ9qy/FHi6fQdWtbGGDTE+0JuW41fUmsoXctgRxuIpA79yvBwQRnnGrJqVzFr2n2MkcSrcWk80u0ltroYgArcZH7xuo5wOlF1a4dbCQ3HiBtZaK40zTU0zc225TUZGmI7ExGAKCe438e9MsrnxI8lwNQ0nSoEWNjA0GqSSmR+wYG3XaD3ILY9DWL4b8b6hq39jS6to1vYW2uQNLYy29+bg7gm/ZIDEm0ldxGCw+U9OM1dd+Jf9jeFdJ1n+zYT/aUSybbi98mODLxr80mw/KPMyTjgL0oem47Xdjo7a68TtYXL3ekaTFdrj7NFFqsrxyeu9zbqUx2wrZ9qBdeJ/wCyTIdI0n+0fNwLf+1ZfJ8vH3vN+z53Z/h2Y96o6L4x/tLwpe61dW0CR2jyKrWV19pgugoBDQybF3gk7fuj5gQM1UufGerr4Im1+00OzeawS4OpWVxqTRtbtDncqMsLbydpIyFBBU96H/kJa7GzPdeJ1062e10jSZLxs/aIZNVlSOP02OLcl898quPelvbnxKht/wCztJ0qcNGDcefqkkXlv3C4t23j3O3PoKyNZ8S+J9F8Pw6jPoGkySPcRwvCmsS4USSJHGQxtufmfkYGAOC2cBb7xfqtqb5odFtLiLR4kfVWGolWiYxiR0hBi/elUIOWMecgcc4PMFqtDZnuPEC6wkdrpmmyaaSu+4k1GRJgP4iIhAVOO3zjPtRDceIG1lorjTNNTTNzbblNRkaYjsTEYAoJ7jfx71hSfES2i1rVNOks2R7URPZyNKAt8rJGz7eOGTzVyvPBB9cdlRsG5j2Vz4keS4GoaTpUCLGxgaDVJJTI/YMDbrtB7kFsehpLa68TtYXL3ekaTFdrj7NFFqsrxyeu9zbqUx2wrZ9qzfDXi+78Q6xPam20q3SAMZbcaoz30HOF822MI2E/75HoWGDV2DVtYvNFnudNsrG5uo724gEVxcvbp5ccroDuCSHdhRxjByelK9lcfX5kouvE/wDZJkOkaT/aPm4Fv/asvk+Xj73m/Z87s/w7Me9E914nXTrZ7XSNJkvGz9ohk1WVI4/TY4tyXz3yq496wU8Z+Ij8PZPFbeHtLEYsxfJbjWJCzQ+WXbJ+zcOMABeQcn5hjl8njy7sLtrTWNIhhuP7MF6gtr0ypJIzuEgBaNDuYLkHHXIxxkuWl79Att/X9bm5e3PiVDb/ANnaTpU4aMG48/VJIvLfuFxbtvHudufQU+e48QLrCR2umabJppK77iTUZEmA/iIiEBU47fOM+1cxN8R5ftmkWsVvoltNqenwXipqmtG1LNKSBHGBC3mEEe3Ucc13lO1vvZPkZUNx4gbWWiuNM01NM3NtuU1GRpiOxMRgCgnuN/HvTLK58SPJcDUNJ0qBFjYwNBqkkpkfsGBt12g9yC2PQ1l2vi+7vPGUuiR22lQ+TKymK61Ro71416ypbeSQyHswfBHcHIG/rOof2ToV/qPleb9jtpJ/L3bd+1S2M4OM464qbpR5uhSV5cqKdtdeJ2sLl7vSNJiu1x9mii1WV45PXe5t1KY7YVs+1AuvE/8AZJkOkaT/AGj5uBb/ANqy+T5ePveb9nzuz/Dsx71ytv8AFJpdC1PUfsujzrYxwOJ7HWfPtAZZNmySfyR5bLwxG1sKQe4pmo/FKXTrXTZXTwxINQW5eO4XxGfsrCHYNqy/Z/nkJcjaF/h6ns3oJa7HWz3XiddOtntdI0mS8bP2iGTVZUjj9Nji3JfPfKrj3pb258Sobf8As7SdKnDRg3Hn6pJF5b9wuLdt49ztz6Cr+nXT32l2t3JbyWrzwpK0Eww8RYA7W9xnBrn/ABF4+0jwxqV3Z6vdWVo8Wni8g+1XiQm5bLjy1DdT8g5GfvDj1JPleoR95XRqT3HiBdYSO10zTZNNJXfcSajIkwH8REQgKnHb5xn2rVrIi1iWbVtJgWNFivrGW5fOSylTFgA+n7w547DpWvTaa0Yk7q6M6LT5U8TXeolk8mazhgUAncGR5WJPGMYkH61zr/DlZl1MXPijW5zqU0NxKzLaArNE0ZjkXbAOQIlGDlSM5BPNb0OkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDS8yr6jtD8PxaK91O17d6je3jq1xeXjJ5km0YVcIqooA6BVHUnkkmtasa20PUILC5t5fFOrXEs2PLuZYrQSW+OuwLAFOe+5W9sUDQ9Q/sk2f/CU6sZ/N3/bvKtPOAx9zHkeXt7/cz70CNms7VNPlvb3SpomQLZ3hnkDE5K+TImBx1y4/DNV59D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmlvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFHUOhU1nwPpOupKL7z98l/FqCSxuA0UqIifKccKVTaQc5DN7Yp33wx8N30BP2QW9+7O0uqQRxrdzbwwcPJs+YMHII6dMYwK2p9IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaF0/rt/kBTk8EaMftn2aJ7T7dFdJceSwG9rjZ5knIPzfu1x268Vd0TR7nSI3S513UNVUqqxi9S3XygP7vlRJnPHXPQdOajstFv7WS4afxPqt4Jo2REnitQICejLsgUkjtuLD1BpLbQ9QgsLm3l8U6tcSzY8u5litBJb467AsAU577lb2xRsgNmisYaHqH9kmz/4SnVjP5u/7d5Vp5wGPuY8jy9vf7mfeifQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75oA2aKx73RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP8As7R6AU+fSL2bWEvI/EWpQQKVJsI47YwtjqCWhMnPfDj2xQBq0VlQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65pllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNAGxRWNbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KBoeof2SbP/hKdWM/m7/t3lWnnAY+5jyPL29/uZ96ANmisafQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBQBsUVlT6Rezawl5H4i1KCBSpNhHHbGFsdQS0Jk574ce2KIdIvYtZa9fxFqU1uWYiweO2EKg9ACIRJgdsv9c0AatFY9lot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KANmisYaHqH9kmz/4SnVjP5u/7d5Vp5wGPuY8jy9vf7mfeifQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75oA2aKx73RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBT59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFAGrRWVDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmmWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0AbFFY1toeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoGh6h/ZJs/8AhKdWM/m7/t3lWnnAY+5jyPL29/uZ96ANmisafQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBQBsUVlT6Rezawl5H4i1KCBSpNhHHbGFsdQS0Jk574ce2KIdIvYtZa9fxFqU1uWYiweO2EKg9ACIRJgdsv8AXNAGrRWPZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDSW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tigDZorGGh6h/ZJs/8AhKdWM/m7/t3lWnnAY+5jyPL29/uZ96J9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmgDZorHvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUAatFZUOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDQBsUVjW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tigaHqH9kmz/AOEp1Yz+bv8At3lWnnAY+5jyPL29/uZ96ANmisafQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP8As7R6AUAT3Gnyy+JLHUFZBFb208LqSdxLtERjjp+7OfwqSPS4ItdudWVpDPcW0Vs6kjaFjaRlIGM5zK2eewqvPpF7NrCXkfiLUoIFKk2EcdsYWx1BLQmTnvhx7Yoh0i9i1lr1/EWpTW5ZiLB47YQqD0AIhEmB2y/1zQg6CSeHLSW31yFpJgutkm4IYZTMKw/Jxx8qA855z9Kfq2hxavoJ0t7m4tk/dlZ4NnmIUZWUjcrLnKjqCKistFv7WS4afxPqt4Jo2REnitQICejLsgUkjtuLD1BpLbQ9QgsLm3l8U6tcSzY8u5litBJb467AsAU577lb2xR0H1LelWFzp1q0V3q15qrs+4TXiQqyjA+UeVGi44zyM89avVjDQ9Q/sk2f/CU6sZ/N3/bvKtPOAx9zHkeXt7/cz70T6HqE2nW1tH4p1aCWHO+6jitDJPnpvDQFBjttVffNAjZorHvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUAatFZUOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDQBsUVjW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tigaHqH9kmz/wCEp1Yz+bv+3eVaecBj7mPI8vb3+5n3oA2aKxp9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmlvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFAE9xp8sviSx1BWQRW9tPC6kncS7REY46fuzn8KoN4U8/WoL/AFDW9SvorW5a6trKcQCKCQhgCpSJZDgOwAZzwec1bn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65oXQHsNl8N2ksWrxtJPs1iVZbkBh2jSMqOOFKxgHqeTyOMR6T4P0TQNUmvNBsYNLE8AhmtrKFIYZMMSHKqo+cZIz6H6YfZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDSW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3til/l/wACwPXQifwla/8ACMabo1teXlqNLEX2S8iZDNEY12hvmUoSVyCCpBDHiox4SlgsYbfTvEer2Dq8ss88Qt3e6eRtzM4khZQc9NiqBnAGMAWRoeof2SbP/hKdWM/m7/t3lWnnAY+5jyPL29/uZ96J9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmm9bsN/6/r+mXNH0q10PRrXTNPUpbWsYjjDHJwKu1j3uiX90bfyPE+q2XkxhHEEVofPI/jbfA2Cf9naPQCnz6Rezawl5H4i1KCBSpNhHHbGFsdQS0Jk574ce2Ke7A1aKyodIvYtZa9fxFqU1uWYiweO2EKg9ACIRJgdsv9c0yy0W/tZLhp/E+q3gmjZESeK1AgJ6MuyBSSO24sPUGkBsUVjW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tigaHqH9kmz/4SnVjP5u/7d5Vp5wGPuY8jy9vf7mfegDZorGn0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AUAbFFZU+kXs2sJeR+ItSggUqTYRx2xhbHUEtCZOe+HHtiiHSL2LWWvX8RalNblmIsHjthCoPQAiESYHbL/XNAGrXPW/hZHitf7QkJez1W41GEQt8reY0oCtkc/LMcgdx1x1s2Wi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0ltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoH5f12/Uot4Gt00+Ky0/VtSsIba7+1WYhMLfYztZfLj8yNh5fzNhW3YzhcAADRl0AXeiQ6dqWo3l8YriK4N1KIlldo5VlUHYipjKgcKOPfmmDQ9Q/sk2f/AAlOrGfzd/27yrTzgMfcx5Hl7e/3M+9E+h6hNp1tbR+KdWglhzvuo4rQyT56bw0BQY7bVX3zRsLqN1G18Uy3zvpOs6PbWhxsiudJlnkXjnLrcoDzn+Efj1rN0vwElncwzahrV9frHePqH2LbHFarcuxdnVVXzMBmYqryOBx1IBrVvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sULRg9VYz4vAHh+C/ttRt7OOHVILj7Q2pRxRrczsc7hJJtyysGII9OmMCtNNEihOrvbXVzBLqsnmySoV3Qv5SRAplSBgRqeQec9uKZDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmmWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0LRW/r+tB9bjNI8NLpmpXGo3Wp3uq388KwG5vFhVljUlggEUaLjLMckE89aqSeEpb/wCH9t4avNTubICzW1upLDyyZV8vYygyxthTnqAG4HIq7baHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KBoeof2SbP/hKdWM/m7/t3lWnnAY+5jyPL29/uZ96HtYE2nciHhK0nsobbV7m51ZY7Oayc3YjHnRSlCwYRooyAigEAcdcnmlsPCVjp2l6FYxT3UkehyeZbvK4Z5D5bx/Occ8SMeMc4qSfQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBTvqLZWIW8MSr4guNUs9f1OzW6ljluLOJbdoZSqquCXiZwCqgHDD2xUemeC9P0dtKfTrm7gk022W0MiuubqFR8qTDbhgCcggAg5wQGYG7PpF7NrCXkfiLUoIFKk2EcdsYWx1BLQmTnvhx7Yoh0i9i1lr1/EWpTW5ZiLB47YQqD0AIhEmB2y/1zSWgMpw+CtMt11hI5LkJq75nTeNqIWLNGgxwrM8hPUkyNz0xVl+HGgol0mjQ/2Et1HGsiaXDDChaOQSJJt2FS4IxkgjBIIPGNOy0W/tZLhp/E+q3gmjZESeK1AgJ6MuyBSSO24sPUGkttD1CCwubeXxTq1xLNjy7mWK0ElvjrsCwBTnvuVvbFIOpC2leI7Wzjh0zxLHPJvZpJ9Y05Z2YHGFUQNAqgYPUEnPXimtomv3tvH/aHiqW0uI2b59GsYoUkQhcBkuPPyQQeQR1xirA0PUP7JNn/AMJTqxn83f8AbvKtPOAx9zHkeXt7/cz70T6HqE2nW1tH4p1aCWHO+6jitDJPnpvDQFBjttVffNMCKPw1FYWfh+x0rbHaaROH2yMdxQQyJxx1y4PYdfpWjNpcE2uWuqs0gntbeW3RQRtKyNGzEjGc5iXHPc/hUvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sU+tw/wAjKuvAovda1HULnxDq7jUbVrOa222wjEJ37UGIdw2mRiDuz0ySOKsyeCNGP2z7NE9p9uiukuPJYDe1xs8yTkH5v3a47deKuQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65pllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNT0+X9Id3e5Wh0LXtOsTFpvimS6lJQK+sWMUyRoAchVgEBycjlmbp05oGj+JLy2aPU/E6W7q6vFNo2nrbsBhgVYTvOrA5B4AII61YttD1CCwubeXxTq1xLNjy7mWK0ElvjrsCwBTnvuVvbFA0PUP7JNn/wAJTqxn83f9u8q084DH3MeR5e3v9zPvTYkZd38P4LnQo9Ki1zVbSP7Uby4lgFvvupzKJd77oSoIdQcIFHXIIq3q3gnTte0y8s9Ynurs3tnDayzsUWQGJnZJV2qAsgZy2QMZAwAOKsz6HqE2nW1tH4p1aCWHO+6jitDJPnpvDQFBjttVffNLe6Jf3Rt/I8T6rZeTGEcQRWh88j+Nt8DYJ/2do9AKLXXL0HdrUq6f4PTSNQE2kazqdnas4kmsEMLwzPjDMd8bOu7AyEZRnnAJJJbeF7vTby6uNM8RX8aTSzTrZTw28lsskmTk4jWUqGbOBIDxjOKuz6Rezawl5H4i1KCBSpNhHHbGFsdQS0Jk574ce2KIdIvYtZa9fxFqU1uWYiweO2EKg9ACIRJgdsv9c0b/ANf18hbIyrnw74i1hI7XxBrmlzWAnineOx0mSCVmjdZEAdriQAbkXPynIyBgnItS+DbB4dUgiubu3ttSYStbxOoSCfdu86LKkq5YBiMlSRnbksTYstFv7WS4afxPqt4Jo2REnitQICejLsgUkjtuLD1BpLbQ9QgsLm3l8U6tcSzY8u5litBJb467AsAU577lb2xR0AhsvCNtbSpcXV9eahdi6+1S3N0Y90zeS0IVlRFQKEbGFUc8nJJzR0/wjqfhmyGneDNUsLLThI8oh1DT5LpkZmJ2qyzx4QDChSCQB1Nag0PUP7JNn/wlOrGfzd/27yrTzgMfcx5Hl7e/3M+9E+h6hNp1tbR+KdWglhzvuo4rQyT56bw0BQY7bVX3zQHkUr7wtqWt2Jg1rxLdKs0Bgu7fTraGK3uEJbPyyrK65VtpKyDpkYrRbSWXXNOuYNiWtnZzW2wsd3zmLbj1AEZzk+lMvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUAZnh/wACWugSWLNqupakmmwmCwivWi2WqkYO0RxpuJAxl9xAzjGTmufhvp0ixR3Gp6nPb2sivZW7vEEswJkl2piMFhmNV+csQowCMk1tw6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65pllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNF3dMfUqy+CdLme7jlaZ9OvZ1uLjS32NavICSx2FSQGOCyg7SRnGSxJF4G0W10nWdL06E6fYaxEY5bWzRIo4iY9jNGoXCsRjPUZAOOubNtoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoGh6h/ZJs/+Ep1Yz+bv+3eVaecBj7mPI8vb3+5n3o22C7vcj8S+GP8AhJbCCzbWNQ06GKRJCtmIP3jI6uhYyRv91kB4xnnORVa98FR3rSb9b1SNLqJItQjiMKrqAVduZP3eVLL8pMRj49MDF2fQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBQLZFPV/Auka1DIl2JlZr6K/jkjYK0MkaIgCHHClU2kHOQze2JILLxgLqM3Wu6JJbhwZI49FmR2XPIDG6IBx32nHoelW59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaA6FTTvCn2TWINS1DWtS1i4tonitjeiAeSr7d+PKiQnO1fvE9Kv6Lp0unWM8M7IzSXdxOChJG2SVnA5HXDDPvUFlot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KHqg/wAyteeDoLnwPD4Wg1S/srKO1Fo8sHkmWWEJsKsXjYcg8kAHPQikh8E6c0ltNq09xq9xbeS0U155YZWhaQxviNEXI81h0xgDvkm0ND1D+yTZ/wDCU6sZ/N3/AG7yrTzgMfcx5Hl7e/3M+9E+h6hNp1tbR+KdWglhzvuo4rQyT56bw0BQY7bVX3zQ9b3DfR/1/wAOUT4J8iS0k0nxBq2ltbWMVj/o62z+bHGSVLebC/PzHkYqe4s/GDXUhtNd0SO3LkxpLosruq54DMLpQTjuFH0FWL3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBT59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFPqBQufCU9zqCXs+v3109vM9zZW13FC1vbTFWCthESRgocgKZORwT3pG0bxLqEE9lrut6VPp91DJDOlnpMkEpVlK/K7XDgEZzyprQh0i9i1lr1/EWpTW5ZiLB47YQqD0AIhEmB2y/wBc0yy0W/tZLhp/E+q3gmjZESeK1AgJ6MuyBSSO24sPUGp6DvqS6tocWr6CdLe5uLZP3ZWeDZ5iFGVlI3Ky5yo6giqCeDYJ7iKbXNTvdbaOC4t8XyQBWim8vejLHEgI/djHH8RznjFm20PUILC5t5fFOrXEs2PLuZYrQSW+OuwLAFOe+5W9sVXn0+8ttM+wv4n1g3Bk8wXwhtPNC9NmPI2Y7/cz71hicVRwsHVrysu78xwi3ZRGLoniLTbaGz0HxDbfZYVKr/a9g95MOTgeYk0WVAwBuDNxksSanj8OSXMd1Lrd4txe3tl9huJLSHyI/L3OQVRmchsSEcsRx0HSq09vdTadbW0fifWoJYc77qOGzMk+em8NCUGO21V980t7Bc3Rt/I8S6zZeTGEcQQ2h88j+Nt8LYJ/2do9AK8x5/lb0deJapVFsi+ujvFrWl3ELqbexsprYhz85LGLaemOkZz06itaufkhuLnW4rqLxBq1vDvXNjHFamFsdQS0Rkwe+HHtiugr0MLjsPjYudCalbexm4OGjRj288p8bX8Blcwpp9s6xljtDGScEgdMkAc+wqzrv2//AIR7Uf7G3f2j9lk+y7dufN2nZjf8vXH3uPWq1uYv+E2vwEcTf2fbFnLjaV8yfAC4yDnPOTnI4GOdO5toL21ltryCO4t5kKSRSoGR1IwQQeCD6V1PYL2n935I4aDX72PwreRpqGrHU4tRgsbl9TitPOsDK0YyPIXyWwkgcH5uW56FQ9dSvIrfWdO1DxLqEf8AZt/FBDqFvbW73VwXhV/J2eUUZsv/AAxg4x7k9Za6FpFjpL6VZaXZW+nOrK9nDbokLBvvAoBgg5OeOaxfEOneE9F8LQ2V94bsbuwFwqWmlQ2ETiSdyQBHGQEDHLHJwANxJAyab/y/T+l6iRRWXxJcf2Jo2parPpN5LZT3NzeW0du0jtGyKqNvRo87ZNz7VxuHykCp9N1u91bQfBuozv5Ut/OpnEJKpIDbTHpn7pIDAc9uuM1DftpJ0jStEuPh3LdxSLLPDo/2ewK2ojYKWKtMIhnzRjYSfmOcc1u6z5KX/h9XibP28iIIwUIfs83UYORjIwMdQc8YL/z/AF/pfIOnyf5GXoc+o+Ida1G8fXrm0i07UZLT+yraOAptjPBlLxtJlx83ysvysuBnJNSx8WazPqtlcTmwOmX+q3GmR2aQOLmExeaPMaTzCrcwkldgwG6nbzf1NvC5+IGm2+o6HbXGtSx7rfUXs43MBXeyJ5p+ZWISUqB/cbp3j05LWP4l30dx4Z0i0v3tPtEerWzB7iePfsxITEpU8Djcw4HNKPRf1pv+T9OgS6/1vt+a9eonhyzEPiTxTpf2vUJbRTBsWe/nlaPfES2x3csnPPykY7YrMstcm8K+BvEl6rXWof2bqc8Nut3dvMwG9VUGSV87QWzyw4zyOtb0Xizw1HqF+Y5DDMkTzz3BspUWdIRhikpQLNtHHyFsVA/i3wxH4Z1bU7WCa50+EGW6W20yVvtAdc71XZ+8Vh/y0GUI5LYBNT3fl+qt+TQ97Lz/AEd/zMpPFHi610iNNa0y10+/vNSisrS5uY0SLa653tFHcS8gqVC+aNxK/dq74gn8W2K+H4bbXNJiuLq+NvcyHSXMcuUkZcIZ8qBtAI3Ek4IIGVNzw9pfhO/8PXEWjeG7az028OLi0m0Y2izHAPzRPGu4cjnBHoara/H4f0XTbTw6ng2PVLK4WWdNNtLW1EKCMqWcpK6J1cHjJzVS93fy+7+r6+fkKOq08/11/LTyK3iDxLr+nXGoxWc+loNE0qPULw3NrJ/phPmZWPEo8ofuj8x8zlhxxzfsNX8Qat4lu4rVdPt9MspoVkSeKRriVXhVyMhgqFS3XDZzjC4yaurXvgiPSdH1TWdFjls47ZZ7Gc6JJcJZRYVgdyRsIQBtPJXp7VsS+INEsdebToxK+oXBjacWVjLNtLDajTPGhCZA4LkcD0FOzT/r+vkK6av5f5f18zbPQ15bF8Q9ct9D02/vdJvVVNKuruWWf7OI7540UqV8t2dByTyq8Hp2rX0i00B9RiLT68l4L+eGGD+3NQuVbyZCpd18wqEJAzvG3LBSTkA7a6x4YisbOdPs6W4sJru1ItSAlsgXzCo2/KMMny8E+hxUva5a+K3mZK694ogk1PTCunatqkNlBd2r2VuYUPmO6lWSSchiNhYfvEDcj5cZqrfeI9en8ESXenaxYwanaajFa3jTaNJGIi0kamPy3mOCBICXDMrD7p5DDR0C28CavY3ujaLomnrZt5c9xZPpH2eKdSTslCPGokUlDhxkccGtO5s9A0fTLPQho1omm6hObVbKG1jEGWVnO5OFwdpzweTVPf8Arv8A0iF/X3f0zUsEvY7GNdUuILi6APmS28DQo3PGEZ3I4x/Ea4Txvreu2PiS4TS7zUbe0s9MS7drWG2eCM75MtOHVpmTCDiEFsA98VoeINN8JaLHZQXA1TT02MltY6DNfRLtU7mYQWZAwC3Llf4gCeRVPUJvhvbQ2P8Aa1tBq7x2gu7ae7tJdUnW3Zi3m+YyyP5YOcsThcjOMil1v/XX+rFdLf1uv+GNG+VY/iH4evLO8vNmoRXHmxfbJTBIqxqVIhLbAec5Cg1VsfFmsz6rZXE/2A6Zf6rcaZHZpA4uYTF5o3tIZCrcwkldgwG6nbzrah4r8NWusQRXshe6iKLHcJZSypCZsKqmZUKIXyvBYEgjsRWlFoGjQazLrEOk2MepyrtkvUtkEzjAGC+Nx4A79hRs79Celuv/AADk4dc17WfAOreIJL2xgs5dPuntra0gkE9uyhgu6fzMFhtOQI1we/HKDxzdxeCtT8QrFG0Vkq26WEqFbmObIXdMzOFUHcrBePlw2/5vl2LC08J3OmS+Kxo2nWgvrRpbq7mtIlkeIjLiVgDkYHOSRxUdv4h8JXVvqOpeSITDbRx3hutLlgmeFsiNdkkYd0JLBQAQTkDJyKXRry/z/r/hinuvX/L+v+HMu38R+MhaW1nqmnWum6hf6gLW0u7mBPK8vyWlZmhjuZDn92ygeaM5B4wRWp8Pby8vvDt1JqV0Lu4XVL2MyqxK4W4dQFySQoAwBk4GB2pmg6Z4H1jSb7T9H8P6clkJlW9sZdI+zjzAoZfMikjXkAqRkdxVLxNp+iaTcWNpaaLrcjSxSvHa+H9TawjREIZ3ZBcQpnMg55Y/hTb5d/62Fa6SX9b/AOf4FnxVb6uuv6MNP8Uanp8GpXv2aSCCG0ZUUW8smVMkLNktGOpI5PA7T2F74iufEt5YQz2Dafpc0MMstxExuboNCrMcoVRGBbOQpBzjC4yWHXPDWheHtKu7pdSlsTF9strq4tbq+aBWUku8xWQx/LIRl2GASOgNaMviDRLDX206NZZNQuDG04s7GWfaW+VGmeNCEyBwXI4HoKdrOwXT18v6f5HPf8JlrDfEC1sLeKK40K4vpbA3BtBGUljhd2UObgs5DRkf6lV9G45TR/FPie5i0y5vIdLlXVrCee2tLdHRkkQKVDSs5BD56bRtzjc2Mm3aL4S1LWjd3nh7ThrEt/LEkosFnmcwSFRMzhMqAVXDMQASoznFXpdQ8JS6TbefFZvYyadPcQo9mSn2VQolwu3hcOoK45z0OKn7C9P0/r0K+21/W5lWfi7UIPD2sPrt1DDq9ikZNt/Y8kZgaTKxAr9odZtzcApKF7EjBxSsvG/iNtKnt9StbOz1f+249KSS4hCRxB4kkDvGk8gJ+YgKJeSV5XkVueG/+EL1CyvtH8O6VZwWjoslzZ/2UbaOdHBUPsZFEqMFI3DKnHWtODwj4btbK4s7bw/pcNtdIsc8EdlGqTKuSFZQMMBk4B9TVPf7v6/T5+RC2/r+vMxPEE/i2xXw/Dba5pMVxdXxt7mQ6S5jlykjLhDPlQNoBG4knBBAypta7LrFpqHh4veWj20l5HDcxxwTRO8hVzvVlmwE4+4yuPUnitM+F/D50QaMdD006WG3CxNnH5AOc58vG3OeenWrq2FmlvbwJaQLDalTbxiMBYdowu0dFwOBjpS/zT+Q3r91vzOP1LxZrNvqmoz25sF03TNSttPls5IHNzcGXyvnSQSBV/1wwpRs7eozw638TeIG1SGe4/sxdLfWJtM8lYpDMyqZAJfML7VOUAK7TnGdwztHTTeH9GudZi1e40mxl1KEBYr2S2Rpoxzwrkbh1PQ9zU/9nWWwL9jt9olM4HlLgSHOX6feOTz15NC0t/Xb89fS43qn/Xf/AIH3HOeGNe1i71yfTvEvk2d2YmngsFsGQiMOF3i4E0kco5AwAjdCVXIFdZWZpPhvQ9AaVtC0XT9MabHmmztUhMmOm7aBnGT19a06OiF1YUUUUAFFFFABRRRQAUUUUAFFFFAGPdzyp4y0yFZHET2V0zxhjtYh4MEjuRk4+prI8IXWoa1pkXiW71y6lWYSM+kQRQeRAQSPKz5fml1xg5k5YHgD5Rr3Zi/4TPSwyOZTZXWxw4Cgb4MgjHJPGDkYweDnjOtW8Lv8RrqCDQ7aPXo4PNfU/scYaThQyCX7xZVePIPZ1684Wuy8/wBRvYz/AA94j1fWJ9Ki1iSxktPEGmy3kCWEckMtmo2fK0nmHfxKBvUJhh054s+ErFbzRNb0u6u9Rlt4tVuII3fUZzMiKVwom3+YMf71L4U+xW2ueIFl8N6Pod7bOjXVzp7hvtKupfc7+VGc9Sc55J5qzbeMvCtvZ31zHP8AYIIWWe5eeyltgwkbaJvnRd6lv+WgyPU09L/L9b/hsLX8f0sc/aeJ9S0X4VaJPaJNqOo3tyLKF7hxM25pHALGSVN5wuADIuSRzVyPxH4skj0bTL2zsdH1jUZbgGa8iEkYjiG4EQxzn5mBHy+adu1jk9Ktan4k8K23gzzLjSrm50eScWj2KaLM2GZwMPAYwVGTn5gAcjGSQDo2GgeF9S8Mx2dv4dsk0iRzKthc6V5CBs/eMEiKVPuVB70knrf+tv8Ah/O4aaW/r+vwsZuqz+LIvFmi2FnrelQx3NnM06yaU7iSSPy9zD9+CAd3AzxzktxiDUfFms22qajPbmwGm6ZqVtp8tpJA5ubgy+Vl0kEgVf8AXDClGzt6jPHSXXhjQb7T7WxvdE064s7PH2a3mtI3jgwMDYpGFwOOKkm8P6NcaxFq1xpFjLqUChYr17ZGmjAzgK5G4Dk9D3NUt/67/wBKwdDK0DUvEGraldT3B02PS4Ly5tVijjkM7+W5VX3lto6YK7T03bhnaOlqOG3htlZbeGOJWdnYIoUFmOSxx3JOSe9SUuiDq2FFFFABRRRQBj3c8q+M9LgWVxE9ldM8YY7WIeDBI7kZOPqfWsa1OrR/E6/tZfEOoT6fb2EV6li0Nts3SPMpTcIg+0BFx82c9Sa2bsxf8JnpYZHMpsrrY4cBQN8GQRjknjByMYPBzxgWA0AfEie2g8A/ZNXiBuZNY+y2Q+Vy6iXeshkO8o4+7u5+YAGhdPn+o38L+X5/0vmZ+j6zrOveKPD0mp31uthrGj3d1HZ2DSQtEpMG0OwkO91DEB1CYJbAqay1ybwr4G8SXqtdah/Zupzw263d28zAb1VQZJXztBbPLDjPI61LpeseF/8AhMUtvCWgWQvry3u7hr8WTWglZGjBxIYv3quzcyIWHy98itTQ9Ztrnw/q9xrWm2Olrb3c8N9FDJ58UjDAZsmNC+7PQrk9OaXTTs//AEr9NvxB9L9/0f8Aw5P4Qv8AxJfWl1/wlemCyljlAgcJHH5yFQc7EnmCkHI+/wA8HAroa57wiPDC2t0vhLTLfTEWULc28emtYuH2ggvGyI3QjBI5HSuhqmSgooopDCiiigAooooAKKKKACiiigArH8Mzy3Gm3LTyvKy6hdoC7EkKtw4A57AAADsBWxWP4ZMTabc+Qjov9oXYIdwxLfaHycgDgnJA7DjJ60A/h+f6M2KKKKACiiigAqK5RpbSWNJ3tmZCBNGF3RnH3huBXI68gj1BqWq2pXVrY6VdXWolRaQws825dw2AZPHfjtSew1uYvhBNQe0ub281u81WzuXBsWu44FbywPv/ALqNBhzyMg/LtPUkC54SnlufBujz3EjyyyWUTPJIxZmJQZJJ6motC1+HUJZdObSrvR7m1hSRbO7WIHyWyFZfKd1xlSMZyMcgZGZfCRibwbo5t0eOI2UWxZHDso2DAJAGT74H0qu/9dxI16KKKQBRRRQAUUUUAcx481fUtJ0W1XRhMLm9vYrQS28SySRhsklFf5NxxtBf5QWBPArGstW1HWpNP0i31nWdNdY7trq7uoLP7V5sMiL5TYjaHAEhOUXkKvzdc9N4svdLs9BdNbsP7TgupEt00/yVlN1Ix+WMI3yk5GfmIAAJJAGaxNQOi/2BpukXXgFrpW3zQaB9msn+zqhwZNpk8kD5x91ifn6dcJbP+un6b/nZbvqv6/q+3/B2l03W73VtB8G6jO/lS386mcQkqkgNtMemfukgMBz264zXYVzk+o6fqkfhe+sw8ltc3Ye2ZTs25tpiNykZ6ZG3gg/TB6Oqe7F29P1YUUUUgCiiigAooooAKKKKACiiigArnfGn9qpovnaJcapDPGWz/Zq2p42n5pPtCn5ARzs+bngGuirmvHX/AAjNt4ek1Pxdo1tq1vYqzxQyWSXMhbGSI1YHkgewABJIAJEy2KjuZmo6jqWoWejTeHvEFxJqV3awzxWlvbRfZp1JBaabzIzJHGQT0ZW7LluK6C7nlXxnpcCyuInsrpnjDHaxDwYJHcjJx9T61zniu88KLPY3mq+Do9cuPsJulk+x20kltboV5zKyk4L8Km45PTkZ6O6aL/hMtLDI5lNldFG3YULvgyCuMknjnIxg8HPGj3+bM4/ov6/ryNiiiipKCiiigDmPHd5q+naJHe6PqEdmsVxCsym2EjyBpo1wrMcKMFs/KSc8FSM0eJrzV7HXtAaz1COGwub9Lae2FsC8mUkJzIScL8q8BQcj72OK1dX8OaJ4gEQ17R9P1MQ58r7bapN5ecZ27gcZwOnpXM+KdN8KaYtu2reAbbUNPtIVjN6thaPFZRbsbdrsH2jJOEVuvAzxSV7r1B2s/Q7H7daf2gLD7VD9sMXnC38weZ5ecb9vXbnjPTNcTa63q/8AamnahJqUzxahrNxpr6S8UXlwpH5oDqQvmbx5IY5YjDNwOMdomnWUd4t3HZ263KQ+QsyxKHWPOdgbGduecdK523ubBPHxDeDJ7XU7pJF/tkxWhM0SYGS6SmXafkA3KOqggU+q/r+tP+AHR/10/wAzM8MeMr/W/iPqFjcre29ibFJrWzuNJmtzCRIyszySINxYAHg7ewyQSem8Mzy3Gm3LTyvKy6hdoC7EkKtw4A57AAADsBVbSNaGpeI7iC48M32mXsdsrPc3X2Zt0e47U3RSuwydxAOBwas+GTE2m3PkI6L/AGhdgh3DEt9ofJyAOCckDsOMnrQtl/XUcuvqvyZsUUUUCCiiigAooooAK4j4k6pf6Xp9qNJEouLu5itRJDGskkYYnJUN8u442gt8oLAngGu3rmPHMtjaaBc3Osae+pWIiIktUtDceZtBf5lxtA+X7zkKDjJHFfP8QUalbB2hDntJOxtRfvHBXfiLVpLDSdP0x9ba6nuriG7keC0N6rRcmMcC3Gc53cjapAyxqzrGsX58K6drelalrEUCwB7iaSC1MaBSNzXKFN5/iBEA7HGBg1s6xZaFaaBpWmHw5BqcF8Q1jo1vawnOFLlgjlY0CgkkkgZOOpAMOvWFlZ/2Zd33gF74wxwrBIsNkzWTFgEhG6UEMDjiPI6YJr4D+ysYpL/ZXo3f3Vre/n6W6K3W50+0i1fm6GqZ5B420eJJHEUltcMyBjtYh4cEjuRk/ma7OuWlsfL8a6T5gzL9luWVg/AUPBkEY5J4wcjGD1zx1NfYcK4PEYTDSjXg4t9/mYV5KTVjHt4WHja/mJTa2n2ygBxuyJJzyucgcjkjB5x0NbFYlt/yP2o/9g21/wDRs9bdfX9DnfxP5fkgrC8W+GYvFOkR2kjwo8M6XERuITNEWXIKyRhl3oVZgV3DINbtY3im31i70VrbQEt3lmcJMJ7t7Y+UQd2yREcqx4GccZJBBAqZbDW5y9x8MPN8O2ulR3GkReTLNL5/9kEtaGRg3+h/vv8ARtuOPv4IB7Yrp9Yt2N94fCuCIb/LGSQBmH2eYcZPzHnoOep6A154NIA+Deg6Z4h0KBbuGJo4oJdDuNWaJQCOAqAwyEFcMwIGOjV2cglWz8Grc28ltMLlA8EsvmvG32SbKs/8RHQnv1q+/qhdPlIZqngG31O+m1NtQu49VN0lzbzrPKIYTGRsBgEgjfCggkjJ3Hp2t2kmk6h48urux1uyury1svsdxYQyo8kP7zducBsrzxgiud1628Qt42h8TWmkpLZ6VMtup8+QXDwHKz7IBGQ4JcMDvGfJXA9ei+zT/wDCzDdeRJ9n/scR+dsOzf5xO3d0zjnFTH7Pz/8ASX+a0CXX5fn+jOf0z4VwaVFfW1odESC4tp7eO6XRFW/USgj57hZAHxn+4CQBk55rs77S1v8Aw7caS8pRbi1a2MijkBk25x+NeXaXH4q3avNqGqauuqtY3ivYJpd8IzJg+WY52le3BGBt8pVJzjrxXXan4TZ/h7q9l9s1i+ur2yZ2E1/L5jTCM/dwRsDN1RcJ22gEik/gfp/mWvjXq/0/4BoRp4u0zS7eCJNF1q4XKvK8kunKqgALhQs+49cnKjpxWVrnhPV/FxsLrWbXw/aXNi0qpb3ED6rCyuEw4z5BRwVOCM4z19JtC8Q+F/D3hy2+26tPpMcxIVfEdzPDKzhV3BftZDlRkfd+XnisnV7/AMO+IvEcWoX9vF4r8NpbeRCbC2/tSC3u9xZ98UQc7ihTDFflG4ZG/m5ayszOPw3Wn9f19wuv/DYeILa10q5121vZrfS/ssv9q2f2yVd2R9pizKPLkJBG87ido5yCTv23hzWrDWn1Cx1myX7YsJ1GKbTmdZHjUIWiImBj3KBw3mYwPfONrmgyajql/c6d/atlbQ+H4/sUdg0tpmVTKUX5NrZUEfuycfN8ynjGVq6+JrjxdbyXmqanpkCR2j2htdKvbpJDgGUP9nkEaktkETRnAII74SetvP8ANv8Ay29Al+n6L+rnWaT4MfQ9eudU07UdjX9xLLfxSQlknRnZ0A+b5HTeRuHBBOVJ2lcSLwTfnQHkttdh1aKHRbvT9Oht7ZY1dJQhRjJvbc37sAtwpyOFwc3NILDxpe/2/wD8JB/aX22UWe0XZsPs+393/q/9H+7nPmfNu/4DWd4MvLjQNOtn8btPpUVrZILWaSR4rKOI7QRNnCrNuwP3nYrs/jFJWcV6fpt6r8NS7tSbW9/1/L8zas9E8SaZKdU8zTtY1NreO0SKUtYRQwLljllWYvIWIycKpxwBjmS807xTrkVs90mkaLdWF0lzbSRzS6gkh2OjK6FICOH4IY/4w614l8KeJ/Dt/p1j4gttR3RAyxaO630wTeoyYY97OmSAw2kFSQetcqltqtv4Ju4/D2jW+kQtqMSz3elaFcWL3Ftt+dxaK6Tgqx2/K+4gEr6U2/6+f+er+ZKVlp/Wn+Wh2l74e1y4ubDU7fWbCDWbaCW2lnbTXe3ljkZW4i88MrAouDvP8WRyMMsvA8Wn6dNZ296xjk0dNLDPHlhjzCZDyM5MmdvHTrWBpGkalqUWjWN1rmvT2G29M86Q3enOfmjMcbGVjN8uW2sX3HbjJG4HZ1xdc0vWIItHN9dw6pbLZGQsXFlKp4nPGFyjOSeAWjQYy1JpNW7/AK3+7f8AEadtf60a+/b7l8jndX8P6rD4ki0KwkZdP1K6s7yV5Y7dd72/k7yjGfzMbYVyvkthiPmCnjsoLzxg91Gt1oWhx25cCSSPWpndVzyQptQCcdtw+o61FPpctp440m5s5NSeCS3mjuQ15PJB8qoEJQsUDdfmwCTnk1maOWHjW8/t/wD4SD+0vtsv2PaLv7B9n2/u/wDV/wCj/dznzPm3f8Bp3u+V+b/L+vS5O0f6/r+kWbHwdqY8K3HhrWNYs7rS5LJrSL7Np7wToCMBi7TOrEDttGT+VU9U8H6tPp+oahqOoJqWseVbLaf2fYLCqGCXzU/dyzEOS/3syKMDA2nmsXwte6npOvzan408QXVhtWUX1veWF3FaKS4CEXMkrWwA42+WE3bsHnium1Xxdpmv6RdaZ4L1nS9X1W5Tykitb9H8pW4aRim4oqg/e2nnaMHIFLVpNb/1/Vyurvt/w39WMaxvfEHhSw1HXNdTRraXVr8SSnWtSXTli2wxxoo8sTrk+Wxx5hIGOSc4PFNomtro174rvfCGkTq8qWcOoP8A2jBciQR4eMubciQEcbc8HrzVrQbDVdO8Ba94dvdMFs9nDOtjHau88TQyIzRxxyMilypJTG0EYX1ybuu2N3N8JYrOK1me6FraqYFjJcENHkbevGDn6U3rfyt+v+QrtW+f9fiZ3iD4Xy69plnZ3esQXgh042MkurWJvHDHrPETIPLl5+8d5wF54ydu28Oa1Ya0+oWOs2S/bFhOoxTaczrI8ahC0REwMe5QOG8zGB75wvHP9of21e/8h/P9nL/Yv9kfafL+17n3eb5XydfJ/wBd8mM/7VWJDOPHDHxR/b20C2/s3+zBdm0zj955n2f5f9ZnPncbcY4zQv1/z/q3oEv6/D+vkzT0nwY+h69c6ppuo7Gv7mWW/ikhLJOjOzoF+b5HTeRuHBBOVJ2lch/AOq22kTRnV01A2ujXmm2NrHaCHKyhCpZy5y/7sAnhTxwuDnMmuNZ/4WnBcldZtrT+0Xtrm1jt9QlhaAwuqTeZvNuFL+WcJGCmclhhjWt4I0ay0+81nTXTxBFdG5uQWuri+aFoWkJVo5XYxlyCDuU7+uTnNJK8V6f8D8n8ir8sr+f+f+X5Gr4W0qdL+bU9R1i11O6jgXTwLSARrbrGSWRvnfMm4/McgcDCjv1FcDNaavY+BdStbZtXZYtVKljNPNdmy89d/luxMjHy92CpLf3ecVTtLTVL+GO2sLjxDb6NJraiGS4e4S6+zfZj5gZpv3qxmTIBbDDOVI+Qir81n6fjb/P8yEuW/lf8P+GPSqK4vR/Emj+FrW40zxP4ht7CWO8n+yprGoATPb+YdjbpW3uvUBiTnHXimr4l01PGb6rbNdajp17psUUF5pdlNfQu6TS7hvgR1BGR1IpLW1uv+VxvRO/T/O3/AATsZLu3iuobaWeJJ5wxiiZwGkC/e2jqcZGcdKlrnLvT5ovHumX1q+oGKeGdbpftMzW4wqbMxlvLU9eQATzXK6dpuvwaxZXUl1r0n762eRJp5TH88s6yZXpgRiIYPA4bG47ilukHRvsekR3dvNcTQQzxSTW5AmjVwWjJGRuHUZHIzUtcvo2jPDfeItOml1U2E0kfkPPf3DMA0Q3+XKzl1+bP3WGD0xVL4YWdtp/hpLNIdZhvLeNI7uPUzdlFcZH7ozfIV6/6rjp7U1qB2tFFFABRRRQAUUUUAFFFFABRRRQBj3cLN4z0uYFNqWV0pBcBsl4MYXOSODkgYHGeorKh8A29vq1nrEOoXY1SG6e4mmeeV4ZQ+RIggMmxMggAqMjaOvfSvP8AketI/wCvC8/9Dt65aytvEMHxCj8SzaSi2OoyNZSlJ5HuFgOPJLwmMBArISTvOPObIHYW6/rr+rB/CzZ0610PxFceJZdO1y11ODVES2uUsZkc2+IyhBZSeSCTyBj3rN0j4aLpenS2SNoUCsbfFxp2hi1ncRSq/wC9YSkOTs7Koyc47VoxW+qQ674xuNPt2W4mhhNi8iYSSQQEDBPBAbANcbott4ibQ79V1vXmvZxaCSFtLvoHt389fMdJLmSVGO0sCsf7vAzjFKPxaf1v/XldA9l/Xb+vkz0vxFpMmuaFNYwXItZmaOSKZo/MCOjq6krkbhlRkZHHcVbsEvY7GNdUuILi6APmS28DQo3PGEZ3I4x/Ea5Dxh4WhTwJJawya3erb3cV0Fjv7mWcqJVLgFW3uAu4hSWIOMcgY6bQfsn9h2/9n/b/ALPg7P7R8/z+p+95/wC86/3u2McYprqBo0UUUAFFFFABRRRQAUUUUAY93CzeM9LmBTalldKQXAbJeDGFzkjg5IGBxnqKoW/h3WU8b3Wt3Or2Etpc262rWiac6OIkaRk/e+eRuzIcnZggcBetXLz/AJHrSP8ArwvP/Q7euT06w0NPjNqF5aeGpIbh7NEhvm0KaNDdB5zK3nmILllZcvuw2QATS/4P6lfZfy/P+mX9J8Nv4c1jSpNW8SWL22kadcW1lbfZRbv9n/dZd2MhDFAiAsFVfmzgVJB4e0fxV4N1qysdattSsdXvZbgXNoY5o43LKwU8sr4KjIPUcYFYXhG2u5fGmiXd1Dr7Xkem3a6rLqMdwII7lmgJWMyfIFJV8CI7CFGM4rUkt9etPBviz+xYJotRl1O4kt/3R3NGWXLICV3HbuxgjJ6HNEvPs/8A0r9d7i7W7/ozd8JeGv8AhGrS4h8jQ4fOcNjR9J+wKcDHzL5j7j75FdBXHfDqK6h0++W51W/1BDMGjW+029tTD8oBVTdu8jgkZ+8QCTjFdjVMlbBRRRSGFFFFABRRRQAUUUUAFFFFABWP4ZhaHTblXKEnULthscOMG4cjkE888jqDwcGtisTwn/yCrr/sJXv/AKUyUA/h+f6M26KKKACiiigAqnq+mQa1o15pl5u8i7haGQqeQGGMjPerlZniWC/uvCuqQaOxW/ktZEtyG2neVOMHIwc988UnsNblTRdBvrTVbnVdb1GC/vpbdLVDbWht40iQsw+Uu5LEscnOOBgDnM/hKJrfwbo8LlC0dlEpMbh1JCDoykgj3BxXPeC7Kzg8QahN4c0ebR9EazhRoJLB7JZLkM5ZhE6qSdhQF8YbgZO3jd8Gf8iNon/XjD/6AKr+vz/4cX9fh+mxt0UUUgCiiigAooooAwvFvhmLxTpEdpI8KPDOlxEbiEzRFlyCskYZd6FWYFdwyDWPp/gO70SytG0LUrG01C389Sx05mtVjmcO0ccAlBjAZFKjeQPm4OeJviXZXt/4Xhis4zNb/bImvovIknEkGTkGKP55V3bSUXG4AjOCa5mCwt5NL0lvE3haCXQIFu447Gx0CXyzKZFMU32LazxEqJfvA7STkjcKldbf1p+u3n9430/rv/w/l9x139hx6NaeFdNtJN8Vhdhd80iqzgW8wzjjJJOcKPXjA46euL0+3v7TQvA8GseZ9tjuVWYSvvcH7LNwzc5IHBOTz3PWu0q3uxdV6fqwooopAFFFFABRRRQAUUUUAFFFFABWB4s8IWPi7TWgvJbqCZYZYoJre6mi8syLtJZY3UOOnytkdR3Nb9cZ8UNN1zV/B93Y6Law3VvLbTfa4jdyQSyDZ8ipsjctk9Vyu7AXOCaiduV3RcPiRBrfw2/tSDT4YL+32WdmLRX1C0e7lhI/5bQSGVWhl/28t91P7vO/LatH4s0giTesNhcxlpJR5jHdBg46t905IGBxnGRXAeNNEvL3R9KkksvO1g6WLeGy/see/SCbjDxXA8tbWTJGZHxjapx8vPbrHPF4p0CO8k824TS7lZX/ALzBrfJ/E1q936v9f6+Zmtl6f5f18jo6KKKkYUUUUAFc3r3h/WNV16yvLPVrGKztVDCxvLB51MobPm5WZAWAxt3BgpG4c10lef8Aj230u/16xs7zQp2uSiSDXIdHmupLRRJkLDJHG2yTIJySoXIb5ulC+JA/hZ2RtL//AISAXY1LGnfZvLNh5C8y7s+b5nXp8u3p3pLHTTa6hfXs8/nzXbrtOzb5USjCxjnkAlmz6uaU6pjxANK+w3nNt9o+2eT/AKOPm2+Xvz9/vtx05rifC2nfYPHWrrYaRbSG4N1LJq50KWxmid5twiaaT/j5UknDJgARjOcil/k/6/r0H/wP6/r1O10nTTpsE3nT/abm4neaafZt3kngYyeFUKo9lFVvDMLQ6bcq5Qk6hdsNjhxg3DkcgnnnkdQeDg1x/gzS/sviDS2stIuLC7gsJY/EFy9k8C3dyTHgmRlAuG3CRg6lgAT8w3c9b4T/AOQVdf8AYSvf/SmSqtYT2+f6M26KKKQBRRRQAUUUUAFZHijS7/W/D91pumX1tYtdRtDLLcWrTjy2UqQqiRMNyMEkjjoa165X4kQ21z4F1CC6ttQumlhkSCCwhuJTJKY2Ch0hBJTPUONmcZ7VE7crurlwvzKxU1XwHc61o+lQ6pd6TeXum7kVrnS3ktJo2UDa9uZvmPyqQS+MrnHpraR4X/suHR4GvWuLfSbYxRRMnWUgDzMknGF3Kq9gx5NcV4nit9W0bw/qCaIdUntLWSGPS9U8MXM4mc+VldzKot2ymBI4K4YnkA1c8b6at3f3smpaLPfzzaWkWiNBZPcGzu8ybiJFBEDbjCfMJQfL975TjWWjb31fz/4f+tTOKTS6afdr/X9XOwu4WbxnpcwKbUsrpSC4DZLwYwuckcHJAwOM9RWxXPMsyeLtCW6YNOumXQkYd23W+T+ddDStbT+twTurmPbzMfG1/CQm1dPtmBCDdkyTjlsZI4HBOBzjqas67dXdj4e1G702Hz7yC1kkgi8tpN7hSVG1eWyQOBye1VrcRf8ACbX5DuZv7PtgyFBtC+ZPghs5JznjAxgcnPFzWJbSDRL2bUpZYbSOB3nkhd1dECksVMfzg4z93n05qXsX9v7vyRy9v4rvf+EUluhf2d9qLXsVkmNLnsxbPK6IvnQSSGQY37+Su5SMYzuqMeJ9dF6/h9pNP/tgagtot8LST7OUNuZ95h83cDgFMeZ1w2f4as2MfhSLwfqLrbajFp0km66fUYLxbmWT5QrAzDznb7gQrkghQvIAFZD4LHhSXZ/aP2c3oEmRe/2j9p2jH/T15mzHv5f+xVf8D9P+D63JW33jbrxTrh8Jve28trBfWd5PZ3G3Sbi8jneNmUMqxyKYVO3JZyVXOCeMnZudQe5Twtc/6Oxu7pWcxFZk5tpW+R8dMjhhjI9iayL0+DF8J6dm51KLT5JXitobC5vkuJ5SWMivHCRNJJkOWDgsCGLYOa17lLAJ4XFoktrbrdL9lh+zGPaPs0uFZG2tHhc8EZBAGO4F1v5f8H7+wPrbtL/gfcRaXqWua7qtzcWVxp9ppNpeyWjQTWjy3E3lttdt4lVY8sCANjcAHPOBXs/Gl1c6nbGTSoo9Hvb+XT7a8W7LTGWPeMtD5YCqTE4BDk/dyBk4a6eFV8bubVdUGqmdPtP9mi9+zGXAx5/k/uN+0rnzOdu3PGKls7DwavjWVbK+tn1yF3uG08amz+S7D55BbFyqMQxywQE7zz8xylsv67X/AK6dgl1/rvb+uovh99SbXfEmk3mtXd4lsYfs88sUCyQ+ZGScbI1U4PTcp981T07xNc6H4R12/wBbu7rVjpeozW0btEgllAZVRSIkAJywHC59jWjoeqW11quvyw6FqFlqcJi+1QzvEWuPkPl7NsrIMrxyV96i0c6HrvhvWFudIlsbSS7nXUrXUXQ4kGPMYlXdQOhyG4xnip1u/T9V/XzH29f0ZRs/H+oXGk75fDc8Goy30dlawTGeCGdnXcH8yaCNwoAbcfLPI43Zq3rWu+K9OTRFt9G0Zrm+ujb3Eb6nLsT5XZQriDJyEySV4PGDncI4LLwTd+E7mddZW/0ZZRJJfTa7LcLbyJjDLO0pMTDI5VlIzVbUtT+HkXhzTLXU/E1ubC4nMlhdya7K0jSKWyy3Pm78DlSd+Bnb3AquvzX9fmLp9/6/8Auaz4w1PTZ7qK10a2um0uwS+1PN+0flq2/5Yf3R81sRufm8sfd55OLNp4m1HUvEE9npmjJLYWskSz3sl4EO2SJZAUj2ksRu5BK8EEFjkClrdl4FuH0063qtvG13brBa+ZrLxf2hDkbVbEg+0rluj7wd5/vHO2l5oGm61Lbi/srfUtRmAaBrlRJLIsajCoTnITYcAdMHvT6h/l/X6muelec2/wAVEOm2F1c2dwP+JdcXl6Dp1xCrGNVIWGSQBHySRwzdjnHNdNB4c1SG6jml8Z65PGjhmgkhsQjgH7pK2wbB6cEH3qCCDwpdafpFvHHHJaS2UyWKSCQq0BQCQHd224+9zUvZjVrlc+MNWtItSg1PQIxqdpaxXcVtYXUl0kyO5T7whDgqVJYLGxxyu48VBeeMtYfwWmr6PaaHdXQvY7aaJdTkeOLdIqFS3khhICwBRlUr3yRtLtLsvA0ug6jc6Tra3FkAgvNRi8QTSvCsfzKpufNLxqASdoYD5jxyajnvPh5YeCJRca1Ytod9c7Hu21VpmnmyOfP3ly42rzuyoUdAvDfX+uol0/rodhYPeyWMbapbwW90QfMit52mReeMOyITxj+EVzXjDXtX0W8iNtNBp+mCEvLqE+lT36K+eVYQyIYlCjcXbK9clcczW2kx6pptpP4Y8ZapDpwjKxvZ3NverL8xyxmnjldjnj7+BjGKr32h6HK80PiHVtWv7mzsvOune8mgEtvucjzI7fy4nHDjGwkgYOe6lpqNbf1/XkVtS+IU9j4iTSbHRrjVxD5C3lzaW9wwBkAOYwkLxkBSGIeROOhNauleIdX1bULpYdDijsLe4uLYXUl8NzyRsVBEYQ4Q4xknIOflIwxrX9n4Nk12wludRj07Ur2OMW0VvqsljJdov3AY0kTzQM4AIOM49q0tP1Tw3Z6tPoen6pp41JpZJ5bBbxWnDsd7koWLDrnpgA+lO3fz/r5E30+7+vmV9L8VnWobRdPs0N5LayTXEEs+BaujFPLZgp58wMucdEY9sGlaeKruy8I6nq2r2rSXVreyW/2WG4WRS/mBERJPLj+XLDllyB1Jq/oM/hQ61qY8O32nT6jdMtzex212ssh4GGKhjtXnPAAyxPViTcu9H0dNF1C2vraM6fcmSe7SUllbd8zsc9PXjp2xSffy/G//AA6K3dvP8DJ1fxVqfh3w8t5rekW4v57pba1tLC5mulkLDILMtvvHRshY24HfPGl4X1ufxBoa3t5ptxpk/mPG8E8cifdOAy+YiOVIwQSin2rnriPw63w7N/YWWseINKkb7WMahK9xFsz+8R7mZXjKleisGBzgZzVfWS2ieH7DUtJ1TxZBpkyI0gtp7W6aHeRh5GvN8h++BhGYDHTuX1d/+G/rUnomv6/rQZ4j+IWpaLf+ILGKCza4tyv9lLIG/fbYUln3gNzsVs8Y6ge9X9Y+IM2n+Kf7IsNCu9SWBoVu5YILhjGZMH5dkLxnCkMd8icdM1ZubPwto0QsPFGsWs97qhkUTapPDDcXJdViYJtCAfIET5AOgzycl+t2fhCLxLbSarqcemavd7Eiji1iSyku8NhAUSRfNwTgZDdce1C+yv6Y31sZ1t8Q7yTUIUutCW3tJJIwbj7bvZUkaRFbYE5O6JjjP3SDnOVDrf4gXn2Bb7UNEht7a80ybU9O8q+MjypGqvslHlgRsVdTwXHUZ4Gb8a+C11waXHfad/alt5bGzF+DNH5RaRSY92ePMZuR0PPGKzPDUPw51V76Hw3qdpqBNvLbSwJqskwggLYdY0Zz5UZIHMYVSAuOAKSvZ/10/wA/6Y9Oby0/r+vwNR/FGqWWhvq+s6ElnaJJG77L0SvHbt96RwFADJkFlBIwCQzYwdbTNWOp31+kEI+yWkogS43586QDLgDHRSQucnJDDAxzl3Wrab4g8L3EfhbxJovkAi2kuiUvIoww27CFkUbjuAGSeo4Oak0XUPCvh/wVbPp2sadHoVmogW8N5GYsg4OZM7dxY889TVaXf9f1/wAElXsv6/r/AIB0VFQWV7a6jZxXmn3MN3bTLujngkDo49Qw4IqekMKKKKACiiigAooooAKKKKACiiigAooooAKKKKAMe7mZfGWmRAJteyumJKAtkPBjDYyBycgHB4z0FZ/hzVta1qzi1+5udPt9GuI2ljsls5GuEj52lpvN27sDJUR8Z25ONx0LsRf8JnpZZ3EosrrYgQFSN8GSTngjjAwc5PIxzjaGnhVPFEw0BdUWbzZd4txe/wBneZz5mP8Al23bs5xzvz/FmlrbTzG7W1HaL4tvtbmsoL3T00uHWrKS6025t7sTyBAFP7xGjCo+2RWABkXIIJ4GZPDJ1PU9E1eyu9cvWuLbUprWLUBFAJlRSMceV5ZPb7lM8PWng+z1G9fwrcw6hf2UTxPaW+qG5a1XdkxJG8hWAFlA2jYvygHpxY0DVbaTRNTvdJ0DUYrhb6X7Tp7yQ+c8+RvwTKY/Q/fA4p6X+X6r/hhO/wCP6MyrTxpdaV8NNL1XUVm1XUbuX7NGBE2ZpNzAFhDGxA2qSdkZ6dKsWvjjU7+y02O08OvHq1/JOotb6WW1iRIsbpN7wiTady7f3QJzyBjNS2aeFtU+G9tPd2/9m6Gg89ftlz5L2jK5+bzVfKMGzhlfjPBpl3ZeCT4VtLy+1lf7LjlMlrqs2vS7lZsqdl2Zd4B5GA+D0xS11v8A1/Wodrf1/WhLqWueKrbxJpWnWej6PKt5aSSzebqcqFJE2bgpEByo38EjLeiY5bfeNLq01K8MWlQy6Tp95DZXl212VmWSTZykXlkMq+amSXU/ewDgZq6zqXw+83RrLV/ElvBPHB52nzf25LFK0TADcZlkDOrcfeY78Z5wcXb+w8GP4yhW/vraPW52jnSwbVGj+0Mn3JDbBwsjDaMMUJ+Qc/KMV1+f6/8AD6fiHT+v6/rYtaN4g1TWNUuEGipBptvcz2pu2vAXZ43K7hGF+4cYyWBByNpGGPQ1k6de6Ba38mladf2X2yWSW5a0W6VpSS58xtuScb8g9geOK1qXRA92FFFFABRRRQBj3czL4z0uEBNr2V0xJQFsh4MYbGQOTkA4PGegrLttT8Q/8LDvdLu77S20u2tEvdsenyLMUkeVVTeZiuV8sEts5yRgVqXYi/4TPSyzuJRZXWxAgKkb4MknPBHGBg5yeRjnAsP+ET/4WRP9l/t//hIMHzvO/tLyNmXxnf8AufLz5mz+DOdvNCvpbz/Ub+FkWneKNc13xLo8TW66VperabdXVs0MyyyuoMPlu6vGPLcCQnaC6/NyTiptO8TXOh+Eddv9bu7rVjpeozW0btEgllAZVRSIkAJywHC59jUOlDwVpvjCO28ObtQ1aC2ulSK21I3C2iq0e+AI8u2HJKAIAqjafu4rS0c6HrvhvWFudIlsbSS7nXUrXUXQ4kGPMYlXdQOhyG4xnil007P/ANK3/QO1+/6PT+uxZ8I+JLzxHa3T6ho9xpctvKEAkinVJVKg7kM0MTnuD8gwR1NdDWH4VTQjpsk3hnVn1W0kkw051aW/UMB90O8j7evQEVuVTJQUUUUhhRRRQAUUUUAFFFFABRRRQAVj+GZmm025ZwgI1C7UbECDAuHA4AHPHJ6k8nJrYrH8MiJdNufId3X+0LskugUhvtD5GATwDkA9xzgdKAfw/P8ARmxRRRQAUUUUAFcFoHxFi8U+M9T0jRtQ0Z7ZLES2DpcCaV5AzKxkRWGFBCnaOcEEkbsDva5q8uPC9/r2tWM0vn6nDpix6jFAZS6WzbiF+ToxyxAX5+R6il1+/wDJj6fd+aH+FdV1TUjqC6jNZX1vBIq2+o2Nu0EVxx86qrSPnaRjeGwSSOqmrfhKZrjwbo8zhA0llExEaBFBKDoqgAD2AxVDwb/YKWl1b+HpNVCwFElt9UkvPMhG35cJdHcqkdCAAcHrji/4SES+DdHFu7yRCyi2NIgRmGwYJAJwfbJ+tV/wCVsa9FFFIYUUUUAFFFVtQ1C10rT5r2/lEVvCuXbBY+gAAyWJOAAASSQACTQBi+N/Ek3hrRYJrRYzc3l1HaQtLG8iRs2SWKJ874CnCLyxwBjOay7TxPq2sLp9homp6a9/LFcTXF3caTPHGnlOqGL7O0quj5kGdz8bScfMMXtS17wzrXh6R7571oEuFiMMdtdQ3kc3DKFjVROr4+YbQDt56c1nXQ8FL4b087tTe3kklaB7Fr570tn99vMWZ+Dw4foQobkAUtbO/wDWmn+fmPtYvW2uvrel+EdT8iOE390DJGVD7D9nmJCsRkcjqMHH1IrrK5ycaWYfC39mOi2IulNkLVA0bJ9ml2gHIwu3kEZ6AY5yOjqnuxdvT9WFFFFIAooooAKKKKACiiigAooooAK5P4h+NofBfh6SaO40+PU5opDZRahcCKN2Rckk9Tjj5RySQOM5HQ6nqcGk2ZurqO6kjDBdtpaS3L5P+xErNj3xisfUvE3h278ORPdSXF3ZatG8SQ21pPLNKmCHHlRqZFxyGOBtPBwcVMr8rsVGykrnPeJPH9/aWem3Ohz2Dw3GnG9knNnLdwk5UKrPHIBbxnL/AL18r8rf3Tnqp7nf4v0lYzE0ctjcvuCqxOGgxhsZxz2ODxnoK5/xDN4Ge30+71OS8kgksSY5NOa8ZHtBj/XfZ+DFz/y1+Xlv9qugnjt08X6SEJRlsblYo0jGzbugzznjHy4ABzk9Mc6Pf5v9fyIV9PQ2qKKKkYUUUUAFcz4r1TWtKntX0e50+QysqR6ZLau9xeNu+YJIJVCAKclijBeSeK1NZ16y0C3WfUEvDG2fmtbCe524GSWESNtHucCue1b/AIRXU/FNjNM+sy6lNaxeTNpT36x+Q7sULtb/ALsKWDHLkcDngULdA9mdnXDD4j2lx8ULbw1ZX2lNbFZ4p83QNz9pj2/IIwflXlhk8sQcABcnqzo1gfEA1swf8TEW32QTb2/1W7dt25x15zjNZba14cvPFtvC8kjalaNLbwTGCZYd7KDJGsu3ynfCcruLDa3AwaXVf1/X+f4nR/1/X9fLI8M/Ea08T+PL/SbC90qawjtFmtDb3QknlYOyuWUHCjgELjOCCcZwOh8MzNNptyzhARqF2o2IEGBcOBwAOeOT1J5OTVfSdV0LV/EdxPZJdR6otqsb/a7O4tmeEOSCiyqodQzH5lB6jJ5FWPDIiXTbnyHd1/tC7JLoFIb7Q+RgE8A5APcc4HSmtl/XUJdfVfkzYooooAKKKKACiiigArF8W6jqmkeF72/0WG0luLaGSU/a3YIiqjNnCjLnIHy5XOT8wrarnvGzeHh4bk/4S68ktdOJIYRXc0DTHa2UHlMHkyu75BnOOhxUzvyuxUbX1Mzxp4+j8K+DIr7z9Oi1a8tWltIb648mJ2VAzck5IGQAo5JKjjORLe6zrl9Y6Pe+GdU0dl1KOPZDJYvceYxG53EiTqFRV5+6emMkkCrl0vhvw74AuTqV1LH4fW2bzpLy6mmPlOMY3uxk5BwFBzyABUl/qHhzw/Bp+s6hcR2qOkdhZyuXJYSldqKvJJJVT0zhcngGtH8b9V8t9PnoQr8q9H+mvy/rcnupmXxnpcOEKvZXTEmMFsh4OjYyByeAcHjPQVsVj3Yi/wCEz0ss7iUWV1sQICpG+DJJzwRxgYOcnkY52KnoMx7eCUeNr+cxOIX0+2RZCp2lhJOSAemQCOPcVc1i0/tDRL2z+zx3IuIHiMMkzQrJuUjaXUFlBzjcASO1ULb/AJH7Uf8AsG2v/o2ep/Ewc+E9VEN5HYSGzlCXUs5hSBthw5kHKAHncOnWk/hKXx/d+SOds/CuuHwvcWl9dR/aEvoryxtnvprpIREyOImuJFEjhmQkkqdu/ABCgFF8L64b2TxA6aeNZOoLdrYrdyfZ9otzBsM3l7s4Jfd5fXC4/irJ07WFuPh9fvpstxHBDqEUV5fxa3PfxGAtH50kF1Id21YyQSAuxg+ORuqBNUhNvMia5cN4NXVljOrf2rL8sX2bcQLvfv2eftXdv6kpnHyiv+B+n56epK2+/wDXQ0LzwFqstpY3aPFJqMd3d3NxbRalNZRkXDbiqXEaGQbSF5Cruwc4B21uJpmo2dj4Utr6aW/ubS6H2m45f/l3mGWbGSMkDccE8Z5NcRqutXA8K6UJdWEdg+o3Ytru/wBZm0+K6tk3iHdeRneWIKsuciRUJJJwx6yyuHu9D8DTym7Z3nTc16AJmP2SYbnwSMnr1PWkttPL+vkD637S/X+v0LelaZr+hatdW9pBpt5pF3eyXZnlupIbiDzGLOuwRssmCTg704IGOMnPsfCeswarZW8/2AaZYarcanHeJO5uZjL5p8tozGFXmYgtvOQvQbuINM1DSrjxjdx+J9dns9dj1Fo7LTZNUltUeEHEJSAOqzBhyWKvliy5+UAVtM8U39x4utrdte8+/l1S4trvw7th/wBDtk8zZNwvmjhI23MxVvMwB8y4I7R/rtb7v+HYS+18/wBb/fr+h2Gm6TPZ+Jta1GV4zDqBg8pVJ3LsTad3Hr0xmsa98G3l74T8Q6T9rjhl1O+luoZEdwApZWCsV2sM7cHaeAeDTfDGjaXZeLPF2n2Wm2lvZStbeZbRQKkb7ojuyoGDnv61k2E0vhXwB4rk8MWEMTWeq3It7eGLbHEu5QWCqpwFBLcKenQ9KjS79H+aH29f0ZPF4I1JdO86LT7K21GHUoL8R3Gt3WoJdmNSoDyzR7o8A8EK2CoODitzVrLxDqWmaXdJZ6ZFqljei5Nqb6QwOu10x53k7gcPn/V9Rj3rj7PxXrK+FpZrjxZpV1bnU4YZtW0+7hvm0+3ZfmaRlgjiU7gAGaPCh8tnFaHiG/jXQ/D2pWfxEu10wagYrnVoZrLy5FKyYMjiLy+GAUcBeeQWwRf+a+/T/gC6fJ/dr/wfMveIPDWv6jcajLZwaW41vSo9PvBc3Un+hkeZlo8RHzR+9Pyny+VHPPGzo/hqPTtY1W9uI4J2u5IDHMyhpSsUKoNxI6hgxHJ+8T3Ncl4q8U3GnXMqReLfsKQ6THc6Udts/wDbc535XlDv+7GNsO0/vMjquN/TJNd1bxVqDS6u9naafPABp8dvEwfdAjuruQWIy3G0qQc5LAgB/wBfj+n39wfn/Wi/4HkXYPEWqTXUcMngzXIEdwrTyTWJRATjcQtyWwOvAJ9q5UfCqUaJa2iahMk50y5s7l5L+4njV5UABjidiqrkHIULxxXo1w0q2srW6h5ghMascAtjgH8a8t8I63p194g0vTru7k1XUdTsZv7UhvNYmMltMBmWKTT2/dxr/CDgcdj1M73X9df6Y721NyTw1ruoHUr7VdO0oXNxa21pHY2uqTxxsIpGfzDcLCrowLfLtQ429eeLA0DxLceB7qw1G7tp9RN0k9oktw0iRJHIjpE0wiVn+4fnKbvm53EZOLZzf8Ix4J18aGsOlW0OvvCZYIUCWMDSoskoQjaAilm5G0YyQQDVK7117uxt5G8UrcabZ6+kdv4iZYCDGbV2dtyKIjsZmXft2jHzA7Wy07q662/Gz/X815Ct+F/wuv0/JndTa5rdlHAtx4TvL2d03SnS7u3khjOSNu6d4WJwAfuY561i6r4Sm8Xz3mpXsN/pT3Gl/ZIbU6lJA6SBpOZfs0hR1O5SAWbvwORWFqHjDUY/Cdhs8ReRJdX1zHaa1cT21pDdQxk7C7vbyISwI2iOMb9pYECrJltPEfhvwF4lu4bG61eS8tElvo4kMiNtbzEDAZX59wK8YIIxSaUtPNL/AMC0/Ud3FN+v4a/oT3vw/v5NeN08EOqWs8dqJIpNcu7JYWhAHEcSskwyAw3BSDn14muPCfiSbxza6xM9tdw2motcRSS6tcL+4aJ4/LFsIzErKHzuyS23kruOO/b7p+leTfDrVobuXwwdG1671a8uLJ212GTU5bxYf3eVZw7sIX8zACjbkFuDt4e8m/61/wCGFa0bfL9DR8LaRr2n6XomoXmixeXpFjJHBZ2MgF1ctMU3b1l8tI8bckF23HnggA7t/Lq/izT5tHfQ9V0CG4AE95cyWjYjyNyoEllyxHHzLtxnPYHk/DvjHxPfanfyz6npM1ytrdP/AMI8b1WuopUz5ai3ECSL0wd0j5DAjtXWeBNWttVsnkTxgPEF20Ub3Nvm3X7G5ByvlxorpzkYkLEbcZznIve/P8wej/ryDTvCmo6bofiDSDqRvoL/AM2S1nutqyLJKp8wOI0VAu8lhtH8R49bOq+Hru++H6aFFJCLpYIIy7MdmUKE84z/AAnHFZvxPvjY6LpjNeR2kMmpIkzzatJpsbL5chw9xH8yDIHTqQBWVqniS70rw3otz4NvU1iZ4pS+nW16dTWaMA+ZMLh/3j+U2Mcjdny8BmXCWzfovuX/AAf8wa1Xzf3v/gGt4o8Mazf6jqz6SmnTwa1pq6fcNfTujWoUyfOirGwkBEpO0lOV688WbbRNa0TxRd3ulw6fqFpqK26zy3d08M8IiTYcYjcSAj5gCUwSfXIwtZ1FIdX8M3snxDvLHSryzlUXgktI4LmQeWVJLxFdzAtx22/KBzneszrOpeMtUxrjwWGm3UKJZx20ZEqtAjMHcgtjLZG0qQc5LAgCrWdv6/rX1B66/wBdP8vQyZ/CfiSXx1aaxM9tdw2epNcRSS6tcL+4aN4/LFsIzErKHzuyS23kruOLzeGtZudF8R6Bc/YobDUjdPbXsVw7SgzMTteIxgADcQSHOQOgzxz8nje7j+KtvZDXYYdObUXsJ9Nu7u3EqHyW2MIhCHRWkCbWaY7twG35hjV8JaxJ4gW4jvfGUketyxSibRYVtUfTiHxlY2jMuVGBmQspznGCMSvhVu3+X9fnuNtqWvdfr/wQuvB+sa8uoTa5baTbPdQ2dr9jt53nhkihnMjFy0SZJDFQu0gevPG74o0ae/02KPStPtppkuBP82oy6eUbaRvWWFGYnBxgjBBOfQ8XoviG4tvhk11b+MZ9TvomtodQluHt5JNIVpAkrsFjBBVdx/e7sbcngHNq01/V9QhjtNH8S/brSTW1s4NbEMEpnhNsZHC7FEbMrggOFwCACG2sC99F/Wq/z/yFtv8A1Zf5L/M7jw/b6ra6DbQ+ILxb3UVU+dMgABJJIHyqoOAQMhVzjO0ZxWlVbT7e4tdPhgvLx76eNcPcyIqNJ7kKAoP0AHsK4L4janaWGrQvdazATFa7ho39vzaXcS5Y/PEYiDMzbdgRhjIGGXJySeoJaHo1Feftc/Y/iXbw3vjDUNMtruxt5LPTLuW3VZ33srRDzIy7N93OG35b72MYzJvG14vxTgsV1+K30+TUXsJ9Puru386M+S+xxCIA6K0gTazStu3AbfmAAtXZef4aBtfy/wArnqdFeaaHqd1Z6X4piHjK81TWtPF8U066a2aSHazGOXy1iV+RtwD8nzcDGKkm8df2lFrMmjeI9PhtYNLsnivp5Uit0mkkmWQiYoy5IVVzh1VhgqTkUdL+Sf3jt+dvuaX6no9Fcz4C1oa54dacXt1qBiuHha6uHt5FlIxkxyQKqPHk4DYB4IYAggdNTasSncKKKKQwooooAKKKKACiiigDHu4JX8ZaZMsTmFLK6R5Ap2qS8GAT2JwcfQ+lZvhrSdf0Oyi0G4i0+XSbaNoodQiu5EuCmDtzCYyu4ZwW8znG7Azir17/AMj1pOP+fC8/9Dt65rwbqWmXdwsuo63PL4wIlN1pE+qyxmNxnKLaFwgUDG1tnIw2STuM6W17P9R69O6LnhjwtrOnX+j/ANqpp8NtoenyWNvJZzu73YbyxudWRRGMRg7QX5PXjnd8PaTPpI1P7S8bfa9Rmuo/LJOEcjAOQOeK47wd4huNe1Ozs7rXE14X2nSyaxpzxQbdMlyg8oqiBlBLSJslLMdvXhs6ngzQ9JfQ9f0d9Lsm0z+2LlPsRt0MO0FSB5eNuM9sVevN52f/AKUr/j2E7WXqvyf9akN34H1Gb4f6Xo6XEf2zT7pbkrHdzW6T4djs86PEicNncAcEDg0y28IappqaTf6XpunR3un3FzI9lc6tcXKS+cAGk+0yRmQPx3Q8FhnnNZcOq3/h74K6O+iGG0DTi3kuXcRR2cRkfLljHIqAYA3MjKM8ilj8U6kNC0X+1vGGm21lc3Nwlz4g064guEUKMxRtM0Swq5yQSYwDtwACaiNtbf1t/wAD/hwfS/8AW/8AwTrNUstfl1LRtWsbTTXu7WGWK6tZr2RIx5mwkpKIiWwU7ouc9qzNR8J6zc6pqMFuLA6bqepW2oS3ck7i5tzF5WUSMRlW/wBSMMXXG7occ0tfvkttd8N3cnxBu7DSbqzlQXYms1guZB5ZVizRFCzgseOOPlC85ZrHim/tvFl3brr/ANnvoNQtrex0DbD/AKfbv5e+XlDKfvy/MjBV8vkcNm18Wn9ar9bPuHT+v6/Q63w94fj0dLxpoLY3FxqFxeebEnJ8xzgk4B3bNqn6YzituuW8NHWdR1C81C91t3tYb66to7BbaNU2JIVUlsby4x1yARgFc5Y9TS6L0QP4n6v8wooooAKKKKAMe7glbxnpc6xOYksrpXkCnapLwYBPYnBx9D6Vl22meIv+FhXuq3Fnpkem3NolmHi1CRp1WN5WV9hhC5PmAFd/GM5bpWjef8j1pH/Xhef+h29cnp2oaEfjNqFpa+I5LieOzR4tPOvSyJ9qLziZBCZSuQqrlNuEwCAKWnXz/UrXlfy/MueGvCet6brWhyajFpcdpomnT2EcttPI8tzvMWJGVowEz5ZJXc3LdTVq98G3l74T8Q6T9rjhl1O+luoZEdwApZWCsV2sM7cHaeAeDXL+GtTTxH430NtR1yPWLifSb77fpzRxFdPdmg3QFVUMAOVKyZb5ee9aFhNL4V8AeK5PDFhDE1nqtyLe3hi2xxLuUFgqqcBQS3Cnp0PSiXn2f/pWv469hdvX9GdJ4O0GfRY717zT47S4upFd2TWrnUmlwu0FnnVWXAAGBkYrpa474datf6rp98dQ1/StcEcw8qbT7+O7aNSoysjRwxLnOSPkBweSa7GqZK2CiiikMKKKKACiiigAooooAKKKKACsfwzBLb6bcrPE8TNqF24DqQSrXDkHnsQQQe4NbFYnhP8A5BV1/wBhK9/9KZKAfw/P9GbdFFFABRRRQAVw+meCNW0DxHfarp+uTaj5mnGGCLUfKAecu7gyGKFTtywOcluWzniu4qG9WF7Cdbl3jhaNhI6SNGyrjkhlIKnHcEEdqT01/rYa10MLwlpurWMF3L4htrNdRuGVprq3vWuGuCBjnMMYRV6KqgjknqSTc8JQy23g3R4LiJ4pY7KJXjkUqykIMgg9DVHwZpi22nTahFcag8OolZbeG9v57kxRY+TmZ2IZgdxAxjIH8OTa8Gf8iNon/XjD/wCgCqe7+RK2NuiiikMKKKKACs/XbAanoV3Zm0hvDKmFhmnaBWbOR+8QFkIIBDKCQQCOa0KxvF3nf8IlqBt9Sh0xxFk3U8/koigjcDIOYwRld45XORyBSlsNbnK6X4F1bS86vGbeTVxfi6Szn1K4ni2CFoRG1zIrSMdrltxTg4ULgZq3Z+F9d0iSDVrBNPu9Vdrs3NrNdSRQKLiVZDskEbE7Ciryg3ZJ+XpXL6Tq91Ppkh1LUzbeFP7WWN9Qg1y4mRIxAxO2+fZI0ZnCDdkAMSgOOKtw6pDLY6ePE2u3Nl4aL3wtNSbVJLU3G2VRb7rhWVnzHvK5Y7wu47sZpvf+trK6+f4h0/rz/LU6m20GbRdL8I6aha6/s+6AmmSMgf8AHvMCxHO1dzADJ7gZrrK4vT7i/u9C8Dz6x5n22S5VpjKmxyfss3LLxgkckYHPYdK7Sm92LqvT9WFFFFIAooooAKKKKACiiigAooooAyvEdre3uiyW2nW6XEkjKGVtTmsCFBzkTQqzg5A4GMjPPrzVz4S1U+ENLs/slleanZmTbKmq3Gni3Vs4RZYI98gA2qchd+3cea2fG9xZW3hmSTUL23s18xRG9zrMmlozZ+6Z4/mHGTgA5x07jir7Uj/wrzw/fXXiCOK0UyrINS1yXTWvMbgjJcxbpJAACVHPmKysTkCp6P1X9f196KW6+ZZ1D4bapHpmlWOmTW9z9i082i3El5NaNDKzbml2IridSQp8mU7fkHJyTXZS21yPFmkSSB5lisLlJZxHhd5aDGccAnaxA9j6VydlYXfiHSPDdvrF1q1trk9ms108Oo3Nr5cKMCWeJHVTI25V+Zc8sT93FdZef8j1pH/Xhd/+h29Xs7eb/X/g/wBIha/d/X9f5m3RRRSGFFFFAHPeMdM1HV9LSz020guQzEyebq09jt4wP9VG/mLyco3ynjINZN34W11tU0i4s54Yrm2gt4bvVEv5ojKsbEun2JU8lwwLAFmBXeSPuik+KWr22j6HbTz+JTo0v2iIxwC7jg+1ATR7sk/OQqkkhWAwTuyKx/iPrPyWuo6R4hs5I7iyY6dbQa3LbSyzZ+WSGOFW+1kkqBG3y5H+0aSdndd/yX9fqVa+nl+v9f8ADnoR/tb/AISAY+x/2P8AZufv/aPP3f8AfOzb+Oa5SfwTc3fjmDUfs8Fjp9teG+Ji1S5lNzJtIH+jELDESSWLjcTj/aJro7HW4DfWui30wXWmsFu5YFRsbchWYNjb97jGc1yUureID8YdNhu7DVbXTpIruGKIT2/2aVF2ETkLJvJz/eAwCAFzuNPaS+evz/z/AK0IveL/AK6f5G14Y03xBFq11f8Aiq005ruVCiXVpfPLsTdkRLG0KBE7k7mYkDJOBjR8MwS2+m3KzxPEzahduA6kEq1w5B57EEEHuDXMeA9U0jVvEd/N4b8QNd2Ih2GzuNXe9mkcP/r9jyM0KfwgfLuzyowuek8J/wDIKuv+wle/+lMlC2Xp/X9ffqN9fX9H/X/ANuiiigAooooAKKKKACsXxdaapqPhe90/RILOa4vIXt2+2XLQpGroVLZWNySCRxgZ9RW1RSklJWY02ndHE6x4Q1fxL4Mgsb2+/sjULa1lhSKwnSe3lZo9il2lg3dM/dUEbmwT1qDxD8PdQ1jQbGCDxJfRXlpbwQfMLfyn2SI7OcwEhjtH3doO1QRjNa/jKyh1GKyshPfQ391I0NqbTULi2CZGXkfyXXcFVSQG74HG6uQ+JN6+k3dhYJq09tHb6a/l+drMtnNPJuUBoSpxdTAKf3UhAJdOfmNO95XfV/kmJLRJdF+qO8uLeb/hMNJl2ySRxWN0jzFeNxaDGSBgE7SccdD6Vs1gSyeb4y0STa6b9Oum2yDDDLW/BHY1v0/6/EFsY9vIp8bX8YiQMun2zGUFtzAyT/KecYGCeBnk5J4xsVnRafLH4nu9RLJ5M1nDAqgncGR5WJPGMYkHf1rRpdAfxP5fkgqpqmox6XYPcvG8zD5YoIsb5nP3UXJAyT6kAdSQATVuqWq6Lpeu2gtdb02z1G3Vw6xXcCyoGAIDYYEZwTz70ne2g0c03jy7HgjSfEKaBK4vhEZoxdIEtd8iphm+8x+bjahBxyV61ta5IqaloIaJJC+oFVZi2UP2eY7hgjnjHORgnjOCMeT4cWCeErPw5pepX+k6dasGKWUduDOwcOGcvE3IZQflxnvmtm40i5kOi5vGum0+586ae52iSUeTImcIoXdlx0AHWmLo/R/kV77xSLLxdZaObQvb3GElvfNwsMzKzRxlcclhG3ORglODuFMtNT1ZPHl1pF/NZTWTWX2u38m2eOSP95t2uxkYPx3Cr9KZqHgDw/qdxPe3VnG2pyzLOmqeVH9qgZSCgjkK5ULtAA9M5zk1r/2RB/wkJ1jfJ9oNr9l25GzZv3ZxjOc+9JXur+f5afiD628vz1/A5Sz+Lnhq+1C+tLWR5TaW89wrxTQSecsPLgIshdD6eYqZ7VpXHjKWPwtqOtQeHNWZbOLzo4JxFC1xHt3B13P8q45IbDj+5nglv4IjtbG602HW9UGkzwywLpzGBo4VkBzscxeZxk4y5A6dBity602C80abTJ9xt5rdrd8HDFSu08+uKNeXz/r/AIBStzK+1/8AL/gkWi6lcarpq3V1pV1pbt0guniZiMA7gY3cY57kHjoKxdb8U6vY+ILrTtH0S21BLLT0v53lvzBIys0i7I08pgzfuj95lGSOR1qymi+IdP02C00jxJHMYyd82s6etw5XACqvkNAABg8kMTnrxWdJ4H1HU764vta8TXCXF1brZzpo9slrFNApZgreYZZA2ZH+ZHU4IxgjNOWr90mOi97+tdfwG6/8VPDnh6z0+4upTIL+1F7GgngiYQkZD4lkTd1+6m5uOlaq+LYbrWBp+k6Zf6oqrE1xdW4iWG3Eg3KWMjqW+U7iEDEDHGSBSXvhGCW6tbnSdSvtEuLa2Fmslh5RDQg5VCsqOuAehABGTzg0reFiutHU7HW9SspphH9rSHyWS8KDALh422kjgmPZkfQYNL/P8P6t8/IWtvl/X6iQeAvB9pdR3Vr4U0OG4icSRzR6dCrowOQwYLkEHnNZXhbx7P4lsrq7hs9KuFt4PNNnpesC6vFbtG8TRxrGxwer9Rj3rVgs/GC3UbXWu6HJbhwZI49FmR2XPIDG6IBx32n6HpUeleEbjSNPext/FGsPa/ZzBBG6Wv8Aow7MjCAMWHQbiw55Bo1K0uV5PH9rP4Z1HWtD0y91WCxg81vLMcQL7ctGS7AhkGN4xx0GWBUWH8YG20ezvdQ0DVbaa9uBbQWYEMsruULKf3cjKFO0jJIx1baMkLYeB9I0ux1Gw07z7fT9Rh8qazWQeXuK7GlBI3B2GNxzgkZI3Ekz2vhnybXT4b3VtQ1JtPuRcQy3IhDcRsgU+XGoK4YnpnPejr93/B/4Aun3/lp/wSvrfjS38P8Ahq31nUtNuoI5nCPBPcWtu8LYPDGWZEJ46KzZ7ZHNMXxzbXUOmnRdK1LVpdQskv0gtViRooHxtZ2lkRRknGAxPB4wM1e1zw4mtXdldpqF5p13ZFxFcWgiLbXADqRIjrg4HIAIxwRzWZbeAYrC2sE0vXtXsbixtfsSXURgZ5IA2UjdXiZDt6Bgob1Jyci31/rf9bfK/kHT+v62/QyLrTPDl54+udLufhfa3kjJHcy6o1pYsG8xmBkYM+8jKnJwWJB+XoTvWvjWzuNWitF06+jtJrqSzt9SYRfZ5po925Fw5cco4BZApK8E5Gbtz4eM3iGDWINVvrSaOFYJo4REY7lFYsA4eNiOWblCp569Kp23gm0ttXjuxqF/JaQXUl5b6a5i+zwzSbtzrhBIeXcgM5UFuAMDAtkv6/qwPW7/AK2/z/An8O+KY/EqCa10rUrW1eISw3V3EqJNzghQGLAg+oAI5UsOaPGcGnyeF7ibVdCttditsSpaXMAlTd0DEFWwAGJLBSQM4B6VYg0mfSvDMOl6DdRwy20SxQT3sJnUAY+8qsm7jPQj+lU00rxRcxSw6p4jtI43UbJdK0w280bhgc7pZZkIwCCCnOetJ66D63OX0rWbHw34Xm1vw94Q0CWO4uordv8AhE76CSOTLhcvIyQjIL4AG7ludoJI6XXPGUXh3w7Bq+r6VdWqyyeW8E93ZxPEecbmedYznHAV2PPTrhB4ItW0nUrS41K/uLnUpY5p9QcxLOXj2+WwCRiMbdi4+TnHOakvfCTXyadJLr2prf6f5gTUEW3811fhlZTF5eCAOVQHjr1y5XtoJW6lE+PZJ7vRRpPhvU9StdYsftsNxDLbptXCHG2SVTkBxn6jG7nGtdeKLK0a8EsVwTZ3lvZybVXl5zGEI56DzVz9DgHvQi8Dx2um6Pa2OuarayaPG0Nvcx+Q0jRNgeWwaIqQAqjO0N8o5zkl+oeCLTUtYlvpdT1KOOa4t7qSzilRYXmgZCjn5Nx4QAru2nrjIBD05vK/4X/y/EF5k+l+LINY8Q32l2NhcuthIYri7863Mcbj+EoJfNXODjdGAcZHGCd6udj8IRHxTb67f6rfX89p5v2SKdLdVtxIMEKyRLIRjjDMw6E5IBHRUuiDqwooooAydV8KeHdeuVudc0DS9SnRNiy3lnHMyrknaCwJxknj3q7p+m2OkWMdlpVlb2NpHnZBbRLHGmTk4VQAMkk/jVmigAooooAKKKKACiiigAooooAKKKKACiiigAooooAx7uRR4z0uMxIzNZXREpLblAeDIHOMHIzkE8DGOcwW/ikTeM5tDa0KQiNvIvTJkTSoEMkYXH8IkU5yc4cYG01euNPll8SWOoKyCK3tp4XUk7iXaIjHHT92c/hWXF4A8PwX9tqNvZxw6pBcfaG1KOKNbmdjncJJNuWVgxBHp0xgULdXB7DdH1zUItR8Q23iS6sXi0ny5BcW1s8AEbRlzuVpHyRjqCPpWZpvxd8OapaX09stw4slidkgeG5eRJJPLVlWGR+ckZRsPz93tXTf8I9YtdavNMJJl1dFjuYnb5SqoUwMYIyDzz+VZ9v4O8vTTp93r+rX9mph8mG58j9z5Tq6gMsSs33QCWLEjvnmkr31/r+tPxD+v6/H8CPVfGN3pvhtdUHhbVXk+1JbvZySW8cibnChyfMKkHcMbSeSM45I6CwuZbyxjnuLGewkcHdb3DRl057mNmX34Y1BrekRa7o82nzzTQLIVYTQEB42Vgysu4EZDKDyCPUVPYW0tnYxwXF9PfyIDuuLhYw789xGqr7cKKYFiiiigAooooAKKKKACiiigDHu5FHjPS4zEjM1ldESktuUB4Mgc4wcjOQTwMY5zRtvEOtSeObnQ7jSNPitLe3W6a8XUnZzE7SKh8swgbsxnI34APBNatxp8sviSx1BWQRW9tPC6kncS7REY46fuzn8Kz7fwm0Pi6fX317U53njEMlpItt5JiBcrHxCHwpkYg7snjJNH/BHpZmbZeN7rW/EWn2Ol6fLZ2WoWNzc297exKyz7DEI5ECSZ2HzCSrhWI29Km0zxbJZeF9Y1PxZc2gGk3k1vLPbQmFHCEBTtd2wSSBy2PpU+j+CLbR9Vs71dV1K7Wwt5LWzt7l4jHbxOU+QbUDNjy1ALMxwOSatS+EtOm0fU9Nma4aDUrl7qUiTa8cjENlCAMYKgjr070Py7P776fgH+f4Wf6kXhHxppfjO1uptKyrWkoimjM0M20lQwIeF3Qgg9mJGDnFdDWfpGm3WmwyJea1fasWYFXvUgUoPQeVGg/ME1oU2SrhRRRSGFFFFABRRRQAUUUUAFFFFABWP4ZkWTTbkpEkQGoXalULEEi4cFuSeT1PbJ4AHFbFZ2iafLptlNDOyM0l5cTgoSRtkmZwOR1wwz70A9vn+jNGiiigAooooAKp6xqcGi6Leand7jDaQtM4XqQozge9XKgvrK31LT57K9jEtvcRtFKhJG5WGCMjkcUnsNWvqY+i69fXmq3Ola5p0FhfRW6XSrbXZuI3icsv3jGhDAqcjGORgnnFjwlIsvg3R5EiSFWsoiI4ySqDYOBuJOPqSabo3hxdJu7i8n1K91S9njSE3N75e5YlJKoBGiLjLMc4yc8k4GLHh/T5dK8N6dp9wyNLa20cTtGSVJVQDjIHHFV/X5i/r+vmaNFFFIAooooAKzPEWsDQPD13qjQmcW6btm7avJAyzYO1BnLNg4UE4OMVp1DeQSXNnLDDdTWcjrhZ4AheM+oDqy5+oIpO9tBrfU4rTfiR/a8BttMttNvdWN4LWJLXVPNs5MxGXeLgR5wFVgf3eQwxjHzVbh8aX+oLb2mk6RbS6wzXAubW5vjFFF5DiOQrKImLZZl2/IMg5O0jFW4/BFtHZvjVNROpvdfazqzNEbjzdmzIHl+UB5fybdm3HON3NH/CE28Vnbpp+q6jY3sLSs2owmJp5jK26XfvjZPmYA8KMbQF2jin/AF/Xl3/AX9fn/wAAb/bVvrdp4V1OC2/d312HRZshoSbeYn7pxkYK85HJ9jXT1iHw7Ha2+g2mlqsVppM4bY7EkoIZIxg85bLgknryc1t03boHVen6sKKKKQBRRRQAUUUUAFFFFABRRRQAVzXjDxhF4VSyTZZme8dgr396LS3iRQNzySlW2jJUABSSWA9SOlrI13w7DrctncLd3Gn3ti7Nb3lqsTSR7l2sAJUdcEdflzx1pO/QaOc8RfEhfD0dk00OlHz7H7a7y6sIo5h3jtWMf+kP6A7MgpyN3G79thvPE2izQxK6XOnXE0crbgyqWgOMZxzkZyCeBjHOa9x4Jt5tCtdEg1bUrPS4YfImtLZolF2hPzB3MZdd3OfLZPvHGK0pNLb/AISDT7yHy0t7S0ngKcg/O0RXAxjAEZ/SqX9fj/wBdv6/rqadFFFIAooooAwvFXiG48OWMNzBpUl9G80cckgmSNIQ0iJls5Yn5+AqnOOSvWsvxn4/TwjfR28kFjt+zNcs19qAtTMAceXbgo3my8fcyvVefm41vFHho+J7FLN9Y1DToFcO62Qg/elWVl3GSN8YKg8Y981HqnheXWNOhsL3xDqxtfK8q7jj8hDeqeokYRBlyMg+WU4J6UtRq3U2rS5jvbKC6gJMU8ayISMEqRkfzrEXXtTh8XQaRf6Zapb3aytbz2160soVMfPLEYlCKcgZDt8xUd81oJpCRa2mox3l2iR2n2VbFZcWwG7IfZj7/GM56cVQ0zwqdL8Q3uqx65qUwvZWkmtZltzGeoVd4iEm1c/KN+B+dVpfTz/4BOttd9P+CZ/hjxy/iPxJe6atnZpHa+cHaC/82aBo5fLCzxGNfKZsMQAzcKfYnX8MyLJptyUiSIDULtSqFiCRcOC3JPJ6ntk8ADiqej+CbPR9Ut7wX99dizikhsYLnyttpG5BZUKorsPlUfOzHj15rT0TT5dNspoZ2RmkvLicFCSNskzOByOuGGfektl/XUb8u/6M0aKKKACiiigAooooAKKKKAMXxNquq6Pp4u9KsLG9RAfNW6vnt2zkBFQLDJvZicAcc4xnNVNY1vxFp0Ni9roemztciON4ZNUeOQTN95EAgYOFAJ3ErwGJAAzW1eabFfXdlPO8n+hymVI1I2O20qCwxzjJI98HtRJpsUusQ6jI8jSQQvFHGSNi7iCWxj73ygZz0z6mhAyldSKPGelxmFC7WV0RIS25cPBkDnGDkdQTwMY5zsVnXGnyy+JLHUFZBFb208LqSdxLtERjjp+7OfwrRo6AZUOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDRRQAltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoGh6h/ZJs/wDhKdWM/m7/ALd5Vp5wGPuY8jy9vf7mfeiigAn0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AUUUAPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65oooAZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDSW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tiiigAGh6h/ZJs/8AhKdWM/m7/t3lWnnAY+5jyPL29/uZ96J9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmiigBb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBT59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFFFABDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmmWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0UUAJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KBoeof2SbP8A4SnVjP5u/wC3eVaecBj7mPI8vb3+5n3oooAJ9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmlvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFFFAD59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaKKAGWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0ltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoooABoeof2SbP/AISnVjP5u/7d5Vp5wGPuY8jy9vf7mfeifQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75oooAW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AU+fSL2bWEvI/EWpQQKVJsI47YwtjqCWhMnPfDj2xRRQAQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65pllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNFFACW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tigaHqH9kmz/AOEp1Yz+bv8At3lWnnAY+5jyPL29/uZ96KKACfQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBRRQA+fSL2bWEvI/EWpQQKVJsI47YwtjqCWhMnPfDj2xRDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmiigBllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KKKAAaHqH9kmz/wCEp1Yz+bv+3eVaecBj7mPI8vb3+5n3on0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aKKAFvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUUUAEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDRRQAltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoGh6h/ZJs/wDhKdWM/m7/ALd5Vp5wGPuY8jy9vf7mfeiigAn0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AUUUAPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65oooAZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDSW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tiiigAGh6h/ZJs/8AhKdWM/m7/t3lWnnAY+5jyPL29/uZ96J9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmiigBb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBT59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFFFABDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmmWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0UUAJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KBoeof2SbP8A4SnVjP5u/wC3eVaecBj7mPI8vb3+5n3oooAJ9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmlvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFFFAD59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaKKAGWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0ltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoooABoeof2SbP/AISnVjP5u/7d5Vp5wGPuY8jy9vf7mfeifQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75oooAW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AU+fSL2bWEvI/EWpQQKVJsI47YwtjqCWhMnPfDj2xRRQAQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65pllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNFFACW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tigaHqH9kmz/AOEp1Yz+bv8At3lWnnAY+5jyPL29/uZ96KKACfQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBRRQA+fSL2bWEvI/EWpQQKVJsI47YwtjqCWhMnPfDj2xRDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmiigBllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KKKAAaHqH9kmz/wCEp1Yz+bv+3eVaecBj7mPI8vb3+5n3on0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aKKAFvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUUUAEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDRRQAltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoGh6h/ZJs/wDhKdWM/m7/ALd5Vp5wGPuY8jy9vf7mfeiigAn0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AUUUAPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65oooAZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDSW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tiiigAGh6h/ZJs/8AhKdWM/m7/t3lWnnAY+5jyPL29/uZ96J9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmiigBb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBT59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFFFABDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmmWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0UUAJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KBoeof2SbP8A4SnVjP5u/wC3eVaecBj7mPI8vb3+5n3oooAJ9D1CbTra2j8U6tBLDnfdRxWhknz03hoCgx22qvvmlvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFFFAD59IvZtYS8j8RalBApUmwjjtjC2OoJaEyc98OPbFEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaKKAGWWi39rJcNP4n1W8E0bIiTxWoEBPRl2QKSR23Fh6g0ltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoooABoeof2SbP/AISnVjP5u/7d5Vp5wGPuY8jy9vf7mfeifQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75oooAW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AU+fSL2bWEvI/EWpQQKVJsI47YwtjqCWhMnPfDj2xRRQAQ6Rexay16/iLUprcsxFg8dsIVB6AEQiTA7Zf65pllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNFFACW2h6hBYXNvL4p1a4lmx5dzLFaCS3x12BYApz33K3tigaHqH9kmz/AOEp1Yz+bv8At3lWnnAY+5jyPL29/uZ96KKACfQ9Qm062to/FOrQSw533UcVoZJ89N4aAoMdtqr75pb3RL+6Nv5HifVbLyYwjiCK0PnkfxtvgbBP+ztHoBRRQA+fSL2bWEvI/EWpQQKVJsI47YwtjqCWhMnPfDj2xRDpF7FrLXr+ItSmtyzEWDx2whUHoARCJMDtl/rmiigBllot/ayXDT+J9VvBNGyIk8VqBAT0ZdkCkkdtxYeoNJbaHqEFhc28vinVriWbHl3MsVoJLfHXYFgCnPfcre2KKKAAaHqH9kmz/wCEp1Yz+bv+3eVaecBj7mPI8vb3+5n3on0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aKKAFvdEv7o2/keJ9VsvJjCOIIrQ+eR/G2+BsE/7O0egFPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sUUUAEOkXsWstev4i1Ka3LMRYPHbCFQegBEIkwO2X+uaZZaLf2slw0/ifVbwTRsiJPFagQE9GXZApJHbcWHqDRRQAltoeoQWFzby+KdWuJZseXcyxWgkt8ddgWAKc99yt7YoGh6h/ZJs/wDhKdWM/m7/ALd5Vp5wGPuY8jy9vf7mfeiigAn0PUJtOtraPxTq0EsOd91HFaGSfPTeGgKDHbaq++aW90S/ujb+R4n1Wy8mMI4gitD55H8bb4GwT/s7R6AUUUAPn0i9m1hLyPxFqUEClSbCOO2MLY6gloTJz3w49sVq0UUAf//Z\" title=\"actionsPerHour.JPG\" width=\"989\" /></p>\r\n<p>You can now find a good trade-off between the load and the collected data. If it is e.g. okay that the history of blocked transactions is only collected every five minutes instead of every single minute, you can increase the interval from 60 to 300 seconds:</p>\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET INTERVALLENGTH = 300 WHERE ID =&#160;5020</pre>\r\n<p>For the following alerts and collectors special considerations are useful if they are responsible for a high runtime or resource consumption:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>ID</strong></td>\r\n<td style=\"text-align: center;\"><strong>Action</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">65</td>\r\n<td>Alert_Backup_Long_Log_Backup</td>\r\n<td>The runtime of backup related actions can be impacted by a large backup catalog. See check ID M0940 (SAP Note <a target=\"_blank\" href=\"/notes/1999993\">1999993</a>) and SAP Note <a target=\"_blank\" href=\"/notes/2505218\">2505218</a>&#160;and make sure that the catalog remains on a reasonable size.</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">\r\n<p>41<br />5008</p>\r\n</td>\r\n<td>Alert_Dec_Extractor_Status<br />Collector_Global_Dec_Extractor_Status</td>\r\n<td>\r\n<p>The Alert_Dec_Extractor_Status and Collector_Global_Dec_Extractor_Status checks don't work correctly and have an increased runtime as long as the CATALOG READ privilege isn't assigned to the _SYS_STATISTICS user. Therefore you should either grant CATALOG READ to&#160;_SYS_STATISTICS or disable the check with ID 5008. See SAP Note <a target=\"_blank\" href=\"/notes/2000002\">2000002</a>&#160;(\"Are there standard recommendations for specific SQL statements available?\" -&gt; \"ba8161b6133633e2984828ce38aa669a, 905dbaa93a672b087c6f226bc283431d\") for more details.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">5001</td>\r\n<td>Collector_Global_Rowstore_Tables_Size</td>\r\n<td>\r\n<p>This collector can suffer from the existence of large row store tables. See SAP Note <a target=\"_blank\" href=\"/notes/2000002\">2000002</a>&#160;(\"Are there standard recommendations for specific SQL statements available?\" -&gt; \"0c2d40de2702bc576de87a9aec2cef30, f45e2eaba236b6655804f6585d631923\") for more details.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">5045</td>\r\n<td>Collector_Host_RS_Indexes</td>\r\n<td>\r\n<p>This collector can suffer from the existence of large row store indexes. See SAP Note <a target=\"_blank\" href=\"/notes/2000002\">2000002</a>&#160;(\"Are there standard recommendations for specific SQL statements available?\" -&gt; \"ac52398f58a752bed7843aac8d829464, e9f2feac54a048984928f3fcbcfacedd\") for more details.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">5046</td>\r\n<td>Collector_Host_Service_Replication</td>\r\n<td>\r\n<p>The runtime of this collector can be increased if the secondary system replication site is not accessible. See SAP Note <a target=\"_blank\" href=\"/notes/2000002\">2000002</a>&#160;(\"Are there standard recommendations for specific SQL statements available?\" -&gt; \"M_SERVICE_REPLICATION\") for more details.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The maximum parallelism of the statistics server threads is controlled by the following parameter (default: 5):</p>\r\n<pre>&lt;service&gt;.ini -&gt; [statisticsserver] -&gt; threadpool</pre>\r\n<p>It is usually not recommended to adjust this parameter, instead workload classes as suggested below should be used.</p>\r\n<p>For SAP HANA &lt;= 2.0 SPS 05 see SAP Note <a target=\"_blank\" href=\"/notes/2970921\">2970921</a>&#160;and make sure that a workload class is defined for the _SYS_STATISTICS user in order to avoid temporary CPU spikes.</p>\r\n<p>With SAP HANA &gt;= 2.0 SPS 06 an <a target=\"_blank\" href=\"https://help.sap.com/viewer/85e6beaa64484eb88b47b34eabdf1326/latest/en-US/3215e1e1400c4d42afc2c36bf7374a4c.html\">Embedded Statistics Service Workload Class</a> is automatically implemented in case no workload class for the _SYS_STATISTICS user exists, yet. When the delivered workload class is used, the following parameter is set to its default 'yes':</p>\r\n<pre>indexserver.ini -&gt; [statisticsserver] -&gt; enable_automatic_workload_management</pre>\r\n<p>In case a workload class already exists from the past, the parameter is set to 'no'. If you want to enable the delivered workload class, you need to drop the previous workload class and subsequently&#160;unset the parameter so that its default 'yes' becomes active. See SAP Note <a target=\"_blank\" href=\"/notes/2222250\">2222250</a>&#160;for more information related to workload classes.</p>\r\n<p>Be aware that the suggested workload class is based on the database user name ('USER NAME' = '_SYS_STATISTICS'). In the thread information (SAP Note <a target=\"_blank\" href=\"/notes/2114710\">2114710</a>) the database user name may be empty for statistics server activities and only the application user name is filled with _SYS_STATISTICS. This topic is tracked via issue number 266415 and has no impact on the validity of the defined workload class.</p>\r\n<p>Also check \"<a target=\"_self\" href=\"#L9\">How can the memory requirements of the statistics server be minimized?</a>\" in terms of STATISTICS_ALERTS_BASE recommendations, because a high number of records in this table can also have CPU and runtime effects.</p>\r\n<p>The following wrapper call is typically triggered by Solution Manager monitoring (statement hash dc571bf5eb7cad9c313c20de904ab709):</p>\r\n<pre>CALL _SYS_STATISTICS.STATISTICS_SCHEDULABLEWRAPPER(&#160; ? ,&#160; ? ,&#160; ? ,&#160; ? , NULL)</pre>\r\n<p>If SAP HANA &gt;= *********** is in place, you can activate the new SAP HANA monitoring mechanism of the Solution Manager (SAP Note <a target=\"_blank\" href=\"/notes/2374272\">2374272</a>). Then the Solution Manager will rely on information already collected by the statistics server rather than executing statistics server checks on its own. As a consequence this particular STATISTICS_SCHEDULABLEWRAPPER call will no longer be executed.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L11\"></a>&#65279;11. Why is the history collection for some tables inactive?</h3>\r\n<p><em>SQL: \"HANA_Configuration_MiniChecks\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>) may report an issue for check ID 718 (\"Number of relevant inactive actions\"), because some history collections are deactivated with certain release levels of the embedded statistics server, although it is normally useful to collect this information.</p>\r\n<p>For example, the monitoring view M_TABLE_PERSISTENCE_STATISTICS contains useful disk size information for tables and it is useful to have also historic information available. With the standalone statistics server histories were collected (GLOBAL_TABLE_PERSISTENCE_STATISTICS), but with the embedded statistics server this collection is&#160;deactivated between SAP HANA 1.00.74 and SAP HANA 1.00.92. It is recommended to re-activate it manually.</p>\r\n<p>The following checks&#160;can be set to&#160;'inactive' on purpose and don&#8217;t need to be activated:</p>\r\n<ul>\r\n<li>Alert_Check_Free_Physical_Mem (ID 1)</li>\r\n<li>Alert_Dec_Extractor_Status (ID 41)</li>\r\n<li>Alert_Plan_Cache_Size (ID 58)</li>\r\n<li>Alert_Row_Store_Version_Space_Overflow (ID 73)</li>\r\n<li>Alert_Metadata_Version_Space_Overflow (ID 74)</li>\r\n<li>Alert_Check_Database_Disk_Usage (ID 77)</li>\r\n<li>Alert_Check_DQ_Reference_Data_Expiring (ID 95)</li>\r\n<li>Alert_Check_DQ_Long_Running_Task_Plan (ID 96)</li>\r\n<li>Alert_Mon_Column_Tables_Record_Count_Incl (ID 117)</li>\r\n<li>Alert_Partitioned_Table_Record_Count_Incl (ID 127)</li>\r\n<li>Collector_Global_Dec_Extractor_Status (ID 5008)</li>\r\n<li>\r\n<div>Collector_Host_Connections (ID 5024)</div>\r\n</li>\r\n<li>\r\n<div>Collector_Host_Connection_Statistics (ID 5025)</div>\r\n</li>\r\n<li>\r\n<div>Collector_Host_Record_Locks (ID 5033)</div>\r\n</li>\r\n<li>\r\n<div>Collector_Host_CS_Unloads (ID 5035)</div>\r\n</li>\r\n<li>Collector_Host_CS_Indexes (ID 5059)</li>\r\n</ul>\r\n<p>The following check is linked to the SAP HANA consistency check based on CHECK_TABLE_CONSISTENCY (SAP Note <a target=\"_blank\" href=\"/notes/2116157\">2116157</a>) and can optionally be activated if no other consistency check approach is used:</p>\r\n<ul>\r\n<li>Alert_Table_Consistency (ID 83)</li>\r\n<li>Collector_Global_Table_Consistency (ID 5047)</li>\r\n</ul>\r\n<p>In order to activate relevant history collections you can run the following command (usually not required in system databases):</p>\r\n<pre>UPDATE \"_SYS_STATISTICS\".\"STATISTICS_SCHEDULE\" SET STATUS = 'Idle' WHERE STATUS = 'Inactive' AND ID NOT IN (1, 41, 58, 73, 74, 77, 83, 95, 96, 117, 127, 5008, 5024, 5025, 5033, 5035, 5047, 5059)</pre>\r\n<p><strong>Attention:</strong> Be aware that in system databases of MDC environments (SAP Note <a target=\"_blank\" href=\"/notes/2101244\">2101244</a>) several other actions are deactivated per default starting with SAP HANA 2.0 SPS 04. This is normal and there is usually no need for adjustments.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L12\"></a>&#65279;12. Why are some statistics server actions disabled?</h3>\r\n<p>Depending on the type and release of the statistics server alerts and history collections are disabled in the following scenarios:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Type</strong></td>\r\n<td style=\"text-align: center;\"><strong>Revision</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SSS</td>\r\n<td>general</td>\r\n<td>The standalone statistics sever never disables individual actions.</td>\r\n</tr>\r\n<tr>\r\n<td>ESS</td>\r\n<td>\r\n<p>1.0 SPS 07<br />1.0 SPS 08 &lt;= Rev. 85.03<br />1.0 SPS 09 &lt;= Rev. 92</p>\r\n</td>\r\n<td>\r\n<p>Individual actions are disabled if the previous execution is not finished when the next&#160;execution is started.</p>\r\n<p>Disabled actions won't be enabled automatically, you can only&#160;enable them manually.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>ESS</td>\r\n<td>1.0 SPS 08 &gt;= Rev. 85.04<br />1.0 Rev. &gt;= 93</td>\r\n<td>\r\n<p>Individual actions are disabled if the previous execution is not finished when the next&#160;execution is started.</p>\r\n<p>Disabled actions are skipped at least once and at least for one hour. Afterwards they are automatically&#160;enabled by the statistics server. So roughly speaking actions with a high execution frequency are&#160;enabled after one hour and actions with a low execution frequency are&#160;enabled after they have been skipped once.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L13\"></a>&#65279;13. How can I&#160;take care for&#160;disabled statistics server actions?</h3>\r\n<p>Disabled statistics server actions are particularly critical with ESS and Revisions &lt;= 1.00.92, because no automatic enabling happens.</p>\r\n<p>Disabled statistics server actions are reported with check ID M0717 (\"Number of disabled actions\") of <em>SQL: \"HANA_Configuration_MiniChecks\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>).</p>\r\n<p>The most frequent root cause for disabled statistics server actions are timeouts. You can check via <em>SQL: \"HANA_StatisticsServer_Schedule\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>) for STATUS, which contains \"Disabled\" and the related reason, e.g.&#160;\"Disabled (timeout)\".</p>\r\n<p>If you find disabled actions due to timeout, you can enable them with the following command:</p>\r\n<pre>UPDATE \"_SYS_STATISTICS\".\"STATISTICS_SCHEDULE\" SET STATUS = 'Idle' WHERE STATUS = 'Disabled' AND STATUSREASON = 'timeout'</pre>\r\n<p>If the same check disables itself repeatedly due to timeouts, you have to analyze the underlying performance problem. A timeout is triggered, if the previous execution is still active when the next execution is triggered, so as a workaround you can reduce the risk of timeouts by reducing the execution frequency of the involved action:</p>\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET INTERVALLENGTH = &lt;interval_s&gt; WHERE ID = &lt;action_id&gt;</pre>\r\n<p>Another reason for disabled statistics server actions can be invalid underlying procedures. A general activation of actions regardless of the timeout reason can be performed with the following command:</p>\r\n<pre>UPDATE \"_SYS_STATISTICS\".\"STATISTICS_SCHEDULE\" SET STATUS = 'Idle' WHERE STATUS = 'Disabled'</pre>\r\n<p>See SAP Note <a target=\"_blank\" href=\"/notes/2113228\">2113228</a>&#160;for more information.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L14\"></a>&#65279;14. How can I check for problems in the area of the statistics server?</h3>\r\n<p>You can&#160;run <em>SQL: \"HANA_Configuration_MiniChecks\"</em> (SAP Notes <a target=\"_blank\" href=\"/notes/1999993\">1999993</a>&#160;and <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>) and have a look at the check results for the \"STATISTICS SERVER\" section starting with CHECK_ID 700. If potentially critical issues are reported (C = 'X'), you can perform an analysis as described in SAP Note <a target=\"_blank\" href=\"/notes/1999993\">1999993</a>&#160;and SAP Notes referenced there, and take appropriate actions if required.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L15\"></a>&#65279;15. What is a good retention time for statistics server histories?</h3>\r\n<p>Experience has shown that a retention time of 6 weeks (42 days) is a good compromise between size and analysis capabilities. Having 6 weeks of history available it is always possible to compare current issues with a time of the same load one month back (e.g. month's end closing).</p>\r\n<p>The retention time for most of the statistics server histories is 42 days on tenant level and 21 days on system DB level. Check ID M0750 (\"Stat. server tables with retention &lt; 42 days\") of <em>SQL: \"HANA_Configuration_MiniChecks\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>) reports tables where the retention time is too small.</p>\r\n<p>For the following collectors a reduced retention time is useful or acceptable:</p>\r\n<ul>\r\n<li>5008 (Collector_Global_Dec_Extractor_Status)</li>\r\n<li>5024 (Collector_Host_Connections)</li>\r\n<li>5025 (Collector_Host_Connection_Statistics)</li>\r\n<li>5026 (Collector_Global_Internal_Events)</li>\r\n<li>5033 (Collector_Host_Record_Locks)</li>\r\n<li>5035 (Collector_Host_CS_Unloads)</li>\r\n<li>5600 (Collector_Streaming_Projects_Statistics)</li>\r\n<li>5601 (Collector_Streaming_Publishers_Statistics)</li>\r\n<li>5602 (Collector_Streaming_Subscribers_Statistics)</li>\r\n<li>5603 (Collector_Streaming_Streams_Statistics)</li>\r\n<li>5604 (Collector_Streaming_Project_Connections_Statistics)</li>\r\n</ul>\r\n<p>For all others you can adjust the retention time to 42 days with the following command:</p>\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET RETENTION_DAYS_CURRENT = 42<br />WHERE (RETENTION_DAYS_CURRENT &lt; 42) OR (RETENTION_DAYS_CURRENT IS NULL) AND ID NOT IN ( 5008, 5010, 5024, 5025, 5026, 5033, 5035, 5600, 5601, 5602, 5603, 5604 ) AND ID &gt;= 1000</pre>\r\n<p>Having a higher retention&#160;value (e.g. 365 days) for certain histories is usually possible without problems. You can take the original table size with 42 days history and estimate the expected target&#160;table size for the new history retention (e.g. about factor 9 for 365 days). Sizes of up to a few GB can still be justified.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L16\"></a>&#65279;16. What can I do if a statistics server action fails with \"301: unique constraint violated\"?</h3>\r\n<p>As a workaround the primary keys of the involved histories can be dropped as&#160;described in&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2143679\">2143679</a>.</p>\r\n<p>Duplicate keys are typically related to statistics server bugs. The following scenarios are currently known:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Tables</strong></td>\r\n<td style=\"text-align: center;\"><strong>SAP Note / issue number</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details&#160;</strong></td>\r\n<td style=\"text-align: center;\"><strong>Fixed with</strong></td>\r\n</tr>\r\n<tr>\r\n<td>GLOBAL_OUT_OF_MEMORY_EVENTS</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>Collector 5053 (Collector_Global_Out_Of_Memory_Events) can suffer from unique constraint related terminations.</td>\r\n<td style=\"text-align: center;\">***********<br />2.00.041</td>\r\n</tr>\r\n<tr>\r\n<td>COLLECTOR_GLOBAL_ROWSTORE_TABLES_SIZE_BASE</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>Row store tables with several containers can result in duplicate entries in M_RS_TABLES with SAP HANA 2.00.040 - 2.00.042. As a workaround you can reorganize these tables, e.g. using <em>SQL: \"HANA_Tables_RowStore_TablesWithMultipleContainers\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>).</td>\r\n<td style=\"text-align: center;\">2.00.043</td>\r\n</tr>\r\n<tr>\r\n<td>COLLECTOR_HOST_CS_INDEXES</td>\r\n<td style=\"text-align: center;\">235390</td>\r\n<td>The primary key of the underlying table HOST_CS_INDEXES_BASE was designed without SCHEMA_NAME and without TABLE_NAME / PART_ID. Therefore duplicates are possible when indexes of the same name exist in more than one schema or when indexes are defined on partitioned tables.</td>\r\n<td style=\"text-align: center;\">2.00.047</td>\r\n</tr>\r\n<tr>\r\n<td>COLLECTOR_HOST_CS_INDEXES</td>\r\n<td style=\"text-align: center;\">259404</td>\r\n<td>The collector is based on view M_CS_INDEXES_FOR_STATISTICSSERVER_ that lacks a PART_ID join condition. As a consequence duplicate keys are possible in context of partitioned tables.</td>\r\n<td style=\"text-align: center;\">in progress</td>\r\n</tr>\r\n<tr>\r\n<td>COLLECTOR_TEL_INIFILE_CONTENTS</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>The parameter telemetry collector can terminate with unique constraint violation when parameters are maintained on HOST level (SAP Note <a target=\"_blank\" href=\"/notes/2186744\">2186744</a>).</td>\r\n<td style=\"text-align: center;\">2.00.040</td>\r\n</tr>\r\n<tr>\r\n<td>HOST_JOB_HISTORY_BASE</td>\r\n<td style=\"text-align: center;\"><a target=\"_blank\" href=\"/notes/2347981\">2347981</a></td>\r\n<td>&nbsp;</td>\r\n<td style=\"text-align: center;\">1.00.122</td>\r\n</tr>\r\n<tr>\r\n<td>HOST_LONG_RUNNING_STATEMENTS_BASE</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>&nbsp;</td>\r\n<td style=\"text-align: center;\">***********<br />2.00.023</td>\r\n</tr>\r\n<tr>\r\n<td>HOST_SERVICE_THREAD_SAMPLES_BASE</td>\r\n<td style=\"text-align: center;\"><a target=\"_blank\" href=\"/notes/2137142\">2137142</a></td>\r\n<td>Several threads with THREAD_ID -1 may exist in M_SERVICE_THREAD_SAMPLES.</td>\r\n<td style=\"text-align: center;\">1.00.90</td>\r\n</tr>\r\n<tr>\r\n<td>HOST_SQL_PLAN_CACHE_OVERVIEW_BASE</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>&nbsp;</td>\r\n<td style=\"text-align: center;\">***********<br />2.00.036</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>HOST_VOLUME_IO_DETAILED_STATISTICS_BASE<br />HOST_VOLUME_IO_RETRY_STATISTICS_BASE<br />HOST_VOLUME_IO_TOTAL_STATISTICS_BASE</p>\r\n</td>\r\n<td style=\"text-align: center;\"><a target=\"_blank\" href=\"/notes/2127852\">2127852</a></td>\r\n<td>&#160;</td>\r\n<td style=\"text-align: center;\">***********</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\">&#160;</td>\r\n<td>The primary key doesn't contain table partitions. If the same alert is issued for different partitions of the same table, the \"unique constraint violated\" error is issued.</td>\r\n<td style=\"text-align: center;\">1.00.110</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>\r\n<p>In context of alert ID&#160;39 (long running statements) the unique index is defined on SNAPSHOT_ID, ALERT_ID and TRANSACTION_ID. This is not generally sufficient in scale-out systems, because the same transaction ID can exist on different hosts.</p>\r\n</td>\r\n<td style=\"text-align: center;\">***********<br />***********<br />2.00.023</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>\r\n<p>In context of alert ID 49 (blocked transactions) duplicate keys are possible if different update transaction IDs are blocked by the same blocker.</p>\r\n</td>\r\n<td style=\"text-align: center;\">***********</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>\r\n<p>In context of alert ID 49 (blocked transactions) duplicate keys are possible if the same connection ID exists on different scale-out nodes.</p>\r\n</td>\r\n<td style=\"text-align: center;\">2.00.047</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\">259567</td>\r\n<td>\r\n<p>In context of alert ID 49 (blocked transactions) duplicate keys are possible due to inadequate join operations.</p>\r\n</td>\r\n<td style=\"text-align: center;\">2.00.048.06<br />2.00.056</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\">248283</td>\r\n<td>\r\n<p>Alert ID 75 (Alert_Row_Store_Version_Space_Skew) can also suffer from \"unique constraint violated\" errors.</p>\r\n</td>\r\n<td style=\"text-align: center;\">***********<br />2.00.055</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\">253739</td>\r\n<td>\r\n<p>In context of alert ID 94 (Alert_Replication_Logreplay_Backlog) duplicate keys are possible if multiple secondary sites of the same primary site suffer from the backlog</p>\r\n</td>\r\n<td style=\"text-align: center;\">***********<br />2.00.053</td>\r\n</tr>\r\n<tr>\r\n<td>STATISTICS_ALERTS_BASE</td>\r\n<td style=\"text-align: center;\">312954</td>\r\n<td>\r\n<p>Alert ID 131 (Alert_Admission_Control_Enqueue_Event) can suffer from duplicate keys due to a missing join condition in the procedure definition.</p>\r\n</td>\r\n<td style=\"text-align: center;\"></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L17\"></a>&#65279;17. Is it possible to mark an alert as acknowledged or handled?</h3>\r\n<p>Currently there is no option to mark an alert as acknowledged or handled, so you may still see alerts that you have already handled and you may still get new alerts for topics you have already acknowledged and you are working on it. SAP is working on an implementation of this feature.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L18\"></a>&#65279;18. Are the default alert thresholds generally sufficient?</h3>\r\n<p>The alert thresholds can be checked via <em>SQL: \"HANA_StatisticsServer_Alerts_Definition\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>) and should be considered as initial proposal, but not as a general best practice. This means, you can adjust thresholds based on your needs. Typical adjustments are:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Alert ID</strong></td>\r\n<td style=\"text-align: center;\"><strong>Alert name</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">17</td>\r\n<td>Record count of non-partitioned column-store tables</td>\r\n<td>Alerts may be issued quite early recommending partitioning of tables. Taking into account the risk of side effects of partitioning, it can be useful to increase the thresholds and implement / extend partitioning only when the number of records gets closer to the 2 billion record limit. See SAP Note <a target=\"_blank\" href=\"/notes/2044468\">2044468</a>&#160;for more information related to partitioning.</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">27</td>\r\n<td>Record count of column-store table partitions</td>\r\n<td>Alerts may be issued quite early recommending partitioning of tables. Taking into account the risk of side effects of partitioning, it can be useful to increase the thresholds and implement / extend partitioning only when the number of records gets closer to the 2 billion record limit. See SAP Note <a target=\"_blank\" href=\"/notes/2044468\">2044468</a>&#160;for more information related to partitioning.</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">50</td>\r\n<td>Number of diagnosis files</td>\r\n<td>Up to SAP HANA 1.0 SPS 08 the number of trace files is counted on a system-wide basis and not per host. This can result in misleading alerts. As a workaround you can increase the threshold for the number of trace files. Starting with 1.0 SPS 09 this adjustment is normally no longer required.</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">77</td>\r\n<td>Database disk usage</td>\r\n<td>The default thresholds (e.g. 300 / 400 / 500 GB) may not match with your system size and so you can adjust them based on your needs.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Be aware that in SAP HANA Studio you can only adjust thresholds for the \"low\", \"medium\" and \"high\" level. Thresholds for the \"info\" level can&#160;be adjusted with SAP HANA Cockpit (SAP Note <a target=\"_blank\" href=\"/notes/2800006\">2800006</a>).</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L19\"></a>&#65279;19. How can the statistics server check intervals be adjusted?</h3>\r\n<p>Adjusting the interval of a specific alert or collector can be done by updating the STATISTICS_SCHEDULE table:</p>\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET INTERVALLENGTH = &lt;length_in_s&gt; WHERE ID =&#160;&lt;id&gt;</pre>\r\n<p>If you e.g. want to&#160;make sure that the&#160;history of blocked transactions (ID = 5020) is only collected every five minutes instead of every single minute, you can increase the interval from 60 to 300 seconds:</p>\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET INTERVALLENGTH = 300 WHERE ID =&#160;5020\r\n</pre>\r\n<p>It is generally recommended that the&#160;history of thread samples (ID = 5034) is only collected at least every 10 minutes in order to avoid too much data loss in case of a crash or shutdown (check ID M0751). If the current interval is higher you can reduce it to 10 minutes (600 seconds) with the following command:</p>\r\n<pre>UPDATE _SYS_STATISTICS.STATISTICS_SCHEDULE SET INTERVALLENGTH = 600 WHERE ID =&#160;5034</pre>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L20\"></a>&#65279;20. How can internal statistics server errors be analyzed?</h3>\r\n<p>Internal statistics server errors are reported with alert ID 0. You can display them by running <em>SQL: \"HANA_StatisticsServer_Alerts_History\" (ALERT_ID = 0)</em> available via SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>.</p>\r\n<p>Internal statistics server errors can result in a red icon in \"Check for disabled collectors\" in transaction DBACOCKPIT and in the detailed screen of this check you see \"Collectors are disabled: &lt;collector&gt;\".</p>\r\n<p>The following table contains typical internal statistics server errors and resolution steps:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Error</strong></td>\r\n<td style=\"text-align: center;\"><strong>Error text</strong></td>\r\n<td style=\"text-align: center;\"><strong>Action&#160;IDs</strong></td>\r\n<td style=\"text-align: center;\"><strong>Details</strong></td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">7</td>\r\n<td>feature not supported: invalid character encoding</td>\r\n<td style=\"text-align: center;\">5034</td>\r\n<td>\r\n<p>Due to&#160;the SAP HANA bug described in SAP Notes <a target=\"_blank\" href=\"/notes/2453766\">2453766</a>&#160;and <a target=\"_blank\" href=\"/notes/2478767\">2478767</a>&#160;certain column values of M_SERVICE_THREAD_SAMPLES can't be processed correctly when being historicized&#160;to HOST_SERVICE_THREAD_SAMPLES (SAP HANA &lt;= ***********, &lt;= ***********, 2.00.020). As a workaround the statistics server procedure can be adjusted based on the attachment to these SAP Notes.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">259</td>\r\n<td>invalid table name</td>\r\n<td style=\"text-align: center;\">600<br />601</td>\r\n<td>\r\n<p>This is a bug with SAP HANA *********** that is caused by the syntax change described in SAP Note <a target=\"_blank\" href=\"/notes/2241598\">2241598</a>. You can correct the procedures based on SAP Note <a target=\"_blank\" href=\"/notes/2259611\">2259611</a>.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">260</td>\r\n<td>invalid column name</td>\r\n<td style=\"text-align: center;\">5029</td>\r\n<td>\r\n<p>This is a bug with SAP HANA 1.00.102 to ***********, see SAP Note <a target=\"_blank\" href=\"/notes/2239843\">2239843</a>&#160;for more information.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">274</td>\r\n<td>inserted value too large for column</td>\r\n<td style=\"text-align: center;\">5009</td>\r\n<td>\r\n<p>This error can happen with SAP HANA 2.00.000 to 2.00.011 and it is typically linked to an long FILESYSTEM_TYPE value caused by the fact that a file system type is marked as unsupported (e.g. 'UNSUPPORTED (ext4)'). See SAP Note <a target=\"_blank\" href=\"/notes/1999930\">1999930</a>&#160;(\"Are all types of filesystems supported for SAP HANA?\") and make sure that only supported file systems are in place.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">274</td>\r\n<td>inserted value too large for column</td>\r\n<td style=\"text-align: center;\">5705</td>\r\n<td>\r\n<p>This termination can happen with SAP HANA 2.00.070 - 2.00.071 in context of&#160;Collector_Tel_Out_Of_Memory_Events when the string&#160;TOTAL_STATEMENT_MEMORY_LIMIT_FROM_WORKLOAD_CLASS is inserted into column EVENT_REASON, because it exceeds the defined limit of 32 characters (issue number 308466). You can use the normal OOM history HOST_OUT_OF_MEMORY_EVENTS collected by&#160;Collector_Global_Out_Of_Memory_Events that already uses a sufficient EVENT_REASON length limit of 64 characters.</p>\r\n<p>Analysis commands available via SAP Note&#160;<a target=\"_blank\" href=\"/notes/1969700\">1969700</a>&#160;generally rely on the properly working history HOST_OUT_OF_MEMORY_EVENTS, so there is no risk of missing information.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">287</td>\r\n<td>cannot insert NULL or update to NULL</td>\r\n<td style=\"text-align: center;\">5031</td>\r\n<td>\r\n<p>This error can happen in context of column OBJECT_TYPE of table HOST_OBJECT_LOCKS_BASE when a primary key including column OBJECT_TYPE is in place. The primary key implicitly results in NOT NULL constraints on all involved columns while at the same time column OBJECT_TYPE of the source monitoring view M_OBJECT_LOCKS sometimes contains NULL values.</p>\r\n<p>With all current SAP HANA Revision levels the statistics server table HOST_OBJECT_LOCKS_BASE is delivered without primary key. You can manually drop it with the following command:</p>\r\n<pre>ALTER TABLE _SYS_STATISTICS.HOST_OBJECT_LOCKS_BASE DROP PRIMARY KEY</pre>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">301</td>\r\n<td>unique constraint violated</td>\r\n<td style=\"text-align: center;\">5015<br />5016<br />5017<br />5034<br />and others</td>\r\n<td>\r\n<p>See \"What can I do if the history collection fails with \"301: unique constraint violated\"?\" above.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">304</td>\r\n<td>division by zero undefined: search table error: [6859] AttributeEngine: divide by zero</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>\r\n<p>This error can be a consequence of check thresholds being erroneously set to 0. See SAP Note <a target=\"_blank\" href=\"/notes/2548692\">2548692</a>&#160;for details. Be aware that also alert IDs different from 3 can be responsible for the issue.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">339</td>\r\n<td>invalid number: not a valid number string</td>\r\n<td style=\"text-align: center;\">98</td>\r\n<td>\r\n<p>This error can happen for action 98 (ALERT_CHECK_CS_LOB_SPACE_RECLAIMS) in case a parameter like garbage_collect_daily_schedule_s or garbage_collect_interval_s is set to a non-number value, e.g. the empty string. Make sure that these parameters are properly configured, i.e. unset or set in line with the best practices in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2600030\">2600030</a>.</p>\r\n<p>Issue number 298734 exists to make sure that the alert implementation gets smarter and uses M_CONFIGURATION_PARAMETER_VALUES instead of M_INIFILE_CONTENTS for the parameter check. M_CONFIGURATION_PARAMETER_VALUES properly maps the empty string to the default value of 43200.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">403</td>\r\n<td>internal error: Failed to execute create and insert statement: General error: 129 transaction rolled back by an internal error: cannot change this transaction's access mode from read-only to update directly: please use \"SET TRANSACTION READ WRITE\" statement first</td>\r\n<td style=\"text-align: center;\">5049<br />5050</td>\r\n<td>\r\n<p>This error can happen with SAP HANA &lt;= *********** and &lt;= 2.00.063 for collectors COLLECTOR_HOST_LOAD_HISTORY_HOST and COLLECTOR_HOST_LOAD_HISTORY_SERVICE when temporary tables need to be created on secondary system replication sites in context of join relocation. See SAP Note <a target=\"_blank\" href=\"/notes/3221290\">3221290</a>&#160;for more details and possible workarounds and solutions.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">592</td>\r\n<td>not supported type conversion</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>\r\n<p>This error can be a consequence of check thresholds being erroneously set to a non-integer value. The actual problem value is part of the reported error, e.g.:</p>\r\n<pre>Not supported conversion from NString to BigInt\r\n('3749.000000000000000000' isn't a valid numeric value)</pre>\r\n<p>In this case a threshold for one alert was erroneously set to 3749.000000000000000000. A change to 3749 resolved the issue.</p>\r\n<p>You can find the configured thresholds in table _SYS_STATISTICS.STATISTICS_ALERT_THRESHOLDS.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">1299</td>\r\n<td>no data found</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>\r\n<p>This error can happen when alerts are configured inconsistently. Check metadata tables like _SYS_STATISTICS.STATISTICS_ALERT_THRESHOLDS and correct existing mistakes (e.g. missing severity levels).</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">6000</td>\r\n<td>missing smtp configuration in STATISTICS_PROPERTIES</td>\r\n<td style=\"text-align: center;\"></td>\r\n<td>\r\n<p>See SAP Note <a target=\"_blank\" href=\"/notes/2529478\">2529478</a>&#160;and make sure that the SMTP parameters are set based on your needs:</p>\r\n<pre>internal.smtp.port\r\ninternal.smtp.sender\r\ninternal.smtp.server</pre>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">&#160;</td>\r\n<td>&lt;action&gt; is disabled and will not be called</td>\r\n<td style=\"text-align: center;\">various</td>\r\n<td>\r\n<p>This message is recorded when SAP HANA has disabled a check due to a long runtime, see \"Why are some statistics server actions disabled?\" above for more details.</p>\r\n<p>If you want to understand why the long runtime happened, you can analyze the performance and activities of SAP HANA during the relevant time frame:</p>\r\n<ul>\r\n<li>Start time: Timestamp mentioned in&#160;alert minus check interval of impacted action</li>\r\n<li>End time: Timestamp mentioned in alert</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Example:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td style=\"text-align: center;\"><strong>Detail</strong></td>\r\n<td style=\"text-align: center;\"><strong>Value</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Alert</td>\r\n<td>603 is disabled and will not be called.</td>\r\n</tr>\r\n<tr>\r\n<td>Alert&#160;timestamp</td>\r\n<td>2016/02/24 09:25:14</td>\r\n</tr>\r\n<tr>\r\n<td>Check interval of action 603</td>\r\n<td>60 seconds</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Critical time frame</strong></td>\r\n<td><strong>2016/02/24 09:24:14 - 2016/02/24 09:25:14</strong></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Attention: </strong>The timestamp shown in the error message (e.g. \"12 is disabled and will not be called. Reason: timeout SNAPSHOT_ID: 2017-09-13 11:23:50.0000000.\") is UTC time, not local system time.</p>\r\n<p><strong>Attention:</strong> STATISTICS_ALERTS.ALERT_TIMESTAMP contains the UTC timestamp rather than the local system timestamp in older SAP HANA Revisions, so you should better check STATISTICS_ALERTS_BASE.</p>\r\n<p>Typical reasons for timeouts are:</p>\r\n<ul>\r\n<li>General system performance issues (SAP Note <a target=\"_blank\" href=\"/notes/2000000\">2000000</a>)</li>\r\n<li>Crash of SAP HANA services (SAP Note <a target=\"_blank\" href=\"/notes/2177064\">2177064</a>)</li>\r\n<li>Solution Manager requests blocking standard statistics server runs&#160;with record locks (SAP Note <a target=\"_blank\" href=\"/notes/1999998\">1999998</a>) on STATISTICS_SCHEDULE table: can be eliminated with new Solution Manager monitoring for SAP HANA (SAP Note <a target=\"_blank\" href=\"/notes/2374272\">2374272</a>) available for SAP HANA *********** and higher</li>\r\n<li>Secondary symptom of \"feature not supported: invalid character encoding\" errors related to check ID 5034 (see above)</li>\r\n<li>Secondary symptom of \"Not supported conversion from NString to Integer\" for large threshold values for action ID 27 (SAP Note <a target=\"_blank\" href=\"/notes/2962294\">2962294</a>)</li>\r\n<li>Secondary symptom of \"Invalid forwarding to datacenter\" that can happen for action IDs like 21, 30, 54, 78 and 89 that implicitly access the secondary system replication site. See SAP Note <a target=\"_blank\" href=\"/notes/2380176\">2380176</a>&#160;for more details about this error scenario.</li>\r\n<li>Timeouts for ID 5032 can be caused by a high amount of \"(unknown)\" objects in HOST_OBJECT_LOCK_STATISTICS_BASE. These entries can be deleted as described in question&#160;\"How can the memory requirements of the statistics server be minimized?\" of this FAQ.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L21\"></a>&#65279;21. What happens if the STATISTICS_SCHEDULE table is manually modified in a wrong way?</h3>\r\n<p>Sometimes updates on the STATISTICS_SCHEDULE table are necessary to adjust settings. Erroneous updates can have undesired effects, so you have to make sure that you only adjust the table in a correct way. For example, setting the STATE to a value different from 'Disabled', 'Idle', 'Inactive' or 'Scheduled' means that the action will no longer be executed. You can use <em>SQL: \"HANA_StatisticsServer_Schedule\"</em> <em>(ONLY_UNKNOWN_STATES = 'X')</em> available via SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>&#160;to detect faulty states.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L22\"></a>&#65279;22. How can I analyze and resolve SAP HANA alerts?</h3>\r\n<p>See SAP Note <a target=\"_blank\" href=\"/notes/2445867\">2445867</a>&#160;for instructions how to analyze&#160; and resolve SAP HANA alerts.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L23\"></a>&#65279;23. Is it possible to restart the statistics server?</h3>\r\n<p>In general the statistics server is restarted when the related SAP HANA service (indexserver or nameserver in MDC system DB) is restarted. For some reasons it is helpful to restart the statistics server individually, e.g.:</p>\r\n<ul>\r\n<li>Statistics server worker threads disappeared and so the statistics server no longer works</li>\r\n<li>Statistics server runs in statement memory limit OOMs due to a high amount of erroneously booked context memory (SAP Note <a target=\"_blank\" href=\"/notes/2584388\">2584388</a>)</li>\r\n</ul>\r\n<p>This restart is possible by temporarily setting the statistics server thread pool to 0:</p>\r\n<pre>&lt;service&gt;.ini -&gt; [statisticsserver] -&gt; threadpool = 0</pre>\r\n<p>See SAP Note <a target=\"_blank\" href=\"/notes/2584388\">2584388</a>&#160;for more details.</p>\r\n<p>See also SAP Note <a target=\"_blank\" href=\"/notes/2658611\">2658611</a>&#160;that describes additional approaches to restart and re-initialize statistics server components.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L24\"></a>&#65279;24. What is the difference of SNAPSHOT_ID and SERVER_TIMESTAMP in histories?</h3>\r\n<p>Statistics server histories typically contain the columns SNAPSHOT_ID and SERVER_TIMESTAMP, both containing a timestamp. The reasoning behind and the main differences are:</p>\r\n<ul>\r\n<li>SNAPSHOT_ID</li>\r\n<ul>\r\n<li>Scheduled start time of the history collection</li>\r\n<li>Mainly used for statistics server internal purposes</li>\r\n<li>May be used as part of a unique index</li>\r\n<li>UTC time to avoid duplicates in context of summer / winter time changes</li>\r\n<li>Identical for all histories being collected in one run</li>\r\n</ul>\r\n<li>SERVER_TIMESTAMP</li>\r\n<ul>\r\n<li>Time when history data was actually collected (typically shortly after the scheduled start time of the history collection)</li>\r\n</ul>\r\n<ul>\r\n<li>Used for tool-based history evaluation (e.g. SQL statements available via SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>)</li>\r\n<li>Local system time</li>\r\n<li>Can vary between different histories collected in one run</li>\r\n</ul>\r\n</ul>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L25\"></a>&#65279;25. Why do different statistics server actions run in the system database and tenants?</h3>\r\n<p>In multitenant environments (SAP Note <a target=\"_blank\" href=\"/notes/2101244\">2101244</a>) different statistics server actions are run in the system database and the tenants based on predefined best practices. It is controlled by the internal.sizing.profile entry in the statistics server control table STATISTICS_PROPERTIES (SAP Note <a target=\"_blank\" href=\"/notes/2529478\">2529478</a>). Adjusting this parameter is possible, but it should normally not be required.</p>\r\n<p>In MCD system databases several alerts are deactivated per default for performance reasons so you shouldn't use the system database for development or production operations. If you want to activate all alerts for specific reasons, you can adjust the profile from SYSTEMDB to M:</p>\r\n<pre>UPDATE \"_SYS_STATISTICS\".STATISTICS_PROPERTIES SET VALUE = 'M' WHERE KEY = 'internal.sizing.profile'</pre>\r\n<p>When you want to return to the original state, you can switch back to SYSTEMDB:</p>\r\n<pre>UPDATE \"_SYS_STATISTICS\".STATISTICS_PROPERTIES SET VALUE = 'SYSTEMDB' WHERE KEY = 'internal.sizing.profile'</pre>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L26\"></a>&#65279;26. How can I monitor and tackle issues with event acknowledgement?</h3>\r\n<p>Events are an internal mechanism to inform SAP HANA components like the statistics server about issues, e.g. system replication issues.</p>\r\n<p>You can evaluate existing events via <em>SQL: \"HANA_Events\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>).</p>\r\n<p>Check ID M0720 of the SAP HANA Mini Checks (SAP Note <a target=\"_blank\" href=\"/notes/1999993\">1999993</a>) reports an issue in case events are not purged in time.</p>\r\n<p>In general events should be handled properly, exceptions should always be analyzed. The following known issues and exceptions exist:</p>\r\n<ul>\r\n<li>SAP HANA &lt;= ***********: General problems with event cleanup</li>\r\n<li>SAP HANA &gt;= 2.00.040: It is normal that&#160;a few&#160;AddressspaceUsage events exist on a permanent basis, so they can be ignored.</li>\r\n</ul>\r\n<p>It is possible to use SAP HANACleaner (SAP Note <a target=\"_blank\" href=\"/notes/2399996\">2399996</a>) in order to schedule an automatic cleanup of events.</p>\r\n<p>Alternatively you can manually acknowledge the events using hdbcons (SAP Note <a target=\"_blank\" href=\"/notes/2222218\">2222218</a>). Use \"-e &lt;process&gt;\" or \"-p &lt;pid&gt;\" to individually connect to the services with a high number of events in M_EVENTS:</p>\r\n<pre>hdbcons \"event acknowledge -t AddressspaceUsage\"<br />hdbcons \"event acknowledge -t DiskFull\"<br />hdbcons \"event acknowledge -t ReplicationError\"<br />hdbcons \"event acknowledge -t SRConfigurationParameterMismatch\"<br />hdbcons \"event acknowledge -t SRConnectionClosed\"</pre>\r\n<p>Afterwards you can delete the acknowledged events:</p>\r\n<pre>ALTER SYSTEM DELETE ALL HANDLED EVENTS\r\n</pre>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L27\"></a>&#65279;27. How can I check the state of the statistics server and restart it if required?</h3>\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/2896127\">2896127</a>&#160;describes how to check the state of the statistics server and steps to activate it again.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L28\"></a>&#65279;28. Can Native Storage Extension (NSE) be used in context of the&#160;statistics server tables?</h3>\r\n<p>When Native Storage Extension (NSE, SAP Note <a target=\"_blank\" href=\"/notes/2799997\">2799997</a>) is used, it is recommended to configure it also for the column store tables of the statistics server. See SAP Note <a target=\"_blank\" href=\"/notes/2983008\">2983008</a>&#160;for more details.</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L29\"></a>&#65279;29. Are there SQLScript version requirements for the statistics server?</h3>\r\n<p>For SAP HANA &gt;= 2.0 the statistics server uses SQLScript&#160;version 3 (v3). If&#160;for some reasons (e.g. SAP Note <a target=\"_blank\" href=\"/notes/2924907\">2924907</a>) an earlier SQLScript version is configured (indexserver.ini -&gt;&#160;[sqlscript] -&gt; kernel_mode), the statistics server may terminate with unspecific errors like \"Error during installation: SQL error $code$: $text$\".</p>\r\n<h3 data-toc-skip><a target=\"_blank\" name=\"L30\"></a>&#65279;30. Do statistics server histories also contain information for remote system replication sites?</h3>\r\n<p>Up to SAP HANA 2.0 SPS 05 the statistics server histories only contain information for the local database. Starting with SAP HANA 2.0 SPS 06 and in SAP HANA Cloud also data of other system replication sites (SAP Note ) is collected for most histories (and some alerts). The system replication site is indicated in column SITE_ID of the history tables. For technical reasons the following histories don't contain information of other system replication sites:</p>\r\n<ul>\r\n<li>Consistency checks (GLOBAL_CATALOG_CONSISTENCY, GLOBAL_TABLE_CONSISTENCY)</li>\r\n<li>SQL cache (HOST_SQL_PLAN_CACHE, HOST_SQL_PLAN_CACHE_OVERVIEW)</li>\r\n<li>Job history (HOST_JOB_HISTORY)</li>\r\n</ul>\r\n<p>Analysis commands of SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>&#160;are provided in specific versions for SAP HANA &gt;= 2.0 SPS 06 that include the possibility to filter and / or aggregate the system replication site.</p>\r\n<p>You can use <em>SQL: \"HANA_StatisticsServer_Schedule\"</em> (SAP Note <a target=\"_blank\" href=\"/notes/1969700\">1969700</a>) in order to determine to what extent statistics server actions consider remote system replication sites. Depending on the value in column EXT_TYPE the behavior is as follows:</p>\r\n<ul>\r\n<li>EXT_TYPE = '': Only primary site is considered</li>\r\n<li>EXT_TYPE = 'SR': All kinds of remote system replication sites are considered</li>\r\n<li>EXT_TYPE = 'AA': Remote system replication sites of type Active/Active (read enabled) are considered</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p style=\"padding-left: 30px;\">GLOBAL_DEC_EXTRACTOR_STATUS<br />GLOBAL_DISKS<br />GLOBAL_INTERNAL_EVENTS<br />GLOBAL_PERSISTENCE_STATISTICS<br />GLOBAL_ROWSTORE_TABLES_SIZE<br />GLOBAL_TABLE_PERSISTENCE_STATISTICS<br />HOST_BLOCKED_TRANSACTIONS<br />HOST_COLUMN_TABLES_PART_SIZE<br />HOST_CONNECTIONS<br />HOST_CONNECTION_STATISTICS<br />HOST_CS_UNLOADS<br />HOST_DATA_VOLUME_PAGE_STATISTICS<br />HOST_DATA_VOLUME_SUPERBLOCK_STATISTICS<br />HOST_DELTA_MERGE_STATISTICS<br />HOST_HEAP_ALLOCATORS<br />HOST_LONG_IDLE_CURSOR<br />HOST_LONG_RUNNING_STATEMENTS<br />HOST_LONG_SERIALIZABLE_TRANSACTION<br />HOST_OBJECT_LOCKS<br />HOST_OBJECT_LOCK_STATISTICS<br />HOST_ONE_DAY_FILE_COUNT<br />HOST_RECORD_LOCKS<br />HOST_RESOURCE_UTILIZATION_STATISTICS<br />HOST_RS_MEMORY<br />HOST_SAVEPOINTS<br />HOST_SERVICE_COMPONENT_MEMORY<br />HOST_SERVICE_MEMORY<br />HOST_SERVICE_STATISTICS<br />HOST_SERVICE_THREAD_SAMPLES<br />HOST_SQL_PLAN_CACHE<br />HOST_SQL_PLAN_CACHE_OVERVIEW<br />HOST_UNCOMMITTED_WRITE_TRANSACTION<br />HOST_VOLUME_FILES<br />HOST_VOLUME_IO_DETAILED_STATISTICS<br />HOST_VOLUME_IO_RETRY_STATISTICS<br />HOST_VOLUME_IO_TOTAL_STATISTICS<br />HOST_WORKLOAD<br />STATISTICS_ALERTS<br />STATISTICS_ALERT_INFORMATION<br />STATISTICS_ALERT_THRESHOLDS<br />STATISTICS_EMAILRECIPIENTS<br />STATISTICS_EMAIL_PROCESSING<br />STATISTICS_OBJECTS<br />STATISTICS_PROPERTIES<br />STATISTICS_SCHEDULE<br />is disabled and will not be called</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "HAN-DB (SAP HANA Database)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002147247/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2970921", "RefComponent": "HAN-DB-MON", "RefTitle": "HANA CPU spikes to 80-100%", "RefUrl": "/notes/2970921"}, {"RefNumber": "2962294", "RefComponent": "HAN-DB-MON", "RefTitle": "HANA Alert 27 is disabled and will not be called. Reason: timeout SNAPSHOT_ID: <timestamp>", "RefUrl": "/notes/2962294"}, {"RefNumber": "2800006", "RefComponent": "HAN-CPT-CPT2", "RefTitle": "FAQ: SAP HANA Cockpit", "RefUrl": "/notes/2800006"}, {"RefNumber": "2799997", "RefComponent": "HAN-DB-NSE", "RefTitle": "FAQ: SAP HANA Native Storage Extension (NSE)", "RefUrl": "/notes/2799997"}, {"RefNumber": "2600030", "RefComponent": "HAN-DB", "RefTitle": "Parameter Recommendations in SAP HANA Environments", "RefUrl": "/notes/2600030"}, {"RefNumber": "2548692", "RefComponent": "HAN-DB-MON", "RefTitle": "<PERSON><PERSON> in HANA Studio '[3] 304 division by zero undefined: search table error: [6859] AttributeEngine: divide by zero'", "RefUrl": "/notes/2548692"}, {"RefNumber": "2529478", "RefComponent": "HAN-DB", "RefTitle": "How-To: Configuring SAP HANA Statistics Server Parameters", "RefUrl": "/notes/2529478"}, {"RefNumber": "2505218", "RefComponent": "HAN-DB-BAC", "RefTitle": "Large Log Backups due to large backup catalog", "RefUrl": "/notes/2505218"}, {"RefNumber": "2445867", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Interpreting and Resolving SAP HANA Alerts", "RefUrl": "/notes/2445867"}, {"RefNumber": "2399996", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Configuring automatic SAP HANA Cleanup with SAP HANACleaner", "RefUrl": "/notes/2399996"}, {"RefNumber": "2380176", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Database Trace", "RefUrl": "/notes/2380176"}, {"RefNumber": "2347981", "RefComponent": "HAN-DB-MON", "RefTitle": "TrexUpdate failed on table '_SYS_STATISTICS:HOST_JOB_HISTORY_BASE'", "RefUrl": "/notes/2347981"}, {"RefNumber": "2343366", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2343366"}, {"RefNumber": "2243158", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2243158"}, {"RefNumber": "2239843", "RefComponent": "HAN-DB", "RefTitle": "Error: Collector_Host_Sql_Plan_Cache: invalid column name: AVG_CHILD_THREAD_COUNT", "RefUrl": "/notes/2239843"}, {"RefNumber": "2223237", "RefComponent": "HAN-DB", "RefTitle": "Troubleshooting HANA Embedded Statistics Server Migration - decision tree", "RefUrl": "/notes/2223237"}, {"RefNumber": "2222250", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Workload Management", "RefUrl": "/notes/2222250"}, {"RefNumber": "2222218", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Database Server Management Console (hdbcons)", "RefUrl": "/notes/2222218"}, {"RefNumber": "2222110", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Load History", "RefUrl": "/notes/2222110"}, {"RefNumber": "2186744", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Parameters", "RefUrl": "/notes/2186744"}, {"RefNumber": "2185556", "RefComponent": "HAN-CPT-ADM", "RefTitle": "FAQ: SAP HANA Cockpit (delivered with SAP HANA 1.0)", "RefUrl": "/notes/2185556"}, {"RefNumber": "2180119", "RefComponent": "HAN-DB-SDA", "RefTitle": "FAQ: SAP HANA Smart Data Access", "RefUrl": "/notes/2180119"}, {"RefNumber": "2177064", "RefComponent": "HAN-DB-ENG", "RefTitle": "FAQ: SAP HANA Service Restarts and Crashes", "RefUrl": "/notes/2177064"}, {"RefNumber": "2159435", "RefComponent": "HAN-DB", "RefTitle": "How-To: Keeping SAP HANA Row Store in Memory when restarting", "RefUrl": "/notes/2159435"}, {"RefNumber": "2143679", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Removing Primary Keys of SAP HANA Statistics Server Histories", "RefUrl": "/notes/2143679"}, {"RefNumber": "2137142", "RefComponent": "HAN-DB-MON", "RefTitle": "HANA Error: 301 unique constraint violated: TrexUpdate failed on table \"_SYS_STATISTICS:HOST_SERVICE_THREAD_SAMPLES_BASE\"", "RefUrl": "/notes/2137142"}, {"RefNumber": "2133799", "RefComponent": "HAN-DB-MON", "RefTitle": "Analyzing problems with delivery of e-mails for Alerts raised by the SAP HANA Embedded Statisticsserver checks", "RefUrl": "/notes/2133799"}, {"RefNumber": "2127852", "RefComponent": "HAN-DB-MON", "RefTitle": "HANA Error: 301 unique constraint violated: TrexUpdate failed on table", "RefUrl": "/notes/2127852"}, {"RefNumber": "2116157", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Consistency Checks and Corruptions", "RefUrl": "/notes/2116157"}, {"RefNumber": "2114710", "RefComponent": "HAN-DB-MON", "RefTitle": "FAQ: SAP HANA Threads and Thread Samples", "RefUrl": "/notes/2114710"}, {"RefNumber": "2113228", "RefComponent": "HAN-DB-MON", "RefTitle": "SAP HANA: Embedded statistics server is not correctly purging", "RefUrl": "/notes/2113228"}, {"RefNumber": "2101244", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Multitenant Database Containers (MDC)", "RefUrl": "/notes/2101244"}, {"RefNumber": "2084747", "RefComponent": "HAN-DB", "RefTitle": "How-To: Disabling Memory-intensive Data Collections of standalone SAP HANA Statistics Server", "RefUrl": "/notes/2084747"}, {"RefNumber": "2081609", "RefComponent": "HAN-DB-MON", "RefTitle": "How to Handle HANA Alert 0: 'Internal statistics server problem'", "RefUrl": "/notes/2081609"}, {"RefNumber": "2044468", "RefComponent": "HAN-DB-ENG", "RefTitle": "FAQ: SAP HANA Partitioning", "RefUrl": "/notes/2044468"}, {"RefNumber": "2036630", "RefComponent": "HAN-DB-MON", "RefTitle": "statisticsserver migration fails with 'error installing' / 'SQL error 129'", "RefUrl": "/notes/2036630"}, {"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002"}, {"RefNumber": "2000000", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Performance Optimization", "RefUrl": "/notes/2000000"}, {"RefNumber": "1999998", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Lock Analysis", "RefUrl": "/notes/1999998"}, {"RefNumber": "1999993", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Interpreting SAP HANA Mini Check Results", "RefUrl": "/notes/1999993"}, {"RefNumber": "1999930", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA I/O Analysis", "RefUrl": "/notes/1999930"}, {"RefNumber": "3221290", "RefComponent": "HAN-DB", "RefTitle": "Internal Statistical Transaction is Rolled Back Due to Failure to Create a Temporary Table for Join Relocation", "RefUrl": "/notes/3221290"}, {"RefNumber": "2983008", "RefComponent": "HAN-DB", "RefTitle": "Enable/Disable NSE (PAGE LOADABLE) for column tables of HANA statistics server", "RefUrl": "/notes/2983008"}, {"RefNumber": "2924907", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Results in SQLScript of SAP HANA 2 SPS04 when Variables with Same Prefix are Used", "RefUrl": "/notes/2924907"}, {"RefNumber": "2896127", "RefComponent": "HAN-DB", "RefTitle": "HANA Statistics Server – Checking the status", "RefUrl": "/notes/2896127"}, {"RefNumber": "2658611", "RefComponent": "HAN-DB-MON", "RefTitle": "Embedded Statistics Service Control in live operation", "RefUrl": "/notes/2658611"}, {"RefNumber": "2584388", "RefComponent": "HAN-DB", "RefTitle": "High Memory Usage in Allocator Connection/XXXXXX/Statement/YYYYYYYY/IMPLICIT by User _SYS_STATISTICS", "RefUrl": "/notes/2584388"}, {"RefNumber": "2478767", "RefComponent": "HAN-DB", "RefTitle": "Selecting Service Thread Sample Data Fails With an \"invalid character encoding\" Error Due to Columns PASSPORT_ROOTCONTEXT_ID or PASSPORT_CONNECTION_ID", "RefUrl": "/notes/2478767"}, {"RefNumber": "2453766", "RefComponent": "HAN-DB", "RefTitle": "Selecting Service Thread Sample Data Fails With an \"invalid character encoding\" Error Due to Columns PASSPORT_ACTION or PASSPORT_COMPONENT_NAME", "RefUrl": "/notes/2453766"}, {"RefNumber": "2374272", "RefComponent": "HAN-DB-MON", "RefTitle": "Enabling new HANA Monitoring mechanism for Solution Manager/Focused Run", "RefUrl": "/notes/2374272"}, {"RefNumber": "2092033", "RefComponent": "HAN-DB-MON", "RefTitle": "Embedded Statistics Service Migration Guide", "RefUrl": "/notes/2092033"}, {"RefNumber": "1969700", "RefComponent": "HAN-DB", "RefTitle": "SQL Statement Collection for SAP HANA", "RefUrl": "/notes/1969700"}, {"RefNumber": "1917938", "RefComponent": "HAN-DB-ENG", "RefTitle": "Migration of the statistics server for Revision 74 or higher", "RefUrl": "/notes/1917938"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Embedded Statistics Service Workload Class", "RefUrl": "https://help.sap.com/viewer/85e6beaa64484eb88b47b34eabdf1326/latest/en-US/3215e1e1400c4d42afc2c36bf7374a4c.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Embedded Statistics Server Views", "RefUrl": "http://help.sap.com/saphelp_hanaplatform/helpdata/en/d2/34eedbd29510148efbf332391de7fd/content.htm"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP HANA Administration Guide", "RefUrl": "http://help.sap.com/hana/SAP_HANA_Administration_Guide_en.pdf"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2800030", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Kernel Profiler", "RefUrl": "/notes/2800030 "}, {"RefNumber": "1999998", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Lock Analysis", "RefUrl": "/notes/1999998 "}, {"RefNumber": "2697558", "RefComponent": "HAN-DB-BAC", "RefTitle": "Increased Memory Consumption in Allocator M_DEV_BACKUP_CATALOG_LOG_FILES", "RefUrl": "/notes/2697558 "}, {"RefNumber": "2800055", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Kernel Sentinel", "RefUrl": "/notes/2800055 "}, {"RefNumber": "3096705", "RefComponent": "HAN-DB-PERF", "RefTitle": "Regular high CPU usage caused by statistics server", "RefUrl": "/notes/3096705 "}, {"RefNumber": "2600076", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Inverted Individual Indexes", "RefUrl": "/notes/2600076 "}, {"RefNumber": "2600030", "RefComponent": "HAN-DB", "RefTitle": "Parameter Recommendations in SAP HANA Environments", "RefUrl": "/notes/2600030 "}, {"RefNumber": "3030821", "RefComponent": "HAN-DB-MON", "RefTitle": "Cockpit does not show longer history than 10 days for performance monitor history data.", "RefUrl": "/notes/3030821 "}, {"RefNumber": "2800020", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Triggers", "RefUrl": "/notes/2800020 "}, {"RefNumber": "2995995", "RefComponent": "HAN-DB", "RefTitle": "How-To: Disable Table Consistency Checks", "RefUrl": "/notes/2995995 "}, {"RefNumber": "2766055", "RefComponent": "HAN-DB-MON", "RefTitle": "SAP HANA Statistics Server issue", "RefUrl": "/notes/2766055 "}, {"RefNumber": "2943943", "RefComponent": "HAN-DB-MON", "RefTitle": "Outdated DiskFull with state HANDLED Alerts", "RefUrl": "/notes/2943943 "}, {"RefNumber": "2943850", "RefComponent": "HAN-DB-MON", "RefTitle": "HANA alerts show incorrect date and / or time", "RefUrl": "/notes/2943850 "}, {"RefNumber": "2160391", "RefComponent": "HAN-DB-ENG", "RefTitle": "FAQ: SAP HANA Indexes", "RefUrl": "/notes/2160391 "}, {"RefNumber": "2642446", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "Transaction DBACOCKPIT gives a warning that the \"statisticsserver is deactivated\"", "RefUrl": "/notes/2642446 "}, {"RefNumber": "2908132", "RefComponent": "HAN-DB-MON", "RefTitle": "How to change the threshold value or scheduling interval for HANA Alerts", "RefUrl": "/notes/2908132 "}, {"RefNumber": "2215093", "RefComponent": "HAN-DB-MON", "RefTitle": "After upgrading the HANA Database the statisticsserver does not work", "RefUrl": "/notes/2215093 "}, {"RefNumber": "2889270", "RefComponent": "HAN-DB-MON", "RefTitle": "High CPU Usage by Embedded Statistics server with CALL _SYS_STATISTICS.STATISTICS_SCHEDULABLEWRAPPER", "RefUrl": "/notes/2889270 "}, {"RefNumber": "2852678", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "Check for disabled Alerts in Self-Monitoring", "RefUrl": "/notes/2852678 "}, {"RefNumber": "2800028", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Optimizer Statistics", "RefUrl": "/notes/2800028 "}, {"RefNumber": "2772514", "RefComponent": "HAN-DB-MON", "RefTitle": "EWA report for HANA has the alert 'Identifies internal statistics server problem'", "RefUrl": "/notes/2772514 "}, {"RefNumber": "2154844", "RefComponent": "HAN-DB", "RefTitle": "HANA Alerts keep getting disabled after switching to the embedded statisticserver", "RefUrl": "/notes/2154844 "}, {"RefNumber": "2169283", "RefComponent": "HAN-DB-ENG", "RefTitle": "FAQ: SAP HANA Garbage Collection", "RefUrl": "/notes/2169283 "}, {"RefNumber": "2638539", "RefComponent": "HAN-DB-MON", "RefTitle": "Difference between Statisticsserver check status 'Inactive' and 'Disabled'", "RefUrl": "/notes/2638539 "}, {"RefNumber": "2736234", "RefComponent": "HAN-LM-UPG-DB", "RefTitle": "Upgrade from HANA 1.0 to 2.0 failed due to ESS not converted", "RefUrl": "/notes/2736234 "}, {"RefNumber": "2732928", "RefComponent": "HAN-DB-HA", "RefTitle": "How to deal with Alert \"Service on <hostname>:<service port number> has increased log replay backlog\"", "RefUrl": "/notes/2732928 "}, {"RefNumber": "2313619", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Generating and Evaluating SAP HANA Call Stacks", "RefUrl": "/notes/2313619 "}, {"RefNumber": "2691613", "RefComponent": "HAN-DB-MON", "RefTitle": "Uncommon growth of \"_SYS_STATISTICS\".\"HOST_VOLUME_FILES_BASE\" table", "RefUrl": "/notes/2691613 "}, {"RefNumber": "2656792", "RefComponent": "HAN-DB-MON", "RefTitle": "Alert [6000] missing smtp configuration in STATISTICS_PROPERTIES SNAPSHOT_ID...", "RefUrl": "/notes/2656792 "}, {"RefNumber": "2629590", "RefComponent": "HAN-DB-MON", "RefTitle": "Recommendation for Interval Length for a statistics server collector", "RefUrl": "/notes/2629590 "}, {"RefNumber": "2593571", "RefComponent": "BC-DB-LCA", "RefTitle": "FAQ: SAP HANA Integrated liveCache", "RefUrl": "/notes/2593571 "}, {"RefNumber": "2536974", "RefComponent": "HAN-DB-MON", "RefTitle": "The system is running without alerts, but not all checks are being performed", "RefUrl": "/notes/2536974 "}, {"RefNumber": "2535951", "RefComponent": "HAN-DB-SEC", "RefTitle": "FAQ: SAP HANA Users and Schemas", "RefUrl": "/notes/2535951 "}, {"RefNumber": "2186744", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Parameters", "RefUrl": "/notes/2186744 "}, {"RefNumber": "2529478", "RefComponent": "HAN-DB", "RefTitle": "How-To: Configuring SAP HANA Statistics Server Parameters", "RefUrl": "/notes/2529478 "}, {"RefNumber": "2493696", "RefComponent": "HAN-DB-MON", "RefTitle": "EWA Report - \"Number of collectors with retention times < 14\" is equal to 1", "RefUrl": "/notes/2493696 "}, {"RefNumber": "2488311", "RefComponent": "HAN-DB-MON", "RefTitle": "How to Manage the Volume of Data Collected by the Statistics Service", "RefUrl": "/notes/2488311 "}, {"RefNumber": "2124112", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Parsing", "RefUrl": "/notes/2124112 "}, {"RefNumber": "2477204", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Services and Ports", "RefUrl": "/notes/2477204 "}, {"RefNumber": "2399996", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Configuring automatic SAP HANA Cleanup with SAP HANACleaner", "RefUrl": "/notes/2399996 "}, {"RefNumber": "2400024", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: SAP HANA Administration and Monitoring", "RefUrl": "/notes/2400024 "}, {"RefNumber": "2222277", "RefComponent": "HAN-DB-PER", "RefTitle": "FAQ: SAP HANA Column Store and Row Store", "RefUrl": "/notes/2222277 "}, {"RefNumber": "2388483", "RefComponent": "HAN-DB", "RefTitle": "How-To: Data Management for Technical Tables", "RefUrl": "/notes/2388483 "}, {"RefNumber": "1999880", "RefComponent": "HAN-DB-HA", "RefTitle": "FAQ: SAP HANA System Replication", "RefUrl": "/notes/1999880 "}, {"RefNumber": "2380176", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Database Trace", "RefUrl": "/notes/2380176 "}, {"RefNumber": "2222110", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Load History", "RefUrl": "/notes/2222110 "}, {"RefNumber": "2222218", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Database Server Management Console (hdbcons)", "RefUrl": "/notes/2222218 "}, {"RefNumber": "2222250", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Workload Management", "RefUrl": "/notes/2222250 "}, {"RefNumber": "2222220", "RefComponent": "HAN-DB-ENG", "RefTitle": "FAQ: SAP HANA DBACOCKPIT", "RefUrl": "/notes/2222220 "}, {"RefNumber": "2073112", "RefComponent": "HAN-STD-ADM-DBA", "RefTitle": "FAQ: SAP HANA Studio", "RefUrl": "/notes/2073112 "}, {"RefNumber": "2116157", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Consistency Checks and Corruptions", "RefUrl": "/notes/2116157 "}, {"RefNumber": "2143679", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Removing Primary Keys of SAP HANA Statistics Server Histories", "RefUrl": "/notes/2143679 "}, {"RefNumber": "2000000", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Performance Optimization", "RefUrl": "/notes/2000000 "}, {"RefNumber": "2114710", "RefComponent": "HAN-DB-MON", "RefTitle": "FAQ: SAP HANA Threads and Thread Samples", "RefUrl": "/notes/2114710 "}, {"RefNumber": "2159435", "RefComponent": "HAN-DB", "RefTitle": "How-To: Keeping SAP HANA Row Store in Memory when restarting", "RefUrl": "/notes/2159435 "}, {"RefNumber": "1999020", "RefComponent": "HAN-DB", "RefTitle": "How-To: SAP HANA Troubleshooting when Database is no longer accessible", "RefUrl": "/notes/1999020 "}, {"RefNumber": "2000003", "RefComponent": "HAN-DB-ENG", "RefTitle": "FAQ: SAP HANA", "RefUrl": "/notes/2000003 "}, {"RefNumber": "1999993", "RefComponent": "HAN-DB-MON", "RefTitle": "How-To: Interpreting SAP HANA Mini Check Results", "RefUrl": "/notes/1999993 "}, {"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002 "}, {"RefNumber": "1999997", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Memory", "RefUrl": "/notes/1999997 "}, {"RefNumber": "1803039", "RefComponent": "HAN-STD-ADM-DBA", "RefTitle": "Alert 0 Internal error for Monitor CHECK_HOSTS_CPU", "RefUrl": "/notes/1803039 "}, {"RefNumber": "3347443", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Alert [5031] 287 cannot insert NULL or update to NULL: \"_SYS_STATISTICS\".\"COLLECTOR_[...]\"", "RefUrl": "/notes/3347443 "}, {"RefNumber": "2997712", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 2 SPS04 Database Maintenance Revision 048.04", "RefUrl": "/notes/2997712 "}, {"RefNumber": "3004477", "RefComponent": "HAN-DB", "RefTitle": "Usage of statistics server test alert (ID 999)", "RefUrl": "/notes/3004477 "}, {"RefNumber": "2983008", "RefComponent": "HAN-DB", "RefTitle": "Enable/Disable NSE (PAGE LOADABLE) for column tables of HANA statistics server", "RefUrl": "/notes/2983008 "}, {"RefNumber": "2957968", "RefComponent": "SV-SMG-DVM", "RefTitle": "Activate Table Statistics on HDB for DVM Cloud Application", "RefUrl": "/notes/2957968 "}, {"RefNumber": "2915604", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes on ptime::ExternalStatement::ExecutionResponse::serialize on Worker Node of Scale Out System", "RefUrl": "/notes/2915604 "}, {"RefNumber": "2846349", "RefComponent": "HAN-DB-HA", "RefTitle": "Service Crash After federation::FederationContext::rdes_for_props", "RefUrl": "/notes/2846349 "}, {"RefNumber": "2570661", "RefComponent": "HAN-DB", "RefTitle": "Exception 71000287 During Execution of Internal Statement Within Procedure \"_SYS_STATISTICS\".\"HELPER_ALERT_MISSING_VOLUME_FILE_AGE\"", "RefUrl": "/notes/2570661 "}, {"RefNumber": "2443239", "RefComponent": "HAN-DB", "RefTitle": "Statistics Service is Disabled After Upgrade or Takeover", "RefUrl": "/notes/2443239 "}, {"RefNumber": "2461913", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes During Startup With \"Assertion failed: size_t(readStart) - 1 == m_RowMapToTarget.size()\"", "RefUrl": "/notes/2461913 "}, {"RefNumber": "1977584", "RefComponent": "HAN-DB", "RefTitle": "Technical Consistency Checks for SAP HANA Databases", "RefUrl": "/notes/1977584 "}, {"RefNumber": "2260972", "RefComponent": "HAN-DB-MON", "RefTitle": "High memory consumption if some checks of embedded Statistics Server are called manually", "RefUrl": "/notes/2260972 "}, {"RefNumber": "1969700", "RefComponent": "HAN-DB", "RefTitle": "SQL Statement Collection for SAP HANA", "RefUrl": "/notes/1969700 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP HANA, platform edition all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "11 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 4.3, "Quality-Votes": 10, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 7, "Stars-5": 3}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}