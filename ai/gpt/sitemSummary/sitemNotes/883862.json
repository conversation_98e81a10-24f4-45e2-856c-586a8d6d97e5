{"Request": {"Number": "883862", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 316, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015957412017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000883862?language=E&token=ECCB632F27400FFF31A65CBD64427C5E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000883862", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000883862/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "883862"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.11.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-CDP-010-R3"}, "SAPComponentKeyText": {"_label": "Component", "value": "Multi-Resource Scheduling R/3"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Internal ONLY for CDP projects (see note 689050)", "value": "XX-PROJ-CDP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Custom Development Project 010", "value": "XX-PROJ-CDP-010", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP-010*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Multi-Resource Scheduling R/3", "value": "XX-PROJ-CDP-010-R3", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP-010-R3*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "883862 - Guidelines to install MRS Utilization report on WAS 620"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The customers running MRS on <B>46C system</B> cannot use the transaction <B>/mrss/utilrep</B> because the necessary ABAP Controls used are not supported in the 46C system.<br />So&#x00A0;&#x00A0;a mechanism has been identified to call the transaction from the 4.6C in a WAS System and then this system calls back to the 4.6C System for the data retrieval. Instead of transaction /mrss/utilrep, a new transaction <B>/mrss/utilrep_46c</B>&#x00A0;&#x00A0;should be called in 46C system to launch the utilization report<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MRSS190, MRS on 46C, Utilization Report</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The necessary ABAP Controls used in the application are not supported in the 46C system.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>Installation Details for this package:</B><br /></p> <UL><LI><B>Prerequisites</B></LI></UL> <p><br />1. MRSS 190 should have been installed on R/3 46C system as per note <B>879410</B>.<br />2. Web application Server (Basis 6.20 ) has to be available with RFC connections maintained between 46C system on which MRS190 is running and the WAS system on which this transport is included. This WAS system should support the Organization Units create /change (Transaction ppome and ppoce).<br />3. The Organization data (Organization units which are used) should be transferred from the 46C system to the WAS system.<br />This step should be performed using ALE transfer (preferably with message type HRMD_A or HRMD_ABA&#x00A0;&#x00A0;).<br /><br />Please note that RFC Users from webAS to 4.6c require authorization for /MRSS/PB1 for all orgunits.<br /></p> <UL><LI><B>Steps on the WAS system:</B></LI></UL> <p><br />De-compress the SAR file YA5K000502.SAR using SAPCAR and install the transport YA5K000502 which includes the packages /MRSS/SGR and /MRSS/RGE to WAS on 620.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; The transport file can be found under the tab 'Attachment' of this note.<br /><br />Remark: This transport contains the developments which are necessary for the Utilization Report and developments for display the Map / Geoinformations. For configuration of the Map Display for MRS 190 please see Note 962930.<br /><br /></p> <UL><LI><B>Customizing needed to implement the functionality:</B></LI></UL> <p><br />After performing above steps, the following customizing is needed :<br /><br /><B><U>In&#x00A0;&#x00A0;46C System:</U></B><br /><br />Maintain Entries in /MRSS/C_UTL_DEST with RFC details of WAS where in Utilization report Report is installed. ( Transaction code&#x00A0;&#x00A0;sm30)<br /><br /><br /><B><U>In WAS :</U></B><br /><br />Maintain the calling system details (46C system which MRS190 is running ) using customizing table /MRSS/C_SGR_EXTD.<br /><br /><br />Now the settings are completed in both 46C system and WAS system.<br />The utilization report transaction can be launched in 46C system with the transaction code <B>/mrss/utilrep_46c</B>.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I035322"}, {"Key": "Processor                                                                                           ", "Value": "I039853"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883862/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "YA5K000502.SAR", "FileSize": "277", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000471432005&iv_version=0011&iv_guid=76388A302810924AA740FE56EB212CEB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "975022", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS 190 Utilization report 46c changes extended.", "RefUrl": "/notes/975022"}, {"RefNumber": "966826", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS190 Utilization report from Planning board.", "RefUrl": "/notes/966826"}, {"RefNumber": "966821", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS 190: Utilization report 46c Changes.", "RefUrl": "/notes/966821"}, {"RefNumber": "962930", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS 190: Enable Map display in MRS 190", "RefUrl": "/notes/962930"}, {"RefNumber": "879410", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Installing MRSS 190 in R/3 46C", "RefUrl": "/notes/879410"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1014314", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Guidelines to install MRS Utilization report on WAS 620", "RefUrl": "/notes/1014314"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "879410", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Installing MRSS 190 in R/3 46C", "RefUrl": "/notes/879410 "}, {"RefNumber": "1014314", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Guidelines to install MRS Utilization report on WAS 620", "RefUrl": "/notes/1014314 "}, {"RefNumber": "975022", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS 190 Utilization report 46c changes extended.", "RefUrl": "/notes/975022 "}, {"RefNumber": "966826", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS190 Utilization report from Planning board.", "RefUrl": "/notes/966826 "}, {"RefNumber": "962930", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS 190: Enable Map display in MRS 190", "RefUrl": "/notes/962930 "}, {"RefNumber": "966821", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "MRS 190: Utilization report 46c Changes.", "RefUrl": "/notes/966821 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "MRSS", "From": "190", "To": "190", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}