{"Request": {"Number": "748759", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1267, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015688092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000748759?language=E&token=E24FAF3D70985846A2FC3A66068CBE94"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000748759", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000748759/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "748759"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.12.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "748759 - Upgr. SAP R/3 Enterprise 47x200 SR1 with PI/PI-A/SLL_PI"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />Upgrade to Release SAP R/3 Enterprise 47x200 SR1 with PI/PI-A/SLL_PI.<br />There is no guide specifically for add-ons. This note contains additional information about the upgrade that is specific to add-ons.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />Upgrade, ABAP Add-On, PI, PI-A, Plug-In, SLL_PI<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />You installed PI or PI-A or their predecessor and want to carry out the upgrade to R/3 Enterprise 47x200 SR1.<br />PI 2004_1_470 with Support Package level 01 is part of the R/3 47x200 SR1 delivery. The following table lists the PI or PI-A source releases that are supported for the upgrade:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>R/3 source release</TH><TH ALIGN=LEFT> PI/PI-A Source Release</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>3.1I</TD><TD> PI 2002.1 - 2004.1, PI-A 2002.1</TD><TD> </TD></TR> <TR><TD>4.0B</TD><TD> PI 2002.1 - 2004.1, PI-A 2002.1</TD><TD> </TD></TR> <TR><TD>4.5B</TD><TD> PI 2002.1 - 2004.1, PI-A 2002.1</TD><TD> </TD></TR> <TR><TD>4.6B</TD><TD> PI 2002.1 - 2004.1</TD><TD> </TD></TR> <TR><TD>4.6C</TD><TD> PI 2002.1 - 2004.1</TD><TD> </TD></TR> <TR><TD>47x110</TD><TD> PI 2002.1 - 2004.1</TD><TD> </TD></TR> </TABLE> <p> <br /><br />Note that PI 2004_1_470 Support Package 10 requires PI_BASIS 2005_1_620. Therefore, you must include the upgrade package PI_BASIS 2005_1_620 in the upgrade.<br /><br />You installed SLL_PI (Release 100, 200 or 200_470) and want to carry out the upgrade to R/3 47x200 SR1. SLL_PI (Release 100, 200 or 200_470)&#x00A0;&#x00A0;is integrated in the PI with PI Release 2004.1.<br />The following table lists the SLL_PI source releases supported in the upgrade to PI 2004_1_470.<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>R/3 source release</TH><TH ALIGN=LEFT> SLL_PI Source Release</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>4.0B</TD><TD> 100, 200</TD><TD> </TD></TR> <TR><TD>4.5B</TD><TD> 100, 200</TD><TD> </TD></TR> <TR><TD>4.6B</TD><TD> 100, 200</TD><TD> </TD></TR> <TR><TD>4.6C</TD><TD> 100, 200</TD><TD> </TD></TR> <TR><TD>47x110</TD><TD> 100, 200_470</TD><TD> </TD></TR> </TABLE> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>Since this note is constantly changed, always read the latest version when you begin the upgrade.</B><br /><br />#Change history</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Section</TH><TH ALIGN=LEFT> Short description</TH></TR> <TR><TD>JUN/20/2007</TD><TD> III.3, IV.1c</TD><TD> Integration of PI SP</TD></TR> <TR><TD>Jan 19, 2006</TD><TD> various </TD><TD> Adjusting to current SAP terminology</TD></TR> <TR><TD>NOV/04/2006</TD><TD> various</TD><TD> PI 2004_1_470 SP10 &amp; PI_BASIS 2005_1_620</TD></TR> <TR><TD>OCT/22/2006</TD><TD> IV</TD><TD> IS_SELECT for Source Release 47x110</TD><TD> </TD></TR> <TR><TD></TD></TR> </TABLE> <p><br /></p> <b>Contents</b><br /> <p><br />&#x00A0;&#x00A0;I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Important general information<br />&#x00A0;&#x00A0;II.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Errors on the CD<br />&#x00A0;&#x00A0;III/&#x00A0;&#x00A0; Checks before the upgrade<br />&#x00A0;&#x00A0;IV/&#x00A0;&#x00A0; Additional information about the upgrade<br />&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems after the import<br />&#x00A0;&#x00A0;VI/&#x00A0;&#x00A0; Problems when importing Support Packages<br />&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;R3up keyword<br />&#x00A0;&#x00A0;VIII/ Actions after the upgrade<br />&#x00A0;&#x00A0;IX/&#x00A0;&#x00A0; Other essential notes<br /><br /><br /></p> <b>&#x00A0;&#x00A0;I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Important general information</b><br /> <OL>1. Upgrade with PI-A 2002.1<br />Note, when upgrading with PI-A 2001.2, \"PI-A\" is automatically replaced by \"PI\", meaning you receive the full plug-in function, without the standard SAP system being modified by the plug-in (in addition, refer to the material on SAP Service Marketplace or Note 214503 on modifications).</OL> <OL>2. Updates (PI, PI-A)</OL> <OL><OL>a) Process your V3 update entries before you carry out the upgrade. Otherwise, there is a risk that you may no longer be able to update entries if changes are introduced into the interface structures of the V3 update modules by the patch or upgrade (see Note 328181).</OL></OL> <OL><OL>b) Before the upgrade, process your entries in the extraction queues. Otherwise, there is a risk that you may no longer be able to update these entries if changes to the interface structures of the qRFC function modules are introduced by the patch or the upgrade (see Note 328181).</OL></OL> <OL><OL>c) Before the upgrade, delete your entries in the reconstruction tables for the logistics extraction applications. Otherwise, there is a risk that you may no longer be able to use these entries if changes to the extraction structures are introduced by the patch or the upgrade (see Note 328181).</OL></OL> <OL>3. Required SAP R/3 and PI or PI-A or SLL_PI Release level<br />Refer to the following table for the prerequisite for the upgrade with PI, PI-A or SLL_PI described here.<br /><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>R/3 source release </TH><TH ALIGN=LEFT> PI/PI-A Source Release</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>3.1I</TD><TD> PI 2002.1 - PI 2004.1, PI-A 2002.1</TD><TD> </TD></TR> <TR><TD>4.0B</TD><TD> PI 2002.1 - PI 2004.1, PI-A 2002.1</TD><TD> </TD></TR> <TR><TD>4.5B</TD><TD> PI 2002.1 - PI 2004.1, PI-A 2002.1</TD><TD> </TD></TR> <TR><TD>4.6B</TD><TD> PI 2002.1 - PI 2004.1</TD><TD> </TD></TR> <TR><TD>4.6C</TD><TD> PI 2002.1 - PI 2004.1</TD><TD> </TD></TR> <TR><TD>47x110</TD><TD> PI 2002.1 - PI 2004.1</TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> <TR><TH>R/3 source release</TH><TH> SLL_PI Source Release</TH><TH></TH></TR> <TR><TD>4.0B</TD><TD> 100, 200</TD><TD> </TD></TR> <TR><TD>4.5B</TD><TD> 100, 200</TD><TD> </TD></TR> <TR><TD>4.6B</TD><TD> 100, 200</TD><TD></TD></TR> <TR><TD>4.6C</TD><TD> 100, 200</TD><TD> </TD></TR> <TR><TD>47x110</TD><TD> 100, 200_470</TD><TD> </TD></TR> <TR><TD></TD></TR> </TABLE></OL> <OL>4. Additional CD<br />The PI Release 2004.1 for SAP_APPL 470 is contained in the R/3 47x200 SR1 delivery. You do not need an additional PI 2004.1 supplement CD.</OL> <OL>5. Including PI_BASIS in the upgrade<br />If you include PI 2004_1_470 Support Package 10 in the upgrade to R/3 47x200 SR1, you must also include the PI_BASIS 2005_1_620 upgrade package, since PI requires the PI_BASIS Release 2005.1 as of PI 2004.1 Support Package 10.&#x00A0;&#x00A0;If you imported PI 2004.1 Support Package 10 into the source release before the upgrade, you must include PI 2004_1_470 Support Package 10 in the R/3 47x200 SR1 upgrade to avoid data loss. The PI_BASIS 2005_1_620 upgrade package is available on the CD with material number 51030956. For more information, see Note 570810.</OL> <OL>6. Upgrading several SAP add-on products<br />The upgrade with PI or PI-A is released with all other add-ons.</OL> <p></p> <b>&#x00A0;&#x00A0;II/&#x00A0;&#x00A0; Errors on the CD</b><br /> <p></p> <b>&#x00A0;&#x00A0;III.&#x00A0;&#x00A0;Checks before the upgrade</b><br /> <OL>1. SAP release:<br />R/3 3.1I/4.0B/4.5B/4.6B/4.6C/47x110 is a prerequisite for the upgrade to R/3 47x200 SR1.</OL> <OL>2. PI release, PI-A release, SLL_PI release:<br />The PI releases listed above, 2002.1, 2002.2, 2003.1, 2004.1 or PI-A 2002.1. or SLL_PI 100, 200 are prerequisites<br />(Depending on your SAP release, the technical name is<br />PI 2002_1_31I/2002_1_40B/2002_1_45B/2002_1_46B/2002_1_46C/2002_1_470,<br />PI 2002_2_31I/2002_2_40B/2002_2_45B/2002_2_46B/2002_2_46C/2002_2_470,<br />PI 2003_1_31I/2003_1_40B/2003_1_45B/2003_1_46B/2003_1_46C/2003_1_470,<br />PI 2004_1_31I/2004_1_40B/2004_1_45B/2004_1_46B/2004_1_46C/2004_1_470,<br />PI-A 2002_1_31I/2002_1_40B/2002_1_45B,<br />see the entry in the <B>AVERS</B> table).</OL> <OL>3. Importing PI Support Packages<br />Ensure that you use the most recent versions of the PI Support Packages.</OL> <p></p> <b>&#x00A0;&#x00A0;IV/&#x00A0;&#x00A0;Additional information about the upgrade</b><br /> <OL>1. Enhancements to PREPARE</OL> <OL><OL>a) <B>R/3 Source Release 3.1I only</B>: READ CD module, IS_CHK phase<br /><br />In this phase, the software products that are installed in the R/3 system (in addition to the core components) are examined. The system tells you that <B>PI</B> is installed, regardless of whether or not you installed PI or PI-A in the source release. If no other add-ons have been installed, confirm the query with \"nothing else\".</OL></OL> <OL><OL>b) <B>R/3 Source Release 3.1I only</B>: READ CD module, IS_READ phase:<br /><br />If the add-on PI or PI-A with Release 2002_1_31I, 2002_2_31I, 2003_1_31I or 2004_1_31I is installed in your 31I system, select \"Passive deletion\" in the IS_READ phase by selecting the following options: 'No more CDs', received 'No', deleted after upgrade 'continue' and passive deletion 'is desired'.<br />Since PI with Version 2004_1_470 is part of the R/3 47x200 SR1export, you receive the current version automatically.</OL></OL> <OL><OL>c) R/3 Source Releases 4.0B, 4.5B, 4.6B, 4.6C, 47x110: EXTENSION module, IS_SELECT phase<br /><br />The selection screen will display the following, among other things:<br />PI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2004_1_470&#x00A0;&#x00A0;&#x00A0;&#x00A0;INST/UPG WITH STD CD<br />&#x00A0;&#x00A0; PI 2004_1_470 is part of the R/3 47x200 SR1 delivery. A supplement CD is not required.<br /><br />If you already installed PI 2004_1_470 in the upgrade source release, the selection screen displays<br />PI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2004_1_470&#x00A0;&#x00A0;&#x00A0;&#x00A0;KEEP VERSION<br /><br />Confirm this selection.<br /><br />If you include PI 2004_1_470 Support Package 10 in the upgrade to R/3 47x200 SR1, you must also include the PI_BASIS 2005_1_620 upgrade package, since PI requires the PI_BASIS Release 2005.1 as of PI 2004.1 Support Package 10.&#x00A0;&#x00A0;If you imported PI 2004.1 Support Package 10 into the source release before the upgrade, you must include PI 2004_1_470 Support Package 10 in the R/3 47x200 SR1 upgrade to avoid data loss. The PI_BASIS 2005_1_620 upgrade package is available on the CD with material number 51030956. For more information, see Note 570810.<br /><br />If the queue calculation terminates with the following error:<br /><br />\"Calculate the queue part for \"PI\" rel.\"2004_1_470\" with target SP \"SAPKIPZI5A\" (level \"0010\")<br />Required software component \"PI_BASIS\" rel.\"2004_1_620\" is not installed OCS package \"SAPKIPZI55\" does not fit the current software component vector \"<br /><br />proceed as follows:<br />Ensure that you download the most recent packages for the PI Support Packages from SAP Service Marketplace in transaction SPAM.<br />Reload the attributes of the Support Packages 02-07 for PI 2004_1_470.  To do this, use the following menu option in transaction SPAM: \"Utilities\" --&gt; \"Reload package attributes\". Enter the names of the PI 2004.1 Support Packages in the OCS package name field (for example, SAPKIPZI57). Continue the PI 2004.1 installation:<br />Repeat the PREPARE phase.</OL></OL><p></p> <OL><OL>d) Phase BIND_PATCH<br /><br />In this phase, you can include Support Packages in the upgrade. You should always include all available Support Packages (for upgrades with source release R/3 3.1I, at least all available LCPs, since it is not yet possible to include Support Packages (Hot Packages)).<br />It is also possible to include the already available PI 2004_1_470 Support Packages in the upgrade. You should always include all available PI 2004_1_470 Support Packages in the upgrade.<br />Include all available PI_BASIS Support Packages. Note that PI_BASIS 2004_1_620 Support Package 3 is automatically included in the upgrade. This Support Package belongs to the delivery stock of R/3 47x200 SR1.</OL></OL> <OL><OL>e) CONFLICT_CHECK phase<br /><br />In this phase, any conflicts between PI and other software components that can be ignored are determined. Check Note 608145 to see whether you can ignore the conflicts.</OL></OL> <OL><OL>f) RUN_RDDIT006 phase (for source release 3.1I only)<br /><br />This phase is optional (PREPARE module \"Modification Support\"). However, if you have modified Plug-In objects, we strongly recommend that you execute this module.<br />Check the corresponding CUSTEXP.&lt;SID&gt; log file. If one of the following objects is executed, it should be <B>deleted</B> <B>before</B> the actual upgrade.<br />R3TR FUGR AIBA<br />R3TR FUGR AIBT<br />R3TR FUGR AQBM<br />R3TR FUGR AQBR<br />R3TR FUGR PABW<br />R3TR FUGR HRBW<br />R3TR FUGR HIBW<br />R3TR FUGR OMBW<br />R3TR FUGR LC05<br />Only the complete function groups are critical; if only parts of them were modified (for example, LIMU FUNC &lt;function module&gt;), they are listed for the purposes of comparison in transaction SPAU, if required.</OL></OL> <p></p> <OL>2. Additions to R3up<br /><br />Currently, this paragraph contains no information.<br /></OL> <b>&#x00A0;&#x00A0;V/&#x00A0;&#x00A0; Problems after the import</b><br /> <OL>1. All R/3 releases:</OL> <OL><OL>a) If you experience problems with the customer exit for transferring data into a BW system, see Note 393492.</OL></OL> <OL><OL>b) If problems such as \"The selection in the BW data extraction process does not work\" occur, see Note 417152.<br />To correct update errors in R/3, see Note 425109.</OL></OL><p></p> <b>&#x00A0;&#x00A0;VI/&#x00A0;&#x00A0;Problems when importing Support Packages</b><br /> <p></p> <b>&#x00A0;&#x00A0;VII.&#x00A0;&#x00A0;R3up keyword<br /></b><br /> <p>No R3up keyword is requested for PI. <br /><br /></p> <b>&#x00A0;&#x00A0;VIII. Actions after the upgrade</b><br /> <OL>1. Importing Add-On Support Packages</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import the available Add-On Support Packages <B>immediately</B> after the upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Note 515520 as well as the information on SAP Service Marketplace (quick link <B>R3-PLUG-IN</B>). <OL>2. Postprocessing for XPRA RMCSBWXP_COM: Business Content incomplete</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Follow the instructions contained in Notes 648565 and 651522. If you do not follow these instructions, Business Content from the Logistics-BW extraction may be lost. <p></p> <b>&#x00A0;&#x00A0;X/&#x00A0;&#x00A0;&#x00A0;&#x00A0; Other essential notes </b><br /> <p><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D032986)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D039662)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000748759/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000748759/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "752263", "RefComponent": "BC-UPG-ADDON", "RefTitle": "PI-A: R/3 upgrade 47x110, 47x200, ECC 500", "RefUrl": "/notes/752263"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "181255", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181255"}, {"RefNumber": "1306751", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: Notes for Add-On SLL_PI (GTS)", "RefUrl": "/notes/1306751"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1306751", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: Notes for Add-On SLL_PI (GTS)", "RefUrl": "/notes/1306751 "}, {"RefNumber": "752263", "RefComponent": "BC-UPG-ADDON", "RefTitle": "PI-A: R/3 upgrade 47x110, 47x200, ECC 500", "RefUrl": "/notes/752263 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2002_1_31I", "To": "2002_1_470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2002_2_31I", "To": "2002_2_470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2003_1_31I", "To": "2003_1_470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2004_1_31I", "To": "2004_1_500", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2002_1_31I", "To": "2002_1_45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}