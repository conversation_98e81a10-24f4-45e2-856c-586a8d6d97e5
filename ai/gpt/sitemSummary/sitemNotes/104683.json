{"Request": {"Number": "104683", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 398, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014566952017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000104683?language=E&token=B1C28EEFD95EE64D3848D2A270BAFEFC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000104683", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000104683/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "104683"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.09.1998"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "104683 - Subsequent settlement: changes in Release 4.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note informs you about the changes and innovations in Release 4.0. It also informs you about required changes for the new release.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (purchasing), volume-based rebate, arrangement</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>none</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>Starting-point: Functions in Release 3.0 (all releases)</b><br /> <p><br />In Release 3.0, you can only perform updating of business volume for an arrangement of the subsequent settlement on the basis of price determination of a purchase order or a scheduling agreement (only scheduling agreements with price determination).<br />Posting of the provisions for accrued income (providing the condition type is indicated as relevant for this) is performed when the material document is being entered.<br />The update of the business volumes including the provisions for accrued income if necessary, is performed when posting the invoice verification document.<br /><br />This causes the following restrictions:</p> <UL><LI>An update can only be performed with reference to a purchase order item since only this purchase order item contains document conditions (price determination was carried out). Credit memos/invoices without order reference are not relevant for the subsequent settlement (no price determination). This, in particular, applies to invoices without order reference for purposes of, for example, the consignment settlement.</LI></UL> <UL><LI>An update at the same time as invoice verification is not flexible enough. You should also be able to perform an update from within, for example, the goods receipt.</LI></UL> <UL><LI>Posting of the provisions for accrued income is made in the material document, the update, however, is only performed at invoice receipt (different times). The value of the provisions for accrued income posted at goods receipt is not known at the time of invoice receipt. Both problems can cause inconsistencies in certain situations. The update of the provisions for accrued income should therefore always be performed from within the material document regardless of the business volume data.</LI></UL> <UL><LI>If you perform a new price determination at goods receipt (this can also be triggered implicitly via ruling metal prices), this cannot be taken into account for the update from within the invoice verification document since there is no possibility to access the document conditions of the material document.</LI></UL> <UL><LI>For scheduling agreements without price determination, that is, with condition records that were created with reference to the scheduling agreement, the price determination is generally only performed at goods receipt and not with the document entry. Since it is not possible to access the document conditions of the material document during invoice receipt, subsequent settlement for this type of scheduling agreements is not possible.</LI></UL> <p></p> <b>Functions in Release 4.0A/B</b><br /> <p><br />As of Release 4.0A, an update of business volumes on the basis of the pricing dates of any documents of the R/3 System is possible. Our aim is the integration of additional document categories. The update of business volume data can be performed from within the document itself or a follow-on document. We therefore distinguish between the document, for which the price determination is performed ('Price determination document') and the document that is used as basis for the update ('Update document').<br />In Release 4.0A/B the purchasing documents (purchase order/scheduling agreement) are still used as 'Price determination document'. For the 'Update document', the purchasing document itself, the material document or the invoice document are available. However, basically, no subsequent settlement is possible without referring to a purchase order/scheduling agreement.</p> <UL><LI>As a result, subsequent settlement is now possible from both categories of scheduling agreements.</LI></UL> <UL><LI>The update of the provisions for accrued income is perfomed fixed for newly created arrangements at the time of goods receipt and it is separated from the update of business volume data.</LI></UL> <UL><LI>The update of business volume data can be created in a flexible way.</LI></UL> <UL><LI>The update of the business volume data of quantity-dependent conditions is more flexible, see Note 112413.</LI></UL> <p></p> <b>Open problems</b><br /> <p><br />From invoices/credit memos of invoice verification without order reference, updating of business volume is not possible. No price determination - no subsequent settlement (for approach see preview on the functions in Release 4.5).<br /></p> <b>Changes in Release 4.0A/B</b><br /> <p><br />For arrangements that were created in Release 3.0 nothing changes! Update is performed according to the old pattern since otherwise a reorganization of the business volumes would be necessary. In particular, provisions for accrued income are still updated at invoice receipt.<br />For all new arrangements, that is, created in Release 4.0, the update of the provisions for accrued income is performed at the time of goods receipt (see above).<br />You can change the time of update of the business volumes for existing arrangement types (provisions for accrued income are updated fixed at goods receipt). For all newly created arrangements after changing the arrangement type the time of the update for the goods receipt (update at document date) or for the purchase order (update from the schedule line at delivery date) is moved. Nothing changes for existing arrangements!<br />Note:<br />Due to the update from within delivery scheduling, the update at the time of the purchase order requires much effort! For a large number of documents or for documents with many items together with several schedule lines, a longer runtime may be the result. Choose another update time if necessary.<br /></p> <b>Required changes regarding the procedure</b><br /> <p><br />As soon as an update is performed at goods receipt (consider provisions for accrued income) or purchase order/scheduling agreement, the tax code in the purchase order item/scheduling agreement item must be known. The entry within invoice verification is not sufficient. Read Note 113031.<br /></p> <b>Special features</b><br /> <p></p> <UL><LI>For scheduling agreements without price determination the price determination is performed at goods receipt. The update of business volume data is also performed at goods receipt even if the arrangement type plans another time for the update. Background for this is that you can only access the document conditions during goods receipt.</LI></UL> <UL><LI>For all other purchasing documents, an update of business volume data based on a price determination can only be performed at goods receipt if the goods receipt is selected as time of update. An update at the time of invoice verification is always based on the document conditions of the purchasing document.</LI></UL> <p></p> <b>Preview on the functions in Release 4.5</b><br /> <p></p> <UL><LI>As of Release 4.5, updating of business volume is possible from within the documents of agency business (also new in Release 4.5) (new document categories in R/3 with price determination, new 'Price determination document' for the subsequent settlement). These documents are vendor billing documents (invoices/credit memos without order reference) and settlement requests (pooled payments).</LI></UL> <UL><LI>As of Release 4.5, subsequent settlement (purchasing) supports customer-side subsequent settlement (customer arrangement, payment to customer, for example, for purposes of pooled payments).</LI></UL> <UL><LI>Replacement of the credit memos (invoice verification) as settlement documents (settlement type 1) against vendor billing documents (credit memos without order reference, agency business). Vendor billing documents to a large extent correspond to the customer billing documents regarding their functions and, in particular, allow cross-company-code, credit-side settlement accounting (new settlement type 0) without the detour via customer billing documents as required up to now. A change from customer billing documents to vendor billing documents is possible.</LI></UL> <UL><LI>For a later release (implementation still unclear), we think of the integration of customer billing documents.</LI></UL> <UL><UL><LI>In some countries it is usual, or required by law, to calculate the vendor volume rebate by sales ( at the POS, --&gt; Customer billing documents) and not based on the purchase order dates.</LI></UL></UL> <UL><UL><LI>A common update from settlement requests and customer billing documents for an arrangement should be possible to be able to process and compress the business volume data of all business processes.</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000104683/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104683/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98650", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, business volume in list output incorrect", "RefUrl": "/notes/98650"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}, {"RefNumber": "104662", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, messages MN546 or MN604", "RefUrl": "/notes/104662"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "104662", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, messages MN546 or MN604", "RefUrl": "/notes/104662 "}, {"RefNumber": "98650", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, business volume in list output incorrect", "RefUrl": "/notes/98650 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}