{"Request": {"Number": "513344", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1262, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015399622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000513344?language=E&token=F5CB5B62E0667A7BA5AC449D599BE7C8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000513344", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000513344/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "513344"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.05.2002"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-SEM-CPM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Corporate Performance Monitor"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Enterprise Management", "value": "FIN-SEM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Corporate Performance Monitor", "value": "FIN-SEM-CPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM-CPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "513344 - SEM 3.1B front end patch 1 (Apr.2002)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SEM 3.1A/B: The system dumps if cause-effect chain is displayed</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Patch, Gui, Sapgui, Frontend, front, graphic, installation, SAPClient, SEM, Frontendpatch, SEMOCX,Cockpit, Balanced Scorecard, Management Cockpit, BSC, MC</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>At least GUI 46D Compilation 3rd In a Downwardly Compatible Way to SEM 2.0B, SEM 3.0A, SEM 3.1A, SEM 3.1B</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Starting with Release 4.6D compilation 3, the SAP GUI installation provides a new tool for the application of patches. The import of the patches is now executed automatically instead of manually disassembling the patches on the installation server as before. In this case, in addition to the copying, also the installation database on the installation server is updated. Clients, on which you start SAPSetup, can recognize in this way that you have added a new patch to the server and they can update themselves correspondingly. As of the following patch levels, you can only install patches with the help of this tool:</p> <UL><LI>sem31a:&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2</LI></UL> <UL><LI>setup46D:&#x00A0;&#x00A0; 5</LI></UL> <p>The manual unpacking is NOT possible anymore with these packages and their successors. (Refer to Note 361222)</p> <OL>1. Prepare an installation server by starting setup.exe and selecting \"Administrative Setup\".</OL> <OL>2. Download the archive from the SapservX and copy the files into theinstallation directory of the server. Procedure:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;a) download archive sem31a.exe&#x00A0;&#x00A0;&#x00A0;&#x00A0;- SEM 3.1b SP2 Patch ftp://sapservX/general/frontend/patches/rel610/Windows/Win32/ <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) Download it locally on the GUI server <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;c) On the installation server, start the SAP setup -&gt; Configuration -&gt; Program SAPAdmin from the \\netinst directory. Note that the call has tobe carried out via an UNC path: \\\\&lt;Servername&gt; <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ShareName&gt;\\netinst\\sapadmin.exe <OL>3. Select the 'Extras --&gt; Import package' menu option.</OL> <OL>4. Specify the patch file that you want to install.</OL> <OL>5. Choose \"Install\" On the clients:</OL> <OL>6. Start the installation as usual with Netsetup, select the SEM Add-On and install it. Do NOT register OCX files manually. In case of a manual registration, a correct deinstallation of Netinstall is not guaranteed any more!</OL> <p><br />Standalone computer&#x00A0;&#x00A0;Even if no installation server is available, you have nevertheless the possibility to install the new patch. To do this, require the setup update: localpat46D_Y.exe (Y designates the current patch level), which is available on sapservx/general/frontend/patches/rel46D/Windows/Win32 (refer to Note 361222). An SAP Chart OCX is required for the display of graphical componentsfor both Balanced Scorecard and Management Cockpit. You can find the most current version of this component on the sapservX as well. CSN Note 318196 describes how to get and install this patch. SAP recommends that you always import the most current patch here. The SEM components require the basis chart components. Install these with the most current frontend patch. Installation of the most current frontend patch from, for example, sapserv3: for 610: ftp://sapserv3/general/frontend/patches/rel610/Windows/Win32/</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030766)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030766)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000513344/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000513344/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000513344/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000513344/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000513344/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000513344/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000513344/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000513344/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000513344/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885"}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415"}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "314973", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/314973"}, {"RefNumber": "311212", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/311212"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415 "}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}