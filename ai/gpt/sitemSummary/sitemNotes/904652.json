{"Request": {"Number": "904652", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 251, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016024622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000904652?language=E&token=B02AA21786DF8FCCF4E13BC3970EDAE4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000904652", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000904652/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "904652"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2011.06.14"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IV-LIV-CRE"}, "SAPComponentKeyText": {"_label": "Component", "value": "Entry MIRO"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Invoice Verification", "value": "MM-IV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Logistics Invoice Verification", "value": "MM-IV-LIV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IV-LIV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Entry MIRO", "value": "MM-IV-LIV-CRE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IV-LIV-CRE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "904652 - MIRO: Different from FB60"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you enter incoming invoices, you notice differences between the Financial Accounting (FI) transaction FB60 and the Logistics Invoice Verification transactions (MIRO, MIRA, MIR7).<br />For example, fewer input fields are available in MIRO.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Vendor invoice, MR1M, MIR4, INVFO<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>MIRO is an independent Materials Management (MM) transaction that does not claim to be the same as the accounting transactions (such as FB60 or FB01).<br />MIRO was developed to allow users to process vendor invoices within the context of the MM procurement processes as simply as possible.<br />For this reason, the field selection was limited to what was absolutely necessary (among other differences). For example, the following fields from the accounting document are not available in transaction MIRO (this list is not complete):</p> <UL><LI>Posting period (BKPF-MONTH)</LI></UL> <UL><LI>Cash discount base amount (BSEG-SKFBT)</LI></UL> <UL><LI>Reference key (BSEG-XREF1, -XREF2, -XREF3)</LI></UL> <UL><LI>Special G/L indicator (BSEG-UMSKZ)</LI></UL> <UL><LI>Instruction keys 1-4 (BSEG-DTWS1, -2, -3, -4)</LI></UL> <UL><LI>Payment currency (BSEG-PYCUR) and Amount in payment currency (BSEG-PYAMT)</LI></UL> <UL><LI>Translation date (BKPF-WWERT) and Amount in local currency (BSEG-DMBTR)</LI></UL> <p><br />Other examples of differences are:</p> <UL><LI>The function of posting to a G/L account is restricted:&#x00A0;&#x00A0;You cannot branch to the detail display.</LI></UL> <UL><LI>In transaction MIRO, you cannot change the reconciliation account that the system gets from the vendor master record (BSEG-HKONT, general ledger account of the vendor line item).</LI></UL> <UL><LI>MIRO primarily creates an MM document in the tables RBKP and RSEG and only subsequently creates follow-on documents in accounting. The corresponding calculations and value transfers do not take place until the document has been posted or simulated. This means that there are differences for FI validations and substitutions, among other things (For more information, see Note 308866).</LI></UL> <UL><LI>There are differences during the conversion into currencies other than the document currency, as described in Note 331910.</LI></UL> <UL><LI>The logic of the \"Check for Duplicated Invoice\" (field LFB1-REPRF in the vendor master record) is different.</LI></UL> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The differences listed above and other similar differences are not program errors.<br />Despite the listed restrictions, it is possible to enter invoice documents correctly in MIRO.<br />In certain cases (for non-order-related documents), you may need to switch to an FI transaction.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022253)"}, {"Key": "Processor                                                                                           ", "Value": "D002416"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000904652/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904652/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "395043", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "FAQ: Posting invoices using Transaction MIRO", "RefUrl": "/notes/395043"}, {"RefNumber": "331910", "RefComponent": "MM-IV-LIV", "RefTitle": "MR1M, MIRO: Postings in foreign currency/local currency", "RefUrl": "/notes/331910"}, {"RefNumber": "308866", "RefComponent": "MM-IV-LIV", "RefTitle": "MR1M / MIRO: Validation", "RefUrl": "/notes/308866"}, {"RefNumber": "144081", "RefComponent": "MM-IV", "RefTitle": "Replacing MR01/functions of MR1M (Release 4.6)", "RefUrl": "/notes/144081"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1668786", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "MIRO/MIR7 - Alternative reconciliation account is not editable -  SAP ERP & SAP S/4HANA", "RefUrl": "/notes/1668786 "}, {"RefNumber": "2916253", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "MIRO: system does not fill the Reference Key 3 in the FI document - SAP ERP & SAP S/4HANA", "RefUrl": "/notes/2916253 "}, {"RefNumber": "2885884", "RefComponent": "MM-FIO-IV", "RefTitle": "Differences between the Create Supplier Invoice (App ID F0859) and Create Incoming Invoices (App ID FB60) apps", "RefUrl": "/notes/2885884 "}, {"RefNumber": "2686899", "RefComponent": "MM-FIO-IV", "RefTitle": "App ID F0859: Posting invoice with Supplier Invoice applications using different reconciliation accounts", "RefUrl": "/notes/2686899 "}, {"RefNumber": "2629376", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "LIV: INVFO-PYCUR (Payment currency) & INVFO-PYAMT (Amount in payment currency)", "RefUrl": "/notes/2629376 "}, {"RefNumber": "2540379", "RefComponent": "MM-IV-LIV-BAPI", "RefTitle": "Alternative reconciliation in BAPI_INCOMINGINVOICE_CREATE(1) not possible - SAP ERP & SAP S/4HANA", "RefUrl": "/notes/2540379 "}, {"RefNumber": "2432586", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "MIRO: Baseline Date and Due On fields not available in G/L account tab - SAP ERP & S/4 HANA", "RefUrl": "/notes/2432586 "}, {"RefNumber": "331910", "RefComponent": "MM-IV-LIV", "RefTitle": "MR1M, MIRO: Postings in foreign currency/local currency", "RefUrl": "/notes/331910 "}, {"RefNumber": "144081", "RefComponent": "MM-IV", "RefTitle": "Replacing MR01/functions of MR1M (Release 4.6)", "RefUrl": "/notes/144081 "}, {"RefNumber": "395043", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "FAQ: Posting invoices using Transaction MIRO", "RefUrl": "/notes/395043 "}, {"RefNumber": "308866", "RefComponent": "MM-IV-LIV", "RefTitle": "MR1M / MIRO: Validation", "RefUrl": "/notes/308866 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}