{"Request": {"Number": "1332333", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 360, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016778312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001332333?language=E&token=9BABD32268E87AE3F231063CD41D7E06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001332333", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001332333/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1332333"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2009"}, "SAPComponentKey": {"_label": "Component", "value": "PA-PD-PM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Objective Setting and Appraisals"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Management", "value": "PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Personnel Development", "value": "PA-PD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Objective Setting and Appraisals", "value": "PA-PD-PM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PD-PM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1332333 - Goal function (Main note)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains the required changes for the correction due to performance improvements.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Performance improvements</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Performance improvements<br />This note contains several subnotes. To correct the performance problems, you must implement all of these subnotes.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package or import the SAPCAR file from this note and implement the following subnotes in the specified sequence:<br /><br />1332334 Goal function: Texts<br />1332335 Goal function: Domains<br />1332336 Goal function: Data elements<br />1332337 Goal function: Structures<br />1332338 Goal function: Table types<br />1341578 Enhancement possibility of IT5029 (Additional information about goals)<br />1332339 Goal function: Code<br />1330606 Goal function: Function module interfaces<br />1331371 Component configuration: Organizational goals<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031342)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D035419)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001332333/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332333/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "EH4K023453.sar", "FileSize": "12", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000192602009&iv_version=0002&iv_guid=44946C984D086F41B06DCC7F8B2EE125"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1560614", "RefComponent": "PA-PD-PM", "RefTitle": "Target application: Asynchronous update of documents", "RefUrl": "/notes/1560614"}, {"RefNumber": "1555494", "RefComponent": "PA-PD-PM", "RefTitle": "Goal application: Cascading of team goals", "RefUrl": "/notes/1555494"}, {"RefNumber": "1433701", "RefComponent": "PA-PD-PM", "RefTitle": "Goals: Data loss warning is missing in portal environment", "RefUrl": "/notes/1433701"}, {"RefNumber": "1429868", "RefComponent": "PA-PD-PM", "RefTitle": "Objectives: Mid-year cascading of objectives in flexible UI", "RefUrl": "/notes/1429868"}, {"RefNumber": "1415459", "RefComponent": "PA-PD-PM", "RefTitle": "Team goals: Subordinate manager can make an appraisal", "RefUrl": "/notes/1415459"}, {"RefNumber": "1382269", "RefComponent": "PA-PD-PM", "RefTitle": "Team Goals in the Predefined Process", "RefUrl": "/notes/1382269"}, {"RefNumber": "1377827", "RefComponent": "PA-PD-PM", "RefTitle": "Goals: The names of the managers are not showing up", "RefUrl": "/notes/1377827"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1364960", "RefComponent": "PA-PD-PM", "RefTitle": "Improvements to the Goals Functionality", "RefUrl": "/notes/1364960"}, {"RefNumber": "1363756", "RefComponent": "PA-PD-PM", "RefTitle": "Adding missing translations", "RefUrl": "/notes/1363756"}, {"RefNumber": "1341578", "RefComponent": "PA-PD-PM", "RefTitle": "Enhancement options for IT5029 (Add. info. about goals)", "RefUrl": "/notes/1341578"}, {"RefNumber": "1333017", "RefComponent": "PA-PD-PM", "RefTitle": "Rating notes for cascaded goals are missing in document", "RefUrl": "/notes/1333017"}, {"RefNumber": "1332339", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Code", "RefUrl": "/notes/1332339"}, {"RefNumber": "1332338", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Table types", "RefUrl": "/notes/1332338"}, {"RefNumber": "1332337", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Structures", "RefUrl": "/notes/1332337"}, {"RefNumber": "1332336", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Data elements", "RefUrl": "/notes/1332336"}, {"RefNumber": "1332335", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Domains", "RefUrl": "/notes/1332335"}, {"RefNumber": "1332334", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Texts", "RefUrl": "/notes/1332334"}, {"RefNumber": "1331371", "RefComponent": "PA-PD-PM", "RefTitle": "Component configuration: Organizational goals", "RefUrl": "/notes/1331371"}, {"RefNumber": "1330606", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Interface function modules", "RefUrl": "/notes/1330606"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1560614", "RefComponent": "PA-PD-PM", "RefTitle": "Target application: Asynchronous update of documents", "RefUrl": "/notes/1560614 "}, {"RefNumber": "1555494", "RefComponent": "PA-PD-PM", "RefTitle": "Goal application: Cascading of team goals", "RefUrl": "/notes/1555494 "}, {"RefNumber": "1364960", "RefComponent": "PA-PD-PM", "RefTitle": "Improvements to the Goals Functionality", "RefUrl": "/notes/1364960 "}, {"RefNumber": "1332339", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Code", "RefUrl": "/notes/1332339 "}, {"RefNumber": "1433701", "RefComponent": "PA-PD-PM", "RefTitle": "Goals: Data loss warning is missing in portal environment", "RefUrl": "/notes/1433701 "}, {"RefNumber": "1429868", "RefComponent": "PA-PD-PM", "RefTitle": "Objectives: Mid-year cascading of objectives in flexible UI", "RefUrl": "/notes/1429868 "}, {"RefNumber": "1415459", "RefComponent": "PA-PD-PM", "RefTitle": "Team goals: Subordinate manager can make an appraisal", "RefUrl": "/notes/1415459 "}, {"RefNumber": "1333017", "RefComponent": "PA-PD-PM", "RefTitle": "Rating notes for cascaded goals are missing in document", "RefUrl": "/notes/1333017 "}, {"RefNumber": "1382269", "RefComponent": "PA-PD-PM", "RefTitle": "Team Goals in the Predefined Process", "RefUrl": "/notes/1382269 "}, {"RefNumber": "1377827", "RefComponent": "PA-PD-PM", "RefTitle": "Goals: The names of the managers are not showing up", "RefUrl": "/notes/1377827 "}, {"RefNumber": "1331371", "RefComponent": "PA-PD-PM", "RefTitle": "Component configuration: Organizational goals", "RefUrl": "/notes/1331371 "}, {"RefNumber": "1341578", "RefComponent": "PA-PD-PM", "RefTitle": "Enhancement options for IT5029 (Add. info. about goals)", "RefUrl": "/notes/1341578 "}, {"RefNumber": "1363756", "RefComponent": "PA-PD-PM", "RefTitle": "Adding missing translations", "RefUrl": "/notes/1363756 "}, {"RefNumber": "1330606", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Interface function modules", "RefUrl": "/notes/1330606 "}, {"RefNumber": "1332338", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Table types", "RefUrl": "/notes/1332338 "}, {"RefNumber": "1332334", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Texts", "RefUrl": "/notes/1332334 "}, {"RefNumber": "1332335", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Domains", "RefUrl": "/notes/1332335 "}, {"RefNumber": "1332336", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Data elements", "RefUrl": "/notes/1332336 "}, {"RefNumber": "1332337", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Structures", "RefUrl": "/notes/1332337 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-HRGXX", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-HRGXX 604", "SupportPackage": "SAPK-60410INEAHRGXX", "URL": "/supportpackage/SAPK-60410INEAHRGXX"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}