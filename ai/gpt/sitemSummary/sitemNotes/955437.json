{"Request": {"Number": "955437", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 368, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016115042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000955437?language=E&token=640747024381C39F2C208811520D2BDD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000955437", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000955437/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "955437"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.12.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-UA-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-UA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-LRQ-UA (Legal Requirement Clarification)", "value": "XX-CSC-UA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-UA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-UA-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-UA-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-UA", "value": "XX-CSC-UA-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-UA-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "955437 - IS-U Billing of Residential customers Ukraine"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>IS-U local enhancement - country specific functionality for Ukraine, installation and documentation.<br /><br />Ukrainian law and business practice define following payments for energy for residential customers:</p> <UL><LI>Residential customers in Ukraine pay one invoice each month.</LI></UL> <UL><LI>There is no budget billing.</LI></UL> <UL><LI>Meter readings are not performed every month. For months without meter reading, invoice is calculated based on contractual consumptions stored for each register.</LI></UL> <p><br />The solution appends contractual consumptions to table EASTE and adds functionality for maintemance of it.<br />For billing of residential customers without actual meter reading a special way of estimation is used. This estimation is based on consumption values from the history or contractual values from table EASTE.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Billing residentials, History of consumption, contractual values of consumption, EASTE, /SAPCE/IUUA_BIRES, /SAPCE/IUUA_BIRES_SAPLELHR_001, _CE_BRH1, /SAPCE/IUUA_REH, /SAPCE/IURU_BILL,  /SAPCE/IURU_EASTE_SAPLEA00_001, /SAPCE/IUUA_REC, /SAPCE/IURU_EASE.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have to install add-on CEEISUT with current AOPs. It<br />contains IS-U/CCS and IS-T localization for CEE countries.<br /><br />Please, read and install also note 865651. In SMOD of enhancment EDMMR003 to screen exits add following lines and activate them in CMOD:<br /><br />Screen areas<br />Calling screen No.&#x00A0;&#x00A0; Area&#x00A0;&#x00A0;&#x00A0;&#x00A0;Called screen No.&#x00A0;&#x00A0; Short Text<br />SAPLEL55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0800&#x00A0;&#x00A0;CUSTDAT SAPLXELP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0100&#x00A0;&#x00A0;Enh. data for EASTE<br />SAPLEL55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0810&#x00A0;&#x00A0;CUSTDAT SAPLXELP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0200&#x00A0;&#x00A0;Enh. data for EASTE<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note:<br /><br />o&#x00A0;&#x00A0;These local enhancements are part of the Russian and Ukrainian<br />&#x00A0;&#x00A0; country version of IS-U/CCS delivered in add-on CEEISUT<br /><br />o&#x00A0;&#x00A0;Local support of SAP Ukraine is responsible for maintenance and hotline support of those enhancements for Ukraine only<br /><br />o&#x00A0;&#x00A0;These enhancements are not released for any other countries.<br /><br />***********************************************************************<br /><br />&#x00A0;&#x00A0;1.&#x00A0;&#x00A0;Overview of enhancements<br /><br />/SAPCE/IUUA_BIRES_SAPLELHR_001- This module computes user defined extrapolation according to history (table /SAPCE/IUUA_REH) or contractual values (table EASTE).<br /><br />_CE_BRH1 - This variant program writes consumption to the table of history /SAPCE/IUUA_REH.<br /><br />/SAPCE/IURU_EASTE_SAPLEA00_001 - This module makes additional proration of billing line items for automatic billing according to external change dates in the table EASTE.<br /><br />/SAPCE/IUUA_REH - Table of history of consumption.<br /><br />/SAPCE/IUUA_REC - Customizing table for activation of module /SAPCE/IUUA_BIRES_SAPLELHR_001 for particular rate category.<br /><br />/SAPCE/IURU_EASE - customizing table. Switches on subscreens for maintenance of period consumption. ***********************************************************************<br /><br /> 2.&#x00A0;&#x00A0;Installation-specific customizing<br /><br />1. Activate functional module /SAPCE/IUUA_BIRES_SAPLELHR_001 in following way: In the include ZXELXU02 of the exit EXIT_SAPLELHR_001 of the enhancement EDMLELHR insert following text:<br /><br />INCLUDE /sapce/iuua_bires_zxelxu02.<br />and the enhancement EDMLELHR must be activated.<br /><br /><br />2.&#x00A0;&#x00A0;Activate the functional module of additional proration of billing line items for automatic billing according to external scheduled dates in the table EASTE. In include ZXBI1U02 of the exit EXIT_SAPLEA00_001 of the enhancement EBIA0002 insert following text:<br /><br />INCLUDE /SAPCE/IURU_PROEASTE_ZXBI1U02<br />and the enhancement EBIA0002 must be activated.<br /><br />3. Activate the functional modules of additional subscreens of  transaction el56. All user exits are in the enhancement EDMMR003.<br /><br />In the Include ZXELPU01 of the user exit EXIT_SAPLEL55_001 fill following text:<br />INCLUDE /sapce/iuua_easte_zxelpu01.<br />In the Include ZXELPU03 of the user exit EXIT_SAPLEL55_003 fill following text:<br />INCLUDE /sapce/iuua_easte_zxelpu03.<br />In the Include ZXELPU04 of the user exit EXIT_SAPLEL55_004 fill following text:<br />INCLUDE /sapce/iuua_easte_zxelpu04.<br />In the Include ZXELPU06 of the user exit EXIT_SAPLEL55_006 fill following text:<br />INCLUDE /sapce/iuua_easte_zxelpu06.<br />In the Include ZXELPZZZ fill following text:<br />INCLUDE /sapce/iuua_easte_zxelpf01.<br />In the Include ZXELPTOP fill following text:<br />INCLUDE /sapce/iuua_easte_zxelptop.<br />and the enhancement EDMMR003 must be activated.<br /><br />4. Customize transaction EL56 for the maintenance of additional data. The subscreen for filling of contractual consumption quantities is displayed if field BI_RES_ON is set to value 'X' in the customizing table /SAPCE/IURU_EASE.<br /><br /><br />In Transaction SMOD, add the following entries to the enhancement EDMMR003 in the components:<br />Screen areas<br />Calling screen No. Area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Called screen No.Short Text<br />SAPLEL55 0800 CUSTDAT SAPLXELP 0100 Enh. data for EASTE<br />SAPLEL55 0810 CUSTDAT SAPLXELP 0200 Enh. data for EASTE<br /><br />In function group XELP change screens 0100 and 0200 (create them if they don't exists as \"Subscreen\" and short description \"Enh. data for EASTE\"), to flow logic add for both screens following text:<br /><br />PROCESS BEFORE OUTPUT.<br />* MODULE STATUS_0100.<br /><br />&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuru_bires INCLUDING '/SAPCE/SAPLIUUA_EASTE'<br />screen_bires.<br /><br />&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuru_maxload INCLUDING '/SAPCE/SAPLIUUA_EASTE'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;screen_maxload.<br /><br />&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuru_loses INCLUDING '/SAPCE/SAPLIUUA_EASTE'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;screen_loses.<br /><br />PROCESS AFTER INPUT.<br />* MODULE USER_COMMAND_0100.<br />&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuru_bires.<br />&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuru_maxload.<br />&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuru_loses.<br /><br />And also in Layout mode create 3 new Subscreen areas with names:<br />/SAPCE/IURU_BIRES<br />/SAPCE/IURU_MAXLOAD<br />/SAPCE/IURU_LOSES<br /><br />5. Use variant program _CE_BRH1 in your rate and billing schema for residential customers. For him customizin use the BC-Set &#x00A0;&#x00A0;/CEEISUT/ISU_UA_07.<br /><br />***********************************************************************<br /><br /> 3.&#x00A0;&#x00A0;Solution-specific customizing<br />If the estimation module does not find the line in customizing table /SAPCE/IUUA_REC for relevant rate category, then standard extrapolation procedure will be used.<br /><br /> ***********************************************************************<br /><br /> 4.&#x00A0;&#x00A0;Documentation<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; The documentation is attached to this note.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; (If you have accessed this note through SAP Service Marketplace<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; please see Tab-page Attachments.)<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-RU-IS-U (Utilities)"}, {"Key": "Responsible                                                                                         ", "Value": "C5062870"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I041621)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955437/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ISU_Doc_UA_BiRes_EN_130.pdf", "FileSize": "139", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000253172006&iv_version=0012&iv_guid=DDDBB55CC040F54E8477B031BE49E9EE"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "865651", "RefComponent": "IS-U-DM-MR", "RefTitle": "Maintenance dialog of the customer include CI_EASTE", "RefUrl": "/notes/865651"}, {"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953"}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953 "}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272 "}, {"RefNumber": "865651", "RefComponent": "IS-U-DM-MR", "RefTitle": "Maintenance dialog of the customer include CI_EASTE", "RefUrl": "/notes/865651 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}