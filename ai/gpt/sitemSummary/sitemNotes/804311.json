{"Request": {"Number": "804311", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 410, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015820922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000804311?language=E&token=A96C29CA77A55A16222D5E3DE01C8739"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000804311", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000804311/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "804311"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.09.2014"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CZ-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-CZ"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Czech Republic", "value": "XX-CSC-CZ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-CZ-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-CZ", "value": "XX-CSC-CZ-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "804311 - Non-delivered credit memos"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>IS-U/CCS and IS-T local enhancement - country specific functionality for Czech Republic, installation and documentation.<br />One of the requirements of the FI-CA localization (IS-U/CCS and IS-T) for CZ&#160;&#160;is to report credit memos issued to business partner which is VAT payer in VAT report only after its delivery is confirmed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FI-CA, /SAPCE/FK_NDCRN, /SAPCE/FK_EVENT_0010_NDCRN, /SAPCE/FK_EVENT_0020_NDCRN, /SAPCE/FK_EVENT_0070_NDCRN, /SAPCE/FK_EVENT_0067_NDCRN, /SAPCE/FK_SAMPLE_X010</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have to install add-on CEEISUT rel.4.72 with AOP up to level 2, or CEEISUT 6.00, or CEEISUT 6.04, or CEEISUT 6.06. It contains IS-U/CCS and IS-T localization for CEE countries.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><em>Change on September 4th 2014:</em> If you are using this solution on EhP7, it is recommended to use standard report delivered with FI-CA EhP7 SP06. Please, check also Notes <strong>2059470</strong>,&#160;<strong>2056151</strong>, and <strong>1992561</strong>.</p>\r\n<p>If you already use the solution in add-on CEEISUT and you want to switch to the standard solution delivered in FI-CA EhP7 SP06 you can use&#160;a migration tool (report <strong>FKKID_LOAD_CREDIT_MEMOS</strong> or transaction <strong>FKKID_LOAD_OLDCRM</strong>) to migrate data about nondelivered credit memos when the following condition is met: you have both the CEEISUT 606 SP09 add-on and FI-CA 617 SP06 standard software component versions (SWCs) installed in your system.</p>\r\n<p>Both software component versions contain a report that processes nondelivered credit memos:</p>\r\n<ul>\r\n<li>In the CEEISUT add-on SWC, the <em>Transfer Posting of Nondelivered Credit Memo</em>s (transaction code: /SAPCE/FKSI_NDCRN) is used.</li>\r\n</ul>\r\n<ul>\r\n<li>In the FI-CA standard SWC, the <em>VAT Transfer of Credit Memos</em> (transaction code: FKKID_VATTR_CM) is used.</li>\r\n</ul>\r\n<p>If both SWCs are installed in your system, only one of the above listed reports can be used; which is the <em>VAT Transfer of Credit Memos</em> in the standard FI-CA SWC. So that you can migrate the relevant data from the add-on /SAPCE/FK_NDCRN database table into the FKKID_VAT_HDR_A and FKKID_VAT_ITM_A database tables, you must run this migration tool.</p>\r\n<p>Also, please note, that if you are installing CEEISUT Add-On on system, where was already introduced the FI-CA standard report, it is required to install the latest SP of CEEISUT Add-On (minumum required is CEEISUT 6.06 SP09). <em>(end of Change on September 4th 2014)</em></p>\r\n<p>Note: If you have decided to use new FI-CA standard&#160;report <em>VAT Transfer of Credit Memos</em> (transaction code: FKKID_VATTR_CM), you have to also make sure, that right function modules are sued in <strong>FQEVENTS</strong> transaction.</p>\r\n<p>Run transaction <strong>FQEVENTS&#160;&#160;- Management of Events</strong> and check if you have removed all the function modules you created in the CEEISUT add-on component. After the migration, you are not allowed to use the add-on function modules. You must ensure that for each event below, you have one or more function module created. <strong>Event X010 must not be used in the standard FI-CA component at all.</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Event</strong></td>\r\n<td><strong>FM in Standard FI-CA</strong></td>\r\n<td><strong>FM in CEEISUT Add-On</strong></td>\r\n</tr>\r\n<tr>\r\n<td>0020</td>\r\n<td>FKKID_VT_0020</td>\r\n<td>/SAPCE/FK_EVENT_0020_NDCRN</td>\r\n</tr>\r\n<tr>\r\n<td>0062</td>\r\n<td>FKKID_VT_0062</td>\r\n<td>/SAPCE/FK_EVENT_0062_NDCRN</td>\r\n</tr>\r\n<tr>\r\n<td>0067</td>\r\n<td>FKKID_VT_0067</td>\r\n<td>/SAPCE/FK_EVENT_0067_NDCRN</td>\r\n</tr>\r\n<tr>\r\n<td>0070</td>\r\n<td>FKKID_VT_0070</td>\r\n<td>/SAPCE/FK_EVENT_0070_NDCRN</td>\r\n</tr>\r\n<tr>\r\n<td>0901</td>\r\n<td>FKKID_VT_0901</td>\r\n<td>/SAPCE/FKSI_EVENT_0901_NDCRN</td>\r\n</tr>\r\n<tr>\r\n<td>0902</td>\r\n<td>FKKID_VT_0902</td>\r\n<td>/SAPCE/FKSI_EVENT_0902_NDCRN</td>\r\n</tr>\r\n<tr>\r\n<td>X010</td>\r\n<td>-</td>\r\n<td>/SAPCE/FKSI_EVENT_X010</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><em>End of Change on September 4th 2014</em></p>\r\n<p>This note describes&#160;local enhancements which are part of the Czech and Slovak country version of IS-U/CCS delivered in add-on CEEISUT.</p>\r\n<p><em>************************************************************************</em></p>\r\n<ol>1. Overview of enhancements</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In order to meet this requirement the tax is posted in two steps:</p>\r\n<ul>\r\n<li>In the first step (either event 0010 during credit memo posting for CEEISUT releases below 6.06 or event 0067 in CEEISUT 6.06 and higher). The VAT is posted with temporary tax code and document number of credit memo is stored in solution specific table (where the state of credit memo delivery is tracked).</li>\r\n</ul>\r\n<ul>\r\n<li>In the second step (in solution specific program when confirmation of credit memo delivery is entered into system). The state of credit memo delivery is changed to 'Delivered' and new document is posted to transfer the tax form 'temporary tax code' to 'target tax code'.</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In case of non-VAT payer the VAT should be posted directly to target tax code (this is not subject of this solution) and included in VAT report, the confirmation of delivery is not necessary there.</p>\r\n<p>************************************************************************</p>\r\n<ol>2. Solution-specific customizing</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The solution-specific customizing could be set by activating:</p>\r\n<ul>\r\n<li>BC set /CEEISUT/ISU_CZSK_1_34</li>\r\n</ul>\r\n<ul>\r\n<li>or one of the following hierarchical BC sets:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/CEEISUT/ISU_CZ for IS-U CZ</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/CEEISUT/IST_CZ for IS-T CZ</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;in transaction SCPR20.</p>\r\n<p>************************************************************************</p>\r\n<ol><ol>3. Installation-specific customizing</ol></ol><ol>To access customizing you can use one of the following transactions:</ol>\r\n<ul>\r\n<li>/SAPCE/IU_CZ_IMG - IMG for IS-U localization for CZ</li>\r\n</ul>\r\n<ul>\r\n<li>/SAPCE/IT_CZ_IMG - IMG for IS-T localization for CZ</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Note: To call for example transaction /SAPCE/IU_CZ_IMG, you have to write /n/SAPCE/IU_CZ_IMG in command field or add transaction to your favorites.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In activity \"Temporary VAT codes -&gt; Target VAT codes\" (view /SAPCE/FK_NDCRV1) you have to specify for each temporary tax code and its tax account the target tax code to which will be temporary tax posting transferred.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In activity \"Document origin keys for postings\" (table /SAPCE/FK_NDCR02) you have to define the origin key for posting of transfer documents and the origin key for reversal of transfer documents. The table is obsolete for CEEISUT release 6.06 and higher, origin key 64 is always used.</p>\r\n<p>************************************************************************</p>\r\n<ol>4. Documentation</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The documentation is attached to this note. <br />(If you have accessed this note through SAP Service Marketplace see Tab-page Attachements.)</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-CZ-IS-T (use FI-LOC-CA-CZ)"}, {"Key": "Responsible                                                                                         ", "Value": "I024302"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I044454)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804311/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "NDCRN_docu_472_en.pdf", "FileSize": "87", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000503322004&iv_version=0009&iv_guid=026494CE780536409EA0F36CFADF3308"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "793026", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "CEEISUT rel.4.72 Add-On AOP2", "RefUrl": "/notes/793026"}, {"RefNumber": "774625", "RefComponent": "XX-CSC-CZ-IS-T", "RefTitle": "Czech & Slovak specific functionality IS-T 4.72 (collective)", "RefUrl": "/notes/774625"}, {"RefNumber": "774624", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/774624"}, {"RefNumber": "1494352", "RefComponent": "XX-CSC-SI-IS-U", "RefTitle": "Slovenian specific functionality for IS-UT 604", "RefUrl": "/notes/1494352"}, {"RefNumber": "1456365", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "IS-U CZSK FI-CA Non-Delivered Credit Memo - Wrong FIKEY", "RefUrl": "/notes/1456365"}, {"RefNumber": "1014963", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-UT 600 (collective)", "RefUrl": "/notes/1014963"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2425702", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "FICA: Non-delivered credit memos - incorrect XBLNR if ODN is activated", "RefUrl": "/notes/2425702 "}, {"RefNumber": "2501757", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FICA localization - country specific steps(BC Set activation replacement)", "RefUrl": "/notes/2501757 "}, {"RefNumber": "2059470", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "CEEISUT Add-On: Migration of Non-Delivered Credit Memos to FI-CA VAT Transfer", "RefUrl": "/notes/2059470 "}, {"RefNumber": "1494352", "RefComponent": "XX-CSC-SI-IS-U", "RefTitle": "Slovenian specific functionality for IS-UT 604", "RefUrl": "/notes/1494352 "}, {"RefNumber": "1456365", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "IS-U CZSK FI-CA Non-Delivered Credit Memo - Wrong FIKEY", "RefUrl": "/notes/1456365 "}, {"RefNumber": "1014963", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-UT 600 (collective)", "RefUrl": "/notes/1014963 "}, {"RefNumber": "774625", "RefComponent": "XX-CSC-CZ-IS-T", "RefTitle": "Czech & Slovak specific functionality IS-T 4.72 (collective)", "RefUrl": "/notes/774625 "}, {"RefNumber": "793026", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "CEEISUT rel.4.72 Add-On AOP2", "RefUrl": "/notes/793026 "}, {"RefNumber": "774624", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/774624 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}