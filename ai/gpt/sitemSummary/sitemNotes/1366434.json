{"Request": {"Number": "1366434", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 761, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008054302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001366434?language=E&token=6E4482DCCCFDFE7BB242DD7AA382955E"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001366434", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001366434/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1366434"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.07.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1366434 - IS-H CH: Analysis List - Service Rules"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1366434&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1366434/D\" target=\"_blank\">/notes/1366434/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Switzerland (CH).<br />The following three new service rules are delivered with the corresponding Customizing tables for billing analysis services:</p> <UL><LI>Generate Surcharge Services (ISH_CH_CHECK_ANALYSE_EXTRA)</LI></UL> <UL><LI>Maximum Value Rule (ISH_CH_CHECK_ANALYSE_MAX)</LI></UL> <UL><LI>Cumulation (ISH_CH_CHECK_ANALYSE_KUMU)</LI></UL> <p><br />After you have implemented this SAP Note, you can find more information in Customizing under SAP Healthcare - Industry-Specific Components for Hospitals -> Patient Accounting -> Service Rules -> CH: Service Rules -> CH: ANALYSIS Service Rules<br /><br />The standard invoice form ISH_CH_BILL_V40 has been enhanced to include the output of analytical services (rate category 317).<br /></p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>analytics, service rules,</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Note that SAP Note 1354157 - IS-H: ANA-LISTE (CH delta) - is an unconditional prerequisite for the implementation of this SAP Note.<br /><br /><B><B>Prerequisites for Release 4.72:</B></B><br />Add-On Support Package 25 and the following SAP Notes must be implemented in the system:<br />1128597 IS-H CH: Invoice form, tax points for percentage services<br />1140747 IS-H CH: INXML - Print : Taxpoint Total Medical Services<br />1147780 IS-H CH: INXML/Print: BAdI for Compulsory Service Indicator<br />1162263 IS-H CH: Implementation of new AHV number<br />1166717 IS-H CH: NV2000 Entry check of new AHV number<br />1248688 IS-H CH: Invoice V4.0, treatment reason, total TARMED TL<br />1252208 IS-H CH: Invoice V4.0, sort sequence<br />1272356 IS-H CH: Invoice V4.0, sort sequence [2]<br />1286728 IS-H CH: Invoice V4.0, sort sequence [3]<br />1325849 IS-H CH: Change of law/treatment reason for invoice<br /><br /><br /><B>Prerequisites for Release 6.00:</B><br />Add-On Support Package 13 and the following SAP Notes must be implemented in your system:<br />1140747 IS-H CH: INXML - Print : Taxpoint Total Medical Services<br />1147780 IS-H CH: INXML/Print: BAdI for Compulsory Service Indicator<br />1162263 IS-H CH: Implementation of new AHV number<br />1166717 IS-H CH: NV2000 Entry check of new AHV number<br />1248688 IS-H CH: Invoice V4.0, treatment reason, total TARMED TL<br />1252208 IS-H CH: Invoice V4.0, sort sequence<br />1272356 IS-H CH: Invoice V4.0, sort sequence [2]<br />1286728 IS-H CH: Invoice V4.0, sort sequence [3]<br />1325849 IS-H CH: Change of law/treatment reason for invoice<br /><br />or the following SAP Notes must be implemented in the system:<br />861428 Corrections for invoice form ISH_CH_BILL_V40<br />892023 Corrections for invoice form ISH_CH_BILL_V40<br />903652 Corrections for invoice form ISH_CH_BILL_V40<br />922087 IS-H CH: Customizing Rate Categories for Invoice<br />955482 Invoice ISH_CH_BILL_V40 - take partial quantity into account<br />968363 IS-H CH: Invoice V4.0, Heading TG/TP Invoice<br />1020045 IS-H CH: Adjustments Based on TARMED Version 1.04<br />1072759 IS-H CH: INXML Proportional Tax Points - Group Therapy<br />1092536 IS-H CH: Invoice V4.0, Tax Points - Reference Digit<br />1128597 IS-H CH: Invoice form, tax points for percentage services<br />1140747 IS-H CH: INXML - Print : Taxpoint Total Medical Services<br />1147780 IS-H CH: INXML/Print: BAdI for Compulsory Service Indicator<br />1162263 IS-H CH: Implementation of new AHV number<br />1166717 IS-H CH: NV2000 Entry check of new AHV number<br />1248688 IS-H CH: Invoice V4.0, treatment reason, total TARMED TL<br />1252208 IS-H CH: Invoice V4.0, sort sequence<br />1272356 IS-H CH: Invoice V4.0, sort sequence [2]<br />1286728 IS-H CH: Invoice V4.0, sort sequence [3]<br />1325849 IS-H CH: Change of law/treatment reason for invoice<br /><br /><br /><B>Prerequisites for Release 6.03:</B><br />Add-On Support Package 1 and the following SAP Notes must be implemented in the system:<br />1140747 IS-H CH: INXML - Print : Taxpoint Total Medical Services<br />1147780 IS-H CH: INXML/Print: BAdI for Compulsory Service Indicator<br />1162263 IS-H CH: Implementation of new AHV number<br />1166717 IS-H CH: NV2000 Entry check of new AHV number<br />1248688 IS-H CH: Invoice V4.0, treatment reason, total TARMED TL<br />1252208 IS-H CH: Invoice V4.0, sort sequence<br />1272356 IS-H CH: Invoice V4.0, sort sequence [2]<br />1286728 IS-H CH: Invoice V4.0, sort sequence [3]<br />1325849 IS-H CH: Change of law/treatment reason for invoice<br /><br /><br /><B>Prerequisites for Release 6.04:</B><br />Add-On Support Package 1 and the following SAP Notes must be implemented in the system:<br />1248688 IS-H CH: Invoice V4.0, treatment reason, total TARMED TL<br />1252208 IS-H CH: Invoice V4.0, sort sequence<br />1272356 IS-H CH: Invoice V4.0, sort sequence [2]<br />1286728 IS-H CH: Invoice V4.0, sort sequence [3]<br />1325849 IS-H CH: Change of law/treatment reason for invoice<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before you implement the source code corrections, you must perform the following actions manually:<br />When you do this, you must take into account the sequence of the manual activities specified here.</p> <OL>1. Unpack the attached files depending on the IS-H version:</OL> <OL><OL>a) HW1366434_472.zip and then HW1366434_472_CUST.zip for 4.72 AOP 01 - 34</OL></OL> <OL><OL>b) HW1366434_60.zip and then HW1366434_60_CUST.zip for 6.0 AOP 01 - 19</OL></OL> <OL><OL>c) HW1366434_603.zip and then HW1366434_603_CUST.zip for 6.03 AOP 01 - 07</OL></OL> <OL><OL>d) HW1366434_604.zip and then HW1366434_604_CUST.zip for 6.04 AOP 01 - 06</OL></OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the requests contained (one request each for the development and Customizing clients) into your system.<br />Note that the relevant request contained in the &quot;_xxx.zip&quot; file must be imported first and only then the relevant request contained in the &quot;_xxx_CUST.zip&quot; file.<br /><br />When you import the attachment transports, generation errors may occur that are corrected after you implement the source code corrections from this SAP Note.<br /></OL> <OL>3. Now implement the source code corrections.<br /></OL> <OL>4. You must now perform the following manual tasks:</OL> <OL>5. Add the following entry to the Implementation Guide (IMG):</OL> <OL><OL>a) Call transaction SIMGH.</OL></OL> <OL><OL>b) Select the IMG structure &quot;SAP Healthcare - Industry-Specific Components for Hospitals&quot; and choose &quot;Change IMG Structure&quot;.</OL></OL> <OL><OL>c) Go to SAP Healthcare - Industry-Specific Components for Hospitals => Patient Accounting => Service Rules => CH: Service Rules.</OL></OL> <OL><OL>d) Click on the text of the IMG activity &quot;CH:TARMED Service Rules&quot; and choose &quot;Structure Node for Same Insert Level&quot;.</OL></OL> <OL><OL>e) In the &quot;Node Text&quot; field, enter &quot;CH: ANALYSIS Service Rules&quot;.</OL></OL> <OL><OL>f) Click on the newly created node and choose &quot;Insert Activity as Subnode&quot;.</OL></OL> <OL><OL>g) In the &quot;IMG Activity&quot; frame, enter:<br />- ID: ISH_CH_ANALYSE_EXTRA<br />- Name: &quot;CH: Surcharge Services&quot;</OL></OL> <OL><OL>h) On the &quot;Document&quot; tab page, enter:<br />- Document Class: SIMG<br />- Document name: ISH_CH_ANALYSE_EXTRA</OL></OL> <OL><OL>i) On the &quot;Attributes&quot; tab page, enter the following:<br />- ID and description see above<br />- ASAP Roadmap ID: 203<br />- Mandatory/Optional: Optional activity<br />- Critical/Non-Critical: Non-Critical<br />- Country Dependency: valid only for specified countries<br />- List CH (Switzerland) as the country.<br />- Under Assigned Application Component add entry   I010004221</OL></OL> <OL><OL>j) On the &quot;Maintenance Objects&quot; tab page, enter:<br />- ID and description see above<br />- Maintenance Object Type: Customizing Object<br />- Customizing Object: TNWCH_AL_EXTRA<br />- Type: S<br />- Transaction: SM30</OL></OL> <OL><OL>k) Save your entry. If you are asked for a package name, specify &quot;NCH1&quot;.</OL></OL> <OL><OL>l) Select the newly created activity and choose &quot;Insert Activity at Same Level&quot;.</OL></OL> <OL><OL>m) In the &quot;IMG Activity&quot; frame, enter:<br />- ID: ISH_CH_ANALYSE_MAX<br />- Name: &quot;CH: Maximum Values&quot;</OL></OL> <OL><OL>n) On the &quot;Document&quot; tab page, enter:<br />- Document Class: SIMG<br />- Document name: ISH_CH_ANALYSE_MAX</OL></OL> <OL><OL>o) On the &quot;Attributes&quot; tab page, enter:<br />- ID and description see above<br />- ASAP Roadmap ID: 203<br />- Mandatory/Optional: Optional activity<br />- Critical/Non-Critical: Non-Critical<br />- Country Dependency: valid only for specified countries<br />- List CH (Switzerland) as the country.<br />- Under Assigned Application Component add entry   I010004221</OL></OL> <OL><OL>p) On the &quot;Maintenance Objects&quot; tab page, enter:<br />- ID and description see above<br />- Maintenance Object Type: Customizing Object<br />- Customizing Object: TNWCH_AL_NLHO<br />- Type: S<br />- Transaction: SM30</OL></OL> <OL><OL>q) Save your entry. If you are asked for a package name, specify &quot;NCH1&quot;.</OL></OL> <OL><OL>r) Choose the newly created activity and choose &quot;Insert Activity at Same Level&quot;.</OL></OL> <OL><OL>s) In the &quot;IMG Activity&quot; frame, enter:<br />- ID: ISH_CH_ANALYSE_KUMU<br />- Name: &quot;CH: Cumulation&quot;</OL></OL> <OL><OL>t) On the &quot;Document&quot; tab page, enter:<br />- Document Class: SIMG<br />- Document name: ISH_CH_ANALYSE_KUMU</OL></OL> <OL><OL>u) On the &quot;Attributes&quot; tab page, enter:<br />- ID and description see above<br />- ASAP Roadmap ID: 203<br />- Mandatory/Optional: Optional activity<br />- Critical/Non-Critical: Non-Critical<br />- Country Dependency: valid only for specified countries<br />- List CH (Switzerland) as the country.<br />- Under Assigned Application Component add entry   I010004221</OL></OL> <OL><OL>v) On the &quot;Maintenance Objects&quot; tab page, enter:<br />- ID and description see above<br />- Maintenance Object Type: Customizing Object<br />- Customizing Object: TNWCH_AL_KUMU<br />- Type: S<br />- Transaction: SM30</OL></OL> <OL><OL>w) Save your entry. If you are asked for a package name, specify &quot;NCH1&quot;.</OL></OL> <p><br /><br />If you do not import AOSP 35 for Release 4.72 or AOSP 20 for Release 6.00 or AOSP 8 for Release 6.03 or Import Add-On Support Package 7 for Release 6.04, perform the following steps:<br /><br />Unpack the attached file HW1366434.zip.<br />Make the changes in your system as described in this document.<br /><br />Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments).<br /></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C5025082)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001366434/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1366434_604_CUST.zip", "FileSize": "92", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=F2A56266CE9533419F1D00E741C78AD0"}, {"FileName": "HW1366434.zip", "FileSize": "185", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=687275887E4D7B42BD30747569B66F4B"}, {"FileName": "HW1366434_604.zip", "FileSize": "105", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=1D43747563B4994EBFB4D8707C2BB160"}, {"FileName": "HW1366434_60_CUST.zip", "FileSize": "49", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=A48D3B0A741B314CBB1565AEBC287369"}, {"FileName": "HW1366434_472.zip", "FileSize": "80", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=A42D51E22FFC56478D7FA708217D52B8"}, {"FileName": "HW1366434_603_CUST.zip", "FileSize": "49", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=A9ED16DC2C04194E9DC150C141D99D71"}, {"FileName": "HW1366434_472_CUST.zip", "FileSize": "4", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=E87FD5ED2397694ABE73B922C7132DF1"}, {"FileName": "HW1366434_60.zip", "FileSize": "78", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=8F8AD0C15DC0884E981656DE7090AE6D"}, {"FileName": "HW1366434_603.zip", "FileSize": "77", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000442682009&iv_version=0002&iv_guid=9AE29D843FA6A040924DEDCBBBD2E861"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1762232", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Invoice Form External Order - EAN Number", "RefUrl": "/notes/1762232"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1354157", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA LIST (CH Delta)", "RefUrl": "/notes/1354157"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1762232", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Invoice Form External Order - EAN Number", "RefUrl": "/notes/1762232 "}, {"RefNumber": "1354157", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA LIST (CH Delta)", "RefUrl": "/notes/1354157 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF35", "URL": "/supportpackage/SAPKIPHF35"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60020INISH", "URL": "/supportpackage/SAPK-60020INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60209INISH", "URL": "/supportpackage/SAPK-60209INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60308INISH", "URL": "/supportpackage/SAPK-60308INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60407INISH", "URL": "/supportpackage/SAPK-60407INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/0001366434/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "772125 ", "URL": "/notes/772125 ", "Title": "IS-H CH: Service Rules: Quantity Limitation, Anesthesia Rule", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "833546 ", "URL": "/notes/833546 ", "Title": "IS-H CH: Service Rule C19, C29 - TARMED Maximum", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "913283 ", "URL": "/notes/913283 ", "Title": "IS-H CH: Service Rule C18 - Assignment Type for TARMED", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "926472 ", "URL": "/notes/926472 ", "Title": "IS-H CH: C30 - Service Blocks", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1099059 ", "URL": "/notes/1099059 ", "Title": "IS-H CH: Tarmed Version 1.05 - Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1126817 ", "URL": "/notes/1126817 ", "Title": "IS-H CH: Tarmed Assignment KVG and UVG/MTK/IVG", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1157762 ", "URL": "/notes/1157762 ", "Title": "IS-H CH: Enhancement of Service Rule C23 Division Surcharge", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "913283 ", "URL": "/notes/913283 ", "Title": "IS-H CH: Service Rule C18 - Assignment Type for TARMED", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "926472 ", "URL": "/notes/926472 ", "Title": "IS-H CH: C30 - Service Blocks", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1099059 ", "URL": "/notes/1099059 ", "Title": "IS-H CH: Tarmed Version 1.05 - Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1126817 ", "URL": "/notes/1126817 ", "Title": "IS-H CH: Tarmed Assignment KVG and UVG/MTK/IVG", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1157762 ", "URL": "/notes/1157762 ", "Title": "IS-H CH: Enhancement of Service Rule C23 Division Surcharge", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1157762 ", "URL": "/notes/1157762 ", "Title": "IS-H CH: Enhancement of Service Rule C23 Division Surcharge", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1366434&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1366434/D\" target=\"_blank\">/notes/1366434/D</a>."}}}}