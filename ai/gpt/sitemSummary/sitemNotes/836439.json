{"Request": {"Number": "836439", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1225, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015879922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=A70B1D5AFBFF7B2F3D948E1D20DCE361"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "836439"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.04.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Business Warehouse"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "836439 - SAPBWNews BW Support Package 14 NW'04 Stack 14 RIN"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Support Package 14 for BW Release 3.5</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPBWNEWS, Support Packages for 3.5, BW 3.5, BW 3.50, BW Patches</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note contains the SAPBWNews for Support Package 14 for BW Release 3.5. It contains a list of all notes that describe the corrections or enhancements in Support Package 14 (SAPKW35014). The note will be updated as other notes are added.<br /><br />The information is divided into the following areas:</p> <UL><LI>Manual activities that may be necessary:</LI></UL> <UL><UL><LI>Factors you must take into account when you import the Support Package</LI></UL></UL> <UL><UL><LI>Errors that may occur after you import the Support Package</LI></UL></UL> <UL><LI>General information:</LI></UL> <UL><UL><LI>This Support Package corrects the following errors</LI></UL></UL> <UL><UL><LI>Enhancements delivered with this Support Package</LI></UL></UL> <p><br />This Support Package 14 for BW 3.5 corresponds to the technological status of Support Package 28 for BW 3.0B (SAPBWNews 783170) and Support Package 22 for BW 3.1 Content (SAPBWNews 783252).<br /></p> <UL><LI>Also refer to the following release and information note:</LI></UL> <UL><UL><LI>SP14 SAP_UDI 350 Note 839096</LI></UL></UL> <p></p> <b>Factors you must take into account when you import the Support Package:</b><br /> <p><br />Note that as of NetWeaver Support Package Stack 14, that is, BW Server Support Package 14, you require PI_BASIS 2005 Support Package 4. For information about upgrading to PI_BASIS 2005, see Note 821148.<br /><br />Note 855424 causes syntax errors during the generation of update rules. Read the note for more information.<br /><br />Refer to Note 912773 if the filter value selection takes a long time.<br /><br />It is essential that you read Note 922544. When you delete master data, data inconsistencies may arise in the Informix/BW combination.<br /></p> <b>Errors that may occur after you import the Support Package:</b><br /> <p>A termination may occur when you completely delete the data target content. For more information, see Note 934417.</p> <b></b><br /> <b></b><br /> <p>A query displays additional rows or columns. For more information, see Note 901097.<br />Querys on MultiCubes in read mode A. For more information, see Note 896066.<br /><br />The update from the PSA using the 'Update' indicator in the scheduler no longer works for normal master data that is not flexible and text DataSources that were updated using 'PSA only' - Reason: The problem is caused by the correction that is contained in Note 889160, Note 905105 corrects the problem.<br /><br />F4 and authorization: SQL error in SAPLRSDM_F4. For more information, see Note 907337.<br /><br />It is essential that you read Note 922544. When you delete master data, data inconsistencies may arise in the Informix/BW combination.<br /><br /></p> <b>This Support Package corrects the following errors:</b><br /> <UL><LI>Corrections in the area of end user technology:</LI></UL> <UL><UL><LI>When you go from one query to another or to a Web template, the system fills value variables on compound characteristics with the initial value (\"#\"), and these variables can be changed simultaneously during query navigation. For more information, see Note 867616.</LI></UL></UL> <UL><LI>Corrections in the area of OLAP technology:</LI></UL> <UL><UL><LI>In certain cases, when you execute a query, a termination (GETWA_NOT_ASSIGNED in the SAPSRRK0 &lt;L_S_SNID&gt; field symbol) occurs or data is missing in columns or structural components. For more information, see Notes 851086 and 848340.</LI></UL></UL> <UL><UL><LI>In certain cases, when you execute a query, a termination occurs and the system issues message X299(BRAIN) 'System error: Program SAPLRRKH; form SELDER_INTERSECT_BUF-01-'. For more information, see Note 851754.</LI></UL></UL> <UL><UL><LI>You can execute a query that does not use any key figures at all even if you do not have the necessary authorizations. For more information, see Note 856144.</LI></UL></UL> <UL><UL><LI>When you check a query, the system may issue error message Brain A 039 (Node &amp;3 &amp;4 is not in hierarchy &amp;2 for InfoObject &amp;1) even though the node exists and you can execute the query. For more information, see Note 855258.</LI></UL></UL> <UL><UL><LI>In certain cases, a query with an exclude in the dynamic filter may not display any data. For more information, see Note 856495.</LI></UL></UL> <UL><UL><LI>In certain cases, the Multiprovider F4 help does not use aggregates that were also used during the query execution. For more information, see Note 870557.</LI></UL></UL> <UL><UL><LI>A termination may occur when you execute a MultiProvider query. For more information, see Note 870505.</LI></UL></UL> <UL><LI>Corrections in the area of Warehouse Management:</LI></UL> <UL><UL><LI>The UNCAUGHT_EXCEPTION runtime error occurs during the upload. For more information, see Note 855424.</LI></UL></UL> <p><br /></p> <b>Enhancements delivered with this Support Package:</b><br /> <p>Also refer to the Enhancement Documentation on the SAP Service Marketplace (http://service.sap.com/bi --&gt; Documentation --&gt; SAP BW 3.5 Documentation Enhancements), which describe the enhancements that are delivered with this Support Package.<br /></p> <UL><LI>Improvements in the area of OLAP technology:</LI></UL> <UL><UL><LI>General performance improvements for OLAP. For more information, see Note 825396.</LI></UL></UL> <UL><UL><LI>The runtime in the REMOVE_SELDR_0INFOPROV_NEW form was long. For more information, see Note 887801.</LI></UL></UL> <UL><UL><LI>The system unnecessarily read a partprovider. For more information, see Note 885733.</LI></UL></UL> <UL><UL><LI>The performance in form SAPLRRK0; loc_rechnen has been improved. For more information, see Note 884482.</LI></UL></UL> <UL><UL><LI>Problems occurred with hierarchy selections in MultiProviders. For more information, see Note 879290.</LI></UL></UL> <UL><UL><LI>Query runtime if there are several nested formulas has been improved. For more information, see Note 872904.</LI></UL></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "D046349"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "922544", "RefComponent": "BW-SYS-DB-INF", "RefTitle": "BW Informix: Data loss after you delete master data", "RefUrl": "/notes/922544"}, {"RefNumber": "877005", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299(BRAIN) in program SAPLRRK0; FORM MEGASORT_14-01-", "RefUrl": "/notes/877005"}, {"RefNumber": "874714", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Exception aggregation AVG in BAPI_MDPROVIDER_GET_MEASURES", "RefUrl": "/notes/874714"}, {"RefNumber": "874662", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Substituting text variables with multiple-value selections", "RefUrl": "/notes/874662"}, {"RefNumber": "874661", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search string and conversion exit with characteristic value", "RefUrl": "/notes/874661"}, {"RefNumber": "874520", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Object CL_RSR_MDX_OLAP_REQUEST locked", "RefUrl": "/notes/874520"}, {"RefNumber": "874189", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Transferring compound characteristics", "RefUrl": "/notes/874189"}, {"RefNumber": "873866", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/873866"}, {"RefNumber": "873849", "RefComponent": "BW-WHM-DST", "RefTitle": "P29: AWB: PSA tree: Slow access to table RSLDTDONE", "RefUrl": "/notes/873849"}, {"RefNumber": "873694", "RefComponent": "BW-WHM-DST", "RefTitle": "Consulting: Delta repeat and status in monitor/data target", "RefUrl": "/notes/873694"}, {"RefNumber": "873685", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Query authorized although an authorization object missing", "RefUrl": "/notes/873685"}, {"RefNumber": "873640", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: RSRV check does not display current table statistics", "RefUrl": "/notes/873640"}, {"RefNumber": "873560", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in SAPLRRI2 and form VREP_SHIFT0_INSERT-02-", "RefUrl": "/notes/873560"}, {"RefNumber": "873483", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "BW device recognition and ITS BW 3.0B/3.10/3.50 (SP 29/23/14", "RefUrl": "/notes/873483"}, {"RefNumber": "873401", "RefComponent": "BW-WHM-DST", "RefTitle": "Consulting: Data mart delta administration/duplicate data", "RefUrl": "/notes/873401"}, {"RefNumber": "873385", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Specifying hierarchy using URL parameter", "RefUrl": "/notes/873385"}, {"RefNumber": "873357", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Conversion between 0FISCPER and 0FISCPER3 with nodes", "RefUrl": "/notes/873357"}, {"RefNumber": "873077", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "BW 3.0B (SP 29) hierarchies: GENERATE_SUBPOOL_DIR_FULL", "RefUrl": "/notes/873077"}, {"RefNumber": "873072", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "0AD_HOC: Initializing broadcaster when you start w/ bookmark", "RefUrl": "/notes/873072"}, {"RefNumber": "872904", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query runtime with many nested formulas", "RefUrl": "/notes/872904"}, {"RefNumber": "872902", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "F4 for InfoSource with ODS in Transaction RSBBS", "RefUrl": "/notes/872902"}, {"RefNumber": "872827", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Update rules are not optimized", "RefUrl": "/notes/872827"}, {"RefNumber": "872799", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Report RSDG_CUBE_ACTIVATE - Execution in Background", "RefUrl": "/notes/872799"}, {"RefNumber": "872650", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P29: RSCRT: Real time: Maintaining table RSCRT_DTA_DS_LG", "RefUrl": "/notes/872650"}, {"RefNumber": "872488", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchy BW 3.0B (SP 29) File: Values for root nodes", "RefUrl": "/notes/872488"}, {"RefNumber": "872416", "RefComponent": "BW-BEX-ET-WEB-DIA", "RefTitle": "Correction on note 868961 for sorting type", "RefUrl": "/notes/872416"}, {"RefNumber": "872397", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: User exit for database hints", "RefUrl": "/notes/872397"}, {"RefNumber": "872277", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 17 NW'04 Stack 17 RIN", "RefUrl": "/notes/872277"}, {"RefNumber": "872275", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 16 NW'04 Stack 16 RIN", "RefUrl": "/notes/872275"}, {"RefNumber": "872119", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a specific case", "RefUrl": "/notes/872119"}, {"RefNumber": "871903", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster: Termination TH_RES_FREE w/ active trans system", "RefUrl": "/notes/871903"}, {"RefNumber": "871767", "RefComponent": "BW-WHM-MTD-XML", "RefTitle": "XML import InfoObjects: Time dependency of attributes", "RefUrl": "/notes/871767"}, {"RefNumber": "871440", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a specific case", "RefUrl": "/notes/871440"}, {"RefNumber": "871355", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "System dumps in the form RSZ_CON_FAC_FILL", "RefUrl": "/notes/871355"}, {"RefNumber": "871276", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in zero suppression with basis formulas", "RefUrl": "/notes/871276"}, {"RefNumber": "870682", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Viewing contents of cache elements (in the debugger)", "RefUrl": "/notes/870682"}, {"RefNumber": "870557", "RefComponent": "BW", "RefTitle": "Multiprovider F4 help does not add any aggregates", "RefUrl": "/notes/870557"}, {"RefNumber": "870505", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Dump GETWA_NOT_ASSIGNED on MultiCube/collect_all_checkfl_0", "RefUrl": "/notes/870505"}, {"RefNumber": "869841", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Hierarchy restriction with lists and single values", "RefUrl": "/notes/869841"}, {"RefNumber": "869556", "RefComponent": "BW-BEX-ET-WEB-RT", "RefTitle": "BEx Web: Reducing storage requirement of filter Dropdown Box", "RefUrl": "/notes/869556"}, {"RefNumber": "869553", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Deletion of a multi provider does not delete queries", "RefUrl": "/notes/869553"}, {"RefNumber": "869488", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "x299 Brain in SAPLRRK0; form SETXX_FUELLEN_15-01-", "RefUrl": "/notes/869488"}, {"RefNumber": "869393", "RefComponent": "BW-BEX-ET-WEB-GRAPH", "RefTitle": "Data table under charts without interaction/links", "RefUrl": "/notes/869393"}, {"RefNumber": "869178", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy: too much or too little data in query", "RefUrl": "/notes/869178"}, {"RefNumber": "869135", "RefComponent": "BW-BEX-ET", "RefTitle": "Decimal places and scaling for \"Calculate Single Values As\"", "RefUrl": "/notes/869135"}, {"RefNumber": "868935", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BW 3.0B (SP 28) ODS object: Exception COMMUNICATION_FAILURE", "RefUrl": "/notes/868935"}, {"RefNumber": "868729", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "MHTML generation: Problems with the character set", "RefUrl": "/notes/868729"}, {"RefNumber": "868391", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BW 3.0B (SP28) ODS Obj. \"Unique data records\" - activation", "RefUrl": "/notes/868391"}, {"RefNumber": "868166", "RefComponent": "BW-WHM", "RefTitle": "Checkman mit Prio 1&2 für Objekte von TRANN in J20", "RefUrl": "/notes/868166"}, {"RefNumber": "867922", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "BBS: Incorrect selection type in EXCLUDING", "RefUrl": "/notes/867922"}, {"RefNumber": "867877", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Constant selection and missing or too many rows/columns", "RefUrl": "/notes/867877"}, {"RefNumber": "867766", "RefComponent": "BW-BCT-TCT", "RefTitle": "F4 help for 0TCTQUERID raises dump UNCAUGHT_EXCEPTION", "RefUrl": "/notes/867766"}, {"RefNumber": "867616", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "BBS: Variable, compounded and changeable with navigation", "RefUrl": "/notes/867616"}, {"RefNumber": "867519", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A125 Brain Too many data control blocks created", "RefUrl": "/notes/867519"}, {"RefNumber": "867510", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Low performance of 'distribute by e-mail' functionality", "RefUrl": "/notes/867510"}, {"RefNumber": "867459", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "TA RSRT1: Fields are deleted if F4 is cancelled", "RefUrl": "/notes/867459"}, {"RefNumber": "867407", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "System error in program SAPLRSDRC and form -5-", "RefUrl": "/notes/867407"}, {"RefNumber": "867334", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable \"Replace from attribute\" and offset", "RefUrl": "/notes/867334"}, {"RefNumber": "867326", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in prog SAPLRRK0 and form SETXX_fuellen_15-01-", "RefUrl": "/notes/867326"}, {"RefNumber": "867000", "RefComponent": "BW-BEX-OT", "RefTitle": "Inconsistencies in temporary tables of TYPE 06", "RefUrl": "/notes/867000"}, {"RefNumber": "866996", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Error 'REBUILD_VALID_FROM_FACTTABLE-3-'", "RefUrl": "/notes/866996"}, {"RefNumber": "866977", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable, mandatory without initial value", "RefUrl": "/notes/866977"}, {"RefNumber": "866695", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BW 3.0B/3.10/3.50 (SP 28/22/14): Error in the Precalc Server", "RefUrl": "/notes/866695"}, {"RefNumber": "866505", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unwanted rounding of units T006-ANDEC", "RefUrl": "/notes/866505"}, {"RefNumber": "866504", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Batch confirmation for LOADING not possible", "RefUrl": "/notes/866504"}, {"RefNumber": "866436", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "F4 ignores higher-level characteristic in compound char", "RefUrl": "/notes/866436"}, {"RefNumber": "866136", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "\"High cardinality\" with aggregate dimensions", "RefUrl": "/notes/866136"}, {"RefNumber": "865667", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Deleting secondary indexes in unit dimension field", "RefUrl": "/notes/865667"}, {"RefNumber": "865512", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Exploding navigation attributes and hierarchy nodes", "RefUrl": "/notes/865512"}, {"RefNumber": "865475", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process chains executed synchronously", "RefUrl": "/notes/865475"}, {"RefNumber": "865453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data in some queries with multiprovider", "RefUrl": "/notes/865453"}, {"RefNumber": "865036", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Currency and quantity key figures are displayed incorrectly", "RefUrl": "/notes/865036"}, {"RefNumber": "864521", "RefComponent": "BW-SYS", "RefTitle": "RSPOR_SETUP: Error message \" ... System Alias w/o spaces\"", "RefUrl": "/notes/864521"}, {"RefNumber": "864410", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Negative values in formula variables", "RefUrl": "/notes/864410"}, {"RefNumber": "864407", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "CX_SY_REF_IS_INITIAL for MDX with ISEMPTY and CURRENTMEMBER", "RefUrl": "/notes/864407"}, {"RefNumber": "864405", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in MDX on query without key figures", "RefUrl": "/notes/864405"}, {"RefNumber": "863796", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Error in RSR_MDX_CREATE_CRM_SET", "RefUrl": "/notes/863796"}, {"RefNumber": "863784", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: OBJECTS_OBJREF_NOT_ASSIGNED in the CCMS collecto", "RefUrl": "/notes/863784"}, {"RefNumber": "863745", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Some columns remain empty or have incorrect values", "RefUrl": "/notes/863745"}, {"RefNumber": "863672", "RefComponent": "BW-BEX-MMR", "RefTitle": "bi/mmr/dictionary cannot be deployed", "RefUrl": "/notes/863672"}, {"RefNumber": "863448", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the constant selection", "RefUrl": "/notes/863448"}, {"RefNumber": "863446", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Priority with variables that can be changed in navigation", "RefUrl": "/notes/863446"}, {"RefNumber": "863278", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Explain query displays incorrect data (time-dep hierarchy)", "RefUrl": "/notes/863278"}, {"RefNumber": "863264", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Using YTD/QTD/MTD/WTD with date hierarchies", "RefUrl": "/notes/863264"}, {"RefNumber": "863054", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Missing packages not in the tree", "RefUrl": "/notes/863054"}, {"RefNumber": "862784", "RefComponent": "FIN-SEM-BPS", "RefTitle": "max. number of connections exeeded", "RefUrl": "/notes/862784"}, {"RefNumber": "862365", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Profile in Transaction RSSM maintainable/not maintainable", "RefUrl": "/notes/862365"}, {"RefNumber": "862284", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain A125 statement is too long", "RefUrl": "/notes/862284"}, {"RefNumber": "861977", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Constant can not be reset to initial for characteristic", "RefUrl": "/notes/861977"}, {"RefNumber": "861949", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299(BRAIN) in program SAPLRRK0; form MEGASORT_14-01-", "RefUrl": "/notes/861949"}, {"RefNumber": "861857", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "X100 DBMAN FORM CONVERT_SEL_TO_SELC; NO CORRESPONDING SFC", "RefUrl": "/notes/861857"}, {"RefNumber": "861760", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 816 \"Node not in hierarchy\" although it exists", "RefUrl": "/notes/861760"}, {"RefNumber": "861597", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a very special situation", "RefUrl": "/notes/861597"}, {"RefNumber": "860815", "RefComponent": "BW-BEX-ET-WEB-RT", "RefTitle": "BW 3.50 (SP 14): Error in the hierarchical context menu", "RefUrl": "/notes/860815"}, {"RefNumber": "860555", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Deleting settings: Scheduling remains", "RefUrl": "/notes/860555"}, {"RefNumber": "860488", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check for selections with Excluding", "RefUrl": "/notes/860488"}, {"RefNumber": "860083", "RefComponent": "BW-BEX-MMR", "RefTitle": "Java Dictionary: Changing the default values", "RefUrl": "/notes/860083"}, {"RefNumber": "860013", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query cannot use cache -missing query name in cache", "RefUrl": "/notes/860013"}, {"RefNumber": "859937", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BW 3.0B (SP 28) ODS object: Lock problem when loading data", "RefUrl": "/notes/859937"}, {"RefNumber": "859768", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "No NON EMPTY with { <member> }", "RefUrl": "/notes/859768"}, {"RefNumber": "859594", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data when you expand a hierarchy node", "RefUrl": "/notes/859594"}, {"RefNumber": "859499", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Prob with \"/\" in characteristic val w/ comp characteristics", "RefUrl": "/notes/859499"}, {"RefNumber": "859495", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Unauthorized nodes are displayed in input help", "RefUrl": "/notes/859495"}, {"RefNumber": "859456", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Increasing performance using the cache", "RefUrl": "/notes/859456"}, {"RefNumber": "859433", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Error in Cube activation with reference to Fact View", "RefUrl": "/notes/859433"}, {"RefNumber": "859430", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Dump MESSAGE_TYPE_X when exeuting RSAPO_CONDENSE_INFOCUBE", "RefUrl": "/notes/859430"}, {"RefNumber": "859414", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P28:P4:SDL: Init request with empty HIGH and LOW values", "RefUrl": "/notes/859414"}, {"RefNumber": "859351", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Using PARALLELPERIOD without time hierarchy", "RefUrl": "/notes/859351"}, {"RefNumber": "859147", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Problems when you use EP6 as a proxy for the BW server", "RefUrl": "/notes/859147"}, {"RefNumber": "859086", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Extended check of the table RSZELTXREF", "RefUrl": "/notes/859086"}, {"RefNumber": "858933", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "No data in master data provider without drilldown", "RefUrl": "/notes/858933"}, {"RefNumber": "858757", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Defining filter navigation for compound characteristics", "RefUrl": "/notes/858757"}, {"RefNumber": "858740", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Incorrect values for update to ODS object", "RefUrl": "/notes/858740"}, {"RefNumber": "858671", "RefComponent": "BW-WHM-DST", "RefTitle": "P28:P4:RSM_DATASTATE_CHECK-5- dump in ODS-update", "RefUrl": "/notes/858671"}, {"RefNumber": "858508", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Error with DESCENDANTS in GENERATE", "RefUrl": "/notes/858508"}, {"RefNumber": "858458", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Program CL_RSR_REQUEST, form TEXT_ELEMENTS_GET:VARIABLE", "RefUrl": "/notes/858458"}, {"RefNumber": "858455", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Expanding nodes: Exception CX_SY_ARITHMETIC_OVERFLOW", "RefUrl": "/notes/858455"}, {"RefNumber": "858442", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Init for time-dependent DataSources dumps", "RefUrl": "/notes/858442"}, {"RefNumber": "858302", "RefComponent": "BW-BEX-ET-WEB-RT", "RefTitle": "BW 3.50 (Support Package 14) error in the condition item", "RefUrl": "/notes/858302"}, {"RefNumber": "858217", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data with dynamic filter", "RefUrl": "/notes/858217"}, {"RefNumber": "857918", "RefComponent": "BW-WHM-DST", "RefTitle": "P28: P4: MON: High number of enqueue rejects in SM12", "RefUrl": "/notes/857918"}, {"RefNumber": "857895", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "'Suppress results' for the display hierarchy", "RefUrl": "/notes/857895"}, {"RefNumber": "857621", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query with variable 0S_RQMRC displays incorrect data", "RefUrl": "/notes/857621"}, {"RefNumber": "857573", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Performance RSNDI_SHIE interface BW30B/310/350 SP28/22/14", "RefUrl": "/notes/857573"}, {"RefNumber": "857466", "RefComponent": "BW-WHM-MTD", "RefTitle": "Selecting objects in the translation system", "RefUrl": "/notes/857466"}, {"RefNumber": "857446", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSRV for multibyte code page errors in master data texts", "RefUrl": "/notes/857446"}, {"RefNumber": "857436", "RefComponent": "BW-BEX-MMR", "RefTitle": "BI UDI/SDK meta model not deployed completely", "RefUrl": "/notes/857436"}, {"RefNumber": "857423", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "ORA 54 in system log with parallel master data store", "RefUrl": "/notes/857423"}, {"RefNumber": "857410", "RefComponent": "BW-WHM-DST", "RefTitle": "P28:P4: various smaller corrections without customer message", "RefUrl": "/notes/857410"}, {"RefNumber": "857409", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P28: P4: SDL: IPG: Bkgd scheduling after Content activation", "RefUrl": "/notes/857409"}, {"RefNumber": "857401", "RefComponent": "BW-WHM-DST", "RefTitle": "P28:P4: MON: High number of enqueue rejects in SM12", "RefUrl": "/notes/857401"}, {"RefNumber": "857356", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query performance, cache entry is not written", "RefUrl": "/notes/857356"}, {"RefNumber": "857300", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Generation of a NAMED SET from ..._GET_MEMBERS", "RefUrl": "/notes/857300"}, {"RefNumber": "857200", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Incorr calc of non-cum values with excpn aggregation AV1 AV2", "RefUrl": "/notes/857200"}, {"RefNumber": "857116", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query without key figures in MultiProvider", "RefUrl": "/notes/857116"}, {"RefNumber": "857066", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Error when loading flatfile hierarchies in catalan language", "RefUrl": "/notes/857066"}, {"RefNumber": "857019", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Optimization level for BW queries cannot be changed", "RefUrl": "/notes/857019"}, {"RefNumber": "856793", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRK0; Model DUPLCUM-02-", "RefUrl": "/notes/856793"}, {"RefNumber": "856783", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "RSDBC - Incorrect selection for time-dependent InfoObjects", "RefUrl": "/notes/856783"}, {"RefNumber": "856562", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in Saplrrk0; Form SETXX_FUELLEN_15-02-", "RefUrl": "/notes/856562"}, {"RefNumber": "856546", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Master data deletion: RSDMD_DEL_BACKGROUND short dump", "RefUrl": "/notes/856546"}, {"RefNumber": "856528", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZDELETE: No components selected for deletion", "RefUrl": "/notes/856528"}, {"RefNumber": "856495", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No Data for dynamic filter with EXCLUDE", "RefUrl": "/notes/856495"}, {"RefNumber": "856485", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculating with attributes with display hierarchy", "RefUrl": "/notes/856485"}, {"RefNumber": "856395", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "New context menu settings missing in bookmark", "RefUrl": "/notes/856395"}, {"RefNumber": "856328", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Memory problem if there are many error records", "RefUrl": "/notes/856328"}, {"RefNumber": "856292", "RefComponent": "BW-WHM-DST", "RefTitle": "P28: Automatic functions: No lock obtained for ERSICENQ", "RefUrl": "/notes/856292"}, {"RefNumber": "856226", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Logging mechanism to analyse Cache related problems", "RefUrl": "/notes/856226"}, {"RefNumber": "856221", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Deletion of entire cache with BLOB and Cluster table", "RefUrl": "/notes/856221"}, {"RefNumber": "856214", "RefComponent": "BW-BCT-TCT", "RefTitle": "Correction: Dump DATA_LENGTH_0 in RSSM_EXTRAKTOR_SELECTIONS", "RefUrl": "/notes/856214"}, {"RefNumber": "856144", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization check for queries without key figures", "RefUrl": "/notes/856144"}, {"RefNumber": "856097", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Check and repair program for Note 849857", "RefUrl": "/notes/856097"}, {"RefNumber": "856094", "RefComponent": "BW-WHM", "RefTitle": "PERFORM_TOO_MANY_PARAMETERS in dataload", "RefUrl": "/notes/856094"}, {"RefNumber": "855907", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Background scheduling deleted after transport", "RefUrl": "/notes/855907"}, {"RefNumber": "855809", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Wrong aggregation direction for property 'Cumulated'", "RefUrl": "/notes/855809"}, {"RefNumber": "855600", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Aggregate indexes P and 900 missing after reconstructing cha", "RefUrl": "/notes/855600"}, {"RefNumber": "854514", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRK0; FORM RGC_PFLEGEN_08-01-", "RefUrl": "/notes/854514"}, {"RefNumber": "854139", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Suppressing results does not work correctly", "RefUrl": "/notes/854139"}, {"RefNumber": "854092", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "During initial aggregate fill a COMMIT is eventually needed", "RefUrl": "/notes/854092"}, {"RefNumber": "853191", "RefComponent": "BW-BEX-OT", "RefTitle": "Compounding w/ initial value and characteristic value check", "RefUrl": "/notes/853191"}, {"RefNumber": "853084", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Transferring temporary /BI0/06 tables to another tablespace", "RefUrl": "/notes/853084"}, {"RefNumber": "852867", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Deadlock when aggregate statistics are written", "RefUrl": "/notes/852867"}, {"RefNumber": "852840", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in constant selection with non-posted values", "RefUrl": "/notes/852840"}, {"RefNumber": "852550", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Several simultaneous routines in an InfoPackage", "RefUrl": "/notes/852550"}, {"RefNumber": "852207", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "DATA_UC_STRUCT_NOT_CHAR_LIKE dump in RSD_CHA_GET_VALUES", "RefUrl": "/notes/852207"}, {"RefNumber": "851754", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "X299 Brain in SAPLRRKH, form SELDER_INTERSECT_BUF-01", "RefUrl": "/notes/851754"}, {"RefNumber": "851481", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dynamic filter is ignored for intervals or selection option", "RefUrl": "/notes/851481"}, {"RefNumber": "851254", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing values for filter value selections after expansion", "RefUrl": "/notes/851254"}, {"RefNumber": "850840", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Constant selection and missing/too many output lines", "RefUrl": "/notes/850840"}, {"RefNumber": "850147", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Support for blank MDX commands", "RefUrl": "/notes/850147"}, {"RefNumber": "849969", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRK0; form CHECK_NAV_INIT_NEW-03-", "RefUrl": "/notes/849969"}, {"RefNumber": "849503", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "XML/A: Incorrect column sequence in the flattening", "RefUrl": "/notes/849503"}, {"RefNumber": "849286", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Average stock may be calculated incorrectly", "RefUrl": "/notes/849286"}, {"RefNumber": "849054", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error while deleting characteristics combination in APO SCM", "RefUrl": "/notes/849054"}, {"RefNumber": "848829", "RefComponent": "BW-BEX-OT", "RefTitle": "Error with ODS query in RSDRO_READ_ODS_DATA", "RefUrl": "/notes/848829"}, {"RefNumber": "848340", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Some columns remain empty or are filled with incorrect value", "RefUrl": "/notes/848340"}, {"RefNumber": "847030", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P28:SDL:InfoPackage crtn/dltn in content-creating BW", "RefUrl": "/notes/847030"}, {"RefNumber": "839096", "RefComponent": "BW", "RefTitle": "BI UDI 350 SP14: Release and information-note", "RefUrl": "/notes/839096"}, {"RefNumber": "838566", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "System error: in SAPLRSSH and form FM RSSH_GET_SIGNCHANGE", "RefUrl": "/notes/838566"}, {"RefNumber": "836440", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 15 NW'04 Stack 15 RIN", "RefUrl": "/notes/836440"}, {"RefNumber": "834829", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Compression of BW InfoCubes without update of markers", "RefUrl": "/notes/834829"}, {"RefNumber": "783254", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 13 NW'04 Stack 13 RIN", "RefUrl": "/notes/783254"}, {"RefNumber": "763340", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 12 NetWeaver'04 Stack 12 RIN", "RefUrl": "/notes/763340"}, {"RefNumber": "763338", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 11 NW'04 stack 11 RIN", "RefUrl": "/notes/763338"}, {"RefNumber": "763336", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 10 NW '04 Stack 10 RIN", "RefUrl": "/notes/763336"}, {"RefNumber": "763335", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 09 NW'04 Stack 09 RIN", "RefUrl": "/notes/763335"}, {"RefNumber": "729243", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "DB Connect - Syntax error in the LRSDBCO01 include", "RefUrl": "/notes/729243"}, {"RefNumber": "716121", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Generating URLs on external BW systems and portals", "RefUrl": "/notes/716121"}, {"RefNumber": "695423", "RefComponent": "BW", "RefTitle": "BW 3.5: Support Package information", "RefUrl": "/notes/695423"}, {"RefNumber": "693495", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 05 NW'04 Stack 05 RIN", "RefUrl": "/notes/693495"}, {"RefNumber": "693494", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP04 NW'04 Stack 04 RIN", "RefUrl": "/notes/693494"}, {"RefNumber": "693363", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP03 NW'04 Stack 03 RIN", "RefUrl": "/notes/693363"}, {"RefNumber": "693362", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP01 NW'04 Stack 01 RIN", "RefUrl": "/notes/693362"}, {"RefNumber": "692636", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP02 NW'04 Stack 02 RIN", "RefUrl": "/notes/692636"}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "695423", "RefComponent": "BW", "RefTitle": "BW 3.5: Support Package information", "RefUrl": "/notes/695423 "}, {"RefNumber": "873401", "RefComponent": "BW-WHM-DST", "RefTitle": "Consulting: Data mart delta administration/duplicate data", "RefUrl": "/notes/873401 "}, {"RefNumber": "873694", "RefComponent": "BW-WHM-DST", "RefTitle": "Consulting: Delta repeat and status in monitor/data target", "RefUrl": "/notes/873694 "}, {"RefNumber": "872397", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: User exit for database hints", "RefUrl": "/notes/872397 "}, {"RefNumber": "873640", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: RSRV check does not display current table statistics", "RefUrl": "/notes/873640 "}, {"RefNumber": "869135", "RefComponent": "BW-BEX-ET", "RefTitle": "Decimal places and scaling for \"Calculate Single Values As\"", "RefUrl": "/notes/869135 "}, {"RefNumber": "853084", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Transferring temporary /BI0/06 tables to another tablespace", "RefUrl": "/notes/853084 "}, {"RefNumber": "854092", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "During initial aggregate fill a COMMIT is eventually needed", "RefUrl": "/notes/854092 "}, {"RefNumber": "861977", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Constant can not be reset to initial for characteristic", "RefUrl": "/notes/861977 "}, {"RefNumber": "859456", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Increasing performance using the cache", "RefUrl": "/notes/859456 "}, {"RefNumber": "866505", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unwanted rounding of units T006-ANDEC", "RefUrl": "/notes/866505 "}, {"RefNumber": "856783", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "RSDBC - Incorrect selection for time-dependent InfoObjects", "RefUrl": "/notes/856783 "}, {"RefNumber": "872277", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 17 NW'04 Stack 17 RIN", "RefUrl": "/notes/872277 "}, {"RefNumber": "872275", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 16 NW'04 Stack 16 RIN", "RefUrl": "/notes/872275 "}, {"RefNumber": "857895", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "'Suppress results' for the display hierarchy", "RefUrl": "/notes/857895 "}, {"RefNumber": "858458", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Program CL_RSR_REQUEST, form TEXT_ELEMENTS_GET:VARIABLE", "RefUrl": "/notes/858458 "}, {"RefNumber": "857423", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "ORA 54 in system log with parallel master data store", "RefUrl": "/notes/857423 "}, {"RefNumber": "870682", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Viewing contents of cache elements (in the debugger)", "RefUrl": "/notes/870682 "}, {"RefNumber": "872827", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Update rules are not optimized", "RefUrl": "/notes/872827 "}, {"RefNumber": "856097", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Check and repair program for Note 849857", "RefUrl": "/notes/856097 "}, {"RefNumber": "856226", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Logging mechanism to analyse Cache related problems", "RefUrl": "/notes/856226 "}, {"RefNumber": "922544", "RefComponent": "BW-SYS-DB-INF", "RefTitle": "BW Informix: Data loss after you delete master data", "RefUrl": "/notes/922544 "}, {"RefNumber": "836440", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 15 NW'04 Stack 15 RIN", "RefUrl": "/notes/836440 "}, {"RefNumber": "783254", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 13 NW'04 Stack 13 RIN", "RefUrl": "/notes/783254 "}, {"RefNumber": "763340", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 12 NetWeaver'04 Stack 12 RIN", "RefUrl": "/notes/763340 "}, {"RefNumber": "873685", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Query authorized although an authorization object missing", "RefUrl": "/notes/873685 "}, {"RefNumber": "763338", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 11 NW'04 stack 11 RIN", "RefUrl": "/notes/763338 "}, {"RefNumber": "763336", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 10 NW '04 Stack 10 RIN", "RefUrl": "/notes/763336 "}, {"RefNumber": "851481", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dynamic filter is ignored for intervals or selection option", "RefUrl": "/notes/851481 "}, {"RefNumber": "856546", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Master data deletion: RSDMD_DEL_BACKGROUND short dump", "RefUrl": "/notes/856546 "}, {"RefNumber": "869556", "RefComponent": "BW-BEX-ET-WEB-RT", "RefTitle": "BEx Web: Reducing storage requirement of filter Dropdown Box", "RefUrl": "/notes/869556 "}, {"RefNumber": "856221", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Deletion of entire cache with BLOB and Cluster table", "RefUrl": "/notes/856221 "}, {"RefNumber": "850840", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Constant selection and missing/too many output lines", "RefUrl": "/notes/850840 "}, {"RefNumber": "868391", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BW 3.0B (SP28) ODS Obj. \"Unique data records\" - activation", "RefUrl": "/notes/868391 "}, {"RefNumber": "861760", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 816 \"Node not in hierarchy\" although it exists", "RefUrl": "/notes/861760 "}, {"RefNumber": "862365", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Profile in Transaction RSSM maintainable/not maintainable", "RefUrl": "/notes/862365 "}, {"RefNumber": "857621", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query with variable 0S_RQMRC displays incorrect data", "RefUrl": "/notes/857621 "}, {"RefNumber": "877005", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299(BRAIN) in program SAPLRRK0; FORM MEGASORT_14-01-", "RefUrl": "/notes/877005 "}, {"RefNumber": "863796", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Error in RSR_MDX_CREATE_CRM_SET", "RefUrl": "/notes/863796 "}, {"RefNumber": "869393", "RefComponent": "BW-BEX-ET-WEB-GRAPH", "RefTitle": "Data table under charts without interaction/links", "RefUrl": "/notes/869393 "}, {"RefNumber": "874714", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Exception aggregation AVG in BAPI_MDPROVIDER_GET_MEASURES", "RefUrl": "/notes/874714 "}, {"RefNumber": "874662", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Substituting text variables with multiple-value selections", "RefUrl": "/notes/874662 "}, {"RefNumber": "874661", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search string and conversion exit with characteristic value", "RefUrl": "/notes/874661 "}, {"RefNumber": "874520", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Object CL_RSR_MDX_OLAP_REQUEST locked", "RefUrl": "/notes/874520 "}, {"RefNumber": "874189", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Transferring compound characteristics", "RefUrl": "/notes/874189 "}, {"RefNumber": "873849", "RefComponent": "BW-WHM-DST", "RefTitle": "P29: AWB: PSA tree: Slow access to table RSLDTDONE", "RefUrl": "/notes/873849 "}, {"RefNumber": "873560", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in SAPLRRI2 and form VREP_SHIFT0_INSERT-02-", "RefUrl": "/notes/873560 "}, {"RefNumber": "873483", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "BW device recognition and ITS BW 3.0B/3.10/3.50 (SP 29/23/14", "RefUrl": "/notes/873483 "}, {"RefNumber": "873357", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Conversion between 0FISCPER and 0FISCPER3 with nodes", "RefUrl": "/notes/873357 "}, {"RefNumber": "873077", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "BW 3.0B (SP 29) hierarchies: GENERATE_SUBPOOL_DIR_FULL", "RefUrl": "/notes/873077 "}, {"RefNumber": "872904", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query runtime with many nested formulas", "RefUrl": "/notes/872904 "}, {"RefNumber": "872902", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "F4 for InfoSource with ODS in Transaction RSBBS", "RefUrl": "/notes/872902 "}, {"RefNumber": "872799", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Report RSDG_CUBE_ACTIVATE - Execution in Background", "RefUrl": "/notes/872799 "}, {"RefNumber": "872650", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P29: RSCRT: Real time: Maintaining table RSCRT_DTA_DS_LG", "RefUrl": "/notes/872650 "}, {"RefNumber": "872488", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchy BW 3.0B (SP 29) File: Values for root nodes", "RefUrl": "/notes/872488 "}, {"RefNumber": "872416", "RefComponent": "BW-BEX-ET-WEB-DIA", "RefTitle": "Correction on note 868961 for sorting type", "RefUrl": "/notes/872416 "}, {"RefNumber": "871767", "RefComponent": "BW-WHM-MTD-XML", "RefTitle": "XML import InfoObjects: Time dependency of attributes", "RefUrl": "/notes/871767 "}, {"RefNumber": "868935", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BW 3.0B (SP 28) ODS object: Exception COMMUNICATION_FAILURE", "RefUrl": "/notes/868935 "}, {"RefNumber": "869178", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy: too much or too little data in query", "RefUrl": "/notes/869178 "}, {"RefNumber": "862784", "RefComponent": "FIN-SEM-BPS", "RefTitle": "max. number of connections exeeded", "RefUrl": "/notes/862784 "}, {"RefNumber": "852550", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Several simultaneous routines in an InfoPackage", "RefUrl": "/notes/852550 "}, {"RefNumber": "839096", "RefComponent": "BW", "RefTitle": "BI UDI 350 SP14: Release and information-note", "RefUrl": "/notes/839096 "}, {"RefNumber": "867519", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A125 Brain Too many data control blocks created", "RefUrl": "/notes/867519 "}, {"RefNumber": "866695", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BW 3.0B/3.10/3.50 (SP 28/22/14): Error in the Precalc Server", "RefUrl": "/notes/866695 "}, {"RefNumber": "864521", "RefComponent": "BW-SYS", "RefTitle": "RSPOR_SETUP: Error message \" ... System Alias w/o spaces\"", "RefUrl": "/notes/864521 "}, {"RefNumber": "850147", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Support for blank MDX commands", "RefUrl": "/notes/850147 "}, {"RefNumber": "858217", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data with dynamic filter", "RefUrl": "/notes/858217 "}, {"RefNumber": "860488", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check for selections with Excluding", "RefUrl": "/notes/860488 "}, {"RefNumber": "864410", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Negative values in formula variables", "RefUrl": "/notes/864410 "}, {"RefNumber": "870505", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Dump GETWA_NOT_ASSIGNED on MultiCube/collect_all_checkfl_0", "RefUrl": "/notes/870505 "}, {"RefNumber": "860013", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query cannot use cache -missing query name in cache", "RefUrl": "/notes/860013 "}, {"RefNumber": "857356", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query performance, cache entry is not written", "RefUrl": "/notes/857356 "}, {"RefNumber": "867334", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable \"Replace from attribute\" and offset", "RefUrl": "/notes/867334 "}, {"RefNumber": "856562", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in Saplrrk0; Form SETXX_FUELLEN_15-02-", "RefUrl": "/notes/856562 "}, {"RefNumber": "867326", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in prog SAPLRRK0 and form SETXX_fuellen_15-01-", "RefUrl": "/notes/867326 "}, {"RefNumber": "869488", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "x299 Brain in SAPLRRK0; form SETXX_FUELLEN_15-01-", "RefUrl": "/notes/869488 "}, {"RefNumber": "865667", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Deleting secondary indexes in unit dimension field", "RefUrl": "/notes/865667 "}, {"RefNumber": "871440", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a specific case", "RefUrl": "/notes/871440 "}, {"RefNumber": "871355", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "System dumps in the form RSZ_CON_FAC_FILL", "RefUrl": "/notes/871355 "}, {"RefNumber": "871276", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in zero suppression with basis formulas", "RefUrl": "/notes/871276 "}, {"RefNumber": "870557", "RefComponent": "BW", "RefTitle": "Multiprovider F4 help does not add any aggregates", "RefUrl": "/notes/870557 "}, {"RefNumber": "854514", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRK0; FORM RGC_PFLEGEN_08-01-", "RefUrl": "/notes/854514 "}, {"RefNumber": "854139", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Suppressing results does not work correctly", "RefUrl": "/notes/854139 "}, {"RefNumber": "853191", "RefComponent": "BW-BEX-OT", "RefTitle": "Compounding w/ initial value and characteristic value check", "RefUrl": "/notes/853191 "}, {"RefNumber": "852867", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Deadlock when aggregate statistics are written", "RefUrl": "/notes/852867 "}, {"RefNumber": "852840", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in constant selection with non-posted values", "RefUrl": "/notes/852840 "}, {"RefNumber": "852207", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "DATA_UC_STRUCT_NOT_CHAR_LIKE dump in RSD_CHA_GET_VALUES", "RefUrl": "/notes/852207 "}, {"RefNumber": "851754", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "X299 Brain in SAPLRRKH, form SELDER_INTERSECT_BUF-01", "RefUrl": "/notes/851754 "}, {"RefNumber": "851254", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing values for filter value selections after expansion", "RefUrl": "/notes/851254 "}, {"RefNumber": "849969", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRK0; form CHECK_NAV_INIT_NEW-03-", "RefUrl": "/notes/849969 "}, {"RefNumber": "849503", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "XML/A: Incorrect column sequence in the flattening", "RefUrl": "/notes/849503 "}, {"RefNumber": "849286", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Average stock may be calculated incorrectly", "RefUrl": "/notes/849286 "}, {"RefNumber": "849054", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error while deleting characteristics combination in APO SCM", "RefUrl": "/notes/849054 "}, {"RefNumber": "848829", "RefComponent": "BW-BEX-OT", "RefTitle": "Error with ODS query in RSDRO_READ_ODS_DATA", "RefUrl": "/notes/848829 "}, {"RefNumber": "848340", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Some columns remain empty or are filled with incorrect value", "RefUrl": "/notes/848340 "}, {"RefNumber": "847030", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P28:SDL:InfoPackage crtn/dltn in content-creating BW", "RefUrl": "/notes/847030 "}, {"RefNumber": "834829", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Compression of BW InfoCubes without update of markers", "RefUrl": "/notes/834829 "}, {"RefNumber": "716121", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Generating URLs on external BW systems and portals", "RefUrl": "/notes/716121 "}, {"RefNumber": "869841", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Hierarchy restriction with lists and single values", "RefUrl": "/notes/869841 "}, {"RefNumber": "869553", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Deletion of a multi provider does not delete queries", "RefUrl": "/notes/869553 "}, {"RefNumber": "868166", "RefComponent": "BW-WHM", "RefTitle": "Checkman mit Prio 1&2 für Objekte von TRANN in J20", "RefUrl": "/notes/868166 "}, {"RefNumber": "867922", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "BBS: Incorrect selection type in EXCLUDING", "RefUrl": "/notes/867922 "}, {"RefNumber": "867877", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Constant selection and missing or too many rows/columns", "RefUrl": "/notes/867877 "}, {"RefNumber": "867766", "RefComponent": "BW-BCT-TCT", "RefTitle": "F4 help for 0TCTQUERID raises dump UNCAUGHT_EXCEPTION", "RefUrl": "/notes/867766 "}, {"RefNumber": "867616", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "BBS: Variable, compounded and changeable with navigation", "RefUrl": "/notes/867616 "}, {"RefNumber": "867407", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "System error in program SAPLRSDRC and form -5-", "RefUrl": "/notes/867407 "}, {"RefNumber": "867000", "RefComponent": "BW-BEX-OT", "RefTitle": "Inconsistencies in temporary tables of TYPE 06", "RefUrl": "/notes/867000 "}, {"RefNumber": "866977", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable, mandatory without initial value", "RefUrl": "/notes/866977 "}, {"RefNumber": "866504", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Batch confirmation for LOADING not possible", "RefUrl": "/notes/866504 "}, {"RefNumber": "866436", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "F4 ignores higher-level characteristic in compound char", "RefUrl": "/notes/866436 "}, {"RefNumber": "866136", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "\"High cardinality\" with aggregate dimensions", "RefUrl": "/notes/866136 "}, {"RefNumber": "865512", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Exploding navigation attributes and hierarchy nodes", "RefUrl": "/notes/865512 "}, {"RefNumber": "865475", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process chains executed synchronously", "RefUrl": "/notes/865475 "}, {"RefNumber": "865036", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Currency and quantity key figures are displayed incorrectly", "RefUrl": "/notes/865036 "}, {"RefNumber": "864407", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "CX_SY_REF_IS_INITIAL for MDX with ISEMPTY and CURRENTMEMBER", "RefUrl": "/notes/864407 "}, {"RefNumber": "864405", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in MDX on query without key figures", "RefUrl": "/notes/864405 "}, {"RefNumber": "863784", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: OBJECTS_OBJREF_NOT_ASSIGNED in the CCMS collecto", "RefUrl": "/notes/863784 "}, {"RefNumber": "863745", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Some columns remain empty or have incorrect values", "RefUrl": "/notes/863745 "}, {"RefNumber": "863448", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the constant selection", "RefUrl": "/notes/863448 "}, {"RefNumber": "863446", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Priority with variables that can be changed in navigation", "RefUrl": "/notes/863446 "}, {"RefNumber": "863278", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Explain query displays incorrect data (time-dep hierarchy)", "RefUrl": "/notes/863278 "}, {"RefNumber": "863264", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Using YTD/QTD/MTD/WTD with date hierarchies", "RefUrl": "/notes/863264 "}, {"RefNumber": "863054", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Missing packages not in the tree", "RefUrl": "/notes/863054 "}, {"RefNumber": "862284", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain A125 statement is too long", "RefUrl": "/notes/862284 "}, {"RefNumber": "861949", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299(BRAIN) in program SAPLRRK0; form MEGASORT_14-01-", "RefUrl": "/notes/861949 "}, {"RefNumber": "861857", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "X100 DBMAN FORM CONVERT_SEL_TO_SELC; NO CORRESPONDING SFC", "RefUrl": "/notes/861857 "}, {"RefNumber": "861597", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a very special situation", "RefUrl": "/notes/861597 "}, {"RefNumber": "859937", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BW 3.0B (SP 28) ODS object: Lock problem when loading data", "RefUrl": "/notes/859937 "}, {"RefNumber": "859768", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "No NON EMPTY with { <member> }", "RefUrl": "/notes/859768 "}, {"RefNumber": "859594", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data when you expand a hierarchy node", "RefUrl": "/notes/859594 "}, {"RefNumber": "859499", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Prob with \"/\" in characteristic val w/ comp characteristics", "RefUrl": "/notes/859499 "}, {"RefNumber": "859495", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Unauthorized nodes are displayed in input help", "RefUrl": "/notes/859495 "}, {"RefNumber": "859433", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Error in Cube activation with reference to Fact View", "RefUrl": "/notes/859433 "}, {"RefNumber": "859430", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Dump MESSAGE_TYPE_X when exeuting RSAPO_CONDENSE_INFOCUBE", "RefUrl": "/notes/859430 "}, {"RefNumber": "859414", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P28:P4:SDL: Init request with empty HIGH and LOW values", "RefUrl": "/notes/859414 "}, {"RefNumber": "859351", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Using PARALLELPERIOD without time hierarchy", "RefUrl": "/notes/859351 "}, {"RefNumber": "858933", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "No data in master data provider without drilldown", "RefUrl": "/notes/858933 "}, {"RefNumber": "858671", "RefComponent": "BW-WHM-DST", "RefTitle": "P28:P4:RSM_DATASTATE_CHECK-5- dump in ODS-update", "RefUrl": "/notes/858671 "}, {"RefNumber": "858508", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Error with DESCENDANTS in GENERATE", "RefUrl": "/notes/858508 "}, {"RefNumber": "858442", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Init for time-dependent DataSources dumps", "RefUrl": "/notes/858442 "}, {"RefNumber": "857918", "RefComponent": "BW-WHM-DST", "RefTitle": "P28: P4: MON: High number of enqueue rejects in SM12", "RefUrl": "/notes/857918 "}, {"RefNumber": "857573", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Performance RSNDI_SHIE interface BW30B/310/350 SP28/22/14", "RefUrl": "/notes/857573 "}, {"RefNumber": "857466", "RefComponent": "BW-WHM-MTD", "RefTitle": "Selecting objects in the translation system", "RefUrl": "/notes/857466 "}, {"RefNumber": "857446", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSRV for multibyte code page errors in master data texts", "RefUrl": "/notes/857446 "}, {"RefNumber": "857410", "RefComponent": "BW-WHM-DST", "RefTitle": "P28:P4: various smaller corrections without customer message", "RefUrl": "/notes/857410 "}, {"RefNumber": "857409", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P28: P4: SDL: IPG: Bkgd scheduling after Content activation", "RefUrl": "/notes/857409 "}, {"RefNumber": "857401", "RefComponent": "BW-WHM-DST", "RefTitle": "P28:P4: MON: High number of enqueue rejects in SM12", "RefUrl": "/notes/857401 "}, {"RefNumber": "857300", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Generation of a NAMED SET from ..._GET_MEMBERS", "RefUrl": "/notes/857300 "}, {"RefNumber": "857200", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Incorr calc of non-cum values with excpn aggregation AV1 AV2", "RefUrl": "/notes/857200 "}, {"RefNumber": "857116", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query without key figures in MultiProvider", "RefUrl": "/notes/857116 "}, {"RefNumber": "857066", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Error when loading flatfile hierarchies in catalan language", "RefUrl": "/notes/857066 "}, {"RefNumber": "857019", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Optimization level for BW queries cannot be changed", "RefUrl": "/notes/857019 "}, {"RefNumber": "856793", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRK0; Model DUPLCUM-02-", "RefUrl": "/notes/856793 "}, {"RefNumber": "856528", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZDELETE: No components selected for deletion", "RefUrl": "/notes/856528 "}, {"RefNumber": "856485", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculating with attributes with display hierarchy", "RefUrl": "/notes/856485 "}, {"RefNumber": "856395", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "New context menu settings missing in bookmark", "RefUrl": "/notes/856395 "}, {"RefNumber": "856328", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Memory problem if there are many error records", "RefUrl": "/notes/856328 "}, {"RefNumber": "856292", "RefComponent": "BW-WHM-DST", "RefTitle": "P28: Automatic functions: No lock obtained for ERSICENQ", "RefUrl": "/notes/856292 "}, {"RefNumber": "856214", "RefComponent": "BW-BCT-TCT", "RefTitle": "Correction: Dump DATA_LENGTH_0 in RSSM_EXTRAKTOR_SELECTIONS", "RefUrl": "/notes/856214 "}, {"RefNumber": "856144", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization check for queries without key figures", "RefUrl": "/notes/856144 "}, {"RefNumber": "855907", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Background scheduling deleted after transport", "RefUrl": "/notes/855907 "}, {"RefNumber": "855809", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Wrong aggregation direction for property 'Cumulated'", "RefUrl": "/notes/855809 "}, {"RefNumber": "855600", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Aggregate indexes P and 900 missing after reconstructing cha", "RefUrl": "/notes/855600 "}, {"RefNumber": "873385", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Specifying hierarchy using URL parameter", "RefUrl": "/notes/873385 "}, {"RefNumber": "873072", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "0AD_HOC: Initializing broadcaster when you start w/ bookmark", "RefUrl": "/notes/873072 "}, {"RefNumber": "871903", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster: Termination TH_RES_FREE w/ active trans system", "RefUrl": "/notes/871903 "}, {"RefNumber": "872119", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a specific case", "RefUrl": "/notes/872119 "}, {"RefNumber": "859086", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Extended check of the table RSZELTXREF", "RefUrl": "/notes/859086 "}, {"RefNumber": "867510", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Low performance of 'distribute by e-mail' functionality", "RefUrl": "/notes/867510 "}, {"RefNumber": "729243", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "DB Connect - Syntax error in the LRSDBCO01 include", "RefUrl": "/notes/729243 "}, {"RefNumber": "838566", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "System error: in SAPLRSSH and form FM RSSH_GET_SIGNCHANGE", "RefUrl": "/notes/838566 "}, {"RefNumber": "868729", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "MHTML generation: Problems with the character set", "RefUrl": "/notes/868729 "}, {"RefNumber": "867459", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "TA RSRT1: Fields are deleted if F4 is cancelled", "RefUrl": "/notes/867459 "}, {"RefNumber": "866996", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Error 'REBUILD_VALID_FROM_FACTTABLE-3-'", "RefUrl": "/notes/866996 "}, {"RefNumber": "865453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data in some queries with multiprovider", "RefUrl": "/notes/865453 "}, {"RefNumber": "857436", "RefComponent": "BW-BEX-MMR", "RefTitle": "BI UDI/SDK meta model not deployed completely", "RefUrl": "/notes/857436 "}, {"RefNumber": "863672", "RefComponent": "BW-BEX-MMR", "RefTitle": "bi/mmr/dictionary cannot be deployed", "RefUrl": "/notes/863672 "}, {"RefNumber": "860083", "RefComponent": "BW-BEX-MMR", "RefTitle": "Java Dictionary: Changing the default values", "RefUrl": "/notes/860083 "}, {"RefNumber": "860815", "RefComponent": "BW-BEX-ET-WEB-RT", "RefTitle": "BW 3.50 (SP 14): Error in the hierarchical context menu", "RefUrl": "/notes/860815 "}, {"RefNumber": "860555", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Deleting settings: Scheduling remains", "RefUrl": "/notes/860555 "}, {"RefNumber": "856495", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No Data for dynamic filter with EXCLUDE", "RefUrl": "/notes/856495 "}, {"RefNumber": "763335", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 09 NW'04 Stack 09 RIN", "RefUrl": "/notes/763335 "}, {"RefNumber": "693495", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 05 NW'04 Stack 05 RIN", "RefUrl": "/notes/693495 "}, {"RefNumber": "859147", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Problems when you use EP6 as a proxy for the BW server", "RefUrl": "/notes/859147 "}, {"RefNumber": "858757", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Defining filter navigation for compound characteristics", "RefUrl": "/notes/858757 "}, {"RefNumber": "858740", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Incorrect values for update to ODS object", "RefUrl": "/notes/858740 "}, {"RefNumber": "858455", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Expanding nodes: Exception CX_SY_ARITHMETIC_OVERFLOW", "RefUrl": "/notes/858455 "}, {"RefNumber": "858302", "RefComponent": "BW-BEX-ET-WEB-RT", "RefTitle": "BW 3.50 (Support Package 14) error in the condition item", "RefUrl": "/notes/858302 "}, {"RefNumber": "856094", "RefComponent": "BW-WHM", "RefTitle": "PERFORM_TOO_MANY_PARAMETERS in dataload", "RefUrl": "/notes/856094 "}, {"RefNumber": "693494", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP04 NW'04 Stack 04 RIN", "RefUrl": "/notes/693494 "}, {"RefNumber": "693363", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP03 NW'04 Stack 03 RIN", "RefUrl": "/notes/693363 "}, {"RefNumber": "692636", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP02 NW'04 Stack 02 RIN", "RefUrl": "/notes/692636 "}, {"RefNumber": "693362", "RefComponent": "BW", "RefTitle": "SAPBWNews BW SP01 NW'04 Stack 01 RIN", "RefUrl": "/notes/693362 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}