{"Request": {"Number": "2340095", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 484, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018362672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002340095?language=E&token=8C2CA2D439A6B0583C17DC76867B9AFB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002340095", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002340095/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2340095"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.11.2022"}, "SAPComponentKey": {"_label": "Component", "value": "CA-HR-S4"}, "SAPComponentKeyText": {"_label": "Component", "value": "Cross-Application HCM Objects in S/4HANA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross-Application objects in HR", "value": "CA-HR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-HR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross-Application HCM Objects in S/4HANA", "value": "CA-HR-S4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-HR-S4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2340095 - S4TWL - Conversion of Employees to Business Partners"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description</strong>&#65279;</p>\r\n<p>With the conversion to SAP S/4HANA the implementation of the employee business partner is mandatory if HR data (HR infotype based PERNR data model including the PA-Tables) is needed. The HR data can be locally maintained (for example via transaction PA30 or PA40) or via integration scenarios with SAP HCM as hub implementation, SAP SuccessFactors Employee Central or an external (third party) HCM system.</p>\r\n<ol>\r\n<li><strong>Constellation SAP HCM embedded in SAP S/4HANA<br /></strong>It is mandatory to synchronize employees to employee business partners.</li>\r\n<li>\r\n<p><strong>Constellation SAP HCM ERP as hub implementation (non SAP S/4HANA)<br /></strong>The synchronization of employees to employee business partners does not take place in the SAP HCM hub system. If the HR mini master record is distributed to connected systems, it is mandatory to synchronize employees to employee business partners in the <strong>receiving SAP S/4HANA system(s).</strong></p>\r\n</li>\r\n<li><strong><strong><strong>Constellation SAP HCM in SAP S/4HANA as hub implementation<br /></strong></strong></strong>The synchronization of employees to employee business partners takes place in the SAP HCM hub system. Additionally, if the HR mini master record is distributed to connected systems, it is mandatory to synchronize employees to employee business partners in the<strong> receiving SAP S/4HANA system(s).<strong><strong><br /></strong></strong></strong><strong><strong><br /></strong></strong></li>\r\n<li><strong><strong>Constellation SAP SuccessFactors in a hybrid scenario<br /></strong></strong>In a hybrid scenario where employees are maintained in SAP SuccessFactors Employee Central and distributed to SAP S/4HANA system(s) it is mandatory to synchronize employees to employee business partners.</li>\r\n</ol>\r\n<p>To generate the employee business partners the HCM integration must be activated via the HCM Integration Switch in system table T77S0.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"121\">\r\n<p><strong>Description&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p><strong>Group (GRPID)</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p><strong>Sem. abbr. (SEMID)</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p><strong>HCM Integration inactive</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p><strong>HCM Integration active</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"121\">\r\n<p>Activate HCM Integration</p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p>HRALX</p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p>HRAC</p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p>Space (blank)</p>\r\n</td>\r\n<td valign=\"top\" width=\"121\">\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If the HR mini master is not needed the HCM integration can be deactivated.</p>\r\n<p>As of SAP S/4HANA 2020 OP a new Employee Business Partner Model was introduced. This new model was also provided with Support Packages for the lower releases SAP S/4HANA 1809 OP SP06 and 1909 OP SP04. See SAP Note <a target=\"_blank\" href=\"/notes/2954033\">2954033</a><strong>. It is recommended to use the New Employee Business Partner Model.</strong></p>\r\n<p>Nevertheless, you can also use the legacy models (VMODE1, VMODE2). For more information regarding legacy employee business partner models please refer to this <a target=\"_blank\" href=\"https://blogs.sap.com/2021/08/16/new-employee-business-partner-data-model-in-sap-s-4hana-2020-on-premise/?preview_id=1377633\">blog post</a> and the integration guide attached to this Note.</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>The data model in SAP S/4HANA is based on Business Partners (BP). A BP must be assigned to each employee.</p>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p>After the conversion to SAP S/4HANA, the migration report /SHCM/RH_SYNC_BUPA_FROM_EMPL must be executed before productive use of the system. In case of concerns regarding performance caused by large data volume it is recommended to use report /SHCM/RH_SYNC_BUPA_EMPL_SINGLE for the initial load. Due to customizing dependencies, the reports cannot be executed in technical downtime. For the daily synchronization the report /SHCM/RH_SYNC_BUPA_FROM_EMPL needs to be scheduled. It is recommended to run the report once a day after the initial run in order to synchronize future-dated employee changes to the Business Partner. Non future-dated changes to employee data are synchronized to the Business Partner automatically.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PY-XX (Payroll: General Parts)"}, {"Key": "Other Components", "Value": "PA-BC (Authorization and HCM Basis)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022866)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D000406)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002340095/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340095/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Integration Guide - OP 2022 FSP01.pdf", "FileSize": "1130", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000874322016&iv_version=0022&iv_guid=00109B36D62A1EDD98995DF9953D0FBE"}, {"FileName": "Installation Guide - OP.pdf", "FileSize": "158", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000874322016&iv_version=0022&iv_guid=00109B36BC261EDD98995ED1C6C7FE5C"}, {"FileName": "Guide - initial Version.pdf", "FileSize": "1822", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000874322016&iv_version=0022&iv_guid=00109B36D6AA1ED89BA5445875D060C4"}, {"FileName": "Integration Guide - OP 2021.pdf", "FileSize": "1112", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000874322016&iv_version=0022&iv_guid=00109B36DBDE1EECA282B9F54A3EDC01"}, {"FileName": "Integration Guide - OP 2020 FPS02.pdf", "FileSize": "992", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000874322016&iv_version=0022&iv_guid=00109B36BCAE1EDBA78B566A0BEDA0EB"}, {"FileName": "Integration Guide - OP 2022.pdf", "FileSize": "1129", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000874322016&iv_version=0022&iv_guid=00109B36D5DA1EDD92A8DF7036118AA7"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2364207", "RefComponent": "CA-HR-S4", "RefTitle": "Employee to Business Partner synchronization in S/4HANA performance issues and parallel job scheduling is required", "RefUrl": "/notes/2364207"}, {"RefNumber": "2323301", "RefComponent": "CA-HR-S4", "RefTitle": "Customizing document - Synchronization of Business Partner for SAP HCM Employee Role", "RefUrl": "/notes/2323301"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3128085", "RefComponent": "CA-HR-S4-OP-BP", "RefTitle": "How to filter out employees from being synchronized into BPs", "RefUrl": "/notes/3128085 "}, {"RefNumber": "2578294", "RefComponent": "CA-HR-S4-OP-BP", "RefTitle": "FAQ - Employee to Business Partner synchronization in S/4HANA OnPremise", "RefUrl": "/notes/2578294 "}, {"RefNumber": "3044085", "RefComponent": "CA-HR-S4", "RefTitle": "FAQ - Use and Future of HCM module in S/4HANA OnPremise", "RefUrl": "/notes/3044085 "}, {"RefNumber": "2713963", "RefComponent": "XX-SER-MCC", "RefTitle": "FAQ: CVI - Customer Vendor Integration for system conversion to SAP S/4HANA", "RefUrl": "/notes/2713963 "}, {"RefNumber": "2697055", "RefComponent": "MM-FIO-PUR-REQ-SSP", "RefTitle": "\"The given date instance isn't valid\" error in My Purchase Requisitions app", "RefUrl": "/notes/2697055 "}, {"RefNumber": "3304275", "RefComponent": "PA-FIO-LKP", "RefTitle": "Enterprise Search Model - Employee Address Book", "RefUrl": "/notes/3304275 "}, {"RefNumber": "2954033", "RefComponent": "CA-HR-S4", "RefTitle": "Downport new Business partner integration", "RefUrl": "/notes/2954033 "}, {"RefNumber": "2910882", "RefComponent": "CRM-S4-FCA", "RefTitle": "FCC/IR: Error COM_PARTNER 119 when navigating to the Interaction Record", "RefUrl": "/notes/2910882 "}, {"RefNumber": "2539457", "RefComponent": "BC-BMT-OM-ALE", "RefTitle": "CVI & employee vendors in incorrect BP category", "RefUrl": "/notes/2539457 "}, {"RefNumber": "2542175", "RefComponent": "BC-BMT-OM-ALE", "RefTitle": "CVI and reuse of existing employee business partner", "RefUrl": "/notes/2542175 "}, {"RefNumber": "2409229", "RefComponent": "CA-HR-S4", "RefTitle": "Employee Synchronization Report: FAQ", "RefUrl": "/notes/2409229 "}, {"RefNumber": "2584969", "RefComponent": "BC-BMT-OM-EBP", "RefTitle": "CVI: HCM CVI: PreChe<PERSON>", "RefUrl": "/notes/2584969 "}, {"RefNumber": "2570961", "RefComponent": "CA-GTF-BUM", "RefTitle": "Simplification item S4TWL - Business User Management", "RefUrl": "/notes/2570961 "}, {"RefNumber": "2241838", "RefComponent": "FI-TV", "RefTitle": "Inconsistencies when you use the program RPRAPA00 or transaction PRAA to create a vendor", "RefUrl": "/notes/2241838 "}, {"RefNumber": "2514220", "RefComponent": "BC-BMT-OM-ALE", "RefTitle": "T77S0-HRALX-PBPON in S/4HANA", "RefUrl": "/notes/2514220 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}