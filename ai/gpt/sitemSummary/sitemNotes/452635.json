{"Request": {"Number": "452635", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 279, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015129182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000452635?language=E&token=4605BBE57B394FA6EB8E9A8EC05CB1AD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000452635", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000452635/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "452635"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.02.2002"}, "SAPComponentKey": {"_label": "Component", "value": "IM-FA-IS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Information System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Investment Management", "value": "IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Spec. investments", "value": "IM-FA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IM-FA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "IM-FA-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IM-FA-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "452635 - Problems when executing a report with selection variants"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you execute reports with selection variants, the system may generate illogical error messages, for example, for RASIMU01, RASIMU02:</p> <UL><UL><LI>DB634: \"Variant &amp; of program &amp; is not the current version\".<br /></LI></UL></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>System selection variants</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by incorrect, in other words inconsistent selection variants.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Create/correct the selection variants, re-import the system selection variants.<br />When you create selection variants, always check their consistency, in other words, the entries that are made with the selection variant have to be allowed as a rule. The error occurs in particular if the type for selection fields has been modified. As a result, the import of new standard variants from SAP systems is useless.<br />System selection variants are only intended as templates and are NOT to be immediately modified, but only copied and the copies to be modified afterwards in accordance with the user requirements.<br />SAP will deliver the system selection variants again only if they are incorrect for ALL of your systems. In this case, consider that the source system version (release) is identical to the target system version into which the new variants are to be transported. In SAP Note 13719, you can find more information on the import of the variants into your system.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AA-IS (Information System)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D036627)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D036627)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000452635/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000452635/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000452635/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000452635/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000452635/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000452635/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000452635/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000452635/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000452635/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "80526", "RefComponent": "BC-ABA-TO", "RefTitle": "Unlocking protected variants", "RefUrl": "/notes/80526"}, {"RefNumber": "65343", "RefComponent": "BC-ABA-TO", "RefTitle": "Problems with variants after the upgrade", "RefUrl": "/notes/65343"}, {"RefNumber": "503059", "RefComponent": "IM-FA-IS", "RefTitle": "RASIMU01: System selection variant SAP&001", "RefUrl": "/notes/503059"}, {"RefNumber": "407926", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer: Problem after variant transport", "RefUrl": "/notes/407926"}, {"RefNumber": "385589", "RefComponent": "FI-AA-IS", "RefTitle": "Revised SAP standard variants Release 4.6B and 4.6C", "RefUrl": "/notes/385589"}, {"RefNumber": "384927", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/384927"}, {"RefNumber": "372724", "RefComponent": "FI-AA-IS", "RefTitle": "Maintenance of report variants", "RefUrl": "/notes/372724"}, {"RefNumber": "351007", "RefComponent": "BC-ABA-TO", "RefTitle": "Save as variants, Cancel", "RefUrl": "/notes/351007"}, {"RefNumber": "331089", "RefComponent": "FI-AA-IS", "RefTitle": "Incorrect default values w/ standard variants", "RefUrl": "/notes/331089"}, {"RefNumber": "320066", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer generated program names as of 4.5A", "RefUrl": "/notes/320066"}, {"RefNumber": "216833", "RefComponent": "BC-ABA-TO", "RefTitle": "Inconsistencies for variant tables", "RefUrl": "/notes/216833"}, {"RefNumber": "200645", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200645"}, {"RefNumber": "176350", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer: Transport variants", "RefUrl": "/notes/176350"}, {"RefNumber": "175343", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer: problems with jobs after upgrade", "RefUrl": "/notes/175343"}, {"RefNumber": "171366", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171366"}, {"RefNumber": "168727", "RefComponent": "FI-SL-IS-A", "RefTitle": "DB628 when executing report group in the background", "RefUrl": "/notes/168727"}, {"RefNumber": "167790", "RefComponent": "FI-AA-IS", "RefTitle": "RAAEND01: Report variant with company code", "RefUrl": "/notes/167790"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "128908", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting report variants", "RefUrl": "/notes/128908"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2554422", "RefComponent": "BC-ABA-TO", "RefTitle": "VARI and VARINUM different number of entries", "RefUrl": "/notes/2554422 "}, {"RefNumber": "80526", "RefComponent": "BC-ABA-TO", "RefTitle": "Unlocking protected variants", "RefUrl": "/notes/80526 "}, {"RefNumber": "128908", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting report variants", "RefUrl": "/notes/128908 "}, {"RefNumber": "216833", "RefComponent": "BC-ABA-TO", "RefTitle": "Inconsistencies for variant tables", "RefUrl": "/notes/216833 "}, {"RefNumber": "407926", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer: Problem after variant transport", "RefUrl": "/notes/407926 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "372724", "RefComponent": "FI-AA-IS", "RefTitle": "Maintenance of report variants", "RefUrl": "/notes/372724 "}, {"RefNumber": "176350", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer: Transport variants", "RefUrl": "/notes/176350 "}, {"RefNumber": "385589", "RefComponent": "FI-AA-IS", "RefTitle": "Revised SAP standard variants Release 4.6B and 4.6C", "RefUrl": "/notes/385589 "}, {"RefNumber": "168727", "RefComponent": "FI-SL-IS-A", "RefTitle": "DB628 when executing report group in the background", "RefUrl": "/notes/168727 "}, {"RefNumber": "200645", "RefComponent": "FI-SL-IS-A", "RefTitle": "Changed function in Release 4.5", "RefUrl": "/notes/200645 "}, {"RefNumber": "503059", "RefComponent": "IM-FA-IS", "RefTitle": "RASIMU01: System selection variant SAP&001", "RefUrl": "/notes/503059 "}, {"RefNumber": "320066", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer generated program names as of 4.5A", "RefUrl": "/notes/320066 "}, {"RefNumber": "351007", "RefComponent": "BC-ABA-TO", "RefTitle": "Save as variants, Cancel", "RefUrl": "/notes/351007 "}, {"RefNumber": "331089", "RefComponent": "FI-AA-IS", "RefTitle": "Incorrect default values w/ standard variants", "RefUrl": "/notes/331089 "}, {"RefNumber": "167790", "RefComponent": "FI-AA-IS", "RefTitle": "RAAEND01: Report variant with company code", "RefUrl": "/notes/167790 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C27", "URL": "/supportpackage/SAPKH46C27"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}