{"Request": {"Number": "823951", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 354, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004454162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000823951?language=E&token=E1FCDF7AAC3435AB836B05421CFF5D4D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000823951", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000823951/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "823951"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.07.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-NC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Non cumulative value"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Non cumulative value", "value": "BW-BEX-OT-NC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-NC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "823951 - Consistency check of non-cumulative cubes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />The non-cumulative queries produce results that cannot be explained. In particular, the non-cumulative values change when you make changes to the drilldown status. The reason for this may be because there are missing reference point records in the E fact table.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />Non-cumulative query, reference point, compression without reference point update, check program, SAP_REFPOINT_COMPLETE, consistency, non-cumulative cube, non-cumulative value</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note and, in particular, the repair report SAP_REFPOINT_COMPLETE are <strong>not suitable for SAP-HANA-optimized InfoCubes</strong>, also known as flat InfoCubes. The original handling problem with historical non-cumulative movements cannot occur in the case of the new non-cumulative handling for SAP-HANA-optimized InfoCubes.</p>\r\n<p><br />These inconsistencies may occur if you leave out the zero stocks during stock initialization (to improve system performance) when historical stock movements are loaded. These must be compressed \"without updating the reference point\". This may lead to stock movements in the E fact table, even though there is no corresponding reference point. To see whether this situation applies to you, use the SAP_REFPOINT_COMPLETE program to check non-cumulative InfoCubes individually. The program is available in standard systems that contain the Support Packages specified below. You can download the program in advance.<br />To implement the advance correction, create an empty program with the name \"Z_SAP_REFPOINT_COMPLETE\". Implement the program using transaction SNOTE.<br /><br />Short description of check program; it has two input parameters:</p>\r\n<ul>\r\n<li><strong>I_PROV</strong>: Name of the InfoCube to be checked.&#x00A0;If no InfoCube name is entered, all active InfoCubes that have non-cumulative key figures are checked.</li>\r\n<li><strong>I_REPAIR</strong>:&#x00A0;If this indicator is not set, the check is carried out and the number of missing records is output in a list if inconsistencies exist.&#x00A0;<br />If the indicator is set, missing records are enhanced with the non-cumulative value 0.</li>\r\n</ul>\r\n<p><br /><strong><span style=\"text-decoration: underline;\">Performance:</span></strong><br /><br />The determination of inconsistencies takes place using an SQL statement that determines missing reference point entries by means of a SELF-JOIN of the E fact table. All key fields of the E fact table are used for the join condition except for the key field for the time dimension. In the assembled P index, the key field for the time dimension is in the 2nd position, so this index cannot be used for the SQL statement in a performant manner. It might therefore be useful to create an <strong>additional index</strong> <strong>for the E fact table</strong> in the database for this CHECK/REPAIR. This index should contain all key fields of the E fact table except for the time dimension key field. This new index corresponds to the P index without the time dimension key field. The entire SQL statement is then executed on this index and the runtime is significantly shortened for (extremely) large InfoCubes.<br />You can ask your database administrator to create this index directly on the database (using native SQL) because this index is only required temporarily. You can also define the index in the ABAP Dictionary and activate it there. You can ignore the popup during the activation of the user-defined index.</p>\r\n<p><br />If the E fact table is partitioned according to the time, this index should be created as a global index.<br /><br /><strong>Please note that you must delete these indexes again</strong>.<br /><br />If there are a lot of reference point records missing (over 100,000 records), delete the existing secondary indexes from the E fact table before the REPAIR run and recreate them afterwards. You can use the function module \"RSDU_INFOCUBE_INDEXES_DROP\" to delete these indexes and the function module \"RSDU_INFOCUBE_INDEXES_REPAIR\" to recreate them.<br />This applies to Oracle database systems in particular, because their indexes are defined as bitmap indexes.<br /><br /><br />The program SAP_REFPOINT_COMPLETE must no longer be executed in certain situations under BW 7.X and higher.  For example, when compressing historical data, reference points that do not yet exist may be added during the compression.  In this situation, the report must no longer be executed. <br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>BW 3.0B:<br />Import Support Package 27 for 3.0B (BW 3.0B Patch 27 or <strong>SAPKW30B27</strong>) into your BW system. The support package is available once <strong>SAP Note 723258</strong> (\"SAPBWNews NW BW 3.0B SP27\"), which describes this SP in more detail, has been released for customers.<br />&#x00A0;</li>\r\n<li>BW 3.10 Content:<br />Import Support Package 21 for 3.10 (BW 3.10 Patch 21 or <strong>SAPKW31021</strong>) into your BW system. The support package is available once <strong>SAP Note 723263</strong> (\"SAPBWNews NW BW 3.1 Content SP21\"), which describes this SP in more detail, has been released for customers.<br />&#x00A0;</li>\r\n<li>BW 3.50:<br />Import Support Package 12 for Release 3.5 (BW 3.50 Patch 12 or <strong>SAPKW35012</strong>) into your BW system. The support package is available once <strong>SAP Note 0763340</strong> (\"SAPBWNews NW BW SP12 Stack 12\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul><p><br />To provide information in advance, the notes mentioned above may already be available before the Support Packages are released. In this case, the short text of the note still contains the words \"Preliminary version\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-DBIF (Interface to Database)"}, {"Key": "Responsible                                                                                         ", "Value": "D017375"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022263)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000823951/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000823951/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "checkprog_ncum.txt", "FileSize": "16", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000201212005&iv_version=0016&iv_guid=E353ED387D3748408240BA42105DC004"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "763340", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 12 NetWeaver'04 Stack 12 RIN", "RefUrl": "/notes/763340"}, {"RefNumber": "728360", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "PLUG: Performance of stock initialization", "RefUrl": "/notes/728360"}, {"RefNumber": "723263", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 21", "RefUrl": "/notes/723263"}, {"RefNumber": "723258", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 27", "RefUrl": "/notes/723258"}, {"RefNumber": "655798", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "No values with stock key figures (0IC_C03, for example)", "RefUrl": "/notes/655798"}, {"RefNumber": "1012847", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "DUMP when running SAP_REFPOINT_COMPLETE", "RefUrl": "/notes/1012847"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2891757", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-Cumulatives: Selective Deletion & 0RECORDTP", "RefUrl": "/notes/2891757 "}, {"RefNumber": "1012847", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "DUMP when running SAP_REFPOINT_COMPLETE", "RefUrl": "/notes/1012847 "}, {"RefNumber": "763340", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 12 NetWeaver'04 Stack 12 RIN", "RefUrl": "/notes/763340 "}, {"RefNumber": "723258", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 27", "RefUrl": "/notes/723258 "}, {"RefNumber": "723263", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 21", "RefUrl": "/notes/723263 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "728360", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "PLUG: Performance of stock initialization", "RefUrl": "/notes/728360 "}, {"RefNumber": "655798", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "No values with stock key figures (0IC_C03, for example)", "RefUrl": "/notes/655798 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "30B", "To": "30B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B36INVCBWTECH", "URL": "/supportpackage/SAPK-30B36INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B34INVCBWTECH", "URL": "/supportpackage/SAPK-30B34INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B33INVCBWTECH", "URL": "/supportpackage/SAPK-30B33INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B29", "URL": "/supportpackage/SAPKW30B29"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B27", "URL": "/supportpackage/SAPKW30B27"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B27", "URL": "/supportpackage/SAPKW30B27"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31021", "URL": "/supportpackage/SAPKW31021"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31023", "URL": "/supportpackage/SAPKW31023"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31023", "URL": "/supportpackage/SAPKW31023"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31021", "URL": "/supportpackage/SAPKW31021"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31021", "URL": "/supportpackage/SAPKW31021"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35015", "URL": "/supportpackage/SAPKW35015"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35013", "URL": "/supportpackage/SAPKW35013"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35012", "URL": "/supportpackage/SAPKW35012"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "NumberOfCorrin": 1, "URL": "/corrins/0000823951/654"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}