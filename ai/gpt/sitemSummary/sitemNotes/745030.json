{"Request": {"Number": "745030", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 350, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015680072017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000745030?language=E&token=5026263B15697B1CDEF88974CE2956B5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000745030", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000745030/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "745030"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.04.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18-UNI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Unicode"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Unicode", "value": "BC-I18-UNI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18-UNI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "745030 - MDMP - Unicode Interfaces: Solution Overview"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Custom<PERSON> has mixed SAP Business Suite Components with Unicode and R/3 MDMP</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MDMP, Unicode, Code Page, Conversion, RFC, Languages, IDOC, BDOC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Customer has R/3 with MDMP connected to SAP Unicode Systems</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>********************* Important ************************************</strong></p>\r\n<p><br /><strong>This is a CONSULTING NOTE and might not provide a suitable solution for your concrete scenario and environment !</strong><br /><strong>For detailed statements, information and a complete solution a consulting service is required.</strong><br /><br /><strong>The interface scenario SAP MDMP - Unicode is no SAP standard scenario and therefore not supported via standard OSS message handling.</strong><br /><br /><strong>For more information <NAME_EMAIL></strong><br /><br /><strong>*********************************************************************</strong></p>\r\n<p><br />Several related notes might not generally released for customers,<br />In this case&#160;&#160;please contact SAP.<br /><br />There are several attachments to this note which contain important<br />further details and documentation.</p>\r\n<p>Please be aware that this SAP note describes scenarios&#160;for several oudated&#160;SAP releases which are&#160;no more supported (see <a target=\"_blank\" href=\"http://support.sap.com/pam\">http://support.sap.com/pam</a> for details) &#160;MDMP is an old solution with many restrictions and&#160; is no more supported in&#160;SAP releases based on SAP NetWeaver 7.0 and higher. If you still have one or several MDMP systems in your SAP system landscape it is urgently recommended to upgrade and convert to Unicode as soon as possible. You can write to <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a> to get further information and dedicated service offerings.</p>\r\n<p><strong>Discussed MDMP Releases: in this note</strong></p>\r\n<p>Note that the term&#160;&#160;\"R/3 MDMP\"&#160; refers to the following product versions:<br />- R/3 Enterprise MDMP using WEB AS 6.20 and higher<br />- ECC 5.0 MDMP&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;using WEB AS 6.40 and higher (limited)<br />- R/3 46C MDMP&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;using 46D and WITH POTENTIAL RESTRICTIONS<br /><br />If you still have a R/3 MDMP with a release lower than 46C this case is very restrictively supported. You should contact SAP for details. SAP note 1145203 describes a limited workaround solution for this case.</p>\r\n<p><strong>Discussed Unicode&#160;Releases:</strong></p>\r\n<p>- SAP Technology Components refer to<br />&#160;&#160; SAP WEB AS 6.20, NETWEAVER 2004 and 7.0 component releases if not<br />&#160;&#160; differently documented.<br />- SAP Application Components: Refer to SAP Note 79991<br />- SAP components which are based on higher SAP NetWeaver releases &gt; 7.00 &#160;are not or only very partially discussed<br />&#160; in this note<br />- SAP products which are based on a SAP NetWeaver releases higher than 7.4x are available in Unicode only<br />- Special SAP products and platforms are only available in Unicode: SAP XI/PI, all products runing on SYBASE and HANA<br />&#160;&#160; database, new SAP products</p>\r\n<p><br />For a general information, overview and restrictions of SAP ERP 2004<br />and SAP ERP 6.0 (2005) upgrades for MDMP systems refer to SAP Notes<br />747036 and 896144.<br /><strong>*******************************************************************</strong><br /><br /><br /></p>\r\n<p><strong>Table of Contents:<br /><br />A.&#160;&#160;Introduction<br /><br />B.&#160;&#160;Background Information<br /><br />C.&#160;&#160;Main Principles of Solution Approach<br /><br />D.&#160;&#160;Solutions per Component</strong></p>\r\n<p><br />========================================================================</p>\r\n<p><strong>A. Introduction<br /></strong></p>\r\n<p>This note contains a collection of all known and available solutions how a MDMP R/3 system can correctly exchange data containing local texts with a Unicode system. In general, additional effort is required so that in nearly all cases the exiting interfaces have to be enhanced and modified via user exits/BADIs or additional development. Depending on the scenario and the interface technology this effort might significantly vary. Therefore, the always best and most recommended solution is to upgrade and/or convert your R/3 system to Unicode.<br /><br />This is the Master Note containing the collection of all<br />available known solutions in the form of:<br /><br />- Related Notes<br />- Various Documents as Attachments<br />- Links to SAP Service Marketplace<br /><br />A summarized overview of the current available solutions can be found<br />in the PDF attachment \"Unicode_MDMP_Solutions.pdf\".</p>\r\n<p><strong>B. Background Information<br /></strong></p>\r\n<p>Since the availability of Unicode in SAP Business Suite components (see SAP Note 79991) SAP strongly recommends it to customers who need to combine several languages with mixed character sets and code pages in one single instance. While Unicode is the only technical complete and general solution for any language&#160;&#160;combinations there have been several workaround solutions in the past in order to support restrictive scenarios and combinations of mixed code page languages in one system. The supported non-Unicode workaround solution in SAP releases up to ERP 2004 is the MDMP ( Multi Display - Multiple Processing) solution which enables the usage of several code pages in one single R/3 system with many restrictions and many rules. MDMP is no more supported as of SAP ERP 6.0 and any components based on Netweaver 7.0. Note that the solutions in this note do not support the pre-MDMP solution \"Blended Code Pages\".<br />Also note that MDMP is only supported for SAP ERP (R/3, R/3 Enterprise) based systems. It is officially not supported for other SAP Business Suite components which have be installed or upgraded/converted to Unicode in case of multlingual language requirements.<br /><br />This, generally, leads to a typical system landscape with mixed Unicode (non R/3) and MDMP R/3 system(s). Although it is strongly recommended to upgrade and convert the MDMP R/3 system(s) to Unicode as well, this is often not feasible in a reasonable time frame.<br />While the ideal solution and recommendation is to have all systems in Unicode, there is the need to run a combination of mixed Unicode and non-Unicode systems in parallel,&#160;&#160;at least for some temporary time. However, the combination of Unicode and non-Unicode systems cause major challenges in the data exchange and interface area, which can be generally categorized as follows:<br /><br />- Unicode System - Single Code Page System&#160;&#160;&#160;&#160; :&#160;&#160;No or low effort<br />- Unicode system - MDMP R/3 system&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:&#160;&#160;High-very high Effort<br /><br />The main reasons for the effort for Unicode - MDMP interfaces are :<br /><br />- Conversion of texts/characters to and from Unicode is correct if<br />&#160;&#160;the language / code page of transferred text/characters are known<br />- Many (text) data structures in R/3 do not have a language key<br />- Global text data often not in English but local language<br />- Different interface technologies<br /><br />In order to provide a correct data exchange between Unicode and MDMP systems, it is therefore necessary to precisely analyze the required exchange scenario, interface technology&#160;&#160;and involved interface data structures of the transferred texts.<br /><br />In most cases it becomes necessary to enhance these interfaces with additional effort since the most important prerequisites for transparent language keys in the transfer data structures are not fulfilled, which means that the interface&#160;&#160;\"does not know\" to which language and code page the \"transferred bytes\" belong to and can therefore not make a correct conversion. While this is basically not a problem in a single code page non-UNICODE system (since the single code page is known) this \"uncertainty\" of byte - code page assignments in the&#160;&#160;MDMP case is the main reason for the interface challenge, in some cases it even makes a correct data transfer impossible.<br /><br />In the following all currently available and known solutions are described how&#160;&#160;the data transfer between a UNICODE and MDMP system can be realized.<br />First, some mandatory prerequisites, rules ( \"Golden Rules\") and system preparations are described, followed by a component-wise description of the solution method. The detailed solution ( if it exists) are mostly contained in further related SAP Notes and/or the various attached documents.<br />If an&#160;&#160;interface scenario is not mentioned in this note then there is no reasonable solution known at present or a realization would cause an unreasonable effort of development and modifications.<br /><br /></p>\r\n<p><strong>C. Main Principles of Solution Approach<br /></strong></p>\r\n<p>First of all, several golden rules are mandatory to establish in such a mixed Unicode - MDMP&#160;&#160;environment. These rules are a must and might even provide a correct data transfer in simple or simplified scenarios without known solutions or no solution in general without extreme effort.</p>\r\n<p><strong>&#160;&#160;&#160;&#160; Golden Rule Nr.1)<br /></strong></p>\r\n<p><span style=\"text-decoration: underline;\">&#160;&#160;&#160;&#160; Always use the correct login language</span><br />&#160;&#160;&#160;&#160;&#160;&#160; - Login language = Data Maintenance language<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - Across all components<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - on MDMP, non-Unicode and Unicode components<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - on ABAP and non-ABAP (JAVA etc.) components<br /><br />&#160;&#160;&#160;&#160;&#160;&#160; - Interface Unicode --&gt; MDMP<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - User (or remote system) must login in local language in<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Unicode prior to data transfer<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - RFC connection (SM59): RFC login language field empty<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; (Unicode login passed to MDMP)<br />&#160;&#160;&#160;&#160;&#160;&#160; - Interfaces MDMP --&gt; Unicode<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - User (or remote system) must login in local language in MDMP<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; before data transfer.</p>\r\n<p><strong>&#160;&#160;&#160;&#160; Golden Rule Nr. 2)<br /></strong></p>\r\n<p><span style=\"text-decoration: underline;\">&#160;&#160;&#160;&#160; Whenever possible, use language filters in order to exchange data</span><br /><span style=\"text-decoration: underline;\">&#160;&#160;&#160;&#160; from one code page only</span><br />&#160;&#160;&#160;&#160; See also this topic in other sections of this note.<br />&#160;&#160;&#160;&#160; In some cases it is possible to do some special configuration<br />&#160;&#160;&#160;&#160;and customizing, in some other cases additional development<br />&#160;&#160;&#160;&#160; is necessary.</p>\r\n<p><strong>&#160;&#160;&#160;&#160; Golden Rule Nr. 3)<br /></strong></p>\r\n<p><span style=\"text-decoration: underline;\">&#160;&#160;&#160;&#160; Install and/or configure all languages that are used during data</span><br /><span style=\"text-decoration: underline;\">&#160;&#160;&#160;&#160; transfer on all communicating systems</span></p>\r\n<p><strong>&#160;&#160;&#160;&#160; Golden Rule Nr. 4)<br /></strong></p>\r\n<p><span style=\"text-decoration: underline;\">&#160;&#160;&#160;&#160; Please check the prerequisites and releases carefully as</span><br />&#160;&#160;&#160;&#160; described here, in the attachmentsand in related notes.<br /><br />As already mentioned above, the main issue in the various Unicode - MDMP interfaces is always the unique recognition and assignment of all transferred texts to a language or code page (knowledge of the language is sufficient since each language in a MDMP system is uniquely assigned to a code page). This is in particular crucial if the transferred texts belong to more than one code page.<br /><br />Therefore the main solution method of practically each interface scenario is to make all transferred texts fully language dependent in a transparent way.<br /><br />If the transferred texts contain more than one code page there are the following significant technology differences for the cases<br /><br />&#160;&#160;Unicode ABAP component&#160;&#160;&#160;&#160; - MDMP&#160;&#160;or<br />&#160;&#160; Unicode non-ABAP component - MDMP<br /><br />as follows:</p>\r\n<p><strong>Data transfer between Unicode ABAP component and MDMP&#160;&#160;with RFC:<br /></strong></p>\r\n<p>Data transfer between ABAP components using RFC technology is much better conditioned for a solution due to the following&#160;&#160;RFC features:<br /><br />- RFC performs correct conversion from and to Unicode automatically<br />- RFC can correctly convert transparent data structures row by row<br />&#160;&#160;with a language key.<br />&#160;&#160;This is very important if the transferred data package contains<br />&#160;&#160;more than one code&#160;&#160;page which is often the case.<br />&#160;&#160;Example: Materials in R/3 contain multiple language descriptions<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;and are mostly transferred as one data package ( e.g. IDOC)<br /><br />- RFC has special configuration options for login language handling<br /><br />- Transfer between ABAP based components allows a relative moderate<br />&#160;&#160; effort for enhancing the interface using the full capabilities of<br />&#160;&#160; the ABAP Dictionary and user exit / BTE / BADI Technology</p>\r\n<p><strong>Data Transfer between Unicode non-ABAP Component and MDMP:<br /></strong></p>\r\n<p>This case is significantly much more challenging than before, since MDMP is an SAP internal and ABAP centric solution which is completely unknown to non-ABAP&#160;&#160;components like JAVA, C/C++, etc. Therefore, in this case here is either much more effort needed for development/enhancement of the interface or significant restrictions have to be accepted or there is no solution at all.<br /><br />Frequent Scenarios and Components:<br /><br />&#160;&#160;- JAVA based SAP application are connected to ABAP components via<br />&#160;&#160;&#160;&#160;the JAVA connector (JCo)<br />&#160;&#160;- Connection between SAP XI/PI&#160;&#160;adapters to ABAP components<br />&#160;&#160; - Connections based on .NET Connector<br /><br />All these connectors and their interfaces have mostly in common that they connect from/to the ABAP component via RFC login using ONE login language during one session. If during one session ( i.e. data transfer) texts with&#160;&#160;mixed code pages are exchanged (as this is the frequent case in MDMP) it is immediately evident that only those texts fitting to the connector login language can be correctly converted and the other would become corrupted without additional measures.<br /><br />Thus, the main solution approaches in those scenarios are mostly<br /><br /><strong>- Language and Code Page Filtering:</strong><br /><br />The transferred data package is filtered / split into several packages for each code page which are then transferred in several steps.<br /><br /><strong>- Special case in Internet/HTML Based Scenarios:</strong><br /><br />Deployment of Internet Transaction Server (ITS) connected to a MDMP system is still possible with limitations since ITS can handle the MDMP behavior in HTML SAPGUI in the nearly same way with the same known MDMP restrictions and golden rules. This ITS approach is recommended for an integration of a MDMP R/3 system into the Enterprise Portal 6.0 (see also below)&#160;&#160;by creating an iView using the ITS interface to the backend R/3. Note that this solution approach is no more valid for SAP Netweaver Portal 7.0 (Enterprise Portal 7.0), in this case ask SAP for details.<br /><br /><strong>- Java Connector (JCo) and RFC combined</strong><br /><br />Applications using&#160;&#160;the connection between JAVA and ABAP components communicate through a combination of RFC and the JAVA connector (JCo).<br />Examples are Web Dynpro based User Interfaces or ISA Internet Sales in CRM 4.0 and higher.<br />For both directions the JCo can only convert one language/codepage correctly for which it has been configured, that is:<br />JAVA client calls R/3&#160;&#160;: JAVA&#160;&#160;connector must login in correct language<br />R/3 calls JAVA:&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Correct Login language before call<br /><br /><strong>- Business Server Pages ( BSP)</strong><br />&#160;&#160;No Solution known so far ( without very high effort) for<br />&#160;&#160;BSP ( Business Server Pages) based interfaces in MDMP (seldom)<br /><br />For the newest releases and scenarios with these components a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br /><br /></p>\r\n<p><strong>D.&#160;&#160;Solutions per Component<br /></strong></p>\r\n<p>In the following paragraphs the known solutions are described for each component with the references to further SAP notes and documentation. An overview at a glance can be found in the attachment \"Unicode_MDMP_Solutions.pdf\", page 5.</p>\r\n<p><strong>1. ALE Scenarios Unicode R/3 - MDMP R/3<br /></strong></p>\r\n<p>Application Link Enabling (ALE) uses Intermediate Documents (IDOCs) between the system based on a distribution model which determines the data flow and content between the different systems. the IDOCs contain the data to transfer and can be seen as a container which is transferred in one operation. If the IDOC contains text with more than one code page an additional solution is required.<br />In case of mixed Unicode - MDMP ALE transfer the solution can be split<br />into 2 cases:</p>\r\n<p><strong>1.1 A Language Filter is Available:<br /></strong></p>\r\n<p>If the ALE Distribution Model and the affected message type provide&#160;&#160;&#160;&#160;a language filter the simplest approach is to split the transfer&#160;&#160;&#160;&#160;of one message type (data in one IDOC) into several transfers with&#160;&#160;&#160;&#160;each one code page.<br />Together with several Logical Systems and RFC destinations, one per code page, ( see also SAP note 613389), the data transfer is adjusted to several single code page transfers which correctly&#160;&#160;&#160;&#160;convert with standard RFC.<br /><br />Example: Message Type MATMAS&#160;&#160;allows a language filter in the ALE<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Distribution Model</p>\r\n<p><strong>1.2 No Language Filter: Data Transfer in One Step:<br /></strong></p>\r\n<p>If there is no language filter or the data must be transferred in one step ( case 1. can lead to performance issues and&#160;&#160;data integrity issues) a further solution can be applied, which, however, can cause high - very high effort for addtional development and modifications.<br /><br />The main approach is to add a language key to the IDOC structure and to fill the IDOC structure with correct language data, basically one text segment in one language in one row/record together with the correct language key.<br /><br />This requires a modification of<br />&#160;&#160; - IDOC structure(s)&#160;&#160;by adding a language key field<br />&#160;&#160; - Modification of Outbound Processing: Filling the enhanced<br />&#160;&#160;&#160;&#160; IDOC structure with correct text data per language row by row<br />&#160;&#160;- Modification of inbound processing: reading the enhanced<br />&#160;&#160;&#160;&#160;text data and passing to application<br />&#160;&#160;- Container Alignment in both outbound and inbound for Asian<br />&#160;&#160;&#160;&#160; languages<br /><br />This means that modifications of the ALE standard functionality<br />is required and must be done in all involved systems.<br /><br />See SAP&#160;&#160;note 991763 \" IDoc communication between Unicode and MDMP systems\" and references for a special solution for this case. This note and the specified Support Packages provide a solution for connecting Unicode SAP systems and MDMP SAP systems with each other using the IDoc interface. The aim is to support messages that contain segments with different languages or code pages.<br /><br />SAP Note 656350 contains a further prototype solution in detail for&#160;&#160;the message type MATMAS ( Material Master Data) which allows to transfer material master data&#160;&#160;between a UNICODE and MDMP system with ALE with background transfer ( collected IDOC transfer)<br /><br />For higher SAP SCM releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br /><br /></p>\r\n<p><strong>2.&#160;&#160;CRM ABAP Server Unicode&#160;&#160;- R/3 MDMP</strong></p>\r\n<p><br />The following paragraphs contain a solution approach for the combination of a SAP CRM 4.0 or&#160;&#160;CRM 5.0 systems with a SAP R/3 MDMP system. For higher releases of SAP CRM (2006s, 2007) contact SAP for information.<br />For CRM 4.0 - R/3 MDMP scenarios only the interfaces between CRM&#160;&#160;Server and R/3 MDMP are considered with focus on the middleware design and functions. The data transfer is mostly done with Business Documents (BDOCs) structures and queued RFC technology. .<br /><br />There are 2 main solution approaches:</p>\r\n<p><strong>2.1&#160;&#160;Data object to Transfer Contains Texts of One Code Page Only<br /></strong></p>\r\n<p>This strongly depends on the given business scenario and the application how it creates and fills the BDOC structures. If the transferred data, i.e. the BDOC; only contains texts of one code page , then in general the golden rules ( see above) can handle a correct data transfer.<br />SAP Note 647495 contains additional details.<br /><br />Example: Sales Order with Japanese texts only to transfer<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;from CRM to R/3 MDMP backend<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; The following steps would be required:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - Check the RFC connection for R/3 in CRM. The RFC login<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; language should be empty ( Unicode CRM login language<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;passed to R/3 login language)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - login in Japanese (JA) in CRM<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; - Check that the given Sales Order contains JA texts only,<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;together with English ok<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Upload the Sales Order to R/3<br /><br />Note that this works for selected scenarios and data objects only.</p>\r\n<p><strong>2.2&#160;&#160;Data object to Transfer (BDOC) with Mixed Code Page Texts<br /></strong></p>\r\n<p>In this case the solution approach is again - similar as in ALE case - to add a language key to the transferred data structure as BDOC (data container).<br />The main BDOC structure is BAPIMTCS to which a language key field has to be added. Then, some additional coding via user exists is needed to fill in the correct language key and data into the structure.<br />SAP notes 686898, 691585 contains details about BDOC enhancements and user exits.<br /><br /><br /><strong>2.3 CRM 5.0 Server Unicode&#160;&#160;- R/3 MDMP and </strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Single (Asian Double Byte) Code Page </strong><br /><br />There is a new code page check in the CRM 5.0 middeware - R/3 Backend connection which prevents / blocks the data exchange in the cases:<br />- CRM Unicode and R/3 (R3E, ECC) single (Asian Double Byte) Code Page<br />- CRM Unicode and R/3 (R3E, ECC) MDMP with at least one<br />&#160;&#160;Asian Double Byte Codep Page<br /><br />This affects the code pages for Simplified (8400) and&#160;&#160;Traditional Chinese (8300) ,Japanese (8000), Korean (8500), and if exists&#160;&#160;Hong Kong Chinese (8340).<br />If you perform an Initial Download&#160;&#160;this effect can even&#160;&#160;occur if you do not actively use Asian Double Byte code pages (certain&#160;&#160;tables might have \"hard-coded\" Asian texts<br />Additional CRM Middewlare configuration after analysis of the concrete case and business data to exchange is required to allow data exchange,, see SAP note 777944. It is highly recommended to involve SAP experts for this case.<br />See SAP note 777944 for details:<br /><br />( <strong>Note</strong>: Under certain conditions this case can also happen for<br />CRM 4.0 Unicode systems - R/3 . In this case proceed as for CRM 5.0 )<br /><br />For higher SAP CRM releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br /></p>\r\n<p><strong>3.&#160;&#160;SRM 4.0 and 5.0 Server Unicode - R/3 MDMP</strong></p>\r\n<p><br />As in CRM 4.0 and 5.0 case above, only the interfaces SRM server 4.0 and 5.0 Unicode - R/3 MDMP are considered.<br /><br />For higher SAP SRM releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br />There are 3 cases to differentiate:</p>\r\n<p><strong>3.1 Data Object to Transfer Contains One Code Page Only<br /></strong></p>\r\n<p>Same case as in CRM 4.0/5.0, following the golden rules and the correct login language in both directions would provide a correct transfer. However, a precise analysis is to be done, if the data object really contains one code page only in a MDMP environment.</p>\r\n<p><strong>3.2 Usage of ALE and IDOC Interface Technology<br /></strong></p>\r\n<p>Several business objects in SRM such as Purchase Orders are transferred between SRM and R/3 with ALE and IDOC data structures. The recommended solution approach is here same as in ALE scenario above, to create several logical destinations and RFC connections, one per code page (by selecting the correct RFC login language) and to extend the ALE distribution model so that one transfer per (correct) code page with the correct destination can be done and converts correctly.</p>\r\n<p><strong>3.3 Usage of Middleware Technology<br /></strong></p>\r\n<p>Several data objects are transferred with middleware technology and datastructures BDOCs approach is the same as in CRM 4.0/5.0 Middleware - R/3 MDMP, namely to add a language key to BDOC structure and to add coding in order to fill it with correct language key and texts.<br />See SAP notes 613389, 656350&#160;&#160;for details.<br /><br /></p>\r\n<p><strong>4. SCM 4.0 and 5.0 Unicode&#160;&#160;- R/3 MDMP</strong></p>\r\n<p><br />In this scenario text data are mostly transferred from the R/3 MDMP system to the APO system, but virtually not or only in few cases sent back from APO to R/3 MDMP ( i.e. local texts). Thus, mostly one direction has to be considered. The interface technology is CIF which is based on the RFC protocol.<br /><br />SAP note&#160;&#160;718722&#160;&#160;contains additional information about the CIF interface and the necessary information for correct data transfer.<br /><br />For higher SAP SCM releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br /><br /></p>\r\n<p><strong>5. Solution Manager 4.0/7.0 (Solman) - R/3 MDMP<br /></strong></p>\r\n<p>The Solution Manager 4.0/7.0 is available in Unicode and should always be used in case of mixed system landscapes with Unicode and non-Unicode backend systems. If the backend is a MDMP system the following rules have to be established for a smooth Unicode - MDMP usage:<br />- SAPGUI 6.20 or higher with minimum patch level &gt;= 40<br />- SAP_BASIS&#160;&#160;Support Packages on highest levels in MDMP backend system<br />&#160;&#160;(status end of 2006)<br />- Correct Login language in Solution Manager 4.0&#160;&#160;Unicode; e.g.<br />&#160;&#160;Login Russian in Solman --&gt; Maintain Russian texts in MDMP backend<br />&#160;&#160;Login English in Solman --&gt; Maintain Russian texts NOT allowed in MDMP<br />- One RFC connection from Solman to MDMP for EACH code page<br />- Only one code page can be processed per remote login from Solman<br />&#160;&#160;to R/3 MDMP.<br /><br />For Solution Manager 3.1 the basically same statements are valid, however there might might be additional issues.<br /><br />For higher SAP Sslution Manager releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br /><br /></p>\r\n<p><strong>NETWEAVER&#160;&#160;Interface Scenarios</strong></p>\r\n<p><br />The following descriptions for SAP Netweaver interface scenarios are only valid for the listed SAP Netweaver components and their listed releases. For any other SAP Netweaver components and/or higer releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br /></p>\r\n<p><strong>6. BW 3.5 Unicode&#160;&#160;- R/3 MDMP</strong></p>\r\n<p><br />The main focus in the interface between a BW 3.5 Unicode and R/3 MDMP is the data structure of the extractors) with the fill and transfer logic.<br />Since the data transfer is in one direction only, i.e. from R/3 MDMP to BW 3.5 Unicode,&#160;&#160;only one direction has to be checked and<br />potentially adjusted.<br /><br />The following descriptions are valid for SAP BE 3.5 and SAP BW 7.0. For higher SAP BW releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br /><br />There are 2 main cases to differentiate:</p>\r\n<p><strong>6.1 Table to Extract / Transfer with Language Key<br /></strong></p>\r\n<p>This case does not require an enhancements since the table with the language key provides the text data with the necessary code page information ( directly derived from langugae key) so that the extractor already contains the proper language information. Then, several extractor runs are necessary, one for each code page, in order to finally transfer all texts data with correct conversion. this is again achieved by the \"golden rule\" by using the correct login language key and background language key (step language), respectively.</p>\r\n<p><strong>6.2 Table to Extract / Transfer without Language Key<br /></strong></p>\r\n<p>This case is more difficult and requires a similar solution approach as in the other scenarios, namely to add a language key to the extractor structure. Alternatively, one can split the extractor run for each code page if a suitable \"alternative\" language key is available, such as a country key.<br /><br />The following cases can be differentiated:</p>\r\n<p><strong>6.2.1 Enhancement of Extractor Structure with Additional Language Key<br /></strong></p>\r\n<p>Similar as in CRM and SRM, an additional language key is added to the extractor structure and additional coding in user exist is required for the correct filling with proper language and texts.</p>\r\n<p><strong>6.2.2 Split the Extraction Process into Several Ones per Code Page<br /></strong></p>\r\n<p>The extraction process is split into several&#160;&#160;processes together with a definition of an alternative language key such as a country so that one process runs for one code page.<br />This case dose not require enhancements and modifications and mainly be solved by following the golden rules:<br />- One extractor job per code page<br />- Using the correct login and background job language, respectively<br />- Suitable alternative language key (like country) can be used to filter<br />- One extraction job ( Info Package) per code page<br /><br />See SAP note 643813 for further information.</p>\r\n<p><strong>6.3&#160;&#160;BW 7.0&#160;&#160;Unicode&#160;&#160;- R/3 MDMP<br /></strong><br />For the combination of BI ABAP 7.0 and R/3 MDMP&#160;&#160;the same solutions are valid as in BW 3.5 whereas only&#160;&#160;ABAP based interfaces / extractors are covered. Any new technolgies and interfaces in BW 7.0 which are based on JAVA are not supported for this case.</p>\r\n<p><br /><br />Additional information about BW Unicode - R/3 MDMP scenarios can be found in the attached PDF presentation \"HowToMDMPConnect.pdf\"</p>\r\n<p>&#160;</p>\r\n<p><strong>7. Enterprise Portal EP 6.0 - R/3 MDMP<br /></strong><br />EP 6.0 is available in Unicode only and is based on JAVA Technology (J2EE, etc.). Since it contains many different components, only the interface and communication between the EP and R/3 MDMP&#160;&#160;through the iViews are considered.<br /><br />The following descriptions are valid for EP 6.0. For higher SAP EP / Netweaver Portal releases a special consulting service is required, and you <NAME_EMAIL> for more information.<br /><br />A general solution is very challenging here due to the very different involved technologies. EP uses JAVA and consequently the JAVA connector (JCo) to access ABAP components. The projection of the MDMP main \"working principle\" feature, namely to process texts of one code page (login language) correctly with all other texts from different code pages in the same screen unchanged, cannot be transformed here directly.<br /><br />The main challenge is the very different behavior of JAVA based components compared to&#160;&#160;MDMP. Since JAVA applications are Unicode only and the interface technology to ABAP components and thus R/3 MDMP is through the JAVA connector (JCo), a general EP - R/3 MDMP interface standard solution would require an extremely high programmming effort<br /><br />Therefore the solution approach is significantly more complex, and it is highly recommended to simplify the scope of accessing&#160;&#160;MDMP R/3 directly from the EP. In many business scenarios and cases this can be achieved by defining the R/3 MDMP system as a backend&#160;&#160;system, which is (seldom) used directly by a EP dialog user; instead, the R/3 MDMP system rather serves as backend for the&#160;&#160;SAP Components like CRM. SRM, BW, etc. Business Packages,<br />(see the SAP Portal Content Portfolio https://www.sdn.sap.com/irj/sdn/contentportfolio),<br />such as CRM People Centric Business Package are mostly designed for non-R/3 components such as CRM, BW, SRM etc and only a few ones for R/3 backend.<br /><br />A suitable solution approach can be recommended as follows:</p>\r\n<p><strong>7.1 EP used for DISPLAY Data only<br /></strong></p>\r\n<p>This case is the simplest one and would allow to access the R/3 MDMP system directly with the proper EP loin language and system configuration.&#160;&#160;However, no data maintenance would be possible.</p>\r\n<p><strong>7.2. EP as Launchpad for SAPGUI<br /></strong></p>\r\n<p>Instead of accessing the R/3 MDMP system through an iView it is possible to define a link to SAPLOGON/SAPGUI&#160;&#160;in the EP Desktop&#160;&#160;and thus use the R/3 MDMP system through the normal SAPGUI which then has to be installed as client software on the affected PC. This would be a very simple solution and, consequently, does not use the real EP functions; on the other hands, it would definitely save a lot of effort for quite more complicated solutions</p>\r\n<p><strong>7.3 Creation of Internet Transaction Server (ITS) iView<br /></strong></p>\r\n<p><strong>NOTE: This solution is quite outdated and only valid for SAP EP 6.0 !</strong><br />This is probably - at present, status Q2-Q3/2004 - the most pragmatic approach with moderate effort and still allowing to use some major features of EP.<br />ITS is the only Internet/HTML based non-ABAP component which can transform the MDMP main feature ( processing one code page correctly and letting other texts unchanged in same screen). This means , that an ITS based HTML screen behaves (nearly) identical as the standard SAPGUI within any MDMP based screen.<br />Since the EP 6.0 supports ITS based Transaction iViews the approach and recommendation is to create ITS iViews for the required R/3 MDMP transactions which shall be accessed via the EP portal, this allows to create pages, worksets, etc. too.<br /><br />The attached PDF Presentation \"Unicode_MDMP_Solutions.pdf\" contains screenshots how to create an ITS Transaction iView.<br /><br />In summary, for the EP - R/3 MDMP interfaces the following<br />solution approaches can&#160;&#160;be recommended:<br />- SAPGUI Launchpad<br />- ITS Transaction iViews</p>\r\n<p><strong>- Individual connections for each codepage to the R/3 MDMP backend<br /><br />7.4 SAP Netweaver Portal 7.0 - R/3 MDMP<br /></strong></p>\r\n<p>Due to the large number of new technologies in SAP Netweaver 7.0 and the Netweaver Portal 7.0 the solutuions described for EP 6.0 might only sporadically work. Therefore specific investigation and consulting is required. Please contact SAP for details.</p>\r\n<p><strong>7.5&#160;&#160;XI 3.0 - R/3 MDMP&#160;&#160; ( incl. MDM&#160;&#160;XI 3.0 - R/3 MDMP)<br /></strong></p>\r\n<p>XI 3.0 and MDM&#160;&#160;exist in Unicode only. The connection between XI and the R/3 baclend is mostly using the RFC or the IDOC adapter for which the following statements are valid:</p>\r\n<p><strong>7.5.1 XI IDOC Adapter for connection to R/3 MDMP</strong></p>\r\n<p><br />7.5.1&#160;&#160;Communication Scenario&#160;&#160;SAP R/3 MDMP - XI - SAP Unicode Component (SAP R/3 Enterprise Unicode or ERP&#160;&#160;Unicode<br /><br /><strong>Case 1: SAP R/3 MDMP sends a IDOC via XI to a Unicode partner system</strong><br /><br />This case is very close to the ALE Unicode - MDMP scenario since the connection R/3 - XI behaves like a SAP-SAP communication with IDOCS using the XI IDOC adapter interface. Thus, the solution for IDOC data structure and outbound/inbound enhancement and modification can be applied in a similar way as described above in section 1, in particular notes 991763, 656350 and references. Some parts of the solution in SAP note 656350 are already contained in the XI 3.0 IDOC adapter framework so that the amount of modifications is reduced, see SAP Note&#160;&#160;656350 and for more information.<br />The MDMP IDOC is sent from R/3 to the XI IDOC adapter using the described adjustments and then converted to the XI XML internal message in Unicode for processing in the Ingertaion Engine. XI then sends this Unicode message to the outb&#243;und adapter for the partner system which receives the Unicode message.<br /><br /><strong>Case 2: SAP R/3 MDMP receives an IDOC via XI from a Unicode partner</strong><br /><br />This case is more difficult so that a general solution requires individual analysis of the given scenario.<br />As a general solution approach the following steps are recommended:<br /><br />-&#160;&#160;If IDocs are received in/sent from&#160;&#160;the Integration Server<br />&#160;&#160; (which is the assunption here) it is s recommended to avoid the XI<br />&#160;&#160; internal conversion of the IDOC&#160;&#160;to a XI XML message (IDOC<br />&#160;&#160; tunneling). This can be achieved by&#160;&#160;setting the XI Integration<br />&#160;&#160; Engine coonfiguration parameter XML_CONVERSION = 0. However, note<br />&#160;&#160; that this parameter is valid for all incoming&#160;&#160;IDOCS in XI, so that<br />&#160;&#160; incoming IDOCS can only be sent out as IDOCS to other partner<br />&#160;&#160; systems.<br />&#160;&#160; If this parameter cannot be set to 0, the IDOC is comverted to<br />&#160;&#160; a XI XML message in Unicode format for processing in the Integration<br />&#160;&#160; Engine. Note that in this case an individual case-by-case analyis<br />&#160;&#160;and effort is necessary to send this XML message in Unicode format<br />&#160;&#160;to a R/3 MDMP system as IDOC.<br />-&#160;&#160;For sending the IDOC from XI to the R/3 MDMP system follow the<br />&#160;&#160; information and adjustments in section 1 for the ALE scenario<br />&#160;&#160; whereas the XI system is the ALE sender system in this case.<br />-&#160;&#160;If the XI Integration&#160;&#160;Engine coonfiguration parameter<br />&#160;&#160; XML_CONVERSION cannnot be set to 0,&#160;&#160; the IDOC is comverted to<br />&#160;&#160; a XI XML message in Unicode format for processing in the Integration<br />&#160;&#160; Engine. Note that in this case an individual case-by-case analyis<br />&#160;&#160;and effort is necessary to send this XML message in Unicode format<br />&#160;&#160; to a R/3 MDMP system as IDOC.</p>\r\n<p><strong>7.5.2 XI RFC Adapter for connection to R/3 MDMP</strong></p>\r\n<p><br />&#160;&#160; The XI RFC adapter is based in JAVA technology, i.e. t uses the SAP<br />&#160;&#160; JAVA connnector (JCo) internally. Since the JCO can only support<br />&#160;&#160;the messgae transfer between Unicode and single code page systems<br />&#160;&#160; no MDMP&#160;&#160;scenario is supported here.</p>\r\n<p><strong>7.5.3 XI/PI 7.0 - R/3 MDMP</strong><br /><br /></p>\r\n<p>&#160;&#160;In case of XI/PI 7.0 the same statements as for XI 3.0 are valid.<br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D003554)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D028792)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000745030/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000745030/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745030/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745030/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745030/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745030/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745030/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745030/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745030/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Unicode_MDMP_Solutions.pdf", "FileSize": "1908", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000329192004&iv_version=0010&iv_guid=544746518B436C41AB6AA9C73532FD1C"}, {"FileName": "HowToMDMPConnect.pdf", "FileSize": "774", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000329192004&iv_version=0010&iv_guid=9A40E699A83AED4DA6541B5F4181AAC0"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971364", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/971364"}, {"RefNumber": "827808", "RefComponent": "MDM", "RefTitle": "Connection of non-unicode master data client", "RefUrl": "/notes/827808"}, {"RefNumber": "823110", "RefComponent": "SLL-LEG", "RefTitle": "GTS: UNICODE and Multi Display Multi Processing (MDMP)", "RefUrl": "/notes/823110"}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991"}, {"RefNumber": "797815", "RefComponent": "CRM-BTX-BF-IF", "RefTitle": "The CRM_DATAEXCHG_BADI data exchange BAdI", "RefUrl": "/notes/797815"}, {"RefNumber": "794411", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Supported codepages of SAP Java Connector 2.1 and 6.x", "RefUrl": "/notes/794411"}, {"RefNumber": "777944", "RefComponent": "CRM-MW-ADP", "RefTitle": "Message: Different character length in R/3 and CRM", "RefUrl": "/notes/777944"}, {"RefNumber": "747036", "RefComponent": "BC-I18", "RefTitle": "mySAP ERP 2004 Upgrade for R/3 MDMP Customers", "RefUrl": "/notes/747036"}, {"RefNumber": "73606", "RefComponent": "BC-I18", "RefTitle": "Supported Languages and Code Pages", "RefUrl": "/notes/73606"}, {"RefNumber": "734325", "RefComponent": "BC-CUS-TOL-CST", "RefTitle": "Table comparison: MDMP and Unicode", "RefUrl": "/notes/734325"}, {"RefNumber": "729473", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/729473"}, {"RefNumber": "718722", "RefComponent": "SCM-APO-INT", "RefTitle": "Product: CIF transfer of short texts", "RefUrl": "/notes/718722"}, {"RefNumber": "716876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/716876"}, {"RefNumber": "710720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/710720"}, {"RefNumber": "694151", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP Plug-in: Multi-code page ability (3)", "RefUrl": "/notes/694151"}, {"RefNumber": "691585", "RefComponent": "CRM-MW-ADP", "RefTitle": "CRM Unicode / R3 MDMP Connection: User Exits", "RefUrl": "/notes/691585"}, {"RefNumber": "686898", "RefComponent": "CRM-MW-ADP", "RefTitle": "Data exchange with R/3 back end that uses several code pages", "RefUrl": "/notes/686898"}, {"RefNumber": "680524", "RefComponent": "BC-MID-RFC", "RefTitle": "Incorrect code page when you use RFC with SAP GUI connection", "RefUrl": "/notes/680524"}, {"RefNumber": "664833", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP Plug-In: Multi-codepage capability (2)", "RefUrl": "/notes/664833"}, {"RefNumber": "656350", "RefComponent": "BC-MID-ALE", "RefTitle": "Master Data Transfer UNICODE <==> MDMP Systems with ALE", "RefUrl": "/notes/656350"}, {"RefNumber": "651497", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/651497"}, {"RefNumber": "651229", "RefComponent": "BC-I18", "RefTitle": "MDMP Restrictions", "RefUrl": "/notes/651229"}, {"RefNumber": "647495", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/647495"}, {"RefNumber": "643813", "RefComponent": "BW-SYS", "RefTitle": "Composite SAP note - BW Unicode", "RefUrl": "/notes/643813"}, {"RefNumber": "640851", "RefComponent": "BC-XI", "RefTitle": "Additional settings of XI2.0 for multi byte languages", "RefUrl": "/notes/640851"}, {"RefNumber": "639046", "RefComponent": "BC-XI", "RefTitle": "XI2.0 Prerequisites and restrictions for multi-byte system", "RefUrl": "/notes/639046"}, {"RefNumber": "633265", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP PlugIn: Multi-codepage ability", "RefUrl": "/notes/633265"}, {"RefNumber": "613389", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE SAP system group with Unicode systems", "RefUrl": "/notes/613389"}, {"RefNumber": "551344", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion Documentation", "RefUrl": "/notes/551344"}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016"}, {"RefNumber": "547444", "RefComponent": "BC-I18", "RefTitle": "RFC Enhancement for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/547444"}, {"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623"}, {"RefNumber": "540911", "RefComponent": "BC-I18", "RefTitle": "Unicode restrictions for R/3 Enterprise, ECC 5.0, ECC 6.0", "RefUrl": "/notes/540911"}, {"RefNumber": "493791", "RefComponent": "BC-I18", "RefTitle": "RFC with deep structures in an MDMP system", "RefUrl": "/notes/493791"}, {"RefNumber": "417906", "RefComponent": "CRM-MW-ADP", "RefTitle": "User exits for the data exchange with CRM servers", "RefUrl": "/notes/417906"}, {"RefNumber": "381028", "RefComponent": "BC-SRV-COM-MSX", "RefTitle": "Character set problems", "RefUrl": "/notes/381028"}, {"RefNumber": "207101", "RefComponent": "BC-I18", "RefTitle": "Customer programs: Enhancements for MDMP", "RefUrl": "/notes/207101"}, {"RefNumber": "1322715", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode FAQs", "RefUrl": "/notes/1322715"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2684898", "RefComponent": "BC-BW", "RefTitle": "Data transfer between R/3 Non-unicode/Unicode system and BW unicode system", "RefUrl": "/notes/2684898 "}, {"RefNumber": "1990240", "RefComponent": "BC-I18", "RefTitle": "Support of mixed landscapes (Unicode <=> Non-Unicode)", "RefUrl": "/notes/1990240 "}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "656350", "RefComponent": "BC-MID-ALE", "RefTitle": "Master Data Transfer UNICODE <==> MDMP Systems with ALE", "RefUrl": "/notes/656350 "}, {"RefNumber": "73606", "RefComponent": "BC-I18", "RefTitle": "Supported Languages and Code Pages", "RefUrl": "/notes/73606 "}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016 "}, {"RefNumber": "1322715", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode FAQs", "RefUrl": "/notes/1322715 "}, {"RefNumber": "547444", "RefComponent": "BC-I18", "RefTitle": "RFC Enhancement for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/547444 "}, {"RefNumber": "551344", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion Documentation", "RefUrl": "/notes/551344 "}, {"RefNumber": "734325", "RefComponent": "BC-CUS-TOL-CST", "RefTitle": "Table comparison: MDMP and Unicode", "RefUrl": "/notes/734325 "}, {"RefNumber": "613389", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE SAP system group with Unicode systems", "RefUrl": "/notes/613389 "}, {"RefNumber": "540911", "RefComponent": "BC-I18", "RefTitle": "Unicode restrictions for R/3 Enterprise, ECC 5.0, ECC 6.0", "RefUrl": "/notes/540911 "}, {"RefNumber": "777944", "RefComponent": "CRM-MW-ADP", "RefTitle": "Message: Different character length in R/3 and CRM", "RefUrl": "/notes/777944 "}, {"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623 "}, {"RefNumber": "1045465", "RefComponent": "EHS-BD", "RefTitle": "Fehlersuche von EH&S-spezifischen NLS-Problemen", "RefUrl": "/notes/1045465 "}, {"RefNumber": "647495", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/647495 "}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991 "}, {"RefNumber": "686898", "RefComponent": "CRM-MW-ADP", "RefTitle": "Data exchange with R/3 back end that uses several code pages", "RefUrl": "/notes/686898 "}, {"RefNumber": "417906", "RefComponent": "CRM-MW-ADP", "RefTitle": "User exits for the data exchange with CRM servers", "RefUrl": "/notes/417906 "}, {"RefNumber": "680524", "RefComponent": "BC-MID-RFC", "RefTitle": "Incorrect code page when you use RFC with SAP GUI connection", "RefUrl": "/notes/680524 "}, {"RefNumber": "493791", "RefComponent": "BC-I18", "RefTitle": "RFC with deep structures in an MDMP system", "RefUrl": "/notes/493791 "}, {"RefNumber": "794411", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Supported codepages of SAP Java Connector 2.1 and 6.x", "RefUrl": "/notes/794411 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "691585", "RefComponent": "CRM-MW-ADP", "RefTitle": "CRM Unicode / R3 MDMP Connection: User Exits", "RefUrl": "/notes/691585 "}, {"RefNumber": "381028", "RefComponent": "BC-SRV-COM-MSX", "RefTitle": "Character set problems", "RefUrl": "/notes/381028 "}, {"RefNumber": "747036", "RefComponent": "BC-I18", "RefTitle": "mySAP ERP 2004 Upgrade for R/3 MDMP Customers", "RefUrl": "/notes/747036 "}, {"RefNumber": "827808", "RefComponent": "MDM", "RefTitle": "Connection of non-unicode master data client", "RefUrl": "/notes/827808 "}, {"RefNumber": "207101", "RefComponent": "BC-I18", "RefTitle": "Customer programs: Enhancements for MDMP", "RefUrl": "/notes/207101 "}, {"RefNumber": "823110", "RefComponent": "SLL-LEG", "RefTitle": "GTS: UNICODE and Multi Display Multi Processing (MDMP)", "RefUrl": "/notes/823110 "}, {"RefNumber": "651229", "RefComponent": "BC-I18", "RefTitle": "MDMP Restrictions", "RefUrl": "/notes/651229 "}, {"RefNumber": "694151", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP Plug-in: Multi-code page ability (3)", "RefUrl": "/notes/694151 "}, {"RefNumber": "639046", "RefComponent": "BC-XI", "RefTitle": "XI2.0 Prerequisites and restrictions for multi-byte system", "RefUrl": "/notes/639046 "}, {"RefNumber": "797815", "RefComponent": "CRM-BTX-BF-IF", "RefTitle": "The CRM_DATAEXCHG_BADI data exchange BAdI", "RefUrl": "/notes/797815 "}, {"RefNumber": "643813", "RefComponent": "BW-SYS", "RefTitle": "Composite SAP note - BW Unicode", "RefUrl": "/notes/643813 "}, {"RefNumber": "633265", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP PlugIn: Multi-codepage ability", "RefUrl": "/notes/633265 "}, {"RefNumber": "718722", "RefComponent": "SCM-APO-INT", "RefTitle": "Product: CIF transfer of short texts", "RefUrl": "/notes/718722 "}, {"RefNumber": "664833", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP Plug-In: Multi-codepage capability (2)", "RefUrl": "/notes/664833 "}, {"RefNumber": "640851", "RefComponent": "BC-XI", "RefTitle": "Additional settings of XI2.0 for multi byte languages", "RefUrl": "/notes/640851 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 32-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007105&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 64-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007108&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EX2 32-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009585&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EX2 64-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009589&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP224", "SupportPackagePatch": "000224", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP224", "SupportPackagePatch": "000224", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}