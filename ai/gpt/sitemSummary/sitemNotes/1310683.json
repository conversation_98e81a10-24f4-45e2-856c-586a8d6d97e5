{"Request": {"Number": "1310683", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 291, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016740092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001310683?language=E&token=12570F311E79DBBEEF246AF9FA2666E8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001310683", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001310683/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1310683"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.04.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1310683 - Prerequisites BWA Remote Configuration/Verification Check"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have ordered a remote configuration or verification check for SAP NetWeaver BW Accelerator (BWA). In order for the SAP support consultant to deliver this service, several prerequisites must be met. These are described here.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BIA BWA Safeguarding CQC Remote Checks</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have purchased and installed a SAP NetWeaver BW Accelerator. To safeguard the implementation, you have ordered a remote configuration check to be executed shortly before going live and/or a remote verification check to be executed one to two months after going live. These checks have been ordered via your TQM (MaxAttention or Safeguarding customers) or via your Support Advisor or the Support Advisory Center (Enterprise Support customers).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>Common prerequisites:</strong></p>\r\n<p><br />The remote configuration/verification check sessions for SAP NetWeaver BW Accelerator have the following prerequisits in common:</p>\r\n<ul>\r\n<li>Installed BW Accelerator (must be useable from your BW system)</li>\r\n</ul>\r\n<ul>\r\n<li>R/3 support connection to the BW system to which the BW Accelerator is connected.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The user in the BW System needs authorization for at least the following transactions: RSDDBIAMON2, TREXADMIN, SE16, RSDDV, RSRT, SM59, SM51, ST06, SM37, RSBATCH, SDCCN, ST13</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>User name and password for the user should be provided via the secure area.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In order for the support consultant to have access to the user data in the secure area, you could create a customer message on component SV-BO-BI.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>In the BW system, the latest ST-A/PI should be installed, at least ST-A/PI 01K* (see SAP Note 69455). As long as ST-A/PI 01O* is not available, please implement the report attached to SAP Note 1517326.</li>\r\n</ul>\r\n<ul>\r\n<li>A service connection of type TREX/BIA must be established and opened to provide access to the BWA hosts.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Please refer to SAP Note 1058533 on details regarding how to set up and open the TREX/BIA support connection.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The password for the operating system user &lt;sid&gt;adm on the BWA hosts must be provided (in the secure area). &lt;sid&gt; here is the System ID of the BWA, which is in general not the System ID of the BW system.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Please note that if a TREX/BIA support connection is not provided, some parts of the check sessions cannot be performed by the support consultant.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>We recommend delivering the session in your Solution Manager system. For this we need:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>R/3 support connection to Solution Manager, with user and password provided in the secure area.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The Solution Manager must have at least ST-SER 2008_1 implemented.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For ST Release 400, at least SP 17 is required</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Special Prerequisites for the Configuration Check Session</strong></p>\r\n<ul>\r\n<li>In order to make most use of the Configuration Check Session, the BW Accelerator should have been used by your team for some time, e.g. for testing.</li>\r\n</ul>\r\n<ul>\r\n<li>The data that you intend to use with BWA at the time of going live should already have been indexed before the Configuration Check is delivered.</li>\r\n</ul>\r\n<ul>\r\n<li>We recommend delivering the Configuration Check shortly (one or two weeks) before going live.</li>\r\n</ul>\r\n<p><strong>Special Prerequisites for the Verification Check Session</strong></p>\r\n<ul>\r\n<li>At the time of delivery of the verification check session, your BW Accelerator should have been live for several weeks (1-2 months).</li>\r\n</ul>\r\n<ul>\r\n<li>The statistics level for BW statistics must be set to 2 (in transaction RSA1 -&gt; Menu Tools -&gt; Settings for BI Statistics).</li>\r\n</ul>\r\n<ul>\r\n<li>A good time for session delivery is in a week following a period of high reporting activities (e.g. the week following reporting for month-end closing). Please do not schedule the check for a period in which high reporting activities are on-going, because performance checks are executed as part of the remote check and the results may be skewed by heavy system usage.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D040960)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D058464)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001310683/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001310683/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "1517326", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BWA Support Toolkit", "RefUrl": "/notes/1517326"}, {"RefNumber": "1058533", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "TREX/BWA/HANA service connection to customer systems", "RefUrl": "/notes/1058533"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1517326", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BWA Support Toolkit", "RefUrl": "/notes/1517326 "}, {"RefNumber": "1058533", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "TREX/BWA/HANA service connection to customer systems", "RefUrl": "/notes/1058533 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}