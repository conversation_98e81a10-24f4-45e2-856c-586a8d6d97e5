{"Request": {"Number": "1143775", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 292, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016469082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001143775?language=E&token=2BB2CBF0EAC1A9F5036B8F091828BDEC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001143775", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001143775/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1143775"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SVD-SCU"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Content Update"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Delivery (and Planning)", "value": "SV-SMG-SVD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SVD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Content Update", "value": "SV-SMG-SVD-SCU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SVD-SCU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1143775 - SAP Service Content Update"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to deliver an SAP service (EarlyWatch, GoingLive, and so on) in SAP Solution Manager, although the&#160;required ST-SER Support Package&#160;is not installed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Service Content Update</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>With the Service Content Update (SCU), you can execute updates for SAP services in the Solution Manager <strong>without importing ST-SER Support Packages or implementing SAP Notes</strong>. With&#160;default settings, the system automatically updates on a daily basis. <br />The prerequisite for SAP Service Content Update are&#160;<strong>two https connections SAP-SUPPORT_PORTAL and SAP-SUPPORT_PARCELBOX to the SAP Support Backend. </strong>Prerequisite is ST 7.2 SP08&#160;or minimum ST 7.2 SP05 with <a target=\"_blank\" href=\"/notes/2714210\">SAP Note </a><span class=\"urTxtStd\" style=\"white-space: nowrap;\"><a target=\"_blank\" href=\"/notes/2714210\">2714210</a></span> implemented. See&#160;<a target=\"_blank\" href=\"https://support.sap.com/en/alm/solution-manager/sap-support-backbone-update.html\">SAP Support Backbone update</a> for details. Please also implement<span class=\"urTxtStd\" style=\"white-space: nowrap;\"> <a target=\"_blank\" href=\"/notes/2722875\">SAP Note&#160;2722875</a> </span>(valid up to ST 7.2 SP09).<span class=\"urTxtStd\" style=\"white-space: nowrap;\"><br /></span><strong>If you are on ST 7.1 or ST 7.2 below SP05, you cannot receive SCU</strong> (there is no downport of the coding required below SP05). But you can enable such a Solution Manager to sent EWA data to SAP by implementing <a target=\"_blank\" href=\"/notes/2837310\">SAP Note&#160;2837310</a>.&#160;To continuously benefit from the latest SAP service content, let this Solution Manager send the EWA data to SAP. Get the results in the <span class=\"urTxtStd\" style=\"white-space: nowrap;\"><a target=\"WATCHERACTION\" href=\"https://launchpad.support.sap.com/#/ewaworkspace\">SAP EarlyWatch Alert Workspace</a>. (Supported are production and test systems.)</span><span class=\"urTxtStd\" style=\"white-space: nowrap;\"><br /></span></p>\r\n<p>The Service Content Update is based on an ST-SER release in each case. ST-SER upgrades still need to be carried out in the Solution Manager to ensure that you have&#160;up-to-date ST-SER content.</p>\r\n<p>The Service Content Update is&#160;only available and maintained for release ST-SER 7.20. Before the upgrade from ST-SER 701_2010_1 to ST-SER 720, implement <a target=\"_blank\" href=\"/notes/2421718\">SAP Note 2421718</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Warning&#160;for Solution Manager without https connection for SCU:</strong></span><em> </em>Don't delete the SCU download. You can't reload it from SAP. There is no way to re-import it into the Solution Manager. This applies to all Solution Manager below ST 7.2 SP05. On ST-SER 7.20 you are better off without SCU applying the latest Support Package with maintenance with SAP Notes. For&#160;ST-SER 701_2010_1 the last SCU contains more corrections than the last SP plus SAP Notes. Therefore you lose some content when deleting SCU. Any way&#160;ST-SER 701_2010_1 is completely outdated.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Administration of SCU background processing</strong></span></p>\r\n<p>The <em>SAP Service Content Update</em> is performed by one of the Solution Manager standard background jobs described in <a target=\"_blank\" href=\"/notes/894279\">SAP Note </a><span lang=\"EN-IE\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: #1f497d; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-IE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/894279\">894279</a></span>. The name of the job is <em>&#65279;&#65279;SM:SERVICE CONTENT UPDATE&#65279;</em> &#65279;and it runs report <em>RAGS_DSWP_SERV_CONTENT_UPDATE</em>. The job is scheduled in the <em>Basic Setup&#160;</em>of the Solution Manager&#160;in step <em>Configure Automatically </em>with activity <em>Schedule Solution Manager Background Jobs. </em>This job can also be scheduled and configured from the Solution Manager 7.2 launchpad using the tile <em>Configuration - Service Content Update</em><em>.&#160;</em>Alternatively, the <em>SAP Service Content Update</em> can also be selected in the <em>SAP Engagement and Service Delivery</em> work center as a '<em>Common Tasks</em>'. The link is '<em>Schedule content update</em>'.</p>\r\n<p>In the SCU administration UI you must set the indicator for the batch job, which then schedules the report RAGS_DSWP_SERV_CONTENT_UPDATE.<br /><br />Step by step description of the procedure in the SAP Solution Manager:</p>\r\n<ul>\r\n<li>Open&#160;the <em>Service Content Update - Configuration </em>by either</li>\r\n<ul>\r\n<li>From the Solution Manager 7.2 launchpad start the tile&#160;<em>Configuration - Service Content Update.</em></li>\r\n<li><em>or</em> Start the <em>'SAP Engagement and Service Delivery'</em> work center. Use the link '<em>Schedule content update</em>' that can be found in the section '<em>Common Tasks</em>'.</li>\r\n</ul>\r\n<li>Make the following settings:</li>\r\n<ul>\r\n<li>You can determine which period is used to check relevant services. 14 days into the past and 35 days into the future are checked by default. For all services that are in this time window, the system checks if updates are available from SAP.</li>\r\n<li>You can set the package size for data transfer. This parameter is now irrelevant for the https connection to SAP-SUPPORT_PARCELBOX. (The parameter was used for RFC protocol used formerly.)</li>\r\n<li>You can configure which steps the program executes. The following tasks can be configured to run automatically when the job runs:</li>\r\n<ul>\r\n<li>Should the system automatically&#160;check for new updates?</li>\r\n<li>If updates exist, should the download be executed?</li>\r\n<li>Should the&#160;new updates be activated?</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>Managing SCU interactively</strong></span></p>\r\n<p>You can control the steps of <em>Service Content Update</em> manually. You need to do so, if one of the SCU steps is not configured to run automatically in background. You can choose between a UI in browser or transaction AGS_UPDATE in SAPgui. In browser, open&#160;the work center 'SAP Engagement and Service Delivery', call the function 'Services' (in older versions 'SAP Delivered Services'). Choose button&#160;'Content Update'. The UI offers three steps, which correspond to the three settings for background processing:</p>\r\n<ol>\r\n<li>\r\n<p>Check for Updates</p>\r\n</li>\r\n<ul>\r\n<li>\r\n<p>If SAP provides relevant changes for the services to be delivered, you can proceed with step 2.</p>\r\n</li>\r\n</ul>\r\n<li>Download Updates</li>\r\n<ul>\r\n<li>Depending on the speed of the connection to the SAP backend, this step may take a long time. This is the case, in particular, if the update is executed for the first time.</li>\r\n</ul>\r\n<li>Activate Updates</li>\r\n<ul>\r\n<li>After the successful download, the updates can be activated.</li>\r\n</ul>\r\n</ol>\r\n<p>Furthermore you have access to detailed information about the <em>Service Content Update</em>:</p>\r\n<ul>\r\n<li>You can display protocols showing when downloads were downloaded, applied, or rolled back.</li>\r\n<li>You can display details of the applied updates. This shows object level information about update's content.</li>\r\n<li>You can display details of available updates.</li>\r\n<li>You can display&#160;packages (these are service sessions, aka bundles) are considered by the SCU&#160;in this Solution Manager. The purpose of this configuration is to limit the required download to those packages, which are used in the Solution Manager.</li>\r\n</ul>\r\n<p>The same functionality is available in transaction <em>AGS_UPDATE </em>(running in SAPgui). <em>AGS_UPDATE </em>offers additional functionality which is available only in this transaction.&#160;<br />Start&#160;transaction AGS_UPDATE or&#160;run report RAGS_DSWP_SERV_CONTENT_UPDATE directly. The above detailed descriptions of these three steps are also valid for <em>AGS_UPDATE</em>.</p>\r\n<ol>\r\n<ul>\r\n<li>Step 1: Check for updates&#160;</li>\r\n<li>Step 2: Download updates&#160;</li>\r\n<li>Step 3: Apply updates</li>\r\n</ul>\r\n</ol>\r\n<p>Using the option \"Tools &amp; General settings\", you can make the settings for background processing described above.</p>\r\n<p>Besides displaying protocols and details like above, you also can:</p>\r\n<ul>\r\n<li>You can configure, which services (=bundles), should be included in SCU. (available in menu <em>Edit</em>)</li>\r\n<li>You can rollback updates completely or to a specific point in time. (available in menu <em>Edit</em>)</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Best Practices</strong></p>\r\n<p>SAP recommends that you schedule the report daily with automatic check, download, and activation of updates.&#160;However, in any case, you should schedule the report regularly. This is the only way to ensure that the SAP services are automatically kept at the status required for the delivery.<br />If you do not run the report regularly, the content of services updated at one point becomes outdated until the report is run again. Even importing ST-SER Support Packages no longer has an effect on the content of these services.</p>\r\n<p><br /><strong>Known Problems</strong></p>\r\n<ol>\r\n<li>Although SCU is active, services are processed with the \"normal\" version (version numbers below 50,000) instead of the SCU version (version numbers above 50,000).<br /><br />Please&#160;implement <a target=\"_blank\" href=\"/notes/2722875\">SAP Note&#160;2722875</a> (valid up to ST 720 SP09). You can check if you suffer from this issue as follows: transaction <em>SE16</em> -&gt; table <em>DSVASSESSPACK</em> -&gt; for <em>BUNDLE_ID</em> enter <em>EW_ALERT</em> and display all entries. If there is <strong>no</strong> entry with <em>BUNDLE_VERSNR</em> &gt; 50,000, the issue is present.&#160;&#160;A check to detect this error is rolled out with RTCCTOOL. For details, see <a target=\"_blank\" class=\"external-link\" href=\"/notes/2917960\" rel=\"nofollow\">SAP KBA 2917960</a>.<br /><br /></li>\r\n<li>SAP Service Content Update&#160;obviously does not take the planned service into account.<br /><br />Check whether the planned delivery date of the service session is in the period that is taken into account. If this is not the case, you can adjust the period (see above). For other problems with the service content update, check <a target=\"_blank\" href=\"/notes/1491227\">SAP Note 1491227</a> (only valid&#160;as of ST 700 Support Package 23; below check&#160;<a target=\"_blank\" href=\"/notes/1317901\">SAP Note 1317901</a>).</li>\r\n</ol>\r\n<p><br /><strong>FAQs</strong></p>\r\n<p>On which component can I report issues with the SAP Service Content Update?</p>\r\n<ul>\r\n<li>For issues with the SCU process please open an incident on SV-SMG-SVD-SCU. For issues with the content&#160;of the service sessions please open an incident on SV-SMG-SER (or a sub-component like SV-SMG-SER-EWA).</li>\r\n</ul>\r\n<p>Which changes are executed by SAP Service Content Update in the system?</p>\r\n<ul>\r\n<li>SAP Service Content Update updates the services that are carried out in your Solution Manager. You can display the changes contained in the update after the download.</li>\r\n</ul>\r\n<p>Which check content is delivered with SAP Service Content Update?</p>\r\n<ul>\r\n<li>For EarlyWatch Alert important changes are documented in&#160;<a target=\"_blank\" class=\"external-link\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/sm/3518047670.html?locale=en-US\" rel=\"nofollow\">Service Content Update (SCU) Release Information</a>.</li>\r\n</ul>\r\n<p><br />Are the services that are used by customers also affected by SAP Service Content Update?</p>\r\n<ul>\r\n<li>There are a number of services that are not updated by SAP Service Content Update. These are Self-Services Business Process Monitoring, Central System Administration, Service Level Reporting, Setup System Monitoring, among others.</li>\r\n</ul>\r\n<ul>\r\n<li>The EarlyWatch Alert is always included in SAP Service Content Update.</li>\r\n</ul>\r\n<ul>\r\n<li>You can deactivate the content update for certain services. To do this, call the report RAGS_DSWP_DEACTIVATE_BUNDLES and deactivate the services that you do not want to be updated by SAP Service Content Update in the future.</li>\r\n</ul>\r\n<p><br />Can I&#160;rollback updates?</p>\r\n<ul>\r\n<li>Yes. You can&#160;rollback changes made by SAP Service Content Update. Under \"Tools &amp; General settings\", you can choose \"Edit\" -&gt; \"(In)activate Updates\" to select an earlier time stamp to which the services are to be reset. For a detailed description see <a target=\"_blank\" href=\"/notes/1814723\">SAP Note 1814723</a>.</li>\r\n</ul>\r\n<p>What is contained in the Service Content Update?</p>\r\n<ul>\r\n<li>When an ST-SER support package is available, it also gets integrated to the Service Content Update and is published in SCU with a few days delay.&#160;Many corrections on top of the latest SP are included in SCU long before they become available in the next SP.&#160;The SCU not only covers corrections to ABAP coding&#160;(which can be made available with correction instruction via Notes Assistant for Solution Manager not using SCU), but also corrections requiring changing table entries, for which no automatic correction instructions exist.</li>\r\n</ul>\r\n<p>Can you implement ST-SER corrections using the Note Assistant (SNOTE) if the service content update is used?</p>\r\n<ul>\r\n<li>No. If a service uses the service content update, corrections are provided by the service content update. SAP notes for ST-SER frequently specify whether and when the correction is available with the service content update. If nothing is specified, at the latest, the correction is also included in the service content update when the relevant ST-SER Support Package is released.<br />Technical background: The service content update uses (and modifies) copies of the ST-SER programs (namespace RDSVAS*) in a separate namespace (/1AGS/*).</li>\r\n</ul>\r\n<p>Which authorizations are required to execute SAP Service Content Update?</p>\r\n<ul>\r\n<li>The system checks the authorization object SM_CNT_UPD. When you execute SAP Service Content Update, the system makes a distinction between the following activities:<br />10: Check updates<br />20: Download updates<br />30: Activate downloaded updates<br />The roles SAP_OP_DSWP, SAP_SV_SOLUTION_MANAGER, and SAP_SOLUTION_MANAGER_ONSITE, among others, contain these authorizations.</li>\r\n</ul>\r\n<p>Which time stamp does the service content update that is active in Solution Manager have?</p>\r\n<ul>\r\n<li>If the EarlyWatch Alert uses the service content update, the time stamp is displayed on the cover sheet of the Microsoft Word report. The time stamp is the largest value in the column TIMESTAMP of the table DSWPTIMESTAMPS (column STATUS must contain 'A').<br />The time stamp refers to the point in time when the update was created at&#160;SAP. Therefore it can be compared between different Solution Manager systems. It can also be seen in transaction AGS_UPDATE in the <em>Protocols </em>as column&#160;<em>UTC timestamp of delta </em>and in the <em>Applied updates&#160;</em>as <em>timestamp</em>. Date and time when an update was downloaded and activated can be checked in the protocols accessible in transaction <em>AGS_UPDATE</em>.</li>\r\n</ul>\r\n<p>How can I check the schedule for the next <em>Service Content Update</em>?</p>\r\n<ul>\r\n<li>Corrections included in the <em>Service Content Update </em>are tested and released when&#160;the tests finished successfully. To avoid delay&#160;there is no schedule planned in advance. It's common that new content is released every 2nd week. Due to the test cycle, the frequency is limited and typically a week passes before new content could be released.</li>\r\n</ul>\r\n<p>Do I still have to import new ST-SER releases if the services can be updated using the new update procedure?</p>\r\n<ul>\r\n<li>Yes. Practically, this does not matter, as for many years the ST-SER release 7.20 is supported and no new release is planned.<br />ST-SER release upgrades are dependent on other software components such as ST and SAP_ABA. As these components cannot be covered by SAP Service Content Update, ST-SER upgrades must be executed in the traditional manner.</li>\r\n</ul>\r\n<p>How can I upgrade&#160;ST-SER?</p>\r\n<ul>\r\n<li>Transaction SAINT is the add-on Installation Tool to be used for&#160;installing add-ons like&#160;ST-SER. Also see SAP Note&#160;<a target=\"_blank\" href=\"/notes/569116\">569116</a>.</li>\r\n</ul>\r\n<p>In transaction AGS_UPDATE a message '<em>New ST-SER release available</em>' is displayed. What does this mean?</p>\r\n<ul>\r\n<li>Typically, this message means, that&#160;your ST release supports a higher ST-SER release, for which the Service Content Update is also available. For available&#160;ST-SER releases see SAP Note&#160;<a target=\"_blank\" href=\"/notes/569116\">569116</a>. The releases covered by SCU are described at the top of this SAP Note.</li>\r\n</ul>\r\n<p><strong>Change Log</strong></p>\r\n<p>After November 30th 2020,<strong>&#160;</strong>you cannot receive SCU&#160;on&#160;ST 710 or ST 720 below SP05. SCU requires https to SAP Support Backbone.</p>\r\n<p>After January 8th 2020</p>\r\n<ul>\r\n<li>you can receive the SCU with RFC only if you have entered a technical communication user in RFC destination SAPOSS, and only for a limited time period due to an exceptional allowance for RFC communication</li>\r\n<li>the SCU for release ST-SER 720 is permanently available only through https connections (no more by RFC).</li>\r\n<li>the SCU for release ST-SER 701_2010_1 is no more available at all (don't delete it).</li>\r\n</ul>\r\n<p>The SCU for ST-SER 720 is available since 1/25/2017.</p>\r\n<p>For ST-SER 701_2010_1, the SCU contains the status of the last Support Package (SP28), and in addition, further corrections that&#160;never were&#160;delivered with a Support Package.</p>\r\n<p>The Service Content Update for releases ST-SER 701_2010_1 and ST-SER 700_2008_2 is no longer maintained.</p>\r\n<p>As of ST 400 Support Package 19, SAP Service Content Update is also integrated in the SAP Engagement and Service Delivery work center: the SCU can be selected as a 'Common Task'. The link is 'Schedule content update'. In the application, you can use a flag to schedule the batch job that schedules the report RAGS_DSWP_SERV_CONTENT_UPDATE in the same way as transaction AGS_UPDATE.</p>\r\n<p>As of ST 400 Support Package 18, the SAP Service Content Update can be called via transaction AGS_UPDATE.</p>\r\n<p><span style=\"font-size: 14px;\">In ST 400 Support Package 16 and Support Package 17, the system checks the authorization for importing workbench requests: Object S_TRANSPRT, field TTYPE 'DTRA', activity 60. As of SP 18 the authorization check listed in the FAQ is valid.</span></p>\r\n<p>As of ST 400 Support Package 16, the SAP Service Content Update can be executed via the report RAGS_DSWP_SERV_CONTENT_UPDATE.</p>\r\n<p><span style=\"font-size: 14px;\">The EarlyWatch Alert is included in&#160;SAP Service Content Update as of May 2010 if at least ST-SER 701_2008_2 is installed.</span></p>\r\n<p>&#160;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SER (SAP Support Services)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028075)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023762)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001143775/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001143775/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2917960", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Silent Deactivation of Service Content Update Causes Regression to EarlyWatch Alert Content", "RefUrl": "/notes/2917960"}, {"RefNumber": "1814723", "RefComponent": "SV-SMG-SER", "RefTitle": "How to Reset Service Content on SAP Solution Manager 7.0 and higher [Video]", "RefUrl": "/notes/1814723"}, {"RefNumber": "894279", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Background processing in SAP Solution Manager", "RefUrl": "/notes/894279"}, {"RefNumber": "2722875", "RefComponent": "SV-SMG-SER", "RefTitle": "Recommended corrections to resolve issues with the new communication channel in Service content update.", "RefUrl": "/notes/2722875"}, {"RefNumber": "2714210", "RefComponent": "SV-SMG-SER", "RefTitle": "New communication channel to SAP Backbone for Service Content Update.", "RefUrl": "/notes/2714210"}, {"RefNumber": "2421718", "RefComponent": "SV-SMG-OP", "RefTitle": "CONNE_IMPORT_WRONG_COMP_TYPE On Running Activate Service Content Update", "RefUrl": "/notes/2421718"}, {"RefNumber": "1909477", "RefComponent": "SV-SMG-SER", "RefTitle": "\"READ_FROM_DATAPACK Error ...\" when saving Systems in scope", "RefUrl": "/notes/1909477"}, {"RefNumber": "1892593", "RefComponent": "SV-SMG-SER", "RefTitle": "Preparing Support Services for SAP HANA Scenarios", "RefUrl": "/notes/1892593"}, {"RefNumber": "1865327", "RefComponent": "SV-SMG-SER", "RefTitle": "Report not generated correctly (BPPO)", "RefUrl": "/notes/1865327"}, {"RefNumber": "1860405", "RefComponent": "SV-SMG-SER", "RefTitle": "System_landscape : object not bound", "RefUrl": "/notes/1860405"}, {"RefNumber": "1837896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1837896"}, {"RefNumber": "1766641", "RefComponent": "SV-SMG-SER", "RefTitle": "Sending of notifications in the CQC GLS", "RefUrl": "/notes/1766641"}, {"RefNumber": "1728978", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service Transport Execution Analysis for Project", "RefUrl": "/notes/1728978"}, {"RefNumber": "1634662", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1634662"}, {"RefNumber": "1604255", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1604255"}, {"RefNumber": "1491227", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "Adjustments to service content update from ST 700 SP 23", "RefUrl": "/notes/1491227"}, {"RefNumber": "1437678", "RefComponent": "SV-SMG-SER", "RefTitle": "Sammelhinweis BW/SBOP Service Sessions ST-SER 701_2010_1", "RefUrl": "/notes/1437678"}, {"RefNumber": "1317901", "RefComponent": "SV-SMG-OP", "RefTitle": "Adjustments to service content update up to ST 700 SP 22", "RefUrl": "/notes/1317901"}, {"RefNumber": "569116", "RefComponent": "SV-SMG-SER", "RefTitle": "Release strategy for Solution Manager Service Tools (ST-SER)", "RefUrl": "/notes/569116"}, {"RefNumber": "2837310", "RefComponent": "SV-SMG-SDD", "RefTitle": "Connecting Legacy Systems Like Solution Manager 7.1 with https to SAP Support Backbone", "RefUrl": "/notes/2837310"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2732608", "RefComponent": "SV-SMG-SER", "RefTitle": "How to replace the Service Content (AGS_UPDATE)", "RefUrl": "/notes/2732608 "}, {"RefNumber": "2917960", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Silent Deactivation of Service Content Update Causes Regression to EarlyWatch Alert Content", "RefUrl": "/notes/2917960 "}, {"RefNumber": "1991613", "RefComponent": "SV-SMG-MON-SLR", "RefTitle": "KPIs missing in monthly Service Level Reports for February 2014", "RefUrl": "/notes/1991613 "}, {"RefNumber": "2439537", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EWA Cannot be Created in Japanese, Chinese, etc. on ST-SER 720 SP06 - Solution Manager", "RefUrl": "/notes/2439537 "}, {"RefNumber": "2725336", "RefComponent": "SV-SMG-SER", "RefTitle": "SM:SERVICE CONTENT UPDATE job triggers Runtime Errors.", "RefUrl": "/notes/2725336 "}, {"RefNumber": "1361506", "RefComponent": "SV-SMG-SER", "RefTitle": "Service Content Update - FAQ for ST-SER Developers", "RefUrl": "/notes/1361506 "}, {"RefNumber": "2125970", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Correction available with SAP Service Content Update", "RefUrl": "/notes/2125970 "}, {"RefNumber": "1610847", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EWA for SAP Solution Manager", "RefUrl": "/notes/1610847 "}, {"RefNumber": "1646341", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert for SAP BusinessObjects IDD / EIM", "RefUrl": "/notes/1646341 "}, {"RefNumber": "1321295", "RefComponent": "SV-SMG-SER", "RefTitle": "Enterprise Support Report", "RefUrl": "/notes/1321295 "}, {"RefNumber": "1837896", "RefComponent": "SV-SMG-SER", "RefTitle": "Self-service PSLE report and ES report", "RefUrl": "/notes/1837896 "}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308 "}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155 "}, {"RefNumber": "1074808", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Change and Transport Analysis Session: Requirements", "RefUrl": "/notes/1074808 "}, {"RefNumber": "1525473", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Long Processing Time For EWA Due to BW Access", "RefUrl": "/notes/1525473 "}, {"RefNumber": "1892593", "RefComponent": "SV-SMG-SER", "RefTitle": "Preparing Support Services for SAP HANA Scenarios", "RefUrl": "/notes/1892593 "}, {"RefNumber": "1865327", "RefComponent": "SV-SMG-SER", "RefTitle": "Report not generated correctly (BPPO)", "RefUrl": "/notes/1865327 "}, {"RefNumber": "1491227", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "Adjustments to service content update from ST 700 SP 23", "RefUrl": "/notes/1491227 "}, {"RefNumber": "1559664", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-SER 701_2010_1: PI/XI Corrections Collection", "RefUrl": "/notes/1559664 "}, {"RefNumber": "1484124", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Security Optimization Self Service - Prerequisites", "RefUrl": "/notes/1484124 "}, {"RefNumber": "1442799", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Enterprise Support Report", "RefUrl": "/notes/1442799 "}, {"RefNumber": "1159758", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Volume Management: Central Preparation Note", "RefUrl": "/notes/1159758 "}, {"RefNumber": "1621722", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service - Transport Execution Analysis", "RefUrl": "/notes/1621722 "}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981 "}, {"RefNumber": "1358253", "RefComponent": "SV-SMG-SER", "RefTitle": "Checks for JAVA workload missing in EP GL Session", "RefUrl": "/notes/1358253 "}, {"RefNumber": "160777", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP BI/BW", "RefUrl": "/notes/160777 "}, {"RefNumber": "1320620", "RefComponent": "SV-SMG-SER", "RefTitle": "Prerequisites for GoingLive Check for Solutions", "RefUrl": "/notes/1320620 "}, {"RefNumber": "1451158", "RefComponent": "SV-SMG-SER", "RefTitle": "Various Bugfixes for Sizing Automation in ST-SER 701_2010_1", "RefUrl": "/notes/1451158 "}, {"RefNumber": "1483988", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-SER: MSG_AVG_NET_TIME not found in PI/XI session", "RefUrl": "/notes/1483988 "}, {"RefNumber": "1504004", "RefComponent": "SV-SMG", "RefTitle": "KPI - Measurement Platform Setup in the SAP Solution Manager", "RefUrl": "/notes/1504004 "}, {"RefNumber": "1507629", "RefComponent": "SV-SMG-SER", "RefTitle": "Preparing BOE XI 3.1 For Services In Solution Manager", "RefUrl": "/notes/1507629 "}, {"RefNumber": "1510956", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EWA: section 'ST-PI and ST-A/PI Plug-Ins' - SPs for ST-A/PI", "RefUrl": "/notes/1510956 "}, {"RefNumber": "1529948", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert: SAP HANA in EWA Services", "RefUrl": "/notes/1529948 "}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951 "}, {"RefNumber": "1663259", "RefComponent": "SV-SMG-SER", "RefTitle": "New Procedure: SAP Security Optimization Self Service", "RefUrl": "/notes/1663259 "}, {"RefNumber": "1701887", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-SER 701_2010_1 Rel.SP14:Verification Session (new) for PI", "RefUrl": "/notes/1701887 "}, {"RefNumber": "1492289", "RefComponent": "SV-SMG-SER", "RefTitle": "SOS: chapter \"Human Resources\" (HR) is missing", "RefUrl": "/notes/1492289 "}, {"RefNumber": "700518", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Service Sessions: How To Do Error Analysis", "RefUrl": "/notes/700518 "}, {"RefNumber": "1614130", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert Fails On ST-SER 701_2010_1 SP07 and Higher", "RefUrl": "/notes/1614130 "}, {"RefNumber": "1470008", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert - Issues in ST-SER 701_2010_1", "RefUrl": "/notes/1470008 "}, {"RefNumber": "1608457", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert Session Not Sent To SAP", "RefUrl": "/notes/1608457 "}, {"RefNumber": "1611503", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert: Fixes For ST-SER 701_2010_1 SP09", "RefUrl": "/notes/1611503 "}, {"RefNumber": "1589905", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert: Corrections To ST-SER 701_2010_1 SP08 ff", "RefUrl": "/notes/1589905 "}, {"RefNumber": "1592544", "RefComponent": "SV-SMG-SER", "RefTitle": "Earlywatch Alert: Runtime Errors BCD_ZERODIVIDE", "RefUrl": "/notes/1592544 "}, {"RefNumber": "1488116", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert: Run time error CONVT_NO_NUMBER", "RefUrl": "/notes/1488116 "}, {"RefNumber": "1606093", "RefComponent": "SV-SMG-SER", "RefTitle": "Short dump 'SAP massprocessing system'", "RefUrl": "/notes/1606093 "}, {"RefNumber": "1610241", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Service Preparaion Note", "RefUrl": "/notes/1610241 "}, {"RefNumber": "1297091", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert - Issues in ST-SER 701_2008_2", "RefUrl": "/notes/1297091 "}, {"RefNumber": "1317901", "RefComponent": "SV-SMG-OP", "RefTitle": "Adjustments to service content update up to ST 700 SP 22", "RefUrl": "/notes/1317901 "}, {"RefNumber": "1274306", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Solution Transition Assessment - Preparation Note", "RefUrl": "/notes/1274306 "}, {"RefNumber": "1355073", "RefComponent": "SV-SMG-SER", "RefTitle": "STA: Corrections for Solution Transition Assessment", "RefUrl": "/notes/1355073 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "720", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL436", "URL": "/supportpackage/SAPKITL436"}, {"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL426", "URL": "/supportpackage/SAPKITL426"}, {"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL429", "URL": "/supportpackage/SAPKITL429"}, {"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL432", "URL": "/supportpackage/SAPKITL432"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}