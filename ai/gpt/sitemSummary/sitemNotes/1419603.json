{"Request": {"Number": "1419603", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 240, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016946422017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001419603?language=E&token=2CF88F189A1D3F18ED28EA5C8C26F7B5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001419603", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001419603/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1419603"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-SLD"}, "SAPComponentKeyText": {"_label": "Component", "value": "System Landscape Directory / Component Repository"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "System Landscape Directory / Component Repository", "value": "BC-CCM-SLD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-SLD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1419603 - Missing ABAP SAP Central Services (ASCS) instance in SLD/LMDB"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>\r\n<li>\r\n<div style=\"margin-right: 0px;\">In Content Maintenance (CIM Instances) in the user interface (UI) of the System Landscape Directory (SLD), the BC Message Server (SAP_BCMessageServer) is not linked to a BC central service instance (SAP_BCCentralServiceInstance).</div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">The following error message is displayed in the SAP Solution Manager setup (\"Managed Systems Configuration -&gt; Check Prerequisites\"):</div>\r\n</li>\r\n</ol>\r\n<p style=\"padding-left: 60px;\">\"[..] Technical System must have a Central Service Instance. [..]\"</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>System Landscape Directory, SLD, SAP_BCCentralServiceInstance, SCS, ASCS, ABAP SAP Central Services, message port, enqueue port, enqPort, msgHttpPort, msgPort, SAPni, SLD data supplier service, AS Java, Application Server Java</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>An SAP system has an ASCS instance, that is, an AS ABAP instance, which contains only the enqueue server and the message server. This technical instance is missing in the LMDB and the SLD if only the ABAP data collector (transaction RZ70) is configured.<br /><br /><strong><strong>As of</strong></strong><strong> SAP Kernel Version 7.20</strong>, the SAP startup framework (sapstartsrv)&#x00A0;registers the ASCS instance in the SLD.&#x00A0;The configuration takes place by calling the executable sldreg[.exe] in the directory /usr/sap/&lt;SAPSID&gt;/SYS/exe/uc/&lt;platform&gt; (UNIX) or X:\\usr\\sap\\&lt;SAPSID&gt;\\SYS\\exe\\uc\\&lt;platform&gt; (Windows):</p>\r\n<p>&#x00A0;<em>sldreg -configure slddest.cfg -usekeyfile</em></p>\r\n<p>The connection parameters for the SLD are queried interactively. Use a separate technical user for each system in the SLD. This prevents the occurrence of deadlocks in system registrations if the connection data is incorrect. After you confirm the entries, the following two files are created in the current directory: \"slddest.cfg\" and \"slddest.cfg.key\". The latter file contains the key for encrypting the contents of \"slddest.cfg\". Protect the key file as much as possible from unauthorized access.</p>\r\n<div>\r\n<p><strong>Place the generated files \"slddest.cfg\" and \"slddest.cfg.key\" in the directory /usr/sap/&lt;SAPSID&gt;/SYS/global (UNIX)&#x00A0;or X:\\usr\\sap\\&lt;SAPSID&gt;\\SYS\\global (Windows).</strong></p>\r\n</div>\r\n<p>Following the configuration, open the context menu for the SCS node in the SAP MMC and choose \"All Tasks -&gt; Restart Service\". The SAP startup service then restarts and registers the ASCS in the SLD. The ASCS instance itself is not restarted, and so the system continues to operate uninterrupted. You can achieve the same result using the command \"<em>sapcontrol -nr NN -function RestartService</em>\".</p>\r\n<p>For details on the configuration of \"sldreg\", see SAP Note 1018839. For details on SLD registration in \"sapstartsrv\", see SAP Note 1717846.</p>\r\n<p>Only use the manual procedure described below if the ASCS instance cannot be registered in the SLD using \"sapstartsrv\"/\"sldreg\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>SAP recommends the automatic registration of the ASCS instance in the SLD.&#x00A0;Only if you cannot upgrade to SAP Kernel Version 7.20 or higher, you can also enter the missing information for the ASCS instance and its ports in the Solution Manager UI (Solution Manager Version 7.1 SP5 or higher). (Transaction LMDB -&gt; select technical system ABAP -&gt; \"Technical Instances\" node -&gt; \"Central Service\" tab.)<br /><br />Alternatively, you can also take the manual steps below to create the missing instances on the SLD UI.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong>Required information for ASCS</strong></p>\r\n<p>To create the missing instances completely, you require additional information from the sending AS ABAP. This information is available in the profiles, for example, in the ASCS profile or the DEFAULT profile.<br /><br />The following profile parameters are important for further processing:</p>\r\n<ul>\r\n<li>&lt;SID&gt; = <strong>SAPSYSTEMNAME</strong></li>\r\n</ul>\r\n<ul>\r\n<li>&lt;No&gt; = <strong>SAPSYSTEM</strong><br />(As defined in the ASCS profile.)</li>\r\n</ul>\r\n<ul>\r\n<li>&lt;Host&gt; = <strong>rdisp/mshost</strong><br />(Note: For use in the SLD, the host name can be used only without domain names; the host name must always be converted to lowercase letters. Example: \"TestHost.sap.corp\" must be converted to \"testhost\".)</li>\r\n</ul>\r\n<ul>\r\n<li>&lt;msgPort&gt; = <strong>rdisp/msserv_internal</strong> (or <strong>rdisp/msserv</strong>)</li>\r\n</ul>\r\n<ul>\r\n<li>&lt;enqPort&gt; = <strong>enque/encni/port</strong><br />(If the port is not set explicitly in the profile, it is generated from sapdp&lt;No&gt;. For example, &lt;No&gt; = 20 results in &lt;enqPort&gt; = sapdp20.)</li>\r\n</ul>\r\n<ul>\r\n<li>&lt;msgHttpPort&gt; = <strong>ms/server_port_0 = PROT=HTTP, PORT=</strong></li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p><strong>Creating the data on the SLD UI</strong></p>\r\n<p>You require a user with administration authorization for the SLD.<br /><br />Note the following: For the classes, CIM (Common Information Model) names are used in the following. The possible display names are specified in brackets.<br /><br />The new instances are created under CIM Instances (up to Version 7.02 Administration -&gt; Content Maintenance) in the SLD UI.<br /><br />1.a. Create a new SAP_BCCentralServiceInstance (BC central service instance).<br />key props:<br />CreationClassName = SAP_BCCentralServiceInstance<br />Name = &lt;SID&gt;.SystemHome.&lt;Host&gt;.ServiceInstanceID.&lt;No&gt;<br />normal props:<br />Caption = Central Service Instance &lt;No&gt; of &lt;SID&gt; on &lt;Host&gt;<br />Description = Manually created by SAP Support.<br />SAPSystemName = &lt;SID&gt;<br />ServiceInstanceID = &lt;No&gt;<br />SystemHome = &lt;Host&gt;<br /><br />After you save the new SAP_BCCentralServiceInstance from point 1.a., click the \"Associations\" tab and choose \"New Association\". (Up to Version 7.02, you select the new instance in the list of SAP_BCCentralServiceInstance after saving and choose \"Associated Instances\".) Choose the association type SAP_BCCentralServiceMessageServer (BC Central Service Message Server).&#x00A0;Choose the message server of the relevant AS ABAP and then choose \"Associate\".<br /><br />1.b. Create the association SAP_BCCentralServiceMessageServer (BC central service message server) from the new SAP_BCCentralServiceInstance (BC Central Service Instance) for the SAP_BCMessageServer (BC Message Server) &lt;SID&gt;.HostName.&lt;Host&gt;.ServiceName.sapms&lt;SID&gt;.<br /><br />2.a. Create the computer system for the new SAP_BCCentralServiceInstance (BC Central Service Instance).<br />Note: First check that the \"SAP_ComputerSystem\" (computer system) has not already been reported to the SLD by a data supplier. If this is the case, this step does not apply.<br />key props:<br />CreationClassName = SAP_ComputerSystem<br />Name = &lt;Host&gt;<br />normal props:<br />Caption = &lt;Host&gt;<br /><br />2.b. Create the association SAP_BCCentralServiceInstanceHost (BC Central Service Instance Host) from the new SAP_BCCentralServiceInstance (BC Central Service Instance) for the SAP_ComputerSystem (computer system); name = &lt;Host&gt;<br /><br />3.a. Create a new SAP_IPServicePort (TCP/IP service port)<br />key props:<br />CreationClassName = SAP_IPServicePort<br />Name = msgPort<br />SystemCreationClassName = SAP_BCCentralServiceInstance<br />SystemName = &lt;SID&gt;.SystemHome.&lt;Host&gt;.ServiceInstanceID.&lt;No&gt;<br />normal props:<br />Caption = msgPort<br />Description = Manually created by SAP Support.<br />PortNumber = &lt;msgPort&gt;<br />Protocol = SAPni<br /><br />3.b  Create the association SAP_BCCentralServiceMessagePort (BC central service message port) of the SAP_IPServicePort (TCP/IP service port) for the SAP_BCCentralServiceInstance (BC central service instance).<br /><br />4.a. Create a new SAP_IPServicePort (TCP/IP service port)<br />key props:<br />CreationClassName = SAP_IPServicePort<br />Name = enqPort<br />SystemCreationClassName = SAP_BCCentralServiceInstance<br />SystemName = &lt;SID&gt;.SystemHome.&lt;Host&gt;.ServiceInstanceID.&lt;No&gt;<br />normal props:<br />Caption = enqPort<br />Description = Manually created by SAP Support.<br />PortNumber = &lt;enqPort&gt;<br />Protocol = SAPni<br /><br />4.b  Create the association SAP_BCCentralServiceEnqueuePort (BC central service enqueue port) of the SAP_IPServicePort (TCP/IP service port) for the SAP_BCCentralServiceInstance (BC central service instance).<br /><br />5.a. Create a new SAP_IPServicePort (TCP/IP service port)<br />key props:<br />CreationClassName = SAP_IPServicePort<br />Name = msgHttpPort<br />SystemCreationClassName = SAP_BCCentralServiceInstance<br />SystemName = &lt;SID&gt;.SystemHome.&lt;Host&gt;.ServiceInstanceID.&lt;No&gt;<br />normal props:<br />Caption = msgHttpPort<br />Description = Manually created by SAP Support.<br />PortNumber = &lt;msgHttpPort&gt;<br />Protocol = Http<br /><br />5.b Create the association SAP_ BCCentralServiceMsgHttpPort (BC central service MsgHttp port) of the SAP_IPServicePort (TCP/IP service port) for the SAP_BCCentralServiceInstance (BC central service instance).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-LDB (Landscape Management Database (LMDB))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D035688)"}, {"Key": "Processor                                                                                           ", "Value": "I070718"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001419603/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419603/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1842956", "RefComponent": "SV-SMG-LDB", "RefTitle": "Check Data Supplier Completeness for Technical System", "RefUrl": "/notes/1842956"}, {"RefNumber": "1778137", "RefComponent": "BC-CCM-SLD", "RefTitle": "Manually creating ASCS in SLD using template", "RefUrl": "/notes/1778137"}, {"RefNumber": "1717846", "RefComponent": "BC-CST-STS", "RefTitle": "sapstartsrv SLD registration", "RefUrl": "/notes/1717846"}, {"RefNumber": "1700491", "RefComponent": "SV-SMG-LDB", "RefTitle": "Diagnostics prerequisites check: No central instance", "RefUrl": "/notes/1700491"}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731"}, {"RefNumber": "1614938", "RefComponent": "SV-SMG-INS", "RefTitle": "ASCS unreliably detected during managed system setup", "RefUrl": "/notes/1614938"}, {"RefNumber": "1571442", "RefComponent": "BC-CCM-SLD", "RefTitle": "Association between Java SCS and computer system is missing", "RefUrl": "/notes/1571442"}, {"RefNumber": "1475046", "RefComponent": "SV-SMG-INS", "RefTitle": "solman_setup: Generated RFC from HA systems is incorrect", "RefUrl": "/notes/1475046"}, {"RefNumber": "1464567", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: Message server and system no. from ASCS are incorrect", "RefUrl": "/notes/1464567"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2484482", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "\"ABAP System Http Availability\" shows red status with error message: \"[RC=404] URL http://<host>:<port>/sap/public/ping called unsuccessfully\".", "RefUrl": "/notes/2484482 "}, {"RefNumber": "2846749", "RefComponent": "BC-CCM-SLD", "RefTitle": "Unexpected host name of ASCS of Netweaver ABAP system is reported to SLD", "RefUrl": "/notes/2846749 "}, {"RefNumber": "2777759", "RefComponent": "SV-SMG-LDB", "RefTitle": "System must have a Central Service Instance error on Managed System Configuration - Solution Manager 7.2", "RefUrl": "/notes/2777759 "}, {"RefNumber": "1916237", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "ABAP Central Service not available alert in Technical Monitoring", "RefUrl": "/notes/1916237 "}, {"RefNumber": "2597417", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "ABAP Message Server Http Availability port is not correct", "RefUrl": "/notes/2597417 "}, {"RefNumber": "2535256", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Infrastructure Preparation Step 2.1 Define Http connectivity test dumps  OBJECTS_OBJREF_NOT_ASSIGNED_NO", "RefUrl": "/notes/2535256 "}, {"RefNumber": "2538204", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solution Manager  - managed system configuration-creating RFC fails - no central service instance defined.", "RefUrl": "/notes/2538204 "}, {"RefNumber": "2090772", "RefComponent": "SV-SMG-LDB", "RefTitle": "Deleting superfluous or obsolete ASCS data from the SLD or LMDB", "RefUrl": "/notes/2090772 "}, {"RefNumber": "1778137", "RefComponent": "BC-CCM-SLD", "RefTitle": "Manually creating ASCS in SLD using template", "RefUrl": "/notes/1778137 "}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731 "}, {"RefNumber": "1717846", "RefComponent": "BC-CST-STS", "RefTitle": "sapstartsrv SLD registration", "RefUrl": "/notes/1717846 "}, {"RefNumber": "1700491", "RefComponent": "SV-SMG-LDB", "RefTitle": "Diagnostics prerequisites check: No central instance", "RefUrl": "/notes/1700491 "}, {"RefNumber": "1614938", "RefComponent": "SV-SMG-INS", "RefTitle": "ASCS unreliably detected during managed system setup", "RefUrl": "/notes/1614938 "}, {"RefNumber": "1635247", "RefComponent": "BC-CCM-SLD", "RefTitle": "Fehlende SCS/ASCS in SLD (Korrekturen in SLD UI und STD API)", "RefUrl": "/notes/1635247 "}, {"RefNumber": "1475046", "RefComponent": "SV-SMG-INS", "RefTitle": "solman_setup: Generated RFC from HA systems is incorrect", "RefUrl": "/notes/1475046 "}, {"RefNumber": "1571442", "RefComponent": "BC-CCM-SLD", "RefTitle": "Association between Java SCS and computer system is missing", "RefUrl": "/notes/1571442 "}, {"RefNumber": "1464567", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: Message server and system no. from ASCS are incorrect", "RefUrl": "/notes/1464567 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}