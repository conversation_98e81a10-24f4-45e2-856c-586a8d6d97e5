{"Request": {"Number": "2891675", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 598, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000309362020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002891675?language=E&token=9E26E14A4C69890B05E9D68F56D373D9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002891675", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002891675/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2891675"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.02.2020"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP S/4HANA Data Migration Cockpit Content"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP S/4HANA Data Migration Cockpit Content", "value": "CA-GTF-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2891675 - SAP S/4HANA Migration Cockpit: FEBRUARY 2020 - Central correction Note for content issues for SAP S/4HANA 1809"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have an issue with the SAP S/4HANA Data Migration content delivered for SAP S/4HANA 1809. Error with the content.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Sales contract - Start &amp; End dates, Unable to confirm mapping of Project and WBS Element, Maintenance Plan,</p>\r\n<p>Mapping issues for the field Evaluation Group 5 and&#160;Property classification Key, Partner Bank Type,&#160;Inspection plan, Material BOM, Fixed Asset Subnumbers, SIF_FIXED_ASSET,</p>\r\n<p>rule SET_ANLN1</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have installed SAP S/4HANA 1809(SP00 - SP03)</p>\r\n<p>You are using SAP S/4HANA migration cockpit</p>\r\n<p>You are using the pre-delivered SAP S/4HANA Data migration content without any modifications</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The TCI included with this SAP Note fixes the following issues listed in the table below. For detailed description on the issues see the linked SAP Notes.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Technical Name</td>\r\n<td>Type</td>\r\n<td>Issue</td>\r\n<td>SAP Note&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>SIF_SD_CONTRACT</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>&#160;Sales contract - Start &amp; End dates are not updated</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2897202\">2897202</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>MAP_PS_POSID</p>\r\n<p>CVT_PS_POSID</p>\r\n<p>CVT_PS_POSNR</p>\r\n</td>\r\n<td>\r\n<p>Mapping Rule</p>\r\n<p>Conversion Rule</p>\r\n<p>Conversion Rule</p>\r\n</td>\r\n<td>\r\n<p>&#160;You get an error: &lt;WBS element&gt;&#160;is not a valid value of table PRPS (field POSID)</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2890015\">2890015</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_MAINT_PLAN_2</p>\r\n</td>\r\n<td>\r\n<p>Migration Object</p>\r\n</td>\r\n<td>\r\n<p>&#160;Migration of \"Maintenance Plan\" getting error \"Maintain a Factory Calendar\"</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2888345\">2888345</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_INSP_PLAN</p>\r\n</td>\r\n<td>\r\n<p>Migration Object</p>\r\n</td>\r\n<td>\r\n<p>&#160;Overflow issue for object Inspection plan and handle the initial value and zero</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2893383\">2893383</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_BOM</p>\r\n</td>\r\n<td>\r\n<p>Migration Object</p>\r\n</td>\r\n<td>\r\n<p>&#160;Global dependency wrongly assigned in material BoM migration</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2895523\">2895523</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_EQUIPMENT</p>\r\n</td>\r\n<td>\r\n<p>Migration Object</p>\r\n</td>\r\n<td>\r\n<p>&#160;Equipment Migration - System taking Valid-From Date as Today's Date</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2895439\">2895439</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SIF_FIXED_ASSET</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>&#160;Mapping issues for the fields Evaluation Group 5</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2889162\">2889162</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SIF_FIXED_ASSET</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>&#160;Mapping issues for the field Property classification key.</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2896803\">2896803</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SIF_FIXED_ASSET</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>&#160;SAP S/4HANA Migration Cockpit: Main Asset numbers not mapped when creating asset subnumbers</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2896079\">2896079</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_OPEN_ITEM_AP</p>\r\n<p>SIF_OPEN_ITEM_AR</p>\r\n</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>Vendor/Customer has no bank details with indicator xxxx (Partner Bank Type)</p>\r\n</td>\r\n<td>&#160;<a target=\"_blank\" href=\"/notes/2895412\">2895412</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Be aware that the TCI only corrects the related objects of SAP delivered content. As a result, the generated migration objects will be updated automatically.</p>\r\n<p><strong>Note:</strong> If you have modified/copied your object, the correction is not done within your modified/copied object.</p>\r\n<p>You may refer KBA&#160;<a target=\"_blank\" href=\"/notes/2543372\">2543372</a> - \"How to implement a Transport-based Correction Instruction\".</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I055188)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I307134)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002891675/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891675/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543372", "RefComponent": "BC-UPG-NA", "RefTitle": "How to implement a Transport-based Correction Instruction", "RefUrl": "/notes/2543372"}, {"RefNumber": "2897202", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Sales contract - Start & End date are not updated", "RefUrl": "/notes/2897202"}, {"RefNumber": "2896803", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed Asset - Property classification key", "RefUrl": "/notes/2896803"}, {"RefNumber": "2896079", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Main Asset numbers not mapped when creating asset subnumbers", "RefUrl": "/notes/2896079"}, {"RefNumber": "2895523", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Global dependency/Subitems wrong assigned in material BOM migration", "RefUrl": "/notes/2895523"}, {"RefNumber": "2895439", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit - Equipment: 'Valid-From Date' does not work", "RefUrl": "/notes/2895439"}, {"RefNumber": "2895412", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Accounts payable open item or Accounts receivable open item- Vendor/Customer has no bank details with indicator xxxx", "RefUrl": "/notes/2895412"}, {"RefNumber": "2893383", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of Inspection Plan\" getting error \"Value can't be interpreted as floating-point number\"", "RefUrl": "/notes/2893383"}, {"RefNumber": "2890015", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Conversion of Work Breakdown Structure Element (WBS Element)", "RefUrl": "/notes/2890015"}, {"RefNumber": "2889162", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed Asset - Evaluation group 5", "RefUrl": "/notes/2889162"}, {"RefNumber": "2888345", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of \"Maintenance Plan\" getting error \"Maintain a Factory Calendar\"", "RefUrl": "/notes/2888345"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}, {"RefNumber": "2896803", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed Asset - Property classification key", "RefUrl": "/notes/2896803 "}, {"RefNumber": "2895439", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit - Equipment: 'Valid-From Date' does not work", "RefUrl": "/notes/2895439 "}, {"RefNumber": "2895523", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Global dependency/Subitems wrong assigned in material BOM migration", "RefUrl": "/notes/2895523 "}, {"RefNumber": "2893383", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of Inspection Plan\" getting error \"Value can't be interpreted as floating-point number\"", "RefUrl": "/notes/2893383 "}, {"RefNumber": "2889162", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed Asset - Evaluation group 5", "RefUrl": "/notes/2889162 "}, {"RefNumber": "2888345", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of \"Maintenance Plan\" getting error \"Maintain a Factory Calendar\"", "RefUrl": "/notes/2888345 "}, {"RefNumber": "2897202", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Sales contract - Start & End date are not updated", "RefUrl": "/notes/2897202 "}, {"RefNumber": "2890015", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Conversion of Work Breakdown Structure Element (WBS Element)", "RefUrl": "/notes/2890015 "}, {"RefNumber": "2896079", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Main Asset numbers not mapped when creating asset subnumbers", "RefUrl": "/notes/2896079 "}, {"RefNumber": "2895412", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Accounts payable open item or Accounts receivable open item- Vendor/Customer has no bank details with indicator xxxx", "RefUrl": "/notes/2895412 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002891675/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}