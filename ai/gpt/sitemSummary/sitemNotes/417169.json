{"Request": {"Number": "417169", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 295, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015034282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000417169?language=E&token=B430A80CED13FF4FE868080043D8969D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000417169", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000417169/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "417169"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2008.11.27"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IM-GR-MIGO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Goods Receipt from External Procurement (MIGO)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Management", "value": "MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Goods Receipt", "value": "MM-IM-GR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM-GR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Goods Receipt from External Procurement (MIGO)", "value": "MM-IM-GR-MIGO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM-GR-MIGO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "417169 - MIGO: No warning messages when posting goods movement"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>As of Release 4.6B, you can use transaction MIGO to post goods movements.<br />The key design feature of this transaction is easy and fast entry of goods movement data. For this reason, the system does not issue any warning or error messages during the data entry.<br />However, if a check table lies behind the input field, the system executes a general input check. For example, when you enter a customer number, it checks whether this customer exists.<br />After you have entered the goods movement data, there are two possible follow-on actions:</p> <UL><LI>Check (F7):<br />The system checks whether the goods movement can be posted correctly. As a result, it displays error and warning messages.</LI></UL> <UL><LI>Post (Shift+F11) / (Ctrl+S):<br />The goods movement is posted. This activity is meant to be swift and straightforward. Only error messages will stop this activity. Therefore, the goods movement is posted without warning messages if no errors occur. If an error occurs during the goods movement, the system displays the error as well as the warning messages that have occurred so far.<br /></LI></UL> <p>The following generally applies:<br />Due to technical reasons, the system cannot output warning messages that were programmed in subsequent user exits.<br />As of Release 4.70, the Business Add-In (BAdI) MB_MIGO_BADI is available. In the method CHECK_ITEM, you can write messages that are output in MIGO to the return table.&#x00A0;&#x00A0;However, warning messages are issued during the document check and not during posting.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Warning</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If it is necessary for the system to display warning messages for a goods movement, carry out the \"Check\" function before you post the goods movement.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D023185)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022253)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000417169/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000417169/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "696362", "RefComponent": "CO-OM-OPA-C", "RefTitle": "User exit: Checks of CO availability control also for goods receipts and service entries", "RefUrl": "/notes/696362"}, {"RefNumber": "519303", "RefComponent": "MM-IM-GR-MIGO", "RefTitle": "FAQ: Goods movement with Transaction MIGO", "RefUrl": "/notes/519303"}, {"RefNumber": "1816363", "RefComponent": "MM-IM-GF", "RefTitle": "MIGO: Enhanced functions for Enhancement Package 7", "RefUrl": "/notes/1816363"}, {"RefNumber": "1304389", "RefComponent": "MM-IM-GR-PO", "RefTitle": "Purchase Order Price Unit during release of GR Blocked Stock", "RefUrl": "/notes/1304389"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1459866", "RefComponent": "MM-IM-GR-MIGO", "RefTitle": "MIGO : FM Derived Account Assignment Data", "RefUrl": "/notes/1459866 "}, {"RefNumber": "519303", "RefComponent": "MM-IM-GR-MIGO", "RefTitle": "FAQ: Goods movement with Transaction MIGO", "RefUrl": "/notes/519303 "}, {"RefNumber": "696362", "RefComponent": "CO-OM-OPA-C", "RefTitle": "User exit: Checks of CO availability control also for goods receipts and service entries", "RefUrl": "/notes/696362 "}, {"RefNumber": "1816363", "RefComponent": "MM-IM-GF", "RefTitle": "MIGO: Enhanced functions for Enhancement Package 7", "RefUrl": "/notes/1816363 "}, {"RefNumber": "1304389", "RefComponent": "MM-IM-GR-PO", "RefTitle": "Purchase Order Price Unit during release of GR Blocked Stock", "RefUrl": "/notes/1304389 "}, {"RefNumber": "595089", "RefComponent": "MM-IM-GR-MIGO", "RefTitle": "MIGO: Output of warning messages from valuation", "RefUrl": "/notes/595089 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}