{"Request": {"Number": "1435190", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 302, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016970702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001435190?language=E&token=876F46E10CFC82CE60C69C6F18DE11CA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001435190", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001435190/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1435190"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 43}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.06.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-DIA-SRV-HPI"}, "SAPComponentKeyText": {"_label": "Component", "value": "HTTP plugin (client side) / Correlation ID Library"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Diagnostics", "value": "SV-SMG-DIA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-DIA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Infrastructure / Framework", "value": "SV-SMG-DIA-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-DIA-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "HTTP plugin (client side) / Correlation ID Library", "value": "SV-SMG-DIA-SRV-HPI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-DIA-SRV-HPI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1435190 - SAP Client Plug-In"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>The architecture of the SAP client plug-in is closely linked to Internet Explorer and its functions therefore cannot be transferred to other browsers. Browsers such as Microsoft Edge, Google Chrome, Mozilla Firefox, or Apple Safari cannot be supported with the client plug-in architecture.</strong></p>\r\n<p><strong>Microsoft is replacing Internet Explorer with Microsoft Edge. Since the technological basis of Microsoft Internet Explorer and Microsoft Edge is completely different, the functions of the SAP client plug-in can no longer be provided. The support of all versions of Microsoft Internet Explorer supported by Microsoft can no longer be guaranteed. </strong></p>\r\n<p><strong>This means that functions based on the SAP client plug-in, such as recording end-to-end traces for the end-to-end trace analysis in SAP Solution Manager 7.2 and SAP Focused Run or recording User Experience Monitoring scripts in SAP Solution Manager 7.2 (UXMON/EEM) or Synthetic User Monitoring scripts in SAP Focused Run (SUM) cannot be provided.</strong></p>\r\n<p><strong>For the end-to-end trace analysis, it is still possible to record end-to-end traces for SAPGUI and SAPUI5 using the corresponding built-in tracing tools independently of the browser. You can find the procedure in the respective Expert Portal for SAP Focused Run or SAP Solution Manager 7.20. </strong></p>\r\n<p><strong>The procedure with regard to recording User Experience Monitoring scripts in SAP Solution Manager 7.2 (UXMON/EEM) or Synthetic User Monitoring scripts in SAP Focused Run (SUM) is described in SAP Note 3050062.</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SolMan Diagnostics E2E TA, SAP Focused Run Advanced Root Cause Analysis</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Host intrusion protection solutions may restrict or prevent the recording.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>The architecture of the SAP client plug-in is closely linked to Internet Explorer and its functions therefore cannot be transferred to other browsers. Browsers such as Microsoft Edge, Google Chrome, Mozilla Firefox, or Apple Safari cannot be supported with the client plug-in architecture.</strong></p>\r\n<p><strong>Microsoft is replacing Internet Explorer with Microsoft Edge. Since the technological basis of Microsoft Internet Explorer and Microsoft Edge is completely different, the functions of the SAP client plug-in can no longer be provided. The support of all versions of Microsoft Internet Explorer supported by Microsoft can no longer be guaranteed.</strong></p>\r\n<p><strong>This means that functions based on the SAP client plug-in, such as recording end-to-end traces for the end-to-end trace analysis in SAP Solution Manager 7.2 and SAP Focused Run or recording User Experience Monitoring scripts in SAP Solution Manager 7.2 (UXMON/EEM) or Synthetic User Monitoring scripts in SAP Focused Run (SUM) cannot be provided.</strong></p>\r\n<p><strong>For the end-to-end trace analysis, it is still possible to record end-to-end traces for SAPGUI and SAPUI5 using the corresponding built-in tracing tools independently of the browser. You can find the procedure in the respective Expert Portal for SAP Focused Run or SAP Solution Manager 7.20.</strong></p>\r\n<p><strong>The procedure with regard to recording User Experience Monitoring scripts in SAP Solution Manager 7.2 (UXMON/EEM) or Synthetic User Monitoring scripts in SAP Focused Run (SUM) is described in SAP Note 3050062.</strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Workarounds and solutions </strong></span></p>\r\n<ul>\r\n<li>By default, when you choose \"Launch\", the Internet Explorer starts with the page \"about:blank\" as the start page. The security settings of the Internet Explorer assign this page to the Internet security zone.</li>\r\n</ul>\r\n<ol>In the Internet Explorer options, go to the \"Security\" tab page. Add \"about:blank\" as an address to the intranet zone.</ol>\r\n<ul>\r\n<li>You can prevent the creation of more than one tab process for measuring by making the following registry entry: <br />HKEY_CURRENT_USER\\Software\\Microsoft\\Internet Explorer\\Main<br />DWORD: TabProcGrowth<br />Value: 1<br /><br /></li>\r\n<li>Due to missing authorizations, performance data cannot be read clearly on Windows Server operating systems. The XML file contains characters that cannot be displayed. To be able to read the performance data, the operating system user must be assigned to at least the following user group:</li>\r\n</ul>\r\n<p>Performance log users</p>\r\n<p>You can do this using computer administration in user and group administration.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Diagnosis</strong></span></p>\r\n<p>If you are prompted to do so, you can increase the log level of the SAP client plug-in. To do this, add the following line to the file smd.properties:</p>\r\n<p style=\"padding-left: 30px;\">debugtrace_level=2</p>\r\n<p>Attach the output_* files from the log directory to the message.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Checksums for the files of the SAP client plug-in</strong></span></p>\r\n<p><span style=\"font-size: 14px;\">The SHA-256 checksums of the important files contained in SAPClientPlugin_1.30_x86.zip:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>http2-proxy.dll</td>\r\n<td>c8e6cb8a8a83386484923b8a9fb24e0fb3be0117389adf0a46ebd74e481d053d&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>http-proxy.dll</td>\r\n<td>e1004e7937812b81a000ad3b9b586e93bf5114f55487628dee1ab0cb85eee70f&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>plugin-framework.dll</td>\r\n<td>a827e26782da755a1edbf0031e18c94923ff512d4c6a9d5e4c606e0c2b4683cf&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>plugin-starter-gui.exe</td>\r\n<td>9f22fb58fa1699bd7e1d3e3b7b70a91ff6cd8072fdc6479c47a45db48a00559d&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>rfc-proxy.dll</td>\r\n<td>2b7b479a53086e5bf35faf8d1791b57089b008f0904c0c20eb691eb9e083dcbe&#x00A0;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The SHA-256 checksums of the important files contained in SAPClientPlugin_1.29_x86.zip:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>http2-proxy.dll</td>\r\n<td>ce388e345799ddcceb28ef177681fbe3d78bef5559c7ebdbc26ecaef55030b40&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>http-proxy.dll</td>\r\n<td>ad1a558cbb75e3077d4fa151a3999cccbeb4882a268364fe91ac2ac18bb21e95&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>plugin-framework.dll</td>\r\n<td>6005585ea83fd98a3364affaa05a3b3cecab12d8942f0bc6b8fd1530c1c9e663&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>plugin-starter-gui.exe</td>\r\n<td>14a5531750696f31100e02f12776ed9ba79a5e12efc2e4920978b8e21ef07a79&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>rfc-proxy.dll</td>\r\n<td>a7d7f6fb7275d80d4f6f461109bfe4f941703e6b5913b2dc0346076ba319a293&#x00A0;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Example: Check the check sum of http2-proxy.dll using certUtil.exe<br /><br />&gt; certUtil -hashfile http2-proxy.dll SHA256</p>\r\n<p>SHA256 hash of http2-proxy.dll:<br />ce388e345799ddcceb28ef177681fbe3d78bef5559c7ebdbc26ecaef55030b40<br />CertUtil: -hashfile command completed successfully.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-DIA (Diagnostics)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020149)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031001)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001435190/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001435190/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAPClientPlugin_1.30_x86.zip", "FileSize": "1658", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000128062010&iv_version=0043&iv_guid=6CAE8B3EA1831ED6B4E54844F2FE20D0"}, {"FileName": "SAPClientPlugin_1.30_X64.rar", "FileSize": "1972", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000128062010&iv_version=0043&iv_guid=6CAE8B3EAD2B1ED6B4E570EEB2BE40C6"}, {"FileName": "SAPClientPlugin_1.29_x86.zip", "FileSize": "1657", "MimeType": "application/zip", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000128062010&iv_version=0043&iv_guid=00109B36BC8E1EDA88C5AD5D944980E2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3050062", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "User Experience Monitoring capabilities statement (for SAP Solution Manager 7.2)", "RefUrl": "/notes/3050062"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1041556", "RefComponent": "SV-SMG-DIA", "RefTitle": "SAP HTTP PlugIn for IE", "RefUrl": "/notes/1041556"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2828977", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "UXMon Script stops recording", "RefUrl": "/notes/2828977 "}, {"RefNumber": "2652425", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "How to set the SAP Client Plug-In to debug", "RefUrl": "/notes/2652425 "}, {"RefNumber": "2825407", "RefComponent": "SV-SMG-DIA-APP-TA", "RefTitle": "Upload the BusinessTransaction.xml on Trace Analysis fails with this error: String index out of range", "RefUrl": "/notes/2825407 "}, {"RefNumber": "2785238", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "Error \"occurred while starting the plug-in!\" \"Loading Plug-in in child process failed!\" when starting Script Recorder (SAPClientPlugin_1.30_X64) UXMON EEM Monitoring on Windows 10 - Solution Manager 7.2", "RefUrl": "/notes/2785238 "}, {"RefNumber": "2785278", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "EEM Recorder not recording scripts - Solution Manager 7.2", "RefUrl": "/notes/2785278 "}, {"RefNumber": "2620863", "RefComponent": "SV-SMG-DIA-SRV-HPI", "RefTitle": "SAP Client Plug-In is recording but showing 0 bytes", "RefUrl": "/notes/2620863 "}, {"RefNumber": "2575714", "RefComponent": "SV-SMG-DIA-SRV-HPI", "RefTitle": "SAP Client Plugin Trace <PERSON>l crashes with a dump", "RefUrl": "/notes/2575714 "}, {"RefNumber": "2522326", "RefComponent": "SV-SMG-DIA-APP-TA", "RefTitle": "Reading HANA Traces was aborted: An unexpected exception occured in Thread HanaTraceReaderThread: The value of a metadate must not be empty - SAP Solution Manager 7.1", "RefUrl": "/notes/2522326 "}, {"RefNumber": "2114667", "RefComponent": "BC-JAS-ADM-MON", "RefTitle": "Not able to monitor long term data in System Performance Statistics", "RefUrl": "/notes/2114667 "}, {"RefNumber": "1879918", "RefComponent": "SV-SMG-DIA", "RefTitle": "Virus scan detects SAP Client Plug-in as a trojan or virus", "RefUrl": "/notes/1879918 "}, {"RefNumber": "1861180", "RefComponent": "BI-BIP-ADM", "RefTitle": "Collecting an end to end trace in BI Platform 4.x - customer instructions and best practice [Video]", "RefUrl": "/notes/1861180 "}, {"RefNumber": "2148328", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "How to record performance statistics for RFC calls in Analysis Office", "RefUrl": "/notes/2148328 "}, {"RefNumber": "1461749", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E Root Cause Analysis for SBOP PC (BPC)", "RefUrl": "/notes/1461749 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "1041556", "RefComponent": "SV-SMG-DIA", "RefTitle": "SAP HTTP PlugIn for IE", "RefUrl": "/notes/1041556 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "720", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}