{"Request": {"Number": "854390", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 415, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015910392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000854390?language=E&token=7F5BC968D651065157981CE6B02A58E7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000854390", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000854390/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "854390"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.06.2005"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BF-PD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Partner Determination"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "SD-BF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner Determination", "value": "SD-BF-PD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BF-PD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "854390 - FAQ: Use of the sales representative in SD without HR"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL>1. Does the HR module have to be in use in order to use the sales representative as a partner in the SD module?</OL> <OL>2. What must be done to make the necessary HR tables available?</OL> <OL>3. There are problems when creating, changing or displaying sales representatives. What is the procedure for troubleshooting?</OL> <OL>4. Why does the sales document not display the name or other data for the sales representative?</OL> <OL>5. Why is the personal data (such as telephone number) of the sales representative not displayed in the sales document?</OL> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PAL1, PAL2, PAL3, sales representative, representative, employee, sales support, RPUTRL00<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>-<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <OL>1. <B>Question: </B><B>Does t</B><B>he HR module </B><B>have to</B><B> be in use in order to use the sales </B><B>representative</B><B> as a partner</B><B> in the SD module</B><B>?</B></OL> <p><br />Answer: No, only an HR master record is needed for the use of the sales representative. If you do not have the Human Resources department (application HR) in use, you can transport the HR tables from the client 000 to the target client that you need for creating, changing or displaying a sales representative.<br /></p> <OL>2. <B>Question: </B><B>What</B><B> must be </B><B>done to make </B><B>the necessary HR tables available?</B></OL> <p><br />Answer: In order to transport the necessary HR tables from the client 000 to the target client, two transfer orders must be available. The called RPUTRL00 report writes the Customizing tables as well as the system tables to separate transfer orders. You must then use Transaction SCC1 (called in client 000) to copy both transfers to the target client.<br /></p> <OL>3. <B>Question: </B><B>There are p</B><B>roblems </B><B>when </B><B>creatin</B><B>g</B><B>, </B><B>c</B><B>hang</B><B>ing</B><B> or display </B><B>ing</B><B> sales r</B><B>epresentatives</B><B>. </B><B>What is </B><B>the procedure for troubleshooting?</B></OL> <p><br />Answer: Note 28418 gives a detailed description of the necessary Customizing settings and approaches for troubleshooting and correcting the problem.<br /></p> <OL>4. <B>Question: Why </B><B>does </B><B>the sales document </B><B>not </B><B>display </B><B>the name or </B><B>other </B><B>d</B><B>ata for the sales</B><B>representative</B><B>?</B></OL> <p><br />Answer: Check whether the infotype 0001 (Transaction PAL3, Organizational Assignment) is maintained correctly for the sales representative.<br /></p> <OL>5. <B>Question</B><B>: Why </B><B>is </B><B>the </B><B>p</B><B>ersonal </B> <B>data</B><B> (</B><B>such </B><B>as </B><B>telephone number</B><B>) of the sales</B><B>representative</B><B> not display</B><B>ed in the sales</B><B> document?</B></OL> <p><br />Answer: If a personnel number is used in a document (for example, as sales representative), the system does not display the private address of the sales representative. This is the standard behavior. Data from the HR must not be displayed in the document. The sales representative can be assigned to a sales group and the sales group can be assigned to a sales office. The address of the sales office is then used for the sales representative.<br /><br />With the following modification, data, such as telephone number or fax-number, is displayed in the sales document from the private address (infotype 0006):<br /><br />Function module SD_REPRESENTANT_GET_DATA<br />...<br />&#x00A0;&#x00A0;DATA: SD_ADDRESSNUMBER LIKE TVKO-ADRNR,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WA_ADDRESS_SELECTION LIKE ADDR1_SEL,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WA_ADDRESS_VALUE&#x00A0;&#x00A0;&#x00A0;&#x00A0; LIKE ADDR1_VAL.<br />* &lt;&lt;&lt;&lt; INSERTATION START &gt;&gt;&gt;&gt;<br />&#x00A0;&#x00A0;data: lv_comm_data_source type char1 value space.<br /><br />&#x00A0;&#x00A0;if &lt;my_field&gt; = &lt;my_value&gt;.&#x00A0;&#x00A0; \"&lt;&lt;--THERE YOU CAN DEFINE OWN IF-CLAUSE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; lv_comm_data_source = 'X'. \"comm.data from private<br />&#x00A0;&#x00A0;endif.<br />* &lt;&lt;&lt;&lt; INSERTATION END &gt;&gt;&gt;&gt;<br /><br />* get telephone number (handy) of employee from infotyp 105<br />&#x00A0;&#x00A0;CALL FUNCTION 'HR_REPRESENTANT_GET_DATA'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; EXPORTING<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P_PERNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= FI_PERNR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P_DATE_FROM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= FI_DATE_FROM<br />* &lt;&lt;&lt;&lt; DELETION START &gt;&gt;&gt;&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P_PRIVATE_ADDRESS = SPACE<br />* &lt;&lt;&lt;&lt; DELETION END &gt;&gt;&gt;&gt;<br />* &lt;&lt;&lt;&lt; INSERTATION START &gt;&gt;&gt;&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P_PRIVATE_ADDRESS = lv_comm_data_source<br />* &lt;&lt;&lt;&lt; INSERTATION END &gt;&gt;&gt;&gt;<br />...<br /><br />* determine work address from SD organization units<br />&#x00A0;&#x00A0;CLEAR SD_ADDRESSNUMBER.<br />* &lt;&lt;&lt;&lt; INSERTATION START &gt;&gt;&gt;&gt;<br />&#x00A0;&#x00A0; if lv_comm_data_source = 'X'.&#x00A0;&#x00A0; \"comm.data from private<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;clear: FE_HRMR_REP-SALES_OFFICE, FE_HRMR_REP-SALES_ORG.<br />&#x00A0;&#x00A0; endif.<br />* &lt;&lt;&lt;&lt; INSERTATION END &gt;&gt;&gt;&gt;<br />...<br /><br /><B>However, note the following</B><B>:</B><br />These source code corrections are a modification. Among other things this means that these changes are neither contained in a following SAP Support Package nor scheduled for a future development cycle. In particular, this modification is not covered by SAP standard support (for more detailed information, see Note 170183).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PA-PA (Personnel Administration)"}, {"Key": "Responsible                                                                                         ", "Value": "C5125698"}, {"Key": "Processor                                                                                           ", "Value": "C5040898"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000854390/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000854390/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000854390/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000854390/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000854390/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000854390/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000854390/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000854390/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000854390/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "550102", "RefComponent": "SD-BF-PD", "RefTitle": "FAQ: SD Partner Determination II", "RefUrl": "/notes/550102"}, {"RefNumber": "28418", "RefComponent": "SD-MD", "RefTitle": "Problems when maintaining sales personnel", "RefUrl": "/notes/28418"}, {"RefNumber": "11887", "RefComponent": "SD-MD", "RefTitle": "PAL1: define sales personnel does not work", "RefUrl": "/notes/11887"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "550102", "RefComponent": "SD-BF-PD", "RefTitle": "FAQ: SD Partner Determination II", "RefUrl": "/notes/550102 "}, {"RefNumber": "11887", "RefComponent": "SD-MD", "RefTitle": "PAL1: define sales personnel does not work", "RefUrl": "/notes/11887 "}, {"RefNumber": "28418", "RefComponent": "SD-MD", "RefTitle": "Problems when maintaining sales personnel", "RefUrl": "/notes/28418 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}