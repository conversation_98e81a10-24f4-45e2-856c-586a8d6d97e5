{"Request": {"Number": "1405975", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 794, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016905282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001405975?language=E&token=CFD0A2B7A2D52ACAD6BCEF14E6ADAC62"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001405975", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001405975/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1405975"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.11.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1405975 - Minimum Authorization Profile for Remote Service Delivery"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Customers are asked to provide a logon user for SAP Remote Service Delivery. Due to security concerns customers wish to grant restricted authorizations only.</p>\r\n<p><strong>Please note, the suggested authorizations are to be used as templates and should be reviewed as per your security policy.</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Support Services, Standard Support, Remote Service Delivery, SAP Continuous Quality Check, Authorization Profile, SAP GoingLive Check, SAP GoingLive Functional Upgrade Check, SAP OS/DB Migration Check, SAP EarlyWatch Check, ERP, BW, CRM, SCM, SRM, PI, <strong>RSDUSER</strong>, ABAP authorization, Transaction does not exist</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Customers are asked to provide a logon user for SAP Continuous Quality Check remote services.</p>\r\n<p>In this case SAP offers the implementation of a minimum authorization profile for a new user&#160;<strong>RSDUSER</strong>(any username can be used, but for the purpose of this note,&#160;<strong>RSDUSER</strong> is used)<strong>&#160;</strong>for the service delivery. This Note only covers ABAP authorization.<br />The minimized authorization profile defines a limited set of transactions that can be be executed, which should be sufficient for checks in the preparation and the delivery of an SAP Continuous Quality Check service. Please refer to the attached file 'TransactionList.zip' for the detail of the list of transactions restricted in the following SAP components.</p>\r\n<ul>\r\n<li>SAP Components:</li>\r\n<ul>\r\n<li>\r\n<p>S/4: 1511 to 2021</p>\r\n</li>\r\n<li>\r\n<p>R/3: 4.6C to ERP 6.0</p>\r\n</li>\r\n<li>\r\n<p>BW : 3.5 to 7.0</p>\r\n</li>\r\n<li>\r\n<p>CRM: 4.0 to 6.0</p>\r\n</li>\r\n<li>\r\n<p>SCM: 4.0 to 7.0</p>\r\n</li>\r\n<li>\r\n<p>SRM: 4.0 to 7.0</p>\r\n</li>\r\n<li>\r\n<p>PI : 3.0 to 7.10</p>\r\n</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>SAP Continuous Quality Check services:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP GoingLive Analysis Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP GoingLive Optimization Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP GoingLive Verification Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP EarlyWatch Check Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP OS/DB Migration Analysis Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP OS/DB Migration Verification Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP GoingLive Functional Upgrade Analysis Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP GoingLive Functional Upgrade Verification Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP CQC Business Process Performance Optimization Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP CQC Technical Performance Optimization Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP CQC Remote Performance Optimization Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP CQC Integration Validation Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP CQC Going Live Support Service</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP CQC Security Optimization Service</li>\r\n</ul>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This solution can be implemented online. The system does not have to be restarted after the implementation.<br />You can apply the changes in this note into your production system or into your development system and transport the changes into the production system.<br />The details of how to transfer a role from development system to the Production System, please refer to SAP Note <a target=\"_blank\" href=\"/notes/571276\">571276</a> and <a target=\"_blank\" href=\"https://help.sap.com/viewer/6f323fd26c4b1014996399b1f02898ae/7.01.22/en-US/6d7c8cfd410ea040aadf92e1f78107a4.html\">Transporting and Distributing Roles</a>.</p>\r\n<p>For information on specific service authorisations please refer to the dedicated SAP Note for that service. For list of these SAP Notes for specific services please refer to SAP Note <a target=\"_blank\" href=\"/notes/91488\">91488</a>.</p>\r\n<p>Please follow the steps below to create a user with the minimum authorization profile.</p>\r\n<p><em><strong>Note:</strong>&#160;in S/4HANA, add authorization (S_SYS_INFO-ACTVT=&#8217;03&#8217;) to the role in order to allow the displaying of the system status information. See SAP note&#160;</em><a target=\"_blank\" href=\"/notes/2658772\">2658772</a>&#160;<em>for details.</em></p>\r\n<p><strong>I. To create a user with minimized authorization profile on the systems of R/3, S/4 and ERP.</strong></p>\r\n<ol>\r\n<li>Download and unzip the attached role file 'Z_BASIC_SERVICE_V1.zip' to your local folder.</li>\r\n<li>Logon to the system in client where the remote service will be performed. The user should have full authorization of transactions PFCG and SU01.</li>\r\n<li>Run transaction SU01 to create the user <strong>RSDUSER </strong>as a dialog user. Do not assign any role or authorization profile to this user.</li>\r\n<li>Start transaction PFCG -&gt; go to \"Role\" from menu bar -&gt; go to \"Upload\". Press &lt;Enter&gt; when a pop up window appears. Find the role file that you have downloaded in step 1 and then press &lt;Enter&gt;.</li>\r\n<li>In the main screen of the transaction PFCG, fill in role name in the field 'Role'.</li>\r\n<li>Click on &lt;Change&gt; button.</li>\r\n<li>Go to &lt;Authorizations&gt; tab, assign a profile name by clicking button &lt;Propose profile name&gt; in section &lt;Information About Authorization Profile&gt;. System will automatically assign a profile name to the role.</li>\r\n<li>Click on the button &lt;Change Authorization Data&gt; in section &lt;Maintain Authorization Data and Generate Profiles&gt;. When a pop up window appears, click &lt;Yes&gt; to save the role.</li>\r\n<li>In the next screen go to menu bar 'Authorizations' -&gt; go to 'Generate'. If a warning window appeared, click on &lt;Continue&gt; button.</li>\r\n<li>Then the minimized authorization profile for the role has been generated.</li>\r\n<li>Click on &lt;Back&gt; button and return to the [Change Roles] screen.</li>\r\n<li>Go to &lt;User&gt; tab, fill in the user name as <strong>RSDUSER</strong> in the field 'User ID' and then press &lt;Enter&gt;.</li>\r\n<li>Click on &lt;User Comparison&gt; button, a pop up window will appears. Click on &lt;Complete Comparison&gt; button and click &lt;Yes&gt;.</li>\r\n<li>Click on &lt;Save&gt; button.</li>\r\n</ol><ol></ol><ol></ol><ol></ol><ol></ol>\r\n<p><strong>II. To create a user with the minimized authorization profiles on the components other than ERP, such as BW, CRM, SCM, SRM and/or PI.</strong></p>\r\n<ol>\r\n<li>Follow the steps described in section I to implement the basic role of 'Z_BASIC_SERVICE_V1' to the service user <strong>RSDUSER.</strong></li>\r\n<li>Download and unzip the appropriate role file of 'Z_ADDITIONAL_ &lt;COMPONENT&gt;_ROLE_V1.zip' to your local folder, where &lt;COMPONENT&gt; is replaced by the targeted SAP component.&#160; For example, if you want to implement additional authorization profile to the user of <strong>RSDUSER </strong>on BW system, you should download and unzip the role file called 'Z_ADDITIONAL_BW_ROLE_V1.zip'.</li>\r\n<li>Repeat the steps from 4 to 14 that described in section I to assign the additional role to the user <strong>RSDUSER.</strong></li>\r\n</ol>\r\n<p><strong>III. To add the authorization object for the new SQL editor on Oracle while delivering a SAP CQC Technical Performance Optimization Session.</strong></p>\r\n<p style=\"padding-left: 30px;\">A new SQL Command Editor for Oracle is delivered by Support Package as of Release 7.30 or as of Release 7.02. The new SQLC runs with Oracle release 11 or higher only. Refer SAP Note <a target=\"_blank\" href=\"/notes/1556453\">1556453</a> for more details. SAP Note <a target=\"_blank\" href=\"/notes/1568173\">1568173</a> delivers the authorization object S_TABU_SQL that is used for an authorization check in the SQL Command Editor. It also gives a step by step approach how to add the authorization object to an authorized user preferably <strong>RSDUSER</strong>.</p>\r\n<p style=\"padding-left: 30px;\">See SAP Note&#160;<a target=\"_blank\" href=\"/notes/2222220\">2222220</a>&#160;-&#160;FAQ: SAP HANA DBACOCKPIT --&gt; \"9. How can the user of the SQL editor in DBACOCKPIT be controlled?\"</p>\r\n<p><strong style=\"font-size: 14px;\">Note:&#160;</strong><span style=\"font-size: 14px;\">This note may terminate with the error message \"Transaction does not exist\", this applies especially to newer releases. Please refer to SAP Note </span><a target=\"_blank\" href=\"/notes/3190897\" style=\"font-size: 14px;\">3190897</a><span style=\"font-size: 14px;\"> in this case.&#160;</span></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I347716)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I035947)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001405975/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001405975/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Z_ADDITIONAL_SCM_ROLE_V1.zip", "FileSize": "5", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000617332009&iv_version=0025&iv_guid=244851769EE6CB41B5335963B632D904"}, {"FileName": "Z_ADDITIONAL_SRM_ROLE_V1.zip", "FileSize": "4", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000617332009&iv_version=0025&iv_guid=0543C1A620A5B5438E96CCCE46A17FA1"}, {"FileName": "Z_ADDITIONAL_PI_ROLE_V1.zip", "FileSize": "3", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000617332009&iv_version=0025&iv_guid=8134F42CDAE9F740B7EE4E4E235D66AD"}, {"FileName": "Z_ADDITIONAL_CRM_ROLE_V1.zip", "FileSize": "7", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000617332009&iv_version=0025&iv_guid=00109B36BC1E1ED9B9AAF855AE1420D1"}, {"FileName": "Z_BASIC_SERVICE_V1.zip", "FileSize": "12", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000617332009&iv_version=0025&iv_guid=00109B36D5CA1EDD87BDC184839CA906"}, {"FileName": "TransactionList.zip", "FileSize": "15", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000617332009&iv_version=0025&iv_guid=00109B36D73A1EDD87BDC2C570F6E86F"}, {"FileName": "Z_ADDITIONAL_BW_ROLE_V2.zip", "FileSize": "9", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000617332009&iv_version=0025&iv_guid=00109B36D5CA1EDD87BDCB6B00C5C906"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951"}, {"RefNumber": "1568173", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Authorization object for SQL Command Editor", "RefUrl": "/notes/1568173"}, {"RefNumber": "1556453", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBACockpit: New SQL Command Editor for Oracle databases", "RefUrl": "/notes/1556453"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}, {"RefNumber": "1090073", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1090073"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3033103", "RefComponent": "SV-PERF", "RefTitle": "Information required for initial analysis of Performance Issues in  ABAP System", "RefUrl": "/notes/3033103 "}, {"RefNumber": "3359598", "RefComponent": "XX-SER-NET-RCP", "RefTitle": "Important policies, compliance and remote connectivity standards for service and support access", "RefUrl": "/notes/3359598 "}, {"RefNumber": "3212346", "RefComponent": "FS-FPS", "RefTitle": "Possible root causes for insufficient performance of FPSL processes", "RefUrl": "/notes/3212346 "}, {"RefNumber": "1321295", "RefComponent": "SV-SMG-SER", "RefTitle": "Enterprise Support Report", "RefUrl": "/notes/1321295 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1323405", "RefComponent": "SV-SMG-SER", "RefTitle": "Technical Preparation of a CQC BPPO service", "RefUrl": "/notes/1323405 "}, {"RefNumber": "1568173", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Authorization object for SQL Command Editor", "RefUrl": "/notes/1568173 "}, {"RefNumber": "1442799", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Enterprise Support Report", "RefUrl": "/notes/1442799 "}, {"RefNumber": "1556453", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBACockpit: New SQL Command Editor for Oracle databases", "RefUrl": "/notes/1556453 "}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "756", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}