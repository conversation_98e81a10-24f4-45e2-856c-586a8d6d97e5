{"Request": {"Number": "1750510", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 218, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017482432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001750510?language=E&token=B50963DEA37CD6714159F137723CEA2B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001750510", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001750510/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1750510"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.10.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SYB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite on Adaptive Server Enterprise"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite on Adaptive Server Enterprise", "value": "BC-DB-SYB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SYB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1750510 - FAQ: Sybase ASE 15.7 Compression"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>1. What is compression?</ol><ol>2. How can I obtain the compression feature?</ol><ol>3. What are the advantages of compression?</ol><ol>4. What are the different kinds of compression?</ol><ol>5. What are the different levels of LOB compression?</ol><ol>6. What are the different types of data compression?</ol><ol>7. What is 'In-Row LOB storage'?</ol><ol>8. How can I check if compression is enabled?</ol><ol>9. Can compression be disabled or limited?</ol><ol>10. Where can I find information on standalone Sybase ASE compression feature?</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, frequently asked questions, compression, ASE, Sybase</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br />Disclaimer:</p>\r\n<ul>\r\n<ul>\r\n<li>This note is meant to provide basic information about the 'compression' feature with reference to SAP Business Suite.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Changes to 'compression level' on SAP Business Suite deployments (on Sybase ASE) are not recommended. If changes need to be made, they are to be done in conjunction with SAP Development Support.</li>\r\n</ul>\r\n</ul>\r\n<p><br />1. What is compression?<br /><br />Compression means transforming data into a shorter form when stored. It can be defined</p>\r\n<ul>\r\n<li>As a database property (create / alter database)</li>\r\n</ul>\r\n<ul>\r\n<li>Per-table, for applicable columns</li>\r\n</ul>\r\n<p><br /><br />2.How can I obtain the compression feature?<br /><br />Compression is enabled by default for all SAP Business Suite deployments on Sybase ASE.<br /><br /><br />3. What are the advantages of compression?<br /><br />With compression less space is required, and therefore has the following advantages:</p>\r\n<ul>\r\n<li>Smaller database size.</li>\r\n</ul>\r\n<ul>\r\n<li>Lower costs for disk allocation.</li>\r\n</ul>\r\n<ul>\r\n<li>More efficient use of the Sybase ASE data caches due to a higher data density.</li>\r\n</ul>\r\n<ul>\r\n<li>Reduced I/O because fewer blocks are read and less page displacement has to be made from the buffer pool.</li>\r\n</ul>\r\n<p><br /><br />4. What are the different kinds of compression?<br /><br />The following kinds of compression are available in Sybase ASE:</p>\r\n<ul>\r\n<li>Data Compression</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Table data can be compressed. Tables can have a mixture of compressed and uncompressed data. Decompression occurs automatically as data is accessed by queries. New or modified data is compressed as it is being written to the disk.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;LOB Compression<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;LOB compression indicates compression of text and image data. LOB data includes unstructured data such as XML documents, images, audio and video, and external files containing large documents and spreadsheets. In-database LOB compression provides different levels of compression based on the data and its access.</p>\r\n<ul>\r\n<li>Backup Compression</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Please refer to SAP Note <a target=\"_blank\" href=\"/notes/1585981\">1585981</a>.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br />5. What are the different levels of LOB compression?<br /><br />There are many compression levels for example 0 to 9, 100 or 101. SAP databases use compression level 100(which uses the 'FastLZ' algorithm to compress data). Level 100 is less CPU intensive and provides faster compression. The column 'lobcomplvl' in the example below shows database-wide LOB Compression Level setting.<br /><br />(NOTE: The output of the below command is shown in 2 rows for readability)<br /><br />&#160;&#160; sp_helpdb &lt;db-name&gt;<br />&#160;&#160; go<br /><br />&#160;&#160; example:<br />&#160;&#160; sp_helpdb NWA<br />&#160;&#160; go<br /><br />&#160;&#160; name&#160;&#160;db_size&#160;&#160;&#160;&#160;&#160;&#160;owner&#160;&#160;dbid&#160;&#160;created&#160;&#160;&#160;&#160;&#160;&#160;durability&#160;&#160;lobcomplvl<br />&#160;&#160; ----&#160;&#160;-------&#160;&#160;&#160;&#160;&#160;&#160;-----&#160;&#160;----&#160;&#160;-------&#160;&#160;&#160;&#160;&#160;&#160;----------&#160;&#160;----------<br />&#160;&#160; NWA&#160;&#160; 51200.0 MB&#160;&#160;sapsa&#160;&#160;4&#160;&#160;&#160;&#160; Sep 11, 2012&#160;&#160;full&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;100<br /><br />&#160;&#160; inrowlen&#160;&#160;status<br />&#160;&#160; --------&#160;&#160;------<br />&#160;&#160; 2000&#160;&#160;&#160;&#160;&#160;&#160;trunc log on chkpt,ddl in tran,allow nulls by default,<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; abort tran on log full,allow wide dol rows,<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; page compression<br /><br /><br />6. What are the different types of data compression?<br /><br />Data can be compressed at page and row level</p>\r\n<ul>\r\n<li>ROW-Level Compression compresses individual rows in a table.</li>\r\n</ul>\r\n<ul>\r\n<li>Page-Level Compression compresses the amount data redundancy on a page using a page dictionary. Page-level compression always includes row-level compression.</li>\r\n</ul>\r\n<p><br /><br />7. What is 'In-Row LOB storage'?<br /><br />LOB columns defined as text/image, get a length specifier for how much can be in-row. In-row LOBs &lt;= length remain in-row, provided there is room on the row/page. The column 'inrowlen' has a value of 2000 (bytes) for SAP databases. To check use the command below.<br /><br />sp_helpdb &lt;db-name&gt; &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;go<br /><br />(NOTE: To see the output of this command look at the example in question 5 )<br /><br /><br />8. How can I check if compression is enabled?<br /><br />You can check if compression is enabled and the level of compression. To check compression settings on individual objects use the 'Single Table Analysis' screen of DBA Cockpit,or the following command.<br /><br />&#160;&#160; sp_help 'SAPSR3.&lt;TABLENAME&gt;'<br /><br /><br />9. Can compression be disabled or limited?<br /><br />Yes, compression might be turned off or limited to row-level compression on individual objects. This is the case for some SAP tables by default (known as 'hot tables' or tables containing data which is already compressed at application level).<br /><br /><br />10. Where can I find information on the compression feature for standalone ASE databases?<br /><br />http://www.sybase.com/files/Product_Overviews/ASE-15.7-Storage-Compression.pdf</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-SYB (BW on Adaptive Server Enterprise)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I828689)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D068770)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001750510/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1585981", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Ensuring Recoverability for SAP ASE", "RefUrl": "/notes/1585981"}, {"RefNumber": "1581695", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Configuration Guide for SAP ASE 16.0", "RefUrl": "/notes/1581695"}, {"RefNumber": "1539125", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Configuration Guide for SAP ASE 15.7.0.0xx", "RefUrl": "/notes/1539125"}, {"RefNumber": "1539124", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Database Configuration for SAP applications on SAP ASE", "RefUrl": "/notes/1539124"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1539125", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Configuration Guide for SAP ASE 15.7.0.0xx", "RefUrl": "/notes/1539125 "}, {"RefNumber": "1539124", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Database Configuration for SAP applications on SAP ASE", "RefUrl": "/notes/1539124 "}, {"RefNumber": "1581695", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Configuration Guide for SAP ASE 16.0", "RefUrl": "/notes/1581695 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}