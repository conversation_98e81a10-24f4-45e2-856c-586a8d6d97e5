{"Request": {"Number": "1926261", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 854, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001926261?language=E&token=3150E46006AE47504A35278DD4CBAA55"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001926261", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001926261/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1926261"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 39}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "13.12.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1926261 - Central Note - Software Update Manager 1.0 SP11 [lmt_007]"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />Errors in the update process; preparations for the update; additional information to the update guides</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />Enhancement package installation, Software Update Manager, SUM, EHP, update, upgrade, Support Package Stack application, SPS update, additional technical usage, maintenance</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note applies to <strong>Software Update Manager 1.0 SP11</strong>.<br /><br />Use Software Update Manager 1.0 SP11 for the following maintenance processes:</p>\r\n<ul>\r\n<li>Upgrading to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.4) for the following upgrade paths (SAP Business Suite systems are considered below):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP R/3 4.6C</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP R/3 Enterprise 4.70 / Extension Set 1.10</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP R/3 Enterprise 4.70 / Extension Set 2.00</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 2004-based systems (SAP ERP 2004, SAP SCM 4.1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based systems (SAP ERP 6.0 and higher, SAP SCM 5.0 and higher, SAP SRM 5.0 and higher, SAP CRM 5.0 and higher)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.4) for the following update/EHP installation paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 (SAP ERP 6.0 EHP4, SAP CRM 7.0, SAP SCM 7.0, SAP SRM 7.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 Innovation 2010 (SAP ERP 6.0 EHP5, SAP CRM 7.0 EHP1, SAP SCM 7.0 EHP1, SAP SRM 7.0 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 Innovation 2011 (SAP ERP 6.0 EHP6, SAP CRM 7.0 EHP2, SAP SCM 7.0 EHP2, SAP SRM 7.0 EHP2)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP enhancement package 6 for SAP ERP 6.0, version for SAP HANA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP enhancement package 2 for SAP CRM 7.0, version for SAP HANA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP enhancement package 2 for SAP SCM 7.0, version for SAP HANA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.3 including enhancement package 1) for the following update/EHP installation paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.3) for the following update/EHP installation paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.4 for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 (with SP 14 or higher) or SAP NetWeaver 7.0 including enhancement package 1 or 2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver AS ABAP 7.1 (for banking services from SAP 7.0 and 8.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver Process Integration 7.1 or SAP NetWeaver Process Integration 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver Composition Environment 7.1, SAP NetWeaver Composition Environment 7.1 including enhancement package 1 or SAP NetWeaver Composition Environment 7.2 (production edition)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 or SAP NetWeaver 7.3 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have SAP Business Suite 7i2013 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have SAP Business 7i2013 applications installed on top</li>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n<li>\r\n<p>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.4)</p>\r\n</li>\r\n<li>\r\n<p>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.4)</p>\r\n</li>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n</ul>\r\n</ul>\r\n<p>Note that depending on whether your system is ABAP-based or Java-based, \"updating\" here means performing a release upgrade or enhancement package installation (on the technical level, this is handled differently on ABAP vs Java stack).</p>\r\n<ul>\r\n<li>Upgrading to SAP NetWeaver 7.3 including enhancement package 1 for the following upgrade paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 2004 (except for dual-stack systems)</li>\r\n</ul>\r\n</ul>\r\n<p>For Java standalone systems: this upgrade path is only supported for pure portal systems</p>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 (with SP 14 or higher) or SAP NetWeaver 7.0 including enhancement package 1 or 2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Process Integration 7.1 or SAP NetWeaver Process Integration 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Composition Environment 7.1, SAP NetWeaver Composition Environment 7.1 including enhancement package 1 or SAP NetWeaver Composition Environment 7.2 (production edition)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver BW 7.3 on SAP HANA DB</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.3 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.3 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.3 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Upgrading to SAP NetWeaver 7.3 for the following upgrade paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Process Integration 7.1 or SAP NetWeaver Process Integration 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 - Process Integration, SAP NetWeaver Process Integration 7.1, or SAP NetWeaver Process Integration 7.1 including enhancement package 1, each with installed PI Adapters (SWIFT, BCONS, ELSTER) on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Mobile 7.1 or SAP NetWeaver Mobile 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Composition Environment 7.1, SAP NetWeaver Composition Environment 7.1 including enhancement package 1 or SAP NetWeaver Composition Environment 7.2 (production edition)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 2004</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 or SAP NetWeaver 7.0 including enhancement package 1 or 2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.3)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.3)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.3)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Upgrading to SAP Solution Manager 7.1 from the following upgrade paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Solution Manager 7.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Solution Manager 7.0 EHP1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP enhancement package 6 for SAP ERP 6.0, version for SAP HANA (based on SAP NetWeaver Application Server ABAP 7.4) from SAP ERP 6.0 and higher (ABAP components only)</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP enhancement package 2 for SAP SCM 7.0, version for SAP HANA (based on SAP NetWeaver Application Server ABAP 7.4) from SAP SCM 7.0 and higher</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP enhancement package 2 for SAP CRM 7.0, version for SAP HANA (based on SAP NetWeaver Application Server ABAP 7.4) from SAP CRM 7.0 (ABAP components only)</li>\r\n</ul>\r\n<ul>\r\n<li>Upgrading to SAP Business Suite 7 Innovation 2011 for the following upgrade paths (SAP Business Suite systems are considered below):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Basis 4.6C-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Basis 4.6D-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Web AS ABAP 6.20-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 04-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2011 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 (SAP ERP 6.0 EHP4, SAP CRM 7.0, SAP SCM 7.0, SAP SRM 7.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 Innovation 2010 (SAP ERP 6.0 EHP5, SAP CRM 7.0 EHP1, SAP SCM 7.0 EHP1, SAP SRM 7.0 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite 7i2010 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite 7i2010 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2010 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 (SAP ERP 6.0 EHP4, SAP CRM 7.0, SAP SCM 7.0, SAP SRM 7.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP ERP 6.0 including enhancement package 4 for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.3 including enhancement package 1 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have SAP Business Suite applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Composition Environment 7.2 from the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP NetWeaver Composition Environment 7.1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP NetWeaver Composition Environment 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Composition Environment 7.1 EHP1 from SAP NetWeaver Composition Environment 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Process Integration 7.1 including enhancement package 1 (enhancement package installation) from SAP NetWeaver Process Integration 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Mobile 7.1 including enhancement package 1 (enhancement package installation) from SAP NetWeaver Mobile 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.0 including enhancement package 2 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.0 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.0 including enhancement package 1 from SAP NetWeaver 7.0 (enhancement package installation)</li>\r\n</ul>\r\n<ul>\r\n<li>Applying Support Packages Stacks to the following systems:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.4 (including Business Suite applications on SAP HANA on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 including enhancement package 1 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 including enhancement package 2 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 including enhancement package 3 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.1 and SAP NetWeaver 7.1 including enhancement package 1 (CE, PI, Mobile, banking services from SAP 7.0 and SAP 8.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver Composition Environment 7.2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.3 (including systems with PI adapters SWIFT, BCONS, ELSTER on top and Java Hub systems with Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.3 including enhancement package 1 (including systems with PI adapters SWIFT, BCONS, ELSTER on top and Java Hub systems with Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Solution Manager 7.0 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Solution Manager 7.1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Installing additional Java usage types / technical usages on top of existing Java systems</li>\r\n</ul>\r\n<ul>\r\n<li>Updating and patching single software components (not supported for ABAP only systems)</li>\r\n</ul>\r\n<p>See the attached document SUM_SP11_paths.pdf for a graphical representation of the supported update and upgrade paths.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>This is the central SAP Note for Software Update Manager 1.0 SP11.</strong><br /><strong>CAUTION:</strong><br />This note is updated regularly!<br />Therefore, you must read it again immediately before starting the tool.<br /><br /><br /><strong>Contents</strong><br />Part A: Update Notes and Keywords<br />Part B: SUM Version, Maintenance Strategy and Documentation<br />Part C: General Problems / Information<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I/.......Important General Information<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;II/ .... Corrections to the Guide<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;III/ ....Preparing the Update<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IV/..... Problems During the Update Phases<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;V/ ..... Problems After the Update<br />Part D: Chronological Summary<br /><br /><br /><br /><br /><strong>Part A: Update Notes and Keywords</strong><br /><br />Keyword for Phase KEY_CHK: 1747235<br /><br />For more information about the update of your specific application, see the following Notes:<br /><br />SAP NetWeaver 7.4.......................................1751237<br />SAP Solution Manager 7.1................................1781833<br />SAP NetWeaver 7.3 EHP1..................................1609441<br />SAP NetWeaver 7.3.......................................1390477<br />EHP1 for SAP NetWeaver 7.1..............................1162299<br /><br />ERP on HANA 1.0 - Release Information Note..............1730095<br />CRM on HANA 1.0 - Release Information Note..............1730098<br />SCM on HANA 1.0 - Release Information Note..............1730175<br /><br />SAP ERP 6.0 EHP 7 - Release Information Note............1737650<br />SAP CRM 7.0 EHP 3 - Release Information Note............1737725<br />SAP SCM 7.0 EHP 3 - Release Information Note............1737723<br />SAP SRM 7.0 EHP 3 - Release Information Note............1818517<br /><br />SAP ERP 6.0 EHP 6:......................................1496212<br />SAP CRM 7.0 EHP 2:......................................1497032<br />SAP SCM 7.0 EHP 2:......................................1497083<br />SAP SRM 7.0 EHP 2:......................................1582094<br /><br /><br />In addition, you need the SAP Note for your database or operating system:<br /><br />Operating system.....................................Note number<br />_________________________________________________________________<br />IBM&#160;i ..................................................1993051<br /><br />Database ............................................Note number<br />_________________________________________________________________<br />SAP MaxDB ..............................................1926740<br />IBM DB2 for Linux, UNIX and Windows ....................1999514<br />IBM DB2 for z/OS .......................................1926798<br />MS SQL Server ..........................................1926923<br />Oracle .................................................1926454<br />SAP ASE.................................................1926697<br />SAP HANA DB.............................................1926741<br />IBM DB2 for i ..........................................1993051<br />_________________________________________________________________<br /><br /><br /><br /><strong>Part B: SUM Version, Maintenance Strategy and Documentation</strong><br /><br /><strong>SUM Version</strong><br />This note applies to <strong>Software Update Manager 1.0 SP11</strong>.<br />Before you start the update, check if there is a newer tool version available on SAP Service Marketplace.<br />SAP recommends that you download the latest tool version as only this version contains the latest corrections and is updated regularly.<br /><br />The Software Update Manager is available on the SAP Service Marketplace at http://service.sap.com/sltoolset -&gt; Software Logistics Toolset 1.0 -&gt; table with the Software Logistics tools -&gt; Software Update Manager &lt;version&gt; -&gt; Download Link.<br /><br /><strong>Maintenance Strategy</strong><br />The Software Update Manager 1.0 SP11 is currently in&#160;<strong>\"patch on request\"&#160;mode</strong>.<br />The maintenance strategy for the Software Update Manager and the other tools delivered with SL Toolset 1.0 SP11 is described in the document attached to the SL Toolset 1.0 SPS11 Release Note 1922474.<br /><br /><strong>Documentation<br /></strong>Before you start using the SUM tool, make sure you have the latest version of the corresponding document, which is available on the SAP Service Marketplace at: http://service.sap.com/sltoolset -&gt; Software Logistics Toolset 1.0 -&gt; Documentation -&gt; System Maintenance -&gt; Updating SAP Systems Using Software Update Manager 1.0 SP11.</p>\r\n<p><br />----------------------------------------------------------------------<br /><strong>Part C: General Problems / Information</strong><br /><br /><strong>I/ Important General Information</strong><br /><br />a) General</p>\r\n<p>--------------------------&lt; I36200 19/NOV/2014 &gt;-------------------------<br /><strong>PI: Downloading the latest patches when generating the stack.xml file<br /></strong>To avoid component version inconsistencies, when you generate the stack.xml file using MOPz you have to choose the option \"Add Java patches\" in the \"Select Stack-Independent Files\" menu. By doing so, you add the latest versions of all Java-stack patches relevant for your system.<br />Note that you might require readjustments to various system configurations to accommodate your regular business processes. We recommend that you execute the SCA Dependency Analysis according to SAP Note <a target=\"_blank\" href=\"/notes/1974464\">1974464</a>.</p>\r\n<p>---------------------&lt; I044400 OCT/07/14 &gt;-------------------------------<br /><strong>Using the ampersand symbol in passwords - Restriction<br /></strong>If you are using system passwords that contain the ampersand (\"&amp;\") symbol, you might encounter errors during a release upgrade. To avoid them, change&#160;the passwords&#160;accordingly&#160;before you start the upgrade process. You can restore&#160;the original system passwords after the process has finished.<br /><br /><br />---------------&lt; Update D023536 OCT/10/2014 &gt;--------------------------<br />--------------------&lt; D023536 SEP/30/2014 &gt;-----------------------------<br /><strong>Target Release SAP_BASIS 740 SP8: Single System Mode&#160;with Minimum Patch Level&#160;7</strong><br />If you want to update or upgrade your system to SAP_BASIS 740 SP8 with Single System mode, you need SUM 1.0 SP11&#160;with patch level 07 or higher.<br /><br /><br />--------------------&lt; I036200 DEC/13/2013 &gt;-----------------------------<br /><strong>Solution Manager only: SPS update of ABAP or Java stack independently is supported</strong><br />If you want to update the ABAP or Java stack of a dual-stack Solution Manager system to the latest SPS independently, use SUM for updating the Java stack and transaction SPAM/SAINT for updating the ABAP stack.<br />SUM supports this scenario for the Java stack but requires a special command on startup.<strong> <br /></strong>Detailed information is available in the Release Information Notes for SP Stacks of SAP Solution Manager, which are listed in SAP Note <a target=\"_blank\" href=\"/notes/1595736\">1595736</a>.<br /><br /><br />-----------------------&lt; I36200 JUL/26/2013 &gt;--------------------------<br /><strong>Update of shared Wily AutoProbe connectors not supported</strong><br />Performing updates in a system that uses a Wily AutoProbe connector shared with other systems is not supported. For example, if you have multiple systems on the same host, all systems use the same AutoProbe connectors, and you upgrade one of the systems, starting production operation of the remaining systems might fail.<br /><br /><br />---------------------&lt; D024828 MAY/17/2013 &gt;--------------------------<br /><strong>SAP NetWeaver 7.4: Release Restrictions and Limitations</strong><br />Before the update, you must familiarize with the following important SAP Notes:</p>\r\n<ul>\r\n<li>Note 1730102 - Release Restrictions for SAP NetWeaver 7.4</li>\r\n<li>Note 1751237 - Add. info about the update/upgrade to SAP NetWeaver 7.4&#160;(including service releases)</li>\r\n</ul>\r\n<p>Be aware that upgrade/update to SAP NetWeaver 7.4 is not supported for <strong>dual-stack </strong>NetWeaver systems. The only exception to this rule is SAP NetWeaver Process Integration on a traditional database system, which still requires a dual-stack implementation. For more information about the discontinuation of dual-stack deployments, see SAP Community Network at: <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-33703\">http://scn.sap.com/docs/DOC-33703</a>.</p>\r\n<p>See also the SAP Note 1751237 which additional information&#160;info about the update or upgrade to SAP NetWeaver 7.4 and higher.<br /><br /><br />---------------------&lt; D031178 MAY/17/2013 &gt;--------------------------<br /><strong>SAP Business Suite Powered by SAP HANA: Release Restrictions and Limitations</strong><br />Before the update, you must familiarize with the following important SAP Notes:</p>\r\n<ul>\r\n<li>Note 1774566: SAP Business Suite Powered by SAP HANA - Restrictions</li>\r\n<li>Note 1830894: Availability of SAP NetWeaver 7.4 on IBM i</li>\r\n</ul>\r\n<p>Be aware that upgrade/update to SAP Business Suite 7 Innovation 2013 and higher is not supported for <strong>dual-stack</strong> SAP Business Suite systems. For more information, see <a target=\"_blank\" href=\"/notes/1816819\">SAP Note 1816819</a>.</p>\r\n<p><br />---------------------&lt; D038245 MAR/25/2013 &gt;--------------------------<br /><strong>SUM requires SAP kernel 7.20 or higher for target release</strong><br />The Software Update Manager requires target release kernel 7.20 or higher for all platforms.<br />We recommend to always check before starting the update or upgrade whether there is a newer version of the SAP kernel available on SAP Service Marketplace and download it to the download directory.<br />Lower target release kernel versions are no no longer supported and may lead to an error during the update or upgrade.<br /><br /><br />-----------------------&lt; D029945 NOV/16/12 &gt;--------------------------<br /><strong>Built-in Capabilities of SUM to include customer transport requests</strong><br />As of version 1.0 SP06, the Software Update Manager 1.0 SP06 is enhanced with a new feature to reduce the downtime by including customer transport requests to the update phase.<br />However, this feature is not generally released yet. We offer interested customers and partners to use this new feature on an 'availability on request' basis.<br />The procedure to follow in order to make use of the inclusion of customer transport requests of the SUM is described in SAP Note 1759080 (Conditions for SUM including customer transport requests).<br /><br /><br />----------------- &lt; D041206 March/26/2013 &gt; -------------------------<br /><strong>Built-in capability near-zero downtime maintenance (nZDM/SUM)</strong><br />The Built-in capability near-zero downtime maintenance (nZDM/SUM) to run the main import almost in the shadow during uptime is generally available without any restrictions since SUM 1.0 SP7. nZDM/SUM is a main feature to optimize the downtime and is available in the advanced pre-configuration of SUM.<br />Further release information about nZDM/SUM is described in the note 1678565.<br /><br /><br />-----------------------&lt; D035496 APR/18/12 &gt;--------------------------<br /><strong>Applying Support Package 02 to Solution Manager 7.1 </strong><br />When applying Support Package stack 02 to SolMan 7.1 with the Software Update Manager, an error occurs for component BI_CONT.<br />If you want to import Support Package queues that contain this Support Package stack, you either use the Support Package Manager (SPAM) or you include at least Support Package stack 05 as well.<br /><br /><br />-----------------------&lt; I053650 DEC/19/11 &gt;--------------------------<br /><strong>Java, ABAP+Java: Check for sapstartsrv Patch Level on the Source System</strong><br />To guarantee that the Java or dual-stack SAP system can be correctly manipulated during the update/upgrade process, the Software Update Manager checks whether the patch level of SAP start service (sapstartsrv) on the source system is appropriate. This check is performed in the beginning of the SUM process so that known problems, related to system detection as well as proper stop and start of the SAP system, will be prevented. For more information, see SAP Note <strong>1656036</strong>.<br /><br /><br />------------------&lt; updated D029128 MAY/31/12 &gt;-----------------------<br />-----------------------&lt; D041112 NOV/22/11 &gt;--------------------------<br /><strong>SAP NetWeaver 7.3 EHP1: Release Restrictions and Limitations</strong><br />Before the update/upgrade, you must familiarize with the following SAP Note:<br />Release Restrictions for SAP EHP1 for SAP NetWeaver 7.3 - Note 1522700<br /><br /><br />-----------------------&lt; D038722 OCT/28/11 &gt;--------------------------<br /><strong>SAP NetWeaver 7.0 EHP3: Release Restrictions and Limitations</strong><br />Before the update/upgrade, you must familiarize yourself with the following important SAP Notes:</p>\r\n<ul>\r\n<li>Release Restrictions for EHP3 for SAP NetWeaver 7.0 - Note 1557825</li>\r\n<li>Installation of SAP EHP3 for SAP NetWeaver 7.0 - Note 1637366</li>\r\n<li>No Process Integration in SAP EHP3 FOR SAP NETWEAVER 7.0 - Note 1637629</li>\r\n</ul>\r\n<p><br />-----------------------&lt; D025095 APR/13/11 &gt;--------------------------<br /><strong>SAP NetWeaver 7.3: Release Restrictions and Limitations</strong><br />Before the update, you must familiarize with the following important SAP Notes:</p>\r\n<ul>\r\n<li>Release Restrictions for SAP NetWeaver 7.3 - Note 1407532</li>\r\n<li>SAP Business Suite PI Adapters for SAP NetWeaver PI 7.3 - Note 1570738</li>\r\n</ul>\r\n<p><br />----------------------&lt; I031257 SEP/08/09 &gt;---------------------------<br /><strong>JSPM NOT to be Used During the Update Process</strong><br />You must not run the tool Java Support Package Manager (JSPM) when running the Software Update Manager. This is critical for the successful completion of the process.<br /><br /><br /><br />b) ABAP<br /><br />/</p>\r\n<p><br /><br />c) Java</p>\r\n<p>-----------------------&lt;&#160;D023890 SEP/10/2014 &gt;--------------------------<br /><strong>New UI available for SUM Java scenarios</strong><br />Scenarios based on SUM Java can now be run using a new user interface that is named <em>SL Common Graphic User Interface.</em> This new UI is based on SAPUI5. It runs in a browser and does not require any Java UI component. The scenario <em>Database Migration Option (DMO) of SUM</em> uses already this UI. <br />For more information about the new UI and how to use it, see the blog \"SUM: extending the new UI to AS Java scenarios\", which is available in the SAP Community Network (SCN) at <a target=\"_blank\" href=\"http://scn.sap.com/community/it-management/alm/software-logistics/blog/2014/07/16/sum-extending-the-new-ui-to-as-java-scenarios\">http://scn.sap.com/community/it-management/alm/software-logistics/blog/2014/07/16/sum-extending-the-new-ui-to-as-java-scenarios</a>.<br /><br /><br />-----------------------&lt; D039661 OCT/28/2013 &gt;--------------------------<br /><strong>Installing SAP Business Suite Usage Types on Top of an Existing SAP NetWeaver Java System</strong><br />This installation scenario is described in the document <em>SAP Solution Manager: Special Cases in Installation and Upgrade</em> available at <a target=\"_blank\" href=\"http://service.sap.com/mopz\">http://service.sap.com/mopz</a>, section <em>How-Tos and Guides</em>. Follow the specific steps described in the chapter <em>Add Installation of SAP Business Suite Usage Types to Existing NW System</em>.</p>\r\n<p><br /><br /><strong>II/ Corrections to the Guide</strong><br /><br />a) General</p>\r\n<p>------------------------------------&lt;&#160;I044400 DEC/13/2014&#160;&gt;-----------------------------</p>\r\n<p><strong>Resetting the Software Update Manager&#160;</strong></p>\r\n<p>Note that when resetting the update during the Preparation roadmap steps, &#1072;fter having stopped the tool with the <em>Exit</em> or the <em>Cancel</em> button, if you want to reset the update procedure choose <em>Update</em>-&gt; <em>Reset Update</em>. Alternatively, you can return to the previous roadmap step by choosing <em>Back</em>, as it becomes active again. The <em>Reset Update</em> option should be preferred, if you plan to start the process from the beginning.</p>\r\n<p><br />-----------------------&lt; I046284 JUN/07/2012 &gt;-------------------------<br /><strong>User Management Engine on a remote instance host</strong><br />During the upgrade or update of an SAP system that has a User Management Engine(UME) located on a remote instance host, make sure that this instance is operational.</p>\r\n<p><br /><br />-----------------------&lt; I046284 JUN/07/2012 &gt;-------------------------<br /><strong>User Management Engine on a remote instance host</strong><br />During the upgrade or update of an SAP system that has a User Management Engine(UME) located on a remote instance host, make sure that this instance is operational.<br /><br /><br />b) ABAP<br /><br />-----------------------&lt;&#160;D031330 JUL/17/2014 &gt;-------------------------<br /><strong>STMS: Distributing the Configuration as Follow-up Activity</strong><br />In the SUM Guide the section \"6 Follow-Up Activities\"&#160;-&gt; \"Transport Management System: Distributing the Configuration\": The procedure within the note, that is relevant in case the display of the releases is not updated automatically, is also valid for target release SAP NetWeaver 7.4.<br /><br /><br />c) Java<br />------------------------&lt; I036200 8/9/2014 &gt;-------------------------------<br /><strong>Resetting the Software Update Manager during the Preprocessing Roadmap Step</strong></p>\r\n<p>The steps for deleting the update directory and afterward unpacking SUM again are not required. You can start the procedure from the beginning after the reset of the procedure has finished and&#160;SUM returns to the <em>Welcome</em> dialog.<br /><br />-----------------------------------------------------------------------<br /><br /><strong>III/ Preparing the Update</strong><br /><br />a) General<br /><br />------------------&lt; Update D019500 OCT/06/14 &gt;-----------------------<br />-----------------------&lt; D003550 SEP/05/14 &gt;--------------------------<br /><strong>Target release SAP_BASIS 740 SP7: Prevent Error in Conversion for Pool/Cluster Tables with CURR and QUAN colums</strong><br />To avoid this issue, set a break-point in phase PARCONV_UPG or PARCONV_TRANS and manually implement the correction attached in Note 1994569 once the break-point is reached. PARCONV_TRANS is only used in case of a resource-minimized execution of an EHP-installation, in all other execution modes PARCONV_UPG is executed. When the break-point is reached, the system must be unlocked and the manual correction must be performed by a non-DDIC user.<br />Note: This information is relevant for SUM 1.0 SP11 patch levels 00 to 05. The issue is solved with patch level&#160;06.<br /><br /><br />-----------------------&lt; D001330 AUG/18/14 &gt;--------------------------<br /><strong>Deactivate IOT feature for certain tables</strong><br />The SUM tool does not support the (Oracle) IOT feature for certain tables because the SAP dictionary does not&#160;support the IOT feature. The tables are in DBDIFF exception table for index differences between DB and SAP dictionary. The solution or workaround is to deactivate the IOT feature for such tables before the SUM run. After finishing the SUM run, the IOT feature may be activated again.<br />Note that if the tables are not present in the dictionary, you do not need to disable the IOT feature.<br /><br /><br />-----------------------&lt; D033486 OCT/14/10 &gt;--------------------------<br /><strong>Apply SAP Note 1910464 before the import of certain SPs</strong><br />If your system is on one of the following Support Package levels<br />SAPKB70029<br />SAPKB70114<br />SAPKB70214<br />SAPKB71017<br />SAPKB71112<br />SAPKB73010<br />SAPKB73109<br />SAPKB74004<br />and you want to import the next Support Packages, apply SAP Note 1910464 before the import.<br /><br /><br />----------------&lt; Update&#160;D023890 OCT/13/2014 &gt;-----------------------<br />---------------------&lt; D035061 AUG/22/2012 &gt;--------------------------<br /><strong>No \"Single System\" mode on dual stack systems</strong><br />On dual stack systems, do not use the preconfiguration mode \"Single System\". Use preconfiguration mode \"Standard\" or \"Advanced\" instead.<br />In the case of&#160;a Solution Manager system, see also the&#160;coment &#8220;Solution Manager only: SPS update of ABAP or Java stack independently is supported&#8221; in section \"I/ Important General Information\" -&gt; \"a) General\".<br /><br /><br />---------------------&lt; D035061 AUG/22/2012 &gt;--------------------------<br /><strong>Implement note 1720495 before you start transaction SPAU <strong>or include the related Support Package</strong></strong><br />Make sure to implement SAP note 1720495 immediately after a Support Package import, an enhancement package installation, or an upgrade and before making any adjustments in transaction SPAU. Alternatively, you can include the related Support Package for your target release.<br />Otherwise you can observe after an EHP installation or a Support package import notes with status 'SAP note &lt;no.&gt; obsolete; de-implementation necessary' in transaction SPAU, when adjusting the note, and in the Snote. If you proceed in transaction SPAU and adjust this kind of notes, the de-implementation of the note can destroy the code of the support package and can make the system inconsistent.<br />The SAP note 1720495 corrects the status of the notes and avoids wrong deimplementation of obsolete notes.<br /><br /><br />---------------------&lt; D035061 JUL/23/2012 &gt;--------------------------<br /><strong>Check platform-specific requirements for the 7.20 EXT kernel</strong><br />In case you want to install the 7.20 EXT kernel, or you want to change to this kernel, check beforhand the platform-specific requirements.<br />See SAP note 1553301 for the 7.20 EXT kernel requirements, especially section \"Platform-Specific Information\".<br />If the requirements are not met, errors might occur in phase TOLVERSION_EXTRACT.<br /><br /><br />---------------------&lt; D035061 JUL/23/2012 &gt;--------------------------<br /><strong>Server Group SAP_DEFAULT_BTC must include Primary Application Server Instance</strong><br />If you have defined a batch server group SAP_DEFAULT_BTC according to SAP Note 786412, make sure that the primary application server instance is included in this server group.<br /><br /><br />-----------------------&lt; D029385 MAR/08/11 &gt;--------------------------<br /><strong>Upgrade on Windows only: Check registration of sapevents.dll</strong><br />During the installation of your source release system, the installation procedure might have inadvertently registered the sapevents.dll from the central DIR_CT_RUN directory instead of from the local DIR_EXECUTABLE directories of the instance. This may lead to errors with locked kernel files during or after the upgrade. Therefore check the registration of sapevents.dll as described in SAP Note 1556113.<br /><br /><br />-----------------------&lt; D037517 08/DEC/10 &gt;--------------------------<br /><strong>Adjust Start Profile</strong><br />Before you start the update, add the SAPSYSTEM parameter to the start profile of your system. The entry should look like:<br />SAPSYSTEM = &lt;instance number&gt;<br /><br /><br />----------------------&lt; D053561 20/OCT/10 &gt;---------------------------<br /><strong>Apply SAP Note to Avoid Error During Reset</strong><br />We recommend to apply SAP Note 1518145 before starting the Software Update Manager (if it is not yet in your system as part of the corresponding Support Package). This note prevents an error that can occur when you reset the update procedure during the Preprocessing roadmap step. Without this note, some tables that need to be deleted during the reset cannot be deleted.<br /><br /><br />--------------------&lt; D033899 31/MAY/10 &gt;-----------------------------<br /><strong>Preventing Errors with Table \"SATC_MD_STEP\"</strong><br />To avoid problems during the update due to conflicting unique indexes, apply the correction described in SAP Note <strong>1463168</strong> or apply the corresponding Support Package.<br /><br /><br /><br />b) ABAP</p>\r\n<p>---------------------&lt;&#160;D056915 DEC/12/2014 &gt;---------------------------<br /><strong>Implement SAP Note 2091348 if SAP Note 1696748 is already implemented</strong><br />To avoid that copying a user for transaction SPDD fails during the update, implement SAP Note 2091348. See also in section<em> IV/ Problems During the Update Roadmap Steps -&gt; ABAP</em> the entry \"Copying user for SPDD fails\".<br /><br /><br />---------------------&lt;&#160;D023536 OCT/21/2014 &gt;---------------------------<br /><strong>De-implenment SAP Note 1819126 before the upgrade or update</strong><br />If you have implemented the SAP Note 1819126 in your system, you must de-implement it before you start the update or upgrade of your basis support packages to a support package that does not include this SAP Note. Otherwise a syntax error may occur in phase XPRAS_AIMMRG. Implement the SAP Note 1819126 again after the update or upgrade.<br />See SAP Note 2069772 for more information.<br /><br /><br />---------------------&lt; I36200 AUG/11/2014 &gt;---------------------------<br /><strong>Preventing issues with Zero Administration Management (ZAMM)<br /></strong>If you perform a release upgrade in an ABAP or dual-stack system, make sure that you are familliar with the information provided in SAP Note <a target=\"_blank\" href=\"/notes/88416\">88416</a>. In addition, if you face ZAMM issues caused by&#160;insufficient page size and virtual memory, see SAP Note <a target=\"_blank\" href=\"/notes/1518419\">1518419</a>.<br /><br /><br />-----------------------&lt;&#160;D035956 JUL/03/2014 &gt;--------------------------<br /><strong>SFW activation: Prevent automatic activation of BC-Sets in all clients<br /></strong>In particular if table SFWPARAM contains an entry: NAME = 'SBCSETS_ACTIVATE_IN_CLIENTS' and VALUE = 'X', follow the steps described in SAP Note 2035728.<br /><br /><br />-----------------------&lt; D024828 FEB/28/2014 &gt;--------------------------<br /><strong>Consider SAP Note 1983758 while preparing the update</strong><br />When you prepare the update/upgrade, consider SAP Note 1983758 to make sure that BW clients are not set wrongly by a previous update. Otherwise BW-specific error messages might occur during the follow-up activities.<br /><br /><br />-----------------------&lt; D037517 DEC/13/2013 &gt;--------------------------<br /><strong>Exclude Z languages</strong><strong><br /></strong>If you have installed your own languages (Z languages ) in the SAP system, you must exclude them from the upgrade or update process.<br />For this, you maintain the Z languages in the SAP system before the upgrade or update as follows:</p>\r\n<ol>\r\n<li>Logon to the SAP system</li>\r\n<li>Choose transaction SE16 and open table T002C</li>\r\n<li>Choose all Z language rows (max. Z1 &#8211; Z9) for editing</li>\r\n<li>Remove the &#8220;x&#8221; (if exists) from field &#8220;can be installed&#8221; (fieldname LAINST</li>\r\n</ol>\r\n<p><br /><br />--------------------&lt; D035061 FEB/12/2013 &gt;---------------------------<br /><strong>Remove usages of customer-developed objects before you start SUM</strong><br />The Software Update Manager applies the following security notes during the Extraction roadmap step by overwriting or deleting the related objects without further notice:<br />1668465, 1631124, 1631072, 1628606, 1584549, 1584548, 1555144, 1526853, 1514066, 1453164<br />If you have not installed those notes yet, check if any customer-developed objects make use of deleted or disabled objects and remove those usages before you start the Software Update Manager.<br /><br /><br />----------------&lt; Update&#160;d023536 15/JUL/14 &gt;--------------------------<br />--------------------&lt; D053561 13/DEC/12 &gt;-----------------------------<br /><strong>See SAP Note 1413569 for table SMSCMAID<br /></strong>Before you start the Software Update Manager, check table SMSCMAID for duplicate records concerning field SCHEDULERID. Otherwise the upgrade might fail during downtime in phase PARCONV_UPG because a unique index cannot be created due to duplicate records in the table. See SAP Note 1413569 for more details.<br /><br /><br />--------------------&lt; D040050 19/SEP/12&gt;-----------------------------<br /><strong>NTsystems: Install latest sapstartsrv before the update</strong><br />If you use an NT-system with the &lt;SID&gt;ADM as domain user, make sure that you apply the SAP Note 1756703 before you start the update. Otherwise, if you enter during the update in phase PREP_PRE_CHECK/PROFREAD the &lt;SID&gt;ADM name and password as domain user, the phase PREP_INPUT/INSTANCELIST_PRE will not accept the &lt;SID&gt;ADM password, and the SUM stops.<br /><br /><br />----------------------- &lt;D020214 30/MAY/11 &gt;--------------------------<br /><strong>Preventing Errors Due to Unicode Length</strong><br />A structure exists that uses a table type with a structure in at least<br />one component. When You call DDIF_FIELDINFO_GET, the system sometimes automatically selects the system Unicode length instead of the UCLEN<br />Unicode length that is specified in DDIF_FIELDINFO_GET. This can lead to an RFC error in phase PREP_INIT/SPAM_CHK_INI during the upgrade or update. To prevent this error, see SAP Note 1029444 and implement the attached correction instruction.<br /><br /><br />----------------------- &lt;D003550 15/DEC/08 &gt;--------------------------<br /><strong>Preventing Activation Errors</strong><br />Under certain circumstances, information of runtime objects might get lost during the activation. This can lead to unwanted conversions of cluster tables. To prevent this error, see SAP Note <strong>1283197</strong> and implement the attached correction instruction.<br /><br /><br />c) Java<br /><br />----------------------&lt;I077286 29/APR/2013&gt;---------------------------<br /><strong>Setting the instance profile parameter AutoStart to '0'</strong><br />Before an upgrade, in each instance profile you must manually set the vaule of the AutoStart parameter to 0. This must be done for each application server instance. Proceed as follows:</p>\r\n<ol>1. Log on to the instance.</ol><ol>2. Navigate to the &lt;drive&gt;:\\user\\sap\\&lt;SID&gt;\\SYS\\profile\\ folder.</ol><ol>3. Edit the &lt;SID&gt;_&lt;Instance Name&gt;_&lt;host name&gt; profile, and change the value of the AutoStart parameter to 0, that is,</ol>\r\n<p><strong>AutoStart = 0 </strong>.</p>\r\n<p>This must be done to prevent unexpected start of instances, which might lead to connection errors.<br /><br /><br />----------------------&lt; I030727 21/SEP/2012 &gt;------------------------<br /><strong>Upgrade from SAP NetWeaver 2004 to SAP NetWeaver 7.3 EHP1 Based PowerPC System on Linux: Update Your Instance Profile</strong><br />Before starting an upgrade from SAP NetWeaver 2004 to SAP NetWeaver 7.3 EHP1 based PowerPC system on Linux, you need to add the following parameter to your instance profile:<br />jstartup/native_stack_size = 2097152<br /><br /><br />----------------------&lt; I035760 30/JAN/09 &gt;----------------------------<br /><strong>Cleaning Up the Profile Directory</strong><br />Before starting the Software Update Manager, you need to clean up the profile directory. Remove any old, unused profiles and move any backup copies to another directory. The profile directory must only contain active profile files. By default, it is located in the central file share:<br />For UNIX: /sapmnt/&lt;SAPSID&gt;<br />For Windows: &lt;Drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\SYS<br />For IBM i: /sapmnt/&lt;SID&gt;<br /><br /><br /><br />-----------------------------------------------------------------------<br /><br /><strong>IV/ Problems During the Update Roadmap Steps</strong><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.<br /><br />a) General</p>\r\n<p><br />---------------------&lt; D038245 AUG/27/2014 &gt;-----------------------------<br /><strong>Windows: Unknown macro 'VCREDISTPATH' in phase EXTRACTKRN_PRE<br /></strong>Description: <br />The following error occurs in phase EXTRACTKRN_PRE on Windows: <em>Unknown macro 'VCREDISTPATH'</em>, and you see in the log \"EXTRKRN.LOG\" the entry:&#160;<em>PID XXXXX exited with status 3010</em><br />Reason:<br />The actual problem is that the installation of the visual studio library requires a restart of the&#160;operating system&#160;to complete the installation&#160;process.<br />Solution:<br />If the new SAP kernel does not work properly, the host has to be restarted. The error should vanish on phase repeat.<br /><br /><br />-----------------------&lt; I069357 NOV/11/2013 &gt;--------------------------<br /><strong>Correcting Issues with Resetting the Upgrade</strong><br />After you have reset the upgrade and a new SUM run is started, after using the \"Cleanup and start afresh\" option the buttons \"Next\" and Back\" on the SUM GUI might be inactive. Alternatively, SUM may crash after the reset procedure when selecting 'Exit' or 'Cleanup and start afresh'\".<br />In this case, proceed as follows:</p>\r\n<ul>\r\n<li>For the ABAP part of the upgrade: Check in the log-file SAPup.log whether the phase SAVELOGS_RESET is mentioned in the end, either as started or skipped.</li>\r\n<li>For the Java part of the upgrade: Check in the Java tab of the SUM GUI whether all steps have been completed.</li>\r\n<li>Afterwards extract the SUM archive again to a new directory and start the new SUM run.</li>\r\n</ul>\r\n<p>The above solution is valid when you reset the update by repeatedly choosing the \"Back\" button on the SUM GUI, or if you choose the Update -&gt; Reset Update option.<br /><br /><br />------------------&lt; I077286 23/OCT/2013 &gt;----------------------------<br /><strong>Shadow instance reset during an upgrade</strong><br /><strong>Linux- or Windows-based systems only:</strong> If during a switch upgrade you reset the SUM procedure after the SET_ENGINE_SAFE_MODE step has passed, afterwards SUM might stop at step \"DELETE_SHADOW_DIR\" with the following message:<br /><br />Error while executing Task with input file FsoTasks.xml and task<br />DELETE_SHADOW_DIR. Could not finish Delete operation with parameters<br />file '', directory '&lt;drive:&gt;/usr/sap/&lt;SID&gt;/SUM/sdt/&lt;SID&gt;'. Check whether the<br />current user has sufficient permission to execute this operation. Cannot<br />delete file &lt;drive:&gt;\\usr\\sap\\&lt;SID&gt;\\SUM\\sdt\\&lt;SID&gt;. Cannot delete<br />&lt;drive:&gt;\\usr\\sap\\&lt;SID&gt;\\SUM\\sdt\\&lt;SID&gt; using Windows console operation. 0<br /><br />To correct this issue, proceed as follows:</p>\r\n<ol>1. Stop the Software Update Manager back-end process. To do this, in the GUI menu bar choose Update -&gt; Stop Update. If required, shut down the SUM GUI as well.</ol><ol>2. Start the Software Update Manager back-end process and GUI again.</ol>\r\n<p><strong>NOTE:</strong> After you have started the SUM GUI, <strong>do not</strong> choose Next.</p>\r\n<ol>3. Reset the SUM update. To do this, in the SUM GUI menu bar choose Update -&gt; Reset Update.</ol><ol>4. Wait until the dialog for repeating a failed step appears again.</ol><ol>5. Repeat the step from point of failure.</ol>\r\n<p><br /><br />---------------------&lt; D026178 AUG/22/2013 &gt;--------------------------<br /><strong>Phase XPRAS_UPG: Errors during post-handling \"RS_AFTER_IMPORT\"</strong><br />In phase XPRAS_UPG, the following error can occur:<br />2EEPU133 Errors occurred during post-handling \"RS_AFTER_IMPORT\" for \"WWIB\" \"L\"<br />This after-import method can fail if only SAP_BASIS 740 with SP03 or lower is imported.<br />To continue the upgrade, see SAP Note 1839664 in which a workaround is described.<br />To prevent this issue before starting the upgrade, see SAP Note 1894463.<br /><br />The above procedure is required as temporary data for operating the shadow instance is locked and cannot be removed when the SUM procedure needs to do so.<br /><br /><br />---------------------&lt;I067708 23/MAY/2013&gt;---------------------------<br /><strong>Correcting issues with primary application server instance host name connectivity</strong><br />During SUM startup, in the console the following error might appear:<br />'Unable to detect an abap and/or AS java instance of the SAP system &lt;SID&gt; installed on host &lt;host name&gt;' .<br />To correct this error, restart SUM and after the startup command enter the command 'hostname=&lt;host name&gt;', where &lt;host name&gt; is the primary application server instance host. For example:<br />'STARTUP.BAT hostname=wdmlbmd5799' .<br />The error might appear when the host where the instance profile directory is located is not directly accessible due to, for example, a specific customer setup.<br /><br /><br />-----------------------&lt; D035061 05/FEB/13 &gt;--------------------------<br /><strong>Wrong MCOD warning for dual stack systems</strong><br />In case your system is a dual stack system with an independent schema name for the JAVA part, this schema might be detected as MCOD setup. In consequence of this, a number of warnings and tasks might be recommended by the tool and the guides, that apply to real MCOD systems.<br />If your dual stack system is the only system on the database, you can ignore related warnings and skip any MCOD related tasks.<br /><br /><br />-----------------------&lt; D038245 10/NOV/11 &gt;--------------------------<br /><strong>UNIX: Prevent Overwriting sapuxuserchk During the Update/Upgrade</strong><br />In case sapuxuserchk inside instance executable directories gets overwritten during the update/upgrade process, the startup of those instances might fail due to failing authentication for sapcontrol calls executed by SUM.<br />It must be prevented that the set-user-ID bit for sapuxuserchk inside the instance executable directories is overwritten during kernel switch. To do this, apply the solution described in SAP Note <strong>1650797</strong> after the Checks roadmap step and before downtime start.<br /><br /><br />-----------------------&lt; D021970 01/FEB/12 &gt;--------------------------<br /><strong>Dual-Stack System Update: Phase STARTSAP_PUPG Fails</strong><br />In special cases during an update (for example, if you perform manual actions after SAP kernel switch), Java shared memory conflicts might be reported in phase STARTSAP_PUPG.<br />To solve the problem, you need to run the \"cleanipc all\" command and then repeat the failing phase.<br /><br /><br /><br />b) ABAP</p>\r\n<p>---------------------&lt; D056915 DEC/12/2014 &gt;--------------------------<br /><strong>Copying user for SPDD fails</strong><br />You are running an upgrade and you are prompted in a SUM dialog to run transaction SPDD. In your system, SAP&#160;Note&#160;1696748 is implemented .<br />Due to&#160;the&#160;prompt, you&#160;want to copy a user (for example&#160;user DDIC) with which you run transaction SPDD. However, the copy procedure fails with an error message in the following way:</p>\r\n<p style=\"padding-left: 30px;\">Error: DBSQL_TABLE:UNKNOWN<br />\"SQL message: invalid table name: Could not find table/view<br />MEP_USR_DIM_STAT in schema SAPSR3SHD: line 1 col 85 (at pos 84)\".</p>\r\n<p>To solve this issue, carry out the following procedure.</p>\r\n<ol>\r\n<li>Log on to the shadow instance in client 000 with a user different from DDIC but with the apropriate privileges.&#160;Example: DDIC_DEV</li>\r\n<li>Start&#160;transaction SE20 and choose option \"Enhancement Implementation\".</li>\r\n<li>Enter enhancement implementation ES_MEP_CHANGEDOC_EVENT_IMPL and choose \"Change\".</li>\r\n<li>In section \"Runtime Behaviour\" of the new dialog, deactivate the option 'Implementation is active', and save the change.&#160;This is a modification and will be recorded in a transport request.</li>\r\n<li>Then copy the user to run transaction SPDD.</li>\r\n<li>Run now transaction SPDD with the copied user.</li>\r\n<li>Log on again to client 000 with the user from step 1 (for example DDIC_DEV) and&#160;reverse the setting done in step 2, that is, you activate&#160;again the enhancement implementation ES_MEP_CHANGEDOC_EVENT_IMPL.</li>\r\n</ol>\r\n<p><strong>CAUTION: </strong>This procedure is only feasible if a development user is available that differs from user DDIC in client 000. Otherwise you must reset the upgrade and implement SAP Note 2091348 before you restart the upgrade. <br /><br /><br /></p>\r\n<p>--------------------&lt; D023536 SEP/30/2014 &gt;-----------------------------<br /><strong>Aborted conversions during phase PARCONV_UPG in step 6</strong><br />Symptom: The conversion of tables aborts during phase PARCONV_UPG in step 6 of the conversion while creating dependent views.<br />Solution:&#160;See SAP Note 2070458 for further details.<br /><br /><br />---------------------&lt; D024828 SEPT/16/2014 &gt;--------------------------<br /><strong>HP-UX for IA64 only: Correcting issues during phase PREP_PRE_CHECK/INITPUT_PRE<br /></strong>If you have encountered an error in this phase with an error message <em>Password for 'sapsso' does not work, please reenter, </em>proceed as described in SAP Note <a target=\"_blank\" href=\"/notes/2061940\">2061940</a>.</p>\r\n<p>---------------------&lt; D023536 APR/11/2014 &gt;--------------------------<br /><strong>P messages in LONGPOST.LOG due to BW object activation</strong><br />It may occur that the file LONGPOST.LOG contains P error messages concerning the activation of BW objects, such as:</p>\r\n<p>2PETK754 Error when creating object directory entry \"R3TR\" \"TABL\" \"/BI0/STCAIFAREA\" <br />A2PERSTCO_UT 003 An error occurred while activating BW object &amp;2\"0TCAIFAREA\" (&lt;(&gt;&amp;&lt;)&gt;1\"IOBJ\")&amp;3\"OM\"</p>\r\n<p>SUM has added these message in phase XPRAS_AIMMRG during the execution of report RS_TCO_ACTIVATION_XPRA. This report installs basic technical content BW objects, but their activation generates in some cases an error. Check the log file to see what BW objects cause problems during the activation. You can execute the report again manually after the upgrade phase. If there are still problems with some objects, try to activate the affected objects using transaction RSOR.<br />You can also skip the execution of report RS_TCO_ACTIVATION_XPRA during the upgrade. In this case, make sure that you execute this report manually after the upgrade phase. See SAP Note 1629923 for more information.<br /><br /><br /></p>\r\n<p>---------------------&lt; D024828 FEB/28/2014 &gt;--------------------------<br /><strong>Runtime error INSERT_PROGRAM_NAME_BLANK in phase XPRAS_AIMMRG</strong><br />If the update or upgrade stops in phase XPRAS_AIMMRG with the runtime error INSERT_PROGRAM_NAME_BLANK, see SAP Note 1941711 for more information and further instructions.<br /><br /><br />---------------------&lt; D020904 FEB/03/2014 &gt;--------------------------<br /><strong>(Windows only:) Error \"Access denied\" in phase MAIN_UPTRANS/UPCONF</strong><br />During an update or upgrade, the process may stop with the following error message:<br />Severe error(s) occured in phase MAIN_UPTRANS/UPCONF!<br />Last error code set: Cannot kill process 59720: Access is denied.<br />The error may disappear after repeating the phase. If the error occurs again, see SAP Note 1973135 for more information.<br /><br /><br />---------------------&lt; D041506 JAN/14/2014 &gt;--------------------------<br /><strong>DYNPRO_NOT_FOUND during parallel DBCLONE processes<br /></strong>In phase MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE, several DBCLONE processes have been started and run in parallel, but some of them stop with ST22 dump DYNPRO_NOT_FOUND. For more information and a solution, see SAP Note 1964350.<br /><br /><br />---------------------&lt; D024828 DEC/16/2013 &gt;--------------------------<br /><strong>Solaris X86_64: Timeout Error during phase MAIN_NEWBAS/STOPSAP_FINAL</strong><br />You use a Solaris X86_64 system and encounter a timeout error during phase MAIN_NEWBAS/STOPSAP_FINAL. A repeat of the phase will solve the error.<br /><br /><br />---------------------&lt; D023536 JUL/16/2013 &gt;--------------------------<br /><strong>Phase ACT_UPG: Certain search helps could not be activated</strong><br />During an update or upgrade, the Software Update Manager displays in phase ACT_UPG error messages such as<br /><br />1EEDO519 \"Srch Help\" \"H_5ITCN\" could not be activated&#8221; or<br />1EEDO519 \"Srch Help\" \"H_5ITTT\" could not be activated&#8221;</p>\r\n<p>You can ignore these errors. See SAP Note 1243014 for more information</p>\r\n<p><br />-----------------------&lt; D035061 OCT/14/10 &gt;--------------------------<br /><strong>Error in Phase MAIN_NEWBAS/SUCCCHK_PROD</strong><br />Symptom:<br />You read in the log-file PHASES.LOG the following line:<br />2EETQ399 Change request 'SAPK-&lt;vers&gt;INCTSPLGN not removed from buffer<br />This message indicates, that the import of a transport request of the CTS Plug-In could not performed.<br /><br />Reason known to date:<br />The system could not create primary indices for tables, if an object with the same name already exists. To solve this issue, drop the tables CL_CTS_REQUEST_REMOTE&#126;&#126;OLD and CTS_REQ_OVERLAPS&#126;&#126;OLD directly in the database.<br /><br />Solution:<br />1. Find out the cause of the import error and resolve it.<br />2. Start the report /CTSPLUG/CTS_ACTIVATION and set the parameter FORCEACT='X'. This report starts the import again.<br />3. Afterwards, check whether the import was successful and the import queue is empty now. If the problem continues to exist, contact the SAP Support.<br />4. If the report has run successfully, repeat the phase<br />MAIN_NEWBAS/SUCCCHK_PROD in the Software Update Manager to continue the upgrade<br /><br /><br />---------------------&lt; D023536 JUL/16/2013 &gt;--------------------------<br /><strong>Phase ACT_UPG: Certain tables cannot be activated</strong><br />During an update or upgrade, the Software Update Manager displays in phase ACT_UPG the error message that the following tables cannot be activated :</p>\r\n<ul>\r\n<li>/SOMO/MA_S_KEYFIG and</li>\r\n</ul>\r\n<ul>\r\n<li>/SOMO/MA_S_MONOBJ</li>\r\n</ul>\r\n<p>You can ignore this error by using the option \"Accept non-severe errors\".<br /><br /><br /></p>\r\n<p>---------------------&lt; D035061 MAY/15/2013 &gt;--------------------------<br /><strong>Error in phases XPRAS_SHD_AIMMRG, XPRAS_AIMMRG, OR XPRAS_TRANS</strong><br />During an update or upgrade, the SUM can stop during the phases XPRAS_SHD_AIMMRG, XPRAS_AIMMRG, OR XPRAS_TRANS with an error message using the following model:<br />Object '&amp;' language '&amp;' not created<br />In this case, you need to apply SAP Note 1866886. Implement manually the correction which corresponds to your target release. Afterwards repeat the affected phase in the SUM.<br />To avoid the error proactively, select a target Support Package level that covers that note.<br /><br /><br />---------------------&lt; d001330 MAY/13/2013 &gt;--------------------------<br /><strong>Phase XPRAS_UPG: Problems in FDT_AFTER_IMPORT or FDT_AFTER_IMPORT_C</strong><br />If you encounter a problem during the upgrade in phase XPRAS_UPG in method FDT_AFTER_IMPORT or FDT_AFTER_IMPORT_C that requires a code correction, see SAP Note 1357207 for further information and consider the solution in the note.<br /><br /><br />-----------------------&lt; D051861 APR/24/13 &gt;--------------------------<br /><strong>View EPIC_V_BRS_BSEG not activated</strong><br />The activation of view EPIC_V_BRS_BSEG fails during an upgade or update. In the log file of the ABAP Dictionary activation, you see the following message: View \"EPIC_V_BRS_BSEG\" was not activated<br />To solve this issue, see SAP Note 1846998.<br /><br /><br />----------------------&lt; D035061 25/MAR/13 &gt;--------------------------<br /><strong>RFC_COMMUNICATION_FAILURE during phase CHECKSYSSTATUS</strong><br />When additional instances are installed but currently stopped, the phase CHECKSYSSTATUS stops with an RFC_COMMUNICATION_FAILURE error regarding these instances.<br />In this case, start all installed instances during this phase so that their status can be checked. After the phase has passed successfully, you can stop the installed instances again if required. Alternatively, the services of the stopped instances need to be stopped as well, followed by a reset of SUM and start from scratch.<br /><br /><br />-----------------------&lt; D053561 OCT/10/10 &gt;--------------------------<br /><strong>tp 212 error in MAIN_NEWBAS/TABIM_UPG: left-over semaphore</strong><br />The SUM stops during phase TABIM_UPG in module MAIN_NEWBAS with the following error message:<br /><br />Calling &lt;path to&gt; tp.exe failed with return code 212, check &lt;path to&gt; SAPup.ECO for details.<br /><br />This stop can occur when tp does not finish correctly due to one or more semaphores that haven't been removed correctly after the end of r3trans. In general, it can happen in each phase where tp is started that semaphores are<br />left over. Note that the semaphores have to be kept if the tp or R3trans process of them still exists.<br /><br />If you encounter the error, use the operating system first to check whether the corresponding tp is still active, for example, whether the process still exists. For more information about semaphores in tp and how to solve the problem, see SAP Note 12746.<br /><br /><br />---------------------&lt; I065580 11/SEP/12 &gt;---------------------------<br /><strong>MAIN_NEWBAS/STARTSAP_TBUPG:System start failed on NT IA64</strong><br />During the MAIN_NEWBAS/STARTSAP_TBUPG phase the upgrade stops with the error message:<br />Checks after phase MAIN_NEWBAS/STARTSAP_TBUPG were negative! (...) System start failed<br />A possible solution can be note 1696517. To prevent the error, select a target kernel version that includes the related correction.<br /><br /><br />---------------------&lt; D035061 13/AUG/12 &gt;---------------------------<br /><strong>Error while sapcpe copies vcredist_x64.msi</strong><br />While using the Software Update Manager for updates or upgrades from systems based on NetWeaver 7.1, errors might occur in phase MOD_INSTNR_POST when sapcpe tries to copy the file vcredist_x64.msi.<br />In this case, enable the write access on this file and repeat the phase.<br /><br /><br />---------------------&lt; D028310 05/JUL/11 &gt;---------------------------<br /><strong>Missing tables during DB02 check after reset of update</strong><br />You have reset an update and before you restart this update, you run a DB02 check (Missing Tables and Indexes). This check comes to the result that two tables (CLU4 and VER_CLUSTR) are missing in the database.<br />You can ignore these missing tables and continue with the upgrade. The nametabs, which caused this DB02 check result, will be deleted after the update has been finished.<br /><br /><br />-------------------- &lt; D037517 18/MAY/11 &gt; -------------------------<br /><strong>Phase STARTSAP_PUPG: System start of the dialog instances failed</strong><br />Checks after phase MAIN_NEWBAS/STARTSAP_PUPG were negative! Last error code set: Unknown dialogue variable 'INSTLIST' System start failed.<br />Solution: Install the latest version of the Visual C++ Runtime on the host for the dialogue instances. Repeat the upgrade step afterwards.<br /><br /><br />--------------------- &lt; D035061 17/MAY/11 &gt; -------------------------<br /><strong>Phase ACT_UPG during EHP installation</strong><br />In this phase, either the following short dump can occur:<br />Runtime Errors.........TSV_TNEW_PAGE_ALLOC_FAILED<br />or this phase can take unusually long time.<br />In this case refer to note 1387739 and follow the instructions there.<br /><br /><br />--------------------&lt; D031901 22/OCT/10 &gt;-----------------------------<br /><strong>Preprocessing:ERROR: Found pattern \"R3load:...</strong><br />You have reset the update in the Preprocessing roadmap step and now, when running the Software Update Manager again, you get the following error message:<br />\"ERROR: Found pattern \"R3load: job completed\" 0 times, but expected 1! Analyze the log file for further error messages or program abort.\"<br />This error occurs if you did not apply SAP Note 1518145 before the reset. To solve the problem, repeat the phase in which the error occured.<br /><br /><br />------------------------&lt; D030559 07/JUN/10 &gt;--------------------------<br /><strong>Preventing long runtime of SUSR_AFTER_IMP_PROFILE</strong><br />During the update, the after-import method SUSR_AFTER_IMP_PROFILE can have a long runtime, and there may be a longer system downtime as a result.<br />For information about how to prevent long runtimes of this method, see SAP Note 821496.<br /><br /><br />--------------------&lt; D028597 29/APR/08 &gt;----------------------------<br /><strong>Modification Adjustment with SPDD</strong><br />If you adjust modifications during the enhancement package installation using transaction SPDD and mark the change request with the \"Select for transport\" function, you are asked whether the modification adjustment is performed for an Upgrade or for a Support Package update. Choose \"Upgrade\".<br /><br /><br />-----------------&lt; updated D023536 14/OCT/09 &gt;-------------------------<br />----------------------&lt; D023536 19/SEP/08 &gt;----------------------------<br /><strong>Activation Error</strong><br />An activation error for domains can occur if your source release system has a Support Package level of SAP NetWeaver 7.1 SP3, SP4, SP5 or SP6. The issue can be solved by repeating the failed phase. For more information, see SAP Note 1242867. If you have already implemented SAP Note 1242867 in your system, the error does not occur.<br /><br />-------------------- &lt; C5003135 26/AUG/08 &gt; ---------------------------<br /><strong>Phase MAIN_SHDIMP/SHDUNINST_DB</strong><br />Description: In this phase, the following error might occur<br />ERROR: Error by drop user SAP&lt;SID&gt;SHD<br />In the detailed log files, you may find the error, that the user is still connected to the database. For example, for SAP MaxDB, the error message in the file XCMDOUT.LOG is as follows:<br />-7048,DROP/ALTER USER not allowed while user is connected<br />Solution: Wait for about 15 minutes and then repeat the phase. You can also repeat the phase, if you cannot clearly identify in the detailed log whether the above mentioned error occured, since repeating the phase is harmless. You can also repeat the phase several times.<br /><br /><br />c) Java</p>\r\n<p>---------------------&lt; I042050 FEB/11/2014 &gt;-----------------------------<br /><strong>Windows only: Correcting Issues in the \"Delete Old Java Content\" Step<br /></strong>During the removal of redundant update process data, SUM might stop with an error stating that a folder under the &lt;DRIVE&gt;:\\usr\\sap\\&lt;SID&gt;\\&lt;Instance_name&gt;\\j2ee\\ path cannot be deleted, for example the \"admin\" folder. This issue might occur when a Windows process named \"conime.exe\" has a handle on the folder, thus preventing it from deletion.<br />To correct this issue, first you have to end the \"conime.exe\" process (for example, by using the Windows Task Manager) and&#160;then repeat the failed SUM step.</p>\r\n<p><br />-------------------------&lt; I024107 27/FEB/14&#160;&gt;-------------------------<br /><strong>SAP NetWeaver 7.3 and higher only: Preventing&#160;refusal issues caused by column drop of NZDM_VALUE2<br /></strong>While updating your SAP system,&#160;an error similar to the following might appear:</p>\r\n<p>E R R O R ******* (DbDeployConfig dev &lt;Date&gt; &lt;Time&gt;)<br />&lt;Time&gt; &lt;Date&gt; dbs-Error:&#160; Refuse due to drop column NZDM_VALUE2</p>\r\n<p>The issue is caused by an additional column added facilitate the near-Zero Downtime Maintenance Java tool. To correct this issue, proceed as described in SAP Note <strong>1978632</strong>.<br /><br /><br />-----------------------&lt; I071217 28/MAR/13 &gt;--------------------------<br /><strong>Preventing Issues with Wily AutoProbe connectors</strong><br />The Software Update Manager may not be able to rebuild the Wily AutoProbe connectors of the Wily Introscope Agent. To prevent this, ensure the following:</p>\r\n<ol>\r\n<li>The &lt;SID&gt;adm user executing the update process must have full administrator rights for the directories containing a Wily AutoProbe connector.&#160;</li>\r\n<li>Verify that the name of each used Wily AutoProbe connector jar file is connector.jar. If the name of a connector is AutoProbeConnector.jar, you have to rename it to connector.jar.</li>\r\n</ol>\r\n<p><br />----------------------&lt; I036707 JUL/28/11 &gt;--------------------------<br /><strong>UNIX: Remote AAS Instance startup fails in phase START-SYSTEM</strong><br />For updates from SAP NetWeaver 7.1 (or higher) to SAP NetWeaver 7.3 on UNIX, it might happen that any remote additional application server instance fails to start in phase START-SYSTEM.<br />In this case, proceed as described in SAP Note <strong>1613445</strong>.<br /><br /><br />-----------------------&lt; D025792 MAY/19/11 &gt;--------------------------<br /><strong>UNIX only: Apply SAP Note 995116</strong><br />If your source release has a lower level than one of the following:</p>\r\n<ul>\r\n<li>SAP Basis 6.40 Kernel patch level 169 or SP 20</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Basis 7.00 Kernel patch 96 or SP 12</li>\r\n</ul>\r\n<p>you have to apply the solution in Note <strong>995116</strong> before starting a release upgrade. Otherwise, the Software Update Manager is unable to start and stop your SAP system as it uses sapcontrol for this.<br /><br />----------------------&lt; I024101 MAY/12/11 &gt;--------------------------<br /><strong>Error in Phase DETECT_START_RELEASE_COMPONENTS</strong><br />When running an upgrade on a NetWeaver 2004-based system, the phase DETECT_START_RELEASE_COMPONENTS might fail with the following error message:<br /><br />The software component &lt;component name&gt; could not be read from the BC_COMPVERS table; the table schema may be outdated. Fix it and then repeat the phase from the beginning.<br /><br />In this case, apply SAP Note <strong>873624</strong>.<br /><br />----------------------&lt; I056573 SEP/15/09 &gt;--------------------------<br /><strong>IBM Databases: Error in Phase DEPLOY_ONLINE_DEPL</strong><br />The following error can occur in the DEPLOY_ONLINE_DEPL phase:<br />\"Table WCR_USERSTAT Conversion currently not possible.\"<br />For the solution, see SAP Note <strong>1156313</strong>.<br /><br /><br />---------------------&lt; I031257 NOV/19/08 &gt;----------------------------<br /><strong>Phase DEPLOY_ONLINE_DEPL: Timeout During AS Java Restart</strong><br />On slow/loaded systems, the DEPLOY_ONLINE_DEPL phase might fail due to a timeout during AS Java restart. This restart can take several hours as during this time portal applications are updated. If this restart takes longer than expected (3h), the Software Update Manager stops with an error.<br />To complete the enhancement package installation, wait for the AS Java restart in SAFE mode to finish and then resume SUM. If the system is still in SAFE mode after the update process completes, apply SAP Note <strong>1267123</strong>.<br /><br /><br />------------------------&lt; D030182 20/Jun/08 &gt;--------------------------<br /><strong>Error in Phase DEPLOY_ONLINE_DEPL (IBM DB2 for z/OS)</strong><br />You may encounter an error in phase DEPLOY_ONLINE_DEPL and the following error message can be found in the log file:<br />===<br />Info: E R R O R ******* (DbModificationManager)<br />Info: ... dbs-Error: Table KMC_WF_TEMPLATES: Conversion currently not possible<br />===<br />See SAP Note <strong>989868</strong> for the solution.<br />Additionally, you may need to increase the heap size of your Java VM. For more information, see SAP Note <strong>1229300</strong>.<br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><strong>V/ Problems After the Update</strong><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br /><br />a) General<br /><br /></p>\r\n<p>--------------------------------&lt; D057755 28/FEB/14&gt;-------------------------------------</p>\r\n<p><strong>Correcting issues with the SAP MC applet digital signature</strong></p>\r\n<p>If you open the SAP Management Console (SAP MC) by using its web interface after a SUM update procedure has been completed, a warning about the digital signature of the applet archives may appear. The reason for this is that these archives are self-signed. To correct this, acquire the archives relevant for your kernel as follows:</p>\r\n<ol>\r\n<li>Go to the SAP Software Distribution Center at: <a target=\"_blank\" href=\"http://service.sap.com/swdc\">http://service.sap.com/swdc</a> and logon with your SAP Service Marketplace ID.</li>\r\n<li>In the navigation bar, choose SAP Software Download Center&#160;-&gt;&#160;Support Packages and Patches&#160; -&gt; &#160;Browse our Download Catalog&#160;-&gt; &#160;Additional Components&#160;-&gt;&#160;SAP Kernel&#160; -&gt; SAP KERNEL <var>&lt;OS&gt;</var> -&gt;&#160;SAP KERNEL <var>&lt;Release&gt;</var> &#160;-&gt;&#160;Database independent.</li>\r\n<li>Select the appropriate sapmc_&lt;version&gt;.sararchive from the &#8220;Download&#8221; tab.</li>\r\n</ol>\r\n<p><strong>Note:</strong> Make sure that the SAPCAR tool is available in the host where you want to update the SAP Management Console. For more information about acquiring and using the SAPCAR tool, see SAP Note <strong>212876</strong>.</p>\r\n<p>&#160; 4. Open a command prompt or PowerShell and extract the sapmc_&lt;version&gt;.sar archive to a local directory by executing the following command:</p>\r\n<p>-&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; for Windows:&#160; SAPCAR.exe &#8211;xvf sapmc_&lt;version&gt;.sar</p>\r\n<p>-&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; For UNIX and IBM-i:&#160; ./SAPCAR -xvf sapmc_&lt;version&gt;.sar</p>\r\n<p>&#160; 5. Replace the existing sapmc directory with the sapmc directory that was extracted during the previous step.</p>\r\n<p>For more information about installing and updating the SAP MC, see SAP Note <strong>1014480</strong>.</p>\r\n<p><br /><br />b) ABAP<br /><br />---------------------&lt;&#160;D035956 SEP/30/2014 &gt;------------------------<br /><strong>Detect orphaned implementations of former classic BAdI definitions</strong><br />Relevant for upgrades from a source release lower than SAP_BASIS 700 to target release SAP_BASIS 700 or higher:<br />After an upgrade, implementations of classic BAdIs are no longer executed since the corresponding BAdI definitions do no longer exist. To detect such orphaned implementations, implement SAP Note 2046770 and&#160;execute the report FIND_ORPHANED_BADI_IMPLS.&#160;If the report finds orphaned BAdI implementations, see the SAP Note 2046770 for more details.<br /><br /><br />---------------------&lt; D023536 APR/22/2014 &gt;------------------------<br /><strong>SAP_BASIS 740 SP4: Missing Views in the database</strong><br />After an update to SAP_BASIS 740 SP4, the views V_DSH_ASSIGNED and V_DSH_TF_DETAIL are missing in the database. These views were delivered accidentally and cannot be created on a system based on SAP_BASIS 740 SP4. You can ignore this inconsistency as the views are not needed by the system. This issue will be solved with SUM version 1.0 SP12.<br /><br /><br />---------------------&lt; D024828 FEB/25/2014 &gt;------------------------<br /><strong>Ignorable short dumps LOAD_NOT_FOUND_AFTER_GEN in Phase MAIN_NEWBAS/XPRAS_AIMMRG</strong><br />After the upgrade you might find short dumps LOAD_NOT_FOUND_AFTER_GEN that happenend (from a time perspective) during phase MAIN_NEWBAS/XPRAS_AIMMRG if your system was upgraded to Kernel Version 7.41 PL 23 or lower.<br />You can ignore these short dumps. The problem is fixed with Kernel 7.41 PL 24.<br /><br /><br />---------------------&lt; D028310 DEC/16/2013 &gt;------------------------<br /><strong>Error: DDIC_ILLEGAL_KEY_COMP_NAME</strong><br />After an upgrade or update, you encounter the following error message:<br />DDIC_ILLEGAL_KEY_COMP_NAME<br />You can ignore this error.<br /><br /><br />---------------------&lt; D057512 DEC/12/2013 &gt;------------------------<br /><strong>Warnings and messages in work process trace files<br /></strong>After a successful update or upgrade using near-zero downtime maintenance (nZDM/SUM) capability of SUM, you see in the trace files of the work processes (dev_w*) warnings similar to the following:<br />C head_p=ffff80ffbfffa710: dbsl err=103=DBSL_ERR_DBOBJECT_UNKNOWN -&gt;<br />dbds err=512=DS_DBOBJECTUNKNOWN, dbdsoci.c:962<br />C *** ERROR =&gt; ^^ Ds_exec_imm() -&gt; err=512=DS_DBOBJECTUNKNOWN<br />[dbdsoci.c 976]<br />You can ignore these warnings. They are caused by the automatic deletion of temporary objects that were used in the shadow system only.<br /><br /><br />-------------------&lt; D047912 OCT/21/2012 &gt;--------------------------<br /><strong>Message: DDIC deletes RFC destination SAP_UPGRADE_SHADOW_SYSTEM</strong><br />After an successful upgrade or update, you see in the system log using<br />transaction SM21 the error message:<br />DDIC deletes RFC destination SAP_UPGRADE_SHADOW_SYSTEM<br />with the following details: A destination has been deleted in SM59,<br />destination maintenance of RFC. Problems can occur if this destination<br />is still used in existing ABAP programs.<br />You can ignore this error message.<br /><br /><br />-------------------&lt; D035061 03/Apr/2013 &gt;--------------------------<br /><strong>SAP_BASIS 731 SP05 to 740: Some tables remain on database</strong><br />After an update from SAP_BASIS 731 SP05 or higher to SAP_BASIS 740, the tables DDREPLIAPPLI and DDREPLIAPPLIT remain on the database. You can remove these tables using transaction SE14 or database commands.<br /><br /><br />-------------------&lt; D033123 17/Nov/2012 &gt;--------------------------<br /><strong>LONGPOST.LOG: Error message GI747</strong><br />SUM has added in phase XPRAS_UPG the error message GI747 (Error during creation ofstructure GLU1\"RECID\"\"TEXT4\"\"JV_RECIND\") to the LONGPOST.LOG file. See SAP note 1779102 and check, if this note solves the problem.<br /><br /><br />------------------&lt; Update D035061 10/JUL/2013 &gt;----------------------<br />-----------------------&lt; D026178 OCT/10/10 &gt;--------------------------<br /><strong>Ignorable P messages in LONGPOST.LOG</strong><br />Problem: SUM has added in phase ACT_UPG the following message to the LONGPOST.LOG file:<br />4PDDH176 Parameter list of append search help \"RDM_WKBK\" differs from appending one.<br />In phase CHK_POSTUP, you will be prompted as a result to check that message.<br />Solution: You can ignore this P message. It was written during ACT_UPG, but no followup action is required.<br /><br /><br />---------------&lt; Update D023536 16/JUL/2013 &gt;-----------------------<br />-------------------&lt; D053561 10/FEB/2012 &gt;--------------------------<br /><strong>LONGPOST.LOG: Tables without DDIC reference or not existing in DDIC</strong><br />After an upgrade or update, the file LONGPOST.LOG contains lines that indicate that a table has no DDIC reference. It can be either the following message:<br /><br />1PEPU203X &gt; Messages extracted from log file \"RDDNTPUR.&lt;SID&gt;\"<br />&lt;3PETG447 Table and runtime object \"NAVERS2\" exist without DDIC reference (\"Transp. table\")<br /><br />or a message such as<br /><br />4 ETG003 Table \"NCVERS2\" does not exist in the ABAP Dictionary<br /><br />The following tables can be be affected from this error message:</p>\r\n<p>/1SAP1/CCE_RUN01<br />CRR_CONFDT<br />CRR4TABLES<br />CRRPARAMETERS<br />CRRRTI<br />CRRRTIT<br />CRRSCENARIOS<br />CRRTASKHIST<br />CRRTASKINFO<br />CRRTCONFID<br />NAVERS2<br />NCVERS2<br />PATALLOWED<br />SUBST_SLANA_HDR<br />SUBST_SLANA_POS</p>\r\n<p>Note: We know of the list above, and the mentioned tables can safely be deleted in the database. If there are additional tables mentioned in the LONGPOST.LOG, please check the relevance for your system before you remove them.</p>\r\n<p>Proceed as follows to solve this issue:</p>\r\n<ol>1. Choose transaction SE11 to display the mentioned tables</ol><ol>2. If the tables don't exist in the transaction, you can remove the tables from the database.</ol><ol>3. In addition, you delete the runtime objects that belong to these tables:</ol><ol><ol>a) Choose transaction SE37.</ol></ol><ol><ol>b) Check if the function module DD_SHOW_NAMETAB contains runtime objects that belong to these tables.</ol></ol><ol><ol>c) Delete the runtime objects of the tables using the function module DD_NAMETAB_DELETE.</ol></ol>\r\n<p>The following tables might appear in upgrades from 702 to 730 and can safely be removed in this case. See SAP Note 824971 for more information.</p>\r\n<p>GENSETM<br />RSJOBTABM<br />SERVTYPEM</p>\r\n<p>If they appear in other updates, please contact the SAP support and report an incident under component BC-UPG-TLS-TLA.</p>\r\n<p><br /><br />c) Java</p>\r\n<p><br />--------------------&lt; D001712 AUG/28/09 &gt;------------------------------<br /><strong>Changing the start profile</strong><br />Include the following entry in your start profile:</p>\r\n<ul>\r\n<li>Unix:</li>\r\n</ul>\r\n<p>DIR_LIBRARY = $(DIR_LIBRARY)<br />PATH = $(DIR_EXECUTABLE):%(PATH)</p>\r\n<ul>\r\n<li>Windows:</li>\r\n</ul>\r\n<p>PATH = $(DIR_EXECUTABLE);%(PATH)<br />This prevents a problem with the jmon.dll file.<br /><br /><br /><strong>Part D Chronological Summary</strong><br /><br />Date..........Topic....Short description<br />---------------------------------------------------------------------------------------<br />DEC/13/14...II...Resetting the Software Update Manager&#160;<br />DEC/12/14...IV...Copying user for SPDD fails<br />DEC/12/14...III..Implement SAP Note 2091348 if SAP Note 1696748 is already implemented<br />NOV/19/14...II...PI: Downloading the latest patches when generating the stack.xml file<br />OCT/21/14...III..De-implenment SAP Note 1819126 before the upgrade or update<br />OCT/13/14...III..No \"Single System\" mode on dual-stack systems<br />OCT/10/14...I....Target Release SAP_BASIS 740 SP8: Single System Mode with Minimum Patch Level 7<br />OCT/07/14...I....Using the ampersand symbol in passwords - Restriction<br />SEP/30/14...V....Detect orphaned implementations of former classic BAdI definitions<br />SEP/30/14...IV...Aborted conversions during phase PARCONV_UPG in step 6<br />SEP/16/14...IV...HP-UX for IA64 only: Correcting issues during phase PREP_PRE_CHECK/INITPUT_PRE<br />SEP/08/14...II...Resetting the Software Update Manager during the Preprocessing Roadmap Step<br />SEP/05/14...III..Target release SAP_BASIS 740 SP7: Prevent Error in Conversion for Pool/Cluster Tables with CURR and QUAN colums<br />AUG/11/27...IV...Windows: Unknown macro 'VCREDISTPATH' in phase EXTRACTKRN_PRE<br />AUG/11/14...III..Preventing issues with Zero Administration Management (ZAMM)<br />JUL/17/14...II...STMS: Distributing the Configuration as Follow-up Activity<br />JUL/03/14...III..SFW activation: Prevent automatic activation of BC-Sets in all clients<br />APR/22/14...V....SAP_BASIS 740 SP4: Missing Views in the database<br />APR/11/14...IV...P messages in LONGPOST.LOG due to BW object activation<br />FEB/28/14...V....Correcting issues with the SAP MC applet digital signature<br />FEB/28/14...IV...Runtime error INSERT_PROGRAM_NAME_BLANK in phase XPRAS_AIMMRG<br />FEB/28/14...III..Consider SAP Note 1983758 while preparing the update<br />FEB/27/14...IV...SAP NetWeaver 7.3 and higher only: Preventing refusal issues caused by column drop of NZDM_VALUE2<br />FEB/25/14....V...Ignorable short dumps LOAD_NOT_FOUND_AFTER_GEN in Phase MAIN_NEWBAS/XPRAS_AIMMRG<br />FEB/12/14...IV...Windows only: Correcting Issues in the \"Delete Old Java Content\" Step<br />FEB/03/14...IV...(Windows only:) Error \"Access denied\" in phase MAIN_UPTRANS/UPCONF<br />JAN/14/14...IV...DYNPRO_NOT_FOUND during parallel DBCLONE processes<br />DEC/16/13...V....Error message DDIC_ILLEGAL_KEY_COMP_NAME<br />DEC/16/13...IV...Solaris X86_64: Timeout Error during phase MAIN_NEWBAS/STOPSAP_FINAL<br />DEC/13/13...III..Exclude Z languages<br />DEC/13/13...I....Solution Manager only: SPS update of ABAP or Java stack independently is supported<br />NOV/16/13...V....Warnings and messages in work process trace files<br />NOV/11/13...IV...Correcting Issues with Resetting the Upgrade<br />NOV/05/13...IV...Phase ACT_UPG: Certain search helps could not be activated<br />OCT/29/13...I....Software Update Manager 1.0 SP09 Release Restrictions<br />OCT/28/13...I....Installing SAP Business Suite Usage Types on Top of an Existing SAP NetWeaver Java System<br />OCT/23/13...IV...Shadow instance reset during an upgrade<br />OCT/21/13...V....Message: DDIC deletes RFC destination SAP_UPGRADE_SHADOW_SYSTEM<br />OCT/14/13...IV...Error in Phase MAIN_NEWBAS/SUCCCHK_PROD<br />OCT/14/13...III..Apply SAP Note 1910464 before the import of certain SPs<br />AUG/16/13...IV...Phase XPRAS_UPG: Errors during post-handling \"RS_AFTER_IMPORT\"<br />JUL/26/13...I....Update of shared Wily AutoProbe connectors<br />JUL/15/13...IV...Phase ACT_UPG: Certain tables cannot be activated<br />JUL/15/13...V....LONGPOST.LOG: Tables without DDIC reference or not existing in DDIC<br />JUN/25/13...IV...Error in phases XPRAS_SHD_AIMMRG, XPRAS_AIMMRG, OR XPRAS_TRANS<br />JUN/17/13...V....Tables without DDIC reference: Message ETG447 in LONGPOST.LOG<br />MAY/23/13...IV...Correcting issues with primary application server instance host name connectivity<br />MAY/14/13...IV...Phase XPRAS_UPG: Problems in FDT_AFTER_IMPORT or FDT_AFTER_IMPORT_C<br />MAY/02/13...IV...ICNVREQ: Incremental conversion in special namespaces<br />APR/29/13...III..Setting the instance profile parameter AutoStart to '0'<br />APR/24/13...IV...View EPIC_V_BRS_BSEG not activated<br />APR/03/13...V....SAP_BASIS 731 SP05 to 740: Some tables remain on database<br />MAR/28/13...IV...Preventing Issues with Wily AutoProbe connectors<br />MAR/26/13...I....Built-in capability near-zero downtime maintenance (nZDM/SUM)<br />MAR/25/13...IV...RFC_COMMUNICATION_FAILURE during phase CHECKSYSSTATUS<br />MAR/25/13...I....SUM requires SAP kernel 7.20 or higher for target release<br />FEB/11/13...III..Remove usages of customer-developed objects before you start SUM<br />FEB/05/13...IV...Wrong MCOD warning for dual stack systems<br />DEC/13/12...III..See SAP Note 1413569 for table SMSCMAID<br />NOV/17/12...V....LONGPOST.LOG: Error message GI747<br />NOV/16/12...I....Build-in Capabilities of SUM to include customer transport requests<br />OCT/10/12...IV...Ignorable P messages in LONGPOST.LOG<br />OCT/10/12...IV...tp 212 error in MAIN_NEWBAS/TABIM_UPG: left-over semaphore<br />SEP/18/12...III..Upgrade from SAP NetWeaver 2004 to SAP NetWeaver 7.3 EHP1 Based PowerPC System on Linux: Update Your Instance Profile<br />SEP/18/12...III..NTsystems: Install latest sapstartserv before the update<br />SEP/09/12...IV...MAIN_NEWBAS/STARTSAP_TBUPG: System start failed on NT IA64<br />AUG/22/12...III..Implement note 1720495 before you start transaction SPAUAUG/13/12...IV...Error while sapcpe copies vcredist_x64.msi<br />AUG/01/12...III..Check platform-specific requirements for the 7.20 EXT kernel<br />JUL/23/12...III..Server Group SAP_DEFAULT_BTC must include Primary Application Server Instance<br />JUN/07/12...II...User Management Engine on a remote instance host<br />FEB/01/12...IV...Dual-Stack System Update: Phase STARTSAP_PUPG Fails<br />NOV/22/11...I....SAP NetWeaver 7.3 EHP1: Release Restrictions and Limitations<br />NOV/10/11...IV...UNIX: Prevent Overwriting sapuxuserchk During Update<br />OCT/28/11...I....SAP NetWeaver 7.0 EHP3: Release Restrictions and Limitations<br />JUL/28/11...IV...UNIX: Remote AASI startup fails in phase START-SYSTEM<br />JUL/05/11...IV...Missing tables during DB02 check after reset of update<br />MAY/30/11...III..Preventing Errors Due to Unicode Length<br />MAY/19/11...IV...UNIX only: Apply SAP Note 995116<br />MAY/18/11...IV...Phase STARTSAP_PUPG: System start of the dialog instances failed<br />MAY/17/11...IV...Phase ACT_UPG during EHP installation<br />MAY/12/11...IV...Error in Phase DETECT_START_RELEASE_COMPONENTS<br />APR/13/11...I....SAP NetWeaver 7.3: Release Restrictions and Limitations<br />MAR/08/11...III..Windows only: Check registration of sapevents.dll<br />DEC/08/10...III..Adjust Start Profile<br />NOV/25/10...III..No Automatic Update of SAPCryptolib<br />NOV/23/10...III..Support Package Stack Update only<br />OCT/22/10...IV...Preprocessing:ERROR: Found pattern \"R3load:..<br />OCT/20/10...III..Apply SAP Note 1518145 to Avoid Error During Reset<br />JUN/07/10...IV...Preventing long runtime of SUSR_AFTER_IMP_PROFILE<br />MAY/31/10...IV...Preventing Errors with Table \"SATC_MD_STEP\"<br />SEP/15/09...IV...IBM databases: Error in DEPLOY_ONLINE_DEPL phase<br />SEP/08/09...I....JSPM NOT to be Used During the Update Process<br />AUG/28/09...V....Changing the Start Profile<br />APR/29/09...IV...Modification Adjustment<br />JAN/30/09...III..Cleaning Up the Profile Directory<br />DEC/15/08...III..Preventing Activation Errors<br />NOV/20/08...IV...File HUGETABS.LST<br />NOV/19/08...IV...Phase DEPLOY_ONLINE_DEPL:Timeout During AS Java Restart<br />NOV/13/08...IV...\"/ISQC/S_UT_REF\" could not be activated<br />SEP/19/08...IV...Activation Error<br />AUG/26/08...IV...Phase: MAIN_SHDIMP/SHDUNINST_DB<br />AUG/20/08...IV...Phase XPRAS_UPG: COMPUTE_INT_PLUS_OVERFLOW</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-SLC (Software Logistics Controller)"}, {"Key": "Other Components", "Value": "BC-UPG-TLS-TLJ (Upgrade tools for Java)"}, {"Key": "Other Components", "Value": "BC-EHP-INS (Please use BC-UPG-TLS*)"}, {"Key": "Other Components", "Value": "BC-UPG-TLS-TLA (Upgrade tools for ABAP)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031330)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001926261/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SUM_SP11_paths.pdf", "FileSize": "182", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001310012013&iv_version=0039&iv_guid=DBBA0D4B9C5FA749AD757E329D90B227"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "995116", "RefComponent": "BC-CST-STS", "RefTitle": "Backward porting of sapstartsrv for earlier releases", "RefUrl": "/notes/995116"}, {"RefNumber": "88416", "RefComponent": "BC-OP-NT", "RefTitle": "Zero administration memory management for the ABAP server", "RefUrl": "/notes/88416"}, {"RefNumber": "873624", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/873624"}, {"RefNumber": "824971", "RefComponent": "BC-UPG", "RefTitle": "Message ETG447 for /1SAP1/CCE_RUN01 in LONGPOST.LOG", "RefUrl": "/notes/824971"}, {"RefNumber": "821496", "RefComponent": "BC-SEC-USR", "RefTitle": "Runtime of after-import method SUSR_AFTER_IMP_PROFILE", "RefUrl": "/notes/821496"}, {"RefNumber": "797147", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Installation for SAP Customers", "RefUrl": "/notes/797147"}, {"RefNumber": "786412", "RefComponent": "BC-CCM-BTC", "RefTitle": "Determination of execution server for jobs without target server", "RefUrl": "/notes/786412"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "2091348", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: No call of BAdI on shadow instance during upgrade", "RefUrl": "/notes/2091348"}, {"RefNumber": "2070458", "RefComponent": "BC-DWB-DIC", "RefTitle": "Conversion: Error when creating dependent views", "RefUrl": "/notes/2070458"}, {"RefNumber": "2061940", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Dump/signal 11 in sybctrl on HP", "RefUrl": "/notes/2061940"}, {"RefNumber": "2046770", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Detect orphaned implementations of former classic BAdI definitions", "RefUrl": "/notes/2046770"}, {"RefNumber": "2035728", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SFW activation: Prevent automatic activation of BC-Sets in all clients", "RefUrl": "/notes/2035728"}, {"RefNumber": "1999514", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1999514"}, {"RefNumber": "1994569", "RefComponent": "BC-DWB-DIC", "RefTitle": "Error during conversion of pooled table or cluster table", "RefUrl": "/notes/1994569"}, {"RefNumber": "1983758", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW client is incorrectly set in prepare phase of upgrade", "RefUrl": "/notes/1983758"}, {"RefNumber": "1978632", "RefComponent": "BC-UPG-DTM-TLJ", "RefTitle": "NZDM Java: Refuse due to drop column", "RefUrl": "/notes/1978632"}, {"RefNumber": "1974464", "RefComponent": "XX-INT-PPMS-TOOL", "RefTitle": "Information on SCA Dependency Analysis for Java download objects", "RefUrl": "/notes/1974464"}, {"RefNumber": "1973135", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1973135"}, {"RefNumber": "1964350", "RefComponent": "BC-ABA-SC", "RefTitle": "DYNPRO_NOT_FOUND during parallel DBCLONE processes", "RefUrl": "/notes/1964350"}, {"RefNumber": "1941711", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD", "RefUrl": "/notes/1941711"}, {"RefNumber": "1926923", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926923"}, {"RefNumber": "1926798", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926798"}, {"RefNumber": "1926741", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926741"}, {"RefNumber": "1926740", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926740"}, {"RefNumber": "1926697", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926697"}, {"RefNumber": "1926454", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926454"}, {"RefNumber": "1922474", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1922474"}, {"RefNumber": "1910464", "RefComponent": "BC-DWB-CEX", "RefTitle": "Processing of SPDD-relevant note corrections", "RefUrl": "/notes/1910464"}, {"RefNumber": "1866886", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: Message CD837 in transport log", "RefUrl": "/notes/1866886"}, {"RefNumber": "1846998", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "View EPIC_V_BRS_BSEG not activated", "RefUrl": "/notes/1846998"}, {"RefNumber": "1830894", "RefComponent": "BC-DB-DB4", "RefTitle": "Availability of SAP NetWeaver 7.4 on IBM i", "RefUrl": "/notes/1830894"}, {"RefNumber": "1789659", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP Netweaver AS ABAP 7.40", "RefUrl": "/notes/1789659"}, {"RefNumber": "1781833", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. About Upgrading to SAP Solution Manager 7.1", "RefUrl": "/notes/1781833"}, {"RefNumber": "1779102", "RefComponent": "FI-GL", "RefTitle": "RGZZGLUX: Error GU093 or GI747 because of field RECID", "RefUrl": "/notes/1779102"}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566"}, {"RefNumber": "1759080", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prerequisites and restrictions of Customer Transport Integration with SUM", "RefUrl": "/notes/1759080"}, {"RefNumber": "1756703", "RefComponent": "BC-CST-STS", "RefTitle": "sapstartsrv: instance property is missing for ERS", "RefUrl": "/notes/1756703"}, {"RefNumber": "1751237", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the update/upgrade to SAP NetWeaver 7.4 (incl. SPs and SRs)", "RefUrl": "/notes/1751237"}, {"RefNumber": "1730175", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 FOR SAP SCM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730175"}, {"RefNumber": "1730102", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP NetWeaver 7.4", "RefUrl": "/notes/1730102"}, {"RefNumber": "1730098", "RefComponent": "CRM-BF", "RefTitle": "EHP2 FOR SAP CRM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730098"}, {"RefNumber": "1730095", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 FOR SAP ERP 6.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730095"}, {"RefNumber": "1720495", "RefComponent": "BC-UPG-NA", "RefTitle": "Invalid deimplementation of obsolete notes by Snote tool", "RefUrl": "/notes/1720495"}, {"RefNumber": "1696748", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: BAdI", "RefUrl": "/notes/1696748"}, {"RefNumber": "1696517", "RefComponent": "BC-CCM-MON", "RefTitle": "Memory alignment problem in sapstartsrv.exe process", "RefUrl": "/notes/1696517"}, {"RefNumber": "1678565", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prerequisites and restrictions of nZDM (near-Zero Downtime Maintenance) for ABAP-based solutions", "RefUrl": "/notes/1678565"}, {"RefNumber": "1668465", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Directory traversal in RSUPG_COPY_SHD_VIEWS", "RefUrl": "/notes/1668465"}, {"RefNumber": "1656036", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SUM functionality for checking sapstartsrv patch level", "RefUrl": "/notes/1656036"}, {"RefNumber": "1650797", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prevent overwriting of sapuxuserchk during update/upgrade", "RefUrl": "/notes/1650797"}, {"RefNumber": "1637629", "RefComponent": "BC-XI", "RefTitle": "No Process Integration in SAP EHP3 FOR SAP NETWEAVER 7.0", "RefUrl": "/notes/1637629"}, {"RefNumber": "1637366", "RefComponent": "BC-INS", "RefTitle": "Installation of SAP EHP3 for SAP NetWeaver 7.0", "RefUrl": "/notes/1637366"}, {"RefNumber": "1633876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1633876"}, {"RefNumber": "1631124", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Potential modification of persisted data by RDDCP6TB", "RefUrl": "/notes/1631124"}, {"RefNumber": "1631072", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Potential modification of persisted data by RDDNT4DL", "RefUrl": "/notes/1631072"}, {"RefNumber": "1628606", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Potential modification of persisted data by RSTRESNC", "RefUrl": "/notes/1628606"}, {"RefNumber": "1615463", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite 7i 2010 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1615463"}, {"RefNumber": "1613445", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "Remote AAS instance startup fails during SUM update on UNIX", "RefUrl": "/notes/1613445"}, {"RefNumber": "1609441", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the upgrade to SAP NetWeaver 7.3 EHP1", "RefUrl": "/notes/1609441"}, {"RefNumber": "1595736", "RefComponent": "SV-SMG-INS", "RefTitle": "Solution Manager: Overview on Release Information Notes", "RefUrl": "/notes/1595736"}, {"RefNumber": "1584549", "RefComponent": "BC-UPG", "RefTitle": "Directory traversal in function group SUGPEWA", "RefUrl": "/notes/1584549"}, {"RefNumber": "1584548", "RefComponent": "BC-UPG", "RefTitle": "Directory traversal in RSUPGSUM", "RefUrl": "/notes/1584548"}, {"RefNumber": "1577595", "RefComponent": "BC-DWB-DIC", "RefTitle": "Index P of table SFW_PACKAGE is not deleted", "RefUrl": "/notes/1577595"}, {"RefNumber": "1570738", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite PI adapters for SAP NetWeaver PI 7.3", "RefUrl": "/notes/1570738"}, {"RefNumber": "1557825", "RefComponent": "XX-SER-SAPSMP-CON", "RefTitle": "Release Restrictions for SAP EHP 3 for SAP NetWeaver 7.0", "RefUrl": "/notes/1557825"}, {"RefNumber": "1556113", "RefComponent": "BC-CST-STS", "RefTitle": "sapevents.dll in DIR_CT_RUN is locked", "RefUrl": "/notes/1556113"}, {"RefNumber": "1555144", "RefComponent": "BC-UPG", "RefTitle": "Potential information disclosure relating to file system", "RefUrl": "/notes/1555144"}, {"RefNumber": "1526853", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Protection of upgrade tools against acts of sabotage", "RefUrl": "/notes/1526853"}, {"RefNumber": "1522700", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP EHP1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1522700"}, {"RefNumber": "1518419", "RefComponent": "BC-OP-NT", "RefTitle": "Windows Pagefile Sizing and Configuration", "RefUrl": "/notes/1518419"}, {"RefNumber": "1518145", "RefComponent": "BC-DWB-DIC", "RefTitle": "FM DD_DB_MISSING_OBJECTS returns incorrect no. of objects", "RefUrl": "/notes/1518145"}, {"RefNumber": "1514066", "RefComponent": "BC-UPG", "RefTitle": "Overwriting of files in upgrade or EHPI", "RefUrl": "/notes/1514066"}, {"RefNumber": "1497083", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 for SAP SCM 7.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1497083"}, {"RefNumber": "1497032", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 for SAP CRM 7.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1497032"}, {"RefNumber": "1496212", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 for SAP ERP 6.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1496212"}, {"RefNumber": "1489787", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1489787"}, {"RefNumber": "1468467", "RefComponent": "BC-DWB-DIC", "RefTitle": "Sequence problem during DROP/CREATE of indexes", "RefUrl": "/notes/1468467"}, {"RefNumber": "1463168", "RefComponent": "BC-DWB-TOO-ATF", "RefTitle": "Tables \"SATC_MD_STEP\" and \"SATC_AC_CHK\" and upgrade tools", "RefUrl": "/notes/1463168"}, {"RefNumber": "1453164", "RefComponent": "BC-UPG", "RefTitle": "Missing authorization check in module of upgrade", "RefUrl": "/notes/1453164"}, {"RefNumber": "1413569", "RefComponent": "CA-GTF-SCM", "RefTitle": "Index for SMSCMAID table for Performance and Upgrade", "RefUrl": "/notes/1413569"}, {"RefNumber": "1407532", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP NetWeaver 7.3", "RefUrl": "/notes/1407532"}, {"RefNumber": "1390477", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1390477"}, {"RefNumber": "1387739", "RefComponent": "BC-UPG-RDM", "RefTitle": "Out of memory errors during shadow system operation", "RefUrl": "/notes/1387739"}, {"RefNumber": "1357207", "RefComponent": "BC-SRV-BR", "RefTitle": "Implementation of coding corrections during phase XPRAS_UPG", "RefUrl": "/notes/1357207"}, {"RefNumber": "1337378", "RefComponent": "BC-UPG-TLS", "RefTitle": "ICNV: Form oidx_cre:Operation DB_CREATE_INDEX failed", "RefUrl": "/notes/1337378"}, {"RefNumber": "1283197", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Cluster tables: Unnecessary conversion", "RefUrl": "/notes/1283197"}, {"RefNumber": "12746", "RefComponent": "BC-CTS", "RefTitle": "WARN <file> is already in use (), I'm waiting 5 sec", "RefUrl": "/notes/12746"}, {"RefNumber": "1267123", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1267123"}, {"RefNumber": "1264734", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Error message for reference table and reference fields", "RefUrl": "/notes/1264734"}, {"RefNumber": "1243486", "RefComponent": "BC-SRV-BR", "RefTitle": "Dump COMPUTE_INT_PLUS_OVERFLOW during background cleanup", "RefUrl": "/notes/1243486"}, {"RefNumber": "1243014", "RefComponent": "PA-PA-IT", "RefTitle": "Search Help H_5ITCD and H_5ITTT Not Activated in ACT_700", "RefUrl": "/notes/1243014"}, {"RefNumber": "1242867", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Domain append structure is not activated", "RefUrl": "/notes/1242867"}, {"RefNumber": "1162299", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1162299"}, {"RefNumber": "1162171", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "DDIC: Texts of domain fixed values are lost after transport", "RefUrl": "/notes/1162171"}, {"RefNumber": "1161733", "RefComponent": "BC-UPG", "RefTitle": "SQL Server: SAP EHPI for SAP NetWeaver 7.0", "RefUrl": "/notes/1161733"}, {"RefNumber": "1156313", "RefComponent": "EP-PIN-SPT", "RefTitle": "Activity Report Upgrade with IBM Databases (DB2, DB4, DB6)", "RefUrl": "/notes/1156313"}, {"RefNumber": "1142632", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for Enhancement Package installer: MaxDB", "RefUrl": "/notes/1142632"}, {"RefNumber": "1109375", "RefComponent": "BC-DWB-DIC", "RefTitle": "Values from fixed value append are missing in the domain", "RefUrl": "/notes/1109375"}, {"RefNumber": "1029444", "RefComponent": "BC-DWB-DIC", "RefTitle": "DDIF_FIELDINFO_GET:Prob with UCLEN <> system Unicode length", "RefUrl": "/notes/1029444"}, {"RefNumber": "1025085", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "How to manually patch the SAPJVM", "RefUrl": "/notes/1025085"}, {"RefNumber": "1014480", "RefComponent": "BC-CCM-MC", "RefTitle": "SAP Management Console (SAP-MC)", "RefUrl": "/notes/1014480"}, {"RefNumber": "1009759", "RefComponent": "BC-UPG-TLS", "RefTitle": "Incremental conversion in special namespaces", "RefUrl": "/notes/1009759"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928729", "RefComponent": "BC-I18-UNI", "RefTitle": "Combined Upgrade & Unicode Conversion (CU&UC)", "RefUrl": "/notes/928729 "}, {"RefNumber": "1730175", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 FOR SAP SCM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730175 "}, {"RefNumber": "1730098", "RefComponent": "CRM-BF", "RefTitle": "EHP2 FOR SAP CRM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730098 "}, {"RefNumber": "1497083", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 for SAP SCM 7.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1497083 "}, {"RefNumber": "1730095", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 FOR SAP ERP 6.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730095 "}, {"RefNumber": "1496212", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 for SAP ERP 6.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1496212 "}, {"RefNumber": "1656036", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SUM functionality for checking sapstartsrv patch level", "RefUrl": "/notes/1656036 "}, {"RefNumber": "1730102", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP NetWeaver 7.4", "RefUrl": "/notes/1730102 "}, {"RefNumber": "1609441", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the upgrade to SAP NetWeaver 7.3 EHP1", "RefUrl": "/notes/1609441 "}, {"RefNumber": "1720495", "RefComponent": "BC-UPG-NA", "RefTitle": "Invalid deimplementation of obsolete notes by Snote tool", "RefUrl": "/notes/1720495 "}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566 "}, {"RefNumber": "797147", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Installation for SAP Customers", "RefUrl": "/notes/797147 "}, {"RefNumber": "1557825", "RefComponent": "XX-SER-SAPSMP-CON", "RefTitle": "Release Restrictions for SAP EHP 3 for SAP NetWeaver 7.0", "RefUrl": "/notes/1557825 "}, {"RefNumber": "1407532", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP NetWeaver 7.3", "RefUrl": "/notes/1407532 "}, {"RefNumber": "1522700", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP EHP1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1522700 "}, {"RefNumber": "1615463", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite 7i 2010 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1615463 "}, {"RefNumber": "1413569", "RefComponent": "CA-GTF-SCM", "RefTitle": "Index for SMSCMAID table for Performance and Upgrade", "RefUrl": "/notes/1413569 "}, {"RefNumber": "1650797", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prevent overwriting of sapuxuserchk during update/upgrade", "RefUrl": "/notes/1650797 "}, {"RefNumber": "1142632", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for Enhancement Package installer: MaxDB", "RefUrl": "/notes/1142632 "}, {"RefNumber": "995116", "RefComponent": "BC-CST-STS", "RefTitle": "Backward porting of sapstartsrv for earlier releases", "RefUrl": "/notes/995116 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SUM", "From": "1.0", "To": "1.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 1.0", "SupportPackage": "SP012", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200013676&support_package=SP012&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 1.0", "SupportPackage": "SP011", "SupportPackagePatch": "000010", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200013676&support_package=SP011&patch_level=000010"}, {"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 1.0", "SupportPackage": "SP016", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200013676&support_package=SP016&patch_level=000000"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}