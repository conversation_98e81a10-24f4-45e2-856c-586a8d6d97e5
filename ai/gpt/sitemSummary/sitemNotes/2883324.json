{"Request": {"Number": "2883324", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 899, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000082042020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002883324?language=E&token=C811C5204006A43082E6509A57C60B72"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002883324", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2883324"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.02.2021"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-REO-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Reorganization AP/AR"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization", "value": "FI-GL-REO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization AP/AR", "value": "FI-GL-REO-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2883324 - Reorganization Profit Center: Runtime Error BCD_FIELD_OVERFLOW occurs"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>During the generation phase of the profit center reorganization the error&#160;BCD_FIELD_OVERFLOW exception&#160;CX_SY_CONVERSION_OVERFLOW is raised.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAGL_R_OBJ_GENERATE_SIM, rounding differences.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The generation phase of the reorganization executes the document split function to elaborate the relations on the account receivable / payables to the profit center determineing objects. Therefore additional attributes are handled by the document split function, which are more detailed.</p>\r\n<p>In some rare document situation the resulting rounding differences cannot be passed to the interface. This kind of documents cannot be handled by the related objetcs and needs to be handled as a first level object.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the following correction instruction.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-FL (Flexible Structures)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031902)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I502233)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002883324/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883324/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1627018", "RefComponent": "FI-GL-REO", "RefTitle": "Composite SAP Note for segment reorganization", "RefUrl": "/notes/1627018"}, {"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10110INS4CORE", "URL": "/supportpackage/SAPK-10110INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10208INS4CORE", "URL": "/supportpackage/SAPK-10208INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10306INS4CORE", "URL": "/supportpackage/SAPK-10306INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10404INS4CORE", "URL": "/supportpackage/SAPK-10404INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10502INS4CORE", "URL": "/supportpackage/SAPK-10502INS4CORE"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60521", "URL": "/supportpackage/SAPKH60521"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60628", "URL": "/supportpackage/SAPKH60628"}, {"SoftwareComponentVersion": "SAP_FIN 617", "SupportPackage": "SAPK-61723INSAPFIN", "URL": "/supportpackage/SAPK-61723INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61817INSAPFIN", "URL": "/supportpackage/SAPK-61817INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72016INSAPFIN", "URL": "/supportpackage/SAPK-72016INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73017INSAPFIN", "URL": "/supportpackage/SAPK-73017INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 1, "URL": "/corrins/0002883324/15841"}, {"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0002883324/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002883324/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1869309 ", "URL": "/notes/1869309 ", "Title": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (IV)", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "2009162 ", "URL": "/notes/2009162 ", "Title": "PRCTR/SEG: Rounding differences for foreign currency valuation differences", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "616", "Number": "2572904 ", "URL": "/notes/2572904 ", "Title": "COMPUTE_BCD_OVERFLOW in SAPLGLT0 form ELIM_ROUND_DIFF", "Component": "FI-GL-FL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2009162 ", "URL": "/notes/2009162 ", "Title": "PRCTR/SEG: Rounding differences for foreign currency valuation differences", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "1869309 ", "URL": "/notes/1869309 ", "Title": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (IV)", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2009162 ", "URL": "/notes/2009162 ", "Title": "PRCTR/SEG: Rounding differences for foreign currency valuation differences", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1869309 ", "URL": "/notes/1869309 ", "Title": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (IV)", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2009162 ", "URL": "/notes/2009162 ", "Title": "PRCTR/SEG: Rounding differences for foreign currency valuation differences", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "617", "ValidTo": "730", "Number": "2572904 ", "URL": "/notes/2572904 ", "Title": "COMPUTE_BCD_OVERFLOW in SAPLGLT0 form ELIM_ROUND_DIFF", "Component": "FI-GL-FL"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2009162 ", "URL": "/notes/2009162 ", "Title": "PRCTR/SEG: Rounding differences for foreign currency valuation differences", "Component": "FI-GL-REO-GL"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}