{"Request": {"Number": "1432783", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 864, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016966122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001432783?language=E&token=E03E5642AE696DF1E2177FC2F99C41C2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001432783", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001432783/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1432783"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.01.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1432783 - IBM i: Known Issues with OS Release IBM i 7.1"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Issues which might occur with operating system release IBM i 7.1 will be collected in this note. For the sake of clarity, the issues will be subdivided as follows:</p>\r\n<ol><ol>a) Issues Resolved Without an IBM PTF</ol></ol><ol><ol>b) Issues Resolved by the Latest IBM Infoapar</ol></ol><ol><ol>c) Issues Resolved by a PTF Which Is Not (Yet) Included in the Infoapar</ol></ol>\r\n<p><br />The note will be updated when a new Infoapar is produced, make sure you check the latest version.</p>\r\n<p><strong>Section a: Issues Resolved Without an IBM PTF</strong></p>\r\n<ol>\r\n<li>The application server fails to connect to the database server after upgrading the operating system from V5R4x if OptiConnect is used.<br />&#160;</li>\r\n<li>Severe performance problems on accesses to tables in the database library are encountered after upgrading from V5R4 or lower to IBM i 7.1.<br />&#160;</li>\r\n<li>Transaction ST04 (Performance Monitor Overview) is returning a message \"No DB performance data available\" (message number D4015), transactions DB4Cockpit or DBACockpit (Database Monitor Overview) are returning a message \"No data available\" (message number D4609) even though profile parameter as4/dbmon/enable is set to 1 or ON and the function to dump new outfiles was selected. The joblog of the workprocess to execute the dump is showing message SQL0204 \"PLAN_CACHE_TO_MEMORY_MONITOR in QSYS2 type *N not found.<br />&#160;</li>\r\n<li>SQL0420 errors in transaction KEDV when trying to create a new level of integration. The failing statement is a \"CALL QCMDEXC\".<br />&#160;</li>\r\n<li>Out-of-memory issues occur with the Toolbox JDBC Driver, for example, during deployment of large packages or database creation using JLoad.<br />&#160;</li>\r\n<li>In DB4COCKPIT or DBACOCKPIT, the display \"SQE Indexes Advised\" produces the error message D4-608: \"No information on advised indexes available\". The dev-trace contains the messages:<br />C&#160; *** ERROR =&gt; Error -551 in function db_open&#160; [dbsldb4.cpp&#160; 10847<br />B&#160; ***LOG BY2=&gt; sql error -551&#160;&#160;&#160;&#160;&#160;&#160; performing OPC [dbds&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 398]<br />B&#160; ***LOG BY0=&gt; Not authorized to object SYSIXADV in QSYS2 type *FILE.MSGID= Job=&lt;qualified job name&gt; [dbds&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 398]<br />B&#160; ***LOG BY1=&gt; sql error -551&#160;&#160;&#160;&#160;&#160;&#160; [dbacds&#160;&#160;&#160;&#160;&#160;&#160; 1496]<br />&#160;</li>\r\n<li>\r\n<p>In transaction RZ20 - \"SAP CCMS Monitor Templates (Operating System)\", the \"MainStoragePools\" node is incomplete.</p>\r\n</li>\r\n<li>\r\n<p>Message SQL7008 with reason code 3 (RC3) in joblog of job DB4SQLDATA for tables PLCACHDUMP, PRUNESTMT1 and PRUNESTMT2 in schema SAPDB4M.</p>\r\n</li>\r\n<li>After upgrading to IBM i 7.1, you experience a general performance degradation caused by excessive page faults in the main storage pool that is used by your SAP system (typically *BASE).</li>\r\n</ol>\r\n<p><strong>Section b: Issues Resolved by the Latest IBM Infoapar</strong></p>\r\n<ol>\r\n<li>After upgrading to this operating system release, problems occur when printing from SAP on IBM i to a remote spool server.<br />&#160;</li>\r\n<li>Jobs loop in QSQCLOSE with high CPU consumption.<br />&#160;</li>\r\n<li>After implementation of PTFs some system files are missing in the libraries QSYS2 and/or SYSIBM. Files known to be affected are: SQLPRIKEYS and SQLFORKEYS in library SYSIBM. REF_CST2, REFCST1, CHECK_CSTS and TABLE_CSTS in libraries QSYS2 and SYSIBM. SYSCST, SYSCSTCOL, SYSCHKCST, SYSCSTDEP and SYSREFCST in libraries QSYS2, SYSIBM and any SQL schema on the system.<br />&#160;</li>\r\n<li>SQL0104 errors occur creating views for the shadow instance during upgrade or the application of enhancement packages.<br />&#160;</li>\r\n<li>Severe problem with database cursors in various transactions most likely in batch jobs.<br /><br /></li>\r\n<li>You are facing long run times for following upgrade phases:&#160;PARMVNT_XCNV (SUM 1.0),&#160;PARMVNT_BAS_TABS (SUM 2.0)&#160;or PARCONV_UPG. In these upgrade phases database tables get altered. This requires a temporary copy of the affected tables.</li>\r\n</ol>\r\n<p><strong>Section c: Issues Resolved by a PTF Which Is Not (Yet) Included in the Infoapar</strong></p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>AS/400, OS/400, i5/os, iSeries, OS upgrade, V7R1M0, V7R1M1, V7R1, i710 STROBJCVN</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>Section a: Issues Resolved Without an IBM PTF</strong></p>\r\n<ol>\r\n<li>The operating system upgrade from V5R4Mx does change the default from XDA to XDN. XDN requires TCP/IP. Therefore TCP/IP over OptiConnect has to be configured.<br />&#160;</li>\r\n<li>DB file statistics are invalidated by the upgrade and need to be rebuilt.<br />&#160;</li>\r\n<li>The stored procedure PLAN_CACHE_TO_MEMORY_MONITOR (external name: QSYS2/PLAN_00001) was not installed during the installation of the operating system. This problem was observed in a few cases after installing IBM i 7.1, but the root cause of this problem has never been found. You can verify that this problem exists by executing the following SQL statement in interactive SQL: SELECT EXTERNAL_NAME FROM QSYS2/SYSPROCS WHERE ROUTINE_NAME = 'PLAN_CACHE_TO_MEMORY_MONITOR'. If this SQL query does not return a row, you are having the problem that is described in this section.<br />&#160;</li>\r\n<li>Change in the CALL QCMDEXC ABAP interface for releases after V5R4.<br />&#160;</li>\r\n<li>You have implemented the Toolbox JDBC Driver / JTOpen (jt400.jar) with a version between JTOpen 6.2 and JTOpen 7.2 inclusive. Later releases will not exhibit this problem.<br />&#160;</li>\r\n<li>If system value QCRTAUT is set to *EXCLUDE, insufficient authority will be assigned to the system file SYSIXADV during the operating system upgrade.<br />&#160;</li>\r\n<li>The PTF SI50530 introduced a change in the application programming interface (API) QWCRSSTS that makes a further patch necessary for SAPHOSTAGENT. For more information see SAP Note 2031545.<br />&#160;</li>\r\n<li>The installation of the PTF SI53206 may move the tables PLCACHDUMP, PRUNESTMT1 and PRUNESTMT2 out of the journal SAPDB4M/QSQJRN. Therefore SQL statements (that rely on commit control) issued by the SAP performance collector may fail. For more information see SAP Note 1622665.<br />&#160;</li>\r\n<li>The main storage pool *BASE contains all main storage that is not being used by other main storage pools. The sizes of shared main storage pools can be configured through the command WRKSHRPOOL, but the storage is only allocated when a subsystem is started that has this main storage pool assigned in its pool definitions. If a subsystem description that is shipped by the operating system (e.g. QINTER) was modified to use a different pool than the default (e.g. *BASE rather than *INTERACT), this setting may be lost during the release upgrade. Thus, the distribution of the main storage over the available main storage pools can be different after the release upgrade, leaving a too small main storage pool for the SAP application server.</li>\r\n</ol>\r\n<p><strong>Section b: Issues Resolved by the Latest IBM Infoapar<br /></strong></p>\r\n<ol>\r\n<li>Problem is described in <strong>IBM APAR MA40198</strong> - an invalid MTU value is being generated.<br />&#160;</li>\r\n<li>Corrupted path information in the ODP cursor chain, causes a loop while searching for a specific cursor.<br />&#160;</li>\r\n<li>This issue can lead to symptoms indicating database inconsistencies. The problem is described in detail in <strong>IBM APAR SE44672</strong>.<br />&#160;</li>\r\n<li>The upgrade uses the SQL statement contained in the definition of the existing view from the original instance to define the shadow view. Under certain circumstances, this information may not have been stored in the view description.<br />&#160;</li>\r\n<li>The joblog contains these messages: \"CPF9898 NON-PSEUDO IN CHAIN 00000\", many different MCH3601, SQL0904 Type 8 RC100<br /><br /></li>\r\n<li>SAP recommends to apply PTF MF62200, which provides significant performance improvements for&#160;ALTER TABLE.</li>\r\n</ol>\r\n<p><br /><strong>Section c: Issues Resolved by a PTF Which Is Not (Yet) Included in the Infoapar</strong></p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Pre-requisites</strong></p>\r\n<ul>\r\n<li><strong>Note:</strong> SAP releases 4.6B and earlier are not supported to run with operating system release IBM i 7.1.<br />&#160;</li>\r\n<li>In order to avoid problems with IBM i 7.1, it is mandatory to apply the following SAP patches or later ones:<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td><strong>4.6D</strong></td>\r\n<td><strong>6.40</strong></td>\r\n<td><strong>7.00</strong></td>\r\n<td><strong>7.01</strong></td>\r\n<td><strong>7.10</strong></td>\r\n<td><strong>7.11</strong></td>\r\n<td><strong>7.20</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>SAPEXE</strong></td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>93</td>\r\n<td>200</td>\r\n<td>86</td>\r\n<td>49</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SAPEXEDB</strong></td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>93</td>\r\n<td>200</td>\r\n<td>86</td>\r\n<td>49</td>\r\n</tr>\r\n<tr>\r\n<td><strong>R3OPT</strong></td>\r\n<td>&nbsp;</td>\r\n<td>327</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>CHKR3PTF</strong></td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>243</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>disp+work (DW)</strong></td>\r\n<td>2582</td>\r\n<td>329</td>\r\n<td>256</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>LIB_DBSL</strong></td>\r\n<td>2582</td>\r\n<td>329</td>\r\n<td>256</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<br />&#160;</li>\r\n<li>IT4J is shipped with SR13 at i 7.1 and it is, therefore, essential that you comply with <strong>SAP Note 1306760</strong> and apply the required patches. Otherwise, errors may occur with Java components that use IIOP communication.<br />&#160;</li>\r\n<li>When using Java, at least the following levels of Software Deployment Tools are required:</li>\r\n<ul>\r\n<li>6.40: SAP J2EE Engine 6.40: SP22</li>\r\n<li>7.00: SAP SOFTWARE DELIVERY MANAGER 7.00: SP16</li>\r\n<li>JAVA SP MANAGER 7.00: SP19 Patch 4</li>\r\n<li>7.01: SAP SOFTWARE DELIVERY MANAGER 7.01: Base</li>\r\n</ul>\r\n<ul>\r\n<li>7.10: SAP SOFTWARE DELIVERY MANAGER 7.10: SP7</li>\r\n</ul>\r\n<ul>\r\n<li>7.11: SAP SOFTWARE DELIVERY MANAGER 7.11: SP1</li>\r\n</ul>\r\n<ul>\r\n<li>7.20: SAP SOFTWARE DELIVERY MANAGER 7.20: Base<br />&#160;</li>\r\n</ul>\r\n<li>If you are using the <strong>JDBC Toolbox Driver (JTOpen)</strong>, then you must upgrade your SAP installations to use at least <strong>JTOpen 7.3</strong>. Make sure that you use the .jar file for the JDBC API 4.0 version of the JTOpen 7.3 driver, if you are running a JDK 6.0 based SAP product (for example CE 7.20). Lower versions of the Toolbox Driver (JTOpen) cannot be used with a database server which is running under IBM i 7.1. In particular, you must not use JTOpen 6.6 or JTOpen 6.7, because this can lead to data corruption! For more information about checking the version and updating the driver, see <strong>SAP Note 654800</strong>, section \"Driver Updates\".</li>\r\n</ul>\r\n<p><strong>Solutions for the issues described above under Symptoms<br /></strong><br /><strong>Section a: Issues Resolved without an IBM PTF</strong></p>\r\n<ol>\r\n<li>For more information about the configuration of TCP/IP over OptiConnect see <strong>SAP Note 1760552</strong>.<br />&#160;</li>\r\n<li>For more information about the utility SAP supplies to solve this issue, see <strong>SAP Note 1264859</strong>.<br />&#160;</li>\r\n<li>Determine the Coded Character Set Identifier (CCSID) of the database cross reference file by entering the command DSPFFD FILE(QSYS/QADBXREF).<br />The fields DBXFIL, DBXLIB and others all show a CCSID value that depends on the installation language that is used in the next command as &lt;CCSID&gt;.<br />Enter the commands:<br />CHGJOB CCSID(&lt;CCSID&gt;)<br />CALL PGM(QSYS/QSQSYSIBM)<br />The program will run for a few minutes. When the executing is done, check the existence of the stored procedure by executing the following SQL statement in interactive SQL again:<br />SELECT EXTERNAL_NAME FROM QSYS2/SYSPROCS WHERE ROUTINE_NAME = 'PLAN_CACHE_TO_MEMORY_MONITOR'<br />This should now return a row. If not, check the joblog of the job that was used to execute program QSYS/QSQSYSIBM.<br />&#160;</li>\r\n<li>The problem is solved with the correction specified in <strong>SAP Note 1526047</strong>.<br />&#160;</li>\r\n<li>The problem and its solution are described in detail in <strong>SAP Note 1548414</strong>.<br />&#160;</li>\r\n<li>If system value QCRTAUT is set to *EXCLUDE, you must grant *PUBLIC authority *CHANGE to system file SYSIXADV manually. The solution is described in detail in <strong>SAP Note 1699187</strong>.<br />&#160;</li>\r\n<li>Implement SAPHOSTAGENT (Release 7.20) patch level 187 or higher.<br />&#160;</li>\r\n<li>Implement SAPHOSTAGENT (Release 7.20) patch level 189 or higher.<br />&#160;</li>\r\n<li>Use the command WRKSYSSTS OUTPUT(*PRINT) to display your main storage pool configuration prior to the upgrade to IBM i 7.1. Use WRKSYSSTS again after the upgrade to verify that your main storage pool configuration is still the same. If not, make the necessary adjustments to ensure that enough main storage is available for the main storage pool that is used by the SAP application servers (typically *BASE).</li>\r\n</ol>\r\n<p><strong>Section b: Issues Resolved by the Latest IBM Infoapar</strong></p>\r\n<ol>\r\n<li>The problem is resolved by <strong>IBM PTF MF51635</strong>.<br />&#160;</li>\r\n<li>The problem is resolved by <strong>IBM PTF MF51384</strong>.<br />&#160;</li>\r\n<li>The originating problem is solved by application of the <strong>IBM PTF SI40917</strong> from the latest DB-Fixpack, but the files still have to be recreated manually. An IBM Techdoc document with&#160;reference number&#160;N1016377 and the title: \"SQL0204 for SYSCOLUMNS or SYSTABLES\" describes how to do this. The document can be found at <a target=\"_blank\" href=\"http://www.ibm.com/support/docview.wss?uid=nas8N1016377\">http://www.ibm.com/support/docview.wss?uid=nas8N1016377</a>.<br />&#160;</li>\r\n<li>The problem is resolved by <strong>IBM PTFs SI42228</strong> and <strong>MF52027</strong>. Once the PTFs have been installed, the affected views should be deleted from the original instance's data and recreated. IBM have created a program, FIXVIEWS, for doing this, which will be given to customers who open a PMR. The program will function only for the original schema (R3&lt;SID&gt;DATA) and not the shadow schema (R3 &lt;SID&gt;SHD). After FIXVIEWS has been executed, AS4FIXFILE should be run like this: AS4FIXFILE DBLIB(R3&lt;SID&gt;DATA) CHGOWN(*YES) STRJRN(*NO). After that the upgrade or EHPI should be able to continue.<br />&#160;</li>\r\n<li>The problem is resolved by <strong>IBM PTF SI47677</strong>.<br /><br /></li>\r\n<li>Apply&#160;<strong>PTF MF62200</strong>&#160;(APAR (APAR&#160;MA45692) to your IBM i machine in front of the upgrade (at least before the shadow database will be created). Due to optimization in the database code a significant improvment for SQL statements with \"ALTER TABLE &lt;table_name&gt; ...\" could be achieved.</li>\r\n</ol>\r\n<p><strong>Section c: Issues Resolved by a PTF Which is Not (Yet) Included in the Infoapar</strong></p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D055775)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5003021)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001432783/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001432783/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432783/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432783/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432783/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432783/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432783/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432783/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001432783/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "68440", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: How do I upgrade to a later OS release?", "RefUrl": "/notes/68440"}, {"RefNumber": "654800", "RefComponent": "BC-DB-DB4", "RefTitle": "FAQ: JDBC driver certified for SAP on IBM i", "RefUrl": "/notes/654800"}, {"RefNumber": "1699187", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: No informationen about recommended indexes", "RefUrl": "/notes/1699187"}, {"RefNumber": "1548414", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: OutOfMemory error due to JDBC driver", "RefUrl": "/notes/1548414"}, {"RefNumber": "1526047", "RefComponent": "CO-PA-TO", "RefTitle": "KEDV: SQL error 420 when creating a summarization level", "RefUrl": "/notes/1526047"}, {"RefNumber": "1481666", "RefComponent": "BC-OP-AS4-JSE", "RefTitle": "IBM i: jcontrol: OS Release OS400 1 7 not supported", "RefUrl": "/notes/1481666"}, {"RefNumber": "1306760", "RefComponent": "BC-JAS-COR-RMT", "RefTitle": "Introducing missing iiop functionality for IBM 142 SR13 JDK", "RefUrl": "/notes/1306760"}, {"RefNumber": "1264859", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Deterioration in performance after upgrade", "RefUrl": "/notes/1264859"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2562760", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Duplicate key entries in tables", "RefUrl": "/notes/2562760 "}, {"RefNumber": "654800", "RefComponent": "BC-DB-DB4", "RefTitle": "FAQ: JDBC driver certified for SAP on IBM i", "RefUrl": "/notes/654800 "}, {"RefNumber": "68440", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: How do I upgrade to a later OS release?", "RefUrl": "/notes/68440 "}, {"RefNumber": "1699187", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: No informationen about recommended indexes", "RefUrl": "/notes/1699187 "}, {"RefNumber": "1264859", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Deterioration in performance after upgrade", "RefUrl": "/notes/1264859 "}, {"RefNumber": "1548414", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: OutOfMemory error due to JDBC driver", "RefUrl": "/notes/1548414 "}, {"RefNumber": "1526047", "RefComponent": "CO-PA-TO", "RefTitle": "KEDV: SQL error 420 when creating a summarization level", "RefUrl": "/notes/1526047 "}, {"RefNumber": "1481666", "RefComponent": "BC-OP-AS4-JSE", "RefTitle": "IBM i: jcontrol: OS Release OS400 1 7 not supported", "RefUrl": "/notes/1481666 "}, {"RefNumber": "1306760", "RefComponent": "BC-JAS-COR-RMT", "RefTitle": "Introducing missing iiop functionality for IBM 142 SR13 JDK", "RefUrl": "/notes/1306760 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}