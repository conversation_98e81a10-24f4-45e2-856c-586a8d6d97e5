{"Request": {"Number": "841612", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 484, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004663662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000841612?language=E&token=46868688EB4489FA46A53C8EDC62FFF0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000841612", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000841612/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "841612"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.06.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-USR-ADM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Users and Authorization administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "User Administration", "value": "BC-SEC-USR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-USR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Users and Authorization administration", "value": "BC-SEC-USR-ADM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-USR-ADM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "841612 - Maximum number of profiles per user"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Changes to the role and/or profile assignments of a user cause profile assignments to disappear without the user administrator being informed about this by the system.<br />Only the end user notices the loss of authorizations since certain transactions can no longer be executed or cannot be executed without errors occurring.<br /><br />In transaction SU01, this error can also occur when any changes are made to the user master.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SU01, SU10, PFCG,<br />BAPI_USER_ACTGROUPS_ASSIGN, BAPI_USER_LOCACTGROUPS_ASSIGN,<br />BAPI_USER_PROFILES_ASSIGN, BAPI_USER_LOCPROFILES_ASSIGN<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by the different system behavior between saving and reading the profile assignments. While up to 311 profiles are written to the database when you save profile assignments, the system only considers 300 profiles when reading the profile assignments.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Use the Note Assistant to implement the correction instructions or import the relevant Support Package.<br /></p> <b>Caution:</b><br /> <b>If you want to use the correction instructions, you must carry out the following manual tasks:</b><br /> <OL>1. Create the new SUSR_USER_PROFS_BUFFER_SAVECHK function module in the SUU2 function group (SUSR package) with the following attributes:</OL> <p></p> <UL><LI>Attributes - Process type:<br />'Normal function module' and<br />'Start immediately'</LI></UL> <UL><LI>Short text:<br />'User/profile assignment object:&#x00A0;&#x00A0;Checks before saving'</LI></UL> <p></p> <UL><LI>Import:<br />Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type spec.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reference Type&#x00A0;&#x00A0;&#x00A0;&#x00A0;Optional<br />USERNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;XUBNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />USE_MESSAGE_TYPE&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BAPI_MTYPE</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Pass Value&#x00A0;&#x00A0;Default value<br />X<br />X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'E'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short text<br />User name<br /> Message type: S Success, E Error, W Warning, I Information, A Abort</p> <UL><LI>Changing:<br />Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type spec.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reference Type&#x00A0;&#x00A0;Optional Pass Value<br />RETURN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BAPIRETTAB<br /><br />Short text<br />Return structure</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Caution:</B><br /> Since the BAPIRETTAB table type is only available as of Release 6.10, you must use BAPIRET2_T as the reference type in Releases 4.6C and 4.6D.<br />The correction is now also available in Release 4.6B.<br />Since Release 4.6B does not contain any table type for the structure BAPIRET2 in the ABAP Dictionary, you must create the following table entry in the interface of the function module instead of the changing entry:<br /><br />Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type spec.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reference Type&#x00A0;&#x00A0;Optional Pass Value<br />RETURN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BAPIRET2 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The source code is implemented by the Note Assistant. <OL>2. Change the interface of the function module SUSR_INTERFACE_PROF.</OL> <OL><OL>a) Add the following IMPORT parameter:<br /><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Parameter Name</TD><TD> PTYPE</TD></TR> <TR><TD>Type spec.</TD><TD> TYPE</TD></TR> <TR><TD>Reference Type</TD><TD> USR10-TYP</TD></TR> <TR><TD>Default value</TD><TD> 'G'</TD></TR> <TR><TD>Optional</TD><TD> Yes</TD></TR> <TR><TD>Pass Value</TD><TD> Yes</TD></TR> <TR><TD></TD></TR> </TABLE></OL></OL> <OL><OL>b) Add the definition of the existing EXPORT parameter PSTATE:<br /><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Type spec.</TD><TD> LIKE</TD></TR> <TR><TD>Associated Type</TD><TD> USR10-AKTPS</TD></TR> <TR><TD>Pass Value</TD><TD> Yes</TD></TR> <TR><TD></TD></TR> </TABLE></OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use Basis Release 7. 00, Basis Release 6.40 with at least Support Package SAPKB64013, or Basis Release 6.20 with at least Support Package SAPKB62055 in your system, you do <B>not</B> have to make changes 2. a) and b) manually. If you use these releases, SNOTE makes the changes automatically.<br /> <p>You can only receive the changed short and long texts for message 263(01) with the Support Package.<br /></p> <b>Explanations for the new functions in the affected transactions:<br /></b><br /> <p>Once this correction is implemented, the maximum possible number of 312 profiles per user can now be managed correctly. This limit is a default value determined by the length of the PROFS field in table USR04 and the maximum length of a profile name. This invalidates the previous limit of 300 profiles.<br /><br />For design reasons, the maintenance transactions unfortunately behave differently for systems with and without Central User Administration (CUA) as soon as the number of possible profiles per user is exceeded:</p> <OL>1. <B>Systems without CUA</B></OL> <UL><LI><B>SU01:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The administrator cannot save a user if the number of profiles is higher than 312. Control reverts to change mode. To allow other roles/profiles to be deleted, the roles/profiles that are newly assigned prior to the error message appears out are not automatically removed from the display.</p> <UL><LI><B>SU10:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The desired changes made to the role and/or profile assignment of a user are not applied. The corresponding error message appears in level 3 of the log.<br />All other changes to this user are implemented.</p> <UL><LI><B>PFCG (with direct assignment of users to a role and with indirect assignments from the HR):</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to assign a user who already has 312 assigned profiles to a new role, the user master comparison prevents additional profiles from being assigned to the user.</p> <UL><LI><B>PFUD (with AUTO_USERCOMPARE=NO and indirect assignment from the HR):</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(see PFCG)</p> <UL><LI><B>BAPI interface</B><br /><B>(BAPI_USER_ACTGROUPS_ASSIGN and BAPI_USER_PROFILES_ASSIGN):</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the list of roles and/or profiles exceeds the 312 limit, no change is made. The RETURN table contains the corresponding error message 01 263.</p> <OL>2. <B>Systems with CUA</B></OL> <OL><OL>a) <B>CUA child system</B></OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the SCUM settings (distribution parameters of the CUA) for roles/profiles are set to <B>'Local'</B>, the child system behaves like a system without CUA. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;However, if these are set to <B>'Global'</B>, <UL><UL><LI>only indirect role assignments can be set locally by means of HR. Here, the child system reacts in the same way as in a system without CUA. Transactions SU01, SU10 and PFCG do not allow any direct profile and/or role assignments.</LI></UL></UL> <UL><UL><LI>the change requests that arrived by IDoc are rejected and the error 01 263 is returned to the central CUA system, which is then visible to this user in transaction SCUL.</LI></UL></UL> <OL><OL>b) <B>Central CUA system</B></OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the SCUM settings (distribution parameters of the CUA) for roles/profiles are set to <B>'Local'</B>, the child system behaves like a system without CUA. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;However, if these are set to <B>'Global'</B>, the following applies to transactions SU01, SU10 and the BAPIs BAPI_USER_LOCACTGROUPS_ASSIGN and BAPI_USER_LOCPROFILES_ASSIGN: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The design for the CUA makes it impossible to have the same behavior as in a system without CUA. <UL><UL><LI>All changes are accepted and distributed. Only the update in the corresponding child system recognizes the error and verifies it. The required changes to the roles and/or profile assignments are not applied. This also applies to roles and/or profile assignments for the central system itself.</LI></UL></UL> <UL><UL><LI><B>Caution:</B><br />The changes made in the central system to roles and/or profile assignments are stored in the tables for the Central User Administration (USL04 and USLA04). If these change requests were rejected by child systems because the maximum number of profiles per user was exceeded, these table entries cannot automatically be undone due to the design of the system. The visible information in transaction SU01 in the central system, therefore, no longer corresponds with the actual status in the child systems.<br /><B>In this case, you must undo <U>all</U> changes that you made to the role and/or profile assignments.</B><br />The corrections from Note 704412 allow you to eliminate this inconsistency for the affected users as of Release 6.20. To do this, use the new function of transaction SCUG that was provided with Note 704412 to import role and profile assignments again.<br />This function is only available in the central CUA system if you implemented the corrections specified in Note 847295.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Unlike a CUA child system, the 'User' tab page is ready for input in transaction PFCG in the central CUA system.<br />When you assign a user to this role, a local role assignment is always created in the central CUA system, regardless of the content of the 'Target system' field in the 'Menu' tab page (see also Note 511200). The system reacts as it would without a CUA. Furthermore, the tables of the CUA (USL04 and USLA04) are also adjusted. </div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D034406)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036362)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000841612/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000841612/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000841612/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000841612/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000841612/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000841612/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000841612/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000841612/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000841612/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "847295", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "SCUG|CUA central system: Reimporting role/profile allocation", "RefUrl": "/notes/847295"}, {"RefNumber": "704412", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "CUA support for license data maintenance", "RefUrl": "/notes/704412"}, {"RefNumber": "511200", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "PFCG/PFUD/SU01/SU10: Role assignment and profile comparison", "RefUrl": "/notes/511200"}, {"RefNumber": "410993", "RefComponent": "BC-SEC-USR", "RefTitle": "Maximum number for profiles and authorizations", "RefUrl": "/notes/410993"}, {"RefNumber": "1554560", "RefComponent": "BC-SEC-USR-IS", "RefTitle": "SUIM|RSUSR100/RSUSR100N Incorrect display of change docs.", "RefUrl": "/notes/1554560"}, {"RefNumber": "1527047", "RefComponent": "GRC-SAC-ARQ", "RefTitle": "Error in CUP provisioning: TOO_MANY_PROFILES_IN_USER", "RefUrl": "/notes/1527047"}, {"RefNumber": "1240660", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error generating authorizations: Inconsistency", "RefUrl": "/notes/1240660"}, {"RefNumber": "1227405", "RefComponent": "BC-SEC-AUT", "RefTitle": "Treatment of generated objects in SU02 and SU03", "RefUrl": "/notes/1227405"}, {"RefNumber": "1124857", "RefComponent": "BC-SEC-USR", "RefTitle": "Error in the function module SUSR_INTERFACE_AUTH", "RefUrl": "/notes/1124857"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3285804", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "PFCG: Authorization name missing and showing like \"_________xx\" or \"xx\" where xx are 2 digits from 00 to 99", "RefUrl": "/notes/3285804 "}, {"RefNumber": "511200", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "PFCG/PFUD/SU01/SU10: Role assignment and profile comparison", "RefUrl": "/notes/511200 "}, {"RefNumber": "1554560", "RefComponent": "BC-SEC-USR-IS", "RefTitle": "SUIM|RSUSR100/RSUSR100N Incorrect display of change docs.", "RefUrl": "/notes/1554560 "}, {"RefNumber": "1527047", "RefComponent": "GRC-SAC-ARQ", "RefTitle": "Error in CUP provisioning: TOO_MANY_PROFILES_IN_USER", "RefUrl": "/notes/1527047 "}, {"RefNumber": "410993", "RefComponent": "BC-SEC-USR", "RefTitle": "Maximum number for profiles and authorizations", "RefUrl": "/notes/410993 "}, {"RefNumber": "1227405", "RefComponent": "BC-SEC-AUT", "RefTitle": "Treatment of generated objects in SU02 and SU03", "RefUrl": "/notes/1227405 "}, {"RefNumber": "1240660", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error generating authorizations: Inconsistency", "RefUrl": "/notes/1240660 "}, {"RefNumber": "1124857", "RefComponent": "BC-SEC-USR", "RefTitle": "Error in the function module SUSR_INTERFACE_AUTH", "RefUrl": "/notes/1124857 "}, {"RefNumber": "704412", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "CUA support for license data maintenance", "RefUrl": "/notes/704412 "}, {"RefNumber": "847295", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "SCUG|CUA central system: Reimporting role/profile allocation", "RefUrl": "/notes/847295 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B59", "URL": "/supportpackage/SAPKB46B59"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C51", "URL": "/supportpackage/SAPKB46C51"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C55", "URL": "/supportpackage/SAPKB46C55"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D41", "URL": "/supportpackage/SAPKB46D41"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61044", "URL": "/supportpackage/SAPKB61044"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62053", "URL": "/supportpackage/SAPKB62053"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62063", "URL": "/supportpackage/SAPKB62063"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64013", "URL": "/supportpackage/SAPKB64013"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64021", "URL": "/supportpackage/SAPKB64021"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70002", "URL": "/supportpackage/SAPKB70002"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70013", "URL": "/supportpackage/SAPKB70013"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71003", "URL": "/supportpackage/SAPKB71003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 11, "URL": "/corrins/0000841612/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 11, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 20, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "46B", "Number": "650465 ", "URL": "/notes/650465 ", "Title": "SU01: Problems with batch input sessions and recordings", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "46B", "Number": "694999 ", "URL": "/notes/694999 ", "Title": "CUA: Error when using BAPIs to assign roles and profiles", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "46B", "Number": "704088 ", "URL": "/notes/704088 ", "Title": "SU01: Missing HR authorization when you delete a user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "46B", "Number": "787292 ", "URL": "/notes/787292 ", "Title": "Password-related problems", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "46B", "Number": "799714 ", "URL": "/notes/799714 ", "Title": "SUIM|Error in variants of the RSUSR002 report", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "640", "Number": "713453 ", "URL": "/notes/713453 ", "Title": "SUIM|RSUSR100: Change documents not found", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "640", "Number": "773542 ", "URL": "/notes/773542 ", "Title": "SU02/SU03: Generated elements processed incorrectly", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "700", "Number": "943796 ", "URL": "/notes/943796 ", "Title": "PFCG: Maintain authorization data and generate profiles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "617012 ", "URL": "/notes/617012 ", "Title": "Table ust10s contains deleted profiles", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "640505 ", "URL": "/notes/640505 ", "Title": "SUIM|RSUSR100 Program termination DBIF_RSQL_INVALID_RSQL", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "650465 ", "URL": "/notes/650465 ", "Title": "SU01: Problems with batch input sessions and recordings", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "704088 ", "URL": "/notes/704088 ", "Title": "SU01: Missing HR authorization when you delete a user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "783792 ", "URL": "/notes/783792 ", "Title": "Transaction can be started despite missing authorization", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "787292 ", "URL": "/notes/787292 ", "Title": "Password-related problems", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "795769 ", "URL": "/notes/795769 ", "Title": "SUIM| User list  Function 'Select subtree expand' (2)", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "799714 ", "URL": "/notes/799714 ", "Title": "SUIM|Error in variants of the RSUSR002 report", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "826908 ", "URL": "/notes/826908 ", "Title": "PFCG: Profile generation fails for large roles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "849011 ", "URL": "/notes/849011 ", "Title": "Authorization check when renaming user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "961294 ", "URL": "/notes/961294 ", "Title": "SUIM|Error when searching for field values in several fields", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46D", "Number": "943796 ", "URL": "/notes/943796 ", "Title": "PFCG: Maintain authorization data and generate profiles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "620", "Number": "694999 ", "URL": "/notes/694999 ", "Title": "CUA: Error when using BAPIs to assign roles and profiles", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "640", "Number": "773542 ", "URL": "/notes/773542 ", "Title": "SU02/SU03: Generated elements processed incorrectly", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46D", "ValidTo": "46D", "Number": "704088 ", "URL": "/notes/704088 ", "Title": "SU01: Missing HR authorization when you delete a user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46D", "ValidTo": "46D", "Number": "787292 ", "URL": "/notes/787292 ", "Title": "Password-related problems", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46D", "ValidTo": "46D", "Number": "799714 ", "URL": "/notes/799714 ", "Title": "SUIM|Error in variants of the RSUSR002 report", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "610", "Number": "704088 ", "URL": "/notes/704088 ", "Title": "SU01: Missing HR authorization when you delete a user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "610", "Number": "787292 ", "URL": "/notes/787292 ", "Title": "Password-related problems", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "620", "Number": "650465 ", "URL": "/notes/650465 ", "Title": "SU01: Problems with batch input sessions and recordings", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "620", "Number": "783792 ", "URL": "/notes/783792 ", "Title": "Transaction can be started despite missing authorization", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "640", "Number": "799714 ", "URL": "/notes/799714 ", "Title": "SUIM|Error in variants of the RSUSR002 report", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "492777 ", "URL": "/notes/492777 ", "Title": "SUIM RSUSR100 performance problems with change documents", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "617012 ", "URL": "/notes/617012 ", "Title": "Table ust10s contains deleted profiles", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "640505 ", "URL": "/notes/640505 ", "Title": "SUIM|RSUSR100 Program termination DBIF_RSQL_INVALID_RSQL", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "675846 ", "URL": "/notes/675846 ", "Title": "SUIM|RSUSR100: Incorrect change documents", "Component": "BC-SEC-USR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "704088 ", "URL": "/notes/704088 ", "Title": "SU01: Missing HR authorization when you delete a user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "733205 ", "URL": "/notes/733205 ", "Title": "SUIM|RSUSR100: Incorrect results for: \"Changed by\"", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "783792 ", "URL": "/notes/783792 ", "Title": "Transaction can be started despite missing authorization", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "787292 ", "URL": "/notes/787292 ", "Title": "Password-related problems", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "795769 ", "URL": "/notes/795769 ", "Title": "SUIM| User list  Function 'Select subtree expand' (2)", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "799714 ", "URL": "/notes/799714 ", "Title": "SUIM|Error in variants of the RSUSR002 report", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "826908 ", "URL": "/notes/826908 ", "Title": "PFCG: Profile generation fails for large roles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "844282 ", "URL": "/notes/844282 ", "Title": "SU01: Creating license data", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "849011 ", "URL": "/notes/849011 ", "Title": "Authorization check when renaming user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "943796 ", "URL": "/notes/943796 ", "Title": "PFCG: Maintain authorization data and generate profiles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "961294 ", "URL": "/notes/961294 ", "Title": "SUIM|Error when searching for field values in several fields", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "694999 ", "URL": "/notes/694999 ", "Title": "CUA: Error when using BAPIs to assign roles and profiles", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "783792 ", "URL": "/notes/783792 ", "Title": "Transaction can be started despite missing authorization", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "787292 ", "URL": "/notes/787292 ", "Title": "Password-related problems", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "795769 ", "URL": "/notes/795769 ", "Title": "SUIM| User list  Function 'Select subtree expand' (2)", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "826908 ", "URL": "/notes/826908 ", "Title": "PFCG: Profile generation fails for large roles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "943796 ", "URL": "/notes/943796 ", "Title": "PFCG: Maintain authorization data and generate profiles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "961294 ", "URL": "/notes/961294 ", "Title": "SUIM|Error when searching for field values in several fields", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "783792 ", "URL": "/notes/783792 ", "Title": "Transaction can be started despite missing authorization", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "795769 ", "URL": "/notes/795769 ", "Title": "SUIM| User list  Function 'Select subtree expand' (2)", "Component": "BC-SEC-USR-IS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "826908 ", "URL": "/notes/826908 ", "Title": "PFCG: Profile generation fails for large roles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "936340 ", "URL": "/notes/936340 ", "Title": "RSUSR101: No change documents for authorization objects", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "943796 ", "URL": "/notes/943796 ", "Title": "PFCG: Maintain authorization data and generate profiles", "Component": "BC-SEC-AUT-PFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "961294 ", "URL": "/notes/961294 ", "Title": "SUIM|Error when searching for field values in several fields", "Component": "BC-SEC-USR-IS"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1237517", "RefTitle": "Error log in function module SUSR_INTERFACE_AUTH", "RefUrl": "/notes/0001237517"}, {"RefNumber": "1097770", "RefTitle": "Migration to analysis auths: Dump in SAPLRSEC_MIGRATION", "RefUrl": "/notes/0001097770"}, {"RefNumber": "1124857", "RefTitle": "Error in the function module SUSR_INTERFACE_AUTH", "RefUrl": "/notes/0001124857"}, {"RefNumber": "1227405", "RefTitle": "Treatment of generated objects in SU02 and SU03", "RefUrl": "/notes/0001227405"}]}}}}}