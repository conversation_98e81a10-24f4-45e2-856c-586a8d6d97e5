{"Request": {"Number": "838003", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 240, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015882582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000838003?language=E&token=7454D77A98FCF2792F6E908D9E527FCF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000838003", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000838003/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "838003"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.11.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "838003 - Industry add-ons integrated into SAP ECC 6.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are preparing to upgrade to SAP ERP Central Component 6.0 with an integrated former add-on.<br /><br />You are preparing to install or activate a business function set.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ERP Central Component, Industry Extension, business function set</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note provides the names of the former IS add-ons that have been integrated into SAP ERP Central Component 6.0 and that are available as business function sets.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>This note is updated on a regular basis. Always refer to the current version when planning and executing the upgrade.</B><br /><br /><B>Change history:</B><br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Section</TH><TH ALIGN=LEFT> Comment</TH></TR> <TR><TD>14.11.2007</TD><TD> I.b)</TD><TD></TD></TR> <TR><TD>25.10.2005 </TD><TD> I.b) </TD><TD> </TD></TR> <TR><TD>09.01.2005</TD><TD> I. and II.</TD><TD> Reference to upgrade and installation notes</TD></TR> </TABLE> <b></b><br /> <b>I.a) Upgrading to SAP ERP Central Component 6.0</b><br /> <b></b><br /> <p>As of Release SAP ERP 2005, most industry solutions that were previously available as add-ons are part of SAP ERP Central Component (SAP ECC). This means that the software exists on the system when you install SAP ECC.<br /><br />To use an industry solution, you must use a switch to activate the relevant business function.<br />When you upgrade former industry solutions to SAP ECC, the system automatically activates the relevant business function.<br /><br />For more information about the upgrade, read section II.<br /><br /></p> <b>I.b) Installing or activating a business function set</b><br /> <p></p> <b><B>Caution</B></b><br /> <p>Note that the activation of an Industry Business Solution (business function set) leads to changes in the ABAP Dictionary, in the function and it also results in an adjustment of existing data. These changes cannot be undone.<br />If you have activated an Industry Business Solution by mistake, but you do not want to use it, you can only reset the system to the status it had before the activation.</p> <b></b><br /> <b><B>Important license information</B></b><br /> <p>Note that your general license agreement does not cover all of the business function sets or business functions that are delivered with SAP ECC 6.0.<br />If you use these software components from SAP ECC 6.0, this may lead to additional charges and changes to the terms of use in accordance with SAP's current List of Prices and Conditions (LPC).<br /><br /><B>Caution:</B><br /><B>********</B><br />If you activate an industry solution, this results in the following restrictions for your system: Note that, generally, you cannot combine an industry solution with other industry solutions or add-ons. In addition to this, you cannot deactivate an industry solution once it has been activated. For this reason, if you activate an industry solution by mistake, this may lead to considerable costs.<br />If you are in doubt, contact your sales representative.<br /><br />Section II of this note specifies the installation note for the industry solution you require. Read the note on installing/activating the industry solution before you begin the installation process.<br /><br /></p> <b>II. Release strategy, upgrade and installation notes for the industry solutions</b><br /> <p>The following list provides the names of the former IS add-ons, the names of the corresponding business function sets in SAP ECC, the corresponding release strategy note and the additional information about the upgrade and installation for an industry solution. Refer to the relevant release strategy note for details about the source releases that are supported for an upgrade.<br /></p> <OL>1. ECC-DIMP, DIMP, DI, IS-SW, IS-HT, IS-MP, IS-AD, IS-A<br />These are included in the Business Function Set 'Discrete Industries &amp; Mill Products', software component ECC-DIMP.<br />Release strategy note:   874473<br />Add info about upgrade to SAP ECC 6.0:  872197<br />Add info about inst/activating on ECC 6.0: 874471</OL> <p></p> <OL>2. INSURANCE<br />This is included in the Business Function Set 'Insurance', software component INSURANCE.<br />Release strategy note:   877945<br />Add info about upgrade to SAP ECC 6.0:  872705<br />Add info about inst/activating on ECC 6.0:  873734</OL> <p></p> <OL>3. IS-H<br />This is included in the Business Function Set 'Healthcare', software component IS-H (Healthcare).<br />Release strategy note:   879311<br />Add info about upgrade to SAP ECC 6.0:  874200<br />Add info about inst/activating on ECC 6.0:  874199</OL> <p></p> <OL>4. IS-M<br />This is included in the Business Function Set 'SAP Media', software component IS-M.<br />Release strategy note:   880233<br />Add info about upgrade to SAP ECC 6.0:  874412<br />Add info about inst/activating on ECC 6.0:  874411</OL> <p></p> <OL>5. IS-PS-CA<br />This is included in the Business Function Set 'Public Services' or in the Business Function Set 'SAP Campus Management', software component IS-PS-CA.<br />Release strategy note:  881284<br />Add info about upgrade to SAP ECC 6.0:  874415<br />Add info about inst/activating on ECC 6.0:  874414</OL> <p></p> <OL>6. IS-U/CCS, ISUCEN and IS-U/IDEX<br />These are included in the Business Function Set 'Utilities', software component IS-UT.<br />Release strategy note:   881842<br />Add info about upgrade to SAP ECC 6.0:  874418<br />Add info about inst/activating on ECC 6.0: 874416</OL> <p></p> <OL>7. IS-T<br />This is included in the Business Function Set 'Telco' (Telecommunications), software component IS-UT.<br />Release strategy note:   881843<br />Add info about upgrade to SAP ECC 6.0:  874478<br />Add info about inst/activating on ECC 6.0:  874477</OL> <p></p> <OL>8. IS-OIL and IS-MINE<br />These are included in the Business Function Set 'SAP Oil and Gas' or in the Business Function Set 'SAP Mining', software component IS-OIL.<br />Release strategy note:   883774<br />Add info about upgrade to SAP ECC 6.0:  874543<br />Add info about inst/activating on ECC 6.0: 874541</OL> <p></p> <OL>9. IS-CWM<br />This is included in the Business Function Set 'CWM' (Catch Weight Management), software component IS-CWM.<br />Release strategy note:   884082<br />Add info about upgrade to SAP ECC 6.0:  884083<br />Add info about inst/activating on ECC 6.0:  884081</OL> <p></p> <OL>10. FI-CAX<br />This is included in the Business Function Set 'Contract Accounting', software component FI-CAX.<br />Release strategy note:   884566<br />Add info about upgrade to SAP ECC 6.0:  874548<br />Add info about inst/activating on ECC 6.0:  874546<br /><br /></OL> <b>Problems activating an IS business function</b><br /> <p>If you want to activate additional business functions after upgrading to ERP 2005, read the following Note:<br />Problems activating an IS Business Function: 886694</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033006)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024152)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000838003/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000838003/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000838003/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000838003/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000838003/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000838003/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000838003/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000838003/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000838003/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2665275", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "Calling a FI-CA transaction error message SFW052 is raised", "RefUrl": "/notes/2665275 "}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "664712", "RefComponent": "XX-PROJ-CS-BC", "RefTitle": "Migration Cable Solution V46C.1A -> DIMP / ECC-DIMP", "RefUrl": "/notes/664712 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}