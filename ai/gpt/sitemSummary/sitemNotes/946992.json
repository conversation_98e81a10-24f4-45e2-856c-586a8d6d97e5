{"Request": {"Number": "946992", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 501, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005566902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000946992?language=E&token=161F368FA836D91A7E8EFBCD909529A6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000946992", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000946992/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "946992"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.01.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DBA-MD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Master Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Basis", "value": "BW-WHM-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "BW-WHM-DBA-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "946992 - Transaction Load fails with RSDMD 138 error"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p> You are doing a transaction load into an info cube and sporadically it fails with the following error.message:<br />Master data/text of characteristic XXX already deleted (RSDMD 138)<br /><br /> XXX is the name of the characteristic which is part of the info cube into which the load is done.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p> transaction load, RSDMD, 138, master data, deletion, delete, RSDMD138, info cube, RSMASD.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p> During a transaction load, it is checked for every characteristic (and every datapackage) if an entry for the same exists in the table RSMASD. If yes, the loading program tries to get an exclusive lock on this characteristic. If it fails to get an exclusive lock at this point, the loading of that particular datapackage fails with RSDMD 138 error.<br /><br /> In cases, where a prior master data deletion into a characteristic aborted due to some reason, the entry in RSMASD table continues to remain. Hence during every transaction load and for every data packages , it finds an entry in RSMASD table and further tries to get an exclusive lock on it. In rare scenarios, if the exclusive lock for the info object is held by another user or process, RSDMD 138 error occurs for the data package.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>1.&#x00A0;&#x00A0;To get rid of this problem, the entry in RSMASD table has to be removed (manually) after making sure that there are no master data deletion process running currently on the same characteristic. A report can be written to delete this entry for that particular characteristic from the table RSMASD as below:&#x00A0;&#x00A0;&#x00A0;&#x00A0;REPORT&#x00A0;&#x00A0;ZRSMASD_DEL_ENTRY.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;data : wa_rsmasd type RSMASD.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;wa_rsmasd-chabasnm = '&lt;characteristic name&gt;'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;delete rsmasd from wa_rsmasd.</p> <UL><LI>2.&#x00A0;&#x00A0;With the code correction in this note, a certain wait period is induced during the requesting of an exclusive lock. When the lock is not available, the program will additionally attempt several times to get this. But if it is still not available, then the loading of the datapackage will end with RSDMD 138 error.</LI></UL> <UL><LI></LI></UL> <UL><LI>BW3.0B</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 32 for Release 3.0B (BW3.0B Patch 32 or <B>SAPKW30B32</B>) into your BW system. The Support Package will be available when <B>note 0914949</B> with the short text \"SAPBWNews BW 3.0B Support Package 32\", describing this Support Package in more detail, is released for customers.</p> <UL><LI>BW3.1C (Content)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 26 for Release 3.1C (BW3.1C Patch 26 or <B>SAPKW31026</B>) into your BW system. The Support Package will be available when <B>note 0935962</B> with the short text \"SAPBWNews BW 3.1C Support Package 26\", describing this Support Package in more detail, is released for customers.</p> <UL><LI>BW 3.50</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 18 for Release 3.50 (BW3.50 Patch 18 or <B>SAPKW35018</B>) into your BW system. The Support Package will be available when <B>note 0928661</B> with the short text \"SAPBWNews BW SP18 NetWeaver'04 Stack 18\", describing this Support Package in more detail, is released for customers.</p> <UL><LI>BW 7.0</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 09 for Release 7.0 (BW7.0 Patch 09 or <B>SAPKW70009</B>) into your BW system. The Support Package will be available when note <B>0914303 </B>with the short text \"SAPBWNews BW 7.0 SP09\", describing this Support Package in more detail, is released for customers.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><br /> <br />This note may already be available before the Support Package is released.&#x00A0;&#x00A0;However, the short text will still contain the words \"preliminary version\" in this case.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DBA-ICUB (InfoCubes)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I034467)"}, {"Key": "Processor                                                                                           ", "Value": "I030948"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000946992/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000946992/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000946992/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000946992/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000946992/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000946992/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000946992/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000946992/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000946992/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "936694", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSDMD138: Master data/text of characteristic already deleted", "RefUrl": "/notes/936694"}, {"RefNumber": "935962", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 26", "RefUrl": "/notes/935962"}, {"RefNumber": "928661", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 18 NW'04 Stack 18 RIN", "RefUrl": "/notes/928661"}, {"RefNumber": "914949", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 32", "RefUrl": "/notes/914949"}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303"}, {"RefNumber": "613951", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Deleting master data: lock (table RSMASD) remains", "RefUrl": "/notes/613951"}, {"RefNumber": "1167783", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Irrelevant Message while maintain Master Data of Infoobject", "RefUrl": "/notes/1167783"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1167783", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Irrelevant Message while maintain Master Data of Infoobject", "RefUrl": "/notes/1167783 "}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303 "}, {"RefNumber": "928661", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 18 NW'04 Stack 18 RIN", "RefUrl": "/notes/928661 "}, {"RefNumber": "935962", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 26", "RefUrl": "/notes/935962 "}, {"RefNumber": "914949", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 32", "RefUrl": "/notes/914949 "}, {"RefNumber": "936694", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSDMD138: Master data/text of characteristic already deleted", "RefUrl": "/notes/936694 "}, {"RefNumber": "613951", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Deleting master data: lock (table RSMASD) remains", "RefUrl": "/notes/613951 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B39INVCBWTECH", "URL": "/supportpackage/SAPK-30B39INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B32", "URL": "/supportpackage/SAPKW30B32"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31026", "URL": "/supportpackage/SAPKW31026"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31026", "URL": "/supportpackage/SAPKW31026"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35018", "URL": "/supportpackage/SAPKW35018"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "NumberOfCorrin": 1, "URL": "/corrins/0000946992/654"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}