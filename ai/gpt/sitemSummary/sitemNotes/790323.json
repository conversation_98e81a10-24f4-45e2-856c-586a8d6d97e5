{"Request": {"Number": "790323", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 399, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015790812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000790323?language=E&token=067E37658F84642156DECD3F2CD5B7F0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000790323", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000790323/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "790323"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.05.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorizations"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorizations", "value": "BW-BEX-OT-OLAP-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "790323 - The log for reporting authorizations in BW"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3> <p>You require detailed information about the authorization check when accessing transaction data in a BW system (mostly when you execute a query). The \"RSSM log\" logs the process of this check in note form. This note provides more detailed information about how to use the RSSM log.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>No authorization, trace, log, RSR, query, BRAIN 804<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Contents</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;------</B><br /><br /><B>Part 1 Working with the RSSM log</B><br /><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(A) Creating a log </B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(B) Simplifying the log</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(C) RSSM log as a spool request</B><br /><br /><br /><B>Part 2 Contents of the RSSM log</B><br /><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(A) Log header</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(B) Filling the Buffer</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(C) Displaying the selection</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(D) Checking the RS authorizations</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(E) Detailed check of the RSR authorizations</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(F) The core of the check</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(G) The block for hierarchy authorizations (highlighted in green)</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(H) End of the log</B><br /><br /><br /><br /><br /><B></B></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><br /><br /><STRONG><U>Part 1 Working with the RSSM log</U></STRONG><br /><STRONG></STRONG></p> <b><B>(A) Creating a log</B></b><br /> <OL>1. Start transaction RSSM (the name of the log is derived from this) --&gt; Choose \"Authorization Check Log\" --&gt; Enter the user ID of the test user in the input field --&gt; Choose the '+' button (or F5). The system displays the user ID in the list below.</OL> <OL>2. Execute the action you want to check. Usually, this is the execution of a query in the BEx Analyzer, the Web or in transaction RSRT. However, you can log all other actions that use the reporting authorization.</OL> <OL>3. After executing the action, choose \"Display Log (F7)\". The system displays the last log that was recorded. The system no longer supports the \"Display (Old Version)\" function.</OL> <OL>4. Remember to remove the user ID after the test. To do this, enter the user ID again in the input field and choose the '-' button (or F6).</OL> <p><br /><br /></p> <b>(B) Simplifying the log</b><br /> <p><br />The RSSM log may be very long and not very clear You should use every possible method to ensure that the log is as simple as possible.</p> <OL>1. <B>Variables filled from authorizations</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Remove authorization variables from the query if possible. Instead of these, use fixed filters with the values that you expect for the variables (in this test). This makes the log shorter and generally simplifies authorization problems. <p></p> <OL>2. <B>F4 Input help</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If possible, avoid using the F4 input help (for authorization-relevant characteristics) when you execute the query. This not only makes the log shorter but it may also simplify the processing sequence of the different blocks. <p></p> <OL>3. <B>Navigation</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Navigating in the query (for example, filtering or adding a characteristic for the drilldown) often causes the system to run certain steps of the authorization check several times. As a result, the authorization log is more difficult to understand. If possible, use the \"Bookmark\" function in transaction RSRT (or RSSMQ) (see Note 788393). Executing a bookmark that was saved before makes the log as short as possible. <p><br /><br /></p> <b>(C) RSSM log as a spool request</b><br /> <b></b><br /> <p>From a technical point of view, a RSSM log is stored in the form of a spool request. Therefore, you can also use transaction SP01 to view the logs. If you do this, you should consider the following points:</p> <OL>1. You can also access old logs. Take the time stamp into account. The entry in the \"Created By\" field in transaction SP01 is always the logon user. (This may not be the same as the test user.) In case of doubt, display the spool requests for all users.</OL> <OL>2. As of Support Package 25 for BW 3.0 (Support Package 19 for BW3.1; Support Package 05 for BW3.5), RSSM logs have a corresponding \"Title\" in the spool request list.</OL> <OL>3. In certain cases, a log can be stored in the form of <B>two</B> spool requests. If this is the case, you can only view the entire log in transaction ST01. Consider this, for example, if the system displays a log incompletely in the RSSM view. You then find a further spool request that was created immediately before or after this.</OL> <OL>4. Longer spool requests (with more than 10 pages) may not be displayed completely. In the normal log display, choose \"Settings\". You can set a higher page number here if necessary.</OL> <OL>5. When creating a spool request, the system uses the logon language and the log that is created is not changed afterwards. If you want to analyze a log for SAP Support employees, then log on to the system in English. Inform SAP of the number of the spool request.<br />To speed up message processing, you can also export an RSSM log and attach the file directly to a customer message. (Menu: \"System\" --&gt; \"Lists\" --&gt; ...)&#x00A0;&#x00A0;Then it may not be necessary to access the system. <B>Important:</B> Select \"HTML\" as the output format. This ensures that the formatting is correct and that the red and green icons are transferred correctly.</OL> <p><br /><br /><B>Note:</B><br />The RSSM authorization log is meant to help administrators understand and solve authorization problems. It is not designed for inexperienced users. Its format and handling is not suitable for use by end users. We welcome any suggestions for improvements but cannot guarantee that these will be implemented. BI development may add, change or remove parts of the log at any time.<br /><br /><br /><br /><br /><br /><br /><STRONG><U>Part 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Contents of the RSSM log</U></STRONG><br /><br /><br />This part of the note contains information about the individual blocks of the RSR log.<br /><B>Note:</B>The log is saved simultaneously to the processing of the corresponding function modules. Therefore, the sequence of the individual blocks may differ, depending on the process of the logged action. In many cases, certain blocks do not exist at all.<br /><br /><br /><br /></p> <b>(A) Log header</b><br /> <p><br />Always take the time stamp and the system information into account to make sure that the relevant spool request matches the activity that you have logged.<br />If there is no header at all, you are probably looking at the second part of a log that is \"cut\" into different parts. If this is the case, proceed as described under point 2.C.2.<br />If you are using transaction RSSMQ, the user specified here is the test user that is used in Transaction RSSMQ and not the logon user.<br /><br /><br /></p> <b>(B) Filling the Buffer</b><br /> <p><br />If you call a routine from the reporting authorizations area (for example, at the beginning of a query execution), the system saves the user authorizations in internal buffers. There are two separate blocks, the beginning and the end of which are marked by text lines highlighted in pink. One of the blocks contains authorization data that is independent of the InfoProvider used. The other block contains data that is specific for the InfoProvider specified.<br />Each block contains the following sections (possibly empty):</p> <OL>1. <B>\"Optimization of Authorizations\"</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Authorizations (for example from different profiles) are summarized here if this is logically possible. For example: Interval [A - C] and single value [B]. <OL>2. <B>\"Authorization-relevant characteristics\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Which characteristics are authorization-relevant depends on the authorization objects that are activated for this InfoProvider (or all InfoProviders). In addition, all characteristics for which the user has complete authorization are ignored. <OL>3. <B>\"Characteristics for which the user has full authorization\"</B>:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If a user has complete authorization for a characteristic in all relevant authorization objects, the characteristic is listed here. To achieve optimal performance, characteristics that are listed here are no longer taken into account in the further (detailed) check. <OL>4. <B>\"The following authorization objects are taken into account\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Authorization objects that are activated for the relevant InfoProvider (or all InfoProviders) and for which the user does not have complete authorization are listed here. If a user has complete authorization for an authorization object, it is not listed here and it is not taken into account in the further authorization check. <OL>5. <B>\"Authorization Objects Checked\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The authorization objects that are activated for the relevant InfoProvider are listed here. This corresponds to the relevant setting in Transaction RSSM. <OL>6. <B>\"Use authorizations\":</B>(the yellow bar)</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The relevant authorizations of the user are displayed here in the following nested form: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Authorization object <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-- authorizations contained in this <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--- characteristics contained in this <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;---- allowed value contained in this (or intervals) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The summaries that occurred under 1) have already been taken into account. You can check the reporting authorizations of the relevant user here. <p><br /><br /></p> <b>(C) Displaying the selection</b><br /> <p><br />The first part of the log contains information about user and system specific settings. In most cases, the quantity of the authorized values is relevant in this section. The following part of the log contains the quantity of the selected values (the \"Selection\"). In most cases, the filters in the executed query determine the selection.<br /><br />There may be a message highlighted in red saying that \"Full Authorization is required for this selection option\" and a characteristic may be specified. In most cases, this means that there is an exclude condition (that is, an excluding selection) for the characteristic. In the current release, this is converted into a full selection in most cases and this can only be authorized by a full (*) authorization. (For more information, see Note 860488)<br /><br />Under the heading that is highlighted in blue \"Selections to be Checked (Query Filter, ...)\", the following blocks are then displayed:<br /></p> <OL>1. Some important parameters of the check are specified (for example the query name).</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Hierarchy information: (yellow bar) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the corresponding values have been specified by the calling procedure, the log lists the following information: <UL><UL><LI>Hierarchy name, version and key date</LI></UL></UL> <UL><UL><LI>Name of the characteristic of the hierarchy</LI></UL></UL> <UL><UL><LI>HIESID and SVER of the hierarchy. These are technical specifications that are used in the RSHIEDIR and RSRHIEDIR_OLAP tables, for example.</LI></UL></UL> <UL><UL><LI>Display status of the hierarchy (hierarchy is or is not displayed.)</LI></UL></UL> <OL>2. Selections to be Checked: (yellow bar)</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The elements of the selection are displayed in a nested hierarchy: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SUBNR: (blue field) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The largest blocks of a selection are the SUB numbers. For example, each structure item used in the query creates its own separate SUB number. The SUB numbers of the authorization correspond to a large extent to the \"FEMS\" of the OLAP processor (see Note 509038). Each SUB number is checked separately. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Characteristic: (orange field, indented twice) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;First row: Characteristic name, characteristic master data ID (SID) (internal characteristic number, see table /BI0/SIOBJNM), the name of the corresponding field in authorization objects. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Indented twice more:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Characteristic Values\" <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Here, the \"flat\" selections (that is single values and intervals) of this characteristic are specified. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Hierarchy node\" <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In this section, the technical data of the hierarchy selections is listed. HIESID and SVER specify the hierarchy used for the selection filter. You cannot display the node name directly. Using the NODESID of the node, you can determine the node name via the K table of the characteristic (/BIx/K&lt;name of characteristic&gt;). Unfortunately, you can no longer display the node name directly. NODELEVEL specifies the hierarchy level at which the selected node is located. RETURNLEVEL specifies how many further levels are to be drilled down under this node. <p><br /><br /></p> <b>(D) Checking the RS authorizations</b><br /> <p><br />This is an exception in the RSSM log. The system does not display data for reporting authorizations (RSR) here, rather it displays checks of the RS authorization objects S_RS_ICUBE and/or S_RS_HIER.&#x00A0;&#x00A0;Note 846839 explains the difference. The basic tools for the authorization check (Transactions ST01 and SU53) contain further information about these checks. For the individual authorization objects, the system checks activities that correspond to the display.<br />If you are using Transaction RSSMQ, note the following:<br />Since these checks use basic functions, you cannot emulate the authorizations of a test user. The system always uses the authorization data of the logon user. If you think this may cause a problem, you cannot use Transaction RSSMQ. In this case, you must log on to the system directly using the test user. Note 177875 contains more information about the authorizations the test user should have.<br /><br /><br /></p> <b>(E) Detailed check of the RSR authorizations</b><br /> <p><br />\"Authorization Check for Reporting Authorizations\": (blue bar)<br /><br />The authorizations of the user are listed in part B.6, the selection in section C.3. In the following part, both quantities are compared. The comparison is based on the following basic principle: If the selected quantity is completely contained in the authorized quantity, the check is successful. If the selected quantity is larger than the authorized quantity, the check fails.<br /><br />Nested loops are used to represent the structure of the logic of the authorization check:<br /><br /><B>First loop</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This loop checks the individual SUB numbers (highlighted in blue). They are checked completely separately and the system displays them either separately or it does not display them at all in the query (if they are not authorized). <UL><UL><LI>\"Check for ':' added.\" If an additional check for colons is necessary (as described in Note 727354), this is reported at this point and the characteristic is specified.</LI></UL></UL> <UL><UL><LI>\"Attribute was replaced, check for basic characteristic\". The authorization check does not distinguish between navigation attributes and \"normal\" characteristics. Here, it is specified that a navigation attribute is represented on its characteristic.</LI></UL></UL><p><br /><B>Second loop (indented twice)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the next inner loop, the system checks the relevant authorization objects individually one after the other. Start of the loop: &lt;Name of the authorization object &gt; (highlighted in orange), \"Authorization Object is Being Checked\". End of loop: A red or a green icon, the name of the authorization object again, then a short message stating if the check was successful or not. <UL><UL><LI>\"The user does not have authorizations for this authorization object\" This message indicates that the user does not have authorization for the authorization object that is being checked.</LI></UL></UL><UL><UL><LI>This message indicates that the user has some authorizations for the authorization object that is being checked, but that these authorizations are not sufficient. Then, there may be the checks of further authorization objects.</LI></UL></UL> <p><br /><B>Third loop (indented two more times)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Here, the individual authorizations for the authorization object that has just been checked are processed. Start of the loop: &lt;Name of the authorization&gt; (highlighted in orange), \"Authorization being checked\" The end of loop is not specifically marked but you can identify it by the beginning of the next run. <p><br /><B>Fourth loop (indented four more times)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The individual characteristics of an authorization object are processed here. Start of the loop: &lt;Name of the characteristic&gt; (highlighted in orange), no further text. In this loop, the system carries out the value comparison. Since this comparison can be very complex, it is handled in the next section. <p><br /><br /></p> <b>(F) The core of the check</b><br /> <p><br />Depending on the authorizations and the query selection, there may be different parts.</p> <OL>1. <B>\"Authorized values\"</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The system displays the list of the authorized flat values (single values and intervals) (always as an interval, with start and end value). <OL>2. <B>\"Checking the selection values\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If flat values are selected, they are listed here, first as actual values, then as SIDs. The SID assignment is specified in the /BIx/S&lt;name of characteristic&gt; table. The check result is displayed separately in red or green for each value or each interval. If no flat values are selected, this block does not exist. <OL>3. <B>\"Reading hierarchy authorizations\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The system must check the hierarchy authorizations only if not all parts of the flat selection could be authorized by flat authorizations, or if there is a hierarchy selection. <UL><UL><LI>Indented twice, the system specifies for which of the hierarchy authorization names in the profile (in the role) it has found a valid hierarchy authorizations (defined in RSSM). The system specifies whether it found valid hierarchy authorizations (defined in RSSM) in each case. The hierarchy authorization names are highlighted in orange.</LI></UL></UL> <OL>4. <B>\"Check of value selection against hierarchy authorizations\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(If there are no unauthorized value selections, this block does not exist). <UL><UL><LI>\"All values were selected, hierarchy authorization cannot be used.\"</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A hierarchy authorization cannot authorize a query selection of \"All values\". For this reason, the evaluation of the hierarchy authorizations terminates. One full (*) authorization or a colon (:) authorization would be required.</p> <UL><UL><LI>\"Determining authorized leaves\":</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Indented four times, there is a list of the hierarchy authorizations that have been taken into account. The name of the hierarchy authorization, the name of the hierarchy and the node name are highlighted in orange. Then, for each hierarchy authorization, the log lists which single values are authorized explicitly by this authorization. However, the system outputs a maximum of 100 values and only the assigned SIDs. Note that a hierarchy authorization may only reach up to a certain level. It may not authorize any node at all. Below this list, the system issues the check status of the individual values (and SIDs) of the selection. If the SID is authorized by one of the nodes, the check is green, otherwise it is red.</p> <OL>5. <B>\"Hierarchy selection check\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The hierarchy nodes in the selection are listed one after the other. For each node, there are the following specifications: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Hierarchy node\", NODESID, NODELEVEL and RETURNLEVEL (for a description, refer to section C). The node-specific check result is displayed by green or red traffic lights. <OL>6. <B>\"Result of all checks for the characteristic\" </B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A green or red traffic light is used to display the overall result of this check for flat and hierarchy selections. (In this authorization, in this authorization object and in this SUB number.) <p><br /><br /></p> <b>(G) The block for hierarchy authorizations (highlighted in green)</b><br /> <p><br />Caution: This block is created by the module for determining the authorized nodes of a hierarchy. Depending on the process of a query, the module may be called at different positions. For this reason, the block is marked with green start and end indicators.<br />\"Processing Hierarchy Authorization(s) --- Start of block\" and<br />\"Processing Hierarchy Authorization(s) --- End of block\".<br />The log may appear a little unclear due to this. Once the insertion is completed, the log continues directly with the item that was processed before.<br /><B>Caution:</B> In the same way, the call of the block \"Filling the buffers\" (see section B) may also only be triggered by this module. Check the marker at the end of the block to identify which block has been processed in which.<br />This display is a result of the log creation: The entries are created at the same time as the system processes the corresponding source code.<br /><br />As of Support Package 27 for BW 3.0 (Support Package 21 BW3.1; Support Package 13 BW3.5), the position of the call of the module is specified (highlighted in green) before the block start line. In this case, the following is displayed:</p> <UL><LI>\"Preparing hierarchy for F4 input help\" Self-explanatory.</LI></UL> <UL><LI>\"Hierarchy thinned out according to hierarchy authorizations\":&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you execute a query, the system creates a runtime object for each hierarchy that is used. When this happens, the system always reduces the hierarchy to the authorized subarea. This process is called \"thinning out\". Often, this hierarchy object is generated very early during the query execution and as a result, the authorization check must be carried out very early as well.</LI></UL> <UL><LI>\"Node variable of type 6 ('Filled from authorization') is filled.\" Self-explanatory.</LI></UL> <p><br />Within the block, the system first checks the S_RS_HIER RS authorization object. For more information, see also section D.<br /><br /><br /></p> <OL>1. \"It follows: <B>Authorized subareas of hierarchies\"</B>:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;First of all, the following are specified (highlighted in orange): The name of the hierarchy-bearing characteristic, the name of the hierarchy, the version of the hierarchy and the used key date. <OL>2. <B>\"Nodes and leaves\":</B> this specifies:</OL> <UL><UL><LI>\"Level\": The hierarchy level of the authorized node.</LI></UL></UL> <UL><UL><LI>SID: The SID of the authorized node. For the determination of the node name, see the K table of the characteristic (/BIx/K&lt;name of characteristic&gt;).</LI></UL></UL> <UL><UL><LI>\"from\", \"to\": Again the level of the authorized node, then the (absolute) depth of the hierarchy authorization.</LI></UL></UL> <OL>3. <B>\"Hierarchy authorizations\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Here, the log lists the details of the relevant hierarchy authorizations. Node, AUTHTYPE, COMPMODE and LEVEL correspond directly to the values specified in the hierarchy authorization definition (RSSM). The first field \"Node characteristic\" means that the node is the following: <UL><UL><LI>0HIER_NODE: a text node</LI></UL></UL> <UL><UL><LI>&lt;empty&gt;: a leaf</LI></UL></UL> <UL><UL><LI>&lt;name of the hierarchy-bearing characteristic&gt;: a chargeable node</LI></UL></UL> <UL><UL><LI>&lt;name of another characteristic&gt;: text node, but the name of another characteristic</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Caution: The differentiation between a leaf and a chargeable node with the same name may be important since this difference is not visible in Transaction RSSM. <p><br /><br /></p> <b>(H) End of the log</b><br /> <OL>1. <B>\"Compatibility settings\"</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This section displays the settings explained in Note 728077 regarding referenced characteristics. Both settings should be switched off. Then, the setting of the buffer for variables and hierarchy nodes is displayed. <OL>2. <B>\"Overall result of authorization check\":</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the system issues a message highlighted in red saying \"Some checks were not successful\", the check <B>of at least one</B> SUB number has failed. The query reports BRAIN 804. There may be other SUB numbers in the query, but in most cases the system does not display any data. <p><br /><br /><br /><br /><br /><br /></p> <b></b><br /> <p><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET-AUT (Authorizations)"}, {"Key": "Responsible                                                                                         ", "Value": "D031457"}, {"Key": "Processor                                                                                           ", "Value": "I026156"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000790323/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000790323/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000790323/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000790323/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000790323/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000790323/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000790323/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000790323/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000790323/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "976340", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "\"No authorization\" BRAIN 804 with customer exit variables", "RefUrl": "/notes/976340"}, {"RefNumber": "943139", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Settings in the authorization log", "RefUrl": "/notes/943139"}, {"RefNumber": "921820", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Information about authorization concept of BW 3.X systems", "RefUrl": "/notes/921820"}, {"RefNumber": "874786", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Query execution: No check for S_RS_MPRO", "RefUrl": "/notes/874786"}, {"RefNumber": "873685", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Query authorized although an authorization object missing", "RefUrl": "/notes/873685"}, {"RefNumber": "860488", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check for selections with Excluding", "RefUrl": "/notes/860488"}, {"RefNumber": "853297", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 804 \"No authorization\" for postable hierarchy nodes", "RefUrl": "/notes/853297"}, {"RefNumber": "846839", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Types of Authorizations in BW", "RefUrl": "/notes/846839"}, {"RefNumber": "824500", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSSM: Generation of authorizations fails", "RefUrl": "/notes/824500"}, {"RefNumber": "821773", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 804 \"No authorization\" with blank selection", "RefUrl": "/notes/821773"}, {"RefNumber": "805855", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Check on colon authorization despite selection condition", "RefUrl": "/notes/805855"}, {"RefNumber": "789536", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Enhancements of the authorization log", "RefUrl": "/notes/789536"}, {"RefNumber": "728077", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Compatibility modes in reporting authorization (only BW3.x)", "RefUrl": "/notes/728077"}, {"RefNumber": "727354", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Colon authorization during query execution", "RefUrl": "/notes/727354"}, {"RefNumber": "591758", "RefComponent": "FIN-SEM-BCS", "RefTitle": "Authorization checks for SEM-BCS functions", "RefUrl": "/notes/591758"}, {"RefNumber": "573725", "RefComponent": "BW-BEX-ET-ODOC", "RefTitle": "Authorizations for documents for transaction data", "RefUrl": "/notes/573725"}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}, {"RefNumber": "727354", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Colon authorization during query execution", "RefUrl": "/notes/727354 "}, {"RefNumber": "824500", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSSM: Generation of authorizations fails", "RefUrl": "/notes/824500 "}, {"RefNumber": "921820", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Information about authorization concept of BW 3.X systems", "RefUrl": "/notes/921820 "}, {"RefNumber": "728077", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Compatibility modes in reporting authorization (only BW3.x)", "RefUrl": "/notes/728077 "}, {"RefNumber": "874786", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Query execution: No check for S_RS_MPRO", "RefUrl": "/notes/874786 "}, {"RefNumber": "591758", "RefComponent": "FIN-SEM-BCS", "RefTitle": "Authorization checks for SEM-BCS functions", "RefUrl": "/notes/591758 "}, {"RefNumber": "846839", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Types of Authorizations in BW", "RefUrl": "/notes/846839 "}, {"RefNumber": "976340", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "\"No authorization\" BRAIN 804 with customer exit variables", "RefUrl": "/notes/976340 "}, {"RefNumber": "943139", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Settings in the authorization log", "RefUrl": "/notes/943139 "}, {"RefNumber": "573725", "RefComponent": "BW-BEX-ET-ODOC", "RefTitle": "Authorizations for documents for transaction data", "RefUrl": "/notes/573725 "}, {"RefNumber": "873685", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Query authorized although an authorization object missing", "RefUrl": "/notes/873685 "}, {"RefNumber": "853297", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 804 \"No authorization\" for postable hierarchy nodes", "RefUrl": "/notes/853297 "}, {"RefNumber": "860488", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check for selections with Excluding", "RefUrl": "/notes/860488 "}, {"RefNumber": "821773", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 804 \"No authorization\" with blank selection", "RefUrl": "/notes/821773 "}, {"RefNumber": "805855", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Check on colon authorization despite selection condition", "RefUrl": "/notes/805855 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "30B", "To": "30B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B34INVCBWTECH", "URL": "/supportpackage/SAPK-30B34INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B27", "URL": "/supportpackage/SAPKW30B27"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31021", "URL": "/supportpackage/SAPKW31021"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31021", "URL": "/supportpackage/SAPKW31021"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35013", "URL": "/supportpackage/SAPKW35013"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}