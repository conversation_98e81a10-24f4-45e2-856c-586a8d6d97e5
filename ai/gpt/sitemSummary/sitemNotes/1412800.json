{"Request": {"Number": "1412800", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 290, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016934912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001412800?language=E&token=D01F68D7E3B65D8B96DA04F01013AC23"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001412800", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001412800/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1412800"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.04.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-MDX"}, "SAPComponentKeyText": {"_label": "Component", "value": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP", "value": "BW-BEX-OT-MDX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-MDX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1412800 - MDX and BW authorizations"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You notice different behavior when a use with SAP_ALL rights or a restricted user logs on to the BW system. <br /><br />There are authorization problems the MDX interface is used for access. &#x00A0;<br /><br />In an SAP BW Support message, you are asked for an analysis of the various authorizations and the recording of traces. </p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Analysis authorizations, RSECADMIN, ST01, S_RS_AUTH, S_RFC, RSSM, EYE007, EYE 007, BRAIN804, BRAIN 804</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The BW system uses basic authorizations and has a user-defined authorization concept that is implemented on a technically independent basis.  The BW system recognizes two authorization concepts:  Standard authorizations and analysis authorizations. <br /><br />It is not clear which traces for which authorizations are relevant for which problem.  It is also not clear how the traces are used. </p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>Most problems can be solved with a precise analysis of the correct trace.  </strong><br /><br />The various authorizations that can have an influence are listed below and which trace helps for the analysis of authorization problems is mentioned briefly. <br />The actual traces are addressed lastly in detail. </p>\r\n<p><strong><span style=\"text-decoration: underline;\">A. Authorization concept in SAP BW system</span></strong><br /> <strong>1) Basic authorizations</strong></p>\r\n<p><br />Various authorization objects are checked when logging on to the BW system.  Messages about missing authorizations are not passed on completely and can often only be determined via the system trace in transaction ST01. <br />Note 23342 contains examples for error messages at this point. <br /><br />For example: The MDX BAPIs are called via Remote Function Call (RFC).  As a result, the authorization object S_RFC is checked for various function groups.  If one of the called function groups, such as RSOB (for example) was not authorized, the MDX interface of the BW system cannot be called by the external user. <br /><br />An overview or a description of the various authorization objects can be found in transaction SU21. <br /><br />Trace:  System trace in transaction ST01</p>\r\n<p><strong>2) BW standard authorizations</strong></p>\r\n<p><br />The BW standard authorizations are usually configured to protect functions such as \"Change/Display\" or entire objects such as \"Queries/InfoProvider/DataSources...\". <br /><br />The BW standard authorizations are technically implemented as basic authorizations.  For this reason, an authorization problem is also to be analyzed here only via the ST01 system trace.  You can find an overview of the BW standard authorization objects in transaction SU21 in the object class RS. <br /><br />For example: You can use the BW-specific authorization object S_RS_COMP to determine whether a user has access to a specific query or a specific InfoProvider.  If a user only has authorization for queries that begin with ZSAP*, no queries that begin with another name ID, such as ZBO* (for example) are offered via the BAPI BAPI_MDPROVIDER_GET_CUBES. <br /><br />Trace: System trace in transaction ST01<br /><br />Special feature: In the ST01 authorization trace, an incorrect check occurs on the authorization object S_RS_AUTH. This indicates a problem with the analysis authorizations and you only find the exact cause via the RSECADMIN authorization log. </p>\r\n<p><strong>3) BW 7.x analysis authorizations</strong></p>\r\n<p><br />You can configure the BW analysis authorizations to restrict access to the data in the InfoProvider.  As a result, the same query object can return different data for two users with different analysis authorizations. <br />The analysis authorizations are managed using transaction RSECADMIN.  This transaction also provides the RSECADMIN authorization log for the analysis of problems with the analysis authorizations. <br /><br />For example: A user accesses a query that uses a variable that is filled by authorizations.  This user sees a smaller and more restricted dataset (for example, only one country or one division) than a user that has the SAP_ALL profile. <br /><br />Trace:  RSECADMIN - authorization log <br />Simulation: Transaction RSUDO provides the option to simulate the execution of MDX statements and queries with a restricted user and to record this log. </p>\r\n<p><strong>4) Old BW 3.2 reporting authorizations (obsolete as of Release 7.0) </strong></p>\r\n<p><br />Releases that are earlier than BW 7.0 contain the BW reporting authorizations to restrict access to the data.  These were technically configured as RSR authorization objects.  The maintenance usually occurred in transaction RSSM.  In transaction RSCUSTV23, you can check whether this old authorization concept is still active in your 7.X system; it is no longer supported (Note:  1125108 and 923176).<br />An overview with tips and tricks for this RSR authorization concept can be found in SAP Note: 921820.<br /><br />Example: A user accesses a query that uses a variable that is filled by authorizations.  This user sees a smaller and more restricted dataset (for example, only one country or one division) than a user that has the SAP_ALL profile. <br /><br />Traces:  RSSMQ authorization log <br /><br /></p>\r\n<p><strong><span style=\"text-decoration: underline;\">B. Authorization traces in SAP BW system</span></strong><br /> <strong>1) ST01 - Trace</strong></p>\r\n<p># Call transaction ST01. <br /># Select the \"Authorization check\" checkbox.<br /># Use the \"General Filters\" button to restrict access to users that are connected with the BW system. <br /># Activate the trace by choosing \"Trace on\". <br /># Now execute the report in the non-BW system. <br /># After the error message is displayed, deactivate the user again in transaction ST01 by choosing \"Trace off\". <br /># Use the \"Analysis\" button to navigate to the recorded records for your user. These can also be exported to Microsoft Excel or as a text file. <br /><br />For more information, see Note 1359226.<br /><br />Provide the time, the restricted user, and (if necessary) the exported result of the trace in the message for BW Support. </p>\r\n<p><strong>2) RSECADMIN - authorization log </strong></p>\r\n<p><br />Note 1234567 contains a very detailed description of the operation, the setup, and the analysis of the authorization log. <br /><br />Provide the log number, the restricted user, and (if necessary) the exported result of the trace in the message for BW Support. <br /><br /></p>\r\n<p><strong>3) RSSM - authorization log </strong></p>\r\n<p><br />Note 790323 contains a very detailed description of the operation, the setup, and the analysis of the reporting authorization log. <br /><br />Provide the log number, the restricted user, and (if necessary) the exported result of the trace in the message for BW Support. </p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BI-BIP-IK (Integration; Integration kits for SAP, Baan, PeopleSoft, Sie)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I026448)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I061830)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001412800/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001412800/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "923176", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Support situation authorization management BI70/NW2004s", "RefUrl": "/notes/923176"}, {"RefNumber": "921820", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Information about authorization concept of BW 3.X systems", "RefUrl": "/notes/921820"}, {"RefNumber": "838800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Composite SAP Note for consulting notes about MDX/OLAP BAPIs", "RefUrl": "/notes/838800"}, {"RefNumber": "790323", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The log for reporting authorizations in BW", "RefUrl": "/notes/790323"}, {"RefNumber": "23342", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "No authorization ... --> analysis (also for external users)", "RefUrl": "/notes/23342"}, {"RefNumber": "1359226", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Tracing of authority checks", "RefUrl": "/notes/1359226"}, {"RefNumber": "1272044", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Analyzing MDX problems", "RefUrl": "/notes/1272044"}, {"RefNumber": "1234567", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The authorization log RSECADMIN", "RefUrl": "/notes/1234567"}, {"RefNumber": "1125108", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Use of obsolete 3.x authorizations in BW 7.x", "RefUrl": "/notes/1125108"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2632620", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "BRAINOLAPAPI 012: Unknown error when executing MDX", "RefUrl": "/notes/2632620 "}, {"RefNumber": "838800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Composite SAP Note for consulting notes about MDX/OLAP BAPIs", "RefUrl": "/notes/838800 "}, {"RefNumber": "1272044", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Analyzing MDX problems", "RefUrl": "/notes/1272044 "}, {"RefNumber": "1125108", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Use of obsolete 3.x authorizations in BW 7.x", "RefUrl": "/notes/1125108 "}, {"RefNumber": "1359226", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Tracing of authority checks", "RefUrl": "/notes/1359226 "}, {"RefNumber": "923176", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Support situation authorization management BI70/NW2004s", "RefUrl": "/notes/923176 "}, {"RefNumber": "1234567", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The authorization log RSECADMIN", "RefUrl": "/notes/1234567 "}, {"RefNumber": "921820", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Information about authorization concept of BW 3.X systems", "RefUrl": "/notes/921820 "}, {"RefNumber": "23342", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "No authorization ... --> analysis (also for external users)", "RefUrl": "/notes/23342 "}, {"RefNumber": "790323", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The log for reporting authorizations in BW", "RefUrl": "/notes/790323 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}