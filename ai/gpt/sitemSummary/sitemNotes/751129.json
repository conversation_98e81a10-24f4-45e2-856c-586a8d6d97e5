{"Request": {"Number": "751129", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 208, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015691822017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000751129?language=E&token=1B45FEB6332876BBB1E6C39127E91AAA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000751129", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000751129/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "751129"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.09.2017"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-PO-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Userinterface - Purchase Orders"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchase Orders", "value": "MM-PUR-PO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-PO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Userinterface - Purchase Orders", "value": "MM-PUR-PO-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-PO-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "751129 - Authorizations in 'new' single screen Transactions in Purchasing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have setup authorizations for the 'new' Purchasing single screen (Enjoy) transactions the same way as for the 'old' Purchasing transactions.<br />But for the 'new' transactions, it doesn't work as intended.</p>\r\n<ul>\r\n<li>E.g. You have NOT assigned transaction ME21N to a user. Still, he can create Purchase Orders.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ME21, ME22, ME23, ME51, ME52, ME53, ME54, ME21N, ME22N, ME23N, ME51N, ME52N, ME53N, ME54N<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The 'new' Purchasing single screen transactions are designed to display, change or create many documents without leaving the transaction. ME21N, ME22N and ME23N actually refer to the same program.<br /><br />Therefore the transaction authorization is less important than in the old design.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The following authorization objects are checked in the 'new'&#160;Purchase order transactions:<br />- M_BEST_BSA (document type in PO)<br />- M_BEST_EKG (purchasing group in PO)<br />- M_BEST_EKO (purchasing organization in PO)<br />- M_BEST_WRK (plant in PO)<br /><br />The distinction between display, change and create is controlled by the <strong>activity codes</strong> rather than the <strong>transaction codes</strong>.<br /><br />So, any user who is supposed to use ANY of the&#160;Purchasing single screen transactions, should be assigned to ALL of them. Both ME21N, ME22N and ME23N.<br /><br />The permitted activities for the authorization objects can be seen in transaction SU21 for Object Class MM_E, <br />but for most objects, the activities are:<br />&#160;&#160;01 Create or generate<br />&#160;&#160;02 Change<br />&#160;&#160;03 Display<br />&#160;&#160;08 Display change documents<br />&#160;&#160;09 Display prices<br /><br />(Please note that activity 06; Delete, is NOT checked! See comment below)<br /><br />The objects and their related activities can also be seen directly via transaction SE16; table TACTZ.<br /><br /><strong>Example:</strong><br />A user must be allowed to change and display, but not to create Purchase Orders.<br /><br />He must then:</p>\r\n<ul>\r\n<li>be assigned to the transactions ME21N, ME22N and ME23N.</li>\r\n</ul>\r\n<ul>\r\n<li>be assigned to a role that is assigned to M_BEST_BSA, M_BEST_EKG, M_BEST_EKO and/or M_BEST_WRK.</li>\r\n</ul>\r\n<p>For these, activity 02, 03, 08, 09 may be assigned, but NOT activity 01.<br /><br /><strong>Purchase Requisitions:</strong><br />For requisitions the principle is exactly the same.<br />Only the authorization objects are M_BANF_BSA, M_BANF_EKG, M_BANF_EKO and M_BANF_WRK.<br /><br /><strong>About Activity 06, Delete:</strong><br />Activity 06 (Delete) is NOT checked in the 'new' single screen&#160;transactions.<br />Delete is by default always permitted if you have Activity 02; change, set.<br />If you need to block users from deleting items in a Purchasing document it is NOT possible by NOT using Activity 06 in your profiles.<br />SAP Provides extension MEREQ001 where additional checks can be done. When a persistent item gets deleted, a check can be performed and an error message created. This way the user will be unable to save such a requisition.<br /><br /><strong>Other Authorization objects:</strong><br />As of release 470 Enterprise you have the option of taking advantage of the BAdIs ME_PROCESS_REQ_CUST and ME_PROCESS_PO_CUST to check additional authorization objects.<br /><br />Note that using the BAdIs require knowledge about object oriented programming.<br />If you need assistance to set this up, it will be charged as consulting.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-PUR-REQ-GUI (Userinterface - Purchase Requisitions)"}, {"Key": "Other Components", "Value": "MM-PUR-PO (Purchase Orders)"}, {"Key": "Other Components", "Value": "MM-PUR-REQ (Purchase Requisitions)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D041269)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025793)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000751129/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000751129/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "668212", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N: Document type authorization when holding a PO", "RefUrl": "/notes/668212"}, {"RefNumber": "491789", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "FAQ: Purchase order (ME21N, ME22N, ME23N)", "RefUrl": "/notes/491789"}, {"RefNumber": "387978", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME22N/ME23N: Display < > change activity category", "RefUrl": "/notes/387978"}, {"RefNumber": "336010", "RefComponent": "MM-PUR-PO", "RefTitle": "ME21: No authorization in contract reference", "RefUrl": "/notes/336010"}, {"RefNumber": "321929", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME23N: Authorization price display status (header)", "RefUrl": "/notes/321929"}, {"RefNumber": "304827", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME22N/ME23N: missing value for authorization check", "RefUrl": "/notes/304827"}, {"RefNumber": "212447", "RefComponent": "MM-PUR-PO", "RefTitle": "ME22N: Activity category", "RefUrl": "/notes/212447"}, {"RefNumber": "1276099", "RefComponent": "IS-AFS-MM-PUR", "RefTitle": "AFS PO:No authorization check for pricing for schedule lines", "RefUrl": "/notes/1276099"}, {"RefNumber": "1050832", "RefComponent": "GRC-SAC-ARA", "RefTitle": "ME23N in Compliance Calibrator (RAR) Default rules", "RefUrl": "/notes/1050832"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2203200", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "Authorization objects checked/not checked in ME21N/ME51N", "RefUrl": "/notes/2203200 "}, {"RefNumber": "2683407", "RefComponent": "MM-PUR-REQ", "RefTitle": "Restrict purchase requisition creation by storage location", "RefUrl": "/notes/2683407 "}, {"RefNumber": "2613671", "RefComponent": "MM-PUR-GF-STO", "RefTitle": "Restrict supplying plant for STO in ME21N", "RefUrl": "/notes/2613671 "}, {"RefNumber": "668212", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N: Document type authorization when holding a PO", "RefUrl": "/notes/668212 "}, {"RefNumber": "1050832", "RefComponent": "GRC-SAC-ARA", "RefTitle": "ME23N in Compliance Calibrator (RAR) Default rules", "RefUrl": "/notes/1050832 "}, {"RefNumber": "1276099", "RefComponent": "IS-AFS-MM-PUR", "RefTitle": "AFS PO:No authorization check for pricing for schedule lines", "RefUrl": "/notes/1276099 "}, {"RefNumber": "491789", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "FAQ: Purchase order (ME21N, ME22N, ME23N)", "RefUrl": "/notes/491789 "}, {"RefNumber": "336010", "RefComponent": "MM-PUR-PO", "RefTitle": "ME21: No authorization in contract reference", "RefUrl": "/notes/336010 "}, {"RefNumber": "212447", "RefComponent": "MM-PUR-PO", "RefTitle": "ME22N: Activity category", "RefUrl": "/notes/212447 "}, {"RefNumber": "387978", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME22N/ME23N: Display < > change activity category", "RefUrl": "/notes/387978 "}, {"RefNumber": "321929", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME23N: Authorization price display status (header)", "RefUrl": "/notes/321929 "}, {"RefNumber": "304827", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME22N/ME23N: missing value for authorization check", "RefUrl": "/notes/304827 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}