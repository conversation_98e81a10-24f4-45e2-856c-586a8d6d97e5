{"Request": {"Number": "1154235", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 360, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006954622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001154235?language=E&token=DF8E85DCCE89598787FFA1638FE87B54"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001154235", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1154235"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1154235 - RAIS: Employees without remuneration in the year."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The RAIS report (HBRRAIS0) is reporting the records even when the employees have the remuneration field from January to December equals to zero.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RAIS, HBRRAIS0, remuneration field</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The RAIS analyzer software does not allow employees without remuneration in the year. It gives an error message when this situation occurs.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The RAIS report is now validating all the remuneration fields and rejecting the employees without remuneration in the year. When this occours, an warning message is appended to the output messages.<br /><br />An Advanced Delivery is available in the attached files according to the following list (\"xxxxxx\" means numbers):<br /><br />L7DKxxxxxx_600.CAR - Release 600 (ERP 2005)<br />L6DKxxxxxx_500.CAR - Release 500 (ERP 2004)<br />L6BKxxxxxx_470.CAR - Release 4.70 (Enterprise)<br />L9CKxxxxxx_46C.CAR - Release 4.6C<br />L9BKxxxxxx_46B.CAR - Release 4.6B<br /><br />For more details about Advance Delivery installation procedure please read the notes listed in \"Related Notes\".<br /><br />Important: Be aware of an Advance Delivery delivers the last version of the object. It means that if you do not have the last HR Support Package installed in your system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br />The correction described in this note will be included in an HR Support Package. The support package includes the following:<br /><br />-&#x00A0;&#x00A0;Changes in the report HBRRAIS0:<br />- Include HBRRAIS0 has been changed.<br />- Include PCRAIBR0 has been changed.<br /><br />-&#x00A0;&#x00A0;Message 223 in message class HRPAYBR99 has been created.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I812659)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I812659)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001154235/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L6DK088531_500.CAR", "FileSize": "31", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000048842008&iv_version=0001&iv_guid=72CB4C0CB61BD34793B5F48725B8538C"}, {"FileName": "L9CK243941_46C.CAR", "FileSize": "31", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000048842008&iv_version=0001&iv_guid=2C8882D99992EF4C80F33AE5591877DB"}, {"FileName": "L7DK076430_600.CAR", "FileSize": "32", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000048842008&iv_version=0001&iv_guid=29CB997F62EFC0419E42D5638208BDFE"}, {"FileName": "L9BK139295_46B.CAR", "FileSize": "27", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000048842008&iv_version=0001&iv_guid=DDABD48BF7FA2F4A8D58D6201388C951"}, {"FileName": "L6BK158000_470.CAR", "FileSize": "31", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000048842008&iv_version=0001&iv_guid=D022EE68CC2FFC4B98D001B3325A0FEA"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1449329", "RefComponent": "PY-BR", "RefTitle": "RAIS: wrong selection of employees", "RefUrl": "/notes/1449329"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1124215", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 Legal Changes 2008", "RefUrl": "/notes/1124215"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1449329", "RefComponent": "PY-BR", "RefTitle": "RAIS: wrong selection of employees", "RefUrl": "/notes/1449329 "}, {"RefNumber": "1124215", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 Legal Changes 2008", "RefUrl": "/notes/1124215 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BD5", "URL": "/supportpackage/SAPKE46BD5"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CD6", "URL": "/supportpackage/SAPKE46CD6"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47081", "URL": "/supportpackage/SAPKE47081"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50048", "URL": "/supportpackage/SAPKE50048"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60030", "URL": "/supportpackage/SAPKE60030"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 5, "URL": "/corrins/0001154235/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 18, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "203897 ", "URL": "/notes/203897 ", "Title": "HBRRAIS - Alterações diversas para emissão RAIS/99", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "327840 ", "URL": "/notes/327840 ", "Title": "HBRRAIS0 - Acertos gerais", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "384995 ", "URL": "/notes/384995 ", "Title": "HBRRAIS0 - Correções para tipo de admissão", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "816281 ", "URL": "/notes/816281 ", "Title": "RAIS: incorrect remuneration assignment", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "390938 ", "URL": "/notes/390938 ", "Title": "Neg. WT are displayed as if there were pos. ones", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "498135 ", "URL": "/notes/498135 ", "Title": "HBRRAIS: Missing init for AVISO Previo", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "500161 ", "URL": "/notes/500161 ", "Title": "hbrrais: SALARY under some circumstances zero, CBO not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "821726 ", "URL": "/notes/821726 ", "Title": "HBRRAIS0 does not read 13DI offcycle in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "1035464 ", "URL": "/notes/1035464 ", "Title": "HBRRAIS0 - The last three absences with bigger duration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "1091850 ", "URL": "/notes/1091850 ", "Title": "HBRRAIS0: wrong values of membership fees.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "390938 ", "URL": "/notes/390938 ", "Title": "Neg. WT are displayed as if there were pos. ones", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "498135 ", "URL": "/notes/498135 ", "Title": "HBRRAIS: Missing init for AVISO Previo", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "500161 ", "URL": "/notes/500161 ", "Title": "hbrrais: SALARY under some circumstances zero, CBO not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "821726 ", "URL": "/notes/821726 ", "Title": "HBRRAIS0 does not read 13DI offcycle in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1035464 ", "URL": "/notes/1035464 ", "Title": "HBRRAIS0 - The last three absences with bigger duration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1036864 ", "URL": "/notes/1036864 ", "Title": "HBRRAIS0: Off-cycle AJUS is not considered to remuneration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1037831 ", "URL": "/notes/1037831 ", "Title": "RAIS - Customizing field \"Bonus\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1040911 ", "URL": "/notes/1040911 ", "Title": "HBRRAIS0 - 13th Salary advanced for the next year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1091850 ", "URL": "/notes/1091850 ", "Title": "HBRRAIS0: wrong values of membership fees.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1151162 ", "URL": "/notes/1151162 ", "Title": "HBRRAIS0: contribution values correction.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1035464 ", "URL": "/notes/1035464 ", "Title": "HBRRAIS0 - The last three absences with bigger duration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1036864 ", "URL": "/notes/1036864 ", "Title": "HBRRAIS0: Off-cycle AJUS is not considered to remuneration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1037831 ", "URL": "/notes/1037831 ", "Title": "RAIS - Customizing field \"Bonus\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1040911 ", "URL": "/notes/1040911 ", "Title": "HBRRAIS0 - 13th Salary advanced for the next year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1091850 ", "URL": "/notes/1091850 ", "Title": "HBRRAIS0: wrong values of membership fees.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1151162 ", "URL": "/notes/1151162 ", "Title": "HBRRAIS0: contribution values correction.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "821726 ", "URL": "/notes/821726 ", "Title": "HBRRAIS0 does not read 13DI offcycle in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1035464 ", "URL": "/notes/1035464 ", "Title": "HBRRAIS0 - The last three absences with bigger duration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1036864 ", "URL": "/notes/1036864 ", "Title": "HBRRAIS0: Off-cycle AJUS is not considered to remuneration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1037831 ", "URL": "/notes/1037831 ", "Title": "RAIS - Customizing field \"Bonus\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1040911 ", "URL": "/notes/1040911 ", "Title": "HBRRAIS0 - 13th Salary advanced for the next year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1091850 ", "URL": "/notes/1091850 ", "Title": "HBRRAIS0: wrong values of membership fees.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1151162 ", "URL": "/notes/1151162 ", "Title": "HBRRAIS0: contribution values correction.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1035464 ", "URL": "/notes/1035464 ", "Title": "HBRRAIS0 - The last three absences with bigger duration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1036864 ", "URL": "/notes/1036864 ", "Title": "HBRRAIS0: Off-cycle AJUS is not considered to remuneration", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1037831 ", "URL": "/notes/1037831 ", "Title": "RAIS - Customizing field \"Bonus\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1040911 ", "URL": "/notes/1040911 ", "Title": "HBRRAIS0 - 13th Salary advanced for the next year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1091850 ", "URL": "/notes/1091850 ", "Title": "HBRRAIS0: wrong values of membership fees.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1151162 ", "URL": "/notes/1151162 ", "Title": "HBRRAIS0: contribution values correction.", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}