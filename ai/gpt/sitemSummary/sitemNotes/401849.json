{"Request": {"Number": "401849", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 344, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015006802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000401849?language=E&token=E53091E03CF6D3F18E6E08C447F27C13"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000401849", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000401849/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "401849"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.09.2002"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "401849 - DB6: Enhancements to upgrade to SAP Web AS 6.10"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Problems in the upgrade documentation or in the upgrade process</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Update release upgrade maintenance level DB2 UDB DB2UDB DB6 productive operation 6.1 7.1</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Topics:</b><br /> <b>=======</b><br /> <p>I/  Deactivating the database before a backup<br />II/ ,Windows NT and Windows 2000 only:<br />  New environment variable for users &lt;sapsid&gt;adm<br />III/ Deadlocks in phase PARCONV_UPG<br />IV/ ,Upgrading a system with a schema name that is not 'SAPR3'<br />V/  Specific postprocessing for DB2 UDB for UNIX and<br />  Windows: SDDB6INS<br />VI/  For source releases up to and including 4.5B:<br />  UNIX only: db2iupdt after changing over to a new group concept<br /><br /></p> <b>Chronological summary</b><br /> <b>===================================================</b><br /> <p>Date   Topic  Type of entry<br />--------------------------------------<br />06/26/2001  I/  ,new<br />06/26/2001  II/  new<br />07/10/2001  III/ ,new<br />07/25/2001  IV/  new<br />07/26/2001  V/  ,new<br />08/30/2001  II/  changed<br />12/05/2001  IV/  changed<br />12/05/2001  V/  ,changed<br />12/11/2001  IV/  changed<br />09/09/2002  VI/  new<br /><br /><STRONG>I/ Deactivating the database before a backup</STRONG><br /><br />-----------------------&lt; d021078 &gt;-----------------------<br /><br />After the kernel exchange the database is explicitly activated (db2 activate database &lt;SAPSID&gt;) by the SAP tools to start the SAP system (startsap ...) after the Database Manager has been started (db2start).<br />If you want to carry out an off-line backup of the database towards the end of the upgrade, you have to deactivate the database before the backup or restart the database manager (db2stop/db2start).<br /><br /><br /><STRONG>II/ Windows NT and Windows 2000 only:</STRONG><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;New environment variable for users &lt;sapsid&gt;adm</STRONG><br /><br />-----------------------&lt; d021078 &gt;-----------------------<br /><br />As of Release 6.10, the &lt;sapsid&gt;adm user requires the SAPSYSTEMNAME environment variable in its environment on Windows NT and Windows 2000.<br />Create this new variable for Windows NT via<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Start -&gt; Settings -&gt; Control panel -&gt; System -&gt; Environment -&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;User Variable for &lt;sapsid&gt;adm<br />and for Windows 2000 via<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Start -&gt; Settings -&gt; Control panel -&gt; System -&gt; Advanced -&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Environment Variables -&gt; &lt;sapsdi&gt;adm: NEW<br />and set their value on the names of the SAP system. The variable becomes active when the system is next started or restarted.<br /><br /><br /><STRONG>III/ Deadlocks in phase PARCONV_UPG</STRONG><br /><br />------------------------&lt; d021078 &gt;-----------------------<br /><br />Deadlocks (SQL0911) can occur in the phase PARCONV_UPG.You can eliminate the errors by repeating the phase.<br /><br /><br /><STRONG>IV/ Upgrading a system with a schema name that is not 'sapr3'</STRONG><br /><br />-----------------------&lt; d021078 &gt;-----------------------<br /><br />If you want to run an upgrade for a system whose schema name (environment variable dbs_db6_schema) is not 'sapr3', some actions are required after PREPARE at the latest. These actions are described in Note 455323.<br />The PREPARE run is not affected by these actions.<br /><br /><br /><STRONG>V/ Specific postprocessing for DB2 UDB for UNIX and Windows:</STRONG><br /><STRONG>&#x00A0;&#x00A0; Windows: SDDB6INS</STRONG><br /><br />-----------------------&lt; d021078 &gt;-----------------------<br /><br />The program SDDB6INS only delivers its output on the screen and not into a file.<br />If your system is running under UNIX, call the program SDDB6INS as follows (the specification in the documentation is not <B>correct):</B><br />\"&lt;kernel CD&gt;/UNIX//DBTOOLS/SDDB6INS -&amp; &lt;Source release of the kernel&gt; | tee &lt;upgrade directory&gt;/log/SDDB6INS.out\"<br /><br />The output is then also logged in the file SDDBINS.out.<br /><br />You must ignore the following errors which are reported by SDDB6INS:<br />1.<br />[E] file /usr/sap/&lt;SAPSID&gt;/SYS/golbal has wrong permissions<br />drwx--x--- (0710), should be drwxr----- (0740)<br />-&gt; Check/change file permissions<br />[E] Verify /usr/sap/&lt;SAPSID&gt;/SYS/global [WITH ERROR(S)]<br />2.<br />[E] File /usr/sap/&lt;SAPSID&gt;/SYS/exe/run/dscdb6up has wrong permissions<br />-rwsrwx-w- (4772), should be -rwsrwxr-x (4775)<br />-&gt; Check/change file permissions<br />[E] Verify /usr/sap/&lt;SAPSID&gt;/SYS/exe/run/dscdb6up [WITH ERROR(S)]<br /><br />If your system runs under Windows, call program SDDB6INS as follows (the specification from the documentation is <B>not</B> correct): \" &lt;Kernel CD&gt;/UNIX/&lt;OS&gt;/DBTOOLS/SDDB6INS -u &lt;Kernel source release&gt;<BR/> -db2dbnamepwd &lt;Pwd of user db2&lt;db2name&gt; &gt; \"<br /><br />In connection with SDDB6INS also see Note 455506.<br /><br />VI/&#x00A0;&#x00A0;&#x00A0;&#x00A0; For source releases up to and including 4.5B:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;UNIX only: db2iupdt after changing over to a new group concept<br /><br />----------------------&lt; d021078 &gt;--------------------------------------<br /><br />A db6-specific postprocessing is the 'changeover to the new group<br />concept'. After finishing this changeover, an instance update is<br />required. For this purpose, proceed as follows:<br /><br /> 1.) As user db2&lt;sapsid&gt; or &lt;sapsid&gt;adm, stop the database.<br /><br /> 2.) As user 'root', execute the following command: &lt;DB2 installation directory&gt;/instance/db2iupdt db2&lt;sapsid&gt;<br /><br /> 3.) As user db2&lt;sapsid&gt; or &lt;sapsid&gt;adm, restart the database.<br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D021078)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D021078)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000401849/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000401849/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "455506", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "DB6: Installing the Latest 6NN DB2 Admin Tools", "RefUrl": "/notes/455506"}, {"RefNumber": "455323", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/455323"}, {"RefNumber": "412677", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: CLI0157E with system start under W2K with DB2 V7", "RefUrl": "/notes/412677"}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717"}, {"RefNumber": "396690", "RefComponent": "CRM", "RefTitle": "SAP CRM 3.0: Information on Upgrade", "RefUrl": "/notes/396690"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "455506", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "DB6: Installing the Latest 6NN DB2 Admin Tools", "RefUrl": "/notes/455506 "}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717 "}, {"RefNumber": "412677", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: CLI0157E with system start under W2K with DB2 V7", "RefUrl": "/notes/412677 "}, {"RefNumber": "396690", "RefComponent": "CRM", "RefTitle": "SAP CRM 3.0: Information on Upgrade", "RefUrl": "/notes/396690 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "610", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}