{"Request": {"Number": "1127132", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 430, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006706632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001127132?language=E&token=88BB0D388FDAC23548D10B17DAFA491D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001127132", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001127132/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1127132"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1127132 - New fields and records to Annual Income Declaration report"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />The Annual Income Declaration report has to comply with a new legal change. From 01/01/2008 on, the records of the Anexo J / Modelo 10 should also display social security contributions and union deductions.<br /><br />Additional to that, the Annual Income Declaration report has to generate the following record types in the TemSe file: 001, 002, 003, 004, 005, 006, and 999.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />Annual Income Declaration, RPCAIDP0, Anexo J / Modelo 10, social security contribution, union deduction.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />In order to have this note correctly installed, the following notes (and their prerequisites) should have been installed before:</p>\r\n<ul>\r\n<li>811311 - HR-PT: Annual Income Declaration - Electronic file</li>\r\n</ul>\r\n<ul>\r\n<li>870565 - HR-PT: Output of report RPUTSVP0 has been improved</li>\r\n</ul>\r\n<ul>\r\n<li>928930 - HR-PT: RPCAIDP0 - Pre-retirement income category H</li>\r\n</ul>\r\n<ul>\r\n<li>1017738 - HT-PT: Legal Change Annual Income Declaration</li>\r\n</ul>\r\n<ul>\r\n<li>1043684 - HR-PT: RPCAIDP0 (Annual Income Declaration)</li>\r\n</ul>\r\n<ul>\r\n<li>1118129 - New layout for A&#231;ores - Personnel Summary</li>\r\n</ul>\r\n<p><br />For more details about installation procedures, read the note listed in \"Related Notes\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>7</td>\r\n<td>July 19, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />The record J02 from the Anexo J / Modelo 10 has now two more columns to display respectively SS contributions and Union deductions.<br /><br />The SS contribution is collected from contributions made for each employee in the report processing period. These contributions are retrieved from the Employee contribution wage types customized on the table T5P2P.<br /><br />The union deduction is collected from the wage types assigned in evaluation class 12 (Wage type class) with value 29 (Union dues). This process is similar to the one used for the report RPCIIDP0 (Individual Income Declaration).<br /><br />New records (001 to 006 and 999) were also created. The Portuguese Temse was adjusted to allow the download of the Annual Income Declaration using these extra records.<br /><br />The following Valid Properties for entities (Caracter&#237;sticas V&#225;lidas) were created on table T5PFV:<br /><br />------------------------------------------------------<br />| FIELD|FLENG|FTYPE|FTEXT&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />------------------------------------------------------<br />| NIFRL|&#160;&#160;&#160;&#160;9|CHAR |NIF Representante Legal&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />| PVOLN|&#160;&#160;&#160;&#160;3|NUMC |Percentagem Volume de Neg&#243;cios&#160;&#160; |<br />| TOCOD|&#160;&#160;&#160;&#160;4|NUMC |C&#243;digo Reparti&#231;&#227;o Finan&#231;as&#160;&#160;&#160;&#160;&#160;&#160; |<br />------------------------------------------------------<br /><br />These entries are used to customize values in the view V_T5PFD (Entidades Caracter&#237;sticas).<br /><br />The following entity properties have to be customized in the view V_T5PFD to allow the correct processing of the report:<br /><br />- CODTA: C&#243;digo da Tabela de Actividades<br />- MACTC: C&#243;digo de atividade principal<br />- NIFRL: NIF Representante Legal<br />- PVOLN: Percentagem Volume de Neg&#243;cios<br />- TAXNU: N&#186; Identifica&#231;&#227;o fiscal<br />- TOCOD: C&#243;digo Reparti&#231;&#227;o Finan&#231;as<br /><br />Note that the logical entities depend on the feature PENTT.<br /><br />An Advanced Delivery is available in the attached files according to the following list (\"xxxxxx\" means numbers):</p>\r\n<ul>\r\n<li>L7DKxxxxxx_600.CAR - Release 600 (ERP 2005)</li>\r\n</ul>\r\n<ul>\r\n<li>L6DKxxxxxx_500.CAR - Release 500 (ERP 2004)</li>\r\n</ul>\r\n<ul>\r\n<li>L6BKxxxxxx_470.CAR - Release 4.70(Enterprise)</li>\r\n</ul>\r\n<ul>\r\n<li>L9CKxxxxxx_46C.CAR - Release 4.6C</li>\r\n</ul>\r\n<p><br />For more details about Advance Delivery installation procedure, please read the notes listed in \"Related Notes\".<br /><br /><strong>IMPORTANT:</strong><br />Be aware that an Advance Delivery delivers the last version of the object. It means that if you do not have the last HR Support Package installed in your system you could get errors, either Syntax Errors or process errors. In this case, the only option is to undo the changes from the Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br />The solution described in this note will be included in an HR Support Package. The support package includes the following:<br /><br /><span style=\"text-decoration: underline;\">Changed Objects</span></p>\r\n<ul>\r\n<li>Function Module</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HR_PT_GET_COMPANY</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HR_PT_GET_ENTITY</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Report Source Code</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDP0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDPD</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDPF</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDPO</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDPS</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPUTSVP0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPUTSVPD</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPUTSVPF</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Report Texts</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDP0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPUTSVP0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Table (Structure)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTX2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTX3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Type Group</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT03</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT05</li>\r\n</ul>\r\n</ul>\r\n<p><br /><span style=\"text-decoration: underline;\">New Objects</span></p>\r\n<ul>\r\n<li>Data Element</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ACTC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD004</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD004</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD059</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD079</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD120</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD131</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD153</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD154</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD160</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD166</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADCAE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADCSF</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADCTA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADCUR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADDAP</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADDEC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADDTY</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADFTY</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADFVE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADIIT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADNIF</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADNR3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADNRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADPDA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADPVN</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADSSC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTSS</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTUD</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADUND</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADYEA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADYMD</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Table (Structure)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTS_AD001</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTS_AD002</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTS_AD003</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTS_AD004</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTS_AD005</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTS_AD006</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTS_AD999</li>\r\n</ul>\r\n</ul>\r\n<p><br /><span style=\"text-decoration: underline;\">New Customizing</span></p>\r\n<ul>\r\n<li>Table Contents</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Table T5PFT</li>\r\n</ul>\r\n</ul>\r\n<p><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; PCODTA<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; PNIFRL<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TOCOD</p>\r\n<ul>\r\n<ul>\r\n<li>Table T5PFV</li>\r\n</ul>\r\n</ul>\r\n<p><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; CODTA<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; NIFRL<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TOCOD<br /><br /></p>\r\n<p><strong>Additional Delivery:</strong></p>\r\n<p><br />&#160;&#160;For the releases 600 (ERP 2005),&#160;&#160;500 (ERP 2004) and 4.6C the valid properties NIFRL and PVOLN in the table T5PFV were missing at the first release of this note. For those systems be aware that you will have an additional advanced delivery to install:</p>\r\n<ul>\r\n<li>L7DKxxxxxx_600_AD.CAR - Release 600 (ERP 2005)</li>\r\n</ul>\r\n<ul>\r\n<li>L6DKxxxxxx_500_AD.CAR - Release 500 (ERP 2004)</li>\r\n</ul>\r\n<ul>\r\n<li>L9CKxxxxxx_46C_AD.CAR - Release 4.6C</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I812659)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001127132/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928930", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCAIDP0 - Pre-retirement income category H", "RefUrl": "/notes/928930"}, {"RefNumber": "870565", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Output of report RPUTSVP0 has been improved", "RefUrl": "/notes/870565"}, {"RefNumber": "811311", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Annual Income Declaration - Electronic file", "RefUrl": "/notes/811311"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1661264", "RefComponent": "PY-PT", "RefTitle": "New format for Annual Income Declaration (RPCAIDP0)", "RefUrl": "/notes/1661264"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1229334", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0 and RPCIIDP0 improvements", "RefUrl": "/notes/1229334"}, {"RefNumber": "1147206", "RefComponent": "PY-PT", "RefTitle": "Missing code in function HR_PT_GET_ENTITY for 4.6C version", "RefUrl": "/notes/1147206"}, {"RefNumber": "1145684", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: other deductions for Soc. Sec. contributions.", "RefUrl": "/notes/1145684"}, {"RefNumber": "1145300", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: J02 when Income and Deduction values are zero.", "RefUrl": "/notes/1145300"}, {"RefNumber": "1143880", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: previous years calc for SS contrib. and union dues", "RefUrl": "/notes/1143880"}, {"RefNumber": "1137030", "RefComponent": "PY-PT", "RefTitle": "RPCAIDPO and RPCIIDPO: correction of negative values.", "RefUrl": "/notes/1137030"}, {"RefNumber": "1118129", "RefComponent": "PY-PT", "RefTitle": "New layout for Açores - Personnel Summary", "RefUrl": "/notes/1118129"}, {"RefNumber": "1043684", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCAIDP0 (Annual Income Declaration)", "RefUrl": "/notes/1043684"}, {"RefNumber": "1017738", "RefComponent": "PY-PT", "RefTitle": "HT-PT: Legal Change Annual Income Declaration", "RefUrl": "/notes/1017738"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1661264", "RefComponent": "PY-PT", "RefTitle": "New format for Annual Income Declaration (RPCAIDP0)", "RefUrl": "/notes/1661264 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1118129", "RefComponent": "PY-PT", "RefTitle": "New layout for Açores - Personnel Summary", "RefUrl": "/notes/1118129 "}, {"RefNumber": "870565", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Output of report RPUTSVP0 has been improved", "RefUrl": "/notes/870565 "}, {"RefNumber": "1229334", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0 and RPCIIDP0 improvements", "RefUrl": "/notes/1229334 "}, {"RefNumber": "1145684", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: other deductions for Soc. Sec. contributions.", "RefUrl": "/notes/1145684 "}, {"RefNumber": "1147206", "RefComponent": "PY-PT", "RefTitle": "Missing code in function HR_PT_GET_ENTITY for 4.6C version", "RefUrl": "/notes/1147206 "}, {"RefNumber": "1145300", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: J02 when Income and Deduction values are zero.", "RefUrl": "/notes/1145300 "}, {"RefNumber": "1143880", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: previous years calc for SS contrib. and union dues", "RefUrl": "/notes/1143880 "}, {"RefNumber": "1137030", "RefComponent": "PY-PT", "RefTitle": "RPCAIDPO and RPCIIDPO: correction of negative values.", "RefUrl": "/notes/1137030 "}, {"RefNumber": "1043684", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCAIDP0 (Annual Income Declaration)", "RefUrl": "/notes/1043684 "}, {"RefNumber": "1017738", "RefComponent": "PY-PT", "RefTitle": "HT-PT: Legal Change Annual Income Declaration", "RefUrl": "/notes/1017738 "}, {"RefNumber": "928930", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCAIDP0 - Pre-retirement income category H", "RefUrl": "/notes/928930 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "811311", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Annual Income Declaration - Electronic file", "RefUrl": "/notes/811311 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CD3", "URL": "/supportpackage/SAPKE46CD3"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47078", "URL": "/supportpackage/SAPKE47078"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50044", "URL": "/supportpackage/SAPKE50044"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60027", "URL": "/supportpackage/SAPKE60027"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 5, "URL": "/corrins/0001127132/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 16, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "544008 ", "URL": "/notes/544008 ", "Title": "HR-PT: Information available from FI system", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "606082 ", "URL": "/notes/606082 ", "Title": "HR-PT: Record type for Attach. J - RPCAIDP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "612856 ", "URL": "/notes/612856 ", "Title": "HR-PT: Remuneration type in record type 2 - RPCAIDP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "811311 ", "URL": "/notes/811311 ", "Title": "HR-PT: Annual Income Declaration - Electronic file", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "863289 ", "URL": "/notes/863289 ", "Title": "HR-PT: Annual income declaration - Electronic file only", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "870565 ", "URL": "/notes/870565 ", "Title": "HR-PT: Output of report RPUTSVP0 has been improved", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "928930 ", "URL": "/notes/928930 ", "Title": "HR-PT: RPCAIDP0 - Pre-retirement income category H", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1017738 ", "URL": "/notes/1017738 ", "Title": "HT-PT: Legal Change Annual Income Declaration", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1043684 ", "URL": "/notes/1043684 ", "Title": "HR-PT: RPCAIDP0 (Annual Income Declaration)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1047773 ", "URL": "/notes/1047773 ", "Title": "HR-PT - RPCAIDP0, Negative income in current year", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1071646 ", "URL": "/notes/1071646 ", "Title": "RPCAIDP0 wage types from previous years are not processed", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1118129 ", "URL": "/notes/1118129 ", "Title": "New layout for A&#x00E7;ores - Personnel Summary", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "648574 ", "URL": "/notes/648574 ", "Title": "HR-PT:Files downloaded are not filled with spaces in the end", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "863289 ", "URL": "/notes/863289 ", "Title": "HR-PT: Annual income declaration - Electronic file only", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "870565 ", "URL": "/notes/870565 ", "Title": "HR-PT: Output of report RPUTSVP0 has been improved", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "928930 ", "URL": "/notes/928930 ", "Title": "HR-PT: RPCAIDP0 - Pre-retirement income category H", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "980037 ", "URL": "/notes/980037 ", "Title": "HR-PT: RPCPRSP0 - city and postal code description", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1017738 ", "URL": "/notes/1017738 ", "Title": "HT-PT: Legal Change Annual Income Declaration", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1043684 ", "URL": "/notes/1043684 ", "Title": "HR-PT: RPCAIDP0 (Annual Income Declaration)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1047773 ", "URL": "/notes/1047773 ", "Title": "HR-PT - RPCAIDP0, Negative income in current year", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1071646 ", "URL": "/notes/1071646 ", "Title": "RPCAIDP0 wage types from previous years are not processed", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1118129 ", "URL": "/notes/1118129 ", "Title": "New layout for A&#x00E7;ores - Personnel Summary", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "863289 ", "URL": "/notes/863289 ", "Title": "HR-PT: Annual income declaration - Electronic file only", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "870565 ", "URL": "/notes/870565 ", "Title": "HR-PT: Output of report RPUTSVP0 has been improved", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "928930 ", "URL": "/notes/928930 ", "Title": "HR-PT: RPCAIDP0 - Pre-retirement income category H", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "980037 ", "URL": "/notes/980037 ", "Title": "HR-PT: RPCPRSP0 - city and postal code description", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1017738 ", "URL": "/notes/1017738 ", "Title": "HT-PT: Legal Change Annual Income Declaration", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1043684 ", "URL": "/notes/1043684 ", "Title": "HR-PT: RPCAIDP0 (Annual Income Declaration)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1047773 ", "URL": "/notes/1047773 ", "Title": "HR-PT - RPCAIDP0, Negative income in current year", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1071646 ", "URL": "/notes/1071646 ", "Title": "RPCAIDP0 wage types from previous years are not processed", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1118129 ", "URL": "/notes/1118129 ", "Title": "New layout for A&#x00E7;ores - Personnel Summary", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "863289 ", "URL": "/notes/863289 ", "Title": "HR-PT: Annual income declaration - Electronic file only", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "870565 ", "URL": "/notes/870565 ", "Title": "HR-PT: Output of report RPUTSVP0 has been improved", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "928930 ", "URL": "/notes/928930 ", "Title": "HR-PT: RPCAIDP0 - Pre-retirement income category H", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "980037 ", "URL": "/notes/980037 ", "Title": "HR-PT: RPCPRSP0 - city and postal code description", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1017738 ", "URL": "/notes/1017738 ", "Title": "HT-PT: Legal Change Annual Income Declaration", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1043684 ", "URL": "/notes/1043684 ", "Title": "HR-PT: RPCAIDP0 (Annual Income Declaration)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1047773 ", "URL": "/notes/1047773 ", "Title": "HR-PT - RPCAIDP0, Negative income in current year", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1071646 ", "URL": "/notes/1071646 ", "Title": "RPCAIDP0 wage types from previous years are not processed", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1073524 ", "URL": "/notes/1073524 ", "Title": "HCM PT PS: BDAP - Public Admin. Human Resources Data Base", "Component": "PY-PT-PS"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1118129 ", "URL": "/notes/1118129 ", "Title": "New layout for A&#x00E7;ores - Personnel Summary", "Component": "PY-PT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}