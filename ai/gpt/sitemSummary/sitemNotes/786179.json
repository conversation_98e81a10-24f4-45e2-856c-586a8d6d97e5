{"Request": {"Number": "786179", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 229, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015782402017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000786179?language=E&token=7E203FE00591716361E5054C8234CD47"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000786179", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000786179/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "786179"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.12.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-VIR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Anti Virus Protection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Anti Virus Protection", "value": "BC-SEC-VIR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-VIR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "786179 - Data security products: Use in the antivirus area"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Queries:</p>\r\n<ul>\r\n<li>Prerequisites when using antivirus solutions in SAP applications</li>\r\n</ul>\r\n<ul>\r\n<li>Using the SAP virus scan interface (VSI)</li>\r\n</ul>\r\n<ul>\r\n<li>Which components are required for use</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Virus protection, virus, data security, NW-VSI, virus scan adapter, VSA, virus scan interface, VSI, virus scan server</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are using SAP R/3 Enterprise, SAP NetWeaver, Business ByDesign or other SAP products. Also see SAP Notes 817623, 1494278, 797108, and 1883424.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br />The VSI allows you to use external antivirus solutions for checking documents between SAP System components (for example, between application servers and front-end clients).</p>\r\n<p><strong>1. What is the virus scan interface used for?</strong></p>\r\n<p style=\"padding-left: 60px;\">The interface allows you to check documents using external products. Therefore, SAP transfers virus checking and checks for other content that is not permitted to external partner products. Customers should decide for themselves which provider best meets their own criteria.<br />VSI supports the combination of several external partner products, which can increase the total security provided by the antivirus solution.<br />You can buy the external antivirus solutions from the relevant vendor. These solutions may cause extra costs, even if you are already using a product of a vendor. The partner determines the license. Therefore, you should place a quetry with a possible partner as early as possible. SAP Note <a target=\"_blank\" href=\"/notes/1494278\">1494278</a> lists the options that are currently available.<br />You can use VSI to perform a document check that exceeds the classic antivirus check (SAP Note <a target=\"_blank\" href=\" /sap/support/notes/1640285\">1640285</a>). The enhancement is merged in a new version for VSI - NW-VSI 2.00. SAP Note <a target=\"_blank\" href=\"/notes/1883424\">1883424</a> provides an overview of this.<br /><br />The interface is provided as an Application Programming Interface (API) within SAP products in the different language environments (C/C++/ABAP/Java).<br /><br />The interface should be integrated into an application by the respective SAP application. If a customer requires protection, the integration can be carried out by the SAP application or the customer can call the interface himself to check documents. Typically, you should call VSI before or after the transfer of documents to others (see SAP Note <a target=\"_blank\" href=\"/notes/817623\">817623</a>).</p>\r\n<p><strong>2. Certification of external antivirus solutions with NW-VSI 2.00</strong></p>\r\n<p style=\"padding-left: 60px;\">The term 'SAP-certified' in connection with the virus scan interface means that the operability of the NW-VSI 2.00 or NW-VSI 2.01 interface has been tested. No information is provided about the \"quality\" of a virus check; that is, for example, information about whether the latest viruses are found on all platforms. New features in NW-VSI 2.00 are tested, including searching SAP archive formats as well as the determination of MIME types to make an effective filter on them possible.<br /><br /></p>\r\n<p style=\"padding-left: 60px;\">Products used can be certified by the SAP Integration and Certification Center (ICC). <strong>Certification was discontinued in December 2023. </strong>For more detailed information, see SAP Note <a target=\"_blank\" href=\"/notes/1494278\">1494278</a>.<br /><br /></p>\r\n<p style=\"padding-left: 60px;\">In principle, the antivirus solution should always be used on the application server, since this enables the fastest check. However, on the application server side, there is seldom an AV product for various UNIX variants, such as AS/400. You can use the Virus Scan Server here (see SAP Note <a target=\"_blank\" href=\"/notes/782963\">782963</a>).<br /><br />In the meantime, there are also some non-certified integrations.&#x00A0;For uncertified connections, you have to buy the antivirus product separately or inform the manufacturer that you are using it because a license for an antivirus product only allows clearly defined forms of usage, for example, operating system protection only. The product manufacturer makes the relevant decisions. Therefore, you should involve the manufacturer before you start using the product, among other things to clarify questions regarding support.</p>\r\n<p><strong><span style=\"font-size: 14px;\">&#x00A0;</span></strong></p>\r\n<p><strong><span style=\"font-size: 14px;\">3. Specification of a connection to the virus scan interface</span></strong></p>\r\n<p style=\"padding-left: 60px;\">The interface for the external antivirus products is a 'C' interface. This was defined by SAP itself using prototype developments. Prototype adapters have been developed for Sophos, Symantec and ClamAV products. However, they have not been certified by the relevant vendors. See SAP Note <a target=\"_blank\" href=\"/notes/1494278\">1494278</a>.<br /><br />As a manufacturer of security solutions, you can download the Software Development Kit (SDK) and a test program for the interface. The C header file 'vsaxxtyp.h' is also available in the SDK; you require this to implement your own solutions.<br /><br />The VSA SDK contains the specification of a virus scan adapter, online helps for the program interface, an example of a VSA, the test and certification program \"vsatest\", and a Java test and certification application (VSA-SDK available on request from SAP ICC, e-mail: <EMAIL>, interface name NW-VSI 2.01).</p>\r\n<p><strong><span style=\"font-size: 14px;\">4. Components of the virus scan interface</span></strong></p>\r\n<p style=\"padding-left: 60px;\">The architecture of the interface is described in the VSA specification. This specification is part of the VSA-SDK. There is documentation about the interface in the<a target=\"_blank\" href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/1531c8a1792f45ab95a4c49ba16dc50b/4e2606c3c61920cee10000000a42189c.html?state=latest&amp;q=virus&amp;locale=en-US\"> SAP Help Portal</a>.</p>\r\n<p style=\"padding-left: 150px;\"><strong style=\"font-size: 14px;\"><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"http://help.sap.com/\">&#x00A0;</a></span></strong></p>\r\n<p style=\"padding-left: 30px;\"><strong><span style=\"font-size: 14px;\">a) Using Virus Scan Server</span></strong></p>\r\n<p style=\"padding-left: 60px;\">Virus Scan Server communicates via the log RFC with Web Application Server (WebAS). You can use Virus Scan Server if the external antivirus product does not match the architecture of the SAP server. However, Virus Scan Server has some restrictions. See SAP Note <a target=\"_blank\" href=\"/notes/782963\">782963</a> or <a target=\"_blank\" href=\"/notes/964305\">964305</a> for this.</p>\r\n<p style=\"padding-left: 30px;\"><strong>b) SAP APIs</strong></p>\r\n<p style=\"padding-left: 60px;\">ABAP: The class 'CL_VSI' provides three methods for the check: SCAN_BYTES, SCAN_FILE, and SCAN_ITAB.</p>\r\n<p style=\"padding-left: 60px;\">Java: The J2EE interface 'tc/sec/vsi/interface' is available for the check. A  reference to this interface must be set in your own applications. Help for the interface is available at http://help.sap.com/javadocs in the security area.</p>\r\n<p style=\"padding-left: 60px;\">RFC: To check a document, you can use the RFC interface of the Virus Scan Server directly using an RFC client program. (See SAP Note<a target=\"_blank\" href=\"/notes/964305\"> 964305</a>)</p>\r\n<p><strong><span style=\"font-size: 14px;\">5. Testing the virus scan interface</span></strong></p>\r\n<p style=\"padding-left: 60px;\">As an antivirus partner or a development partner, you can test the interface by unpacking the sample adapter from the VSA-SDK (on request from SAP ICC, e-mail: <EMAIL>, interface name NW-VSI 2.01) and specifying it as the adapter in the configuration (\"Adapter Path\" field).   For this purpose, you require the complete path to the library. In other words, for Windows, the example 'd:\\usr\\sap\\SAP\\exe\\vssap.dll' might apply, for Linux '/usr/sap/SAP/exe/vssap.so'. Note that the sample adapter recognizes only the EICAR virus (Note 666568) or a 'positive' example (see VSA-SDK). However, these two examples are available in ABAP and Java test applications.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC (Security - Read KBA 2985997 for subcomponents)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D067847)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I549564)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000786179/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000786179/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "996008", "RefComponent": "BC-CCM-ADK", "RefTitle": "Virus check for data archiving", "RefUrl": "/notes/996008"}, {"RefNumber": "964305", "RefComponent": "BC-SEC-VIR", "RefTitle": "Virus scan server as self-starter", "RefUrl": "/notes/964305"}, {"RefNumber": "927050", "RefComponent": "PA-ER", "RefTitle": "Virus scan during the upload of documents", "RefUrl": "/notes/927050"}, {"RefNumber": "851789", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/851789"}, {"RefNumber": "847934", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Error in the data transfer when you save a Web template", "RefUrl": "/notes/847934"}, {"RefNumber": "817623", "RefComponent": "BC-SEC-VIR", "RefTitle": "Frequent questions about VSI in SAP applications", "RefUrl": "/notes/817623"}, {"RefNumber": "807989", "RefComponent": "BC-SEC-VIR", "RefTitle": "Problem analysis: Virus scan during file upload", "RefUrl": "/notes/807989"}, {"RefNumber": "803637", "RefComponent": "BC-FES-GUI", "RefTitle": "Virus check added to gui_upload", "RefUrl": "/notes/803637"}, {"RefNumber": "797108", "RefComponent": "BC-SEC-VIR", "RefTitle": "Virus scan interface (NW-VSI): Changes and releases", "RefUrl": "/notes/797108"}, {"RefNumber": "782963", "RefComponent": "BC-SEC-VIR", "RefTitle": "SAP Note about usage of Virus Scan Server for NW-VSI", "RefUrl": "/notes/782963"}, {"RefNumber": "666568", "RefComponent": "BC-SEC-VIR", "RefTitle": "Using the EICAR anti-virus test file", "RefUrl": "/notes/666568"}, {"RefNumber": "639486", "RefComponent": "BC-SEC-VIR", "RefTitle": "Anti-virus protection within SAP applications (BC-SEC-VIR)", "RefUrl": "/notes/639486"}, {"RefNumber": "1917129", "RefComponent": "BC-SEC-VIR", "RefTitle": "Correction of VSI call in GUI Up- and  Download", "RefUrl": "/notes/1917129"}, {"RefNumber": "1883929", "RefComponent": "BC-SEC-VIR", "RefTitle": "Correction of VSI call in HTTP Up- and  Download", "RefUrl": "/notes/1883929"}, {"RefNumber": "1883424", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1883424"}, {"RefNumber": "1873145", "RefComponent": "SD-SLS-WOM", "RefTitle": "Security update for stock pool upload", "RefUrl": "/notes/1873145"}, {"RefNumber": "1845564", "RefComponent": "MOB-APP-EMR-EMS", "RefTitle": "SAP EMR Unwired: Upload fails due to Virus Scan Config.", "RefUrl": "/notes/1845564"}, {"RefNumber": "1796762", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI 2.0: Error when loading external adapters", "RefUrl": "/notes/1796762"}, {"RefNumber": "1743288", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1743288"}, {"RefNumber": "1693981", "RefComponent": "BC-SEC-VIR", "RefTitle": "Unauthorized modification of displayed content", "RefUrl": "/notes/1693981"}, {"RefNumber": "1671303", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Virus scans in Web service messages(payload and attachments)", "RefUrl": "/notes/1671303"}, {"RefNumber": "1642209", "RefComponent": "BC-SEC-VIR", "RefTitle": "Content-Check during file exchange", "RefUrl": "/notes/1642209"}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285"}, {"RefNumber": "1585767", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Enabling Virus Scanning in SAP Content Server", "RefUrl": "/notes/1585767"}, {"RefNumber": "1494278", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI: Summary of Virus Scan Adapter´s for SAP integration", "RefUrl": "/notes/1494278"}, {"RefNumber": "1492914", "RefComponent": "EHS-BD-TLS", "RefTitle": "Missing virus scan during report import", "RefUrl": "/notes/1492914"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2040443", "RefComponent": "BC-OP-NT", "RefTitle": "Anti Virus SW and Windows Server OS files exclusion - INTERNAL ONLY", "RefUrl": "/notes/2040443 "}, {"RefNumber": "2621764", "RefComponent": "SV-SMG-ADM-DTM", "RefTitle": "How to upload work modes in IT Calendar using csv files", "RefUrl": "/notes/2621764 "}, {"RefNumber": "1519484", "RefComponent": "BC", "RefTitle": "How to analyze network disconnections shown in System log (transaction SM21)", "RefUrl": "/notes/1519484 "}, {"RefNumber": "1730930", "RefComponent": "HAN-DB", "RefTitle": "Using antivirus software in an SAP HANA appliance", "RefUrl": "/notes/1730930 "}, {"RefNumber": "2081108", "RefComponent": "HAN-AS-XS", "RefTitle": "Virus Scan Interface (VSI) Support in SAP HANA DB and XS", "RefUrl": "/notes/2081108 "}, {"RefNumber": "1917129", "RefComponent": "BC-SEC-VIR", "RefTitle": "Correction of VSI call in GUI Up- and  Download", "RefUrl": "/notes/1917129 "}, {"RefNumber": "1796762", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI 2.0: Error when loading external adapters", "RefUrl": "/notes/1796762 "}, {"RefNumber": "1883929", "RefComponent": "BC-SEC-VIR", "RefTitle": "Correction of VSI call in HTTP Up- and  Download", "RefUrl": "/notes/1883929 "}, {"RefNumber": "1494278", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI: Summary of Virus Scan Adapter´s for SAP integration", "RefUrl": "/notes/1494278 "}, {"RefNumber": "817623", "RefComponent": "BC-SEC-VIR", "RefTitle": "Frequent questions about VSI in SAP applications", "RefUrl": "/notes/817623 "}, {"RefNumber": "639486", "RefComponent": "BC-SEC-VIR", "RefTitle": "Anti-virus protection within SAP applications (BC-SEC-VIR)", "RefUrl": "/notes/639486 "}, {"RefNumber": "1873145", "RefComponent": "SD-SLS-WOM", "RefTitle": "Security update for stock pool upload", "RefUrl": "/notes/1873145 "}, {"RefNumber": "1845564", "RefComponent": "MOB-APP-EMR-EMS", "RefTitle": "SAP EMR Unwired: Upload fails due to Virus Scan Config.", "RefUrl": "/notes/1845564 "}, {"RefNumber": "1671303", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Virus scans in Web service messages(payload and attachments)", "RefUrl": "/notes/1671303 "}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285 "}, {"RefNumber": "803637", "RefComponent": "BC-FES-GUI", "RefTitle": "Virus check added to gui_upload", "RefUrl": "/notes/803637 "}, {"RefNumber": "1642209", "RefComponent": "BC-SEC-VIR", "RefTitle": "Content-Check during file exchange", "RefUrl": "/notes/1642209 "}, {"RefNumber": "964305", "RefComponent": "BC-SEC-VIR", "RefTitle": "Virus scan server as self-starter", "RefUrl": "/notes/964305 "}, {"RefNumber": "782963", "RefComponent": "BC-SEC-VIR", "RefTitle": "SAP Note about usage of Virus Scan Server for NW-VSI", "RefUrl": "/notes/782963 "}, {"RefNumber": "1476664", "RefComponent": "PY-DE-BA", "RefTitle": "SV: Optional check of inbound SI data for viruses", "RefUrl": "/notes/1476664 "}, {"RefNumber": "807989", "RefComponent": "BC-SEC-VIR", "RefTitle": "Problem analysis: Virus scan during file upload", "RefUrl": "/notes/807989 "}, {"RefNumber": "996008", "RefComponent": "BC-CCM-ADK", "RefTitle": "Virus check for data archiving", "RefUrl": "/notes/996008 "}, {"RefNumber": "927050", "RefComponent": "PA-ER", "RefTitle": "Virus scan during the upload of documents", "RefUrl": "/notes/927050 "}, {"RefNumber": "847934", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Error in the data transfer when you save a Web template", "RefUrl": "/notes/847934 "}, {"RefNumber": "666568", "RefComponent": "BC-SEC-VIR", "RefTitle": "Using the EICAR anti-virus test file", "RefUrl": "/notes/666568 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}