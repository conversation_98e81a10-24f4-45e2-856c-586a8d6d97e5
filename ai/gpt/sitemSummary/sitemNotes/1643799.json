{"Request": {"Number": "1643799", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 429, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001643799?language=E&token=E78163E7323601C05B5275A7E9157FF4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001643799", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001643799/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1643799"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "09.12.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-SUN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SUN Solaris"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SUN Solaris", "value": "BC-OP-SUN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-SUN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1643799 - Support for Oracle Solaris 11"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />This note describes the initial runtime-release of SAP on Oracle Solaris 11 in December 2011.</p>\r\n<p>Since May 2012 SAP products are generally released on Oracle Solaris 11.</p>\r\n<p>Check SAP note 1669684 - SAP on Oracle Solaris 11 for information on how to install, update or upgrade SAP systems on Oracle Solaris 11. Check the SAP PAM (Product Availability Matrix) for further details of specific products.<br /><br />****<br /><br />You have an already running SAP System on Solaris 9 or Solaris 10.<br />You want to run this SAP System on Oracle Solaris 11.<br />This note describes the prerequisites and necessary steps for already available SAP Products based on SAP Basis 700 which are on ABAP Stack to run on Oracle Solaris 11.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Solaris 11, Solaris SPARC, Solaris X64, Oracle Database, SAP MaxDB, Release for productive use</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have Solaris SPARC or X64 Servers on which you have installed Oracle Solaris 11.<br />Your System Stack is ABAP.<br />Your SAP Products are based on SAP Basis 700.<br /><br />Your existing SAP System fulfills the following:</p>\r\n<ul>\r\n<li>is currently on Oracle Solaris 9 or Oracle Solaris 10.</li>\r\n</ul>\r\n<ul>\r\n<li>uses an ABAP Stack based on SAP Basis 700</li>\r\n</ul>\r\n<ul>\r\n<li>uses SAP MaxDB Database or Oracle Database</li>\r\n</ul>\r\n<ul>\r\n<li>is Unicode or Non Unicode(SPARC only)</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Prerequisites for SAP Systems on running on Oracle Solaris 11</p>\r\n<ul>\r\n<li>Only SAP Systems with ABAP Stack are supported.</li>\r\n</ul>\r\n<ul>\r\n<li>The installation process (SAPInst, System Copy) is not yet supported.</li>\r\n</ul>\r\n<p><br />Database support</p>\r\n<ul>\r\n<li>Systems with SAP MaxDB and Oracle Database are supported</li>\r\n</ul>\r\n<ul>\r\n<li>IBM DB2/UDB is planned after IBM supports DB2/UDB on Oracle Solaris 11</li>\r\n</ul>\r\n<p><br />Only the following Product Versions are currently supported</p>\r\n<ul>\r\n<li>SAP Netweaver 7.0</li>\r\n</ul>\r\n<ul>\r\n<li>SAP ERP 6.0 (incl. Enhancement Package 2 and 3)</li>\r\n</ul>\r\n<ul>\r\n<li>SAP CRM 5.0,</li>\r\n</ul>\r\n<ul>\r\n<li>SAP CRM 2007(6.0)</li>\r\n</ul>\r\n<ul>\r\n<li>SAP SCM 5.0 (liveCache Server 7.6 is not supported)</li>\r\n</ul>\r\n<ul>\r\n<li>SAP SCM 5.1 (liveCache Server 7.7 is supported)</li>\r\n</ul>\r\n<ul>\r\n<li>SAP SRM 5.0</li>\r\n</ul>\r\n<p><br /><br />System Requirements for Oracle Solaris 11 for productive use</p>\r\n<ol>1. Oracle Solaris 11</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Minimum patchlevel for Oracle Solaris is Solaris 11 11/11 with SRU 01 Check for SRU 01 with command \"sudo pkg info entire\"</p>\r\n<ol>2. SAP Kernel</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAP Kernel 720_EXT is required</p>\r\n<ul>\r\n<li>Install the 720_EXT kernel as described in SAP Note 1636252. The minimum patchlevel is 105</li>\r\n</ul>\r\n<ol>3. Database support</ol><ol><ol>a) Oracle Database</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Oracle Database is supported on Oracle Solaris 11 with version ******** and higher. The Oracle Instant Client version has to be ******** and higher.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Oracle Database ******** on SAP Service Marketplace(SMP)</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Path to the download Package:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; http://service.sap.com/swdc</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Database and Database Patches (from other vendors)</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Oracle</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Oracle 11.2 Software (64-bit)</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Installation</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Solaris Oracle Database</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; ORACLE RDBMS ********</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The update procedure for Oracle Database to version 11.2 is described in SAP note 1431793 - Oracle 11.2.0: Upgrade Scripts</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Oracle Instant Client ******** on SMP</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Path to the download Package:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; http://service.sap.com/swdc</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Database and Database Patches (from other vendors)</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Oracle</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Oracle other</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Special Instant Client Builds</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; ORA_IC_11203_DVD_SOL_11.SAR</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Install the Oracle Instant Client like described in SAP note: 819829 Oracle Instant Client Installation and Configuration especially chapter \"Manually Installing the Oracle Instant Client in an SAP Environment\"</p>\r\n<ul>\r\n<li>Raw Devices are not supported with Oracle Database 11.2.</li>\r\n</ul>\r\n<ol><ol>a) SAP MaxDB Database</ol></ol>\r\n<ul>\r\n<li>SAP MaxDB client version</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Compared to SAP kernel 7.00, which requires SQLDBC version 7.6, SAP kernel 720_EXT requires SQLDBC version 7.7.04.09 or higher.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Before performing the kernel upgrade one must provide a sufficient MaxDB client software (SQLDBC) on all application servers.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check the SQLDBC version on all application servers as described in the MaxDB interface note 822239.</p>\r\n<ul>\r\n<ul>\r\n<li>If the SQLDBC version is lower than 7.7.04.09 perform an update of the MaxDB client software. Note 649814 describes where to download the software and how to install it.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>SAP MaxDB server version</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAP MaxDB is supported on Solaris 11 with Version ********* or higher</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If necessary, perform the MaxDB database upgrade or patch installation as described in the version specific upgrade guide. Descriptions can be found on SAP Service Marketplace at:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;http://service.sap.com/instguides</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Other Documentation -&gt; Database Upgrades -&gt; MaxDB</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For patch installation please also check SAP Note 498036, the overview note on importing SAP MaxDB versions.</p>\r\n<ul>\r\n<li>Comments:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Kernel 720_EXT is the only supported Kernel on Oracle Solaris 11. The SAP upgrade paths on Solaris 11 will be limited to SAP target releases which were initially released with SAP Kernel 720_REL/720_EXT or higher.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5074214)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (C5061499)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643799/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "819829", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Instant Client Installation and Configuration on Unix or Linux", "RefUrl": "/notes/819829"}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814"}, {"RefNumber": "498036", "RefComponent": "BC-DB-SDB", "RefTitle": "Overview note: Installing SAP MaxDB/liveCache versions", "RefUrl": "/notes/498036"}, {"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684"}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356"}, {"RefNumber": "1469675", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "MaxDB 7.8: Inst./System Copy based on SAP Kernel 7.00/7.01", "RefUrl": "/notes/1469675"}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800"}, {"RefNumber": "1431793", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Upgrade Scripts", "RefUrl": "/notes/1431793"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684 "}, {"RefNumber": "1431793", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Upgrade Scripts", "RefUrl": "/notes/1431793 "}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800 "}, {"RefNumber": "498036", "RefComponent": "BC-DB-SDB", "RefTitle": "Overview note: Installing SAP MaxDB/liveCache versions", "RefUrl": "/notes/498036 "}, {"RefNumber": "819829", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Instant Client Installation and Configuration on Unix or Linux", "RefUrl": "/notes/819829 "}, {"RefNumber": "1716200", "RefComponent": "BC-OP-SUN", "RefTitle": "SYB: Sybase ASE on Solaris 11", "RefUrl": "/notes/1716200 "}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356 "}, {"RefNumber": "1469675", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "MaxDB 7.8: Inst./System Copy based on SAP Kernel 7.00/7.01", "RefUrl": "/notes/1469675 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}