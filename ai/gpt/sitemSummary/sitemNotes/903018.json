{"Request": {"Number": "903018", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2178, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000903018?language=E&token=217F72AEE2AE2C966BFBE4DF99A078B4"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000903018", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000903018/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "903018"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Troubleshooting Help"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "28.09.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SDB"}, "SAPComponentKeyText": {"_label": "Component", "value": "MaxDB"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MaxDB", "value": "BC-DB-SDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "903018 - SAP MaxDB JDBC Trace"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=903018&TargetLanguage=EN&Component=BC-DB-SDB&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/903018/D\" target=\"_blank\">/notes/903018/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>There is a problem in the SAP J2EE Engine. SAP Support requests a JDBC trace to obtain further information and to be able to forward the problem to SAP MaxDB Support if necessary.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>JDBC, trace, Java</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p>The SAP MaxDB JDBC trace logs JDBC API calls of the JDBC application including the call parameters. In addition, executed SQL statements and their results are recorded.<br /><br />The SAP MaxDB JDBC trace is user-dependent. Trace output is only generated for the user who has activated the trace. You must therefore ensure that the trace is activated by the user who also executes the action that is to be analyzed using the trace. The user&#39;s trace settings are stored under the user&#39;s HOME directory in the jdbctrace.shm file.</p>\r\n<p><strong>Call</strong></p>\r\n<p>The trace function is addressed using the following command:<br /><br />java -jar &lt;installation_directory>\\runtime\\jar\\sapdbc.jar [&lt;option>] [&lt;command>]<br /><br />If not known, the installation directory can be determined using the program XINSTINFO. This is located in the instance-independent part of the SAP MaxDB software (independent program path, display using, for example, Database Manager CLI: dbmcli dbm_getpath IndepProgPath).<br /><br />Example: /sapdb/programs/bin/xinstinfo MYDB<br /><br />IndepData          : /sapdb/data<br />IndepPrograms      : /sapdb/programs<br />InstallationPath    : /sapdb/MYDB/db<br /><br />For this example, the trace function is called as follows:<br /><br />java -jar /sapdb/MYDB/db/runtime/jar/sapdbc.jar [&lt;option>] [&lt;command>]</p>\r\n<p><strong>Options</strong></p>\r\n<p><br /> -h  (help; shows the available options and commands)<br /> -V  (shows the version of the installed SAP MaxDB JDBC driver<br />      [Caution: uppercase &#39;V&#39;])<br /> -d  (database_name)<br /> -u  (database user)<br /> -n  (host on which the database is running)</p>\r\n<p><strong>Commands</strong></p>\r\n<p><br />TRACE ON|OFF<br />  Activate|Deactivate Trace<br /><br />TRACE SIZE &lt;number><br />  Limit the size of the trace file to &lt;number> bytes<br />   minimum size is 8192 bytes<br /><br />TRACE FILENAME &lt;file_name><br />  Sets the name of the trace file. A clear annex shall be given to the<br />   names added.<br /><br />TRACE STOP ON ERROR &lt;error_number>|OFF<br />  Stops trace writing immediately after the specified error<br />  occurred. The trace remains active; only<br />   no more trace output.<br /><br />SHOW [ALL|TRACESETTINGS]<br />  Display Current Trace Settings<br /><br />Only one command can be executed at a time. To execute several commands, the call &#39;java -jar ...&#39; must be executed accordingly often with the relevant command.<br />The trace files are created in the directory displayed in the trace settings.<br />The files follow the naming convention jdbctrace&lt;numerical_string>.prt. The numeric string represents a hash code that is generated by the Java class loader. This distinguishes the files of the different Java threads.<br />It is hardly possible to use this name to determine the trace file of interest for the relevant analysis. Therefore, the timestamp of the files should be used as orientation criterion. The files that contain useful information can also be renamed.<br />It is important that the trace is deactivated again after the generation of the trace files so that the system load is not permanently increased unnecessarily.<br />----</p>\r\n<p><strong>Examples:</strong></p>\r\n<p><br />Determining the SAP MaxDB JDBC driver version:<br />java -jar /sapdb/MYDB/db/runtime/jar/sapdbc.jar -V<br />package com.sap.dbtech.jdbc, MaxDB JDBC Driver, SAP AG, 7.6.06 Build 007-000-009 -441 (Make Version: 7.8.02 Build 022-121-242-659)<br />--<br /><br />Activate SAP MaxDB JDBC trace:<br />java -jar /sapdb/MYDB/db/runtime/jar/sapdbc.jar trace on<br />Configuration:<br />  Trace              : enabled<br />  Trace file name    : jdbctrace.prt<br />  Trace file size    : unlimited<br />  Stop on error      : disabled<br />  Shared memory file : $HOME/.sdb/&lt;computer_name>/&lt;user_name>/jdbctrace.shm<br />  Files will be created in path ./.<br />--<br /><br />Limit the size of the trace file to 1 MB:<br />java -jar /sapdb/MYDB/db/runtime/jar/sapdbc.jar trace size 1048576<br />Configuration:<br />  Trace              : enabled<br />  Trace file name    : jdbctrace.prt<br />  Trace file size    : limited to 1 MBytes<br />  Stop on error      : disabled<br />  Shared memory file : $HOME/.sdb/&lt;computer_name>/&lt;user_name>/jdbctrace.shm<br />  Files will be created in path ./.<br />--<br /><br />Stop trace writing when error -4004 occurs:<br />java -jar /sapdb/MYDB/db/runtime/jar/sapdbc.jar trace stop on error -4004<br />Configuration:<br />  Trace              : enabled<br />  Trace file name    : jdbctrace.prt<br />  Trace file size    : limited to 1 MBytes<br />  Stop on error      : -4004<br />  Shared memory file : $HOME/.sdb/&lt;computer_name>/&lt;user_name>/jdbctrace.shm<br />  Files will be created in path ./.<br />--<br /><br />Display of current trace settings:<br />java -jar /sapdb/MYDB/db/runtime/jar/sapdbc.jar show<br />Configuration:<br />  Trace              : enabled<br />  Trace file name    : jdbctrace.prt<br />  Trace file size    : limited to 1 MBytes<br />  Stop on error      : -4004<br />  Shared memory file : $HOME/.sdb/&lt;computer_name>/&lt;user_name>/jdbctrace.shm<br />  Files will be created in path ./.<br />--<br /><br />Deactivate trace:<br />java -jar /sapdb/MYDB/db/runtime/jar/sapdbc.jar trace off<br />Configuration:<br />  Trace              : disabled<br />  Trace file name    : jdbctrace.prt<br />  Trace file size    : unlimited<br />  Stop on error      : disabled<br />  Shared memory file : $HOME/.sdb/&lt;computer_name>/&lt;user_name>/jdbctrace.shm<br />  Files will be created in path ./.</p>\r\n<p><strong>More Information</strong></p>\r\n<p>For more information, see the SAP MaxDB documentation (SAP Note 767598) in the glossary under the keyword JDBC.</p>\r\n</div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON> (D026206)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON> (D025588)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000903018/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB Interfaces (Interfaces)", "RefUrl": "/notes/822239"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "2823627", "RefComponent": "BC-DB-SDB", "RefTitle": "Performance deterioration in J2EE environment after importing a new SAP MaxDB JDBC driver", "RefUrl": "/notes/2823627 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB Interfaces (Interfaces)", "RefUrl": "/notes/822239 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=903018&TargetLanguage=EN&Component=BC-DB-SDB&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/903018/D\" target=\"_blank\">/notes/903018/D</a>."}}}}