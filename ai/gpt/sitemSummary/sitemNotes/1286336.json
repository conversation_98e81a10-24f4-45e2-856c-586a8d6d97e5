{"Request": {"Number": "1286336", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 522, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007560682017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001286336?language=E&token=83C5B02288AF160597AF6F7A73519EE4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001286336", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001286336/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1286336"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2016/06/14"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BMT-WFM-RUN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Management", "value": "BC-BMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Workflow", "value": "BC-BMT-WFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT-WFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Runtime", "value": "BC-BMT-WFM-RUN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT-WFM-RUN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1286336 - SWIA: New mass-capable ‘Logically Delete’ function"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to delete a large number of work items logically. This applies particularly to the ccBPM environment to clear open correlation instances.<br /><br />Up to now, you could not use standard SAP tools to do this. Other than a customer development, only the following two workarounds were possible in the past:<br />1.) Using the work item administration to manually delete them individually.<br />2.) Using the report RSWWWIDE to physically delete them.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implementing this note adds the \"Logically Delete\" function, which is suitable for mass processing, to transaction SWIA. We recommend that you use this function only on top-level work items. Generally, you can ensure this using an appropriate selection (for example, by task).<br /><br />Important: The correction instructions do not contain the enhancement for the PF status. Therefore, you can access the new function only by entering \"ADMC\" (as in ADMin Cancel) in the OK code field. Following the implementation of the relevant Support Package, the function can be reached via the menu &#x201C;Edit -&gt; Work Item -&gt; Logically Delete&#x201D;.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-XI-IS-BPE (Business Process Engine)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021052)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D026263)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001286336/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001286336/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1705866", "RefComponent": "BC-BMT-WFM", "RefTitle": "\"Logically Delete\" for a large number of work items", "RefUrl": "/notes/1705866"}, {"RefNumber": "1399658", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "SWIA: No authorization check for 'Logically Delete'", "RefUrl": "/notes/1399658"}, {"RefNumber": "1396805", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "\"Logically Delete\" function active in workflow transactions", "RefUrl": "/notes/1396805"}, {"RefNumber": "1332139", "RefComponent": "BC-XI-CON", "RefTitle": "NW04s Support Package Stack 19", "RefUrl": "/notes/1332139"}, {"RefNumber": "1320523", "RefComponent": "BC-XI", "RefTitle": "XI 30 Support Package Stack (SPS) 24", "RefUrl": "/notes/1320523"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2298456", "RefComponent": "EHS-SRC", "RefTitle": "Uninstalling SAP Product and REACH Compliance (SPRC) 2.0 or Compliance for Products (CfP) 2.2 -  /TDAG/CPCL_S4_AOF_PLUGIN", "RefUrl": "/notes/2298456 "}, {"RefNumber": "2239250", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance of workflow inbox applications", "RefUrl": "/notes/2239250 "}, {"RefNumber": "1705866", "RefComponent": "BC-BMT-WFM", "RefTitle": "\"Logically Delete\" for a large number of work items", "RefUrl": "/notes/1705866 "}, {"RefNumber": "1399658", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "SWIA: No authorization check for 'Logically Delete'", "RefUrl": "/notes/1399658 "}, {"RefNumber": "1396805", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "\"Logically Delete\" function active in workflow transactions", "RefUrl": "/notes/1396805 "}, {"RefNumber": "1320523", "RefComponent": "BC-XI", "RefTitle": "XI 30 Support Package Stack (SPS) 24", "RefUrl": "/notes/1320523 "}, {"RefNumber": "1332139", "RefComponent": "BC-XI-CON", "RefTitle": "NW04s Support Package Stack 19", "RefUrl": "/notes/1332139 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C62", "URL": "/supportpackage/SAPKB46C62"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62070", "URL": "/supportpackage/SAPKB62070"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64024", "URL": "/supportpackage/SAPKB64024"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70019", "URL": "/supportpackage/SAPKB70019"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70104", "URL": "/supportpackage/SAPKB70104"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71008", "URL": "/supportpackage/SAPKB71008"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71102", "URL": "/supportpackage/SAPKB71102"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 8, "URL": "/corrins/0001286336/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 8, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "754876 ", "URL": "/notes/754876 ", "Title": "SWI2_ADM2: Work items with deleted agents", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "862695 ", "URL": "/notes/862695 ", "Title": "Administrator Forwarding Functionality in SWI2_ADM1", "Component": "BC-BMT-WFM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1374696 ", "URL": "/notes/1374696 ", "Title": "SWIA: \"Administrator Forward\" function inactive", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "620", "Number": "624152 ", "URL": "/notes/624152 ", "Title": "Authority-Check for priority", "Component": "BC-BMT-WFM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1081189 ", "URL": "/notes/1081189 ", "Title": "Multiple selection in transaction SWIA", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "836317 ", "URL": "/notes/836317 ", "Title": "Changing the work item text in the Business Workplace", "Component": "BC-BMT-WFM-WLC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1008927 ", "URL": "/notes/1008927 ", "Title": "Authorization check for Administrator Forward", "Component": "BC-BMT-WFM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1081189 ", "URL": "/notes/1081189 ", "Title": "Multiple selection in transaction SWIA", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1274031 ", "URL": "/notes/1274031 ", "Title": "SWF_GMP: Restart single steps after error", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "836317 ", "URL": "/notes/836317 ", "Title": "Changing the work item text in the Business Workplace", "Component": "BC-BMT-WFM-WLC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1008927 ", "URL": "/notes/1008927 ", "Title": "Authorization check for Administrator Forward", "Component": "BC-BMT-WFM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1081189 ", "URL": "/notes/1081189 ", "Title": "Multiple selection in transaction SWIA", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1274031 ", "URL": "/notes/1274031 ", "Title": "SWF_GMP: Restart single steps after error", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1274031 ", "URL": "/notes/1274031 ", "Title": "SWF_GMP: Restart single steps after error", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1081189 ", "URL": "/notes/1081189 ", "Title": "Multiple selection in transaction SWIA", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1274031 ", "URL": "/notes/1274031 ", "Title": "SWF_GMP: Restart single steps after error", "Component": "BC-BMT-WFM-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1274031 ", "URL": "/notes/1274031 ", "Title": "SWF_GMP: Restart single steps after error", "Component": "BC-BMT-WFM-MON"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1396805", "RefTitle": "\"Logically Delete\" function active in workflow transactions", "RefUrl": "/notes/0001396805"}]}}}}}