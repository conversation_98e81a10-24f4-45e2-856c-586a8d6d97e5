{"Request": {"Number": "1694697", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 304, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017405052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001694697?language=E&token=70286A4F00D5314D761DAA445D9B8E89"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001694697", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001694697/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1694697"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.10.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DBI"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB Independent Database Interface"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB Independent Database Interface", "value": "BC-DB-DBI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DBI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1694697 - SAP Business Application Accelerator powered by HANA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are looking for information about the SAP Business Application Accelerator powered by HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Suite Accelerator, Scenarios, Context, RDA_MAINTAIN</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The SAP Business Application Accelerator powered by HANA has been offered as ABAP Add-on in a restricted shipment mode until end of 2016.</p>\r\n<p>Starting with 2017 the Add-on is no longer available for new customers.</p>\r\n<p>The Add-on is in general&#160;not supported in S/4 HANA. The existence of the Add-on is blocking technical conversions to S/4HANA 1511 and 1610. Technical conversions to 1709 and beyond&#160;are possible.</p>\r\n<p>Support for existing customers on non-4/HANA products&#160;is continued under the same conditions as before.</p>\r\n<p>To benefit from SAP HANA it is recommended to migrate the entire system to SAP HANA, possibly with a multi node setup for optimized load distribution.</p>\r\n<p>For a description of the functionality and usage of the SAP Business Application Accelerator powered by HANA please read the attached Customer Guide.</p>\r\n<p>Contents:</p>\r\n<p>1 Introduction <br />2 Concept <br />2.1 Redirected Operations <br />2.1.1 Statements <br />2.1.2 Source <br />2.2 Context Definition <br />2.3 Context Evaluation at Runtime <br />3 Using the SAP Business Application Accelerator <br />3.1 Installation <br />3.2 General Activation <br />3.3 Loading Scenarios <br />3.3.1 Predefined SAP Scenarios <br />3.3.2 Custom Scenarios <br />3.4 Trouble Shooting</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023980)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021209)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001694697/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694697/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ApplicationAccelerator_Guide_v23.zip", "FileSize": "451", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000025822012&iv_version=0016&iv_guid=6CAE8B28A04B1EE5BFACDB3AF3EAC083"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1949508", "RefComponent": "BC-DB-DBI", "RefTitle": "SWT2DB: No redirection", "RefUrl": "/notes/1949508"}, {"RefNumber": "1771304", "RefComponent": "BC-DB-DBI", "RefTitle": "SWT2DB: Permitting all database connections", "RefUrl": "/notes/1771304"}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}, {"RefNumber": "1716742", "RefComponent": "BC-DB-DBI", "RefTitle": "SWT2DB: Supporting a placeholder in the job name", "RefUrl": "/notes/1716742"}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986"}, {"RefNumber": "1707579", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "SWT2DB: No check of bit in runtime object header", "RefUrl": "/notes/1707579"}, {"RefNumber": "1699600", "RefComponent": "BC-DB-DBI", "RefTitle": "Documentation of profile parameter rsdb/rda missing", "RefUrl": "/notes/1699600"}, {"RefNumber": "1696402", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of SWT2DB 100/100_740 on SAP NetWeaver", "RefUrl": "/notes/1696402"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2483976", "RefComponent": "BC-DB-DBI", "RefTitle": "SWT2DB: No  redirect to HANA database for logical cluster tables", "RefUrl": "/notes/2483976 "}, {"RefNumber": "2477271", "RefComponent": "LO-MD-MM", "RefTitle": "Short dump CX_SY_OPEN_SQL_DB when reading material data", "RefUrl": "/notes/2477271 "}, {"RefNumber": "2146293", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: SAP Business Application Accelerator combined with the IBM DB2 Analytics Accelerator", "RefUrl": "/notes/2146293 "}, {"RefNumber": "1895820", "RefComponent": "PS-IS-LOG", "RefTitle": "CN41 etc.: Optimized selection with HANA DB via SAP BAA", "RefUrl": "/notes/1895820 "}, {"RefNumber": "1919094", "RefComponent": "BC-DB-DBI", "RefTitle": "Defined SAP Scenarios for the SAP Business Application Accelerator", "RefUrl": "/notes/1919094 "}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283 "}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986 "}, {"RefNumber": "1696402", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of SWT2DB 100/100_740 on SAP NetWeaver", "RefUrl": "/notes/1696402 "}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}, {"RefNumber": "1716742", "RefComponent": "BC-DB-DBI", "RefTitle": "SWT2DB: Supporting a placeholder in the job name", "RefUrl": "/notes/1716742 "}, {"RefNumber": "1699600", "RefComponent": "BC-DB-DBI", "RefTitle": "Documentation of profile parameter rsdb/rda missing", "RefUrl": "/notes/1699600 "}, {"RefNumber": "1707579", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "SWT2DB: No check of bit in runtime object header", "RefUrl": "/notes/1707579 "}, {"RefNumber": "1771304", "RefComponent": "BC-DB-DBI", "RefTitle": "SWT2DB: Permitting all database connections", "RefUrl": "/notes/1771304 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}