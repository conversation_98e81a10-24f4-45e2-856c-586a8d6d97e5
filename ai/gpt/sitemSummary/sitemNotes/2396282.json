{"Request": {"Number": "2396282", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 253, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018443642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002396282?language=E&token=315AEC2FD4C53BADA06E51E15D9E00E3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002396282", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002396282/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2396282"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.12.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-SYS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Interface / DBMS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface / DBMS", "value": "BC-DB-ORA-SYS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-SYS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2396282 - Installation, System Copy, Database Refresh and System Rename of NW 7.3x and NW 7.2x AS Java Systems with Oracle 12c or higher"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Installation, System Copy, DB Refresh and System Rename of SAP&#160;NetWeaver 7.2x&#160;and NW 7.3x&#160;AS Java or AS ABAP + AS Java directly with Oracle 12c or higher (server and client)</p>\r\n<p>The error 'Exception of type com.sap.sql.log.OpenSQLException caught: JDBC driver not supported for ORACLE database' will occur when running the system copy or installation of the mentioned realeases without using the workaround.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>NW 7.30, NW 7.31, AS Java</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Since the Java DVD containing open*sql.jar&#160;does not&#160;support Oracle 12c or higher, this note describes&#160;a workaround for the installation, system copy,&#160;system rename and DB Refresh with Oracle 12c or higher.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Installation: </strong></p>\r\n<p>To install SAP systems (AS Java or AS ABAP + AS Java) based on NW 7.2x or NW 7.3x with Oracle 12c or higher download&#160;the following file from:</p>\r\n<p>&#160;&#160; <a target=\"_blank\" href=\"https://support.sap.com/software/databases.html\">https://support.sap.com/software/databases.html</a></p>\r\n<p>&#160; -&gt; Oracle&#160;-&gt; ORACLE PATCHES&#160;-&gt; ORACLE PATCHES MISCELLANEOUS&#160;-&gt; # OS INDEPENDENT&#160;-&gt; OJDBC6IC11204P_3-10010819.JAR</p>\r\n<p>&#160; and save it in a separate temporary directory&#160;as ojdbc6.jar.</p>\r\n<p><strong>On Unix: </strong></p>\r\n<ol>\r\n<li>\r\n<p>For a central system with Oracle Server Version 12.1 do the following:<br /><br /></p>\r\n<p>Oracle Server Version 12.1</p>\r\n</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Start the installation, choosing 121 as server and client version. You can exchange ojdb6.jar with the downloaded file when sapinst stops and you have already installed the Oracle 12 database software with RUNINSTALLER. If you installed the database software before starting sapinst, you can exchange ojdbc6.jar directly after the Oracle 12c software installation.</li>\r\n<li>Change to the Oracle directory /oracle/&lt;DBSID&gt;/121/jdbc/lib.</li>\r\n<li>Save the the original&#160;ojdbc6.jar under another name, e.g.&#160;ojdbc6.jar_12c.</li>\r\n<li>Copy the&#160;downloaded ojdbc6.jar&#160;to directory&#160;/oracle/&lt;DBSID&gt;/121/jdbc/lib.</li>\r\n<li>Proceed with the installation</li>\r\n</ol></ol>\r\n<p>&#160; &#160; &#160; &#160; Oracle Server Version 12.2 or higher</p>\r\n<ol><ol style=\"list-style-type: lower-alpha;\">\r\n<li>Start sapinst choosing 121, 122, 18 or 19 as server and 121, 122 or 19 as client version and check \"Stop after 'Create DB' for Patch Update\"&#160; in dialog \"Oracle Database\" so that the SWPM stops after database creation.</li>\r\n<li>Change to the Oracle client directory:<br />For Oracle client 121:&#160; /oracle/client/12x/instantclient <br />For Oracle client 122 : /oracle/client/122/instantclient&#160;</li>\r\n<li>Save the original ojdbc6.jar under another name e.g. ojdbc6.jar_12c.</li>\r\n<li>Copy the downloaded file named ojdbc6.jar to directory /oracle/client/12x/instantclient or /oracle/client/122/instantclient.</li>\r\n<li>Adapt the owner and group with this command: <br />chown &lt;sid&gt;adm:sapsys ojdbc6.jar</li>\r\n<li>Proceed with the installation</li>\r\n</ol>\r\n<li>&#160;For a distributed system, do the following:<br /><br /></li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Install the the central services instance (SCS/ASCS).</li>\r\n<li>Install the database instance,&#160;choosing 121, 122 or 18 as server and 121 or 122 as client version:</li>\r\n<ol style=\"list-style-type: lower-roman;\">\r\n<li>Start sapinst and check&#160;\"Stop after 'Create DB' for&#160;Patch Update\"&#160; in dialog \"Oracle Database\"&#160;so that the SWPM&#160;stops after database creation.</li>\r\n<li>Change to the Oracle client directory:<br />For Oracle client 121: &#160;/oracle/client/12x/instantclient <br />For Oracle client 122&#160;: /oracle/client/122/instantclient</li>\r\n<li>Save&#160;the original&#160;ojdbc6.jar under another name e.g.&#160;ojdbc6.jar_12c.</li>\r\n<li>Copy&#160;the downloaded file named ojdbc6.jar to directory&#160;/oracle/client/12x/instantclient or /oracle/client/122/instantclient.</li>\r\n<li>Adapt the owner and group&#160;with this command: <br />chown &lt;sid&gt;adm:sapsys ojdbc6.jar</li>\r\n<li>Proceed with the installation</li>\r\n</ol>\r\n<li>Install the primary application server instance:</li>\r\n<ol style=\"list-style-type: lower-roman;\">\r\n<li>If the the user &lt;sid&gt;adm and group SAPSYS do not exist on the host, create them&#160;by&#160;selecting&#160;SWPM -&gt; Generic Option&#160; -&gt; Oracle -&gt; Preparations -&gt; Operating System Users and Groups&#160; (do not create DB-specific users).</li>\r\n<li>Create the client directory depending on the client version:<br />For Oracle client 121: /oracle/client/12x<br />For Oracle client 122: /oracle/client/122&#160;<br />For example for Oracle client 121&#160;<br />mkdir -p /oracle/client/12x<br />chmod 775 /oracle/client/12x</li>\r\n<li>Adapt the owner and group:<br />chown &lt;sid&gt;adm:sapsys /oracle/client/12x or /oracle/client/122</li>\r\n<li>Extract the Oracle 12 client software with the command:<br />SAPCAR -xvf &lt;mountpoint of the client DVD/&lt;your OS&gt;/OCL12164.SAR or OCL12264.SAR</li>\r\n<li>For client 121 change to the client directory /oracle/client/12x and <br />create the symbolic link: &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;&#160; <br />ln -s instantclient_12102 <br />instantclient &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; <br /><br />For client 122 change to /oracle/client/122 and create the symbolic link:&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br />ln -s instantclient_12201 instantclient</li>\r\n<li>Save the original ojdbc6.jar in the client directory with this command:<br />mv ojdbc6.jar ojdbc6.jar_12c</li>\r\n<li>Copy the&#160;downloaded ojdbc6.jar to the directory <br />/oracle/client/12x/instantclient_12102 or <br />/oracle/client/122/instantclient_12201.</li>\r\n<li>Adapt the owner and group:<br />chown &lt;sid&gt;adm:sapsys ojdbc6.jar</li>\r\n<li>Start the installation of the central instance</li>\r\n</ol></ol>\r\n<li>After the installation, install the required support package patches for Oracle 12 as described in SAP Note <a target=\"_blank\" href=\"/notes/1777021\">1777021</a>.</li>\r\n<li>After the installation:</li>\r\n<ol>\r\n<li>Delete the file ojdbc6.jar that you downloaded at the beginning.</li>\r\n<li>Move the original file (which was renamed to, for example,&#160;ojdbc6.jar_12c) back to /oracle/client/12x/instantclient_12102 or /oracle/client/122/instantclient_12201, renaming it back&#160;to ojdbc6.jar</li>\r\n<li>Restart the system. <br /><strong>It is very important to use the original ojdbc6.jar otherwise the system&#160;does not work correctly.</strong></li>\r\n</ol></ol>\r\n<p><strong>On Windows: </strong></p>\r\n<ol>\r\n<li>For a central system, do the following:</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Start sapinst, choosing 121, 122, 18 or 19 as server and 121, 122 or 19 as client version,&#160;and check&#160;\"Stop after 'Create DB' for&#160;Patch Update\" in dialog \"Oracle Database\"&#160;so that the SWPM&#160;stops after database creation.</li>\r\n<li>Change to your exe directory &lt;drive&gt;:\\usr\\sap\\&lt;SID&gt;\\SYS\\exe\\uc\\NTAMD64.</li>\r\n<li>Save the existing ojdbc6,jar by renaming it to, for example, ojdbc6.jar_12c.</li>\r\n<li>Copy the downloaded ojdbc6.jar to this directory.</li>\r\n<li>Proceed with the installation.</li>\r\n</ol>\r\n<li>For a distributed system, do the following:</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Install the central services instance (SCS/ASCS).</li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; letter-spacing: -0.05pt; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Install</span><span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"> <span style=\"letter-spacing: -0.05pt;\">the database instance </span>choosing 121, 122, 18 or 19 as server and 121, 122 or 19 as client version<span style=\"letter-spacing: -0.05pt;\">:</span></span></li>\r\n<ol style=\"list-style-type: lower-roman;\">\r\n<li><span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"letter-spacing: -0.05pt;\">Start sapinst and check&#160;\"Stop after 'Create DB' for&#160;Patch Update\" in dialog \"Oracle Database\"&#160;so that the SWPM&#160;stops after database creation.</span></span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"letter-spacing: -0.05pt;\">Change to your exe directory &lt;SAPGLOBALHOST&gt;\\sapmnt\\SID&gt;\\SYS\\exe\\uc\\NTAMD64.</span></span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"letter-spacing: -0.05pt;\">Save the existing ojdbc6,jar by renaming it to, for example, ojdbc6.jar_12c.</span></span></li>\r\n<li>Copy&#160;the downloaded file named ojdbc6.jar this directory<span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"letter-spacing: -0.05pt;\">.</span></span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"letter-spacing: -0.05pt;\">Proceed with the installation.</span></span></li>\r\n</ol>\r\n<li><span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-fareast-theme-font: minor-fareast; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"letter-spacing: -0.05pt;\">Install the primary application server instance.</span></span></li>\r\n</ol>\r\n<li>After the installation, install the required support package patches for Oracle 12 as described in SAP Note <a target=\"_blank\" href=\"/notes/1777021\">1777021</a>.</li>\r\n<li>After the installation:</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Delete the file ojdbc6.jar that you downloaded at the beginning.</li>\r\n<li>Move the original file (which was renamed to, for example,&#160;ojdbc6.jar_12c) back to &lt;drive&gt;:\\usr\\sap\\&lt;SID&gt;\\SYS\\exe\\uc\\NTAMD64 or &lt;SAPGLOBALHOST&gt;\\sapmnt\\&lt;SID&gt;\\SYS\\exe\\uc\\NTAMD64, renaming it back&#160;to ojdbc6.jar.</li>\r\n<li>Restart the system.&#160;<br /><strong>It is very important to use the original ojdbc6.jar otherwise the system&#160;does not work correctly.</strong>&#160;</li>\r\n</ol></ol>\r\n<p><strong>System Copy - install the target system:</strong></p>\r\n<p>Follow the description for the installation.</p>\r\n<p>After the installation of the target system apply SAP Note&#160;&#160;<a target=\"_blank\" href=\"/notes/1777021\">1777021</a>. If&#160;the required PL is&#160;already installed on the source system,&#160;re-deploy&#160;it.</p>\r\n<p><strong>System Rename</strong>:</p>\r\n<p><strong>On UNIX</strong>:</p>\r\n<ol>\r\n<li>Before starting the rename, save the original&#160;ojdbc6.jar in directory /oracle/client/12x/instantclient under another name and copy the downloaded ojdbc6.jar to this directory</li>\r\n<li>After the rename, return&#160;to the original ojdbc6.jar.</li>\r\n</ol>\r\n<p><strong>On Windows</strong>:</p>\r\n<ol>\r\n<li>Before starting the rename, save the original&#160;ojdbc6.jar in directory &lt;DRIVE&gt;: \\usr\\sap\\&lt;SID&gt;\\SYS\\exe\\uc\\NTAMD64 under another name e.g. ojdbc6.jar_12c, and copy the downloaded ojdbc6.jar to this directory</li>\r\n<li>After the rename, return&#160;to the original ojdbc6.jar</li>\r\n<li>Restart the system</li>\r\n</ol>\r\n<p>&#160;</p>\r\n<p><strong>DB Refresh</strong>:</p>\r\n<p><strong>On Unix:</strong></p>\r\n<p>Follow the description for System Rename</p>\r\n<p><strong>On Windows:</strong></p>\r\n<ol>\r\n<li>Before starting the DB Refresh, save the original&#160;ojdbc6.jar in the &lt;DIR_INSTANCE\\exe&gt; directory&#160;&lt;DRIVE&gt;: \\usr\\sap\\&lt;SID&gt;\\&lt;INSTANCE&gt;\\exe under another name e.g. ojdbc6.jar_12c, and copy the downloaded ojdbc6.jar to this directory.</li>\r\n<li>After the DB Refresh, return&#160;to the original ojdbc6.jar</li>\r\n<li>Restart the system</li>\r\n</ol>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-INS (Installation Tools (SAP Note 1669327))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> (D029385)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5000979)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002396282/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002396282/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2870867", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "\"JDBC driver not supported for ORACLE database.\" error while installing JAVA AS or upgrading an existing JAVA System", "RefUrl": "/notes/2870867 "}, {"RefNumber": "2595196", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *70JDS*.SAR of Software Provisioning Manager 1.0 - Covers No Longer Supported Java and Dual-Stack Options and Access to Guides for Software Provisioning Manager 1.0 Java and Dual Stack", "RefUrl": "/notes/2595196 "}, {"RefNumber": "1619720", "RefComponent": "BC-INS-SRN", "RefTitle": "System Rename for SAP Systems based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1619720 "}, {"RefNumber": "1738258", "RefComponent": "BC-INS-MIG", "RefTitle": "System Copy for Systems Based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1738258 "}, {"RefNumber": "2172935", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Installation - SAP Systems based on SAP NetWeaver : Oracle Database", "RefUrl": "/notes/2172935 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}