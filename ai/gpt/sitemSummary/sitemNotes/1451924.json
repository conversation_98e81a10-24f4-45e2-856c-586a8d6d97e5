{"Request": {"Number": "1451924", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 418, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017001662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=CB5A1518CFD28A946EE28762CA43ADC7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1451924"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released Internally"}, "ReleasedOn": {"_label": "Released On", "value": "22.06.2023"}, "SAPComponentKey": {"_label": "Component", "value": "EC-PCA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Profit Center Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Enterprise Controlling", "value": "EC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Profit Center Accounting", "value": "EC-PCA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-PCA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1451924 - Profit center reorganization in the classic general ledger"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In an active system, you want to change the profit center assignement in logistical objects (material master, sales documents, purchase orders, or deliveries) or in CO objects (for example, internal orders, production orders) as part of a restructuring or reimplementation of Profit Center Accounting.<br /><br />You use the classic general ledger.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Profit center assignment, PCA reorganization, restructuring, reimplementation, old general ledger, settlement rule, purchase order, internal order, profitability segment, delivery, billing document, receivables, payables<br />Cost centers, projects, assets</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>If you reorganize your Profit Center Accounting, the profit centers that were valid on the day the object was created are used when postings are made to the existing objects. During the reimplementation of Profit Center Accounting, postings may be made to the dummy profit center. <br /><br />Postings are made to:<br />- Sales documents in case of delivery or billing document<br />- Purchase orders in case of goods receipt or invoice receipt<br />- Production orders in case of confirmation, goods withdrawal, application of overhead<br /> or delivery against warehouse<br />- In case of additional CO objects in the profit center assignment in<br /> the relevant object</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>If you restructure or reimplement Profit Center Accounting, the service to reorganize Profit Center Accounting converts existing objects simply and quickly. As part of the solution, the \"Profit Center\" field is corrected or filled for the first time in the following objects and logistical data:<br />o Material master<br />o Sales documents<br />o Purchase orders (assigned and unassigned)<br />o Production orders and process orders<br />o Product cost collector<br />o Projects with WBS elements<br />o Networks with network activities<br />o Internal orders, model orders, and accrual orders<br />o CO production orders<br />o Deliveries and billing documents<br />o Regeneration of settlement rule in CO-PA<br /><br />The tool reads the new profit center using predefined criteria from the material master, the master data of the CO object or from the requesting or responsible cost center, and fills the fields. You can preassign the field according to your requirements using user exits or you can use Excel to maintain the field.<br /><br />The service also supports the transfer of material stock and work in process transferred online. <br /><br />The service can be used to create settlement rules in the Profitability Analysis (CO-PA) in the actual and plan according to the SAP standard logic for periods for which no settlements have yet been carried out.<br /><br />SAP Consulting provides the solution by transport request. Additional support is available for adjustments. Implementation is possible for Releases 6.0 to 6.18.<br /><br />The solution is a clearly defined, clear investment with tested results. It can be used productively immediately after a fast implementation.<br /><br />Contact Frank Gelbarth (<EMAIL>) if you have any queries.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CO-PC (Product Cost Controlling)"}, {"Key": "Other Components", "Value": "CO-PA (Profitability Analysis)"}, {"Key": "Other Components", "Value": "MM-PUR-PO (Purchase Orders)"}, {"Key": "Other Components", "Value": "PM-WOC-MO (Maintenance Orders)"}, {"Key": "Other Components", "Value": "PS-COS (Costs)"}, {"Key": "Other Components", "Value": "SD-BIL-CA (Account Assignment)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D048202)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D048202)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "858363", "RefComponent": "EC-PCA", "RefTitle": "Organizing/reorganizing Profit Center Accounting", "RefUrl": "/notes/858363"}, {"RefNumber": "79847", "RefComponent": "SD-SLS-GF-CO", "RefTitle": "Update of profitability segment in sales document", "RefUrl": "/notes/79847"}, {"RefNumber": "580216", "RefComponent": "SD-SLS-GF-CO", "RefTitle": "Profit center cannot be changed if partial billing document exists", "RefUrl": "/notes/580216"}, {"RefNumber": "189325", "RefComponent": "CO-OM-OPA-F", "RefTitle": "Info: Settlement rule for settling variances into CO-PA", "RefUrl": "/notes/189325"}, {"RefNumber": "174512", "RefComponent": "CO-PA-ACT", "RefTitle": "Profitability segment: Alternative profit center", "RefUrl": "/notes/174512"}, {"RefNumber": "173798", "RefComponent": "EC-PCA-MD", "RefTitle": "User exit for PCA substitution", "RefUrl": "/notes/173798"}, {"RefNumber": "1534197", "RefComponent": "CA-LT-CNV", "RefTitle": "SAP LT: Transformation Solution - Profit Center Reorganization", "RefUrl": "/notes/1534197"}, {"RefNumber": "141009", "RefComponent": "EC-PCA-ACT", "RefTitle": "Resetting new profit center for PO", "RefUrl": "/notes/141009"}, {"RefNumber": "117544", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Changing the profit center in the PM/SM order", "RefUrl": "/notes/117544"}, {"RefNumber": "107698", "RefComponent": "EC-PCA-ACT-PE", "RefTitle": "Structure of receivables/payables in classic Profit Center Accounting (PCA)", "RefUrl": "/notes/107698"}, {"RefNumber": "1015677", "RefComponent": "SD-SLS-GF-CO", "RefTitle": "Profit center of billing document for inter-company process", "RefUrl": "/notes/1015677"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2280287", "RefComponent": "FI-GL-REO", "RefTitle": "Excel Upload for first level objects in Profit Center Reorganization with New General Ledger", "RefUrl": "/notes/2280287 "}, {"RefNumber": "858363", "RefComponent": "EC-PCA", "RefTitle": "Organizing/reorganizing Profit Center Accounting", "RefUrl": "/notes/858363 "}, {"RefNumber": "141009", "RefComponent": "EC-PCA-ACT", "RefTitle": "Resetting new profit center for PO", "RefUrl": "/notes/141009 "}, {"RefNumber": "580216", "RefComponent": "SD-SLS-GF-CO", "RefTitle": "Profit center cannot be changed if partial billing document exists", "RefUrl": "/notes/580216 "}, {"RefNumber": "117544", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Changing the profit center in the PM/SM order", "RefUrl": "/notes/117544 "}, {"RefNumber": "189325", "RefComponent": "CO-OM-OPA-F", "RefTitle": "Info: Settlement rule for settling variances into CO-PA", "RefUrl": "/notes/189325 "}, {"RefNumber": "1534197", "RefComponent": "CA-LT-CNV", "RefTitle": "SAP LT: Transformation Solution - Profit Center Reorganization", "RefUrl": "/notes/1534197 "}, {"RefNumber": "79847", "RefComponent": "SD-SLS-GF-CO", "RefTitle": "Update of profitability segment in sales document", "RefUrl": "/notes/79847 "}, {"RefNumber": "174512", "RefComponent": "CO-PA-ACT", "RefTitle": "Profitability segment: Alternative profit center", "RefUrl": "/notes/174512 "}, {"RefNumber": "1015677", "RefComponent": "SD-SLS-GF-CO", "RefTitle": "Profit center of billing document for inter-company process", "RefUrl": "/notes/1015677 "}, {"RefNumber": "107698", "RefComponent": "EC-PCA-ACT-PE", "RefTitle": "Structure of receivables/payables in classic Profit Center Accounting (PCA)", "RefUrl": "/notes/107698 "}, {"RefNumber": "173798", "RefComponent": "EC-PCA-MD", "RefTitle": "User exit for PCA substitution", "RefUrl": "/notes/173798 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}