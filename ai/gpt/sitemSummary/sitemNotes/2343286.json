{"Request": {"Number": "2343286", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 412, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018750532017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002343286?language=E&token=49E2E0828F6ED1C90A75634E7BDA1611"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002343286", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2343286"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "EPM-BPC-BW4-PLA"}, "SAPComponentKeyText": {"_label": "Component", "value": "BPC/4 - Planning Enginee"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Enterprise Performance Management", "value": "EPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Planning and Consolidation", "value": "EPM-BPC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EPM-BPC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BPC/4", "value": "EPM-BPC-BW4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EPM-BPC-BW4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BPC/4 - Planning Enginee", "value": "EPM-BPC-BW4-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EPM-BPC-BW4-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2343286 - Planning on BW/4HANA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use planning in BW4HANA. You need the Business Object Planning and Consolidation (BPC) license and product for this.</p>\r\n<p>You might have used planning in BW with integrated planning (BW-IP), HANA optimized wiht planning application kit (PAK) or with BPC before (see notes <span class=\"urTxtStd\" style=\"white-space: nowrap;\">2061095, <span class=\"urTxtStd\" style=\"white-space: nowrap;\">1637199, </span><span class=\"urTxtStd\" style=\"white-space: nowrap;\">1919631)</span></span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BPC, Business Planning and Consolidation, integrated planning, BW-IP, PAK, Planning Application Kit, BW-BPS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have installed BW4HANA. Now you want to also leverage planning similar to Bw-IP, PAK or BPC on Netweaver.</p>\r\n<p>&#160;You need to install also the BPC4HANA Addon. All kind of planning on BW4 requires the 'SAP BusinessObjects Planning and Consolidation, version for BW/4HANA' license.</p>\r\n<p>Planning solutions based on BW-BPS are not supported in BW/4HANA and have to be migrated manually to a BPC embedded model.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Following switches exist to de-/activate planning in BPC/4HANA which requires the above license:</p>\r\n<ul>\r\n<li>Main switch to generally activate&#160;deep HANA integration: table view RSPLS_HDB_ACT in SM30.&#160;If not yet existing, create a new entry for&#160;\"Deep HANA Integration Active\"&#160;(HANA_ACT) and mark the checkbox 'Function Active'&#160;in order to activate the ABAP Planning Applications KIT.</li>\r\n</ul>\r\n<p>HANA optimized processing can in also the controlled by:</p>\r\n<ul>\r\n<li>In rare cases some scenarios might cause problems when executed in HANA. Thus the table view RSPLS_HDB_ACT_IP in SM30 can be used to deactivate the ABAP Planning Applications KIT for individual InfoProviders. Choose the real-time InfoCube, direct update DSO or aDSO you&#160;do not want to use for HANA planning. This feature is available in order to guarantee a smooth transition from existing BW-IP scenarios and to avoid additional roundtrips due to the restrictions mentioned below.&#160;<br /><br />Every&#160;data store object&#160;must be HANA optimized to run in HANA.&#160;In case of&#160; &#160;CompositeProvider all planning enabled part providers must&#160;be HANA enabled (not de -activated in the table mentioned above) and also HANA optimized. One disabled or not HANA optimized InfoProvider disables the entire&#160; CompositeProvider for HANA processing. By default all InfoProviders not mentioned in the table view RSPLS_HDB_ACT_IP are HANA enabled. It is only necessary to add an InfoProvider when it should be HANA disabled. This can be done by adding the InfoProvider to the table and de-selecting the &#8216;Functn. Active&#8217; checkbox.</li>\r\n</ul>\r\n<ul>\r\n<li>There is a global &#8220;emergency exit switch&#8221; that can be set using report SAP_RSADMIN_MAINTAIN with parameter object RSPLS_HDB_PROCESSING_OFF. This switch should only be used in rare special circumstances.<br />Possible values are:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'F': Force Off&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;No HANA optimized processing, cannot be overruled any user</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'X': Off&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;No HANA optimized processing, but can be overruled for single users for special purposes</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Not set: switch inactive (default and usual value) <br />HANA processing will run on the intended planning function etc., but can be switched off for single users (traces, comparison etc.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>For project studies/PoC or special tasks like special process chains one can set a user specific PAK activation switch: Set/Get-Parameter RSPLS_HDB_SUPPORT (set in transaction SU01)<br />Possible Values are:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'HDB_ON': HANA optimized processing when possible also if global switch set&#160;&#160; 'X' (OFF) .</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'HDB_OFF': HANA optimized processing disabled independently from the global setting.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Not set: inactive, general system settings are used (including the global switch)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Trace information like whether a planning function is executed HANA optimized or the information about changed and created records are only displayed and written to the log when the user parameter RS_DEBUGLEVEL is set to 2 (or higher).</li>\r\n<li>If you use transaction RSPLAN and run a step in a planning sequence by pressing 'Execute Step with Trace' (as opposed to 'Execute Step') the function currently still runs in the IP (ABAP) runtime since we need to retrieve information for a detailed display. See knowledge base article 1911914 and 2)</li>\r\n</ul>\r\n<p><br />&#160;<br />Below we provide a list of features which are supported to run fully in HANA and also a list of features which are still executed in ABAP application server. Those will cause an additional data round trip and might harm the performance.</p>\r\n<p>Also a list of functions is added which are not supported on HANA. We have been working on enabling further functions in HANA (and continue to do so) so this list is subject to change. Some of the topics refer to a comment at the end of the note indicating the potential to eliminate the restrictions over time. In order to check whether a scenario in principle can run HANA optimized we provide the report RSPLS_PLANNING_ON_HDB_ANALYSIS.&#160; .</p>\r\n<p>1. List of features which run fully in HANA</p>\r\n<ul>\r\n<li>Following Planning Functions are only executed in HANA:</li>\r\n<ul>\r\n<li>Planning Function Type: 0RSPL_COPY&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Copy<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_REPOST&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160; Repost<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_REVALUATION&#160;&#160;&#160;&#160;&#160;&#160; Revaluation<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_DELETE&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Delete<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_DELETE_CR&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Deletion of Invalid Combinations <br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_SET_VALUES&#160;&#160;&#160;&#160;&#160;&#160;&#160; Set Key Figure Values<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_DISTR_REFDATA&#160; Distribution by Reference Data<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_CREATE_CR&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;Generate Combinations<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_REPOST_CR&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Repost on Basis of Characteristic Relationship <br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_DISTR_KEY&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Distribution with Keys &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br />&#160;</li>\r\n<li>Planning Function Type: 0RSPL_FORMULA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;Formula<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; (see restrictions below)</li>\r\n<li>Planning Function Type: 0RSPL_UNIT_CONV&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Unit Conversion</li>\r\n<li>Planning Function Type: 0RSPL_CURR_CONV&#160;&#160;&#160;&#160;&#160;&#160;&#160; Currency Translation&#160;</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>delta mode flag switched on in planning functions is supported. However the determination of the filter is done in ABAP and can cause more time then executing all records in HANA. It should therefore be only used for functional reasons and not due to better performance as in the classic IP case</li>\r\n<li>Disaggregation in the Query is executed in HANA if</li>\r\n<ul>\r\n<li>the query is modeled directly on the aggregation level</li>\r\n<li>for all restricting characteristics all contributing key figures are only restricted to one single value <br />&#160;</li>\r\n</ul>\r\n<li>Following characteristic relationships are supported to be fully checked in HANA</li>\r\n<ul>\r\n<li>Characteristic relationship of type attribute</li>\r\n<li>Characteristic relationship of type DSO</li>\r\n<li>Characteristic relationships of type hierarchy&#160;</li>\r\n<li>Characteristic relationship of type transient. This is only available through the ABAP BICS interface<br />&#160;</li>\r\n</ul>\r\n<li>Following data slices are supported to be fully checked in HANA</li>\r\n<ul>\r\n<li>Data slices of type selection&#160; <br />&#160;</li>\r\n</ul>\r\n<li>Logging BADI&#160; <br />&#160;</li>\r\n<li>Key-figures in the InfoCube or aDSO with aggregate &#8216;SUM&#8217;. For direct update DSO we also allow &#8216;NO2&#8217;. The usage of MIN/MAX in read only key figure.</li>\r\n<li>&#160;In this case where the model allows the algorithm to fully execute in HANA also the data of the planning buffer only resists in HANA (see above). Then also &#160;the save of plan buffers is fully executed within HANA only.</li>\r\n</ul>\r\n<p>2. List of features still executed in ABAP application server with data round trip and not fully HANA optimized</p>\r\n<ul>\r\n<li>Planning:</li>\r\n<ul>\r\n<li>Own planning function type: any planning function exit as own type as long as no interface IF_RSPLFA_SRVTYPE_TREX_EXEC or IF_RSPLFA_SRVTYPE_TREX_EXEC_R is implemented leveraging SQL Script for HANA processing. See Standard BW4 Documentation or note 1870369.</li>\r\n<li>Planning function type: 0RSPL_FORECASTING&#160;&#160; Forecast<br />As workaround one can use a SQL Script based implementation (see footnote 1) which includes some PAL functions<br />(See <a target=\"_blank\" href=\"http://help.sap.com/hana/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf\">http://help.sap.com/HANA/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf</a> and <a target=\"_blank\" href=\"http://www.saphana.com/community/hana-academy/#predictive-analytics-library\">http://www.sapHANA.com/community/HANA-academy/#predictive-analytics-library</a>)<br />&#160;&#160;</li>\r\n</ul>\r\n<li>Planning function type 'FOX' does not support the following commands and is in those cases executed in the application server</li>\r\n<ul>\r\n<li>CALL FUNCTION (1)</li>\r\n<li>ABAP BREAK-POINT command is ignored</li>\r\n<li>FOX key words are not supported for other usage like variable names. E.g. DATA I TYPE I is not allowed but DATA J TYPE I is allowed.</li>\r\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on standard time&#160;characteristics like 0CALYEAR or characteristic&#160;referencing those</li>\r\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on characteristics which don't have a master data table, which is a BW object like HANA View based info objects.</li>\r\n<li>Using a hierarchy node variable in VARC or similar statements</li>\r\n<li>Using InfoObjects&#160;outside the aggregation level (e.g. in DATA statements)</li>\r\n<li>The FOX features &#8216;modularization&#8217; or &#8216;forms&#8217;&#160; are currently not yet supported in PAK and lead to the processing in ABAP application server.&#160;</li>\r\n<li>The new FOX feature &#8216;directly reading from a non-input enabled DSO &#8216; &#160;is&#160; supported to run HANA optimized. Only in case the DSO contains non-key fields the the exectuion however is still on the ABAP stack.&#160;</li>\r\n<li>The feature of transient attributes&#160;is not be supported to run in HANA for time dependent attributes in FOX (e.g. ATRV). Then the FOX is processed on ABAP side.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160; &#160;In general a HANA check of&#160;FOX exit in the Fox Editor</p>\r\n<ul>\r\n<li>If conditions exist on a planning function and</li>\r\n<ul>\r\n<li>More on has more than&#160;1 condition on this planning function or</li>\r\n<li>The global filter contains selection on attributes, but the condition contains selection on the basic characteristics or</li>\r\n<li>The filter contains selections on hierarchy nodes and the condition has selections on the characteristic the hierarchy is defined on or</li>\r\n<li>The selection contains selections which are not single values restriction and those selections are not one by the same in the global filter.<br />&#160;</li>\r\n</ul>\r\n<li>Disaggregations in the Query are executed not in the database if</li>\r\n<ul>\r\n<li>Planning model uses MultiProvider on top of an aggregation level</li>\r\n<li>A formula is used as reference key figure for disaggregation</li>\r\n<li>The key figure is restricted to multiple values for a given characteristic except several single values. E.g. intervals, or hierarchy nodes in the restriction lead to execution in the application server.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Here the additional impact of a round trip is not as considerable as for planning functions. Only the generated data has to be pushed to the database.</p>\r\n<p>&#160;</p>\r\n<p>3. List of features which cannot be executed with the ABAP applications planning KIT in HANA and leads to switch to BW-IP (for the entire model, not a single step)</p>\r\n<ul>\r\n<li>TIMS &amp; DATS key-figures in the InfoCube. We recommend to use data type DEC and instead of TIMS or DATS</li>\r\n</ul>\r\n<ul>\r\n<li>Master data access of type Own Implementation or Remote (Direct Access).&#160;We allow (as exemption) characteristics referencing to standard BW time characteristics, source system (0SOURSYSTEM) or InfoProvider (0INFOPROV) when using their standard implementations.</li>\r\n</ul>\r\n<ul>\r\n<li>Virtual master data, including hierarchies and technical master data which are not persisted in tables. This also affects compounded characteristics leveraging those master data. Examples are InfoObjects that exist in BW but without stored data in the InfoObject tables. Exceptions are time and system characteristics which also work in the HANA optimized case.</li>\r\n</ul>\r\n<ul>\r\n<li>Virtual and referencing key-figures</li>\r\n</ul>\r\n<ul>\r\n<li>Transient characteristics</li>\r\n</ul>\r\n<ul>\r\n<li>Certain constraints by characteristic relationship cannot be checked in HANA yet. This includes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>characteristic relationships of type EXIT as long as the interface IF_RSPLS_CR_EXIT_HDB is not implemented. One can either leveraging SQL Script for HANA processing or use ABAP as fall back (see note 1956085 - BW-IP (PAK): ABAP as fallback for exit characteristic relations and data slices on SAP HANA). See footnote (1)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Certain constraints by data slices cannot be checked in HANA yet. This includes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>data slices of type EXIT as long as the interface IF_RSPLS_DS_EXIT_HDB is not implemented leveraging SQL Script for HANA processing. See footnote (1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CP-Problem as explained in the long text of message BRAIN 313 (see note 2095418)&#160;</li>\r\n<li>HANA views with placeholder in virtual providers or as part providers in a CompositeProvider</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>Footnote</p>\r\n<ul>\r\n<ul>\r\n<li>(1) By conception, we will not be able to support the full flexibility of an ABAP EXIT within HANA, in particular, when other ABAP sources are leveraged in the implementation. However, there are EXIT implementations using SQL script that can be executed in HANA.&#160;&#160; &#160;</li>\r\n</ul>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029075)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022158)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002343286/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002343286/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2061095", "RefComponent": "EPM-BPC-NW", "RefTitle": "SAP Business Planning & Consolidation 10.1 NW SP05 Central Note", "RefUrl": "/notes/2061095"}, {"RefNumber": "1919631", "RefComponent": "BW-PLA-BPC", "RefTitle": "Activating the BPC embedded", "RefUrl": "/notes/1919631"}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2443189", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL - Business Planning & Consolidation", "RefUrl": "/notes/2443189 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DW4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}