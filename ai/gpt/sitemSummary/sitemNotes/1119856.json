{"Request": {"Number": "1119856", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 634, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016425362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001119856?language=E&token=310BD26C9B6D225CFC3CC8133CFC5FA2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001119856", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001119856/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1119856"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.06.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-OCS-SPA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Support package tools for ABAP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)", "value": "BC-UPG-OCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Support package tools for ABAP", "value": "BC-UPG-OCS-SPA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS-SPA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1119856 - Description, tips and tricks for Attribute Change Packages"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you use the Support Package Manager (SPAM) or the Add-On Installation Tool (SAINT), or when reading notes, you notice the term <B>Attribute Change Package (ACP)</B>, and you are now looking for information about this package type.<br />This note describes the technical background of this new package type, and provides you with tips and tricks for using these packages.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Attribute Change Package, ACS, SPAM, Support Package Manager, SAINT, Add-On Installation Tool, Support Package, Add-On Installation Package, Add-On Upgrade Package<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>To subsequently deliver and correct import attributes of ABAP OCS packages (Support Packages, Add-On Installation Packages, Add-On Upgrade Packages) that have already been released, we have introduced the new package type <B>Attribute Change Package</B> with SPAM/SAINT Version 0025.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>Technical background:</B><br />Attribute Change Package is a new technical OCS package type. It contains only attributes (for example, import attributes) of OCS packages. ACPs are used to modify attributes of OCS packages (for example, for enhancing the import prerequisites to allow new system statuses as the basis for the import) so that the customer does not have to download an OCS package again if this package was already downloaded from SAP Service Marketplace. Since ACPs are only a few kilobytes in size, this saves download time and network resources.<br />In contrast to other OCS package types, ACPs do not have any content in the form of transport objects. As a result, you do not have to import them. Instead, they already take effect when you upload them into the system (using the functions \"Load From Application Server\" or \"Load From Front End\"). Therefore, ACPs are not displayed in the dialogs for the queue calculation or in the OCS package directory.<br />There is always only one ACP for each software component version. This ACP contains the import attributes of all the OCS packages of this software component version that were modified since their release. The name of an ACP is created from the name of the software component version. The first 10 characters of the ACP name contain the name of the software component. If this name is not 10 characters long, equal signs are used to make up the remaining characters. The version of the software component starts as of the eleventh character of the ACP name.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For example:</p> <UL><UL><LI>ACP for OCS packages of the component version SAP_APPL 600: SAP_APPL==600</LI></UL></UL> <UL><UL><LI>ACP for OCS packages of the component version IS-OIL 600: IS-OIL====600</LI></UL></UL> <p><br />If attributes of an OCS package that has not yet been modified are to be changed, or if attributes of an OCS package that have already been modified are to be changed again, a new version of the relevant ACP is created. In addition to the attributes of the current OCS package that was modified, this new version contains all the attributes of the packages that were contained in the old version of the ACP. Therefore, you must load the latest version of an ACP into your system. If the EPS inbox contains several versions of an ACP, the OCS tools 'Support Package Manager' and 'Add-On Installation Tool' ensure that only the attributes that are contained in the ACP with the highest version are loaded and processed in the system. In this case, no user interaction is required. The order in which normal OCS packages and the ACPs that modify them are placed in the EPS inbox and in which they are uploaded into the system is also irrelevant because the upload logic in the Support Package Manager and in the Add-On Installation Tool ensures that the latest attribute versions are used in any constellation.<br /><br /><B>FAQ, Tips, Tricks</B></p> <UL><LI><B>How often are ACPs or new versions of ACPs created?</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ACPs or new versions of ACPs are created only if required. Therefore, there are no fixed dates. In general, the availability of an ACP for a software component version is made known in a note, for example, in the installation note or in the maintenance strategy note for this software component version.</p> <UL><LI><B>Where do I obtain ACPs?</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ACPs of a software component are usually delivered in an integrated manner in the CAR/SAR archive of a normal delivery package (Support Package, Add-On Installation Package and so on). In addition, you can also download ACPs directly from SAP Service Marketplace. Since ACPs are not normal Support Packages, they are not listed in the overview of the Support Packages belonging to a software component version. To find an ACP, use the search function and search for the name of the ACP.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you schedule maintenance operations with the maintenance optimizer in the Solution Manager, required ACPs are automatically added to the download basket.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In exceptional cases, ACPs may be attached to a note in the form of their own CAR/SAR archive. Among other things, this note describes why or in which situation you require the attached ACPs, and how to use them.</p> <UL><LI><B>How do I use the integrated ACPs that are delivered?</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use the CAR/SAR archive of the normal delivery package as usual. Therefore, load it into the system by choosing the function \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Front End\" in the Support Package Manager, or unpack it manually using the SAPCAR utility on the application server, and then choose the function \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Application Server\" in the Support Package Manager. The additional ACP contained in the archive is processed automatically.</p> <UL><LI><B>How do I use ACPs that were loaded directly from SAP Service Marketplace?</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use the SAR archive of the ACP as you are used to by a normal delivery package. Therefore, load it into the system by choosing the function \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Front End\" in the Support Package Manager, or unpack it manually using the SAPCAR utility on the application server, and then choose the function \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Application Server\" in the Support Package Manager.</p> <UL><LI><B>How do I use the ACPs that are attached to a note?</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The simplest option is to save the CAR/SAR archives (that are attached to the note) to the Desktop PC. In the Support Package Manager, you can then use the function \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Front End\" to load these archives into the system, where they are processed automatically.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As an alternative, you can also transport the CAR/SAR archives to the application server and unpack them there using the SAPCAR utility. You must place the .PAT files that are contained in the archives in the EPS/in directory. You must then upload them by choosing \"Support Package\"-&gt;\"Load Packages\"-&gt;\"From Application Server\" in the Support Package Manager.</p> <UL><LI><B>When uploading, the system displays the error message \"Unknown OCS file format '06' ; SPAM/SAINT Update required\".</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You require at least SPAM/SAINT Version 0025 so that ACPs are processed correctly. Import the current SPAM/SAINT version.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the specified ACPs are not relevant for your system (for example, because you have a common EPS inbox for different systems), you can also continue to work with the SPAM/SAINT version that is currently installed. The error message has no further effects on the other functions in the system.</p> <UL><LI><B>The import attributes of OCS packages are not modified as expected or as described in the note.</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Only import attributes of OCS packages that have not yet been imported are changed. OCS packages that have already been completely imported remain unchanged. For these OCS packages, a modification of the import attributes would not make any sense.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028597)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019419)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001119856/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001119856/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119856/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119856/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119856/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119856/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119856/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119856/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001119856/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "822379", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems with support packages in SAP NW 7.0x AS ABAP", "RefUrl": "/notes/822379"}, {"RefNumber": "1589409", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Vendor key handling in EHP2, EHP3 for NW 7.0, EHP1 NW 7.3, NW 7.4", "RefUrl": "/notes/1589409"}, {"RefNumber": "1565198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1565198"}, {"RefNumber": "1560383", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1560383"}, {"RefNumber": "1472649", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Attribute Change Package 09 for FINBASIS 604.", "RefUrl": "/notes/1472649"}, {"RefNumber": "1326381", "RefComponent": "BC-EHP-INS-TLA", "RefTitle": "Attribute Change Packages (ACP) do not work in the EHPI", "RefUrl": "/notes/1326381"}, {"RefNumber": "1252111", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 640", "RefUrl": "/notes/1252111"}, {"RefNumber": "1247785", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 620", "RefUrl": "/notes/1247785"}, {"RefNumber": "1247361", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 700", "RefUrl": "/notes/1247361"}, {"RefNumber": "1246567", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 4.6C", "RefUrl": "/notes/1246567"}, {"RefNumber": "1225060", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to IS-M 603 stops due to conflict with EXX-SE 602", "RefUrl": "/notes/1225060"}, {"RefNumber": "1158886", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP REAL ESTATE CEE 110_600 add-on and ERP Enhancement Pack", "RefUrl": "/notes/1158886"}, {"RefNumber": "1150868", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP IS-UT CEE 600 add-on and ERP Enhancement Packages", "RefUrl": "/notes/1150868"}, {"RefNumber": "1143354", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP Invoice Management by Open Text", "RefUrl": "/notes/1143354"}, {"RefNumber": "1140663", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-PS-XT 6.0 and ERP Enhancement Packages", "RefUrl": "/notes/1140663"}, {"RefNumber": "1118803", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC 600 Support Packages and ERP enhancement packages", "RefUrl": "/notes/1118803"}, {"RefNumber": "1100222", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation and Delta Upgrade of SLL-LEG 720", "RefUrl": "/notes/1100222"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3335194", "RefComponent": "BC-UPG-MP", "RefTitle": "There is no Attribute Change Package file generated  in the stack xml file", "RefUrl": "/notes/3335194 "}, {"RefNumber": "2932910", "RefComponent": "BC-UPG-ADDON", "RefTitle": "How to find the installation / upgrade note for a package, its import conditions and the object list.", "RefUrl": "/notes/2932910 "}, {"RefNumber": "2918882", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAINT: Installation of ODTFINCC 600 in S/4HANA", "RefUrl": "/notes/2918882 "}, {"RefNumber": "2864197", "RefComponent": "BC-UPG-RDM", "RefTitle": "Error in Phase UNINSTALL_PREPARE : Add-on \"XXX\" Release \"YYY\" is not flagged as uninstallable", "RefUrl": "/notes/2864197 "}, {"RefNumber": "2830310", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "'stack release = source release where KEEP is not allowed' during upgrade to SAP S/4HANA 1909", "RefUrl": "/notes/2830310 "}, {"RefNumber": "2767018", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Component UIX01CA1 is already retrofitted into component version UIBAS001 Release <version>", "RefUrl": "/notes/2767018 "}, {"RefNumber": "1827677", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS package XXXXXXXX does not match the current software component vector", "RefUrl": "/notes/1827677 "}, {"RefNumber": "2536267", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Stack file is invalid: component '<name>' (stack release = source release, but KEEP not allowed) during S/4HANA 1709 conversion", "RefUrl": "/notes/2536267 "}, {"RefNumber": "2773103", "RefComponent": "BC-UPG-OCS", "RefTitle": "DDIC_ACTIVATION errors while updating SMERP add-on", "RefUrl": "/notes/2773103 "}, {"RefNumber": "2698762", "RefComponent": "BC-UPG-PRP", "RefTitle": "Could not determine EPS parcel for OCS Package for <package name>", "RefUrl": "/notes/2698762 "}, {"RefNumber": "2688217", "RefComponent": "BC-UPG-NA", "RefTitle": "SPAM/SAINT: ST-A/PI or ST-PI Prerequisite check for note 1487337  fails in CHECK_REQUIREMENTS  step", "RefUrl": "/notes/2688217 "}, {"RefNumber": "2593702", "RefComponent": "BNS-ARI-SE-ERP", "RefTitle": "During installation of Ariba Business Suite Integration (add-on), you encounter \"Non-permitted release of add-on SAP_BS_FND installed\"", "RefUrl": "/notes/2593702 "}, {"RefNumber": "2589137", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "IW_FNDGC 100: Equivalence level for start release not reached", "RefUrl": "/notes/2589137 "}, {"RefNumber": "2573311", "RefComponent": "BC-UPG-ADDON", "RefTitle": "OCS package SAPK-100AGINPASEIN does not match the current software component vector", "RefUrl": "/notes/2573311 "}, {"RefNumber": "2355372", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-on could not be installed due to Message no. TN688", "RefUrl": "/notes/2355372 "}, {"RefNumber": "2473294", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "SPAM queue check failed for SAPKE60841", "RefUrl": "/notes/2473294 "}, {"RefNumber": "2399266", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Error \"<Component Version> is not valid for the target constellation\" happens in SAINT", "RefUrl": "/notes/2399266 "}, {"RefNumber": "3153571", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on LSPUI", "RefUrl": "/notes/3153571 "}, {"RefNumber": "3412681", "RefComponent": "PPM-PRO", "RefTitle": "Uninstalling Add-On CPRXRPM", "RefUrl": "/notes/3412681 "}, {"RefNumber": "3154627", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on LSP", "RefUrl": "/notes/3154627 "}, {"RefNumber": "3418514", "RefComponent": "TM-LOC-RU", "RefTitle": "Uninstalling ERP Add-On TMRU", "RefUrl": "/notes/3418514 "}, {"RefNumber": "3400631", "RefComponent": "XX-PROJ-CDP-TEST-713", "RefTitle": "Uninstalling Add-On HEMCRM", "RefUrl": "/notes/3400631 "}, {"RefNumber": "3400697", "RefComponent": "XX-PROJ-CDP-TEST-713", "RefTitle": "Uninstalling Add-On HEMECC", "RefUrl": "/notes/3400697 "}, {"RefNumber": "2856944", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on ACMTM", "RefUrl": "/notes/2856944 "}, {"RefNumber": "3400682", "RefComponent": "IS-H-MOB", "RefTitle": "Uninstalling ISHMOBIL", "RefUrl": "/notes/3400682 "}, {"RefNumber": "3104283", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on BWBEKA", "RefUrl": "/notes/3104283 "}, {"RefNumber": "3006487", "RefComponent": "PA-ER-LOC", "RefTitle": "LOCFEREC - Add-on Uninstallation", "RefUrl": "/notes/3006487 "}, {"RefNumber": "2889616", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on PRATVUI", "RefUrl": "/notes/2889616 "}, {"RefNumber": "3345113", "RefComponent": "FS-AM", "RefTitle": "Uninstalling UICB4H", "RefUrl": "/notes/3345113 "}, {"RefNumber": "3331164", "RefComponent": "FS-RI-UI", "RefTitle": "Uninstalling UIFSRI", "RefUrl": "/notes/3331164 "}, {"RefNumber": "3328095", "RefComponent": "XX-PROJ-CDP-488", "RefTitle": "Uninstalling Add-On SAAP", "RefUrl": "/notes/3328095 "}, {"RefNumber": "3322875", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling OEE_ERP Add on for SAP MII Product", "RefUrl": "/notes/3322875 "}, {"RefNumber": "3312057", "RefComponent": "MM-IV-HUB-CIM", "RefTitle": "Uninstalling HUBERPI2", "RefUrl": "/notes/3312057 "}, {"RefNumber": "1720483", "RefComponent": "BC-UPG-ADDON", "RefTitle": "TEMPLATE: Release strategy and Maintenance Information for the ABAP add-on [Add-on]", "RefUrl": "/notes/1720483 "}, {"RefNumber": "3295853", "RefComponent": "XX-PROJ-CDP-307", "RefTitle": "RCS Enhanced Security Issuance Management: Uninstallation of the WPV Add-On", "RefUrl": "/notes/3295853 "}, {"RefNumber": "3292693", "RefComponent": "XX-PROJ-CDP-490", "RefTitle": "Uninstallation Information Note for Add-On: DSFW", "RefUrl": "/notes/3292693 "}, {"RefNumber": "3283116", "RefComponent": "IS-A-DBM", "RefTitle": "Uninstalling Add-On DFD", "RefUrl": "/notes/3283116 "}, {"RefNumber": "3260035", "RefComponent": "LOD-SF-INT", "RefTitle": "Uninstalling Add-On SFIECPEP", "RefUrl": "/notes/3260035 "}, {"RefNumber": "3269225", "RefComponent": "XX-PROJ-CDP-309", "RefTitle": "Uninstalling Add-On AOCSO", "RefUrl": "/notes/3269225 "}, {"RefNumber": "3268164", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-On ARBCI1 - ARIBA CLOUD INT SAP ERP 1.0", "RefUrl": "/notes/3268164 "}, {"RefNumber": "3243916", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on UIMABAC", "RefUrl": "/notes/3243916 "}, {"RefNumber": "3256795", "RefComponent": "XX-PROJ-CDP-306", "RefTitle": "Uninstalling of Add-On OERRR", "RefUrl": "/notes/3256795 "}, {"RefNumber": "3252062", "RefComponent": "IS-PHA-REG", "RefTitle": "Uninstalling Add-On UIIDMP", "RefUrl": "/notes/3252062 "}, {"RefNumber": "3249725", "RefComponent": "IS-PHA-REG", "RefTitle": "Uninstalling Add-On IDMP", "RefUrl": "/notes/3249725 "}, {"RefNumber": "3243704", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-ons  'ARIBA CLOUD INT SAP ERP 1.0', 'ARIBA CLOUD INT SAP S/4HANA 1.0' and 'ARIBA CLOUD INT SAP ERP SELLER 1.0'", "RefUrl": "/notes/3243704 "}, {"RefNumber": "3224468", "RefComponent": "FS-CYT", "RefTitle": "Deinstallation CYT 800", "RefUrl": "/notes/3224468 "}, {"RefNumber": "3230193", "RefComponent": "TM-LOC-RU", "RefTitle": "Uninstalling Add-On TMRUS4H", "RefUrl": "/notes/3230193 "}, {"RefNumber": "3227080", "RefComponent": "FS-RI", "RefTitle": "Uninstalling FS-RI", "RefUrl": "/notes/3227080 "}, {"RefNumber": "3224088", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-CRM60", "RefUrl": "/notes/3224088 "}, {"RefNumber": "3107792", "RefComponent": "FS-FPS", "RefTitle": "Uninstalling S4FPSL", "RefUrl": "/notes/3107792 "}, {"RefNumber": "3219427", "RefComponent": "LO-AGR-LSP", "RefTitle": "Uninstalling Add-On LSPUI", "RefUrl": "/notes/3219427 "}, {"RefNumber": "3219393", "RefComponent": "LO-AGR-LSP", "RefTitle": "Uninstalling Add-On LSP", "RefUrl": "/notes/3219393 "}, {"RefNumber": "3217902", "RefComponent": "MM-PUR-HUB-CTR", "RefTitle": "Uninstalling HUBS4IC", "RefUrl": "/notes/3217902 "}, {"RefNumber": "3213576", "RefComponent": "XX-PROJ-CDP", "RefTitle": "Uninstalling Add-On GEP (Pool Car Management)", "RefUrl": "/notes/3213576 "}, {"RefNumber": "3207198", "RefComponent": "IS-PS-BFM", "RefTitle": "Uninstalling SAP Bulk Fuel Management 2.0 - Add-On OGFM", "RefUrl": "/notes/3207198 "}, {"RefNumber": "3197963", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-BW", "RefUrl": "/notes/3197963 "}, {"RefNumber": "3197899", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-SCM", "RefUrl": "/notes/3197899 "}, {"RefNumber": "3197942", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-CRM70", "RefUrl": "/notes/3197942 "}, {"RefNumber": "3197906", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-CRM", "RefUrl": "/notes/3197906 "}, {"RefNumber": "3170006", "RefComponent": "CA-DEC", "RefTitle": "Uninstalling DECSERMG", "RefUrl": "/notes/3170006 "}, {"RefNumber": "3146547", "RefComponent": "XX-PROJ-CDP-658", "RefTitle": "Uninstallation S4CARDPE", "RefUrl": "/notes/3146547 "}, {"RefNumber": "3146548", "RefComponent": "XX-PROJ-CDP-658", "RefTitle": "Uninstallation S4CARDCB", "RefUrl": "/notes/3146548 "}, {"RefNumber": "3167254", "RefComponent": "PLM-INT-TC", "RefTitle": "Uninstalling PLM SYST INTEG 2.0 FOR S/4HANA UIPLMSI", "RefUrl": "/notes/3167254 "}, {"RefNumber": "3159658", "RefComponent": "CA-GTF-AIF", "RefTitle": "Uninstalling BAEBOP", "RefUrl": "/notes/3159658 "}, {"RefNumber": "2998384", "RefComponent": "TM-ADP-CSL-OTC", "RefTitle": "Uninstalling add-on TMO2C", "RefUrl": "/notes/2998384 "}, {"RefNumber": "2998385", "RefComponent": "TM-ADP-CSL-NAO", "RefTitle": "Uninstalling add-on TMNAO", "RefUrl": "/notes/2998385 "}, {"RefNumber": "2999474", "RefComponent": "TM-ADP-CSL-L2A", "RefTitle": "Uninstalling add-on TML2A", "RefUrl": "/notes/2999474 "}, {"RefNumber": "2998564", "RefComponent": "TM-ADP-CSL", "RefTitle": "Uninstalling add-on TMCSLUI", "RefUrl": "/notes/2998564 "}, {"RefNumber": "2999445", "RefComponent": "TM-ADP-CSL", "RefTitle": "Uninstalling Add-on TMCSL", "RefUrl": "/notes/2999445 "}, {"RefNumber": "3047034", "RefComponent": "GRC-DC", "RefTitle": "Uninstalling SDC-ECC: SAP Data Custodian Application Control for SAP ERP", "RefUrl": "/notes/3047034 "}, {"RefNumber": "3045811", "RefComponent": "GRC-DC", "RefTitle": "Uninstalling SDCAC - SAP Data Custodian Application Controls (SDC-ABAC)", "RefUrl": "/notes/3045811 "}, {"RefNumber": "3127872", "RefComponent": "XX-PROJ-CDP-529", "RefTitle": "Uninstalling EWMCP", "RefUrl": "/notes/3127872 "}, {"RefNumber": "3126063", "RefComponent": "LO-CMM-DC", "RefTitle": "Deal Capture: DMUI Uninstallation", "RefUrl": "/notes/3126063 "}, {"RefNumber": "3128706", "RefComponent": "CRM-SLC", "RefTitle": "Uninstalling SLCUI Add-On", "RefUrl": "/notes/3128706 "}, {"RefNumber": "3120149", "RefComponent": "CRM-SLC", "RefTitle": "Uninstalling SLCC Add-On", "RefUrl": "/notes/3120149 "}, {"RefNumber": "3106031", "RefComponent": "PLM-INT", "RefTitle": "Uninstalling ABAP Add-on PLMSI 100 (PLM System Integration - Extension Layer 100)", "RefUrl": "/notes/3106031 "}, {"RefNumber": "3105677", "RefComponent": "PLM-INT", "RefTitle": "Uninstalling ABAP Add-on PLMSIFND 100 (PLM System Integration - Foundation Layer 100)", "RefUrl": "/notes/3105677 "}, {"RefNumber": "3104219", "RefComponent": "GRC-FIO-SAC", "RefTitle": "Uninstalling LWMGRC02 100", "RefUrl": "/notes/3104219 "}, {"RefNumber": "3104993", "RefComponent": "GRC-FIO-SAC", "RefTitle": "Uninstalling LWMGRC03 100", "RefUrl": "/notes/3104993 "}, {"RefNumber": "3103667", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling IW_HDB", "RefUrl": "/notes/3103667 "}, {"RefNumber": "3103719", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling IW_BEP", "RefUrl": "/notes/3103719 "}, {"RefNumber": "3103674", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling GW_CORE", "RefUrl": "/notes/3103674 "}, {"RefNumber": "3103648", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling IW_FND", "RefUrl": "/notes/3103648 "}, {"RefNumber": "3099907", "RefComponent": "SCM-EM-MGR-ADM", "RefTitle": "Uninstalling SAP Event Management on SAP S/4HANA as Add-on on S/4HANA", "RefUrl": "/notes/3099907 "}, {"RefNumber": "3073903", "RefComponent": "EPM-SCP", "RefTitle": "Uninstallation of Add-On ANASCPM", "RefUrl": "/notes/3073903 "}, {"RefNumber": "3068639", "RefComponent": "XX-PROJ-CDP-524", "RefTitle": "Uninstalling IDXCH2", "RefUrl": "/notes/3068639 "}, {"RefNumber": "3067735", "RefComponent": "XX-PROJ-CDP-324", "RefTitle": "Uninstalling PREPAY", "RefUrl": "/notes/3067735 "}, {"RefNumber": "3068649", "RefComponent": "XX-PROJ-CDP-310", "RefTitle": "Uninstalling IDXAT2", "RefUrl": "/notes/3068649 "}, {"RefNumber": "3062389", "RefComponent": "IS-U-EEG", "RefTitle": "EEG-Billing: Deinstallation des Add-On ENBIL", "RefUrl": "/notes/3062389 "}, {"RefNumber": "3030112", "RefComponent": "TM-ADP-ML-ICR", "RefTitle": "Uninstalling ICR", "RefUrl": "/notes/3030112 "}, {"RefNumber": "3030174", "RefComponent": "TM-ADP-ML-ICR", "RefTitle": "Uninstalling ICRUI", "RefUrl": "/notes/3030174 "}, {"RefNumber": "3030191", "RefComponent": "SCM-EWM-FIO-ILO", "RefTitle": "Uninstalling ILOUI", "RefUrl": "/notes/3030191 "}, {"RefNumber": "3030372", "RefComponent": "SCM-EWM-ILO", "RefTitle": "Uninstalling ILO", "RefUrl": "/notes/3030372 "}, {"RefNumber": "3082662", "RefComponent": "CA-GTF-AIF", "RefTitle": "Uninstalling the Add-On AIFX", "RefUrl": "/notes/3082662 "}, {"RefNumber": "3075684", "RefComponent": "FS-FBS-CML-LWP", "RefTitle": "Uninstalling Add-On LOANSWPU", "RefUrl": "/notes/3075684 "}, {"RefNumber": "3075431", "RefComponent": "PLM-PDM", "RefTitle": "Uninstalling TDMI", "RefUrl": "/notes/3075431 "}, {"RefNumber": "2858136", "RefComponent": "LO-AGR-BT", "RefTitle": "Uninstalling ACMTM", "RefUrl": "/notes/2858136 "}, {"RefNumber": "3070081", "RefComponent": "CA-TDM", "RefTitle": "Objects needed for uninstallation of DMIS_CNT", "RefUrl": "/notes/3070081 "}, {"RefNumber": "3070079", "RefComponent": "LO-AGR-BT", "RefTitle": "Uninstalling ACMTMUI", "RefUrl": "/notes/3070079 "}, {"RefNumber": "3069809", "RefComponent": "FS-FBS-CML-LWP", "RefTitle": "Uninstalling Add-On LOANSWP", "RefUrl": "/notes/3069809 "}, {"RefNumber": "3032697", "RefComponent": "EPM-SA", "RefTitle": "Uninstalling Add-On ANAXSA", "RefUrl": "/notes/3032697 "}, {"RefNumber": "3059018", "RefComponent": "IS-U-EEG", "RefTitle": "EEG Billing: Uninstalling the add-on ENBI4", "RefUrl": "/notes/3059018 "}, {"RefNumber": "3036724", "RefComponent": "IS-PS-SBP", "RefTitle": "Uninstalling Add-On PBFBI", "RefUrl": "/notes/3036724 "}, {"RefNumber": "3049768", "RefComponent": "IS-PS-SBP", "RefTitle": "Uninstalling Add-On BPPS", "RefUrl": "/notes/3049768 "}, {"RefNumber": "2426707", "RefComponent": "IS-A-DBM", "RefTitle": "Uninstallation of Add-on DBM (Dealer Business Management)", "RefUrl": "/notes/2426707 "}, {"RefNumber": "3032105", "RefComponent": "PA-ER", "RefTitle": "Uninstalling SAP E-Recruiting for SAP S/4HANA", "RefUrl": "/notes/3032105 "}, {"RefNumber": "3025266", "RefComponent": "BC-MOB-DOE", "RefTitle": "Uninstalling CRMSPGWY Add-On", "RefUrl": "/notes/3025266 "}, {"RefNumber": "3024896", "RefComponent": "XX-PROJ-CDP-589", "RefTitle": "Uninstalling GRISG", "RefUrl": "/notes/3024896 "}, {"RefNumber": "3019307", "RefComponent": "XX-PROJ-CDP-176", "RefTitle": "Uninstalling EPAY", "RefUrl": "/notes/3019307 "}, {"RefNumber": "3018272", "RefComponent": "CA-RT-CAR", "RefTitle": "Uninstalling RTLPOSDM", "RefUrl": "/notes/3018272 "}, {"RefNumber": "3018271", "RefComponent": "CA-RT-SYN", "RefTitle": "Uninstalling RTLCONS", "RefUrl": "/notes/3018271 "}, {"RefNumber": "3018250", "RefComponent": "CA-RT-CAR", "RefTitle": "Uninstalling RTLCAR", "RefUrl": "/notes/3018250 "}, {"RefNumber": "3018248", "RefComponent": "CA-RT-AP", "RefTitle": "Uninstalling RTLRAP", "RefUrl": "/notes/3018248 "}, {"RefNumber": "3018247", "RefComponent": "CA-RT-PMR", "RefTitle": "Uninstalling RTLPROMO", "RefUrl": "/notes/3018247 "}, {"RefNumber": "3018245", "RefComponent": "CA-DDF-RT", "RefTitle": "Uninstalling RTLDDF", "RefUrl": "/notes/3018245 "}, {"RefNumber": "3018135", "RefComponent": "CA-DDF-RT", "RefTitle": "Uninstalling Add-On RTLDDF", "RefUrl": "/notes/3018135 "}, {"RefNumber": "3014719", "RefComponent": "XX-PROJ-CDP-774", "RefTitle": "Uninstalling ACMFPUI", "RefUrl": "/notes/3014719 "}, {"RefNumber": "3013717", "RefComponent": "IS-DFS-OF", "RefTitle": "Uninstalling ADFDIF", "RefUrl": "/notes/3013717 "}, {"RefNumber": "2998983", "RefComponent": "CRM-IPS-FRM", "RefTitle": "Uninstalling add-on FRMBI", "RefUrl": "/notes/2998983 "}, {"RefNumber": "3010466", "RefComponent": "PA-ER", "RefTitle": "Uninstalling SAP E-Recruiting", "RefUrl": "/notes/3010466 "}, {"RefNumber": "2995938", "RefComponent": "CRM-IPS-FRM", "RefTitle": "Uninstalling add-on FRMCM", "RefUrl": "/notes/2995938 "}, {"RefNumber": "2994020", "RefComponent": "SLL-LEG-FUN", "RefTitle": "Uninstalling SAP Fiori for SAP Global Trade Services", "RefUrl": "/notes/2994020 "}, {"RefNumber": "2991208", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling SAP S/4HANA integration with SAP Easy Document Management RCS", "RefUrl": "/notes/2991208 "}, {"RefNumber": "2987986", "RefComponent": "XX-PROJ-CDP-001", "RefTitle": "Uninstalling FB4U", "RefUrl": "/notes/2987986 "}, {"RefNumber": "2988020", "RefComponent": "XX-PROJ-CDP-001", "RefTitle": "Uninstalling FB4UUI", "RefUrl": "/notes/2988020 "}, {"RefNumber": "2985137", "RefComponent": "RE-FX-YC", "RefTitle": "Uninstalling RECA", "RefUrl": "/notes/2985137 "}, {"RefNumber": "2969579", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component GBX01HR5", "RefUrl": "/notes/2969579 "}, {"RefNumber": "2180598", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData components GBX01HR", "RefUrl": "/notes/2180598 "}, {"RefNumber": "2977299", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling FAAM 604 --- <PERSON><PERSON>", "RefUrl": "/notes/2977299 "}, {"RefNumber": "2970254", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling FAAM 604", "RefUrl": "/notes/2970254 "}, {"RefNumber": "2947416", "RefComponent": "FS-PE", "RefTitle": "Uninstalling PES4UI 100", "RefUrl": "/notes/2947416 "}, {"RefNumber": "2955487", "RefComponent": "FS-PE", "RefTitle": "Uninstalling PEUI 500", "RefUrl": "/notes/2955487 "}, {"RefNumber": "2964250", "RefComponent": "LOD-PER", "RefTitle": "FS-PER Rel 3.0 : Uninstalling SAP PaPM (NXI)", "RefUrl": "/notes/2964250 "}, {"RefNumber": "2960693", "RefComponent": "IOT-EDG-BEF", "RefTitle": "Uninstalling S4EF", "RefUrl": "/notes/2960693 "}, {"RefNumber": "2960853", "RefComponent": "XX-PROJ-CDP-487", "RefTitle": "Uninstalling AMSOG", "RefUrl": "/notes/2960853 "}, {"RefNumber": "2850986", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on PACG", "RefUrl": "/notes/2850986 "}, {"RefNumber": "2953399", "RefComponent": "OPU-ASA-EE", "RefTitle": "Uninstalling ASANWEE 100", "RefUrl": "/notes/2953399 "}, {"RefNumber": "2953056", "RefComponent": "OPU-ASA-FG", "RefTitle": "Uninstalling ASAFGEE 100", "RefUrl": "/notes/2953056 "}, {"RefNumber": "2949178", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling TMLSPERP 100", "RefUrl": "/notes/2949178 "}, {"RefNumber": "2939629", "RefComponent": "XX-PROJ-CDP-294", "RefTitle": "Uninstalling USC2B", "RefUrl": "/notes/2939629 "}, {"RefNumber": "2931283", "RefComponent": "XX-PROJ-CDP-551", "RefTitle": "Uninstalling add-on FGL", "RefUrl": "/notes/2931283 "}, {"RefNumber": "2931270", "RefComponent": "XX-PROJ-CDP-551", "RefTitle": "Uninstalling add-on FIF", "RefUrl": "/notes/2931270 "}, {"RefNumber": "2936344", "RefComponent": "CRM-IPS", "RefTitle": "Uninstalling DHSSW", "RefUrl": "/notes/2936344 "}, {"RefNumber": "2322477", "RefComponent": "XAP-SBC-BUI-ABA", "RefTitle": "Uninstallation of Add-on POA SBC", "RefUrl": "/notes/2322477 "}, {"RefNumber": "2806933", "RefComponent": "FS-XA", "RefTitle": "Uninstalling FSXAL", "RefUrl": "/notes/2806933 "}, {"RefNumber": "2806931", "RefComponent": "FS-XA", "RefTitle": "Uninstalling FSPOT", "RefUrl": "/notes/2806931 "}, {"RefNumber": "2831301", "RefComponent": "FS-XA", "RefTitle": "Uninstalling POCB4H", "RefUrl": "/notes/2831301 "}, {"RefNumber": "2915339", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS_UI5GT", "RefUrl": "/notes/2915339 "}, {"RefNumber": "2915371", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS_UI5", "RefUrl": "/notes/2915371 "}, {"RefNumber": "2915332", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS_NW", "RefUrl": "/notes/2915332 "}, {"RefNumber": "2914011", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS", "RefUrl": "/notes/2914011 "}, {"RefNumber": "2821406", "RefComponent": "FS-AM", "RefTitle": "Uninstalling CB4HANA", "RefUrl": "/notes/2821406 "}, {"RefNumber": "2916946", "RefComponent": "EPM-SA", "RefTitle": "Uninstalling Add-On OPMFND", "RefUrl": "/notes/2916946 "}, {"RefNumber": "2916759", "RefComponent": "GRC-SPM-SR", "RefTitle": "Uninstalling Add-On SR_CORE", "RefUrl": "/notes/2916759 "}, {"RefNumber": "2911053", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation configuration", "RefUrl": "/notes/2911053 "}, {"RefNumber": "2910825", "RefComponent": "LOD-FSN-AGT", "RefTitle": "Uninstalling add-on PSCW", "RefUrl": "/notes/2910825 "}, {"RefNumber": "2909138", "RefComponent": "SCM-YL", "RefTitle": "Uninstalling add-on SAPYL", "RefUrl": "/notes/2909138 "}, {"RefNumber": "2907724", "RefComponent": "LOD-FSN-AGT", "RefTitle": "Uninstalling add-on BSNAGT", "RefUrl": "/notes/2907724 "}, {"RefNumber": "2899706", "RefComponent": "XX-PROJ-CDP-584", "RefTitle": "Uninstallation of EWMCPS4 Add-On", "RefUrl": "/notes/2899706 "}, {"RefNumber": "2866277", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGGW", "RefUrl": "/notes/2866277 "}, {"RefNumber": "2866276", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGIF", "RefUrl": "/notes/2866276 "}, {"RefNumber": "2866282", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGCRM", "RefUrl": "/notes/2866282 "}, {"RefNumber": "2866260", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGBW", "RefUrl": "/notes/2866260 "}, {"RefNumber": "2866268", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGWDA", "RefUrl": "/notes/2866268 "}, {"RefNumber": "2894377", "RefComponent": "XX-PROJ-CDP-283", "RefTitle": "CBCOM: Uninstalling CBCOM 100", "RefUrl": "/notes/2894377 "}, {"RefNumber": "2891297", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling PRATVUI", "RefUrl": "/notes/2891297 "}, {"RefNumber": "2888899", "RefComponent": "GRC-UDS-DO", "RefTitle": "Uninstalling Addon UIMUI5", "RefUrl": "/notes/2888899 "}, {"RefNumber": "2888859", "RefComponent": "GRC-UDS-DO", "RefTitle": "Uninstalling Addon UISM", "RefUrl": "/notes/2888859 "}, {"RefNumber": "2881849", "RefComponent": "XX-PROJ-CDP-566", "RefTitle": "Uninstalling Addon UIMUI5", "RefUrl": "/notes/2881849 "}, {"RefNumber": "2866246", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGSGUI", "RefUrl": "/notes/2866246 "}, {"RefNumber": "2882129", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "Uninstalling PCAI_ENT", "RefUrl": "/notes/2882129 "}, {"RefNumber": "2881878", "RefComponent": "XX-PROJ-CDP-553", "RefTitle": "Uninstalling Addon UIMWUI", "RefUrl": "/notes/2881878 "}, {"RefNumber": "2881905", "RefComponent": "XX-PROJ-CDP-568", "RefTitle": "Uninstalling Addon UIMWDA", "RefUrl": "/notes/2881905 "}, {"RefNumber": "2869393", "RefComponent": "XX-PROJ-CDP-571", "RefTitle": "Uninstalling ABAP Add-On PLVAT", "RefUrl": "/notes/2869393 "}, {"RefNumber": "2881095", "RefComponent": "XX-PROJ-CDP-566", "RefTitle": "Uninstalling Addon UIMGW", "RefUrl": "/notes/2881095 "}, {"RefNumber": "2880997", "RefComponent": "XX-PROJ-CDP-266", "RefTitle": "Uninstalling Addon UIM", "RefUrl": "/notes/2880997 "}, {"RefNumber": "2876462", "RefComponent": "XX-PROJ-CDP-394", "RefTitle": "Uninstalling MNFS", "RefUrl": "/notes/2876462 "}, {"RefNumber": "2873441", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Best Practice add-ons", "RefUrl": "/notes/2873441 "}, {"RefNumber": "2866274", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGCOM", "RefUrl": "/notes/2866274 "}, {"RefNumber": "2864344", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling TMAB4 400", "RefUrl": "/notes/2864344 "}, {"RefNumber": "2861037", "RefComponent": "LOD-PER", "RefTitle": "FS-PER Rel 3.0 SP11: Plugin class for uninstalling SAP PaPM (NXI)", "RefUrl": "/notes/2861037 "}, {"RefNumber": "2860461", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling LOGS4H", "RefUrl": "/notes/2860461 "}, {"RefNumber": "2849695", "RefComponent": "XX-PROJ-CDP-574", "RefTitle": "Uninstalling CASHREG 100", "RefUrl": "/notes/2849695 "}, {"RefNumber": "2851245", "RefComponent": "FI-AF-SAI", "RefTitle": "Uninstalling SAIUI (S/4 HANA for Accounting Integration 1907)", "RefUrl": "/notes/2851245 "}, {"RefNumber": "2851291", "RefComponent": "FI-AF-SAI", "RefTitle": "Uninstalling SAI (S/4 HANA for Accounting Integration 1907)", "RefUrl": "/notes/2851291 "}, {"RefNumber": "2847388", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNEU", "RefUrl": "/notes/2847388 "}, {"RefNumber": "2847452", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNEB", "RefUrl": "/notes/2847452 "}, {"RefNumber": "2847410", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNUI", "RefUrl": "/notes/2847410 "}, {"RefNumber": "2847343", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNSB", "RefUrl": "/notes/2847343 "}, {"RefNumber": "2842802", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling TMAB 400", "RefUrl": "/notes/2842802 "}, {"RefNumber": "2846015", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling SLCEUI addon", "RefUrl": "/notes/2846015 "}, {"RefNumber": "2832367", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-on IBPP_PI", "RefUrl": "/notes/2832367 "}, {"RefNumber": "2831306", "RefComponent": "XX-PROJ-CDP-394", "RefTitle": "Uninstalling NFSE", "RefUrl": "/notes/2831306 "}, {"RefNumber": "2821159", "RefComponent": "XX-PROJ-CDP-539", "RefTitle": "Uninstallation Attribute Based Planning(ABPSCM)", "RefUrl": "/notes/2821159 "}, {"RefNumber": "2821177", "RefComponent": "XX-PROJ-CDP-539", "RefTitle": "ABP: Uninstallation Attribute Based Planning (ABPERP)", "RefUrl": "/notes/2821177 "}, {"RefNumber": "2807957", "RefComponent": "SCM-APO-PPS-ERP", "RefTitle": "Uninstalling SAP SCM PPDS ON ERP", "RefUrl": "/notes/2807957 "}, {"RefNumber": "2807689", "RefComponent": "EPM-NM", "RefTitle": "Uninstalling NOTESMG", "RefUrl": "/notes/2807689 "}, {"RefNumber": "2796120", "RefComponent": "XX-PROJ-CDP-539", "RefTitle": "ABP: Uninstallation Attribute Based Planning(ABPSCM, ABPERP, ABPFMS)", "RefUrl": "/notes/2796120 "}, {"RefNumber": "2792183", "RefComponent": "BC-UPG-ADDON", "RefTitle": "STWACS: Uninstalling (Stock Transfer Workbench)", "RefUrl": "/notes/2792183 "}, {"RefNumber": "2792127", "RefComponent": "BC-UPG-ADDON", "RefTitle": "RAERP: Uninstalling (Automated Store Allocation)", "RefUrl": "/notes/2792127 "}, {"RefNumber": "2791460", "RefComponent": "SCM-APO-EMS", "RefTitle": "Uninstalling of MSPLERP 100 using SAINT transaction", "RefUrl": "/notes/2791460 "}, {"RefNumber": "2768528", "RefComponent": "LOD-EC-INT-ORG", "RefTitle": "Uninstalling PA_SE_IN add-on", "RefUrl": "/notes/2768528 "}, {"RefNumber": "2782223", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling CPQUICRM addon", "RefUrl": "/notes/2782223 "}, {"RefNumber": "2781679", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling CPQUIGW addon", "RefUrl": "/notes/2781679 "}, {"RefNumber": "2755621", "RefComponent": "EPM-DSM-INS", "RefTitle": "Uninstalling DISCLMG", "RefUrl": "/notes/2755621 "}, {"RefNumber": "2776914", "RefComponent": "PLM-ECC", "RefTitle": "ECTR: Uninstalling S4ECTR", "RefUrl": "/notes/2776914 "}, {"RefNumber": "2768034", "RefComponent": "CA-FS-POL", "RefTitle": "Uninstalling SAP Process Object Builder", "RefUrl": "/notes/2768034 "}, {"RefNumber": "2763956", "RefComponent": "XX-PROJ-CDP-691", "RefTitle": "Uninstalling Cash Application Downport for ERP (CASHAPPE)", "RefUrl": "/notes/2763956 "}, {"RefNumber": "2757276", "RefComponent": "GRC-BIS", "RefTitle": "Uninstall ASSURANCE AND COMPLIANCE - Software Component UIACS", "RefUrl": "/notes/2757276 "}, {"RefNumber": "2754644", "RefComponent": "FS-PE", "RefTitle": "Uninstalling PAY-ENGINE 500", "RefUrl": "/notes/2754644 "}, {"RefNumber": "2755064", "RefComponent": "PLM-ECC", "RefTitle": "ECTR: Uninstalling ECTR", "RefUrl": "/notes/2755064 "}, {"RefNumber": "2463636", "RefComponent": "GRC-SPC-IU", "RefTitle": "Uninstalling GRCFND_A - SAP Access Control, SAP Process Control, SAP Risk Management", "RefUrl": "/notes/2463636 "}, {"RefNumber": "2732902", "RefComponent": "XX-CSC-US-WE-WM", "RefTitle": "Uninstalling WEC 605", "RefUrl": "/notes/2732902 "}, {"RefNumber": "2749060", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 5.2 Plug-In Component VIRSAHR", "RefUrl": "/notes/2749060 "}, {"RefNumber": "2748914", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 5.2 PlugIn Component VIRSANH", "RefUrl": "/notes/2748914 "}, {"RefNumber": "2732994", "RefComponent": "XX-PROJ-CDP-157", "RefTitle": "Uninstallation Add-on NEGCON  602", "RefUrl": "/notes/2732994 "}, {"RefNumber": "2203365", "RefComponent": "CA-LT-INS", "RefTitle": "Uninstalling DMIS 2011_1_7xx  / DMIS 2018_1_752", "RefUrl": "/notes/2203365 "}, {"RefNumber": "2717731", "RefComponent": "IS-U-IDEX", "RefTitle": "APEU: Uninstalling APEU", "RefUrl": "/notes/2717731 "}, {"RefNumber": "2717705", "RefComponent": "CA-GTF-APE", "RefTitle": "UIAPE: Uninstalling UIAPE", "RefUrl": "/notes/2717705 "}, {"RefNumber": "2715328", "RefComponent": "IS-U-EIM", "RefTitle": "Uninstalling UCOM", "RefUrl": "/notes/2715328 "}, {"RefNumber": "2715355", "RefComponent": "IS-U-EIM", "RefTitle": "Uninstallation of Add-on US4G", "RefUrl": "/notes/2715355 "}, {"RefNumber": "2711685", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation for Addon - GMA RCS, using SAINT", "RefUrl": "/notes/2711685 "}, {"RefNumber": "2711615", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation For Addon - DSiM 3.0, using SAINT", "RefUrl": "/notes/2711615 "}, {"RefNumber": "2437183", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling SLCE addon", "RefUrl": "/notes/2437183 "}, {"RefNumber": "2708037", "RefComponent": "CA-GTF-APE", "RefTitle": "APE: Uninstalling APE", "RefUrl": "/notes/2708037 "}, {"RefNumber": "2687669", "RefComponent": "GRC-BIS", "RefTitle": "Uninstall ASSURANCE AND COMPLIANCE - Software Component SAPFRA", "RefUrl": "/notes/2687669 "}, {"RefNumber": "2664869", "RefComponent": "XX-PROJ-CDP-ACS-008", "RefTitle": "Uninstallation of Add-on CONDET 600", "RefUrl": "/notes/2664869 "}, {"RefNumber": "2664866", "RefComponent": "IS-U-LIB-DE-GF", "RefTitle": "Uninstallation of Add-on IDXGF 100", "RefUrl": "/notes/2664866 "}, {"RefNumber": "2660321", "RefComponent": "IS-U-LIB-DE-MM", "RefTitle": "Uninstallation of Add-on MEMI 100", "RefUrl": "/notes/2660321 "}, {"RefNumber": "2660330", "RefComponent": "XX-PROJ-CDP-586", "RefTitle": "Uninstallation of Add-on MOSB 100", "RefUrl": "/notes/2660330 "}, {"RefNumber": "2660905", "RefComponent": "IS-U-EIM", "RefTitle": "IDEIM: Uninstallation of Add-on IDEIM 200", "RefUrl": "/notes/2660905 "}, {"RefNumber": "2663327", "RefComponent": "IS-U-LIB-DE-GM", "RefTitle": "Uninstallation of Add-on IDEXGM 604", "RefUrl": "/notes/2663327 "}, {"RefNumber": "2663357", "RefComponent": "IS-U-LIB-DE-GM", "RefTitle": "Uninstallation of Add-on IDXGM 200", "RefUrl": "/notes/2663357 "}, {"RefNumber": "2656381", "RefComponent": "XX-PROJ-CDP-578", "RefTitle": "Uninstallation of Add-on IDXGC 200 or IDXGC 802", "RefUrl": "/notes/2656381 "}, {"RefNumber": "2657369", "RefComponent": "IS-U-LIB-DE-CL", "RefTitle": "Uninstallation of Add-on IDXGL 200", "RefUrl": "/notes/2657369 "}, {"RefNumber": "2662186", "RefComponent": "GRC-SAC-ARQ", "RefTitle": "Uninstalling LWMGRC01 100 - SAP Access Approver 2.0.0", "RefUrl": "/notes/2662186 "}, {"RefNumber": "2656877", "RefComponent": "XX-PROJ-CDP-OEW", "RefTitle": "OEWB: Uninstalling SAP OEWB Release 600", "RefUrl": "/notes/2656877 "}, {"RefNumber": "2632454", "RefComponent": "CA-MON", "RefTitle": "Uninstalling Business Process Tracking BPMT 702", "RefUrl": "/notes/2632454 "}, {"RefNumber": "2630240", "RefComponent": "BC-CTS-TMS-CTR", "RefTitle": "Uninstalling CTS_PLUG 200", "RefUrl": "/notes/2630240 "}, {"RefNumber": "2525068", "RefComponent": "XX-PROJ-CDP-184", "RefTitle": "IDXPF: Uninstalling IDXPF 604", "RefUrl": "/notes/2525068 "}, {"RefNumber": "2622613", "RefComponent": "CA-FE-CFG", "RefTitle": "Uninstalling SAPUIFT 100", "RefUrl": "/notes/2622613 "}, {"RefNumber": "2616622", "RefComponent": "FI-AF-ARO", "RefTitle": "Uninstalling SAP ARO - FOM604", "RefUrl": "/notes/2616622 "}, {"RefNumber": "2569045", "RefComponent": "BW-BCT-ISR-PIP", "RefTitle": "Performing the Uninstall Process of RTLPOSDM 100_xxx Using Transaction SAINT", "RefUrl": "/notes/2569045 "}, {"RefNumber": "2611463", "RefComponent": "AIE-AII", "RefTitle": "Uninstall SAP Auto-ID Infrastructure", "RefUrl": "/notes/2611463 "}, {"RefNumber": "2609756", "RefComponent": "XX-PROJ-CDP-524", "RefTitle": "Uninstalling IDEXCH 604 - DATA XCHANGE SWISS UTILITY", "RefUrl": "/notes/2609756 "}, {"RefNumber": "2586823", "RefComponent": "SCM-APO-CA-TOL", "RefTitle": "Uninstalling SCMAPO 7.13 and SCMAPO 7.14 as Add-On to SAP ERP", "RefUrl": "/notes/2586823 "}, {"RefNumber": "2544275", "RefComponent": "CA-TRA-IN", "RefTitle": "GST - ITR : Uninstallation ITR ( Indain Tax Reform )", "RefUrl": "/notes/2544275 "}, {"RefNumber": "2381952", "RefComponent": "LO-INT-COD", "RefTitle": "Uninstalling software component CODERINT 600", "RefUrl": "/notes/2381952 "}, {"RefNumber": "2528206", "RefComponent": "XX-PROJ-CDP-040", "RefTitle": "Uninstalling Add-on DMEE 601", "RefUrl": "/notes/2528206 "}, {"RefNumber": "2529018", "RefComponent": "SD-SLS-WOM", "RefTitle": "WOM: Uninstallation WOM 200 (Cross Channel Order Management)", "RefUrl": "/notes/2529018 "}, {"RefNumber": "2531823", "RefComponent": "SCM-EM-MGR", "RefTitle": "Uninstalling Add-On SCEMSRV 900 or SCEMSRV 920", "RefUrl": "/notes/2531823 "}, {"RefNumber": "2356692", "RefComponent": "SLL-LEG-FUN", "RefTitle": "Uninstalling SAP Global Trade Services", "RefUrl": "/notes/2356692 "}, {"RefNumber": "2513541", "RefComponent": "FIN-FB", "RefTitle": "Uninstallation of add-on FINBASIS 700, 748/FSCM_CCD 618 and above", "RefUrl": "/notes/2513541 "}, {"RefNumber": "2505027", "RefComponent": "FIN-SEM", "RefTitle": "Uninstallation of add-on SEM-BW 700, 748, and above", "RefUrl": "/notes/2505027 "}, {"RefNumber": "2256000", "RefComponent": "BC-SRV-APS-APL", "RefTitle": "Uninstallation of SAP Fiori UI Component: UI for Basis Applications", "RefUrl": "/notes/2256000 "}, {"RefNumber": "2497991", "RefComponent": "CA-GTF-AIF", "RefTitle": "Uninstalling the add-on AIF (Application Interface Framework)", "RefUrl": "/notes/2497991 "}, {"RefNumber": "2481113", "RefComponent": "XX-PROJ-CDP-597", "RefTitle": "Uninstalling CPRA Add-ons", "RefUrl": "/notes/2481113 "}, {"RefNumber": "2477324", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-On SECLG1 100", "RefUrl": "/notes/2477324 "}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620 "}, {"RefNumber": "2409846", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation WFMCORE 200", "RefUrl": "/notes/2409846 "}, {"RefNumber": "1905172", "RefComponent": "BC-UPG-ADDON", "RefTitle": "TEMPLATE: Uninstalling <Add-On>", "RefUrl": "/notes/1905172 "}, {"RefNumber": "2312680", "RefComponent": "OPU-GW-CNT", "RefTitle": "Uninstallation of the add-ons IW_CNT 200 and IW_CBS 200 using transaction SAINT", "RefUrl": "/notes/2312680 "}, {"RefNumber": "2319097", "RefComponent": "OPU-GW-CNT", "RefTitle": "Uninstallation of the add-on IW_SCS 200 using transaction SAINT", "RefUrl": "/notes/2319097 "}, {"RefNumber": "2293817", "RefComponent": "MOB-APP-EMR-EMS", "RefTitle": "SAP EMR Mobile Server for HC: ACP needed for installation on NetWeaver 7.50", "RefUrl": "/notes/2293817 "}, {"RefNumber": "2012921", "RefComponent": "LOD-TEM-IMD", "RefTitle": "Collective Note for the Integration of SAP ERP with SAP Cloud for Travel and Expense", "RefUrl": "/notes/2012921 "}, {"RefNumber": "1942870", "RefComponent": "MOB-APP-EMR-EMS", "RefTitle": "SAP EMR Mobile Server for HC: ACP needed for installation on NetWeaver 7.40", "RefUrl": "/notes/1942870 "}, {"RefNumber": "1560383", "RefComponent": "GRC-ACP", "RefTitle": "Cannot install GRC 10.0 plug-in due to low BASIS, ABA SP lvl", "RefUrl": "/notes/1560383 "}, {"RefNumber": "1100222", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation and Delta Upgrade of SLL-LEG 720", "RefUrl": "/notes/1100222 "}, {"RefNumber": "1490355", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ABAP add-on CALC", "RefUrl": "/notes/1490355 "}, {"RefNumber": "1452608", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation CALC 700 on SAP CRM ABAP 7.0", "RefUrl": "/notes/1452608 "}, {"RefNumber": "1589409", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Vendor key handling in EHP2, EHP3 for NW 7.0, EHP1 NW 7.3, NW 7.4", "RefUrl": "/notes/1589409 "}, {"RefNumber": "822379", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems with support packages in SAP NW 7.0x AS ABAP", "RefUrl": "/notes/822379 "}, {"RefNumber": "850655", "RefComponent": "BC-UPG-ADDON", "RefTitle": "JBM: Release strategy", "RefUrl": "/notes/850655 "}, {"RefNumber": "1619429", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of UI Logging of SAP GUI f. Windows(LOGWIN 100)", "RefUrl": "/notes/1619429 "}, {"RefNumber": "994644", "RefComponent": "BC-UPG-ADDON", "RefTitle": "CLM0 601: Installation of SAP CLM 601", "RefUrl": "/notes/994644 "}, {"RefNumber": "1472649", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Attribute Change Package 09 for FINBASIS 604.", "RefUrl": "/notes/1472649 "}, {"RefNumber": "1246567", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 4.6C", "RefUrl": "/notes/1246567 "}, {"RefNumber": "1143354", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP Invoice Management by Open Text", "RefUrl": "/notes/1143354 "}, {"RefNumber": "1158886", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP REAL ESTATE CEE 110_600 add-on and ERP Enhancement Pack", "RefUrl": "/notes/1158886 "}, {"RefNumber": "1326381", "RefComponent": "BC-EHP-INS-TLA", "RefTitle": "Attribute Change Packages (ACP) do not work in the EHPI", "RefUrl": "/notes/1326381 "}, {"RefNumber": "1140663", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-PS-XT 6.0 and ERP Enhancement Packages", "RefUrl": "/notes/1140663 "}, {"RefNumber": "1252111", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 640", "RefUrl": "/notes/1252111 "}, {"RefNumber": "1247785", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 620", "RefUrl": "/notes/1247785 "}, {"RefNumber": "1247361", "RefComponent": "GRC-SAC", "RefTitle": "AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 700", "RefUrl": "/notes/1247361 "}, {"RefNumber": "1118803", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC 600 Support Packages and ERP enhancement packages", "RefUrl": "/notes/1118803 "}, {"RefNumber": "1225060", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to IS-M 603 stops due to conflict with EXX-SE 602", "RefUrl": "/notes/1225060 "}, {"RefNumber": "1150868", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP IS-UT CEE 600 add-on and ERP Enhancement Packages", "RefUrl": "/notes/1150868 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}