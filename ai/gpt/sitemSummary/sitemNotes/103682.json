{"Request": {"Number": "103682", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 266, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000103682?language=E&token=0A385091C686BFDD3BE957DC9FD23C44"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000103682", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000103682/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "103682"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "18.02.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "103682 - DB2/390: Index/tablespace storage attributes, SE14"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains information on the topics:</p> <OL>1. Storage attributes of indexes and tablespaces</OL> <OL>2. Creating and converting tables and indexes</OL> <OL>3. Using Transaction SE14</OL> <OL>4. DDIC corrections</OL> <p></p> <b>Note that this note is only valid for R/3 installations on DB2/390 with SAP Releases &lt;= 4.0B.</b><br /> <p></p> <b>For Releases &gt;= 4.5A the handling of the DB2 storage attributes - and consequently Transaction SE14 - was revised essentially within the R/3 System. Additional information can be found in the Database Administration Guide, chapter 2.</b><br /><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SE14, conversion, storage parameters, storage attributes, database,<br />operation, isolation, separation, table<br />DB2, MVS, OS390, ABAP<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Storage attributes of tables and indexes</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The first question is which of the many storage parameters are actually considered by the R/3 System in the DB2/390. <UL><LI>In the case of <B>Tables</B> this includes the name of the database and of the tablespaces where the table can be found (or where it should be found).</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Usually the R/3 System ignores the storage parameters of the tablespaces (PRIQTY, SECQTY, BUFFERPOOL, ...). They are only important when tablespaces are dynamically created for absolutely new tables.</p> <UL><LI>For <B>indexes</B> these are Stogroup, primary and secondary quantity, PctFree and the buffer pool.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These storage parameters can originate from different sources: <UL><LI>Saved parameters (abbreviation: USR)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These storage parameters were specified and saved by the user using Transaction SE14.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Details on using this transaction can be found in the section \"Using Transaction SE14\".</p> <UL><LI>Status on the database (abbreviation: DBS)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These parameters return the information that was read directly from the DB2 catalog (SYSIBM.SYSINDEXPART, SYSIBM.SYSTABLES).</p> <UL><LI>Default values (CMP)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The default values depend on the technical settings of the table (maintenance in Transaction SE15). They are defined in the tables TADB2, TGDB2, IADB2 and IGDB2.</p> <OL>2. Creating and converting tables and indexes</OL> <UL><LI>Activating new tables and indexes</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you activate a completely new <B>table</B> , default parameters are used.</p> <UL><UL><LI>Up to size category 3 the table is created in a what is referred to as multi-table-tablespace. If necessary (no database with less than 100 tables available) a new multi-table-tablespace location (tablespace, database, index stogroup, data stogroup) is created dynamically.</LI></UL></UL> <UL><UL><LI>For tables of size category 4 the system always generates a separate single-table tablespace with database and index/data stogroups.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The <B>primary key</B> (R/3 index name = \"0\") belongs to the table and is created together with it, also with default values.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is possible to create additional <B>indexes</B> (secondary indexes) along with the primary key. For this, the R/3 System reads the storage parameters of the primary key and uses them as a reference for the new index. Therefore there is only a slight danger of an indexspace bursting.</p> <UL><LI>Converting tables and indexes</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Sometimes the structures of tables and indexes must be changed in such a way that a DROP or ReCREATE is necessary (=conversion). In this case the storage parameters of the existing database objects (DBS) are read first in order to use them again for the ReCREATE. Thus, tables are created again in the original tablespace.<br />Note that you cannot use the original image copies afterwards due to the changed object IDs.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note the following three \"problem areas\":</p> <UL><UL><LI>4K-Pagesize -&gt; 32K-Pagesize</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the pagesize of the table increases from 4K to 32K, the new table no longer fits into the original tablespace. Then the R/3 System creates a new 32K-tablespace with default storage attributes. Under certain circumstances the default value of SECQTY is too small and the maximum number of extents is reached during the conversion. This causes a termination.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can avoid this problem by using DB2 means to create a 32-K copy of the existing tablespace before the conversion (add the special character $\" to the old tablespace name, for example TEST.TEST -&gt; TEST.TEST$). Use Transaction SE14 to save the new tablespace. As a result the table is created in the new tablespace (TEST.TEST$) during the conversion.</p> <UL><UL><LI>Large tables (&gt; 32 GByte)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With tables that are larger than 32 GByte, you reach the 64-Gbyte limit for tablespaces during the conversion because both the original table and its copy are in the original table. The conversion terminates during the data transfer from the old to the new table.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Also in this case, create a copy of the existing tablespace, and save it in Transaction SE14. Also refer to Note 96515 (as an alternative).</p> <UL><UL><LI>Partitioned tables</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Manual actions are required (see Note 78354) because the R/3 can only convert partitioned tables as of Release 4.5A.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can also use Transaction SE14 for indexes:</p> <UL><UL><LI>Specify and save storage parameters</LI></UL></UL> <UL><UL><LI>Delete index (in SE14)</LI></UL></UL> <UL><UL><LI>Create index again (in SE14)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that you can delete or recreate the primary key of a table in Transaction SE14 only together with the table.</p> <OL>3. Using Transaction SE14</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You save storage parameters as follows: <OL><OL>a) Call Transaction SE14, enter the table name and select \"Edit\".</OL></OL> <OL><OL>b) Select \"Indexes...\" in case you want to edit the storage parameters of an index.</OL></OL> <OL><OL>c) Select \"Goto -&gt; Storage parameters\".</OL></OL> <OL><OL>d) Set the radio button under \"Use\" to line \"USR\".</OL></OL> <OL><OL>e) Specify</OL></OL> <UL><UL><LI>for tables:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the name of the tablespace (and respective database), to which the table is to be converted. If the database and/or tablespace was created by the database administrator, they must have creator SAPR3 and must adhere to the naming convention described in the guide \"Database Administration Guide\", chapter 1.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is allowed (and much easier) to create a copy of the existing single-table tablespace with identical database and stogroups and add the special character&#x00A0;&#x00A0;\"$\" (TEST.TEST -&gt; TEST.TEST$).</p> <UL><UL><LI>for indexes:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PRIQTY, SECQTY, PCTFREE, BUFFERPOOL and STOGROUP.</p> <OL><OL>f) Select \"DB parameters -&gt; Save\".</OL></OL> <OL><OL>g) Select \"Goto -&gt; Back\", to return to the initial screen of Transaction SE14.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you edit and save a table, also edit and save the storage attributes of the corresponding indexes. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Within Transaction SE14 you have the option of triggering a conversion directly (for example in order to isolate a table in a separate tablespace): <OL><OL>a) Specify the processing type.</OL></OL> <OL><OL>b) Select \"Extras -&gt; Force conversion\".</OL></OL> <OL><OL>c) Answer \"Yes\" in the dialog box 'Request: \"Force conversion\"'.<br /></OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After the conversion, the status attributes of the table must be \"active\", \"saved\" and \"exists in the database\".<br /> <OL>4. DDIC corrections</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that you must import DDIC corrections so that Transaction SE14 is available with full range of functions. To this matter please refer to Note 162250.</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Operating system", "Value": "NT/INTEL"}, {"Key": "Operating system", "Value": "AIX"}, {"Key": "Operating system", "Value": "OS/390"}, {"Key": "Operating system", "Value": "AIX     4.2.X"}, {"Key": "Operating system", "Value": "AIX     4.1.5"}, {"Key": "Database System", "Value": "DB2/390"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022631)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D000269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000103682/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000103682/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96515", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Isolating or converting large tables and table clusters", "RefUrl": "/notes/96515 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "529178", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Table conversion with COMPRESS", "RefUrl": "/notes/529178 "}, {"RefNumber": "78354", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Conversion of partitioned tables", "RefUrl": "/notes/78354 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}