{"Request": {"Number": "820622", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 436, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016001122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000820622?language=E&token=C81AB387A5A204B2F68B6631A7CA05BF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000820622", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000820622/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "820622"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.09.2006"}, "SAPComponentKey": {"_label": "Component", "value": "SV-PERF-PI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Process Integration (PI) related performance issues"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Performance Problems", "value": "SV-PERF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-PERF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Process Integration (PI) related performance issues", "value": "SV-PERF-PI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-PERF-PI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "820622 - Standard jobs for XI performance monitoring"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>XI message performance statistics are not generated for SAP service reports, and/or performance headers for XI messages (SXMSPFRAWH table entries) must be reorganized. This applies to XI 3.0 and above.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SXMSPFRAWH SXMSPFAGG reorg message statistics<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Missing performance statistics in EarlyWatch Alert for XI message processing. SXMSPFRAWH table growth.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Take the steps listed below in sequence. All steps must be carried out in the Integration Server client, which can be found in SE16 by looking at table SXMSCONFVL. In the field 'VALUE' enter 'HUB' and press F8 - the corresponding table entry lists the IS client beside parameter ENGINE_TYPE.</p> <OL>1. Check that note 958402 is implemented to eliminate duplicate address data for sending &amp; receiving interfaces.</OL> <OL>2. Set the following XI parameters using transaction SXMB_ADM -&gt; Integration Engine Configuration -&gt; Category PERF.</OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| Value<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------------------|-----<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;PERF DAYS_TO_KEEP_DATA MEASUREMENT_ITEMS | 15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;PERF DAYS_TO_KEEP_DATA AGGREGATION_ITEMS | 15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;PERF MEASUREMENT_LEVEL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;PERF MEASUREMENT_PERSIST&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | 1</p> <OL>3. Schedule an initial single run of the reorganization job at a time of low workload. This job may run for several hours as it removes all XI message performance headers up to the previous 15 days. Create the job in SM36 using the name SAP_XMB_PERF_REORG and including program step SXMS_PF_REORG.</OL> <OL>4. When the initial reorganization job is finished schedule two periodic background jobs to aggregate and reorganize XI performance data. Use the following settings:</OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Job name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| Program&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | Period<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;--------------------------------------------------------------<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP_XMB_PERF_AGGREGATE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| SXMS_PF_AGGREGATE&#x00A0;&#x00A0;&#x00A0;&#x00A0; | Hourly<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP_XMB_PERF_REORG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| SXMS_PF_REORG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | Hourly<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D049866)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D049866)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000820622/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000820622/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820622/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820622/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820622/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820622/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820622/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820622/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820622/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "957428", "RefComponent": "SV-PERF-PI", "RefTitle": "Troubleshooting XI Service Downloads", "RefUrl": "/notes/957428"}, {"RefNumber": "876614", "RefComponent": "BC-XI-IS", "RefTitle": "XI 3.0: Enhancement for the display of XI audit data", "RefUrl": "/notes/876614"}, {"RefNumber": "872388", "RefComponent": "BC-XI", "RefTitle": "Troubleshooting Archiving and Deletion in PI", "RefUrl": "/notes/872388"}, {"RefNumber": "871473", "RefComponent": "BC-XI-IS", "RefTitle": "XI 3.0: Displaying XI audit data", "RefUrl": "/notes/871473"}, {"RefNumber": "746088", "RefComponent": "SV-PERF-PI", "RefTitle": "Statistics on PI/XI messages for Remote Services", "RefUrl": "/notes/746088"}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604"}, {"RefNumber": "1083220", "RefComponent": "BC-XI-IS", "RefTitle": "XI/PI: Runtime RSXMB_REMOTE_SERVICE / SXMS_PF_AGGREGATE", "RefUrl": "/notes/1083220"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "872388", "RefComponent": "BC-XI", "RefTitle": "Troubleshooting Archiving and Deletion in PI", "RefUrl": "/notes/872388 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "1083220", "RefComponent": "BC-XI-IS", "RefTitle": "XI/PI: Runtime RSXMB_REMOTE_SERVICE / SXMS_PF_AGGREGATE", "RefUrl": "/notes/1083220 "}, {"RefNumber": "746088", "RefComponent": "SV-PERF-PI", "RefTitle": "Statistics on PI/XI messages for Remote Services", "RefUrl": "/notes/746088 "}, {"RefNumber": "957428", "RefComponent": "SV-PERF-PI", "RefTitle": "Troubleshooting XI Service Downloads", "RefUrl": "/notes/957428 "}, {"RefNumber": "871473", "RefComponent": "BC-XI-IS", "RefTitle": "XI 3.0: Displaying XI audit data", "RefUrl": "/notes/871473 "}, {"RefNumber": "876614", "RefComponent": "BC-XI-IS", "RefTitle": "XI 3.0: Enhancement for the display of XI audit data", "RefUrl": "/notes/876614 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}