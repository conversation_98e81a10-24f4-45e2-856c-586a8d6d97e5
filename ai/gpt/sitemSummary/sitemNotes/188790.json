{"Request": {"Number": "188790", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 292, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014752252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000188790?language=E&token=F53E020C4B4AF93DE1BF7734A80FFFF0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000188790", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000188790/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "188790"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.08.2000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-UTL-BRR"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON>os<PERSON><PERSON>, Object Navigator"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workbench Utilities", "value": "BC-DWB-UTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON>os<PERSON><PERSON>, Object Navigator", "value": "BC-DWB-UTL-BRR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL-BRR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "188790 - Multiple scheduling of jobs EU_PUT and EU_REORG"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Job EU_PUT or job EU_REORG or both jobs are scheduled more than once (two or three times, ...) and therefore run more than once every night.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EU_REORG, SAPRSLOG, EU_PUT, SAPRSEUT, SE80, Object Navigator, Repository Browser, Object Browser, batch, ABAP Workbench, ABAP Development</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>For the continuous reorganization of its tables, the ABAP Workbench needs two background jobs, which should run every night.These are the jobs EU_REORG and EU_PUT.<br />These jobs are scheduled automatically.This is performed by an automatic scheduling mechanism in Transaction SE80 (Object Navigator / Repository Browser).<br />This scheduling mechanism first determines all jobs that have already been scheduled.If the jobs are not found the mechanism schedules the jobs again.<br />If the user who triggers the mechanism has only restricted background authorizations, then only the background jobs of the <B>current</B>client are determined.However, if the background jobs are already scheduled in another client, then the multiple scheduling described occurs.<br />Then, one job or both jobs EU_REORG and EU_PUT run two or even three times every night.<br />This multiple running of the jobs causes <B>no</B> data inconsistencies. But of course resources are used unnecessarily.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of Release 4.6C, the problem no longer exists.<br />It is corrected with Support Package 16 in Release 4.6B, with Support Package 30 in Release 4.5B and with Support Package 52 in Release 4.0B.<br />If you want to implement the correction in advance, you can easily do this. Just <B>replace</B><br />&#x00A0;&#x00A0; in function module&#x00A0;&#x00A0;&#x00A0;&#x00A0; RS_EU_JOBS<br />&#x00A0;&#x00A0; the form&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; READ_JOB<br />with the following statements:<br /> form read_job using jobname programname found.<BR/> <BR/> &#x00A0;&#x00A0;data begin of step_list occurs 1.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;include structure tbtcstep.<BR/> &#x00A0;&#x00A0;data end of step_list.<BR/> <BR/> *&#x00A0;&#x00A0;JOB_SELECT-JOBNAME = JOBNAME.<BR/> *&#x00A0;&#x00A0;JOB_SELECT-SCHEDUL = 'X'.<BR/> *&#x00A0;&#x00A0;JOB_SELECT-USERNAME = '*'.<BR/> *&#x00A0;&#x00A0;CALL FUNCTION 'BP_JOB_SELECT'<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; EXPORTING<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;JOBSELECT_DIALOG&#x00A0;&#x00A0;= 'N'<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;JOBSEL_PARAM_IN&#x00A0;&#x00A0; = JOB_SELECT<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; IMPORTING<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;JOBSEL_PARAM_OUT&#x00A0;&#x00A0;= JOB_SELECT<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TABLES<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;JOBSELECT_JOBLIST = JOB_LIST<BR/> *&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; EXCEPTIONS<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OTHERS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= 01.<BR/> <BR/> * The following SELECT replaces the function call above;<BR/> * this is necessary to get ALL jobs of ALL clients;<BR/> * in the past the above function call have sometimes read<BR/> * only the jobs in the current client. This happened<BR/> * because some users did not have the admin privileges.<BR/> <BR/> &#x00A0;&#x00A0;include lbtchdef.<BR/> &#x00A0;&#x00A0;tables: tbtco.<BR/> &#x00A0;&#x00A0;data: l_job_set like tbtco occurs 0,<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;l_job&#x00A0;&#x00A0;&#x00A0;&#x00A0; like tbtco.<BR/> <BR/> &#x00A0;&#x00A0;clear l_job_set.<BR/> &#x00A0;&#x00A0;select * from tbtco<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; into table l_job_set<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; where jobname = jobname&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; and<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; status&#x00A0;&#x00A0;= btc_released.<BR/> <BR/> &#x00A0;&#x00A0;found = space.<BR/> &#x00A0;&#x00A0;check sy-subrc = 0.<BR/> <BR/> &#x00A0;&#x00A0;clear l_job.<BR/> &#x00A0;&#x00A0;loop at l_job_set into l_job.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;call function 'BP_JOB_READ'<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; exporting<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;job_read_jobcount = l_job-jobcount<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;job_read_jobname&#x00A0;&#x00A0;= l_job-jobname<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;job_read_opcode&#x00A0;&#x00A0; = btc_read_all_jobdata<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; tables<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;job_read_steplist = step_list<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; exceptions<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;others&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= 01.<BR/> <BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;check sy-subrc = 0.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;read table step_list index 1.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;if sy-subrc = 0 and step_list-program = programname.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;found = 'X'. exit.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.<BR/> &#x00A0;&#x00A0;endloop.<BR/> <BR/> endform.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000552"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000188790/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000188790/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000188790/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000188790/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000188790/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000188790/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000188790/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000188790/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000188790/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "208540", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/208540"}, {"RefNumber": "18023", "RefComponent": "BC-DWB", "RefTitle": "Jobs EU_INIT, EU_REORG, EU_PUT", "RefUrl": "/notes/18023"}, {"RefNumber": "174645", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/174645"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "18023", "RefComponent": "BC-DWB", "RefTitle": "Jobs EU_INIT, EU_REORG, EU_PUT", "RefUrl": "/notes/18023 "}, {"RefNumber": "174645", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/174645 "}, {"RefNumber": "208540", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/208540 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30A", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}