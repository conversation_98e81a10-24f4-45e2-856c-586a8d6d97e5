{"Request": {"Number": "1818517", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 286, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017597702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001818517?language=E&token=FDF340539A8020576BA7036CF4D94FCB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001818517", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001818517/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1818517"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 78}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.05.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1818517 - EHP3 for SAP SRM 7.0 SP stacks - Release Information Note"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release- &amp; Information Note (RIN) contains information and references to notes for applying Support Package Stacks (SPS) of SAP Enhancement Package 3 for SAP Supplier Relationship Management (SRM) 7.0<br /><br /><strong>Note</strong>: This note is subject to change. Listed below are a few general points to keep in mind:<br /><br /><strong>GENERAL INFORMATION</strong><br /><br />Read this note completely BEFORE applying SP Stacks of SAP Enhancement Package&#160;3 of SAP SRM 7.0 and follow the instructions given below.</p>\r\n<ul>\r\n<li>Check this note for changes on a regular basis. All changes made after release of a Support Package (SP) Stack are documented in section \"Changes made after Release of SP Stack &lt;xx&gt;\".</li>\r\n<li>You will find general information about <strong>SP Stacks&#160;</strong>on SAP Support Portal at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/patches/stacks.html\">https://support.sap.com/software/patches/stacks.html</a>&#160;. The Schedule for SP Stacks&#160;could be found in the SAP Support Portal under Release, Upgrade&#160;&amp; Maintenance Info --&gt; Schedules for maintenance deliveries (Support Packages and SP Stacks) see <a target=\"_blank\" href=\"https://support.sap.com/release-upgrade-maintenance.html\">https://support.sap.com/release-upgrade-maintenance.html</a>&#160;</li>\r\n<li>In addition to the notes mentioned in this note, you should also take into account the list of side effects known for Support Packages, which is created especially for your situation (SP queue). You can request this list on SAP Service Marketplace at <a target=\"_blank\" href=\"http://support.sap.com/notes\">http://support.sap.com/notes</a>.</li>\r\n<li>Please note: the specific observed performance is depending on configuration, data volumes and data constellation.</li>\r\n<li>Technical Usages - You can select the Technical Usages on the SP stack download page in the Maintenance Planner.</li>\r\n<li>\r\n<p><strong>NEW:</strong> SAP Solution Manager&#8217;s cloud-based <strong>Maintenance Planner</strong> is the successor of Maintenance Optimizer, Landscape Planner and Product System Editor. <em><strong>Maintenance Optimizer is no longer supported</strong></em><strong>.</strong> Maintenance planner helps you plan and maintain systems in your landscape. Please use the Maintenance planner to calculate a stack XLM file for a system maintenance and add-on installation. To access the tool go to <a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/mp\">https://apps.support.sap.com/sap/support/mp</a> . A <a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/download/attachments/187337812/Maintenance_Planner_User_Guide.pdf\">Maintenance Planner User Guide</a> can be accessed from the home screen of Maintenance Planner. For the<strong> big picture of the Maintenance Planner</strong> and related tools, see <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-55363\">Landscape Management</a> @ the SCN.</p>\r\n</li>\r\n</ul>\r\n<p><strong>SPAM Update</strong> - We strongly recommend that you apply the latest version of the Support Package Manager before you apply any other Support Packages.</p>\r\n<ul>\r\n<li>SPAM Updates are available on SAP Support Portal under Software Downloads -&gt; Support Packages and Patches --&gt; Tools to apply Support Packages at <a target=\"_blank\" href=\"https://support.sap.com/software/patches/support-packages.html\">https://support.sap.com/software/patches/support-packages.html</a>&#160;.&#160;&#160;For more information about Support Package Manager and Software Update Manager, see the System Maintenance information on the <a target=\"_self\" href=\"https://service.sap.com/sltoolset\">SL Toolset page</a>.</li>\r\n<li>For additional information, see the initial screen of transaction SPAM, choose the 'i' icon (online documentation: help -&gt; application help).</li>\r\n<li>When you import components using the Support Package Manager, ensure that no system activities occur in parallel and no background jobs are running</li>\r\n<li>To patch Java components, use&#160;SUM and the related SP-Stack file \"stack.xml\".</li>\r\n</ul>\r\n<p>Read the <strong>restriction for scenarios</strong> in:</p>\r\n<ul>\r\n<li>Release Restriction Note for Enhancement Packages of SRM 7.0 in SAP Note <a target=\"_blank\" href=\"/notes/1649406\">1649406</a></li>\r\n<li>SAP SRM 7.0 EHP2 and EHP3: Important info about scenarios in SAP Note <a target=\"_blank\" href=\"/notes/1621788\">1621788</a></li>\r\n</ul>\r\n<p><strong>Installation and Upgrade Information</strong></p>\r\n<ul>\r\n<li>Upgrades are possible from any Support Package level of the supported start release. It is not necessary to apply a higher Support Packages on the start release.</li>\r\n<li>You can find information about <strong>browser</strong> support in SAP Note <a target=\"_blank\" href=\"/notes/1853989\">1853989</a>.</li>\r\n<li>Make sure to check the SAP NetWeaver Upgrade Master Guide of the SAP NW upgrade target release if you are planning a <strong>NetWeaver upgrade</strong>. It contains specific information about SAP NetWeaver prerequisites, upgrade paths and upgrade dependencies. You find the guides for the SAP NetWeaver Platform on the SAP Help Portal at <a target=\"_blank\" href=\"http://help.sap.com/netweaver\">help.sap.com/netweaver</a> .</li>\r\n<li>If the Add-on CCM 200 is installed on your source system, please follow the&#160;steps described in note <a target=\"_blank\" href=\"/notes/2000086\">2000086</a>.</li>\r\n<li>After installation/ upgrade&#160;the \"SAP System data\" box can show the Product Version \"- See Details-\". For more information&#160;see SAP Note <a target=\"_blank\" href=\"/notes/2122939\">2122939</a>.</li>\r\n</ul>\r\n<p><strong>Important Consideration</strong></p>\r\n<ul>\r\n<li>Please be aware that by SAP Business Suite 7 Innovations 2013 (including EhP7 for SAP ERP 6.0, EhP3 for SAP CRM 7.0, EHP3 for SAP SCM 7.0 and EhP3 for SAP SRM 7.0)&#160;&#160;dual-stack is not supported anymore. For more information (including information about a dual stack split tool), see SAP Note <a target=\"_blank\" href=\"/notes/1816819\">1816819</a>&amp; <a target=\"_blank\" href=\"/notes/1655335\">1655335</a>.</li>\r\n<li>Please be aware that SAP Business Suite 7 Innovations 2013 has been <span style=\"text-decoration: underline;\">initially delivered</span> with SP01-stack (hence this release information note starts with SP01). Although at least SP stack 01 is required for productive usage, the initial upgrade and installation shipment contains SP stack 00.Therefore the required SP stack must be included into the upgrade or installed after the installation.</li>\r\n<li>Please note that as of SAP Business Suite 7 (including EhP4 for SAP ERP 6.0, SAP CRM 7.0, SAP SRM 7.0 and SAP SCM 7.0), you can no longer install <span style=\"text-decoration: underline;\">Dual Stack</span> Application Systems (ABAP+Java) see SAP Note <a target=\"_blank\" href=\"/notes/855534\">855534</a>.</li>\r\n<li>Be aware, that if you are currently using Enterprise Portal, Business Warehouse and/or Process Integration in conjunction with SAP Business Suite applications, you will have to upgrade those SAP <strong>NetWeaver Hubs</strong> to 7.30 or higher before you can upgrade SAP SRM to SAP Enhancement Package 3 for SAP&#160;SRM 7.0. For more information about the Version Interoperability within the SAP Business Suite, see SAP Note <a target=\"_blank\" href=\"/notes/1388258\">1388258</a>.</li>\r\n<li>To run embedded analytics you need to order the necessary SAP Business Objects Software. For more information see SAP Note <a target=\"_blank\" href=\"/notes/1486885\">1486885</a>.</li>\r\n<li>If you want to use <strong>Add-Ons</strong> with a specific Enhancement Package, then refer to SAP Note <a target=\"_blank\" href=\"/notes/1820905\">1820905</a>&#160;&amp; <a target=\"_blank\" href=\"/notes/1896062\">1896062</a>.</li>\r\n<li>Information about the usage and release of <strong>SAP SRM Server on one client</strong> in SAP ERP can be found in SAP note <a target=\"_blank\" href=\"/notes/963000\">963000</a>.</li>\r\n<li>Please be informed that SAP Enhancement Package 7 for SAP ERP 6.0 will not be released for all<strong> platforms </strong>(OS/DB dependencies). Further information can be found at the SAP Product Availabilty Matrix (<a target=\"_blank\" href=\"https://support.sap.com/patches\">https://</a><a target=\"_blank\" href=\"https://support.sap.com/pam\">support.sap.com/pam</a>).</li>\r\n<li>Additional important information to be considered before using <strong>pricing, conditions and scales</strong> in SRM are provided in SAP note&#160;<a target=\"_blank\" href=\"/notes/2037465\">2037465</a>&#160;.</li>\r\n<li>Please be aware that it is recommended to <strong>launch NWBC in SRM</strong> from the path /sap/bc/nwbc/srm.</li>\r\n<li>If you use <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum Revision&#160;85.</li>\r\n</ul>\r\n<p><br /><strong>PROCESS INFORMATION</strong><br /><br />Important points to consider:</p>\r\n<ul>\r\n<li><strong>Contract Management in Procurement for Public Sector(PPS)<br /></strong>When a Guaranteed Minimum amount is provided in a contract in SRM, it should create a Earmarked Funds document in SAP ERP. In SRM 703 SP01, the configuration steps mentioned in SAP note 949041 should be followed to create a Guaranteed Minimum Earmarked Funds document.</li>\r\n<li><strong>Plan driven procurement with MM-SUS integration<br /></strong>In SRM 703 SP01, Invoice posting from SUS to ERP when GR based invoice verification is active in ERP Purchase Order can be configured using the steps mentioned in SAP note 1903345.</li>\r\n<li><strong>Plan driven procurement with plant maintenance<br /></strong>Create PO item &#160;with reference to contract, with contract having a condition type as 01 RH. Order the purchase order document. In the change version purchase order add 01RH as header level condition and order the document. While approving the change version of purchase order the total price of purchase order is overwritten with the old purchase order value.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP SRM 7.0, EHP3, SRM 7.03, EhP3,&#160;Enhancement Package 3, Supplier Relationship Management, EBP, SAP Enterprise Buyer, SUS, Supplier Self-Services, Auctioning, Direct Procurement, Indirect Procurement, Self-Service Procurement, Plan-Driven Procurement, Service Procurement, Supplier Enablement, Strategic Sourcing, Sourcing, Contract Management, Bidding Engine, Catalog, Catalog Content Management, eProcurement,&#160;Sourcing and Procurement</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;26 (03/2023)</strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack&#160;26 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 26. For more information about SAP SRM 7.0 SPS26, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;29 (01/2023). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;26 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 24.&#160;&#160;<br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a>&#160;(Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP22 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS26 -- SRM 7.0 EHP2 SPS31 -- SRM 7.0 EHP1 SPS22 -- SRM 7.0 SPS26 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong>&#160;Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 26:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;25 (09/2022)</strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack&#160;25 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 25. For more information about SAP SRM 7.0 SPS25, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;28 (07/2022). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;25 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 24.&#160;&#160;<br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a>&#160;(Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP22 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS25 -- SRM 7.0 EHP2 SPS30 -- SRM 7.0 EHP1 SPS21 -- SRM 7.0 SPS25 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong>&#160;Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 25:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;24 (03/2022)</strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack&#160;24 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 25. For more information about SAP SRM 7.0 SPS25, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;27 (01/2022). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;24 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 24.&#160;&#160;<br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a>&#160;(Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP22 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS24 -- SRM 7.0 EHP2 SPS29 -- SRM 7.0 EHP1 SPS21 -- SRM 7.0 SPS25 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong>&#160;Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 24:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;23 (09/2021)</strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack&#160;23 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 24. For more information about SAP SRM 7.0 SPS24, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;26 (07/2021). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;23 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 23.&#160;&#160;<br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a>&#160;(Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP22 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS23 -- SRM 7.0 EHP2 SPS28 -- SRM 7.0 EHP1 SPS20 -- SRM 7.0 SPS24 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong>&#160;Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 23:</strong></p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;22 (03/2021)</strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack&#160;22 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 24. For more information about SAP SRM 7.0 SPS24, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;25 (12/2020). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;22 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 22.&#160;&#160;<br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a>&#160;(Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP22 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS22 -- SRM 7.0 EHP2 SPS27 -- SRM 7.0 EHP1 SPS20 -- SRM 7.0 SPS24 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong>&#160;Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p>3027802 - Follow-on note 2457719 (ITAB_DUPLICATE_KEY in SELECT_FROM_PDACC)</p>\r\n<p>3034421 - Follow-on note 2816716 [mt_borf_buffer]</p>\r\n<p>For all the BOs (PO, POR, RFX, QUO, etc.) accessed from the portal, the header menu is hidden. When any tab is selected on the header menu, the tabs prior to the one selected are collapsed, as described via KBA:</p>\r\n<p class=\"MsoNormal\">2986036 - WDA: Item tab navigation issue</p>\r\n<p class=\"MsoNormal\">This issue is not related to SRM product and can be resolved via this SAP Note:</p>\r\n<p class=\"MsoNormal\">2980849 - WDA: wrong first item in Horizontal Contextual Panel</p>\r\n<p><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 22</strong></p>\r\n<p>3049512 - Follow-on note 3018269 (search for an old SC which is Not Sourcing-Relevant)</p>\r\n<p class=\"MsoNormal\">3057121 - Follow-on note 3017733 [cx_wd_context=&gt;node_was_invalidated]</p>\r\n<p class=\"MsoNormal\">3058651 - Follow-on note 3014572 (/SAPSRM/IF_PDO_META_CONF_ROOT&#126;MT_SET_SET_SUB)</p>\r\n<p>3061469 - Follow-on note 3020421 (SM13 BBP_DELETE_FROM_DOCUMENT_TAB)</p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;21 (09/2020)</strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack&#160;21 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 23. For more information about SAP SRM 7.0 SPS23, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;24 (07/2020). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;21 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;122</strong>&#160;of SAP HANA platform software SP stack 12. For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 21.&#160;&#160;<br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a>&#160;(Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP21 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS21 -- SRM 7.0 EHP2 SPS26 -- SRM 7.0 EHP1 SPS19 -- SRM 7.0 SPS23 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong>&#160;Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p>2962112 - Performance issue while message processing in itemlist_f_check</p>\r\n<p>2967841 - 2 purchase orders assigned to 1 shopping cart item - BBP_GET_STATUS_2</p>\r\n<p>3007207 - Follow-on note 2939458 (order issue)</p>\r\n<p>3008908 - Follow-on note 2913853 (Pricing Change Version with deleted item)</p>\r\n<p>3009929 - Deleted shopping carts are displaying in SoCo</p>\r\n<p>3020976 - Follow-on note 2317983 (expsv_item_type)</p>\r\n<p>3027802 - Follow-on note 2457719 (ITAB_DUPLICATE_KEY in SELECT_FROM_PDACC)</p>\r\n<p>3034421 - Follow-on note 2816716 [mt_borf_buffer]</p>\r\n<p>2959819 - Follow-on note 2934861 (PPS TOTAL_VALUE)</p>\r\n<p>2960668 - Follow-on note 2884870 (UI Bidder, Contact, Proc. Org., Proc. Group)</p>\r\n<p><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 21</strong></p>\r\n<p class=\"MsoNormal\">2999622 - Delta Changes not triggering for PO History Data for BW Extractor: 0SRM_TD_PO_ACC</p>\r\n<p>For all the BOs (PO, POR, RFX, QUO, etc.) accessed from the portal, the header menu is hidden. When any tab is selected on the header menu, the tabs prior to the one selected are collapsed, as described via KBA:</p>\r\n<p class=\"MsoNormal\">2986036 - WDA: Item tab navigation issue</p>\r\n<p class=\"MsoNormal\">This issue is not related to SRM product and can be resolved via this SAP Note:</p>\r\n<p class=\"MsoNormal\">2980849 - WDA: wrong first item in Horizontal Contextual Panel</p>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK&#160;20 (03/2020)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack&#160;20 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 23. For more information about SAP SRM 7.0 SPS23, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;23 (01/2020). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;20 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 20.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP20 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS20 -- SRM 7.0 EHP2 SPS25 -- SRM 7.0 EHP1 SPS19 -- SRM 7.0 SPS23 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p>2962112 - Performance issue while message processing in itemlist_f_check</p>\r\n<p>2967841 - 2 purchase orders assigned to 1 shopping cart item - BBP_GET_STATUS_2</p>\r\n<p>2948497 - Follow-on note 2783562 (iv_without_checks)</p>\r\n<p>3020976 - Follow-on note 2317983 (expsv_item_type)</p>\r\n<p>3027802 - Follow-on note 2457719 (ITAB_DUPLICATE_KEY in SELECT_FROM_PDACC)</p>\r\n<p>3034421 - Follow-on note 2816716 [mt_borf_buffer]</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 20</strong></p>\r\n<p>2896890 - Some shopping cart items remain unprocessed after executing report bbp_get_status_2</p>\r\n<p>2911946 - Follow-on note 2850188 (IPC BUILD_ITEM_TREE KPOSN)</p>\r\n<p>2914242 - Items order distorted when exlin is not supplied</p>\r\n<p>2915209 - Pricing PRC_FILL_ITEM_DATA and PRC_FILL_ITEM_DATA_LIST unification</p>\r\n<p>2920215 - Follow-on note 2844113 (Shop in 3 steps - Team Cart)</p>\r\n<p>2921939 - Follow-on note 2802337 (duplicate data on follow-on document)</p>\r\n<p>2921983 - Follow-on note 2843604 (change log)</p>\r\n<p>2924455 - No Further Confirmation / Invoice Radio buttons not updated</p>\r\n<p>2930274 - Follow-on note 2441656 [memory allocation issue when iv_parent_guid is not supplied]</p>\r\n<p>2960668 - Follow-on note 2884870 (UI Bidder, Contact, Proc. Org., Proc. Group)</p>\r\n<p class=\"MsoNormal\">2999622 - Delta Changes not triggering for PO History Data for BW Extractor: 0SRM_TD_PO_ACC</p>\r\n<p>For all the BOs (PO, POR, RFX, QUO, etc.) accessed from the portal, the header menu is hidden. When any tab is selected on the header menu, the tabs prior to the one selected are collapsed, as described via KBA:</p>\r\n<p class=\"MsoNormal\">2986036 - WDA: Item tab navigation issue</p>\r\n<p class=\"MsoNormal\">This issue is not related to SRM product and can be resolved via this SAP Note:</p>\r\n<p class=\"MsoNormal\">2980849 - WDA: wrong first item in Horizontal Contextual Panel</p>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 19 (09/2019)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 19 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 22. For more information about SAP SRM 7.0 SPS22, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;22 (07/2019). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 19 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 19.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP19 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS19 -- SRM 7.0 EHP2 SPS24 -- SRM 7.0 EHP1 SPS18 -- SRM 7.0 SPS22 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p>2962112 - Performance issue while message processing in itemlist_f_check</p>\r\n<p>2948497 - Follow-on note 2783562 (iv_without_checks)</p>\r\n<p>2967841 - 2 purchase orders assigned to 1 shopping cart item - BBP_GET_STATUS_2</p>\r\n<p>3020976 - Follow-on note 2317983 (expsv_item_type)</p>\r\n<p>3027802 - Follow-on note 2457719 (ITAB_DUPLICATE_KEY in SELECT_FROM_PDACC)</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 19</strong></p>\r\n<p>2837060 - SUS CF XML - Exception CX_SY_MOVE_CAST_ERROR</p>\r\n<p>2896890 - Some shopping cart items remain unprocessed after executing report bbp_get_status_2</p>\r\n<p>2914242 - Items order distorted when exlin is not supplied</p>\r\n<p>2915209 - Pricing PRC_FILL_ITEM_DATA and PRC_FILL_ITEM_DATA_LIST unification</p>\r\n<p>2921939 - Follow-on note 2802337 (duplicate data on follow-on document)</p>\r\n<p>2930274 - Follow-on note 2441656 [memory allocation issue when iv_parent_guid is not supplied]</p>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 18 (03/2019)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 18 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 22. For more information about SAP SRM 7.0 SPS22, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;21 (01/2019). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 18 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 18.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP18 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS18 -- SRM 7.0 EHP2 SPS23 -- SRM 7.0 EHP1 SPS18 -- SRM 7.0 SPS22 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p>2962112 - Performance issue while message processing in itemlist_f_check</p>\r\n<p>2967841 - 2 purchase orders assigned to 1 shopping cart item - BBP_GET_STATUS_2</p>\r\n<p>3020976 - Follow-on note 2317983 (expsv_item_type)</p>\r\n<p>3027802 - Follow-on note 2457719 (ITAB_DUPLICATE_KEY in SELECT_FROM_PDACC)</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 18</strong></p>\r\n<p>2422294 - SUS PO XML - Exception CX_SY_MOVE_CAST_ERROR</p>\r\n<p>2755805 - Follow-on note 2588195 (UPDATE_HEADER_PARTNERS)</p>\r\n<p>2794413 - Item sequence display problem after updating existing item lines</p>\r\n<p>2796059 - Follow-on note 2716194</p>\r\n<p>2837060 - SUS CF XML - Exception CX_SY_MOVE_CAST_ERROR</p>\r\n<p>2896890 - Some shopping cart items remain unprocessed after executing report bbp_get_status_2</p>\r\n<p>2914242 - Items order distorted when exlin is not supplied</p>\r\n<p>2930274 - Follow-on note 2441656 [memory allocation issue when iv_parent_guid is not supplied]</p>\r\n<p>&#160;</p>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 17 (10/2018)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 17 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 21. For more information about SAP SRM 7.0 SPS21, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;20 (08/2018). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 17 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 17.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP17 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS17 -- SRM 7.0 EHP2 SPS22 -- SRM 7.0 EHP1 SPS17 -- SRM 7.0 SPS21 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p>2962112 - Performance issue while message processing in itemlist_f_check</p>\r\n<p>2967841 - 2 purchase orders assigned to 1 shopping cart item - BBP_GET_STATUS_2</p>\r\n<p>3020976 - Follow-on note 2317983 (expsv_item_type)<br /> 3027802 - Follow-on note 2457719 (ITAB_DUPLICATE_KEY in SELECT_FROM_PDACC)</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 17</strong></p>\r\n<p>2422294 - SUS PO XML - Exception CX_SY_MOVE_CAST_ERROR</p>\r\n<p>2692111 - Follow-on note 2588195 (object_type)</p>\r\n<p>2697115 - External Line Numbering related issues</p>\r\n<p>2730129 - Follow-on note 2588195 (BBP_CALCULATE_GRP_CND)</p>\r\n<p>2755805 - Follow-on note 2588195 (UPDATE_HEADER_PARTNERS)</p>\r\n<p>2837060 - SUS CF XML - Exception CX_SY_MOVE_CAST_ERROR</p>\r\n<p>2896890 - Some shopping cart items remain unprocessed after executing report bbp_get_status_2</p>\r\n<p>2914242 - Items order distorted when exlin is not supplied</p>\r\n<p>2930274 - Follow-on note 2441656 [memory allocation issue when iv_parent_guid is not supplied]</p>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 16 (03/2018)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 16 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 21. For more information about SAP SRM 7.0 SPS21, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 19 (02/2018). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 16 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 16.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP16 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS16 -- SRM 7.0 EHP2 SPS21 -- SRM 7.0 EHP1 SPS17 -- SRM 7.0 SPS21 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p>&#160;</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 16</strong></p>\r\n<p>&#160;</p>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 15 (01/2018)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 15 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 20. For more information about SAP SRM 7.0 SPS20, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 18 (10/2017). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 15 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 15.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP15 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS15 -- SRM 7.0 EHP2 SPS20 -- SRM 7.0 EHP1 SPS16 -- SRM 7.0 SPS20 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p>&#160;</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 15</strong></p>\r\n<p>&#160;</p>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 14 (07/2017)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 14 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 20. For more information about SAP SRM 7.0 SPS20, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 17 (05/2017). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 14 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 14.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP14 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS14 -- SRM 7.0 EHP2 SPS19 -- SRM 7.0 EHP1 SPS16 -- SRM 7.0 SPS20 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p>&#160;</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 14</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;3 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 13 (01/2017)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 13 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 19. For more information about SAP SRM 7.0 SPS19, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 16 (11/2016). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 13 of Enhancement Package 3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;122</strong> of SAP HANA platform software SP stack 12. For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em></a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . SAP HANA database 2.0 is not released for Enhancement Package&#160;3 for SAP SRM 7.0.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 13.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP13 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS13 -- SRM 7.0 EHP2 SPS18 -- SRM 7.0 EHP1 SPS15 -- SRM 7.0 SPS19 -- SRM 5.0 SPS25</p>\r\n<p><strong>Note:</strong> Starting with&#160;SP-Stack&#160;13, when you display a Shopping Cart Template from Shopping Cart Monitor, it will be displayed using the SC Template iView. For that, it is required that you perform the manual activity of note 2359568</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p>&#160;</p>\r\n<p><strong><strong>For customers using Live Auction Cockpit (LAC), please refer the below note:</strong></strong></p>\r\n<p>2369341 - New Live Auction application - DHTML Technology</p>\r\n<p><strong>Changes made after release of SP stack 13</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><strong><strong>___________________________________________________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 12 (07/2016)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 12 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 19. For more information about SAP SRM 7.0 SPS19, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 15 (06/2016). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 12 of Enhancement Package&#160;3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;85</strong>&#160;of SAP HANA platform software SP stack 08<strong>.&#160;</strong>For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 12.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS12 -- SRM 7.0 EHP2 SPS17 -- SRM 7.0 EHP1 SPS15 -- SRM 7.0 SPS19 -- SRM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>Changes made after release of SP stack 12</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong><strong>_________________________________________________________________________________________________</strong></strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 11 (01/2016)</strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 11 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 18. For more information about SAP SRM 7.0 SPS18, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 13 (11/2015). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 11 of Enhancement Package&#160;3 for SAP SRM 7.0 check if the SAP&#160;<strong>HANA</strong>&#160;database was already updated to the required minimum&#160;<strong>Revision&#160;85</strong>&#160;of SAP HANA platform software SP stack 08<strong>.&#160;</strong>For more details, please refer to the&#160;<a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em>&#160;</a>&#160;documented on SAP Service Marketplace and SAP note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 11.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to&#160;<strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note&#160;<a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes&#160;<a target=\"_blank\" href=\"/notes/2086899\">2086899</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP&#160;<strong>Fiori</strong>&#160;for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a>&#160;concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP11 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS11 -- SRM 7.0 EHP2 SPS16 -- SRM 7.0 EHP1 SPS14 -- SRM 7.0 SPS18 -- SRM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<p><strong>Changes made after release of SP stack 11</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><strong>___________________________________________________________________________________________________</strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 10 (10/2015)</span></strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 10 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 18. For more information about SAP SRM 7.0 SPS18, see SAP Note <a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 12 (08/2015). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 10 of Enhancement Package&#160;3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 10.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP <strong>Fiori</strong> for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP10 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7.0 EHP3 SPS10 -- SRM 7.0 EHP2 SPS15 -- SRM 7.0 EHP1 SPS14 -- SRM 7.0 SPS18 -- SRM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2214033\">2214033</a></p>\r\n</td>\r\n<td>\r\n<p>Contract Negotiation Cockpit - Bug fixes</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2219454\">2219454</a></p>\r\n</td>\r\n<td>\r\n<p>Contract Negotiation Performance Improvements</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2217713\">2217713</a></p>\r\n</td>\r\n<td>\r\n<p>Service item is not marked as closed in Purchase Requisition</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after release of SP stack 10</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong><span style=\"text-decoration: underline;\"><strong><span style=\"text-decoration: underline;\">___________________________________________________________________________________________________</span></strong></span></strong></span></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 09 (07/2015)</span></strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 09 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 18. For more information about SAP SRM 7.0 SPS18, see SAP Note <a target=\"_blank\" href=\"/notes/2066081\">2066081</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 11 (06/2015). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 09 of Enhancement Package&#160;3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 09.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP10 or higher</strong>&#160;requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes <a target=\"_blank\" href=\"/notes/2086899\">2086899</a> and <a target=\"_blank\" href=\"/notes/2153150\">2153150</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP <strong>Fiori</strong> for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP09 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">SRM 7.0 EHP3 SPS09 -- SRM 7.0 EHP2 SPS15 -- SRM 7.0 EHP1 SPS14 -- SRM 7.0 SPS18 -- SRM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Strategic Sourcing&#160;</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2164200\">2164200</a></p>\r\n</td>\r\n<td>\r\n<p>GETWA_NOT_ASSIGNED dump while editing RFx</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2186019\">2186019&#160;</a></p>\r\n</td>\r\n<td>\r\n<p>Requirement sent back to sourcing in case of follow-on RFx creation</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Operational <br /></strong><strong>Procurement</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2178777\">2178777</a></p>\r\n</td>\r\n<td>\r\n<p>History: Missing confirmation in case servicesHistory: Missing confirmation in case services</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Self-Service <br />Procurement</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2185054\">2185054</a></p>\r\n</td>\r\n<td>\r\n<p>Dump occurs when navigating from POWL to SC details</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Contract Management&#160;</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2179101\">2179101&#160;</a></p>\r\n</td>\r\n<td>\r\n<p>Contract not locked during approval</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2183488\">2183488&#160;</a></p>\r\n</td>\r\n<td>\r\n<p>Dump during the assignment of contract data to data of a follow up document</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after release of SP stack 09</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong><span style=\"text-decoration: underline;\">___________________________________________________________________________________________________</span></strong></span></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 08 (04/2015)</span></strong></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 08 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 17. For more information about SAP SRM7.0 SPS17, see SAP Note <a target=\"_blank\" href=\"/notes/2005636\">2005636</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 10 (03/2015). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 08 of Enhancement Package&#160;3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;85</strong> of SAP HANA platform software SP stack 08<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> .</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 08.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The SUM based import may stop in the activation phase with DDL source activation error. Error and workaround is described in Notes 2086899 and 2153150.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP <strong>Fiori</strong> for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP08 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">SRM 7.0 EHP3 SPS08 -- SRM 7.0 EHP2 SPS14 -- SRM 7.0 EHP1 SPS13 -- SRM 7.0 SPS17 -- SRM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Contract Release Value</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2152316\">2152316</a></p>\r\n</td>\r\n<td>\r\n<p>CCM : Incorrect Release order values sent from ERP to SRM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2143651\">2143651</a></p>\r\n</td>\r\n<td>\r\n<p>Deletion of service lines does not delete the service lines.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2153309\">2153309</a></p>\r\n</td>\r\n<td>\r\n<p>Contract Release Value Updates not happening at header in specific cases</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2157109\">2157109</a></p>\r\n</td>\r\n<td>\r\n<p>Contract releae values not updated properly for free text items from catalog</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Enhance Existing Documents</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2150172\">2150172</a></p>\r\n</td>\r\n<td>\r\n<p>IMG node is not accessible for Enhancement of Follow-on Documents</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Large Document Handling for PO</strong></p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2149670\">2149670</a></p>\r\n</td>\r\n<td>\r\n<p>Issue with adding service items from catalog items</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2157077\">2157077</a></p>\r\n</td>\r\n<td>\r\n<p>Purchase Order does not open in LDH mode</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Self Service Procurement Classic</strong></p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2145380\">2145380</a></p>\r\n</td>\r\n<td>\r\n<p>Team Carts not getting displayed</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after release of SP stack 08</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"text-decoration: underline;\"><strong><span style=\"text-decoration: underline;\"><strong>___________________________________________________________________________________________________</strong></span></strong></span></strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 07 (01/2015)</strong></span></strong></span></p>\r\n<p><strong>Installation Requirements:</strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 07 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 17. For more information about SAP SRM7.0 SPS17, see SAP Note <a target=\"_blank\" href=\"/notes/2005636\">2005636</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 09 (11/2014). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 07 of Enhancement Package&#160;3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a>). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 07.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP <strong>Fiori</strong> for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a> concerning interoperability.</p>\r\n<p>Update to ERP 6.0 EHP7 SP07 is possible both via SUM and SPAM. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>. Only the SP update from SP06 to SP07 is possible via SPAM.</p>\r\n<p>For Classic Purchase Order Creation with Free Text Service Items with Central Contract as Source of Supply there are inconsistencies in the creation of Classic Purchase Order in this scenario. Please refer to the note <a target=\"_blank\" href=\"/notes/2122742\">2122742 </a>for details.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP04 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>SRM 7. 0 EHP3 SPS07 -- SRM 7.0 EHP2 SPS14 -- SRM 7.0 EHP1 SPS13 -- SRM 7.0 SPS17 -- SRM 5.0 SPS25</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong><strong>SAP Note</strong></strong></p>\r\n</td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Contract Mass Update</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2116903\">2116903</a></td>\r\n<td>Work item in purchaser inbox remains though contract document is moved from awaiting approval status to saved status</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2113384\">2113384</a></td>\r\n<td>Handling checkbox 'Save Status For Mass Contracts' based on switch status</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Contract Release Value</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2112995\">2112995</a></td>\r\n<td>Issues in Contract release Value update</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2113284\">2113284</a></td>\r\n<td>Release Value update at Header level and sub outlines of contract have issues</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1621788\">1621788</a></td>\r\n<td>SAP SRM 7.0 EHP2 and EHP3: Important info about scenarios</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2113811\">2113811</a></td>\r\n<td>CCM: Incorrect Release value in change mode</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1758374\">1758374</a></td>\r\n<td>Central Contract Release value updation fails for Outlines</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2115122\">2115122</a></td>\r\n<td>FAQ: Contract Release Value Updates for Hierarchical Service Items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2091653\">2091653</a></td>\r\n<td>Unable to add item in purchase order with error at header level</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2118514\">2118514</a></td>\r\n<td>Contract Release Value not updated when service outline assigned in Shopping cart and issues in Service details pop up</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2119184\">2119184</a></td>\r\n<td>Product ID search for contract fails</td>\r\n</tr>\r\n<tr>\r\n<td><strong>LDH Contracts</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2107302\">2107302</a></td>\r\n<td>Issues with target value and clear search criteria in LDH Contracts</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2112924\">2112924</a></td>\r\n<td>Background Check for Large Contract Document is failing</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2113043\">2113043</a></td>\r\n<td>Search of items giving wrong results for Contract opened by Approver</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2106494\">2106494</a></td>\r\n<td>Issues with target value and email notification in LDH scenario</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2110437\">2110437</a></td>\r\n<td>LDH: Blank mail is send to user for background Check and background deletion of items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2110088\">2110088</a></td>\r\n<td>LDH: Blank mail is send to user for background Check and background deletion of items</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2106494\">2106494</a></td>\r\n<td>Issues with target value and email notification in LDH scenario</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2107183\">2107183</a></td>\r\n<td>Incorrect sorting of items during contract transfer</td>\r\n</tr>\r\n<tr>\r\n<td><strong>LDH RFx</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2107299\">2107299</a></td>\r\n<td>LDH RFx Interface for Calendar pop out missing</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2107363\">2107363</a></td>\r\n<td>Calendar Pop Out Missing in delivery date search field in LDH RFx</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2107707\">2107707</a></td>\r\n<td>Multiple item count detail in header area in RFx</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2113880\">2113880</a></td>\r\n<td>Change version of RFx is not getting published if document is opened in LDH mode</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SC New Field</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2113801\">2113801</a></td>\r\n<td>SRM version does not match applet version Error</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2108193\">2108193</a></td>\r\n<td>New Field in shopping cart for follow-on documents - some improvements</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SOCO Rejection Reason</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2111390\">2111390</a></td>\r\n<td>SOCO Rejection Reason : The Rejection Text is not updated in Purchase Requisition when an item is rejected at SRM sourcing cock-pit</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Sourcing with RFx</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2062854\">2062854</a></td>\r\n<td>Dump while paying tender fees in bidder</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1821664\">1821664</a></td>\r\n<td>RFX response version number is incorrect</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2112705\">2112705</a></td>\r\n<td>Success message after approval or rejection of Response</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Update</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1751237\">1751237</a></td>\r\n<td>Add. info about the update/upgrade to SAP NetWeaver 7.4 (incl. SPs and SRs)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after release of SP stack 07</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>___________________________________________________________________________________________________</strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT RELEASE 2 (12/2014)</strong></span></p>\r\n<p><br />SR2: Enhancement Package&#160;3 for SAP&#160;SRM 7.0 with Support Release&#160;2 includes the support packages up to SP06. If you perform an update with target release&#160;EHP3 for SAP&#160;SRM 7.0 SR2, your system goes immediately to EHP3 for SRM 7.0 SP06 and SAP NetWeaver 7.40 SP08.</p>\r\n<p>With the availability of Support Release 2, the DVDs of the predecessor Support Release 1 have been archived. If you have already started upgrading a system with the DVDs of Support Release 1, you can still select the corresponding valid Support Package Stacks (03 &#8211; 05) in Maintenance Planner for a transition phase.</p>\r\n<p><strong>Changes made after release of SR 2</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p>___________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 06 (10/2014)</strong></span></p>\r\n<p><strong>I</strong><strong><strong>nstallation Requirements:</strong></strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 06 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 17. For more information about SAP SRM7.0 SPS17, see SAP Note <a target=\"_blank\" href=\"/notes/2005636\">2005636</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 08 (09/2014). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 06 of Enhancement Package&#160;3 for SAP SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimum <strong>Revision&#160;82</strong> of SAP HANA platform software SP stack 08 (see SAP note <a target=\"_blank\" href=\"/notes/2047977\">2047977</a> ). This SAP HANA database revision has been verified in SAP production systems<strong>. </strong>For more details, please refer to the <a target=\"_blank\" href=\"http://service.sap.com/&#126;sapidb/011000358700001182742013\"><em>SAP HANA revision and maintenance strategy</em> </a>&#160;documented on SAP Service Marketplace and SAP note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a> . Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 06.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>The update to <strong>NetWeaver 7.40 SP08</strong> requires the minimal release of database systems as described in SAP note <a target=\"_blank\" href=\"/notes/1951491\">1951491</a>. Please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/2069621\">2069621</a>&#160;before upgrading your system to NetWeaver 7.40 SP08.</p>\r\n<p>The implementation of SP Stack 06 is only possible via <strong>SUM</strong> tool and not as for previous support packages via SPAM as well. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>.</p>\r\n<p>Before performing a <strong>system copy</strong> check that&#160;you use the correct SAP Kernel medium for the target system installation. For more details please refere to SAP note&#160;<a target=\"_blank\" href=\"/notes/1738258\">1738258</a>. If your system is still non-unicode and you have updated it to SP Stack 06 of EHP3 for SAP&#160;SRM 7.0, it is currently not possible to do a <strong>heterogeneous </strong>system copy (i.e. changing the database product). For details see SAP note <a target=\"_blank\" href=\"/notes/2054965\">2054965</a>.</p>\r\n<p>In case you use the product version&#160;UI FOR EHP3 FOR SAP SRM 7.0 (SAP <strong>Fiori</strong> for SAP&#160;SRM 1.0), please have a look at SAP note&#160;<a target=\"_blank\" href=\"/notes/1914502\">1914502</a> concerning interoperability.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP04 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SRM 7. 0 EHP3 SPS06 -- SRM 7.0 EHP2 SPS13 -- SRM 7.0 EHP1 SPS13 -- SRM 7.0 SPS17 -- SRM 5.0 SPS24</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong><strong>SAP Note</strong></strong></p>\r\n</td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Upgrade</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2069621\">2069621</a></td>\r\n<td>ACT_UPG: View \"/SAPSRM/CTRHDR_D\" could not be activated</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Pricing conditions &amp; scales</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2070853\">2070853</a></td>\r\n<td>Contract creation result in update termination error</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2070853\">2052543</a></td>\r\n<td>Conditions not updated when updating Outline Agreement in parallel mode (RFC, SOA)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2070853\">2055770</a></td>\r\n<td>Percentage based header conditions have incorrect value</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2070853\">2078079</a></td>\r\n<td>Header condition changes alone not updated for contract</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Self Sevice Procurement Classic</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2069673\">2069673</a></td>\r\n<td>Dump while creating easy or one step shopping cart</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2078547\">2078547</a></td>\r\n<td>Important information regarding Formatted text modules and Additional status for RFx</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>There is a change in the behavior for creation of PO in ME21N, please refer to the below notes for details:</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1881882\">1881882</a></td>\r\n<td>PO item displays contract info although user not authorized</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2055770\">2055770</a></td>\r\n<td>Percentage based header conditions have incorrect value</td>\r\n</tr>\r\n<tr>\r\n<td><strong><strong>Self Sevice Procurement <br />Extended Classic</strong></strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2061880\">2061880</a></td>\r\n<td>PDO Layer error when clicking on Related Links -&gt; Display Purchase Order in Confirmation document</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2072403\">2072403</a></td>\r\n<td>\r\n<p>Decide which shopping cart (WD or UI5) should launch from NWB desktop</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Strategic Sourcing with RFx</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2043247\">2043247</a></td>\r\n<td>Generation of random password and sending them over e-mail is the only default option for report /SAPSRM/REPLICATE_BIDDERS</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2074544\">2074544</a></td>\r\n<td>Dump is issued before XML for PurchaseRequestERPSourcingConfirmation_Out is triggered</td>\r\n</tr>\r\n<tr>\r\n<td><strong>DPP</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2011539\">2011539</a></td>\r\n<td>End of Purpose check &amp; Archiving of SRM business partners &amp; SRM documents</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2078946\">2078946</a></td>\r\n<td>Important Information regarding switch activation</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2079206\">2079206</a></td>\r\n<td>ILM: Considering condition fields of policies of residence rules category during writing of archive files</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Ended RFx</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2078547\">2078547</a></td>\r\n<td>Important information regarding Formatted text modules and Additional status for RFx</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Portal</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2021994\">2021994</a>&#160;</td>\r\n<td>Malfunctioning of Portal due to omission of post parameters</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2055770\">2055770</a></td>\r\n<td>Contextual Navigation Panel (CNP) is not displayed in AFP</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Security</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2080679\">2080679</a></td>\r\n<td>Missing authorization check in AP-MD-BP</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2079818\">2079818</a></td>\r\n<td>Missing authorization check in SRM-EBP-ADM-XBP</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2080283\">2080283</a></td>\r\n<td>Missing authorization check in AP-MD-BP</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Additional Information</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2082526\">2082526</a></td>\r\n<td>Important Information for customers Upgrading to SRM 7.03 SP06</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 06:</strong></p>\r\n<p>Added notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2082526\">2082526</a></td>\r\n<td>Important Information for customers Upgrading to SRM 7.03 SP06</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2021994\">2021994</a>&#160;</td>\r\n<td>Malfunctioning of Portal due to omission of post parameters</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2055770\">2055770</a></td>\r\n<td>Contextual Navigation Panel (CNP) is not displayed in AFP</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>There is a change in the behavior for creation of PO in ME21N, please refer to the below notes for details:</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1881882\">1881882</a></td>\r\n<td>PO item displays contract info although user not authorized</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2055770\">2055770</a></td>\r\n<td>Percentage based header conditions have incorrect value</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2070853\">2052543</a></td>\r\n<td>Conditions not updated when updating Outline Agreement in parallel mode (RFC, SOA)</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2070853\">2055770</a></td>\r\n<td>Percentage based header conditions have incorrect value</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2070853\">2078079</a></td>\r\n<td>Header condition changes alone not updated for contract</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2061880\">2061880</a></td>\r\n<td>PDO Layer error when clicking on Related Links -&gt; Display Purchase Order in Confirmation document</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2072403\">2072403</a></td>\r\n<td>\r\n<p>Decide which shopping cart (WD or UI5) should launch from NWB desktop</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li>The <strong>EHP update</strong> is released since 04.11.2014.</li>\r\n<li>The <strong>upgrade </strong>to Suite7i2013 SPS06 is released since 04.12.2014.</li>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p>**************************************************************************************************************************************************</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 04 (04/2014)</strong></span></p>\r\n<p><strong>I</strong><strong><strong>nstallation Requirements:</strong></strong></p>\r\n<p>Enhancement Package&#160;3 for SAP SRM SP Stack 04 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 16. For more information about SAP SRM7.0 SPS16, see SAP Note <a target=\"_blank\" href=\"/notes/1972688\">1972688</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 06 (03/2014). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Attention: In case you use the product version \"UI FOR EHP3 FOR SAP SRM 7.0\" (SAP <strong>Fiori</strong> for SAP SRM 1.0), please be aware that the SP Stack 04 does only work with \"UI FOR EHP3 FOR SAP SRM 7.0\" SP02 or SP01. The combination between SRM EhP3 SP04 and \"UI FOR EHP3 FOR SAP SRM 7.0\" SP00&#160;is not supported.</p>\r\n<p>Before update to SP Stack 04 of Enhancement Package&#160;3 for SAP&#160;SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimal <strong>revision 74</strong> of SP stack 07 of SAP HANA database (see SAP note <a target=\"_blank\" href=\"/notes/2003736\">2003736</a>). SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. For more details, please refer to the <em>SAP HANA revision strategy</em> document on SAP Service Marketplace at <a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">https://service.sap.com/&#126;sapidb/011000358700001182742013</a>. Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 04.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p><strong>PO Header Condition with Pricing/Condition/Scales</strong>:</p>\r\n<p>As part of EHP3 for SAP SRM 7.0 SP02, a new feature to provide Header Conditions in SRM Purchase Order was released to customers. If a customer is using this feature in EHP3 for SAP SRM 7.0 SP02 or SP03 after activating the business function switch SRM_700_PO_HEADER_PRICING. It is not recommended for such customers to upgrade to SRM703 SP04. There could be some inconsistencies in SRM Purchase Order if the customer is using Purchase Order with header conditions &amp; scales.</p>\r\n<p><strong>Classic PO creation from SRM with reference to central contract with free text items</strong></p>\r\n<p>If there is a shopping cart created with free text items with reference to a central contract or a combination of free text items and service master with reference to a central contract which results in classic purchase order creation from SRM, the service items are created but the service lines are not created in ERP purchase Order.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP04 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SRM 7. 0 EhP3 SPS04 -- SRM 7.0 EHP2 SPS11 -- SRM 7.0 EHP1 SPS12 -- SRM 7.0 SPS16 -- SRM 5.0 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong><strong>SAP Note</strong></strong></p>\r\n</td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Upgrade</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2069621\">2069621</a></td>\r\n<td>ACT_UPG: View \"/SAPSRM/CTRHDR_D\" could not be activated</td>\r\n</tr>\r\n<tr>\r\n<td><strong><strong>Catalog Content Management</strong></strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1938462</td>\r\n<td>SAP NetWeaver MDM 7.1 SP12 Release</td>\r\n</tr>\r\n<tr>\r\n<td>2009612</td>\r\n<td>PurchaseOrderERPRequest_V1: Prices not fetched from central contract for service master services</td>\r\n</tr>\r\n<tr>\r\n<td>2009715</td>\r\n<td>BAPI: MEPO 601 issued during PO creation from shopping cart</td>\r\n</tr>\r\n<tr>\r\n<td>2011896</td>\r\n<td>Free text contract item reference in classic purchase order</td>\r\n</tr>\r\n<tr>\r\n<td>1649374</td>\r\n<td>PurchaseOrderERPRequest_In_V1: Contract reference</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Self Service Procurement Classic</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1621788</td>\r\n<td>SAP SRM 7.0 EHP2 and EHP3: Important info about scenarios</td>\r\n</tr>\r\n<tr>\r\n<td>1993876</td>\r\n<td>Manual Price overwrites by Catalog Price in PO and Manual price overwrites by Contract Price in SC</td>\r\n</tr>\r\n<tr>\r\n<td>2005524</td>\r\n<td>Buy on behalf and Team shopping carts are not visible in POWL</td>\r\n</tr>\r\n<tr>\r\n<td>2005854</td>\r\n<td>Harmonized substitution: Team Cart option is not appearing in Shopping Cart even if substitutes are maintained</td>\r\n</tr>\r\n<tr>\r\n<td>2007628</td>\r\n<td>Return delivery cannot be posted in backend system as reason&#160;for return delivery is empty</td>\r\n</tr>\r\n<tr>\r\n<td>2010049</td>\r\n<td>\r\n<p>New row with soco substitution enabled automatically on changing the start date of existing substitute</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SSP ExtendedClassic</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>1599159</p>\r\n</td>\r\n<td>Internet Explorer 9 Release Notes</td>\r\n</tr>\r\n<tr>\r\n<td>1846225</td>\r\n<td>Deleted items show up in PO POWL</td>\r\n</tr>\r\n<tr>\r\n<td>1942187</td>\r\n<td>ESH: Snapshot search</td>\r\n</tr>\r\n<tr>\r\n<td>1960230</td>\r\n<td>Cannot save \"Reason for return delivery\"</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Strategic Sourcing with Auction</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1727026</td>\r\n<td>FAQ SRM workflow</td>\r\n</tr>\r\n<tr>\r\n<td>1998043</td>\r\n<td>Live Auction Cockpit - Java 7 Update 51 - Manifest file Permission Issue</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Strategic Sourcing with RFx</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1523906</td>\r\n<td>RFx Response Submission Outside Firewall: Bidder Transfer</td>\r\n</tr>\r\n<tr>\r\n<td>1806151</td>\r\n<td>Title is missing while creating SU01 Id for contact person</td>\r\n</tr>\r\n<tr>\r\n<td>1895293</td>\r\n<td>Failure of Contact person Replication in Bid-Decoupling</td>\r\n</tr>\r\n<tr>\r\n<td>1955956</td>\r\n<td>Application log not found in main memory</td>\r\n</tr>\r\n<tr>\r\n<td>1957352</td>\r\n<td>Deletion of references for logical expressions can be activated</td>\r\n</tr>\r\n<tr>\r\n<td>1958636</td>\r\n<td>Email can be triggered once the report \"/SAPSRM/REPLICATE_BIDDERS\"</td>\r\n</tr>\r\n<tr>\r\n<td>2001935</td>\r\n<td>Blank mail notification for the contact person creation in bidder replication</td>\r\n</tr>\r\n<tr>\r\n<td>2002314</td>\r\n<td>Performance issue when sending items to external sourcing</td>\r\n</tr>\r\n<tr>\r\n<td>2007485</td>\r\n<td>External source of supply tab visible although not relevant</td>\r\n</tr>\r\n<tr>\r\n<td>2011439</td>\r\n<td>Authorization missing in Role /SAPSRM/ADMINISTRATOR_EHP1 to create Contact Person</td>\r\n</tr>\r\n<tr>\r\n<td>2043247</td>\r\n<td>Generation of random password and sending them over e-mail is the only default option for report /SAPSRM/REPLICATE_BIDDERS</td>\r\n</tr>\r\n<tr>\r\n<td><strong>PO Header Conditions</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1602954</td>\r\n<td>Price condition is lost in change version that has contract</td>\r\n</tr>\r\n<tr>\r\n<td>1712226</td>\r\n<td>Important Information about Strategic Sourcing Scenario</td>\r\n</tr>\r\n<tr>\r\n<td>1812839</td>\r\n<td>PO Net value is not changed when item quantity is changed</td>\r\n</tr>\r\n<tr>\r\n<td>1969432</td>\r\n<td>Update of condition value does not update total value of RFx Response</td>\r\n</tr>\r\n<tr>\r\n<td>1970008</td>\r\n<td>Invalidating Shared Memory due to Meta Data for Pricing</td>\r\n</tr>\r\n<tr>\r\n<td>1970547</td>\r\n<td>Changes made to PO condtion on a Change Version is lost during Order</td>\r\n</tr>\r\n<tr>\r\n<td>1971773</td>\r\n<td>Configuration steps for percentage condition at header level in purhcase order</td>\r\n</tr>\r\n<tr>\r\n<td>1979337</td>\r\n<td>Better conflict handling for text table components</td>\r\n</tr>\r\n<tr>\r\n<td>1984104</td>\r\n<td>Price conditions tab is does not work as per metadata</td>\r\n</tr>\r\n<tr>\r\n<td>1984528</td>\r\n<td>Error in Purchase Order</td>\r\n</tr>\r\n<tr>\r\n<td>1984538</td>\r\n<td>SRM713_SP03_T061_Error:Not able to update PO Price(manual)</td>\r\n</tr>\r\n<tr>\r\n<td>1985034</td>\r\n<td>Contract reference issue for PO with SOS from Contract in Change Version</td>\r\n</tr>\r\n<tr>\r\n<td>1985036</td>\r\n<td>Restrict Contract Condition from display on PO Header Condition tab</td>\r\n</tr>\r\n<tr>\r\n<td>1993876</td>\r\n<td>Manual Price overwrites by Catalog Price in PO and Manual price overwrites by Contract Price in SC</td>\r\n</tr>\r\n<tr>\r\n<td>2000557</td>\r\n<td>Total Value of RFx response not getting updated</td>\r\n</tr>\r\n<tr>\r\n<td>2004603</td>\r\n<td>Total value of SC is zero</td>\r\n</tr>\r\n<tr>\r\n<td>2006636</td>\r\n<td>Price condition cannot be changed</td>\r\n</tr>\r\n<tr>\r\n<td>2008025</td>\r\n<td>Gross Price update for PO Items during item delete in Change version</td>\r\n</tr>\r\n<tr>\r\n<td>2008231</td>\r\n<td>Notes and attachments tab is not visible if user closes and opens the item details</td>\r\n</tr>\r\n<tr>\r\n<td>2010137</td>\r\n<td>Price Conditions lost on making changes in attachments tab of purchase order</td>\r\n</tr>\r\n<tr>\r\n<td>1959126</td>\r\n<td>Unwanted contract relevant information update in PO</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 04:</strong></p>\r\n<p>Added note</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2069621\">2069621</a></td>\r\n<td>ACT_UPG: View \"/SAPSRM/CTRHDR_D\" could not be activated</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</p>\r\n<p>__________________________________________________________________________________________________________________________</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 03 (01/2014)</strong></span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>I<strong>nstallation Requirements: </strong></strong></span></p>\r\n<p>Enhancement Package 3 for SAP SRM SP Stack 03 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 15. For more information about SAP SRM7.0 SPS15, see SAP Note <a target=\"_blank\" href=\"/notes/1789402\">1789402</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 05 (12/2013). For moreInformation about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The <strong>upgrade, <strong>EHP update </strong></strong>and the<strong><strong> installation</strong> </strong>to Suite7i2013 SPS03 is released with Support Release 1 (SR01).</p>\r\n<p>The implementation of SP Stack 03 is only possible via <strong>SUM</strong> tool and not as for previous support packages via SPAM as well. For more information please refer to SAP Note <a target=\"_blank\" href=\"/notes/1803986\">1803986</a>.</p>\r\n<p>You run your SAP NetWeaver 7.40 Java system (with an application from EHP3) on Hana database. If you want to update your system to Enhancement Package 3 SP stack 03, your Hana DB shall not be on revision 69 or 70 but it shall be on revision 63. You can find further details in SAP note <a target=\"_blank\" href=\"/notes/1968009\">1968009</a>. Using <strong>Software Logistics Toolset 1.0</strong> with Software Update Manager (SUM) 1.0 SP13 you have to update SAP HANA platform software to minimum <strong>Revision&#160;85</strong>.</p>\r\n<p>Attention: In case you use the product version &#8216;UI FOR EHP3 FOR SAP&#160;SRM 7.0&#8217; (SAP <strong>Fiori</strong> for SAP SRM1.0), please be aware that the SP03-stack does only work with &#8216;&#8216;UI FOR EHP3 FOR SAP SRM 7.0&#8217; SP01. The combination between EhP3 SP03 and &#8216;UI FOR EHP3 FOR SAP SRM 7.0&#8217; SP00 is not supported.</p>\r\n<p>Before update to SP Stack 03 of Enhancement Package&#160;3 for SAP&#160;SRM 7.0 check if the SAP <strong>HANA</strong> database was already updated to the required minimal <strong>revision 70</strong> of SP stack 07 of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner.</p>\r\n<p>The following is only relevant if you do <span style=\"text-decoration: underline;\">not use BW</span> in the system you want to update: Please follow SAP note&#160;<a target=\"_blank\" href=\"/notes/1629923\">1629923</a> to avoid activation issues in the XPRA phase of the update.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>SAP <strong>HANA database 2.0</strong> SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 03.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP02 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SRM 7. 0 EhP3 SPS03 -- SRM 7.0 EHP2 SPS10 -- SRM 7.0 EHP1 SPS11 -- SRM 7.0 SPS15 -- SRM 5.0 SPS23</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to scenarios:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong><strong>SAP Note</strong></strong></p>\r\n</td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Central substitution</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1900262</td>\r\n<td>Information on central substitution</td>\r\n</tr>\r\n<tr>\r\n<td><strong><strong>Purchase Order </strong>Header</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1522337</td>\r\n<td>Wrong Release Value in Contract</td>\r\n</tr>\r\n<tr>\r\n<td>1602954</td>\r\n<td>Price condition is lost in change version that has contract</td>\r\n</tr>\r\n<tr>\r\n<td>1712226</td>\r\n<td>Important Information about Strategic Sourcing Scen</td>\r\n</tr>\r\n<tr>\r\n<td>1812839</td>\r\n<td>PO Net value is not changed when item quantity is changed</td>\r\n</tr>\r\n<tr>\r\n<td>1851256</td>\r\n<td>Header Release Value of is not updating in PO</td>\r\n</tr>\r\n<tr>\r\n<td>1887741</td>\r\n<td>Contract release value updated twice from credit me</td>\r\n</tr>\r\n<tr>\r\n<td>1925290</td>\r\n<td>Several gross price conditions: issues in pricing and master data handling</td>\r\n</tr>\r\n<tr>\r\n<td>1952449</td>\r\n<td>Partner Name not copied to RFx from Shopping cart</td>\r\n</tr>\r\n<tr>\r\n<td>1961149</td>\r\n<td>Bid decoupled scenario: Responses with scales</td>\r\n</tr>\r\n<tr>\r\n<td>1961975</td>\r\n<td>Incorrect Header Target Value and Item price replic</td>\r\n</tr>\r\n<tr>\r\n<td>1963952</td>\r\n<td>Bonus/Statistical conditions deleted after SRM pric</td>\r\n</tr>\r\n<tr>\r\n<td>1965967</td>\r\n<td>CCM: Statistical conditions not determined when SRM</td>\r\n</tr>\r\n<tr>\r\n<td>1967607</td>\r\n<td>Error message not thrown while applying Header Disc</td>\r\n</tr>\r\n<tr>\r\n<td>1969432</td>\r\n<td>Update of condition value does not update total val</td>\r\n</tr>\r\n<tr>\r\n<td>1970008</td>\r\n<td>Invalidating Shared Memory due to Meta Data for Pricing</td>\r\n</tr>\r\n<tr>\r\n<td>1970547</td>\r\n<td>Changes made to PO condtion on a Change Version is lost during Order.</td>\r\n</tr>\r\n<tr>\r\n<td><strong>RFX</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1969374</td>\r\n<td>Performance issue while creating RFX from Sourcing</td>\r\n</tr>\r\n<tr>\r\n<td>1941803</td>\r\n<td>Application log is locked for a long time</td>\r\n</tr>\r\n<tr>\r\n<td>1928838</td>\r\n<td>DBSQL_DUPLICATE_KEY_ERROR on save of application</td>\r\n</tr>\r\n<tr>\r\n<td>1925253</td>\r\n<td>Application logs remain locked despite save</td>\r\n</tr>\r\n<tr>\r\n<td>1815604</td>\r\n<td>Follow on 1800317</td>\r\n</tr>\r\n<tr>\r\n<td>1920549</td>\r\n<td>Display of a log in editing doesn't work</td>\r\n</tr>\r\n<tr>\r\n<td>916019</td>\r\n<td>Deleting configurations and personalizations</td>\r\n</tr>\r\n<tr>\r\n<td>1959163</td>\r\n<td>PO link not updated in Rfx tracking tab when create</td>\r\n</tr>\r\n<tr>\r\n<td>2043247</td>\r\n<td>\r\n<p>Generation of random password and sending them over e-mail is the only default option for report /SAPSRM/REPLICATE_BIDDERS</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><strong>SSP classic</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1967529</td>\r\n<td>Express Confirmation should be enabled for Administ</td>\r\n</tr>\r\n<tr>\r\n<td>1966762</td>\r\n<td>Easy Shopping cart View opens from UWL</td>\r\n</tr>\r\n<tr>\r\n<td>1959912</td>\r\n<td>Easy Shopping cart open Professional shopping cart</td>\r\n</tr>\r\n<tr>\r\n<td>1823170</td>\r\n<td>Easy Shopping cart open Professional shopping cart</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Worlkload redistribution</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1962643</td>\r\n<td>Workload Redistribution: Wrong Purchasing Group</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Enterprise Search</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1966167</td>\r\n<td>Enterprise Search in SRM 703</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after Release of SP Stack 03:</strong></p>\r\n<p>The following is only relevant if you do not use BW in the system you want to update: <br />Please follow SAP note&#160;<a target=\"_blank\" href=\"/notes/1629923\">1629923</a> to avoid activation issues in the XPRA phase of the update.</p>\r\n<p>The <strong>upgrade, <strong>EHP update </strong></strong>and the<strong><strong> installation</strong> </strong>to Suite7i2013 SPS03 is released with Support Release 1 (SR01). <br />The restriction about the EHP update and upgrade are lifted see SAP note <a target=\"_blank\" href=\"/notes/1949802\">1949802</a>.(March 17, 2014).</p>\r\n<p>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</p>\r\n<p>__________________________________________________________________________________________________________________________</p>\r\n<p><strong>SUPPORT PACKAGE STACK 02 (11/2013)</strong><br /><br />As a special case, Support Package Stack 02 has to be applied with the installation of Enhancement Package 3 for SAP SRM 7.0.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SRM SP Stack 02 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 15. For more information about SAP SRM7.0 SPS15, see SAP Note <a target=\"_blank\" href=\"/notes/1789402\">1789402</a>. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 04 (10/2013). For more Information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 14.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p>&#160;<strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP02 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">SRM 7. 0 EhP3 SPS02 -- SRM 7.0 EHP2 SPS09 -- SRM 7.0 EHP1 SPS11 -- SRM 7.0 SPS15 -- SRM 5.0 SPS23</p>\r\n<p>&#160;</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to processes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SAP Note Number</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>CC</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1898319</td>\r\n<td>Harmonized Substitution Functionality Implementation</td>\r\n</tr>\r\n<tr>\r\n<td>1883870</td>\r\n<td>Implementation note for mandatory fields</td>\r\n</tr>\r\n<tr>\r\n<td>1893610</td>\r\n<td>Workload Redistribution in Sourcing Application</td>\r\n</tr>\r\n<tr>\r\n<td>1915327</td>\r\n<td>Purchase Order does not take over the price from RFx Respond</td>\r\n</tr>\r\n<tr>\r\n<td>1133821</td>\r\n<td>UWL Destination Service configuration</td>\r\n</tr>\r\n<tr>\r\n<td><strong>MDM/RFX</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1915278</td>\r\n<td>CX_SY_ITAB_LINE_NOT_FOUND when calling FuGr 2014</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Purchase Order header</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1929277</td>\r\n<td>PO Header condition - interface changes - Header condtion gets duplicated in item when header condtion is modified in change version</td>\r\n</tr>\r\n<tr>\r\n<td>1932239</td>\r\n<td>Header condtion gets duplicated in item when header condtion is modified in change version</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Self-Service Procurement</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1931812</td>\r\n<td>It is not possible to Approve multiple confirmation</td>\r\n</tr>\r\n<tr>\r\n<td>1895074</td>\r\n<td>Incorrect result on completed Shopping Carts in SC</td>\r\n</tr>\r\n<tr>\r\n<td>1911889</td>\r\n<td>Standard Mode support for WebDynpro Java application</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Sourcing with Auction</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1940427</td>\r\n<td>Jar signing issue when launching applet with Java 1.7 Update 45</td>\r\n</tr>\r\n<tr>\r\n<td>1932156</td>\r\n<td>Backend contract creation from accepted auction response leads to dump at ERP</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Sourcing with RFX</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1916248</td>\r\n<td>Version and change document of Tracking Tab open in</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>1910089</p>\r\n</td>\r\n<td>RFx tracking tab not updated with backend contract number</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>2043247</p>\r\n</td>\r\n<td>\r\n<p>Generation of random password and sending them over e-mail is the only default option for report /SAPSRM/REPLICATE_BIDDERS</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><strong><strong>&#160;<strong>Enterprise Search</strong></strong></strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&#160;1966167</td>\r\n<td>Enterprise Search in SRM 703</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Changes made after release of SP stack 02</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p><strong>*********************************************************************************************</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 01 (08/2013)</strong><br /><br />As a special case, Support Package Stack 01 has to be applied with the installation of Enhancement Package 3 for SAP SRM 7.0.</p>\r\n<p><strong>Installation Requirements</strong>:</p>\r\n<p>Enhancement Package 3 for SAP SRM SP Stack 01 is based on:</p>\r\n<ul>\r\n<li>SAP SRM 7.0 SP Stack 14. For more information about SAP SRM7.0 SPS13, see SAP Note 1650662. Refer to the corresponding Upgrade Master Guide for detailed information on EhP3. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/srm-inst\">http://service.sap.com/srm-inst</a>.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.40 SP Stack 03 (07/2013). For moreInformation about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>SAP HANA database 2.0 SP stack 00 Revision 02 or higher is released for SAP Enhancement Package&#160;3 for SAP SRM 7.0 SP Stack 14.&#160; <br />To learn about technical requirements to the database hardware and operating system with SAP HANA 2, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2426339\">2426339</a> (Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements). The contents of that note are of interest also for the operation of SAP&#160;SRM 7.0 EhP3 on SAP HANA 2.</p>\r\n<p><strong>SP Equivalence Levels</strong></p>\r\n<ul>\r\n<li>Be aware that the SAP Solution Manager - Maintenance Planner calculates the correct Support Packages for you.</li>\r\n</ul>\r\n<ul>\r\n<li>The SP equivalence levels for SAP SRM 7.0 EhP3 SP01 are listed below. This means that the correction levels are equivalent and also determines your upgrade path to any Enhancement Package:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SRM 7. 0 EhP3 SPS01 -- SRM 7.0 EHP2 SPS07 -- SRM 7.0 EHP1 SPS10 -- SRM 7.0 SPS14 -- SRM 5.0 SPS19</p>\r\n<p><strong>Notes to be applied as part of this Stack sorted according to processes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP Note Number</td>\r\n<td>Description</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>General Notes (process independent)</strong></td>\r\n</tr>\r\n<tr>\r\n<td>1850717</td>\r\n<td>Compatibility issues with new backend system</td>\r\n</tr>\r\n<tr>\r\n<td>1548532</td>\r\n<td>SRM-MDM Catalog 7.02 Repository information</td>\r\n</tr>\r\n<tr>\r\n<td>1523939</td>\r\n<td>SAP SRM 7.0 EhPxx: PPS-Important info on New</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Process dependent notes:</strong></td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>Catalog Content management:</td>\r\n</tr>\r\n<tr>\r\n<td>1894977</td>\r\n<td>Backend Contract from Service RFx response</td>\r\n</tr>\r\n<tr>\r\n<td>1803585</td>\r\n<td>Backend Contract XML doesnt have service line</td>\r\n</tr>\r\n<tr>\r\n<td>1888198</td>\r\n<td>Issues when upgrading from Catalog 701 to 702</td>\r\n</tr>\r\n<tr>\r\n<td>1856335</td>\r\n<td>UI Enhancement Administration page is empty</td>\r\n</tr>\r\n<tr>\r\n<td>1894271</td>\r\n<td>Sustainability data not uploaded into SRM MDM</td>\r\n</tr>\r\n<tr>\r\n<td>1748289</td>\r\n<td>Problem with uploading Sustainability record</td>\r\n</tr>\r\n<tr>\r\n<td>1727113</td>\r\n<td>MECCM: Missing items and messages when</td>\r\n</tr>\r\n<tr>\r\n<td>1713481</td>\r\n<td>MECCM: Upload of free text items without</td>\r\n</tr>\r\n<tr>\r\n<td>1887020</td>\r\n<td>Not able to launch the MDM catalog</td>\r\n</tr>\r\n<tr>\r\n<td>1599021</td>\r\n<td>Change Application Name property of SRM-MDM</td>\r\n</tr>\r\n<tr>\r\n<td>1249846</td>\r\n<td>Enhance Performance - Navigation between SRM</td>\r\n</tr>\r\n<tr>\r\n<td>1147103</td>\r\n<td>SRM-MDM Catalog: Portal Deployment Restriction</td>\r\n</tr>\r\n<tr>\r\n<td>1871053</td>\r\n<td>PO: Wrong net_price and total value when PO</td>\r\n</tr>\r\n<tr>\r\n<td>1869238</td>\r\n<td>Issues with Manual and Catalog price in SC and</td>\r\n</tr>\r\n<tr>\r\n<td>1710764</td>\r\n<td>Catalog doesnt support validation for Service</td>\r\n</tr>\r\n<tr>\r\n<td>1896090</td>\r\n<td>FAQ: APM creates follow-on document as already</td>\r\n</tr>\r\n<tr>\r\n<td>1895116</td>\r\n<td>BBP_BW_SC2/3/4 zeigen keine Folgebelege mehr</td>\r\n</tr>\r\n<tr>\r\n<td>1895044</td>\r\n<td>Create follow-on doc works if search criteria</td>\r\n</tr>\r\n<tr>\r\n<td>1885636</td>\r\n<td>Process type for classic PO from RFx Response</td>\r\n</tr>\r\n<tr>\r\n<td>1872435</td>\r\n<td>Help content update for Catalog Innovations</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>Contract Management:</td>\r\n</tr>\r\n<tr>\r\n<td>1876657</td>\r\n<td>PurchasingContractERPConfirmation_Out not</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>Multiple scenarios:</td>\r\n</tr>\r\n<tr>\r\n<td>1871053</td>\r\n<td>PO: Wrong net_price and total value when PO</td>\r\n</tr>\r\n<tr>\r\n<td>1869238</td>\r\n<td>Issues with Manual and Catalog price in SC and</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>PDP:</td>\r\n</tr>\r\n<tr>\r\n<td>1890295</td>\r\n<td>BSP exception: No structure component with</td>\r\n</tr>\r\n<tr>\r\n<td>1861165</td>\r\n<td>CL_THTMLB_CELLERATOR error in IC</td>\r\n</tr>\r\n<tr>\r\n<td>1857532</td>\r\n<td>CL_THTMLB_CELLERATOR error in pop up.</td>\r\n</tr>\r\n<tr>\r\n<td>1890295</td>\r\n<td>BSP exception: No structure component with</td>\r\n</tr>\r\n<tr>\r\n<td>1890294</td>\r\n<td>Errors during Unit of Measurement changes lead</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>Sourcing with Auction:</td>\r\n</tr>\r\n<tr>\r\n<td>1896298</td>\r\n<td>SAP SRM Auction Relication to SUS fails</td>\r\n</tr>\r\n<tr>\r\n<td>1894001</td>\r\n<td>Hierarchy of auctions items lost in quot in</td>\r\n</tr>\r\n<tr>\r\n<td>1890398</td>\r\n<td>Auction factors not working in bid decoupling</td>\r\n</tr>\r\n<tr>\r\n<td>1885636</td>\r\n<td>Process type for classic PO from RFx Response</td>\r\n</tr>\r\n<tr>\r\n<td>1846990</td>\r\n<td>Applet version does not match abap server</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>Sourcing with RFX:</td>\r\n</tr>\r\n<tr>\r\n<td>1839794</td>\r\n<td>Approval notififcations. Logon link failure.</td>\r\n</tr>\r\n<tr>\r\n<td>1894382</td>\r\n<td>Issue while creating Follow on RFx in</td>\r\n</tr>\r\n<tr>\r\n<td>1890542</td>\r\n<td>No created link between RFx response and</td>\r\n</tr>\r\n<tr>\r\n<td>1816189</td>\r\n<td>PurchaseOrderERPConfirmation_Out: SC has</td>\r\n</tr>\r\n<tr>\r\n<td>1827308</td>\r\n<td>PO creation fails when supplement item is</td>\r\n</tr>\r\n<tr>\r\n<td>1754887</td>\r\n<td>Product id copied from base item to expressive</td>\r\n</tr>\r\n<tr>\r\n<td>1883830</td>\r\n<td>OPS_SE_PUR_COMMON 001: Sourcing status not</td>\r\n</tr>\r\n<tr>\r\n<td>1872766</td>\r\n<td>External Sourcing status not updated,</td>\r\n</tr>\r\n<tr>\r\n<td>1727942</td>\r\n<td>APPL_COMMON 000 The content of the field Item</td>\r\n</tr>\r\n<tr>\r\n<td>1884311</td>\r\n<td>311&#160;&#160;&#160;&#160;Sourcing status remains on Pending for</td>\r\n</tr>\r\n<tr>\r\n<td>1895141</td>\r\n<td>Alignment of the text in Bid Invitation emails</td>\r\n</tr>\r\n<tr>\r\n<td>1874432</td>\r\n<td>Alignment of the text in Purchase Order emails</td>\r\n</tr>\r\n<tr>\r\n<td>1712226</td>\r\n<td>Important Information about Strategic Sourcing</td>\r\n</tr>\r\n<tr>\r\n<td>1656689</td>\r\n<td>Simulate and Create PO Button enabled for</td>\r\n</tr>\r\n<tr>\r\n<td>1895293</td>\r\n<td>Failure of Contact person Replication in Bid</td>\r\n</tr>\r\n<tr>\r\n<td>1523906</td>\r\n<td>RFx Response Submission Outside Firewall:</td>\r\n</tr>\r\n<tr>\r\n<td>1860839</td>\r\n<td>Syntax error in CL_WDR_SERVER_EVENT after</td>\r\n</tr>\r\n<tr>\r\n<td>1825893</td>\r\n<td>Dumps when testing Web services in transaction</td>\r\n</tr>\r\n<tr>\r\n<td>1869110</td>\r\n<td>Search in Web Service Configuration</td>\r\n</tr>\r\n<tr>\r\n<td>2043247</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SSP classic:</td>\r\n</tr>\r\n<tr>\r\n<td>1882112</td>\r\n<td>Unable to remove supplier on recall of</td>\r\n</tr>\r\n<tr>\r\n<td>1852061</td>\r\n<td>Remove Assigned Supplier button during</td>\r\n</tr>\r\n<tr>\r\n<td>1871053</td>\r\n<td>Wrong net_price and total value when PO</td>\r\n</tr>\r\n<tr>\r\n<td>1869238</td>\r\n<td>Issues with Manual and Catalog price in SC and</td>\r\n</tr>\r\n<tr>\r\n<td>1888830</td>\r\n<td>Issue with Display Shopping cart with Employee</td>\r\n</tr>\r\n<tr>\r\n<td>1638075</td>\r\n<td>Iview for invoicing party is not assigned to</td>\r\n</tr>\r\n<tr>\r\n<td>1867367</td>\r\n<td>Unable to order simplified Shopping Cart</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SSP classic extended:</td>\r\n</tr>\r\n<tr>\r\n<td>1671923</td>\r\n<td>FAQ note for Customer Connection Topic:SC Edit</td>\r\n</tr>\r\n<tr>\r\n<td>1621788</td>\r\n<td>SAP SRM 7.0 EHP2 : Important info about</td>\r\n</tr>\r\n<tr>\r\n<td>1886296</td>\r\n<td>PO Quantity overwritten by contract quantity</td>\r\n</tr>\r\n<tr>\r\n<td>1878959</td>\r\n<td>Update requirement price button not visible.</td>\r\n</tr>\r\n<tr>\r\n<td>1867312</td>\r\n<td>Not able to Activate switch</td>\r\n</tr>\r\n<tr>\r\n<td>1867230</td>\r\n<td>Create RFx Button is not available in Shopping</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;<strong>Changes made after release of SP stack 01</strong></p>\r\n<ul>\r\n<li>[21.11.2017] SAP HANA database 2.0 SP stack 00 Revision 2 is released for Enhancement Package&#160;4 for SAP&#160;SRM 7.0.</li>\r\n</ul>\r\n<p class=\"MsoNormal\">3049512 - Follow-on note 3018269 (search for an old SC which is Not Sourcing-Relevant)</p>\r\n<p class=\"MsoNormal\">3057121 - Follow-on note 3017733 [cx_wd_context=&gt;node_was_invalidated]</p>\r\n<p class=\"MsoNormal\">3058651 - Follow-on note 3014572 (/SAPSRM/IF_PDO_META_CONF_ROOT&#126;MT_SET_SET_SUB)</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SRM-LA (Live Auction)"}, {"Key": "Other Components", "Value": "SRM-SUS (Supplier Self-Services)"}, {"Key": "Other Components", "Value": "PSM-GPR (Procurement for Public Sector)"}, {"Key": "Other Components", "Value": "SRM-EBP-PD (Procurement Document Methods)"}, {"Key": "Other Components", "Value": "SRM-BW (SRM Analytics)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I065382)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I036507)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001818517/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1853989", "RefComponent": "XX-SER-REL", "RefTitle": "Main Browser Note for SAP Business Suite", "RefUrl": "/notes/1853989"}, {"RefNumber": "1825774", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite Powered by SAP HANA - Multi-Node Support", "RefUrl": "/notes/1825774"}, {"RefNumber": "1820905", "RefComponent": "XX-SER-REL", "RefTitle": "EHP3 for SAP SRM 7.0: Compatible Add-ons", "RefUrl": "/notes/1820905"}, {"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819"}, {"RefNumber": "1803986", "RefComponent": "BC-UPG-RDM", "RefTitle": "Rules to use SUM or SPAM/SAINT to apply SPs for ABAP stacks", "RefUrl": "/notes/1803986"}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566"}, {"RefNumber": "1671788", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "Calc. of interest during account maintenance is incorrect", "RefUrl": "/notes/1671788"}, {"RefNumber": "1655335", "RefComponent": "BC-INS-DSS", "RefTitle": "Use Cases for Splitting Dual-Stack Systems", "RefUrl": "/notes/1655335"}, {"RefNumber": "1649406", "RefComponent": "SRM-EBP-TEC-INS", "RefTitle": "Release Restriction Note for Enhancement Packages of SRM 7.0", "RefUrl": "/notes/1649406"}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2500000", "RefComponent": "SRM", "RefTitle": "How to find your User Assistance for SAP Supplier Relationship Management (SAP SRM)", "RefUrl": "/notes/2500000 "}, {"RefNumber": "2420699", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Release of SAP HANA Database 2.0 for older SAP Versions", "RefUrl": "/notes/2420699 "}, {"RefNumber": "1914052", "RefComponent": "BC-UPG-PRP", "RefTitle": "Minimal HANA and MaxDB platform requirements for NetWeaver 7.40 Support Packages", "RefUrl": "/notes/1914052 "}, {"RefNumber": "1914502", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information Note: UI FOR EHP3 FOR SAP SRM 7.0", "RefUrl": "/notes/1914502 "}, {"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819 "}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258 "}, {"RefNumber": "1820905", "RefComponent": "XX-SER-REL", "RefTitle": "EHP3 for SAP SRM 7.0: Compatible Add-ons", "RefUrl": "/notes/1820905 "}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566 "}, {"RefNumber": "1803986", "RefComponent": "BC-UPG-RDM", "RefTitle": "Rules to use SUM or SPAM/SAINT to apply SPs for ABAP stacks", "RefUrl": "/notes/1803986 "}, {"RefNumber": "1825774", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite Powered by SAP HANA - Multi-Node Support", "RefUrl": "/notes/1825774 "}, {"RefNumber": "1853989", "RefComponent": "XX-SER-REL", "RefTitle": "Main Browser Note for SAP Business Suite", "RefUrl": "/notes/1853989 "}, {"RefNumber": "1649406", "RefComponent": "SRM-EBP-TEC-INS", "RefTitle": "Release Restriction Note for Enhancement Packages of SRM 7.0", "RefUrl": "/notes/1649406 "}, {"RefNumber": "1655335", "RefComponent": "BC-INS-DSS", "RefTitle": "Use Cases for Splitting Dual-Stack Systems", "RefUrl": "/notes/1655335 "}, {"RefNumber": "1671788", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "Calc. of interest during account maintenance is incorrect", "RefUrl": "/notes/1671788 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SRM_SERVER", "From": "713", "To": "713", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}