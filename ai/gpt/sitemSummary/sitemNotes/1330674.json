{"Request": {"Number": "1330674", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 281, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016775272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001330674?language=E&token=9999A76D3BEEE1D8D1579539FF58B06A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001330674", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001330674/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1330674"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.11.2013"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1330674 - SAP Custom Code Maintainability Check"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note describes the requirements for the delivery of the SAP Custom Code Maintainability Check (CCMC) which is offered as part of SAP Enterprise Support.<br /><br />This note is only valid if your SAP Solution Manager is on release 7.0 or on release 7.10 with support package stack&#160;7 or lower. If your SAP Solution Manager is on release 7.10 with support package stack&#160;8 or higher, see note 1861798 instead.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Please find additional information in the SAP Service Marketplace under<br />http://service.sap.com/ccmc<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>One of the following conditions applies to your situation:</p>\r\n<ul>\r\n<li>You are running an implementation project with a high amout of custom code.</li>\r\n</ul>\r\n<ul>\r\n<li>You want to analyze how modified your system is or will be.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>See the attached information sheet on the deliverables of the SAP Custom Code Maintainability Check.</li>\r\n</ul>\r\n<ul>\r\n<li>Download the attached questionnaire and enter the required data.</li>\r\n</ul>\r\n<ul>\r\n<li>Create a support message on the component SV-BO-REQ. Enter the short text \"Request for Custom Code Maintainability Check\". Attach the completed questionnaire or ask your support advisor to create a service request.</li>\r\n</ul>\r\n<ul>\r\n<li>Make sure that the following technical requirements are fulfilled in your systems. All requirements can be checked by running the report RTCCTOOL with the setting \"Prepare for Software Change Management services (TEA/CCMC)\" in the managed production system. Especially the required SAP Notes must be implemented and the \"Procedure after addon impl.\" must have a green traffic light.</li>\r\n</ul>\r\n<p><strong>Required Solution Tool Plugins on the managed systems (DEV, QAS, PRD)</strong></p>\r\n<p>The service can only be delivered if the Solution Tool Plugins are at least on the following versions</p>\r\n<ul>\r\n<li>ST-PI 2008_1 with support package 2</li>\r\n</ul>\r\n<ul>\r\n<li>ST-A/PI 01M</li>\r\n</ul>\r\n<p>The required support packages are available on SAP Service Marketplace under: http://service.sap.com/swdc.</p>\r\n<p><br /><strong>Required SAP Notes on the managed systems (DEV, QAS, PRD)</strong></p>\r\n<p>The following SAP Notes need to be implemented with SAP Note Assistant:</p>\r\n<p><strong>ST-PI</strong></p>\r\n<ul>\r\n<li>ST-PI 2008_1* with support package 2:</li>\r\n<ul>\r\n<li>SAP Note 1337204</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1335023</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1460933</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1465940</li>\r\n</ul>\r\n<li>ST-PI 2008_1* with support package 3:</li>\r\n<ul>\r\n<li>SAP Note 1460933</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1516806</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1524900</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1625527</li>\r\n</ul>\r\n<li>ST-PI 2008_1* with support package 4:</li>\r\n<ul>\r\n<li>SAP Note 1559178</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1559263</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 1625527</li>\r\n</ul>\r\n<li>ST-PI 2008_1* with support package 5:</li>\r\n<ul>\r\n<li>SAP Note 1644660</li>\r\n</ul>\r\n<li>ST-PI 2008_1* with support package 6:</li>\r\n<ul>\r\n<li>SAP Note 1738183</li>\r\n</ul>\r\n<li>ST-PI 2008_1* with support package 7:</li>\r\n<ul>\r\n<li>SAP Note 1781984</li>\r\n</ul>\r\n<li>ST-PI 2008_1* with support package 8:</li>\r\n<ul>\r\n<li>SAP Note 1870040</li>\r\n</ul>\r\n</ul>\r\n<p><strong>ST-A/PI</strong></p>\r\n<ul>\r\n<li>ST-A/PI 01M* without support package:</li>\r\n<ul>\r\n<li>SAP Note 1466759</li>\r\n</ul>\r\n<li>ST-A/PI 01M* with support package 1:</li>\r\n<ul>\r\n<li>SAP Note 1511492</li>\r\n</ul>\r\n<li>ST-A/PI 01N* without support package:</li>\r\n<ul>\r\n<li>SAP Note 1563060</li>\r\n</ul>\r\n<li>ST-A/PI 01N* with support package 1:</li>\r\n<ul>\r\n<li>SAP Note 1646195</li>\r\n</ul>\r\n<li>ST-A/PI 01P* without support package:</li>\r\n<ul>\r\n<li>SAP Note 1721643</li>\r\n</ul>\r\n<li>ST-A/PI 01P*&#160;without support package:</li>\r\n<ul>\r\n<li>SAP Note 1721643</li>\r\n</ul>\r\n<li>ST-A/PI 01Q* without support package or with support package 1:</li>\r\n<ul>\r\n<li>SAP Note 1721643</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP_BASIS (only 4.6C)</strong></p>\r\n<ul>\r\n<li>For systems on 4.6C implement the SAP Code Inspector as described in SAP Note 543359. Do also perform the manual postprocessing steps as described in the attachment of the note.</li>\r\n</ul>\r\n<p><strong>Required authorization on the managed systems (DEV, QAS, PRD)</strong></p>\r\n<ul>\r\n<li>Role SAP_S_SWCM</li>\r\n</ul>\r\n<ul>\r\n<li>Role SAP_CDMC_MASTER</li>\r\n</ul>\r\n<p>Upload the roles in transaction PFCG, generate them and assign them to a user.</p>\r\n<p><strong>TMS Configuration in the managed systems (DEV, QAS, PRD)</strong></p>\r\n<p>A basic setup of the Transport Management System (TMS) is required. The TMSADM and TMSSUP connections must exist between DEV, QAS and PRD. Transport routes do not have to be configured.</p>\r\n<p><strong>Requirements on the SAP Solution Manager</strong></p>\r\n<p>On the SAP Solution Manager the software component ST-SER 2010.1 is required which is part of the support package stack 23 for SAP Solution Manager. Furthermore the dynamic content update should be activated as described in SAP Note 1143775.<br />On the SAP Solution Manager we need a user with the roles SAP_SMWORK_SDA and SAP_SOLMAN_ONSITE_COMP. See SAP Note 872800.</p>\r\n<p><strong>Required Service Connections</strong></p>\r\n<p>Service Connections and users are required to the development, quality assurance, production system and to the SAP Solution Manager.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SDD (Service Data Download)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031384)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D044419)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001330674/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001330674/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001330674/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001330674/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001330674/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001330674/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001330674/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001330674/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001330674/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "CCMC_Questionnaire.zip", "FileSize": "21", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000173452009&iv_version=0027&iv_guid=7513A85A93D26447B9ABB9B04834287E"}, {"FileName": "SAPCustomCodeMaintainabilityCheck.pdf", "FileSize": "25", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000173452009&iv_version=0027&iv_guid=DD0C95E503D1EA4ABB2A5822BE2318FA"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "543359", "RefComponent": "BC-ABA-LA", "RefTitle": "Code Inspector for SAP R/3 Release 4.6C", "RefUrl": "/notes/543359"}, {"RefNumber": "1738183", "RefComponent": "SV-SMG-SDD", "RefTitle": "Corrections for TEA and CCMC service on ST-PI 2008.1 SP6", "RefUrl": "/notes/1738183"}, {"RefNumber": "1721643", "RefComponent": "SV-SMG-SDD", "RefTitle": "TEA, TEAP, CCMC - Corrections for ST-A/PI 01P* and 01Q*", "RefUrl": "/notes/1721643"}, {"RefNumber": "1646195", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Collectors corrections ST-A/PI 01N* SP1", "RefUrl": "/notes/1646195"}, {"RefNumber": "1563060", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Collectors corrections ST-A/PI 01N* SP0", "RefUrl": "/notes/1563060"}, {"RefNumber": "1534142", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Custom Code Maintainability Check - VAR Service", "RefUrl": "/notes/1534142"}, {"RefNumber": "1465940", "RefComponent": "SV-SMG-SER", "RefTitle": "Corrections for SAP Custom Code Maintainability Check (CCMC)", "RefUrl": "/notes/1465940"}, {"RefNumber": "1460933", "RefComponent": "SV-SMG-SER", "RefTitle": "Correction for SAP CQC Transport Execution Analysis", "RefUrl": "/notes/1460933"}, {"RefNumber": "1337204", "RefComponent": "SV-SMG-CCM-CDM", "RefTitle": "CDMCEHP1CA:Dump CONVT_NO_NUMBER in Obejcts with No Reference", "RefUrl": "/notes/1337204"}, {"RefNumber": "1335023", "RefComponent": "SV-SMG-CCM-CDM", "RefTitle": "CDMC:Find Used SAP Objs CALL_FUNCTION_NOT_FOUND,CONVT_NO_NUM", "RefUrl": "/notes/1335023"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3138242", "RefComponent": "SV-SMG-SVD-SSP", "RefTitle": "How to reset a CQC service preparation step?", "RefUrl": "/notes/3138242 "}, {"RefNumber": "1738183", "RefComponent": "SV-SMG-SDD", "RefTitle": "Corrections for TEA and CCMC service on ST-PI 2008.1 SP6", "RefUrl": "/notes/1738183 "}, {"RefNumber": "1721643", "RefComponent": "SV-SMG-SDD", "RefTitle": "TEA, TEAP, CCMC - Corrections for ST-A/PI 01P* and 01Q*", "RefUrl": "/notes/1721643 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "1534142", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Custom Code Maintainability Check - VAR Service", "RefUrl": "/notes/1534142 "}, {"RefNumber": "1563060", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Collectors corrections ST-A/PI 01N* SP0", "RefUrl": "/notes/1563060 "}, {"RefNumber": "1646195", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Collectors corrections ST-A/PI 01N* SP1", "RefUrl": "/notes/1646195 "}, {"RefNumber": "1460933", "RefComponent": "SV-SMG-SER", "RefTitle": "Correction for SAP CQC Transport Execution Analysis", "RefUrl": "/notes/1460933 "}, {"RefNumber": "1465940", "RefComponent": "SV-SMG-SER", "RefTitle": "Corrections for SAP Custom Code Maintainability Check (CCMC)", "RefUrl": "/notes/1465940 "}, {"RefNumber": "1335023", "RefComponent": "SV-SMG-CCM-CDM", "RefTitle": "CDMC:Find Used SAP Objs CALL_FUNCTION_NOT_FOUND,CONVT_NO_NUM", "RefUrl": "/notes/1335023 "}, {"RefNumber": "1337204", "RefComponent": "SV-SMG-CCM-CDM", "RefTitle": "CDMCEHP1CA:Dump CONVT_NO_NUMBER in Obejcts with No Reference", "RefUrl": "/notes/1337204 "}, {"RefNumber": "543359", "RefComponent": "BC-ABA-LA", "RefTitle": "Code Inspector for SAP R/3 Release 4.6C", "RefUrl": "/notes/543359 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}