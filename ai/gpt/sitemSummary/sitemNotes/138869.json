{"Request": {"Number": "138869", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 195, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014647072017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000138869?language=E&token=9C9DD222021D9D7CD2992D49E1A63562"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000138869", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000138869/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "138869"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 48}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.10.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows", "value": "BC-FES-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "138869 - SAP GUI on Windows Terminal Server (WTS)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Can SAP GUI be used on Windows Server/Terminal Server edition (WTS), Citrix Presentation Server/XenApp, and SUN Secure Global Desktop (SSGD)?</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Hydra, Metaframe, Citrix, WTS, SSGD, Tarantella, XenApp; XenServer</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to operate the SAP GUI for Windows SAP Frontend software on a Terminal Server.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>There is no special version of the SAP GUI for Windows for Terminal Server, but you can use the standard version.<br /><br />For Microsoft WTS, Citrix, and SUN Secure Global Desktop, you can use all of the releases of the SAP GUI that are currently supported, but there are restrictions, which are mentioned below.</p>\r\n<p><strong>Release for Citrix XenApp/XenDesktop 7.x</strong></p>\r\n<p>Citrix XenApp 7.x and Citrix XenDesktop are released as of patch level 10 of SAP GUI for Windows 7.30 (see below). This release is also valid for later SAP GUI releases, such as Release SAP GUI for Windows 7.40.</p>\r\n<p><strong>Release for Citrix XenApp/XenDesktop 6.x</strong></p>\r\n<p>Citrix XenApp 6.x and Citrix XenDesktop are released as of patch level 5 of SAP GUI for Windows 7.20 (see below). This release is also valid for later SAP GUI releases, such as Release SAP GUI for Windows 7.30.</p>\r\n<p><strong>Functional restrictions of the SAP GUI for Windows on Terminal Servers.</strong></p>\r\n<ul>\r\n<li>Print: Please observe SAP Note <a target=\"_blank\" href=\"/notes/150533\">150533</a>.</li>\r\n</ul>\r\n<p><strong>Not Supported:</strong></p>\r\n<ul>\r\n<li>SAPKALE</li>\r\n<li>SAPphone server</li>\r\n<li>ECL viewer (see also SAP Note <a target=\"_blank\" href=\"/notes/1086426\">1086426</a>)</li>\r\n</ul>\r\n<p><strong>Software prerequisites:</strong></p>\r\n<ul>\r\n<li>Use of a server operating system that is released for SAP GUI for Windows (see SAP Note <a target=\"_blank\" href=\"/notes/66971\">66971</a>)</li>\r\n<li>Use of a supported release of SAP GUI for Windows (see SAP Note <a target=\"_blank\" href=\"/notes/147519\">147519</a>)</li>\r\n<li>Use of a WTS product that is supported by the manufacturer (see the <a target=\"_blank\" href=\"https://www.citrix.com/support/product-lifecycle/product-matrix.html\">Citrix Product Lifecycle Database</a>, for example)</li>\r\n<li>In principle, SAP GUI for Windows works with these products, but you should check whether they are still supported by the manufacturer in question</li>\r\n<ul>\r\n<li>Citrix Metaframe as of Version 1.0 (32-bit versions).</li>\r\n<li>Citrix Presentation Server 4.x x32 / x64*</li>\r\n<li>Citrix XenApp 5.x x32 / x64*</li>\r\n<li>Citrix XenApp or Citrix XenDesktop 6.x: The prerequisite for using this XenApp release is that you use SAP GUI for Windows 7.20 patch level 5 or a more recent version*.</li>\r\n<li>SUN Secure Global Desktop as of Version 4.30*<br /><br />* Please note the recommendation in the \"<a target=\"_self\" href=\"https://css.wdf.sap.corp/sap(bD1kZSZjPTAwMQ==)/bc/bsp/sno/ui/main.do#Signature\">SAP Signature Design/Corbu Design&#x00A0;on WTS</a>\" section.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Supported Protocols</strong></p>\r\n<p>We support RDP and ICA (metaframe) and the logs that are used by SUN SGD. This means that clients that are not based on Microsoft Windows (for example, Mac OS, Linux, SUN Solaris, HP-UX, and other UNIX variants and Web browsers) can also be used. Clients on Terminal Services are also supported in Windows operating systems. This means that it can be possible to use SAP GUI functions on platforms that are no longer supported by SAP (however, the releases of the WTS manufacturer for these platforms must always be observed).</p>\r\n<p><strong>Hardware requirements</strong></p>\r\n<ul>\r\n<li>Unlike the operation of the SAP GUI on a dedicated workstation, no general recommendations can be made for a terminal server with respect to CPU, main memory, and so on. Contact your hardware partner for assistance with this question.</li>\r\n<li>SAP does not plan to certify WTS servers.</li>\r\n<li>Note that SAP does not recommend the simultaneous operation of WTS on an SAP application server.</li>\r\n</ul>\r\n<p><strong>Support</strong></p>\r\n<p>SAP offers support for problems which are caused by the SAP GUI during the installation and operation of SAP GUI. In the case of other problems, contact the producers of the product in question (Microsoft or Citrix).</p>\r\n<p><strong>Related support components</strong></p>\r\n<ul>\r\n<li>XX-PART-CTX (Citrix)</li>\r\n<li>BC-OP-SUN (SUN)</li>\r\n<li>BC-OP-NT (WTS)</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"Signature\"></a>SAP Signature Design/Corbu Design on WTS</strong></p>\r\n<p>SAP GUI for Windows 7.10 introduced the SAP Signature Design and, as of SAP GUI for Windows 7.20, this becomes the default design. In addition, the \"Corbu\" design was implemented in SAP GUI for Windows 7.30. Both designs place greater demands on system resources (memory, CPU, and graphic resources) than other designs (Enjoy or Classic) - see also SAP Note <a target=\"_blank\" href=\"/notes/26417\">26417</a> in this regard. This might have a noticeable negative effect when you use SAP GUI on terminal servers. To preserve the graphic resources in particular, we recommend that when using the SAP Signature Design/Corbu Design on terminal servers you deactivate the transparent frame (\"shadow frame\") that is drawn around the SAP Logon and the main window of the SAP GUI.<br /><br />To do so, you define the following registry value (REG_DWORD):</p>\r\n<ul>\r\n<li>HKEY_LOCAL_MACHINE\\Software\\SAP\\General\\Appearance\\ShowShadowBorder (on 32-bit operating systems and on 64-bit operating systems if a 64-bit version of SAP GUI for Windows is used)<br /><br />or</li>\r\n</ul>\r\n<ul>\r\n<li>HKEY_LOCAL_MACHINE\\Software\\Wow6432Node\\SAP\\General\\Appearance\\ShowShadowBorder (on 64-bit operating systems if a 32-bit version of SAP GUI for Windows is used)</li>\r\n</ul>\r\n<p><br />and assign the value \"0\".<br />As a result, a solid frame is drawn around the SAP GUI window and the resources are preserved.<br /><br />If, despite this setting, problems occur when you use the SAP Signature Design/Corbu Design, you can select one of the other designs as an alternative.<br /><br />When you use the transparent frame (\"shadow frame\"), problems may occur when drawing the SAP GUI window on Citrix XenApp if you use the SAP GUI as a published application. For this reason also, we recommend that you deactivate this option, particularly in this scenario.</p>\r\n<p><strong>Network resources</strong></p>\r\n<p>The network load strongly depends on parameters, such as the size of the WTS window, usage of the SAP GUI Enjoy Design and the transaction used. The WTS connection in LAN generally does not cause a bottleneck.<br />The performance in WAN can be optimized by the following measures:</p>\r\n<ul>\r\n<li>You can deactivate the SAP Signature Design, Corbu Design, or Enjoy Design (\"Control Panel -&gt; SAP GUI Configuration\").</li>\r\n</ul>\r\n<ul>\r\n<li>Animations of SAP GUI can be prevented (SAP Logon -&gt; Settings-&gt; Extended Settings -&gt; Low speed connection - as of SAP GUI for Windows 7.10 using \"Change Entry\" on the \"Network\" page). If using the low-speed connection, please refer to SAP Note <a target=\"_blank\" href=\"/notes/161053\">161053</a> for information about functional restrictions.</li>\r\n</ul>\r\n<p><strong>More information</strong></p>\r\n<ul>\r\n<li>see the MSDN library using keyword \"Windows Terminal Services\": <a target=\"_blank\" href=\"http://msdn.microsoft.com/en-gb/library/bb892075.aspx\">http://msdn.microsoft.com/en-gb/library/bb892075.aspx</a></li>\r\n</ul>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"http://www.citrix.com/\">http://www.citrix.com</a></li>\r\n</ul>\r\n<ul>\r\n<li>In the Citrix knowledge base (<a target=\"_blank\" href=\"http://knowledgebase.citrix.com/\">http://knowledgebase.citrix.com</a>)</li>\r\n</ul>\r\n<p><strong>Known problems/FAQ</strong></p>\r\n<p><strong>When you use the SAP GUI through Terminal Server, the colours are changed.</strong></p>\r\n<p>There are two known causes for this:</p>\r\n<ul>\r\n<li>This problem occurs when the Terminal Server Client is operated with 256 colors or less. It can be eliminated with Citrix Metaframe 1.8 as of Feature Release 1 by activating the High Color or True Color option.<br /><br />At present there is no comprehensive solution for Microsoft Windows Terminal Services as the RDP log used only supports up to 256 colors.<br /><br />If the SAP GUI uses more colors, conflicts occur that result in the use of wrong colors.</li>\r\n</ul>\r\n<ul>\r\n<li>Some Clients/PCs (mostly older ones) can only display 256 colors or are configured for only 256 colors. In this case, incorrect colors are used too.</li>\r\n</ul>\r\n<p>In both cases, the incorrect colors are not caused by the SAP software. If you cannot work with these incorrect colors, you can use SAP GUI with the Classic Design.</p>\r\n<p><strong>SAP GUI occasionally flickers in seamless mode (Citrix) or does not behave correctly when you use the mouse.</strong></p>\r\n<p>This can be corrected by entries in the registry of the terminal server. Create a new key (with any name) under the key<br />[HKLM]\\SYSTEM\\CurrentControlSet\\Control\\Citrix\\Wfshell\\TWI<br />. For example SAPGUI.<br />Enter two values in this key:</p>\r\n<ul>\r\n<li>\"ClassName\" = \"SAP_FRONTEND_SESSION\", Typ REG_SZ</li>\r\n</ul>\r\n<ul>\r\n<li>\"Type\" = 1, Typ REG_DWORD</li>\r\n</ul>\r\n<p>The entry is activated the next time the user logs on.</p>\r\n<p><strong>How should the ROOTDRIVE/home directory be set for terminal servers?</strong></p>\r\n<p>The environment variable \"sapworkdir\" should point to the Home Directory of the user.</p>\r\n<p>In the past, there have been various problems (for example, error messages that state that SAP GUI configuration files cannot be changed) that could be traced back to an incorrect configuration of the ROOTDRIVE or home directory.<br /><br />For a more detailed explanation, read the following Microsoft knowledge base article:</p>\r\n<ul>\r\n<ul>\r\n<li>How and why ROOTDRIVE is used on Windows Terminal Server<br /><a target=\"_blank\" href=\"http://support.microsoft.com/kb/195950/en-us\">http://support.microsoft.com/kb/195950/en-us</a></li>\r\n</ul>\r\n<ul>\r\n<li>Terminal Server User's Home Directory Is Not Set Correctly<br /><a target=\"_blank\" href=\"http://support.microsoft.com/kb/230165/en-us\">http://support.microsoft.com/kb/230165/en-us</a></li>\r\n</ul>\r\n<ul>\r\n<li>The HOMEDRIVE, HOMEPATH, and HOMESHARE environment variables may not function as you intend if you modify them in Windows 2000<br /><a target=\"_blank\" href=\"http://support.microsoft.com/kb/841343/en-us\">http://support.microsoft.com/kb/841343/en-us</a></li>\r\n</ul>\r\n</ul>\r\n<p><strong>Citrix XenServer - Application streaming</strong></p>\r\n<p>SAP GUI for Windows is not released for Citrix XenServer application streaming technology.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031909)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031909)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000138869/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000138869/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971"}, {"RefNumber": "512995", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/512995"}, {"RefNumber": "431163", "RefComponent": "BC-FES-WTS", "RefTitle": "Troubleshooting Citrix Metaframe / SAP GUI Issues", "RefUrl": "/notes/431163"}, {"RefNumber": "26417", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI Resources: Hardware and software", "RefUrl": "/notes/26417"}, {"RefNumber": "200694", "RefComponent": "BC-FES-GUI", "RefTitle": "Information for SAP GUI when used via terminal server", "RefUrl": "/notes/200694"}, {"RefNumber": "1782982", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Smart UI: Technical Prerequisites for smart UI Components", "RefUrl": "/notes/1782982"}, {"RefNumber": "177626", "RefComponent": "IS-H", "RefTitle": "IS-H with terminal server: Restrictions", "RefUrl": "/notes/177626"}, {"RefNumber": "161053", "RefComponent": "BC-FES-GUI", "RefTitle": "Use of SAP GUI in WAN", "RefUrl": "/notes/161053"}, {"RefNumber": "150533", "RefComponent": "BC-FES-WTS", "RefTitle": "Printing in Windows Terminal Server (WTS)", "RefUrl": "/notes/150533"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "1086426", "RefComponent": "CA-DMS-EAI", "RefTitle": "Using ECL Viewer within Citrix Environment", "RefUrl": "/notes/1086426"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2689485", "RefComponent": "BC-FES-WTS", "RefTitle": "Component BC-FES-WTS is locked - please use BC-FES-GUI instead", "RefUrl": "/notes/2689485 "}, {"RefNumber": "2646715", "RefComponent": "BC-FES-WTS", "RefTitle": "SAP GUI Terminal Virtualization with Amazon AppStream 2.0", "RefUrl": "/notes/2646715 "}, {"RefNumber": "1442303", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI for Windows - replacement of SAPWORKDIR", "RefUrl": "/notes/1442303 "}, {"RefNumber": "26417", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI Resources: Hardware and software", "RefUrl": "/notes/26417 "}, {"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971 "}, {"RefNumber": "1782982", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Smart UI: Technical Prerequisites for smart UI Components", "RefUrl": "/notes/1782982 "}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "161053", "RefComponent": "BC-FES-GUI", "RefTitle": "Use of SAP GUI in WAN", "RefUrl": "/notes/161053 "}, {"RefNumber": "177626", "RefComponent": "IS-H", "RefTitle": "IS-H with terminal server: Restrictions", "RefUrl": "/notes/177626 "}, {"RefNumber": "200694", "RefComponent": "BC-FES-GUI", "RefTitle": "Information for SAP GUI when used via terminal server", "RefUrl": "/notes/200694 "}, {"RefNumber": "150533", "RefComponent": "BC-FES-WTS", "RefTitle": "Printing in Windows Terminal Server (WTS)", "RefUrl": "/notes/150533 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}