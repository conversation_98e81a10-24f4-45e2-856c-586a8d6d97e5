{"Request": {"Number": "2891757", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 173, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002891757?language=E&token=D510F93B85A380340AA23BEEA66CEBCB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002891757", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002891757/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2891757"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-NC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Non cumulative value"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2891757 - Non-Cumulatives: Selective Deletion & 0RECORDTP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP NetWeaver All Versions</li>\r\n<li>SAP BW/4HANA All versions</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>As described in SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1548125\">1548125</a>, the special handling of non-cumulative key figures requires the additional technical characteristic 0recordtp in the BW provider. The different possible values(0, 1 and 2)&#160;define whether a data record is a so called reference point, a 'historical' or a 'new' movement(see note <a target=\"_blank\" href=\"https://me.sap.com/notes/1548125\">1548125</a> for details).&#160;If you want to use the feature selective deletion, the following points regarding 0recordtp are important to know:</p>\r\n<p><strong>SAP BW NetWeaver Systems</strong></p>\r\n<ul>\r\n<li>If 0recordtp isn't restricted when defining the deletion selections, then all movements(0recordtp=0,2) and the corresponding reference points(0recordtp=1) are removed.</li>\r\n<li>It is supported to only delete movements by using the filter 0recordtp to 0 or 2, respectively and also to delete only the reference points(0recordtp=1).</li>\r\n</ul>\r\n<p>However, in general you need to be very careful when using explicit deletion selections regarding 0recordtp since this can lead to inconsistencies in the provider. In particular, if you want to delete data records and reload them again in order to repair incorrect data, you need to consider the following:</p>\r\n<ul>\r\n<li>When historical movements are removed and reloaded, the newly loaded records need to have again 0recordtp=2, otherwise the compression of the request would update the reference points again.</li>\r\n<li>In general, deleting only the reference points makes no sense since probably the newly loaded records with 0recordtp=1 cannot be compressed any more!</li>\r\n<ul>\r\n<li>The reason is that compressed movements without markers should never exists and that this inconsistent state can lead to a termination of the compression process(see <a target=\"_blank\" href=\"https://me.sap.com/notes/1795551\">1795551</a>). Only when the records with 0recordtp=1 do not contain any combination of keys which already exist(as a movement), then it would work. In general, this is not the case.</li>\r\n<li>Remark: missing reference points can be created (again) by using program SAP_REFPOINT_COMPLETE_NEW, however, it only adds records with value zero. Hence, only when it is clear that this value (for the reference points) is correct, this report can be used.</li>\r\n</ul>\r\n</ul>\r\n<p><strong><span style=\"font-size: 14px;\">SAP BW/4HANA Systems</span></strong></p>\r\n<ul>\r\n<li>0recordtp isn't offered as deletion criteria on BW/4Hana systems up to release BW/4Hana 2.0.</li>\r\n<li>As of&#160;BW/4Hana 2023 there is a new way to delete markers.</li>\r\n</ul>\r\n<p>This new feature allows you to define time restrictions (see also&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2374993\">2374993</a>) and a deletion of markers in transaction&#160;DELETE_FACTS. The following documentation provides more details:</p>\r\n<p><a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_BW4HANA/107a6e8a38b74ede94c833ca3b7b6f51/321a4042ffba4898a68003116192d775.html?locale=en-US\">Selective Deletion with Non-Cumulative Key Figures</a></p>\r\n<p>Note that there two ways available for deleting markers:</p>\r\n<p><img class=\"img-responsive\" alt=\"D3T_SELDEL_2.jpg\" height=\"190\" id=\"00109B36D5CA1EDEAAC322D969F5891E\" src=\"https://i7p.wdf.sap.corp/sap/support/sapnotes/public/services/embedded_image.htm?iv_key=012003146900001258542016&amp;iv_guid=00109B36D5CA1EDEAAC322D969F5891E&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334B688F734378A3730B4728DAF744B8B88304E318EF289722C2BC94E8D2F372C4BB134CBF132282A33704A2A493528328977D2750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\" title=\"D3T_SELDEL_2.jpg\" width=\"511\" /></p>\r\n<p>Markers are considered unused if there are no non-cumulative changes with the same key in the InfoProvider. In case you e.g. want to delete all data for a certain material then it is necessary to do this in two steps: first run the tool with the setting 'Reference Points are not deleted' in order to delete all movements assigned to this material. Afterwards it is possible to delete the corresponding markers (reference points) by activating the setting 'Unused Reference Points are deleted' (and again using the material filter). Note that there is a simulation modus available and the possibilty to display the SQL query or report that is generated according to the selection criteria and the field selection.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<ul>\r\n<li>Consulting Notes</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/1548125\">1548125</a> Interesting Facts about Non-Cumulatives</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/1795551\">1795551</a> Non-Cumulatives: Issues when Compressing/Activating Requests containing Reference Points(0RECORDTP=1)</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/2374993\">2374993</a> Non-Cumulatives: Selective Deletion with Time Restrictions</li>\r\n</ul>\r\n<li>SAP Support Content</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/3361385681.html?locale=en-US\">Non-Cumulative Key Figures</a></li>\r\n</ul>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>ncum, inventory cube, ADSo, non-cumulative, advances datastore object</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DBA-SDEL (Selective deletion)"}, {"Key": "Other Components", "Value": "BW4-DM-ADSO (DataStore Object (Advanced))"}, {"Key": "Other Components", "Value": "BW4-AE-CORE-NC (Non-cumulatives)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I022439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I022439)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002891757/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2374993", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-Cumulatives: Selective Deletion with Time Restrictions", "RefUrl": "/notes/2374993"}, {"RefNumber": "1795551", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "BW Non-Cumulatives: Issues when Compressing/Activating Requests containing Reference Points(0RECORDTP=1)", "RefUrl": "/notes/1795551"}, {"RefNumber": "823951", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Consistency check of non-cumulative cubes", "RefUrl": "/notes/823951"}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Non-Cumulative Key Figures", "RefUrl": "https://wiki.scn.sap.com/wiki/x/BIFdEg"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2374993", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-Cumulatives: Selective Deletion with Time Restrictions", "RefUrl": "/notes/2374993 "}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP BW/4HANA all versions "}, {"Product": "SAP NetWeaver all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 1.0, "Quality-Votes": 1, "RatingQualityDetails": {"Stars-1": 1, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}