{"Request": {"Number": "774933", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 351, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015996892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000774933?language=E&token=EFEC22DE32034E7EC2E499A969A30BB8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000774933", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000774933/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "774933"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.09.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "774933 - Installation and upgrade to BI_CONT 3.53"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Information about <B>Business Intelligence Content 3.53</B> in relation to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-On installation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basis (BW) upgrade to SAP_BW 3.50<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-On Support Packages<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />SAINT, installation, delta upgrade, upgrade, BI_CONT, add-on, AOI, AOU<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to install Business Intelligence Content (BI_CONT 3.53).<br />You want to upgrade to SAP_BW 3.50.<br />You must import Add-On Support Packages for BI_CONT 3.53.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>This note is updated on a regular basis. You should therefore obtain the latest version before you start the upgrade.</B><br /><br /></p> <b>Contents</b><br /> <p>0.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Change history</p> <OL>1. Important general information</OL> <OL>2. Installation and delta upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <OL><OL>b) Prerequisites</OL></OL> <OL><OL>c) Execution</OL></OL> <OL><OL>d) Postprocessing</OL></OL> <OL>3. BW upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <OL><OL>b) Preparation</OL></OL> <OL><OL>c) Execution</OL></OL> <OL>4. Support Package for BI_CONT 3.53</OL> <p>0.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Change history</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Section</TH><TH ALIGN=LEFT> Short description</TH></TR> <TR><TD>22.11.2004</TD><TD> 1</TD><TD> UNICODE problem with the ramp-up version</TD></TR> <TR><TD>11.02.2004</TD><TD> 3</TD><TD> Inclusion in the SCM 4.1 upgrade</TD></TR> <TR><TD>15.12.2005</TD><TD> various</TD><TD> Adjustment to SAP terminology</TD></TR> </TABLE> <p>------------------------------------------------------------------------</p> <OL>1. Important general information</OL> <OL><OL>a) UNICODE problem with the ramp-up version</OL></OL> <UL><UL><LI>The ramp-up version of BI_CONT 353 can result in illegible languages in a UNICODE system (Note 775114). Therefore, you must not import packages: CSN0120061532_0019768.PAT (installation), CSN0120061532_0019770.PAT (delta upgrade - full task), or CSN0120061532_0019767.PAT (delta upgrade - delta task) into a UNICODE system.</LI></UL></UL> <OL><OL>b) qRFC version</OL></OL> <UL><UL><LI>Note that the qRFC version must be 45 or higher (see Note 0498484).</LI></UL></UL> <OL><OL>c) Updates</OL></OL> <UL><UL><LI>Process your V3 update entries before you carry out the upgrade. Otherwise, there is a risk that you may no longer be able to update entries if changes are introduced into the interface structures of the V3 update modules by the patch or upgrade (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the upgrade, process your entries in the extraction queues. Otherwise, there is a risk that you may no longer be able to update these entries if changes to the interface structures of the qRFC function modules are introduced by the patch or the upgrade (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the upgrade, delete your entries in the reconstruction tables for the logistics extraction applications. Otherwise, there is a risk that you may no longer be able to use these entries if changes to the extraction structures are introduced by the patch or the upgrade (see Note 328181).</LI></UL></UL> <OL>2. Installation and delta upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Refer to the following notes before you start the installation:</TH></TR> <TR><TD>Add-ons: General conditions </TD><TD> &#x00A0;&#x00A0;70228</TD></TR> <TR><TD>Note about the release strategy</TD><TD> 153967</TD></TR> <TR><TD>Known problems with transaction SAINT</TD><TD> 179116</TD></TR> </TABLE></UL></UL> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Check whether the following prerequisites are met in your system:</TH></TR> <TR><TD>SAP_BASIS</TD><TD> 640</TD><TD> 05</TD></TR> <TR><TD>SAP_ABA</TD><TD> 640</TD><TD> 05</TD></TR> <TR><TD>SAP_BW</TD><TD> 350</TD><TD> 05</TD></TR> <TR><TD>PI_BASIS</TD><TD> 2004_1_640</TD><TD> 05</TD></TR> <TR><TD>-----------------------------------------------------------</TD></TR> <TR><TD>Only relevant for the delta upgrade (FULLTASK SAPKIBIFUH)</TD></TR> <TR><TD>BI_CONT</TD><TD> 351</TD></TR> <TR><TD>BI_CONT</TD><TD> 352</TD></TR> <TR><TD>-----------------------------------------------------------</TD></TR> <TR><TD>Only relevant for the delta upgrade (delta task SAPKIBIFD0)</TD></TR> <TR><TD>BI_CONT</TD><TD> 352</TD></TR> <TR><TD>-----------------------------------------------------------</TD></TR> </TABLE> <p></p> <OL><OL>b) Preparations</OL></OL> <UL><UL><LI>Download the installation or upgrade package from SAP Service Marketplace (quick link /INSTALLATIONS) into a temporary directory, or install the DVD&#x00A0;&#x00A0;with material number: 51030630.<br />&lt;SAR archive&gt; = SAPKIBIFIH (Installation)<br />&lt;SAR archive&gt; = SAPKIBIFUH (delta upgrade - full task)<br />&lt;SAR archive&gt; = SAPKIBIFD0 (delta upgrade - delta task)</LI></UL></UL><UL><UL><LI>Log on as one of the following users:<br />&lt;sid&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />&lt;SID&gt;OFR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />&lt;SID&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT</LI></UL></UL> <UL><UL><LI>Switch to the transport directory of your SAP System. You can display the transport directory by calling transaction AL11 and selecting the DIR_TRANS parameter.</LI></UL></UL> <UL><UL><LI>Unpack the SAR archive &lt;SAR archive&gt; with the following statement:<br />SAPCAR -xvf /&lt;CD_DIR&gt;/INST/DATA/&lt;SAR archive&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/INST/DATA/&lt;SAR archive&gt;'&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />SAPCAR -xvf &lt;CD_DRIVE&gt;:\\INST\\DATA\\&lt;SAR archive&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT.</LI></UL></UL> <UL><UL><LI>You must now be able to find the following file in the /EPS/in directory:<br />CSN0120061532_0020014.PAT (Installation)<br />CSN0120061532_0020020.PAT (delta upgrade - full task)<br />CSN0120061532_0020018.PAT (delta upgrade - delta task)</LI></UL></UL> <OL><OL>c) Execution</OL></OL> <UL><UL><LI>Log onto client 000 as a user with SAP_ALL authorization. Do NOT use SAP* or DDIC.</LI></UL></UL> <UL><UL><LI>Display the installation packages for the add-on installation.</LI></UL></UL> <UL><UL><LI>Call transaction SAINT and choose 'Load'. After the system displays the list of loaded packages, choose F3 or Back to return to the initial screen of transaction SAINT.</LI></UL></UL> <UL><UL><LI>If you want to use the option of an import with several background processes, proceed as described in Note 705192.&#x00A0;&#x00A0;The optimum number of parallel processes in this case is four.</LI></UL></UL> <UL><UL><LI>Start the installation:<br />Call transaction SAINT and select the BI_CONT 353 add-on. Then choose 'Continue'. If your system meets all the prerequisites for importing the add-on, the relevant queue is displayed. The queue contains the installation package and, in certain cases, Support Packages and Add-On Support Packages. Include all available Add-On Packages in the installation or delta upgrade (section 4). Choose \"Continue\" to start the installation. To obtain more information, call transaction SAINT and choose the relevant button in the application toolbar.</LI></UL></UL> <UL><UL><LI>The passwords for the installation/delta upgrade are:<br />2DCFC710F5 (installation)<br />2DCFC70CF5 (Delta-upgrade with SAPKIBIFUH)<br />2DCFC71D8D (Delta-upgrade with SAPKIBIFD0)<br /></LI></UL></UL> <OL>3. BW Upgrade (and SCM 4.1 upgrade)</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI></UL></UL> <UL><UL><LI>The upgrades of the following BW releases are supported:<br />BW 20B, 21C, 30B, 310 (BI_CONT 310, 320 or 330)</LI></UL></UL> <UL><UL><LI>BI_CONT 351 is already contained in the export during the SCM 4.1 upgrade. In the Prepare phase, package BI_CONT 353 can be included to allow a direct upgrade to BI_CONT 353.</LI></UL></UL> <OL><OL>b) Preparation</OL></OL> <UL><UL><LI>Download the upgrade package from SAP Service Marketplace (quick link INSTALLATIONS) to a temporary directory, or install the CD with the material number: 51030630.</LI></UL></UL> <UL><UL><LI>Log on as one of the following users:<br />&lt;sid&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />&lt;SID&gt;OFR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />&lt;SID&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT</LI></UL></UL> <UL><UL><LI>Switch to the transport directory of your SAP System. You can display the transport directory by calling transaction AL11 and selecting the DIR_TRANS parameter.</LI></UL></UL> <UL><UL><LI>Unpack the CAR archive &lt;CAR archives&gt; with the following statement:<br />SAPCAR -xvf /&lt;CD_DIR&gt;/UPGR/DATA/KIBIFUH.SAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX<br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/UPGR/DATA/KIBIFUH.SAR' on AS/400<br />SAPCAR -xvf &lt;CD_DRIVE&gt;:\\UPGR\\DATA\\KIBIFUH.SAR&#x00A0;&#x00A0; on Windows NT</LI></UL></UL> <UL><UL><LI>You must now be able to find the following file in the /EPS/in directory:<br />CSN0120061532_0020020.PAT</LI></UL></UL> <OL><OL>c) Execution<br />Log onto client 000 as a user with SAP_ALL authorization. Do NOT use SAP* or DDIC.</OL></OL> <UL><UL><LI>Display the installation/upgrade packages for the add-on installation: Call transaction SAINT and choose 'Load'.</LI></UL></UL> <OL><OL>d) Additional information about PREPARE</OL></OL> <UL><UL><LI>IS_SELECT phase: Answer the question for BI_CONT with 'Upgrade with SAINT package'.</LI></UL></UL> <UL><UL><LI>Phase PATCH_CHK: Include the available Add-On Support Packages in the upgrade. (See section 4)</LI></UL></UL> <OL><OL>e) The password in the KEY_CHK phase is:<br /><B>281450</B></OL></OL> <OL>4. Postprocessing the BI_CONT 711 installation/upgrade</OL> <UL><LI>You can find the current Support Packages for BI_CONT 3.53 on SAP Service Marketplace under the quick link /patches.</LI></UL> <UL><LI>In addition to German and English, BI_CONT 3.53 supports the following languages: Bulgarian, Czech, Danish, Greek, Spanish, Finnish, French, Croatian, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Romanian, Russian, Slovakian, Slovenian, Swedish, Turkish, Traditional Chinese, Chinese<br />No further postprocessing is necessary for any languages that were already installed as a default language before the add-on installation. However, if you want to install one of these languages at a later stage, proceed as described in Note 877049:</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D039662)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002681)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000774933/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774933/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "note_554255.CAR", "FileSize": "6", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000444542004&iv_version=0015&iv_guid=8526AE5042DF5649A0C2399B57FB1AFD"}, {"FileName": "R3ADDONPAR.ZIP", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000444542004&iv_version=0015&iv_guid=FD689E21CD494240AF0409AF6CCE65E3"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "943365", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 353:Information on Add-On Support Packages", "RefUrl": "/notes/943365"}, {"RefNumber": "921995", "RefComponent": "IS-DFS", "RefTitle": "BI Content for DFPS", "RefUrl": "/notes/921995"}, {"RefNumber": "877049", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Subsequent language installation for BI_CONT", "RefUrl": "/notes/877049"}, {"RefNumber": "844007", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "Dump through where-used list for delivery InfoObjects", "RefUrl": "/notes/844007"}, {"RefNumber": "797995", "RefComponent": "FS-BA", "RefTitle": "SAP Bank Analyzer 4.0 add-on: Upgrading to SAP NetWeaver04", "RefUrl": "/notes/797995"}, {"RefNumber": "797994", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer 4.0 add-on: Installation on NetWeaver04", "RefUrl": "/notes/797994"}, {"RefNumber": "763493", "RefComponent": "BW-BCT", "RefTitle": "BI Content 3.5.3: Incompatible changes for release upgrade", "RefUrl": "/notes/763493"}, {"RefNumber": "724419", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 4.0", "RefUrl": "/notes/724419"}, {"RefNumber": "724418", "RefComponent": "FIN-SEM", "RefTitle": "Installing SAP SEM Release 4.0", "RefUrl": "/notes/724418"}, {"RefNumber": "717812", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation and upgrade to BI_CONT 3.52", "RefUrl": "/notes/717812"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "658992", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information for the upgrade to BW 3.50", "RefUrl": "/notes/658992"}, {"RefNumber": "634214", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation and upgrade of BI_CONT 3.51", "RefUrl": "/notes/634214"}, {"RefNumber": "634173", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Delta upgrade BI_CONT 320 -> 330", "RefUrl": "/notes/634173"}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967"}, {"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "943365", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 353:Information on Add-On Support Packages", "RefUrl": "/notes/943365 "}, {"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822 "}, {"RefNumber": "797995", "RefComponent": "FS-BA", "RefTitle": "SAP Bank Analyzer 4.0 add-on: Upgrading to SAP NetWeaver04", "RefUrl": "/notes/797995 "}, {"RefNumber": "797994", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer 4.0 add-on: Installation on NetWeaver04", "RefUrl": "/notes/797994 "}, {"RefNumber": "724084", "RefComponent": "BC-INS", "RefTitle": "OBSOLETE: SAP NetWeaver information for Asian language versions", "RefUrl": "/notes/724084 "}, {"RefNumber": "921995", "RefComponent": "IS-DFS", "RefTitle": "BI Content for DFPS", "RefUrl": "/notes/921995 "}, {"RefNumber": "877049", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Subsequent language installation for BI_CONT", "RefUrl": "/notes/877049 "}, {"RefNumber": "724418", "RefComponent": "FIN-SEM", "RefTitle": "Installing SAP SEM Release 4.0", "RefUrl": "/notes/724418 "}, {"RefNumber": "658992", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information for the upgrade to BW 3.50", "RefUrl": "/notes/658992 "}, {"RefNumber": "717812", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation and upgrade to BI_CONT 3.52", "RefUrl": "/notes/717812 "}, {"RefNumber": "634214", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation and upgrade of BI_CONT 3.51", "RefUrl": "/notes/634214 "}, {"RefNumber": "634173", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Delta upgrade BI_CONT 320 -> 330", "RefUrl": "/notes/634173 "}, {"RefNumber": "763493", "RefComponent": "BW-BCT", "RefTitle": "BI Content 3.5.3: Incompatible changes for release upgrade", "RefUrl": "/notes/763493 "}, {"RefNumber": "724419", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 4.0", "RefUrl": "/notes/724419 "}, {"RefNumber": "844007", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "Dump through where-used list for delivery InfoObjects", "RefUrl": "/notes/844007 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "353", "To": "353", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}