{"Request": {"Number": "1690850", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 434, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010044442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001690850?language=E&token=89FB151A0C28B6E7F56334F76B443536"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001690850", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1690850"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.04.2012"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-EWM-RF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Radio Frequency Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Extended Warehouse Management", "value": "SCM-EWM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Radio Frequency Processing", "value": "SCM-EWM-RF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM-RF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1690850 - Performance measurement in RFUI"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The note contains the backend part of the RFUI performance measurement.<br />The performance measurement can be triggered on Resource level in transaction /SCWM/RSRC. There is a checkbox to switch on/off the performance measurement.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>/SCWM/RFUI, performance measurement</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>enhancement</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the enhancement.<br />This note contains only the RF backend part, but the evaluation part can be found in note 1595305.<br />If you want to measure the performance then both notes implementation are mandatory!<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I034552)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I043965)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001690850/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690850/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1595305", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Measuring runtimes for RF devices in SAP EWM", "RefUrl": "/notes/1595305"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1595305", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Measuring runtimes for RF devices in SAP EWM", "RefUrl": "/notes/1595305 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCMEWM", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "712", "To": "712", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "900", "To": "900", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "910", "To": "910", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "920", "To": "920", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "930", "To": "930", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "940", "To": "940", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SCMEWM 701", "SupportPackage": "SAPK-70109INSCMEWM", "URL": "/supportpackage/SAPK-70109INSCMEWM"}, {"SoftwareComponentVersion": "SCMEWM 702", "SupportPackage": "SAPK-70204INSCMEWM", "URL": "/supportpackage/SAPK-70204INSCMEWM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SCMEWM", "NumberOfCorrin": 2, "URL": "/corrins/0001690850/4620"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SCMEWM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Extended Wareho...|<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-70103INSCMEWM - SAPK-70108INSCMEWM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-70203INSCMEWM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>1. Start transaction SE11.<br/>2. Select radiobutton Data type and enter /SCWM/DE_RF_PERF_MEAS and press button Create, and select Data element<br/>3. Fill out the following fields<br/> Short desctiption: RF Performance Measurement on/off<br/> On tabtrip Data Type:<br/>  Domain: XFELD<br/> On tabstrip Field Label<br/>  Short: 10 Perf.Meas.<br/>  Medium: 15 Perf.Meas.<br/>  Long: 20 Performance Measure<br/>  Heading: 10 Perf.Meas.<br/>Save and activate it.<br/>During save assign the new data element to package&nbsp;&nbsp;/SCWM/RESOURCE_MGMT.<br/><br/><br/>4. Start transaction SE11.<br/>5. Select radiobutton Database table and enter /SCWM/RSRC<br/>and press button Create.<br/>6. Add a new field to the table:<br/> Field: PERF_MEAS<br/> Data element: /SCWM/DE_RF_PERF_MEAS<br/>Save and activate it.<br/><br/><br/>7. Start transaction SE11.<br/>8. Select radiobutton Data type and enter /SCWM/S_RF_PERF_MEAS and press button Create, and select Structure<br/>9. Fill out the following fields<br/> Short desctiption: Structure for RF performance measurement<br/> Component:  STEP<br/> Typing method: Types<br/> Component type: /SCMB/PFM2_STEP<br/><br/> Component:  PSTEP<br/> Typing method: Types<br/> Component type: /SCMB/PFM2_PARENT_STEP<br/><br/> Component:  KYF_VAL_TAB<br/> Typing method: Types<br/> Component type: /SCMB/PFM2_T_KYF_VAL<br/><br/> Component:  CHA_VAL_TAB<br/> Typing method: Types<br/> Component type: /SCMB/PFM2_T_CHA_VAL<br/>Save and activate it.<br/>During save assign the new structure to package&nbsp;&nbsp;/SCWM/RF_FRAMEWORK.<br/><br/><br/>10. Start transaction SE11.<br/>11. Select radiobutton Data type and enter /SCWM/TT_RF_PERF_MEAS and press button Create, and select Table type<br/>12. Fill out the following fields<br/> Short desctiption: Table type for RF performance measurement<br/> Line type: /SCWM/S_RF_PERF_MEAS<br/>Save and activate it.<br/>During save assign the new structure to package&nbsp;&nbsp;/SCWM/RF_FRAMEWORK.<br/><br/><br/>13. Start transaction SE11.<br/>14. Select radiobutton View and enter /SCWM/V_RSRC and press button Change<br/>15. Add a new line with the following fields<br/>View field: PERF_MEAS<br/>Table: /SCWM/RSRC<br/>Field: PERF_MEAS<br/>Save and activate it.<br/><br/><br/>16. Start transaction SE54.<br/>17. Enter to field Table/View /SCWM/V_RSRC, select radiobutton Generated Objects and press pushbutton Create/Change<br/>On the next screen go to edit mode.<br/>18. A popup screen comes and select New field/sec.table in structure.<br/>19. On the next popup screen select checkbox of Overview screen and Normal field.<br/>System will regenerate the view.<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SCMEWM", "ValidFrom": "701", "ValidTo": "701", "Number": "1521250 ", "URL": "/notes/1521250 ", "Title": "Hardcoded logical transactions in the standard code", "Component": "SCM-EWM-RF-FW"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "701", "ValidTo": "701", "Number": "1556212 ", "URL": "/notes/1556212 ", "Title": "Technical improvement of BAdI interface", "Component": "SCM-EWM-RF-FW"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "701", "ValidTo": "701", "Number": "1577248 ", "URL": "/notes/1577248 ", "Title": "Field has not cleared if error msg raised during validation", "Component": "SCM-EWM-RF-PCK"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "701", "ValidTo": "701", "Number": "1597763 ", "URL": "/notes/1597763 ", "Title": "Field navigation changed on the login screen", "Component": "SCM-EWM-RF-FW"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "701", "ValidTo": "701", "Number": "1601908 ", "URL": "/notes/1601908 ", "Title": "Standard code enhancement to measure performance in RFUI", "Component": "SCM-EWM-RF-FW"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1785323", "RefTitle": "The performance measurement trace is incomplete", "RefUrl": "/notes/0001785323"}]}}}}}