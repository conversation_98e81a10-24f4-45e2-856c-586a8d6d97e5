{"Request": {"Number": "540406", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 307, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000540406?language=E&token=556697CEDF01F7EA69264F6971D62C3D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000540406", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000540406/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "540406"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB4"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for AS/400", "value": "BC-DB-DB4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "540406 - FAQ AS/400 IBM eServer iSeries (Kernel + OS)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p></p> <OL>1. SAP always recommends deploying, the latest version of the tools. How can I find out the versions of the Kernel, Lib_Dbsl, TP and R3trans ?</OL> <OL>2. How do I apply a fix from the SAP Service Marketplace on my AS/400 ?</OL> <OL>3. How do I upgrade to a higher OS/400 release ? What should I take into account ?</OL> <OL>4. I can see the file system.cfg in directory<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;usr/sap/trans/config/&lt;SID&gt;<br />Should or could I edit it manually ?</OL> <OL>5. I do not know, if I am using a 3.1I/4.0B COM kernel or the old 3.1I/4.0B kernel on my R/3 system. How can I find out ?</OL> <OL>6. My system is not performing well. Where should I look for problems ?</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />FAQ, Q+A, AS/400, OS/400, DB2/400, SAP on IBM eServer iSeries, AS400, OS400<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. SAP always recommends deploying the latest version of the tools. How can I find out the versions of the Kernel, Lib_Dbsl, TP and R3trans ?</OL> <UL><LI>For the kernel you can see it on R/3 in -&gt; system -&gt; status -&gt; other kernel info or in the developer tracefile.</LI></UL> <UL><LI>For DBSL it is in the developer tracefile (ST11). At the beginning of the file it is listed. For example :<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Version of library '.../run/dbdb4slib' is \"46D.00\",<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;patchlevel (0.638).'<br />&#x00A0;&#x00A0; Here the patchlevel of DBSL is 638 with a 46D version.</LI></UL> <UL><LI>In the case of TP the command is as follows :<br />CALL PGM(&lt;kernel library&gt;/tpos4) PARM('-V')</LI></UL> <UL><LI>For R3trans it would be:<br />CALL PGM(&lt;kernel library&gt;/R3trans) PARM('-V')<br /></LI></UL> <OL>2. How do I apply a fix from SAP Service Marketplace on my AS/400 ?</OL> <UL><LI>The note 49365 has been updated and contains now an extensive description of all different scenarios. Please study this note carefully. In there should be all the information you need. If there are any questions arising please open a customer message on component BC-OP-AS4.<br /></LI></UL> <OL>3. How do I upgrade to a higher OS/400 release ?<br />What should I take into account ?<br />The following points have to be considered :</OL> <UL><LI>It is very important to check beforehand, if the future OS/400 release you want to upgrade to is released with the R/3 version and kernel. Please check attached notes:<br />48007&#x00A0;&#x00A0;(3.0x/3.1x)<br />85845&#x00A0;&#x00A0;(4.0x/4.5x)<br />156557 (4.6x)<br />410783 (6.x)<br />If your new AS/400 release is not certified you can NOT upgrade. If in doubt, please open a customer message on component BC-OP-AS4.</LI></UL> <UL><LI>Check which patchlevel is required -&gt; see note 68440 point 2.</LI></UL> <UL><LI>Study note 68440.</LI></UL> <UL><LI>Search beforehand in the SAP note system for your new AS/400 release to see if there are any issues.<br />V5R1M0 (and higher): note 392165</LI></UL> <UL><LI>After you have upgraded the operating system, it is very important to apply all the PTFs from the current info-apar. Please see note 83292 for that.<br /></LI></UL> <OL>4. I can see the file system.cfg in directory<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;usr/sap/trans/config/&lt;SID&gt;<br />Should or could I edit it manually ?</OL> <UL><LI>The file system.cfg should not be edited manually, as this may cause corruption. This configuration file is automatically updated by the R3SETUP process or when an instance is deleted.<br /></LI></UL> <OL>5. I do not know if I use a 3.1I/4.0B COM kernel or the old 3.1I/4.0B kernel on my R/3 system. How can I find out ?</OL> <UL><LI>This can be seen in transaction SM51. At least the follwing patch levels are necessary:<br />3.1I_COM: 552<br />4.0B_COM: 802</LI></UL> <OL>6. My system is not performing well. Where should I look for problems ?</OL> <UL><LI>see the note 428855 for suggestions regarding settings at the operating system level, which may help avoid performance problems</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-OP-AS4 (IBM AS/400)"}, {"Key": "Responsible                                                                                         ", "Value": "D019267"}, {"Key": "Processor                                                                                           ", "Value": "D023193"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000540406/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540406/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "85845", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/85845"}, {"RefNumber": "83292", "RefComponent": "BC-INS-AS4", "RefTitle": "Release levels and PTFs for SAP on IBM i", "RefUrl": "/notes/83292"}, {"RefNumber": "68440", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: How do I upgrade to a later OS release?", "RefUrl": "/notes/68440"}, {"RefNumber": "621793", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: How do zero-interactive iSeries behave ?", "RefUrl": "/notes/621793"}, {"RefNumber": "541840", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/541840"}, {"RefNumber": "49365", "RefComponent": "XX-INT-FA-MAKE", "RefTitle": "iSeries: Applying a patch", "RefUrl": "/notes/49365"}, {"RefNumber": "48007", "RefComponent": "XX-SER-GEN", "RefTitle": "SAP Kernel 3.0x/3.1x DB2/400: Released operating systems", "RefUrl": "/notes/48007"}, {"RefNumber": "428855", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i system values", "RefUrl": "/notes/428855"}, {"RefNumber": "410783", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/410783"}, {"RefNumber": "392165", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Known problems under V5R1M0/V5R2M0", "RefUrl": "/notes/392165"}, {"RefNumber": "156557", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156557"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "83292", "RefComponent": "BC-INS-AS4", "RefTitle": "Release levels and PTFs for SAP on IBM i", "RefUrl": "/notes/83292 "}, {"RefNumber": "49365", "RefComponent": "XX-INT-FA-MAKE", "RefTitle": "iSeries: Applying a patch", "RefUrl": "/notes/49365 "}, {"RefNumber": "621793", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: How do zero-interactive iSeries behave ?", "RefUrl": "/notes/621793 "}, {"RefNumber": "392165", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Known problems under V5R1M0/V5R2M0", "RefUrl": "/notes/392165 "}, {"RefNumber": "68440", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: How do I upgrade to a later OS release?", "RefUrl": "/notes/68440 "}, {"RefNumber": "428855", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i system values", "RefUrl": "/notes/428855 "}, {"RefNumber": "48007", "RefComponent": "XX-SER-GEN", "RefTitle": "SAP Kernel 3.0x/3.1x DB2/400: Released operating systems", "RefUrl": "/notes/48007 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}