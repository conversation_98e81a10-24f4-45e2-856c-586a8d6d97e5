{"Request": {"Number": "1140031", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 635, "Error": {}, "SAPNote": {"_type": "00200720410000000532", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016462802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001140031?language=E&token=8F477639AE6CB63D1B30608C81A5E1A1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001140031", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1140031"}, "Type": {"_label": "Type", "value": "SAP Security Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.09.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-MID-RFC"}, "SAPComponentKeyText": {"_label": "Component", "value": "RFC"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "BC-MID", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "RFC", "value": "BC-MID-RFC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID-RFC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1140031 - Security Note: rfcexec/startrfc Used in File Interfaces"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Questions about the security of rfcexec and the RFC Library used in this context.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Windows: rfcexec.exe, startrfc.exe, librfc32.dll<br />Security Advisory</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>One of the ALE file interfaces, i.e. port type \"file\" and port type \"XML file\", are used in conjunction with the option to trigger the external subsystem through rfcexec.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In general there are 3 options to secure the file interface with triggering with respect to vulnerabilities exposed by the RFC Library.<br />For any solution using RFC communication SAP strongly recommends to secure the connection by SNC.</p> <UL><LI>Option 1: Triggering using SAP NetWeaver RFC Library.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Based on the SAP NetWeaver RFC Library (SAP note #1025361) ALE- tailored rfcexec and startrfc executables are provided in SAP NetWeaver RFCSDK 7.10 Patch Level 2 (SAP note #1058327), which can be downloaded from the SMP.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Using this package benefits from the security relevant improvements of the SAP NetWeaver RFC Library compared to the classic RFC Libraries ASCII and Unicode. In addition startrfc of the package can only invoke function modules EDI_DATA_INCOMING and EDI_STATUS_INCOMING; rfcexec checks that the command is requested by the ALE layer (requires kernel 6.40 Patch-level 236, 7.00 Patch-level 163&#x00A0;&#x00A0;or 7.10 Patch-level 107), and can be accompanied by a configuration file.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Each line of the configuration file bears:</p> <UL><UL><LI>The command, which is checked against the value from the port definition</LI></UL></UL> <UL><UL><LI>System ID</LI></UL></UL> <UL><UL><LI>Client ID</LI></UL></UL> <UL><UL><LI>User name</LI></UL></UL> <UL><LI>Option 2: Triggering using classic RFC Library.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rfcexec based on the classic RFC Library is a generic RFC server, which can be controlled by a list of forbidden commands (blacklist). See SAP note #618516 for more details. Both, the blacklist as well as the classic RFC Library, bear security risks just due to their complexity.</p> <UL><LI>Option 3: Don't use the trigger!</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Without the trigger, the ALE file interfaces do not require any RFC functionality, and RFC Libraries can be removed (watch out for other use cases!).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Since it is currently ambiguous for the subsystem to decide when a file has been finally created by the ALE interface, the scheduling of SAP to create IDoc files and the subsystem to consume these files may be challenging. To improve this situation an ALE development will be made available for SAP NetWeaver 7.11 onwards (SAP note #1169005), which establishes a file based hand shake for the IDoc files.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-MID-ALE (Integration Technology ALE)"}, {"Key": "Externally Reported", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D001521)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029216)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001140031/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001140031/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001140031/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001140031/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001140031/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001140031/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001140031/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001140031/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001140031/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "618941", "RefComponent": "BC-MID-ALE", "RefTitle": "EDI: Authorization check when triggering the file output", "RefUrl": "/notes/618941"}, {"RefNumber": "618516", "RefComponent": "BC-MID-RFC", "RefTitle": "Security-related enhancement of RFCEXEC program", "RefUrl": "/notes/618516"}, {"RefNumber": "27517", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "Installation of RFC SDK", "RefUrl": "/notes/27517"}, {"RefNumber": "1581595", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "rfcexec or startrfc are missing after upgrade", "RefUrl": "/notes/1581595"}, {"RefNumber": "1481923", "RefComponent": "BC-MID-RFC", "RefTitle": "Possible execution of arbitrary commands in RFC SDK tools", "RefUrl": "/notes/1481923"}, {"RefNumber": "1169005", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc file creation without triggering with rfcexec", "RefUrl": "/notes/1169005"}, {"RefNumber": "1058327", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "SAP NW RFC SDK 7.10 -- Patch-Level 2", "RefUrl": "/notes/1058327"}, {"RefNumber": "1025361", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "Installation, Support and Availability of the SAP NetWeaver RFC Library 7.20", "RefUrl": "/notes/1025361"}, {"RefNumber": "1005832", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1005832"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1913011", "RefComponent": "BC-MID-RFC", "RefTitle": "\"Timeout during allocate\" error after upgrading the Classic RFC SDK Library to SAP NW RFC SDK Library", "RefUrl": "/notes/1913011 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "27517", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "Installation of RFC SDK", "RefUrl": "/notes/27517 "}, {"RefNumber": "1025361", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "Installation, Support and Availability of the SAP NetWeaver RFC Library 7.20", "RefUrl": "/notes/1025361 "}, {"RefNumber": "1581595", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "rfcexec or startrfc are missing after upgrade", "RefUrl": "/notes/1581595 "}, {"RefNumber": "618941", "RefComponent": "BC-MID-ALE", "RefTitle": "EDI: Authorization check when triggering the file output", "RefUrl": "/notes/618941 "}, {"RefNumber": "1169005", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc file creation without triggering with rfcexec", "RefUrl": "/notes/1169005 "}, {"RefNumber": "1058327", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "SAP NW RFC SDK 7.10 -- Patch-Level 2", "RefUrl": "/notes/1058327 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.10", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.10", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.00", "To": "7.00", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.10", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.10", "Subsequent": ""}, {"SoftwareComponent": "SAP RFCSDK", "From": "6.20", "To": "6.20", "Subsequent": ""}, {"SoftwareComponent": "SAP RFCSDK", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "SAP RFCSDK", "From": "7.00", "To": "7.00", "Subsequent": ""}, {"SoftwareComponent": "SAP RFCSDK", "From": "7.10", "To": "7.10", "Subsequent": ""}, {"SoftwareComponent": "NWRFCSDK", "From": "7.10", "To": "7.10", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP107", "SupportPackagePatch": "000107", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP107", "SupportPackagePatch": "000107", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP107", "SupportPackagePatch": "000107", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP107", "SupportPackagePatch": "000107", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP236", "SupportPackagePatch": "000236", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP163", "SupportPackagePatch": "000163", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "CVSS": {"_label": "CVSS", "CVSS_Score": {"_label": "CVSS Score                                        ", "value": "0"}, "CVSS_Vector": {"_label": "CVSS Vector                                       ", "vectorValue": "", "vectorVersion": "3.0", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Attack Vector (AV)", "Value": ""}, {"Key": "Attack Complexity (AC)", "Value": ""}, {"Key": "Privileges Required (PR)", "Value": ""}, {"Key": "User Interaction (UI)", "Value": ""}, {"Key": "<PERSON><PERSON> (S)", "Value": ""}, {"Key": "Confidentiality Impact (C)", "Value": ""}, {"Key": "Integrity Impact (I)", "Value": ""}, {"Key": "Availability Impact (A)", "Value": ""}]}}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}