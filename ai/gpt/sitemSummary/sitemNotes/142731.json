{"Request": {"Number": "142731", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 240, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014655462017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000142731?language=E&token=DD0A8A575A95C2007808003904B4D132"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000142731", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000142731/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "142731"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.04.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-MSS"}, "SAPComponentKeyText": {"_label": "Component", "value": "SQL Server in SAP NetWeaver Products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SQL Server in SAP NetWeaver Products", "value": "BC-DB-MSS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-MSS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "142731 - SQL Server DBCC checks"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note describes how to check the physical consistency of a database with the Microsoft SQL Server. The following synonyms for this procedure all mean the same thing:<br />- Database check<br />- DBCC check (DBCC = Database Consistency Checker)<br />- DBCC CHECKDB<br />- Consistency check</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>DBCC CHECKDB, DBCC CHECKTABLE, DB13</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>There are two types of consistency in Relational Database Management Systems (RDBMS).<br /><br />Logical consistency:<br />The application (SAP R/3) must ensure the logical consistency of the database. Database transactions and SQL commands are available to do this. For example, there is a 'logical inconsistency' if there is no corresponding primary key for a foreign key.<br /><br />Physical consistency:<br />The RDBMS (MS SQL Server) is responsible for the physical consistency of the database. There is a physical inconsistency if there are errors in internal structures (page pointer, allocation ...) in the database. A physical inconsistency is also referred to as a (database) corruption.<br /><br />A manual DBCC check should be carried out as soon as you suspect a database corruption. You may suspect this if there are frequent short dumps in R/3, in particular if the following SQL Server error messages appear: SQL Error 601, 605, 644, 823, 2511, 8928, 8944, 8952, 8976<br />It is also advisable to run a regular DBCC check, for example once a month. However, experience has shown that you can detect corruptions much faster by analyzing R/3 short dumps instead of regular DBCC checks.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>A DBCC check should be executed at times when there is a low load on the database, because a DBCC check causes a very high I/O load.<br /><br />The duration of a DBCC check depends (among other things) to a very large extent on the size of the database and the hardware used. The following blog discusses additional factors affecting runtime:<br /> <a target=\"_blank\" href=\"https://techcommunity.microsoft.com/t5/SQL-Server/CHECKDB-Part-7-How-long-will-CHECKDB-take-to-run/ba-p/383221\">https://techcommunity.microsoft.com/t5/SQL-Server/CHECKDB-Part-7-How-long-will-CHECKDB-take-to-run/ba-p/383221</a></p>\r\n<p><strong>Executing the DBCC check</strong></p>\r\n<p>The consistency of the &lt;SAPSID&gt; database is checked using an SQL command in the SQL Server Management Studio. To do this, execute the following SQL command and save the result as a text file:</p>\r\n<ul>\r\n<li>dbcc checkdb(&lt;SAPSID&gt;) with no_infomsgs</li>\r\n</ul>\r\n<p><br />In the case of large databases or short maintenance windows, the runtime may exceed the maximum time available.  In such a case, you can split the CHECKDB in a CHECKCATALOG and a CHECKTABLE across all tables. You can use the attached script as a template to check the tables in several steps.  To do this, the SELECT </p>\r\n<ul>\r\n<li>SELECT user_name(uid) , name</li>\r\n</ul>\r\n<p>FROM sysobjects<br /> WHERE type IN ('U' ,'S')<br /><br />must be enhanced with a condition, for example, for a name restriction (for example, name like 'A%'). In this way, all of the tables can be checked bit by bit (for example, Mondays = A%, Tuesdays = B%, Wednesdays = C%, and so on).  The check can also be used if the CHECKDB terminates with an error message. <br /><br />If there is no corruption, the result is as follows: DBCC execution completed. If DBCC printed error messages,<br /> contact your system administrator.<br />In the case of a corruption, the DBCC CHECKDB ends with: CHECKDB found ... allocation errors and ... consistency errors<br /> in database '&lt;SID&gt;'.<br /> &lt;level&gt; is the minimum repair level for the errors found<br /> by DBCC CHECKDB (&lt;SID&gt;).<br />where &lt;level&gt; stands for one of the following three repair levels: repair_allow_data_loss<br /> repair_fast<br /> repair_rebuild<br />If the repair level \"repair_fast\" or \"repair_rebuild\" is displayed, you can repair all corrupt tables without loss of data using SQL commands. The repair level \"repair_allow_data_loss\" is a critical corruption. This means that at least one table can no longer be repaired without loss of data. To find out which tables these are, you can check all corrupt tables separately. You should then separately check every table mentioned in the output of DBCC CHECKDB WITH NO_INFOMSGS again using the following SQL command: use &lt;SAPSID&gt;<br /> setuser '&lt;sid&gt;' - required only for a schema system<br /> dbcc checktable(&lt;TABLE&gt;)<br />The repair level for each table will then be displayed separately.<br /><br /></p>\r\n<p><strong>Repair of a corruption</strong></p>\r\n<p>Before you try to repair a corruption, you should be aware of the following: a corruption is almost always caused by a hardware (HW) problem.. You should therefore first contact your HW partner to have the HW checked. So long as the database runs on a defective HW, any attempt at repairing is a complete waste of time.</p>\r\n<ul>\r\n<li><strong>Transient corruptions</strong><br />Each access to a database page is first stored in the data buffer of the SQL server. Defective RAM modules or software bugs may cause transient corruptions. In this case, only the database buffers are corrupt, not the database files on the hard disk. In the case of a transient corruption, the system displays various corruptions if you repeatedly execute DBCC CHECKDB. A corruption may also disappear again of its own accord. If you want to check the database files in the case of a transient corruption, you must empty the SQL server data buffers before executing DBCC CHECKDB. This occurs automatically when you stop/start the SQL server or can be performed manually using the following SQL command: dbcc dropcleanbuffers<br />Since a transient corruption is the result of a serious error, you should not simply ignore this type of corruption. If the database files are on a RAID system, individual pages could be out of sync. This means that the RAID system mirror is not 100% identical to the original system. To optimize performance when reading from the RAID system, readings are sometimes taken from the original and sometimes from the mirror system. This means that the result of the DBCC CHECKDB can differ each time it is performed, even if you always execute DBCC DROPCLEANBUFFERS beforehand.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Non-critical corruptions</strong><br />Non-critical corruptions: You can repair tables that need the repair level \"repair_fast\" or \"repair_rebuild\" using the SQL command DBCC CHECKTABLE with the option REPAIR_REBUILD. The database option \"single user\" must be set for this:<br />1) Stop R/3.<br />2) Back up the database.<br />3) Stop/start the SQL Server.<br />4) Set single user mode using the following SQL command: sp_dboption '&lt;SID&gt;', 'single user', 'TRUE'.<br />5) Execute CHECKTABLE for each corrupt table separately: use &lt;SAPSID&gt;<br /> dbcc checktable(&lt;TABLE&gt;, repair_rebuild). \n6) Reset single user mode: sp_dboption '&lt;SID&gt;', 'single user', 'FALSE'7) DBCC CHECKDB. Back up the database.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Critical corruptions</strong><br />If one or more tables need the repair level \"repair_allow_data_loss\", this is a critical corruption. If several tables are affected, restoring the database is usually the best solution. A prerequisite for this is that the database was not corrupt at the time of the backup. The restore can take place with or without additional transaction logs. When you also try to restore the transaction logs, a single corrupt transaction log can cause the restore to terminate and thereby cause further time loss.<br /><br />If only tables with redundant or less important data are affected, you can repair these tables using DBCC CHECKTABLE with the option REPAIR_ALLOW_DATA_LOSS. However, this can cause data loss in the affected tables. Data records that have not been changed for years may also be affected. The effect of the data loss can only be evaluated by an application developer, for example by comparing the data records with other tables. A database expert cannot make any assertions in relation to this.<br /><br />Use the option REPAIR_ALLOW_DATA_LOSS only if you have no other choice - that is, if all the backups are corrupt. Neither Microsoft nor SAP can guarantee that this type of repair will function correctly or achieve the desired results. Even a REPAIR_ALLOW_DATA_LOSS may not repair all corruptions. Sometimes you may have to execute this a second or third time. A REPAIR_ALLOW_DATA_LOSS may delete more data than is necessary.  For this reason, it is essential that you back up your database before and after a REPAIR_ALLOW_DATA_LOSS. </li>\r\n</ul>\r\n<p><strong>Investigation of causes</strong></p>\r\n<p>Theoretically, every component of a database server, either software (SW) or hardware (HW), can cause a corruption. In practice, however, a corruption can almost always be traced back to hardware or driver problems. A defect in any HW component can cause corruption. For example, disks, RAID controllers, RAM, CPU and even defective fans can cause malfunctions due to overheating. Many HW problems only occur sporadically. The NT event log and DRWatson Log may indicate an HW problem. However, often you will find nothing unusual there, despite the fact that the HW is defective. If in doubt, it is safest to completely replace the HW. However, in the case of a driver or BIOS problem, the same problem can also occur on different servers of the same type.<br /><br />Therefore it is very important that you carry out a complete HW check.</p>\r\n<ul>\r\n<li>Check the RAM modules.</li>\r\n</ul>\r\n<ul>\r\n<li>Check the RAID controller and the disk.<br />If the RAID controller does not have a battery buffer, it is imperative that you deactivate all WRITE caches.<br />Server disks are always delivered with the WRITE cache deactivated.</li>\r\n</ul>\r\n<ul>\r\n<li>Check all other HW components.</li>\r\n</ul>\r\n<p><br />If there is a corruption, you should update all other software components as a precaution:</p>\r\n<ul>\r\n<li>The latest BIOS for all components, including disks and the RAID controllers</li>\r\n</ul>\r\n<ul>\r\n<li>The latest drivers for the disks and the RAID controllers</li>\r\n</ul>\r\n<ul>\r\n<li>The latest Windows Service Pack</li>\r\n</ul>\r\n<ul>\r\n<li>The latest Service Pack for SQL Server</li>\r\n</ul>\r\n<p><strong>Consistent view of database for CHECKDB<br />In SQL Server 2000, other transactions can change a table while it is being checked. The CHECKDB uses the transaction log to recognize these changes. This has the disadvantage that the transaction log cannot be truncated during a CHECKDB. A long CHECKDB may require a large transaction log.<br /><br />SQL Server 2005 and higher creates a database snapshot at the beginning of the CHECKDB if all database files are on one NTFS file system. The system then creates an additional stream (NTFS sparse file) for each database file for the snapshot, which is not visible in Windows Explorer. Therefore, a long CHECKDB requires free space in all directories containing database files. The space requirement depends on the transactions running in parallel, and therefore cannot be calculated in advance.<br />If you cannot use the snapshot procedure or if you specified the option WITH TABLOCK, SQL 2005 (and higher) uses table locks instead.</strong><br /> <strong>Automate DBCC checks</strong></p>\r\n<p>The job \"Check Database Consistency\" can be scheduled in the R/3 transaction DB13. This executes a DBCC CHECKDB. The following prerequisites are then necessary:<br />- you must start the SQL Server<br />- you must start the SQL Server agent<br />- the database load should be as low as possible during the job<br /><br />The following reasons can cause the job in DB13 to be highlighted in red:<br />- the job is currently being executed<br />- the job could not be executed<br />- the job was executed and detected a corruption.<br /><br />The job logs the start and finish in the SQL Server error log. The SQL error message 50000 is displayed for this. SQL error 50000 is therefore not an error and can be ignored.<br />The log file CCMS_CHECKDB_HIST.txt in the SQL Server directory contains detailed error messages. Example: C:\\Program Files\\Microsoft SQL Server\\MSSQL\\<br />C:\\Program Files\\Microsoft SQL Server\\MSSQL$&lt;SID&gt;\\</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C5024907)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5188554)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000142731/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000142731/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "DBCC_checktable_all_tables.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700006943082001&iv_version=0020&iv_guid=A6E4127E6489614A9B7E7486221B0E36"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "565708", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/565708"}, {"RefNumber": "325443", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/325443"}, {"RefNumber": "161189", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/161189"}, {"RefNumber": "1597910", "RefComponent": "BC-DB-MSS", "RefTitle": "Handling of database corruptions on SQL Server", "RefUrl": "/notes/1597910"}, {"RefNumber": "1420452", "RefComponent": "BC-DB-MSS", "RefTitle": "FAQ: Restore and recovery with MS SQL Server", "RefUrl": "/notes/1420452"}, {"RefNumber": "139945", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/139945"}, {"RefNumber": "1237674", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Error 5128: Write to sparse file failed", "RefUrl": "/notes/1237674"}, {"RefNumber": "1006920", "RefComponent": "BC-DB-MSS", "RefTitle": "DBCC Checks show 8914 Errors on SQL Server 2005", "RefUrl": "/notes/1006920"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2575656", "RefComponent": "BC-DB-MSS", "RefTitle": "CREATE UNIQUE INDEX error while trying to repair corrupted table", "RefUrl": "/notes/2575656 "}, {"RefNumber": "1660220", "RefComponent": "BC-DB-MSS", "RefTitle": "Microsoft SQL Server: Common misconceptions", "RefUrl": "/notes/1660220 "}, {"RefNumber": "1237674", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Error 5128: Write to sparse file failed", "RefUrl": "/notes/1237674 "}, {"RefNumber": "1597910", "RefComponent": "BC-DB-MSS", "RefTitle": "Handling of database corruptions on SQL Server", "RefUrl": "/notes/1597910 "}, {"RefNumber": "1420452", "RefComponent": "BC-DB-MSS", "RefTitle": "FAQ: Restore and recovery with MS SQL Server", "RefUrl": "/notes/1420452 "}, {"RefNumber": "965530", "RefComponent": "BC-DB-MSS", "RefTitle": "601 Errors in SQL Server", "RefUrl": "/notes/965530 "}, {"RefNumber": "1006920", "RefComponent": "BC-DB-MSS", "RefTitle": "DBCC Checks show 8914 Errors on SQL Server 2005", "RefUrl": "/notes/1006920 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}