{"Request": {"Number": "165619", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 354, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000814382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=C18146DE1FD3C3A392ACF3156F55370D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "165619"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.05.2000"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AP-AP-Q"}, "SAPComponentKeyText": {"_label": "Component", "value": "Withholding Tax (Calculation)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounts Payable", "value": "FI-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AP-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Withholding Tax (Calculation)", "value": "FI-AP-AP-Q", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP-Q*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "165619 - Correction of 770 module for fiscal year 1998"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Attention! This note is relevant only for Italy.<br /><br />Various errors present in the version of 770 module downloaded from Sapserv3 in June 99 according to the note 0151510.<br /><br />Here are described the 4 symptoms :<br /></p> <OL>1. In the file sent to the Tax Authorities, the format of the payee's date of birth was DDMMYY for the fiscal year 1997. It has been changed in DDMMYYYY for the fiscal year 1998. The field concerned is situated in the section SC, for each payee, in column 5 and is called 'Data di nascita del percipiente'.</OL> <OL>2. The option 'List also null amounts' doesn't work: That means that no matter if this option is marked or not, the null amounts were always filled and sent to the Tax Authorities. There is no legal obligation concerning this point.</OL> <OL>3. Within the section SC where the income for self-employment and certain other forms of income are registered, the juridical persons non resident were not declared as 'Non Resident'. The field concerned is 'Casella non residenti' and must be filled in that case with the appropriate value ('1' = 'Non Resident').</OL> <OL>4. If you use the entry \"Form 770\" in report tree Accounts payable-&gt; Withholding tax -&gt; Italy, you'll get the error message \"include report ZFKQSI02 not found\".</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Italy, RFKQST40, RF0KQST4, RF0KQST5, 770, Withholding tax, report tree.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The transport files are available on Sapserv3 ( or Sapserv4) and<br />have to be imported into customers environment via Ftp, and afterwards<br />generated and activated via ./tp put or import.<br />If you decide to implement the corrections directly into your system, look at the attached corrections instructions.<br />Attention ! These corrections have to be applied on the original program downloaded from Sapserv3.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The transport files are stored on the directory:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;~/general/R3server/abap/note.0165619<br /><br />Files names :<br /><br />For classical Withholding tax : program RF0KQST4<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;For 4.0B to 4.5B systems : K902391.TI4<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R902391.TI4<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;For 3.0F to 3.1I systems : K902490.TI1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R902490.TI1<br /><br />For extended Withholding tax : program RF0KQST5<br />( this program can be used for classical WT from 4.0B as well )<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;From 4.0B to 4.5B systems: K902392.TI4<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R902392.TI4<br /><br />Documentation : The program documentation remains unchanged.<br /><br /><br />------------------------------------------------------------------------<br /><br />Please refer to the related note n. 13719 which explains how to fetch<br />transport from SapservX.<br />------------------------------------------------------------------------<br />For the symptom 4 using the report tree entry: As the report RFKQST40 was deleted in some releases and replaced by the RF0KQST4 from SAPServ, you have to manually modify the report tree. You can use transaction OBRK and enter the tree name FIAP to modify the tree node Accounts payable-&gt; Withholding tax Italy -&gt; Form 770. You can delete this entry and add the correct program RF0KQST4 as node.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D028279"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "85012", "RefComponent": "BC-DOC-TTL", "RefTitle": "Translation guidelines for Releases 3.1H - 4.0B", "RefUrl": "/notes/85012"}, {"RefNumber": "303619", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Version of Modello 770 / 2000", "RefUrl": "/notes/303619"}, {"RefNumber": "26682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/26682"}, {"RefNumber": "209092", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Problem in Modello 770 due to FIWT0010 update", "RefUrl": "/notes/209092"}, {"RefNumber": "202294", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Correction of 770 module for fiscal year 1998", "RefUrl": "/notes/202294"}, {"RefNumber": "190184", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Correction of 770 module for fiscal year 1998", "RefUrl": "/notes/190184"}, {"RefNumber": "170677", "RefComponent": "FI-AP-AP-Q", "RefTitle": "2nd correction of 770 module for fiscal year 1998", "RefUrl": "/notes/170677"}, {"RefNumber": "151510", "RefComponent": "FI-AP-AP-Q", "RefTitle": "New version of 770 module for fiscal year 1998", "RefUrl": "/notes/151510"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "125382", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/125382"}, {"RefNumber": "121875", "RefComponent": "FI-AP", "RefTitle": "Version of 770 module(1997) - Corrections", "RefUrl": "/notes/121875"}, {"RefNumber": "119486", "RefComponent": "FI-AP", "RefTitle": "New Version of 770 module for fiscal year 1997.", "RefUrl": "/notes/119486"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "85012", "RefComponent": "BC-DOC-TTL", "RefTitle": "Translation guidelines for Releases 3.1H - 4.0B", "RefUrl": "/notes/85012 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "303619", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Version of Modello 770 / 2000", "RefUrl": "/notes/303619 "}, {"RefNumber": "170677", "RefComponent": "FI-AP-AP-Q", "RefTitle": "2nd correction of 770 module for fiscal year 1998", "RefUrl": "/notes/170677 "}, {"RefNumber": "202294", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Correction of 770 module for fiscal year 1998", "RefUrl": "/notes/202294 "}, {"RefNumber": "151510", "RefComponent": "FI-AP-AP-Q", "RefTitle": "New version of 770 module for fiscal year 1998", "RefUrl": "/notes/151510 "}, {"RefNumber": "190184", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Correction of 770 module for fiscal year 1998", "RefUrl": "/notes/190184 "}, {"RefNumber": "209092", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Problem in Modello 770 due to FIWT0010 update", "RefUrl": "/notes/209092 "}, {"RefNumber": "121875", "RefComponent": "FI-AP", "RefTitle": "Version of 770 module(1997) - Corrections", "RefUrl": "/notes/121875 "}, {"RefNumber": "119486", "RefComponent": "FI-AP", "RefTitle": "New Version of 770 module for fiscal year 1997.", "RefUrl": "/notes/119486 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/**********/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}