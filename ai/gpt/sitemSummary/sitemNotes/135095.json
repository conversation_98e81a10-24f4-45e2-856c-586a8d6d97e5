{"Request": {"Number": "135095", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 260, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014634722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=8B72BF616725C807C4BF665FFA81D374"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "135095"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.01.1999"}, "SAPComponentKey": {"_label": "Component", "value": "TR-CB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Cash Budget Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Treasury", "value": "TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cash Budget Management", "value": "TR-CB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR-CB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "135095 - Auxiliary programs for Cash Budget Management"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use Cash Budget Management. Because of errors in the update or changed master data assignments your flow data is in an unsatisfactory state.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In the transport request: P45K073615<br />directory:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /general/R3server/abap/note.0135095<br />on SAPSERVx, you will find the auxiliary programs:<br /></p> <UL><LI>RFFMDL17: Individual Deletion Program (Activity/Account Assignment)</LI></UL> <UL><LI>RFFMDLOG: FMLOG Deletion Program</LI></UL> <UL><LI>RFFMHIST: Apportionment of a (Payment) Document in Cash Budget Management</LI></UL> <UL><LI>RFFMS182: Document items receive commt item from G/L account (for LI/OI accounts)</LI></UL> <UL><LI>RFFMS183: Enter Commitment Item in Purchase Order Invoices</LI></UL> <UL><LI>RFFMS184: FI documents receive commitment item from G/L account</LI></UL> <p><br />Consult Note 13719 on how to import a transport request.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021772)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "532333", "RefComponent": "TR-CB-IN", "RefTitle": "Reorganization of commitment items in original documents", "RefUrl": "/notes/532333"}, {"RefNumber": "355348", "RefComponent": "TR-CB-IN", "RefTitle": "High amounts w/GR/IR item after data reconstruction", "RefUrl": "/notes/355348"}, {"RefNumber": "340026", "RefComponent": "TR-CB-IS", "RefTitle": "Line item display from RFFMHIST does not work", "RefUrl": "/notes/340026"}, {"RefNumber": "308164", "RefComponent": "TR-CB-IN", "RefTitle": "Incnstncy BSEG/index tables after commt item reorg", "RefUrl": "/notes/308164"}, {"RefNumber": "178678", "RefComponent": "TR-CB", "RefTitle": "(FI) Using both Cash Budget Management and PS-Cash Managmnt", "RefUrl": "/notes/178678"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "122636", "RefComponent": "TR-CB-IN", "RefTitle": "Data reorganization and commitment items", "RefUrl": "/notes/122636"}, {"RefNumber": "114151", "RefComponent": "TR-CB-IN", "RefTitle": "Collective note Cash Budget Management Release 4.0A/B", "RefUrl": "/notes/114151"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "355348", "RefComponent": "TR-CB-IN", "RefTitle": "High amounts w/GR/IR item after data reconstruction", "RefUrl": "/notes/355348 "}, {"RefNumber": "532333", "RefComponent": "TR-CB-IN", "RefTitle": "Reorganization of commitment items in original documents", "RefUrl": "/notes/532333 "}, {"RefNumber": "122636", "RefComponent": "TR-CB-IN", "RefTitle": "Data reorganization and commitment items", "RefUrl": "/notes/122636 "}, {"RefNumber": "178678", "RefComponent": "TR-CB", "RefTitle": "(FI) Using both Cash Budget Management and PS-Cash Managmnt", "RefUrl": "/notes/178678 "}, {"RefNumber": "114151", "RefComponent": "TR-CB-IN", "RefTitle": "Collective note Cash Budget Management Release 4.0A/B", "RefUrl": "/notes/114151 "}, {"RefNumber": "340026", "RefComponent": "TR-CB-IS", "RefTitle": "Line item display from RFFMHIST does not work", "RefUrl": "/notes/340026 "}, {"RefNumber": "308164", "RefComponent": "TR-CB-IN", "RefTitle": "Incnstncy BSEG/index tables after commt item reorg", "RefUrl": "/notes/308164 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46A", "SupportPackage": "SAPKH46A10", "URL": "/supportpackage/SAPKH46A10"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}