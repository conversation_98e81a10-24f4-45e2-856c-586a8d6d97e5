{"Request": {"Number": "113031", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 234, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014586922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=F632BC55D29D86227ABF51B885D51D30"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "113031"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.02.2004"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "113031 - Subsequent settlement: Taxes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes handling of taxes within the process of subsequent settlement (purchasing).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (purchasing), volume-based rebate, Transaction MEB4, report RWMBON01<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>None.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Releases as of Release 4.5A are considered first. Various restrictions apply for earlier releases which you can find later in this note, partly with advance corrections.<br />Be aware in particular of the different procedure for company codes with a taxation procedure via tax jurisdiction codes (for example, USA, Canada); see the Special Features section.<br />First, some basic comments.<br />When you carry out updating of business volume from a document relevant for subsequent settlement (purchase order/scheduling agreement (as of Release 4.0A), goods receipt (as of Release 4.0A), invoice verification, vendor billing document (as of Release 4.5A), settlement request (as of Release 4.5A)), the business volumes are determined from document data and stored in an aggregated form in an information structure. The tax code, to which the business volumes were incurred, is recorded as characteristics (data retention level), in addition to the allocation to the condition record and the plant. The aim is in any case to allow tax adjustment posting during the settlement.<br />Therefore, a prerequisite is that the tax code is known at the time of update. For updates from purchase orders/scheduling agreements and goods receipts, this means that the tax code must already be known in the purchase order item. Note that an update for the time of goods receipt in particular is then carried out if you work with provisions for accrued income.<br />The tax code, for which updating of business volume is being carried out, is unrelated to the tax code which is used to settle the incomes. Ensure that you do not change the tax code of the business volume data when executing the updating of business volume, in other words, do not change any of the fields MCKONA-MWSKZ, VBRP-MWSKZ, WBRP-MWSKZ in user exits or by means of modification.<br />The tax calculation must only be carried out via the settlement schema.<br /><br />Tax code in purchase orders/scheduling agreements<br /><br />For this, there are several possibilities to avoid manual maintenance:</p> <UL><LI>Maintenance in the material info record</LI></UL> <UL><UL><LI>On purchasing organization level (only one company code)</LI></UL></UL> <UL><UL><LI>On plant level (several company codes, different countries)</LI></UL></UL> <UL><LI>Determination with the condition technique</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Condition type NAVS (non-ded. input tax) with condition category N for non-deductible input taxes is delivered here. It is useful to create a new condition type (copy NAVS) with condition category D (taxes).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The tax code determined via the document conditions is transferred to the document item and, thus, is available for subsequent settlement.<br /><br />The tax code of business volume data is transferred to the settlement document so that it can be included in the tax calculation there (option).<br />Example: Business volume 50,000 DEM for tax code V1 - 16 %,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50,000 DEM for tax code V2 - 7 %,<br />Condition of 3 % on 100,000 DEM results in 3,000 DEM income.<br />The settlement document contains two items for 1,500 DEM for tax code V1 and 1,500 DEM for tax code V2. Whether taxes are now actually calculated and in which way they are calculated depends on the settlement schema. All varieties which are allowed by the condition technique are imaginable here.<br /><br />Examples:</p> <UL><LI>Tax determination by means of the settlement material of the condition record (tax indicator material), for example, if the settlement refers to a service, thus, output tax should be calculated (tax code of business volume data is not relevant).</LI></UL> <UL><LI>Direct tax calculation by means of the tax code of business volume data (new condition class \"G\" (tax code)), see below, for example, input tax adjustment with settlement.</LI></UL> <UL><LI>Do not calculate any taxes (do not enter a tax condition type in the settlement schema).</LI></UL> <p><br />Mixed cases can be solved, for example, by assigning different settlement schemas (vendor-specific), different settlement materials, or by using requirements. Also the use of condition exclusion groups would be conceivable. As of Release 4.5A, there is the option of assigning the settlement schema directly to the arrangement type and, therefore, of differentiating during tax calculation.<br /><br />Special features:</p> <UL><LI>In company codes with tax processing using tax jurisdiction codes (for example, USA, Canada), during updating of business volume the tax code of the document item is replaced by the tax code of the company code for non-taxable transactions (vendor arrangements: input tax, customer arrangements, output tax). The used tax jurisdiction code is not stored. The tax code of the company code for non-taxable transactions plus the respective tax jurisdiction code is transferred to the settlement document.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Maintain the tax codes as well as the tax jurisdiction code for non-taxable transactions of the company code (Transaction OBCL).</p> <UL><LI>If you want to create new condition tables in tax processing (other fields on which tax calculation should depend), refer to Note 79947 for Release 3.0/4.0 and to Note 160412 for Release 4.5.</LI></UL> <UL><LI>You want to replace the tax code of business volume data with another tax code, for example, from country Germany (DE) and tax code V1 of the business volume data, derive tax code A1 for the settlement. For this you should create a condition table with the fields Country, Tax code.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Do not use field MWSKZ in the condition table since this field has special rules. Instead, insert field ZMWSKZ in structure KOMG, in the field catalog Price Determination Purchasing insert the field, create the condition table, call condition maintenance once (so the maintenance screen is generated) and refer to the above-mentioned notes. In the access sequence, define that the access should be performed with field KOMK-MWSKZ (Tax code of business volume data).<br /><br />Restrictions:</p> <UL><LI>Up to and including Release 4.0B, during credit-side settlement credit memos without order reference (invoice verification) are generated. These documents unfortunately have no kind of functions for differentiated tax calculation. The tax code of business volume data is transferred so that a tax adjustment posting is triggered.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Release 4. 5A, vendor billing documents are used as settlement documents instead of credit memos. Vendor billing documents correspond in their functions to a large extent to customer billing documents. In particular, a price determination is carried out so that a differentiated tax calculation is possible.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Workaround:</p> <UL><UL><LI>Use customer billing documents (settlement types \"2\" and \"3\") as settlement documents. In Release 4.5A, a change to vendor billing documents is possible.</LI></UL></UL> <UL><UL><LI>In Release 3.1I as well as in Release 4.0B, credit memos can be parked so that changes, in particular of the taxes, are possible before your posting in Financial Accounting.</LI></UL></UL> <UL><UL><LI>Replace the tax code during updating of business volume (see Note 113655).</LI></UL></UL> <UL><LI>The debit-side settlement by customer billing document can only process one tax code in Release 3.0 (all maintenance levels). A direct calculation of taxes via the tax code of business volume data is not possible (condition category \"G\" does not exist/is not implemented).</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For both problems a very costly advance correction exists that is available up to now for Release 3.0F and Release 3.1I, that is, it was tested. You can receive the corresponding notes on separate request.<br /><br />New condition class \"G\" (tax code) for condition types<br /><br />As of Release 4.0A, the option exists to directly transfer the tax calculation from Financial Accounting to the price determinaton data (customer billing document, vendor billing document). This new method can, of course, also be used outside of subsequent settlement. In particular, this procedure is then recommendable if a tax adjustment is required with the settlement.<br />Tax calculation can be carried out directly with the tax code of business volume data, that is, without using the settlement material.<br />For this, the new condition class \"G\" (tax code) was implemented. For condition records of class \"D\" (taxes), first the tax calculation in the document is carried out with the data of the respective application (sales and distribution, purchasing), that is, with the tax data maintained here. During the transfer of the document to Financial Accounting, a recalculation is carried out with the data maintained in Financial Accounting (double maintenance).<br />If condition class \"G\" is used instead of condition class \"D\", the taxes are determined directly in the document from Financial Accounting where the tax code of the document item is used. The generated condition records of Financial Accounting are set in the logistics document.<br />As of Release 4.0A, condition type VS00 (tax trigger) is provided in the standard system for this. Settlement schema RM5001 is delivered as a sample.<br />Set the Condition Class field to \"G\" and the calculation rule to \"A\", select field \"Item condition\". The remaining fields stay empty.<br />Enter condition type VS00 in the settlement schema. At the same time, you must create in purchasing all relevant tax condition types from the tax calculation schema of your country with the same name and also enter them in the settlement schema (generally, directly behind condition type VS00). If you do not enter the relevant tax condition types into the settlement schema, you get error message MN 164: \"Pricing error during billing check run\".<br />Select \"D\" as condition class, set the condition category to \"D\", set the calculation rule to \"A\", and select field \"Item condition\". The remaining fields stay empty.<br />Indicator \"Man.\" must be set in the calculation schema.<br />Example: Country DE (Germany) with tax calculation schema TAXD. Tax code V1 provides 16% input tax for condition type MWVS. Therefore, create condition type MWVS in purchasing with the same name (as described above) and enter it in the settlement schema behind condition type VS00. Check whether a basic formula unequal to 2 is necessary (see below).<br />Note:</p> <UL><LI>The new mechanism cannot handle all tax calculations since not all functions of Financial Accounting are supported.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Depending on the document (vendor billing document, customer billing document), not all tax types can be posted. Test the settings of your system carefully, especially for non-deductible input tax.<br /><br />Posting keys<br />When settling by vendor billing document, the posting keys, including the tax rate to be posted, are determined exclusively from the settings in Financial Accounting. The posting keys in the settlement schema are not relevant and do not need to be maintained. The amounts displayed in the \"Taxes\" screen are posted. These are derived from the settings in Financial Accounting. The values displayed in the document conditions are not relevant (for example, tax indicator determination).<br />When settling by customer billing document, a distinction must be made between condition class \"D\" and \"G\". For the condition types of condition \"D\" (tax code determination), you must maintain the account key, however you do not have to maintain this for condition class \"G\" (tax trigger). In the latter case, the settings in Financial Accounting again apply.<br />General comments:</p> <UL><LI>Remember that the basic formula 2 (net value KOMP-NETWR) is generally used, therefore, the net value is used as the tax base amount. You can enter a different formula in the settlement schema. Customized formulas should, therefore, contain the code from formula 2. Remember that the valuation, that is, calculating the tax for displaying the document via these settings in the settlement schema, should correspond with the settings in Financial Accounting.</LI></UL> <UL><LI>Example: Tax code E1 (Acquisition tax), Country DE, Tax procedure TAXD, Tax type input tax.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tax type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A/C Key&#x00A0;&#x00A0;Tax Perc. Record&#x00A0;&#x00A0;Level&#x00A0;&#x00A0;FromLevel CondType<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basic amount&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BASB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Output tax&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MWS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MWAS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Input tax&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VST&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;120&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MWVS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Travel expenses&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VST&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;130&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MWRK (from H)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Input tx after red. for NAV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;140&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MWVN<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Input tx after assign.&#x00A0;&#x00A0;NVV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MWVZ<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Acquisition tax Output&#x00A0;&#x00A0;ESA&#x00A0;&#x00A0;10,000-&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NLXA<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Acquisition tax Input&#x00A0;&#x00A0; ESE 100,000-&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;210&#x00A0;&#x00A0;&#x00A0;&#x00A0;200&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NLXV<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create the NLXA and NLXV condition types as described and enter in the settlement schema, for example:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Level CondType&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Manual&#x00A0;&#x00A0;Subtotal&#x00A0;&#x00A0;Basic formula<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;900 0 VS00 Tax trigger<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;910 0 NLXA Acquisition tax Output&#x00A0;&#x00A0;X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;920 0 NLXV Acquisition tax Input&#x00A0;&#x00A0; X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 5<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The tax value calculated in line 910 is transferred to the KOMP-KZWI1 subtotal and used in formula 5 as the base value for calculating the tax in line 920. You must not use the subtotal of 1 elsewhere in the schema.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Important: For condition type VS00 (Tax trigger), the \"manual\" indicator must not be set; for condition types NLXA and NLXV, the indicator must be set.</p> <UL><LI>You can apply a tax rate in Logistics for tax with condition category \"D\". However, the important values are those applied in Financial Accounting. In particular, this can be more than one tax rate. For differing data, the tax data displayed in the document conditions may not match the postings in Financial Accounting.</LI></UL> <UL><LI>Refer to Note 391793 under Release 4.5 and 4.6.<br /></LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-IV-LIV (Logistics Invoice Verification)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027740)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I027824)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "525462", "RefComponent": "LO-AB", "RefTitle": "Subsequent settlement: Vendor billing doc w/acquisition tax", "RefUrl": "/notes/525462"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "391793", "RefComponent": "LO-AB", "RefTitle": "Tax: inconsistent amounts for TxCd E1, N1 ...", "RefUrl": "/notes/391793"}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044"}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006"}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716"}, {"RefNumber": "189359", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recoding of tax codes", "RefUrl": "/notes/189359"}, {"RefNumber": "159298", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Message MN164 with settlemnt", "RefUrl": "/notes/159298"}, {"RefNumber": "1356780", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Corrections to Note 1356238", "RefUrl": "/notes/1356780"}, {"RefNumber": "1356238", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error MN 311 when settling customer agreement", "RefUrl": "/notes/1356238"}, {"RefNumber": "113655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deviating tax calculation in credt memos", "RefUrl": "/notes/113655"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1423752", "RefComponent": "LO-GT-TC", "RefTitle": "Trading Contract Tax code handling", "RefUrl": "/notes/1423752 "}, {"RefNumber": "1356780", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Corrections to Note 1356238", "RefUrl": "/notes/1356780 "}, {"RefNumber": "1356238", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error MN 311 when settling customer agreement", "RefUrl": "/notes/1356238 "}, {"RefNumber": "525462", "RefComponent": "LO-AB", "RefTitle": "Subsequent settlement: Vendor billing doc w/acquisition tax", "RefUrl": "/notes/525462 "}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006 "}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "189359", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recoding of tax codes", "RefUrl": "/notes/189359 "}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044 "}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716 "}, {"RefNumber": "391793", "RefComponent": "LO-AB", "RefTitle": "Tax: inconsistent amounts for TxCd E1, N1 ...", "RefUrl": "/notes/391793 "}, {"RefNumber": "113655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deviating tax calculation in credt memos", "RefUrl": "/notes/113655 "}, {"RefNumber": "159298", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Message MN164 with settlemnt", "RefUrl": "/notes/159298 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}