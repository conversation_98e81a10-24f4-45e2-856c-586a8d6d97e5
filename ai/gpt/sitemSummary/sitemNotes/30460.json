{"Request": {"Number": "30460", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 250, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014371892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000030460?language=E&token=A07FCB5CD910CCE17EA52ABC88CC02C7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000030460", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000030460/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "30460"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.01.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows", "value": "BC-FES-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "30460 - SAP GUI: Compatibility with different SAP System releases"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>SAP GUI and SAP System have different versions.<br />Which SAP System releases can be connected to with&#160;SAP GUI?</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP GUI, Front-end, version, release, compatible, SAP client, SAPGUI, compatibility</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>Note:&#160;</strong>The term \"SAP System release\" is used here to refer to the release of the subcomponent \"SAP_BASIS\" of your SAP System.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>SAP GUI release higher than or the same as the SAP System release</strong></p>\r\n<p>It is possible to connect newer versions of SAP GUI to an older SAP System, and SAP explicitly recommends this, because the SAP GUIs are <strong>downward-compatible. </strong>For example it is possible to connect to an R/3 system of release 3.1I with SAP GUI for Windows 7.70 / and the 32bit version of SAP GUI for Windows 8.00.<br /><br /><strong>Important remark for 64bit versions of SAP GUI for Windows</strong>: As of release 8.00 of SAP GUI for Windows, both a 32bit and a 64bit version are shipped. The 64bit version is not compatible with SAP products using SAP_BASIS versions older than 7.00. An attempt to log on to such a system with a 64bit version will be rejected. If you have such products (which are all long out of support) in your landscape, you need to continue using a 32bit version of SAP GUI for Windows. See also SAP Note <a target=\"_blank\" href=\"/notes/3218166\">3218166</a> for more information on functional differences between the 32bit and 64bit versions of SAP GUI for Windows.<br /><strong><br />SAP GUI release lower than the SAP System release</strong></p>\r\n<p>In former times this status was not supported due to the fact that changes in the interface between Application Server and SAP GUI sometimes introduced a dependency between SAP GUI and Application Server. Nowadays due&#160;to the stabilization of the interfaces between SAP GUI and SAP Application Server it is possible to use SAP GUI for Windows&#160;7.70 / 8.00&#160;to connect against all currently supported SAP Systems (including systems based on SAP BASIS 7.55 and newer versions). This also includes&#160;SAP S/4HANA products like SAP S/4HANA 2022.&#160;<br /><br />However, you need to consider that new SAP GUI functions available in new SAP GUI releases are usually not \"downported\" to earlier releases. Therefore an upgrade of SAP GUI may be desirable due to new features in a newer release. An example for this is the support for the Belize and Quartz themes which is an important feature for new SAP S/4HANA HANA product releases.<br />&#160;<br /><strong>Additional remarks:</strong></p>\r\n<ul>\r\n<li>You can use different versions / releases / patchlevels of SAP GUI to connect to a single SAP System in parallel. Using different SAP GUI versions for connecting to an SAP system does not cause any harm to the system. Please note that only a single version of SAP GUI for Windows can be installed on a client computer (unless you are using virtualization technologies).<br /><br /></li>\r\n<li>SAP only guarantees compatibility of SAP GUI to releases of SAP Software which are still being maintained by SAP.<br />&#160;</li>\r\n<li>SAP GUI for Windows cannot be used to connect to SAP S/4HANA CE&#160;(Cloud Edition) systems.<br />&#160;</li>\r\n<li>See SAP Note <a target=\"_blank\" href=\"/notes/147519\">147519</a> for support lifecycle information on SAP GUI releases.</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-FES-JAV (SAP GUI for JAVA)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031909)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031909)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000030460/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000030460/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961487"}, {"RefNumber": "959236", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/959236"}, {"RefNumber": "947334", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947334"}, {"RefNumber": "92454", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/92454"}, {"RefNumber": "92444", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/92444"}, {"RefNumber": "91651", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/91651"}, {"RefNumber": "91038", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/91038"}, {"RefNumber": "88791", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88791"}, {"RefNumber": "82288", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/82288"}, {"RefNumber": "78804", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/78804"}, {"RefNumber": "78480", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/78480"}, {"RefNumber": "78323", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/78323"}, {"RefNumber": "77704", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/77704"}, {"RefNumber": "76436", "RefComponent": "PP", "RefTitle": "Profile table overflow for calendars in Gantt chart", "RefUrl": "/notes/76436"}, {"RefNumber": "74565", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/74565"}, {"RefNumber": "73403", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/73403"}, {"RefNumber": "71428", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/71428"}, {"RefNumber": "710720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/710720"}, {"RefNumber": "70759", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/70759"}, {"RefNumber": "69773", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/69773"}, {"RefNumber": "68116", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/68116"}, {"RefNumber": "66841", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/66841"}, {"RefNumber": "66644", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/66644"}, {"RefNumber": "66632", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/66632"}, {"RefNumber": "66631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/66631"}, {"RefNumber": "64945", "RefComponent": "PS-ST-PPB", "RefTitle": "Printing planning table not possible...", "RefUrl": "/notes/64945"}, {"RefNumber": "61780", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/61780"}, {"RefNumber": "61277", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/61277"}, {"RefNumber": "511408", "RefComponent": "BC-FES-GRA", "RefTitle": "Use of netgraph and bar chart in Unicode systems", "RefUrl": "/notes/511408"}, {"RefNumber": "510431", "RefComponent": "PS-ST-PPB", "RefTitle": "CJ2B: Positioning on highlighted project objects", "RefUrl": "/notes/510431"}, {"RefNumber": "485812", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/485812"}, {"RefNumber": "48231", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/48231"}, {"RefNumber": "458778", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/458778"}, {"RefNumber": "458123", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/458123"}, {"RefNumber": "449350", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/449350"}, {"RefNumber": "439318", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/439318"}, {"RefNumber": "437915", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/437915"}, {"RefNumber": "436910", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/436910"}, {"RefNumber": "433797", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/433797"}, {"RefNumber": "432098", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/432098"}, {"RefNumber": "425169", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/425169"}, {"RefNumber": "420714", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/420714"}, {"RefNumber": "417101", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/417101"}, {"RefNumber": "416709", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/416709"}, {"RefNumber": "414070", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/414070"}, {"RefNumber": "413530", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/413530"}, {"RefNumber": "412598", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/412598"}, {"RefNumber": "399897", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/399897"}, {"RefNumber": "393977", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/393977"}, {"RefNumber": "393521", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/393521"}, {"RefNumber": "390330", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF Builder/WF Explorer: Hardware/software requiremts", "RefUrl": "/notes/390330"}, {"RefNumber": "388622", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/388622"}, {"RefNumber": "387730", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/387730"}, {"RefNumber": "379106", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/379106"}, {"RefNumber": "376243", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/376243"}, {"RefNumber": "371613", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/371613"}, {"RefNumber": "368520", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/368520"}, {"RefNumber": "367760", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/367760"}, {"RefNumber": "365737", "RefComponent": "SCM-APO-PPS-DST-PRE", "RefTitle": "Incorrect positioning on first/last graphical object", "RefUrl": "/notes/365737"}, {"RefNumber": "364803", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/364803"}, {"RefNumber": "364166", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/364166"}, {"RefNumber": "364147", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/364147"}, {"RefNumber": "362100", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/362100"}, {"RefNumber": "361116", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361116"}, {"RefNumber": "358544", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/358544"}, {"RefNumber": "326582", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/326582"}, {"RefNumber": "3218166", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI for Windows: Functional differences of the 64bit version compared to the 32bit version", "RefUrl": "/notes/3218166"}, {"RefNumber": "316232", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/316232"}, {"RefNumber": "315813", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/315813"}, {"RefNumber": "307952", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/307952"}, {"RefNumber": "22903", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/22903"}, {"RefNumber": "215921", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/215921"}, {"RefNumber": "204643", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/204643"}, {"RefNumber": "203317", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/203317"}, {"RefNumber": "201604", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/201604"}, {"RefNumber": "189390", "RefComponent": "SCM-APO-PPS-DST-PRE", "RefTitle": "Incorrect display of alternative resources", "RefUrl": "/notes/189390"}, {"RefNumber": "187004", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/187004"}, {"RefNumber": "169426", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/169426"}, {"RefNumber": "162002", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162002"}, {"RefNumber": "161993", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI resources (BW): Hardware and software", "RefUrl": "/notes/161993"}, {"RefNumber": "161252", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/161252"}, {"RefNumber": "159369", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/159369"}, {"RefNumber": "158109", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/158109"}, {"RefNumber": "158090", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/158090"}, {"RefNumber": "156323", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156323"}, {"RefNumber": "154156", "RefComponent": "BC-FES-GUI", "RefTitle": "Parallel use of SAP GUIs of different releases", "RefUrl": "/notes/154156"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "142614", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/142614"}, {"RefNumber": "1400273", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1400273"}, {"RefNumber": "139298", "RefComponent": "PS-IS-INT-EXT", "RefTitle": "Download/export MS Access 32 bit and 16 bit", "RefUrl": "/notes/139298"}, {"RefNumber": "139123", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/139123"}, {"RefNumber": "137965", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/137965"}, {"RefNumber": "137615", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/137615"}, {"RefNumber": "132663", "RefComponent": "BC-FES-CTL", "RefTitle": "Search funct.: Short d., crsr pos. and Jap. variant", "RefUrl": "/notes/132663"}, {"RefNumber": "131604", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/131604"}, {"RefNumber": "120841", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/120841"}, {"RefNumber": "108529", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/108529"}, {"RefNumber": "105061", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/105061"}, {"RefNumber": "104230", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104230"}, {"RefNumber": "101934", "RefComponent": "PP-PI-MD-MRC", "RefTitle": "Recipes: relationships not displayed in graphic", "RefUrl": "/notes/101934"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1519484", "RefComponent": "BC", "RefTitle": "How to analyze network disconnections shown in System log (transaction SM21)", "RefUrl": "/notes/1519484 "}, {"RefNumber": "166130", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP front end: Delivery and compatibility", "RefUrl": "/notes/166130 "}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "154156", "RefComponent": "BC-FES-GUI", "RefTitle": "Parallel use of SAP GUIs of different releases", "RefUrl": "/notes/154156 "}, {"RefNumber": "511408", "RefComponent": "BC-FES-GRA", "RefTitle": "Use of netgraph and bar chart in Unicode systems", "RefUrl": "/notes/511408 "}, {"RefNumber": "390330", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF Builder/WF Explorer: Hardware/software requiremts", "RefUrl": "/notes/390330 "}, {"RefNumber": "161993", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI resources (BW): Hardware and software", "RefUrl": "/notes/161993 "}, {"RefNumber": "132663", "RefComponent": "BC-FES-CTL", "RefTitle": "Search funct.: Short d., crsr pos. and Jap. variant", "RefUrl": "/notes/132663 "}, {"RefNumber": "510431", "RefComponent": "PS-ST-PPB", "RefTitle": "CJ2B: Positioning on highlighted project objects", "RefUrl": "/notes/510431 "}, {"RefNumber": "189390", "RefComponent": "SCM-APO-PPS-DST-PRE", "RefTitle": "Incorrect display of alternative resources", "RefUrl": "/notes/189390 "}, {"RefNumber": "365737", "RefComponent": "SCM-APO-PPS-DST-PRE", "RefTitle": "Incorrect positioning on first/last graphical object", "RefUrl": "/notes/365737 "}, {"RefNumber": "139298", "RefComponent": "PS-IS-INT-EXT", "RefTitle": "Download/export MS Access 32 bit and 16 bit", "RefUrl": "/notes/139298 "}, {"RefNumber": "101934", "RefComponent": "PP-PI-MD-MRC", "RefTitle": "Recipes: relationships not displayed in graphic", "RefUrl": "/notes/101934 "}, {"RefNumber": "76436", "RefComponent": "PP", "RefTitle": "Profile table overflow for calendars in Gantt chart", "RefUrl": "/notes/76436 "}, {"RefNumber": "64945", "RefComponent": "PS-ST-PPB", "RefTitle": "Printing planning table not possible...", "RefUrl": "/notes/64945 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}