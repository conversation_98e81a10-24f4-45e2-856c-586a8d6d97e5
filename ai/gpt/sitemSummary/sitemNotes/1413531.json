{"Request": {"Number": "1413531", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 557, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008626532017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001413531?language=E&token=0C093ECAA17C6E651465CFB880DC4F37"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001413531", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001413531/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1413531"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.02.2010"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-JP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Japan"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Japan", "value": "RE-FX-LC-JP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-JP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1413531 - Payment Charge Report- agent comm., address, BP cat, limit"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You run the Payment Charges Report for Japan (RE-FX Localization for Japan) and encountered the following issues:<br /></p> <OL>1. The Payment Charges Report for Japan (RE-FX Localization for Japan) does not show the agent commission data in the payment document list.</OL> <OL>2. Only one payment limit can be set in the report customizing, which is used for payments and agent commissions too, although they are handled separately. This is currently not an error, as currently the law defines both as 150.000 Yen. But this can change.</OL> <OL>3. If the address data is not entered in the bussiness partner master data but in the vendor master data, the program does not find it.</OL> <OL>4. BP category is not always used from the business partner master data. The user has to be supported in this, e.g. with offering a BADI method where he can exchange the standard value with his own value.</OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, Localization, Japan, Payment Charges Report, REXCJPPAYMENTREP, agent commission data, payment document list ALV, payment limit, BP category, address data, BP master, Vendor master</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>After you have installed this note, the report will</p> <OL>1. show the agent commission payments in the payment document list together with the other payments</OL> <OL>2. The database table TIVXCJPCCSET - Company-specific Data will be extended by a new field: COMM_LIMIT, to store a separate agent commission limit. Also the view V_TIVXCJPCCSET is extended by this new field. So you will be able to set this limit in the \"Define Company-Specific Data for Payment Report\" customizing.</OL> <OL>3. the program will search also in the vendor master data for the address if it could be found in the business partner master data.</OL> <OL>4. A new BADI method will be created, which allows you to set the business partner category individually as you wish.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The new method is called SET_BP_CATEGORY and has the following parameters: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ID_BP TYPE BU_PARTNER <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- CD_BP_CAT TYPE BU_TYPE <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- CS_BAL_MESSAGE TYPE BAL_S_MSG. <p><br /><B>IMPORTANT!</B><br /><br />The program is only able to handle one agent per vendor, if a second one is used in one of the contracts for the same vendor, it will be ignored. However its agent commission payment to this agent will be shown in the payment document list.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Install the correction instruction indicated.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}, {"Key": "Processor                                                                                           ", "Value": "I034516"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001413531/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001413531/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001413531/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001413531/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001413531/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001413531/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001413531/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001413531/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001413531/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Real_Estate_Payment_Charges.pdf", "FileSize": "82", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000655182009&iv_version=0005&iv_guid=1EFA2068C2CF7F4999D5ED207CF6E84B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175"}, {"RefNumber": "1546605", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report: Missing obj. address for CTR/CN", "RefUrl": "/notes/1546605"}, {"RefNumber": "1475800", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: Some records not taken into account", "RefUrl": "/notes/1475800"}, {"RefNumber": "1455668", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charge Report running the ALV in background", "RefUrl": "/notes/1455668"}, {"RefNumber": "1451291", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report for Japan - New Grouping", "RefUrl": "/notes/1451291"}, {"RefNumber": "1445195", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Handling Multi-landlord-agent in REXCJPPAYMENTREP", "RefUrl": "/notes/1445195"}, {"RefNumber": "1434419", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report - correction for Note 1413531", "RefUrl": "/notes/1434419"}, {"RefNumber": "1393969", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Contracts w/o agents are not selected", "RefUrl": "/notes/1393969"}, {"RefNumber": "1383363", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Agent commission in the payment charges report", "RefUrl": "/notes/1383363"}, {"RefNumber": "1367636", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: one-time posting not taken into account", "RefUrl": "/notes/1367636"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1546605", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report: Missing obj. address for CTR/CN", "RefUrl": "/notes/1546605 "}, {"RefNumber": "1455668", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charge Report running the ALV in background", "RefUrl": "/notes/1455668 "}, {"RefNumber": "1475800", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: Some records not taken into account", "RefUrl": "/notes/1475800 "}, {"RefNumber": "1451291", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report for Japan - New Grouping", "RefUrl": "/notes/1451291 "}, {"RefNumber": "1445195", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Handling Multi-landlord-agent in REXCJPPAYMENTREP", "RefUrl": "/notes/1445195 "}, {"RefNumber": "1434419", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report - correction for Note 1413531", "RefUrl": "/notes/1434419 "}, {"RefNumber": "1383363", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Agent commission in the payment charges report", "RefUrl": "/notes/1383363 "}, {"RefNumber": "1393969", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Contracts w/o agents are not selected", "RefUrl": "/notes/1393969 "}, {"RefNumber": "1367636", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: one-time posting not taken into account", "RefUrl": "/notes/1367636 "}, {"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD17", "URL": "/supportpackage/SAPKGPAD17"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60207INEAAPPL", "URL": "/supportpackage/SAPK-60207INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60306INEAAPPL", "URL": "/supportpackage/SAPK-60306INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 604", "SupportPackage": "SAPK-60406INEAAPPL", "URL": "/supportpackage/SAPK-60406INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 4, "URL": "/corrins/0001413531/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKGPAD16&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 602&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60206INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60305INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60405INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual pre-implementation step manually and separately in each system <B>before</B> you import the Note to implement.<br/><br/>A ) Create Data element REXCJP_AGENTLIMIT<br/><br/>1. Call the SE11 transaction.<br/>2. Select the Data type radiobutton, and enter REXCJP_AGENTLIMIT<br/>3. Press \"Create\".<br/>4. A dialog box appears, here select data element and press the continue button.<br/>5. The Change data element screen appears. here enter the following:<br/>Short Description:  \"Agent Commission Limit\"<br/>On the Data type tab page:<br/>Predefined Type : \"CURR\"<br/><br/>On the field Label tab page:<br/>Short:   10 \"Comm.Limit\"<br/>Medium:  17 \"Agent Comm. Limit\"<br/>Long:  22 \"Agent Commission Limit\"<br/>Heading:  10 \"Comm.Limit\"<br/><br/>6. Activate the data element<br/><br/>B) Extend the TIVXCJPCCSET database table.<br/><br/>1. Call the SE11 transaction.<br/>2. Select the \"Database Table\" radiobutton and enter TIVXCJPCCSET.<br/>3. Press \"Change\".<br/>4. After the last component (REPCOMM) enter the fieldname COMM_LIMIT, and the data element REXCJP_AGENTLIMIT.<br/>5. Activate the database table.<br/><br/>C) Extend the V_TIVXCJPCCSET view.<br/><br/>1. Call the SE11 transaction.<br/>2. Select the \"View\" radiobutton and enter V_TIVXCJPCCSET.<br/>3. Press \"Change\".<br/>4. Go to the line after the last view field, COMM_LIMIT, and press the \"Table Fields\".<br/>5. A dialog box appears, there select the field COMM_LIMIT, and press ok.<br/>6. Activate the view.<br/>7. In change mode call from the menu Utilities -&gt; Table Maintenance Generator.<br/><br/>8. On the screen that appears press the change button. (F7)<br/>9. A dialog box appears: \"Change generation elements\". Here select the  \"Expert mode\" button, and on the next dialog box \"select all (F6)\", and press OK.<br/>This regenerates the function group and all manual changes are lost.  Therefore the manual entries in the flow logic and in the function group  are missing, so you have to reinsert them. (These manual entries take care of the automatic currency handling.)<br/><br/>10. Double click on the screen number of the overview screen. (press ok for the next dialog).<br/>11. In the flow logic, after the line \"FIELD V_TIVXCJPCCSET-REPCOMM .\" insert the following line:<br/><br/>&nbsp;&nbsp;&nbsp;&nbsp;module fill_currency.<br/><br/>12. Activate the program.<br/><br/>13. After that go to the function group 0TIVXCJPCCSET in transaction  SE80. Double click on its name and in the appearing dialog box select the \"Main program\" button.<br/>14. Enter the following lines at the end of SAPL0TIVXCJPCCSET include:<br/><br/>* Note 1383363<br/>&nbsp;&nbsp;INCLUDE L0TIVXCJPCCSETI01.&nbsp;&nbsp;\" For view of company code dependent data<br/>* Note 1383363<br/><br/>Activate it.<br/>15. Check that in the function group you still have the include file<br/>L0TIVXCJPCCSETI01. (the regeneration should have kept it.).<br/><br/>16. go to the include L0TIVXCJPCCSETTOP and add the following lines at the end of the include:<br/><br/>* Note 1383363<br/>DATA: gs_001 LIKE T001.<br/>* Note 1383363<br/><br/>After this activate the function group.<br/><br/><br/>D) Extend the structure REXCJP_PYMNTREC.<br/><br/>1. Call the SE11 transaction.<br/>2. Select the \"Data Type\"&nbsp;&nbsp;radiobutton and enter REXCJP_PYMNTREC.<br/>3. Press \"Change\".<br/>4. Go to the line after the last component in the structure, and enter  the component name \"PYMNTCATOK\" and for component type enter RECABOOL.<br/>5. Press enter.<br/>6. Activate the structure.<br/><br/>E) Maintain the error messages<br/><br/>1. Call the SE91 transaction<br/>2. Enter the message class name REXCJP. Press display.<br/>3. Select message Nr. 157, and press the \"selected entries\" button.<br/>4. change the message short text to the following:<br/>\"Address could not be found either for BP(&amp;1) or for its vendor (&amp;2)\".<br/>5. Save it.<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 12, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1031686 ", "URL": "/notes/1031686 ", "Title": "Changes after downgrade of the RE-FX Japan", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1036115 ", "URL": "/notes/1036115 ", "Title": "Checkman error correction after downgrade", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1302595 ", "URL": "/notes/1302595 ", "Title": "Costdistribution - Error REXCJP414 during Addit. Posting", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1383363 ", "URL": "/notes/1383363 ", "Title": "Agent commission in the payment charges report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1393969 ", "URL": "/notes/1393969 ", "Title": "Contracts w/o agents are not selected", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1302595 ", "URL": "/notes/1302595 ", "Title": "Costdistribution - Error REXCJP414 during Addit. Posting", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1383363 ", "URL": "/notes/1383363 ", "Title": "Agent commission in the payment charges report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1393969 ", "URL": "/notes/1393969 ", "Title": "Contracts w/o agents are not selected", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1302595 ", "URL": "/notes/1302595 ", "Title": "Costdistribution - Error REXCJP414 during Addit. Posting", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1383363 ", "URL": "/notes/1383363 ", "Title": "Agent commission in the payment charges report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1393969 ", "URL": "/notes/1393969 ", "Title": "Contracts w/o agents are not selected", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1302595 ", "URL": "/notes/1302595 ", "Title": "Costdistribution - Error REXCJP414 during Addit. Posting", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1383363 ", "URL": "/notes/1383363 ", "Title": "Agent commission in the payment charges report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1393969 ", "URL": "/notes/1393969 ", "Title": "Contracts w/o agents are not selected", "Component": "RE-FX-LC-JP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}