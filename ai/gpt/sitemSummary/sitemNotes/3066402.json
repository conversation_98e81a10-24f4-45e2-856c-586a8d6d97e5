{"Request": {"Number": "3066402", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 406, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000899262021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003066402?language=E&token=F16C886DCA09390B42AF55F97FA81D17"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003066402", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003066402/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3066402"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP S/4HANA Data Migration Cockpit Content"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP S/4HANA Data Migration Cockpit Content", "value": "CA-GTF-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3066402 - SAP S/4HANA Migration Cockpit: G/L account balance and open/line item -  Transaction type 0xx not defined"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have SAP S/4HANA <strong>1809 </strong>or<strong> 1909 </strong>or<strong> 2020 or 2021 or 2022 or 2023.</strong></p>\r\n<p>You are trying to load&#160;G/L account balance data into&#160;SAP S/4HANA&#160;using the SAP S/4HANA migration cockpit and the migration object is \"FI - G/L account balance and open/line item\".</p>\r\n<p>You have provided a valid transaction type via migration template and receive while simulation or import the error message \" Transaction type 0xx not defined\" with message number GZ262.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FI - G/L account balance and open/line item,&#160;GZ262, Transaction type, RMVCT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The related migration field rule contains a conversion exit alpha which automatically add a leading zero, e.g. a valid transaction type 10 is changed to non existing value 010.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 274px; width: 420px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>Release</p>\r\n</td>\r\n<td>Service Pack</td>\r\n<td>Solution</td>\r\n</tr>\r\n<tr>\r\n<td>1809</td>\r\n<td>SP00 - SP06</td>\r\n<td>\r\n<p>Implement TCI Note&#160;<a target=\"_blank\" href=\"/notes/3066267\">3066267</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1909</td>\r\n<td>SP00 - SP04</td>\r\n<td>Implement TCI Note&#160;<a target=\"_blank\" href=\"/notes/3066224\">3066224</a></td>\r\n</tr>\r\n<tr>\r\n<td>2020</td>\r\n<td>SP00 - SP02</td>\r\n<td>Implement TCI Note&#160;<a target=\"_blank\" href=\"/notes/3066214\">3066214</a></td>\r\n</tr>\r\n<tr>\r\n<td>2021</td>\r\n<td>SP00 - SP04</td>\r\n<td>Implement TCI Note <a target=\"_blank\" href=\"/notes/3382062\">3382062</a></td>\r\n</tr>\r\n<tr>\r\n<td>2022</td>\r\n<td>SP00 - SP02</td>\r\n<td>Implement TCI Note <a target=\"_blank\" href=\"/notes/3382093\">3382093</a></td>\r\n</tr>\r\n<tr>\r\n<td>2023</td>\r\n<td>SP00</td>\r\n<td>Implement TCI Note <a target=\"_blank\" href=\"/notes/3398244\">3398244</a></td>\r\n</tr>\r\n<tr>\r\n<td>2023</td>\r\n<td>SP01 &amp; Above</td>\r\n<td>Issue will not occur any more!</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D066139)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I075763)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003066402/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066402/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543372", "RefComponent": "BC-UPG-NA", "RefTitle": "How to implement a Transport-based Correction Instruction", "RefUrl": "/notes/2543372"}, {"RefNumber": "3398244", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: November 2023 - Central correction Note for content issues for SAP S/4HANA 2023", "RefUrl": "/notes/3398244"}, {"RefNumber": "3382093", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: September 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3382093"}, {"RefNumber": "3382062", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: September 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3382062"}, {"RefNumber": "3066267", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/3066267"}, {"RefNumber": "3066224", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3066224"}, {"RefNumber": "3066214", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3066214"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3398244", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: November 2023 - Central correction Note for content issues for SAP S/4HANA 2023", "RefUrl": "/notes/3398244 "}, {"RefNumber": "3382093", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: September 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3382093 "}, {"RefNumber": "3382062", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: September 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3382062 "}, {"RefNumber": "3066224", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3066224 "}, {"RefNumber": "3066267", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/3066267 "}, {"RefNumber": "3066214", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3066214 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10307INS4CORE", "URL": "/supportpackage/SAPK-10307INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10405INS4CORE", "URL": "/supportpackage/SAPK-10405INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10503INS4CORE", "URL": "/supportpackage/SAPK-10503INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 106", "SupportPackage": "SAPK-10605INS4CORE", "URL": "/supportpackage/SAPK-10605INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 107", "SupportPackage": "SAPK-10703INS4CORE", "URL": "/supportpackage/SAPK-10703INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 108", "SupportPackage": "SAPK-10801INS4CORE", "URL": "/supportpackage/SAPK-10801INS4CORE"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}