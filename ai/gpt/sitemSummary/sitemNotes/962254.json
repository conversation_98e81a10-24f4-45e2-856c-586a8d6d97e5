{"Request": {"Number": "962254", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 365, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016125572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000962254?language=E&token=710FB24E04DED1CF57ABB345DD2FFD26"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000962254", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000962254/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "962254"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.10.2006"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MW-COM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Exchange CRM Server -  Mobile Client"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "CRM-MW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Exchange CRM Server - Mobile Client", "value": "CRM-MW-COM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MW-COM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "962254 - Error during conntrans receive on mobile client."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /> Con<PERSON><PERSON><PERSON> fails during receive phase.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /> Conntrans, OutOfMemoryException, RFC, and Mobile Client.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><STRONG></STRONG><br /><STRONG><B>Reason :</B></STRONG><br /> Since the size of messages is too big to receive by RFC calls, connt<PERSON><PERSON> throws an outofmemory exception error.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /> Open DotnetQmtClient.log file present in the directory mentioned in registry key [HKEY_LOCAL_MACHINE\\SOFTWARE\\SAP\\MSA\\MW\\TL\\TL_TRACE_DIR]. If the error is related to \"OutOfMemoryException\" then only apply this note.<br /><br /> Follow the steps mentioned below to apply the fix on Mobile Client<br /> machine.</p> <UL><UL><LI>Take the back up of MsgTransClient.dll present in the folder &lt;Mobile Client Installation Path&gt;\\Bin.Net.</LI></UL></UL> <UL><UL><LI>Download the attached Note962254.zip in a temporary folder. Refer to Note 480180 for the procedure of downloading attachments from a Note.</LI></UL></UL> <UL><UL><LI>Extract the contents of the zip file to the folder &lt;Mobile Client Installation Path&gt;\\Bin.Net. The zip file contains the files MsgTransClient.dll, RegMobileClient.bat.and NextPullMsg.reg</LI></UL></UL> <UL><UL><LI>Double click on the file NextPullMsg.reg to create a registry key [HKEY_LOCAL_MACHINE\\SOFTWARE\\SAP\\MSA\\NewTransferService \\Parameters\\NumMsgToPull].</LI></UL></UL> <UL><UL><LI>Search for RegAsm.exe in your machine. Note down the path for this file. The directory where this file resides should be &lt;Windows Installation Path&gt;\\Microsoft.NET\\Framework \\v1.1.4322\\.</LI></UL></UL> <UL><UL><LI>Open RegMobileClient.bat with notepad. Check if the path mentioned for RegAsm.exe is correct. If it is not correct, please correct the path and save the file.</LI></UL></UL> <UL><UL><LI>Double click on the file RegMobileClient.bat to execute it.</LI></UL></UL> <p><br />This fix may affect the performance of conntrans during receive phase. Since the number of messages to be received from CRM server in every call is picked up from registry key entry [HKEY_LOCAL_MACHINE\\SOFTWARE \\SAP\\MSA\\NewTransferService\\Parameters\\NumMsgToPull] now. The default value for this key is set to 100, in such case it will receive 100 messages in every call. If it is set to 0, conntrans will function in usual way.<br /><br /><B>Please verify that the MsgTransClient.dll has the date stamp 06-July-2006 or onwards to confirm that the fix is installed properly.</B><br /><br /><B>This note is valid till CRM 5.0 SP06 and CRM 5.1 only.</B><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I032878)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I032878)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000962254/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962254/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Note962254.zip", "FileSize": "17", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000292542006&iv_version=0003&iv_guid=3A43982EA388404D823E5248DD11EB1D"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BBPCRM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "510", "To": "510", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}