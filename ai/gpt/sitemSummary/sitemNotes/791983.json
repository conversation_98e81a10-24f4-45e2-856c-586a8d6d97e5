{"Request": {"Number": "791983", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 499, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004647442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=6D65BBF5554FF7C956F9380F5653E45A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "791983"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "FS-CML-PO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portfolio"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Consumer and Mortgage Loans", "value": "FS-CML", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CML*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portfolio", "value": "FS-CML-PO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CML-PO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "791983 - STM: CML connection to the Data Retention Tool"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>CMl has no connection to the Data Retention Tool.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DART, Data Retention Tool, tax reduction law, GDPdU, SAP Business Partner, Business Partner, Financial Services, DART 2.3, DART Version 2.3</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a tax reduction law and the GDPdU.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>With Version 2.3, the Data Retention Tool is enhanced, for example, to include data extraction from Loans Management (CML). If you do <U>not</U>&#x00A0;&#x00A0;use these products, this note is not relevant for you.<br />To extract data from Loans Management, you must have implemented this note as well as DART Version 2.3 in accordance with Note 763867. If you also want to extract data for the business partner, you must also implement Note 691546.<br />SAP recommends that you implement this enhancement using the Support Package.<br />Alternatively, you can release the transport files found under 'attachments' for this note for the following releases.<br /><br />&#x00A0;&#x00A0;Release  | File name<br />&#x00A0;&#x00A0;---------------------------------------------------<br />&#x00A0;&#x00A0;Finserv 2.00&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| P2FK024649.zip<br />&#x00A0;&#x00A0;Finserv 1.10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| P1FK026721.zip<br />&#x00A0;&#x00A0;Banking 463_20&#x00A0;&#x00A0;&#x00A0;&#x00A0;| PL8K031832.zip<br />&#x00A0;&#x00A0;4.6C&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| P9CK428986.zip<br /><br />Section 1.7 of Note 480031 describes how you can download attachments.<br /><br />Note the following points when you install the transport files:</p> <OL>1. What are the prerequisites?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You need to have installed DART 2. 2 in your system; in particular, the CFM-specific parts must also be installed for this DART version. If DART 2.2 is not yet installed, implement Notes 668943 and 691546. <OL>2. How are the transport files installed?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The files are installed by the system administration. The relevant transport files are specified above. <OL>3. What should I consider when I install the files?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The files can be installed during production operation. <OL>4. What postprocessing is required after the installation?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For technical reasons and to avoid having to formulate any preconditions for importing the objects, some source code corrections are required for objects that are already delivered. The correction instructions are provided in this note. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use Transaction SE80 to check the transport layer of the package or the development class FVVD_GDPDU. Ensure that SAP is entered here.</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-GTF-DRT (DART (Data Retention Tool))"}, {"Key": "Responsible                                                                                         ", "Value": "C5035442"}, {"Key": "Processor                                                                                           ", "Value": "D021133"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "P1FK026721.zip", "FileSize": "65", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001595252004&iv_version=0005&iv_guid=293EC24476D26B4E82D1E7702E1CE8E8"}, {"FileName": "P2FK024649.zip", "FileSize": "64", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001595252004&iv_version=0005&iv_guid=4B06AEF9FA238E4D86407344DDE20554"}, {"FileName": "P9CK428986.zip", "FileSize": "61", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001595252004&iv_version=0005&iv_guid=3EF77139B365BF469D089AE00DE3930B"}, {"FileName": "PL8K031832.zip", "FileSize": "67", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001595252004&iv_version=0005&iv_guid=5C80A8C875E6BB46892EDB8F5B8D13BD"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "903762", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "DART 2.3: Extraction of CFM/Treasury without CML", "RefUrl": "/notes/903762"}, {"RefNumber": "799349", "RefComponent": "FS-CML-PO", "RefTitle": "STM: Missing authorization check of DART connection CML", "RefUrl": "/notes/799349"}, {"RefNumber": "763867", "RefComponent": "CA-GTF-DRT", "RefTitle": "DART version 2.3 for 31I - 470", "RefUrl": "/notes/763867"}, {"RefNumber": "691546", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "DART version 2.2 - CFM/Treasury-specific enhancements", "RefUrl": "/notes/691546"}, {"RefNumber": "445148", "RefComponent": "CA-GTF-GDP", "RefTitle": "Access by tax authorities to stored data", "RefUrl": "/notes/445148"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "445148", "RefComponent": "CA-GTF-GDP", "RefTitle": "Access by tax authorities to stored data", "RefUrl": "/notes/445148 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "763867", "RefComponent": "CA-GTF-DRT", "RefTitle": "DART version 2.3 for 31I - 470", "RefUrl": "/notes/763867 "}, {"RefNumber": "903762", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "DART 2.3: Extraction of CFM/Treasury without CML", "RefUrl": "/notes/903762 "}, {"RefNumber": "691546", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "DART version 2.2 - CFM/Treasury-specific enhancements", "RefUrl": "/notes/691546 "}, {"RefNumber": "799349", "RefComponent": "FS-CML-PO", "RefTitle": "STM: Missing authorization check of DART connection CML", "RefUrl": "/notes/799349 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "463_20", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 110", "SupportPackage": "SAPKGPFA20", "URL": "/supportpackage/SAPKGPFA20"}, {"SoftwareComponentVersion": "EA-FINSERV 200", "SupportPackage": "SAPKGPFB09", "URL": "/supportpackage/SAPKGPFB09"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ27", "URL": "/supportpackage/SAPKIPBJ27"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C49", "URL": "/supportpackage/SAPKH46C49"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 2, "URL": "/corrins/**********/201"}, {"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 1, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "691546 ", "URL": "/notes/691546 ", "Title": "DART version 2.2 - CFM/Treasury-specific enhancements", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "691546 ", "URL": "/notes/691546 ", "Title": "DART version 2.2 - CFM/Treasury-specific enhancements", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "713032 ", "URL": "/notes/713032 ", "Title": "Error in deduction of security flows in DART 2.2", "Component": "FIN-FSCM-TRM-TM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}