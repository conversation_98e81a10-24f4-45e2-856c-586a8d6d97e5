{"Request": {"Number": "1320335", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1518, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001320335?language=E&token=7826119A27FB7A8FF23A0C5A8EC3EC0B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001320335", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001320335/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1320335"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "03.02.2011"}, "SAPComponentKey": {"_label": "Component", "value": "CA-LT-CNV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Landscape Transformation Conversion"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Landscape Transformation", "value": "CA-LT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Landscape Transformation Conversion", "value": "CA-LT-CNV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT-CNV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1320335 - SLO: Availability of LT Development Support"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol><ol>a) You are an SAP employee (such as Primary Support, IRT coordinator, shift officer, and so on) ) and you notice that a message with VERY HIGH or HIGH priority was submitted to the component CA-LT-CNV. You wonder the following:</ol></ol>\r\n<ul>\r\n<ul>\r\n<li>Which AGS-SLO Development Support (DEV-SUP) tools and regulations are connected to this component?</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Are the Enterprise Support SLAs valid for the component CA-LT-CNV?</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>How can you contact AGS-SLO Development Support?</li>\r\n</ul>\r\n</ul>\r\n<ol><ol>b) You are an SLO employee and want to request 24 hours of AGS-SLO DEV-SUP readiness for a production changeover to ensure that a development expert is available for the entire production changeover period in case of problems.</ol></ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Package, COA, chart of accounts, CWB basis, CNV, customer/vendor, fiscal year, merging clients, technical system consolidation, SAP Organizational Structure Changes, SAP Data Harmonization, chart of accounts changeover, fiscal year changeover, customer/vendor changeover, material number changeover, controlling area changeover, currency key changeover, merging company codes, deleting company codes, separating controlling areas</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br /><strong>Determining factors:</strong><br />The component CA-LT-CNV is responsible for LT problem messages that arise from using the SLO Conversion Workbench (CWB) (package 00001) and the application packages that are to be started there (for example, chart of accounts changeover (package 20100)).<br />Other SLO tools or products (such as the Migration Workbench (MWB), the Test Data Migration Server (TDMS), the Client Migration Server (CMIS), the Data Migration Server (DMIS), EURO, and so on) are supported under other components. If you experience problems with these tools or products, create a message under the relevant component.<br /><br /><strong>Background information:</strong><br />The SLOP/CWB-based SLO tools are <strong>NOT</strong> standard SAP products. Therefore, the Enterprise Support SLAs for \"Initial Reaction Time\" and \"Corrective Actions\" are not valid.<br />The SLAs will&#160;&#160;be valid for the Packages which are available in SAP Landscape Transformation(LT). For the same there is a corresponding note 1463386.<br /><br />Customers sign standalone contracts for SLO projects and these contracts govern the details about the availability of AGS-SLO DEV-SUP.<br />These agreements are summarized in this note or described in the documents linked to this note.<br />LT projects generally consist of several test changeovers in test systems and one production changeover (usually on the weekend) in the production system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>Availability of LT Development Support:</strong><br /><br /><strong>For test changeovers:</strong></p>\r\n<ul>\r\n<li>AGS-SLO DEV-SUP is available between 08:30 AM and 05:30 PM CET.</li>\r\n</ul>\r\n<p><br /><strong>For the production changeover (on the weekend but also during the week):</strong></p>\r\n<ul>\r\n<li>AGS-SLO DEV-SUP is available for 24 hours during the changeover time, <strong>as long as</strong> you notified us of the production changeover in advance.</li>\r\n</ul>\r\n<ul>\r\n<li>If you did not notify us of the changeover, we cannot guarantee that DEV-SUP will be available. The section below describes how you can determine whether we were notified of a changeover.</li>\r\n</ul>\r\n<p><br />If problems occur during the test changeover or the production changeover you must open a message with the appropriate priority. In addition, you can contact DEV-SUP by telephone. The telephone numbers are provided in the document <strong>SLO_developers_on_duty.xls</strong>.<br /><br />AGS-SLO DEV-SUP details and documents are provided in the folder <strong><a target=\"_blank\" href=\"file://dwdf030/slo/slo/Public_Information/Development_Support\">\\\\</a></strong>dwdf030\\slo\\slo\\Public_Information\\Development_Support.<br /><br />Various pieces of information are provided here, such as:</p>\r\n<ul>\r\n<li>The name and the telephone number of the DEV-SUP colleague that is on duty <strong>(SLO_developers_on_duty.xls)</strong></li>\r\n</ul>\r\n<ul>\r\n<li>The production changeovers that we were notified of: <strong>SLO_(CNV)weekend_support.xls</strong></li>\r\n</ul>\r\n<p><br /><strong>Notifying us of a production changeover</strong><br />To notify us of a production changeover, proceed as follows:</p>\r\n<ul>\r\n<li>E-mail the DEV-SUP colleague that is on duty <strong>or</strong> open a message under the component CA-LT-CNV. Do NOT open a VERY HIGH priority message.</li>\r\n</ul>\r\n<ul>\r\n<li>If the production changeover starts during the week and you require <strong>DEV-SUP overnight from Monday to Thursday (05:30 PM to 08:30 AM CET),</strong><span style=\"text-decoration: underline;\">you must notify us at least one week</span> in advance. Otherwise we <strong>CANNOT</strong> guarantee that we can schedule 24-hour Development Support.</li>\r\n</ul>\r\n<ul>\r\n<li>For production changeovers on the weekend, for which you require DEV-SUP from Friday (05:30 PM CET) to Monday (08:30 AM), you must notify us by Thursday at 06:00 PM (CET).</li>\r\n</ul>\r\n<p><br />The following information is required in the e-mail or the message:</p>\r\n<ul>\r\n<li>From-date</li>\r\n</ul>\r\n<ul>\r\n<li>From-time</li>\r\n</ul>\r\n<ul>\r\n<li>To-date</li>\r\n</ul>\r\n<ul>\r\n<li>To-time if possible (estimated end time)</li>\r\n</ul>\r\n<ul>\r\n<li>Customer name</li>\r\n</ul>\r\n<ul>\r\n<li>Customer number</li>\r\n</ul>\r\n<ul>\r\n<li>Installation number</li>\r\n</ul>\r\n<ul>\r\n<li>System name</li>\r\n</ul>\r\n<ul>\r\n<li>Type of changeover (for example, chart of accounts)</li>\r\n</ul>\r\n<ul>\r\n<li>R/3 Release</li>\r\n</ul>\r\n<ul>\r\n<li>Database in use</li>\r\n</ul>\r\n<ul>\r\n<li>Assigned advisor or developer</li>\r\n</ul>\r\n<ul>\r\n<li>Mobile phone number in case of queries</li>\r\n</ul>\r\n<p><br />AGS-SLO DEV-SUP enters notifications received into the document <strong><a target=\"_blank\" href=\"file://dwdf030/slo/slo/Public_Information/Development_Support/SLO(CNV)_weekend_support.xls\">\\\\dwdf030<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-bidi-theme-font: minor-bidi; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">\\slo\\slo\\Public_Information\\Development_Support\\</span>SLO(CNV)_weekend_support.xls</a></strong><br /><br />Note about AGS-SLO Standard Service Delivery:<br />A special regulation applies to AGS-SLO Standard Service Delivery (SSD). The notification of production SSD changeovers is performed centrally by one single person.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I046700)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I058982)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320335/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "769289", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40115, merging clients", "RefUrl": "/notes/769289"}, {"RefNumber": "500290", "RefComponent": "CA-LT-CNV", "RefTitle": "Composite SAP note for CWB Basis package CNV_00001", "RefUrl": "/notes/500290"}, {"RefNumber": "329116", "RefComponent": "CA-LT-CNV", "RefTitle": "Composite SAP Note for conversions as of Release 4.0", "RefUrl": "/notes/329116"}, {"RefNumber": "1840187", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22680 : 1:1 Rename of PRODH", "RefUrl": "/notes/1840187"}, {"RefNumber": "1840186", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22620 : 1:1 Rename of ZLSCH + ZWELS", "RefUrl": "/notes/1840186"}, {"RefNumber": "1840183", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22170:1:1 Rename of OBJEKT-Contain EQUNR,TPLNR", "RefUrl": "/notes/1840183"}, {"RefNumber": "1840152", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22130 : 1:1 Rename of EQUNR", "RefUrl": "/notes/1840152"}, {"RefNumber": "1840149", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20710: Material Merge without plant", "RefUrl": "/notes/1840149"}, {"RefNumber": "1838399", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22242: Delta 4.7 MATKL Conversion", "RefUrl": "/notes/1838399"}, {"RefNumber": "1838398", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22240: MATKL Rename With/Without Ref to MATNR", "RefUrl": "/notes/1838398"}, {"RefNumber": "1838397", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22180:1:1 Rename of EQFNR(contain EQUNR,TPLNR)", "RefUrl": "/notes/1838397"}, {"RefNumber": "1838396", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22060: 1:1 Rename of Functional.Loc No (TPLNR)", "RefUrl": "/notes/1838396"}, {"RefNumber": "1838395", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22670: 1:1 Rename of LFART", "RefUrl": "/notes/1838395"}, {"RefNumber": "1838394", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22590: 1:1 Rename of SPART", "RefUrl": "/notes/1838394"}, {"RefNumber": "1838393", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22490: 1:1 Rename of FKART", "RefUrl": "/notes/1838393"}, {"RefNumber": "1838372", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22370: 1:1 Rename of FEHGR", "RefUrl": "/notes/1838372"}, {"RefNumber": "1838370", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22350: 1:1 Rename of PSTYV", "RefUrl": "/notes/1838370"}, {"RefNumber": "1838368", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22340: 1:1 Rename of ETTYP", "RefUrl": "/notes/1838368"}, {"RefNumber": "1838367", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22160: 1:1 Rename of PS_PROKI", "RefUrl": "/notes/1838367"}, {"RefNumber": "1838366", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22155: 1:1 Rename of PS_PSPID", "RefUrl": "/notes/1838366"}, {"RefNumber": "1838365", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20140: FC_ITEM Conversion Package", "RefUrl": "/notes/1838365"}, {"RefNumber": "1838363", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22600: 1:1 Rename of VTWEG", "RefUrl": "/notes/1838363"}, {"RefNumber": "1838267", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20580: Convert MANDT in Change Docs and Texts", "RefUrl": "/notes/1838267"}, {"RefNumber": "1838266", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 30000: 1:1 Rename of Plants (WERKS)", "RefUrl": "/notes/1838266"}, {"RefNumber": "1838265", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 30200: 1:1 renaming EKORG", "RefUrl": "/notes/1838265"}, {"RefNumber": "1838264", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22470: 1:1 Rename of  AUART", "RefUrl": "/notes/1838264"}, {"RefNumber": "1838263", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22400: 1:1 Rename of BWART", "RefUrl": "/notes/1838263"}, {"RefNumber": "1838181", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22570: 1:1 Rename of ZAHLS", "RefUrl": "/notes/1838181"}, {"RefNumber": "1838180", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22230: 1:1 Rename of LAND1 (Country Key)", "RefUrl": "/notes/1838180"}, {"RefNumber": "1834830", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 25130: Value Category Conversion(Rename/Merge)", "RefUrl": "/notes/1834830"}, {"RefNumber": "1834829", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20246 : FM Area Rename", "RefUrl": "/notes/1834829"}, {"RefNumber": "1834828", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20245 : Funds Center Rename", "RefUrl": "/notes/1834828"}, {"RefNumber": "1834826", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20243 : Commitment Items Rename", "RefUrl": "/notes/1834826"}, {"RefNumber": "1834824", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20480 : Controlling Area Split", "RefUrl": "/notes/1834824"}, {"RefNumber": "1834823", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20450 : Set Co.Area Indicator \"All Currencies\"", "RefUrl": "/notes/1834823"}, {"RefNumber": "1834772", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP : Package 30100: <PERSON><PERSON><PERSON>cy Key Rename 1:1", "RefUrl": "/notes/1834772"}, {"RefNumber": "1834771", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21270 : <PERSON><PERSON>l Currency Deletion", "RefUrl": "/notes/1834771"}, {"RefNumber": "1834770", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21269 : <PERSON><PERSON><PERSON> Currency Copy", "RefUrl": "/notes/1834770"}, {"RefNumber": "1834766", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21268 : SL Currency Conversion", "RefUrl": "/notes/1834766"}, {"RefNumber": "1834765", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21267 : FI Currency Conversion in NewG/L", "RefUrl": "/notes/1834765"}, {"RefNumber": "1834763", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21266 : Material Ledger Currency Conversion", "RefUrl": "/notes/1834763"}, {"RefNumber": "1834752", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21265 : Object Currency Conversion", "RefUrl": "/notes/1834752"}, {"RefNumber": "1834750", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21264 : Operating Concern Currency Conversion", "RefUrl": "/notes/1834750"}, {"RefNumber": "1834749", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21262 : PCA Currency Conversion", "RefUrl": "/notes/1834749"}, {"RefNumber": "1834747", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21260 : FI <PERSON><PERSON><PERSON><PERSON>", "RefUrl": "/notes/1834747"}, {"RefNumber": "1834746", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21263 : Co.Area Currency Conversion", "RefUrl": "/notes/1834746"}, {"RefNumber": "1834702", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21261 : FI Currency Conversion in Classic G/L", "RefUrl": "/notes/1834702"}, {"RefNumber": "1412979", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10030, determining organizational unit for CWB", "RefUrl": "/notes/1412979"}, {"RefNumber": "1412978", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10021, table entries and spec. routines 10020", "RefUrl": "/notes/1412978"}, {"RefNumber": "1412977", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10020, determining organizational units", "RefUrl": "/notes/1412977"}, {"RefNumber": "1412974", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 02400, merge simulation", "RefUrl": "/notes/1412974"}, {"RefNumber": "1412973", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 00004, confirm package module settings", "RefUrl": "/notes/1412973"}, {"RefNumber": "1411875", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10200, converting number ranges", "RefUrl": "/notes/1411875"}, {"RefNumber": "1410134", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40190, deleting clients", "RefUrl": "/notes/1410134"}, {"RefNumber": "1410070", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40104, client move (Mandt/OCLNT)", "RefUrl": "/notes/1410070"}, {"RefNumber": "1405157", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 30600, merging or renaming business area", "RefUrl": "/notes/1405157"}, {"RefNumber": "1405155", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40152, renaming ledger number", "RefUrl": "/notes/1405155"}, {"RefNumber": "1405153", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40151, renaming original client", "RefUrl": "/notes/1405153"}, {"RefNumber": "1405080", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40150, renaming document types", "RefUrl": "/notes/1405080"}, {"RefNumber": "1405079", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40107, renaming logical system", "RefUrl": "/notes/1405079"}, {"RefNumber": "1404973", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40102, converting client field", "RefUrl": "/notes/1404973"}, {"RefNumber": "1403311", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 00200, ENICMA - CWB Extension for Migration", "RefUrl": "/notes/1403311"}, {"RefNumber": "1402841", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10850, open transport requests", "RefUrl": "/notes/1402841"}, {"RefNumber": "1375859", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package <CNVPACK-PACKID>, <CNVPACKT-DESCRIPT>", "RefUrl": "/notes/1375859"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1962693", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20390, Renaming of CO order numbers (internal orders)", "RefUrl": "/notes/1962693 "}, {"RefNumber": "1961532", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22550, 1:1 Rename of PARVW", "RefUrl": "/notes/1961532 "}, {"RefNumber": "1838372", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22370: 1:1 Rename of FEHGR", "RefUrl": "/notes/1838372 "}, {"RefNumber": "1838394", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22590: 1:1 Rename of SPART", "RefUrl": "/notes/1838394 "}, {"RefNumber": "1838395", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22670: 1:1 Rename of LFART", "RefUrl": "/notes/1838395 "}, {"RefNumber": "1838368", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22340: 1:1 Rename of ETTYP", "RefUrl": "/notes/1838368 "}, {"RefNumber": "1838367", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22160: 1:1 Rename of PS_PROKI", "RefUrl": "/notes/1838367 "}, {"RefNumber": "1838365", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20140: FC_ITEM Conversion Package", "RefUrl": "/notes/1838365 "}, {"RefNumber": "1838366", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22155: 1:1 Rename of PS_PSPID", "RefUrl": "/notes/1838366 "}, {"RefNumber": "1838397", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22180:1:1 Rename of EQFNR(contain EQUNR,TPLNR)", "RefUrl": "/notes/1838397 "}, {"RefNumber": "1838396", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22060: 1:1 Rename of Functional.Loc No (TPLNR)", "RefUrl": "/notes/1838396 "}, {"RefNumber": "1834750", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21264 : Operating Concern Currency Conversion", "RefUrl": "/notes/1834750 "}, {"RefNumber": "1834766", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21268 : SL Currency Conversion", "RefUrl": "/notes/1834766 "}, {"RefNumber": "1838398", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22240: MATKL Rename With/Without Ref to MATNR", "RefUrl": "/notes/1838398 "}, {"RefNumber": "1838399", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22242: Delta 4.7 MATKL Conversion", "RefUrl": "/notes/1838399 "}, {"RefNumber": "1838180", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22230: 1:1 Rename of LAND1 (Country Key)", "RefUrl": "/notes/1838180 "}, {"RefNumber": "1838181", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22570: 1:1 Rename of ZAHLS", "RefUrl": "/notes/1838181 "}, {"RefNumber": "1840149", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20710: Material Merge without plant", "RefUrl": "/notes/1840149 "}, {"RefNumber": "1834746", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21263 : Co.Area Currency Conversion", "RefUrl": "/notes/1834746 "}, {"RefNumber": "1834702", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21261 : FI Currency Conversion in Classic G/L", "RefUrl": "/notes/1834702 "}, {"RefNumber": "1834749", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21262 : PCA Currency Conversion", "RefUrl": "/notes/1834749 "}, {"RefNumber": "1834752", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21265 : Object Currency Conversion", "RefUrl": "/notes/1834752 "}, {"RefNumber": "1834763", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21266 : Material Ledger Currency Conversion", "RefUrl": "/notes/1834763 "}, {"RefNumber": "1834772", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP : Package 30100: <PERSON><PERSON><PERSON>cy Key Rename 1:1", "RefUrl": "/notes/1834772 "}, {"RefNumber": "1834770", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21269 : <PERSON><PERSON><PERSON> Currency Copy", "RefUrl": "/notes/1834770 "}, {"RefNumber": "1834765", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21267 : FI Currency Conversion in NewG/L", "RefUrl": "/notes/1834765 "}, {"RefNumber": "1840152", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22130 : 1:1 Rename of EQUNR", "RefUrl": "/notes/1840152 "}, {"RefNumber": "1840187", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22680 : 1:1 Rename of PRODH", "RefUrl": "/notes/1840187 "}, {"RefNumber": "1840186", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22620 : 1:1 Rename of ZLSCH + ZWELS", "RefUrl": "/notes/1840186 "}, {"RefNumber": "1840183", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22170:1:1 Rename of OBJEKT-Contain EQUNR,TPLNR", "RefUrl": "/notes/1840183 "}, {"RefNumber": "1838393", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22490: 1:1 Rename of FKART", "RefUrl": "/notes/1838393 "}, {"RefNumber": "1838370", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22350: 1:1 Rename of PSTYV", "RefUrl": "/notes/1838370 "}, {"RefNumber": "1838363", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22600: 1:1 Rename of VTWEG", "RefUrl": "/notes/1838363 "}, {"RefNumber": "1838267", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20580: Convert MANDT in Change Docs and Texts", "RefUrl": "/notes/1838267 "}, {"RefNumber": "1838266", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 30000: 1:1 Rename of Plants (WERKS)", "RefUrl": "/notes/1838266 "}, {"RefNumber": "1838265", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 30200: 1:1 renaming EKORG", "RefUrl": "/notes/1838265 "}, {"RefNumber": "1838264", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22470: 1:1 Rename of  AUART", "RefUrl": "/notes/1838264 "}, {"RefNumber": "1838263", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 22400: 1:1 Rename of BWART", "RefUrl": "/notes/1838263 "}, {"RefNumber": "1834830", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 25130: Value Category Conversion(Rename/Merge)", "RefUrl": "/notes/1834830 "}, {"RefNumber": "1834829", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20246 : FM Area Rename", "RefUrl": "/notes/1834829 "}, {"RefNumber": "1834828", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20245 : Funds Center Rename", "RefUrl": "/notes/1834828 "}, {"RefNumber": "1834826", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20243 : Commitment Items Rename", "RefUrl": "/notes/1834826 "}, {"RefNumber": "1834824", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20480 : Controlling Area Split", "RefUrl": "/notes/1834824 "}, {"RefNumber": "1834823", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 20450 : Set Co.Area Indicator \"All Currencies\"", "RefUrl": "/notes/1834823 "}, {"RefNumber": "1834771", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21270 : <PERSON><PERSON>l Currency Deletion", "RefUrl": "/notes/1834771 "}, {"RefNumber": "1834747", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 21260 : FI <PERSON><PERSON><PERSON><PERSON>", "RefUrl": "/notes/1834747 "}, {"RefNumber": "1405079", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40107, renaming logical system", "RefUrl": "/notes/1405079 "}, {"RefNumber": "1410134", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40190, deleting clients", "RefUrl": "/notes/1410134 "}, {"RefNumber": "1410070", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40104, client move (Mandt/OCLNT)", "RefUrl": "/notes/1410070 "}, {"RefNumber": "1405157", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 30600, merging or renaming business area", "RefUrl": "/notes/1405157 "}, {"RefNumber": "1405155", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40152, renaming ledger number", "RefUrl": "/notes/1405155 "}, {"RefNumber": "1405153", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40151, renaming original client", "RefUrl": "/notes/1405153 "}, {"RefNumber": "1375859", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package <CNVPACK-PACKID>, <CNVPACKT-DESCRIPT>", "RefUrl": "/notes/1375859 "}, {"RefNumber": "1412974", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 02400, merge simulation", "RefUrl": "/notes/1412974 "}, {"RefNumber": "1412973", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 00004, confirm package module settings", "RefUrl": "/notes/1412973 "}, {"RefNumber": "329116", "RefComponent": "CA-LT-CNV", "RefTitle": "Composite SAP Note for conversions as of Release 4.0", "RefUrl": "/notes/329116 "}, {"RefNumber": "500290", "RefComponent": "CA-LT-CNV", "RefTitle": "Composite SAP note for CWB Basis package CNV_00001", "RefUrl": "/notes/500290 "}, {"RefNumber": "1411875", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10200, converting number ranges", "RefUrl": "/notes/1411875 "}, {"RefNumber": "1405080", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40150, renaming document types", "RefUrl": "/notes/1405080 "}, {"RefNumber": "1404973", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40102, converting client field", "RefUrl": "/notes/1404973 "}, {"RefNumber": "769289", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 40115, merging clients", "RefUrl": "/notes/769289 "}, {"RefNumber": "1412979", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10030, determining organizational unit for CWB", "RefUrl": "/notes/1412979 "}, {"RefNumber": "1412978", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10021, table entries and spec. routines 10020", "RefUrl": "/notes/1412978 "}, {"RefNumber": "1412977", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10020, determining organizational units", "RefUrl": "/notes/1412977 "}, {"RefNumber": "1402841", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 10850, open transport requests", "RefUrl": "/notes/1402841 "}, {"RefNumber": "1403311", "RefComponent": "CA-LT-CNV", "RefTitle": "SLOP: Package 00200, ENICMA - CWB Extension for Migration", "RefUrl": "/notes/1403311 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}