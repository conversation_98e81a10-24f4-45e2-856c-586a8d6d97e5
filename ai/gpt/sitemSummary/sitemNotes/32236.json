{"Request": {"Number": "32236", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 472, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014379912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000032236?language=E&token=EE5D4177E82028D1479136EEEDBEE214"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000032236", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000032236/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "32236"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 125}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.10.2011"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Inventory Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Management", "value": "MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "32236 - Incorrect stock qty or stock value in material master"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The system issues one of the following error messages when you enter a goods movement:</p> <UL><LI>M7 308 'Stock value and qty are unrealistic: &amp; / &amp;</LI></UL> <UL><LI>M7 309 'Stock value is negative: &amp;</LI></UL> <UL><LI>M7 310 'Valuated stock is negative: &amp;</LI></UL> <UL><LI>M7 314 Valuated stock becomes negative: &amp;<br /></LI></UL> <p>The stock tables are not consistent.<br /><br />There were differences between the material valuation and FI. Report RM07MMFI for the 'MM/FI balance comparison' shows the differences.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MMINKON, data inconsistency, inconsistency<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>These messages are generated if the system finds a data inconsistency when reading material master data, that is, the stock quantity or the stock value is already inconsistent before the goods movement.<br /><br />You have inconsistencies in your tables, for example, if you compare the stock quantity in the stock overview (taking into account all valuated stocks) with the stock in the accounting view of the material master.<br /><br />These data inconsistencies must be analyzed by our support team. There are a number of different reasons for these inconsistencies, such as:</p> <UL><LI>Program errors in the SAP standard programs</LI></UL> <UL><LI>Program errors in the user exits or modifications</LI></UL> <UL><LI>Error or malfunctions in the database program</LI></UL> <UL><LI>Error when starting programs, for example:</LI></UL> <UL><UL><LI>The period closing program is started in parallel in a productive system if you have made the Customizing setting \"Late Lock\".</LI></UL></UL> <UL><UL><LI>The periods are initialized.</LI></UL></UL> <UL><UL><LI>Customer-defined programs execute database updates without a lock mechanism in parallel with the postings.<br /></LI></UL></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The stock postings (material documents and accounting documents) for the material and data in the material masters must be checked by SAP support. You need to import several check reports for this purpose.</p> <UL><LI>Implement the inconsistency tools.<br /></LI></UL> <p>Transaction SNOTE cannot be used.<br /><br /><B>**********************************************************************</B><br /><B>See FAQ Note 1000077 if problems or errors occur during the note implementation.</B><br /><B>**********************************************************************</B><br /><br />In Release 4.70 and higher, Note 921161 significantly improves the performance of report RM07MMFI.<br /><br />The implementation of the inconsistency tools depends on your current system release:<br /></p> <UL><UL><LI>For Release 3.x:<br /><br />The files for the import are:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ALRK299731 under general/R3server/abap/note.0032236.<br /><br />For this purpose, also see Note 13719.<br /></p> <UL><UL><LI>For Release 4.0B to Release 6.04:<br /></LI></UL></UL> <p>Check whether your system contains the latest version of the inconsistency tools (Version 00021). To do this, start the MBFIRST report for a plant.</p> <UL><LI>If this report is missing, implement the inconsistency tools.</LI></UL> <UL><LI>Your system contains the report and displays the current version in the line \"Reports from Note 32236 Version:\").&#x00A0;&#x00A0; If the version is lower than 18, implement the inconsistency tools. For releases lower than 4.7, Version 17, 18 or 19 is sufficient.</LI></UL> <UL><LI>Your system contains the report and displays the current version (\"Reports from Note 32236 Version: 00021\"). A further import is not required and you can start the analysis.</LI></UL> <p><br />******************************* CAUTION ***************************<br />The inconsistency tools are delivered in standard systems as of Release 6.00. This means that you do not have to implement the inconsistency tools if you are using Release 6.00 or higher and one of the following Support Package levels. An exception is when SAP support have requested you to implement the tools in addition.<br /><br />The standard delivery contains the inconsistency tools as of the following Support Packages:<br /><br />Software Comp.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Rel. Support Package<br />SAP_APPL &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;600 SAPKH60014<br />SAP_APPL &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;602 SAPKH60204<br />SAP_APPL &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;603 SAPKH60303<br />SAP_APPL &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;604 SAPKH60401<br /><br />If you want to check whether your system contains the inconsistency tools, run the report MBFIRSTS for a plant.<br />See Note 1227770 for further details about the standard delivery of the inconsistency tools.<br />******************************* CAUTION ***************************<br /></p> <UL><LI></LI></UL> <p>The attachments section of this note (Note 32236) contains the zip file<br />\"Transport_Files_note_32236_V21.ZIP\".<br /><br />Copy this file from the attachment, unpack and import it into your system.<br /><br />This import file contains:<br />Data elements:<br />BWTAR_D, CHARG_D, FELD_30, F_SPLIT, LFDNR_MM, LFYEAR, LGORT_D, MMINKON_K, NEW_VALUE, NEW_VAL_C, OLD_VALUE, OLD_VAL_C, PROG_KEY, TABN_30, VERS_MM, XMBEW_FI, XMBEW_MM, XMSEG_MM, - MMIM_MESSAGE_NUMBER, MMIM_MESSAGE_YEAR<br /><br />Package:<br />MB-INCON<br /><br />Tables<br />MMINKON, MMINKON_UP<br /><br />Transaction<br />MBMENU<br /><br />Reports:<br />MBANEKBE, MBCKBSEG, MBCKBSIM, MBCKMACI, MBCKMD01, MBCKMD02, MBCKMDFI, MBCKMDLI, MBCKMDOC, MBCKMDTY, MBCKMI1,&#x00A0;&#x00A0;MBCKMQCI, MBCKMSSA, MBCKMSSQ, MBENQMAT, MBFIRST,&#x00A0;&#x00A0;MBHIST,&#x00A0;&#x00A0; MBINKON,&#x00A0;&#x00A0;MBINKON2, MBLABST,&#x00A0;&#x00A0;MBMENU, MBMISSFI, MBMSSACO, MBMSSAPO, MBMSSATY, MBMSSI01, MBMSSQCO, MBMSSQCR, MBMSSQTY, MBMSSQUA, MBMSSVAL, MSPSTCRR, MBQUANT,&#x00A0;&#x00A0;MBSEGBSX, MBSHOWUP, MBSTOCK,&#x00A0;&#x00A0;MBTRAME,&#x00A0;&#x00A0;MBTRAMEW, MBUPDATE, MB_MRMI,&#x00A0;&#x00A0;RM07APP1, RM07AUTH, RM07COMP, RM07HIST, RM07MB51, RM07MR51, RM07REUS, MBVALUE,&#x00A0;&#x00A0;MBCKT030, RM07SHUP, MBMSSSTO.<br /><br />If the MMINKON and MMINKON_UP tables are not active after you have imported this transport, ensure that the data elements listed above are active. If not, activate them manually.<br /><br />If you still have reports from an older version of the transport in your system (before May 2000) and if these reports are in the customer namespace, see Note 304670 and delete these reports.<br /><br />As of version 18, the inconsistency report was assigned to the new package \"MB_INCON\". This package contains the \"HOME\" software component. This software component allows the assigned objects and reports to be transported within your system landscape.<br /><br />In Release 6.03, the reports of Version 21 were revised. The transport files were also created in this system. Since the import will probably occur in a system with a release lower than Release 6.03, see Note 454321 and its related notes.<br /></p> <UL><LI>Analyzing the inconsistencies, and necessary preparations<br /></LI></UL> <p>Since analyzing and correcting inconsistencies can sometimes be very time-consuming, SAP carries this out exclusively in the PRODUCTION SYSTEM.<br /><br />To be able to process and analyze the data inconsistencies promptly, our support team requires a user master record with sufficient authorization. The reports listed above can usually only be started by users that have debugging authorization (object 'S_DEVELOP', object type 'DEBUG' and activity '03'). In addition, Debug&amp;Replace authorization is required as well as authorization for all transactions in package MB and SM*/SE* system transactions.<br /><br /><B>In general, you should not execute the reports contained in this note yourself because the results can often be misleading. The results of these reports generally need to be interpreted.</B><br /><br />If you have already archived material documents or accounting documents in your system or if you use the aggregation of posting lines for the stock accounts, report this to the hotline since some check reports are based on the documents and are no longer executable or they return incorrect results after an archiving or an aggregation of the posting lines, (for more information about this, see the questionnaire in Note 76926).<br /><br />If your system contains many material masters, for example, the MBEW material valuation table contains more than 500,000 entries, further requirements must be met before the inconsistency analysis:<br />- Check the \"Size Category\" setting of the MMINKON database table. This table should have the same size category as the MBEW database table.<br />- Inform our support team about the maximum number of background jobs that can be executed simultaneously without causing problems in your system.<br /><br />Some check reports select MM documents from the MKPF and MSEG database tables. For better performance, we provide some SELECT commands with HINTs for the database.<br /><br />SELECT commands for the Oracle database work with histograms. If your system works with an Oracle database, see Note 902675. This note describes the structure of histograms.<br /></p> <UL><LI>Implement the attached corrections.<br /></LI></UL> <p>Some of the check reports are also able to make hard updates on the database, provided the update parameters have been set. Only a few members of our support team have the code required for this function. Updates are only executed by the SAP Support Department since SAP cannot guarantee the data consistency in the event of an incorrect application.<br /><br />These hard updates in database tables are logged by the correction reports in the MMINKON_UP table. As of Version 19, Report RM07SHUP (MBSHOWUP for versions lower than 19) can display these changes.<br /><br />If the data inconsistencies were caused by SAP software, only SAP development support is authorized to change the data. SAP development support will also attempt to create a note for correcting the errors in the SAP standard software that cause these inconsistencies to avoid future errors.<br /><br />If the inconsistencies are caused by customer-defined programs, modifications, or errors in user exits, SAP Remote Consulting can fix the error (this processing is subject to charges). If you try to correct such inconsistencies yourself using the data inconsistency programs listed in this note, note that you are responsible for any problems that might occur.<br /><br />Follow-up problems caused by this approach cannot be processed by SAP Support but only by SAP Remote Consulting.<br /><br />For more information about the corrections which might have been implemented, see Note 34440.<br /></p> <UL><LI>What can you do if there are goods movements that are required urgently?<br /></LI></UL> <p>Usually, we recommend that you do not enter any more movements in inconsistent material masters. However, if this cannot be avoided, then the error message can be issued as a warning message. For this, you have to make an entry for the relevant error message in Table T160M. If you make an entry in Table T160M and then carry out the \"absolutely necessary\" material movement, you have to delete the entry in Table T160M again.<br /><br />Example:<br />Version AG Msg Cat Message text00 M7 308 Stock value and qty are unrealistic: &lt;(&gt;&amp;&lt;)&gt; / &lt;(&gt;&amp;&lt;)&gt;<br />Bear in mind that the \"Cat\" column must remain empty. The version is 00 in the standard system, but you can use the ID MSV parameter to define it according to your requirements.<br /><br />As a first measure, it is often sufficient to increase the stock of the inconsistent material master with an inventory adjustment posting so that the urgent goods movement can be posted. After the inconsistencies have been corrected, you can undo the stock increase with a further inventory adjustment posting.<br /></p> <UL><LI>Further analysis and correction reports<br /></LI></UL> <p>In the past, there used to be further reports for analyzing and correcting specific data inconsistencies. We provided these in the form of notes. If, when processing your inconsistencies, we noticed that one of these special reports was required, a lot of time was lost before the required report was available in your system.<br /><br />To speed up the processing of inconsistencies, we have included some of these special reports in the transport of the inconsistency tools (Version 18). Therefore, no further implementations are required.<br /><br /><br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Note</TH><TH ALIGN=LEFT> Report&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Old report name</TH></TR> <TR><TD>143132 </TD><TD> MBCKBSIM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ZMKORR60 ( internal note )</TD></TR> <TR><TD>590609 </TD><TD> MBCKMDOC</TD></TR> <TR><TD>590612 </TD><TD> MBCKMSSQ</TD></TR> <TR><TD>619135 </TD><TD> MBCKMSSA</TD></TR> <TR><TD>638510 </TD><TD> MBMSSAPO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Z_619135_POST ( internal note )</TD></TR> <TR><TD>661856 </TD><TD> MBMSSACO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Z_MSSA_CHANGE_KZBWS</TD></TR> <TR><TD>661856 </TD><TD> MBMSSQCO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Z_MSSQ_CHANGE_KZBWS</TD></TR> <TR><TD>661856 </TD><TD> MBMSSQCR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Z_MSSQ_CHANGE_KZBWS_REVERSE</TD></TR> <TR><TD>782522 </TD><TD> MBMSSQUA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ZMME_MBMASS_MBQUANT</TD></TR> <TR><TD>782522 </TD><TD> MBMSSVAL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ZMME_MBMASS_MBVALUE</TD></TR> <TR><TD></TD></TR> </TABLE></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-IM-GF-INC (Stock Inconsistencies)"}, {"Key": "Transaction codes", "Value": "SE11"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D040334)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D040334)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000032236/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000032236/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000032236/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000032236/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000032236/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000032236/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000032236/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000032236/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000032236/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Transport_Files_note_32236_V21.zip", "FileSize": "970", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700007212082001&iv_version=0125&iv_guid=F4CEAB1D0B99B3409350AC385C78584B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "989970", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/989970"}, {"RefNumber": "98942", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Multipe material documents in one LUW", "RefUrl": "/notes/98942"}, {"RefNumber": "968812", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MM/FI Difference for material stocks", "RefUrl": "/notes/968812"}, {"RefNumber": "967610", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP ROIH1L04 ADDITIONAL PARAMETER TO REDUCE RUNTIME", "RefUrl": "/notes/967610"}, {"RefNumber": "945346", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MMINKON stock inconsistency tool: Enhancements to standard", "RefUrl": "/notes/945346"}, {"RefNumber": "921161", "RefComponent": "MM-IM-GF-REP", "RefTitle": "RM07MMFI: Improvement of runtime and function", "RefUrl": "/notes/921161"}, {"RefNumber": "834147", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MBEWH entries: Material with split valuation", "RefUrl": "/notes/834147"}, {"RefNumber": "827988", "RefComponent": "MM-IM-GF-INC", "RefTitle": "RMFIFO00: Error in material with split valuation", "RefUrl": "/notes/827988"}, {"RefNumber": "79083", "RefComponent": "MM-IS-IC", "RefTitle": "Setup of statistical data in BCO: Additional information", "RefUrl": "/notes/79083"}, {"RefNumber": "782522", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/782522"}, {"RefNumber": "76926", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Checklist for stock inconsistencies", "RefUrl": "/notes/76926"}, {"RefNumber": "697809", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/697809"}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261"}, {"RefNumber": "661856", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Correction of valuation indicator KZBWS with stock 0", "RefUrl": "/notes/661856"}, {"RefNumber": "655052", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/655052"}, {"RefNumber": "65435", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/65435"}, {"RefNumber": "638510", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/638510"}, {"RefNumber": "626859", "RefComponent": "PP-SFC-EXE-GM", "RefTitle": "COGI: No FI documents created for special valuated materials", "RefUrl": "/notes/626859"}, {"RefNumber": "619135", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Analysis and correction of sales order stock", "RefUrl": "/notes/619135"}, {"RefNumber": "60928", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 3.0 or 3.1 and Release 4.0", "RefUrl": "/notes/60928"}, {"RefNumber": "590612", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Note 560226: Analysis and correction of incorrect data", "RefUrl": "/notes/590612"}, {"RefNumber": "590609", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Check of material document using report MBCKMDOC", "RefUrl": "/notes/590609"}, {"RefNumber": "579241", "RefComponent": "MM-IM-GR", "RefTitle": "M7302: Field overflow in routine WERT_SIMULIEREN", "RefUrl": "/notes/579241"}, {"RefNumber": "560226", "RefComponent": "MM-IM-GF-SPV", "RefTitle": "Project stock not valuated although project valuated", "RefUrl": "/notes/560226"}, {"RefNumber": "558119", "RefComponent": "MM-IM-GF-INC", "RefTitle": "RM07AUTH: Error message no. S4104", "RefUrl": "/notes/558119"}, {"RefNumber": "547965", "RefComponent": "MM-IM-GR-PO", "RefTitle": "Material number differs in material document +purchase order", "RefUrl": "/notes/547965"}, {"RefNumber": "542798", "RefComponent": "MM-IV-CA", "RefTitle": "MR11: Reports for correcting the purchase order history", "RefUrl": "/notes/542798"}, {"RefNumber": "520010", "RefComponent": "MM-IM-GF-INC", "RefTitle": "FAQ: Inconsistencies in inventory management", "RefUrl": "/notes/520010"}, {"RefNumber": "454321", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Release 6.* and 7.0", "RefUrl": "/notes/454321"}, {"RefNumber": "419714", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/419714"}, {"RefNumber": "407315", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "Stock inconsistency through cancellation or parking", "RefUrl": "/notes/407315"}, {"RefNumber": "378731", "RefComponent": "IS-OIL-DS", "RefTitle": "Overview of consistency check procedures in IS-OIL systems", "RefUrl": "/notes/378731"}, {"RefNumber": "373376", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO: <PERSON>rror F5559 when posting to material", "RefUrl": "/notes/373376"}, {"RefNumber": "368185", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO:Incnsistncy posting matl items to prior period", "RefUrl": "/notes/368185"}, {"RefNumber": "357468", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Changing valuation type via goods movement", "RefUrl": "/notes/357468"}, {"RefNumber": "354856", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/354856"}, {"RefNumber": "34440", "RefComponent": "MM-IM", "RefTitle": "Procedure for correcting the material master", "RefUrl": "/notes/34440"}, {"RefNumber": "313803", "RefComponent": "MM-IM-GF-MISC", "RefTitle": "xxx RM07APP1: Routine GET_RELEASE does not exist", "RefUrl": "/notes/313803"}, {"RefNumber": "304670", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Renaming consistency analysis tools of the stock", "RefUrl": "/notes/304670"}, {"RefNumber": "212707", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-Oil systems", "RefUrl": "/notes/212707"}, {"RefNumber": "204393", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Inconsistencies between stocks of MARD and MBEW", "RefUrl": "/notes/204393"}, {"RefNumber": "198596", "RefComponent": "MM-IM", "RefTitle": "Fast MM-FI balance comparison (replacement of RM07MBST)", "RefUrl": "/notes/198596"}, {"RefNumber": "1831522", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Enhancement of log table MMINKON_UP", "RefUrl": "/notes/1831522"}, {"RefNumber": "161724", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Incorrect update of material document", "RefUrl": "/notes/161724"}, {"RefNumber": "143132", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/143132"}, {"RefNumber": "1426921", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1426921"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1320899", "RefComponent": "MM-IM-GF-REP", "RefTitle": "Changes to the analysis report for inventory mgmt V. 21s", "RefUrl": "/notes/1320899"}, {"RefNumber": "1284654", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Caution with implementations of the BAdI: MB_DOCUMENT_BADI", "RefUrl": "/notes/1284654"}, {"RefNumber": "1280132", "RefComponent": "MM-IM-GF-INC", "RefTitle": "OBEWH Inconsistency with MM postings to previous year", "RefUrl": "/notes/1280132"}, {"RefNumber": "1248511", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Differences between storage location stock and batch stock", "RefUrl": "/notes/1248511"}, {"RefNumber": "1234865", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1234865"}, {"RefNumber": "1227770", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Analysis and correction reports for inventory management", "RefUrl": "/notes/1227770"}, {"RefNumber": "116601", "RefComponent": "MM-IM-GI", "RefTitle": "Inconsistencies between MM and FI", "RefUrl": "/notes/116601"}, {"RefNumber": "1143537", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1143537"}, {"RefNumber": "1116512", "RefComponent": "MM-IM-GR-PO", "RefTitle": "Valuated GR blocked stock and split valuation", "RefUrl": "/notes/1116512"}, {"RefNumber": "1089083", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Releases 7.0* and 7.1*", "RefUrl": "/notes/1089083"}, {"RefNumber": "1069539", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1069539"}, {"RefNumber": "104490", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104490"}, {"RefNumber": "1021928", "RefComponent": "MM-IM-PI-PI", "RefTitle": "MMINKON: Problem with inv adj posting in vendor con stock", "RefUrl": "/notes/1021928"}, {"RefNumber": "1000077", "RefComponent": "MM-IM", "RefTitle": "FAQ: Problems when implementing Note 32236", "RefUrl": "/notes/1000077"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1618888", "RefComponent": "MM-IM-GF-INC", "RefTitle": "How to deal with stock inconsistency?", "RefUrl": "/notes/1618888 "}, {"RefNumber": "2149876", "RefComponent": "CO-PC-ACT", "RefTitle": "C+048 root cause analysis for inconsistencies MM-ML", "RefUrl": "/notes/2149876 "}, {"RefNumber": "3390339", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Scope of MM <> FI corrections from MM-IM development support team", "RefUrl": "/notes/3390339 "}, {"RefNumber": "3406811", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Incorrect stock qty or stock value in material master - II", "RefUrl": "/notes/3406811 "}, {"RefNumber": "3129281", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Dump occurs on executing report MBLABSTS", "RefUrl": "/notes/3129281 "}, {"RefNumber": "2677487", "RefComponent": "MM-IM-GF-INC", "RefTitle": "R/3 connection required for MM-IM inconsistency analysis", "RefUrl": "/notes/2677487 "}, {"RefNumber": "968812", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MM/FI Difference for material stocks", "RefUrl": "/notes/968812 "}, {"RefNumber": "661856", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Correction of valuation indicator KZBWS with stock 0", "RefUrl": "/notes/661856 "}, {"RefNumber": "1426921", "RefComponent": "MM-SRV-GF", "RefTitle": "MAA: Folgebuchungen für DL-Bestellung nicht möglich", "RefUrl": "/notes/1426921 "}, {"RefNumber": "1831522", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Enhancement of log table MMINKON_UP", "RefUrl": "/notes/1831522 "}, {"RefNumber": "590612", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Note 560226: Analysis and correction of incorrect data", "RefUrl": "/notes/590612 "}, {"RefNumber": "1248511", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Differences between storage location stock and batch stock", "RefUrl": "/notes/1248511 "}, {"RefNumber": "34440", "RefComponent": "MM-IM", "RefTitle": "Procedure for correcting the material master", "RefUrl": "/notes/34440 "}, {"RefNumber": "1089083", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Releases 7.0* and 7.1*", "RefUrl": "/notes/1089083 "}, {"RefNumber": "204393", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Inconsistencies between stocks of MARD and MBEW", "RefUrl": "/notes/204393 "}, {"RefNumber": "1000077", "RefComponent": "MM-IM", "RefTitle": "FAQ: Problems when implementing Note 32236", "RefUrl": "/notes/1000077 "}, {"RefNumber": "1320899", "RefComponent": "MM-IM-GF-REP", "RefTitle": "Changes to the analysis report for inventory mgmt V. 21s", "RefUrl": "/notes/1320899 "}, {"RefNumber": "1234865", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Pre-analysis of MM-IM-GF-INC messages in Primary Support", "RefUrl": "/notes/1234865 "}, {"RefNumber": "1284654", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Caution with implementations of the BAdI: MB_DOCUMENT_BADI", "RefUrl": "/notes/1284654 "}, {"RefNumber": "1227770", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Analysis and correction reports for inventory management", "RefUrl": "/notes/1227770 "}, {"RefNumber": "782522", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MM-IM-GF-REP : Additional inconsistency tools", "RefUrl": "/notes/782522 "}, {"RefNumber": "76926", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Checklist for stock inconsistencies", "RefUrl": "/notes/76926 "}, {"RefNumber": "1280132", "RefComponent": "MM-IM-GF-INC", "RefTitle": "OBEWH Inconsistency with MM postings to previous year", "RefUrl": "/notes/1280132 "}, {"RefNumber": "454321", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Release 6.* and 7.0", "RefUrl": "/notes/454321 "}, {"RefNumber": "198596", "RefComponent": "MM-IM", "RefTitle": "Fast MM-FI balance comparison (replacement of RM07MBST)", "RefUrl": "/notes/198596 "}, {"RefNumber": "945346", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MMINKON stock inconsistency tool: Enhancements to standard", "RefUrl": "/notes/945346 "}, {"RefNumber": "967610", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP ROIH1L04 ADDITIONAL PARAMETER TO REDUCE RUNTIME", "RefUrl": "/notes/967610 "}, {"RefNumber": "1116512", "RefComponent": "MM-IM-GR-PO", "RefTitle": "Valuated GR blocked stock and split valuation", "RefUrl": "/notes/1116512 "}, {"RefNumber": "60928", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 3.0 or 3.1 and Release 4.0", "RefUrl": "/notes/60928 "}, {"RefNumber": "1069539", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MMINKON: Change of update key of inconsistency tool", "RefUrl": "/notes/1069539 "}, {"RefNumber": "989970", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Addition inconsistency tools version 18 of Note 32236", "RefUrl": "/notes/989970 "}, {"RefNumber": "1021928", "RefComponent": "MM-IM-PI-PI", "RefTitle": "MMINKON: Problem with inv adj posting in vendor con stock", "RefUrl": "/notes/1021928 "}, {"RefNumber": "619135", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Analysis and correction of sales order stock", "RefUrl": "/notes/619135 "}, {"RefNumber": "921161", "RefComponent": "MM-IM-GF-REP", "RefTitle": "RM07MMFI: Improvement of runtime and function", "RefUrl": "/notes/921161 "}, {"RefNumber": "520010", "RefComponent": "MM-IM-GF-INC", "RefTitle": "FAQ: Inconsistencies in inventory management", "RefUrl": "/notes/520010 "}, {"RefNumber": "590609", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Check of material document using report MBCKMDOC", "RefUrl": "/notes/590609 "}, {"RefNumber": "638510", "RefComponent": "MM-IM-GF-SPV", "RefTitle": "Collection of correction programs for MSSA/ MSSQ", "RefUrl": "/notes/638510 "}, {"RefNumber": "834147", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MBEWH entries: Material with split valuation", "RefUrl": "/notes/834147 "}, {"RefNumber": "212707", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-Oil systems", "RefUrl": "/notes/212707 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "560226", "RefComponent": "MM-IM-GF-SPV", "RefTitle": "Project stock not valuated although project valuated", "RefUrl": "/notes/560226 "}, {"RefNumber": "783293", "RefComponent": "MM-IV-GF-INC", "RefTitle": "MM/FI inconsistencies, report ZREP_MBVALUE_RETAIL", "RefUrl": "/notes/783293 "}, {"RefNumber": "827988", "RefComponent": "MM-IM-GF-INC", "RefTitle": "RMFIFO00: Error in material with split valuation", "RefUrl": "/notes/827988 "}, {"RefNumber": "697809", "RefComponent": "MM-IM-GF-REP", "RefTitle": "Additional inconsistencies tools", "RefUrl": "/notes/697809 "}, {"RefNumber": "542798", "RefComponent": "MM-IV-CA", "RefTitle": "MR11: Reports for correcting the purchase order history", "RefUrl": "/notes/542798 "}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261 "}, {"RefNumber": "655052", "RefComponent": "MM-IM-GF-SPV", "RefTitle": "What do do? Error situations w/ valuated special stocks", "RefUrl": "/notes/655052 "}, {"RefNumber": "626859", "RefComponent": "PP-SFC-EXE-GM", "RefTitle": "COGI: No FI documents created for special valuated materials", "RefUrl": "/notes/626859 "}, {"RefNumber": "558119", "RefComponent": "MM-IM-GF-INC", "RefTitle": "RM07AUTH: Error message no. S4104", "RefUrl": "/notes/558119 "}, {"RefNumber": "547965", "RefComponent": "MM-IM-GR-PO", "RefTitle": "Material number differs in material document +purchase order", "RefUrl": "/notes/547965 "}, {"RefNumber": "579241", "RefComponent": "MM-IM-GR", "RefTitle": "M7302: Field overflow in routine WERT_SIMULIEREN", "RefUrl": "/notes/579241 "}, {"RefNumber": "368185", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO:Incnsistncy posting matl items to prior period", "RefUrl": "/notes/368185 "}, {"RefNumber": "407315", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "Stock inconsistency through cancellation or parking", "RefUrl": "/notes/407315 "}, {"RefNumber": "79083", "RefComponent": "MM-IS-IC", "RefTitle": "Setup of statistical data in BCO: Additional information", "RefUrl": "/notes/79083 "}, {"RefNumber": "378731", "RefComponent": "IS-OIL-DS", "RefTitle": "Overview of consistency check procedures in IS-OIL systems", "RefUrl": "/notes/378731 "}, {"RefNumber": "373376", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO: <PERSON>rror F5559 when posting to material", "RefUrl": "/notes/373376 "}, {"RefNumber": "313803", "RefComponent": "MM-IM-GF-MISC", "RefTitle": "xxx RM07APP1: Routine GET_RELEASE does not exist", "RefUrl": "/notes/313803 "}, {"RefNumber": "357468", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Changing valuation type via goods movement", "RefUrl": "/notes/357468 "}, {"RefNumber": "419714", "RefComponent": "CO-PC-ACT", "RefTitle": "Bestandsdifferenzen nach Kontenpflege (MR11)", "RefUrl": "/notes/419714 "}, {"RefNumber": "354856", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Kundenfragebogen für Inkonsistenzcheck", "RefUrl": "/notes/354856 "}, {"RefNumber": "116601", "RefComponent": "MM-IM-GI", "RefTitle": "Inconsistencies between MM and FI", "RefUrl": "/notes/116601 "}, {"RefNumber": "304670", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Renaming consistency analysis tools of the stock", "RefUrl": "/notes/304670 "}, {"RefNumber": "161724", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Incorrect update of material document", "RefUrl": "/notes/161724 "}, {"RefNumber": "98942", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Multipe material documents in one LUW", "RefUrl": "/notes/98942 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SRMGP", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SRM_SERVER", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SRM_SERVER 600", "SupportPackage": "SAPKIBKU05", "URL": "/supportpackage/SAPKIBKU05"}, {"SoftwareComponentVersion": "SRMGP 600", "SupportPackage": "SAPK-60001INSRMGP", "URL": "/supportpackage/SAPK-60001INSRMGP"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60302", "URL": "/supportpackage/SAPKH60302"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}