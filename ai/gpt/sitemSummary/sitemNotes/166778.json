{"Request": {"Number": "166778", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1031, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014709802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000166778?language=E&token=BB73D2F29DCE2D6CCB5D83653DA923D1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000166778", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000166778/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "166778"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.08.2002"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-SD"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Sales and Distribution"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Sales and Distribution", "value": "BW-BCT-SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "166778 - Current SD extractors for PI.1999 and higher"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Caution:<br /><br />You may only import this note if you are using the BW OLTP PI.1999 or higher!<br /><br />You may only carry out the download after you imported the BW Add-ons!<br /><br />Otherwise, new fields in the transfer structures S260 - S263 will not be updated after the upgrade to BW Release 2.0A (and higher), for example.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BW Release 2.0A, 2.0B, 3.0A, Extraktor, S260, S261, S262, S263, RMC<PERSON>260, RMCSS261, RMCSS262, RMCSS263, update, 2.0B, PI-1999, PI-2000, PI-2000, PI-2001<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Upgrade to BW Release 2.0A (and higher) or new installation of BW Release 2.0A (and higher).<br /><br />or<br /><br />Upgrade to PI-1999 (and higher) or new installatin of PI-1999 and higher<br /><br /><br />SD transfer structures S260 - S263 and their extractors have been enhanced in BW Release 2.0A (and higher) and PI-1999 (and higher).<br />The structure enhancements were imported with the BW Add-on, but the extraction program enhancements are still missing.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>CAUTION!<br /><br />For technical reasons, these reports cannot be delivered via Hot Packages!!<br /><br /><br />Since the necessary objects are delivered via the plug-in in the core releases up to and including 3.1H, this Note must NOT be imported.<br /><br />This Note applies to PI-1999 and higher in the core releases and higher. Please import the following Note from SAPSERV3 if you are using<br />BW release 2.0A or higher. Avoid importing the programs while<br />the system is in operation.<br /><br />ATTENTION: As of Release 4.70, Plug-In PI 2002.1, the reports are delivered together with the plug-in. The SAPServ transport needs NOT be imported!<br /><br />************************************************************************<br />Correction processing via sapserv3:<br />Directory:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /general/R3server/abap/note.0166778<br />respective transport: AX8K014569<br />respective files:&#x00A0;&#x00A0;&#x00A0;&#x00A0; R014569.AX8 and K014569.AX8<br />************************************************************************<br /><br />The following objects are imported: &#x00A0;&#x00A0;RMCDW001<BR/> &#x00A0;&#x00A0;RMCDW002<BR/> &#x00A0;&#x00A0;RMCDW003<BR/> &#x00A0;&#x00A0;RMCDW004<BR/> &#x00A0;&#x00A0;RMCSS260<BR/> &#x00A0;&#x00A0;RMCSS261<BR/> &#x00A0;&#x00A0;RMCSS262<BR/> &#x00A0;&#x00A0;RMCSS263<br /><br />In order to protect the reports from inadvertent overwriting during the release upgrade, access the objects after the import with Transaction SE38, so that they receive status 'modified'.<br /><br />This way, the objects are not overwritten automatically during release upgrade but are included into your comparison list.The reports should not be overwritten by an upgrade!<br /><br />If you have already imported this Note with PI-1999 or PI-2000, you do NOT need to to import it again when upgrading to a higher PI release.<br /><br />Note:<br /><br />Make sure that the changes you possibly made according to Note 301410 do not get lost.<br /><br />If you do not find the call of the routines in the update program, use Notes 131702 and 166778 to check whether you have installed the correct extractor programs.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D023373"}, {"Key": "Processor                                                                                           ", "Value": "D023373"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000166778/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000166778/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "526984", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "SIS/BW: Deadlocks when updating into S26x info structures", "RefUrl": "/notes/526984"}, {"RefNumber": "399114", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/399114"}, {"RefNumber": "358096", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "VIS/BW: S263 Allocation for Deliveries Without Order Referen", "RefUrl": "/notes/358096"}, {"RefNumber": "357050", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "VIS/BW: S260-263 Incorrect.Update of Single Records", "RefUrl": "/notes/357050"}, {"RefNumber": "357041", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "VIS/BW: S264 Incorrect Update of Single Records", "RefUrl": "/notes/357041"}, {"RefNumber": "334612", "RefComponent": "BW-BCT-SD", "RefTitle": "Collective note: Terminations in BW-SD update", "RefUrl": "/notes/334612"}, {"RefNumber": "315266", "RefComponent": "BW-BCT-SD", "RefTitle": "Determination of extractor vers. in customer system", "RefUrl": "/notes/315266"}, {"RefNumber": "301410", "RefComponent": "BW-BCT-SD", "RefTitle": "S260-S264: User-defined fields with incorrect sign", "RefUrl": "/notes/301410"}, {"RefNumber": "176290", "RefComponent": "BW-BCT-SD", "RefTitle": "S260 - S264 some fields are not filled", "RefUrl": "/notes/176290"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "136259", "RefComponent": "BW-BCT-LO", "RefTitle": "Deadlocks when updating LIS information structures", "RefUrl": "/notes/136259"}, {"RefNumber": "131702", "RefComponent": "BW-BCT-SD", "RefTitle": "Current SD extractors for BW Release 1.2B", "RefUrl": "/notes/131702"}, {"RefNumber": "123972", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@8R@ Not or only partly compatible with IS-Oil", "RefUrl": "/notes/123972"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "123972", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@8R@ Not or only partly compatible with IS-Oil", "RefUrl": "/notes/123972 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "526984", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "SIS/BW: Deadlocks when updating into S26x info structures", "RefUrl": "/notes/526984 "}, {"RefNumber": "136259", "RefComponent": "BW-BCT-LO", "RefTitle": "Deadlocks when updating LIS information structures", "RefUrl": "/notes/136259 "}, {"RefNumber": "176290", "RefComponent": "BW-BCT-SD", "RefTitle": "S260 - S264 some fields are not filled", "RefUrl": "/notes/176290 "}, {"RefNumber": "358096", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "VIS/BW: S263 Allocation for Deliveries Without Order Referen", "RefUrl": "/notes/358096 "}, {"RefNumber": "357050", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "VIS/BW: S260-263 Incorrect.Update of Single Records", "RefUrl": "/notes/357050 "}, {"RefNumber": "357041", "RefComponent": "BW-BCT-SD-LIS", "RefTitle": "VIS/BW: S264 Incorrect Update of Single Records", "RefUrl": "/notes/357041 "}, {"RefNumber": "334612", "RefComponent": "BW-BCT-SD", "RefTitle": "Collective note: Terminations in BW-SD update", "RefUrl": "/notes/334612 "}, {"RefNumber": "131702", "RefComponent": "BW-BCT-SD", "RefTitle": "Current SD extractors for BW Release 1.2B", "RefUrl": "/notes/131702 "}, {"RefNumber": "301410", "RefComponent": "BW-BCT-SD", "RefTitle": "S260-S264: User-defined fields with incorrect sign", "RefUrl": "/notes/301410 "}, {"RefNumber": "315266", "RefComponent": "BW-BCT-SD", "RefTitle": "Determination of extractor vers. in customer system", "RefUrl": "/notes/315266 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "1999_1_31I", "To": "1999_1_46B", "Subsequent": "X"}, {"SoftwareComponent": "PI", "From": "2000_1_31I", "To": "2000_1_46C", "Subsequent": "X"}, {"SoftwareComponent": "PI", "From": "2000_2_31I", "To": "2000_2_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2001_1_31I", "To": "2001_1_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2001_2_31I", "To": "2001_2_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2002_1_31I", "To": "2002_1_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2002_2_31I", "To": "2002_2_46C", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "1999_1_31I", "To": "1999_1_46A", "Subsequent": "X"}, {"SoftwareComponent": "PI-A", "From": "2000_1_31I", "To": "2000_1_45B", "Subsequent": "X"}, {"SoftwareComponent": "PI-A", "From": "2000_2_31I", "To": "2000_2_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2001_1_31I", "To": "2001_1_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2001_2_31I", "To": "2001_2_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2002_1_31I", "To": "2002_1_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2002_2_31I", "To": "2002_2_45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}