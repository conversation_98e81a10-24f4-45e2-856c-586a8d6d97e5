{"Request": {"Number": "1074388", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1637, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016340202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=A4C9121B730B2CE92CE92E595ACE01BD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1074388"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1074388 - SAPBWNews BW 7.00 ABAP SP 16"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Support Package 16 for BI Release 7.0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.0, BW 7.0, BW 7.00, BW Patches, BI, BI 7.00, SAPBINews, NW2004s, 2004s</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBINews for Support Package 16 of BI Release 7.0 (Support Package 16 is part of SAP NetWeaver 7.0 Support Package Stack 14). Here you can find a list of the notes that describe the corrections or enhancements in Support Package 16. This note will be updated when other notes are added. <br /><br />Note that BI 7.0 ABAP SP16 is a part of SAP NW 7.0 SPS14.<br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li><strong>Manual actions that may be necessary:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>General information:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected in this Support Package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Improvements delivered with this Support Package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See the release and information notes.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong><br /><br /></p>\r\n<p>We recommend that you implement Note 1157796, so that a consistency check is performed for the characteristic 0REQUEST. You can find more information about this in Note 1157796. As of Support Package 18, the source code corrections are contained in the standard system.</p>\r\n<p>Before you import a Support Package, use transaction SNOTE to implement Note 1106385. This prevents a potential source code loss when you use transaction SPAU to adjust obsolete notes. <br /><br />Before you import the Support Package, check if there are inactive objects in your system: Call transaction SE80, select \"Inactive objects\" and enter * in the user field.  If objects that are assigned to the SAP namespace are displayed in the section \"Transportable Objects\", you must activate these before you import the Support Package.  If you find objects, proceed as described SAP consulting Note 1131831.  You must never delete inactive objects from the transport request as this would result in extensive follow-up problems after you import the Support Package.  For more detailed information, refer to Note 822379.<br /><br /><br /><strong><span style=\"text-decoration: underline;\">Please find out about the latest SAP Notes that you also have to read and perhaps implement after you import SP16. </span></strong><br /><strong><span style=\"text-decoration: underline;\">We recommend that you do not simply indiscriminately implement all SAP Notes that could be implemented after the corresponding Support Package. However, always implement \"Hot News\" and priority 1 notes.</span></strong><br /><strong><span style=\"text-decoration: underline;\">In the search function in the SAP Notes database or in SAP Service Marketplace, use the following search term: \"SAPKW70016\", and under \"Search Criteria\", choose \"Extended Search Criteria\"; select \"HotNews\" and \"Correction with high priority\" as the \"Priority\".</span></strong><br /><strong><span style=\"text-decoration: underline;\">You can also restrict the search to a specific topic by entering the relevant specifications in the \"Applic. Area\" field; for example, BW-WHM*.</span></strong>If later Support Packages have already been released, you should add the name of the later SPs to the search term, too (for example, SAPKW70017).</p>\r\n<p>Following the import of a Support Package, <strong>post-implementation steps</strong> are normally <strong>required</strong> in transaction SNOTE. SAP Notes that have already been implemented can become inconsistent and can cause <strong>functions that work incorrectly or syntax errors</strong>. Call transaction <strong>SNOTE</strong> and <strong>reimplement</strong> the SAP Notes that have become inconsistent. When you have done this, your BW system is operative and consistent again.<br /><br />Import the SNOTE corrections first and read composite SAP Note 875986, which contains important notes about SAP Note Assistant.<br /><br />You should implement Notes 932065 and 948389 before you use transaction SNOTE to implement advance corrections. Otherwise, serious problems may occur when you try to deimplement notes again.<br /><br />If documents have already been migrated to the portal, you may have to repeat the migration. For more information, see Note 950877.<br /><br />If you have questions about downloading Support Package Stacks, see Note 911032 (FAQ - SAP Support Package Stack Download).<br /><br />For minor revisions in the BI Accelerator, see Note 1075579.<br /><br />You must implement IGS 7.00 Patch 10 due to a changed interpretation of chart-axes titles as of Support Package Stack 14. Therefore, SAP recommends that you always implement the current patch for IGS 7.00. For more information, refer to Note 1127545.</p>\r\n<p><strong>Errors that may occur after you import the Support Package:</strong><br /><br /><br /></p>\r\n<p>After you import Support Package 16, you may no longer be able to transfer BI Content. The system issues message R7B 641. No further transport of InfoObjects is then possible, for example, central objects such as 0Material can no longer be transported. For more information, see Note 1139547.<br /><br />When you activate a MultiProvider or when you check or start a query, termination R7I 135 or BRAIN 152 occurs. For more information, see Note 1086744.<br /><br />During MultiProvider processing, various errors may occur. For more information, see Notes 1110997, 1113372 ,1144874 (subsequent corrections to Note 1090490). The following symptoms may occur if Note 1110997 has not been implemented:</p>\r\n<ul>\r\n<li>Changes to a MultiProvider do not take effect.</li>\r\n</ul>\r\n<ul>\r\n<li>A RAISE EXCEPTION TYPE CX_RS_NO_MULTIPROV occurs in the constuctor of the class CL_RSD_MULTIPROV_CONSTRUCT.</li>\r\n</ul>\r\n<ul>\r\n<li>A system error occurs in program CL_RSD_MULTIPROV GET_PART_IOBJNM-03-</li>\r\n</ul>\r\n<ul>\r\n<li>A system error occurs in the program CL_RSD_MULTIPROV and in the form FACTORY-02-</li>\r\n</ul>\r\n<ul>\r\n<li>A system error occurs in the program CL_RSDM_READ_MASTER_DATA and in the form _VALUE_READ-02-</li>\r\n</ul>\r\n<p>For more information, see Note 1110997.<br /><br /><br />Various unclear and partially incorrect error messages may occur when you maintain transformations. For more information, refer to Note 1112742.<br /><br />You can start the change run that edits the same aggregate as the current rollup. This is due to an error. For more information, refer to Note 1123312.<br /><br />A dump occurs when you activate a transformation with several groups. For more information, refer to Note 1125231.<br /><br />Titles of axes are displayed incorrectly in diagrams when you implement Support Package Stack 14 for BI 7.0. For more information, refer to Note 1127545.<br /><br />An UNCAUGHT_EXCEPTION CX_RSROUT_SKIP_RECORD occurs when master data is read in the transformation. For more information, see Note 1116379.<br /><br />An incorrect selection is used for planning functions that are started from Business Explorer (BEx). For more information, see Note 1140808.<br /><br />When you import Support Package Stack 14 of Support Package 16 BW7.0 (SAPKW70016), transaction SPAM may terminate in the XPRA phase (method execution) with SYNTAX_ERROR in the program RDDEXECL. The actual syntax error is in the class CL_RSR_OLAP. The problem occurs if Note 1101187 (\"The OLAP tunnel\") was implemented before the Patch was applied. For more information, see Note 1141108.<br /><br />For error message \"Termination:  Inconsistent input parameter:  I_KHANDLE, value 0000\",  refer to Note 1122063. <br /><br />DTP extraction from an InfoProvider with navigation attributes might<br />no longer work following the import of SP16.&#x00A0;&#x00A0;Please implement SAP Note 1145673 to avoid incorrect data in the case of the use of navigation attributes in the transformation.<br /><br />If your query simultaneously uses non-cumulative key figures and virtual key figures but no virtual characteristics, the following error can occur: The system always displays the value 0 for non-cumulative key figures. For more information, see Note 1145972.<br /><br />A query on an InfoProvider, for which the property \"Nearline Storage Should Be Read As Well\" is set, delivers incorrect results when you use restricted key figures and/or structure elements.  For more information, see Note 1155195.<br /><br />Due to an error that was delivered in Note 1085394, an InfoPackage that was executed in the background may<br />request a repeat. As a result, there may be duplicate data in the data targets. For more information, see Note 1166020.<br /><br />After you implement Note 1104648 or import Support Package 16 or 17, not enough combinations may be created if you set the \"Access Type for Result Values\" according to \"Master Data\" or \"Characteristic Relationships\". In input-ready queries, cells that should be input-ready may not be input-ready. For more information, see Note 1166020.<br /><br />Subsequent corrections to Note 1090490. The following symptoms may occur:&#x00A0;&#x00A0; The NCUMTIM is determined incorrectly. For more information, see Note 1163116.</p>\r\n<p><strong>Errors corrected with this Support Package:</strong></p>\r\n<p><br />Delivered transformations are deleted and can no longer be transferred from Content. The active and saved versions of the transformation are not affected. Please see SAP Note 1171293 for more information about this.<br /><br />DTP monitor: The number of transferred records is displayed incorrectly. Please see SAP Note 1099213 for more information about this.</p>\r\n<p><strong>Enhancements delivered with this Support Package:</strong></p>\r\n<p><br />In the online analytical processing (OLAP) technology area:</p>\r\n<ul>\r\n<li>The changerun monitor was revised. Note 1041822 contains a list of the improvements.</li>\r\n</ul>\r\n<ul>\r\n<li>Ready for input status despite \"Calculate as...\".  For more information, refer to Note 1093146.</li>\r\n</ul>\r\n<p><br /><strong>See the release and information notes.</strong><br /><br /></p>\r\n<p>See Note 1056259 for an overview of the most important factors that influence performance, which you must take into account when creating planning applications.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX (Business Explorer)"}, {"Key": "Other Components", "Value": "BW-BEX-ET (Enduser Technology)"}, {"Key": "Other Components", "Value": "BW-SYS-GUI (BW Frontend and GUI)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "880660", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "DBC: Syntax error in the start routine with the STRG type", "RefUrl": "/notes/880660"}, {"RefNumber": "1273778", "RefComponent": "BW", "RefTitle": "Currency conversion from SSK into Euro in SAP BW", "RefUrl": "/notes/1273778"}, {"RefNumber": "1145972", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "No data for non-cumulative key figures & virtual key figures", "RefUrl": "/notes/1145972"}, {"RefNumber": "1145108", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Brain A264, A320 or A348 when you generate a query", "RefUrl": "/notes/1145108"}, {"RefNumber": "1144874", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSD_MULTIPROV_CHECK; _CHECK_CMP_MESS-01-", "RefUrl": "/notes/1144874"}, {"RefNumber": "1138199", "RefComponent": "BW-BEX-OT", "RefTitle": "X299 BRAIN in CL_RSD_MULTIPROV; form GET_PART_IOBJNM-01-", "RefUrl": "/notes/1138199"}, {"RefNumber": "1129774", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning function terminates w/o specific error message", "RefUrl": "/notes/1129774"}, {"RefNumber": "1125594", "RefComponent": "BW", "RefTitle": "RSTT trace creation from Web runtime terminates", "RefUrl": "/notes/1125594"}, {"RefNumber": "1121289", "RefComponent": "BW-PLA-IP", "RefTitle": "Master locks not displayed in RSPLSE", "RefUrl": "/notes/1121289"}, {"RefNumber": "1117966", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain 299 in SAPLRRI2, form REP_ASSIGN_INITIAL_OPT-02-", "RefUrl": "/notes/1117966"}, {"RefNumber": "1116671", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Slow listing of chains if logon language incorr.", "RefUrl": "/notes/1116671"}, {"RefNumber": "1116189", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Cannot switch to transaction in Web", "RefUrl": "/notes/1116189"}, {"RefNumber": "1116157", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Monitor terminates if source system not existing", "RefUrl": "/notes/1116157"}, {"RefNumber": "1115243", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: RSPCINSTANCE not deleted", "RefUrl": "/notes/1115243"}, {"RefNumber": "1113372", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CL_RSD_MULTIPROV FACTORY-02-", "RefUrl": "/notes/1113372"}, {"RefNumber": "1113121", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Duplicated data when delta extracted frm write-opt DataStore", "RefUrl": "/notes/1113121"}, {"RefNumber": "1112596", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17:P22:DWB: Slow refresh if several srce systems connected", "RefUrl": "/notes/1112596"}, {"RefNumber": "1112591", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P17:P22:BAPI_IPAK_START: Long runtime for date scheduling", "RefUrl": "/notes/1112591"}, {"RefNumber": "1112271", "RefComponent": "BW-BEX-ET", "RefTitle": "Interval limits in selections not compared correctly", "RefUrl": "/notes/1112271"}, {"RefNumber": "1110997", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1090490", "RefUrl": "/notes/1110997"}, {"RefNumber": "1110570", "RefComponent": "BW-WHM-DST", "RefTitle": "Correction: Incorrect message for Job_open", "RefUrl": "/notes/1110570"}, {"RefNumber": "1109644", "RefComponent": "BW-BEX-ET", "RefTitle": "Query views are not deleted in the D version", "RefUrl": "/notes/1109644"}, {"RefNumber": "1106719", "RefComponent": "BW-BEX-OT", "RefTitle": "First roll up to BIA index takes a long time", "RefUrl": "/notes/1106719"}, {"RefNumber": "1106705", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error ASSERTION_FAILED for formulas", "RefUrl": "/notes/1106705"}, {"RefNumber": "1106485", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Deleting master data; and aggregates", "RefUrl": "/notes/1106485"}, {"RefNumber": "1106393", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "SAP_DROP_TMPTABLES do not drop RSDD_TMPNM_ADM entries", "RefUrl": "/notes/1106393"}, {"RefNumber": "1106363", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Repeat is not reported", "RefUrl": "/notes/1106363"}, {"RefNumber": "1106197", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termin. during query generation in CL_RSR and GET_CHANM-01-", "RefUrl": "/notes/1106197"}, {"RefNumber": "1106078", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Precalculate value set: Object BUCKET already exists", "RefUrl": "/notes/1106078"}, {"RefNumber": "1106058", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation end routine - ALL_FIELDS does not work", "RefUrl": "/notes/1106058"}, {"RefNumber": "1105583", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Correct parameter display for reduced call stack", "RefUrl": "/notes/1105583"}, {"RefNumber": "1105498", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: 'GET_SID-1-' in program SAPLRRS2", "RefUrl": "/notes/1105498"}, {"RefNumber": "1105495", "RefComponent": "BW-BEX-OT", "RefTitle": "Verbesserung Technische Information in RSRT", "RefUrl": "/notes/1105495"}, {"RefNumber": "1105139", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Filling the OLAP cache with \"contains pattern\" in BI 7.0", "RefUrl": "/notes/1105139"}, {"RefNumber": "1105096", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation cannot be deleted due to transport system", "RefUrl": "/notes/1105096"}, {"RefNumber": "1105094", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Structure name error in RSDS_RANGE_TO_WHERE", "RefUrl": "/notes/1105094"}, {"RefNumber": "1104367", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No success message when you successfully activate a DAP", "RefUrl": "/notes/1104367"}, {"RefNumber": "1104151", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error during activation", "RefUrl": "/notes/1104151"}, {"RefNumber": "1104069", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND process looses events during content activtn", "RefUrl": "/notes/1104069"}, {"RefNumber": "1104014", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exporting data from ALV -> BEx: Termination DEFINE_BEXQUERY", "RefUrl": "/notes/1104014"}, {"RefNumber": "1104012", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data when navigating in the BEx Analyzer", "RefUrl": "/notes/1104012"}, {"RefNumber": "1103912", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "3.x Archiving: Termination with RAISE_EXCEPTION in SAPLRSDRI", "RefUrl": "/notes/1103912"}, {"RefNumber": "1103886", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 function in 0DATE is not restricted by a time interval", "RefUrl": "/notes/1103886"}, {"RefNumber": "1103558", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1103558"}, {"RefNumber": "1103425", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: RSPC 006 if follow-on job is suspended", "RefUrl": "/notes/1103425"}, {"RefNumber": "1103342", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination when planning functions are executed", "RefUrl": "/notes/1103342"}, {"RefNumber": "1103316", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "New master data value is generated", "RefUrl": "/notes/1103316"}, {"RefNumber": "1103315", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IF_RSMD_RS~READ_DATA-04 in CL_RSMD_RS_SPECIAL", "RefUrl": "/notes/1103315"}, {"RefNumber": "1102965", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:PC:MON:ODS:Statman:Req. does not have qualok stat. in PC", "RefUrl": "/notes/1102965"}, {"RefNumber": "1102838", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correct.: Endless loop after API termination in dialog mode", "RefUrl": "/notes/1102838"}, {"RefNumber": "1102729", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA e-mai notification - RSADMIN parameter is incorrect", "RefUrl": "/notes/1102729"}, {"RefNumber": "1102701", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Usability: Customer exit variables in analysis authorization", "RefUrl": "/notes/1102701"}, {"RefNumber": "1102676", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SAP_USER_EXTENTS/SAP_USER_SEGMENTS do not exist in RSRV", "RefUrl": "/notes/1102676"}, {"RefNumber": "1102675", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1102675"}, {"RefNumber": "1102587", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Correction of failure in Aggregate Check Tool", "RefUrl": "/notes/1102587"}, {"RefNumber": "1102487", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Loading hierarchies: Sequence of siblings", "RefUrl": "/notes/1102487"}, {"RefNumber": "1102486", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "Creating transfer rules w/o InfoObject assignment for BAPIs", "RefUrl": "/notes/1102486"}, {"RefNumber": "1102405", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16) Error during content transfer", "RefUrl": "/notes/1102405"}, {"RefNumber": "1102318", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Cancel function does not work for the RSDDV dialog box", "RefUrl": "/notes/1102318"}, {"RefNumber": "1102296", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Improvements to 3.X variable screen filter dialog", "RefUrl": "/notes/1102296"}, {"RefNumber": "1102263", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:DTP: Incorrect display of DTP selections", "RefUrl": "/notes/1102263"}, {"RefNumber": "1102123", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data when you use excluding and including sel.", "RefUrl": "/notes/1102123"}, {"RefNumber": "1101947", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when using URL to start BEx Analyzer", "RefUrl": "/notes/1101947"}, {"RefNumber": "1101933", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Termin. MOVE_TO_LIT_NOTALLOWED_NODAT: <l_sx_nkyfnm>-aggrexc", "RefUrl": "/notes/1101933"}, {"RefNumber": "1101924", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Dump in transformation with InfoSet as source", "RefUrl": "/notes/1101924"}, {"RefNumber": "1101846", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process chain status as a message", "RefUrl": "/notes/1101846"}, {"RefNumber": "1101759", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:Manage:Canceling during branching to PC does not work", "RefUrl": "/notes/1101759"}, {"RefNumber": "1101726", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Changing the active filter of planning functions", "RefUrl": "/notes/1101726"}, {"RefNumber": "1101648", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Usability in authorization maintenance", "RefUrl": "/notes/1101648"}, {"RefNumber": "1101612", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Termination occurs in input template in planning modeler", "RefUrl": "/notes/1101612"}, {"RefNumber": "1101589", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Start/end routine; InfoCube; time reference", "RefUrl": "/notes/1101589"}, {"RefNumber": "1101481", "RefComponent": "BW-BEX-ET-WEB-MOB", "RefTitle": "BEx Mobile Intelligence with Windows Mobile 6 and Pocket IE", "RefUrl": "/notes/1101481"}, {"RefNumber": "1101289", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Unknown index (900) on E tables of BW aggregates", "RefUrl": "/notes/1101289"}, {"RefNumber": "1101187", "RefComponent": "BW-PLA-IP", "RefTitle": "The OLAP tunnel", "RefUrl": "/notes/1101187"}, {"RefNumber": "1101166", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error: Node & with predecessor 0 not found in hierarchy &", "RefUrl": "/notes/1101166"}, {"RefNumber": "1100980", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Dump when activating a transformation", "RefUrl": "/notes/1100980"}, {"RefNumber": "1100977", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Meldung: Transformation nicht vorhanden", "RefUrl": "/notes/1100977"}, {"RefNumber": "1100976", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IOBJ_VALUE_NOT_VALID in CL_RSMD_RS and _TRANSFORM_DATA", "RefUrl": "/notes/1100976"}, {"RefNumber": "1100955", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RS_EXCEPTION000 in parallel query execution", "RefUrl": "/notes/1100955"}, {"RefNumber": "1100695", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Incorrect data displayed when an aggregate is checked", "RefUrl": "/notes/1100695"}, {"RefNumber": "1100630", "RefComponent": "BW-WHM-DBA-SDEL", "RefTitle": "Follow-up note for Note 1077415", "RefUrl": "/notes/1100630"}, {"RefNumber": "1100504", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query with node selection terminates when using BIA", "RefUrl": "/notes/1100504"}, {"RefNumber": "1100490", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "No data for non-cumulatives with missing currency/unit", "RefUrl": "/notes/1100490"}, {"RefNumber": "1100475", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16): Falsche Meldung beim Abbrec des Löschens", "RefUrl": "/notes/1100475"}, {"RefNumber": "1100472", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Error during content transfer", "RefUrl": "/notes/1100472"}, {"RefNumber": "1100457", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correcting stop due to missing job", "RefUrl": "/notes/1100457"}, {"RefNumber": "1100456", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Syntax error for type P in error stack key", "RefUrl": "/notes/1100456"}, {"RefNumber": "1100454", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction:No conversion in change log deletion after import", "RefUrl": "/notes/1100454"}, {"RefNumber": "1100006", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Query view delivers an incorrect navigation status", "RefUrl": "/notes/1100006"}, {"RefNumber": "1099870", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Display of statistics when executing trace", "RefUrl": "/notes/1099870"}, {"RefNumber": "1099477", "RefComponent": "BW-PLA-IP", "RefTitle": "x299 BRAIN in CL_RSPLS_DELTA_BUFFER_B, READ-17- or READ-18", "RefUrl": "/notes/1099477"}, {"RefNumber": "1099459", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Selection on 0FISCYEAR processed incorrectly", "RefUrl": "/notes/1099459"}, {"RefNumber": "1099415", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Performance optimization for RSZDELETE", "RefUrl": "/notes/1099415"}, {"RefNumber": "1099391", "RefComponent": "BW-PLA-IP", "RefTitle": "Figures disappear after you transfer", "RefUrl": "/notes/1099391"}, {"RefNumber": "1099157", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:DTP: Enqueue deadlock on RSSTATMANSTATUS", "RefUrl": "/notes/1099157"}, {"RefNumber": "1099053", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: RSTPRFC: Password in lower case not allowed", "RefUrl": "/notes/1099053"}, {"RefNumber": "1098905", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Hierarchy node variable value not retained when invoking F4", "RefUrl": "/notes/1098905"}, {"RefNumber": "1098782", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help does not return any data", "RefUrl": "/notes/1098782"}, {"RefNumber": "1098734", "RefComponent": "BW-BEX-OT", "RefTitle": "Subs. corr. to 1090490; BRAIN A338 0INFOPROV does not exist", "RefUrl": "/notes/1098734"}, {"RefNumber": "1098731", "RefComponent": "BW-BEX-OT", "RefTitle": "Correction for Note 1091714", "RefUrl": "/notes/1098731"}, {"RefNumber": "1098613", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL:Hier:Many hier.headers in source syst.; Performance", "RefUrl": "/notes/1098613"}, {"RefNumber": "1098278", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Correction: Trying to activate 3.x-DataSource remotely", "RefUrl": "/notes/1098278"}, {"RefNumber": "1098180", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BRAIN X299 in SAPLRSDRC_SPLIT; form COMPOUND_CHAVL_CHECK-01-", "RefUrl": "/notes/1098180"}, {"RefNumber": "1098160", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Too much data for constant selection", "RefUrl": "/notes/1098160"}, {"RefNumber": "1098146", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: Content timestamp of queries is deleted", "RefUrl": "/notes/1098146"}, {"RefNumber": "1098117", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:LOADING:Switch init to delta - init simulation indicator", "RefUrl": "/notes/1098117"}, {"RefNumber": "1098095", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Correction:Field texts in primary language (ABAP Dict) blank", "RefUrl": "/notes/1098095"}, {"RefNumber": "1098071", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Incorrect data with hierarchies and new master data provider", "RefUrl": "/notes/1098071"}, {"RefNumber": "1098057", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query: Dump NO_ROLL_MEMORY or other memory overflow", "RefUrl": "/notes/1098057"}, {"RefNumber": "1097915", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchy overview: No update  BI7.0 SP 16 (SPS 14)", "RefUrl": "/notes/1097915"}, {"RefNumber": "1097832", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable for characteristic with time-dependent text", "RefUrl": "/notes/1097832"}, {"RefNumber": "1097770", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Migration to analysis auths: Dump in SAPLRSEC_MIGRATION", "RefUrl": "/notes/1097770"}, {"RefNumber": "1097706", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1097706"}, {"RefNumber": "1097674", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "NW2004s: Administration of backup object version", "RefUrl": "/notes/1097674"}, {"RefNumber": "1097613", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:PSA process:Termination w/o requests that can be posted", "RefUrl": "/notes/1097613"}, {"RefNumber": "1097529", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "DB fallback for BIA: Change wait time", "RefUrl": "/notes/1097529"}, {"RefNumber": "1097448", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: PSADELETE w/o PSA reference after content actvtn", "RefUrl": "/notes/1097448"}, {"RefNumber": "1097425", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "InfoProvider-spezifische Texte von InfoObjeken falsch", "RefUrl": "/notes/1097425"}, {"RefNumber": "1097330", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1097330"}, {"RefNumber": "1097290", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1097290"}, {"RefNumber": "1097125", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:Check useful only for REQU requests", "RefUrl": "/notes/1097125"}, {"RefNumber": "1096987", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1096987"}, {"RefNumber": "1096778", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:BATCH:Default settings, one parallel process for A1S", "RefUrl": "/notes/1096778"}, {"RefNumber": "1096774", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Enhancement for advance 7.1 development-init/delta", "RefUrl": "/notes/1096774"}, {"RefNumber": "1096771", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Combined DTP extraction from active table and archive", "RefUrl": "/notes/1096771"}, {"RefNumber": "1096620", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Incorrect PSA version in RSDSSEG", "RefUrl": "/notes/1096620"}, {"RefNumber": "1096553", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: ASCII default for RDA adapter", "RefUrl": "/notes/1096553"}, {"RefNumber": "1096307", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "MD Update when only keys fields are mapped", "RefUrl": "/notes/1096307"}, {"RefNumber": "1096279", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Printing PDF docs: Error during processing of spool request", "RefUrl": "/notes/1096279"}, {"RefNumber": "1096023", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Due to cube &1 being locked, query cannot be executed", "RefUrl": "/notes/1096023"}, {"RefNumber": "1096008", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Error message \"RESULT\" unknown", "RefUrl": "/notes/1096008"}, {"RefNumber": "1095984", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting when you use ctrl query for recipient determination", "RefUrl": "/notes/1095984"}, {"RefNumber": "1095958", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P16:DTP:Batch manager works in the same logical unit of work", "RefUrl": "/notes/1095958"}, {"RefNumber": "1095955", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Manage: Rebuilding: Process chain jump button", "RefUrl": "/notes/1095955"}, {"RefNumber": "1095924", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Deletion/analyis report for error handling logs", "RefUrl": "/notes/1095924"}, {"RefNumber": "1095828", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:DTP:Cannot delete yellow DTP requests in Manage", "RefUrl": "/notes/1095828"}, {"RefNumber": "1095759", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Display of red requests in the RDA monitor", "RefUrl": "/notes/1095759"}, {"RefNumber": "1095721", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination in program SAPLRRK0 WORKCELL_SET-01-", "RefUrl": "/notes/1095721"}, {"RefNumber": "1095656", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correctn: Monitor wizard does not know RDA and Web services", "RefUrl": "/notes/1095656"}, {"RefNumber": "1095653", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Repair start twice at the second time", "RefUrl": "/notes/1095653"}, {"RefNumber": "1095651", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Correction: Incompl. RDA InfoPackgs transferred from content", "RefUrl": "/notes/1095651"}, {"RefNumber": "1095625", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Attributes of reference char. for variables not displayed", "RefUrl": "/notes/1095625"}, {"RefNumber": "1095466", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Suppressing the message \"Text variable cannot be replaced\"", "RefUrl": "/notes/1095466"}, {"RefNumber": "1095410", "RefComponent": "BW", "RefTitle": "P16:PSA:Enhancement of PSA process to allow update of 0 days", "RefUrl": "/notes/1095410"}, {"RefNumber": "1095397", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Fill BIA index: Termination with SAPSQL_INVALID_TABLENAME", "RefUrl": "/notes/1095397"}, {"RefNumber": "1095230", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "An error occurs during sorting in a very specific case", "RefUrl": "/notes/1095230"}, {"RefNumber": "1095140", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Relative time restriction does not work for 0CALWEEK", "RefUrl": "/notes/1095140"}, {"RefNumber": "1094989", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Wait time for RDA requests incorrect in the monitor", "RefUrl": "/notes/1094989"}, {"RefNumber": "1094948", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:RSMDATASTATE: Only aggregation check for InfoCubes", "RefUrl": "/notes/1094948"}, {"RefNumber": "1094902", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: <PERSON><PERSON><PERSON> before writing to PSA not sent to srce sys", "RefUrl": "/notes/1094902"}, {"RefNumber": "1094885", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error message BRAIN A258 with addition of null value", "RefUrl": "/notes/1094885"}, {"RefNumber": "1094884", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Long runtime in the function module RRSV_INT_CHA_VAL_SPLIT", "RefUrl": "/notes/1094884"}, {"RefNumber": "1094839", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:PSA: Sucessfully updated check during deletion", "RefUrl": "/notes/1094839"}, {"RefNumber": "1094810", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "A termination occurs when you try to create a single index", "RefUrl": "/notes/1094810"}, {"RefNumber": "1094736", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "No access to a running RDA InfoPackage", "RefUrl": "/notes/1094736"}, {"RefNumber": "1094591", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:STATMAN: Including time stamp field for fast access", "RefUrl": "/notes/1094591"}, {"RefNumber": "1094483", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRSDRC_SPLIT and form RETFL_N_SET-01-", "RefUrl": "/notes/1094483"}, {"RefNumber": "1094472", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Master data authorization: Not all values are displayed", "RefUrl": "/notes/1094472"}, {"RefNumber": "1094401", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Menu entries 'Back' and 'Cancel'", "RefUrl": "/notes/1094401"}, {"RefNumber": "1094400", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Logging missing if the status is unclear", "RefUrl": "/notes/1094400"}, {"RefNumber": "1094358", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving: Job cannot be repeated after error in deletion", "RefUrl": "/notes/1094358"}, {"RefNumber": "1094315", "RefComponent": "BW-BEX-ET-WEB-DIA", "RefTitle": "Multiple dialog does not remember the old values - part 3", "RefUrl": "/notes/1094315"}, {"RefNumber": "1094309", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "DB fallback triggered during BIA restart", "RefUrl": "/notes/1094309"}, {"RefNumber": "1094305", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW indexes with incorrect names (only NW 2004s)", "RefUrl": "/notes/1094305"}, {"RefNumber": "1094302", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Planning buttons displayed when you have no authorization", "RefUrl": "/notes/1094302"}, {"RefNumber": "1094292", "RefComponent": "BW-WHM-MTD", "RefTitle": "Reference to used style sheet is absolute", "RefUrl": "/notes/1094292"}, {"RefNumber": "1094100", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "NetWeaver BI Accelerator: Continuous improvements", "RefUrl": "/notes/1094100"}, {"RefNumber": "1094014", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P16:DTP:Dump occurs when you repair a terminated DTP request", "RefUrl": "/notes/1094014"}, {"RefNumber": "1094013", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Obsolete Debug_User in old RSADMIN table", "RefUrl": "/notes/1094013"}, {"RefNumber": "1093988", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Multilingual entries: Fragments in the incorrect language", "RefUrl": "/notes/1093988"}, {"RefNumber": "1093899", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA indexing performance for certain platforms", "RefUrl": "/notes/1093899"}, {"RefNumber": "1093893", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16) RSB_GUI 048 when you check an InfoSpoke", "RefUrl": "/notes/1093893"}, {"RefNumber": "1093808", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster with PDF, PS or PCL: Specifying margins", "RefUrl": "/notes/1093808"}, {"RefNumber": "1093755", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Statistics counter of OLAP cache not increased", "RefUrl": "/notes/1093755"}, {"RefNumber": "1093749", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Importing InfoPackages for several source systems", "RefUrl": "/notes/1093749"}, {"RefNumber": "1093737", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Decimal separator displayed incorrectly in char. values", "RefUrl": "/notes/1093737"}, {"RefNumber": "1093719", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA capacity utilization: Preventing overloading", "RefUrl": "/notes/1093719"}, {"RefNumber": "1093677", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRK0, form 'DELETE_FST-01-", "RefUrl": "/notes/1093677"}, {"RefNumber": "1093595", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:REQUDEL: Requests are deleted only in administration", "RefUrl": "/notes/1093595"}, {"RefNumber": "1093594", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Deleting complete contents for yellow requests", "RefUrl": "/notes/1093594"}, {"RefNumber": "1093522", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "System error in CL_RSR_RRK0_HIERARCHY APPLY_SLICER_NAV-02-", "RefUrl": "/notes/1093522"}, {"RefNumber": "1093481", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Statistics with ANALYZE after you create new partitions", "RefUrl": "/notes/1093481"}, {"RefNumber": "1093476", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in GET_FILTER_VALUES_CHA", "RefUrl": "/notes/1093476"}, {"RefNumber": "1093435", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data when executing query on non-cumulative InfoProvider", "RefUrl": "/notes/1093435"}, {"RefNumber": "1093422", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in CL_RSR_RRK0_PARTITION; NOTIFY_READ_REQUEST-01-", "RefUrl": "/notes/1093422"}, {"RefNumber": "1093292", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1093292"}, {"RefNumber": "1093146", "RefComponent": "BW-PLA-IP", "RefTitle": "Ready for input status and \"Calculate as...\"", "RefUrl": "/notes/1093146"}, {"RefNumber": "1093130", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Termination when you call a transformation", "RefUrl": "/notes/1093130"}, {"RefNumber": "1093121", "RefComponent": "BW-BCT-TCT", "RefTitle": "Negative times in BI statistics: Event 13052", "RefUrl": "/notes/1093121"}, {"RefNumber": "1093017", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA e-mail notification does not write to BAL", "RefUrl": "/notes/1093017"}, {"RefNumber": "1092986", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Schattenversion wird falsch erzeugt", "RefUrl": "/notes/1092986"}, {"RefNumber": "1092965", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CX_SY_DYNAMIC_OSQL_SYNTAX in CL_RSDM_READ_MASTER_DATA", "RefUrl": "/notes/1092965"}, {"RefNumber": "1092897", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "SP16:RSPC012 when trying to connect collector processes", "RefUrl": "/notes/1092897"}, {"RefNumber": "1092813", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Structure element text is not displayed", "RefUrl": "/notes/1092813"}, {"RefNumber": "1092641", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System does not filter according to problem class correctly", "RefUrl": "/notes/1092641"}, {"RefNumber": "1092607", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Invalid selection: All values are displayed", "RefUrl": "/notes/1092607"}, {"RefNumber": "1092558", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Close RDA requests before changing data structure", "RefUrl": "/notes/1092558"}, {"RefNumber": "1092539", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Master Data Lookup with prefetch", "RefUrl": "/notes/1092539"}, {"RefNumber": "1092510", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: You cannot call F4 help in InfoPackage", "RefUrl": "/notes/1092510"}, {"RefNumber": "1092376", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Variants are not transferred completely", "RefUrl": "/notes/1092376"}, {"RefNumber": "1092332", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "EXCEPTION cx_rs_input_invalid in CL_RSR_OLAP; CLOSE_LIST", "RefUrl": "/notes/1092332"}, {"RefNumber": "1092251", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: nicht behandelte Exception", "RefUrl": "/notes/1092251"}, {"RefNumber": "1092244", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: System transfers data rec warnings as info msgs", "RefUrl": "/notes/1092244"}, {"RefNumber": "1092135", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Formulas with an error are displayed with an X", "RefUrl": "/notes/1092135"}, {"RefNumber": "1092080", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Message: No SID found", "RefUrl": "/notes/1092080"}, {"RefNumber": "1092056", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Workbook precalculation: Scheduling during data change", "RefUrl": "/notes/1092056"}, {"RefNumber": "1092044", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Priorities of properties not evaluated correctly", "RefUrl": "/notes/1092044"}, {"RefNumber": "1091770", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Distribution across several channels - HTML format zipped", "RefUrl": "/notes/1091770"}, {"RefNumber": "1091714", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dynamic DATA table during reading of data", "RefUrl": "/notes/1091714"}, {"RefNumber": "1091690", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Delta index proposed", "RefUrl": "/notes/1091690"}, {"RefNumber": "1091524", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16) Missing impact bet. open hub and transformation", "RefUrl": "/notes/1091524"}, {"RefNumber": "1091456", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "PSA view for DataSource universe", "RefUrl": "/notes/1091456"}, {"RefNumber": "1091455", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in very rare cases", "RefUrl": "/notes/1091455"}, {"RefNumber": "1091436", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSADMIN parameter for reversing changes made by Note 1079359", "RefUrl": "/notes/1091436"}, {"RefNumber": "1091269", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Performance: Long runtime in LRSSBRF03, Form FILL_AUTH_BUFFE", "RefUrl": "/notes/1091269"}, {"RefNumber": "1091174", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Invalid restrictions accepted in variable screen", "RefUrl": "/notes/1091174"}, {"RefNumber": "1091019", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem: Replacing variables with variables (attribute)", "RefUrl": "/notes/1091019"}, {"RefNumber": "1091018", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination CL_RSR_RRK0_ATTR_C and form _SEL_TO_SELDR_14-02-", "RefUrl": "/notes/1091018"}, {"RefNumber": "1090952", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Request creation is locked too restictively", "RefUrl": "/notes/1090952"}, {"RefNumber": "1090935", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception CX_RS_INPUT_INVALID in SET_RETURNFLS", "RefUrl": "/notes/1090935"}, {"RefNumber": "1090871", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import of ISFS sets PSA to \"orphaned\"", "RefUrl": "/notes/1090871"}, {"RefNumber": "1090847", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "RSR_CACHE: Wait time too long during locking for main memory", "RefUrl": "/notes/1090847"}, {"RefNumber": "1090736", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "UNCAUGHT - CL_BICS_PROV_CONVERTER and form Missing text", "RefUrl": "/notes/1090736"}, {"RefNumber": "1090660", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Short dump OBJREF NOT ASSIGNED for new user", "RefUrl": "/notes/1090660"}, {"RefNumber": "1090514", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Improving performance of queries with two structures & hier.", "RefUrl": "/notes/1090514"}, {"RefNumber": "1090503", "RefComponent": "BW", "RefTitle": "Venezuelan currency conversion - Conversion in SAP BW", "RefUrl": "/notes/1090503"}, {"RefNumber": "1090490", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Buffering the MultiProvider runtime object", "RefUrl": "/notes/1090490"}, {"RefNumber": "1090488", "RefComponent": "BW-PLA-IP", "RefTitle": "Termination DMMAN 13; reading of delta buffer improved", "RefUrl": "/notes/1090488"}, {"RefNumber": "1090484", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "OBJECTS_OBJREF_NOT_ASSIGNED in CL_RS_TIME_SERVICE", "RefUrl": "/notes/1090484"}, {"RefNumber": "1090476", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Error message RSTRAN 502 and RSTRAN 503", "RefUrl": "/notes/1090476"}, {"RefNumber": "1090428", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Node variables: Compounded node values incorrect", "RefUrl": "/notes/1090428"}, {"RefNumber": "1090358", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:BATCH: Report RSBATCH_DEL_MSG_PARM_DTPTEMP terminates", "RefUrl": "/notes/1090358"}, {"RefNumber": "1090345", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not trigger Information Broadcasting", "RefUrl": "/notes/1090345"}, {"RefNumber": "1090281", "RefComponent": "BW-BEX-ET", "RefTitle": "Authorization not checked when you change global variants", "RefUrl": "/notes/1090281"}, {"RefNumber": "1090162", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA CCMS: Report results in STACK_STATE_NO_ROLL_MEMORY", "RefUrl": "/notes/1090162"}, {"RefNumber": "1090119", "RefComponent": "BW-BEX-OT", "RefTitle": "Table SELDR requires a large amount of memory space", "RefUrl": "/notes/1090119"}, {"RefNumber": "1090060", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Enhanced transformation check", "RefUrl": "/notes/1090060"}, {"RefNumber": "1089831", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enterprise Search: Performance when searching for queries", "RefUrl": "/notes/1089831"}, {"RefNumber": "1089777", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in a specific situation", "RefUrl": "/notes/1089777"}, {"RefNumber": "1089663", "RefComponent": "BW-WHM-AWB", "RefTitle": "P16: DWWB: Drag & drop terminates with meaningless message", "RefUrl": "/notes/1089663"}, {"RefNumber": "1089645", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Missing data for ACTUALDATA = '1' on transactional cube", "RefUrl": "/notes/1089645"}, {"RefNumber": "1089582", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect or no data in specific cases (after SP 15)", "RefUrl": "/notes/1089582"}, {"RefNumber": "1089545", "RefComponent": "BW-BEX-ET-BR", "RefTitle": "BEx Browser terminates immediately after being started", "RefUrl": "/notes/1089545"}, {"RefNumber": "1089525", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0 (SP16): No confirmation when open hub dest. checked", "RefUrl": "/notes/1089525"}, {"RefNumber": "1089524", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (Support Package 16) Selecting the source unit", "RefUrl": "/notes/1089524"}, {"RefNumber": "1089504", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in column or structural component", "RefUrl": "/notes/1089504"}, {"RefNumber": "1089492", "RefComponent": "BW-WHM-DST", "RefTitle": "Consulting: <PERSON><PERSON><PERSON> not automatically set/incorrect data", "RefUrl": "/notes/1089492"}, {"RefNumber": "1089491", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:R<PERSON><PERSON><PERSON><PERSON>:Report RSREQARCH_WRITE hangs in endless loop", "RefUrl": "/notes/1089491"}, {"RefNumber": "1089487", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Deleting a 3.x DataSource leaves 'orphaned' objects", "RefUrl": "/notes/1089487"}, {"RefNumber": "1089469", "RefComponent": "BW-BEX-ET", "RefTitle": "Improvements for report RSWB_ROLES_REORG", "RefUrl": "/notes/1089469"}, {"RefNumber": "1089438", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Data comparison database/BIA for flat fact index", "RefUrl": "/notes/1089438"}, {"RefNumber": "1089336", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16)/BI7.1(SPS04) Falsche Pseudo-D", "RefUrl": "/notes/1089336"}, {"RefNumber": "1089335", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Termination due to missing authorization for a role", "RefUrl": "/notes/1089335"}, {"RefNumber": "1089295", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables are hidden when you refresh", "RefUrl": "/notes/1089295"}, {"RefNumber": "1089231", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Master data provider", "RefUrl": "/notes/1089231"}, {"RefNumber": "1089224", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generation: Error with different DSO objects", "RefUrl": "/notes/1089224"}, {"RefNumber": "1089157", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:STATMAN:PSA:Performance improvement in PSA loading", "RefUrl": "/notes/1089157"}, {"RefNumber": "1089064", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:PSA:STATMAN:Slow access for RSREQDONE with enqueue", "RefUrl": "/notes/1089064"}, {"RefNumber": "1089041", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA InfoPackage does not delete delta queue", "RefUrl": "/notes/1089041"}, {"RefNumber": "1089022", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Error when you change transformation rules", "RefUrl": "/notes/1089022"}, {"RefNumber": "1088927", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P16:RDA:SDL: No active init: Incomprehensible error message", "RefUrl": "/notes/1088927"}, {"RefNumber": "1088836", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Netweaver BI 7.0 BEx Analyzer: Expanding hierarchy", "RefUrl": "/notes/1088836"}, {"RefNumber": "1088820", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Transport called during RSPC_API_CHAIN_INTERRUPT", "RefUrl": "/notes/1088820"}, {"RefNumber": "1088819", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: CX_SY_RANGE_OUT_OF_BOUNDS with negative amounts", "RefUrl": "/notes/1088819"}, {"RefNumber": "1088811", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA repartitioning: Names of the new partitions", "RefUrl": "/notes/1088811"}, {"RefNumber": "1088594", "RefComponent": "BW-WHM-AWB", "RefTitle": "RSINPUT: DYN_TABLE_ILL_COMP_VAL CX_SY_DYN_TABLE_ILL_COMP_VAL", "RefUrl": "/notes/1088594"}, {"RefNumber": "1088469", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Enhancement in exit \"virtual characteristics/key figures\"", "RefUrl": "/notes/1088469"}, {"RefNumber": "1088462", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination with MASTER_QUERY_RETURNNM-01-", "RefUrl": "/notes/1088462"}, {"RefNumber": "1088455", "RefComponent": "BW-WHM-AWB", "RefTitle": "DataSource: Performance when collecting BI objects", "RefUrl": "/notes/1088455"}, {"RefNumber": "1088365", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Transaction RSDV terminates", "RefUrl": "/notes/1088365"}, {"RefNumber": "1088354", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message BRAIN 206 for formula elements in structures", "RefUrl": "/notes/1088354"}, {"RefNumber": "1088342", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sum of the rounded values is not applied", "RefUrl": "/notes/1088342"}, {"RefNumber": "1088324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception in cl_rspls_delta_buffer_a - method check_locked", "RefUrl": "/notes/1088324"}, {"RefNumber": "1088307", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL: New DS: Hierarchy InfoPackages for new DataSource", "RefUrl": "/notes/1088307"}, {"RefNumber": "1088148", "RefComponent": "BW-BEX-MMR", "RefTitle": "ClassCastException for refPackage()", "RefUrl": "/notes/1088148"}, {"RefNumber": "1088098", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0(SP16) Dump when saving an expert routine", "RefUrl": "/notes/1088098"}, {"RefNumber": "1088079", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: MDC Fast Roll Out with DB2 9.5 or higher", "RefUrl": "/notes/1088079"}, {"RefNumber": "1088008", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "\"UPDATE_ERROR\" termination during change run", "RefUrl": "/notes/1088008"}, {"RefNumber": "1087783", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Code Changes for Real Time Statistics DB2 9.5", "RefUrl": "/notes/1087783"}, {"RefNumber": "1087754", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Text elements for variables in the BEx Analyzer", "RefUrl": "/notes/1087754"}, {"RefNumber": "1087740", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data in a specific situation", "RefUrl": "/notes/1087740"}, {"RefNumber": "1087714", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Error in authorization check when deleting chain", "RefUrl": "/notes/1087714"}, {"RefNumber": "1087635", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Optimization for writable PartProvider II", "RefUrl": "/notes/1087635"}, {"RefNumber": "1087619", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Request deletion and roll up and validity intervall", "RefUrl": "/notes/1087619"}, {"RefNumber": "1087527", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Performance of direct access: runtime object for transformation", "RefUrl": "/notes/1087527"}, {"RefNumber": "1087451", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Error RSM2 716 in data transfer process (DTP)", "RefUrl": "/notes/1087451"}, {"RefNumber": "1087429", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: RSSM_FILL_REQ_FOR_PSADEL: DUPRE<PERSON> dump RSTSODSREQUEST", "RefUrl": "/notes/1087429"}, {"RefNumber": "1087426", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:REQUDEL: Routines are not activated automatically", "RefUrl": "/notes/1087426"}, {"RefNumber": "1087398", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Initial SX_REPORT for !!1 query/filter/selection objects", "RefUrl": "/notes/1087398"}, {"RefNumber": "1087379", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 in SAPLRSTIME; Form RST_TOBJ_INFO-01-", "RefUrl": "/notes/1087379"}, {"RefNumber": "1087280", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message BRAIN 182 should not be issued", "RefUrl": "/notes/1087280"}, {"RefNumber": "1087258", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0(SP16) Deletion of transformation not transported", "RefUrl": "/notes/1087258"}, {"RefNumber": "1087254", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Dump when saving or activating in content", "RefUrl": "/notes/1087254"}, {"RefNumber": "1087250", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchy maintenance: Sequence when using F4 help to add", "RefUrl": "/notes/1087250"}, {"RefNumber": "1087067", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: References in decision formula", "RefUrl": "/notes/1087067"}, {"RefNumber": "1087034", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generation: >10 000 analysis auth's: Missing data records", "RefUrl": "/notes/1087034"}, {"RefNumber": "1087024", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "ABAP-Unit-Test: <PERSON><PERSON><PERSON><PERSON><PERSON> von 0AMOUNT und 0CURRENCY", "RefUrl": "/notes/1087024"}, {"RefNumber": "1086979", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Web Service, type of DATA parameter changes name", "RefUrl": "/notes/1086979"}, {"RefNumber": "1086877", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Error when activating dependent objects", "RefUrl": "/notes/1086877"}, {"RefNumber": "1086744", "RefComponent": "BW-BEX-OT", "RefTitle": "MultiProvider inconsistency BRAIN A152 or R7I 135", "RefUrl": "/notes/1086744"}, {"RefNumber": "1086710", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Current status of data is displayed as initial", "RefUrl": "/notes/1086710"}, {"RefNumber": "1086673", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "MODTIME not updated during query generation", "RefUrl": "/notes/1086673"}, {"RefNumber": "1086648", "RefComponent": "BW-WHM-AWB", "RefTitle": "P16:Old AWB: Search in AWB and PSA time selection too large", "RefUrl": "/notes/1086648"}, {"RefNumber": "1086645", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "Deadlock when you load master data in parallel", "RefUrl": "/notes/1086645"}, {"RefNumber": "1086610", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IP: Too few values for query with result access modes", "RefUrl": "/notes/1086610"}, {"RefNumber": "1086567", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Termination BRAIN 299 in CL_RSR_RRK0_CURR; FILL_CUDIM_02-05-", "RefUrl": "/notes/1086567"}, {"RefNumber": "1086483", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Result Access Mode, default in BEx Analyzer 2004s", "RefUrl": "/notes/1086483"}, {"RefNumber": "1086388", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Sequence of variables in the Web template", "RefUrl": "/notes/1086388"}, {"RefNumber": "1086339", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Metadatenmodell - Ausgabe", "RefUrl": "/notes/1086339"}, {"RefNumber": "1086332", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Long runtimes: Queries with many characteristics/attributes", "RefUrl": "/notes/1086332"}, {"RefNumber": "1086283", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Leaf is in hierarchy and also under \"Remaining Nodes\"", "RefUrl": "/notes/1086283"}, {"RefNumber": "1086256", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:P22:PC: Memory overflow dump in RSSM_PROCESS_CHECK_CHAIN", "RefUrl": "/notes/1086256"}, {"RefNumber": "1086229", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Web template code is not available after transport", "RefUrl": "/notes/1086229"}, {"RefNumber": "1086220", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "You cannot hide all the structure elements", "RefUrl": "/notes/1086220"}, {"RefNumber": "1086153", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Single rule simulation", "RefUrl": "/notes/1086153"}, {"RefNumber": "1086074", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Short dump in selective delete", "RefUrl": "/notes/1086074"}, {"RefNumber": "1085982", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Exit variables perform not run step 2", "RefUrl": "/notes/1085982"}, {"RefNumber": "1085934", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Dump and error message when you have fixed unit", "RefUrl": "/notes/1085934"}, {"RefNumber": "1085933", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search with several selections and ALPHA does not work", "RefUrl": "/notes/1085933"}, {"RefNumber": "1085923", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Index data display terminates for SID index", "RefUrl": "/notes/1085923"}, {"RefNumber": "1085914", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Restarting a change run results in error RSDD 85", "RefUrl": "/notes/1085914"}, {"RefNumber": "1085912", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem: Variable replacement from variable and compounding", "RefUrl": "/notes/1085912"}, {"RefNumber": "1085895", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Enhancement of Log messages for Reporting agent", "RefUrl": "/notes/1085895"}, {"RefNumber": "1085859", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting by value ignored for active hierarchy", "RefUrl": "/notes/1085859"}, {"RefNumber": "1085839", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A170 with compound char. & constant compounding parent", "RefUrl": "/notes/1085839"}, {"RefNumber": "1085836", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Performance problem during search in hierarchies", "RefUrl": "/notes/1085836"}, {"RefNumber": "1085822", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "BRAIN 629 for query with variable replacement from query", "RefUrl": "/notes/1085822"}, {"RefNumber": "1085820", "RefComponent": "BW-BEX-ET", "RefTitle": "BEx tools do not start if you log on in Arabic", "RefUrl": "/notes/1085820"}, {"RefNumber": "1085692", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IF_RSMD_RSH~GET_CHILDREN-03 in CL_RSMD_RSH", "RefUrl": "/notes/1085692"}, {"RefNumber": "1085673", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:P21:SDL:Incorrect selection check for routine", "RefUrl": "/notes/1085673"}, {"RefNumber": "1085469", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in hierarchies on MultiCubes and CHACONST", "RefUrl": "/notes/1085469"}, {"RefNumber": "1085447", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Navigation in Routinen", "RefUrl": "/notes/1085447"}, {"RefNumber": "1085398", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P16:DTP: Complete deletion of compressed InfoCubes", "RefUrl": "/notes/1085398"}, {"RefNumber": "1085397", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC: Complete deletion of contents does not wait", "RefUrl": "/notes/1085397"}, {"RefNumber": "1085394", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:RESET interface implemented in LOADING and PSAPROCESS", "RefUrl": "/notes/1085394"}, {"RefNumber": "1085358", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA check \"Random Queries\" creates great load", "RefUrl": "/notes/1085358"}, {"RefNumber": "1085318", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Rules disappear", "RefUrl": "/notes/1085318"}, {"RefNumber": "1085307", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Initial hierarchy date and new variable screen", "RefUrl": "/notes/1085307"}, {"RefNumber": "1085297", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Resetting chain runs automatically", "RefUrl": "/notes/1085297"}, {"RefNumber": "1085274", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1085274"}, {"RefNumber": "1085243", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Direct access performance: InfoObject metadata in DTP", "RefUrl": "/notes/1085243"}, {"RefNumber": "1085215", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Dump when you change to \"No conversion\"", "RefUrl": "/notes/1085215"}, {"RefNumber": "1085057", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Search function in field list", "RefUrl": "/notes/1085057"}, {"RefNumber": "1085026", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in program SAPLRRI2, form BEST_WGR-01-", "RefUrl": "/notes/1085026"}, {"RefNumber": "1084967", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:Delete change log has errors after transport", "RefUrl": "/notes/1084967"}, {"RefNumber": "1084965", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:PSA-deletion:Maintain instead of delete authorization", "RefUrl": "/notes/1084965"}, {"RefNumber": "1084934", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Hotfix for critical program error in BEx Analyzer", "RefUrl": "/notes/1084934"}, {"RefNumber": "1084887", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Technical name of PSA not displayed", "RefUrl": "/notes/1084887"}, {"RefNumber": "1084856", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "SAP NetWeaver BI Accelerator index is not used", "RefUrl": "/notes/1084856"}, {"RefNumber": "1084816", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error when reading virtual providers", "RefUrl": "/notes/1084816"}, {"RefNumber": "1084742", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculating with packed numbers and constants", "RefUrl": "/notes/1084742"}, {"RefNumber": "1084628", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Direct access performance: Meta data for DataSource universe", "RefUrl": "/notes/1084628"}, {"RefNumber": "1084602", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Performance of combination check in the input-ready query", "RefUrl": "/notes/1084602"}, {"RefNumber": "1084560", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA index consistency checks: specify restrictions", "RefUrl": "/notes/1084560"}, {"RefNumber": "1084478", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:DSO: Write-opt.: MOVE data cannot be loaded (or to cube)", "RefUrl": "/notes/1084478"}, {"RefNumber": "1084458", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Error messages during saving in update task", "RefUrl": "/notes/1084458"}, {"RefNumber": "1084422", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Dump when you transfer invalid LOGID in API", "RefUrl": "/notes/1084422"}, {"RefNumber": "1084418", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Query with hierarchy that reads data from BIA terminates", "RefUrl": "/notes/1084418"}, {"RefNumber": "1084334", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Old variable screen: Input help for hierarchy unexpected", "RefUrl": "/notes/1084334"}, {"RefNumber": "1084328", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Assigning InfoSources - content option displayed", "RefUrl": "/notes/1084328"}, {"RefNumber": "1084219", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Cube WITHOUT fact table but with BIA storage ONLY", "RefUrl": "/notes/1084219"}, {"RefNumber": "1084217", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect data in BIA after initial indexing", "RefUrl": "/notes/1084217"}, {"RefNumber": "1083912", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Dump when maintaining an open hub destination", "RefUrl": "/notes/1083912"}, {"RefNumber": "1083879", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "DB fallback for BIA activated, during index check", "RefUrl": "/notes/1083879"}, {"RefNumber": "1083764", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DataStore object with display attributes cannot be archived", "RefUrl": "/notes/1083764"}, {"RefNumber": "1083745", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA time stamp is not updated after you roll up", "RefUrl": "/notes/1083745"}, {"RefNumber": "1083740", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL: Entry of leading zeros in INT4 fields", "RefUrl": "/notes/1083740"}, {"RefNumber": "1083738", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: TSV_TNEW_PAGE_ALLOC_FAILED for extractor 0TCTREQUID", "RefUrl": "/notes/1083738"}, {"RefNumber": "1083660", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Report for error stack analysis", "RefUrl": "/notes/1083660"}, {"RefNumber": "1083658", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Dialog boxes when batch authorization is missing", "RefUrl": "/notes/1083658"}, {"RefNumber": "1083561", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Protokollierung beim <PERSON>", "RefUrl": "/notes/1083561"}, {"RefNumber": "1083513", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Authority check for Filter deletion fails", "RefUrl": "/notes/1083513"}, {"RefNumber": "1083486", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump during activation of a transformation (content)", "RefUrl": "/notes/1083486"}, {"RefNumber": "1083423", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Workbook cannot be saved", "RefUrl": "/notes/1083423"}, {"RefNumber": "1083390", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Locking failure during master data/texts upload or deletion", "RefUrl": "/notes/1083390"}, {"RefNumber": "1083328", "RefComponent": "BW", "RefTitle": "Prerequisite note", "RefUrl": "/notes/1083328"}, {"RefNumber": "1083305", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:STATMAN:Dump in function module RSM_INFOCUBE_WRITE_CHECK", "RefUrl": "/notes/1083305"}, {"RefNumber": "1083285", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Field KBYTES in table RSDDSTATTREXSERV is incorrect", "RefUrl": "/notes/1083285"}, {"RefNumber": "1083278", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Authorization check during execution", "RefUrl": "/notes/1083278"}, {"RefNumber": "1083267", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1083267"}, {"RefNumber": "1083266", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rule type for reading master data for open hub destination", "RefUrl": "/notes/1083266"}, {"RefNumber": "1083265", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems occur during the transport of transformations", "RefUrl": "/notes/1083265"}, {"RefNumber": "1083135", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination in transaction RRMX", "RefUrl": "/notes/1083135"}, {"RefNumber": "1083103", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Error in AND process after restart", "RefUrl": "/notes/1083103"}, {"RefNumber": "1082968", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA monitor deletes released job", "RefUrl": "/notes/1082968"}, {"RefNumber": "1082888", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you execute planning functions in the Analyzer", "RefUrl": "/notes/1082888"}, {"RefNumber": "1082605", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination BRAIN 299 in SAPLRRK0; LRECH_AGGR_BNR-02-", "RefUrl": "/notes/1082605"}, {"RefNumber": "1082499", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Asynchronous confirmation in QM action", "RefUrl": "/notes/1082499"}, {"RefNumber": "1082498", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "MDX: No data with fixed node filter and drilldown", "RefUrl": "/notes/1082498"}, {"RefNumber": "1082468", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Error with RSRV BIA sums check with non-cumulative key figs", "RefUrl": "/notes/1082468"}, {"RefNumber": "1082157", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination in Analyzer when you create system messages", "RefUrl": "/notes/1082157"}, {"RefNumber": "1081884", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Short dump during content activation", "RefUrl": "/notes/1081884"}, {"RefNumber": "1081856", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when entering plan values", "RefUrl": "/notes/1081856"}, {"RefNumber": "1081827", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Abort in function modules RSDU_CHKREP_PKEY*", "RefUrl": "/notes/1081827"}, {"RefNumber": "1081608", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "WAD & Meta data download on J2EE after upgrade to SPS >= 11", "RefUrl": "/notes/1081608"}, {"RefNumber": "1081534", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:P21:SDL:F4 on AL11 list truncates names after 30 chars.", "RefUrl": "/notes/1081534"}, {"RefNumber": "1081453", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error in old RSSM log in BI 7.0", "RefUrl": "/notes/1081453"}, {"RefNumber": "1081122", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_OBJREF_NOT_ASSIGNED_NO", "RefUrl": "/notes/1081122"}, {"RefNumber": "1081066", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Proposal creates new PSA", "RefUrl": "/notes/1081066"}, {"RefNumber": "1081026", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor proposes action \"Rebuild BIA Indexes\"", "RefUrl": "/notes/1081026"}, {"RefNumber": "1080822", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: RSPLS 131 when creating aggregation level", "RefUrl": "/notes/1080822"}, {"RefNumber": "1080725", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:P21:SDL:InfoPackage from content transfer/job scheduling", "RefUrl": "/notes/1080725"}, {"RefNumber": "1080701", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination 299 in SAPLRRI2, SELECTION_WITH_COMPSEL_GET-2-", "RefUrl": "/notes/1080701"}, {"RefNumber": "1080672", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Mehrere BIA-Indizes aktivieren und füllen", "RefUrl": "/notes/1080672"}, {"RefNumber": "1080661", "RefComponent": "BW-BEX-OT", "RefTitle": "Messages are not displayed", "RefUrl": "/notes/1080661"}, {"RefNumber": "1080558", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Module RSPC_API_CHAIN_GET_RUNS does not exist", "RefUrl": "/notes/1080558"}, {"RefNumber": "1080556", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Selection filled although it is empty", "RefUrl": "/notes/1080556"}, {"RefNumber": "1080551", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: No call trace for asynchronous call", "RefUrl": "/notes/1080551"}, {"RefNumber": "1080458", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable in default value and dependencies in F4", "RefUrl": "/notes/1080458"}, {"RefNumber": "1080453", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination w/node variable in Java Web variable value", "RefUrl": "/notes/1080453"}, {"RefNumber": "1080449", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Old long text function also for local messages", "RefUrl": "/notes/1080449"}, {"RefNumber": "1080438", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Currency field/unit field appear changeable", "RefUrl": "/notes/1080438"}, {"RefNumber": "1080434", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Exception not triggered when method is called", "RefUrl": "/notes/1080434"}, {"RefNumber": "1080379", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BW 70 BEx: RRMX<PERSON> causes dump in RSAH_ENSURE_GUI_VISIBLE", "RefUrl": "/notes/1080379"}, {"RefNumber": "1080251", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "EIBV with two different hierarchies", "RefUrl": "/notes/1080251"}, {"RefNumber": "1080241", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Improvement of usability in Support Package 16", "RefUrl": "/notes/1080241"}, {"RefNumber": "1080200", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Error for negative default value of a formula variable", "RefUrl": "/notes/1080200"}, {"RefNumber": "1080197", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Rollup of a transactional InfoCube terminates", "RefUrl": "/notes/1080197"}, {"RefNumber": "1080181", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during transformation of reverse images", "RefUrl": "/notes/1080181"}, {"RefNumber": "1080019", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Web Service DataSource: Error message RSDS 305 for transport", "RefUrl": "/notes/1080019"}, {"RefNumber": "1080014", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Transport of DataSources and transformations", "RefUrl": "/notes/1080014"}, {"RefNumber": "1079956", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Incorrect number of decimal places in new formula elements", "RefUrl": "/notes/1079956"}, {"RefNumber": "1079726", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL:New DS: No selections for init extraction from DSO", "RefUrl": "/notes/1079726"}, {"RefNumber": "1079489", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you drilldown across worksheets", "RefUrl": "/notes/1079489"}, {"RefNumber": "1079448", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a very special situation", "RefUrl": "/notes/1079448"}, {"RefNumber": "1079447", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN CL_RSR_FIPT_TRANSFORMATOR; GET_MAPPING-01-", "RefUrl": "/notes/1079447"}, {"RefNumber": "1079222", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Filter value list for structures is sorted incorrectly", "RefUrl": "/notes/1079222"}, {"RefNumber": "1079193", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:STATMAN: Several parallel requests and enqueue problem", "RefUrl": "/notes/1079193"}, {"RefNumber": "1079186", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you filter by characteristic values", "RefUrl": "/notes/1079186"}, {"RefNumber": "1079183", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BW 70 BEx: Suppressing transport dialog box for active CTO", "RefUrl": "/notes/1079183"}, {"RefNumber": "1079068", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 17 BIA Revision of compatibility check", "RefUrl": "/notes/1079068"}, {"RefNumber": "1079040", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Assignment of DTP to RDA daemon is missing", "RefUrl": "/notes/1079040"}, {"RefNumber": "1078719", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Direct access performance:No RFC during extraction in Myself", "RefUrl": "/notes/1078719"}, {"RefNumber": "1078527", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Request deletion with error RSSTATMAN 102 in row 34", "RefUrl": "/notes/1078527"}, {"RefNumber": "1078524", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:REBUILD:Reconstruction of empty reqs w/o data turns RED", "RefUrl": "/notes/1078524"}, {"RefNumber": "1078508", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Database table of open hub destination incorrect", "RefUrl": "/notes/1078508"}, {"RefNumber": "1078500", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in CL_RSR_CHABIT; form SET_BIT1-01-", "RefUrl": "/notes/1078500"}, {"RefNumber": "1078461", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Displaying the query results when you run traces", "RefUrl": "/notes/1078461"}, {"RefNumber": "1078192", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL: Allow ACCP fields for selection", "RefUrl": "/notes/1078192"}, {"RefNumber": "1078188", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Jump ALV->BEX: Termination CREATE_COBPRO_FROM_DDFIELD-02-", "RefUrl": "/notes/1078188"}, {"RefNumber": "1078155", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Error in structure proposal for files", "RefUrl": "/notes/1078155"}, {"RefNumber": "1078151", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Record number unclear w/error in structure move", "RefUrl": "/notes/1078151"}, {"RefNumber": "1078105", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Attributes in the input help for variables are not displayed", "RefUrl": "/notes/1078105"}, {"RefNumber": "1077916", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A PartProvider returns no data", "RefUrl": "/notes/1077916"}, {"RefNumber": "1077906", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Scheduling RDA daemon with a different job class", "RefUrl": "/notes/1077906"}, {"RefNumber": "1077774", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Fehler ohne eindeutiges Symptom", "RefUrl": "/notes/1077774"}, {"RefNumber": "1077682", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "%RT(x), %GT(x) or %CT(x) displays values less than 0.5% as 0", "RefUrl": "/notes/1077682"}, {"RefNumber": "1077655", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource is inactive when loaded (or after replication)", "RefUrl": "/notes/1077655"}, {"RefNumber": "1077601", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA 140 in line 46 of CL_RSDA_DAP_A=================CM00Z", "RefUrl": "/notes/1077601"}, {"RefNumber": "1077562", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message BRAIN 633 when you check variables", "RefUrl": "/notes/1077562"}, {"RefNumber": "1077472", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "MESSAGE_TYPE_X in CL_RSCRT_RDA_DELTA_CHECK", "RefUrl": "/notes/1077472"}, {"RefNumber": "1077439", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1077439"}, {"RefNumber": "1077412", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "A constant is not treated correctly", "RefUrl": "/notes/1077412"}, {"RefNumber": "1077397", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Problems with SP table being used several times", "RefUrl": "/notes/1077397"}, {"RefNumber": "1077394", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:REQARCH: Resetting archive and requests after termination", "RefUrl": "/notes/1077394"}, {"RefNumber": "1077372", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing data in a special situation", "RefUrl": "/notes/1077372"}, {"RefNumber": "1077308", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "0FISCVARNT treated as a key in write-optimized DataStore", "RefUrl": "/notes/1077308"}, {"RefNumber": "1077147", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: 'GET_SID-1-' in program SAPLRRS2", "RefUrl": "/notes/1077147"}, {"RefNumber": "1077144", "RefComponent": "BW-PLA-IP", "RefTitle": "E message: \"Please enter a valid value for characteristic &\"", "RefUrl": "/notes/1077144"}, {"RefNumber": "1077127", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Process types for starting and ending an RDA", "RefUrl": "/notes/1077127"}, {"RefNumber": "1077125", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Optimization when you execute BEx Web (ABAP) for first time", "RefUrl": "/notes/1077125"}, {"RefNumber": "1077061", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Check when reading master data", "RefUrl": "/notes/1077061"}, {"RefNumber": "1076972", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: PSA index in DataSource does not contain request", "RefUrl": "/notes/1076972"}, {"RefNumber": "1076836", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Missing rollback for X index and Y index", "RefUrl": "/notes/1076836"}, {"RefNumber": "1076796", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error CREATE_DATA_UNKNOWN_TYPE during transport", "RefUrl": "/notes/1076796"}, {"RefNumber": "1076688", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Drill down to 0INFOPROV in the key figure definition", "RefUrl": "/notes/1076688"}, {"RefNumber": "1076664", "RefComponent": "BW-WHM-DST", "RefTitle": "Program for controlled system shutdown", "RefUrl": "/notes/1076664"}, {"RefNumber": "1076301", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No values when you expand a hierarchy node", "RefUrl": "/notes/1076301"}, {"RefNumber": "1076291", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RECORD_NO and RECORD_ALL not filled", "RefUrl": "/notes/1076291"}, {"RefNumber": "1076113", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Roll up in BIA terminates if there is an empty request", "RefUrl": "/notes/1076113"}, {"RefNumber": "1075964", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Selecting filter value for temporal hierarchy join", "RefUrl": "/notes/1075964"}, {"RefNumber": "1075922", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Resetting kill not possible", "RefUrl": "/notes/1075922"}, {"RefNumber": "1075879", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "\"Not assigned\" selection not authorized with nodes", "RefUrl": "/notes/1075879"}, {"RefNumber": "1075807", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "New button for user variants in the 3.X variable dialog", "RefUrl": "/notes/1075807"}, {"RefNumber": "1075789", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Displaying BEx Web Application 7.0 in role menu 3.x", "RefUrl": "/notes/1075789"}, {"RefNumber": "1075728", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Module RSNDI_SHIE_SUBTREE_DELETE: Deleting level texts", "RefUrl": "/notes/1075728"}, {"RefNumber": "1075584", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Improve performance of data load", "RefUrl": "/notes/1075584"}, {"RefNumber": "1075579", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 16 BIA revision of compatability check", "RefUrl": "/notes/1075579"}, {"RefNumber": "1075477", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Copying a transformation", "RefUrl": "/notes/1075477"}, {"RefNumber": "1075470", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when maintaining conversion types (unit and currency)", "RefUrl": "/notes/1075470"}, {"RefNumber": "1075469", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error in TLOGO framework implementation (get_prop)", "RefUrl": "/notes/1075469"}, {"RefNumber": "1075466", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message - Status message when you call a transformtion", "RefUrl": "/notes/1075466"}, {"RefNumber": "1075365", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Raise exception type cx_rs_step_failed in transformation", "RefUrl": "/notes/1075365"}, {"RefNumber": "1075262", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Enhanced proposal mapping for transformation", "RefUrl": "/notes/1075262"}, {"RefNumber": "1075214", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generating analysis authorizations: Email address is missing", "RefUrl": "/notes/1075214"}, {"RefNumber": "1075149", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump ASSIGN_TYPE_CONFLICT for Open Hub Destination", "RefUrl": "/notes/1075149"}, {"RefNumber": "1075147", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Checking source and target when building the factory", "RefUrl": "/notes/1075147"}, {"RefNumber": "1075125", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unauthorized data displayed when structure element expanded", "RefUrl": "/notes/1075125"}, {"RefNumber": "1075080", "RefComponent": "BW-BEX-OT", "RefTitle": "Classification of messages", "RefUrl": "/notes/1075080"}, {"RefNumber": "1075041", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction:General message issued for error in direct access", "RefUrl": "/notes/1075041"}, {"RefNumber": "1074997", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Migration of Web templates to BI 7.0: User-defined items", "RefUrl": "/notes/1074997"}, {"RefNumber": "1074953", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "FEMS, BIA optimization", "RefUrl": "/notes/1074953"}, {"RefNumber": "1074850", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: File name routine is not saved", "RefUrl": "/notes/1074850"}, {"RefNumber": "1074844", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND processes inactive after content transfer", "RefUrl": "/notes/1074844"}, {"RefNumber": "1074803", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help does not display any values in variable screen", "RefUrl": "/notes/1074803"}, {"RefNumber": "1074769", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Despite warning Brain 053, IBV is not switched off correctly", "RefUrl": "/notes/1074769"}, {"RefNumber": "1074767", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1067984", "RefUrl": "/notes/1074767"}, {"RefNumber": "1074754", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: reserved field names, no field names", "RefUrl": "/notes/1074754"}, {"RefNumber": "1074569", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "SAP NetWeaver BI Accelerator COMMIT logic in change run", "RefUrl": "/notes/1074569"}, {"RefNumber": "1074558", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_HIERARCHY; SEL_TO_SELDR_EQSID-01-", "RefUrl": "/notes/1074558"}, {"RefNumber": "1074492", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Technical group for write-optimized DataStore objects", "RefUrl": "/notes/1074492"}, {"RefNumber": "1072622", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP16:Multiple record PSA Maintenance for Numerical Fields", "RefUrl": "/notes/1072622"}, {"RefNumber": "1070967", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Exception with \"Calculation after list calculation\" incorr.", "RefUrl": "/notes/1070967"}, {"RefNumber": "1069288", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Values sorted incorrectly in results list and input help", "RefUrl": "/notes/1069288"}, {"RefNumber": "1067918", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CL_RSBOLAP_BICS_SERVICES, HANDLE_UNCAUGHT_EXCEPTION", "RefUrl": "/notes/1067918"}, {"RefNumber": "1048163", "RefComponent": "BW-BEX-ET", "RefTitle": "BEx filter cannot be found using the input help", "RefUrl": "/notes/1048163"}, {"RefNumber": "1046558", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization: Checking for 0DATE", "RefUrl": "/notes/1046558"}, {"RefNumber": "1041822", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Optimizing the \"Change run monitor\"", "RefUrl": "/notes/1041822"}, {"RefNumber": "1039009", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "QDEF: Additional authorization check during SAVE", "RefUrl": "/notes/1039009"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1088079", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: MDC Fast Roll Out with DB2 9.5 or higher", "RefUrl": "/notes/1088079 "}, {"RefNumber": "1087783", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Code Changes for Real Time Statistics DB2 9.5", "RefUrl": "/notes/1087783 "}, {"RefNumber": "1093719", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA capacity utilization: Preventing overloading", "RefUrl": "/notes/1093719 "}, {"RefNumber": "1075584", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Improve performance of data load", "RefUrl": "/notes/1075584 "}, {"RefNumber": "1094305", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW indexes with incorrect names (only NW 2004s)", "RefUrl": "/notes/1094305 "}, {"RefNumber": "1096307", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "MD Update when only keys fields are mapped", "RefUrl": "/notes/1096307 "}, {"RefNumber": "1091524", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16) Missing impact bet. open hub and transformation", "RefUrl": "/notes/1091524 "}, {"RefNumber": "1097529", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "DB fallback for BIA: Change wait time", "RefUrl": "/notes/1097529 "}, {"RefNumber": "1100630", "RefComponent": "BW-WHM-DBA-SDEL", "RefTitle": "Follow-up note for Note 1077415", "RefUrl": "/notes/1100630 "}, {"RefNumber": "1102587", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Correction of failure in Aggregate Check Tool", "RefUrl": "/notes/1102587 "}, {"RefNumber": "1077394", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:REQARCH: Resetting archive and requests after termination", "RefUrl": "/notes/1077394 "}, {"RefNumber": "1102676", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SAP_USER_EXTENTS/SAP_USER_SEGMENTS do not exist in RSRV", "RefUrl": "/notes/1102676 "}, {"RefNumber": "1081066", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Proposal creates new PSA", "RefUrl": "/notes/1081066 "}, {"RefNumber": "1102123", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data when you use excluding and including sel.", "RefUrl": "/notes/1102123 "}, {"RefNumber": "1106393", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "SAP_DROP_TMPTABLES do not drop RSDD_TMPNM_ADM entries", "RefUrl": "/notes/1106393 "}, {"RefNumber": "1103425", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: RSPC 006 if follow-on job is suspended", "RefUrl": "/notes/1103425 "}, {"RefNumber": "1089545", "RefComponent": "BW-BEX-ET-BR", "RefTitle": "BEx Browser terminates immediately after being started", "RefUrl": "/notes/1089545 "}, {"RefNumber": "1273778", "RefComponent": "BW", "RefTitle": "Currency conversion from SSK into Euro in SAP BW", "RefUrl": "/notes/1273778 "}, {"RefNumber": "1089777", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in a specific situation", "RefUrl": "/notes/1089777 "}, {"RefNumber": "1104014", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exporting data from ALV -> BEx: Termination DEFINE_BEXQUERY", "RefUrl": "/notes/1104014 "}, {"RefNumber": "1092897", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "SP16:RSPC012 when trying to connect collector processes", "RefUrl": "/notes/1092897 "}, {"RefNumber": "1079186", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you filter by characteristic values", "RefUrl": "/notes/1079186 "}, {"RefNumber": "1089231", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Master data provider", "RefUrl": "/notes/1089231 "}, {"RefNumber": "1086744", "RefComponent": "BW-BEX-OT", "RefTitle": "MultiProvider inconsistency BRAIN A152 or R7I 135", "RefUrl": "/notes/1086744 "}, {"RefNumber": "1085469", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in hierarchies on MultiCubes and CHACONST", "RefUrl": "/notes/1085469 "}, {"RefNumber": "1098905", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Hierarchy node variable value not retained when invoking F4", "RefUrl": "/notes/1098905 "}, {"RefNumber": "1090488", "RefComponent": "BW-PLA-IP", "RefTitle": "Termination DMMAN 13; reading of delta buffer improved", "RefUrl": "/notes/1090488 "}, {"RefNumber": "1145972", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "No data for non-cumulative key figures & virtual key figures", "RefUrl": "/notes/1145972 "}, {"RefNumber": "1096771", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Combined DTP extraction from active table and archive", "RefUrl": "/notes/1096771 "}, {"RefNumber": "1083764", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DataStore object with display attributes cannot be archived", "RefUrl": "/notes/1083764 "}, {"RefNumber": "1113121", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Duplicated data when delta extracted frm write-opt DataStore", "RefUrl": "/notes/1113121 "}, {"RefNumber": "1067918", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CL_RSBOLAP_BICS_SERVICES, HANDLE_UNCAUGHT_EXCEPTION", "RefUrl": "/notes/1067918 "}, {"RefNumber": "1105498", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: 'GET_SID-1-' in program SAPLRRS2", "RefUrl": "/notes/1105498 "}, {"RefNumber": "1095230", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "An error occurs during sorting in a very specific case", "RefUrl": "/notes/1095230 "}, {"RefNumber": "1089645", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Missing data for ACTUALDATA = '1' on transactional cube", "RefUrl": "/notes/1089645 "}, {"RefNumber": "1101726", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Changing the active filter of planning functions", "RefUrl": "/notes/1101726 "}, {"RefNumber": "1091714", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dynamic DATA table during reading of data", "RefUrl": "/notes/1091714 "}, {"RefNumber": "1101589", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Start/end routine; InfoCube; time reference", "RefUrl": "/notes/1101589 "}, {"RefNumber": "1080019", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Web Service DataSource: Error message RSDS 305 for transport", "RefUrl": "/notes/1080019 "}, {"RefNumber": "1090871", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import of ISFS sets PSA to \"orphaned\"", "RefUrl": "/notes/1090871 "}, {"RefNumber": "1086979", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Web Service, type of DATA parameter changes name", "RefUrl": "/notes/1086979 "}, {"RefNumber": "1102486", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "Creating transfer rules w/o InfoObject assignment for BAPIs", "RefUrl": "/notes/1102486 "}, {"RefNumber": "1101187", "RefComponent": "BW-PLA-IP", "RefTitle": "The OLAP tunnel", "RefUrl": "/notes/1101187 "}, {"RefNumber": "1093749", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Importing InfoPackages for several source systems", "RefUrl": "/notes/1093749 "}, {"RefNumber": "1101933", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Termin. MOVE_TO_LIT_NOTALLOWED_NODAT: <l_sx_nkyfnm>-aggrexc", "RefUrl": "/notes/1101933 "}, {"RefNumber": "1086645", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "Deadlock when you load master data in parallel", "RefUrl": "/notes/1086645 "}, {"RefNumber": "1088469", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Enhancement in exit \"virtual characteristics/key figures\"", "RefUrl": "/notes/1088469 "}, {"RefNumber": "1106363", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Repeat is not reported", "RefUrl": "/notes/1106363 "}, {"RefNumber": "1084628", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Direct access performance: Meta data for DataSource universe", "RefUrl": "/notes/1084628 "}, {"RefNumber": "1116189", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Cannot switch to transaction in Web", "RefUrl": "/notes/1116189 "}, {"RefNumber": "1079068", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 17 BIA Revision of compatibility check", "RefUrl": "/notes/1079068 "}, {"RefNumber": "1145108", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Brain A264, A320 or A348 when you generate a query", "RefUrl": "/notes/1145108 "}, {"RefNumber": "1090514", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Improving performance of queries with two structures & hier.", "RefUrl": "/notes/1090514 "}, {"RefNumber": "1074569", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "SAP NetWeaver BI Accelerator COMMIT logic in change run", "RefUrl": "/notes/1074569 "}, {"RefNumber": "1079489", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you drilldown across worksheets", "RefUrl": "/notes/1079489 "}, {"RefNumber": "1093435", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data when executing query on non-cumulative InfoProvider", "RefUrl": "/notes/1093435 "}, {"RefNumber": "1097674", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "NW2004s: Administration of backup object version", "RefUrl": "/notes/1097674 "}, {"RefNumber": "1087024", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "ABAP-Unit-Test: <PERSON><PERSON><PERSON><PERSON><PERSON> von 0AMOUNT und 0CURRENCY", "RefUrl": "/notes/1087024 "}, {"RefNumber": "1090490", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Buffering the MultiProvider runtime object", "RefUrl": "/notes/1090490 "}, {"RefNumber": "1088811", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA repartitioning: Names of the new partitions", "RefUrl": "/notes/1088811 "}, {"RefNumber": "1085836", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Performance problem during search in hierarchies", "RefUrl": "/notes/1085836 "}, {"RefNumber": "1078461", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Displaying the query results when you run traces", "RefUrl": "/notes/1078461 "}, {"RefNumber": "1074997", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Migration of Web templates to BI 7.0: User-defined items", "RefUrl": "/notes/1074997 "}, {"RefNumber": "1100006", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Query view delivers an incorrect navigation status", "RefUrl": "/notes/1100006 "}, {"RefNumber": "1104151", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error during activation", "RefUrl": "/notes/1104151 "}, {"RefNumber": "1085394", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:RESET interface implemented in LOADING and PSAPROCESS", "RefUrl": "/notes/1085394 "}, {"RefNumber": "1085297", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Resetting chain runs automatically", "RefUrl": "/notes/1085297 "}, {"RefNumber": "1094885", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error message BRAIN A258 with addition of null value", "RefUrl": "/notes/1094885 "}, {"RefNumber": "1078500", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in CL_RSR_CHABIT; form SET_BIT1-01-", "RefUrl": "/notes/1078500 "}, {"RefNumber": "1089831", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enterprise Search: Performance when searching for queries", "RefUrl": "/notes/1089831 "}, {"RefNumber": "1083423", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Workbook cannot be saved", "RefUrl": "/notes/1083423 "}, {"RefNumber": "1087398", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Initial SX_REPORT for !!1 query/filter/selection objects", "RefUrl": "/notes/1087398 "}, {"RefNumber": "1105139", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Filling the OLAP cache with \"contains pattern\" in BI 7.0", "RefUrl": "/notes/1105139 "}, {"RefNumber": "1101846", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process chain status as a message", "RefUrl": "/notes/1101846 "}, {"RefNumber": "1078524", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:REBUILD:Reconstruction of empty reqs w/o data turns RED", "RefUrl": "/notes/1078524 "}, {"RefNumber": "1110997", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1090490", "RefUrl": "/notes/1110997 "}, {"RefNumber": "1104367", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No success message when you successfully activate a DAP", "RefUrl": "/notes/1104367 "}, {"RefNumber": "1084742", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculating with packed numbers and constants", "RefUrl": "/notes/1084742 "}, {"RefNumber": "1094292", "RefComponent": "BW-WHM-MTD", "RefTitle": "Reference to used style sheet is absolute", "RefUrl": "/notes/1094292 "}, {"RefNumber": "1089022", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Error when you change transformation rules", "RefUrl": "/notes/1089022 "}, {"RefNumber": "1088365", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Transaction RSDV terminates", "RefUrl": "/notes/1088365 "}, {"RefNumber": "1087619", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Request deletion and roll up and validity intervall", "RefUrl": "/notes/1087619 "}, {"RefNumber": "1084219", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Cube WITHOUT fact table but with BIA storage ONLY", "RefUrl": "/notes/1084219 "}, {"RefNumber": "1144874", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSD_MULTIPROV_CHECK; _CHECK_CMP_MESS-01-", "RefUrl": "/notes/1144874 "}, {"RefNumber": "1077397", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Problems with SP table being used several times", "RefUrl": "/notes/1077397 "}, {"RefNumber": "1098734", "RefComponent": "BW-BEX-OT", "RefTitle": "Subs. corr. to 1090490; BRAIN A338 0INFOPROV does not exist", "RefUrl": "/notes/1098734 "}, {"RefNumber": "1100457", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correcting stop due to missing job", "RefUrl": "/notes/1100457 "}, {"RefNumber": "1075466", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message - Status message when you call a transformtion", "RefUrl": "/notes/1075466 "}, {"RefNumber": "1075579", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 16 BIA revision of compatability check", "RefUrl": "/notes/1075579 "}, {"RefNumber": "1103912", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "3.x Archiving: Termination with RAISE_EXCEPTION in SAPLRSDRI", "RefUrl": "/notes/1103912 "}, {"RefNumber": "1085318", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Rules disappear", "RefUrl": "/notes/1085318 "}, {"RefNumber": "1106058", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation end routine - ALL_FIELDS does not work", "RefUrl": "/notes/1106058 "}, {"RefNumber": "1095721", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination in program SAPLRRK0 WORKCELL_SET-01-", "RefUrl": "/notes/1095721 "}, {"RefNumber": "1081884", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Short dump during content activation", "RefUrl": "/notes/1081884 "}, {"RefNumber": "1075477", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Copying a transformation", "RefUrl": "/notes/1075477 "}, {"RefNumber": "1103316", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "New master data value is generated", "RefUrl": "/notes/1103316 "}, {"RefNumber": "1084478", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:DSO: Write-opt.: MOVE data cannot be loaded (or to cube)", "RefUrl": "/notes/1084478 "}, {"RefNumber": "1138199", "RefComponent": "BW-BEX-OT", "RefTitle": "X299 BRAIN in CL_RSD_MULTIPROV; form GET_PART_IOBJNM-01-", "RefUrl": "/notes/1138199 "}, {"RefNumber": "1089582", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect or no data in specific cases (after SP 15)", "RefUrl": "/notes/1089582 "}, {"RefNumber": "1098095", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Correction:Field texts in primary language (ABAP Dict) blank", "RefUrl": "/notes/1098095 "}, {"RefNumber": "1092539", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Master Data Lookup with prefetch", "RefUrl": "/notes/1092539 "}, {"RefNumber": "1094358", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving: Job cannot be repeated after error in deletion", "RefUrl": "/notes/1094358 "}, {"RefNumber": "1076291", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RECORD_NO and RECORD_ALL not filled", "RefUrl": "/notes/1076291 "}, {"RefNumber": "1092376", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Variants are not transferred completely", "RefUrl": "/notes/1092376 "}, {"RefNumber": "1089295", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables are hidden when you refresh", "RefUrl": "/notes/1089295 "}, {"RefNumber": "1092332", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "EXCEPTION cx_rs_input_invalid in CL_RSR_OLAP; CLOSE_LIST", "RefUrl": "/notes/1092332 "}, {"RefNumber": "1113372", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CL_RSD_MULTIPROV FACTORY-02-", "RefUrl": "/notes/1113372 "}, {"RefNumber": "1093899", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA indexing performance for certain platforms", "RefUrl": "/notes/1093899 "}, {"RefNumber": "1087714", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Error in authorization check when deleting chain", "RefUrl": "/notes/1087714 "}, {"RefNumber": "1102965", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:PC:MON:ODS:Statman:Req. does not have qualok stat. in PC", "RefUrl": "/notes/1102965 "}, {"RefNumber": "1086332", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Long runtimes: Queries with many characteristics/attributes", "RefUrl": "/notes/1086332 "}, {"RefNumber": "1088324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception in cl_rspls_delta_buffer_a - method check_locked", "RefUrl": "/notes/1088324 "}, {"RefNumber": "1090060", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Enhanced transformation check", "RefUrl": "/notes/1090060 "}, {"RefNumber": "1105495", "RefComponent": "BW-BEX-OT", "RefTitle": "Verbesserung Technische Information in RSRT", "RefUrl": "/notes/1105495 "}, {"RefNumber": "1090660", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Short dump OBJREF NOT ASSIGNED for new user", "RefUrl": "/notes/1090660 "}, {"RefNumber": "1129774", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning function terminates w/o specific error message", "RefUrl": "/notes/1129774 "}, {"RefNumber": "1125594", "RefComponent": "BW", "RefTitle": "RSTT trace creation from Web runtime terminates", "RefUrl": "/notes/1125594 "}, {"RefNumber": "1099415", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Performance optimization for RSZDELETE", "RefUrl": "/notes/1099415 "}, {"RefNumber": "1089469", "RefComponent": "BW-BEX-ET", "RefTitle": "Improvements for report RSWB_ROLES_REORG", "RefUrl": "/notes/1089469 "}, {"RefNumber": "1083305", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:STATMAN:Dump in function module RSM_INFOCUBE_WRITE_CHECK", "RefUrl": "/notes/1083305 "}, {"RefNumber": "1093755", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Statistics counter of OLAP cache not increased", "RefUrl": "/notes/1093755 "}, {"RefNumber": "1041822", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Optimizing the \"Change run monitor\"", "RefUrl": "/notes/1041822 "}, {"RefNumber": "1088836", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Netweaver BI 7.0 BEx Analyzer: Expanding hierarchy", "RefUrl": "/notes/1088836 "}, {"RefNumber": "1086256", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:P22:PC: Memory overflow dump in RSSM_PROCESS_CHECK_CHAIN", "RefUrl": "/notes/1086256 "}, {"RefNumber": "1088819", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: CX_SY_RANGE_OUT_OF_BOUNDS with negative amounts", "RefUrl": "/notes/1088819 "}, {"RefNumber": "1115243", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: RSPCINSTANCE not deleted", "RefUrl": "/notes/1115243 "}, {"RefNumber": "1121289", "RefComponent": "BW-PLA-IP", "RefTitle": "Master locks not displayed in RSPLSE", "RefUrl": "/notes/1121289 "}, {"RefNumber": "1117966", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain 299 in SAPLRRI2, form REP_ASSIGN_INITIAL_OPT-02-", "RefUrl": "/notes/1117966 "}, {"RefNumber": "1084816", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error when reading virtual providers", "RefUrl": "/notes/1084816 "}, {"RefNumber": "1106485", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Deleting master data; and aggregates", "RefUrl": "/notes/1106485 "}, {"RefNumber": "1080181", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during transformation of reverse images", "RefUrl": "/notes/1080181 "}, {"RefNumber": "1078151", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Record number unclear w/error in structure move", "RefUrl": "/notes/1078151 "}, {"RefNumber": "1112596", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17:P22:DWB: Slow refresh if several srce systems connected", "RefUrl": "/notes/1112596 "}, {"RefNumber": "1112591", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P17:P22:BAPI_IPAK_START: Long runtime for date scheduling", "RefUrl": "/notes/1112591 "}, {"RefNumber": "1095958", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P16:DTP:Batch manager works in the same logical unit of work", "RefUrl": "/notes/1095958 "}, {"RefNumber": "1095924", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Deletion/analyis report for error handling logs", "RefUrl": "/notes/1095924 "}, {"RefNumber": "1097832", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable for characteristic with time-dependent text", "RefUrl": "/notes/1097832 "}, {"RefNumber": "1089064", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:PSA:STATMAN:Slow access for RSREQDONE with enqueue", "RefUrl": "/notes/1089064 "}, {"RefNumber": "1097425", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "InfoProvider-spezifische Texte von InfoObjeken falsch", "RefUrl": "/notes/1097425 "}, {"RefNumber": "1090503", "RefComponent": "BW", "RefTitle": "Venezuelan currency conversion - Conversion in SAP BW", "RefUrl": "/notes/1090503 "}, {"RefNumber": "1091019", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem: Replacing variables with variables (attribute)", "RefUrl": "/notes/1091019 "}, {"RefNumber": "1109644", "RefComponent": "BW-BEX-ET", "RefTitle": "Query views are not deleted in the D version", "RefUrl": "/notes/1109644 "}, {"RefNumber": "1081026", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor proposes action \"Rebuild BIA Indexes\"", "RefUrl": "/notes/1081026 "}, {"RefNumber": "1116157", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Monitor terminates if source system not existing", "RefUrl": "/notes/1116157 "}, {"RefNumber": "1116671", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Slow listing of chains if logon language incorr.", "RefUrl": "/notes/1116671 "}, {"RefNumber": "1100955", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RS_EXCEPTION000 in parallel query execution", "RefUrl": "/notes/1100955 "}, {"RefNumber": "1110570", "RefComponent": "BW-WHM-DST", "RefTitle": "Correction: Incorrect message for Job_open", "RefUrl": "/notes/1110570 "}, {"RefNumber": "1039009", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "QDEF: Additional authorization check during SAVE", "RefUrl": "/notes/1039009 "}, {"RefNumber": "1106719", "RefComponent": "BW-BEX-OT", "RefTitle": "First roll up to BIA index takes a long time", "RefUrl": "/notes/1106719 "}, {"RefNumber": "1089524", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (Support Package 16) Selecting the source unit", "RefUrl": "/notes/1089524 "}, {"RefNumber": "1095397", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Fill BIA index: Termination with SAPSQL_INVALID_TABLENAME", "RefUrl": "/notes/1095397 "}, {"RefNumber": "1095984", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting when you use ctrl query for recipient determination", "RefUrl": "/notes/1095984 "}, {"RefNumber": "1106078", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Precalculate value set: Object BUCKET already exists", "RefUrl": "/notes/1106078 "}, {"RefNumber": "1096279", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Printing PDF docs: Error during processing of spool request", "RefUrl": "/notes/1096279 "}, {"RefNumber": "1102729", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA e-mai notification - RSADMIN parameter is incorrect", "RefUrl": "/notes/1102729 "}, {"RefNumber": "1096008", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Error message \"RESULT\" unknown", "RefUrl": "/notes/1096008 "}, {"RefNumber": "1077562", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message BRAIN 633 when you check variables", "RefUrl": "/notes/1077562 "}, {"RefNumber": "1077682", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "%RT(x), %GT(x) or %CT(x) displays values less than 0.5% as 0", "RefUrl": "/notes/1077682 "}, {"RefNumber": "1086610", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IP: Too few values for query with result access modes", "RefUrl": "/notes/1086610 "}, {"RefNumber": "1099477", "RefComponent": "BW-PLA-IP", "RefTitle": "x299 BRAIN in CL_RSPLS_DELTA_BUFFER_B, READ-17- or READ-18", "RefUrl": "/notes/1099477 "}, {"RefNumber": "1102405", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16) Error during content transfer", "RefUrl": "/notes/1102405 "}, {"RefNumber": "1100980", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Dump when activating a transformation", "RefUrl": "/notes/1100980 "}, {"RefNumber": "1100977", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Meldung: Transformation nicht vorhanden", "RefUrl": "/notes/1100977 "}, {"RefNumber": "1105583", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Correct parameter display for reduced call stack", "RefUrl": "/notes/1105583 "}, {"RefNumber": "1106705", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error ASSERTION_FAILED for formulas", "RefUrl": "/notes/1106705 "}, {"RefNumber": "1076972", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: PSA index in DataSource does not contain request", "RefUrl": "/notes/1076972 "}, {"RefNumber": "1098731", "RefComponent": "BW-BEX-OT", "RefTitle": "Correction for Note 1091714", "RefUrl": "/notes/1098731 "}, {"RefNumber": "1093130", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Termination when you call a transformation", "RefUrl": "/notes/1093130 "}, {"RefNumber": "1093893", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16) RSB_GUI 048 when you check an InfoSpoke", "RefUrl": "/notes/1093893 "}, {"RefNumber": "1092986", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Schattenversion wird falsch erzeugt", "RefUrl": "/notes/1092986 "}, {"RefNumber": "1100475", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP16): Falsche Meldung beim Abbrec des Löschens", "RefUrl": "/notes/1100475 "}, {"RefNumber": "1100472", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Error during content transfer", "RefUrl": "/notes/1100472 "}, {"RefNumber": "1112271", "RefComponent": "BW-BEX-ET", "RefTitle": "Interval limits in selections not compared correctly", "RefUrl": "/notes/1112271 "}, {"RefNumber": "1101924", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Dump in transformation with InfoSet as source", "RefUrl": "/notes/1101924 "}, {"RefNumber": "1098057", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query: Dump NO_ROLL_MEMORY or other memory overflow", "RefUrl": "/notes/1098057 "}, {"RefNumber": "1094100", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "NetWeaver BI Accelerator: Continuous improvements", "RefUrl": "/notes/1094100 "}, {"RefNumber": "1097448", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: PSADELETE w/o PSA reference after content actvtn", "RefUrl": "/notes/1097448 "}, {"RefNumber": "1079183", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BW 70 BEx: Suppressing transport dialog box for active CTO", "RefUrl": "/notes/1079183 "}, {"RefNumber": "1101289", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Unknown index (900) on E tables of BW aggregates", "RefUrl": "/notes/1101289 "}, {"RefNumber": "1093481", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Statistics with ANALYZE after you create new partitions", "RefUrl": "/notes/1093481 "}, {"RefNumber": "1087258", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0(SP16) Deletion of transformation not transported", "RefUrl": "/notes/1087258 "}, {"RefNumber": "1090476", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Error message RSTRAN 502 and RSTRAN 503", "RefUrl": "/notes/1090476 "}, {"RefNumber": "1093594", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Deleting complete contents for yellow requests", "RefUrl": "/notes/1093594 "}, {"RefNumber": "1102263", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:DTP: Incorrect display of DTP selections", "RefUrl": "/notes/1102263 "}, {"RefNumber": "1098071", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Incorrect data with hierarchies and new master data provider", "RefUrl": "/notes/1098071 "}, {"RefNumber": "1080241", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Improvement of usability in Support Package 16", "RefUrl": "/notes/1080241 "}, {"RefNumber": "1106197", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termin. during query generation in CL_RSR and GET_CHANM-01-", "RefUrl": "/notes/1106197 "}, {"RefNumber": "1048163", "RefComponent": "BW-BEX-ET", "RefTitle": "BEx filter cannot be found using the input help", "RefUrl": "/notes/1048163 "}, {"RefNumber": "1085397", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC: Complete deletion of contents does not wait", "RefUrl": "/notes/1085397 "}, {"RefNumber": "1102296", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Improvements to 3.X variable screen filter dialog", "RefUrl": "/notes/1102296 "}, {"RefNumber": "1103886", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 function in 0DATE is not restricted by a time interval", "RefUrl": "/notes/1103886 "}, {"RefNumber": "1095759", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Display of red requests in the RDA monitor", "RefUrl": "/notes/1095759 "}, {"RefNumber": "1100490", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "No data for non-cumulatives with missing currency/unit", "RefUrl": "/notes/1100490 "}, {"RefNumber": "1104012", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data when navigating in the BEx Analyzer", "RefUrl": "/notes/1104012 "}, {"RefNumber": "1105096", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation cannot be deleted due to transport system", "RefUrl": "/notes/1105096 "}, {"RefNumber": "1092558", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Close RDA requests before changing data structure", "RefUrl": "/notes/1092558 "}, {"RefNumber": "1077127", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Process types for starting and ending an RDA", "RefUrl": "/notes/1077127 "}, {"RefNumber": "1098180", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BRAIN X299 in SAPLRSDRC_SPLIT; form COMPOUND_CHAVL_CHECK-01-", "RefUrl": "/notes/1098180 "}, {"RefNumber": "1094884", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Long runtime in the function module RRSV_INT_CHA_VAL_SPLIT", "RefUrl": "/notes/1094884 "}, {"RefNumber": "1096620", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Incorrect PSA version in RSDSSEG", "RefUrl": "/notes/1096620 "}, {"RefNumber": "1092251", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: nicht behandelte Exception", "RefUrl": "/notes/1092251 "}, {"RefNumber": "1092510", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: You cannot call F4 help in InfoPackage", "RefUrl": "/notes/1092510 "}, {"RefNumber": "1102487", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Loading hierarchies: Sequence of siblings", "RefUrl": "/notes/1102487 "}, {"RefNumber": "1105094", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Structure name error in RSDS_RANGE_TO_WHERE", "RefUrl": "/notes/1105094 "}, {"RefNumber": "1099870", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Display of statistics when executing trace", "RefUrl": "/notes/1099870 "}, {"RefNumber": "1102318", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Cancel function does not work for the RSDDV dialog box", "RefUrl": "/notes/1102318 "}, {"RefNumber": "1094736", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "No access to a running RDA InfoPackage", "RefUrl": "/notes/1094736 "}, {"RefNumber": "1085914", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Restarting a change run results in error RSDD 85", "RefUrl": "/notes/1085914 "}, {"RefNumber": "1094948", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:RSMDATASTATE: Only aggregation check for InfoCubes", "RefUrl": "/notes/1094948 "}, {"RefNumber": "1101612", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Termination occurs in input template in planning modeler", "RefUrl": "/notes/1101612 "}, {"RefNumber": "1104069", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND process looses events during content activtn", "RefUrl": "/notes/1104069 "}, {"RefNumber": "1046558", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization: Checking for 0DATE", "RefUrl": "/notes/1046558 "}, {"RefNumber": "1093737", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Decimal separator displayed incorrectly in char. values", "RefUrl": "/notes/1093737 "}, {"RefNumber": "1101947", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when using URL to start BEx Analyzer", "RefUrl": "/notes/1101947 "}, {"RefNumber": "1081856", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when entering plan values", "RefUrl": "/notes/1081856 "}, {"RefNumber": "1100695", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Incorrect data displayed when an aggregate is checked", "RefUrl": "/notes/1100695 "}, {"RefNumber": "1096023", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Due to cube &1 being locked, query cannot be executed", "RefUrl": "/notes/1096023 "}, {"RefNumber": "1099459", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Selection on 0FISCYEAR processed incorrectly", "RefUrl": "/notes/1099459 "}, {"RefNumber": "1098782", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help does not return any data", "RefUrl": "/notes/1098782 "}, {"RefNumber": "1092965", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CX_SY_DYNAMIC_OSQL_SYNTAX in CL_RSDM_READ_MASTER_DATA", "RefUrl": "/notes/1092965 "}, {"RefNumber": "1092641", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System does not filter according to problem class correctly", "RefUrl": "/notes/1092641 "}, {"RefNumber": "1103315", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IF_RSMD_RS~READ_DATA-04 in CL_RSMD_RS_SPECIAL", "RefUrl": "/notes/1103315 "}, {"RefNumber": "1075080", "RefComponent": "BW-BEX-OT", "RefTitle": "Classification of messages", "RefUrl": "/notes/1075080 "}, {"RefNumber": "1097770", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Migration to analysis auths: Dump in SAPLRSEC_MIGRATION", "RefUrl": "/notes/1097770 "}, {"RefNumber": "1079447", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN CL_RSR_FIPT_TRANSFORMATOR; GET_MAPPING-01-", "RefUrl": "/notes/1079447 "}, {"RefNumber": "1080661", "RefComponent": "BW-BEX-OT", "RefTitle": "Messages are not displayed", "RefUrl": "/notes/1080661 "}, {"RefNumber": "1080251", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "EIBV with two different hierarchies", "RefUrl": "/notes/1080251 "}, {"RefNumber": "1098160", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Too much data for constant selection", "RefUrl": "/notes/1098160 "}, {"RefNumber": "1101648", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Usability in authorization maintenance", "RefUrl": "/notes/1101648 "}, {"RefNumber": "1084334", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Old variable screen: Input help for hierarchy unexpected", "RefUrl": "/notes/1084334 "}, {"RefNumber": "1102701", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Usability: Customer exit variables in analysis authorization", "RefUrl": "/notes/1102701 "}, {"RefNumber": "1085057", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Search function in field list", "RefUrl": "/notes/1085057 "}, {"RefNumber": "1102838", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correct.: Endless loop after API termination in dialog mode", "RefUrl": "/notes/1102838 "}, {"RefNumber": "1100456", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Syntax error for type P in error stack key", "RefUrl": "/notes/1100456 "}, {"RefNumber": "1096553", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: ASCII default for RDA adapter", "RefUrl": "/notes/1096553 "}, {"RefNumber": "1094400", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Logging missing if the status is unclear", "RefUrl": "/notes/1094400 "}, {"RefNumber": "1094401", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Menu entries 'Back' and 'Cancel'", "RefUrl": "/notes/1094401 "}, {"RefNumber": "1095828", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:DTP:Cannot delete yellow DTP requests in Manage", "RefUrl": "/notes/1095828 "}, {"RefNumber": "1092244", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: System transfers data rec warnings as info msgs", "RefUrl": "/notes/1092244 "}, {"RefNumber": "1103342", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination when planning functions are executed", "RefUrl": "/notes/1103342 "}, {"RefNumber": "1100504", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query with node selection terminates when using BIA", "RefUrl": "/notes/1100504 "}, {"RefNumber": "1090345", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not trigger Information Broadcasting", "RefUrl": "/notes/1090345 "}, {"RefNumber": "1101759", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:Manage:Canceling during branching to PC does not work", "RefUrl": "/notes/1101759 "}, {"RefNumber": "1099157", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:DTP: Enqueue deadlock on RSSTATMANSTATUS", "RefUrl": "/notes/1099157 "}, {"RefNumber": "1101166", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error: Node & with predecessor 0 not found in hierarchy &", "RefUrl": "/notes/1101166 "}, {"RefNumber": "1101481", "RefComponent": "BW-BEX-ET-WEB-MOB", "RefTitle": "BEx Mobile Intelligence with Windows Mobile 6 and Pocket IE", "RefUrl": "/notes/1101481 "}, {"RefNumber": "1099391", "RefComponent": "BW-PLA-IP", "RefTitle": "Figures disappear after you transfer", "RefUrl": "/notes/1099391 "}, {"RefNumber": "1100976", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IOBJ_VALUE_NOT_VALID in CL_RSMD_RS and _TRANSFORM_DATA", "RefUrl": "/notes/1100976 "}, {"RefNumber": "1094483", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRSDRC_SPLIT and form RETFL_N_SET-01-", "RefUrl": "/notes/1094483 "}, {"RefNumber": "1098613", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL:Hier:Many hier.headers in source syst.; Performance", "RefUrl": "/notes/1098613 "}, {"RefNumber": "1098117", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:LOADING:Switch init to delta - init simulation indicator", "RefUrl": "/notes/1098117 "}, {"RefNumber": "1097613", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:PSA process:Termination w/o requests that can be posted", "RefUrl": "/notes/1097613 "}, {"RefNumber": "1100454", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction:No conversion in change log deletion after import", "RefUrl": "/notes/1100454 "}, {"RefNumber": "1086220", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "You cannot hide all the structure elements", "RefUrl": "/notes/1086220 "}, {"RefNumber": "1098278", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Correction: Trying to activate 3.x-DataSource remotely", "RefUrl": "/notes/1098278 "}, {"RefNumber": "1099053", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: RSTPRFC: Password in lower case not allowed", "RefUrl": "/notes/1099053 "}, {"RefNumber": "1093422", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in CL_RSR_RRK0_PARTITION; NOTIFY_READ_REQUEST-01-", "RefUrl": "/notes/1093422 "}, {"RefNumber": "1097915", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchy overview: No update  BI7.0 SP 16 (SPS 14)", "RefUrl": "/notes/1097915 "}, {"RefNumber": "1098146", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: Content timestamp of queries is deleted", "RefUrl": "/notes/1098146 "}, {"RefNumber": "1087527", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Performance of direct access: runtime object for transformation", "RefUrl": "/notes/1087527 "}, {"RefNumber": "1090162", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA CCMS: Report results in STACK_STATE_NO_ROLL_MEMORY", "RefUrl": "/notes/1090162 "}, {"RefNumber": "1097125", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Manage:Check useful only for REQU requests", "RefUrl": "/notes/1097125 "}, {"RefNumber": "1096778", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:BATCH:Default settings, one parallel process for A1S", "RefUrl": "/notes/1096778 "}, {"RefNumber": "1096774", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Enhancement for advance 7.1 development-init/delta", "RefUrl": "/notes/1096774 "}, {"RefNumber": "1093988", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Multilingual entries: Fragments in the incorrect language", "RefUrl": "/notes/1093988 "}, {"RefNumber": "1094810", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "A termination occurs when you try to create a single index", "RefUrl": "/notes/1094810 "}, {"RefNumber": "1091455", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in very rare cases", "RefUrl": "/notes/1091455 "}, {"RefNumber": "1072622", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP16:Multiple record PSA Maintenance for Numerical Fields", "RefUrl": "/notes/1072622 "}, {"RefNumber": "1094902", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: <PERSON><PERSON><PERSON> before writing to PSA not sent to srce sys", "RefUrl": "/notes/1094902 "}, {"RefNumber": "1095140", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Relative time restriction does not work for 0CALWEEK", "RefUrl": "/notes/1095140 "}, {"RefNumber": "1095955", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Manage: Rebuilding: Process chain jump button", "RefUrl": "/notes/1095955 "}, {"RefNumber": "1088098", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0(SP16) Dump when saving an expert routine", "RefUrl": "/notes/1088098 "}, {"RefNumber": "1093595", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:REQUDEL: Requests are deleted only in administration", "RefUrl": "/notes/1093595 "}, {"RefNumber": "1094013", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: Obsolete Debug_User in old RSADMIN table", "RefUrl": "/notes/1094013 "}, {"RefNumber": "1094014", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P16:DTP:Dump occurs when you repair a terminated DTP request", "RefUrl": "/notes/1094014 "}, {"RefNumber": "1090952", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Request creation is locked too restictively", "RefUrl": "/notes/1090952 "}, {"RefNumber": "1094591", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:STATMAN: Including time stamp field for fast access", "RefUrl": "/notes/1094591 "}, {"RefNumber": "1094839", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:PSA: Sucessfully updated check during deletion", "RefUrl": "/notes/1094839 "}, {"RefNumber": "1090358", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:BATCH: Report RSBATCH_DEL_MSG_PARM_DTPTEMP terminates", "RefUrl": "/notes/1090358 "}, {"RefNumber": "1087426", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:REQUDEL: Routines are not activated automatically", "RefUrl": "/notes/1087426 "}, {"RefNumber": "1095410", "RefComponent": "BW", "RefTitle": "P16:PSA:Enhancement of PSA process to allow update of 0 days", "RefUrl": "/notes/1095410 "}, {"RefNumber": "1094989", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Wait time for RDA requests incorrect in the monitor", "RefUrl": "/notes/1094989 "}, {"RefNumber": "1093017", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA e-mail notification does not write to BAL", "RefUrl": "/notes/1093017 "}, {"RefNumber": "1089438", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Data comparison database/BIA for flat fact index", "RefUrl": "/notes/1089438 "}, {"RefNumber": "1095656", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correctn: Monitor wizard does not know RDA and Web services", "RefUrl": "/notes/1095656 "}, {"RefNumber": "1095466", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Suppressing the message \"Text variable cannot be replaced\"", "RefUrl": "/notes/1095466 "}, {"RefNumber": "1095625", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Attributes of reference char. for variables not displayed", "RefUrl": "/notes/1095625 "}, {"RefNumber": "1078105", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Attributes in the input help for variables are not displayed", "RefUrl": "/notes/1078105 "}, {"RefNumber": "1095651", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Correction: Incompl. RDA InfoPackgs transferred from content", "RefUrl": "/notes/1095651 "}, {"RefNumber": "1095653", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Repair start twice at the second time", "RefUrl": "/notes/1095653 "}, {"RefNumber": "1083285", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Field KBYTES in table RSDDSTATTREXSERV is incorrect", "RefUrl": "/notes/1083285 "}, {"RefNumber": "1091174", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Invalid restrictions accepted in variable screen", "RefUrl": "/notes/1091174 "}, {"RefNumber": "1084458", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Error messages during saving in update task", "RefUrl": "/notes/1084458 "}, {"RefNumber": "1094315", "RefComponent": "BW-BEX-ET-WEB-DIA", "RefTitle": "Multiple dialog does not remember the old values - part 3", "RefUrl": "/notes/1094315 "}, {"RefNumber": "880660", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "DBC: Syntax error in the start routine with the STRG type", "RefUrl": "/notes/880660 "}, {"RefNumber": "1085243", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Direct access performance: InfoObject metadata in DTP", "RefUrl": "/notes/1085243 "}, {"RefNumber": "1094472", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Master data authorization: Not all values are displayed", "RefUrl": "/notes/1094472 "}, {"RefNumber": "1078719", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Direct access performance:No RFC during extraction in Myself", "RefUrl": "/notes/1078719 "}, {"RefNumber": "1094309", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "DB fallback triggered during BIA restart", "RefUrl": "/notes/1094309 "}, {"RefNumber": "1085822", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "BRAIN 629 for query with variable replacement from query", "RefUrl": "/notes/1085822 "}, {"RefNumber": "1093476", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in GET_FILTER_VALUES_CHA", "RefUrl": "/notes/1093476 "}, {"RefNumber": "1093808", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster with PDF, PS or PCL: Specifying margins", "RefUrl": "/notes/1093808 "}, {"RefNumber": "1094302", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Planning buttons displayed when you have no authorization", "RefUrl": "/notes/1094302 "}, {"RefNumber": "1093522", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "System error in CL_RSR_RRK0_HIERARCHY APPLY_SLICER_NAV-02-", "RefUrl": "/notes/1093522 "}, {"RefNumber": "1093121", "RefComponent": "BW-BCT-TCT", "RefTitle": "Negative times in BI statistics: Event 13052", "RefUrl": "/notes/1093121 "}, {"RefNumber": "1093677", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRK0, form 'DELETE_FST-01-", "RefUrl": "/notes/1093677 "}, {"RefNumber": "1090935", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception CX_RS_INPUT_INVALID in SET_RETURNFLS", "RefUrl": "/notes/1090935 "}, {"RefNumber": "1092135", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Formulas with an error are displayed with an X", "RefUrl": "/notes/1092135 "}, {"RefNumber": "1093146", "RefComponent": "BW-PLA-IP", "RefTitle": "Ready for input status and \"Calculate as...\"", "RefUrl": "/notes/1093146 "}, {"RefNumber": "1077601", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA 140 in line 46 of CL_RSDA_DAP_A=================CM00Z", "RefUrl": "/notes/1077601 "}, {"RefNumber": "1092056", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Workbook precalculation: Scheduling during data change", "RefUrl": "/notes/1092056 "}, {"RefNumber": "1092813", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Structure element text is not displayed", "RefUrl": "/notes/1092813 "}, {"RefNumber": "1092607", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Invalid selection: All values are displayed", "RefUrl": "/notes/1092607 "}, {"RefNumber": "1082888", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you execute planning functions in the Analyzer", "RefUrl": "/notes/1082888 "}, {"RefNumber": "1082157", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination in Analyzer when you create system messages", "RefUrl": "/notes/1082157 "}, {"RefNumber": "1086153", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Single rule simulation", "RefUrl": "/notes/1086153 "}, {"RefNumber": "1074953", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "FEMS, BIA optimization", "RefUrl": "/notes/1074953 "}, {"RefNumber": "1091770", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Distribution across several channels - HTML format zipped", "RefUrl": "/notes/1091770 "}, {"RefNumber": "1092044", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Priorities of properties not evaluated correctly", "RefUrl": "/notes/1092044 "}, {"RefNumber": "1092080", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Message: No SID found", "RefUrl": "/notes/1092080 "}, {"RefNumber": "1077125", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Optimization when you execute BEx Web (ABAP) for first time", "RefUrl": "/notes/1077125 "}, {"RefNumber": "1090736", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "UNCAUGHT - CL_BICS_PROV_CONVERTER and form Missing text", "RefUrl": "/notes/1090736 "}, {"RefNumber": "1091269", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Performance: Long runtime in LRSSBRF03, Form FILL_AUTH_BUFFE", "RefUrl": "/notes/1091269 "}, {"RefNumber": "1091436", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSADMIN parameter for reversing changes made by Note 1079359", "RefUrl": "/notes/1091436 "}, {"RefNumber": "1091690", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Delta index proposed", "RefUrl": "/notes/1091690 "}, {"RefNumber": "1085839", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A170 with compound char. & constant compounding parent", "RefUrl": "/notes/1085839 "}, {"RefNumber": "1085026", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in program SAPLRRI2, form BEST_WGR-01-", "RefUrl": "/notes/1085026 "}, {"RefNumber": "1084856", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "SAP NetWeaver BI Accelerator index is not used", "RefUrl": "/notes/1084856 "}, {"RefNumber": "1079448", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data in a very special situation", "RefUrl": "/notes/1079448 "}, {"RefNumber": "1077372", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing data in a special situation", "RefUrl": "/notes/1077372 "}, {"RefNumber": "1076688", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Drill down to 0INFOPROV in the key figure definition", "RefUrl": "/notes/1076688 "}, {"RefNumber": "1091456", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "PSA view for DataSource universe", "RefUrl": "/notes/1091456 "}, {"RefNumber": "1075789", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Displaying BEx Web Application 7.0 in role menu 3.x", "RefUrl": "/notes/1075789 "}, {"RefNumber": "1080822", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: RSPLS 131 when creating aggregation level", "RefUrl": "/notes/1080822 "}, {"RefNumber": "1089487", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Deleting a 3.x DataSource leaves 'orphaned' objects", "RefUrl": "/notes/1089487 "}, {"RefNumber": "1088594", "RefComponent": "BW-WHM-AWB", "RefTitle": "RSINPUT: DYN_TABLE_ILL_COMP_VAL CX_SY_DYN_TABLE_ILL_COMP_VAL", "RefUrl": "/notes/1088594 "}, {"RefNumber": "1090847", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "RSR_CACHE: Wait time too long during locking for main memory", "RefUrl": "/notes/1090847 "}, {"RefNumber": "1089224", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generation: Error with different DSO objects", "RefUrl": "/notes/1089224 "}, {"RefNumber": "1085933", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search with several selections and ALPHA does not work", "RefUrl": "/notes/1085933 "}, {"RefNumber": "1090484", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "OBJECTS_OBJREF_NOT_ASSIGNED in CL_RS_TIME_SERVICE", "RefUrl": "/notes/1090484 "}, {"RefNumber": "1091018", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination CL_RSR_RRK0_ATTR_C and form _SEL_TO_SELDR_14-02-", "RefUrl": "/notes/1091018 "}, {"RefNumber": "1088462", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination with MASTER_QUERY_RETURNNM-01-", "RefUrl": "/notes/1088462 "}, {"RefNumber": "1086074", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Short dump in selective delete", "RefUrl": "/notes/1086074 "}, {"RefNumber": "1075125", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unauthorized data displayed when structure element expanded", "RefUrl": "/notes/1075125 "}, {"RefNumber": "1089492", "RefComponent": "BW-WHM-DST", "RefTitle": "Consulting: <PERSON><PERSON><PERSON> not automatically set/incorrect data", "RefUrl": "/notes/1089492 "}, {"RefNumber": "1090428", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Node variables: Compounded node values incorrect", "RefUrl": "/notes/1090428 "}, {"RefNumber": "1086483", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Result Access Mode, default in BEx Analyzer 2004s", "RefUrl": "/notes/1086483 "}, {"RefNumber": "1090119", "RefComponent": "BW-BEX-OT", "RefTitle": "Table SELDR requires a large amount of memory space", "RefUrl": "/notes/1090119 "}, {"RefNumber": "1082968", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA monitor deletes released job", "RefUrl": "/notes/1082968 "}, {"RefNumber": "1090281", "RefComponent": "BW-BEX-ET", "RefTitle": "Authorization not checked when you change global variants", "RefUrl": "/notes/1090281 "}, {"RefNumber": "1084934", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Hotfix for critical program error in BEx Analyzer", "RefUrl": "/notes/1084934 "}, {"RefNumber": "1086283", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Leaf is in hierarchy and also under \"Remaining Nodes\"", "RefUrl": "/notes/1086283 "}, {"RefNumber": "1089663", "RefComponent": "BW-WHM-AWB", "RefTitle": "P16: DWWB: Drag & drop terminates with meaningless message", "RefUrl": "/notes/1089663 "}, {"RefNumber": "1089491", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:R<PERSON><PERSON><PERSON><PERSON>:Report RSREQARCH_WRITE hangs in endless loop", "RefUrl": "/notes/1089491 "}, {"RefNumber": "1089157", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P16:STATMAN:PSA:Performance improvement in PSA loading", "RefUrl": "/notes/1089157 "}, {"RefNumber": "1083390", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Locking failure during master data/texts upload or deletion", "RefUrl": "/notes/1083390 "}, {"RefNumber": "1089504", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in column or structural component", "RefUrl": "/notes/1089504 "}, {"RefNumber": "1088354", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message BRAIN 206 for formula elements in structures", "RefUrl": "/notes/1088354 "}, {"RefNumber": "1089525", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0 (SP16): No confirmation when open hub dest. checked", "RefUrl": "/notes/1089525 "}, {"RefNumber": "1087250", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchy maintenance: Sequence when using F4 help to add", "RefUrl": "/notes/1087250 "}, {"RefNumber": "1085447", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Navigation in Routinen", "RefUrl": "/notes/1085447 "}, {"RefNumber": "1089335", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Termination due to missing authorization for a role", "RefUrl": "/notes/1089335 "}, {"RefNumber": "1089336", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16)/BI7.1(SPS04) Falsche Pseudo-D", "RefUrl": "/notes/1089336 "}, {"RefNumber": "1089041", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA InfoPackage does not delete delta queue", "RefUrl": "/notes/1089041 "}, {"RefNumber": "1083561", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Protokollierung beim <PERSON>", "RefUrl": "/notes/1083561 "}, {"RefNumber": "1081827", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Abort in function modules RSDU_CHKREP_PKEY*", "RefUrl": "/notes/1081827 "}, {"RefNumber": "1088342", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sum of the rounded values is not applied", "RefUrl": "/notes/1088342 "}, {"RefNumber": "1086673", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "MODTIME not updated during query generation", "RefUrl": "/notes/1086673 "}, {"RefNumber": "1083513", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Authority check for Filter deletion fails", "RefUrl": "/notes/1083513 "}, {"RefNumber": "1087740", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data in a specific situation", "RefUrl": "/notes/1087740 "}, {"RefNumber": "1076664", "RefComponent": "BW-WHM-DST", "RefTitle": "Program for controlled system shutdown", "RefUrl": "/notes/1076664 "}, {"RefNumber": "1087067", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: References in decision formula", "RefUrl": "/notes/1087067 "}, {"RefNumber": "1088927", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P16:RDA:SDL: No active init: Incomprehensible error message", "RefUrl": "/notes/1088927 "}, {"RefNumber": "1088307", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL: New DS: Hierarchy InfoPackages for new DataSource", "RefUrl": "/notes/1088307 "}, {"RefNumber": "1088820", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Transport called during RSPC_API_CHAIN_INTERRUPT", "RefUrl": "/notes/1088820 "}, {"RefNumber": "1077061", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Check when reading master data", "RefUrl": "/notes/1077061 "}, {"RefNumber": "1088455", "RefComponent": "BW-WHM-AWB", "RefTitle": "DataSource: Performance when collecting BI objects", "RefUrl": "/notes/1088455 "}, {"RefNumber": "1087429", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: RSSM_FILL_REQ_FOR_PSADEL: DUPRE<PERSON> dump RSTSODSREQUEST", "RefUrl": "/notes/1087429 "}, {"RefNumber": "1088008", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "\"UPDATE_ERROR\" termination during change run", "RefUrl": "/notes/1088008 "}, {"RefNumber": "1088148", "RefComponent": "BW-BEX-MMR", "RefTitle": "ClassCastException for refPackage()", "RefUrl": "/notes/1088148 "}, {"RefNumber": "1087254", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Dump when saving or activating in content", "RefUrl": "/notes/1087254 "}, {"RefNumber": "1069288", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Values sorted incorrectly in results list and input help", "RefUrl": "/notes/1069288 "}, {"RefNumber": "1085982", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Exit variables perform not run step 2", "RefUrl": "/notes/1085982 "}, {"RefNumber": "1087754", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Text elements for variables in the BEx Analyzer", "RefUrl": "/notes/1087754 "}, {"RefNumber": "1087635", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Optimization for writable PartProvider II", "RefUrl": "/notes/1087635 "}, {"RefNumber": "1087379", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 in SAPLRSTIME; Form RST_TOBJ_INFO-01-", "RefUrl": "/notes/1087379 "}, {"RefNumber": "1086710", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Current status of data is displayed as initial", "RefUrl": "/notes/1086710 "}, {"RefNumber": "1076836", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Missing rollback for X index and Y index", "RefUrl": "/notes/1076836 "}, {"RefNumber": "1087451", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Error RSM2 716 in data transfer process (DTP)", "RefUrl": "/notes/1087451 "}, {"RefNumber": "1087280", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message BRAIN 182 should not be issued", "RefUrl": "/notes/1087280 "}, {"RefNumber": "1080379", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BW 70 BEx: RRMX<PERSON> causes dump in RSAH_ENSURE_GUI_VISIBLE", "RefUrl": "/notes/1080379 "}, {"RefNumber": "1085398", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P16:DTP: Complete deletion of compressed InfoCubes", "RefUrl": "/notes/1085398 "}, {"RefNumber": "1087034", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generation: >10 000 analysis auth's: Missing data records", "RefUrl": "/notes/1087034 "}, {"RefNumber": "1086648", "RefComponent": "BW-WHM-AWB", "RefTitle": "P16:Old AWB: Search in AWB and PSA time selection too large", "RefUrl": "/notes/1086648 "}, {"RefNumber": "1085673", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:P21:SDL:Incorrect selection check for routine", "RefUrl": "/notes/1085673 "}, {"RefNumber": "1084967", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:Delete change log has errors after transport", "RefUrl": "/notes/1084967 "}, {"RefNumber": "1084965", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P16:PC:PSA-deletion:Maintain instead of delete authorization", "RefUrl": "/notes/1084965 "}, {"RefNumber": "1086877", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Error when activating dependent objects", "RefUrl": "/notes/1086877 "}, {"RefNumber": "1086229", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Web template code is not available after transport", "RefUrl": "/notes/1086229 "}, {"RefNumber": "1076113", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Roll up in BIA terminates if there is an empty request", "RefUrl": "/notes/1076113 "}, {"RefNumber": "1086567", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Termination BRAIN 299 in CL_RSR_RRK0_CURR; FILL_CUDIM_02-05-", "RefUrl": "/notes/1086567 "}, {"RefNumber": "1086388", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Sequence of variables in the Web template", "RefUrl": "/notes/1086388 "}, {"RefNumber": "1086339", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Metadatenmodell - Ausgabe", "RefUrl": "/notes/1086339 "}, {"RefNumber": "1085912", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem: Variable replacement from variable and compounding", "RefUrl": "/notes/1085912 "}, {"RefNumber": "1085934", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Dump and error message when you have fixed unit", "RefUrl": "/notes/1085934 "}, {"RefNumber": "1085820", "RefComponent": "BW-BEX-ET", "RefTitle": "BEx tools do not start if you log on in Arabic", "RefUrl": "/notes/1085820 "}, {"RefNumber": "1085895", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Enhancement of Log messages for Reporting agent", "RefUrl": "/notes/1085895 "}, {"RefNumber": "1085859", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting by value ignored for active hierarchy", "RefUrl": "/notes/1085859 "}, {"RefNumber": "1085692", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "IF_RSMD_RSH~GET_CHILDREN-03 in CL_RSMD_RSH", "RefUrl": "/notes/1085692 "}, {"RefNumber": "1085923", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Index data display terminates for SID index", "RefUrl": "/notes/1085923 "}, {"RefNumber": "1078188", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Jump ALV->BEX: Termination CREATE_COBPRO_FROM_DDFIELD-02-", "RefUrl": "/notes/1078188 "}, {"RefNumber": "1085307", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Initial hierarchy date and new variable screen", "RefUrl": "/notes/1085307 "}, {"RefNumber": "1083266", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rule type for reading master data for open hub destination", "RefUrl": "/notes/1083266 "}, {"RefNumber": "1083265", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems occur during the transport of transformations", "RefUrl": "/notes/1083265 "}, {"RefNumber": "1078508", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Database table of open hub destination incorrect", "RefUrl": "/notes/1078508 "}, {"RefNumber": "1085358", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA check \"Random Queries\" creates great load", "RefUrl": "/notes/1085358 "}, {"RefNumber": "1080672", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Mehrere BIA-Indizes aktivieren und füllen", "RefUrl": "/notes/1080672 "}, {"RefNumber": "1085215", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16): Dump when you change to \"No conversion\"", "RefUrl": "/notes/1085215 "}, {"RefNumber": "1084887", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Technical name of PSA not displayed", "RefUrl": "/notes/1084887 "}, {"RefNumber": "1084602", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Performance of combination check in the input-ready query", "RefUrl": "/notes/1084602 "}, {"RefNumber": "1084560", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA index consistency checks: specify restrictions", "RefUrl": "/notes/1084560 "}, {"RefNumber": "1084217", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect data in BIA after initial indexing", "RefUrl": "/notes/1084217 "}, {"RefNumber": "1084418", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Query with hierarchy that reads data from BIA terminates", "RefUrl": "/notes/1084418 "}, {"RefNumber": "1084328", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Assigning InfoSources - content option displayed", "RefUrl": "/notes/1084328 "}, {"RefNumber": "1084422", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Dump when you transfer invalid LOGID in API", "RefUrl": "/notes/1084422 "}, {"RefNumber": "1080197", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Rollup of a transactional InfoCube terminates", "RefUrl": "/notes/1080197 "}, {"RefNumber": "1081534", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:P21:SDL:F4 on AL11 list truncates names after 30 chars.", "RefUrl": "/notes/1081534 "}, {"RefNumber": "1080725", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:P21:SDL:InfoPackage from content transfer/job scheduling", "RefUrl": "/notes/1080725 "}, {"RefNumber": "1079726", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL:New DS: No selections for init extraction from DSO", "RefUrl": "/notes/1079726 "}, {"RefNumber": "1083738", "RefComponent": "BW-WHM-DST", "RefTitle": "P16: TSV_TNEW_PAGE_ALLOC_FAILED for extractor 0TCTREQUID", "RefUrl": "/notes/1083738 "}, {"RefNumber": "1079193", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:STATMAN: Several parallel requests and enqueue problem", "RefUrl": "/notes/1079193 "}, {"RefNumber": "1083740", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL: Entry of leading zeros in INT4 fields", "RefUrl": "/notes/1083740 "}, {"RefNumber": "1078192", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P16:SDL: Allow ACCP fields for selection", "RefUrl": "/notes/1078192 "}, {"RefNumber": "1083278", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Authorization check during execution", "RefUrl": "/notes/1083278 "}, {"RefNumber": "1074492", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Technical group for write-optimized DataStore objects", "RefUrl": "/notes/1074492 "}, {"RefNumber": "1083879", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "DB fallback for BIA activated, during index check", "RefUrl": "/notes/1083879 "}, {"RefNumber": "1083660", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Report for error stack analysis", "RefUrl": "/notes/1083660 "}, {"RefNumber": "1074844", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND processes inactive after content transfer", "RefUrl": "/notes/1074844 "}, {"RefNumber": "1076796", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error CREATE_DATA_UNKNOWN_TYPE during transport", "RefUrl": "/notes/1076796 "}, {"RefNumber": "1075365", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Raise exception type cx_rs_step_failed in transformation", "RefUrl": "/notes/1075365 "}, {"RefNumber": "1083912", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Dump when maintaining an open hub destination", "RefUrl": "/notes/1083912 "}, {"RefNumber": "1083658", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Dialog boxes when batch authorization is missing", "RefUrl": "/notes/1083658 "}, {"RefNumber": "1083745", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA time stamp is not updated after you roll up", "RefUrl": "/notes/1083745 "}, {"RefNumber": "1083103", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Error in AND process after restart", "RefUrl": "/notes/1083103 "}, {"RefNumber": "1082468", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Error with RSRV BIA sums check with non-cumulative key figs", "RefUrl": "/notes/1082468 "}, {"RefNumber": "1083486", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump during activation of a transformation (content)", "RefUrl": "/notes/1083486 "}, {"RefNumber": "1083328", "RefComponent": "BW", "RefTitle": "Prerequisite note", "RefUrl": "/notes/1083328 "}, {"RefNumber": "1083135", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination in transaction RRMX", "RefUrl": "/notes/1083135 "}, {"RefNumber": "1082605", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination BRAIN 299 in SAPLRRK0; LRECH_AGGR_BNR-02-", "RefUrl": "/notes/1082605 "}, {"RefNumber": "1082498", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "MDX: No data with fixed node filter and drilldown", "RefUrl": "/notes/1082498 "}, {"RefNumber": "1082499", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Asynchronous confirmation in QM action", "RefUrl": "/notes/1082499 "}, {"RefNumber": "1080014", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Transport of DataSources and transformations", "RefUrl": "/notes/1080014 "}, {"RefNumber": "1081608", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "WAD & Meta data download on J2EE after upgrade to SPS >= 11", "RefUrl": "/notes/1081608 "}, {"RefNumber": "1081453", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error in old RSSM log in BI 7.0", "RefUrl": "/notes/1081453 "}, {"RefNumber": "1081122", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_OBJREF_NOT_ASSIGNED_NO", "RefUrl": "/notes/1081122 "}, {"RefNumber": "1080438", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Currency field/unit field appear changeable", "RefUrl": "/notes/1080438 "}, {"RefNumber": "1080453", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination w/node variable in Java Web variable value", "RefUrl": "/notes/1080453 "}, {"RefNumber": "1080458", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable in default value and dependencies in F4", "RefUrl": "/notes/1080458 "}, {"RefNumber": "1080701", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination 299 in SAPLRRI2, SELECTION_WITH_COMPSEL_GET-2-", "RefUrl": "/notes/1080701 "}, {"RefNumber": "1080558", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Module RSPC_API_CHAIN_GET_RUNS does not exist", "RefUrl": "/notes/1080558 "}, {"RefNumber": "1080449", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Old long text function also for local messages", "RefUrl": "/notes/1080449 "}, {"RefNumber": "1080556", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Selection filled although it is empty", "RefUrl": "/notes/1080556 "}, {"RefNumber": "1080551", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: No call trace for asynchronous call", "RefUrl": "/notes/1080551 "}, {"RefNumber": "1080434", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Exception not triggered when method is called", "RefUrl": "/notes/1080434 "}, {"RefNumber": "1075214", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generating analysis authorizations: Email address is missing", "RefUrl": "/notes/1075214 "}, {"RefNumber": "1080200", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Error for negative default value of a formula variable", "RefUrl": "/notes/1080200 "}, {"RefNumber": "1079956", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Incorrect number of decimal places in new formula elements", "RefUrl": "/notes/1079956 "}, {"RefNumber": "1077916", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A PartProvider returns no data", "RefUrl": "/notes/1077916 "}, {"RefNumber": "1077472", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "MESSAGE_TYPE_X in CL_RSCRT_RDA_DELTA_CHECK", "RefUrl": "/notes/1077472 "}, {"RefNumber": "1077906", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Scheduling RDA daemon with a different job class", "RefUrl": "/notes/1077906 "}, {"RefNumber": "1079040", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Assignment of DTP to RDA daemon is missing", "RefUrl": "/notes/1079040 "}, {"RefNumber": "1070967", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Exception with \"Calculation after list calculation\" incorr.", "RefUrl": "/notes/1070967 "}, {"RefNumber": "1075964", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Selecting filter value for temporal hierarchy join", "RefUrl": "/notes/1075964 "}, {"RefNumber": "1078155", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Error in structure proposal for files", "RefUrl": "/notes/1078155 "}, {"RefNumber": "1079222", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Filter value list for structures is sorted incorrectly", "RefUrl": "/notes/1079222 "}, {"RefNumber": "1078527", "RefComponent": "BW-WHM-DST", "RefTitle": "P16:Request deletion with error RSSTATMAN 102 in row 34", "RefUrl": "/notes/1078527 "}, {"RefNumber": "1074767", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1067984", "RefUrl": "/notes/1074767 "}, {"RefNumber": "1077655", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource is inactive when loaded (or after replication)", "RefUrl": "/notes/1077655 "}, {"RefNumber": "1074754", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: reserved field names, no field names", "RefUrl": "/notes/1074754 "}, {"RefNumber": "1077308", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "0FISCVARNT treated as a key in write-optimized DataStore", "RefUrl": "/notes/1077308 "}, {"RefNumber": "1077774", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Fehler ohne eindeutiges Symptom", "RefUrl": "/notes/1077774 "}, {"RefNumber": "1076301", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No values when you expand a hierarchy node", "RefUrl": "/notes/1076301 "}, {"RefNumber": "1077147", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: 'GET_SID-1-' in program SAPLRRS2", "RefUrl": "/notes/1077147 "}, {"RefNumber": "1077412", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "A constant is not treated correctly", "RefUrl": "/notes/1077412 "}, {"RefNumber": "1077144", "RefComponent": "BW-PLA-IP", "RefTitle": "E message: \"Please enter a valid value for characteristic &\"", "RefUrl": "/notes/1077144 "}, {"RefNumber": "1075470", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when maintaining conversion types (unit and currency)", "RefUrl": "/notes/1075470 "}, {"RefNumber": "1075469", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error in TLOGO framework implementation (get_prop)", "RefUrl": "/notes/1075469 "}, {"RefNumber": "1075147", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Checking source and target when building the factory", "RefUrl": "/notes/1075147 "}, {"RefNumber": "1075879", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "\"Not assigned\" selection not authorized with nodes", "RefUrl": "/notes/1075879 "}, {"RefNumber": "1075728", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Module RSNDI_SHIE_SUBTREE_DELETE: Deleting level texts", "RefUrl": "/notes/1075728 "}, {"RefNumber": "1075922", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Resetting kill not possible", "RefUrl": "/notes/1075922 "}, {"RefNumber": "1075807", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "New button for user variants in the 3.X variable dialog", "RefUrl": "/notes/1075807 "}, {"RefNumber": "1075262", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Enhanced proposal mapping for transformation", "RefUrl": "/notes/1075262 "}, {"RefNumber": "1074803", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help does not display any values in variable screen", "RefUrl": "/notes/1074803 "}, {"RefNumber": "1075041", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction:General message issued for error in direct access", "RefUrl": "/notes/1075041 "}, {"RefNumber": "1075149", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump ASSIGN_TYPE_CONFLICT for Open Hub Destination", "RefUrl": "/notes/1075149 "}, {"RefNumber": "1074850", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: File name routine is not saved", "RefUrl": "/notes/1074850 "}, {"RefNumber": "1074558", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_HIERARCHY; SEL_TO_SELDR_EQSID-01-", "RefUrl": "/notes/1074558 "}, {"RefNumber": "1074769", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Despite warning Brain 053, IBV is not switched off correctly", "RefUrl": "/notes/1074769 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}