{"Request": {"Number": "1669429", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 432, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009911522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001669429?language=E&token=0EFE32C3BD34D205C4F58AA7B376A864"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001669429", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001669429/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1669429"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.01.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Security - Read KBA 2985997 for subcomponents"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1669429 - VSI customizing user-interface changes for MIME-check"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>**********************************************************************<br />Do not implement this SAP Note manually unless SAP requests this explicitly or this note is a prerequisite for the implementation of another SAP Note.<br />**********************************************************************<br /><br />This note prepares user interface changes in the configuration of the Virus Scan Interface for future enhancements.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>VSI, VSCAN, MIME.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>Manual Pre-Implement steps according to description.</LI></UL> <UL><LI>Automatic implementation of code changes.</LI></UL> <UL><LI>Manual Post-Implement Steps according to description.</LI></UL> <p><br />The manual steps require a processor with ABAP workbench expertise.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D026337)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D026337)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669429/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62072", "URL": "/supportpackage/SAPKB62072"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64030", "URL": "/supportpackage/SAPKB64030"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70027", "URL": "/supportpackage/SAPKB70027"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70112", "URL": "/supportpackage/SAPKB70112"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70211", "URL": "/supportpackage/SAPKB70211"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71015", "URL": "/supportpackage/SAPKB71015"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71110", "URL": "/supportpackage/SAPKB71110"}, {"SoftwareComponentVersion": "SAP_BASIS 720", "SupportPackage": "SAPKB72007", "URL": "/supportpackage/SAPKB72007"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73007", "URL": "/supportpackage/SAPKB73007"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73103", "URL": "/supportpackage/SAPKB73103"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0001669429/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 620&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB62068 - SAPKB62071&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 640&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB64015 - SAPKB64029&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70004 - SAPKB70026&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB71014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB71101 - SAPKB71109&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB70111&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70201 - SAPKB70210&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73001 - SAPKB73006&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB72002 - SAPKB72006&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>For all newly created objects, choose package SVSCAN.<br/><br/>Activate each object after creation or change.<br/><br/>#######################################################################<br/>SE11, Domain, VSCAN_MIMETYPE, Create.<br/>Short description: MIME-Type<br/>Data Type: CHAR.<br/>No. Characters: 64.<br/>Lower Case: Checked.<br/>#######################################################################<br/>SE11, Data type, VSCAN_MIMETYPE, Create, Data element.<br/>Short Description: MIME-Type<br/>Domain: VSCAN_MIMETYPE<br/>Field labels: Short 10 \"MIME\", Medium 15 \"MIME-Type\", Long 20 \"MIME-Type\". No heading.<br/>Documentation:<br/><U>Definition</U><br/>MIME type for the MIME type check during a virus scan.<br/><U>Use</U><br/>You can maintain lists of MIME types for virus scan profiles for the  purpose of checking the correct MIME type during a virus scan.<br/><U>Dependencies</U><br/>You can maintain MIME types as one of the following:<br/>&nbsp;&nbsp;- &lt;Mediatype&gt;/&lt;Subtype&gt;<br/>&nbsp;&nbsp;- &lt;Mediatype&gt;/*<br/>&nbsp;&nbsp;- */*<br/>The length of a MIME type is restricted to 64 characters and valid  characters are the lower case letters a-z, the digits 0-9, the dot (\".\"), the plus (\"+\") and the dash (\"-\").<br/>There is no value help for MIME types available in the SAP system. MIME  types are currently defined in the Internet Request for Comments (RFC)  documents RFC 2045 and RFC 2046 (this information is subject to obsolescence as time evolves).<br/>The list of MIME types is evaluated if and only if the parameter  CUST_CHECK_MIME_TYPE is maintained in the \"Profile Configuration  Parameters\" area of the virus scan profile with value \"1\", and the  evaluation of the profile configuration parameters is enabled in the header data of the virus scan profile.<br/>By setting the parameter CUST_MIME_TYPES_ARE_BLACKLIST to value \"1\", you  let the list of MIME types be interpreted as blacklist instead of whitelist.<br/>#######################################################################<br/>SE11, Data type, VSCAN_EVAL_PROF_PARGLOB, Create, Data element.<br/>Short Description: Evaluate Profile Configuration Parameters<br/>Domain: XFELD.<br/>Field labels: Long 40 \"Evaluate Profile Configuration Param.\".<br/>Documentation:<br/><U>Definition</U><br/>This indicator controls whether the profile configuration parameters shall be evaluated or ignored.<br/><U>Use</U><br/>When this indicator is set, the profile configuration parameters that  are maintained for the profile in area \"Profile Configuration Parameters\" are evaluated, otherwise they are ignored.<br/><U>Dependencies</U><br/>This indicator was introduced to overcome a limitation of the \"Step  Configuration Parameters\" that cannot be pre-delivered by SAP (as steps are not deliverable).<br/>For newly delivered profiles or new installations, this indicator is set  and SAP pre-delivers profile configuration parameters according to the profile owner's demands.<br/>#######################################################################<br/>SE11, Database table, VSCAN_PROF_PARGL, Create.<br/>Short Description: Config. Parameters for Virus Scan Profile (group independ.)<br/>Delivery Class: G.<br/>Maint.: Allowed with Restrictions.<br/>Fields:<br/>&nbsp;&nbsp;MANDT (Key/Init): MANDT<br/>&nbsp;&nbsp;PROFILE (Key/Init): VSCAN_PROFILE<br/>&nbsp;&nbsp;PARAMETER_KEY (Key/Init): VSCAN_PARAMETER_KEY<br/>&nbsp;&nbsp;PARAMETER_VALUE: VSCAN_PARAMETER_VALUE<br/>Foreign Keys (accept corresponding proposal):<br/>&nbsp;&nbsp;PROFILE: Profile (VSCAN_PROF), Key fields/candidates, 1:CN.<br/>&nbsp;&nbsp;PARAMETER_KEY: Parameter Key (VSCAN_PARAM), Key fields/candidates, 1:CN.<br/>Enhancement category: Can be enhanced (character).<br/>Technical Settings:<br/>&nbsp;&nbsp;Data class: APPL2.<br/>&nbsp;&nbsp;Size category: 0.<br/>&nbsp;&nbsp;Buffering switched on.<br/>&nbsp;&nbsp;Fully buffered.<br/>Maintain Customer Namespace:<br/>&nbsp;&nbsp;PROFILE<br/>&nbsp;&nbsp;&nbsp;&nbsp;Y*<br/>&nbsp;&nbsp;&nbsp;&nbsp;Z*<br/>#######################################################################<br/>SE11, Database table, VSCAN_PROF_MIME, Create.<br/>Short Description: MIME-Type List for Profile<br/>Delivery Class: G.<br/>Maint.: Allowed with Restrictions.<br/>Fields:<br/>&nbsp;&nbsp;MANDT (Key/Init): MANDT<br/>&nbsp;&nbsp;PROFILE (Key/Init): VSCAN_PROFILE<br/>&nbsp;&nbsp;MIMETYPE (Key/Init): VSCAN_MIMETYPE<br/>Foreign Keys (accept corresponding proposal):<br/>&nbsp;&nbsp;PROFILE: Profile (VSCAN_PROF), Key fields/candidates, 1:CN.<br/>Enhancement category: Can be enhanced (character).<br/>Technical Settings:<br/>&nbsp;&nbsp;Data class: APPL2<br/>&nbsp;&nbsp;Size category: 0<br/>&nbsp;&nbsp;Buffering switched on<br/>&nbsp;&nbsp;Fully buffered<br/>Maintain Customer Namespace:<br/>&nbsp;&nbsp;PROFILE<br/>&nbsp;&nbsp;&nbsp;&nbsp;Y*<br/>&nbsp;&nbsp;&nbsp;&nbsp;Z*<br/>#######################################################################<br/>SE11, View, V_VSCAN_PROF_PGL, Create, Maintenance View.<br/>Short Description: Profile Configuration Parameters<br/>Tables: VSCAN_PROF_PARGL.<br/>View fields:<br/>&nbsp;&nbsp;MANDT: VSCAN_PROF_PARGL MANDT<br/>&nbsp;&nbsp;PROFILE: VSCAN_PROF_PARGL PROFILE<br/>&nbsp;&nbsp;PARAMETER_KEY: VSCAN_PROF_PARGL PARAMETER_KEY<br/>&nbsp;&nbsp;PARAMETER_VALUE: VSCAN_PROF_PARGL PARAMETER_VALUE<br/>For PROFILE, set column \"P\" (Maintenance attribute for view field) to \"S\".<br/>Maintenance Status:<br/>&nbsp;&nbsp;Read, change, delete and insert.<br/>&nbsp;&nbsp;Delivery class: G.<br/>&nbsp;&nbsp;Maint.: Allowed with Restrictions.<br/>&nbsp;&nbsp;Customer namespace definition:<br/>&nbsp;&nbsp;&nbsp;&nbsp;PROFILE<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Y*<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Z*<br/>#######################################################################<br/>SE11, View, V_VSCAN_PROF_MIM, Create, Maintenance View.<br/>Short Description: MIME-Types<br/>Tables: VSCAN_PROF_MIME.<br/>View fields:<br/>&nbsp;&nbsp;MANDT: VSCAN_PROF_MIME MANDT<br/>&nbsp;&nbsp;PROFILE: VSCAN_PROF_MIME PROFILE<br/>&nbsp;&nbsp;MIMETYPE: VSCAN_PROF_MIME MIMETYPE<br/>For PROFILE, set column \"P\" to \"S\".<br/>Maintenance Status:<br/>&nbsp;&nbsp;Read, change, delete and insert.<br/>&nbsp;&nbsp;Delivery class: G.<br/>&nbsp;&nbsp;Maint.: Allowed with Restrictions.<br/>&nbsp;&nbsp;Customer namespace definition:<br/>&nbsp;&nbsp;&nbsp;&nbsp;PROFILE<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Y*<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Z*<br/>#######################################################################<br/>SE11, View, V_VSCAN_PROF_PAR, Change.<br/>Maintenance Status:<br/>&nbsp;&nbsp;Customer namespace definition:<br/>&nbsp;&nbsp;&nbsp;&nbsp;PROFILE<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Y*<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Z*<br/>#######################################################################<br/>SE11, Database table, VSCAN_PROF, Change.<br/>Fields: Add new field at the end:<br/>&nbsp;&nbsp;EVAL_PAR_GLOBAL: VSCAN_EVAL_PROF_PARGLOB<br/>#######################################################################<br/>SE54, V_VSCAN_PROF_PGL, Generated Objects, Create/Change.<br/>Create Modules: Yes.<br/>Authorization Group: SRZL.<br/>Function group: VSCAN_CUST<br/>Maintenance type: One step.<br/>Overview screen: 50<br/>Standard recording routine.<br/>Create (F6).<br/>#######################################################################<br/>SE54, V_VSCAN_PROF_MIM, Generated Objects, Create/Change.<br/>Create Modules: Yes.<br/>Authorization Group: SRZL.<br/>Function group: VSCAN_CUST<br/>Maintenance type: One step.<br/>Overview screen: 60<br/>Standard recording routine.<br/>Create (F6).<br/>Ignore possible warning regarding shortening of field MIMETYPE.<br/>#######################################################################<br/>SE54, VSCAN_PROF, Generated Objects, Create/Change.<br/>Change (F7).<br/>New field/sec. table in structure.<br/>Field type: Normal field.<br/>#######################################################################<br/>SE11, Database table, VSCAN_PARAM, Change.<br/>Delivery and Maintenance: Allowed.<br/>#######################################################################<br/>SE16, VSCAN_PARAM, Create Entries (F5).<br/>Parameter Key: CUST_CHECK_MIME_TYPE<br/>Parameter Type: BOOL<br/>Parameter Text: Activates MIME type recognition and check<br/>#######################################################################<br/>SE16, VSCAN_PARAM, Create Entries (F5).<br/>Parameter Key: CUST_MIME_TYPES_ARE_BLACKLIST<br/>Parameter Type: BOOL<br/>Parameter Text: List of MIME types is blacklist<br/>#######################################################################<br/>SE16, VSCAN_PARAM, Table Contents.<br/>Select<br/>&nbsp;&nbsp;CUST_CHECK_MIME_TYPE<br/>&nbsp;&nbsp;CUST_MIME_TYPES_ARE_BLACKLIST<br/>Table Entry / Transport Entries<br/>#######################################################################<br/>SE91, VSCAN.<br/>Create new message 082 with text:<br/>&nbsp;&nbsp;&amp;1 is no allowed MIME type<br/>#######################################################################<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 620&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB62068 - SAPKB62071&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 640&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB64016 - SAPKB64029&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70004 - SAPKB70026&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB71014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB71101 - SAPKB71109&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB70111&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70201 - SAPKB70210&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73001 - SAPKB73006&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB72002 - SAPKB72006&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>#######################################################################<br/>SE11, Search help, VSCAN_HELP_PARAMETER_2, Create, Elementary search help.<br/><br/>Short description: Search for Configuration Parameters by type<br/>Selection method: VSCAN_PARAM<br/>Dialog type: Display values immediately<br/>Search help exit: VSCAN_HELP_EXIT_PARAMETER<br/>Parameter:<br/>&nbsp;&nbsp;PARAMETER_KEY (IMP/EXP): LPos 1, VSCAN_PARAMETER_KEY<br/>&nbsp;&nbsp;PARAMETER_TEXT: LPos 3, VSCAN_PARAMETER_TEXT<br/>&nbsp;&nbsp;PARAMETER_TYPE: LPos 2, VSCAN_PARAMETER_TYPE<br/>&nbsp;&nbsp;SHOW_INIT (IMP): XFELD<br/>&nbsp;&nbsp;SHOW_SCAN (IMP): XFELD<br/>&nbsp;&nbsp;SHOW_CUST (IMP): XFELD<br/>Ignore possible warnings \"parameter ... does not occur\") during activation.<br/>#######################################################################<br/>SE11, Database table, VSCAN_PROF_PARGL, Change.<br/>Select PARAMETER_KEY, Press \"Srch Help\".<br/>Saerch help name: VSCAN_HELP_PARAMETER_2, \"Generate Proposal\".<br/>&nbsp;&nbsp;PARAMETER_KEY: VSCAN_PROF_PARGL PARAMETER_KEY<br/>&nbsp;&nbsp;SHOW_INIT: Constant ' '<br/>&nbsp;&nbsp;SHOW_SCAN: Constant ' '<br/>&nbsp;&nbsp;SHOW_CUST: Constant 'X'<br/>Caution: Order of parameters might vary.<br/>#######################################################################<br/>SE11, Database table, VSCAN_GROUP_P, Change.<br/>Select PARAMETER_KEY, Press \"Srch Help\".<br/>Search help name: VSCAN_HELP_PARAMETER_2, \"Generate Proposal\".<br/>&nbsp;&nbsp;PARAMETER_KEY: VSCAN_GROUP_P PARAMETER_KEY<br/>&nbsp;&nbsp;SHOW_INIT: Constant 'X'<br/>&nbsp;&nbsp;SHOW_SCAN: Constant 'X'<br/>&nbsp;&nbsp;SHOW_CUST: Constant ' '<br/>Caution: Order of parameters might vary.<br/>#######################################################################<br/>SE11, Database table, VSCAN_PROF_PAR, Change.<br/>Select PARAMETER_KEY, Press \"Srch Help\".<br/>Search help name: VSCAN_HELP_PARAMETER_2, \"Generate Proposal\".<br/>&nbsp;&nbsp;PARAMETER_KEY: VSCAN_PROF_PAR PARAMETER_KEY<br/>&nbsp;&nbsp;SHOW_INIT: Constant ' '<br/>&nbsp;&nbsp;SHOW_SCAN: Constant 'X'<br/>&nbsp;&nbsp;SHOW_CUST: Constant ' '<br/>Caution: Order of parameters might vary.<br/>#######################################################################<br/>SE54, Edit View Cluster, VSCAN_PROFILE_VC, Create/Change.<br/>Object structure:<br/>&nbsp;&nbsp;For V_VSCAN_PROF_PAR, change \"Short text\" to \"Step Configuration Parameters\".<br/>&nbsp;&nbsp;Create new entry:<br/>&nbsp;&nbsp;&nbsp;&nbsp;V_VSCAN_PROF_PGL, \"Profile Configuration Parameters\", VSCAN_PROF, S, 4<br/>&nbsp;&nbsp;&nbsp;&nbsp;with field dependency:<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;V_VSCAN_PROF_PGL PROFILE / VSCAN_PROF PROFILE<br/>&nbsp;&nbsp;Create new entry:<br/>&nbsp;&nbsp;&nbsp;&nbsp;V_VSCAN_PROF_MIM, \"MIME-Types\", VSCAN_PROF, S, 5<br/>&nbsp;&nbsp;&nbsp;&nbsp;with field dependency:<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;V_VSCAN_PROF_MIM PROFILE / VSCAN_PROF PROFILE<br/>Events:<br/> Change FORM Routines main program to RSVSCANCUST_VC_CALLBACK.<br/>&nbsp;&nbsp;Create new entry<br/>&nbsp;&nbsp;&nbsp;&nbsp;01 VSCAN_PROFILE_VC_HIDE_MIME<br/>Ignore possible warning regarding \"compare flag\".<br/>Header entry:<br/>&nbsp;&nbsp;Activate<br/>&nbsp;&nbsp;Should the subobject maintenance screens be modified: Yes<br/>#######################################################################<br/>SE51, SAPLVSCAN_CUST, 0050, Flow logic, Change.<br/>Insert single line of code:<br/>&nbsp;&nbsp;&nbsp;&nbsp;FIELD v_vscan_prof_pgl-parameter_key .<br/>&nbsp;&nbsp;&nbsp;&nbsp;FIELD v_vscan_prof_pgl-parameter_value .<br/>&nbsp;&nbsp;&nbsp;&nbsp;MODULE vscan_prof_pgl_value_check ON CHAIN-REQUEST.&nbsp;&nbsp;\" INSERT<br/>&nbsp;&nbsp;&nbsp;&nbsp;MODULE set_update_flag ON CHAIN-REQUEST.<br/>#######################################################################<br/>SE51, SAPLVSCAN_CUST, 0060, Flow logic, Change.<br/>Insert single line of code:<br/>&nbsp;&nbsp;&nbsp;&nbsp;FIELD v_vscan_prof_mim-mimetype .<br/>&nbsp;&nbsp;&nbsp;&nbsp;MODULE vscan_check_mime_type ON CHAIN-REQUEST.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; \" INSERT<br/>&nbsp;&nbsp;&nbsp;&nbsp;MODULE set_update_flag ON CHAIN-REQUEST.<br/>#######################################################################<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "731", "Number": "1669429 ", "URL": "/notes/1669429 ", "Title": "VSI customizing user-interface changes for MIME-check", "Component": "BC-SEC"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}