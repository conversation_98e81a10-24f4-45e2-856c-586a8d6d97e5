{"Request": {"Number": "415727", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 341, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015031272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000415727?language=E&token=A84C948EEEE7BB3A0CA6F5D38C3291A8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000415727", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000415727/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "415727"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.05.2002"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-TEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "In Case of LiveCache Problems: Please use SCM-APO-LCA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "In Case of LiveCache Problems: Please use SCM-APO-LCA", "value": "SCM-TEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-TEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "415727 - APO Support Package 13 for APO Release 3.0A"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This Note describes how to import APO Support Package APO 13 (SAPKY30A13) for APO Release 3.0A in your System.<br />The APO Support Package 13 contains all Notes that were created from May 19, 2001 up to June 18, 2001. These contain corrections in the following areas:<br /><br /><B>Upgrade information for</B><B>SAPGUI</B><br />The maintenance end of the Frontend Release 4.6C ended on December 31, 2000. Therefore, we recommend you upgrade to Frontend Release 4.6D. For more information, refer to the following notes:</p> <UL><LI>147519<br /><br /><B>Newly recommended procedure with Transaction SPAM</B><br />As of maintenance level APO Support Release 2 (SR2 includes APO Support Packages 1 - 8), we recommend you to import several APO Support Packages (from Support Package 9 to the current Support Package) always together in one queue.</LI></UL> <p><br />Maintenance strategy 'SAPGUI'</p> <UL><LI>361222<br />SapPatch: Importing GUI patches</LI></UL> <p><br /><B>Kernel</B><br />Always import the most recent 46D kernel. For more information, refer to the following note:<br /></p> <UL><LI>0373047<br />Error in field input processing</LI></UL> <p><br /><B>If you have </B><B>APO</B><B> Support Package 6 or lower in your APO system, read the following notes:</B></p> <UL><LI>314218<br />Converting movement data between the Support Packages and</LI></UL> <UL><LI>361635<br />liveCache upgrade APO 3.0A SP7<br /><br /><B>before you import the </B><B>APO</B><B> Support Package 7. Another procedure would </B><B>threaten</B><B> data loss</B>.<br /></LI></UL> <UL><LI>APO Support Package 13 for Release APO 3.0A (SAPKY30A13)<br /></LI></UL> <UL><LI>OCX update<br /></LI></UL> <UL><LI>Update of LiveCache Version: 7.2.5 Build 4<br /></LI></UL> <UL><LI>COM routines<br /></LI></UL> <UL><LI>Optimizer corrections<br /></LI></UL> <UL><LI>Additional notes<br /></LI></UL> <p>The APO Support Package 13 for Release APO 3.0A is available in the following languages: German, English, French, Spanish, Danish, Finnish, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Swedish, Russian and Czech.<br />The languages are contained in APO Support Package 13.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPKY30A01, SAPKY30A02, SAPKY30A03, SAPKY30A04, SAPKY30A05, SAPKY30A06, SAPKY30A06, SAPKY30A07, SAPKY30A08, SAPKY30A09, SAPKY30A10, SAPKY30A11, SAPKY30A12, SAPKY30A13, APO Support Package, APO patch, APO Release 3.0A.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The APO Support Package 13 (SAPKY30A13) for APO Release 3.0A requires a full installation/upgrade (delivery May 15, 2000).<br />In addition, the APO Support Packages 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 and 13 must be imported for APO Release 3.0A.<br /><br />If the system was installed using <B>Service Release 1</B> for APO Release 3.0A, you must also import APO Support Packages 5, 6, 7, 8, 9, 10, 11, 12 and 13 for APO Release 3.0A.<br /><br />If the system was installed using <B>Service Release</B><B>2</B> for APO Release 3.0A, you only have to import Support Packages 9, 10, 11, 12 and 13 for APO Release 3.0A.<br /><br /><br />===========================================================<br /></p> <UL><LI>Basis Support Packages for 4.6C</LI></UL> <p>For APO Support Package 13, it is absolutely imperative that you import the Basis Support Packages up to and including Basis Support Package 20 (SAPKB46C20).</p> <UL><LI>ABA Support Packages for Basis 4.6C</LI></UL> <p>In addition to the Basis Support Packages, it is absolutely imperative that you import the ABA Support Packagesup to and including ABA Support Package ABA 20 (SAPKA46C20).</p> <UL><LI>BW Support Packages (2.0B)</LI></UL> <p>For APO Support Package 13, you absolutely must import the BW Support Packages up to and including BW Support Packages 15 (SAPKW20B15).<br />Note 328237: Importing Support Packages into a BW 2.0B system<br />Basis Support Packages/BW Support Packages are basically imported separately from the APO Support Package. Import the Basis Support Packages/BW Support Packages. Import the APO Support Package directly.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To import APO Support Package 13 fully, proceed as described below:</p> <OL>1. <B>SPAM - Updates</B></OL> <p>Before you began importing the Support Packages, you need to update the SPAM Manager to the most current status. Download the most current version of the SPAM update from OSS or SAPNET.<br />Import the most current SPAM update into your system. For more information about this, go to the initial screen of Transaction SPAM by pressing the 'i' key in the online documentation (Help-&gt;Help about the application).</p> <OL>2. <B>APO </B><B>corrections</B></OL> <p>The APO Support Package SAPKY30A13 can be downloaded from the Online Service System (OSS) or from SAPNet. The URL is: http://sapnet.sap.de/ocs-download. Follow the path: APO Support Package -&gt; APO 3.0A. Download the APO Support Package SAPKY30A13. For more information, see Note 83458.</p> <OL>3. <B>OCX </B><B>update</B></OL> <p>The OCX files that you need for the APO Support Package for APO Release 3.0A are available on the sapervX ftp server. All further information about importing the new OCX files is available in Note:<br />0415099&#x00A0;&#x00A0;APO 3.0 Frontend patch 14<br /></p> <OL>4. <B>Updating</B><B></B><B> </B><B>to</B><B> </B><B>L</B><B>iveCache Version 7.2.5 Build 4</B></OL> <p>Before updating the LiveCache to version 7.2.5 Build 4 or higher, please refer to Note<br />410002&#x00A0;&#x00A0;Setting for MAXCPU from LiveCache 7.2.5 Build 4<br /><br />You can download the LiveCache update from the sapservX. The following directory contains the corresponding LiveCache versions for the individual platforms:<br />ftp://sapservX/general/3rdparty/sapdb/LC_VERSIONS/&lt;Build&gt;//<br /><B>Note that you must import a </B><B>kernel patch</B><B> &gt;= 579 for Release </B><B>46D</B><B> if you install the LiveCache Version </B><B>7.2.5 Build</B><B> 4. With a lower patch level, incompatibilities occur between the R/3 kernel patch and the database interface !!</B> Please read the note referring to this:<br />406248 <B>LiveCache </B>Connect problems<br /><B>Important !! You must also </B><B>import</B><B> the most recent</B><B>adaslib</B><B> as described in Note 325402.</B><br />Refer to Note 379051 for all additional information about updating the LiveCache:<br />379051 Importing a LiveCache Version &gt;= 7.2.4 B15<br />After a successful LiveCache update please refer to<br />Note 424886<br />Additional sources of information for the UNIX LiveCache and COM routines are contained in Note<br />0391746 APO SP, COM routines and LiveCache versions on UNIX.</p> <OL>5. <B>COM </B><B>routines</B></OL> <p>You can download the COM routines from the sapservX.<br />The SAPCOM_30_n.SAR (n = version counter) file is contained the ftp://sapservX/specific/apo/apo30/sp13/ directory.<br />To import the new COM routines, proceed as described in the following Notes<br />415958 SAPAPO 3.0 COM object Build 21<br />157265 Exchanging COM objects for LiveCache in APO 3.0<br />Refer also to the following Note as of LiveCache Version 7.2.4:<br />336470 Environment var. DBROOT no longer exists<br />!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br />You do not need to initialize the liveCache for APO Support Package 13.<br />!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br /></p> <OL>6. <B>Optimizer corrections</B></OL> <p>The <B>Optimizer corrections</B> can be downloaded from sapservX. The <B>SAPAPO_n.SAR</B> (n = version counter) file is contained in the <B>ftp://sapservX/specific/apo/apo30/sp13/</B> directory<B>.</B><br />To import the new files, proceed as described in the following Notes:</p> <UL><LI>413897&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APO 3.0 Optimizer Support Package 13</LI></UL> <UL><LI>300930&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To Import APO 3.0 one version optimizer</LI></UL> <UL><LI><B>New functionality for Support Package 13 for APO Release 3.0A</B></LI></UL> <p>Geocoding functionality is delivered with APO Support Package 13.<br />You can geocode locations by means of address data via an RFC interface together with the Map&amp;Guide MapServer of MAP&amp;GUIDE GmbH / PTV AG. Presently, the geocoder with worldwide data is used for geocoding at place level.<br />The Map&amp;Guide MapServer module Geocoding is used to determine the geographical position on the basis of address data by entering the relevant place name and - as far as available in the respective country - the postal codes. The Geocoder determines corresponding geographical x and y coordinates that allow to position address data. To do this, the geocoding module uses place data and postal codes.<br />For detailed information please see Note<br />413084&#x00A0;&#x00A0;&#x00A0;&#x00A0;Consulting note: geocoding APO 3.0 SP13<br /></p> <OL>7. <B>The following Notes must be implemented manually !</B></OL> <p><br />Note 353197<br />User for synchronous RFC dialog as of Release 4.6<br />Note 352844<br />Authorizations for RFC users: APO &lt;-&gt; R/3<br />Note 147218<br />SAP APO Demand Planning - datamart ==&gt; BW patches<br />Note 415449<br />Adjustment to confirmation in Vehicle Scheduling<br />Note 415123<br />COM objects and inbound controller<br />Note 414696<br />Application error with COM routine call, return code<br />Note 386486<br />Category text incorrect after goods issue for delivery<br />Note 410002<br />Setting for MAXCPU from LiveCache 7.2.5 Build 4<br />Note 417948<br />/Sapapo/om_lccheck abends with error message<br /></p> <OL>8. <B>Please read the following notes and implement them, if they are relevant to you:</B></OL> <p><br /><B>Number&#x00A0;&#x00A0; Component&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Priority&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Valid</B><br />0414113&#x00A0;&#x00A0;APO-FCS Sales planning&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;low&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP13<br />0419931&#x00A0;&#x00A0;APO-SNP-OPT Optimizing the SNP plan&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13<br />0424211&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; medium&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-14<br />0431072&#x00A0;&#x00A0;APO-SNP Supply Network Planning (SNP)&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-15<br />0431113&#x00A0;&#x00A0;APO-INT Interfaces&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Hot News&#x00A0;&#x00A0; SP13-15<br />0434010&#x00A0;&#x00A0;APO-INT-PPS Production and det. sched. Hot News&#x00A0;&#x00A0; SP13-15<br />0418839&#x00A0;&#x00A0;APO-MD-PPM Production process model&#x00A0;&#x00A0;&#x00A0;&#x00A0;low&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP13<br />0436193&#x00A0;&#x00A0;APO-SNP-DPL Deployment&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-16<br />0436621&#x00A0;&#x00A0;APO-FCS-MAP Mass processing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;medium&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-16<br />0436758&#x00A0;&#x00A0;APO-SNP Supply Network Planning (SNP)&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-16<br />0439167&#x00A0;&#x00A0;APO-SNP-OPT Optimizing the SNP plan&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-16<br />0441764&#x00A0;&#x00A0;APO-SNP Supply Network Planning (SNP)&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-16<br />0442747&#x00A0;&#x00A0;APO-MD-VM Version management&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-16<br />0446224&#x00A0;&#x00A0;APO-COM COM objects in liveCache&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13<br />0450182&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP13-17<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Responsible                                                                                         ", "Value": "D027030"}, {"Key": "Processor                                                                                           ", "Value": "D027030"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000415727/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000415727/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000415727/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000415727/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000415727/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000415727/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000415727/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000415727/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000415727/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458"}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076"}, {"RefNumber": "523217", "RefComponent": "BW", "RefTitle": "SAPBWNews for BW 3.0A Support Package 12", "RefUrl": "/notes/523217"}, {"RefNumber": "458702", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 10", "RefUrl": "/notes/458702"}, {"RefNumber": "456548", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 21", "RefUrl": "/notes/456548"}, {"RefNumber": "450182", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "CTM: Calculating horizons", "RefUrl": "/notes/450182"}, {"RefNumber": "446224", "RefComponent": "BC-DB-LCA", "RefTitle": "Confirmation from parallel planning transaction overwritten", "RefUrl": "/notes/446224"}, {"RefNumber": "442747", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Version deletion does not work for confirmed orders", "RefUrl": "/notes/442747"}, {"RefNumber": "441764", "RefComponent": "SCM-APO-SNP", "RefTitle": "Correction to note 436758", "RefUrl": "/notes/441764"}, {"RefNumber": "439167", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Optimizer: Internal error 602", "RefUrl": "/notes/439167"}, {"RefNumber": "436758", "RefComponent": "SCM-APO-SNP", "RefTitle": "Termination when executing the optimizer in the background", "RefUrl": "/notes/436758"}, {"RefNumber": "436621", "RefComponent": "SCM-APO-FCS-MAP", "RefTitle": "DP background job ignores selection limit", "RefUrl": "/notes/436621"}, {"RefNumber": "436193", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Transfer quantities saved incorrectly", "RefUrl": "/notes/436193"}, {"RefNumber": "434010", "RefComponent": "SCM-APO-INT-PPS", "RefTitle": "Error with the Adding operation in the process order", "RefUrl": "/notes/434010"}, {"RefNumber": "431113", "RefComponent": "SCM-APO-INT", "RefTitle": "Reservation changes are not made in APO", "RefUrl": "/notes/431113"}, {"RefNumber": "431072", "RefComponent": "SCM-APO-SNP", "RefTitle": "Planned orders (by the Optimizer) are not transferred", "RefUrl": "/notes/431072"}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886"}, {"RefNumber": "424211", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "CTM resource check: Capacity level and break duration", "RefUrl": "/notes/424211"}, {"RefNumber": "419931", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Validity of the transportation lanes", "RefUrl": "/notes/419931"}, {"RefNumber": "418839", "RefComponent": "SCM-APO-MD-PPM", "RefTitle": "PPM generat.: No generation if start date is in the future", "RefUrl": "/notes/418839"}, {"RefNumber": "417948", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/OM_LCCHECK terminates with the error RC = 266", "RefUrl": "/notes/417948"}, {"RefNumber": "415449", "RefComponent": "SCM-APO-VS-BF", "RefTitle": "Adjustment to confirmation in the vehicle scheduling", "RefUrl": "/notes/415449"}, {"RefNumber": "415123", "RefComponent": "SCM-APO-INT-LE", "RefTitle": "apo30sp14: COM-Obj. and Inbound Controller", "RefUrl": "/notes/415123"}, {"RefNumber": "415099", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/415099"}, {"RefNumber": "414723", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 20", "RefUrl": "/notes/414723"}, {"RefNumber": "414696", "RefComponent": "SCM-APO-PPS", "RefTitle": "Application error with COM routine call, return code 144", "RefUrl": "/notes/414696"}, {"RefNumber": "414113", "RefComponent": "SCM-APO-FCS", "RefTitle": "APO 3.0: create life cycle planning authorization check", "RefUrl": "/notes/414113"}, {"RefNumber": "413897", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer Support Package 13", "RefUrl": "/notes/413897"}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002"}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248"}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746"}, {"RefNumber": "386486", "RefComponent": "SCM-APO-SNP", "RefTitle": "Category text incorrect after goods issue for delivery", "RefUrl": "/notes/386486"}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047"}, {"RefNumber": "361635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361635"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "353197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353197"}, {"RefNumber": "352844", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352844"}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470"}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237"}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218"}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002 "}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886 "}, {"RefNumber": "523217", "RefComponent": "BW", "RefTitle": "SAPBWNews for BW 3.0A Support Package 12", "RefUrl": "/notes/523217 "}, {"RefNumber": "450182", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "CTM: Calculating horizons", "RefUrl": "/notes/450182 "}, {"RefNumber": "446224", "RefComponent": "BC-DB-LCA", "RefTitle": "Confirmation from parallel planning transaction overwritten", "RefUrl": "/notes/446224 "}, {"RefNumber": "442747", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Version deletion does not work for confirmed orders", "RefUrl": "/notes/442747 "}, {"RefNumber": "441764", "RefComponent": "SCM-APO-SNP", "RefTitle": "Correction to note 436758", "RefUrl": "/notes/441764 "}, {"RefNumber": "439167", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Optimizer: Internal error 602", "RefUrl": "/notes/439167 "}, {"RefNumber": "436758", "RefComponent": "SCM-APO-SNP", "RefTitle": "Termination when executing the optimizer in the background", "RefUrl": "/notes/436758 "}, {"RefNumber": "436621", "RefComponent": "SCM-APO-FCS-MAP", "RefTitle": "DP background job ignores selection limit", "RefUrl": "/notes/436621 "}, {"RefNumber": "436193", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Transfer quantities saved incorrectly", "RefUrl": "/notes/436193 "}, {"RefNumber": "414113", "RefComponent": "SCM-APO-FCS", "RefTitle": "APO 3.0: create life cycle planning authorization check", "RefUrl": "/notes/414113 "}, {"RefNumber": "417948", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/OM_LCCHECK terminates with the error RC = 266", "RefUrl": "/notes/417948 "}, {"RefNumber": "431072", "RefComponent": "SCM-APO-SNP", "RefTitle": "Planned orders (by the Optimizer) are not transferred", "RefUrl": "/notes/431072 "}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047 "}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458 "}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237 "}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746 "}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076 "}, {"RefNumber": "456548", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 21", "RefUrl": "/notes/456548 "}, {"RefNumber": "414723", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 20", "RefUrl": "/notes/414723 "}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265 "}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218 "}, {"RefNumber": "458702", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 10", "RefUrl": "/notes/458702 "}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218 "}, {"RefNumber": "414696", "RefComponent": "SCM-APO-PPS", "RefTitle": "Application error with COM routine call, return code 144", "RefUrl": "/notes/414696 "}, {"RefNumber": "434010", "RefComponent": "SCM-APO-INT-PPS", "RefTitle": "Error with the Adding operation in the process order", "RefUrl": "/notes/434010 "}, {"RefNumber": "418839", "RefComponent": "SCM-APO-MD-PPM", "RefTitle": "PPM generat.: No generation if start date is in the future", "RefUrl": "/notes/418839 "}, {"RefNumber": "424211", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "CTM resource check: Capacity level and break duration", "RefUrl": "/notes/424211 "}, {"RefNumber": "419931", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Validity of the transportation lanes", "RefUrl": "/notes/419931 "}, {"RefNumber": "413897", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer Support Package 13", "RefUrl": "/notes/413897 "}, {"RefNumber": "415449", "RefComponent": "SCM-APO-VS-BF", "RefTitle": "Adjustment to confirmation in the vehicle scheduling", "RefUrl": "/notes/415449 "}, {"RefNumber": "415123", "RefComponent": "SCM-APO-INT-LE", "RefTitle": "apo30sp14: COM-Obj. and Inbound Controller", "RefUrl": "/notes/415123 "}, {"RefNumber": "386486", "RefComponent": "SCM-APO-SNP", "RefTitle": "Category text incorrect after goods issue for delivery", "RefUrl": "/notes/386486 "}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}