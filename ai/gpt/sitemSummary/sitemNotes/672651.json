{"Request": {"Number": "672651", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015550192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000672651?language=E&token=627CC91A3AE9A268EEE65D141763A129"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000672651", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000672651/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "672651"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 50}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.01.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-OCS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)", "value": "BC-UPG-OCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "672651 - OCS: Known problems with Support Packages in Basis Rel.6.40"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note provides information about problems that may occur while you import Support Packages for ABAP components.<br /><br /><B>Caution: This note is regularly updated to the latest status. Therefore always download the latest version from SAP Service Marketplace!</B><br /><br />Sometimes you may need to perform a SPAM/SAINT update before you can import a particular Support Package. A SPAM/SAINT update may in turn require installation of a particular R3trans or tp version in your system. This note tells you when a new SPAM/SAINT update, R3trans or tp is required.<br /><br />You should also bear in mind that dependencies may exist between various Support Package types due to our new Support Package stack concept (for example, BW Support Package SAPKW35001 requires Basis and ABA Support Packages SAPKB64001 and SAPKA64001). You will find details about the Support Package stack concept and previously defined Support Package stacks on SAP Service Marketplace at http://service.sap.com/sp-stacks.<br />Basis Support Packages may also require SAP kernels with a minimum patch number. No dependencies have been defined as yet.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>OCS, SPAM, SPAM/SAINT UPDATE, CONFLICT RESOLUTION TRANSPORT, SUPPORT PACKAGE, CRT, LOAD_PROGRAM_LOST, SAPSQL_SELECT_WA_TOO_SMALL, SAPSQL_WA_TOO_SMALL, CALL_FUNCTION_PARM_UNKNOWN, XPRA_EXECUTION, TP_FAILURE, TESTIMPORT, DDIC ACTIVATION, RUNT_ILLEGAL_SWITCH<br /><br /><br /><B>Change history</B><br />- 05.01.2011: Use an R3trans version from July 20, 2010 or later to prevent import errors (see Note 1491290).<br />- 05.01.2011: SAPKB64027: Syntax error in the central program MENUSYST (see Note 1285869).<br />- 05.01.2011: SAPKB64026: If Note <B>1420178</B> is implemented, it is essential that you deimplement it before you import the specified Basis Support Packages.<br />- 05.01.2011: SAPKA64027: Converting the table BP1030<br />- 25.07.2010: Database DB6:&#x00A0;&#x00A0;Make sure that you read HotNews <B>1406740</B>.<br />- 25.07.2010: SAPKB64026 and SAPKB64027: RSPO1043 generates inconsistencies (see Note 1488015)<br />- 29.06.2010: SAPKB64025: The validity check for the ALE distribution model contains errors.<br />- 19.09.2008: SAPKB64020 - SAPKB64022: Prior to implementing the Support Package, see Note 1249184.<br />- 19.09.2008: SAPKE50048: Long runtime when importing a Support Package.<br />- 19.09.2008: SPAM/SAINT Version 0029: TP_FAILURE 0212 in Phase IMPORT_OBJECT_LIST<br />- 18.07.2008: SAPKE50043: TrueType fonts were deleted.<br />- 04.03.2008: SAPKB64021: After you import the Support Package, syntax errors occur in the function group PA_PACKAGE_SERVICES.<br />- 04.03.2008: To avoid syntax errors in classes after SPAU, implement Note 970837.<br />- 04.03.2008: General note about errors in the phase XPRA_EXECUTION<br />- 07.11.2007: SAPKA64020: Important: All field modification settings for the business partner category are overwritten.<br />- 07.11.2007: SAPKE50039: Import conditions have been enhanced.<br />- 07.11.2007: SAPKGPHC39: Import conditions have been enhanced.<br />- 27.07.2007: On MSSQL Server systems: The import step for the table conversion (tp step \"N\") terminates with the runtime error SAPSQL_SUBQUERY_ILL_TABLE_TYPE.<br />- 27.07.2007: To avoid DDIC activation errors, implement the corrections contained in Notes 791984, 1015068, and 1022755.<br />- 27.07.2007: SAPKB64020: Termination during the import (phase IMPORT_PROPER) on MaxDB systems<br />- 27.07.2007: SAPKB64021: It takes a very long time to refresh the TMS QA worklist.<br />- 27.07.2007: SAPKE50030: Incorrect print screen when you print SAPscript forms<br />- 16.01.2004: SAPKB64004, SAPKB64009 higher - test import errors with SAPKB64009 and further Basis Support Packages<br />- 21.12.2006: SAPKE50026: It may take a long time to create new indexes for the PPOIX and PPOPX tables.<br />- 21.12.2006: SAPKGPAC05, SAPKGPAC13: Error in the DDIC_ACTIVATION phase when you activate the table indexes.<br />- 27.10.2006: SAPKB64015: Syntax error in the Note Assistant and modification adjustment after the import<br />- 27.10.2006: SAPKGPHC06, SAPKGPHC19: Test import error in SAPKGPHC19<br />- 06.09.2006: SAPKB64018 Incorrect delivery -&gt; Use the corrected version of the Support Package!<br />- 06.09.2006: DB2 V8 for z/OS: You must import APAR/PTF PK26209/UK16177.<br />- 28.07.2006: SAPKE50022: Serious error in German settlement (component PY-DE-NT-NI)<br />- 28.07.2006: SAPKB64017 Problem with BAdI CTS_REQUEST_CHECK in Phase XPRA_EXECUTION<br />- 11.05.2006: Serious error in the CRM SP Manager<br />- 11.05.2006: SAR archives cannot be loaded and unpacked using the function \"Load from front end\"<br />- 11.05.2006: SAPCAR error: format error in header<br />- 06.01.2006: When you use the downtime-minimized import mode, you <B>must</B> use a minimum of the R3trans version from December 06, 2005.<br />- 06.01.2006: SAPKW35014 requires PI_BASIS 2005_1_640 Support Package 04.<br />- 06.01.2006: SAPKB64014, SAPKB64015: Testing import error in SAPKB64015<br />- 06.01.2006: SAPKE50004, SAPKE50008: Testing import error in SAPKE50008<br />- 18.08.2005: <B>Oracle DB:</B> After you implement Note 865478, there is a risk of <B>data loss</B>when you import the Support Package. <B>You must take into account Note 871846.</B><br />- 18.08.2005: SAPKB64011 and DB2 UDB for iSeries: Problems with long-running cleanup jobs on the DB after implementing the support package<br />- 18.08.2005: SAPKB64009, SAPKB64012: Testing import error in SAPKB64012<br />- 22.07.2005: SAPKGPAC08 - DDIC activation error in Unicode systems<br />- 29.06.2005: SAPKH50007: Serious problems when processing measurement documents after importing the Support Package<br />- 29.06.2005: SAPKB64012, SAPKB64013: Duplicate key error in main import (table RSMPTEXTS)<br />- 29.06.2005: SAPKB64011: Creating new indexes for the CDHDR table may take a long time.<br />- 12.05.2005: SAPKB64012 - Serious error! All background jobs may be deleted. <B>You must take into account Notes 715355 and 837691.</B><br />- 12.05.2005: Problems with the buffer synchronization when you use a 6.40 kernel and an IBM DB2 database (Note 841938)<br />- 06.04.2005: SAPKB64011: Serious error in the Workbench Organizer after the import (see Note 816523)<br />- 06.04.2005: SAPKB64011: After you import this Basis Support Package, BSP Services are deactivated in the Internet Communication Framework.<br />- 06.04.2005: SAPKW35011: Serious error and import termination when you import into a system on a <B>DB2/DB4/DB6</B> or <B>Informix</B> database<br />- 06.04.2005: SAPKB64001, SAPKB64010 - Test import error with SAPKB64010<br />- 10.03.2005: <B>Do not use R3trans with patch level 62 to import Support Packages (as of February 11, 2005).</B><br />- 10.03.2005: Runtime error ASSIGN_LENGTH_0 during DDIC activation - Note 743408<br />- 10.03.2005: SAPKB64004: Error in the after-import method execution (AFTER_IMP_CHDO)<br />- 29.11.2004: SAPKB64004, SAPKB64009 - Test import error with SAPKB64009<br />- 04.11.2004: SAPKGPHC03, SAPKGPHC02: DDIC activation error (see Note 780475)<br />- 04.11.2004: SAPKGPAC02: Errors that can be ignored in the DDIC activation<br />- 04.11.2004: SAPKE50002 - requires the Support Packages #01 of all software components contained in the SAP ERP Central Component 5.0- NOV/04/2004: SAPKH50002 - requires the Support Packages #01 of all software components contained in SAP ERP Central Component 5.0.<br />- 04.11.2004: SAPKH50001, SAPKH50002 - test import error in SAPKH50002<br />- 04.11.2004: SAPKB64004, SAPKB64009 - test import error with SAPKB64009<br />- 04.11.2004: Database error ORA-02168 on Oracle systems with 'Automatic Segment Space Management (ASSM)' active<br />- 04.11.2004: Basis Support Packages with systems on MS SQL Server: Library dbmssslib.dll should have the minimum patch level lib_dbsl_16 (Note 731265)<br />- 14.07.2004: Termination of the 'Move Nametab' step (background job RDDMNTAB) with kernels with patch level 22-23. <B>Do not use this kernel to import Support Packages!</B><br />- 14.07.2004: SAPKA64003, SAPKA64004 - Test import error with SAPKA64004<br />- 14.07.2004: SAPKA64004: Errors that can be ignored in the ABAP generation<br />- 23.06.2004: SAPKH50001: Errors that can be ignored in the ABAP generation<br />- 23.06.2004: SAPKGPAC01: Errors that can be ignored in the DDIC activation<br />- 09.06.2004: SAPKB64003: Errors that can be ignored in the DDIC activation of the SXIVERIDT_EXTENSION table<br />- 09.06.2004: SPAM/SAINT Version 0015: Support Packages are missing in the Support Package browser<br />- 14.05.2004: SAPKB64001, SAPKB64002 - Test import error with SAPKB64002<br />- 14.04.2004: SAPKB64002: potential loss of customer-specific alert classifications<br />- 05.04.2004: Note initially released for customers<br />- 20.10.2003: Note was created again<br /><br /><br /><B>SAP recommendation</B><br />We strongly recommend that you always import the latest version of the SPAM/SAINT update before you import any other Support Packages.<br />We recommend that you download the latest versions of the tp and R3trans executables from SAP Service Marketplace (see Note 19466).<br /><br /><B>V3 update entries:</B> Process your V3 update entries before you import Support Packages. Otherwise, there is a risk that you will no longer be able to update the update entries if changes in the interface structures of the V3 update modules are delivered by Support Packages. As of SPAM/SAINT Version 0024, the Support Package Manager checks whether there are any open orders and you are then informed of this.<br /><br />If you are using several application servers, make sure that profile parameter <B>rdisp/bufrefmode</B> is set to <B>sendon,exeauto</B>, and if you are using only one application server, make sure that the profile parameter is set to <B>sendoff,exeauto</B>. Otherwise, you only have the option of executing the \"/$sync\" command to make the Support Package changes become effective.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is generally caused by a program error or the Support Package type.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. <B>SPAM/SAINT updates</B></OL> <UL><LI>Symptom: When you import the SPAM/SAINT update, a range of different runtime errors may occur:</LI></UL> <UL><UL><LI>LOAD_PROGRAM_LOST</LI></UL></UL> <UL><UL><LI>LOAD_TEXTPOOL_LOST</LI></UL></UL> <UL><UL><LI>SAPSQL_SELECT_WA_TOO_SMALL</LI></UL></UL> <UL><UL><LI>SAPSQL_SELECT_TAB_TOO_SMALL</LI></UL></UL> <UL><UL><LI>SAPSQL_WA_TOO_SMALL</LI></UL></UL> <UL><UL><LI>DDIC_STRUCLENG_INCONSISTENT</LI></UL></UL> <UL><UL><LI>RUNT_ILLEGAL_SWITCH</LI></UL></UL> <UL><UL><LI>CALL_FUNCTION_PARM_UNKNOWN</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These errors occur because the source code of the Support Package Manager is modified by the import during the run. CALL_FUNCTION_PARM_UNKNOWN, LOAD_PROGRAM_LOST and LOAD_TEXTPOOL_LOST occur if the ABAP load or text elements are to be loaded back into the local buffer and there is a different version in the database.<br />However, SAPSQL_SELECT_WA_TOO_SMALL, SAPSQL_SELECT_TAB_TOO_SMALL, SAPSQL_WA_TOO_SMALL, DDIC_STRUCLENG_INCONSISTENT and RUNT_ILLEGAL_SWITCH  occur if changes are made on the SPAM/SAINT data structures with the SPAM/SAINT update.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: None of these errors will occur again if you restart the Support Package Manager (transaction SPAM) and continue to import the SPAM/SAINT update.</p> <UL><LI><B>SPAM/SAINT Version 640/0015</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: Not all the Support Packages available in the system are displayed in the Support Package browser (for example, Support Packages for the SAP_BW or PI_BASIS component).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: This is a simply a display problem - the Support Packages can be included and imported in a Support Package queue without problems.<br />The problem is solved in SPAM/SAINT Version 640/0016.</p> <UL><LI><B>SPAM/SAINT Version 0029</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: The import terminates in the phase IMPORT_OBJECT_LIST and the error<br />&#x00A0;&#x00A0;TP_FAILURE, 0212, could not access file as supposed<br />is reported.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Delete the current queue and implement the corrections contained in Note 1232804. You can then redefine the queue and implement it.<br /><br /></p> <OL>2. <B>All Support Packages</B></OL> <UL><LI><B>Termination of the 'Move nametab' step (background job RDDMNTAB)</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Packages with a kernel with patch level 22 - 23, the \"Move nametabs\" or \"Activation of inactive runtime objects\" import step terminates (in the IMPORT_PROPER phase) with return code 0012. This error is fatal when you are importing Basis Support Package because the system then has an inconsistent status. data structures during the SPAM/SAINT update.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <B> Do not use a kernel with patch level 22 - 23 to import Support Packages. Install a kernel with patch level 24 or higher!</B><br />If the corrected kernel is not yet available, you can also avoid the error by setting the parameter <B>MAB=0</B> in the transport tool configuration of the Transport Management System (transaction STMS). See also Note 753627 for details.<br />If you have already encountered the error while importing Basis Support Packages, this inconsistent status can only be corrected by SAP Support. Open a message on the component BC-UPG-OCS.<br />The problem is not quite as serious if you are importing other Support Packages, as you can always continue the import in the Support Package Manager. The import is then completed successfully.</p> <UL><LI><B>ORA-02168 on Oracle systems with activated 'Automatic Segment Space Management (ASSM)'</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Packages, the process terminates at the IMPORT_PROPER phase. The corresponding log records the database error \"ORA-02168: invalid value for FREELISTS\".<br />This problem only occurs on Oracle systems on which 'Automatic Segment Space Management (ASSM)' is activated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid the error, implement the corrections described in Note 627246 before importing the Support Package.<br />If you have already come across this problem, you must also correct the incorrect table entries in Tables TATAF and DDSTORAGE, as described in Sections 2.a) and 2.b) of this note.</p> <UL><LI><B>Runtime error ASSIGN_LENGTH_0 during DDIC activation</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If Japanese is set as the system language, DDIC activation may terminate with runtime error ASSIGN_LENGTH_0 in certain cases.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: The problem is solved with Basis Support Package SAPKB64001. If the problem occurs while you are importing Support Packages, follow the solution guidelines in Note 743408.</p> <UL><LI><B>Problems with R3trans patch level 62 (as of February 11th, 2005)</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you use R3trans with patch level 62 (see R3trans -v) when importing Support Packages, the import may terminate during DDIC activation or when executing after-import-methods. Error messages of the following type appear in the logs:<br />LANG DTEL * Object information could not be imported<br />LANG TABL * Object information could not be imported<br />or<br />Object \"...\" \"...\" has not yet been imported successfully<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <B>Do not use the R3trans with patch level 62 to import Support Packages (as of February 11, 2005)!</B><br />If you have already encountered these errors, it is no longer sufficient to simply change the R3trans. In this case, contact SAP Support (component BC-UPG-OCS).</p> <UL><LI><B>Usage of a 6.40 kernel with a database of the family IBM DB2</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Packages to use a 6. 40 kernel on an IBM DB2 database, in some circumstances problems can occur with the buffer synchronization.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Refer to Note 841938 and only use the kernel and transportation tool versions recommended there.</p> <UL><LI><B>Oracle DB: Risk of data loss after you implement Note 865478 </B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have implemented the correction instructions from Note 865478, make sure to take into account Note 871846 before you import the Support Package and, where necessary, revise the correction as specified in the note. <B>Otherwise, data may be lost when you import the Support Package.</B></p> <UL><LI><B>Downtime-minimized import only with R3trans from December 06, 2005 (or later)</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The downtime-minimized import mode in the Support Package Manager may only be used with an R3trans version from December 06, 2005 (or later). If you use an older R3trans version, ABAP Object methods may not be imported correctly. Note 906008 describes this phenomenon and the symptoms that occur.<br />As of Version 0020, the Support Package Manager checks whether the minimum R3trans version exists.</p> <UL><LI><B>Serious error in the CRM SP Manager</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In CRM systems, you must use the special <B>CRM SP Manager</B> to import Support Packages, since it provides some necessary special functions.<br />If you have imported queues in test mode using the CRM SP Manager, you must close the CRM SP Manager and restart it before you import a Support Package queue in standard mode. This is the only way to ensure that important internal statuses are initialized properly and that the import is correct and complete. For detailed information about this problem, refer to Note 810081.</p> <UL><LI><B>\"SAPCAR error: format error in header\" when extracting an SAR archive</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you unpack an SAR archive, the SAPCAR utility terminates with the error message \"SAPCAR error: format error in header\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: The SAR archive was packed using SAPCAR Version 700. Since changes were made to the SAR archive format in this version, older SAPCAR versions can no longer unpack the archive (also refer to SAP Note 892842).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Use SAPCAR Version 700 or SAPCAR Version 640 patch level 4. These versions can process the new archive format (refer to SAP Note 892842).</p> <UL><LI><B>SAR archives cannot be loaded and unpacked using the function \"Load from front end\"</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: You want to unpack an SAR archive using the function \"Load from front end\". The system refuses the archive with the message \"File '???.SAR' is not a CAR or SAPCAR file'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: The SAR archive was packed using SAPCAR Version 700. Since changes were made to the SAR archive format in this version, and the archive format versions were also changed, the Support Package Manager does not recognize the file as a valid SAR archive.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: SPAM/SAINT Version 640/0020 contains an adjustment to the new SAR archive format. Note that you may require a new SAPCAR version (refer to the problem described above, or to SAP Note 892842).</p> <UL><LI><B>DB2 V8 for z/OS - Termination in phase IMPORT_PROPER</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Packages, the process terminates at the IMPORT_PROPER phase. The main import log contains the following entries:<br />&#x00A0;&#x00A0;sap_dext called with msgnr 128:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;db call info<br />&#x00A0;&#x00A0;function: db_report_interface<br />&#x00A0;&#x00A0; fcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;UPDATE_REPORT<br />&#x00A0;&#x00A0;tabname:&#x00A0;&#x00A0;TEXT<br />&#x00A0;&#x00A0;len:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40<br />&#x00A0;&#x00A0;key:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPLALM_MEREP_031<br />&#x00A0;&#x00A0;retcode:&#x00A0;&#x00A0;128<br />&#x00A0;&#x00A0;ROLLBACK (6287848).<br />&#x00A0;&#x00A0; Main import<br />The termination may also occur for other key values here.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: The import error is caused by a database error. Import IBM APAPR/PTF PK26209/UK16177, as described in Note 81737.</p> <UL><LI><B>Avoiding DDIC activation errors when importing Support Packages</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid DDIC activation errors when you import Support Package queues (especially long queues), you must implement the following notes BEFORE you implement the corrections.<br /><B>791984</B>&#x00A0;&#x00A0;\"Simultaneous activation of view append and appending view\"<br /><B>1015068</B> \"Deleting and recreating dictionary objects\"<br /><B>1022755</B> \"Language import and deletion of dictionary objects\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Alternatively, check whether you have already implemented this notes as part of a Basis Support Package.</p> <UL><LI><B>Termination of table conversion (tp step \"N\") on MSSQL systems</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Packages on MSSQL Server systems, the import step for the table conversion (tp step \"N\") terminates. The job log of the associated background job RDDGEN0L contains the runtime error SAPSQL_SUBQUERY_ILL_TABLE_TYPE.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid the error, implement the corrections contained in Note 1042435 before you import the Support Package.<br />If you have already encountered the error, proceed as described in Note 1042435.</p> <UL><LI><B>Procedure for termination in the phase XPRA_EXECUTION </B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: The importing of Support Packages terminates in the phase XPRA_EXECUTION with return code 0012. In the related import log, you are referred to the job log of the XPRA job, in which runtime errors (for example, SYNTAX_ERROR) are listed as the cause of the termination.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: It is possible that the errors are caused by note corrections that were implemented before you imported the Support Packages and that were partially deimplemented when you imported the Support Package queue. You can correct such problems by performing a modification adjustment and by implementing the notes that are still valid (yellow or green traffic light in SPAU) again. The modification adjustment is already prepared at this point and it can be called easily in the Support Package Manager by choosing the menu path 'Extras'-&gt;'Modification Adjustment'.<br />To avoid such problems when you subsequently import in the production system, you can include the modification adjustment transports that were created in the development system in the Support Package queue. For more information, see the online documentation of the Support Package Manager ('i' button).</p> <UL><LI><B>Syntax error in classes after the modification adjustment of notes </B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid syntax errors in classes after the modification adjustment, it is essential that you implement Note 970837 before you import Support Packages.</p> <UL><LI><B>Database DB6:&#x00A0;&#x00A0;Unreported rollback during import</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Make sure that you read HotNews <B>1406740</B> to avoid serious errors when importing Support Packages.</p> <UL><LI><B>Error after importing include deletions</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid errors that are caused by incorrect handling of include deletions in the transport tool R3trans, you must use an R3trans version from 20.07.10 or later to import Support Packages. For detailed information about this problem, see Note 1491290.<br /></p> <OL>3. <B>Basis Support Packages (SAP_BASIS component)</B></OL> <UL><LI><B>All Basis Support Packages</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: In systems on MS SQL Servers, the main import may terminate during the IMPORT_PROPER phase (see Note 731265).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Use a DbSL (dbmssslib. dll) with at least patch level lib_dbsl_16.</p> <UL><LI><B>SAPKB64001 - SAPKB64002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import Basis Support Packages SAPKB64001 and SAPKB64002 together in one queue, an error occurs with Support Package SAPKB64002 in the TEST_IMPORT step. The following error message appears in the test import log of SAPKB64002:<br /> Function SWW_WI_COMP_EVT_RECEIVE_INTERN (SWW_SRV 10) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((SWWA 61))<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by choosing 'Extras'-&gt; 'Ignore test-import error'.&#x00A0;&#x00A0;The error will not occur during the later import.</p> <UL><LI><B>SAPKB64001 - SAPKB64009</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the Basis Support Packages, SAPKB64001 and SAPKB64009, are imported together into a queue, an error occurs in the TEST_IMPORT step in Support Package SAPKB64009. In the test import log for SAPKB64009, there are the following error messages:<br /> Function ECATT_DISPLAY_DOM_OR_XSTRING (ECATT_REUSE 02) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((ECATT_WEBDYNPRO 03))<br /> Function SWW_EI_EVENT_RECEIVE (SWW_SRV 04) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((SWWA 31))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors by choosing 'Extras'-&gt;'Ignore test import error'. These errors no longer occur during subsequent imports.</p> <UL><LI><B>SAPKB64001 - SAPKB64010</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When Basis Support Packages SAPKB64001 and SAPKB64010 are imported together in a queue, an error occurs in the SAPKB64009 Support Package in the TEST_IMPORT step. You find the following error messages in the test import log of SAPKB64010:<br /> Function SWW_WI_COMP_EVENT_RECEIVE (SWW_SRV 08) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((SWWA 32))<br /> Function SWW_WI_COMP_EVENT_RECEIVE_IBF (SWW_SRV 09) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((SWWA 62))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors by choosing 'Extras'-&gt;'Ignore test import error'. These errors no longer occur during subsequent imports.</p> <UL><LI><B>SAPKB64002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problem: In Basis Support Package SAPKB64002, the delivery class of the SALRTCATC table that contains alert classifications is changed. As a result, customer-specific alert classifications that were already created in the system on Basis Support Package Version SP0 or SP1 may be overwritten by classifications delivered by SAP when you import additional Support Packages.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Note 727149.<br /> If you have not created any customer-specific alert classifications on Basis Support Package Versions SP1 and SP0, no action is required.</p> <UL><LI><B>SAPKB64003</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: In rare cases, importing Basis Support Package SAPKB64003 may terminate in the DDIC_ACTIVATION phase. The activation log displays:<br /> Table SXIVERIDT_EXTENSION (active version) was deleted.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Check references)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: The DDIC activation program carries out unnecessary tests when deleting DDIC objects (see Note 742219).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors, as they will not occur next time you run the DDIC activation program. Continue importing the Support Package queue.</p> <UL><LI><B>SAPKB64004</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Basis Support Package SAPKB64004, a termination may occur in after-import method execution when the method AFTER_IMP_CHDO method is executed. The log records the following:<br />Character string TEST is already used for object ...<br />R3TR CHDO SADOCATTR was not imported<br />Errors occurred during post-handling AFTER_IMP_CHDO for CHDO L<br />AFTER_IMP_CHDO belongs to package SZD. The errors affect the following components: BC-SRV-ASF-CHD (Change Documents)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: A general solution is available in Basis Support Package SAPKB64010 (see Note 763795). To avoid this error, Basis Support Packages SAPKB64004 and SAPKB64010 should be imported in a queue.<br />If you have already encountered these errors, contact SAP Support (component BC-SRV-ASF-CHD).</p> <UL><LI><B>SAPKB64004 - SAPKB64009, SAPKB64015, SAPKB64016...</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import the Basis Support Packages SAPKB64004 and SAPKB64009 (and higher) together in a queue, an error occurs in Support Package SAPKB64009 (and higher Support Packages) in the step TEST_IMPORT. The following typical error messages are displayed in the test import log:<br /> Function CCMSBI_* (SCSM_BI_UTIL_FUNCTIONS xx)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;does not fit into the existing function group<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;((SCCMSBI_UTIL_FUNCTIONS xx))<br />This errors also occur with further function modules of the SCSM_BI_UTIL_FUNCTIONS and SCCMSBI_UTIL_FUNCTIONS function groups specified above.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors by choosing 'Extras'-&gt;'Ignore test import error'. These errors no longer occur during subsequent imports.</p> <UL><LI><B>SAPKB64009 - SAPKB64012</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the Basis Support Packages SAPKB64009 and SAPKB64012 are imported together in a queue, there is an error in Support Package SAPKB64012 in the TEST_IMPORT phase. The test import log of SAPKB64012 contains the following error message:<br /> The TMW_GET_PROJECT_SWITCHES function (TMW_CTS_SWITCHES 01)<br />&#x00A0;&#x00A0;does not fit in the existing function group ((TMW_TRACKING 05))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors by choosing 'Extras'-&gt;'Ignore test import error'. These errors no longer occur during subsequent imports.</p> <UL><LI><B>SAPKB64011</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB64011, services are deactivated in the Internet Communication Framework. During the access, the \"HTTP 403\" error appears with the error text \"Forbidden\" or \"Service is not active\".<br />The deactivation is a safety measure, because new BSP nodes were delivered with the Basis Support Package.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Activate the services you need as described in Note 517484.</p> <UL><LI><B>SAPKB64011</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB64011, serious problems can arise in the Workbench Organizer (see Note 816523).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Apply the solution from Note 816523 or import Basis Support Package SAPKB64012. To avoid the error, import Basis Support Packages SAPKB64011 and SAPKB64012 together in a queue.</p> <UL><LI><B>SAPKB64011</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problem: Basis Support Package SAPKB64011 contains the new database indexes for the CDHDR table. Since the table may contain a large number of entries (depending upon the application environment), it may take the system a long time to create the indexes on the database during the import process.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: See Note 856624, and follow the instructions mentioned in that note <B>before the import</B>.</p> <UL><LI><B>SAPKB64011</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use a DB2 UDB database for iSeries, it may be that the tables DBSTATHDB4 and DBSTAIHDB4, or the tables DB4TABLE_HIST (short name: DB4TA00001) and DB4INDEX_HIST (short name: DB4IN00001) have grown very large. As of Support Package SAPKB64011, the system automatically cleans up these tables. The first cleanup run can take a very long time and possibly lead to an overflow of the journal receivers in the System-ASP. To avoid this problem, check the size of the tables before implementing the package, for example, using the OS command DSPOBJD. If the tables are larger than 1 GB, carry out a clean-up by using CLRPFM before you import the Support Package, as described in Note 93774.<br />You can also use this clean-up if you encounter the problems described after importing a Support Package.</p> <UL><LI><B>SAPKB64012</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB64012, background jobs are selected incorrectly by the background system administration tools. As a result, background jobs may be deleted by mistake.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You must take into account SAP Notes 715355 and 837691.<br />To solve the problem, import Support Package SAPKB64013 or implement the corrections in the latest version of Note 715355.</p> <UL><LI><B>SAPKB64012</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: A termination may occur while you are importing Basis Support Package SAPKB64012 during the main import (phase IMPORT_PROPER). The import log shows:<br /> start import of \"LIMUCUADRSWIMEAN\" ...<br /> ...<br /> Table RSMPTEXTS: Duplicate record during array insert occured.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: The error is caused by corrupt entries in table RSMPTEXTS, which have no other functional effects upon the SAP System.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Refer to Note 88656, section 29, and use the attached allow file to ignore the error.<br />Basis Support Package SAPKB64013 deletes the corrupt entries in RSMPTEXTS.</p> <UL><LI><B>SAPKB64013</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: A termination may occur while you are importing Basis Support Package SAPKB64013 during the main import (phase IMPORT_PROPER). The import log shows:<br /> Table RSMPTEXTS: Duplicate record during array insert occured.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: The error is caused by corrupt entries in the RSMPTEXTS table, which are deleted by Support Package SAPKB64013.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: The corrupt entries are deleted successfully despite the error message, and so you can easily continue the import. The error does not recur during the second import attempt.</p> <UL><LI><B>SAPKB64014 - SAPKB64015</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the Basis Support Packages SAPKB64014 and SAPKB64015 are imported together in a queue, an error occurs in the step TEST_IMPORT. The following error message occurs in the test import log of SAPKB64015:<br /> Function TREX_RFC_CONNECTION_CHECK (TREX_ADMIN_TOOL 01) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((TREX_UTIL 24))<br /> Function TREX_RFC_CONNECTION_CHECK_LOCAL (TREX_ADMIN_TOOL 02) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((TREX_UTIL 25))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by choosing 'Extras'-&gt; 'Ignore test-import error'.&#x00A0;&#x00A0;The error will not occur during the later import.</p> <UL><LI><B>SAPKB64015</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Package SAPKB64015, syntax errors occur in the Note Assistant and in the modification adjustment.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: This problem only occurs if you implement the corrections from Note 948389 in Version 0003 (or lower) before the import. The Support Package SAPKB64015 partly undoes the corrections, which leads to syntax errors.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can prevent the problem in different ways:<br />1. The Support Packages SAPKB64015 and SAPKB64018 are imported together in one queue.<br />2. Note 948389 should be implemented at least in Version 0004. As of this version, the corrections that build on each other are compatible, so no syntax errors occur after you import Support Package SAPKB64015. For this reason, check, whether Note 948389 is already implemented in Version 0004 (or higher). If this is not the case, download the latest version with the Note Assistant and implement it.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have already encountered the error, you can correct it by importing Support Package SAPKB64018. If this is not possible, contact SAP Support. In this case, log a Support message under component BC-UPG-NA.</p> <UL><LI><B>SAPKB64017</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Basis Support Package SAPKB64017, an error occurs in phase XPRA_EXECUTION. The import log shows:<br />&#x00A0;&#x00A0;Post-import method AFTER_IMP_SXCI started for SXCI L ...<br />&#x00A0;&#x00A0;Multiple active implementations for definition CTS_REQUEST_CHECK.<br />&#x00A0;&#x00A0;Errors occurred during post-handling AFTER_IMP_SXCI for SXCI L<br />&#x00A0;&#x00A0;The errors affect the following components:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; BC-DWB-CEX (Customer Enhancements)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You must configure the BAdI CTS_REQUEST_CHECK for multiple implementation. To do this, follow the instructions in note 961093.</p> <UL><LI><B>SAPKB64018</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import Basis Support Package SAPKB64018 in the same queue as other Support Packages, or if you import further Support Packages after successfully importing SAPKB64018, the import may terminate in phase XPRA_EXECUTION with return code 0012. The after import method log records that the process terminated with a runtime error after the after import method SRM_FILL_KC_TABLES_AFTER_IMP was started for SRHL objects.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: Recently (Aug, 21, 2006 - September 05, 2006), a version of SAPKB64018 was available, which contained an incorrect implementation of the after import method SRM_FILL_KC_TABLES_AFTER_IMP. You can recognize this Support Package version by the EPS file name CSN0120061532_0024117.PAT in the downloaded CAR archive.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <B>Do not use the incorrect versions of Support Package SAPKB64018!</B>A corrected version has been made available and this can be recognized by the CSN0120061532_0024351.PAT EPS file.<br />If you have already encountered the error, implement Note 967821 to correct the incorrect after import method SRM_FILL_KC_TABLES_AFTER_IMP. This correction is also available in Basis Support Package SAPKB64019.</p> <UL><LI><B>SAPKB64020</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom:When you import Support Package SAPKB64020 into systems with a MaxDB database, the main import (phase \"Import_PROPER\") may terminate (RC=12). The import log contains the following error message:<br /> \"SQL error -8006 accessing ALMONISETS:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data types must be compatible\"<br /><br />This problem occurs after you restart the import.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Update R3trans to at least the version registered with the date March 27, 2007 and continue the import.</p> <UL><LI><B>SAPKB64021</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Package SAPKB64021, it may take a very long time to refresh the TMS QA worklist in the Transport Management System (transaction STMS).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Note 1019572.</p> <UL><LI><B>SAPKB64021</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB64021, syntax errors may occur in the function group PA_PACKAGE_SERVICES. Due to these errors, for example, the development workbench no longer works.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To prevent this error, before you import this Basis Support Packages, use the Note Assistant to implement the most current version of Notes 1013336 and 1136009.<br />If an error has already occurred, contact SAP Support and create a message under the component BC-DWB-UTL.</p> <UL><LI><B>SAPKB64020 - SAPKB64022:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import the Support Packages SAPKB64020, SAPKB64021, SAPKB64022, syntax errors occur in the class CL_GUI_ALV_GRID because corrections from notes have been partially overwritten. This may cause important Basis functions (such as the background systems) to no longer work.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Before you import the specified Basis Support Packages, see the information in Note 1249184.</p> <UL><LI><B>SAPKB64025</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import the Basis Support Package SAPKB64025, problems occur in the validity check of the ALE distribution models. See Note 1398380 for detailed information.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Implement the corrections contained in Note 1398380 and follow the instructions in the note.<br />You can prevent errors by importing the Basis Support Package SAPKB64026 in conjunction with SAPKB64025.</p> <UL><LI><B>SAPKB64026 and SAPKB64027</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Packages SAPKB64026 and SAPKB64027, executing the report RSPO1043 generates inconsistencies in spool requests.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Apply the solution described in Note 1488015. </p> <UL><LI><B>SAPKB64026</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you have implemented Note 1420178, errors occur in the user administration (syntax error in class CL_SUSR_BASIC_TOOLS) after you import Support Package SAPKB64026 and before the modification adjustment. As a result, user name checks in the Support Package Manager no longer work also and the importing of the Support Package cannot be terminated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can avoid the problem if you import the subsequent Support Package SAPKB64027 together with SAPKB64026 because the complete correction of Note 1420178 is then implemented.<br />If you cannot import SAPKB64026 and SAPKB64027 together, you must deimplement Note 1420178 before you import the Support Package. You can then implement it again.</p> <UL><LI><B>SAPKB64027</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Package SAPKB64024, a syntax error may occur in the central program MENUSYST. As a result, you can no longer log on to the SAP system.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed according to the solution provided in Note 1285869.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br /> Based on the remarks above, we recommend the following Basis Support Package queues:<br />SAPKB64001 - SAPKB640.. (highest available Basis Support Package).<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, you need instructions on optimal or permitted queues from those people who produce the add-ons or CRTs used.<br /> <OL>4. <B>ABA Support Packages (SAP_ABA component)</B></OL> <UL><LI><B>SAPKA64003 - SAPKA64004</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the Support Packages SAPKA64003 and SAPKA64004 are imported together in a queue, an error occurs in the TEST_IMPORT step in Support Package SAPKA64004. The test import log of SAPKA64004 contains the following error:<br /> Function MDM_FSBP_MAP_PROXY_TO_DDIC (MDM_FSBP_TEST_MAPPINGS 02)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;does not fit into the existing function group<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;((MDM_FSBP_TEST_PR_CS_MAPPIN 02))<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by choosing 'Extras'-&gt; 'Ignore test-import error'.&#x00A0;&#x00A0;The error will not occur during the later import.</p> <UL><LI><B>SAPKA64004</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the ABAP/Screen generation is activated, an error may occur during the ABAP/Screen generation of SAPKA64004. In the generation log you will find:<br />Program UKCNNNMM: Syntax error in line 000171<br />Key field 'KEY01' has been used more than once. This is not allowed. .<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: This syntax error disappears when the SAP_APPL Support Package SAPKH50002 is imported. To complete the import of the current queue with SAPKA64004, ignore the generation errors by selecting the menu option 'Extras' -&gt; 'Ignore generation errors' and continue importing the queue. Alternatively, you can implement the corrections from Note 741674.</p> <UL><LI><B>SAPKA64020:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Package SAPKA64020, all field modification settings for the business partner category are overwritten or reset to the standard SAP settings.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Note 1107216 describes the cause of the problem and a solution. Take the actions described in this note <B>before</B> you import the Support Package.<br />If you have any questions about this problem, create a problem message in the component CA-GTF-BDT.</p> <UL><LI><B>SAPKA64027</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you import Support Package SAPKA64027, the table BP1030 may be converted. If the table has a very large number of entries, this can take a long time and can critically extend the Support Package import process.<br />Check the number of entries in the table BP1030. If the table has more than 1 million entries, see the information in Note 1527430.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />Based on the previous comments, we recommend the following for ABA Support Package queues:<br />SAPKA64001 - SAPKA640.. (highest available Support Package ABA)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, you need instructions on optimal or permitted queues from those people who produce the add-ons or CRTs used.<br /> <OL>5. <B>BW Support Packages (component SAP_BW)</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You will find information on the BW Support Packages in Note 695423 and in the related notes linked there. You must also always read these notes before you import BW Support Packages. <UL><LI><B>SAPKW35011</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you use a <B>DB2/DB4/DB6</B> or <B>Informix</B> database, an error occurs in the 'Activating inactive runtime objects' step (Move nametabs) when you import the BW Support Package SAPKW35011. You find the following error message in the log for this step:<br />Retcode 1: SQL-error -614-SQL0614N The index or index extension<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"RSCRMAT_EMAIL_T~0\" cannot be created or altered because the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;combined length of the specified columns is too long.<br />SQLSTATE=54008\" in DDL statement for \"RSCRMAT_EMAIL_T\"<br /><br />You will find further information and details in Note 827915.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To prevent this error, Support Package SAPKW35011 <B>must</B> be imported <B>together</B> with BW Support Package SAPKW35012.<br />If this is not possible, you cannot pro-actively prevent the error, rather it can only be eliminated after it has appeared (See Note 827915). In this case, you may not import the BW Support Package in a queue with the Basis or ABA Support Packages, unlike the general stack recommendations!</p> <UL><LI><B>SAPKW35014</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The BW of Support Package SAPKW35014 requires Support Package SAPKIPYJ64 of PI_BASIS 2005_1_640. If necessary, you must therefore upgrade to the new PI_BASIS release (with the Add-On installation tool) before importing the BW Support Package (see also Note 836439).<br /><B>Note the following:</B>As of version 0016, the add-on installation tool can include any Support Packages in an add-on installation/upgrade queue. You can therefore include the PI_BASIS upgrade together with the Support Packages to be imported (PI_BASIS SPs, BW SPs, and so on) in a queue..<br /></p> <OL>6. <B>Application Support Packages (component SAP_APPL)</B></OL> <UL><LI><B>SAPKH50001</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the ABAP/Screen generation is switched on, an error can occur during the ABAP/Screen generation of SAPKH50001. In the generation log you will find:<br />Program /ISDFPS/RIIFLO15, Include MIHIXF10:<br />&#x00A0;&#x00A0;Syntax error in line 000391<br />&#x00A0;&#x00A0;Field 'G_FULL_TREE' is unknown.<br />&#x00A0;&#x00A0;It is neither in one of the specified tables nor defined by a 'DATA'<br />Program SAPMFCX1, Include MF05AFK0_KONTO_PRUEFEN:<br />&#x00A0;&#x00A0;Syntax error in line 000048<br />&#x00A0;&#x00A0;Field 'FIREVDOC' is unknown.<br />&#x00A0;&#x00A0;It is neither in one of the specified tables nor defined by a 'DATA'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: These syntax errors disappear if the EA-APPL Support Package SAPKGPAC01 is imported. To complete the importing of the current queue with SAPKH50001, ignore the generation errors using the menu option 'Extras' -&gt; 'Ignore generation errors' and continue to import the queue.</p> <UL><LI><B>SAPKH50002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: You want to import the Support Package SAPKH50002, but the queue-calculation fails.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Check whether all the Support Packages #01 of the software components contained in SAP ERP Central Component 5.0 have already been imported, or are at least available in the system. These form the prerequisite for the Support Package SAPKH50002, and must be imported irrespective of whether you actively use the relevant software component or not. Note 746576 contains more detailed information.</p> <UL><LI><B>SAPKH50001 - SAPKH50002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the Support Packages SAPKH50001 and SAPKH50002 are imported together in a queue, an error occurs in Support Package SAPKH50002 in the step TEST_IMPORT. In the test import log of SAPKH50002, the following error message appears:<br /> Function FC_COI_SYSTEM_USAGE_GET (FC06 32) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;in the existing function group ((FC17 04))<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by choosing 'Extras'-&gt; 'Ignore test-import error'.&#x00A0;&#x00A0;The error will not occur during the later import.</p> <UL><LI><B>SAPKH50007</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Package SAPKH50007 due to an error in the Support package, serious errors may occur when you process measurement documents.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Implement the corrections from Note 848399, or import Support Package SAPKH50008 as soon as possible.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br /> Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKH50001 - SAPKH500.. (highest available SAP_APPL Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, you need instructions on optimal or permitted queues from those people who produce the add-ons or CRTs used.<br /> <OL>7. <B>HR Support Packages (component SAP_HR)</B></OL> <UL><LI><B>SAPKE50002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: You want to import the HR Support Package SAPKE50002, but the queue calculation fails. Solution:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Check whether all the Support Packages #01 of the software components contained in SAP ERP Central Component 5.0 have already been imported, or are at least available in the system. These form the prerequisite for the Support Package SAPKH50002, and must be imported irrespective of whether you actively use the relevant software component or not. Note 746576 contains more detailed information.</p> <UL><LI><B>SAPKE50004 - SAPKE50008, SAPKE50013, SAPKE50015</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the HR Support Packages SAPKE50004 and SAPKE50008 are imported together in a queue, an error occurs in Support Package SAPKE50008 in the step TEST_IMPORT. The following error message occurs in the test import log of SAPKE50008:<br />&#x00A0;&#x00A0;Function HR_PAL_LOG_AND_ALV_DISPLAY (HRPADPAL00 07) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((HRBAS00PAL 04))<br />&#x00A0;&#x00A0;Function HR_PAL_LOG_DISPLAY (HRPADPAL00 08) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((HRBAS00PAL 05))<br /><br />Similar errors also occur for Support Packages SAPKE50013 and SAPKE50015 if they are also in the queue. Function modules of the function groups HRPADPAL00 and HRBAS00PAL are always affected.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors by choosing 'Extras'-&gt;'Ignore test import error'. These errors no longer occur during subsequent imports.</p> <UL><LI><B>SAPKE50022:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import HR Support Package SAPKE50022, errors occur in the German settlement component (PY-DE-NT-NI). It is no longer possible to send statements of contributions paid by e-mail using report RPCSVHD0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Implement the corrections from SAP Note 958559. </p> <UL><LI><B>SAPKE50026:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problem: HR Support Package SAPKE50026 contains the new database indexes for the PPOIX and PPOPX&#x00A0;&#x00A0;tables. Since the tables may contain a large number of entries, depending upon the application environment, it can take the system a long time to create the indexes on the database during the import process.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: See Note 1012176, and follow the instructions mentioned in that note <B>before the import</B>.</p> <UL><LI><B>SAPKE50030</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom:&#x00A0;&#x00A0;After you import HR Support Package SAPKE50030, SAPscript forms that have their own TrueType fonts are no longer printed correctly. Instead of the customer-defined font types, other font types are printed that are formatted incorrectly (for example, some letters overlap each other).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Note 1030460.</p> <UL><LI><B>SAPKE50039:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For some time, a version of Support Package SAPKE50039 was available whose import conditions did not require Support Packages SAPKW64019, SAPKA64019 and SAPKB64019, even though these Support Packages are required for the system to function correctly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The import conditions of Support Package SAPKE50039 have been enhanced. The correct version of the Support Package is indicated by the file CSN0120061532_0028501.PAT in the EPS inbox (directory /usr/sap/trans/EPS/in).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ensure that you import only this version of the Support Package. If you have already imported the incorrect version, ensure that Support Packages SAPKW64019, SAPKA64019 and SAPKB64019 are imported as quickly as possible.</p> <UL><LI><B>SAPKE50043</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import the HR Support Package, the following error is displayed when you execute the report RPCP01Q0/RPCPSPQ0:<br />&#x00A0;&#x00A0;Font Family 'Arial' is not maintained<br />&#x00A0;&#x00A0;Font Family 'Arial_B' is not maintained<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: The TrueType fonts were deleted by the HR Support Package; for more information, see Note 1132685.</p> <UL><LI><B>SAPKE50048</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: It takes a long time to import the Support Package SAPKE50048. The import process hangs in the phase AUTO_MOD_SPAU for a long time.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Before you import the Support Package, implement Note 1236444 or import the Support Packages SAPKE50048 and SAPKE50051 together in a queue.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br /> Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKE50001 - SAPKE500.. (highest available SAP_HR Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, you need instructions on optimal or permitted queues from those people who produce the add-ons or CRTs used.<br /> <OL>8. <B>Support Packages for application extension EA-APPL 500</B></OL> <UL><LI><B>SAPKGPAC01</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: In rare cases, the process of importing of the SAPKGPAC01 Support Package may terminate in the DDIC_ACTIVATION phase. The activation log displays:<br /> FBICRC01020-RPROC is used in aggregate EF_FBICRC01020<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Deletion is not allowed)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: The DDIC activation program carries out unnecessary tests when deleting DDIC objects (see Note 742219).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors. They no longer occur when the DDIC activation program is run again. Continue importing the Support Package queue.</p> <UL><LI><B>SAPKGPAC02</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: In rare cases, the Support Package SAPKGPAC02 import may terminate in the DDIC_ACTIVATION phase. The activation log displays:<br />&#x00A0;&#x00A0;Table V_RMXT_TRIAL_HD could not be activated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(W- Included search helps RECNCNA and RESCSUA both use hot key A)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors. They no longer occur when the DDIC activation program is run again. Continue importing the Support Package queue.</p> <UL><LI><B>SAPKGPAC08</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Package SAPKGPAC08 into a Unicode system, there is a termination in the DDIC_ACTIVATION phase.<br />The following error messages appear in the activation log:<br />&#x00A0;&#x00A0;Matchcode object \"ESM2\" could not be activated<br />&#x00A0;&#x00A0;Matchcodes are not supported in Unicode systems<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: For a short time (July 01, 2005 - July 13, 2005) an incorrect version of SAPKGPAC08 was available. You can recognize it by the EPS file name CSN0120061532_0021194.PAT in the downloaded CAR archive.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <B>Do not use the incorrect versions of Support Package SAPKGPAC08!</B>A corrected version was made available and this can be recognized by the CSR0120031469_0021490.PAT EPS file.<br />If you have already encountered the activation error, contact SAP Support (component BC-UPG-OCS).</p> <UL><LI><B>SAPKGPAC05 - SAPKGPAC13</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import the SAPKGPAC05 and SAPKGPAC13 Support Packages together in a queue, an error occurs in the DDIC_ACTIVATION phase. The DDIC activation log records the following error messages:<br />&#x00A0;&#x00A0;Indexes \"001\" and \"MEA\" for table \"/DSD/ME_DEL_ITM\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;have identical fields<br />&#x00A0;&#x00A0; Indexes \"001\" and \"MEA\" for table \"/DSD/ME_DEL_ITM\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;have identical fields<br />&#x00A0;&#x00A0; Indexes \"001\" and \"MEA\" for table \"/DSD/ME_DEL_ITM\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;have identical fields<br />&#x00A0;&#x00A0; Indexes \"001\" and \"MEA\" for table \"/DSD/ME_DEL_ITM\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;have identical fields<br />&#x00A0;&#x00A0;Indexes \"001\" and \"MEA\" for table \"/DSD/ME_DEL_ITM\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; have identical fields<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reason: Support Package SAPKGPAC05 creates the MEA indexes, and Support Package SAPKGPAC13 changes the names of the indexes from MEA to 001. As a result of renaming the indexes, the old index is actually deleted and a new index is created. The DDIC activation cannot deal with this situation.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid this error, you <B>must</B> import the Support Packages SAPKGPAC05 and SAPKGPAC13 in separate queues.<br />If this error has already occurred in your system, call transaction SE11 and delete the relevant MEA indexes for the tables /DSD/ME_DEL_ITM, /DSD/ME_INV_ITM, /DSD/ME_ORD_ITM, /DSD/ME_DEL_HD and /DSD/ME_ORD_HD.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />Based on the remarks above, we recommend the following Basis Support Package queues:<br />SAPKGPAC01 - SAPKGPAC12<br />SAPKGPAC13 - SAPKGPAC.. (highest available EA-APPL Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues are only valid if you have not installed any add-ons or do not have to include any CRTs in the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.<br /> <OL>9. <B>Support Packages for Application Extension EA-HR 500</B></OL> <UL><LI><B>SAPKGPHC02, SAPKGPHC03</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: Depending on the content of the EA-HR Support Packages SAPKGPHC02 and SAPKGPHC03, serious activation errors may occur during import.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: It is essential that you read Note 780475 before importing the EA-HR Support Packages, and import at least the Basis Support Package SAPKB64004 beforehand.<br />Any activation errors of the type:<br />&#x00A0;&#x00A0;Structure \"TPHOTEL_PROPERTY_DESCRIPTION35\" is part of a cycle<br />can by solved by reactivating the DDIC.</p> <UL><LI><B>SAPKGPHC06 - SAPKGPHC19</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the Support Packages SAPKGPHC06 and SAPKGPHC19 are imported together in a queue, an error occurs in the Support Package SAPKGPHC19 in the step TEST_IMPORT. You can find the following error message in the test import log of SAPKGPHC19:<br /> Function HRF_GB_READ_DIM_TAX_BASIS (HRF_GB_READ_DIM_ATTRIBU)<br /> does not fit into the existing function group ((HRF_GB_READ_DIMS))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by choosing 'Extras'-&gt; 'Ignore test-import error'.&#x00A0;&#x00A0;The error will not occur during the later import.</p> <UL><LI><B>SAPKGPHC39:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For some time, a version of Support Package SAPKGPHC39 was available whose import conditions did not require HR Support Package SAPKE50039, even though this Support Package is required for the system to function correctly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The import conditions of Support Package SAPKGPHC39 have been enhanced. The correct version of the Support Package is indicated by the file CSN0120061532_0028500.PAT in the EPS inbox (directory /usr/sap/trans/EPS/in).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ensure that you import only this version of the Support Package. If you have already imported the incorrect version, ensure that HR Support Package SAPKE50039 is imported as quickly as possible.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br /> Based on the remarks above, we recommend the following Basis Support Package queues:<br />SAPKGPHC01 - SAPKGPHC.. (highest EA-HR Support Package available)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues are only valid if you have not installed any add-ons or do not have to include any CRTs in the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.<br /> <OL>10. <B>Support Package for the components SEM-BW and FINBASIS</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can find information on the SEM-BW and Support Package FINBASIS in Note 571354, and the related notes contained there. Always read these notes before you import SEM-BW or FINBASIS Support Packages.<br /><br /></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028597)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028597)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000672651/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000672651/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620"}, {"RefNumber": "970837", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creating methods with Note Assistant - mod infos created", "RefUrl": "/notes/970837"}, {"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821"}, {"RefNumber": "961093", "RefComponent": "BC-CTS-ORG", "RefTitle": "Set 'Multiple use' flag for BADI CTS_REQUEST_CHECK", "RefUrl": "/notes/961093"}, {"RefNumber": "958559", "RefComponent": "PY-DE-NT-NI", "RefTitle": "Contribution statements cannot be sent by e-mail", "RefUrl": "/notes/958559"}, {"RefNumber": "906008", "RefComponent": "BC-UPG-OCS", "RefTitle": "Missing method implementation after Support Package import", "RefUrl": "/notes/906008"}, {"RefNumber": "892842", "RefComponent": "BC-INS", "RefTitle": "SAPCAR error: format error in header", "RefUrl": "/notes/892842"}, {"RefNumber": "88656", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Duplicate key composite SAP note (Release 4.0-4.6)", "RefUrl": "/notes/88656"}, {"RefNumber": "871846", "RefComponent": "BC-DB-ORA", "RefTitle": "Error in corrections within Notes 865198 / 865478 / 864677", "RefUrl": "/notes/871846"}, {"RefNumber": "865478", "RefComponent": "BC-DB-ORA", "RefTitle": "Lengthy status display for a result area (6xx)", "RefUrl": "/notes/865478"}, {"RefNumber": "864677", "RefComponent": "BC-DB-ORA", "RefTitle": "Lengthy status display for a result area (46x)", "RefUrl": "/notes/864677"}, {"RefNumber": "856624", "RefComponent": "BC-UPG-OCS", "RefTitle": "Indexes for table CDHDR (SAPKB62050/51, SAPKB64011)", "RefUrl": "/notes/856624"}, {"RefNumber": "848399", "RefComponent": "PM-EQM-SF-MPC", "RefTitle": "Measurement docs: Problems after you implement Note 806624", "RefUrl": "/notes/848399"}, {"RefNumber": "841938", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/DB4/DB6: Buffer synchronisation between dw, tp & R3trans", "RefUrl": "/notes/841938"}, {"RefNumber": "837691", "RefComponent": "BC-CCM-BTC", "RefTitle": "Background jobs are deleted in unintentionally", "RefUrl": "/notes/837691"}, {"RefNumber": "828554", "RefComponent": "BW-SYS", "RefTitle": "Incorrect BW MDMP check when upgrading to Basis 6.40", "RefUrl": "/notes/828554"}, {"RefNumber": "827915", "RefComponent": "BW-EI-RTR", "RefTitle": "RSCRM - applying SP11 dumps - RSCRMAT_EMAIL_T", "RefUrl": "/notes/827915"}, {"RefNumber": "816523", "RefComponent": "BC-CTS-ORG", "RefTitle": "Termination when a user logs on: \"No authorization\"", "RefUrl": "/notes/816523"}, {"RefNumber": "810081", "RefComponent": "CRM-MW-SPM", "RefTitle": "Import of Support Packages fails", "RefUrl": "/notes/810081"}, {"RefNumber": "788623", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0 SR1", "RefUrl": "/notes/788623"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "780475", "RefComponent": "BC-UPG-OCS", "RefTitle": "Risk of activation errors with EA HR 500 Support Package 3", "RefUrl": "/notes/780475"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "753627", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/753627"}, {"RefNumber": "746576", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Central Component 5.0: Installation/upgrade with SP1", "RefUrl": "/notes/746576"}, {"RefNumber": "743408", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Runtime error ASSIGN_LENGTH_0 in XPRAS_UPG phase", "RefUrl": "/notes/743408"}, {"RefNumber": "742219", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Error when deleting ABAP Dictionary objects", "RefUrl": "/notes/742219"}, {"RefNumber": "741674", "RefComponent": "EC-EIS-DB", "RefTitle": "EC-EIS: Error in generation template UKCNNNMM: Double key", "RefUrl": "/notes/741674"}, {"RefNumber": "731265", "RefComponent": "BC-DB-MSS", "RefTitle": "BindInputParams: the parameter is incorrect", "RefUrl": "/notes/731265"}, {"RefNumber": "727149", "RefComponent": "BC-SRV-GBT-ALM", "RefTitle": "Alert classifications deleted by Support Package", "RefUrl": "/notes/727149"}, {"RefNumber": "726147", "RefComponent": "BC-INS", "RefTitle": "SRM 4.0 Installation: Composite Note", "RefUrl": "/notes/726147"}, {"RefNumber": "715355", "RefComponent": "BC-CCM-BTC", "RefTitle": "BP_JOB_SELECT: Optimizing the job selection", "RefUrl": "/notes/715355"}, {"RefNumber": "695423", "RefComponent": "BW", "RefTitle": "BW 3.5: Support Package information", "RefUrl": "/notes/695423"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "672652", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems in transaction SAINT in Basis Release 6.40", "RefUrl": "/notes/672652"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "627246", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Error during creation of objects: ORA-02168", "RefUrl": "/notes/627246"}, {"RefNumber": "571354", "RefComponent": "FIN-SEM", "RefTitle": "SEM-BW, FINBASIS: problems during support package import", "RefUrl": "/notes/571354"}, {"RefNumber": "517484", "RefComponent": "BC-MID-ICF", "RefTitle": "Inactive services in the Internet Communication Framework", "RefUrl": "/notes/517484"}, {"RefNumber": "211077", "RefComponent": "BC-UPG-PRP", "RefTitle": "Replacement of target release kernel for upgrade/EHPI", "RefUrl": "/notes/211077"}, {"RefNumber": "1527430", "RefComponent": "FS-BP", "RefTitle": "BP_LEG: Database conversion of the table BP1030", "RefUrl": "/notes/1527430"}, {"RefNumber": "1491290", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing synchronization after importing include deletions", "RefUrl": "/notes/1491290"}, {"RefNumber": "1488015", "RefComponent": "BC-CCM-PRN", "RefTitle": "Spool consistency check creates inconsistencies", "RefUrl": "/notes/1488015"}, {"RefNumber": "1406740", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Potentially unnoticed rollback during array operation", "RefUrl": "/notes/1406740"}, {"RefNumber": "1285869", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Program MENUSYST: Syntax error or session termination", "RefUrl": "/notes/1285869"}, {"RefNumber": "1249184", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV Grid: SYNTAX_ERROR in class CL_GUI_ALV_GRID", "RefUrl": "/notes/1249184"}, {"RefNumber": "1232804", "RefComponent": "BC-UPG-OCS", "RefTitle": "SPAM/SAINT: TP_FAILURE 0212 in phase IMPORT_OBJECT_LIST", "RefUrl": "/notes/1232804"}, {"RefNumber": "1136009", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Performance improved when accessing ABAP Workbench objects", "RefUrl": "/notes/1136009"}, {"RefNumber": "1132685", "RefComponent": "PY-XX", "RefTitle": "Deletion of Arial True type font used in Payment/ETP summary", "RefUrl": "/notes/1132685"}, {"RefNumber": "1107216", "RefComponent": "CA-GTF-BDT", "RefTitle": "BP_BDT: Field modification overwritten by Support Package", "RefUrl": "/notes/1107216"}, {"RefNumber": "1074030", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: \"Duplicate key\" composite SAP Note (as of Release 6.10)", "RefUrl": "/notes/1074030"}, {"RefNumber": "1013336", "RefComponent": "BC-DWB-UTL", "RefTitle": "Performance problems with Enterprise Add-On Switches", "RefUrl": "/notes/1013336"}, {"RefNumber": "1012176", "RefComponent": "BW-BCT-PY", "RefTitle": "Indexes for tables PPOIX and PPOPX", "RefUrl": "/notes/1012176"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1285869", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Program MENUSYST: Syntax error or session termination", "RefUrl": "/notes/1285869 "}, {"RefNumber": "211077", "RefComponent": "BC-UPG-PRP", "RefTitle": "Replacement of target release kernel for upgrade/EHPI", "RefUrl": "/notes/211077 "}, {"RefNumber": "726147", "RefComponent": "BC-INS", "RefTitle": "SRM 4.0 Installation: Composite Note", "RefUrl": "/notes/726147 "}, {"RefNumber": "715355", "RefComponent": "BC-CCM-BTC", "RefTitle": "BP_JOB_SELECT: Optimizing the job selection", "RefUrl": "/notes/715355 "}, {"RefNumber": "970837", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creating methods with Note Assistant - mod infos created", "RefUrl": "/notes/970837 "}, {"RefNumber": "1555660", "RefComponent": "IS-REA", "RefTitle": "IS-REA 5.10: Installing Support Package 04", "RefUrl": "/notes/1555660 "}, {"RefNumber": "1249184", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV Grid: SYNTAX_ERROR in class CL_GUI_ALV_GRID", "RefUrl": "/notes/1249184 "}, {"RefNumber": "1406740", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Potentially unnoticed rollback during array operation", "RefUrl": "/notes/1406740 "}, {"RefNumber": "810081", "RefComponent": "CRM-MW-SPM", "RefTitle": "Import of Support Packages fails", "RefUrl": "/notes/810081 "}, {"RefNumber": "1527430", "RefComponent": "FS-BP", "RefTitle": "BP_LEG: Database conversion of the table BP1030", "RefUrl": "/notes/1527430 "}, {"RefNumber": "1488015", "RefComponent": "BC-CCM-PRN", "RefTitle": "Spool consistency check creates inconsistencies", "RefUrl": "/notes/1488015 "}, {"RefNumber": "1491290", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing synchronization after importing include deletions", "RefUrl": "/notes/1491290 "}, {"RefNumber": "1074030", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: \"Duplicate key\" composite SAP Note (as of Release 6.10)", "RefUrl": "/notes/1074030 "}, {"RefNumber": "695423", "RefComponent": "BW", "RefTitle": "BW 3.5: Support Package information", "RefUrl": "/notes/695423 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "1107216", "RefComponent": "CA-GTF-BDT", "RefTitle": "BP_BDT: Field modification overwritten by Support Package", "RefUrl": "/notes/1107216 "}, {"RefNumber": "816523", "RefComponent": "BC-CTS-ORG", "RefTitle": "Termination when a user logs on: \"No authorization\"", "RefUrl": "/notes/816523 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "1132685", "RefComponent": "PY-XX", "RefTitle": "Deletion of Arial True type font used in Payment/ETP summary", "RefUrl": "/notes/1132685 "}, {"RefNumber": "1232804", "RefComponent": "BC-UPG-OCS", "RefTitle": "SPAM/SAINT: TP_FAILURE 0212 in phase IMPORT_OBJECT_LIST", "RefUrl": "/notes/1232804 "}, {"RefNumber": "1013336", "RefComponent": "BC-DWB-UTL", "RefTitle": "Performance problems with Enterprise Add-On Switches", "RefUrl": "/notes/1013336 "}, {"RefNumber": "627246", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Error during creation of objects: ORA-02168", "RefUrl": "/notes/627246 "}, {"RefNumber": "837691", "RefComponent": "BC-CCM-BTC", "RefTitle": "Background jobs are deleted in unintentionally", "RefUrl": "/notes/837691 "}, {"RefNumber": "906008", "RefComponent": "BC-UPG-OCS", "RefTitle": "Missing method implementation after Support Package import", "RefUrl": "/notes/906008 "}, {"RefNumber": "864677", "RefComponent": "BC-DB-ORA", "RefTitle": "Lengthy status display for a result area (46x)", "RefUrl": "/notes/864677 "}, {"RefNumber": "88656", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Duplicate key composite SAP note (Release 4.0-4.6)", "RefUrl": "/notes/88656 "}, {"RefNumber": "1136009", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Performance improved when accessing ABAP Workbench objects", "RefUrl": "/notes/1136009 "}, {"RefNumber": "993629", "RefComponent": "IS-REA", "RefTitle": "IS-REA 5.10: Installation of Support Package 02", "RefUrl": "/notes/993629 "}, {"RefNumber": "1114926", "RefComponent": "IS-REA", "RefTitle": "IS-REA 5.10: Installation Support Package 03", "RefUrl": "/notes/1114926 "}, {"RefNumber": "848399", "RefComponent": "PM-EQM-SF-MPC", "RefTitle": "Measurement docs: Problems after you implement Note 806624", "RefUrl": "/notes/848399 "}, {"RefNumber": "958559", "RefComponent": "PY-DE-NT-NI", "RefTitle": "Contribution statements cannot be sent by e-mail", "RefUrl": "/notes/958559 "}, {"RefNumber": "961093", "RefComponent": "BC-CTS-ORG", "RefTitle": "Set 'Multiple use' flag for BADI CTS_REQUEST_CHECK", "RefUrl": "/notes/961093 "}, {"RefNumber": "892842", "RefComponent": "BC-INS", "RefTitle": "SAPCAR error: format error in header", "RefUrl": "/notes/892842 "}, {"RefNumber": "1012176", "RefComponent": "BW-BCT-PY", "RefTitle": "Indexes for tables PPOIX and PPOPX", "RefUrl": "/notes/1012176 "}, {"RefNumber": "672652", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems in transaction SAINT in Basis Release 6.40", "RefUrl": "/notes/672652 "}, {"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821 "}, {"RefNumber": "788623", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0 SR1", "RefUrl": "/notes/788623 "}, {"RefNumber": "882097", "RefComponent": "IS-REA", "RefTitle": "IS-REA 5.10: Installation note", "RefUrl": "/notes/882097 "}, {"RefNumber": "938151", "RefComponent": "IS-REA", "RefTitle": "IS-REA 5.10: Installing Support Package 01", "RefUrl": "/notes/938151 "}, {"RefNumber": "856624", "RefComponent": "BC-UPG-OCS", "RefTitle": "Indexes for table CDHDR (SAPKB62050/51, SAPKB64011)", "RefUrl": "/notes/856624 "}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620 "}, {"RefNumber": "865478", "RefComponent": "BC-DB-ORA", "RefTitle": "Lengthy status display for a result area (6xx)", "RefUrl": "/notes/865478 "}, {"RefNumber": "871846", "RefComponent": "BC-DB-ORA", "RefTitle": "Error in corrections within Notes 865198 / 865478 / 864677", "RefUrl": "/notes/871846 "}, {"RefNumber": "828554", "RefComponent": "BW-SYS", "RefTitle": "Incorrect BW MDMP check when upgrading to Basis 6.40", "RefUrl": "/notes/828554 "}, {"RefNumber": "827915", "RefComponent": "BW-EI-RTR", "RefTitle": "RSCRM - applying SP11 dumps - RSCRMAT_EMAIL_T", "RefUrl": "/notes/827915 "}, {"RefNumber": "841938", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/DB4/DB6: Buffer synchronisation between dw, tp & R3trans", "RefUrl": "/notes/841938 "}, {"RefNumber": "780475", "RefComponent": "BC-UPG-OCS", "RefTitle": "Risk of activation errors with EA HR 500 Support Package 3", "RefUrl": "/notes/780475 "}, {"RefNumber": "743408", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Runtime error ASSIGN_LENGTH_0 in XPRAS_UPG phase", "RefUrl": "/notes/743408 "}, {"RefNumber": "571354", "RefComponent": "FIN-SEM", "RefTitle": "SEM-BW, FINBASIS: problems during support package import", "RefUrl": "/notes/571354 "}, {"RefNumber": "746576", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Central Component 5.0: Installation/upgrade with SP1", "RefUrl": "/notes/746576 "}, {"RefNumber": "731265", "RefComponent": "BC-DB-MSS", "RefTitle": "BindInputParams: the parameter is incorrect", "RefUrl": "/notes/731265 "}, {"RefNumber": "727149", "RefComponent": "BC-SRV-GBT-ALM", "RefTitle": "Alert classifications deleted by Support Package", "RefUrl": "/notes/727149 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}