{"Request": {"Number": "818359", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 527, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004415252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000818359?language=E&token=26091F0D4DDCA90CFFD12DC888B60FC5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000818359", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000818359/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "818359"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.02.2005"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "818359 - Legal change in HBRDIRF0 for year 2005"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Legal change on DIRF of year 2005, reference year 2004.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>HBRDIRF0; 2005; HBRUTMS5; declaracao imposto retido fonte, Brasil,<br />Brazil</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>\"Instrucao Normativa SRF n&#x00B0;493\" of January 13th, 2005 has changed the<br />layout of DIRF file.<br />In specific art.13 defines the report of R$100(hundred reais) related to law 10.996 of December 15th, 2004.<br /><br /><br />\"Instrucao Normativa SRF n&#x00B0;511\" of February 15th, 2005 has defined that<br />only employees with income greater than R$ 6000,00(six thousand reais)<br />should be reported.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In order to have the R$100(hundred reais) refered in art.13 of \"Instrucao Normativa SRF n&#x00B0;493\" of January 13th, 2005 correctly reported the evaluation classes 07 and 15 have to be customized to consider the<br />wage types you have used to process this discount.<br /><br /><br />The correction described in this note will be included in an HR Support<br />Package, as indicated in item \"Reference to Support Packages\".<br /><br />The support package includes:<br /></p> <UL><LI>new constant DIRFL which has value 6000,00 as it is the actual legal limit of income for an employee to be reported;</LI></UL> <UL><LI>new domains PBR_DCLDEP and&#x00A0;&#x00A0;PBR_EVETP;</LI></UL> <UL><LI>change in data element PBR_FIL01;</LI></UL> <UL><LI>new data elements PBR_DCLDEP, PBR_EVEDT, PBR_EVETP and PBR_FIL28;</LI></UL> <UL><LI>new structure HBRDF1_2005;</LI></UL> <UL><LI>change in includes HBRDIRF0, PCDIFBR0, PCDIFBRD, PUTMDBR0 and PUTMMBR2.</LI></UL> <p><br /><br />An Advanced Delivery with new objects of Data Dictionary is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /></p> <UL><LI>L6DKxxxxxx_500.CAR - Release ERP 2004</LI></UL> <UL><LI>L6BKxxxxxx_470.CAR - Release 4.70(Enterprise)</LI></UL> <UL><LI>L9CKxxxxxx_46C.CAR - Release 4.6C</LI></UL> <UL><LI>L9BKxxxxxx_46B.CAR - Release 4.6B</LI></UL> <UL><LI>L4DKxxxxxx_45B.CAR - Release 4.5B</LI></UL> <p><br />For more details about Advance Delivery installation procedure please<br />read the notes listed in \"Related Notes\".<br /><br /><br />In the solution delivered for each release by the first version of this<br />note an issue was found and is solved as described below:<br /></p> <UL><LI>field \"Data do evento\" was not initialized with spaces, the solution is described in Correction Instruction 0120031469 0000375566.</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D034789"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I810942)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818359/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L4DK119632_45B.CAR", "FileSize": "5", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000139192005&iv_version=0007&iv_guid=671D90B4B2FD414BBC48845E35FDDEAB"}, {"FileName": "L9CK182291_46C.CAR", "FileSize": "5", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000139192005&iv_version=0007&iv_guid=F7C24F13B493FB41966298470468A217"}, {"FileName": "L6DK018844_500.CAR", "FileSize": "5", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000139192005&iv_version=0007&iv_guid=85946B3D0CE7CE4BBDEA960F157174C1"}, {"FileName": "L6BK086511_470.CAR", "FileSize": "5", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000139192005&iv_version=0007&iv_guid=D13AE21BE107E644A064FBFBBD42B2D1"}, {"FileName": "L9BK125611_46B.CAR", "FileSize": "6", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000139192005&iv_version=0007&iv_guid=F65A59EE7BB56C4C92E3AF0A267088FC"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BC2", "URL": "/supportpackage/SAPKE45BC2"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BA4", "URL": "/supportpackage/SAPKE46BA4"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C95", "URL": "/supportpackage/SAPKE46C95"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C96", "URL": "/supportpackage/SAPKE46C96"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47040", "URL": "/supportpackage/SAPKE47040"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47041", "URL": "/supportpackage/SAPKE47041"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50008", "URL": "/supportpackage/SAPKE50008"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 6, "URL": "/corrins/0000818359/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 30, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "489631 ", "URL": "/notes/489631 ", "Title": "HBRDIRF: Legal Change 2001", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "493326 ", "URL": "/notes/493326 ", "Title": "HBRDIRF: O displayed instead of an 0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "496808 ", "URL": "/notes/496808 ", "Title": "HBRDIRF: Ref Year and Calender Year should be both custom.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "497796 ", "URL": "/notes/497796 ", "Title": "HBRDIRF,CCED: 13DI of Jan next y. is not take into account", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "518430 ", "URL": "/notes/518430 ", "Title": "HBRDIRF0: Reg3 is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "554348 ", "URL": "/notes/554348 ", "Title": "HBRDIRF,HBRCCED coding redesign", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "591739 ", "URL": "/notes/591739 ", "Title": "HBRDIRF0/HBRCCED0: 13th DI prob., \"Natureza do declarante\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "593388 ", "URL": "/notes/593388 ", "Title": "HBRDIRF0: Register 3 (REG3) is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "598275 ", "URL": "/notes/598275 ", "Title": "HBRCCED0, H<PERSON><PERSON>RF0, HBRRTER1: negative value treatment", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "708247 ", "URL": "/notes/708247 ", "Title": "HBRDIRF0: while processing all off-cycles,depend.calc twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "381758 ", "URL": "/notes/381758 ", "Title": "HBRUTMS no display for 2001 DIRF reports", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "528719 ", "URL": "/notes/528719 ", "Title": "HBRDIRF does not bring all information", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "697199 ", "URL": "/notes/697199 ", "Title": "DIRF 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "181399 ", "URL": "/notes/181399 ", "Title": "DARF wagetypes accounting", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "197389 ", "URL": "/notes/197389 ", "Title": "HBRDIRF0 - acertos gerais", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "211535 ", "URL": "/notes/211535 ", "Title": "SEFIP - Alter. para nova versao", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "302941 ", "URL": "/notes/302941 ", "Title": "HBRCAGED - Atualização para a versão 1.16", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "316684 ", "URL": "/notes/316684 ", "Title": "HBRDIRF0 - Acerto dados responsável", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "366301 ", "URL": "/notes/366301 ", "Title": "Correções Informe de Rendimentos e DIRF", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "373920 ", "URL": "/notes/373920 ", "Title": "HBRCCED0, HBRDIRF0 - Acerto de períodos na tela e deduções", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "379549 ", "URL": "/notes/379549 ", "Title": "Legal change DIRF 2001", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "381485 ", "URL": "/notes/381485 ", "Title": "HBRDIRF IDTAX preset wrong, third struct unnecessary", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "382607 ", "URL": "/notes/382607 ", "Title": "HBRDIRF0, HBRCCED0, acertos mudança de empresa", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "645931 ", "URL": "/notes/645931 ", "Title": "HBRDIRF0: Incorrect handling of hiring and firing dates", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "721258 ", "URL": "/notes/721258 ", "Title": "HBRDIRF0: Depend. deduction only calc. in first month", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "489631 ", "URL": "/notes/489631 ", "Title": "HBRDIRF: Legal Change 2001", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "493326 ", "URL": "/notes/493326 ", "Title": "HBRDIRF: O displayed instead of an 0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "496808 ", "URL": "/notes/496808 ", "Title": "HBRDIRF: Ref Year and Calender Year should be both custom.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "497796 ", "URL": "/notes/497796 ", "Title": "HBRDIRF,CCED: 13DI of Jan next y. is not take into account", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "518430 ", "URL": "/notes/518430 ", "Title": "HBRDIRF0: Reg3 is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "554348 ", "URL": "/notes/554348 ", "Title": "HBRDIRF,HBRCCED coding redesign", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "591739 ", "URL": "/notes/591739 ", "Title": "HBRDIRF0/HBRCCED0: 13th DI prob., \"Natureza do declarante\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "593388 ", "URL": "/notes/593388 ", "Title": "HBRDIRF0: Register 3 (REG3) is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "598275 ", "URL": "/notes/598275 ", "Title": "HBRCCED0, H<PERSON><PERSON>RF0, HBRRTER1: negative value treatment", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "708247 ", "URL": "/notes/708247 ", "Title": "HBRDIRF0: while processing all off-cycles,depend.calc twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "380722 ", "URL": "/notes/380722 ", "Title": "HBRUTMS0 version handling is missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "489631 ", "URL": "/notes/489631 ", "Title": "HBRDIRF: Legal Change 2001", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "493326 ", "URL": "/notes/493326 ", "Title": "HBRDIRF: O displayed instead of an 0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "496808 ", "URL": "/notes/496808 ", "Title": "HBRDIRF: Ref Year and Calender Year should be both custom.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "497796 ", "URL": "/notes/497796 ", "Title": "HBRDIRF,CCED: 13DI of Jan next y. is not take into account", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "528719 ", "URL": "/notes/528719 ", "Title": "HBRDIRF does not bring all information", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "554348 ", "URL": "/notes/554348 ", "Title": "HBRDIRF,HBRCCED coding redesign", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "591739 ", "URL": "/notes/591739 ", "Title": "HBRDIRF0/HBRCCED0: 13th DI prob., \"Natureza do declarante\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "593388 ", "URL": "/notes/593388 ", "Title": "HBRDIRF0: Register 3 (REG3) is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "598275 ", "URL": "/notes/598275 ", "Title": "HBRCCED0, H<PERSON><PERSON>RF0, HBRRTER1: negative value treatment", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "708247 ", "URL": "/notes/708247 ", "Title": "HBRDIRF0: while processing all off-cycles,depend.calc twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "470", "Number": "518430 ", "URL": "/notes/518430 ", "Title": "HBRDIRF0: Reg3 is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "470", "Number": "697199 ", "URL": "/notes/697199 ", "Title": "DIRF 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "554348 ", "URL": "/notes/554348 ", "Title": "HBRDIRF,HBRCCED coding redesign", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "591739 ", "URL": "/notes/591739 ", "Title": "HBRDIRF0/HBRCCED0: 13th DI prob., \"Natureza do declarante\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "593388 ", "URL": "/notes/593388 ", "Title": "HBRDIRF0: Register 3 (REG3) is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "598275 ", "URL": "/notes/598275 ", "Title": "HBRCCED0, H<PERSON><PERSON>RF0, HBRRTER1: negative value treatment", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "708247 ", "URL": "/notes/708247 ", "Title": "HBRDIRF0: while processing all off-cycles,depend.calc twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}