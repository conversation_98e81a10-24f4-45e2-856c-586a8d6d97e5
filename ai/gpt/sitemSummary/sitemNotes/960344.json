{"Request": {"Number": "960344", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 522, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005658542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000960344?language=E&token=29079985CE9C33BA5F921F642762A821"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000960344", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000960344/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "960344"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.09.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP-HIER"}, "SAPComponentKeyText": {"_label": "Component", "value": "Using Hierarchies"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Using Hierarchies", "value": "BW-BEX-OT-OLAP-HIER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP-HIER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "960344 - Hierarchy class is instantiated although (still) not used"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The CL_RSR_HIERARCHY_BINCL hierarchy class is instantiated, even though the characteristic has not been drilled down or fixed or dynamically filtered.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Query, hierarchy<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Up to now, this situation has not yet been caught appropriately.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>If this correction was implemented and if BW 3.0B Patch 32 or BW 3.10 Patch 26 was then imported, you must implement this correction again on BW 3.0B Patch 32.</B></p> <UL><LI>BW 3.0B</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 33 for 3.0B (BW 3.0B Patch 33 or <B>SAPKW30B33</B>) into your BW system. The Support Package is available once <B>Note 914950</B> \"SAPBWNews BW 3.0B Support Package 33\", which describes this Support Package in more detail, has been released for customers.</p> <UL><LI>BW 3.10 Content</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 27 for 3.10 (BW 3.10 Patch 27 or <B>SAPKW31027</B>) into your BW system. The Support Package is available once <B>Note 935963</B> \"SAPBWNews BW 3.1 Content Support Package 27\", which describes this Support Package in more detail, has been released for customers.</p> <UL><LI>BW 3.50</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 18 for 3.5 (BW 3.50 Patch 18 or <B> SAPKW35018</B>) into your BW system. The Support Package is available once <B>Note 928661</B> \"SAPBWNews BW Support Package18 NetWeaver'04 Stack 18\", which describes this Support Package in more detail, has been released for customers.</p> <UL><LI>BW 7.0</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 09 for 7.0 (BW 7.0 Patch 09 or <B> SAPKW70009</B>) into your BW system. The Support Package is available once <B>Note 914303</B> \"SAPBINews BW 7.0 Support Package 09\", which describes this Support Package in more detail, is released for customers.<br /><br /> <br />To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET (Enduser Technology)"}, {"Key": "Responsible                                                                                         ", "Value": "D017461"}, {"Key": "Processor                                                                                           ", "Value": "D017461"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000960344/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000960344/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "987135", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "X299 in CL_RSR_RRK0_HIERARCHY and APPLY_SLICER_NODE-01-", "RefUrl": "/notes/987135"}, {"RefNumber": "977912", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "X299 Brain in SAPLRRK0; NACHLESEN_INIT_NODE-01-", "RefUrl": "/notes/977912"}, {"RefNumber": "976689", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Various terminations after deactivation of hierarchy", "RefUrl": "/notes/976689"}, {"RefNumber": "935962", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 26", "RefUrl": "/notes/935962"}, {"RefNumber": "928851", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Performance when you drill down a character. with hierarchy", "RefUrl": "/notes/928851"}, {"RefNumber": "928661", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 18 NW'04 Stack 18 RIN", "RefUrl": "/notes/928661"}, {"RefNumber": "925960", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "x299 Brain in CL_RSR_HIERARCHY_BINCL; OLAP_DYN_FILTER_SET", "RefUrl": "/notes/925960"}, {"RefNumber": "914949", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 32", "RefUrl": "/notes/914949"}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303 "}, {"RefNumber": "928661", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 18 NW'04 Stack 18 RIN", "RefUrl": "/notes/928661 "}, {"RefNumber": "935962", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 26", "RefUrl": "/notes/935962 "}, {"RefNumber": "914949", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 32", "RefUrl": "/notes/914949 "}, {"RefNumber": "987135", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "X299 in CL_RSR_RRK0_HIERARCHY and APPLY_SLICER_NODE-01-", "RefUrl": "/notes/987135 "}, {"RefNumber": "977912", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "X299 Brain in SAPLRRK0; NACHLESEN_INIT_NODE-01-", "RefUrl": "/notes/977912 "}, {"RefNumber": "976689", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Various terminations after deactivation of hierarchy", "RefUrl": "/notes/976689 "}, {"RefNumber": "925960", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "x299 Brain in CL_RSR_HIERARCHY_BINCL; OLAP_DYN_FILTER_SET", "RefUrl": "/notes/925960 "}, {"RefNumber": "928851", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Performance when you drill down a character. with hierarchy", "RefUrl": "/notes/928851 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B39INVCBWTECH", "URL": "/supportpackage/SAPK-30B39INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B33", "URL": "/supportpackage/SAPKW30B33"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B32", "URL": "/supportpackage/SAPKW30B32"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31026", "URL": "/supportpackage/SAPKW31026"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31027", "URL": "/supportpackage/SAPKW31027"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31026", "URL": "/supportpackage/SAPKW31026"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35019", "URL": "/supportpackage/SAPKW35019"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35018", "URL": "/supportpackage/SAPKW35018"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "NumberOfCorrin": 2, "URL": "/corrins/0000960344/654"}, {"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 2, "URL": "/corrins/0000960344/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "30B", "ValidTo": "310", "Number": "928851 ", "URL": "/notes/928851 ", "Title": "Performance when you drill down a character. with hierarchy", "Component": "BW-BEX-ET-WEB"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "350", "ValidTo": "350", "Number": "928851 ", "URL": "/notes/928851 ", "Title": "Performance when you drill down a character. with hierarchy", "Component": "BW-BEX-ET-WEB"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "920101 ", "URL": "/notes/920101 ", "Title": "x 299 Brain in CL_RSR_RRK0_HIERARCHY; form 'HIERA_DELETE-01-", "Component": "BW-BEX-OT-OLAP-HIER"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "930923 ", "URL": "/notes/930923 ", "Title": "Refresh query with variable values and display hierarchy", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "30B", "ValidTo": "30B", "Number": "925960 ", "URL": "/notes/925960 ", "Title": "x299 Brain in CL_RSR_HIERARCHY_BINCL; OLAP_DYN_FILTER_SET", "Component": "BW-BEX-OT-OLAP"}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "30B", "ValidTo": "30B", "Number": "960344 ", "URL": "/notes/960344 ", "Title": "Hierarchy class is instantiated although (still) not used", "Component": "BW-BEX-OT-OLAP-HIER"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928851", "RefTitle": "Performance when you drill down a character. with hierarchy", "RefUrl": "/notes/0000928851"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "977912", "RefTitle": "X299 Brain in SAPLRRK0; NACHLESEN_INIT_NODE-01-", "RefUrl": "/notes/0000977912"}]}}}}}