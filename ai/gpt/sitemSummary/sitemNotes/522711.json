{"Request": {"Number": "522711", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015220712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000522711?language=E&token=82912334E4EF0A2DECB2ED4737F0AEA5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000522711", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000522711/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "522711"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 152}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.04.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade - general"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "522711 - Corrections for upgrade to Basis 620"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to correct the ABAP upgrade programs or update the control files.<br />This note is valid for all products based on the SAP Web Application Server Release 6.20.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>620, upgrade, shadow system, shadow instance, SHD_FIX_IMP, TOOLIMP4_FIX, TOOLIMP6_FIX, TOOLFIX_CHK, IS_MOVES.LST<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note describes how to add the repairs to the upgrade procedure.<br />Applying this note prevents the occurrence of miscellaneous errors, upgrade shutdowns, the loss of data and long upgrade runtimes.<br /><B>It is essential that you implement this note: If you do not implement this note, you risk losing data, creating data inconsistencies and shutting down the upgrade.</B><br />Also see Note <B>517731</B> for the correct R3up version.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The upgrade correction is provided as an SAR archive that you must integrate into the upgrade. <B>Caution:</B> Each SAR archive is only valid for one product (- specifically one delivered CD set). Therefore, there is <B>just one</B> valid SAR archive for each product. You must reset the upgrade if you apply the wrong archive.<br /><br /><B>Attention: The archive is no longer on the sapservX servers, but is available from the SAP Service Marketplace.</B><br /><br />The SAR archives are available on the SAP Service Marketplace in the following area:<br />http://service.sap.com/patches -&gt; Entry by Application Group -&gt;<br />Additional Components -&gt; Upgrade Tools -&gt; Corrections for Upgrade -&gt;<br />CORRECTIONS FOR UPGRADE 6.20 -&gt; #OS independent<br />or directly under<br />http://www.service.sap.com/~form/handler?_APP=00200682500000001943&amp; _EVENT=DISPHIER&amp;HEADER=N&amp;FUNCTIONBAR=N&amp;EVENT=TREE&amp;TMPL=01200314690200004617&amp;V=MAINT<br /><br /></p> <b>Selecting the correction package</b><br /> <p>The following table tells you which archive you need and how to integrate it into the upgrade. You can find the archive version in the file &lt;PUTDIR&gt;/bin/TOOLFIX_STAMP or in the column ] \"Patchlevel\" on the Service Marketplace.</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Product&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Correction Package Name</TH></TR> <TR><TD>EBP 3.5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_EBP350.SAR</TD></TR> <TR><TD>WEB AS 6.20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_WEBAS620.SAR</TD></TR> <TR><TD>R/3E 4.70 EXT1&#x00A0;&#x00A0;&#x00A0;&#x00A0; FIX_R3E470.SAR</TD></TR> <TR><TD>CRM 3.10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; FIX_CRM310.SAR</TD></TR> <TR><TD>BW 3.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_BW30B.SAR</TD></TR> <TR><TD>BW 3.1 Content&#x00A0;&#x00A0;&#x00A0;&#x00A0; FIX_BW310.SAR</TD></TR> <TR><TD>EBP 3.5 SR1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_EBP350SR1.SAR</TD></TR> <TR><TD>R/3E 4.70 EXT1 SR1 FIX_R3E470SR1.SAR</TD></TR> <TR><TD>SCM 4.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_SCM401.SAR</TD></TR> <TR><TD>EBP/CRM 4.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_CRM400.SAR</TD></TR> <TR><TD>R/3E 4.70 EXT2&#x00A0;&#x00A0;&#x00A0;&#x00A0; FIX_R3E470EXT2.SAR</TD></TR> <TR><TD>CRM 3.10 SR1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; FIX_CRM310SR1.SAR</TD></TR> <TR><TD>R/3E 4.70 EXT2 SR1 FIX_R3E470EXT2SR1.SAR</TD></TR> <TR><TD>EBP/CRM 4.0 SR1&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_CRM400SR1.SAR</TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> </TABLE> <p>Comments:</p> <UL><LI>The above table lists <B>all</B> the Basis 620 products delivered. If you cannot find your product there, it may be called a different name. For products with R3up Version 620-4 or higher, R3up tells you the archive name in the TOOLFIX_CHK phase so that you can identify it in the above table. If the table shows that there is no fix archive for your product, choose \"ignore\" to continue the TOOLFIX_CHK phase.</LI></UL> <UL><LI>Always use <B>one</B> fix archive only.</LI></UL> <UL><LI>Only import the fix archive FIX_WEBAS620.SAR if you carry out an upgrade to a standalone Web AS (SAP WEB AS 6.20) that no additional main component is installed on. Do not confuse this with other products that are based on SAP WEB AS 6.20, for example, R/3 Enterprise 4.70.</LI></UL> <UL><LI>Service releases featuring improved upgrade procedures and higher product quality are provided for the R/3 Enterprise 4.70, BW and EBP products. Always use the service release CDs for an upgrade.</LI></UL> <UL><LI>If you upgrade to R/3 Enterprise (FIX_R3E470.SAR), you must at least import Basis Support Package 14 (SAPKB62014), otherwise terminations occur later in Transaction SPAM, when you import this Support Package.</LI></UL> <p></p> <b>Integration into the upgrade</b><br /> <p>For R3up versions up to and including 620-3, the package must be integrated into the upgrade manually; as of R3up version 620-4, it is integrated automatically. See Note 517731 to check which R3up you need to use for your product. Always use the most recent R3up from the Service Marketplace.<br /></p> <UL><LI>Automatic integration</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Load the corresponding correction package from the SAP Service Marketplace and save it in the upgrade directory.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Unpack the SAR Archiv from the marketplace by using \"SAPCAR -xvf &lt;name&gt;.SAR\". Two files are unpacked, the actual Fix archive and the change history for reference purposes.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For iSeries also use the option '-h' when unpacking so as to avoid later authorization problems:<br />&#x00A0;&#x00A0;SAPCAR '-xvf &lt;SAR_Archiv&gt; -h'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The actual correction package corresponds to the naming convention \"FIX_*.SAR\" and must <B>not</B> be unpacked manually. Unpacking is carried out automatically by R3up. Do not rename the archive under any circumstances and only use the exact archive that R3up requested. Otherwise there is the risk of losing data.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the TOOLFIX_CHK prepare phase (the \"Import\" or \"Extension\" module), R3up searches the upgrade directory for the right correction package. If R3up is successful, the package is automatically integrated into the upgrade. If R3up does not find a valid package, the system displays a user dialog requesting that you place the correction package in the upgrade directory.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use \"Retry\" to repeat the search or \"Ignore\" to continue with the upgrade without integrating the correction package. You can only use the \"ignore\" option if there is no fix archive for the product according to the above table.<br /></p> <UL><LI>Manual integration (only with old R3up up to version 6.20/3):</LI></UL> <UL><UL><LI>First download the file from the Service Marktplace as described under \"Automatic Integration\" and unpack it so that you get the archive \"FIX_*.SAR\" in the upgrade directory.</LI></UL></UL> <UL><UL><LI>Start <B>PREPARE</B> and run the \"Parameter input\", \"Initialization\" and \"Import\" modules.</LI></UL></UL> <UL><UL><LI>Put the SAR archive in the &lt;DIR_PUT&gt; directory.</LI></UL></UL> <UL><UL><LI>Go to the &lt;DIR_PUT&gt; directory &#x00A0;&#x00A0;cd &lt;DIR_PUT&gt;</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and unpack it there using the SAPCAR tool. &#x00A0;&#x00A0;SAPCAR -xvf FIX_&lt;name&gt;.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For iSeries, also use the option '-h', to avoid subsequent authorization problems: &#x00A0;&#x00A0;SAPCAR '-xvf &lt;SAR_Archiv&gt; -h'</p> <UL><UL><LI>Continue PREPARE with the \"Extension\" module or repeat the \"Extension\" module. <B>Caution:</B> If you repeat the \"Import\" module, control data is unpacked from the CD into the upgrade directory and the corrections are undone. You must unpack the archive again and continue PREPARE using the \"Extension\" module.</LI></UL></UL> <p><br /><br /><br />The archive contains data files and cofiles for the upgrade's transport requests, buffers and control files. Import corrections to the ABAP tools in the source release in phases TOOLIMP4_FIX and TOOLIMP6_FIX (\"Extension\" PREPARE module) (provided these exist). Import corrections for the shadow system in the phase SHD_FIX_IMP (upgrade). Transport requests are imported <B>automatically</B>. <B>Never import requests into your system manually, otherwise you risk losing data.</B></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "SPDD"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D038245)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023890)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000522711/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000522711/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000522711/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000522711/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000522711/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000522711/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000522711/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000522711/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000522711/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "905420", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905420"}, {"RefNumber": "876198", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Several delete/activate actions during import", "RefUrl": "/notes/876198"}, {"RefNumber": "782140", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/782140"}, {"RefNumber": "780985", "RefComponent": "BC-UPG-TLS", "RefTitle": "No versions are created in the shadow system", "RefUrl": "/notes/780985"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "716377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Problem analysis in the upgrade: SPACECHK phases", "RefUrl": "/notes/716377"}, {"RefNumber": "670330", "RefComponent": "BC-UPG-TLS", "RefTitle": "Objects from comparison transports displayed in SPAU", "RefUrl": "/notes/670330"}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240"}, {"RefNumber": "658370", "RefComponent": "BC-DWB-DIC", "RefTitle": "Incomplete renaming of SAP data elements", "RefUrl": "/notes/658370"}, {"RefNumber": "650310", "RefComponent": "SCM-APO-ATP", "RefTitle": "XPRA /SAPCN1/CUST_INDX_TO_CN1 did not convert any data", "RefUrl": "/notes/650310"}, {"RefNumber": "650291", "RefComponent": "SCM-APO-ATP", "RefTitle": "Condition technique: Data lost after upgrade to SAP SCM 4.0", "RefUrl": "/notes/650291"}, {"RefNumber": "650162", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to R/3 WebAs 620 with BANK-TRBK 3.0", "RefUrl": "/notes/650162"}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516"}, {"RefNumber": "640223", "RefComponent": "BC-UPG", "RefTitle": "Application error after 31I -> Enterprise 47X110 upgrade", "RefUrl": "/notes/640223"}, {"RefNumber": "63845", "RefComponent": "XX-SER-NET", "RefTitle": "Corrections on sapserv - searching for files", "RefUrl": "/notes/63845"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "633093", "RefComponent": "SCM-TEC", "RefTitle": "SCM Release 4.0 (installation/upgrade - delivery status)", "RefUrl": "/notes/633093"}, {"RefNumber": "619339", "RefComponent": "BC-DB-SDB", "RefTitle": "ICNV: invalid end of SQL statement, SQL error -3014", "RefUrl": "/notes/619339"}, {"RefNumber": "604728", "RefComponent": "CRM", "RefTitle": "SAP CRM 4.0: Information on Upgrade", "RefUrl": "/notes/604728"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "603278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to SCM 4.0", "RefUrl": "/notes/603278"}, {"RefNumber": "600824", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 4.0 / SRM Server 4.0", "RefUrl": "/notes/600824"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941"}, {"RefNumber": "575170", "RefComponent": "BW-SYS", "RefTitle": "Duplicate key in RSRA_BP_OBJECTST upgrade from 2.x to 3.1", "RefUrl": "/notes/575170"}, {"RefNumber": "571698", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 15", "RefUrl": "/notes/571698"}, {"RefNumber": "571695", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 14", "RefUrl": "/notes/571695"}, {"RefNumber": "569260", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrading to Solution Manager 2.2", "RefUrl": "/notes/569260"}, {"RefNumber": "551845", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Activation error with reference to class/interface", "RefUrl": "/notes/551845"}, {"RefNumber": "545281", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Performance of DD activation", "RefUrl": "/notes/545281"}, {"RefNumber": "544569", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.10", "RefUrl": "/notes/544569"}, {"RefNumber": "541009", "RefComponent": "BC-UPG-TLS", "RefTitle": "Long runtime for the report RSUMOD03", "RefUrl": "/notes/541009"}, {"RefNumber": "535107", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/535107"}, {"RefNumber": "522391", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/522391"}, {"RefNumber": "517731", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for the upgrade to systems with Basis 620", "RefUrl": "/notes/517731"}, {"RefNumber": "517278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 3.1", "RefUrl": "/notes/517278"}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267"}, {"RefNumber": "513536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/513536"}, {"RefNumber": "506339", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.0B", "RefUrl": "/notes/506339"}, {"RefNumber": "493599", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to EBP 3.5", "RefUrl": "/notes/493599"}, {"RefNumber": "490065", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to Web AS 6.20", "RefUrl": "/notes/490065"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "447925", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/447925"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "716377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Problem analysis in the upgrade: SPACECHK phases", "RefUrl": "/notes/716377 "}, {"RefNumber": "782140", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/782140 "}, {"RefNumber": "664712", "RefComponent": "XX-PROJ-CS-BC", "RefTitle": "Migration Cable Solution V46C.1A -> DIMP / ECC-DIMP", "RefUrl": "/notes/664712 "}, {"RefNumber": "545281", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Performance of DD activation", "RefUrl": "/notes/545281 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "571695", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 14", "RefUrl": "/notes/571695 "}, {"RefNumber": "517731", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for the upgrade to systems with Basis 620", "RefUrl": "/notes/517731 "}, {"RefNumber": "619339", "RefComponent": "BC-DB-SDB", "RefTitle": "ICNV: invalid end of SQL statement, SQL error -3014", "RefUrl": "/notes/619339 "}, {"RefNumber": "876198", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Several delete/activate actions during import", "RefUrl": "/notes/876198 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "569260", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrading to Solution Manager 2.2", "RefUrl": "/notes/569260 "}, {"RefNumber": "650162", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to R/3 WebAs 620 with BANK-TRBK 3.0", "RefUrl": "/notes/650162 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "604728", "RefComponent": "CRM", "RefTitle": "SAP CRM 4.0: Information on Upgrade", "RefUrl": "/notes/604728 "}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240 "}, {"RefNumber": "544569", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.10", "RefUrl": "/notes/544569 "}, {"RefNumber": "517278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 3.1", "RefUrl": "/notes/517278 "}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941 "}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267 "}, {"RefNumber": "600824", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 4.0 / SRM Server 4.0", "RefUrl": "/notes/600824 "}, {"RefNumber": "506339", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.0B", "RefUrl": "/notes/506339 "}, {"RefNumber": "603278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to SCM 4.0", "RefUrl": "/notes/603278 "}, {"RefNumber": "493599", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to EBP 3.5", "RefUrl": "/notes/493599 "}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516 "}, {"RefNumber": "490065", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to Web AS 6.20", "RefUrl": "/notes/490065 "}, {"RefNumber": "571698", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 15", "RefUrl": "/notes/571698 "}, {"RefNumber": "633093", "RefComponent": "SCM-TEC", "RefTitle": "SCM Release 4.0 (installation/upgrade - delivery status)", "RefUrl": "/notes/633093 "}, {"RefNumber": "670330", "RefComponent": "BC-UPG-TLS", "RefTitle": "Objects from comparison transports displayed in SPAU", "RefUrl": "/notes/670330 "}, {"RefNumber": "447925", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/447925 "}, {"RefNumber": "780985", "RefComponent": "BC-UPG-TLS", "RefTitle": "No versions are created in the shadow system", "RefUrl": "/notes/780985 "}, {"RefNumber": "638303", "RefComponent": "BC-UPG-TLS", "RefTitle": "RDDMCMRG writes empty log", "RefUrl": "/notes/638303 "}, {"RefNumber": "541009", "RefComponent": "BC-UPG-TLS", "RefTitle": "Long runtime for the report RSUMOD03", "RefUrl": "/notes/541009 "}, {"RefNumber": "658370", "RefComponent": "BC-DWB-DIC", "RefTitle": "Incomplete renaming of SAP data elements", "RefUrl": "/notes/658370 "}, {"RefNumber": "650291", "RefComponent": "SCM-APO-ATP", "RefTitle": "Condition technique: Data lost after upgrade to SAP SCM 4.0", "RefUrl": "/notes/650291 "}, {"RefNumber": "650310", "RefComponent": "SCM-APO-ATP", "RefTitle": "XPRA /SAPCN1/CUST_INDX_TO_CN1 did not convert any data", "RefUrl": "/notes/650310 "}, {"RefNumber": "640223", "RefComponent": "BC-UPG", "RefTitle": "Application error after 31I -> Enterprise 47X110 upgrade", "RefUrl": "/notes/640223 "}, {"RefNumber": "575170", "RefComponent": "BW-SYS", "RefTitle": "Duplicate key in RSRA_BP_OBJECTST upgrade from 2.x to 3.1", "RefUrl": "/notes/575170 "}, {"RefNumber": "551845", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Activation error with reference to class/interface", "RefUrl": "/notes/551845 "}, {"RefNumber": "63845", "RefComponent": "XX-SER-NET", "RefTitle": "Corrections on sapserv - searching for files", "RefUrl": "/notes/63845 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}