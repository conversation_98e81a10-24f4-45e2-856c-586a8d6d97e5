{"Request": {"Number": "1636252", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 643, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017316942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001636252?language=E&token=6C02A4B29D63C5C82E72FA9978087E28"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001636252", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001636252/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1636252"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 73}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.03.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST"}, "SAPComponentKeyText": {"_label": "Component", "value": "Client/Server Technology"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1636252 - Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Remark: </strong></span></p>\r\n<p><strong>Since the maintenance of SAP kernel release 720 ended in Q1/2015, SAP kernel release 721 replaces SAP kernel release 720 as the new standard SAP kernel release with corrections being supplied via SAP Kernel 721.</strong></p>\r\n<p><strong>Our recommendation is an upgrade to the&#160;latest available&#160;721 Stack Kernel. New&#160;Stack Kernels will be anounced on <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-53415\">http://scn.sap.com/docs/DOC-53415</a>.&#160;Further information on upgrading from 720 to 721 can be found in note <a target=\"_blank\" href=\"/notes/1975687\">1975687</a>.</strong></p>\r\n<p>You want to use the 7.20 or 7.20 EXT kernel as a downward-compatible kernel in one of the following NetWeaver releases</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.0 (\"7.00\")</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP1 for SAP NetWeaver 7.0 (\"7.01\")</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.10 (\"7.10\")</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP1 for SAP NetWeaver 7.10 (\"7.11\")</li>\r\n</ul>\r\n<p><br />or in</p>\r\n<ul>\r\n<li>SAP NetWeaver CE 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP1 for NW CE 7.1</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>7.20, 7.20 EXT, 720_EXT, AKK, DCK, Downward-Compatible Kernel</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br /><strong>Attention</strong></p>\r\n<ul>\r\n<li>This note is valid for all systems with NetWeaver Release 7.00, 7.01 (7.0 Enhancement Package 1), 7.10 and 7.11 (7.1 Enhancement Package 1) as well as CE 7.10 and 7.11 that still run with the 7.00, 7.01, 7.10 or 7.11 kernel that was originally delivered.</li>\r\n</ul>\r\n<ul>\r\n<li>You can also use the 7.20 EXT kernel as a downward-compatible kernel for the NetWeaver releases mentioned above. If you want to use the 7.20 EXT kernel, you have to download <strong>all</strong> kernel archives from the link of the corresponding EXT branch on the SAP Service Marketplace (e.g., from link \"SAP Kernel 7.20 EXT 64-BIT UC\" if you look for 64-bit UC kernel ). Please notice that it is very important not to mix executables from kernel 7.20 and 7.20 EXT. It is explicitly mentioned in the text if the handling of the 7.20 EXT kernel differs from the handling of the 7.20 standard kernel.<br />For the handling of SAP HostAgent, please refer to section 3 \"Installing SAPHOSTAGENT\".</li>\r\n</ul>\r\n<ul>\r\n<li>You may have to perform an operating system upgrade before you upgrade the kernel, or you may have to ensure additional database-specific prerequisites before the kernel upgrade (for example, the 7.20 kernel may be supported for certain operating system releases or database releases only). In particular, this applies to SAP kernel 7.20 EXT.<br />Release information is available at: http://service.sap.com/pam</li>\r\n</ul>\r\n<ul>\r\n<li>The 7.20 kernel checks complex DDIC structures in more detail than the previous kernels. Short dumps of the type DDIC_TYPELENG_INCONSISTENT may occur, for example, when calling transaction SM66. For more detailed information about correcting these inconsistencies, see Note <strong>1610716</strong>.<br />The actions described in this note can be performed anytime, but we recommend to implement it before the kernel switch.</li>\r\n</ul>\r\n<ul>\r\n<li>If you want to use DCK for CE Developer Workplace systems, then check note 1709911.</li>\r\n</ul>\r\n<ul>\r\n<li>Release <strong>7.00 or 7.01</strong>: Follow SAP note 1119735 regarding the relocation of the contents of the CCMS agents' working directories. This is described under \"Preface\" in section \"Solution\".</li>\r\n</ul>\r\n<p><br />The following four bullet points apply only to <strong>Dual-Stack or Java only systems</strong>:</p>\r\n<ul>\r\n<li>Release <strong>7.00 or 7.01</strong>: There was an incompatibility regarding jmon (SAP note <strong>1652660</strong>). This has been eliminated with the following versions of the SAP Java Technology S Offline component (SAPTECHF.SCA)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Release 7.00: SAP TECH S 7.00 OFFLINE, SP 14, PL 24</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Release 7.01: SAP TECH S OFFLINE 7.01, SP 3, PL 13</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This implies that at least support-package stack 14 or 3, respectively, must be installed in your system.<br />Note 1652660 also lists the required patch levels for the higher support packages.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Please note: As described in note 1652660, you have to install the corresponding SAPJTECH<strong><strong>S</strong></strong>.SCA (\"SAP Java Technology Services\") as well if you need to install a suitable SAPJTECH<strong><strong>F</strong></strong>.SCA (cf. also KBA 1738769).</p>\r\n<ul>\r\n<li>Release <strong>7.00 or 7.01</strong>: The tool <strong>JSPM </strong>must have at least SP 24 or 9, respectively.</li>\r\n</ul>\r\n<ul>\r\n<li>Release <strong>7.10 or 7.11</strong>: When using the <strong>SAPJVM 5.1</strong>, you have to install the latest patch collection, at least PC 58 (Build 5.1.074).</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAP Kernel 7.20 (EXT)</strong>should have at least Patch Level 201.</li>\r\n</ul>\r\n<p><strong>Platform-Specific Information</strong></p>\r\n<p>The downward compatible SAP Kernel 7.20 is designed for maximum coverage. As a result the SAP Kernel 7.20 can basically be used as DCK without additional platform requirements. Please verify for your SAP product release which database and operating system releases are supported with the 7.20 kernel. The PAM for your SAP product release can be accessed via the SAP Service Marketplace http://service.sap.com/pam. However, if you intend to use the SAP Kernel 7.20 as DCK you have to consider the following platform specific information:</p>\r\n<ul>\r\n<li>IBM AIX<br />Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</li>\r\n</ul>\r\n<ul>\r\n<li>IBM DB2 LUW<br />IBM DB2/UDB 8 is not supported with the SAP Kernel 7.20.</li>\r\n</ul>\r\n<ul>\r\n<li>IBM z/OS<br />IBM z/OS 1.6, 1.7 and 1.8 are not supported with the SAP Kernel 7.20.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP MaxDB<br />The SAP Kernel 7.20 requires at least version 7.7.07.41 of the MaxDB Client software (SQLBC). For more details refer to SAP Notes 822239 and 1487269.</li>\r\n</ul>\r\n<ul>\r\n<li>Microsoft SQL Server</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The SAP Kernel 7.20 is intended for Microsoft SQL Server 2005 and higher. If you are still using Microsoft SQL Server 2000 refer to SAP Note 1341097.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For additional information about the usage of the SAP Kernel 7.20 as DCK with Microsoft SQL Server refer to SAP Note 1467086.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Oracle Database<br />Oracle 10.1 is not supported with the SAP Kernel 7.20. You have to use at least Oracle 10.2.</li>\r\n</ul>\r\n<p><br />The downward compatible SAP Kernel 7.20 EXT is designed for maximum supportability. As a result you may need to update your database and operating system version to use the SAP Kernel 7.20 EXT as DCK. Please verify for your SAP product release which database and operating system releases are supported with the 7.20 EXT kernel. The PAM for your SAP product release can be accessed via the SAP Service Marketplace http://service.sap.com/pam. However, if you intend to use the SAP Kernel 7.20 EXT as DCK you have to consider the following platform specific information:</p>\r\n<ul>\r\n<li>AIX<br />Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</li>\r\n</ul>\r\n<ul>\r\n<li>IBM i<br />The SAP Kernel 7.20 EXT (but only non-Unicode) requires the locales of all languages you use within your SAP system to be installed on within PASE. For details refer to SAP Note 1423600.</li>\r\n</ul>\r\n<ul>\r\n<li>Linux<br />For details on Linux requirements refer to SAP Note 1563102.</li>\r\n</ul>\r\n<ul>\r\n<li>Microsoft Windows<br />The SAP Kernel 7.20 EXT requires a specific Microsoft Windows C Runtime environment. For details refer to SAP Note 1553465.</li>\r\n</ul>\r\n<ul>\r\n<li>Oracle Database<br />On all operating system platforms, except IBM Power Linux, Windows IA64 and Linux IA64 (Intel Itanium), only Oracle 11g is supported. On IBM Power Linux, Windows IA64 and on Linux IA64 (Intel Itanium), only Oracle 10g is supported.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Oracle dababase server<br />The Oracle database server has to be Oracle 11.2.0.2 or higher.<br />The latest SAP Bundle Patch needs to be applied on top of the existing patch set release.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Oracle database client<br />Install the Oracle 11g Instant Client on all application servers and the database server as described in SAP Note 1431794. The Oracle client software can be downloaded from the SAP Service Marketplace at: https://service.sap.com/oracle-download<br /><strong>Please note: </strong>After the installation of the Oracle 11g Instant Client, make sure that the library path for the &lt;sid&gt;adm user (LIBPATH, SHLIB_PATH, LD_LIBRARY_PATH) no longer contains any references to the old Oracle 10g Instant Client location.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>On IBM Power Linux, Windows IA64 and on Linux IA64 (Intel Itanium), only Oracle 10g is supported. For these three platforms you do not have to make any changes to your database client.</li>\r\n</ul>\r\n</ul>\r\n<p><br />If you use a DB/OS platform combination in your system environment where both the DB release and the OS release are supported by the 7.20 EXT kernel, we recommend to use the 7.20 EXT kernel instead of the 7.20 standard kernel.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The 7.20 kernel, which is delivered by SAP for Release 7.02 (7.0 Enhancement Package 2), is downward-compatible to the kernels for releases 7.00, 7.01, 7.10, and 7.11. Therefore, you can eliminate errors by replacing the kernel with a 7.20 kernel so that you do not have to perform an SAP upgrade.</p>\r\n<p><strong>Important</strong></p>\r\n<ul>\r\n<li>You must exchange the kernels on all servers of the affected system.</li>\r\n</ul>\r\n<ul>\r\n<li>The general SAP recommendation to keep up to date with the upgrades or updates remains unaffected by this downward-compatible kernel because it does not correct application errors.</li>\r\n</ul>\r\n<ul>\r\n<li>For compatibility reasons, the 7.20 kernel is still available for certain 32-bit platforms (Windows, Linux); however, we strongly recommend that you use the 64-bit variant. The 32-bit variant is not suitable for production operation.<br />The 7.20 EXT kernel is not available in a 32-bit variant.</li>\r\n</ul>\r\n<ul>\r\n<li>You can upgrade to the 7.20 kernel without having to install the new GUI version on the front-end PCs.</li>\r\n</ul>\r\n<ul>\r\n<li>After you have installed a 7.20 (EXT) kernel, you must:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Continue to import the Support Packages available for your original SAP release when you use Support Packages to implement corrections of repository objects.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In future, when correcting the kernel using kernel patches, use 7.20 (EXT) patches only.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Definition of terms</strong></p>\r\n<p><br />Here, 'kernel' means all executable programs located in /usr/sap/&lt;SAPSID&gt;/SYS/exe/run (UNIX, IBM i) or \\\\$(SAPGLOBALHOST)\\sapmnt\\&lt;SAPSID&gt;\\sys\\exe\\&lt;nuc|uc&gt;\\&lt;platform&gt; (Windows), not just the executable disp+work.<br /><br /><br /><br /></p>\r\n<p><strong>1. Obtaining the Archives</strong></p>\r\n<p><br />The elements that are required for importing the kernel are available as an SAR archive on SAP Service Marketplace under \"SAP Kernel\". There a differentiation is made between 32-bit and 64-bit kernels and also between the character width (Unicode or non-Unicode). As a further sub-option, a differentiation is made between the platform and under this, a differentiation is made between database-independent archives and (for each database supported in the platform) database-dependent archives. The name of the archive is made from a template of type &lt;name&gt;_&lt;plevel&gt;-&lt;uid&gt;.SAR. Here, &lt;name&gt; is the actual name of the archive, followed by the patch level &lt;plevel&gt; and a unique ID &lt;uid&gt; in which the bit value, the character width, the platform and the database type of the relevant archive are included again. In general, only the short form &lt;name&gt;.SAR is used in this note (for example, SAPEXE.SAR). For more information about the download, see Note 19466.<br />You should download the following archives into the same directory &lt;newkernel&gt;:</p>\r\n<ul>\r\n<li>Due to the digital signature of the archives (see Note 1598550), you require the latest <strong>SAPCAR</strong>. Therefore, download the archive SAPCAR_&lt;plevel&gt;-&lt;uid&gt;.EXE from SAP Service Marketplace under the name SAPCAR (UNIX, IBM i) or sapcar.exe (Windows) into your local &lt;newkernel&gt; directory.</li>\r\n</ul>\r\n<ul>\r\n<li>Download the latest 7.20 stack kernel from SAP Service Marketplace. The stack kernel consists of the database-independent archive <strong>SAPEXE.SAR</strong> and the database-dependent archive <strong>SAPEXEDB.SAR</strong>.<br />On Oracle on Windows, you also still require the current Oracle Instant Client (see Note 998004).<br />If you use different platforms (for example, Windows Application Server with database DB2 on IBM i), you can immediately download the stack kernel for all of the required platforms.</li>\r\n</ul>\r\n<ul>\r\n<li>If the Internet Graphics Server (IGS) is installed, you must also download the archives <strong>igsexe.sar</strong> and, optionally, <strong>igshelper.sar</strong>. When upgrading to kernel 7.20 or kernel 7.20 EXT you also need to upgrade the IGS installation accordingly:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP IGS 7.20 needs kernel 7.20</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP IGS 7.20 EXT needs kernel 7.20 EXT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>The archive <strong>igshelper.sar</strong> contains an optional component (see SAP note 1028690), which is only available as of SAP IGS 7.20. We recommend to apply the igshelper.sar to your installation as described in SAP note 1028690 when you are upgrading to kernel 7.20 or 7.20 EXT. The igshelper.sar is operating-system independent and release independent regarding SAP IGS releases newer than or equal to 7.20.<br />You can download igshelper.sar from http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; \"SAP Frontend Components\" -&gt; \"SAP IGS HELPER APPLICATIONS\" -&gt; \"SAP IGS HELPER\" -&gt; \"SAP IGS HELPER\".</li>\r\n</ul>\r\n<ul>\r\n<li>If you use <strong>SAPCRYPTOLIB:&#160;</strong>from the PL 513 on it is replaced by the new CommonCryptoLib, please refer to the section <strong>5.3</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>For Oracle only:</strong><br />Download the package DBATL720O10_&lt;plevel&gt;-&lt;uid&gt;.SAR from http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; \"Additional Components\" -&gt; \"SAP Kernel\" -&gt; SAP KERNEL (32|64)-BIT (UNICODE) -&gt; SAP KERNEL 7.20 (EXT) ... -&gt; &lt;your platform&gt; -&gt; ORACLE.<br />For detailed information see note 12741.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>For IBM i only:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Download the package ILE_&lt;plevel&gt;-&lt;uid&gt;.SAR with at least patch level 109 as ILE.SAR.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you have already changed your SAP system to SAPJVM, you must also download one of the archives SAPJVM4 or SAPJVM5, depending on which one you currently use.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you know that there are already patches on SAP Service Marketplace for the SAPEXE.SAR that is used (for example, DW), download these into the directory. So they are automatically applied with the kernel.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For a 7.00/7.01 ASCII based dual stack system you used to download only ASCII Packages. The ASCII package contained the UNICODE part for the SCS Instance, too. The 7.20 Kernel does not contain UNICODE parts in the ASCII Packages. Therefore you have to download the UNICODE SAPEXE package in addition.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>For UNIX/Windows only:</strong><br />Download any additional programs (such as the RFCSDK) if you still want to install such programs after the kernel installation.</li>\r\n</ul>\r\n<p><strong>2. Preparations</strong></p>\r\n<p><br />After you have downloaded the required kernel components, you must stop all of the relevant processes on all instances for the system to be processed and release or delete the kernel-specific resources. To do this, carry out the following actions as the user &lt;sapsid&gt;adm on all instances:</p>\r\n<ol>1. Stop the SAP system. (You do not need to stop the database.)</ol>\r\n<ul>\r\n<li>On UNIX or Windows:<br />Stop the SAP system as usual.</li>\r\n</ul>\r\n<ul>\r\n<li>On IBM i:<br />Stop the SAP system together with sapstartsrv:<br />STOPSAP INSTANCE(*ALL) STARTUPSRV(*ALL) XDNLISTEN(*YES) WAIT(*YES) ENDSBS(*YES)</li>\r\n</ul>\r\n<ol>2. Stop saposcol.</ol>\r\n<ul>\r\n<li>On UNIX (Web AS release 7.0 and 7.01):<br />cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run<br />./saposcol -k</li>\r\n</ul>\r\n<ul>\r\n<li>On UNIX (Web AS release 7.10 and 7.11):<br />cd /usr/sap/hostctrl/exe<br />./saposcol -k</li>\r\n</ul>\r\n<ul>\r\n<li>On Windows:<br />If it exists, stop the Windows service SAPOsCol and delete it using the following commands:<br />net stop saposcol<br />sc delete saposcol</li>\r\n</ul>\r\n<ul>\r\n<li>On IBM i:<br />CALL PGM(SAPOSCOL) PARM('-k')</li>\r\n</ul>\r\n<ol>3. Stop sapstartsrv (this is required on UNIX and Windows only):</ol>\r\n<ul>\r\n<li>On UNIX:<br />kill -2 &lt;pid of sapstartsrv&gt;</li>\r\n</ul>\r\n<ul>\r\n<li>On Windows:<br />Stop and deactivate (Startup Type = Disabled) the services SAP&lt;SID&gt;_&lt;INSTANCE no.&gt;.</li>\r\n</ul>\r\n<ol>4. If present, unregister all standalone CCMS agents (sapccmsr [-j2ee], sapccm4x). On Unix you can see corresponding processes, on Windows you can see them running as services &lt;agent name&gt;.&lt;instance number&gt;:</ol>\r\n<ul>\r\n<li>sapccm4x -u pf=&lt;profile the agent started with&gt;<br />sapccmsr -u pf=&lt;profile the agent started with&gt; [-j2ee]</li>\r\n</ul>\r\n<ol>5. Remove any IPC objects that still exist (this is required on UNIX only):</ol>\r\n<ul>\r\n<li>cleanipc &lt;instance no&gt; remove</li>\r\n</ul>\r\n<p><strong>3. Installing SAPHOSTAGENT</strong></p>\r\n<p><br />For releases 7.00 and 7.01 (7.0 Enhancement Package 1), you must also install the package SAPHOSTAGENT.SAR in the latest 7.20 version available (in accordance with Note 1031096). <strong>On IBM i, </strong>you must also update SAP HostAgent to the latest 7.20 version, when you use SAP systems based on NetWeaver 7.10 or 7.11 (7.10 Enhancement Package 1). <strong>SYBASE ASE:</strong> minimum PL is 115.<br />Afterwards, you must delete the call of the program SAPOSCOL from all the start profiles. (not Windows)<br />SAP HostAgent is available on the SAP Service Marketplace (http://service.sap.com/swdc) under following menu path: \"Support Packages and Patches\" -&gt; \"Browse our Download Catalog\" -&gt; \"SAP Technology Components\" -&gt; \"SAP HOST AGENT\".<br />Please note that a 7.20 EXT version of SAP HostAgent does not exist. You have to install the 7.20 version of SAP HostAgent even if you install SAP Kernel 7.20 EXT.<br /><br /></p>\r\n<p><strong>4. Importing the new kernel</strong></p>\r\n<p><br />Import the 7.20 kernel from the directory &lt;newkernel&gt; on the host of the central instance and then on all of the application servers with local executables.</p>\r\n<p><strong>4.1 On UNIX</strong></p>\r\n<ol>1. Log on as user &lt;sapsid&gt;adm and switch to the directory /usr/sap/&lt;SAPSID&gt;/SYS/exe/run.</ol><ol><ol>2. We recommend saving the old kernel before deploying the new kernel.</ol></ol><ol>Save the old kernel by creating a tar archive of the complete kernel directory using the following command:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAPCAR -cvf ../sapexe.tar ./*</p>\r\n<ol>3. Save the following files and directories</ol><ol><ol>a) the directory jvm or sapjvm* (e.g., sapjvm, sapjvm_4, sapjvm_5) if it exists</ol></ol><ol><ol>b) the file protect.lst if it exists</ol><ol></ol><ol></ol><ol>c) the files rfcexec, rfcexec.sec if exists</ol></ol><ol><ol>d) the icu libraries (libicu*30.&lt;a sh, so&gt; or libicu*34.&lt;a sh, so&gt; if they exist</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The directories jvm or sapjvm* and the file protect.lst only exist in the case of a Java or Dual-Stack implementation.</p>\r\n<p style=\"padding-left: 60px;\">The files rfcexec and rfcexec.sec are only used in customer specific non-ALE scenarios. It is mandatory to check if such scenarios exist.&#160;With the ALE scenario the classic rfcexec needs to be replaced with the version delivered with NW RFC SDK 720. Details are available in SAP note 1140031.</p>\r\n<ol>4. Switch to the user root and change the owner of all files to &lt;SAPSID&gt;adm using the following commands:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;su - root<br />chown &lt;sapsid&gt;adm /usr/sap/&lt;SAPSID&gt; /SYS/exe/run/*<br />exit<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Important: On the operating system OS/390, you must use the command \"su &lt;user&gt;\" instead of \"su - root\", where &lt;user&gt; must have the UID 0.</p>\r\n<ol>5. Delete all of the files from the kernel directory, including the subdirectories. This ensures that there are no remaining files from the earlier release, which have a different name in Release 7.20 or are in a different place in a subdirectory.</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rm -rf *</p>\r\n<ol>6. Unpack the new kernel with the following commands:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/SAPEXE.SAR<br />&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/SAPEXEDB.SAR</p>\r\n<p style=\"padding-left: 30px;\">7. <strong><strong>Oracle only:</strong></strong></p>\r\n<ol>Unpack the DBATools with the following command:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/DBATL720O10. SAR<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Install the Oracle instant client as described in note 819829.</p>\r\n<ol><ol>8. If you use IGS, you must unpack the IGS archive using the following</ol></ol><ol>command:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/igsexe. sar</p>\r\n<ol>9. If there are files or directories that were saved in step 3, restore them into the current directory</ol><ol>10. To deploy the optional IGSHELPER archive switch to the relevant local directory /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt; on every instance and execute the command:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/IGSHELPER. SAR</p>\r\n<ol>11. Switch to the user root and run the shell script saproot.sh, which is available in the kernel directory.</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;su - root (or su &lt;user with UID 0&gt; on OS/390)<br />cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run<br />./saproot.sh &lt;SAPSID&gt;<br />exit</p>\r\n<ol>12. Delete all of the local executables on the individual instances. To do this, switch to the relevant local executable directory /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/exe and execute</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rm -rf *</p>\r\n<ol>13. Since executables from the local executable directories may already be executed for the start before sapcpe runs, start an initial copy of the executables.</ol><ol><ol>a) All application server instances (primary application server/central instance, additional application servers/dialog instances):</ol></ol>\r\n<ul>\r\n<ul>\r\n<li>For ABAP-only systems:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br />sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt;</p>\r\n<ul>\r\n<ul>\r\n<li>For Dual-Stack systems:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br />sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If your system is running with the SAPJVM:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;/sapjvm_&lt;version&gt;.lst</p>\r\n<ul>\r\n<ul>\r\n<li>For Java-only systems:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br />sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; list:/usr/sap/&lt;SAPSID&gt;/SYS/exe/run/j2eeinst.lst<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If your system is running with the SAPJVM:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;/sapjvm_&lt;version&gt;.lst</p>\r\n<ol><ol>b) For additional instances such as ASCS, SCS and ERS (exist only in a cluster):</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br />sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; list:/usr/sap/&lt;SAPSID&gt;/SYS/exe/run/scs.lst<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;NB: the arguments (\"pf\", \"list\", \"source\") are separated by a white space, not by a newline character. &lt;sapjvm directory&gt; means the location of the saved SAPJVM, see the step 9 above.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br /><strong>4.2 On Windows</strong></p>\r\n<ol>1. Log on as user &lt;sapsid&gt;adm and switch to the global host in your kernel directory, for example: &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;</ol><ol>2. Rename the &lt;platform&gt; directory, for example &lt;platform&gt;.save.</ol><ol><ol>3. Create a new directory &lt;platform&gt; and switch to this directory.</ol></ol><ol><ol>Unpack the new kernel from the directory &lt;newkernel&gt; of the downloaded archive in the specified sequence.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Important: Do not call SAPCAR.EXE directly without specifying a path; instead, use the specified directory structure.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\SAPEXE.SAR</ol></ol><ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\SAPEXEDB.SAR</ol><ol>4. Restore the following directories and files into the newly created directory if they exist in the &lt;platform&gt;.save directory</ol><ol><ol>a) the directory jvm or sapjvm* (e.g., sapjvm, sapjvm_4)</ol></ol><ol><ol>b) the file protect.lst</ol><ol></ol><ol></ol><ol>c) the files rfcexec.exe, rfcexec.sec</ol></ol><ol><ol>d) the icu libraries icu*30.dll or icu*34.dll if they exist</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The directories jvm or sapjvm* and the file protect.lst only exist in the case of a Java or Dual-Stack implementation.</p>\r\n<p style=\"padding-left: 60px;\">The files rfcexec and rfcexec.sec are only used in customer specific non-ALE scenarios. It is mandatory to check if such scenarios exist. With the ALE scenario the classic rfcexec needs to be replaced with the version delivered with NW RFC SDK 720. Details are available in SAP note 1140031.</p>\r\n<p style=\"padding-left: 60px;\">5.</p>\r\n<p><strong><strong>Oracle only:</strong></strong></p>\r\n<ol><ol>Unpack the DBATools to the &lt;platform&gt; directory:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\DBATL720O10.SAR</ol></ol>\r\n<p><br /><br /></p>\r\n<ol>Install the Oracle instant client as described in note 998004.</ol><ol><ol>6. If you use IGS, unpack the new IGS using the following command:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\igsexe.sar</ol><ol>7. To deploy the optional IGSHELPER archive, still logged in as &lt;sapsid&gt;adm, switch to the relevant local directory on every instance. For example:</ol>\r\n<ul>\r\n<ul>\r\n<li>primary application server instance:<br />&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\DVEBMGS&lt;No&gt;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>additional application server instance:<br />&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\D&lt;No&gt;</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;and execute the command<br /><br />&lt; newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\IGSHELPER.SAR</p>\r\n<ol>8. On all instances in the directories &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\exe, delete all of the files (including the subdirectories).</ol><ol>9. Install the current C runtime library as per note 1553465 by executing vcredist_&lt;platform&gt;.msi in the command box (or by double-clicking this file in the Windows Explorer). Before you start the system for the first time, and if you have a distributed system environment, perform this step manually on each node where a component of the system is configured to run.</ol><ol>10. Since executables from the local executable directories may already be executed for the start before sapcpe runs, start an initial copy of the executables.</ol><ol><ol>a) For all application server instances (primary application server/central instance, additional application servers/dialog instances):</ol></ol>\r\n<ul>\r\n<ul>\r\n<li>For ABAP-only systems:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt; drive&gt;:<br />cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br />sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt;</p>\r\n<ul>\r\n<ul>\r\n<li>For Dual-Stack systems:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt; drive&gt;:<br />cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br />sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If your system is running with the SAPJVM:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;\\sapjvm_&lt;version&gt;.lst</p>\r\n<ul>\r\n<ul>\r\n<li>For Java-only systems:</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt; drive&gt;:<br />cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br />sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; list:&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;\\&lt;platform&gt;\\j2eeinst.lst<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If your system is running with the SAPJVM:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;\\sapjvm_&lt;version&gt;.lst</p>\r\n<ol><ol>b) For additional instances such as ASCS, SCS and ERS (exist only in a cluster):</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt; drive&gt;:<br />cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br />sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; list:&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;\\&lt;platform&gt;\\scs.lst<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;NB: the arguments (\"pf\", \"list\", \"source\") are separated by a white space, not by a line break. &lt;sapjvm directory&gt; means the location of the saved SAPJVM, see the step 4 above.</p>\r\n<ol>11. Activate (Startup Type = Automatic) and start the Windows Services SAP&lt;SID&gt;_&lt;INSTANCE no.&gt; of primary application server instance and of every additional application server instance to active the new version of sapstartsrv.exe.</ol>\r\n<p><br /><br /><strong>MSCS only</strong>:</p>\r\n<ol>1. Start and Stop the clustered (A)SCS instances and the appropriate SAP services using the cluster admin tool or the Powershell.</ol><ol>2. Also see SAP Note 1596496 on how to update the SAP Resource Type DLL.</ol><ol><ol>3. You have to follow step 6-10 also for the ERS instances on your cluster nodes.</ol></ol><ol><ol>Search for REPSRV.lst in all Start Profile of the ERS instances (line Start_Program_00 =)and if it exists replace it with SCS.lst.</ol></ol><ol>Be sure to check if your Enqueue Replication Service is replicating again after starting the ERS instances</ol><ol>4. If you have standalone gateway instances installed on cluster nodes, these also need to be updated with the corresponding files from the kernel staging directory \\\\$(SAPGLOBALHOST)\\sapmnt\\&lt;SAPSID&gt;\\sys\\exe\\&lt;nuc|uc&gt;\\&lt;platform&gt;.</ol>\r\n<ul>\r\n<ul>\r\n<li>On Windows Server 2003 (see Note 657999) the standalone gateway is installed in the &lt;Windows&gt;\\SAPCLUSTER directory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>On Windows Server 2008 and higher (see Note 1764650 - How to install a standalone gateway in a Microsoft Failover Cluster for Oracle) it is &lt;oracle shared disk&gt;:\\sap\\&lt;DB-SID&gt;\\dbtools.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br /></p>\r\n<p><strong>4.3 On IBM i:</strong></p>\r\n<p><br />Log on as a QSECOFR-type user and execute the following commands or actions:</p>\r\n<ol><ol>1. Point the current directory explicitly to the downloaded archives:</ol></ol><ol>CHGCURDIR '&lt;newkernel&gt;'</ol><ol><ol>2. Cleanup the library SAP_TOOLS if it exists, otherwise you need not care</ol></ol><ol>CLRLIB SAP_TOOLS</ol><ol><ol>3. Extract the required tools from *SAVF file ILE_TOOLS in ILE.SAR by program iletools into SAP_TOOLS (*LIB) - use 'Copy &amp; Paste' to transfer the following command to your session and execute it:</ol></ol><ol>CALL QP2SHELL PARM('/QOpenSys/usr/bin/csh' '-c' 'SAPCAR -xvf ILE.SAR iletools ILE_TOOLS;iletools')</ol><ol>4. Since this call to QP2SHELL does not produce any output, check whether library SAP_TOOLS exists now and has some objects in it. If not, check with the WRKSPLF command for spool files with error messages.</ol><ol><ol>5. Set the authorities of the objects in SAP_TOOLS by these commands:</ol></ol><ol><ol>If your starting kernel is already running with the 'newuserconcept' set:</ol></ol><ol><ol>&#160;&#160;ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</ol></ol><ol><ol>&#160;&#160;ADDLIBLE&#160;&#160;SAP_TOOLS</ol></ol><ol><ol>&#160;&#160;FIXSAPOWN *NONE SAP_TOOLS</ol></ol><ol><ol>If your starting kernel is still running with the 'classicuserconcept' set:</ol></ol><ol><ol>&#160;&#160;ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</ol></ol><ol><ol>&#160;&#160;ADDLIBLE&#160;&#160;SAP_TOOLS</ol></ol><ol><ol>&#160;&#160;FIXSAPOWN *NONE SAP_TOOLS</ol></ol>\r\n<p><br /><br /><strong>note:</strong></p>\r\n<ol>If you are not sure which userconcept your current system is using please check for the file /sapmnt/&lt;SID&gt;/profile/.newuserconcept The existence of this file says that the newuserconcept is active. You can also check who is the owner/primary-group of the executable files underneath /sapmnt/&lt;SID&gt;/exe. &lt;SID&gt;ADM/R3GROUP is an indicator for the newuserconcept, while R3OWNER/&lt;SID&gt;GROUP is an indicator for the classicuserconcept.</ol><ol>6. For SAP systems with a release lower than 7.10, check the contents of the file /usr/sap/sapservices. If sapstartsrv is started under the name sapstartsrvu (/usr/sap/&lt;sapsid&gt;/SYS/exe/run/sapstartsrvu ...), you must change the entry so that sapstartsrv is started from the subdirectory .../uc of the previous directory in future (/usr/sap/&lt;sapsid&gt;/SYS/exe/run/uc/sapstartsrv ...)</ol>\r\n<p><br />Log on as &lt;SAPSID&gt;ADM and execute the following commands for importing the new kernel:</p>\r\n<ol>1. ADDLIBLE SAP_TOOLS</ol><ol><ol>2. Check whether the environment variable CLASSICUSERCONCEPT is set by the login process (use WRKENVVAR); if it is not set, set it in the following way:</ol></ol><ol><ol>If your starting kernel is already running with the 'newuserconcept' set:</ol></ol><ol><ol>&#160;&#160;ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</ol></ol><ol><ol>If your starting kernel is still running with the 'classicuserconcept' set:</ol></ol><ol>&#160;&#160;ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</ol><ol>3. Check that all instance- and start-profiles of the system contain the parameter DIR_CT_RUN. If missing in a 7.00/7.01 environment set it to: DIR_CT_RUN = $(DIR_EXE_ROOT)/run (for Windows instances: DIR_CT_RUN = $(DIR_EXE_ROOT)\\run ); For 7.10/7.11 set it to: DIR_CT_RUN = $(DIR_EXE_ROOT)/$(OS_UNICODE)/as400_pase_64 (for Windows: DIR_CT_RUN = $(DIR_EXE_ROOT)\\$(OS_UNICODE)\\ntamd64 )</ol><ol>4. Please remove any DLTOLDPKG call from all start profiles, because with the 7.20 Kernel DLTOLDPKG will be started by STARTSAP automatically. For further information please refer to note 1657890.</ol><ol><ol>5. Apply all archives in &lt;newkernel&gt; simultaneously to the system:</ol></ol><ol>APYSIDKRN SID(&lt;SAPSID&gt;) ARCHIVES('&lt;newkernel&gt;/*') SAVSAR(*NONE) MODE(*FULLY) CHGENV(*NO) UPDAPAR(*NO)</ol><ol><ol>6. Remove the SQL packages left over from using the old kernel:</ol></ol><ol>DLTR3PKG SID(&lt;SAPSID&gt;)</ol>\r\n<p><br />Log off and then log on again with &lt;SAPSID&gt;ADM. You are now in the new 7.20 environment with the kernel library SAP&lt;sapsid&gt;IND.<br />(Caution: The name of the kernel library is predefined after you import the 7.20 kernel and can no longer be freely selected.)<br /><br />If not done yet, it is highly recommended to switch to the SAPCPE process for kernel maintenance now (see SAP note 1632754 for details) in order to minimize the system downtime for kernel maintenance.<br /><br />If you change a system with a 7.10 or 7.11(7.1 Enhancement Package 1) kernel to the 7.20 kernel and have not yet changed the user concept, use the opportunity and change it now in accordance with Note 1149318 because future upgrade or update paths require the new user concept.The faster storage management with SHM_SEGS can only be used after the changeover (see Note 808607).<br /><br /></p>\r\n<p><strong>5. Additional steps</strong><br /><strong>5.1 CAUTION: Retroactive kernel patches</strong></p>\r\n<p><br />In some executables (in particular, disp+work), errors were corrected at a later date. You must apply these kernel patches in any case. These are available in SAP Support Portal (http://service.sap.com/swdc).<br /><br />Read Note 19466 (Downloading SAP kernel patches) or Note 1097751 for IBM i.</p>\r\n<p><strong>5.2 Additional manual changes in Dual-Stack and Java-only systems</strong></p>\r\n<p><br />This step applies only to releases 7.00 and 7.01.</p>\r\n<ul>\r\n<li>Only necessary in case of a Dual-Stack system: you must set the following parameter in the default system profile DEFAULT.PFL:<br />system/type = DS</li>\r\n</ul>\r\n<ul>\r\n<li>In all (Dnn, DVEBMGSnn, Jnn, JCnn, etc.) instance profiles, you must set the following parameter:<br />FN_JSTART = jcontrol$(FT_EXE)</li>\r\n</ul>\r\n<p><strong>5.3 Reinstalling SAPCRYPTOLIB</strong></p>\r\n<p>Starting with the patch level 513, also in SP stack kernels 720 PL &gt;=600, a new CommonCryptoLib is delivered with the SAP Kernel. This library is fully compatible to the SAPCRYPTOLIB and replaces it, so the reinstallation of SAPCRYPTOLIB is no longer necessary. See the Note 1848999 for more detail.</p>\r\n<p><br /><strong>5.4 Reinstalling additional programs</strong></p>\r\n<p><br />If you had installed additional programs such as the RFC Library, you have to install them again. To do this, proceed as follows:<br /><br /><strong>On UNIX:</strong><br />Execute the following commands as user &lt;sapsid&gt;adm:</p>\r\n<ol>1. cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run</ol><ol>2. &lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/&lt;additional package&gt;.SAR</ol>\r\n<p><br /><strong>On Windows:</strong><br />Execute the following commands as user &lt;SAPSID&gt;ADM:</p>\r\n<ol>1. CD \\USR\\SAP\\&lt;SAPSID&gt;\\SYS\\EXE\\RUN</ol><ol>2. &lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\&lt;additional package&gt;.SAR</ol>\r\n<p><br /><strong>On IBM i:</strong><br />On IBM i, you do not have to install any further programs.</p>\r\n<p><br /><strong>5.4.1 Copying executable files from NetWeaver RFC SDK 720.</strong></p>\r\n<p><br />Due to security reasons the program rfcexec&#160;(UNIX and IBM i) or rfcexec.exe (Windows) is no longer shipped together with SAP kernel 720. For various reasons, it is mandatory to move to a new version of these files, contained in the NW RFC SDK 720. Please refer to SAP Notes 1581595 and 1025361for detailed instructions and further information.</p>\r\n<p>Remark: The new version&#160;of rfcexec might&#160;break some of your application scenarios.&#160;Only in this case&#160;it is required to&#160;use the old version, which was saved in the previous preparation step (Unix, step 3 c, Windows 4 c).</p>\r\n<p><strong>5.5 Special features for the syslog (ABAP-only and Dual-Stack systems)</strong></p>\r\n<p><br />Due to the situation described in note 1517379, you have to set the profile parameter<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rslg/new_layout = 9.<br /><br />If a syslog file already exists in the new format because this parameter has not been set from the very beginning, the syslog will still be written in the new format even if the parameter has been set in the meantime. In that case, the existing syslog files have to be deleted.</p>\r\n<p><strong>5.6 Dynamic work processes (NW 7.00 and NW 7.01 systems only)</strong></p>\r\n<p><br />The 7.20 kernel supports the dynamic increase of the number of work processes at runtime. However, this function is not fully compatible with NW 7.00 and NW 7.01. To prevent errors from occurring, deactivate it by setting the following parameters:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rdisp/wp_no_restricted = 0<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rdisp/configurable_wp_no = 0<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rdisp/dynamic_wp_check = FALSE<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rdisp/wp_max_no = Sum of<br />( rdisp/wp_no_dia + rdisp/wp_no_btc + rdisp/wp_no_vb + rdisp/wp_no_vb2 + rdisp/wp_no_spo +rdisp/wp_no_enq )<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Mind that rdisp/wp_max_no has the default value DEFAULT, which will add two unwanted dynamic processes if not set to the number of configured classical wp types.</p>\r\n<p><strong><strong>5.7 Configuration of the CCMS central monitoring</strong></strong></p>\r\n<p><br />Depending on the SAP kernel release of the monitored system you upgraded from and depending on the release of the central monitoring system (CEN), it is necessary to check the following scenarios:</p>\r\n<ul>\r\n<li>You upgraded the monitored system:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CEN system is based on SAP_BASIS 7.0 (7.00) and lower:<br />You have to disable the integrated CCMS agents in sapstartsrv of the upgraded system and use standalone CCMS agents instead. sapccm4x for the central monitoring of pure ABAP and Dual-Stack systems, sapccmsr -j2ee for the central monitoring of pure Java systems. Please proceed as outlined in note 1368389, section \"Solution\".</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CEN system is based on SAP_BASIS 7.0 EhP 1 (7.01):<br />The registration of the integrated CCMS agents from the central system needs to be repeated. Please follow the instructions given in SAP Help (http://help.sap.com/saphelp_nwpi71/helpdata/EN/44/893e933dc912d3e10000000a422035/content.htm).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>You upgraded the central system:<br />No reconfiguration is necessary.</li>\r\n</ul>\r\n<p><br /><strong>SAP note 1667336</strong> contains a detailed description for every case.</p>\r\n<p><strong>5.8. Check your environment on Linux and UNIX platforms</strong></p>\r\n<p>On all UNIX/Linux platforms with deactivated interactive user logon for &lt;sidadm&gt; user - check Note 1825259.</p>\r\n<p><strong>6 System Start</strong></p>\r\n<p><br />Start the SAP system with the new kernel and check the poins below if relevant.</p>\r\n<p><strong><strong>6.1 AS Java or Dual-Stack systems with release 7.00 or 7.01</strong></strong></p>\r\n<p><br />If the Java instance or even the complete system does not start, check the following points:</p>\r\n<ul>\r\n<li>The minimum support packages and patch levels have been installed as described in section \"Attention\" under \"Reason and Prerequisites\".</li>\r\n</ul>\r\n<ul>\r\n<li>sapjvm: The directory jvm or sapjvm or the file protect.lst have been saved and restored as described in</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Section 4.1, \"On Unix\", steps 3 and 9</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Section 4.2, \"On Windows\", step 4</li>\r\n</ul>\r\n</ul>\r\n<p><strong>6.2 DDIC_TYPELENG_INCONSISTENT short dumps</strong></p>\r\n<p><br />If you did not handle the DDIC_TYPELENG_INCONSISTENT issue, which is described in chapter \"Reason and Prerequisites\", section \"Attention\", while the original kernel was still running, you should now install Note 1610716.</p>\r\n<p><strong>6.3 Load format of ABAP programs</strong></p>\r\n<p><br />After you start the SAP system with the new kernel, the following message is displayed in the system log: 'Load format of PROGRAM not valid.'<br />You can ignore this message because the load is automatically regenerated.<br />It is also possible to regenerate these ABAP loads directly using transaction SGEN. For more information see Note 185745.</p>\r\n<p><strong><strong>6.4 </strong>CCMS Monitoring issues</strong></p>\r\n<p><br />In case of any issues with the CCMS monitoring after the kernel upgrade, check note 1667336, in particular the section \"Local monitoring in the upgraded double-stack system\".-</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D001398"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001636252/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001636252/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1738769", "RefComponent": "BC-JAS-COR", "RefTitle": "core/AbstractManager java.lang.NoClassDefFoundError", "RefUrl": "/notes/1738769"}, {"RefNumber": "998004", "RefComponent": "BC-DB-ORA", "RefTitle": "Update the Oracle Instant Client on Windows", "RefUrl": "/notes/998004"}, {"RefNumber": "808607", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Memory management in a PASE-based system", "RefUrl": "/notes/808607"}, {"RefNumber": "684106", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684106"}, {"RefNumber": "510007", "RefComponent": "BC-SEC-SSL", "RefTitle": "Additional considerations about setting up SSL on Application Server ABAP", "RefUrl": "/notes/510007"}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519"}, {"RefNumber": "397175", "RefComponent": "BC-SEC", "RefTitle": "SAP Cryptographic software - export control", "RefUrl": "/notes/397175"}, {"RefNumber": "185745", "RefComponent": "BC-ABA-LA", "RefTitle": "Mass generation due to incorrect load format", "RefUrl": "/notes/185745"}, {"RefNumber": "1825259", "RefComponent": "BC-CST-STS", "RefTitle": "UNIX sapstartsrv: the system does not start with 720 kernel", "RefUrl": "/notes/1825259"}, {"RefNumber": "1785100", "RefComponent": "BC-WD-ABA", "RefTitle": "Internet Explorer 10 Release Notes", "RefUrl": "/notes/1785100"}, {"RefNumber": "1781218", "RefComponent": "BC-I18", "RefTitle": "Spontaneous code page conversion error after DB recoonect", "RefUrl": "/notes/1781218"}, {"RefNumber": "1780629", "RefComponent": "BC-OP-AIX", "RefTitle": "AIX: Minimal OS Requirements for SAP Kernel", "RefUrl": "/notes/1780629"}, {"RefNumber": "1760861", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR data in double stack with SAP_BASIS 7.00", "RefUrl": "/notes/1760861"}, {"RefNumber": "1744209", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720, 721 and 722: Versions and Kernel Patch Levels", "RefUrl": "/notes/1744209"}, {"RefNumber": "1737262", "RefComponent": "BC-CST-MS", "RefTitle": "Message server: crash during URL handler load", "RefUrl": "/notes/1737262"}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283"}, {"RefNumber": "1722035", "RefComponent": "BC-I18", "RefTitle": "Islamic calendars: Initial dates not handled correctly", "RefUrl": "/notes/1722035"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986"}, {"RefNumber": "1709911", "RefComponent": "BC-UPG-RDM", "RefTitle": "Maintenance tools on request", "RefUrl": "/notes/1709911"}, {"RefNumber": "1707141", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent - How to upgrade SAP Kernel", "RefUrl": "/notes/1707141"}, {"RefNumber": "1707012", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Prepare Installation Kits for Usage with DCK Kernel", "RefUrl": "/notes/1707012"}, {"RefNumber": "1702912", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: collectSystemInfo: sid DLT ... does not match sid DLT", "RefUrl": "/notes/1702912"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684"}, {"RefNumber": "1667336", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Monitoring with Kernel 7.20 (DCK)", "RefUrl": "/notes/1667336"}, {"RefNumber": "1657890", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: STARTSAP calling DLTOLDPKG via instance profile", "RefUrl": "/notes/1657890"}, {"RefNumber": "1652660", "RefComponent": "BC-CCM-MON", "RefTitle": "JMonAPI: UnsatisfiedLinkError, InvocationTargetException, deadlock of Java threads", "RefUrl": "/notes/1652660"}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731"}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598"}, {"RefNumber": "1610716", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Correcting runtime objects with the wrong alignment", "RefUrl": "/notes/1610716"}, {"RefNumber": "1598550", "RefComponent": "BC-INS", "RefTitle": "SAPCAR: Signed archive", "RefUrl": "/notes/1598550"}, {"RefNumber": "1597627", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SAP HANA connection", "RefUrl": "/notes/1597627"}, {"RefNumber": "1581595", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "rfcexec or startrfc are missing after upgrade", "RefUrl": "/notes/1581595"}, {"RefNumber": "1563102", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux Requirements for 7.20 EXT and higher kernel", "RefUrl": "/notes/1563102"}, {"RefNumber": "1553465", "RefComponent": "BC-OP-NT", "RefTitle": "Installation requirements for SAP Kernels on Windows (C++ runtime environment, VCredist versions)", "RefUrl": "/notes/1553465"}, {"RefNumber": "1553301", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553301"}, {"RefNumber": "1553300", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553300"}, {"RefNumber": "1517379", "RefComponent": "BC-CCM-MON-SLG", "RefTitle": "Which system log format does the 720 kernel write?", "RefUrl": "/notes/1517379"}, {"RefNumber": "1487269", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation of 720 kernel on SAP MaxDB-based SAP systems", "RefUrl": "/notes/1487269"}, {"RefNumber": "1467086", "RefComponent": "BC-DB-MSS", "RefTitle": "SAP 7.20 Kernel (DCK) on MS SQL Server", "RefUrl": "/notes/1467086"}, {"RefNumber": "1454536", "RefComponent": "BC-CTS-TLS", "RefTitle": "Deadlock on table REPOSRC when importing into running system", "RefUrl": "/notes/1454536"}, {"RefNumber": "1431794", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Instant Client", "RefUrl": "/notes/1431794"}, {"RefNumber": "1423600", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Note in work process trace: \"locale is missing\"", "RefUrl": "/notes/1423600"}, {"RefNumber": "1341097", "RefComponent": "BC-DB-MSS", "RefTitle": "MSSQL: 720 DCK, 7.0* on SQL 2000, dbmssslib_oledb.dll", "RefUrl": "/notes/1341097"}, {"RefNumber": "1163719", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Incorrect version of the JMON library", "RefUrl": "/notes/1163719"}, {"RefNumber": "1149318", "RefComponent": "BC-OP-AS4", "RefTitle": "User concept conversion using the tool CONVUSRCPT", "RefUrl": "/notes/1149318"}, {"RefNumber": "1140031", "RefComponent": "BC-MID-RFC", "RefTitle": "Security Note: rfcexec/startrfc Used in File Interfaces", "RefUrl": "/notes/1140031"}, {"RefNumber": "1119735", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agents: Upgrade of monitored systems from 7.0 to 7.1", "RefUrl": "/notes/1119735"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096"}, {"RefNumber": "1028690", "RefComponent": "BC-FES-IGS", "RefTitle": "True Type Fonts delivered with IGS", "RefUrl": "/notes/1028690"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "1848999", "RefComponent": "BC-IAM-SSO-CCL", "RefTitle": "Central Note for CommonCryptoLib 8 (SAPCRYPTOLIB)", "RefUrl": "/notes/1848999"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1911741", "RefComponent": "BC-XI-IBC", "RefTitle": "Cannot connect to server using message server ms:// P4 error in PI ESR", "RefUrl": "/notes/1911741 "}, {"RefNumber": "2264694", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP Programs / Transactions Recompiling (Program Load Invalidated) after Kernel Upgrade or Patch.", "RefUrl": "/notes/2264694 "}, {"RefNumber": "1793070", "RefComponent": "BC-VMC", "RefTitle": "VMC doesn't start after installing new kernel", "RefUrl": "/notes/1793070 "}, {"RefNumber": "2494181", "RefComponent": "BC-DB-DB4", "RefTitle": "Choosing the correct kernel to upgrade with on IBM i", "RefUrl": "/notes/2494181 "}, {"RefNumber": "1737528", "RefComponent": "BC-JAS-COR", "RefTitle": "Could not open the ICU common library: AS JAVA startup issue", "RefUrl": "/notes/1737528 "}, {"RefNumber": "1972803", "RefComponent": "BC-OP-AIX", "RefTitle": "SAP on AIX: Recommendations", "RefUrl": "/notes/1972803 "}, {"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684 "}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731 "}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283 "}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986 "}, {"RefNumber": "1760861", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR data in double stack with SAP_BASIS 7.00", "RefUrl": "/notes/1760861 "}, {"RefNumber": "1785100", "RefComponent": "BC-WD-ABA", "RefTitle": "Internet Explorer 10 Release Notes", "RefUrl": "/notes/1785100 "}, {"RefNumber": "1477428", "RefComponent": "BC-SEC-LGN", "RefTitle": "Downport (SAP_BASIS 7.01): Security Session Management", "RefUrl": "/notes/1477428 "}, {"RefNumber": "1707141", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent - How to upgrade SAP Kernel", "RefUrl": "/notes/1707141 "}, {"RefNumber": "1702912", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: collectSystemInfo: sid DLT ... does not match sid DLT", "RefUrl": "/notes/1702912 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519 "}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}, {"RefNumber": "1780629", "RefComponent": "BC-OP-AIX", "RefTitle": "AIX: Minimal OS Requirements for SAP Kernel", "RefUrl": "/notes/1780629 "}, {"RefNumber": "1667336", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Monitoring with Kernel 7.20 (DCK)", "RefUrl": "/notes/1667336 "}, {"RefNumber": "1744209", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720, 721 and 722: Versions and Kernel Patch Levels", "RefUrl": "/notes/1744209 "}, {"RefNumber": "1825259", "RefComponent": "BC-CST-STS", "RefTitle": "UNIX sapstartsrv: the system does not start with 720 kernel", "RefUrl": "/notes/1825259 "}, {"RefNumber": "1781218", "RefComponent": "BC-I18", "RefTitle": "Spontaneous code page conversion error after DB recoonect", "RefUrl": "/notes/1781218 "}, {"RefNumber": "1737262", "RefComponent": "BC-CST-MS", "RefTitle": "Message server: crash during URL handler load", "RefUrl": "/notes/1737262 "}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598 "}, {"RefNumber": "1707012", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Prepare Installation Kits for Usage with DCK Kernel", "RefUrl": "/notes/1707012 "}, {"RefNumber": "1722035", "RefComponent": "BC-I18", "RefTitle": "Islamic calendars: Initial dates not handled correctly", "RefUrl": "/notes/1722035 "}, {"RefNumber": "1652660", "RefComponent": "BC-CCM-MON", "RefTitle": "JMonAPI: UnsatisfiedLinkError, InvocationTargetException, deadlock of Java threads", "RefUrl": "/notes/1652660 "}, {"RefNumber": "1454536", "RefComponent": "BC-CTS-TLS", "RefTitle": "Deadlock on table REPOSRC when importing into running system", "RefUrl": "/notes/1454536 "}, {"RefNumber": "1643916", "RefComponent": "BC-FES-IGS", "RefTitle": "igshelper.sar not updated when upgrading from 720 to 720_EXT", "RefUrl": "/notes/1643916 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}