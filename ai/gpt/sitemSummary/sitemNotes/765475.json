{"Request": {"Number": "765475", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 448, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015743862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000765475?language=E&token=431A9AAB01817745548B75B80D9AF564"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000765475", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "765475"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 125}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.09.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18-UNI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Unicode"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Unicode", "value": "BC-I18-UNI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18-UNI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "765475 - Unicode Conversion: Troubleshooting"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Problems or errors occur during conversion from non-Unicode SAP System to Unicode. This SAP Note applies for Single Code Page, MDMP system conversion and Combined Upgrade &amp; Unicode Conversion.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MDMP, Single Code Page, CU&amp;UC, TU&amp;UC, database export, database conversion, database import, R3load, SPUMG, SUMG, preconversion, postconversion</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You encounter problems or errors during system conversion.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Attached to this SAP Note you will find a Troubleshooting Guide for Unicode Conversion, Combined Upgrade &amp; Unicode Conversion (CU&amp;UC) and Twin Upgrade &amp; Unicode Conversion (TU&amp;UC).<br />For general information about Unicode support at SAP please refer to SAP Developer Network -&gt; Quicklink /unicode.<br /><br /><B>NOTE</B><br />There are some R3load patches which should not be used for the Unicode Conversion:<br />R3load 6.40 Patch Level.59, 60, 61<br />R3load 7.00 Patch Level 14 and 15.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-I18 (Internationalization (I18N))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D035318)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D040933)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000765475/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765475/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "UCTroubleshootingguide.pdf", "FileSize": "1299", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000339162004&iv_version=0125&iv_guid=6CAE8B3E8D4B1ED7938889421C3720D4"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "954001", "RefComponent": "BC-I18-UNI", "RefTitle": "Error fix: ILLEGAL_SUBSTRING_MODIFICATION in SUMG", "RefUrl": "/notes/954001"}, {"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441"}, {"RefNumber": "928909", "RefComponent": "BC-I18-UNI", "RefTitle": "Repair table data in SUMG in Unicode systems", "RefUrl": "/notes/928909"}, {"RefNumber": "895804", "RefComponent": "XX-CSC-RU-HR", "RefTitle": "Problematic rows in table DMEE_TREE_NODE", "RefUrl": "/notes/895804"}, {"RefNumber": "895560", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for languages only available in Unicode systems", "RefUrl": "/notes/895560"}, {"RefNumber": "885441", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "Common Migration Errors", "RefUrl": "/notes/885441"}, {"RefNumber": "842788", "RefComponent": "BC-DB-DBI", "RefTitle": "DB Multiconnect: Unicode Migration", "RefUrl": "/notes/842788"}, {"RefNumber": "837173", "RefComponent": "BC-I18-UNI", "RefTitle": "RADCUCNT in Unicode Conversion: Collective Note", "RefUrl": "/notes/837173"}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991"}, {"RefNumber": "756534", "RefComponent": "BC-I18-UNI", "RefTitle": "Automatic Assignment of Languages with Character Statistics", "RefUrl": "/notes/756534"}, {"RefNumber": "753334", "RefComponent": "BC-I18", "RefTitle": "Unicode Conversion: Problem in Japanese device types", "RefUrl": "/notes/753334"}, {"RefNumber": "73606", "RefComponent": "BC-I18", "RefTitle": "Supported Languages and Code Pages", "RefUrl": "/notes/73606"}, {"RefNumber": "726954", "RefComponent": "BC-I18-UNI", "RefTitle": "Private Use Areas in Unicode Systems", "RefUrl": "/notes/726954"}, {"RefNumber": "722193", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC legacy non-Unicode clients and Unicode servers", "RefUrl": "/notes/722193"}, {"RefNumber": "718329", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load terminates the export during a Unicode conversion", "RefUrl": "/notes/718329"}, {"RefNumber": "695196", "RefComponent": "BC-DB-DBI", "RefTitle": "Error in the export for Unicode migration", "RefUrl": "/notes/695196"}, {"RefNumber": "672835", "RefComponent": "BC-I18", "RefTitle": "Textflags could cause problems during Unicode conversion", "RefUrl": "/notes/672835"}, {"RefNumber": "627764", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Unicode migration: table pools inconsistent after conversion", "RefUrl": "/notes/627764"}, {"RefNumber": "614550", "RefComponent": "BC-I18", "RefTitle": "Troubleshooting BC-I18", "RefUrl": "/notes/614550"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "573044", "RefComponent": "PA", "RefTitle": "Unicode conversion HR", "RefUrl": "/notes/573044"}, {"RefNumber": "480671", "RefComponent": "BC-I18", "RefTitle": "The Text Language Flag of LANG Fields", "RefUrl": "/notes/480671"}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519"}, {"RefNumber": "42305", "RefComponent": "BC-I18", "RefTitle": "RSCPINST (I18N configuration tool)", "RefUrl": "/notes/42305"}, {"RefNumber": "379940", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode based mySAP availability", "RefUrl": "/notes/379940"}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1054852", "RefComponent": "BC-DB-MSS", "RefTitle": "Recommendations for migrations using Microsoft SQL Server", "RefUrl": "/notes/1054852"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2248474", "RefComponent": "BC-I18-UNI", "RefTitle": "DDIC_TYPE_REF_ACCESS_ERROR during CU&UC", "RefUrl": "/notes/2248474 "}, {"RefNumber": "2074715", "RefComponent": "BC-I18", "RefTitle": "Asian code pages 8008, 8308, 8408 and 8508", "RefUrl": "/notes/2074715 "}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438 "}, {"RefNumber": "837173", "RefComponent": "BC-I18-UNI", "RefTitle": "RADCUCNT in Unicode Conversion: Collective Note", "RefUrl": "/notes/837173 "}, {"RefNumber": "1054852", "RefComponent": "BC-DB-MSS", "RefTitle": "Recommendations for migrations using Microsoft SQL Server", "RefUrl": "/notes/1054852 "}, {"RefNumber": "42305", "RefComponent": "BC-I18", "RefTitle": "RSCPINST (I18N configuration tool)", "RefUrl": "/notes/42305 "}, {"RefNumber": "73606", "RefComponent": "BC-I18", "RefTitle": "Supported Languages and Code Pages", "RefUrl": "/notes/73606 "}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519 "}, {"RefNumber": "672835", "RefComponent": "BC-I18", "RefTitle": "Textflags could cause problems during Unicode conversion", "RefUrl": "/notes/672835 "}, {"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441 "}, {"RefNumber": "516246", "RefComponent": "BC-INS", "RefTitle": "INST: System Copy for SAP Systems based on SAP Web AS 6.20", "RefUrl": "/notes/516246 "}, {"RefNumber": "726954", "RefComponent": "BC-I18-UNI", "RefTitle": "Private Use Areas in Unicode Systems", "RefUrl": "/notes/726954 "}, {"RefNumber": "895560", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for languages only available in Unicode systems", "RefUrl": "/notes/895560 "}, {"RefNumber": "614550", "RefComponent": "BC-I18", "RefTitle": "Troubleshooting BC-I18", "RefUrl": "/notes/614550 "}, {"RefNumber": "573044", "RefComponent": "PA", "RefTitle": "Unicode conversion HR", "RefUrl": "/notes/573044 "}, {"RefNumber": "480671", "RefComponent": "BC-I18", "RefTitle": "The Text Language Flag of LANG Fields", "RefUrl": "/notes/480671 "}, {"RefNumber": "756534", "RefComponent": "BC-I18-UNI", "RefTitle": "Automatic Assignment of Languages with Character Statistics", "RefUrl": "/notes/756534 "}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991 "}, {"RefNumber": "753334", "RefComponent": "BC-I18", "RefTitle": "Unicode Conversion: Problem in Japanese device types", "RefUrl": "/notes/753334 "}, {"RefNumber": "895804", "RefComponent": "XX-CSC-RU-HR", "RefTitle": "Problematic rows in table DMEE_TREE_NODE", "RefUrl": "/notes/895804 "}, {"RefNumber": "695196", "RefComponent": "BC-DB-DBI", "RefTitle": "Error in the export for Unicode migration", "RefUrl": "/notes/695196 "}, {"RefNumber": "722193", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC legacy non-Unicode clients and Unicode servers", "RefUrl": "/notes/722193 "}, {"RefNumber": "928909", "RefComponent": "BC-I18-UNI", "RefTitle": "Repair table data in SUMG in Unicode systems", "RefUrl": "/notes/928909 "}, {"RefNumber": "954001", "RefComponent": "BC-I18-UNI", "RefTitle": "Error fix: ILLEGAL_SUBSTRING_MODIFICATION in SUMG", "RefUrl": "/notes/954001 "}, {"RefNumber": "885441", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "Common Migration Errors", "RefUrl": "/notes/885441 "}, {"RefNumber": "718329", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load terminates the export during a Unicode conversion", "RefUrl": "/notes/718329 "}, {"RefNumber": "379940", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode based mySAP availability", "RefUrl": "/notes/379940 "}, {"RefNumber": "627764", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Unicode migration: table pools inconsistent after conversion", "RefUrl": "/notes/627764 "}, {"RefNumber": "842788", "RefComponent": "BC-DB-DBI", "RefTitle": "DB Multiconnect: Unicode Migration", "RefUrl": "/notes/842788 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}