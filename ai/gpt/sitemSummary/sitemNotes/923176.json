{"Request": {"Number": "923176", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 364, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016057202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000923176?language=E&token=CE2C650AC841A5DE7EF7C33AD20A2518"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000923176", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000923176/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "923176"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06/26/2009"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorizations"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorizations", "value": "BW-BEX-OT-OLAP-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "923176 - Support situation authorization management BI70/NW2004s"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note deals with the upgrade and support of reporting authorizations.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Upgrade and support of reporting authorizations</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note deals with the upgrade and support of reporting authorizations.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;New features<br /><br />As of SAP NetWeaver 2004s, a completely new concept for analysis authorizations will be used in BI, based only on authorizations as elementary objects. The previous authorization concept (called reporting authorizations) will be completely replaced. We will be moving away from the SAP authorization concept of authorization objects. The new authorizations can now include any authorization-relevant characteristics and treat single values, intervals and hierarchy authorizations the same. Navigation attributes can now also be flagged as authorization relevant in the attribute maintenance for characteristics and can be transferred into authorizations as characteristics.<br />Furthermore, the restriction to ten characteristics and the name restriction for InfoObjects to ten characters are no longer valid.<br /><br />The activation of authorizations by InfoProvider is no longer required. Instead, all authorization-relevant characteristics are checked. Beyond that, there are three special characteristics for the InfoProvider, the activity, and the reconceived authorization characteristic validity, which specifies the validity period of an authorization. The authorization of an activity, such as Write, is set for an InfoProvider using InfoProvider. The validity period of an authorization is set using Validity. Patterns and open time intervals provide a variety of options, such as creating authorizations that are valid periodically.<br /><br />The characteristic for InfoProvider maps the structure of the InfoProvider store in the Data Warehousing Workbench with its master data and the hierarchy characteristic for InfoArea. In this way it is also possible to authorize entire InfoAreas.<br /><br />The authorizations are available as virtual master data for the characteristic 0TCTAUTH and can also be grouped hierarchically, for example to create thematic or role-based arrangements.<br /><br />The authorization checks on authorization objects for hierarchies and InfoProviders that also had to be maintained until now are no longer required for reporting analysis and have thus been removed from the analysis, as opposed to back-end management. This means that there is no longer a difference between hierarchy authorization and hierarchy node authorization in the query.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Managing analysis authorizations<br /><br />There is a new and better integrated maintenance transaction, from which all functions for managing analysis authorizations can be reached.<br />All activities for managing components of the analysis authorization system are maintained with authorizations for the new S_RSEC authorization object, which covers all relevant objects with namespace authorizations for specific activities.<br /><br />The maintenance transactions have been completely redesigned, made accessible and customized to typical users. Closer integration enables quicker administration and better control of the relevant objects than was possible previously.<br /><br />There is a separate infrastructure for maintaining the authorizations and assigning them to users. These replace the standard transactions for user maintenance from SAP NetWeaver. It is not absolutely necessary to assign authorizations to roles. This can also be achieved with a connection to the SAP role concept. With a special authorization object for role connection, the new authorizations can be assigned using role maintenance.<br /><br />The function for generating authorizations was adapted and enhanced to include the option of loading medium and long texts as well. It is no longer necessary to select authorization objects. The authorizations are generated directly from the entries in special DataStore objects. In general, the adjustments are not very extensive.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Checking and monitoring analysis authorizations<br /><br />To improve revision capabilities, a complete change recording of authorizations and assignments to users was created. These changes can be analyzed using queries on RemoteProviders and restricted with analysis authorizations.<br /><br />In addition, there is a new tool for troubleshooting that replaces the old authorization log. In addition, there is a new tool for troubleshooting that replaces the old authorization log. It uses the HTML format and can be saved and printed and it is stored persistently on the database.<br /><br />The log for generating authorizations was improved with regard to the readability of messages.<br /><br />It is possible to execute certain actions in connection with the analysis as other users (as a test). This is protected by password.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Migration<br /><br />The old SAP BW 3.x authorizations concept will continue to exist in SAP NetWeaver 2004s for compatibility reasons, but it will be removed completely in the next release. You should no longer use features to be discontinued in new implementations. SAP recommends that you change to the new concept, which is also the default setting.<br /><br />To make the transition easier when you upgrade to NetWeaver 2004s,<br />the old source code was retained and integrated as much as possible.<br />This should allow you to use the old authorizations concept more or less as before. There is a switch that allows you to switch back to the old authorization concept in an emergency.<br /><br />The new concept is not completely compatible with the old concept that was based on authorization objects.<br />For this reason, there is a migration help that can carry out many steps semi-automatically. However, this may take some time which can be reduced using the migration option.<br /><br />In addition, features that do not yet exist in BW3.x and that do not or only partly work in the old authorization concept cannot be supported.  An example is integrated planning, which is not designed for old authorizations and which may behave unexpectedly or inconsistently in places.<br /><br />SAP Notes and Support Packages have been provided to correct certain security problems; however, these may require a reconfiguration.<br />Therefore, if you upgrade from a 3.x Support Package level that does not correspond to the general support strategy (see Note 375631), we cannot guarantee that the authorization configuration still works. In this case, you may have to make certain adjustments.<br />This also applies if you upgrade from BW2.x to BI7.x.<br /><br />For general security reasons, we recommend that you plan the upgrade in advance (before the GoLive) and that you do so thoroughly to prevent any security problems.<br />SAP provides a consulting package here - the BI Authorization Migration Service:<br /><br /><B>BI Authorization Migration Service</B><br />Is a complete package for planning and executing the BI authorization migration and includes</p> <UL><LI>Detailed planning of the migration including system analysis, concept adjustment, and project planning</LI></UL> <UL><LI>Migration of the old BW reporting authorizations to the new BW 7.0 analysis authorizations</LI></UL> <UL><LI>Test and GoLive support</LI></UL> <p><br />For more detailed information, contact the SAP Business Technology Factory at <U><EMAIL></U><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Further information<br /><br /> Release Notes (German):<br />http://help.sap.com/saphelp_nw04s/helpdata/de/80/D71042F664E22CE10000000A1550B0/content.htm<br /><br /> Release Notes (English):<br />http://help.sap.com/saphelp_nw04s/helpdata/en/80/D71042F664E22CE10000000A1550B0/content.htm<br /><br /> German documentation:<br /> http://help.sap.com/saphelp_nw04s/helpdata/de/66/019441b8972e7be10000000a1550b0/frameset.htm<br /><br /> English documentation<br /> http://help.sap.com/saphelp_nw04s/helpdata/en/66/019441b8972e7be10000000a1550b0/frameset.htm<br /><br />FAQ:<br /> Consulting Note 820183 provides answers to some frequently asked questions.<br /><br />A further forum for questions and exchange with other customers during the ramp up phase of SAP NetWeaver 2004s in SDN:<br /><br />https://www.sdn.sap.com/irj/sdn/forum?forumID=154&amp;start=0<br /><br />Also see the FAQs on SAP Service Marketplace alias /bifaq!<br />http://service.sap.com/bifaq --&gt; NetWeaver 2004s<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041044)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D041044)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000923176/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000923176/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000923176/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000923176/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000923176/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000923176/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000923176/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000923176/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000923176/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990"}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}, {"RefNumber": "1125108", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Use of obsolete 3.x authorizations in BW 7.x", "RefUrl": "/notes/1125108"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1125108", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Use of obsolete 3.x authorizations in BW 7.x", "RefUrl": "/notes/1125108 "}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}, {"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}