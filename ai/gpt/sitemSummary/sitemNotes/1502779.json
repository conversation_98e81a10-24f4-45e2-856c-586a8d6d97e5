{"Request": {"Number": "1502779", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 325, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008895402017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001502779?language=E&token=9EECC5EC879B5A0A7AE86D909FF88F7C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001502779", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001502779/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1502779"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2011/03/08"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-DFS-PM"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - BI Content DFPS Plant Maintenance"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BI Content Defense & Public Security", "value": "BW-BCT-DFS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-DFS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - BI Content DFPS Plant Maintenance", "value": "BW-BCT-DFS-PM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-DFS-PM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1502779 - 0DF_LMMIS_ATTR:Termination-RSDMD 191 \"Duplicate data record\""}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use the DataSource 0DF_LMMIS_ATTR or 0DF_LMMIS_TEXT to load data to your Business Warehouse (BW) system.<br /><br />The update of the request terminates with the following error message:</p> <UL><LI>RSDMD 191: &amp;1: Data record &amp;2 (with the key &amp;3 of the characteristic 0DF_LMMIS) : Duplicate data record</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Mission<br />FORCE_ID, 0DF_FORCE, Internal Key for a Force Element<br />FORCE_CNT, 0DF_LMFOCNT, Internal (Version) Counter<br />RSDMD 191, RSDMD191<br />RSM2704, RSM2 704 \"Data records for package &amp;2 selected in PSA - &amp;1 error(s)\"<br />RSAR119, RSAR 119 \"Error &amp; in the update\"<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br /><br />In the source system, in the table /ISDFPS/LM_MIS, in addition to the \"MISSION\" field, the fields FORCE_ID and FORCE_CNT are also turned into key fields.<br />This change is not taken into account in the BW system.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package and then transfer the BI CONTENT in the BW system using transaction RSOR.<br /><br />If you implement this note manually, the characteristics FORCE_ID and FORCE_CNT must be compounded to the \"MISSION\" field for the InfoObject 0DF_LMMIS in the BW system.<br />Due to compounding, a change to the DataSource 0DF_LMMIS_TEXT is also required.<br /><br />If you want to implement this note manually, you must perform the steps described below in the specified sequence.<br />Note the following: After you have manually implemented this note, do not activate the BI content, as this would result in the changes being overwritten with the old content.<br /><br />A. Changes in the R/3 source system:<br />=================================</p> <OL>1. Change the DataSource 0DF_LMMIS_TEXT:</OL> <OL><OL>a) Call transaction RSA2, enter the DataSource 0DF_LMMIS_TEXT and choose \"Change\".</OL></OL> <OL><OL>b) Choose the \"Fields\" tab page and, in the properties column, change the values of the fields FORCE_ID and FORCE_CNT from value 3 to value P. Choose ENTER.<br />In the \"Transfer Possible\" column, a checkmark is now set for both fields.</OL></OL> <OL><OL>c) Save your change.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note the following:<br /> Afterwards, the changed DataSource cannot be activated with transaction RSA5, since the changed \"A\" version would be overwritten with the old \"D\" version. <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the \"Change\" button is not available in transaction RSA2 in your R/3 system, you can execute the required change using the report Z_CHANGE_ROOSFIELD_NOTE1502779. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To create the report in your system, carry out the steps described below. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Call transaction SE38, enter the program name Z_CHANGE_ROOSFIELD_NOTE1502779, and choose \"Create\". <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create the report with the following attributes: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Title:&#x00A0;&#x00A0;See SAP Note 1502779.<br />- Type: 1 (Executable program)<br />- Application: I (Plant maintenance)<br />- Development class: Local object <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy the report from the correction instructions in this note and insert the source code into the ABAP Editor. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save and activate the report.<br /> Execute the report. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The report changes the field attributes of the fields FORCE_CNT and FORCE_ID from the value '3' to 'P' in both the 'D' and the 'A' versions. <p><br /><br />B. Changes in the BW system:<br />===========================</p> <OL>2. Replicate the DataSource 0DF_LMMIS_TEXT:</OL> <OL><OL>a) Call transaction RSA1; in the navigator, choose \"Modeling\" and then \"Source Systems\".</OL></OL> <OL><OL>b) Use the mouse to select the source system and go to the menu \"Edit -&gt; Objects -&gt; DataSource\".</OL></OL> <OL><OL>c) On the screen \"DataSource Repository\", enter the DataSource 0DF_LMMIS_TEXT and the source system.</OL></OL> <OL><OL>d) Now use the menu \"DataSource -&gt; Replicate DataSource\" to replicate the DataSource.</OL></OL> <p></p> <OL>3. Change the data type of the InfoObject 0DF_FORCE:</OL> <OL><OL>a) Call transaction RSD1, enter the InfoObject 0DF_FORCE in the input field and choose \"Maintain\".</OL></OL> <OL><OL>b) Choose the \"General\" tab page; in the \"Dictionary\" screen area, change the data type from NUMC and length 8 to the data type CHAR with the length 32.</OL></OL> <OL><OL>c) Activate the changed InfoObject.</OL></OL> <p></p> <OL>4. Add the characteristics 0DF_FORCE and 0DF_LMFOCNT to the communication structure 0DF_IS_DFS_41:</OL> <OL><OL>a) Call transaction RS_ISTD_REMOTE; in the screen area \"Maintain InfoSource\", enter the InfoSource 0DF_IS_DFS_41 in the input field and choose \"Maint. InfoSource\".</OL></OL> <OL><OL>b) In the displayed communication structure, scroll down and add the InfoObjects 0DF_FORCE and 0DF_LMFOCNT to the column \"InfoObject\".<br />Choose ENTER so that the system adds the remaining data.</OL></OL> <OL><OL>c) Activate the enhanced communication structure.</OL></OL> <p></p> <OL>5. Remove the InfoObjects 0DF_FORCE and 0DF_LMFOCNT from the transfer rules for your source systems:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To allow compounding, you must temporarily remove the InfoObjects 0DF_FORCE and 0DF_LMFOCNT from the transfer rules. <OL><OL>a) Call transaction RSA1 and open the structure for the InfoSource 0DF_LMMIS.</OL></OL> <OL><OL>b) Open the transfer rule 0DF_LMMIS_ATTR in edit mode by choosing \"Change\" in the \"Execute Function\" column.</OL></OL> <OL><OL>c) On the \"Transfer Rules\" tab page, in the table on the left, select the rows with the InfoObjects 0DF_FORCE and 0DF_LMFOCNT; choose the \"right\" arrow (to delete selected rules).<br />After you delete the rules, a red cross is displayed in the column \"Tp\" of both InfoObjects.</OL></OL> <OL><OL>d) Activate the changed transfer rule.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If there are further transfer rules 0DF_LMMIS_ATTR for other source systems, carry out point 5 for each source system. <p></p> <OL>6. Compound the characteristics 0DF_FORCE and 0DF_LMFOCNT in the InfoObject 0DF_LMMIS:</OL> <OL><OL>a) Call transaction RSD1, enter the InfoObject 0DF_LMMIS in the input field and choose \"Maintain\".</OL></OL> <OL><OL>b) Choose the tab page \"Attribute\"; in the screen area for characteristics attributes, select the row with the attributes 0DF_LMFOCNT and 0DF_FORCE; delete the attributes by choosing \"Delete\".</OL></OL> <OL><OL>c) Choose the tab page for compounding and enter the characteristics 0DF_FORCE and 0DF_LMFOCNT in the column \"Superior InfoObject\".<br />Choose ENTER so that the system adds the remaining data.</OL></OL><OL><OL>d) Activate the changed InfoObject.</OL></OL> <p></p> <OL>7. Add the InfoObjects 0DF_FORCE and 0DF_LMFOCNT to the transfer rules for your source system:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The changes that were made in point 5 must now be reversed. <OL><OL>a) Execute points 5a and 5b again.</OL></OL> <OL><OL>b) In the table on the left, select the row with the InfoObject 0DF_FORCE and, in the table on the right, select the row with the InfoObject 0DF_FORCE.</OL></OL> <OL><OL>c) Choose the \"left\" arrow (for exceptions in transfer rules).</OL></OL> <OL><OL>d) Now select the row with the InfoObject 0DF_LMFOCNT in the table on the left, and select the row with the InfoObject with the same name in the table on the right. Choose the \"left\" arrow again.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The transfer rule now has the same status as it had before point 5. <OL><OL>e) Activate the changed transfer rule.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If there are further transfer rules 0DF_LMMIS_ATTR for other source systems, carry out point 7 for each source system. <p></p> <OL>8. Change the transfer rule for the DataSource 0DF_LMMIS_TEXT:</OL> <OL><OL>a) In the \"Modeling\" screen area, choose \"InfoSources\" and search for the InfoSource 0DF_LMMIS.</OL></OL> <OL><OL>b) Double-click the transfer rule that already exists for your source system for the DataSource 0DF_LMMIS_TEXT to open it.</OL></OL> <OL><OL>c) Choose the tab page \"DataSource/Trans. Structure\", select the field FORCE_ID on both the right-hand side (DataSource) and on the left-hand side (Transfer Structure). Using the \"left\" arrow key, transfer the field to the left-hand side.</OL></OL> <OL><OL>d) Select the field FORCE_CNT on the right-hand side and the field with the same name on the left-hand side. Transfer this field to the left-hand side using the \"left\" arrow key.</OL></OL> <OL><OL>e) Choose the \"Transfer Rules\" tab page. On the right-hand side (Assign.InfObjct-field), add the InfoObjects 0DF_FORCE and 0DF_LMFOCNT at the end of the table.</OL></OL> <OL><OL>f) Choose ENTER to assign the InfoObjects to the InfoObjects with the same name on the left-hand side (Communication Structure/Transfer Rules).</OL></OL> <OL><OL>g) Activate the changed transfer rule.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the transfer rule for the DataSource 0DF_LMMIS_TEXT does not exist yet, then the measures described in point 8 are not required. This is because the system automatically adds and assigns both the fields FORCE_ID and FORCE_CNT together with their InfoObjects when the transfer rules are created. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to save work, then delete the existing transfer rules, including the PSA table, after the replication, and create the transfer rule again. <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D033047"}, {"Key": "Processor                                                                                           ", "Value": "D033047"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001502779/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001502779/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1736351", "RefComponent": "BW-BCT-DFS-OF", "RefTitle": "InfoObject '0DF_FORCE':  Data type CHAR32 is incorrect", "RefUrl": "/notes/1736351"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1736351", "RefComponent": "BW-BCT-DFS-OF", "RefTitle": "InfoObject '0DF_FORCE':  Data type CHAR32 is incorrect", "RefUrl": "/notes/1736351 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "704", "To": "704", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "705", "To": "705", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "735", "To": "735", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-DFPS", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-DFPS 603", "SupportPackage": "SAPK-60308INEADFPS", "URL": "/supportpackage/SAPK-60308INEADFPS"}, {"SoftwareComponentVersion": "EA-DFPS 604", "SupportPackage": "SAPK-60409INEADFPS", "URL": "/supportpackage/SAPK-60409INEADFPS"}, {"SoftwareComponentVersion": "EA-DFPS 605", "SupportPackage": "SAPK-60503INEADFPS", "URL": "/supportpackage/SAPK-60503INEADFPS"}, {"SoftwareComponentVersion": "BI_CONT 704", "SupportPackage": "SAPK-70409INBICONT", "URL": "/supportpackage/SAPK-70409INBICONT"}, {"SoftwareComponentVersion": "BI_CONT 705", "SupportPackage": "SAPK-70502INBICONT", "URL": "/supportpackage/SAPK-70502INBICONT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-DFPS", "NumberOfCorrin": 1, "URL": "/corrins/0001502779/485"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}