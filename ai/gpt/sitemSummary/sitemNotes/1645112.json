{"Request": {"Number": "1645112", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 475, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009768322017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001645112?language=E&token=A4908EDC16C26B9A2FE2A0CEBCDA428A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001645112", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001645112/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1645112"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.05.2012"}, "SAPComponentKey": {"_label": "Component", "value": "PY-GB-PS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Public Sector"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "United Kingdom", "value": "PY-GB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-GB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Public Sector", "value": "PY-GB-PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-GB-PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1645112 - PY-GB-PS: USS CARE Schemes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>USS have introduced rules for anyone that joins a Higher Education Institution on or after 01/10/2011 and wishes to join a USS pension scheme.<br /><br />If they were previously (within the last 30 months) a member of a USS pension scheme, then they will continue under those rules (Final Salary Section).<br /><br />However, if they were not a member of a USS pension scheme in the last 30 months, then they will have to join under the new rules (Career Revalued Benefits section).<br /><br />The CARE (Career Average Revalued Earnings) Scheme rules are published at the USS website (www.uss.co.uk) and have been sent out via letters to all Higher Education Institutions.<br /><br />This note does not cover the implementation of the CARE scheme in your Master Data or Payroll solutions. It assumes that you will create a new scheme that is specific to CARE and implement it \"as normal\".<br /><br />This note covers the reporting requirements that USS have placed on the Higher Education Institutions for any members that join the new CARE scheme that they have implemented. Again the note does not cover all reporting requirements, but only covers the enhancements, to the existing reports that SAP provides, to handle the new CARE reporting requirements.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>USS RPUUSSG0 RPUUSSG1</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>USS Legal Change</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Customers in release 6.00<br />_________________________<br /><br />Please apply - L7DK183733.SAR and the correction instructions attached<br />with this note.<br /><br />Customers in release 6.04<br />_________________________<br /><br />Please apply - USS604.SAR&#x00A0;&#x00A0;and the correction instructions attached with<br />this note.<br /><br />Customers in other lower releases:<br />Please apply - USS_Manual_steps.pdf nd the correction instructions attached with<br />this note.<br /></p> <b></b><br /> <p><br />Software Changes<br />================<br /><br />Please load the relevant HRSP that includes these changes.<br /><br />Customising Changes<br />===================<br /><br />1) USS Pension Schemes.<br /><br />From October 2011 onwards, it is necessary to identify which of the USS schemes in your system are CARE (Career Average Revalued Earnings) Schemes. This can be done via a new checkbox provided on the USS Pension Schemes view (V_T5GPBS_USS).<br /><br />2) Wage Type Groups<br /><br />a) Regular (Full Time) Employees<br /><br />USC1 USS CARE Non-SalSacrifice<br />USC2 USS CARE Salary Sacrifice<br />USC3 USS CARE AVCS<br /><br />b) Variable Time (Part Time) Employees<br /><br />When CARE was introduced most SAP customers had not implemented a payroll solution that separated VTE (Variable Time Employees) and REG (Full Time Employees) contributions. So all payments VTE and non-VTE were cumulated into the same cumulation wage type (i.e. /111), then the contributions applicable were not separated back out into VTE and non-VTE contributions.<br /><br />As such there was no possible way for the Contributions Interface (RPUUSSG0), by looking at the payroll results, to separate out VTE and Non-VTE contributions.<br /><br />If your payroll implementation does separate out VTE and Non-VTE contributions then please follow the \"Complete Solution\" below.<br /><br />If your payroll implementation does not separate out VTE and Non-VTE contributions then all you can do is highlight (during the processing of the Contributions Interface (RPUUSSG0)) employees that are receiving VTE payments. After which, you will have to manually alter the entries that are sent to USS for those employees. If this is applicable to your payroll implementation then please follow the \"Manual Solution\" below.<br /><br /><br />Complete Solution<br />-----------------<br />Customers<br />USC4 USS CARE VTE Non-Sal Sac<br />USC5 USS CARE VTE Salary Sac<br />USC6 USS CARE VTE AVCS<br /><br />Manual Solution<br />---------------<br />USC7 USS CARE VTE Payments<br /><br />Processing Changes<br />==================<br /><br />CARE Schemes<br />------------<br /><br />The USS Contributions Interface (RPUUSSG0) has been amended.<br /><br />There is now a specific choice that the user has to make, CARE or not CARE. If the CARE option is chosen then the \"Current Pension Month\" must be chosen as well.<br /><br />Employees that have not joined a CARE scheme do not need to have their Contributions reported on a monthly basis. They are rejected from the Contributions report with the following message.<br /><br />\"Employee Omitted (not in an active USS CARE Scheme between &amp;1 and &amp;2)\"<br /><br />The USS Salary Changes Interface (RPUUSSG1) has been amended.<br /><br />Employees that have joined a CARE scheme do not need to have their Salary Changes reported. They are rejected from the Salary Changes report with the following message.<br /><br />\"Employee Omitted (in an active USS CARE Scheme on &amp;1)\"<br /><br />Non-CARE Schemes<br />----------------<br /><br />The USS Contributions Interface (RPUUSSG0) has been amended.<br /><br />There is now a specific choice that the user has to make, CARE or not CARE. If the Non-CARE option is chosen then there are no changes from the old processing. The processing only processes employees that are in a Non-CARE scheme. If they are in a CARE scheme they will be rejected with the following message.<br /><br />\"Employee Omitted (not in an active USS Scheme between &amp;1 and &amp;2)\"<br /><br />The USS Salary Changes Interface (RPUUSSG1) has not been changed for  Non-Care Schemes and they will be processed as they are currently.</p> <b></b><br /> <b><U>To Apply the changes manually follow the steps given below:</U></b><br /> <p><br />The details of the SAR files attached with the note are as below:<br /><br />Release 600:<br />WB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; L7D.zip<br /><br />Apply the workbench SAR file attached with this note first.Also note that after applying the SAR files, the correction instructions<br />attached with the note must be applied.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Page 2<br />Note : The correction instructions attached must be applied only after the application of the SAR files.<br /><br />Important Information : The attached SAR files can be downloaded as<br />described in Notes 480180, 212876 and 13719. The files are SAPCAR files.The software to download these files can be found at SAP service<br />marketplace<br /><br />Risk and Restrictions inherent in Transport Files<br />=================================================<br /><br />If you use a Transport (SAR) file instead of installing the appropriate<br />Support Package or CLC Package, please note the following:<br /><br /> 1) Read carefully SAP Note 1318389, where conditions and risks of &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;using Transport files are explained in detail.<br /><br /> 2) There are no updates to Transport files when any object in them are<br /> modified. Objects contained in Transport files may become obsolete<br /> without warning.<br /><br /> 3) Transport files are not valid once their content is available via<br /> Support Packages or CLC Packages. The changes may then be installed &#x00A0;&#x00A0;&#x00A0;&#x00A0;only via the Packages.<br /><br /> 4) Text objects are provided in the language in which they were &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;created.Translation is available only via the Packages.<br /><br /> 5) Changes to the SAP Easy Access menu and Implementation Guide<br /> (IMG) are provided only via the Packages.<br /><br /> 6) When you apply more than one Transport file, the order of<br /> implementation must be followed as indicated. A wrong sequence will<br /> cause transports to fail.<br /><br /> 7) Once a Transport file has been installed, future installations of<br /> Support Packages (or CLC Packages for the HR components modified by &#x00A0;&#x00A0;&#x00A0;&#x00A0;the Transport file) must include the Packages that delivered the &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; changes contained in the Transport file. Otherwise objects may be &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;replaced by older versions.<br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I031975)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I031975)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001645112/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "USS_Manual_steps.pdf", "FileSize": "943", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000468112011&iv_version=0005&iv_guid=DD756B3527862040957C2331BBBA20BE"}, {"FileName": "USS604.SAR", "FileSize": "145", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000468112011&iv_version=0005&iv_guid=76AE4B714BE85441ADEBF243029C92D6"}, {"FileName": "L7DK183733.SAR", "FileSize": "59", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000468112011&iv_version=0005&iv_guid=C84A94607E1F3642AACE25B86D8E9D3E"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1711369", "RefComponent": "PY-GB-PS", "RefTitle": "PY-GB-PS: USS Final Salary Schemes: Monthly Reporting", "RefUrl": "/notes/1711369"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1711369", "RefComponent": "PY-GB-PS", "RefTitle": "PY-GB-PS: USS Final Salary Schemes: Monthly Reporting", "RefUrl": "/notes/1711369 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HRCGB", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCGB", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCGB", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCGB", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HRCGB 470", "SupportPackage": "SAPK-470D3INSAPHRCGB", "URL": "/supportpackage/SAPK-470D3INSAPHRCGB"}, {"SoftwareComponentVersion": "SAP_HRCGB 500", "SupportPackage": "SAPK-50099INSAPHRCGB", "URL": "/supportpackage/SAPK-50099INSAPHRCGB"}, {"SoftwareComponentVersion": "SAP_HRCGB 600", "SupportPackage": "SAPK-60082INSAPHRCGB", "URL": "/supportpackage/SAPK-60082INSAPHRCGB"}, {"SoftwareComponentVersion": "SAP_HRCGB 604", "SupportPackage": "SAPK-60448INSAPHRCGB", "URL": "/supportpackage/SAPK-60448INSAPHRCGB"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HRCGB", "NumberOfCorrin": 4, "URL": "/corrins/0001645112/5360"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HRCGB", "ValidFrom": "604", "ValidTo": "604", "Number": "1431379 ", "URL": "/notes/1431379 ", "Title": "USS Interface File Format Changes for April 2010", "Component": "PY-GB-PS"}, {"SoftwareComponent": "SAP_HRCGB", "ValidFrom": "604", "ValidTo": "604", "Number": "1516365 ", "URL": "/notes/1516365 ", "Title": "HRGB: Quality improvements to GB Reports", "Component": "PY-GB"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}