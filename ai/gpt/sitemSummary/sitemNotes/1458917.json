{"Request": {"Number": "1458917", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 579, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017014172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001458917?language=E&token=AFA9E3780E64DD82234CA425A4943CE3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001458917", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001458917/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1458917"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.05.2014"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1458917 - Composite SAP Note for archiving"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note references notes that were created in the context of archiving in RE-FX.<br/><br/></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SARA REFX_AO REFX_AT REFX_BE REFX_BU REFX_CG REFX_CN REFX_CNCFREFX_JL REFX_LR REFX_MP REFX_NA REFX_OF REFX_OO REFX_OR REFX_PE REFX_PG REFX_PL REFX_PR REFX_RADOC REFX_RC REFX_RO REFX_ROCF REFX_RR REFX_SCSE REFX_SU REFX_TC ARCH ARCHIV</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For further information, see the &quot;Reference to related Notes&quot; section.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D035027)"}, {"Key": "Processor                                                                                           ", "Value": "D030839"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001458917/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001458917/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "2472716", "RefComponent": "RE-FX-SC", "RefTitle": "Service charge settlement: Deletion flag in the lease-out", "RefUrl": "/notes/2472716"}, {"RefNumber": "2367610", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Check when you set the deletion indicator", "RefUrl": "/notes/2367610"}, {"RefNumber": "2319840", "RefComponent": "RE-FX", "RefTitle": "ARCH: Archive information structures for cash flow II", "RefUrl": "/notes/2319840"}, {"RefNumber": "2187624", "RefComponent": "RE-FX-SC", "RefTitle": "Archiving object REFX_SCSE is incomplete", "RefUrl": "/notes/2187624"}, {"RefNumber": "2155875", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2155875"}, {"RefNumber": "2133003", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Protocol of the write phase 2", "RefUrl": "/notes/2133003"}, {"RefNumber": "2109820", "RefComponent": "RE-FX-RS", "RefTitle": "Archiving object REFX_RS: ILM selection text", "RefUrl": "/notes/2109820"}, {"RefNumber": "2050075", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Contract object in real estate contracts", "RefUrl": "/notes/2050075"}, {"RefNumber": "2032053", "RefComponent": "RE-FX-SR", "RefTitle": "Archiving object REFX_CN: Archiving of sales", "RefUrl": "/notes/2032053"}, {"RefNumber": "2023067", "RefComponent": "RE-FX-CN", "RefTitle": "Archiving object REFX_CN: Advance payment archiving - legacy data transfer", "RefUrl": "/notes/2023067"}, {"RefNumber": "2018591", "RefComponent": "RE-FX-RA", "RefTitle": "Archiving object REFX_CN: Archiving of invoice", "RefUrl": "/notes/2018591"}, {"RefNumber": "2008505", "RefComponent": "RE-FX-RS", "RefTitle": "REFX_RS: It is possible to change reservations with deletion indicator set", "RefUrl": "/notes/2008505"}, {"RefNumber": "1986975", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - Archiving Object REFX_AT", "RefUrl": "/notes/1986975"}, {"RefNumber": "1970906", "RefComponent": "RE-FX-LM", "RefTitle": "Archiving of joint liability: Additional texts", "RefUrl": "/notes/1970906"}, {"RefNumber": "1958155", "RefComponent": "RE-FX-BD", "RefTitle": "REFX_CN: New condtion and time-reference fields for the ILM policies", "RefUrl": "/notes/1958155"}, {"RefNumber": "1885663", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Check for usage in settlement rules", "RefUrl": "/notes/1885663"}, {"RefNumber": "1866492", "RefComponent": "RE-FX-RS", "RefTitle": "Reservation: Delete withouth archiving needed", "RefUrl": "/notes/1866492"}, {"RefNumber": "1856793", "RefComponent": "RE-FX-IT", "RefTitle": "Documentation for archiving object REFX_OR", "RefUrl": "/notes/1856793"}, {"RefNumber": "1833182", "RefComponent": "RE-FX-RS", "RefTitle": "ARCH: Reservations and deletion indicator", "RefUrl": "/notes/1833182"}, {"RefNumber": "1826563", "RefComponent": "RE-FX-RS", "RefTitle": "Archive: REFX_RS reservation", "RefUrl": "/notes/1826563"}, {"RefNumber": "1825224", "RefComponent": "RE-FX-SC", "RefTitle": "Deletion of settlement units II", "RefUrl": "/notes/1825224"}, {"RefNumber": "1825034", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: Multiple archived FI documents", "RefUrl": "/notes/1825034"}, {"RefNumber": "1813298", "RefComponent": "RE-FX-SC", "RefTitle": "Deletion of settlement units", "RefUrl": "/notes/1813298"}, {"RefNumber": "1812276", "RefComponent": "RE-FX-CF", "RefTitle": "Cash flow archiving: Residence time 1", "RefUrl": "/notes/1812276"}, {"RefNumber": "1810590", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Residence time 1 is ignored", "RefUrl": "/notes/1810590"}, {"RefNumber": "1796329", "RefComponent": "RE-FX-AJ", "RefTitle": "CEA: Archiving of prestage adjustment records - level 3", "RefUrl": "/notes/1796329"}, {"RefNumber": "1792444", "RefComponent": "RE-FX-AJ", "RefTitle": "WiBe: Archivierung von Vorstufenanpassungen - Stufe 2", "RefUrl": "/notes/1792444"}, {"RefNumber": "1784683", "RefComponent": "RE-FX-BD", "RefTitle": "ARCH: Deletion indicator set instead of residence time 1", "RefUrl": "/notes/1784683"}, {"RefNumber": "1784337", "RefComponent": "RE-FX-AJ", "RefTitle": "Comparative group of apartments w/ DLFL in adjustment term", "RefUrl": "/notes/1784337"}, {"RefNumber": "1703087", "RefComponent": "RE-FX", "RefTitle": "ARCH: Enabling ILM for RE-FX archiving objects", "RefUrl": "/notes/1703087"}, {"RefNumber": "1701225", "RefComponent": "RE-FX-CF", "RefTitle": "Process ID for archived RE documents", "RefUrl": "/notes/1701225"}, {"RefNumber": "1673748", "RefComponent": "RE-FX-OR", "RefTitle": "Error REBDOA 031 after contract archived", "RefUrl": "/notes/1673748"}, {"RefNumber": "1654259", "RefComponent": "RE-FX-BD", "RefTitle": "Possible to set deletion flag although still used", "RefUrl": "/notes/1654259"}, {"RefNumber": "1639187", "RefComponent": "RE-FX-CF", "RefTitle": "Conditions: Object from calculation/distribution formula", "RefUrl": "/notes/1639187"}, {"RefNumber": "1604550", "RefComponent": "RE-FX-SC", "RefTitle": "Deleting rental spaces despite use in settlement", "RefUrl": "/notes/1604550"}, {"RefNumber": "1599941", "RefComponent": "RE-FX-IT", "RefTitle": "REITTC: BO without correction object cannot be deleted", "RefUrl": "/notes/1599941"}, {"RefNumber": "1569413", "RefComponent": "RE-FX-RA", "RefTitle": "Archive: Err log overflows during prep. program for RE docs", "RefUrl": "/notes/1569413"}, {"RefNumber": "1569111", "RefComponent": "RE-FX-BD", "RefTitle": "Business entity: Sequence of archiving", "RefUrl": "/notes/1569111"}, {"RefNumber": "1568878", "RefComponent": "RE-FX-BD", "RefTitle": "Business entity: Setting the deletion indicator", "RefUrl": "/notes/1568878"}, {"RefNumber": "1566349", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving the rental object: Infrastructure and adjustment", "RefUrl": "/notes/1566349"}, {"RefNumber": "1565061", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving architecture: Infrstrctre and fixtures and fittngs", "RefUrl": "/notes/1565061"}, {"RefNumber": "1564585", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving the land: Infrastructure and hierarchical location", "RefUrl": "/notes/1564585"}, {"RefNumber": "1564417", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving the business entity: Infrastructure", "RefUrl": "/notes/1564417"}, {"RefNumber": "1561167", "RefComponent": "RE-FX-BD", "RefTitle": "Deletion flag for rental space and pooled space", "RefUrl": "/notes/1561167"}, {"RefNumber": "1556262", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Architecture/usage relationship", "RefUrl": "/notes/1556262"}, {"RefNumber": "1551605", "RefComponent": "RE-FX-BD", "RefTitle": "Archived usage objects: CO settlement rule", "RefUrl": "/notes/1551605"}, {"RefNumber": "1551358", "RefComponent": "RE-FX-BD", "RefTitle": "Archived usage objects: Termination on \"Architecture\" tab", "RefUrl": "/notes/1551358"}, {"RefNumber": "1550836", "RefComponent": "RE-FX-CN", "RefTitle": "Archiving of contracts: Note 1548853 and PSCD", "RefUrl": "/notes/1550836"}, {"RefNumber": "1550835", "RefComponent": "RE-FX-CN", "RefTitle": "Archiving contracts: Note 1548853 and PSCD", "RefUrl": "/notes/1550835"}, {"RefNumber": "1550497", "RefComponent": "RE-FX-BD", "RefTitle": "PS-RS relationship no longer dsiplayed after archiving", "RefUrl": "/notes/1550497"}, {"RefNumber": "1550443", "RefComponent": "RE-FX-BD", "RefTitle": "Termination in AO-UO assignment after archiving", "RefUrl": "/notes/1550443"}, {"RefNumber": "1549613", "RefComponent": "RE-FX-RA", "RefTitle": "Accrual/deferral and deletion flag: Deactivated contracts", "RefUrl": "/notes/1549613"}, {"RefNumber": "1548853", "RefComponent": "RE-FX", "RefTitle": "Archiving contracts: Check for open items", "RefUrl": "/notes/1548853"}, {"RefNumber": "1547551", "RefComponent": "RE-FX-RA", "RefTitle": "Displaying documents with assignment to archived object", "RefUrl": "/notes/1547551"}, {"RefNumber": "1522848", "RefComponent": "RE-FX-CF", "RefTitle": "Incorrect cash flow after cash flow archiving", "RefUrl": "/notes/1522848"}, {"RefNumber": "1510569", "RefComponent": "RE-FX-RA", "RefTitle": "Accruals/deferrals and DLFL: Case with accruals only", "RefUrl": "/notes/1510569"}, {"RefNumber": "1506141", "RefComponent": "RE-FX-AJ", "RefTitle": "Contract with incomplete adjustment can be archived", "RefUrl": "/notes/1506141"}, {"RefNumber": "1503676", "RefComponent": "RE-FX-MI", "RefTitle": "Archiving: Deleting \"RE classic\" data under RE-FX", "RefUrl": "/notes/1503676"}, {"RefNumber": "1503487", "RefComponent": "RE-FX-CF", "RefTitle": "Archiving cash flows: Deletion indicator despite accr./def.", "RefUrl": "/notes/1503487"}, {"RefNumber": "1502649", "RefComponent": "RE-FX-OR", "RefTitle": "Contract offer: Mark for deletion missinng in context menu", "RefUrl": "/notes/1502649"}, {"RefNumber": "1501365", "RefComponent": "RE-FX-SC", "RefTitle": "RE objects with deletion flag as remainder object", "RefUrl": "/notes/1501365"}, {"RefNumber": "1501269", "RefComponent": "RE-FX-CN", "RefTitle": "Deletion flag can be set despite planned records", "RefUrl": "/notes/1501269"}, {"RefNumber": "1501199", "RefComponent": "RE-BD", "RefTitle": "Archiving: Deleting \"Classic RE\" data in RE-FX", "RefUrl": "/notes/1501199"}, {"RefNumber": "1500682", "RefComponent": "RE-FX-CF", "RefTitle": "No cash flows displayed after archiving", "RefUrl": "/notes/1500682"}, {"RefNumber": "1499940", "RefComponent": "RE-FX-OR", "RefTitle": "ARCHIVING: Archiving the contract offer", "RefUrl": "/notes/1499940"}, {"RefNumber": "1499936", "RefComponent": "RE-FX", "RefTitle": "ARCHIVING: Archiving object REFX_* - new restrictions", "RefUrl": "/notes/1499936"}, {"RefNumber": "1497962", "RefComponent": "RE-RT-RA", "RefTitle": "G/L account line items: Archive access and performance", "RefUrl": "/notes/1497962"}, {"RefNumber": "1489201", "RefComponent": "RE-FX-CF", "RefTitle": "Not possible to select deletion indicator for cash flow", "RefUrl": "/notes/1489201"}, {"RefNumber": "1487943", "RefComponent": "RE-FX-RA", "RefTitle": "Archiving of RE documents does not check FI-CA documents", "RefUrl": "/notes/1487943"}, {"RefNumber": "1487869", "RefComponent": "RE-FX-AJ", "RefTitle": "Objects w/ deletion flag or deletion indicator in adjustment", "RefUrl": "/notes/1487869"}, {"RefNumber": "1486678", "RefComponent": "RE-FX-SC", "RefTitle": "Error in SCS after settlement unit is deleted", "RefUrl": "/notes/1486678"}, {"RefNumber": "1485147", "RefComponent": "RE-FX", "RefTitle": "Display of archived objects: No status object", "RefUrl": "/notes/1485147"}, {"RefNumber": "1484574", "RefComponent": "RE-FX-OR", "RefTitle": "Deleting request/offer: Change documents are not deleted", "RefUrl": "/notes/1484574"}, {"RefNumber": "1483949", "RefComponent": "RE-FX-CN", "RefTitle": "Reading contracts from archive: Database buffer incorrect", "RefUrl": "/notes/1483949"}, {"RefNumber": "1483315", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Incomplete usage check", "RefUrl": "/notes/1483315"}, {"RefNumber": "1468368", "RefComponent": "RE-FX-AJ", "RefTitle": "Archiving CGA if there are only reversed adjustments", "RefUrl": "/notes/1468368"}, {"RefNumber": "1467563", "RefComponent": "RE-FX-AJ", "RefTitle": "Using an archived comparative group of apartments", "RefUrl": "/notes/1467563"}, {"RefNumber": "1466909", "RefComponent": "RE-FX-RA", "RefTitle": "RERAPPRV: No error message for archived FI document", "RefUrl": "/notes/1466909"}, {"RefNumber": "1466506", "RefComponent": "RE-FX-CN", "RefTitle": "Display of archived cash flow records", "RefUrl": "/notes/1466506"}, {"RefNumber": "1466417", "RefComponent": "RE-FX-RA", "RefTitle": "RE document display: Document not indicated as archived", "RefUrl": "/notes/1466417"}, {"RefNumber": "1466272", "RefComponent": "RE-FX-RA", "RefTitle": "Document display: Displaying RE document after archiving", "RefUrl": "/notes/1466272"}, {"RefNumber": "1462301", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Structure definition REFX_AO is incorrect", "RefUrl": "/notes/1462301"}, {"RefNumber": "1459684", "RefComponent": "RE-FX-IS", "RefTitle": "IS: Cash Flow - Archived and Splitcondition Positions", "RefUrl": "/notes/1459684"}, {"RefNumber": "1457074", "RefComponent": "RE-FX", "RefTitle": "RE-FX archiving: Assigned objects with deletion indicator", "RefUrl": "/notes/1457074"}, {"RefNumber": "1450336", "RefComponent": "RE-FX-SC", "RefTitle": "RESCSU: Setting deletion flag in change mode", "RefUrl": "/notes/1450336"}, {"RefNumber": "1410080", "RefComponent": "RE-FX-BD", "RefTitle": "Error for DLFL for contract and dependent sec deposit agrmt", "RefUrl": "/notes/1410080"}, {"RefNumber": "1394016", "RefComponent": "RE-FX-RA", "RefTitle": "RECN: Accrual/deferral and deletion flag", "RefUrl": "/notes/1394016"}, {"RefNumber": "1387349", "RefComponent": "RE-FX-SC", "RefTitle": "SU:Locked cost collector w/out postings:Deletn flag possible", "RefUrl": "/notes/1387349"}, {"RefNumber": "1368976", "RefComponent": "RE-FX-BD", "RefTitle": "Error in connection with message RECAAR 042", "RefUrl": "/notes/1368976"}, {"RefNumber": "1355312", "RefComponent": "RE-FX-SR", "RefTitle": "Sales-based settl after contract end and w/ deletion flag", "RefUrl": "/notes/1355312"}, {"RefNumber": "1340042", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Incomplete error message", "RefUrl": "/notes/1340042"}, {"RefNumber": "1327297", "RefComponent": "RE-FX-SC", "RefTitle": "RESCSEAL: Acr/def of service charges for SU w/ del flag sts", "RefUrl": "/notes/1327297"}, {"RefNumber": "1309902", "RefComponent": "RE-FX-MM", "RefTitle": "REMMBUDGET: Settlement units with DLFL or DLT", "RefUrl": "/notes/1309902"}, {"RefNumber": "1294660", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving technical locations", "RefUrl": "/notes/1294660"}, {"RefNumber": "1287465", "RefComponent": "RE-FX-SC", "RefTitle": "RESCSU: Deletion flag for completed SU or MSU", "RefUrl": "/notes/1287465"}, {"RefNumber": "1287260", "RefComponent": "PM-EQM-FL", "RefTitle": "Archiving technical locations after migration", "RefUrl": "/notes/1287260"}, {"RefNumber": "1271861", "RefComponent": "RE-FX-CN", "RefTitle": "Set deletion indicator for cash flow: Object cash flow", "RefUrl": "/notes/1271861"}, {"RefNumber": "1264209", "RefComponent": "RE-FX-CN", "RefTitle": "Cash flow archiving does not set deletion indicator", "RefUrl": "/notes/1264209"}, {"RefNumber": "1263146", "RefComponent": "RE-FX-IT", "RefTitle": "RE-FX: Archiving of option rate result tables", "RefUrl": "/notes/1263146"}, {"RefNumber": "1262730", "RefComponent": "RE-FX-BD", "RefTitle": "Deleting without archiving: COBRA", "RefUrl": "/notes/1262730"}, {"RefNumber": "1262725", "RefComponent": "RE-FX", "RefTitle": "Archiving CO data", "RefUrl": "/notes/1262725"}, {"RefNumber": "1261496", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Incomplete archiving", "RefUrl": "/notes/1261496"}, {"RefNumber": "1257820", "RefComponent": "RE-FX-CN", "RefTitle": "Set deletion flag: Term of contract not yet elapsed", "RefUrl": "/notes/1257820"}, {"RefNumber": "1253871", "RefComponent": "RE-FX-AJ", "RefTitle": "Deleting rental objects", "RefUrl": "/notes/1253871"}, {"RefNumber": "1251454", "RefComponent": "RE-FX-SC", "RefTitle": "Setting deletion flag for MSU or SU", "RefUrl": "/notes/1251454"}, {"RefNumber": "1249906", "RefComponent": "RE-FX-IT", "RefTitle": "Option rate determination for objects with deletion flag", "RefUrl": "/notes/1249906"}, {"RefNumber": "1238101", "RefComponent": "RE-FX-IT", "RefTitle": "InpTxCor: Archiving correction objects", "RefUrl": "/notes/1238101"}, {"RefNumber": "1170273", "RefComponent": "RE-FX-IS", "RefTitle": "Line items SU: Archived documents not displayed completely", "RefUrl": "/notes/1170273"}, {"RefNumber": "1160481", "RefComponent": "RE-FX-CP", "RefTitle": "Archiving during invoice printout for several contracts", "RefUrl": "/notes/1160481"}, {"RefNumber": "1157966", "RefComponent": "RE-FX-AJ", "RefTitle": "Rent adjustment termination (object status)", "RefUrl": "/notes/1157966"}, {"RefNumber": "1085462", "RefComponent": "RE-FX", "RefTitle": "Archiving object REFX_CN incomplete", "RefUrl": "/notes/1085462"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2340444", "RefComponent": "RE-FX", "RefTitle": "ARCH: REFX_AO and table VIBPOBJREL", "RefUrl": "/notes/2340444 "}, {"RefNumber": "2310826", "RefComponent": "RE-FX-BD", "RefTitle": "ARCH: Archiving standard addoption", "RefUrl": "/notes/2310826 "}, {"RefNumber": "2319840", "RefComponent": "RE-FX", "RefTitle": "ARCH: Archive information structures for cash flow II", "RefUrl": "/notes/2319840 "}, {"RefNumber": "2307396", "RefComponent": "RE-FX-BD", "RefTitle": "ARCH: Archiving programs and selection screen", "RefUrl": "/notes/2307396 "}, {"RefNumber": "2309125", "RefComponent": "RE-FX-BD", "RefTitle": "ARCH: Archive information structures for cash flow", "RefUrl": "/notes/2309125 "}, {"RefNumber": "2187624", "RefComponent": "RE-FX-SC", "RefTitle": "Archiving object REFX_SCSE is incomplete", "RefUrl": "/notes/2187624 "}, {"RefNumber": "2152384", "RefComponent": "RE-FX-BD", "RefTitle": "REFX_OO: Archiving of reservation objects without reference to architectural object", "RefUrl": "/notes/2152384 "}, {"RefNumber": "2154781", "RefComponent": "RE-FX-BD", "RefTitle": "REFX_AO: Archiving of corresponding reservation objects", "RefUrl": "/notes/2154781 "}, {"RefNumber": "2175875", "RefComponent": "RE-FX-SC", "RefTitle": "SCS archiving: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED", "RefUrl": "/notes/2175875 "}, {"RefNumber": "2151290", "RefComponent": "RE-FX-BD", "RefTitle": "REFX_AO: The statistics in the _DEL program are not showin", "RefUrl": "/notes/2151290 "}, {"RefNumber": "2079661", "RefComponent": "RE-FX-OR", "RefTitle": "Move Plan: Archiving", "RefUrl": "/notes/2079661 "}, {"RefNumber": "2116654", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Protocol of the write phase", "RefUrl": "/notes/2116654 "}, {"RefNumber": "2123538", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_OR", "RefUrl": "/notes/2123538 "}, {"RefNumber": "2123448", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_ATPS", "RefUrl": "/notes/2123448 "}, {"RefNumber": "2117140", "RefComponent": "RE-FX-BD", "RefTitle": "REBDCACHECK: Termination due to archiving", "RefUrl": "/notes/2117140 "}, {"RefNumber": "2109820", "RefComponent": "RE-FX-RS", "RefTitle": "Archiving object REFX_RS: ILM selection text", "RefUrl": "/notes/2109820 "}, {"RefNumber": "2103698", "RefComponent": "RE-FX-SC", "RefTitle": "Posting log of the service charge settlement is not displayed.", "RefUrl": "/notes/2103698 "}, {"RefNumber": "2051215", "RefComponent": "RE-FX-BD", "RefTitle": "Rental object can be archived in spite of existing contract assignments", "RefUrl": "/notes/2051215 "}, {"RefNumber": "2075221", "RefComponent": "RE-FX-OR", "RefTitle": "Archiving: Archiving an RE search request", "RefUrl": "/notes/2075221 "}, {"RefNumber": "2055399", "RefComponent": "RE-FX-BD", "RefTitle": "ARCH: Further Selection Criteria for the preprocessing program of archiving object REFX_RSREC", "RefUrl": "/notes/2055399 "}, {"RefNumber": "2016914", "RefComponent": "RE-FX-BD", "RefTitle": "Archived objects: Change documents are not displayed.", "RefUrl": "/notes/2016914 "}, {"RefNumber": "2008528", "RefComponent": "RE-FX-BP", "RefTitle": "ILM enablement - REFX_RSREC", "RefUrl": "/notes/2008528 "}, {"RefNumber": "2008505", "RefComponent": "RE-FX-RS", "RefTitle": "REFX_RS: It is possible to change reservations with deletion indicator set", "RefUrl": "/notes/2008505 "}, {"RefNumber": "2008503", "RefComponent": "RE-FX-RS", "RefTitle": "Displaying archived reservations", "RefUrl": "/notes/2008503 "}, {"RefNumber": "2008470", "RefComponent": "RE-FX-BD", "RefTitle": "REFX_RSREC: New archiving object for recurring reservations", "RefUrl": "/notes/2008470 "}, {"RefNumber": "2003942", "RefComponent": "RE-FX-BD", "RefTitle": "ILM: Minor adjustment of RE ILM objects", "RefUrl": "/notes/2003942 "}, {"RefNumber": "1993849", "RefComponent": "RE-FX-BP", "RefTitle": "ILM enablement - Archiving Object REFX_CNCF", "RefUrl": "/notes/1993849 "}, {"RefNumber": "1992608", "RefComponent": "RE-FX-BP", "RefTitle": "ILM enablement - Archiving Object REFX_NA", "RefUrl": "/notes/1992608 "}, {"RefNumber": "1991542", "RefComponent": "RE-FX-BP", "RefTitle": "ILM enablement - Archiving Object REFX_LR", "RefUrl": "/notes/1991542 "}, {"RefNumber": "1987400", "RefComponent": "RE-FX-BP", "RefTitle": "ILM enablement - Archiving Object REFX_CG", "RefUrl": "/notes/1987400 "}, {"RefNumber": "1982895", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_OO", "RefUrl": "/notes/1982895 "}, {"RefNumber": "1974432", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_OF", "RefUrl": "/notes/1974432 "}, {"RefNumber": "1974396", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_BE", "RefUrl": "/notes/1974396 "}, {"RefNumber": "1973290", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_BU", "RefUrl": "/notes/1973290 "}, {"RefNumber": "1970906", "RefComponent": "RE-FX-LM", "RefTitle": "Archiving of joint liability: Additional texts", "RefUrl": "/notes/1970906 "}, {"RefNumber": "1968829", "RefComponent": "RE-FX-BD", "RefTitle": "ARCH: BADI_RECA_ARCHIVING_OBJECT extension of interface", "RefUrl": "/notes/1968829 "}, {"RefNumber": "1961725", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_AO", "RefUrl": "/notes/1961725 "}, {"RefNumber": "1961661", "RefComponent": "RE-FX-BD", "RefTitle": "ILM enablement - archiving object REFX_RO", "RefUrl": "/notes/1961661 "}, {"RefNumber": "1958155", "RefComponent": "RE-FX-BD", "RefTitle": "REFX_CN: New condtion and time-reference fields for the ILM policies", "RefUrl": "/notes/1958155 "}, {"RefNumber": "1937814", "RefComponent": "RE-FX-RA", "RefTitle": "RECN: Contract account sheet for archived documents: Special G/L indicator", "RefUrl": "/notes/1937814 "}, {"RefNumber": "1933858", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: Archived FI documents in the contract account sheet", "RefUrl": "/notes/1933858 "}, {"RefNumber": "1506141", "RefComponent": "RE-FX-AJ", "RefTitle": "Contract with incomplete adjustment can be archived", "RefUrl": "/notes/1506141 "}, {"RefNumber": "1813298", "RefComponent": "RE-FX-SC", "RefTitle": "Deletion of settlement units", "RefUrl": "/notes/1813298 "}, {"RefNumber": "1825224", "RefComponent": "RE-FX-SC", "RefTitle": "Deletion of settlement units II", "RefUrl": "/notes/1825224 "}, {"RefNumber": "1866492", "RefComponent": "RE-FX-RS", "RefTitle": "Reservation: Delete withouth archiving needed", "RefUrl": "/notes/1866492 "}, {"RefNumber": "1885663", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Check for usage in settlement rules", "RefUrl": "/notes/1885663 "}, {"RefNumber": "1796329", "RefComponent": "RE-FX-AJ", "RefTitle": "CEA: Archiving of prestage adjustment records - level 3", "RefUrl": "/notes/1796329 "}, {"RefNumber": "1856793", "RefComponent": "RE-FX-IT", "RefTitle": "Documentation for archiving object REFX_OR", "RefUrl": "/notes/1856793 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1833182", "RefComponent": "RE-FX-RS", "RefTitle": "ARCH: Reservations and deletion indicator", "RefUrl": "/notes/1833182 "}, {"RefNumber": "1487869", "RefComponent": "RE-FX-AJ", "RefTitle": "Objects w/ deletion flag or deletion indicator in adjustment", "RefUrl": "/notes/1487869 "}, {"RefNumber": "1468368", "RefComponent": "RE-FX-AJ", "RefTitle": "Archiving CGA if there are only reversed adjustments", "RefUrl": "/notes/1468368 "}, {"RefNumber": "1467563", "RefComponent": "RE-FX-AJ", "RefTitle": "Using an archived comparative group of apartments", "RefUrl": "/notes/1467563 "}, {"RefNumber": "1826563", "RefComponent": "RE-FX-RS", "RefTitle": "Archive: REFX_RS reservation", "RefUrl": "/notes/1826563 "}, {"RefNumber": "1368976", "RefComponent": "RE-FX-BD", "RefTitle": "Error in connection with message RECAAR 042", "RefUrl": "/notes/1368976 "}, {"RefNumber": "1825034", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: Multiple archived FI documents", "RefUrl": "/notes/1825034 "}, {"RefNumber": "1810590", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Residence time 1 is ignored", "RefUrl": "/notes/1810590 "}, {"RefNumber": "1812276", "RefComponent": "RE-FX-CF", "RefTitle": "Cash flow archiving: Residence time 1", "RefUrl": "/notes/1812276 "}, {"RefNumber": "1792444", "RefComponent": "RE-FX-AJ", "RefTitle": "WiBe: Archivierung von Vorstufenanpassungen - Stufe 2", "RefUrl": "/notes/1792444 "}, {"RefNumber": "1784337", "RefComponent": "RE-FX-AJ", "RefTitle": "Comparative group of apartments w/ DLFL in adjustment term", "RefUrl": "/notes/1784337 "}, {"RefNumber": "1784683", "RefComponent": "RE-FX-BD", "RefTitle": "ARCH: Deletion indicator set instead of residence time 1", "RefUrl": "/notes/1784683 "}, {"RefNumber": "1703087", "RefComponent": "RE-FX", "RefTitle": "ARCH: Enabling ILM for RE-FX archiving objects", "RefUrl": "/notes/1703087 "}, {"RefNumber": "1701225", "RefComponent": "RE-FX-CF", "RefTitle": "Process ID for archived RE documents", "RefUrl": "/notes/1701225 "}, {"RefNumber": "1654259", "RefComponent": "RE-FX-BD", "RefTitle": "Possible to set deletion flag although still used", "RefUrl": "/notes/1654259 "}, {"RefNumber": "1340042", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Incomplete error message", "RefUrl": "/notes/1340042 "}, {"RefNumber": "1483315", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Incomplete usage check", "RefUrl": "/notes/1483315 "}, {"RefNumber": "1459684", "RefComponent": "RE-FX-IS", "RefTitle": "IS: Cash Flow - Archived and Splitcondition Positions", "RefUrl": "/notes/1459684 "}, {"RefNumber": "1673748", "RefComponent": "RE-FX-OR", "RefTitle": "Error REBDOA 031 after contract archived", "RefUrl": "/notes/1673748 "}, {"RefNumber": "1639187", "RefComponent": "RE-FX-CF", "RefTitle": "Conditions: Object from calculation/distribution formula", "RefUrl": "/notes/1639187 "}, {"RefNumber": "1263146", "RefComponent": "RE-FX-IT", "RefTitle": "RE-FX: Archiving of option rate result tables", "RefUrl": "/notes/1263146 "}, {"RefNumber": "1238101", "RefComponent": "RE-FX-IT", "RefTitle": "InpTxCor: Archiving correction objects", "RefUrl": "/notes/1238101 "}, {"RefNumber": "1501199", "RefComponent": "RE-BD", "RefTitle": "Archiving: Deleting \"Classic RE\" data in RE-FX", "RefUrl": "/notes/1501199 "}, {"RefNumber": "1170273", "RefComponent": "RE-FX-IS", "RefTitle": "Line items SU: Archived documents not displayed completely", "RefUrl": "/notes/1170273 "}, {"RefNumber": "1497962", "RefComponent": "RE-RT-RA", "RefTitle": "G/L account line items: Archive access and performance", "RefUrl": "/notes/1497962 "}, {"RefNumber": "1599941", "RefComponent": "RE-FX-IT", "RefTitle": "REITTC: BO without correction object cannot be deleted", "RefUrl": "/notes/1599941 "}, {"RefNumber": "1547551", "RefComponent": "RE-FX-RA", "RefTitle": "Displaying documents with assignment to archived object", "RefUrl": "/notes/1547551 "}, {"RefNumber": "1604550", "RefComponent": "RE-FX-SC", "RefTitle": "Deleting rental spaces despite use in settlement", "RefUrl": "/notes/1604550 "}, {"RefNumber": "1262730", "RefComponent": "RE-FX-BD", "RefTitle": "Deleting without archiving: COBRA", "RefUrl": "/notes/1262730 "}, {"RefNumber": "1483949", "RefComponent": "RE-FX-CN", "RefTitle": "Reading contracts from archive: Database buffer incorrect", "RefUrl": "/notes/1483949 "}, {"RefNumber": "1499936", "RefComponent": "RE-FX", "RefTitle": "ARCHIVING: Archiving object REFX_* - new restrictions", "RefUrl": "/notes/1499936 "}, {"RefNumber": "1499940", "RefComponent": "RE-FX-OR", "RefTitle": "ARCHIVING: Archiving the contract offer", "RefUrl": "/notes/1499940 "}, {"RefNumber": "1502649", "RefComponent": "RE-FX-OR", "RefTitle": "Contract offer: Mark for deletion missinng in context menu", "RefUrl": "/notes/1502649 "}, {"RefNumber": "1503676", "RefComponent": "RE-FX-MI", "RefTitle": "Archiving: Deleting \"RE classic\" data under RE-FX", "RefUrl": "/notes/1503676 "}, {"RefNumber": "1569413", "RefComponent": "RE-FX-RA", "RefTitle": "Archive: Err log overflows during prep. program for RE docs", "RefUrl": "/notes/1569413 "}, {"RefNumber": "1569111", "RefComponent": "RE-FX-BD", "RefTitle": "Business entity: Sequence of archiving", "RefUrl": "/notes/1569111 "}, {"RefNumber": "1568878", "RefComponent": "RE-FX-BD", "RefTitle": "Business entity: Setting the deletion indicator", "RefUrl": "/notes/1568878 "}, {"RefNumber": "1565061", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving architecture: Infrstrctre and fixtures and fittngs", "RefUrl": "/notes/1565061 "}, {"RefNumber": "1566349", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving the rental object: Infrastructure and adjustment", "RefUrl": "/notes/1566349 "}, {"RefNumber": "1564585", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving the land: Infrastructure and hierarchical location", "RefUrl": "/notes/1564585 "}, {"RefNumber": "1564417", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving the business entity: Infrastructure", "RefUrl": "/notes/1564417 "}, {"RefNumber": "1561167", "RefComponent": "RE-FX-BD", "RefTitle": "Deletion flag for rental space and pooled space", "RefUrl": "/notes/1561167 "}, {"RefNumber": "1556262", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Architecture/usage relationship", "RefUrl": "/notes/1556262 "}, {"RefNumber": "1551358", "RefComponent": "RE-FX-BD", "RefTitle": "Archived usage objects: Termination on \"Architecture\" tab", "RefUrl": "/notes/1551358 "}, {"RefNumber": "1551605", "RefComponent": "RE-FX-BD", "RefTitle": "Archived usage objects: CO settlement rule", "RefUrl": "/notes/1551605 "}, {"RefNumber": "1550443", "RefComponent": "RE-FX-BD", "RefTitle": "Termination in AO-UO assignment after archiving", "RefUrl": "/notes/1550443 "}, {"RefNumber": "1550835", "RefComponent": "RE-FX-CN", "RefTitle": "Archiving contracts: Note 1548853 and PSCD", "RefUrl": "/notes/1550835 "}, {"RefNumber": "1550836", "RefComponent": "RE-FX-CN", "RefTitle": "Archiving of contracts: Note 1548853 and PSCD", "RefUrl": "/notes/1550836 "}, {"RefNumber": "1550497", "RefComponent": "RE-FX-BD", "RefTitle": "PS-RS relationship no longer dsiplayed after archiving", "RefUrl": "/notes/1550497 "}, {"RefNumber": "1486678", "RefComponent": "RE-FX-SC", "RefTitle": "Error in SCS after settlement unit is deleted", "RefUrl": "/notes/1486678 "}, {"RefNumber": "1549613", "RefComponent": "RE-FX-RA", "RefTitle": "Accrual/deferral and deletion flag: Deactivated contracts", "RefUrl": "/notes/1549613 "}, {"RefNumber": "1548853", "RefComponent": "RE-FX", "RefTitle": "Archiving contracts: Check for open items", "RefUrl": "/notes/1548853 "}, {"RefNumber": "1503487", "RefComponent": "RE-FX-CF", "RefTitle": "Archiving cash flows: Deletion indicator despite accr./def.", "RefUrl": "/notes/1503487 "}, {"RefNumber": "1410080", "RefComponent": "RE-FX-BD", "RefTitle": "Error for DLFL for contract and dependent sec deposit agrmt", "RefUrl": "/notes/1410080 "}, {"RefNumber": "1522848", "RefComponent": "RE-FX-CF", "RefTitle": "Incorrect cash flow after cash flow archiving", "RefUrl": "/notes/1522848 "}, {"RefNumber": "1510569", "RefComponent": "RE-FX-RA", "RefTitle": "Accruals/deferrals and DLFL: Case with accruals only", "RefUrl": "/notes/1510569 "}, {"RefNumber": "1261496", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Incomplete archiving", "RefUrl": "/notes/1261496 "}, {"RefNumber": "1253871", "RefComponent": "RE-FX-AJ", "RefTitle": "Deleting rental objects", "RefUrl": "/notes/1253871 "}, {"RefNumber": "1501365", "RefComponent": "RE-FX-SC", "RefTitle": "RE objects with deletion flag as remainder object", "RefUrl": "/notes/1501365 "}, {"RefNumber": "1500682", "RefComponent": "RE-FX-CF", "RefTitle": "No cash flows displayed after archiving", "RefUrl": "/notes/1500682 "}, {"RefNumber": "1501269", "RefComponent": "RE-FX-CN", "RefTitle": "Deletion flag can be set despite planned records", "RefUrl": "/notes/1501269 "}, {"RefNumber": "1489201", "RefComponent": "RE-FX-CF", "RefTitle": "Not possible to select deletion indicator for cash flow", "RefUrl": "/notes/1489201 "}, {"RefNumber": "1487943", "RefComponent": "RE-FX-RA", "RefTitle": "Archiving of RE documents does not check FI-CA documents", "RefUrl": "/notes/1487943 "}, {"RefNumber": "1485147", "RefComponent": "RE-FX", "RefTitle": "Display of archived objects: No status object", "RefUrl": "/notes/1485147 "}, {"RefNumber": "1484574", "RefComponent": "RE-FX-OR", "RefTitle": "Deleting request/offer: Change documents are not deleted", "RefUrl": "/notes/1484574 "}, {"RefNumber": "1466506", "RefComponent": "RE-FX-CN", "RefTitle": "Display of archived cash flow records", "RefUrl": "/notes/1466506 "}, {"RefNumber": "1466909", "RefComponent": "RE-FX-RA", "RefTitle": "RERAPPRV: No error message for archived FI document", "RefUrl": "/notes/1466909 "}, {"RefNumber": "1466272", "RefComponent": "RE-FX-RA", "RefTitle": "Document display: Displaying RE document after archiving", "RefUrl": "/notes/1466272 "}, {"RefNumber": "1466417", "RefComponent": "RE-FX-RA", "RefTitle": "RE document display: Document not indicated as archived", "RefUrl": "/notes/1466417 "}, {"RefNumber": "1462301", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving: Structure definition REFX_AO is incorrect", "RefUrl": "/notes/1462301 "}, {"RefNumber": "1457074", "RefComponent": "RE-FX", "RefTitle": "RE-FX archiving: Assigned objects with deletion indicator", "RefUrl": "/notes/1457074 "}, {"RefNumber": "1085462", "RefComponent": "RE-FX", "RefTitle": "Archiving object REFX_CN incomplete", "RefUrl": "/notes/1085462 "}, {"RefNumber": "1450336", "RefComponent": "RE-FX-SC", "RefTitle": "RESCSU: Setting deletion flag in change mode", "RefUrl": "/notes/1450336 "}, {"RefNumber": "1251454", "RefComponent": "RE-FX-SC", "RefTitle": "Setting deletion flag for MSU or SU", "RefUrl": "/notes/1251454 "}, {"RefNumber": "1394016", "RefComponent": "RE-FX-RA", "RefTitle": "RECN: Accrual/deferral and deletion flag", "RefUrl": "/notes/1394016 "}, {"RefNumber": "1387349", "RefComponent": "RE-FX-SC", "RefTitle": "SU:Locked cost collector w/out postings:Deletn flag possible", "RefUrl": "/notes/1387349 "}, {"RefNumber": "1355312", "RefComponent": "RE-FX-SR", "RefTitle": "Sales-based settl after contract end and w/ deletion flag", "RefUrl": "/notes/1355312 "}, {"RefNumber": "1327297", "RefComponent": "RE-FX-SC", "RefTitle": "RESCSEAL: Acr/def of service charges for SU w/ del flag sts", "RefUrl": "/notes/1327297 "}, {"RefNumber": "1309902", "RefComponent": "RE-FX-MM", "RefTitle": "REMMBUDGET: Settlement units with DLFL or DLT", "RefUrl": "/notes/1309902 "}, {"RefNumber": "1294660", "RefComponent": "RE-FX-BD", "RefTitle": "Archiving technical locations", "RefUrl": "/notes/1294660 "}, {"RefNumber": "1287465", "RefComponent": "RE-FX-SC", "RefTitle": "RESCSU: Deletion flag for completed SU or MSU", "RefUrl": "/notes/1287465 "}, {"RefNumber": "1287260", "RefComponent": "PM-EQM-FL", "RefTitle": "Archiving technical locations after migration", "RefUrl": "/notes/1287260 "}, {"RefNumber": "1271861", "RefComponent": "RE-FX-CN", "RefTitle": "Set deletion indicator for cash flow: Object cash flow", "RefUrl": "/notes/1271861 "}, {"RefNumber": "1264209", "RefComponent": "RE-FX-CN", "RefTitle": "Cash flow archiving does not set deletion indicator", "RefUrl": "/notes/1264209 "}, {"RefNumber": "1262725", "RefComponent": "RE-FX", "RefTitle": "Archiving CO data", "RefUrl": "/notes/1262725 "}, {"RefNumber": "1257820", "RefComponent": "RE-FX-CN", "RefTitle": "Set deletion flag: Term of contract not yet elapsed", "RefUrl": "/notes/1257820 "}, {"RefNumber": "1249906", "RefComponent": "RE-FX-IT", "RefTitle": "Option rate determination for objects with deletion flag", "RefUrl": "/notes/1249906 "}, {"RefNumber": "1157966", "RefComponent": "RE-FX-AJ", "RefTitle": "Rent adjustment termination (object status)", "RefUrl": "/notes/1157966 "}, {"RefNumber": "1160481", "RefComponent": "RE-FX-CP", "RefTitle": "Archiving during invoice printout for several contracts", "RefUrl": "/notes/1160481 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}