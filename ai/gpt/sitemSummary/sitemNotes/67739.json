{"Request": {"Number": "67739", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 156, "Error": {}, "SAPNote": {"_type": "00200720420000000131", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000067739?language=E&token=6EB2692BD72162967AFC726510D9988C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000067739", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000067739/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "67739"}, "Type": {"_label": "Type", "value": "SAP Standard Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Info about Customer Support workflow"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use note 1433157 for finding the right component"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "67739 - Priority of problem cases"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Long Text\">Long Text</h3>\r\n<p><strong>Symptom</strong></p>\r\n<p>The definition of priorities in SAP problem cases is not clear.</p>\r\n\r\n<p><strong>Solution</strong></p>\r\n<p>SAP has defined the priorities for problem cases as follows:</p>\r\n<p>1. <strong>Very high:</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A&#160;case should be categorized with the priority \"very high\" if the problem has very serious consequences for normal business processes or IT processes related to core business processes.&#160;Urgent work cannot be performed.&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This is generally caused by the following circumstances:<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- A productive system is completely down.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- The imminent system go-live of a production system&#160;or upgrade of a production system can't be completed.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- The customer's core business processes are seriously affected.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- And for each circumstance a workaround is not available.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The&#160;case requires immediate processing because the malfunction may cause serious losses.<br />&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In case of a go-live or upgrade, the reason to delay the go-live or upgrade must be one that would cause serious losses if not resolved before go-live.</p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160;A&#160;case can be classified with priority &#8216;very high&#8217; in case of severe security vulnerability or cyber attack or in case of an event relevant for a data notification breach event.</p>\r\n<p>2. <strong>High:</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A&#160;case should be categorized with the priority \"high\" if normal business processes are seriously affected. Necessary tasks cannot be performed. This is caused by incorrect or inoperable functions in the SAP system that are required immediately. For example; users cannot access the system, a go-live cannot be completed, users complete data is not accessible, etc.&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The&#160;case is to be processed as quickly as possible because a continuing malfunction can seriously disrupt the entire productive business flow.</p>\r\n<p>3. <strong>Medium:</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A&#160;case should be categorized with the priority \"medium\" if normal business processes are affected. The problem is caused by incorrect or inoperable functions in the SAP system.</p>\r\n<p>4. <strong>Low:</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A&#160;case should be categorized with the priority \"low\" if the problem has little or no effect on normal business processes. The problem is caused by incorrect or inoperable functions in the SAP system that are not required daily, or are rarely used.</p>\r\n<p>&#160;</p>\r\n<p><strong>What the customer must do to ensure prompt processing of cases with the priority \"very high\":</strong></p>\r\n<ul style=\"list-style-type: circle;\">\r\n<li>Remote access to the relevant system must be ensured.</li>\r\n<li>A contact person must be designated for opening the system who must be<br />- <strong>available</strong><br />- and can provide the required logon data.</li>\r\n<li>A contact person must be available to provide information about the problem.</li>\r\n<li>The contact person should be reachable under the provided phone number.</li>\r\n<li>The problem should be described in as much detail as possible: The&#160;case should contain instructions about how to simulate the problem.</li>\r\n<li>To ensure 24/7 processing, the&#160;case must be written in English.</li>\r\n</ul>\r\n<p>&#160;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021740)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067739/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "998028", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/998028"}, {"RefNumber": "90835", "RefComponent": "XX-SER-SAPSMP-CON", "RefTitle": "SAP Case Escalation Procedure", "RefUrl": "/notes/90835"}, {"RefNumber": "628183", "RefComponent": "BC-SEC", "RefTitle": "Top 33 general notes", "RefUrl": "/notes/628183"}, {"RefNumber": "361732", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361732"}, {"RefNumber": "350813", "RefComponent": "PY-IT", "RefTitle": "Priority of your problem has been changed - PY-IT / PA-PA-IT", "RefUrl": "/notes/350813"}, {"RefNumber": "307037", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/307037"}, {"RefNumber": "1364684", "RefComponent": "XX-PART-PMM", "RefTitle": "Vendavo: Distribution Process SAP Price & Margin Management", "RefUrl": "/notes/1364684"}, {"RefNumber": "1337181", "RefComponent": "XX-SER-CS-CCW", "RefTitle": "SLO: Availability of CCW Development Support", "RefUrl": "/notes/1337181"}, {"RefNumber": "1320335", "RefComponent": "CA-LT-CNV", "RefTitle": "SLO: Availability of LT Development Support", "RefUrl": "/notes/1320335"}, {"RefNumber": "46962", "RefComponent": "XX-SER-GEN", "RefTitle": "More information required", "RefUrl": "/notes/46962"}, {"RefNumber": "19500", "RefComponent": "XX-SER-GEN", "RefTitle": "The priority of your request has been changed", "RefUrl": "/notes/19500"}, {"RefNumber": "16018", "RefComponent": "XX-SER-GEN", "RefTitle": "More information required on reported incident", "RefUrl": "/notes/16018"}, {"RefNumber": "1281633", "RefComponent": "XX-SER-GEN", "RefTitle": "Speed Up Processing of a Case", "RefUrl": "/notes/1281633"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}