{"Request": {"Number": "1138507", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 886, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006849672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001138507?language=E&token=D1E1B12D96ADBD4AEC73167C8EF152B4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001138507", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001138507/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1138507"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 43}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-CDP-010-R3"}, "SAPComponentKeyText": {"_label": "Component", "value": "Multi-Resource Scheduling R/3"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Internal ONLY for CDP projects (see note 689050)", "value": "XX-PROJ-CDP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Custom Development Project 010", "value": "XX-PROJ-CDP-010", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP-010*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Multi-Resource Scheduling R/3", "value": "XX-PROJ-CDP-010-R3", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP-010-R3*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1138507 - Integrate MRS Qualifications with PM/CS, MM and Equipments"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Integrate the MRS qualifications and profiles with PM/CS, MM and Equipment master modules</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MRSS, Qualifications, Profiles, PM, CS, Material master, Equipment master</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Integrate the MRS qualifications and profiles with PM/CS, MM and Equipment master modules.</p>\r\n<p><strong>If you are in EHP8 SP11 version or if you have implemented SAP Note 2637577:</strong></p>\r\n<p>You should also implement SAP Note 2745163. Otherwise you may get a syntax error.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Please download the latest version of this note before proceeding.<br /><br />Follow the below mentioned manual steps and then apply the automatic corrections.<br /><br /><br /><br /><strong>1. Enhancements to PM/CS</strong><br /><br /><strong>Steps 1.1 and 1.2 are not valid for MRS 8.0 upwards</strong><br /><br />1.1 Create a customer include CI_AUFK (if not existing already) with a dummy field DUMMY_AUFK and append structure /MRSS/T_ORDER_H to it.<br /><br />1.2 Create a customer include CI_AFVU (if not existing already) with a dummy field DUMMY_AFVU and append structure /MRSS/T_OPERATION_H to it.<br /><br />1.3 The screen sequence for program SAPLCOIH has to be customized. Launch the transaction VFBS. Enter program name as SAPLCOIH.<br /><br /><span style=\"text-decoration: underline;\">a) Function Codes</span><br /><br />Select the radio button \"Function codes for T185F'. Go to change mode and add the following entries:<br />-&gt; Maintain 4 new entries with FCode : ZUS1, ZUS2, ZUS3, ZUS4.<br />Refer to the screen shot 1 (in the attached word document) to see&#160;&#160;&#160;&#160;the example entries.<br /><br /><span style=\"text-decoration: underline;\">b) Paths between process locations</span><br /><br />Select the radio button \"Paths between process locations for T185'. Go to change mode and add the following entries:<br />-&gt; Maintain 4 new entries with FCode : ZUS1, ZUS2, ZUS3, ZUS4.<br />Maintain the values using the following parameter values: Fcode = 'ENT1' and Source of Parameters = 'A'.<br />Refer to the screen shot 2 (in the attached word document) to see&#160;&#160;&#160;&#160;the example entries.<br /><br />1.4. For customers who are in EHP5 or above (only), kindly implement the following manual steps before implementing the automatic corrections.</p>\r\n<ul>\r\n<li>Run the transaction SE80</li>\r\n</ul>\r\n<ul>\r\n<li>Enter the program name as SAPLCOIH</li>\r\n</ul>\r\n<ul>\r\n<li>Expand the 'Screens' object on the left and select the screen 1280</li>\r\n</ul>\r\n<ul>\r\n<li>Go to change mode</li>\r\n</ul>\r\n<ul>\r\n<li>Go to the Attributes tab of the screen</li>\r\n</ul>\r\n<ul>\r\n<li>Here, under the 'Other attributes' change the lines/columns maintained to 30 (lines) and 166 (columns)</li>\r\n</ul>\r\n<ul>\r\n<li>Save the entry</li>\r\n</ul>\r\n<ul>\r\n<li>Now click on the 'Layout' option</li>\r\n</ul>\r\n<ul>\r\n<li>The screen painter will now open</li>\r\n</ul>\r\n<ul>\r\n<li>Go to the change mode</li>\r\n</ul>\r\n<ul>\r\n<li>Select the subscreen area EAML_LFE (the big subscreen on the right) by double clicking on it or using the select block button</li>\r\n</ul>\r\n<ul>\r\n<li>The selected block will now be highlighted</li>\r\n</ul>\r\n<ul>\r\n<li>Place the cursor on the first cell available on the 9th line of the same block</li>\r\n</ul>\r\n<ul>\r\n<li>From the menu, select the option Edit -&gt; Move Subscreen</li>\r\n</ul>\r\n<ul>\r\n<li>Or directly move this subscreen down such that it starts from the 9th line of the same block</li>\r\n</ul>\r\n<ul>\r\n<li>A EAML_LFE subscreen will be moved 10 lines below from its original position</li>\r\n</ul>\r\n<ul>\r\n<li>Save and activate the changes</li>\r\n</ul>\r\n<p>1.5. Now apply the automatic corrections.</p>\r\n<p><strong>2. Check</strong><br />Run SE80<br />Navigate to function group \"COIH \" &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Double click on COIH, the pop-up 'Display Function Group' is triggered. Navigate to Main program and search for 'INCLUDE LCOIHFO4.'.<br />Now check if the statment 'INCLUDE /MRSS/RAC_MOD_PROFILES.' has been inserted below the above mentioned code. If it is not found, manually insert the following code:<br />*&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />* MRS 610: Note 1138507<br />INCLUDE /MRSS/RAC_MOD_PROFILES.<br />*&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *</p>\r\n<p><br /><strong>3. Enhancements to Material master</strong></p>\r\n<p><strong>Steps&#160;3 is&#160;not valid for&#160;MRS 1.0 for SAP S/4HANA</strong></p>\r\n<p><strong>Note: SAP Multiresource Scheduling 1.0 for SAP S/4HANA does not support the material master enhancement for requirements profiles. If you have used this functionality in previous SAP Multiresource Scheduling releases, you must copy the values for fields in the /MRSS/* namespace from table MARC to a custom table. You must do so before the conversion to SAP Multiresource Scheduling 1.0 for SAP S/4HANA, otherwise your data might be lost.</strong><br /><br />To show the created customer subscreens in the transactions<br />MM01 / MM02 / MM03, there is some customizing necessary:<br /><br /><span style=\"text-decoration: underline;\">a) Creation of screen sequences</span><br /><br />Follow the path in SPRO transaction:<br />Logistics - General - Material Master - Configuring the Material &#160;&#160;&#160;&#160;&#160;&#160; Master - Define Structure of Data Screens for Each Screen Sequence<br />(Transaction OMT3B).<br />Copy screen sequence e.g. 21 .. Z1<br />Refer to the screen shots 3-5 (in the attached word document) to see &#160;&#160; the example entries.<br />Here in the Fig. 5 of the attached document, enter the Program name &#160;&#160;&#160;&#160;for MRS as /MRSS/SAPLRAC_INTERFACE.<br /><br /><span style=\"text-decoration: underline;\">b) Customizing of screen order</span><br /><br />Follow the path in SPRO transaction:<br />Logistics - General - Material Master - Configuring the Material &#160;&#160;&#160;&#160;&#160;&#160; Master - Maintain Order of Main and Additional Screens.<br />(Transaction OMT3R)<br />-&gt; the new will be automatic the last one<br />Refer to the screen shots 6-7 (in the attached word document) to see &#160;&#160; the example entries.<br /><br /><span style=\"text-decoration: underline;\">c) Assign screen sequences to user/material type/transaction/industry sector. </span><br /><br />Use the following path :<br />Logistics - General - Material Master - Configuring the Material &#160;&#160;&#160;&#160;&#160;&#160; Master - Assign Screen Sequences to User / Material / Type / &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Transaction / Industry Sector<br />(Transaction OMT3E)<br />- e.g. 21-&gt;Z1<br />Refer to screen shot 8 (in the attached word document) to see the example entries.<br /><br /><span style=\"text-decoration: underline;\">d) Creating entries in table V_T133D.</span><br /><br />Run the transaction SM30.<br />Enter the view name as V_T133D and click on the 'maintain' button<br /><br />Create 3 entries with code PB97, PB98 and PB99 respectively where:<br /><br />Screen sequence number (SSq) is same as that maintained in transaction OMT3B for the MRS Enhancements for all 3 entries.<br /><br />Logical screen (SScrn) is again same as the logical data screen maintained for the MRS Enhancements sequence number in OMT3B transaction for all 3 entries.<br /><br />Processing Routine type (Typ) = I for all 3 entries and<br /><br />Process Same screen indicator (P) is checked for all 3 entries<br /><br />Save the table.<br /><br />You may refer to the example screenshot present in the attachment 'V_T133D_entries'</p>\r\n<p><br /><strong>4. Enhancements to Equipment master</strong><br /><br /><strong>Steps 4.1 and 4.2 are not valid for 800 and above releases</strong><br /><br />4.1 Create a customer include CI_EQUI (if not existing already) with a dummy field DUMMY_EQUI and append structure /MRSS/T_EQUI to it.<br /><br />4.2 Create a customer include CI_EQUI_U (if not existing already) with a dummy field DUMMY_EQUI_U and append structure /MRSS/T_EQUI_U to it.<br /><br />4.3 User exit enhancement ITOB0001<br />From transaction CMOD create a new project ZMRSS_CS and use the enhancement ITOB0001.<br /><br /><span style=\"text-decoration: underline;\">a) Implement call of sub-screens ZMRSS_SUBSCREEN_PROF with Dynpro 1000</span><br /><br />The screen type will be subscreen.<br />From the graphical editor create a subscreen area and name it as ZMRSS_SUBSCREEN_PROF.<br /><br />Copy the below logic into the Flow logic of screen.<br /><br />PROCESS BEFORE OUTPUT.<br />** MODULE STATUS_1000.<br />** Begin MRSS extension:<br />** call subscreen with user data<br />&#160;&#160;CALL SUBSCREEN zmrss_subscreen_prof<br />&#160;&#160;&#160;&#160;&#160;&#160; INCLUDING '/MRSS/SAPLRAC_INTERFACE' '2000'.<br />** End MRSS extension<br /><br />PROCESS AFTER INPUT.<br />** MODULE USER_COMMAND_1000.<br />** Begin MRSS extension:<br />** call subscreen with user data<br />&#160;&#160;CALL SUBSCREEN zmrss_subscreen_prof.<br />** End MRSS extension<br /><br /><span style=\"text-decoration: underline;\">b) Implement the function modules EXIT_SAPLITO0_001 and EXIT_SAPLITO0_002 via the include files</span><br /><br />Function exit EXIT_SAPLITO0_001 is implemented via include ZXTOBU01<br />** Begin MRSS extension:<br />** call function module to transport data to subscreen<br />&#160;&#160;CALL FUNCTION '/MRSS/RAC_ACT_DISPLAY_DATA'<br />&#160;&#160;&#160;&#160;&#160;&#160; EXPORTING<br />*&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; types of technical obj in PM: 02= Equipments<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_OBJECT_TYPE&#160;&#160;&#160;&#160;&#160;&#160;= I_OBJECT_TYPE<br />*&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 01=anlegen; 02=&#228;ndern; 03=anzeigen<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_ACTIVITY_TYPE&#160;&#160;&#160;&#160;= I_ACTIVITY_TYPE<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_DELETION_FLAG&#160;&#160;&#160;&#160;= I_DELETION_FLAG<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_ACTIVE_FCODE&#160;&#160;&#160;&#160;&#160;&#160;= I_ACTIVE_FCODE<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_DATA_EQUI&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= I_DATA_EQUI<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_DATA_EQKT&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= I_DATA_EQKT<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_DATA_EQUZ&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= I_DATA_EQUZ<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_DATA_ILOA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= I_DATA_ILOA<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_DATA_IFLO&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= I_DATA_IFLO<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I_DATA_FLEET&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= I_DATA_FLEET<br />&#160;&#160;&#160;&#160;&#160;&#160;IMPORTING<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;E_SUBSCREEN_NUMBER&#160;&#160;= E_SUBSCREEN_NUMBER.<br />** End MRSS extension<br /><br />Function exit EXIT_SAPLITO0_002 is implemented via include ZXTOBU02<br />** Begin MRSS extension:<br />** call function module to transport data to subscreen<br />&#160;&#160;CALL FUNCTION '/MRSS/RAC_ACT_CHANGE_DATA'<br />&#160;&#160;&#160;&#160;&#160;&#160; IMPORTING<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;E_UPDATE_DATA_EQ&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= E_UPDATE_DATA_EQ<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;E_UPDATE_FLAGS_EQ&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; = E_UPDATE_FLAGS_EQ<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;E_UPDATE_DATA_EZ&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= E_UPDATE_DATA_EZ<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;E_UPDATE_FLAGS_EZ&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; = E_UPDATE_FLAGS_EZ<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;E_UPDATE_DATA_IFLO&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;= E_UPDATE_DATA_IFLO<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;E_UPDATE_FLAGS_IFLO&#160;&#160;&#160;&#160;&#160;&#160; = E_UPDATE_FLAGS_IFLO.<br />** End MRSS extension<br /><br />4.4 The new function codes in order to call the maintenance<br />(create / change / display / automatic determination) of the requirement profile must have the name: ZZITO_xx with x = 01, 02, 03 or 04.<br /><br />4.4.1 Use transaction VFBS in order to maintain the subsequent screen control.<br /><br />Launch the transaction VFBS. Give program name as SAPMIEQ0 and select the radio button \"Function codes for T185F'. Go to change mode and add following entries.<br />-&gt; Maintain 3 new entries with FCode : ZZITO_01, ZZITO_02, ZZITO_03.<br />-&gt; For all 3 entries maintain AcCat = 'X'.<br />-&gt; Leave all other values as default and save.<br /><br />4.5 Via Customizing the (new) tabstrip has to be activated:<br />Transaction SPRO : Plant Maintenance and Customer Service - Master Data in Plant Maintenance and Customer Service - Technical Objects -&gt; General Data - Set View Profiles for Technical Objects.<br /><br />Here for the screen group H2(Screen group equipment data), navigate to \"Activity and Layout views\" and make sure that the tab No. 120<br />( Other ) is maintained and active. This tab contains the MRS data.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-MRS (Multi-Resource Scheduling)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I333392)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I322099)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001138507/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Note_1138507.pdf", "FileSize": "310", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000117362008&iv_version=0043&iv_guid=B2C24CA4416CF54CA49615E64269474C"}, {"FileName": "V_T133D_entries.pdf", "FileSize": "95", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000117362008&iv_version=0043&iv_guid=C4ACE5B72A3FE548928A09425F669112"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2745163", "RefComponent": "CA-MRS", "RefTitle": "Run time error when MRS requirement profiles are maintained in equipment", "RefUrl": "/notes/2745163"}, {"RefNumber": "2637577", "RefComponent": "PM-EQM-EQ", "RefTitle": "Enable screens from different program for Technical Object View profile", "RefUrl": "/notes/2637577"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2810966", "RefComponent": "CA-MRS", "RefTitle": "Requirement Profile not populated when order is created from maintenance plan", "RefUrl": "/notes/2810966 "}, {"RefNumber": "2745163", "RefComponent": "CA-MRS", "RefTitle": "Run time error when MRS requirement profiles are maintained in equipment", "RefUrl": "/notes/2745163 "}, {"RefNumber": "2488679", "RefComponent": "CA-MRS", "RefTitle": "S4TC MRSS Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2488679 "}, {"RefNumber": "2493452", "RefComponent": "CA-MRS", "RefTitle": "MRS enhancements to Material master", "RefUrl": "/notes/2493452 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": ""}, {"SoftwareComponent": "MRSS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "MRSS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "MRSS", "From": "800", "To": "800", "Subsequent": ""}, {"SoftwareComponent": "MRSS", "From": "900", "To": "900", "Subsequent": ""}, {"SoftwareComponent": "MRSS", "From": "V1000", "To": "V1000", "Subsequent": ""}, {"SoftwareComponent": "MRSS", "From": "V2000", "To": "V2000", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0001138507/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 3, "URL": "/corrins/0001138507/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 7, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2335765 ", "URL": "/notes/2335765 ", "Title": "Deployed Server: Distribution of Maintenance Plan Change Protection / Completion of Call Objects", "Component": "IS-DFS-PM-DIS"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2435617 ", "URL": "/notes/2435617 ", "Title": "EAM CC4 D10156 - IF - MPlan/MPlanSchedule/Notif/Order/OrdOp/Selscreen", "Component": "PM"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2335765 ", "URL": "/notes/2335765 ", "Title": "Deployed Server: Distribution of Maintenance Plan Change Protection / Completion of Call Objects", "Component": "IS-DFS-PM-DIS"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2435617 ", "URL": "/notes/2435617 ", "Title": "EAM CC4 D10156 - IF - MPlan/MPlanSchedule/Notif/Order/OrdOp/Selscreen", "Component": "PM"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2555479 ", "URL": "/notes/2555479 ", "Title": "SAP Geo Framework for SAP S/4HANA Asset Management - Message control in SAP GUI and BAPIs", "Component": "PM"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2555479 ", "URL": "/notes/2555479 ", "Title": "SAP Geo Framework for SAP S/4HANA Asset Management - Message control in SAP GUI and BAPIs", "Component": "PM"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2571301 ", "URL": "/notes/2571301 ", "Title": "Interface Note", "Component": "PM-F<PERSON>"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}