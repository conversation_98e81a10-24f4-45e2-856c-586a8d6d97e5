{"Request": {"Number": "1057459", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 275, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006842752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001057459?language=E&token=D6F4222F4F807D5E685A37205943C34B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001057459", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1057459"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.05.2007"}, "SAPComponentKey": {"_label": "Component", "value": "LO-SPM-OUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Outbound Process"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Parts Management", "value": "LO-SPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-SPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Outbound Process", "value": "LO-SPM-OUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-SPM-OUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1057459 - Control of CRM-related functionality in ERP 2005"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have connected a CRM system of release 5.0 or higher to an ECC system. You want to use one of the following business processes which start with a sales or service order in CRM</p> <UL><LI>Sales order management in CRM without replication of the sales order to ERP (Direct Delivery Scenario with unchecked deliveries);</LI></UL> <UL><LI>Third Party Sales Order Processing (TPOP) with CRM sales orders;</LI></UL> <UL><LI>Complaints and returns with CRM service orders.<br /></LI></UL> <p>In order to use one of the aforementioned processes you currently have to activate the Service Parts Management (SPM) switch in the Implementation Guide. However, this switch controls several other functions and it is not fully transparent what actually is activated with it. You may also want to use only parts of Service Parts Management and not the full range of functionality or the full system landscape.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DDS, outbound delivery, unchecked delivery, SFS, sales order, VL10UC<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>A new switch will be introduced which exclusively controls the usage of CRM-related functionality in ERP. The new switch mainly serves for performance improvements since unnecessary communication steps to the CRM system can be avoided if it is not active.<br /><br />Activate this flag if you want to use one of the following functions:</p> <UL><LI>Sales order management in CRM without replication of the sales order to ERP (Direct Delivery Scenario with unchecked deliveries);</LI></UL> <UL><LI>Third Party Sales Order Processing (TPOP) with CRM sales orders;</LI></UL> <UL><LI>Complaints and returns with CRM service orders.</LI></UL> <p><br />The new switch is backwards-compatible to the existing SPM-activation switch: The CRM-functionality is activated also in case that the SPM switch is active and the new CRM-Switch is not. That means that no action is necessary after implementation of the support package in which the new CRM switch is shipped. You may at anytime later deactivate the SPM switch in favor of the new CRM switch if you want to use CRM-related functionality only.<br /><br />It is not recommended to manually implement the corrections which are necessary to use the new switch. The following description of the correction only contains those steps which are necessary from a technical point of view in case that a subsequent correction depends on the current ones.</p> <OL>1. Create the new data element /SPE/CRM_ACTIVE by means of transaction SE11 with the following attributes:</OL> <UL><UL><LI>Short Description: Activation of CRM-Related Functionality in ERP</LI></UL></UL> <UL><UL><LI>Package: /SPE/IF_SERVICES</LI></UL></UL> <UL><UL><LI>Domain: XFELD</LI></UL></UL> <UL><UL><LI>Field labels:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT></TH><TH ALIGN=LEFT> Length</TH><TH ALIGN=LEFT> Text</TH></TR> <TR><TD>Short</TD><TD> 10</TD><TD> CRM Act.</TD></TR> <TR><TD>Medium</TD><TD> 15</TD><TD> CRM Active</TD></TR> <TR><TD>Long</TD><TD> 20</TD><TD> CRM Functions Active</TD></TR> <TR><TD>Heading</TD><TD> 10</TD><TD> CRM Act.</TD></TR> </TABLE></UL></UL> <p></p> <OL>2. Extend the database table /SPE/CTRL_SW by the new field CRM_ACTIVE. Assign the new data element /SPE/CRM_ACTIVE to this field. Activate the table.</OL> <OL>3. Implement the corrections from the correction instruction in your system.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031178)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001057459/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001057459/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "990909", "RefComponent": "LO-SPM-STO", "RefTitle": "Control Framework Redesign", "RefUrl": "/notes/990909"}, {"RefNumber": "990782", "RefComponent": "LO-SPM-OUT", "RefTitle": "Control Framework for SPM functions in deliveries", "RefUrl": "/notes/990782"}, {"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611"}, {"RefNumber": "1294067", "RefComponent": "LO-SPM-X", "RefTitle": "Activation of enhancements for SPM", "RefUrl": "/notes/1294067"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1294067", "RefComponent": "LO-SPM-X", "RefTitle": "Activation of enhancements for SPM", "RefUrl": "/notes/1294067 "}, {"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611 "}, {"RefNumber": "990782", "RefComponent": "LO-SPM-OUT", "RefTitle": "Control Framework for SPM functions in deliveries", "RefUrl": "/notes/990782 "}, {"RefNumber": "990909", "RefComponent": "LO-SPM-STO", "RefTitle": "Control Framework Redesign", "RefUrl": "/notes/990909 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60010", "URL": "/supportpackage/SAPKH60010"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0001057459/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "871336 ", "URL": "/notes/871336 ", "Title": "ASN for TPOP purchase order: Delivery quantity not updated", "Component": "LO-SPM-OUT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "883877 ", "URL": "/notes/883877 ", "Title": "/SPE/ BAdI Implementation processed outside SPM", "Component": "LO-SPM-OUT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "885452 ", "URL": "/notes/885452 ", "Title": "Inbound delivery with batch split can not be created", "Component": "LO-SPM-X"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "888798 ", "URL": "/notes/888798 ", "Title": "Inbound IDoc failed in ERP with message status 51", "Component": "LO-SPM-INB"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}