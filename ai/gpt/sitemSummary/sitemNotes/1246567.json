{"Request": {"Number": "1246567", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 591, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016589332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001246567?language=E&token=2A6BA63DAB3D75A9D3B1F060E2DEA40E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001246567", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001246567/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1246567"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.02.2011"}, "SAPComponentKey": {"_label": "Component", "value": "GRC-SAC"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Access Control"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Governance, Risk and Compliance", "value": "GRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Access Control", "value": "GRC-SAC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC-SAC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1246567 - AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 4.6C"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you want to import the add-ons VIRSANH 530_46C and/or VIRSAHR 530_46C (for HR systems only) in SAP 4.6C systems. The system issues an error message in transaction SAINT stating that the import prerequisites are not met.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>VIRSANH, VIRSAHR, 530_46C, ACP, attribute change package (ACP), SPAM, SAINT.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The add-ons VIRSANH 530_46C and/or VIRSAHR 530_46C (for HR systems only) were originally released on the following Support Package levels (see Notes 1133161 resp. 133162):<br />SAP_ABA&#x00A0;&#x00A0; 46C - SAPKA46C55<br />SAP_BASIS 46C - SAPKB46C55<br />SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C - SAPKE46CC3<br /><br />The system will not allow to install these add-ons because of import prerequisites are not met.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of September '08 the Add-Ons VIRSANH 530_46C and VIRSAHR 530_46C has been released on lowered Support Package levels:<br />SAP_ABA&#x00A0;&#x00A0; 46C - SAPKA46C45<br />SAP_BASIS 46C - SAPKB46C45<br />SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C - SAPKE46C19<br /><br />You can install the add-on VIRSANH 530_46C and VIRSAHR 530_46C (for HR systems) on the lowered Support Packages levels only if the corresponding Attribute Change Package (ACP) is uploaded to the systems.<br />For this reason, you must download an attribute change package (ACP) in addition to the installation package(s).<br />For more information about attribute change packages, see Note 1119856.<br /><br />If you want to install VIRSANH 530_46C and VIRSAHR 530_46C (for HR System only) into SAP 4.6C system, proceed as follows:</p> <UL><UL><LI>At least SPAM/SAINT Version 0047 is a prerequisite for the processing of ACPs in release 46C.</LI></UL></UL> <UL><UL><LI>Download the installation Packages SAPK-530COINVIRSANH and SAPK-530COINVIRSAHR (for HR System only) from the SAP Service Marketplace and download ACP files attached to this note.</LI></UL></UL> <UL><UL><LI>Load the contents of this file into the EPS inbox (SAINT &gt; Installation Package &gt; Load Packages &gt; From Front End).</LI></UL></UL> <UL><UL><LI>Then you can use SAINT to define the required queue.</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I039562)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I811179)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001246567/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001246567/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ACP_VIRSANH_530_46C.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000482292008&iv_version=0008&iv_guid=0099142A36AE6B4E8926688B520BDC34"}, {"FileName": "ACP_VIRSAHR_530_46C.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000482292008&iv_version=0008&iv_guid=8C5F59FA2F40AA4FA863CB918AA1E384"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1133162", "RefComponent": "GRC", "RefTitle": "VIRSAHR 530_46C Install / Delta Upgrade on SAP R/3 4.6C", "RefUrl": "/notes/1133162"}, {"RefNumber": "1133161", "RefComponent": "GRC", "RefTitle": "VIRSANH 530_46C Install / Delta Upgrade on SAP_BASIS 46C", "RefUrl": "/notes/1133161"}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1133162", "RefComponent": "GRC", "RefTitle": "VIRSAHR 530_46C Install / Delta Upgrade on SAP R/3 4.6C", "RefUrl": "/notes/1133162 "}, {"RefNumber": "1133161", "RefComponent": "GRC", "RefTitle": "VIRSANH 530_46C Install / Delta Upgrade on SAP_BASIS 46C", "RefUrl": "/notes/1133161 "}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "VIRSAHR", "From": "530_46C", "To": "530_46C", "Subsequent": ""}, {"SoftwareComponent": "VIRSANH", "From": "530_46C", "To": "530_46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}