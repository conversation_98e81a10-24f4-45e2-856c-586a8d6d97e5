{"Request": {"Number": "1510974", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1306, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017215902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001510974?language=E&token=695B130611DCA3BF1E572A59D94EAA37"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001510974", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001510974/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1510974"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1510974 - SAPBWNews BW 7.02 ABAP SP 07"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note deals with ABAP Support Package 07 for BW Release 7.02 (NW 7.0 Enhancement Package 02).<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.02,&#160;&#160;BW Patches, BI, BI 7.02, SAPBINews</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBWNews for ABAP Support Package 07of BW Release 7.02 (part of NetWeaver 7.0 Enhancement Package 2).<br /><br />It provides a list of all notes describing the corrections or enhancements in Support Package 06. This note will be updated when other notes are added.<br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li>Manual actions that may be necessary:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>General information:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected by this Support Package;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements delivered with this Support Package</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>Factors you must take into account when you import the Support Package:</strong><br /><br />- To date, none known.<br /><br /><strong>Errors that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>Properties of query elements in the queries that are changed in the current Support Packages via Version 1200 or higher of the BEx Query Designer may be evaluated incorrectly by the OLAP processor (for example, the number of decimal places). For more information, see Note 1544434.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Queries that have the characteristic 1CUDIM terminate in the JAVA runtime. For more information, see Note 1533748.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>Errors corrected in this Support Package:</strong><br /><br />- To date, none known.<br /><br /><br /><strong>Enhancements delivered with this Support Package:</strong><br /><br />- To date, none known.<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001510974/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001510974/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1666937", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in PREPARE_BASE_UOM_OLAP / CL_RSUOM_UOMT_RUNTIME", "RefUrl": "/notes/1666937"}, {"RefNumber": "1554344", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1554344"}, {"RefNumber": "1554274", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Clean up class CL_RSBM_LOG_DATAPACKAGE", "RefUrl": "/notes/1554274"}, {"RefNumber": "1554272", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Long runtime if crossjoin is used", "RefUrl": "/notes/1554272"}, {"RefNumber": "1554217", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Save of filter is not cancelled in case of CTS error", "RefUrl": "/notes/1554217"}, {"RefNumber": "1554150", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (Support Package 26): Dump during migration", "RefUrl": "/notes/1554150"}, {"RefNumber": "1554108", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in New MD deletion where used checks", "RefUrl": "/notes/1554108"}, {"RefNumber": "1554098", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "GETWA_NOT_ASSIGNED when you execute RSPC_METADATA_CLEANUP", "RefUrl": "/notes/1554098"}, {"RefNumber": "1553927", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: High memory consumption; crossjoin, intersect, generate", "RefUrl": "/notes/1553927"}, {"RefNumber": "1553696", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Incorrect check during transport of content roles (ACGR)", "RefUrl": "/notes/1553696"}, {"RefNumber": "1553608", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Changelog Deletion Variant doesnt work in BW 7.0X-7.3X", "RefUrl": "/notes/1553608"}, {"RefNumber": "1553601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Adjustments for the input help", "RefUrl": "/notes/1553601"}, {"RefNumber": "1553368", "RefComponent": "BW-WHM", "RefTitle": "Lockwait situation for table NRIV", "RefUrl": "/notes/1553368"}, {"RefNumber": "1553334", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Msg. R3 416 instead of a relevant msg. during direct access", "RefUrl": "/notes/1553334"}, {"RefNumber": "1553333", "RefComponent": "BC-EIM-ODP", "RefTitle": "Release of DataSources for ODP", "RefUrl": "/notes/1553333"}, {"RefNumber": "1553204", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BWA: Delta property lost when index is recreated", "RefUrl": "/notes/1553204"}, {"RefNumber": "1553201", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.11SP07:RSAR_PSA_NEWDS_MAPPING_CHECK considers inactive PSA", "RefUrl": "/notes/1553201"}, {"RefNumber": "1553159", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems with initial value '#'", "RefUrl": "/notes/1553159"}, {"RefNumber": "1553144", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "No RFC authorization for function group RFC_METADATA", "RefUrl": "/notes/1553144"}, {"RefNumber": "1552825", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:DTP:Filter in Content Version of DTP is deleted", "RefUrl": "/notes/1552825"}, {"RefNumber": "1552762", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual characteristic 0INFOPROV is ignored", "RefUrl": "/notes/1552762"}, {"RefNumber": "1552741", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Performance Optimization :SID Flag Updates - Follow-up Note", "RefUrl": "/notes/1552741"}, {"RefNumber": "1552573", "RefComponent": "BW-BCT-FI-GL", "RefTitle": "Query with virtual provider shows incorrect data", "RefUrl": "/notes/1552573"}, {"RefNumber": "1552001", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "A query on a MultiProvider terminates", "RefUrl": "/notes/1552001"}, {"RefNumber": "1551985", "RefComponent": "BW-PLA-BPS", "RefTitle": "Program termination occurs when maintaining documents", "RefUrl": "/notes/1551985"}, {"RefNumber": "1551587", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: SUBSELECT and external hierarchies", "RefUrl": "/notes/1551587"}, {"RefNumber": "1551586", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Duplicated technical names of query components", "RefUrl": "/notes/1551586"}, {"RefNumber": "1551463", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Optimization in the data manager", "RefUrl": "/notes/1551463"}, {"RefNumber": "1551354", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26:ASSERTION_FAILED in single rule test(Infoset as Source)", "RefUrl": "/notes/1551354"}, {"RefNumber": "1551352", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Various minor issues in the Planning Modeler", "RefUrl": "/notes/1551352"}, {"RefNumber": "1551289", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:TRF:After import: Error message unclear, TRF missing", "RefUrl": "/notes/1551289"}, {"RefNumber": "1551005", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:After-import:T versions are no longer deleted", "RefUrl": "/notes/1551005"}, {"RefNumber": "1550919", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Deadlock/lock wait for complete deletion of DTA content", "RefUrl": "/notes/1550919"}, {"RefNumber": "1550918", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P26:BAPI_IPAK_CHANGE undoes field for warning handling", "RefUrl": "/notes/1550918"}, {"RefNumber": "1550723", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "System error in program CL_RSR and form GET_COB_PRO-01-", "RefUrl": "/notes/1550723"}, {"RefNumber": "1550270", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "DTP-LOG; no LOG for authorization problems", "RefUrl": "/notes/1550270"}, {"RefNumber": "1550187", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Termination in CL_RSDDB_INDEX_M and BUILD_TH_JOIN", "RefUrl": "/notes/1550187"}, {"RefNumber": "1549726", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2 and form FAC_VARIABLES-03-", "RefUrl": "/notes/1549726"}, {"RefNumber": "1549724", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Incorrect data for filter on Thj leaf", "RefUrl": "/notes/1549724"}, {"RefNumber": "1549224", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Request generation also reads deleted requests", "RefUrl": "/notes/1549224"}, {"RefNumber": "1549127", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Formulas of function module calls", "RefUrl": "/notes/1549127"}, {"RefNumber": "1549040", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P26:DSO:Deleting activation queue when AQ empty", "RefUrl": "/notes/1549040"}, {"RefNumber": "1548635", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Corrections to the time service routines in BW", "RefUrl": "/notes/1548635"}, {"RefNumber": "1548619", "RefComponent": "BW-BEX", "RefTitle": "BW-IP: internal", "RefUrl": "/notes/1548619"}, {"RefNumber": "1548452", "RefComponent": "BW-BEX-OT", "RefTitle": "New Infocube Load : Memory Leak", "RefUrl": "/notes/1548452"}, {"RefNumber": "1548259", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORACLE: Compression of UNIQUE INDEX of PSA tables", "RefUrl": "/notes/1548259"}, {"RefNumber": "1548242", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW cube indexes incorrect if no time char. in aggregate", "RefUrl": "/notes/1548242"}, {"RefNumber": "1548170", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "RRMX launches Excel, but the connection is not established", "RefUrl": "/notes/1548170"}, {"RefNumber": "1547838", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:DWB: Search for DTPs in workbench is slow", "RefUrl": "/notes/1547838"}, {"RefNumber": "1547708", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: some Key Figures are not visible", "RefUrl": "/notes/1547708"}, {"RefNumber": "1547667", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Error DTP:No LOGSYS with p_r_request->get_logsys", "RefUrl": "/notes/1547667"}, {"RefNumber": "1547453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Explain: X message for explain for formula with variables", "RefUrl": "/notes/1547453"}, {"RefNumber": "1547419", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "<PERSON>um<PERSON> Missing in Bex Analyzer", "RefUrl": "/notes/1547419"}, {"RefNumber": "1547391", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data when node is expanded", "RefUrl": "/notes/1547391"}, {"RefNumber": "1547290", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "DTP is not generated -> two source systems/IOBJ target", "RefUrl": "/notes/1547290"}, {"RefNumber": "1547271", "RefComponent": "BW-WHM-AWB", "RefTitle": "Missing authorization check in RFC with call transaction", "RefUrl": "/notes/1547271"}, {"RefNumber": "1546975", "RefComponent": "BW-BEX-OT", "RefTitle": "Internal <-> external conversion to DECFLOAT34 key figures", "RefUrl": "/notes/1546975"}, {"RefNumber": "1546906", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Enqueue: Minor improvements for creation of request", "RefUrl": "/notes/1546906"}, {"RefNumber": "1546399", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error in program CL_RSR and form GET_CHANM-02", "RefUrl": "/notes/1546399"}, {"RefNumber": "1545971", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Incorrect value for property 'MEMBER_UNIQUE_NAME'", "RefUrl": "/notes/1545971"}, {"RefNumber": "1545857", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:PC:Process chain step set t.red when request deleted", "RefUrl": "/notes/1545857"}, {"RefNumber": "1545834", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Authorization check on S_RFC_ADM", "RefUrl": "/notes/1545834"}, {"RefNumber": "1545818", "RefComponent": "BW-BEX", "RefTitle": "Orange", "RefUrl": "/notes/1545818"}, {"RefNumber": "1545663", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:WO-DSO:RSBX_BIW_GET_ODSDATA:Myself extraction: Selection", "RefUrl": "/notes/1545663"}, {"RefNumber": "1545450", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2, form LRECH_F_CHFP_01-01-", "RefUrl": "/notes/1545450"}, {"RefNumber": "1544520", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No Authorization with hierarchy authorization + interval", "RefUrl": "/notes/1544520"}, {"RefNumber": "1544434", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Properties of query elements are not evaluated correctly", "RefUrl": "/notes/1544434"}, {"RefNumber": "1544249", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Dump DYNPRO_FIELD_CONVERSION in screen 104", "RefUrl": "/notes/1544249"}, {"RefNumber": "1543704", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "CompositeProvider corrections", "RefUrl": "/notes/1543704"}, {"RefNumber": "1542855", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Simulation:RSBKDTPREPAIR_MAXSIZE: No list", "RefUrl": "/notes/1542855"}, {"RefNumber": "1542810", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Consulting:MD Update when key fields are mapped", "RefUrl": "/notes/1542810"}, {"RefNumber": "1541274", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP26:Impact on transfer structure - Infoobject Activation", "RefUrl": "/notes/1541274"}, {"RefNumber": "1541216", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1541216"}, {"RefNumber": "1540840", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "730:DTP:Performance:<PERSON><PERSON><PERSON>. Achtung:Hinweis NIE freigeben", "RefUrl": "/notes/1540840"}, {"RefNumber": "1540733", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Data flow migration: Name of InfoSource is lost", "RefUrl": "/notes/1540733"}, {"RefNumber": "1540688", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 BRAIN CL_RSDRC_PROVRQ_SRVS GET_SELDR_FROM_PROV_RQDR-01-", "RefUrl": "/notes/1540688"}, {"RefNumber": "1540666", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Screen 101 of program SAPLRSSM_PROCESS delivered again", "RefUrl": "/notes/1540666"}, {"RefNumber": "1540583", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Missing restrictions via the label", "RefUrl": "/notes/1540583"}, {"RefNumber": "1540273", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Different Issues with selector on F4", "RefUrl": "/notes/1540273"}, {"RefNumber": "1540186", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: RSSM_EXPAND_REQUESTLIST: Expansion in wrong order", "RefUrl": "/notes/1540186"}, {"RefNumber": "1540010", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Subsequent corrections to Note 1531144", "RefUrl": "/notes/1540010"}, {"RefNumber": "1540008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems in the context of SAP Business ByDesign - Part 2", "RefUrl": "/notes/1540008"}, {"RefNumber": "1539990", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate w/time hierarchy is compressed (2)", "RefUrl": "/notes/1539990"}, {"RefNumber": "1539903", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Optimization of master data access to Teradata", "RefUrl": "/notes/1539903"}, {"RefNumber": "1539601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Exception \"NAME_ERROR\" in the class CL_RSMD_RS_TREX_QUERY", "RefUrl": "/notes/1539601"}, {"RefNumber": "1539445", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DTP:DataSource extractor:PSA extractor:Join/cleanup", "RefUrl": "/notes/1539445"}, {"RefNumber": "1539234", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No message if authorization is missing", "RefUrl": "/notes/1539234"}, {"RefNumber": "1539226", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP27:Deletion of the Infobject terminates with error R7 157", "RefUrl": "/notes/1539226"}, {"RefNumber": "1539178", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:RSBX_BIW_GET_ODSDATA:Memory overflow:For all entries", "RefUrl": "/notes/1539178"}, {"RefNumber": "1538876", "RefComponent": "BW-BEX", "RefTitle": "BRAIN X299 in class CL_RSR_CHABIT; form SET_BIT1-02-", "RefUrl": "/notes/1538876"}, {"RefNumber": "1538402", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query terminates during generation", "RefUrl": "/notes/1538402"}, {"RefNumber": "1538109", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error when PARTITIONMODE 2 or 3", "RefUrl": "/notes/1538109"}, {"RefNumber": "1537931", "RefComponent": "BW-BEX", "RefTitle": "MP + non-cumulative and multiple assgmt of characteristics", "RefUrl": "/notes/1537931"}, {"RefNumber": "1537570", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1537570"}, {"RefNumber": "1537314", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "FB RSNDI_SHIE_SUBTREE_DELETE: SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/1537314"}, {"RefNumber": "1537125", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "SP26:Transaction RSH1 doesnt work correctly in display mode", "RefUrl": "/notes/1537125"}, {"RefNumber": "1536816", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 Enhanced SP compression postcorrection", "RefUrl": "/notes/1536816"}, {"RefNumber": "1536468", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS job cancels during status confirmation of a process.", "RefUrl": "/notes/1536468"}, {"RefNumber": "1536334", "RefComponent": "BW-BEX", "RefTitle": "DTP loading; archive areas are not checked correctly", "RefUrl": "/notes/1536334"}, {"RefNumber": "1536212", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "OLAP cache and currency translation", "RefUrl": "/notes/1536212"}, {"RefNumber": "1535607", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error in RSRV text on InfoCube with referencing key figure", "RefUrl": "/notes/1535607"}, {"RefNumber": "1535604", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: DTP: Manage: Where-used: No reset for DM", "RefUrl": "/notes/1535604"}, {"RefNumber": "1535576", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26: NOP key figures in transformations", "RefUrl": "/notes/1535576"}, {"RefNumber": "1535548", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "WJR:THJ - Subsequent correction to note 1464244", "RefUrl": "/notes/1535548"}, {"RefNumber": "1535514", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:DSP:PC:Truncate:Locked act. queue end with act. red", "RefUrl": "/notes/1535514"}, {"RefNumber": "1535483", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables exit i_step = 3 is not executed", "RefUrl": "/notes/1535483"}, {"RefNumber": "1535124", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Endless loop for complete deletion of DTA contents", "RefUrl": "/notes/1535124"}, {"RefNumber": "1535070", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 enhanced compression on large cache objects", "RefUrl": "/notes/1535070"}, {"RefNumber": "1535008", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Delta DTP: Transport with BEx variables or routines", "RefUrl": "/notes/1535008"}, {"RefNumber": "1535006", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1535006"}, {"RefNumber": "1534889", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Short dump during master data update (second attempt)", "RefUrl": "/notes/1534889"}, {"RefNumber": "1534748", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Quantity conversion does not take place", "RefUrl": "/notes/1534748"}, {"RefNumber": "1534680", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Hierarchy date variable is not filled when you navigate", "RefUrl": "/notes/1534680"}, {"RefNumber": "1534298", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1534298"}, {"RefNumber": "1534285", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RSBBS: Problems during the administration of jump targets", "RefUrl": "/notes/1534285"}, {"RefNumber": "1534231", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the case of constant selection and compounding", "RefUrl": "/notes/1534231"}, {"RefNumber": "1534209", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Overflow dump in report SAP_INFOCUBE_DESIGNS (2)", "RefUrl": "/notes/1534209"}, {"RefNumber": "1533694", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Red, updated DP cannot be repaired", "RefUrl": "/notes/1533694"}, {"RefNumber": "1533515", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "New MasterData Deletion: Usage check extended to include NLS", "RefUrl": "/notes/1533515"}, {"RefNumber": "1533381", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:Too many messages in DTP monitor during MD update", "RefUrl": "/notes/1533381"}, {"RefNumber": "1533378", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BExAnalyzer: Saving of Workbooks by Multiple Users", "RefUrl": "/notes/1533378"}, {"RefNumber": "1533324", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "No 'Standard Text' for hierarchy node is displayed", "RefUrl": "/notes/1533324"}, {"RefNumber": "1533243", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CL_RSMD_RS_TREX_QUERY->IF_RSMD_RS_BUILD_QUERY~READ_DATA", "RefUrl": "/notes/1533243"}, {"RefNumber": "1533143", "RefComponent": "BW-SYS", "RefTitle": "SP28:RS_BW_POST_MIGRATION doesnt convert PSAs of new DS", "RefUrl": "/notes/1533143"}, {"RefNumber": "1533115", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message: \"TREX call with check_n and FEMS_N\"", "RefUrl": "/notes/1533115"}, {"RefNumber": "1532837", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW write-optimized DSO, LISTCUBE and selective deletion", "RefUrl": "/notes/1532837"}, {"RefNumber": "1532624", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: REQARCH: Endless loop in write report", "RefUrl": "/notes/1532624"}, {"RefNumber": "1532596", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Explorer: 2920:multiprovider schema is not consistent;", "RefUrl": "/notes/1532596"}, {"RefNumber": "1532278", "RefComponent": "BW-BEX", "RefTitle": "Read authorization check for virtual providers", "RefUrl": "/notes/1532278"}, {"RefNumber": "1532223", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search based on calendar month/year does not work", "RefUrl": "/notes/1532223"}, {"RefNumber": "1532061", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Dump in process chains for sequence during saving", "RefUrl": "/notes/1532061"}, {"RefNumber": "1531920", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Error during InfoPackage import for non-existing target", "RefUrl": "/notes/1531920"}, {"RefNumber": "1531678", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT:Incorrect results when F4 in variable screen java (WJR)", "RefUrl": "/notes/1531678"}, {"RefNumber": "1531519", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Subsequent correction to Note 1385580 and Note 1431226", "RefUrl": "/notes/1531519"}, {"RefNumber": "1531453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and constant selection", "RefUrl": "/notes/1531453"}, {"RefNumber": "1531437", "RefComponent": "BW-BEX-ET-WEB-ITM", "RefTitle": "Role Menu Item: Javascript error with special characters", "RefUrl": "/notes/1531437"}, {"RefNumber": "1531427", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Extended where-used list for query elements", "RefUrl": "/notes/1531427"}, {"RefNumber": "1530803", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error CX_SY_DYN_TABLE_ILL_COMP_VAL; in  _SORT_X", "RefUrl": "/notes/1530803"}, {"RefNumber": "1530624", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "RSRHIEDIR_OLAP inconsistency,Deleted entries of INFOAREAHIER", "RefUrl": "/notes/1530624"}, {"RefNumber": "1530546", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Filling of formula variable via report-report interface", "RefUrl": "/notes/1530546"}, {"RefNumber": "1530454", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: XML injection when an XMLA interface is used", "RefUrl": "/notes/1530454"}, {"RefNumber": "1530319", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01SP9:New PSA Del Var ignores PSA of Exp DS of T-DSO/W-DSO", "RefUrl": "/notes/1530319"}, {"RefNumber": "1530274", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjustments to the CompositeProvider", "RefUrl": "/notes/1530274"}, {"RefNumber": "1530271", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception condition \"INCONSISTENCY\" raised", "RefUrl": "/notes/1530271"}, {"RefNumber": "1530255", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW selective deletion w/ very large single record conditions", "RefUrl": "/notes/1530255"}, {"RefNumber": "1530165", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:SDL:PC:Enabling user entries for InfoPackages", "RefUrl": "/notes/1530165"}, {"RefNumber": "1530110", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "F4 help for Attribute change run in PCs works incorrect", "RefUrl": "/notes/1530110"}, {"RefNumber": "1529997", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "deficient OLAP cache mode 6 'Shared Objects'", "RefUrl": "/notes/1529997"}, {"RefNumber": "1529720", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No data for calculated member and metadata reference", "RefUrl": "/notes/1529720"}, {"RefNumber": "1529591", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Tracing für BI Content Extraction", "RefUrl": "/notes/1529591"}, {"RefNumber": "1529540", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "BW InfoObject with RDA, navigation attributes and aggregates", "RefUrl": "/notes/1529540"}, {"RefNumber": "1529488", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.30 (SP02) Falsche Meldung", "RefUrl": "/notes/1529488"}, {"RefNumber": "1529455", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Stammdaten Optimierungen", "RefUrl": "/notes/1529455"}, {"RefNumber": "1529440", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: No text for new DataSources in request list", "RefUrl": "/notes/1529440"}, {"RefNumber": "1529330", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Deadlock when DSO contents are deleted in parallel", "RefUrl": "/notes/1529330"}, {"RefNumber": "1529152", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Runtime error in MDXTEST with missing authorization", "RefUrl": "/notes/1529152"}, {"RefNumber": "1529080", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01 SP9:PSA/CLG deletion variant save doesnt work correctly", "RefUrl": "/notes/1529080"}, {"RefNumber": "1528921", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Queries regenerated each time during execution", "RefUrl": "/notes/1528921"}, {"RefNumber": "1528906", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS dispatcher does not remove nodes for deleted PC Log IDs", "RefUrl": "/notes/1528906"}, {"RefNumber": "1528875", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Incorrect authorization check f. jump destination activation", "RefUrl": "/notes/1528875"}, {"RefNumber": "1528869", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_MEMBERS, CP/NP, Prty Member_Caption", "RefUrl": "/notes/1528869"}, {"RefNumber": "1528864", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Query generation generates syntax error for non-cml. cubes", "RefUrl": "/notes/1528864"}, {"RefNumber": "1528374", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error GET_PRPTY_VALUE-02-", "RefUrl": "/notes/1528374"}, {"RefNumber": "1528332", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0(SP26) Fehlerhafte Anzeige InfoSource", "RefUrl": "/notes/1528332"}, {"RefNumber": "1528293", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Dump in report RSSM_SM50_ALT", "RefUrl": "/notes/1528293"}, {"RefNumber": "1528191", "RefComponent": "BW-BEX-OT", "RefTitle": "Incorrect payment in case of MultiProvider with delta pair", "RefUrl": "/notes/1528191"}, {"RefNumber": "1528052", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java View cannot be loaded in ABAP runtime", "RefUrl": "/notes/1528052"}, {"RefNumber": "1527970", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Log overflow during query processing", "RefUrl": "/notes/1527970"}, {"RefNumber": "1527930", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination in BW aggregates with COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/1527930"}, {"RefNumber": "1527868", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BW InfoCube with BIA displays compressed aggregates", "RefUrl": "/notes/1527868"}, {"RefNumber": "1527844", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "%_HINTS ORACLE not built correct in the code LRRSIF03", "RefUrl": "/notes/1527844"}, {"RefNumber": "1527808", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BexAnalyzer:Jump to an Transaction freezes Sap GUI window", "RefUrl": "/notes/1527808"}, {"RefNumber": "1527728", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Error handler: Endless loop during deletion", "RefUrl": "/notes/1527728"}, {"RefNumber": "1527726", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "730:New DS & hierarchy sel. w/o existing hierarchy headers", "RefUrl": "/notes/1527726"}, {"RefNumber": "1527063", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data with constant selection and compounding", "RefUrl": "/notes/1527063"}, {"RefNumber": "1527025", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Manage:Complete data target deletion and request list", "RefUrl": "/notes/1527025"}, {"RefNumber": "1527011", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_STREAMINFO & fixed fiscal variant", "RefUrl": "/notes/1527011"}, {"RefNumber": "1526867", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Query does not return navigation attribute in MultiProvider", "RefUrl": "/notes/1526867"}, {"RefNumber": "1526857", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Incorrect display of destination", "RefUrl": "/notes/1526857"}, {"RefNumber": "1526835", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:REQARCH: Corr. in report RSREQARCH_REMO_INCONSISTENCIES", "RefUrl": "/notes/1526835"}, {"RefNumber": "1526833", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P26: RDA: No F1 help in real-time fields in the scheduler", "RefUrl": "/notes/1526833"}, {"RefNumber": "1526821", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Hierarchy without unassigned node has incorrect totals", "RefUrl": "/notes/1526821"}, {"RefNumber": "1526816", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Not possible to skip processes that have errors", "RefUrl": "/notes/1526816"}, {"RefNumber": "1526642", "RefComponent": "BW-PLA-IP", "RefTitle": "Adding a free characteristic unnecessary (BRAIN 224)", "RefUrl": "/notes/1526642"}, {"RefNumber": "1526618", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Fields in PSA have no text", "RefUrl": "/notes/1526618"}, {"RefNumber": "1526599", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Wrong values filtered with manual input on the Var. Screen", "RefUrl": "/notes/1526599"}, {"RefNumber": "1526103", "RefComponent": "BW-WHM-DBA-MPRO", "RefTitle": "MultiProvider activation abends with x message", "RefUrl": "/notes/1526103"}, {"RefNumber": "1526045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Non-unique hierarchies, link nodes, and structures", "RefUrl": "/notes/1526045"}, {"RefNumber": "1525723", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Program termination in program RSPC_LOG_DELETE", "RefUrl": "/notes/1525723"}, {"RefNumber": "1525635", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Additional index for RSZWVIEW table", "RefUrl": "/notes/1525635"}, {"RefNumber": "1525546", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Destination transport RC = 8", "RefUrl": "/notes/1525546"}, {"RefNumber": "1525419", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate with time hierarchy is compressed", "RefUrl": "/notes/1525419"}, {"RefNumber": "1525127", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Filter for hierarchies and empty mode", "RefUrl": "/notes/1525127"}, {"RefNumber": "1524797", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in MultiProvider hint buffer", "RefUrl": "/notes/1524797"}, {"RefNumber": "1524724", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI GET_MEMBERS, duplicate display of Self member", "RefUrl": "/notes/1524724"}, {"RefNumber": "1524315", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE terminates on write-optimized DSO", "RefUrl": "/notes/1524315"}, {"RefNumber": "1524281", "RefComponent": "BW-BEX-ET-OSD", "RefTitle": "BETA_NW73_BW - URL for query or query view is not created", "RefUrl": "/notes/1524281"}, {"RefNumber": "1524269", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Subsequent correction for Note 1505587", "RefUrl": "/notes/1524269"}, {"RefNumber": "1523946", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "BPC has problems with the ABAP process type", "RefUrl": "/notes/1523946"}, {"RefNumber": "1523876", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Sequence of flexible InfoObjects for hierarchy file", "RefUrl": "/notes/1523876"}, {"RefNumber": "1523832", "RefComponent": "BW-PLA-IP", "RefTitle": "Integrated planning: Data is not current", "RefUrl": "/notes/1523832"}, {"RefNumber": "1523789", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter maintenance:Switch to display mode:Termination occurs", "RefUrl": "/notes/1523789"}, {"RefNumber": "1523116", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The factory calendar does not exist", "RefUrl": "/notes/1523116"}, {"RefNumber": "1522770", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "New Master Data Deletion dumps on MS SQL Server", "RefUrl": "/notes/1522770"}, {"RefNumber": "1522701", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: Technisches Merkmal 0REQUEST in InfoProvider-View", "RefUrl": "/notes/1522701"}, {"RefNumber": "1522691", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Performance Optimization : SID Flag Updates", "RefUrl": "/notes/1522691"}, {"RefNumber": "1522519", "RefComponent": "BW-BEX", "RefTitle": "SAP_NOT_NULL_REPAIR_ORACLE", "RefUrl": "/notes/1522519"}, {"RefNumber": "1522498", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Duplicates in result of BAPI_MDPROVIDER_GET_CUBES", "RefUrl": "/notes/1522498"}, {"RefNumber": "1522488", "RefComponent": "BW-PLA-BPS-WIB", "RefTitle": "Style sheet does not work with Internet Explorer 8", "RefUrl": "/notes/1522488"}, {"RefNumber": "1522480", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.30 Dump bei Navigation", "RefUrl": "/notes/1522480"}, {"RefNumber": "1522393", "RefComponent": "BW-BEX", "RefTitle": "Transactional InfoCube: Follow-on note to Note 1391793", "RefUrl": "/notes/1522393"}, {"RefNumber": "1522372", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Performance optimization for processing of query elements", "RefUrl": "/notes/1522372"}, {"RefNumber": "1522356", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "BAPI_ISOURCE_HI_T_CREATE does not work for 7.x DataSources", "RefUrl": "/notes/1522356"}, {"RefNumber": "1522280", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "User-friendliness of data flow copy", "RefUrl": "/notes/1522280"}, {"RefNumber": "1522040", "RefComponent": "BW-BEX", "RefTitle": "Missing S_BDS_DS authorization for t-logo obj. in AP systems", "RefUrl": "/notes/1522040"}, {"RefNumber": "1521799", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Structures and non empty", "RefUrl": "/notes/1521799"}, {"RefNumber": "1521436", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "BAdI SMOD_RSR00004 is called twice during navigation", "RefUrl": "/notes/1521436"}, {"RefNumber": "1521154", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: bXML flattening: Incorrect values for compound char.", "RefUrl": "/notes/1521154"}, {"RefNumber": "1521055", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Estimated time for calculating default value is inaccurate", "RefUrl": "/notes/1521055"}, {"RefNumber": "1521023", "RefComponent": "BW-SYS-DB-MSS", "RefTitle": "SP25:Full table scan during master data update in MSSQL", "RefUrl": "/notes/1521023"}, {"RefNumber": "1520878", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index: Activation does not generate object in BWA", "RefUrl": "/notes/1520878"}, {"RefNumber": "1520825", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Introducing debug in RSNDI APIs for MD operations", "RefUrl": "/notes/1520825"}, {"RefNumber": "1520748", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (SP25): \"Time characteristic\" rule type selectable", "RefUrl": "/notes/1520748"}, {"RefNumber": "1520708", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "performance issues with RSEC_GENERATE_AUTHORIZATIONS", "RefUrl": "/notes/1520708"}, {"RefNumber": "1520064", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Incorrect data for calculated measures with AVG or IIF", "RefUrl": "/notes/1520064"}, {"RefNumber": "1519889", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP processing: \"Database selection was interrupted\"", "RefUrl": "/notes/1519889"}, {"RefNumber": "1519816", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index: Error messages when loading", "RefUrl": "/notes/1519816"}, {"RefNumber": "1519653", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination TABLE_INVALID_INDEX in routine SORT_END_DELSUM", "RefUrl": "/notes/1519653"}, {"RefNumber": "1519646", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Creating filter: More than one filter is generated", "RefUrl": "/notes/1519646"}, {"RefNumber": "1519344", "RefComponent": "BW-WHM-DST", "RefTitle": "Syntax error in RSSM_REDUCE_REQUESTLIST", "RefUrl": "/notes/1519344"}, {"RefNumber": "1519326", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Problem with report RSR_MDX_RSTT_TRACE_EXTRACT", "RefUrl": "/notes/1519326"}, {"RefNumber": "1519320", "RefComponent": "BW-PLA-BPS", "RefTitle": "Endless loop while using document copy function", "RefUrl": "/notes/1519320"}, {"RefNumber": "1518793", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index: Runtime error ASSIGN_TYPE_CONFLICT", "RefUrl": "/notes/1518793"}, {"RefNumber": "1518745", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO explorer: Incorrect authorization check for queries", "RefUrl": "/notes/1518745"}, {"RefNumber": "1518659", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (SP25): All changes lost after cancelation", "RefUrl": "/notes/1518659"}, {"RefNumber": "1518383", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "No source system in monitor tree", "RefUrl": "/notes/1518383"}, {"RefNumber": "1518164", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Exception NODE_NOT_AUTHORIZED and method _SELF_GET", "RefUrl": "/notes/1518164"}, {"RefNumber": "1518017", "RefComponent": "BW-WHM", "RefTitle": "SP25:Dump in RSRV test Compare Number Range and Maximum SIDs", "RefUrl": "/notes/1518017"}, {"RefNumber": "1517833", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Olap reftable has an unsupported type", "RefUrl": "/notes/1517833"}, {"RefNumber": "1517543", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "RSRCACHE Faults in BW730", "RefUrl": "/notes/1517543"}, {"RefNumber": "1517324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in PREPARE_BASE_UOM_OLAP / CL_RSUOM_UOMT_RUNTIME", "RefUrl": "/notes/1517324"}, {"RefNumber": "1517320", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Not able to open WB when SU01 parameter STATANALYZE is set", "RefUrl": "/notes/1517320"}, {"RefNumber": "1517312", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_RESOLVE_NAMES", "RefUrl": "/notes/1517312"}, {"RefNumber": "1517151", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "M version text is displayed in both change & display mode", "RefUrl": "/notes/1517151"}, {"RefNumber": "1517091", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_MEMBERS: Runtime for CP and NP", "RefUrl": "/notes/1517091"}, {"RefNumber": "1516988", "RefComponent": "BW-WHM-DST", "RefTitle": "SP26:Error while displaying PSA for 3.X datasource", "RefUrl": "/notes/1516988"}, {"RefNumber": "1516891", "RefComponent": "BW-EI-APD", "RefTitle": "APD: Authorization check S_TABU_DIS", "RefUrl": "/notes/1516891"}, {"RefNumber": "1516796", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "RSWR_BOOKMARK_DELETE doesn't delete personalization entries", "RefUrl": "/notes/1516796"}, {"RefNumber": "1516706", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DM:STATMAN: Data mart icons in display of request list", "RefUrl": "/notes/1516706"}, {"RefNumber": "1516590", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter maintenance: SAPLRRI2, GET_QUERY_PROP_SRDATE-01-", "RefUrl": "/notes/1516590"}, {"RefNumber": "1516261", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "F4 hierarchy node variable ignores compunded parent restrict", "RefUrl": "/notes/1516261"}, {"RefNumber": "1516179", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25; DTP: No lock for parallel update of DM pointers", "RefUrl": "/notes/1516179"}, {"RefNumber": "1516139", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Context menu entry: Maintain BW Accelerator index in RSA1", "RefUrl": "/notes/1516139"}, {"RefNumber": "1515988", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "System error in program CL_RSR and form GET_CHANM-01-", "RefUrl": "/notes/1515988"}, {"RefNumber": "1515964", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:SDL:USAB:F4 help of BEx variables improved again", "RefUrl": "/notes/1515964"}, {"RefNumber": "1515897", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect average runtime in query statistics", "RefUrl": "/notes/1515897"}, {"RefNumber": "1515687", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Performance optimization DSO data activation", "RefUrl": "/notes/1515687"}, {"RefNumber": "1515614", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:Expand all pushbutton on detail tabpage not working", "RefUrl": "/notes/1515614"}, {"RefNumber": "1515559", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Endless extraction for fetch size larger than 999999", "RefUrl": "/notes/1515559"}, {"RefNumber": "1515434", "RefComponent": "BW-BEX-OT", "RefTitle": "RSRT: Technical information: Resize leads to initial screen", "RefUrl": "/notes/1515434"}, {"RefNumber": "1515371", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Currency translation does not work", "RefUrl": "/notes/1515371"}, {"RefNumber": "1515330", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "Division by Zero error during SPO Activation", "RefUrl": "/notes/1515330"}, {"RefNumber": "1515291", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Sequence maintenance: Errors on variable screen", "RefUrl": "/notes/1515291"}, {"RefNumber": "1515200", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:Error stack filled - no error DTP", "RefUrl": "/notes/1515200"}, {"RefNumber": "1515194", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:Manage:Load request becomes green if monitor set by QM", "RefUrl": "/notes/1515194"}, {"RefNumber": "1514902", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "7.30 Patch 2: Bugs in DataServices integration", "RefUrl": "/notes/1514902"}, {"RefNumber": "1514838", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Characteristic relationship cannot be selected", "RefUrl": "/notes/1514838"}, {"RefNumber": "1514733", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Flattening: Header text is missing for default measure", "RefUrl": "/notes/1514733"}, {"RefNumber": "1514722", "RefComponent": "BW-WHM-DST", "RefTitle": "P25: Change run: Start time displays \"000000\" during run", "RefUrl": "/notes/1514722"}, {"RefNumber": "1514462", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Not all rows getting suppressed with zero suppression", "RefUrl": "/notes/1514462"}, {"RefNumber": "1514446", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW change run monitor: Display problem for no. of BIAs", "RefUrl": "/notes/1514446"}, {"RefNumber": "1514404", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO Explorer: \"Attribute not defined\" for text columns", "RefUrl": "/notes/1514404"}, {"RefNumber": "1514349", "RefComponent": "BW-BEX", "RefTitle": "Adjustments for CompositeProvider", "RefUrl": "/notes/1514349"}, {"RefNumber": "1514275", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "730:SDL:Generic delta:Full call of DATA_PULL", "RefUrl": "/notes/1514275"}, {"RefNumber": "1514244", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in class CL_RSR_LFMM; GET_FISCV_SID-01-", "RefUrl": "/notes/1514244"}, {"RefNumber": "1514208", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P25:PC:Priority dialog box has English heading in German", "RefUrl": "/notes/1514208"}, {"RefNumber": "1514046", "RefComponent": "BW-SYS-DB", "RefTitle": "Function Module RSDU_AQ_PRIMARY_INDEX_EXIT", "RefUrl": "/notes/1514046"}, {"RefNumber": "1514007", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy is evaluated for the incorrect key date", "RefUrl": "/notes/1514007"}, {"RefNumber": "1513942", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP26:Improving logging of decide partition error in PSA", "RefUrl": "/notes/1513942"}, {"RefNumber": "1513909", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "formal code corrections on OLAP cache component", "RefUrl": "/notes/1513909"}, {"RefNumber": "1513815", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Read mode and cache mode of default query cannot be changed", "RefUrl": "/notes/1513815"}, {"RefNumber": "1513767", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter maintenance: \"Cancel\" when switching to display mode", "RefUrl": "/notes/1513767"}, {"RefNumber": "1513465", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Partitioning extension leads to loss of data (2)", "RefUrl": "/notes/1513465"}, {"RefNumber": "1513261", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "OLE DB for OLAP: Problems with descendants and level", "RefUrl": "/notes/1513261"}, {"RefNumber": "1513252", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Status of requests for deleted systems is unknown", "RefUrl": "/notes/1513252"}, {"RefNumber": "1513125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Query generation for non-cumulative InfoCube: Syntax errors", "RefUrl": "/notes/1513125"}, {"RefNumber": "1512989", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P25:PC:SDL:Loading process remains yellow during termination", "RefUrl": "/notes/1512989"}, {"RefNumber": "1512782", "RefComponent": "BW-WHM-AWB", "RefTitle": "P25: AWB: Monitor display for all DataStore objects", "RefUrl": "/notes/1512782"}, {"RefNumber": "1512777", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Msg.: 'The Cube Schema XML is not valid: Invalid datatype'", "RefUrl": "/notes/1512777"}, {"RefNumber": "1512750", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "WAD Parameter SAVE_VARIABLE_VALUES with command SAVE_VIEW", "RefUrl": "/notes/1512750"}, {"RefNumber": "1512646", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Loading processes terminate after importing SP", "RefUrl": "/notes/1512646"}, {"RefNumber": "1512592", "RefComponent": "BI-RA-BICS", "RefTitle": "Lesen einer Hierarchie für Variablen nicht möglich", "RefUrl": "/notes/1512592"}, {"RefNumber": "1512540", "RefComponent": "BW-PLA-BPS", "RefTitle": "CALL_FUNCTION_CONFLICT_LENG when planning area is called", "RefUrl": "/notes/1512540"}, {"RefNumber": "1512499", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "RSPC_DISPLAY_JOBS aborts when the report has syntax errors.", "RefUrl": "/notes/1512499"}, {"RefNumber": "1512490", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P25:SDL:PC:Attempt to process control in background", "RefUrl": "/notes/1512490"}, {"RefNumber": "1512483", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Problem with new rows in input template", "RefUrl": "/notes/1512483"}, {"RefNumber": "1511916", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP: Hard termination if DTP does not exist", "RefUrl": "/notes/1511916"}, {"RefNumber": "1511501", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:BATCH:Time selections for deleting old messages", "RefUrl": "/notes/1511501"}, {"RefNumber": "1511222", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP: Dump in CL_RSBK_REQUEST_STATE=>GET_OBJ_REF_TSTATE", "RefUrl": "/notes/1511222"}, {"RefNumber": "1510914", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DTP:BEx variable F4 takes long time/useless results", "RefUrl": "/notes/1510914"}, {"RefNumber": "1510617", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:CTS: Incorrect call sequence when DTP is activated", "RefUrl": "/notes/1510617"}, {"RefNumber": "1509871", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DB connect & old DataSource:Msg. 'TEXT_040' in caller 20", "RefUrl": "/notes/1509871"}, {"RefNumber": "1509752", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P25:DSO:Activation in DSO terminates with RSM1 362", "RefUrl": "/notes/1509752"}, {"RefNumber": "1509505", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DTP:STATMAN:Dump occurs after reducing a request list", "RefUrl": "/notes/1509505"}, {"RefNumber": "1508938", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP: Cancelling if there are too many selection fields", "RefUrl": "/notes/1508938"}, {"RefNumber": "1504123", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DM:DTP:WO-DSO:Delta extraction from WO-DSO:Missing req.", "RefUrl": "/notes/1504123"}, {"RefNumber": "1504034", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:RSBATCH_DEL_MSG_PARM_DTPTEMP:Timestamp increase too soon", "RefUrl": "/notes/1504034"}, {"RefNumber": "1481835", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Performance of INTERSECT() function or Incorrect data", "RefUrl": "/notes/1481835"}, {"RefNumber": "1431163", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Incorrect date filled when wrong format used for date vars", "RefUrl": "/notes/1431163"}, {"RefNumber": "1370227", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT : Hierarchy activation dump", "RefUrl": "/notes/1370227"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1958826", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "<PERSON>um<PERSON> Missing in Bex Analyzer", "RefUrl": "/notes/1958826 "}, {"RefNumber": "1534285", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RSBBS: Problems during the administration of jump targets", "RefUrl": "/notes/1534285 "}, {"RefNumber": "1548452", "RefComponent": "BW-BEX-OT", "RefTitle": "New Infocube Load : Memory Leak", "RefUrl": "/notes/1548452 "}, {"RefNumber": "1530624", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "RSRHIEDIR_OLAP inconsistency,Deleted entries of INFOAREAHIER", "RefUrl": "/notes/1530624 "}, {"RefNumber": "1537314", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "FB RSNDI_SHIE_SUBTREE_DELETE: SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/1537314 "}, {"RefNumber": "1531437", "RefComponent": "BW-BEX-ET-WEB-ITM", "RefTitle": "Role Menu Item: Javascript error with special characters", "RefUrl": "/notes/1531437 "}, {"RefNumber": "1540273", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Different Issues with selector on F4", "RefUrl": "/notes/1540273 "}, {"RefNumber": "1542855", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Simulation:RSBKDTPREPAIR_MAXSIZE: No list", "RefUrl": "/notes/1542855 "}, {"RefNumber": "1533515", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "New MasterData Deletion: Usage check extended to include NLS", "RefUrl": "/notes/1533515 "}, {"RefNumber": "1536816", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 Enhanced SP compression postcorrection", "RefUrl": "/notes/1536816 "}, {"RefNumber": "1531678", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT:Incorrect results when F4 in variable screen java (WJR)", "RefUrl": "/notes/1531678 "}, {"RefNumber": "1525127", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Filter for hierarchies and empty mode", "RefUrl": "/notes/1525127 "}, {"RefNumber": "1526816", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Not possible to skip processes that have errors", "RefUrl": "/notes/1526816 "}, {"RefNumber": "1514462", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Not all rows getting suppressed with zero suppression", "RefUrl": "/notes/1514462 "}, {"RefNumber": "1666937", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in PREPARE_BASE_UOM_OLAP / CL_RSUOM_UOMT_RUNTIME", "RefUrl": "/notes/1666937 "}, {"RefNumber": "1553608", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Changelog Deletion Variant doesnt work in BW 7.0X-7.3X", "RefUrl": "/notes/1553608 "}, {"RefNumber": "1517324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in PREPARE_BASE_UOM_OLAP / CL_RSUOM_UOMT_RUNTIME", "RefUrl": "/notes/1517324 "}, {"RefNumber": "1513942", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP26:Improving logging of decide partition error in PSA", "RefUrl": "/notes/1513942 "}, {"RefNumber": "1514046", "RefComponent": "BW-SYS-DB", "RefTitle": "Function Module RSDU_AQ_PRIMARY_INDEX_EXIT", "RefUrl": "/notes/1514046 "}, {"RefNumber": "1535070", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "BW730 enhanced compression on large cache objects", "RefUrl": "/notes/1535070 "}, {"RefNumber": "1519344", "RefComponent": "BW-WHM-DST", "RefTitle": "Syntax error in RSSM_REDUCE_REQUESTLIST", "RefUrl": "/notes/1519344 "}, {"RefNumber": "1553333", "RefComponent": "BC-EIM-ODP", "RefTitle": "Release of DataSources for ODP", "RefUrl": "/notes/1553333 "}, {"RefNumber": "1535604", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: DTP: Manage: Where-used: No reset for DM", "RefUrl": "/notes/1535604 "}, {"RefNumber": "1520708", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "performance issues with RSEC_GENERATE_AUTHORIZATIONS", "RefUrl": "/notes/1520708 "}, {"RefNumber": "1553696", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Incorrect check during transport of content roles (ACGR)", "RefUrl": "/notes/1553696 "}, {"RefNumber": "1511501", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:BATCH:Time selections for deleting old messages", "RefUrl": "/notes/1511501 "}, {"RefNumber": "1504034", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:RSBATCH_DEL_MSG_PARM_DTPTEMP:Timestamp increase too soon", "RefUrl": "/notes/1504034 "}, {"RefNumber": "1515687", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Performance optimization DSO data activation", "RefUrl": "/notes/1515687 "}, {"RefNumber": "1516796", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "RSWR_BOOKMARK_DELETE doesn't delete personalization entries", "RefUrl": "/notes/1516796 "}, {"RefNumber": "1528869", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_MEMBERS, CP/NP, Prty Member_Caption", "RefUrl": "/notes/1528869 "}, {"RefNumber": "1517091", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_MEMBERS: Runtime for CP and NP", "RefUrl": "/notes/1517091 "}, {"RefNumber": "1539234", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No message if authorization is missing", "RefUrl": "/notes/1539234 "}, {"RefNumber": "1547419", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "<PERSON>um<PERSON> Missing in Bex Analyzer", "RefUrl": "/notes/1547419 "}, {"RefNumber": "1553144", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "No RFC authorization for function group RFC_METADATA", "RefUrl": "/notes/1553144 "}, {"RefNumber": "1540688", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 BRAIN CL_RSDRC_PROVRQ_SRVS GET_SELDR_FROM_PROV_RQDR-01-", "RefUrl": "/notes/1540688 "}, {"RefNumber": "1532837", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW write-optimized DSO, LISTCUBE and selective deletion", "RefUrl": "/notes/1532837 "}, {"RefNumber": "1553601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Adjustments for the input help", "RefUrl": "/notes/1553601 "}, {"RefNumber": "1516261", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "F4 hierarchy node variable ignores compunded parent restrict", "RefUrl": "/notes/1516261 "}, {"RefNumber": "1549127", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Formulas of function module calls", "RefUrl": "/notes/1549127 "}, {"RefNumber": "1532278", "RefComponent": "BW-BEX", "RefTitle": "Read authorization check for virtual providers", "RefUrl": "/notes/1532278 "}, {"RefNumber": "1540840", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "730:DTP:Performance:<PERSON><PERSON><PERSON>. Achtung:Hinweis NIE freigeben", "RefUrl": "/notes/1540840 "}, {"RefNumber": "1548619", "RefComponent": "BW-BEX", "RefTitle": "BW-IP: internal", "RefUrl": "/notes/1548619 "}, {"RefNumber": "1536468", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS job cancels during status confirmation of a process.", "RefUrl": "/notes/1536468 "}, {"RefNumber": "1531427", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Extended where-used list for query elements", "RefUrl": "/notes/1531427 "}, {"RefNumber": "1554272", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Long runtime if crossjoin is used", "RefUrl": "/notes/1554272 "}, {"RefNumber": "1553927", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: High memory consumption; crossjoin, intersect, generate", "RefUrl": "/notes/1553927 "}, {"RefNumber": "1529591", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Tracing für BI Content Extraction", "RefUrl": "/notes/1529591 "}, {"RefNumber": "1528191", "RefComponent": "BW-BEX-OT", "RefTitle": "Incorrect payment in case of MultiProvider with delta pair", "RefUrl": "/notes/1528191 "}, {"RefNumber": "1526642", "RefComponent": "BW-PLA-IP", "RefTitle": "Adding a free characteristic unnecessary (BRAIN 224)", "RefUrl": "/notes/1526642 "}, {"RefNumber": "1534889", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Short dump during master data update (second attempt)", "RefUrl": "/notes/1534889 "}, {"RefNumber": "1512592", "RefComponent": "BI-RA-BICS", "RefTitle": "Lesen einer Hierarchie für Variablen nicht möglich", "RefUrl": "/notes/1512592 "}, {"RefNumber": "1533143", "RefComponent": "BW-SYS", "RefTitle": "SP28:RS_BW_POST_MIGRATION doesnt convert PSAs of new DS", "RefUrl": "/notes/1533143 "}, {"RefNumber": "1527970", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Log overflow during query processing", "RefUrl": "/notes/1527970 "}, {"RefNumber": "1528906", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "CCMS dispatcher does not remove nodes for deleted PC Log IDs", "RefUrl": "/notes/1528906 "}, {"RefNumber": "1529997", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "deficient OLAP cache mode 6 'Shared Objects'", "RefUrl": "/notes/1529997 "}, {"RefNumber": "1510914", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DTP:BEx variable F4 takes long time/useless results", "RefUrl": "/notes/1510914 "}, {"RefNumber": "1519326", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Problem with report RSR_MDX_RSTT_TRACE_EXTRACT", "RefUrl": "/notes/1519326 "}, {"RefNumber": "1554108", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP27:Short dump in New MD deletion where used checks", "RefUrl": "/notes/1554108 "}, {"RefNumber": "1549724", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Incorrect data for filter on Thj leaf", "RefUrl": "/notes/1549724 "}, {"RefNumber": "1528921", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Queries regenerated each time during execution", "RefUrl": "/notes/1528921 "}, {"RefNumber": "1535124", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Endless loop for complete deletion of DTA contents", "RefUrl": "/notes/1535124 "}, {"RefNumber": "1528052", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java View cannot be loaded in ABAP runtime", "RefUrl": "/notes/1528052 "}, {"RefNumber": "1370227", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "THJT : Hierarchy activation dump", "RefUrl": "/notes/1370227 "}, {"RefNumber": "1522488", "RefComponent": "BW-PLA-BPS-WIB", "RefTitle": "Style sheet does not work with Internet Explorer 8", "RefUrl": "/notes/1522488 "}, {"RefNumber": "1530110", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "F4 help for Attribute change run in PCs works incorrect", "RefUrl": "/notes/1530110 "}, {"RefNumber": "1538402", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query terminates during generation", "RefUrl": "/notes/1538402 "}, {"RefNumber": "1539226", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP27:Deletion of the Infobject terminates with error R7 157", "RefUrl": "/notes/1539226 "}, {"RefNumber": "1512750", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "WAD Parameter SAVE_VARIABLE_VALUES with command SAVE_VIEW", "RefUrl": "/notes/1512750 "}, {"RefNumber": "1541274", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SP26:Impact on transfer structure - Infoobject Activation", "RefUrl": "/notes/1541274 "}, {"RefNumber": "1553204", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BWA: Delta property lost when index is recreated", "RefUrl": "/notes/1553204 "}, {"RefNumber": "1519889", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP processing: \"Database selection was interrupted\"", "RefUrl": "/notes/1519889 "}, {"RefNumber": "1522498", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Duplicates in result of BAPI_MDPROVIDER_GET_CUBES", "RefUrl": "/notes/1522498 "}, {"RefNumber": "1551985", "RefComponent": "BW-PLA-BPS", "RefTitle": "Program termination occurs when maintaining documents", "RefUrl": "/notes/1551985 "}, {"RefNumber": "1533381", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:Too many messages in DTP monitor during MD update", "RefUrl": "/notes/1533381 "}, {"RefNumber": "1512646", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Loading processes terminate after importing SP", "RefUrl": "/notes/1512646 "}, {"RefNumber": "1553159", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems with initial value '#'", "RefUrl": "/notes/1553159 "}, {"RefNumber": "1550919", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Deadlock/lock wait for complete deletion of DTA content", "RefUrl": "/notes/1550919 "}, {"RefNumber": "1519653", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination TABLE_INVALID_INDEX in routine SORT_END_DELSUM", "RefUrl": "/notes/1519653 "}, {"RefNumber": "1534231", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the case of constant selection and compounding", "RefUrl": "/notes/1534231 "}, {"RefNumber": "1526045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Non-unique hierarchies, link nodes, and structures", "RefUrl": "/notes/1526045 "}, {"RefNumber": "1540010", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Subsequent corrections to Note 1531144", "RefUrl": "/notes/1540010 "}, {"RefNumber": "1551587", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: SUBSELECT and external hierarchies", "RefUrl": "/notes/1551587 "}, {"RefNumber": "1535483", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables exit i_step = 3 is not executed", "RefUrl": "/notes/1535483 "}, {"RefNumber": "1552762", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual characteristic 0INFOPROV is ignored", "RefUrl": "/notes/1552762 "}, {"RefNumber": "1551586", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Duplicated technical names of query components", "RefUrl": "/notes/1551586 "}, {"RefNumber": "1522040", "RefComponent": "BW-BEX", "RefTitle": "Missing S_BDS_DS authorization for t-logo obj. in AP systems", "RefUrl": "/notes/1522040 "}, {"RefNumber": "1524269", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Subsequent correction for Note 1505587", "RefUrl": "/notes/1524269 "}, {"RefNumber": "1515371", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Currency translation does not work", "RefUrl": "/notes/1515371 "}, {"RefNumber": "1522372", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Performance optimization for processing of query elements", "RefUrl": "/notes/1522372 "}, {"RefNumber": "1547391", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data when node is expanded", "RefUrl": "/notes/1547391 "}, {"RefNumber": "1517833", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Olap reftable has an unsupported type", "RefUrl": "/notes/1517833 "}, {"RefNumber": "1535548", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "WJR:THJ - Subsequent correction to note 1464244", "RefUrl": "/notes/1535548 "}, {"RefNumber": "1551352", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Various minor issues in the Planning Modeler", "RefUrl": "/notes/1551352 "}, {"RefNumber": "1538109", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error when PARTITIONMODE 2 or 3", "RefUrl": "/notes/1538109 "}, {"RefNumber": "1522280", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "User-friendliness of data flow copy", "RefUrl": "/notes/1522280 "}, {"RefNumber": "1532061", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Dump in process chains for sequence during saving", "RefUrl": "/notes/1532061 "}, {"RefNumber": "1547290", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "DTP is not generated -> two source systems/IOBJ target", "RefUrl": "/notes/1547290 "}, {"RefNumber": "1553368", "RefComponent": "BW-WHM", "RefTitle": "Lockwait situation for table NRIV", "RefUrl": "/notes/1553368 "}, {"RefNumber": "1546399", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error in program CL_RSR and form GET_CHANM-02", "RefUrl": "/notes/1546399 "}, {"RefNumber": "1543704", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "CompositeProvider corrections", "RefUrl": "/notes/1543704 "}, {"RefNumber": "1548170", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "RRMX launches Excel, but the connection is not established", "RefUrl": "/notes/1548170 "}, {"RefNumber": "1522770", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "New Master Data Deletion dumps on MS SQL Server", "RefUrl": "/notes/1522770 "}, {"RefNumber": "1552573", "RefComponent": "BW-BCT-FI-GL", "RefTitle": "Query with virtual provider shows incorrect data", "RefUrl": "/notes/1552573 "}, {"RefNumber": "1537931", "RefComponent": "BW-BEX", "RefTitle": "MP + non-cumulative and multiple assgmt of characteristics", "RefUrl": "/notes/1537931 "}, {"RefNumber": "1512499", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "RSPC_DISPLAY_JOBS aborts when the report has syntax errors.", "RefUrl": "/notes/1512499 "}, {"RefNumber": "1552741", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Performance Optimization :SID Flag Updates - Follow-up Note", "RefUrl": "/notes/1552741 "}, {"RefNumber": "1550270", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "DTP-LOG; no LOG for authorization problems", "RefUrl": "/notes/1550270 "}, {"RefNumber": "1522519", "RefComponent": "BW-BEX", "RefTitle": "SAP_NOT_NULL_REPAIR_ORACLE", "RefUrl": "/notes/1522519 "}, {"RefNumber": "1553334", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Msg. R3 416 instead of a relevant msg. during direct access", "RefUrl": "/notes/1553334 "}, {"RefNumber": "1554217", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Save of filter is not cancelled in case of CTS error", "RefUrl": "/notes/1554217 "}, {"RefNumber": "1527808", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BexAnalyzer:Jump to an Transaction freezes Sap GUI window", "RefUrl": "/notes/1527808 "}, {"RefNumber": "1554274", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Clean up class CL_RSBM_LOG_DATAPACKAGE", "RefUrl": "/notes/1554274 "}, {"RefNumber": "1554150", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (Support Package 26): Dump during migration", "RefUrl": "/notes/1554150 "}, {"RefNumber": "1552825", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "SP26:DTP:Filter in Content Version of DTP is deleted", "RefUrl": "/notes/1552825 "}, {"RefNumber": "1554098", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "GETWA_NOT_ASSIGNED when you execute RSPC_METADATA_CLEANUP", "RefUrl": "/notes/1554098 "}, {"RefNumber": "1553201", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.11SP07:RSAR_PSA_NEWDS_MAPPING_CHECK considers inactive PSA", "RefUrl": "/notes/1553201 "}, {"RefNumber": "1481835", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Performance of INTERSECT() function or Incorrect data", "RefUrl": "/notes/1481835 "}, {"RefNumber": "1552001", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "A query on a MultiProvider terminates", "RefUrl": "/notes/1552001 "}, {"RefNumber": "1550723", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "System error in program CL_RSR and form GET_COB_PRO-01-", "RefUrl": "/notes/1550723 "}, {"RefNumber": "1534680", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Hierarchy date variable is not filled when you navigate", "RefUrl": "/notes/1534680 "}, {"RefNumber": "1549224", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Request generation also reads deleted requests", "RefUrl": "/notes/1549224 "}, {"RefNumber": "1549040", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P26:DSO:Deleting activation queue when AQ empty", "RefUrl": "/notes/1549040 "}, {"RefNumber": "1550918", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P26:BAPI_IPAK_CHANGE undoes field for warning handling", "RefUrl": "/notes/1550918 "}, {"RefNumber": "1546975", "RefComponent": "BW-BEX-OT", "RefTitle": "Internal <-> external conversion to DECFLOAT34 key figures", "RefUrl": "/notes/1546975 "}, {"RefNumber": "1537570", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1537570 "}, {"RefNumber": "1533115", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Message: \"TREX call with check_n and FEMS_N\"", "RefUrl": "/notes/1533115 "}, {"RefNumber": "1549726", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2 and form FAC_VARIABLES-03-", "RefUrl": "/notes/1549726 "}, {"RefNumber": "1514902", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "7.30 Patch 2: Bugs in DataServices integration", "RefUrl": "/notes/1514902 "}, {"RefNumber": "1551463", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Optimization in the data manager", "RefUrl": "/notes/1551463 "}, {"RefNumber": "1522691", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Performance Optimization : SID Flag Updates", "RefUrl": "/notes/1522691 "}, {"RefNumber": "1547453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Explain: X message for explain for formula with variables", "RefUrl": "/notes/1547453 "}, {"RefNumber": "1551354", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26:ASSERTION_FAILED in single rule test(Infoset as Source)", "RefUrl": "/notes/1551354 "}, {"RefNumber": "1520825", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "SP26:Introducing debug in RSNDI APIs for MD operations", "RefUrl": "/notes/1520825 "}, {"RefNumber": "1551289", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:TRF:After import: Error message unclear, TRF missing", "RefUrl": "/notes/1551289 "}, {"RefNumber": "1551005", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:After-import:T versions are no longer deleted", "RefUrl": "/notes/1551005 "}, {"RefNumber": "1509505", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DTP:STATMAN:Dump occurs after reducing a request list", "RefUrl": "/notes/1509505 "}, {"RefNumber": "1509752", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P25:DSO:Activation in DSO terminates with RSM1 362", "RefUrl": "/notes/1509752 "}, {"RefNumber": "1509871", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DB connect & old DataSource:Msg. 'TEXT_040' in caller 20", "RefUrl": "/notes/1509871 "}, {"RefNumber": "1512782", "RefComponent": "BW-WHM-AWB", "RefTitle": "P25: AWB: Monitor display for all DataStore objects", "RefUrl": "/notes/1512782 "}, {"RefNumber": "1526835", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:REQARCH: Corr. in report RSREQARCH_REMO_INCONSISTENCIES", "RefUrl": "/notes/1526835 "}, {"RefNumber": "1527025", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:Manage:Complete data target deletion and request list", "RefUrl": "/notes/1527025 "}, {"RefNumber": "1529330", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Deadlock when DSO contents are deleted in parallel", "RefUrl": "/notes/1529330 "}, {"RefNumber": "1533324", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "No 'Standard Text' for hierarchy node is displayed", "RefUrl": "/notes/1533324 "}, {"RefNumber": "1539990", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate w/time hierarchy is compressed (2)", "RefUrl": "/notes/1539990 "}, {"RefNumber": "1548242", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW cube indexes incorrect if no time char. in aggregate", "RefUrl": "/notes/1548242 "}, {"RefNumber": "1544434", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Properties of query elements are not evaluated correctly", "RefUrl": "/notes/1544434 "}, {"RefNumber": "1550187", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Termination in CL_RSDDB_INDEX_M and BUILD_TH_JOIN", "RefUrl": "/notes/1550187 "}, {"RefNumber": "1524315", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE terminates on write-optimized DSO", "RefUrl": "/notes/1524315 "}, {"RefNumber": "1534748", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Quantity conversion does not take place", "RefUrl": "/notes/1534748 "}, {"RefNumber": "1548259", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORACLE: Compression of UNIQUE INDEX of PSA tables", "RefUrl": "/notes/1548259 "}, {"RefNumber": "1548635", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Corrections to the time service routines in BW", "RefUrl": "/notes/1548635 "}, {"RefNumber": "1539903", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Optimization of master data access to Teradata", "RefUrl": "/notes/1539903 "}, {"RefNumber": "1547708", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: some Key Figures are not visible", "RefUrl": "/notes/1547708 "}, {"RefNumber": "1547667", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Error DTP:No LOGSYS with p_r_request->get_logsys", "RefUrl": "/notes/1547667 "}, {"RefNumber": "1547838", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:DWB: Search for DTPs in workbench is slow", "RefUrl": "/notes/1547838 "}, {"RefNumber": "1545971", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Incorrect value for property 'MEMBER_UNIQUE_NAME'", "RefUrl": "/notes/1545971 "}, {"RefNumber": "1541216", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1541216 "}, {"RefNumber": "1544249", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Dump DYNPRO_FIELD_CONVERSION in screen 104", "RefUrl": "/notes/1544249 "}, {"RefNumber": "1545663", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:WO-DSO:RSBX_BIW_GET_ODSDATA:Myself extraction: Selection", "RefUrl": "/notes/1545663 "}, {"RefNumber": "1545857", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:PC:Process chain step set t.red when request deleted", "RefUrl": "/notes/1545857 "}, {"RefNumber": "1546906", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Enqueue: Minor improvements for creation of request", "RefUrl": "/notes/1546906 "}, {"RefNumber": "1540666", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Screen 101 of program SAPLRSSM_PROCESS delivered again", "RefUrl": "/notes/1540666 "}, {"RefNumber": "1535008", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP: Delta DTP: Transport with BEx variables or routines", "RefUrl": "/notes/1535008 "}, {"RefNumber": "1535514", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:DSP:PC:Truncate:Locked act. queue end with act. red", "RefUrl": "/notes/1535514 "}, {"RefNumber": "1545818", "RefComponent": "BW-BEX", "RefTitle": "Orange", "RefUrl": "/notes/1545818 "}, {"RefNumber": "1545834", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Authorization check on S_RFC_ADM", "RefUrl": "/notes/1545834 "}, {"RefNumber": "1535607", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error in RSRV text on InfoCube with referencing key figure", "RefUrl": "/notes/1535607 "}, {"RefNumber": "1545450", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2, form LRECH_F_CHFP_01-01-", "RefUrl": "/notes/1545450 "}, {"RefNumber": "1544520", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No Authorization with hierarchy authorization + interval", "RefUrl": "/notes/1544520 "}, {"RefNumber": "1542810", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Consulting:MD Update when key fields are mapped", "RefUrl": "/notes/1542810 "}, {"RefNumber": "1530274", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjustments to the CompositeProvider", "RefUrl": "/notes/1530274 "}, {"RefNumber": "1530319", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01SP9:New PSA Del Var ignores PSA of Exp DS of T-DSO/W-DSO", "RefUrl": "/notes/1530319 "}, {"RefNumber": "1540008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems in the context of SAP Business ByDesign - Part 2", "RefUrl": "/notes/1540008 "}, {"RefNumber": "1537125", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "SP26:Transaction RSH1 doesnt work correctly in display mode", "RefUrl": "/notes/1537125 "}, {"RefNumber": "1539445", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:DTP:DataSource extractor:PSA extractor:Join/cleanup", "RefUrl": "/notes/1539445 "}, {"RefNumber": "1540186", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: RSSM_EXPAND_REQUESTLIST: Expansion in wrong order", "RefUrl": "/notes/1540186 "}, {"RefNumber": "1540733", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Data flow migration: Name of InfoSource is lost", "RefUrl": "/notes/1540733 "}, {"RefNumber": "1540583", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Missing restrictions via the label", "RefUrl": "/notes/1540583 "}, {"RefNumber": "1534298", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections in the CompositeProvider environment", "RefUrl": "/notes/1534298 "}, {"RefNumber": "1539178", "RefComponent": "BW-WHM-DST", "RefTitle": "P26:RSBX_BIW_GET_ODSDATA:Memory overflow:For all entries", "RefUrl": "/notes/1539178 "}, {"RefNumber": "1536212", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "OLAP cache and currency translation", "RefUrl": "/notes/1536212 "}, {"RefNumber": "1539601", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Exception \"NAME_ERROR\" in the class CL_RSMD_RS_TREX_QUERY", "RefUrl": "/notes/1539601 "}, {"RefNumber": "1521436", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "BAdI SMOD_RSR00004 is called twice during navigation", "RefUrl": "/notes/1521436 "}, {"RefNumber": "1538876", "RefComponent": "BW-BEX", "RefTitle": "BRAIN X299 in class CL_RSR_CHABIT; form SET_BIT1-02-", "RefUrl": "/notes/1538876 "}, {"RefNumber": "1531453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and constant selection", "RefUrl": "/notes/1531453 "}, {"RefNumber": "1528875", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Incorrect authorization check f. jump destination activation", "RefUrl": "/notes/1528875 "}, {"RefNumber": "1522393", "RefComponent": "BW-BEX", "RefTitle": "Transactional InfoCube: Follow-on note to Note 1391793", "RefUrl": "/notes/1522393 "}, {"RefNumber": "1517543", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "RSRCACHE Faults in BW730", "RefUrl": "/notes/1517543 "}, {"RefNumber": "1528864", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Query generation generates syntax error for non-cml. cubes", "RefUrl": "/notes/1528864 "}, {"RefNumber": "1535576", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP26: NOP key figures in transformations", "RefUrl": "/notes/1535576 "}, {"RefNumber": "1536334", "RefComponent": "BW-BEX", "RefTitle": "DTP loading; archive areas are not checked correctly", "RefUrl": "/notes/1536334 "}, {"RefNumber": "1535006", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: APO requests: Type of Data Update field empty", "RefUrl": "/notes/1535006 "}, {"RefNumber": "1527844", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "%_HINTS ORACLE not built correct in the code LRRSIF03", "RefUrl": "/notes/1527844 "}, {"RefNumber": "1530255", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW selective deletion w/ very large single record conditions", "RefUrl": "/notes/1530255 "}, {"RefNumber": "1529540", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "BW InfoObject with RDA, navigation attributes and aggregates", "RefUrl": "/notes/1529540 "}, {"RefNumber": "1534209", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Overflow dump in report SAP_INFOCUBE_DESIGNS (2)", "RefUrl": "/notes/1534209 "}, {"RefNumber": "1533694", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26:DTP:Red, updated DP cannot be repaired", "RefUrl": "/notes/1533694 "}, {"RefNumber": "1530165", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P26:SDL:PC:Enabling user entries for InfoPackages", "RefUrl": "/notes/1530165 "}, {"RefNumber": "1526103", "RefComponent": "BW-WHM-DBA-MPRO", "RefTitle": "MultiProvider activation abends with x message", "RefUrl": "/notes/1526103 "}, {"RefNumber": "1533378", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BExAnalyzer: Saving of Workbooks by Multiple Users", "RefUrl": "/notes/1533378 "}, {"RefNumber": "1532624", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: REQARCH: Endless loop in write report", "RefUrl": "/notes/1532624 "}, {"RefNumber": "1533243", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CL_RSMD_RS_TREX_QUERY->IF_RSMD_RS_BUILD_QUERY~READ_DATA", "RefUrl": "/notes/1533243 "}, {"RefNumber": "1531519", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Subsequent correction to Note 1385580 and Note 1431226", "RefUrl": "/notes/1531519 "}, {"RefNumber": "1529080", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "7.01 SP9:PSA/CLG deletion variant save doesnt work correctly", "RefUrl": "/notes/1529080 "}, {"RefNumber": "1532596", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Explorer: 2920:multiprovider schema is not consistent;", "RefUrl": "/notes/1532596 "}, {"RefNumber": "1532223", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Search based on calendar month/year does not work", "RefUrl": "/notes/1532223 "}, {"RefNumber": "1531920", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "Error during InfoPackage import for non-existing target", "RefUrl": "/notes/1531920 "}, {"RefNumber": "1529488", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.30 (SP02) Falsche Meldung", "RefUrl": "/notes/1529488 "}, {"RefNumber": "1530803", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error CX_SY_DYN_TABLE_ILL_COMP_VAL; in  _SORT_X", "RefUrl": "/notes/1530803 "}, {"RefNumber": "1530546", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Filling of formula variable via report-report interface", "RefUrl": "/notes/1530546 "}, {"RefNumber": "1530271", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception condition \"INCONSISTENCY\" raised", "RefUrl": "/notes/1530271 "}, {"RefNumber": "1529720", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No data for calculated member and metadata reference", "RefUrl": "/notes/1529720 "}, {"RefNumber": "1529152", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Runtime error in MDXTEST with missing authorization", "RefUrl": "/notes/1529152 "}, {"RefNumber": "1529440", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Manage: No text for new DataSources in request list", "RefUrl": "/notes/1529440 "}, {"RefNumber": "1527930", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination in BW aggregates with COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/1527930 "}, {"RefNumber": "1527868", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BW InfoCube with BIA displays compressed aggregates", "RefUrl": "/notes/1527868 "}, {"RefNumber": "1529455", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Stammdaten Optimierungen", "RefUrl": "/notes/1529455 "}, {"RefNumber": "1526833", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P26: RDA: No F1 help in real-time fields in the scheduler", "RefUrl": "/notes/1526833 "}, {"RefNumber": "1520748", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (SP25): \"Time characteristic\" rule type selectable", "RefUrl": "/notes/1520748 "}, {"RefNumber": "1528374", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: System error GET_PRPTY_VALUE-02-", "RefUrl": "/notes/1528374 "}, {"RefNumber": "1528293", "RefComponent": "BW-WHM-DST", "RefTitle": "P26: Dump in report RSSM_SM50_ALT", "RefUrl": "/notes/1528293 "}, {"RefNumber": "1528332", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0(SP26) Fehlerhafte Anzeige InfoSource", "RefUrl": "/notes/1528332 "}, {"RefNumber": "1525546", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Destination transport RC = 8", "RefUrl": "/notes/1525546 "}, {"RefNumber": "1526867", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Query does not return navigation attribute in MultiProvider", "RefUrl": "/notes/1526867 "}, {"RefNumber": "1527011", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_GET_STREAMINFO & fixed fiscal variant", "RefUrl": "/notes/1527011 "}, {"RefNumber": "1527728", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P26: DTP: Error handler: Endless loop during deletion", "RefUrl": "/notes/1527728 "}, {"RefNumber": "1527726", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "730:New DS & hierarchy sel. w/o existing hierarchy headers", "RefUrl": "/notes/1527726 "}, {"RefNumber": "1527063", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data with constant selection and compounding", "RefUrl": "/notes/1527063 "}, {"RefNumber": "1526821", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Hierarchy without unassigned node has incorrect totals", "RefUrl": "/notes/1526821 "}, {"RefNumber": "1526618", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Fields in PSA have no text", "RefUrl": "/notes/1526618 "}, {"RefNumber": "1526857", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.0 (SP26): Incorrect display of destination", "RefUrl": "/notes/1526857 "}, {"RefNumber": "1526599", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Wrong values filtered with manual input on the Var. Screen", "RefUrl": "/notes/1526599 "}, {"RefNumber": "1516988", "RefComponent": "BW-WHM-DST", "RefTitle": "SP26:Error while displaying PSA for 3.X datasource", "RefUrl": "/notes/1516988 "}, {"RefNumber": "1431163", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Incorrect date filled when wrong format used for date vars", "RefUrl": "/notes/1431163 "}, {"RefNumber": "1524724", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI GET_MEMBERS, duplicate display of Self member", "RefUrl": "/notes/1524724 "}, {"RefNumber": "1525723", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Program termination in program RSPC_LOG_DELETE", "RefUrl": "/notes/1525723 "}, {"RefNumber": "1525635", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Additional index for RSZWVIEW table", "RefUrl": "/notes/1525635 "}, {"RefNumber": "1524281", "RefComponent": "BW-BEX-ET-OSD", "RefTitle": "BETA_NW73_BW - URL for query or query view is not created", "RefUrl": "/notes/1524281 "}, {"RefNumber": "1523946", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "BPC has problems with the ABAP process type", "RefUrl": "/notes/1523946 "}, {"RefNumber": "1525419", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error when BW aggregate with time hierarchy is compressed", "RefUrl": "/notes/1525419 "}, {"RefNumber": "1524797", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in MultiProvider hint buffer", "RefUrl": "/notes/1524797 "}, {"RefNumber": "1523876", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Sequence of flexible InfoObjects for hierarchy file", "RefUrl": "/notes/1523876 "}, {"RefNumber": "1523832", "RefComponent": "BW-PLA-IP", "RefTitle": "Integrated planning: Data is not current", "RefUrl": "/notes/1523832 "}, {"RefNumber": "1523116", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The factory calendar does not exist", "RefUrl": "/notes/1523116 "}, {"RefNumber": "1523789", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter maintenance:Switch to display mode:Termination occurs", "RefUrl": "/notes/1523789 "}, {"RefNumber": "1514349", "RefComponent": "BW-BEX", "RefTitle": "Adjustments for CompositeProvider", "RefUrl": "/notes/1514349 "}, {"RefNumber": "1514838", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Characteristic relationship cannot be selected", "RefUrl": "/notes/1514838 "}, {"RefNumber": "1513767", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter maintenance: \"Cancel\" when switching to display mode", "RefUrl": "/notes/1513767 "}, {"RefNumber": "1515291", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Sequence maintenance: Errors on variable screen", "RefUrl": "/notes/1515291 "}, {"RefNumber": "1516590", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter maintenance: SAPLRRI2, GET_QUERY_PROP_SRDATE-01-", "RefUrl": "/notes/1516590 "}, {"RefNumber": "1519646", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Creating filter: More than one filter is generated", "RefUrl": "/notes/1519646 "}, {"RefNumber": "1512483", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Problem with new rows in input template", "RefUrl": "/notes/1512483 "}, {"RefNumber": "1522480", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "NW BW 7.30 Dump bei Navigation", "RefUrl": "/notes/1522480 "}, {"RefNumber": "1522701", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: Technisches Merkmal 0REQUEST in InfoProvider-View", "RefUrl": "/notes/1522701 "}, {"RefNumber": "1522356", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "BAPI_ISOURCE_HI_T_CREATE does not work for 7.x DataSources", "RefUrl": "/notes/1522356 "}, {"RefNumber": "1517151", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "M version text is displayed in both change & display mode", "RefUrl": "/notes/1517151 "}, {"RefNumber": "1521799", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Structures and non empty", "RefUrl": "/notes/1521799 "}, {"RefNumber": "1521154", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: bXML flattening: Incorrect values for compound char.", "RefUrl": "/notes/1521154 "}, {"RefNumber": "1521023", "RefComponent": "BW-SYS-DB-MSS", "RefTitle": "SP25:Full table scan during master data update in MSSQL", "RefUrl": "/notes/1521023 "}, {"RefNumber": "1521055", "RefComponent": "BW-WHM-DST-DFG", "RefTitle": "Estimated time for calculating default value is inaccurate", "RefUrl": "/notes/1521055 "}, {"RefNumber": "1520878", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index: Activation does not generate object in BWA", "RefUrl": "/notes/1520878 "}, {"RefNumber": "1520064", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Incorrect data for calculated measures with AVG or IIF", "RefUrl": "/notes/1520064 "}, {"RefNumber": "1516891", "RefComponent": "BW-EI-APD", "RefTitle": "APD: Authorization check S_TABU_DIS", "RefUrl": "/notes/1516891 "}, {"RefNumber": "1519816", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index: Error messages when loading", "RefUrl": "/notes/1519816 "}, {"RefNumber": "1518017", "RefComponent": "BW-WHM", "RefTitle": "SP25:Dump in RSRV test Compare Number Range and Maximum SIDs", "RefUrl": "/notes/1518017 "}, {"RefNumber": "1515434", "RefComponent": "BW-BEX-OT", "RefTitle": "RSRT: Technical information: Resize leads to initial screen", "RefUrl": "/notes/1515434 "}, {"RefNumber": "1519320", "RefComponent": "BW-PLA-BPS", "RefTitle": "Endless loop while using document copy function", "RefUrl": "/notes/1519320 "}, {"RefNumber": "1518164", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Exception NODE_NOT_AUTHORIZED and method _SELF_GET", "RefUrl": "/notes/1518164 "}, {"RefNumber": "1518745", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO explorer: Incorrect authorization check for queries", "RefUrl": "/notes/1518745 "}, {"RefNumber": "1518793", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analytical index: Runtime error ASSIGN_TYPE_CONFLICT", "RefUrl": "/notes/1518793 "}, {"RefNumber": "1518659", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "NW BW 7.0 (SP25): All changes lost after cancelation", "RefUrl": "/notes/1518659 "}, {"RefNumber": "1515897", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect average runtime in query statistics", "RefUrl": "/notes/1515897 "}, {"RefNumber": "1518383", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "No source system in monitor tree", "RefUrl": "/notes/1518383 "}, {"RefNumber": "1517312", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: BAPI_MDPROVIDER_RESOLVE_NAMES", "RefUrl": "/notes/1517312 "}, {"RefNumber": "1517320", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Not able to open WB when SU01 parameter STATANALYZE is set", "RefUrl": "/notes/1517320 "}, {"RefNumber": "1514244", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in class CL_RSR_LFMM; GET_FISCV_SID-01-", "RefUrl": "/notes/1514244 "}, {"RefNumber": "1516706", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DM:STATMAN: Data mart icons in display of request list", "RefUrl": "/notes/1516706 "}, {"RefNumber": "1515964", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:SDL:USAB:F4 help of BEx variables improved again", "RefUrl": "/notes/1515964 "}, {"RefNumber": "1516179", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25; DTP: No lock for parallel update of DM pointers", "RefUrl": "/notes/1516179 "}, {"RefNumber": "1515194", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:Manage:Load request becomes green if monitor set by QM", "RefUrl": "/notes/1515194 "}, {"RefNumber": "1515614", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:Expand all pushbutton on detail tabpage not working", "RefUrl": "/notes/1515614 "}, {"RefNumber": "1515200", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:Error stack filled - no error DTP", "RefUrl": "/notes/1515200 "}, {"RefNumber": "1512989", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P25:PC:SDL:Loading process remains yellow during termination", "RefUrl": "/notes/1512989 "}, {"RefNumber": "1514722", "RefComponent": "BW-WHM-DST", "RefTitle": "P25: Change run: Start time displays \"000000\" during run", "RefUrl": "/notes/1514722 "}, {"RefNumber": "1515988", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "System error in program CL_RSR and form GET_CHANM-01-", "RefUrl": "/notes/1515988 "}, {"RefNumber": "1516139", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Context menu entry: Maintain BW Accelerator index in RSA1", "RefUrl": "/notes/1516139 "}, {"RefNumber": "1513465", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Partitioning extension leads to loss of data (2)", "RefUrl": "/notes/1513465 "}, {"RefNumber": "1513815", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Read mode and cache mode of default query cannot be changed", "RefUrl": "/notes/1513815 "}, {"RefNumber": "1515330", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "Division by Zero error during SPO Activation", "RefUrl": "/notes/1515330 "}, {"RefNumber": "1515559", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Endless extraction for fetch size larger than 999999", "RefUrl": "/notes/1515559 "}, {"RefNumber": "1514733", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Flattening: Header text is missing for default measure", "RefUrl": "/notes/1514733 "}, {"RefNumber": "1514446", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW change run monitor: Display problem for no. of BIAs", "RefUrl": "/notes/1514446 "}, {"RefNumber": "1513261", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "OLE DB for OLAP: Problems with descendants and level", "RefUrl": "/notes/1513261 "}, {"RefNumber": "1514208", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P25:PC:Priority dialog box has English heading in German", "RefUrl": "/notes/1514208 "}, {"RefNumber": "1514275", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "730:SDL:Generic delta:Full call of DATA_PULL", "RefUrl": "/notes/1514275 "}, {"RefNumber": "1514404", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BO Explorer: \"Attribute not defined\" for text columns", "RefUrl": "/notes/1514404 "}, {"RefNumber": "1513909", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "formal code corrections on OLAP cache component", "RefUrl": "/notes/1513909 "}, {"RefNumber": "1514007", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy is evaluated for the incorrect key date", "RefUrl": "/notes/1514007 "}, {"RefNumber": "1513252", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Status of requests for deleted systems is unknown", "RefUrl": "/notes/1513252 "}, {"RefNumber": "1513125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Query generation for non-cumulative InfoCube: Syntax errors", "RefUrl": "/notes/1513125 "}, {"RefNumber": "1512490", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P25:SDL:PC:Attempt to process control in background", "RefUrl": "/notes/1512490 "}, {"RefNumber": "1512540", "RefComponent": "BW-PLA-BPS", "RefTitle": "CALL_FUNCTION_CONFLICT_LENG when planning area is called", "RefUrl": "/notes/1512540 "}, {"RefNumber": "1512777", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Msg.: 'The Cube Schema XML is not valid: Invalid datatype'", "RefUrl": "/notes/1512777 "}, {"RefNumber": "1504123", "RefComponent": "BW-WHM-DST", "RefTitle": "P25:DM:DTP:WO-DSO:Delta extraction from WO-DSO:Missing req.", "RefUrl": "/notes/1504123 "}, {"RefNumber": "1508938", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP: Cancelling if there are too many selection fields", "RefUrl": "/notes/1508938 "}, {"RefNumber": "1510617", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP:CTS: Incorrect call sequence when DTP is activated", "RefUrl": "/notes/1510617 "}, {"RefNumber": "1511222", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP: Dump in CL_RSBK_REQUEST_STATE=>GET_OBJ_REF_TSTATE", "RefUrl": "/notes/1511222 "}, {"RefNumber": "1511916", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P25:DTP: Hard termination if DTP does not exist", "RefUrl": "/notes/1511916 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "702", "To": "702", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}