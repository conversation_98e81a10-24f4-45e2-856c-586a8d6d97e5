{"Request": {"Number": "41300", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 262, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000106722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000041300?language=E&token=34902A0EC308AB6C58464FCB6307E556"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000041300", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000041300/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "41300"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.02.2000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DBI"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB Independent Database Interface"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB Independent Database Interface", "value": "BC-DB-DBI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DBI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "41300 - Table DBTABPRT is very large"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The table DBTABPRT (R/3-Releases up to 3.1I) or table DBTABLOG is very large.<br />How can data be removed?<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>rec/client, DBTABPRT, DBTABLOG<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Table changes are logged in table DBTABPRT or DBTABLOG (see Note 1916).<br />If a lot of changes are made and logging is activated, table DBTABPRT or DBTABLOG can become very large.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The data is not deleted automatically from the table. Data which you do no longer need, can either be deleted or if you want to access it at a later date it can be archived (as of Release 4.0A):</p> <UL><LI>You can delete the data with a report (see in section \"Corrections\" report ZDELTPRT).<br />As of Release 4.5A you can use Transaction SCU3 to start a deletion program (Menu \"Edit -&gt; Change Docs -&gt; Delete\").<br /><br />For some databases, the physical storage space is only released after a reorganization (Note 35952).</LI></UL> <p><br />If you want to delete all data from the table log, you can do this much faster at database level:<br /><br />In general, you can do this for any database from the R/3 System with the database utility (SE14): Drop the table on database level and recreate it. (As the structure information already exists, this is done automatically with SE14).<br />As of Release 4.0A there is an archiving function available on the basis of the Archive Development Kit.<br />With Release 4.0 for this start Transaction SARA with object name \"BC_DBLOGS\".<br />As of Release 4.5A in Transaction SCU3 use the menu path \"Edit -&gt; Change Docs -&gt; Delete\".<br />Note: You can easily include archived change docs in your Log evaluation by setting the radio button \"Archived logs also evaluate\" on the initial screen (report RSVTPROT).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I073741)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023441)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000041300/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000041300/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041300/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041300/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041300/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041300/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041300/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041300/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041300/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "991735", "RefComponent": "IS-M", "RefTitle": "Table DBTABLOG increases considerably", "RefUrl": "/notes/991735"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "52542", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/52542"}, {"RefNumber": "490095", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Additional information on upgrading to SAP Web AS 6.20", "RefUrl": "/notes/490095"}, {"RefNumber": "35952", "RefComponent": "BC-DB-DBI", "RefTitle": "Client deleted, space still filled in database", "RefUrl": "/notes/35952"}, {"RefNumber": "1916", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Logging of table changes in R/3", "RefUrl": "/notes/1916"}, {"RefNumber": "1524924", "RefComponent": "BC-CCM-MON", "RefTitle": "The table logging issue for ALPFASSIGN", "RefUrl": "/notes/1524924"}, {"RefNumber": "139418", "RefComponent": "BC-SEC", "RefTitle": "Logging of user actions (ABAP server)", "RefUrl": "/notes/139418"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1916", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Logging of table changes in R/3", "RefUrl": "/notes/1916 "}, {"RefNumber": "1524924", "RefComponent": "BC-CCM-MON", "RefTitle": "The table logging issue for ALPFASSIGN", "RefUrl": "/notes/1524924 "}, {"RefNumber": "490095", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Additional information on upgrading to SAP Web AS 6.20", "RefUrl": "/notes/490095 "}, {"RefNumber": "991735", "RefComponent": "IS-M", "RefTitle": "Table DBTABLOG increases considerably", "RefUrl": "/notes/991735 "}, {"RefNumber": "139418", "RefComponent": "BC-SEC", "RefTitle": "Logging of user actions (ABAP server)", "RefUrl": "/notes/139418 "}, {"RefNumber": "35952", "RefComponent": "BC-DB-DBI", "RefTitle": "Client deleted, space still filled in database", "RefUrl": "/notes/35952 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "22A", "To": "22J", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000041300/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}