{"Request": {"Number": "547773", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 311, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002685122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000547773?language=E&token=CAFB33D50A7E417079A96EC0F924D9EE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000547773", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000547773/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "547773"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.02.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-CEX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Customer Extensions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Customer Extensions", "value": "BC-DWB-CEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-CEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "547773 - SPAU: interface method names are truncated"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The names of modified methods as well as methods overwritten as a result of the upgrade (ABAP Objects) are shortened to 30 characters in transaction SPAU.<br /><br />The objects appear under:</p> <UL><LI>\"Without Modification Assistant\" with a red stop sign or a green question mark</LI></UL> <UL><LI>or inadvertently under \"Deleted objects\".</LI></UL> <p><br />Double-clicking the abbreviated method name results in the \"Component ... does not exist\" message.<br />Clicking on the traffic light for deleted objects results in a message indicating that modifications have already been adjusted.<br /><br /><STRONG>Caution:</STRONG><br />Deleted objects are not displayed in transaction SPAU during the standard selection. It is therefore very easy to overlook these.<br /><br />You must select the \"Deleted objects\" checkbox in the second tab (\"Reset objects\") on the SPAU selection screen in order to display deleted objects.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SPAU, SE24</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a program error when creating entries in the modification log.<br />It only takes effect for certain ABAP objects and interface method names.<br />If names composed as follows<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;name_of_interface&gt;~&lt;name_of_method&gt;<br />consist of more than 30 characters, the name will be truncated after the 30th character.<br />These types of entries may be displayed under the \"Without Modification Assistant\" and \"Deleted objects\" of the SPAU display but never under \"With Modification Assistant\".<br /><br />Truncated entries in the modification log may only appear in Release 4.6. However, these truncated entries may also exist in the modification log of a system with a higher release if a release upgrade was performed for the system or if modification log entries were imported from other systems.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The attached correction instructions only correct the writing of future entries. Unfortunately, truncated method names cannot be reproduced automatically because the information is insufficient for this.<br /><br />Procedure for comparing the affected methods:</p> <OL>1. Find the correct method.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To do this, use the search/display functions in the Class Builder as well as the version management. <OL>2. Manually compare the imported version with the modified version</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Both versions may be accessed via the version management. <OL>3. Delete the old modification logs (with method names abbreviated to 30 characters) using the \"Delete Modification Log\" function in transaction SE95_Util.</OL> <p><br />Procedure for resetting to the original:<br /><br />Use the \"Delete Modification Log\" function in transaction SE95_UTIL to delete the modification logs.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DWB-TOO-C<PERSON> (Class Builder)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020815)"}, {"Key": "Processor                                                                                           ", "Value": "D017401"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000547773/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000547773/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000547773/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000547773/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000547773/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000547773/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000547773/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000547773/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000547773/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861"}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513"}, {"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "913848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913848"}, {"RefNumber": "905029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905029"}, {"RefNumber": "890202", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/890202"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826487"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "435140", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to Basis 4.6D (APO 3.1x", "RefUrl": "/notes/435140"}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371"}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "335029", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to the upgrade to Basis 4.6D (NDI Upgrade)", "RefUrl": "/notes/335029"}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622"}, {"RefNumber": "302309", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions Workplace Server Upgrade to Rel. 2.10/2.11", "RefUrl": "/notes/302309"}, {"RefNumber": "173814", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages for Release 4.6", "RefUrl": "/notes/173814"}, {"RefNumber": "1293744", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade Systems on SAP NetWeaver 7.0 EHP1 SR1", "RefUrl": "/notes/1293744"}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071"}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1276895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. about Upgrading to SAP Solution Manager 7.0 EHP1", "RefUrl": "/notes/1276895"}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185"}, {"RefNumber": "1146578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.0 EHP1", "RefUrl": "/notes/1146578"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "1095506", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.1 for banking services from SAP", "RefUrl": "/notes/1095506"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1071404", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071404"}, {"RefNumber": "1061649", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver Process Integration 7.1", "RefUrl": "/notes/1061649"}, {"RefNumber": "1039395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1039395"}, {"RefNumber": "1019585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1019585"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1095506", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.1 for banking services from SAP", "RefUrl": "/notes/1095506 "}, {"RefNumber": "1146578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.0 EHP1", "RefUrl": "/notes/1146578 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1293744", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade Systems on SAP NetWeaver 7.0 EHP1 SR1", "RefUrl": "/notes/1293744 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "1061649", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver Process Integration 7.1", "RefUrl": "/notes/1061649 "}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185 "}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070 "}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677 "}, {"RefNumber": "1276895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. about Upgrading to SAP Solution Manager 7.0 EHP1", "RefUrl": "/notes/1276895 "}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783 "}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322 "}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513 "}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "302309", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions Workplace Server Upgrade to Rel. 2.10/2.11", "RefUrl": "/notes/302309 "}, {"RefNumber": "435140", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to Basis 4.6D (APO 3.1x", "RefUrl": "/notes/435140 "}, {"RefNumber": "335029", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to the upgrade to Basis 4.6D (NDI Upgrade)", "RefUrl": "/notes/335029 "}, {"RefNumber": "173814", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages for Release 4.6", "RefUrl": "/notes/173814 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622 "}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717 "}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B46", "URL": "/supportpackage/SAPKB46B46"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B47", "URL": "/supportpackage/SAPKB46B47"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C37", "URL": "/supportpackage/SAPKB46C37"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C38", "URL": "/supportpackage/SAPKB46C38"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D26", "URL": "/supportpackage/SAPKB46D26"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D27", "URL": "/supportpackage/SAPKB46D27"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61026", "URL": "/supportpackage/SAPKB61026"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62012", "URL": "/supportpackage/SAPKB62012"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP137", "SupportPackagePatch": "000137", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP137", "SupportPackagePatch": "000137", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP137", "SupportPackagePatch": "000137", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP137", "SupportPackagePatch": "000137", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 32-BIT", "SupportPackage": "SP2356", "SupportPackagePatch": "002356", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007105&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D 32-BIT", "SupportPackage": "SP2356", "SupportPackagePatch": "002356", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200002427&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 64-BIT", "SupportPackage": "SP2356", "SupportPackagePatch": "002356", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007108&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D 64-BIT", "SupportPackage": "SP2356", "SupportPackagePatch": "002356", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200002567&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 32-BIT", "SupportPackage": "SP2364", "SupportPackagePatch": "002364", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007105&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D 64-BIT", "SupportPackage": "SP2364", "SupportPackagePatch": "002364", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200002567&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 64-BIT", "SupportPackage": "SP2364", "SupportPackagePatch": "002364", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007108&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 64-BIT", "SupportPackage": "SP2357", "SupportPackagePatch": "002357", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007108&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0000547773/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}