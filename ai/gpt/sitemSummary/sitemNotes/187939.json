{"Request": {"Number": "187939", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 611, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014797702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000187939?language=E&token=736E8E8E3A2FCCA26A19BC749760CF06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000187939", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000187939/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "187939"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 44}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.07.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "187939 - SAP Servicetools Update (RTCCTOOL)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Some service tools are updated regularly with enhanced functions, or they are only available as of higher R/3 Releases.<br />As a preparation for the next TCC service session, you have to check whether the required tools are available in your system and implement them if necessary.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP GoingLive, GoingLive Functional Upgrade, EarlyWatch,<br />RTCCTOOL, ST14, Logistics Monitor, SQLR, SDCC,&#160;&#160;Database monitor</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note is only valid for R/3 Releases up to 3.1I.<br />For all R/3 Releases &gt;= 4.0B, the RTCCTOOL is contained in the addon ST-A/PI and the transport below should not be implemented anymore.<br />The implementation of addon ST-A/PI is described in SAP note 69455.<br /><br />Another restriction:<br />The provedure currently relies on an FTP connection to sapserv3-7.<br />For customers using an internet VPN/SNC connection to sapserv1 or sapserv2, a shortened procedure without RTCCTOOL applies which is described in the Q&amp;A section below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Only for R/3 Releases 3.0D-3.1I:<br />Import the transport SAPKVSDE03 in accordance with Note 13719<br />(or as described in the Q&amp;A below):</p>\r\n<ol><ol>1. Please fetch the transport files KVSDE03.SAP and RVSDE03.SAP from sapservX[X=3..7] under &#126;ftp/general/R3server/abap/note.0187939/.</ol></ol><ol>Alternatively, you can fetch the packed CAR-file SAPKVSDE03.CAR from there, put it into your transport directory /usr/sap/trans and unpack it using the command car -xvf SAPKVSDE03.CAR. The resulting transport files will automatically be put into the cofiles and data subdirectory.</ol><ol>2. Import the transport.</ol><ol><ol>3. Execute report RTCCTOOL \"TCC Servicetools update\" using transaction SA38.</ol></ol>\r\n<p>This report connects to SAPNet R/3 and fetches a list of required transports and notes for your system configuration. It checks whether they are implemented successfully and presents a list of missing transports and notes.<br /><br />The list combines all necessary information, such as:<br />- SAP Notes<br />- Transport logs<br />- Status details (import returncode, files)<br />- Short description and implementation text<br /><br />The \"Serviectransport assistant\" then guides you through the implementation of the transports. It features an automated FTP from sapserv3-7 and provides copy&amp;paste commands.<br />To benefit from the automated file transfer, your server or frontend PC must be able to access sapserv3-7. Use the server where your saprouter is running.<br /><br /><br /><br />Q &amp; A:The Q/A secion is currently worked over to eliminate all references to SAP Releases &gt; 3.1I.<br /><br />Q: when calling report RTCCTOOL only a not very detailed 5 lines&#160;output shows up like for example<br />&#160;&#160;&#160;&#160; \"Existence Text for Required TCC Tools\"<br />&#160;&#160;&#160;&#160; \"Version 03-12-1998\"<br />&#160;&#160;&#160;&#160; \"Note 091488 missing\"<br />&#160;&#160;&#160;&#160;\"Note 116095 missing\"&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;?<br />A: This is a very old version of RTCCTOOL which exists in basis&#160;standard as of 4.6A but which is no longer valid nor helpful. Implement the above transport to get a valid version of RTCCTOOL.<br /><br />Q: after Upgrade ?<br />A: After Upgrade the imported servicetools are lost. You may only find&#160;a very old version of the RTCCTOOL report, which is part of 4.x-<br />standard but not useful anymore, and which does not come up with&#160;'TCC Servicetools Update'.&#160;There may also be DB02 error messages that for some servicetool&#160;tables like STSL* BDL* BAM* the DB object still exists but without&#160;DDIC reference (note 197886).<br />After upgrade to target release &gt;= 4.0B implement the addon&#160;ST-A/PI from note 69455. This addon contains report RTCCTOOL&#160;for releases &gt;= 4.0B. Run report RTCCTOOL from SE38 and&#160;click on 'Addons&amp;Upgr.' and on button 'Procedure after upgrade'.This will clear any DB02 differences for the servicetool tables.<br /><br />Q: warnings like 'table E071: entry in DB is 10 bytes bigger than&#160;in file' ?<br />A: The transport SAPKVSDE03 was issued from a 3.0D system for all&#160;releases from 3.0D-3.1I. The length of certain fields like the&#160;program name differ between the 3.x releases, and therefore this&#160;kind of messages will appear as information with returncode &lt;= 4.&#160;Please ignore all these warnings with returncode &lt; 8, the&#160;transport is still imported succesfully.<br /><br />Q: How to implement the transport ?<br />A: The steps to apply the note are as follows.<br />&#160;&#160; 1. via your host that runs saprouter process, ftp to sapserv3 using<br />&#160;&#160;&#160;&#160;&#160;&#160;user 'ftp' and password 'ftp'<br />&#160;&#160;2. using command cd &lt;directory&gt; to access to the path for the<br />&#160;&#160;&#160;&#160;&#160;&#160;eg. cd general<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cd R3server<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cd abap<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cd note.0187939<br />&#160;&#160;3. command 'bin' to change to binary mode<br />&#160;&#160;4. get KVSDE03.SAP<br />&#160;&#160; 5. get RVSDE03.SAPS<br />&#160;&#160; 6. command 'bye' or close dos window to end session<br />&#160;&#160; 7. place KVSDE03.SAP in /usr/sap/trans/cofiles directory of your R3<br />&#160;&#160; 8. place RVSDE03.SAP in /usr/sap/trans/data directory of your R3<br />&#160;&#160;&#160;&#160;&#160;&#160;(for Unix: take care that the owner is &lt;SID&gt;adm for both files)<br />&#160;&#160; The steps 9. and 10. for import depend on your R/3 release:<br />&#160;&#160;&#160;&#160;9.&#160;&#160;in directory /usr/sap/trans/bin issue command<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;'tp addtobuffer SAPKVSDE03 &lt;SID&gt;' . &lt;SID&gt; being the 3-char<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;system ID.<br />&#160;&#160;&#160;&#160;10. issue command tp import SAPKVSDE03 &lt;SID&gt; to begin importing the<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;transport.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;(for reimport of the transport, add an unconditional mode,&#160;e.g.&#160;&#160;tp import SAPKVSDE03 &lt;SID&gt; U1268 )<br />&#160;&#160; The tp tool should end with returncode 00 or 04 if successfully.&#160;Ignore any warnings rc=4.<br />&#160;&#160;After the successful import of the transport, continue with the&#160;note and run report RTCCTOOL in SA38.<br /><br />Q: 4.6C and connection to SapNet R/3<br />A: In Release 4.6C the following issues may occur:<br />&#160;&#160; - transaction OSS1 may be locked in standard -&gt; unlock it<br />&#160;&#160; - logon error with lgtst -&gt; replace program lgtst by sapserv version&#160;or take the file from your older release (note 302548)<br />&#160;&#160; - RFC connection test to SapNet in RTCCTOOL failes -&gt; either get&#160;the kernel patch 199 (note 316221) or use the workarround without&#160;logongroup as proposed by the RTCCTOOL RFC error popup (if you&#160;have deselected the 'use logon balancing' but still the connection&#160;does not go through after 'Save/Test', then there may be a buffer&#160;refresh problem; please try once more later in another screen).<br /><br />Q: Shortened procedure for customers using an internet VPN/SNC&#160;connection to sapserv1 or sapserv2<br />A: This connection type does not allow FTP access to the sapserv file&#160;system. Currently these customers can only download the files for&#160;the Addons for service preparation ST-PI (from note 116095) and&#160;ST-A/PI (from note 69455).&#160;They are available in the download corner of the SAP Services&#160;Marketplace http://service.sap.com.<br /><br /><br />Q: List of recommended notes<br />A: RTCCTOOL automatically checks for transports&#160;from the following notes:<br />&#160;&#160; Note 116095 (ST-PI Addons with basis tools, includes the download&#160;tool SDCC (note 178631), the SQL trace interpreter SQLR. Includes also RTCCTOOL/RTCCAUPG (this note 187939) ).&#160;Note 69455 (ST14 application monitor, important for the&#160; GoingLive Optimization session) &#160;&#160;Note 389742 (ST14 monitor &amp; service download modules for CRM/EBP)&#160;&#160;Note 7312 (New Earlywatch user / client 066)<br />&#160;&#160; Note 36637 (Database monitor for MS SQL Server Release 6.5)<br />&#160;&#160;Note 139945 (Database monitor for MS SQL Server Release 7.0)<br />&#160;&#160; Note 84060 (Database monitor for Informix)<br />&#160;&#160;Note 101217 (Correction transports for DB2/390)<br />&#160;&#160; Note 124268 (Explain function for DB6)<br />&#160;&#160; and others</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027971)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031877)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000187939/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000187939/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000187939/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000187939/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000187939/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000187939/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000187939/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000187939/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000187939/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "7312", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/7312"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "540442", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/540442"}, {"RefNumber": "405802", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/405802"}, {"RefNumber": "383270", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/383270"}, {"RefNumber": "360995", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/360995"}, {"RefNumber": "309711", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update: Online help", "RefUrl": "/notes/309711"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "215361", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/215361"}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886"}, {"RefNumber": "195189", "RefComponent": "SV-SMG-SDD", "RefTitle": "Modification adjustment for SSQ0 / SQLD / OCSI", "RefUrl": "/notes/195189"}, {"RefNumber": "178631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178631"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "156198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156198"}, {"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316"}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095"}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217 "}, {"RefNumber": "195189", "RefComponent": "SV-SMG-SDD", "RefTitle": "Modification adjustment for SSQ0 / SQLD / OCSI", "RefUrl": "/notes/195189 "}, {"RefNumber": "309711", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update: Online help", "RefUrl": "/notes/309711 "}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095 "}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31I", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}