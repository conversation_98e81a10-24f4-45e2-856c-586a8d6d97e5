{"Request": {"Number": "782947", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 496, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015775792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000782947?language=E&token=45B7CB95E1CCE38886E40B3B5B623034"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000782947", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000782947/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "782947"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.01.2016"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "782947 - Programming interfaces for RE-FX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In the context of additional developments, you want to use RE-FX interfaces to read or change Real Estate master data, for example.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BAPI, Business Application Programming Interface, API, Application Programming Interface, BAdI, Business Add-In</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The following RE-FX standard objects are provided for use in customer-specific programs or enhancements:</p>\r\n<ul>\r\n<li>API function modules for accessing master data and contracts</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Find modules: Transaction SE37, search string API_RE_*<br />You can also use these modules in BAdI implementations. For almost all of these modules, there is also a corresponding BAPI (same function module name: BAPI_RE_... instead of API_RE_ ...). The BAPI contains the documentation for the parameters. As of Release ERP 2004, there are also modules for mass access (*_GET_LIST).</p>\r\n<ul>\r\n<li>BAdI RECA_STORABLE_EXT or, as of Release ERP 2005, BAdI BADI_RECA_STORABLE_EXT for enhancing master data objects and contracts (additional fields, checks, and preassignments)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the IMG, you find one IMG activity \"Implement Enhancements\" for each object type. The two enhancement methods for flat and tabular master data enhancements are described in the relevant IMG documentation (this corresponds to the BAdI documentation). For table-like enhancements, you have to use the released interface IF_RECA_STORABLE_EXT and the released class CL_RECA_STORABLE_EXT.</p>\r\n<ul>\r\n<li>Enhancements to the user interface for master data objects and the contract</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP Note 690900 describes the procedure for enhancing the user interface. The content of this SAP Note is contained in the system as of ERP 2004 (part of the afore-mentioned IMG documentation \"Implement enhancements\").</p>\r\n<ul>\r\n<li>Additional BAdIs (for example, for service charge settlement, resubmission, and correspondence)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Find BAdIs: Transaction SE18, search string RE++_*.</p>\r\n<ul>\r\n<li>BAPI function modules for accessing master data and contracts</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Find modules: Transaction SE37, search string BAPI_RE_*<br />These BAPIs are provided for the legacy data transfer (transactions LSMW and SXDA). Since BAPIs are generally RFC-enabled, you can also use these modules for remote access from external systems, for example, to integrate external graphics systems.<br />Also use the BAPIs, where applicable, even though they are not formally released yet.</p>\r\n<ul>\r\n<li>Global classes for accessing configuration data (C and S tables)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Find classes: Transaction SE24, search string CL_RE++C_*<br />To determine the attributes of a certain contract type, for example, call the method CL_RECNC_CONTRACT_TYPE=&gt;GET_DETAIL.</p>\r\n<ul>\r\n<li>Logical databases:</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REBD - Master data for usage objects<br />REAO - Master data for architectural objects<br />REBP - Business partner for Real Estate objects<br />RECN - Real Estate Contracts<br /><br />SAP will enhance these interfaces (compatible with BAdIs and released BAPIs) or will make incompatible changes only in exceptional cases (API function modules).<br /><br />To minimize the effort required to make adjustments after a release upgrade, avoid the following usages:</p>\r\n<ul>\r\n<li>Do not execute any SELECTs in RE-FX tables, but instead use the API modules for accessing master data and transaction data or the aforementioned classes for accessing configuration data. Bear this in mind, in particular in the context of a table change from Release 470x200 to ERP 2004 (see SAP Note 681951).</li>\r\n</ul>\r\n<ul>\r\n<li>Do not change table data directly using INSERT, UPDATE or DELETE, but use the CREATE and CHANGE BAPI function modules or API function modules for the relevant object type. In the case of changes made directly, the system will not run the business logic, which means that some data will be inconsistent or additional data will be missing (for example, change documents).</li>\r\n</ul>\r\n<ul>\r\n<li>The direct issuing of messages using the statement MESSAGE (without the clause RAISING ...) is not permitted.</li>\r\n</ul>\r\n<ul>\r\n<li>For data declarations and for the parameterization of methods, function modules, form routines, and so on, do not use the table reference or the data element, but use the structure reference instead. Example for number of business entity: &#x00A0;&#x00A0;DATA: ld_swenr TYPE bapi_re_bus_entity_int-swenr.&#x00A0;&#x00A0;\"korrekt<br />&#x00A0;&#x00A0;DATA: ld_swenr TYPE vibdbe-swenr.&#x00A0;&#x00A0;\"falsch<br />&#x00A0;&#x00A0;DATA: ld_swenr TYPE swenr.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; \"falsch</li>\r\n</ul>\r\n<ul>\r\n<li>Do not use other interfaces, classes, function modules, or form routines from RE-FX if these objects or modules were not released or recommended for use by SAP.<br />In particular, do not create a subclass for an RE-FX class and do not implement an RE-FX interface in a separate class or a separate interface. Exceptions are the aforementioned RE-FX class (CL_RECA_STORABLE_EXT) and RE-FX interface (IF_RECA_STORABLE_EXT).</li>\r\n</ul>\r\n<p>For information on using API or BAPI function modules to make data changes, read the following notes in connection with COMMIT WORK:</p>\r\n<ul>\r\n<li><strong>Within an BAdI implementation, you shall not execute a COMMIT WORK or a ROLLBACK WORK. The transaction control must only be in the surrounding application that calls the Business Add-In method. Otherwise data inconsistencies may arise.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>Data changes using API and BAPI modules are always executed with IN UPDATE TASK. The modules never execute a COMMIT WORK or ROLLBACK WORK. To effectively write the changes to the database, you must execute COMMIT WORK after calling the data change module. To do so, call the function module BAPI_TRANSACTION_COMMIT.</li>\r\n</ul>\r\n<ul>\r\n<li>If possible, call BAPI_TRANSACTION_COMMIT <strong>each time</strong> a data change module is called successfully. This procedure is absolutely necessary if you want to change the same Real Estate object when you call data change modules twice in quick succession or if you want to create dependent Real Estate objects (for example, an architectural object and lower-level architectural object).</li>\r\n</ul>\r\n<ul>\r\n<li>If errors occur when you call a module, this means that the system could not make at least one of the changes you wanted. You do not have to carry out a ROLLBACK WORK. The update modules are not called internally until all changes are can be made. You do not have to perform a COMMIT WORK or call BAPI_TRANSACTION_COMMIT, which is time-consuming, but this cannot result in data inconsistencies or similar errors.</li>\r\n</ul>\r\n<ul>\r\n<li>If you only want to simulate changes, set the TEST_RUN parameter to \"X\". For this, update modules are generally not called so that neither a COMMIT WORK nor a ROLLBACK WORK is necessary or useful.</li>\r\n</ul>\r\n<p>For the Real Estate master data, BOR object types are available in the Business Object Repository. You require these object types to define workflows or other tools (such as document management). The BOR object types are:</p>\r\n<ul>\r\n<li>BUS1151&#x00A0;&#x00A0;&#x00A0;&#x00A0;Architectural object</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1501&#x00A0;&#x00A0;&#x00A0;&#x00A0;Business entity</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1502&#x00A0;&#x00A0;&#x00A0;&#x00A0;Property</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1503&#x00A0;&#x00A0;&#x00A0;&#x00A0;Building</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1504&#x00A0;&#x00A0;&#x00A0;&#x00A0;Rental object</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1505&#x00A0;&#x00A0;&#x00A0;&#x00A0;Real estate contract</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1506&#x00A0;&#x00A0;&#x00A0;&#x00A0;Settlement unit</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1507&#x00A0;&#x00A0;&#x00A0;&#x00A0;Participation group</li>\r\n</ul>\r\n<ul>\r\n<li>BUS1508&#x00A0;&#x00A0;&#x00A0;&#x00A0;Comparative group of apartments</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I001503)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000782947/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000782947/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "969234", "RefComponent": "RE-FX", "RefTitle": "User-defined tables real estate master data", "RefUrl": "/notes/969234"}, {"RefNumber": "690900", "RefComponent": "RE-FX-BD", "RefTitle": "User-defined real estate master data fields as of 470x200", "RefUrl": "/notes/690900"}, {"RefNumber": "681951", "RefComponent": "RE-FX", "RefTitle": "New master data tables as of ERP 2004 & subsequent releases", "RefUrl": "/notes/681951"}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311"}, {"RefNumber": "1862400", "RefComponent": "RE-FX", "RefTitle": "BAdI runtime measurement", "RefUrl": "/notes/1862400"}, {"RefNumber": "1709315", "RefComponent": "RE-FX-CO", "RefTitle": "BAPIs and profitability segments as settlement receiver", "RefUrl": "/notes/1709315"}, {"RefNumber": "1526490", "RefComponent": "RE-FX", "RefTitle": "FAQ: Using BAdIs in RE-FX", "RefUrl": "/notes/1526490"}, {"RefNumber": "1468490", "RefComponent": "RE-FX-BD", "RefTitle": "Error in method DELETE_FUNC_LOC_RE", "RefUrl": "/notes/1468490"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2146784", "RefComponent": "RE-FX", "RefTitle": "Batch Input for RE-FX transactions is not working", "RefUrl": "/notes/2146784 "}, {"RefNumber": "1678657", "RefComponent": "RE-FX", "RefTitle": "Loading database status RE-FX objects with API", "RefUrl": "/notes/1678657 "}, {"RefNumber": "2132925", "RefComponent": "RE-FX", "RefTitle": "Executing BAdI runtime measurements for the area RE-FX", "RefUrl": "/notes/2132925 "}, {"RefNumber": "969234", "RefComponent": "RE-FX", "RefTitle": "User-defined tables real estate master data", "RefUrl": "/notes/969234 "}, {"RefNumber": "690900", "RefComponent": "RE-FX-BD", "RefTitle": "User-defined real estate master data fields as of 470x200", "RefUrl": "/notes/690900 "}, {"RefNumber": "1862400", "RefComponent": "RE-FX", "RefTitle": "BAdI runtime measurement", "RefUrl": "/notes/1862400 "}, {"RefNumber": "681951", "RefComponent": "RE-FX", "RefTitle": "New master data tables as of ERP 2004 & subsequent releases", "RefUrl": "/notes/681951 "}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "1709315", "RefComponent": "RE-FX-CO", "RefTitle": "BAPIs and profitability segments as settlement receiver", "RefUrl": "/notes/1709315 "}, {"RefNumber": "1526490", "RefComponent": "RE-FX", "RefTitle": "FAQ: Using BAdIs in RE-FX", "RefUrl": "/notes/1526490 "}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311 "}, {"RefNumber": "1468490", "RefComponent": "RE-FX-BD", "RefTitle": "Error in method DELETE_FUNC_LOC_RE", "RefUrl": "/notes/1468490 "}, {"RefNumber": "1003258", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX integration with PSCD: Useful settings", "RefUrl": "/notes/1003258 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}