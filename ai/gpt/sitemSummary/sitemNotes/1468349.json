{"Request": {"Number": "1468349", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 305, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017030352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001468349?language=E&token=765966908A7DFAB6EFC7E4C4EC6BAE36"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001468349", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001468349/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1468349"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.05.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-TLS-TLJ"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade tools for Java"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Tools (SUM)", "value": "BC-UPG-TLS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-TLS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade tools for Java", "value": "BC-UPG-TLS-TLJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-TLS-TLJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1468349 - SAP Business Suite 7 for SAP NetWeaver 7.3 hub systems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You use SAP Business Suite applications that are based on an Application Server Java and on hub systems such as Enterprise Portal Hub, BW Hub, or PI Hub. You want to install these applications on Release SAP NetWeaver 7.3, or you want to change from your current release to SAP NetWeaver 7.3. Changing refers to upgrading or updating.<br />This note describes which applications can run on a hub system with SAP NetWeaver 7.3 and as of which start release or SAP Enhancement Package this is possible.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Suite Enablement NW 7.3, media list, DVD</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have installed the Business Suite applications on SAP NetWeaver 7.3 or you perform an upgrade or updates of the Business Suite applications to SAP NetWeaver 7.3.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Further information and sources:</strong></p>\r\n<ul>\r\n<li>Information about SAP NetWeaver 7.3 is available in the relevant guides (for example, Master Guide, Installation Guide).</li>\r\n</ul>\r\n<ul>\r\n<li>Information about the operation of SAP Business Suite applications in Enhancement Package 1 for SAP NetWeaver 7.3 Application Server Java and in hub systems is available in SAP Note 1698276.</li>\r\n</ul>\r\n<ul>\r\n<li>The \"SAP Business Suite Landscape Implementation - Technical Planning Guide\" provides you with an overview of SAP Business Suite with its possible system landscapes. This guide is provided under the alias /erp-inst in the planning area on SAP Service Marketplace. The Planning Guide describes both the application-based innovation approach and the technology-based innovation approach. Here, technology-based innovation stands for the use of SAP Business Suite applications in an SAP NetWeaver 7.3 environment.</li>\r\n</ul>\r\n<ul>\r\n<li>For the upgrade of an SAP NetWeaver Business Warehouse hub system including the instance SAP SEM with the software component SEM-BW, especially refer to Note 1531022.</li>\r\n</ul>\r\n<p><br /><strong>Limitation:</strong><br />Possible approaches for system landscapes for SAP NetWeaver 7.3 are the following:</p>\r\n<ul>\r\n<li>Upgrading to SAP NetWeaver 7.3 works only for hub systems (a definition is provided in the Planning Guide).</li>\r\n</ul>\r\n<ul>\r\n<li>Embedded Deployment (a definition is provided in the Planning Guide) continues to work only for SAP NetWeaver 7.0x releases and their Enhancement Packages (this means, for example, for SAP NetWeaver 7.00, SAP NetWeaver 7.01, and SAP NetWeaver 7.02)</li>\r\n</ul>\r\n<p><br /><strong>Applicability of the note:</strong><br />The operation of the SAP Business Suite applications on SAP NetWeaver 7.3 is only possible for hub systems as of the release of business suite 7. For the Business Suite 7 Innovations 2010 release, you can find the relevant information in Note 1615463.<br /><br /><strong>Technical background:</strong></p>\r\n<ul>\r\n<li>SAP NetWeaver 7.3 is a main release with extensive technology innovation. It is not an Enhancement Package for the SAP NetWeaver 7.0x releases. Jumping from SAP NetWeaver 7.0x to SAP NetWeaver 7.3 is referred to as an upgrade. The Master Guide of the SAP NetWeaver 7.3 delivery contains details about the underlying JDK version, and so on.</li>\r\n</ul>\r\n<ul>\r\n<li>Jumping from SAP NetWeaver 7.1 or SAP NetWeaver 7.11 environments to SAP NetWeaver 7.3 is referred to as an update within an Enhancement Package.</li>\r\n</ul>\r\n<ul>\r\n<li>For Business Suite applications that are based on older SAP NetWeaver releases (for example, SAP NetWeaver 2004), it is possible to perform the upgrade to SAP NetWeaver 7.3 in two steps using the maintenance optimizer.<br />In the first step, you perform an upgrade of Business Suite applications with older SAP NetWeaver releases to Business Suite 7 with SAP NetWeaver 7.0x. In the second step, you perform an upgrade of SAP NetWeaver 7.0x to SAP NetWeaver 7.3.<br />If the maintenance planner is used, you can perform these two steps in one planning step.</li>\r\n</ul>\r\n<ul>\r\n<li>When using SAP NetWeaver 7.3, there are no functional changes for the Java applications of the Business Suite. According to the approach of technology innovation, SAP provides executable software components of the Business Suite applications for SAP NetWeaver 7.3.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP ensures that the Business Suite applications run on an SAP NetWeaver 7.3 hub system as follows:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>by introducing new software component versions with Build-Time SAP NetWeaver 7.3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>or by releasing the SAP NetWeaver 7.3 releases for existing software component versions with a different Build-Time (for example, of SAP NetWeaver 7.1).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Note that this technology change is decisive for components that SAP previously changed from a Java technology to an ABAP technology. This means that these components continue to be available only in the ABAP environment (SAP NetWeaver 7.0x). Before you perform a scheduled upgrade to SAP NetWeaver 7.3, check if the Business Suite component that you are using supports SAP NetWeaver 7.3. If the technology of this component was changed from Java to ABAP, the upgrade and use of this component on SAP NetWeaver 7.3 is not possible.</li>\r\n</ul>\r\n<ul>\r\n<li>A list is attached to this note. It contains product versions of SAP Business Suite with the relevant instance and software component that can run on SAP NetWeaver 7.3 hub systems. In addition, the list provides information about whether an upgrade or an update of the SAP NetWeaver release is involved for the relevant component, and about which Lifecycle Management tool (for example, SAPJup or SUM) and which upgrade master is used (for example, BS7 hub upgrade master or SAP NetWeaver 7.3 upgrade master).</li>\r\n</ul>\r\n<ul>\r\n<li>The contents of the Business Suite Java applications for SAP NetWeaver 7.3 are provided on new media or DVDs. The media and the DVDs are provided in the relevant media lists on SAP Service Marketplace at http://servic.sap.com/instguides. For SAP CRM 7.0, for example, the media list is provided on f SAP Service Marketplace at http://servic.sap.com/instguides -&gt; SAP Business Suite Applications -&gt; SAP CRM -&gt; SAP CRM 7.0 -&gt; Upgrade -&gt; Media List SAP CRM 7.0 SR1. For Enhancement Package 4 for SAP ERP 6.0, the updated media list is contained in the Master Guide SAP Enhancement Package 4 for SAP ERP 6.0.</li>\r\n</ul>\r\n<p><br /><strong>Information about the installation:</strong></p>\r\n<ul>\r\n<li>For installing Business Suite applications on SAP NetWeaver 7.3, use the installation DVD set that is made available with the release of the Business Suite applications on SAP NetWeaver 7.3. To install the contents of Business Suite core applications, use the DVD \"SAP Business Suite 7 (2008SR1) - JAVA HUB BS Java Content\". To deploy applications that belong to add-on product versions, proceed as follows: After the installation of SAP NetWeaver 7.3, use the DVD \"BS Enablement Components\".</li>\r\n</ul>\r\n<ul>\r\n<li>Before you configure the settings for the SAP Business Suite applications, import Support Package SP02 for SAP NetWeaver 7.3. To do this, create a logical component for the release of the relevant Business Suite product and execute a maintenance optimizer transaction (MOPZ).</li>\r\n</ul>\r\n<ul>\r\n<li>After you import SAP NetWeaver 7.3 Support Package 02, you can check your system landscape using the landscape verification tool.</li>\r\n</ul>\r\n<ul>\r\n<li>Make the settings for your Business Suite application based on the post-installation steps from the relevant installation guide or note.</li>\r\n</ul>\r\n<p><br /><strong>Information about the upgrade or update:</strong></p>\r\n<ul>\r\n<li>Before you perform the actual upgrade, make sure that you have maintained the modeling of your system landscape in SAP Solution Manager System Landscape (SMSY). The general documentation about the maintenance in SMSY is available in the Help Portal at http://help.sap.com/solutionmanager. </li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Maintain product systems and product instances in SMSY according to your application and the relevant upgrade procedure. For Business Suite core products such as Enhancement Package EHP 4 for SAP ERP 6.0, a product system is required for SAP NetWeaver and a product system is required for the Business Suite product.<br />For add-on products such as SAP WORKFORCE MANAGEMENT 3.1 or SAP F&amp;R 5.1, a product system is required for SAP NetWeaver and a product system is required for the add-on product.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Create one or several logical components for the different product systems. For the upgrade or update to SAP NetWeaver 7.3, you require at least one logical component for the SAP NetWeaver product system.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Based on the logical component for the SAP NetWeaver product system, you must create a maintenance optimizer transaction (MOPZ) and execute it.</li>\r\n</ul>\r\n<ul>\r\n<li>After the upgrade, you must update the original product system and the product instance in SMSY. For a detailed description, see Note 1603103 that follows.</li>\r\n</ul>\r\n<ul>\r\n<li>Note that you must use the BS7 hub upgrade master for upgrading several components together that would require different upgrade masters according to the attached list.</li>\r\n</ul>\r\n<ul>\r\n<li>Note that a technology-based upgrade and an application-based innovation cannot be combined in an upgrade step (transaction MOPZ).</li>\r\n</ul>\r\n<ul>\r\n<li>Dual-stack upgrades to SAP NetWeaver 7.3 with additionally installed  Business Suite applications are not supported. A dual-stack upgrade to SAP NetWeaver 7.3 is allowed for the following exceptions:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP NetWeaver Process Integration with Business Suite XI/ESR Content and / or PI Adapter (Swift, BCONS, ELSTER)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP SEM with further SAP NetWeaver instances (but without further Business Suite Java applications)</li>\r\n</ul>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-INS-JCI (Java component inst. / jload jmigmon, jsizecheck)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029128)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D039661)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001468349/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468349/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Details_BS7EnableNW73_2012CW22.pdf", "FileSize": "214", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000236932010&iv_version=0026&iv_guid=9EDA528842127447B1BEDD877B98EDCC"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "979883", "RefComponent": "BC-ESI-WS-JAV", "RefTitle": "XPath expressions do not work in case of a comment in XML", "RefUrl": "/notes/979883"}, {"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819"}, {"RefNumber": "1721364", "RefComponent": "XX-SER-REL", "RefTitle": "SAP-CRMDIC and SAP-SHRAPP have older version in the stack", "RefUrl": "/notes/1721364"}, {"RefNumber": "1681813", "RefComponent": "PE-LSO-LPO", "RefTitle": "Unable to Import BP for Learning in NW 7.3", "RefUrl": "/notes/1681813"}, {"RefNumber": "1621704", "RefComponent": "FIN-FSCM-BC-CON", "RefTitle": "BCONS: BCONS 6.24 Connector Configuration exception", "RefUrl": "/notes/1621704"}, {"RefNumber": "1618606", "RefComponent": "BC-UPG", "RefTitle": "Patches for SAP Business Suite 7 for SAP NetWeaver 7.3 hub", "RefUrl": "/notes/1618606"}, {"RefNumber": "1617234", "RefComponent": "BC-CCM-SLD", "RefTitle": "Incorrect SLD security role assignments after upgrade", "RefUrl": "/notes/1617234"}, {"RefNumber": "1616574", "RefComponent": "XAP-IC-TXS", "RefTitle": "Taxpayer Online Services NW CE 7.30 Enablement", "RefUrl": "/notes/1616574"}, {"RefNumber": "1615463", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite 7i 2010 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1615463"}, {"RefNumber": "1603103", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: The product definition for one or several SAP product versions is missing", "RefUrl": "/notes/1603103"}, {"RefNumber": "1602543", "RefComponent": "MDM-FN", "RefTitle": "Error during BIND_SUPPORT_PACKAGES for MDM_JAVA_API", "RefUrl": "/notes/1602543"}, {"RefNumber": "1602476", "RefComponent": "CRM-ISA", "RefTitle": "Upgrade from CRM 7.0/NW70x to NW7.3 with modifications", "RefUrl": "/notes/1602476"}, {"RefNumber": "1596746", "RefComponent": "CA-GTF-WFM", "RefTitle": "Undeploy SAP-CRMDIC before upgrade of WFM to NW 7.3", "RefUrl": "/notes/1596746"}, {"RefNumber": "1595356", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1595356"}, {"RefNumber": "1587662", "RefComponent": "SRM-LA", "RefTitle": "Configuration Setting after upgrading from NW7.0X to NW7.30", "RefUrl": "/notes/1587662"}, {"RefNumber": "1573180", "RefComponent": "XX-SER-REL", "RefTitle": "AEX Enablement for SAP Business Suite", "RefUrl": "/notes/1573180"}, {"RefNumber": "1571357", "RefComponent": "LO-SRS-IM", "RefTitle": "Mobile In-Store IM - IS-R-SRS 632 Installation", "RefUrl": "/notes/1571357"}, {"RefNumber": "1567275", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1567275"}, {"RefNumber": "1563579", "RefComponent": "BC-INS-MIG", "RefTitle": "Central Release Note for Software Logistics Toolset 1.0", "RefUrl": "/notes/1563579"}, {"RefNumber": "1550790", "RefComponent": "PA-PA-ZA", "RefTitle": "ESS:Bank name incorrect on review page in ZA bank scenario", "RefUrl": "/notes/1550790"}, {"RefNumber": "1537729", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade of SAP BS7 on SAP NW 7.3 Hub Systems", "RefUrl": "/notes/1537729"}, {"RefNumber": "1532805", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-On Compatibility of SAP NetWeaver 7.3", "RefUrl": "/notes/1532805"}, {"RefNumber": "1528990", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1528990"}, {"RefNumber": "1527351", "RefComponent": "PY-ZA", "RefTitle": "Address Infotype giving dump in ESS on change/creation.", "RefUrl": "/notes/1527351"}, {"RefNumber": "1517139", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Installation Note: Import EC User Groups", "RefUrl": "/notes/1517139"}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258"}, {"RefNumber": "1334357", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SCM Java components handling by the upgrade program", "RefUrl": "/notes/1334357"}, {"RefNumber": "1293294", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1293294"}, {"RefNumber": "1280819", "RefComponent": "FIN-FSCM-BC-CON", "RefTitle": "SAP BCONS Adapter for Billing Consolidation EDX", "RefUrl": "/notes/1280819"}, {"RefNumber": "1279464", "RefComponent": "PY-DE-BA", "RefTitle": "ETNotif/ETStmt/ELStAM: Elster with PI 7.10, 7.11, 7.30, 7.31", "RefUrl": "/notes/1279464"}, {"RefNumber": "1251875", "RefComponent": "IS-U-CS-ISS", "RefTitle": "SAP Utility Customer E-Services (UCES) 6.04 installatn note", "RefUrl": "/notes/1251875"}, {"RefNumber": "1139005", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 Central Note", "RefUrl": "/notes/1139005"}, {"RefNumber": "1064419", "RefComponent": "FIN-FSCM-BNK-SWF", "RefTitle": "SAP Integration Package for SWIFT: Installation information", "RefUrl": "/notes/1064419"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1816819", "RefComponent": "XX-SER-REL", "RefTitle": "Dual Stack support for Business Suite systems", "RefUrl": "/notes/1816819 "}, {"RefNumber": "1603103", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: The product definition for one or several SAP product versions is missing", "RefUrl": "/notes/1603103 "}, {"RefNumber": "1388258", "RefComponent": "XX-SER-REL", "RefTitle": "Version Interoperability between SAP Business Suite and SAP NetWeaver Systems", "RefUrl": "/notes/1388258 "}, {"RefNumber": "1139005", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 Central Note", "RefUrl": "/notes/1139005 "}, {"RefNumber": "1528990", "RefComponent": "BC-UPG-PRP", "RefTitle": "SP Equivalence for update/upgrade to SAP NW 7.30", "RefUrl": "/notes/1528990 "}, {"RefNumber": "1334357", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SCM Java components handling by the upgrade program", "RefUrl": "/notes/1334357 "}, {"RefNumber": "1615463", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite 7i 2010 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1615463 "}, {"RefNumber": "1517139", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Installation Note: Import EC User Groups", "RefUrl": "/notes/1517139 "}, {"RefNumber": "1567275", "RefComponent": "BC-INS", "RefTitle": "OBSOLETE: Inst.Add.Software Units BS 7 on NW 7.3 Java Hubs", "RefUrl": "/notes/1567275 "}, {"RefNumber": "1573180", "RefComponent": "XX-SER-REL", "RefTitle": "AEX Enablement for SAP Business Suite", "RefUrl": "/notes/1573180 "}, {"RefNumber": "1279464", "RefComponent": "PY-DE-BA", "RefTitle": "ETNotif/ETStmt/ELStAM: Elster with PI 7.10, 7.11, 7.30, 7.31", "RefUrl": "/notes/1279464 "}, {"RefNumber": "1587662", "RefComponent": "SRM-LA", "RefTitle": "Configuration Setting after upgrading from NW7.0X to NW7.30", "RefUrl": "/notes/1587662 "}, {"RefNumber": "1721364", "RefComponent": "XX-SER-REL", "RefTitle": "SAP-CRMDIC and SAP-SHRAPP have older version in the stack", "RefUrl": "/notes/1721364 "}, {"RefNumber": "1532805", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-On Compatibility of SAP NetWeaver 7.3", "RefUrl": "/notes/1532805 "}, {"RefNumber": "1616574", "RefComponent": "XAP-IC-TXS", "RefTitle": "Taxpayer Online Services NW CE 7.30 Enablement", "RefUrl": "/notes/1616574 "}, {"RefNumber": "1681813", "RefComponent": "PE-LSO-LPO", "RefTitle": "Unable to Import BP for Learning in NW 7.3", "RefUrl": "/notes/1681813 "}, {"RefNumber": "1617234", "RefComponent": "BC-CCM-SLD", "RefTitle": "Incorrect SLD security role assignments after upgrade", "RefUrl": "/notes/1617234 "}, {"RefNumber": "979883", "RefComponent": "BC-ESI-WS-JAV", "RefTitle": "XPath expressions do not work in case of a comment in XML", "RefUrl": "/notes/979883 "}, {"RefNumber": "1618606", "RefComponent": "BC-UPG", "RefTitle": "Patches for SAP Business Suite 7 for SAP NetWeaver 7.3 hub", "RefUrl": "/notes/1618606 "}, {"RefNumber": "1621704", "RefComponent": "FIN-FSCM-BC-CON", "RefTitle": "BCONS: BCONS 6.24 Connector Configuration exception", "RefUrl": "/notes/1621704 "}, {"RefNumber": "1537729", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade of SAP BS7 on SAP NW 7.3 Hub Systems", "RefUrl": "/notes/1537729 "}, {"RefNumber": "1596746", "RefComponent": "CA-GTF-WFM", "RefTitle": "Undeploy SAP-CRMDIC before upgrade of WFM to NW 7.3", "RefUrl": "/notes/1596746 "}, {"RefNumber": "1602476", "RefComponent": "CRM-ISA", "RefTitle": "Upgrade from CRM 7.0/NW70x to NW7.3 with modifications", "RefUrl": "/notes/1602476 "}, {"RefNumber": "1602543", "RefComponent": "MDM-FN", "RefTitle": "Error during BIND_SUPPORT_PACKAGES for MDM_JAVA_API", "RefUrl": "/notes/1602543 "}, {"RefNumber": "1064419", "RefComponent": "FIN-FSCM-BNK-SWF", "RefTitle": "SAP Integration Package for SWIFT: Installation information", "RefUrl": "/notes/1064419 "}, {"RefNumber": "1571357", "RefComponent": "LO-SRS-IM", "RefTitle": "Mobile In-Store IM - IS-R-SRS 632 Installation", "RefUrl": "/notes/1571357 "}, {"RefNumber": "1280819", "RefComponent": "FIN-FSCM-BC-CON", "RefTitle": "SAP BCONS Adapter for Billing Consolidation EDX", "RefUrl": "/notes/1280819 "}, {"RefNumber": "1550790", "RefComponent": "PA-PA-ZA", "RefTitle": "ESS:Bank name incorrect on review page in ZA bank scenario", "RefUrl": "/notes/1550790 "}, {"RefNumber": "1527351", "RefComponent": "PY-ZA", "RefTitle": "Address Infotype giving dump in ESS on change/creation.", "RefUrl": "/notes/1527351 "}, {"RefNumber": "1251875", "RefComponent": "IS-U-CS-ISS", "RefTitle": "SAP Utility Customer E-Services (UCES) 6.04 installatn note", "RefUrl": "/notes/1251875 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}