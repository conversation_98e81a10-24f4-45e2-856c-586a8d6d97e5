{"Request": {"Number": "775047", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 333, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015996992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000775047?language=E&token=7B18B139BFFB00A157C9B88EF58303C9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000775047", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000775047/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "775047"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.09.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "775047 - Add. info. on upgrading to SAP Web AS 6.40 SR1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Errors in the upgrade procedure or in the upgrade guide; preparations for the upgrade</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Update, migration, upgrade, release, maintenance level, R3up, PREPARE<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>*</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>CAUTION:</B><br />This note is updated regularly!<br />Therefore, you should read it again immediately before starting the upgrade.<br /><br /></p> <b>What information can I expect from this note?</b><br /> <p>This note describes problems that may occur during the system upgrade and provides information on how to solve them. This usually takes the form of references to other notes.<br />The main purpose of this note is to prevent data loss, upgrade shutdowns, and long runtimes.<br />It deals with database-independent problems only.<br /><br /></p> <b>Which additional notes do I require in preparation for the upgrade?</b><br /> <p>This depends on the functions that you are using. You will need one or several of the following notes:<br /><br />Short text................................................ Note number<br />_____________________________________________________________________<br />Additional information on upgrade to WAS 6.40 MaxDB............669656<br />Additional information: Upgrade to SAP Web AS 6.40.............661569<br />DB6: Additions to upgrade (based) on SAP Web AS 6.40...........662191<br />DB2-z/OS: Additions upgrade ot Basis 6.40......................661252<br />Enhancements tu upgrade to WEB As 6.40 with MS SQL.............669236<br />Add. info. on upgrading to SAP Web AS 6.40 ORACLE..............662219<br />Additional information for upgrading to Basis..................647130<br />_____________________________________________________________________<br />Repairs for upgrade to Basis 640 ............................. 663240<br />Corrections for for R3up Version 640 ......................... 663258<br />_____________________________________________________________________<br />OCS: Known problems with Supp.Packages in Basis Rel.6.40 ......672651<br />_____________________________________________________________________<br /><br /></p> <b>Contents</b><br /> <p>I/ ...... R3up Keyword<br />II/ ..... Important General Information<br />III/ .... Corrections to the Guide<br />IV/ ..... Errors on the CD-ROM<br />V/ ...... Preventing Data Loss, Upgrade Shutdown, and Long Runtimes<br />VI/ ..... Preparing the Upgrade<br />VII/..... Problems During the PREPARE and Upgrade Phases<br />VIII/ ... Problems After the Upgrade<br />IX/ ..... Chronological Summary<br /><br /></p> <b>I/ R3up keyword</b><br /> <p><br />-----------------------&lt; D023536 FEB/20/02 &gt;--------------------<br />The R3up keyword is: 160421<br />This must be entered the first time you call R3up.<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p> <b>II/ Important General Information</b><br /> <p><br /><br />-----------------------&lt; D025323 MAY/24/02 &gt;--------------------<br /><B>Corrections and Repairs for the Upgrade</B><br />Before the upgrade, you must check whether a new version of the R3up exists for your specific upgrade.<br />For more information, see <B>Note 663258</B>.<br /><br />-----------------------&lt; D021371 NOV/29/05 &gt;--------------------<br /><B>Upgrade on Linux x86_64: Correct R3up Version</B><br />For SAP systems installed on Linux x86_64, there is no 64Bit R3up version. You need to use R3up for Linux x86 32-Bit.<br />For more information on how to use the R3up version in combination with the Kernel DVD, see Note <B>893352</B>.<br /><br />-----------------------&lt; D034302 DEZ/03/03 &gt;--------------------<br /><B>Windows only: Execute program R3dllins.exe</B><br />The 6.40 kernel is compiled with the new version of MS compiler and requires additional libraries for operation. To prevent problems during and after the upgrade, you must execute program R3dllins.exe on your central host, all application hosts, and on the remote shadow host, if you are planning to use one.<br />You can find the program on the Upgrade Master CD in the NT\\I386\\NTPATCH folder. It must be executed before you start prepare and directly from the NTPATCH folder (it can be shared). Copying and executing the program<br />will not work!<br /><br />-----------------------&lt; D028310 JUL/19/02 &gt;--------------------<br /><B>Problems with the Shadow Instance.</B><br />The following Notes contain information about problems with the shadow instance:</p> <UL><LI><B>525677</B>: Problems when starting the shadow instance</LI></UL> <UL><LI><B>430318</B>: Remote shadow instance on a different operating system</LI></UL> <p><br />------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br /><B>LSMW now part of SAP_BASIS</B><br />As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW<br />and your source release is based on SAP Web AS 6.10 or lower, do not<br />implement LSMW after the upgrade.<br />For more information, see <B>Note 673066</B>.<br /><br />---------------------------------------------------------------------<br /><br /><br /><br /></p> <b>III/ Corrections to the Guides</b><br /> <p><br />-----------------------&lt; D033903 15/SEP/08 &gt;--------------------------<br /><B>SDK Version 1.4.x for Upgrade Assistant</B><br />The upgrade assistant only supports Java Software Development Kit (SDK) 1.4.x. It does not support version 1.5 or higher.<br /><br />------------------------&lt; D035220 APR/23/07 &gt;--------------------------<br /><B>Migrate data from table TVARV to TVARVC</B><br />As of SAP Basis 6.10, the client-specific table TVARVC is used instead of cross-client table TVARV. If you want to migrate entries from table TVARV to the new table, you can use report RSTVARVCLIENTDEPENDENT.<br />For more information, see Note <B>557314</B>.<br /><br />------------------------&lt; D024991 OCT/19/06 &gt;-------------------------<br /><B>Data Management Planning - Link to SAP Service Marketplace</B><br />Site service.sap.com/dao on SAP Service Marketplace is temporarily underconstruction. Please refer to the following site and document instead:<br />service.sap.com/data-archiving -&gt; Media Library -&gt; Literature &amp; Brochure-&gt; Data Management Guide<br /><br />------------------------&lt; D022030 JUN/21/06 &gt;-------------------------<br /><B>Section: Making Entries for the Parameter Input Module</B><br />The maximum length of the mount directory path is <B>50</B> characters.<br />It may not contain any blanks or special characters.<br /><br />------------------------&lt; D030328 09/AUG/05 &gt;-------------------------<br /><B>Optional Follow-Up Activity: Where-used list in the Workbench</B><br />As of release 6.10, the where-used list for Dictionary objects has changed. If you need a proper display of the list, you need to run report SAPRSEUB after the upgrade.<br />As the runtime of the report may be quite long, we recommend that you run it in the development system only.<br />For more information, see Notes <B>401389</B> and <B>28022</B>.<br /><br />------------------------&lt; D022030 MAR/18/05 &gt;-------------------------<br /><B>Documentation \"SAP Software on UNIX - OS Dependencies\"</B><br />The information formerly contained in the documentation \"SAP Software onUNIX - OS Dependencies\" has been moved to the documentation \"Component Installation Guide &lt;your SAP component system combination&gt;, Part I.<br /><br />------------------------&lt; I002675 MAR/11/05 &gt;-------------------------<br /><B>Windows Guide Section on Database-Specific Parameters</B><br />In the Guide for the Upgrade on Windows, section \"Checking the Database-specific requirements for PREPARE\", you are asked to check the profile parameters for MS SQL server. As some of the parameter names have changed, SAP system tools may not recognize the parameters.<br />For more information, see <B>Note 826528</B>.<br /><br />------------------------&lt; D028310 25/FEB/05 &gt;-------------------------<br /><B>Section \"SAP NW AS: J2EE Engine Installation Planning\"</B><br />Please ignore this section! After the upgrade, proceed as described in section \"Installing the J2EE Engine\".<br /><br />------------------------&lt; D028310 09/FEB/05 &gt;-------------------------<br /><B>Section: Making Entries for the Extension Module</B><br />In subsection \"Phase IS_SELECT\" the following applies to add-ons with status \"Undecided\":<br />\"Your software vendor has predefined the strategy to choose for each add-on.<br />For more information, see the SAP Note displayed by R3up or contact your software vendor.\"<br /><br />-----------------------&lt; D022030 JUL/06/04 &gt;--------------------<br /><B>Phase INITSHD: Instance Number of the Shadow Instance</B><br />For more information on choosing the instance number for the shadow instance, see <B>note 29972</B>.<br /><br />-----------------------&lt; D022030 MAY/07/04 &gt;--------------------<br /><B>Implementing the Integrated SAP ITS</B><br />The text \"Meeting the Requirements for the SAP Internet Solutions\" does not contain a procedure for implementing the integrated ITS.<br />If you want to use it, proceed as follows:</p> <OL>1. After the upgrade of the system, configure the integrated ITS as described in the online documentation under Application Platform (SAP Web Application Server) -&gt; ABAP Technology -&gt; UI Technology -&gt; Web UI Technology -&gt; ITS/SAP@Web Studio -&gt; SAP ITS in the SAP Web Application Server -&gt; Configuration.</OL> <OL>2. Migrate the applications as described in the document \"Migration of Existing ITS Services\" on SAP Service Marketplace at service.sap.com\\sap-its -&gt; Media Library -&gt; Literature.</OL> <p><br />---------------------------------------------------------------------<br /><br /><br /><br /></p> <b>IV/ Errors on the CD-ROM</b><br /> <p><br /><br />---------------------------------------------------------------------<br /><br /><br /><br /></p> <b>V/ Preventing Data Loss, Upgrade Shutdown, and Long Runtimes</b><br /> <p><br />-----------------------&lt; D000706 28/NOV/06 &gt;--------------------------<br /><B>Modification Adjustment Planning and Unicode Conversion</B><br />You cannot import transport requests created in a Unicode SAP system into a non-Unicode SAP system. If you want to perform a Unicode conversion of your SAP system, create the transport request <B>before </B> the conversion.<br /><br />-----------------------&lt; D028597 15/SEP/06 &gt;--------------------------<br /><B>Support Package SAPKB64018 - use corrected version</B><br />If you include Support Package SAPKB70009 in the upgrade, make sure to use the corrected version, indicated by EPS file name CSN0120061532_0024351.PAT.<br />If you use the old version, phase XPRAS_UPG returns an error on the after import method SRM_FILL_KC_TABLES_AFTER_IMP. In this case, proceed as described in Note <B>967821</B>.<br />For more information about the problem in general, see Note <B>672651 </B>.<br /><br />------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br /><B>Back up customer-specific entries in table EDIFCT</B><br />If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br />For more information, see Note <B>865142</B>.<br /><br />----------------------------------------------------------------<br /><B>Do not include ABA Support Package 12 in the Upgrade</B><br />If you included Support Package SAPKA64012 in the upgrade, the upgrade fails in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br />To prevent the failure, only include the Suport Package in the upgrade if you can also include at least Support Package <B>SAPKA64013</B> as the problem will be fixed with SP 13.<br />For more information - also on the procedure in case of an upgrade failure, see <B>note 849925</B>.<br /><br />------------------------&lt; D001330 20/OCT/04 &gt;-------------------------<br /><B>Function Groups in the Customer Name Space</B><br />If you have created function groups in the customer namespace, you may lose data during the upgrade.<br />For more information, see Note <B>783308</B>.<br /><br />-----------------------&lt; D020815 13/FEB/04 &gt;--------------------------<br /><B>SPDD - Return to Standard</B><br />If you choose \"return to standard\" in transaction SPDD for data elements for which you have already performed the modification adjustment, the activation of the data elements starts. If you want to return adjusted data elements to the standard, proceed as described in <B>Note 705943 </B>.<br /><br />--------------------------&lt; D030559 27/JAN/04 &gt;-----------------------<br /><B>Import of Asian Languages: Change System Language</B><br />If you are importing Asian languages during the upgrade, you may get an error message during phase STARTSAP_TRANS or XPRAS_UPG (program RADBTLOG).<br />In order to prevent this problem, change your system language to \"English\" <B>before the upgrade</B>: Set instance parameter zcsa/system_language = E<br />and restart the SAP system.<br /><br />----------------------------------------------------------------------<br /><br /><br /><br /></p> <b>VI/ Preparing the Upgrade</b><br /> <p><br />-----------------------&lt; D001330 27/APR/06 &gt;--------------------------<br /><B>Handling of customer translation in the upgrade</B><br />Z languages or customer translations on system texts with transaction SE63 are not considered as modifications to the system by the upgrade and are therefore lost during the upgrade. As the SAP system may change considerably from one release to the next, it may not be worth saving the translations.<br />If you think that it is worth saving your translations or languages, seeNote <B>485741</B> for more information.<br /><br /><br />--------------------------&lt; D001330 10/OCT/05 &gt;-----------------------<br /><B>Apply the Latest Upgrade Repairs for Correct Language Import</B><br />If you do not apply the latest repairs for the upgrade as described in Note 663240, the language import from the data dictionary will be incomplete.<br />For more information, see Note <B>885955</B>.<br /><br />------------------------&lt; D034302 13/DEC/04 &gt;------------------------<br /><B>Windows only: Shut Down IGS Before Starting PREPARE</B><br />When you upgrade your system from Source Release 6.20, you have to make sure that the IGS is not running when you start PREPARE. Otherwise you will get the following error message in module General Checks: The LIBRFC32.DLL cannot be updated<br />To shut down the IGS, proceed as follows:</p> <UL><LI>Non-cluster installations: Kill the process as described in <B>Note 737099</B>.</LI></UL> <UL><LI>Windows Cluster installations:</LI></UL> <OL><OL>a) Turn the following lines in you start profile into a comment:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;_IGS=igswd.exe Start_Program_06=local $(DIR_INSTANCE)\\igs\\bin\\$(_IGS) (this is one line) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-dir=$(DIR_INSTANCE)\\igs -mode=all <OL><OL>b) Restart the system. Restarting the system cannot be avoided in this case!</OL></OL> <p><br />------------------------&lt; D038245 09/JUL/04 &gt;------------------------<br /><B>Adjust Profile Parameter \"rsts/ccc/cachesize\"</B><br />Check whether profile parameter \"rsts/ccc/cachesize\" has been set. If yes, either delete the parameter or set it to 6.000.000 bytes as described in <B>note 5470</B>.<br />Otherwise, you get the follwoing errors in several of the upgrade phases (e.g. PARCONV_UPG, XPREAS_UPG): Runtime error \"IMPORT_INIT_CONVERSATION_FAILED\"<br />The sys log contains the message: \"E 14 Reorganized Buffer RSCPCCC with Length xxxxx (Overflow)\"<br />The error also occurs in phases UVERS_CHK and START_SHDI_FIRST. Here you get a segmantation fault or Dr. Watson dump.<br /><br />------------------------&lt; D038245 19/APR/04 &gt;------------------------<br /><B>Unicode Systems: Downward Compatible Kernel 6.40</B><br />When you are using the normal kernel for Release 6.20 with your Unicode system, PREPARE issues the error message: Could not open the ICU common library.<br />Before you start PREPARE, install the Downward Compatible Kernel for Release 6.40. Until this kernel is available, proceed as described in <B>Note 716378</B>.<br /><br />------------------------&lt; D035318 04/FEB/04 &gt;------------------------<br /><B>Unicode Systems: Report RUTTTYPACT</B><br />If your system is a unicode system, check whether you have run report RUTTTYPACT in your system after the installation. If you have not run the report, do so before you start PREPARE. To run the report, proceed as described in <B>note 544623</B>.<br /><br />--------------------------&lt; D034302 16/JAN/04 &gt;-----------------------<br /><B>Java Application Server</B><br />If you have Java Application Server installed and running together with your ABAP system, problems can occur when the upgrade program tries to exchange executables and libraries. The problems can occur during PREPARE module General Checks as well as during upgrade phase KX_SWITCH.<br />To prevent these problems, shut down the Java Application Server during the upgrade.<br /><br />--------------------------&lt; D022030 22/MAR/04 &gt;-----------------------<br /><B>Database Archives During the Upgrade</B><br />Knowing the size of the archives written during the upgrade may help you select an upgrade or archiving strategy. The first of the two numbers specifies the size of the archives up to phase MODPROF_TRANS, while the second specifies the size of the archives up to the end of the upgrade.<br />DB2 UDB for UNIX/Windows: 6 GB / 8 GB<br />Informix: 8 GB / 10 GB<br />MS SQL Server: 7 GB / 8.5 GB<br />Oracle: 9.5 GB / 10.5 GB<br />MaxDB: 5 GB / 6 GB<br />These sizes are based on <B>sample data</B>.<br /><br />--------------------------&lt; D022030 22/MAR/04 &gt;-----------------------<br /><B>Space Requirements in the Database</B><br />You must enhance the database both during and because of the upgrade. The numbers specify the enhancement required temporarily during the upgrade.<br />DB2 UDB for UNIX/Windows: 17 GB<br />Informix: 18 GB<br />iSeries: 20 GB*<br />MS SQL Server: 9 GB<br />Oracle: 17 GB<br />MaxDB: 5.5 GB<br />* largely depends on the upgrade and archiving strategy because when using upgrade strategy archiving_on, a rather big number of journal receivers may arise during the upgrade.<br />These sizes are based on sample data. Additional freespace requirements (depending on your system) are calculated during the PREPARE.<br />To determine realistic freespace requirements, you should execute PREPARE.<br />After the upgrade, you can free space by deleting substitution tablespaces or removing superfluous database objects. For more information on how to proceed, see the Upgrade Guide under \"Post-Upgrade Activities\".<br /><br />--------------------------&lt; D025323 24/APR/03 &gt;-----------------------<br /><B>Upgrade on AIX: saposcol</B><br />Refer to Note <B>526694</B> before the upgrade.<br /><br />--------------------------&lt; D019926 DEC/10/02 &gt;-----------------------<br /><B>Upgrading with AIX 5.1</B><br />If you want to upgrade with AIX 5.1, see <B>Note 502532</B> before starting PREPARE.<br /><br />-----------------------&lt; D025323 FEB/20/02 &gt;--------------------------<br /><B>Source Releases on UNIX 32-bit or AIX 64-bit</B><br />In some cases, you may have to upgrade the operating system to 64-bit before the actual upgrade.<br />When you upgrade from AIX 4.3 64-bit, you must perform some additional actions before upgrading.<br />For more information, see <B>Notes 496963</B> and <B>499708</B>.<br /><br />-----------------------------------------------------------------------<br /><br /><br /><br /></p> <b>VII/ Problems During the PREPARE and Upgrade Phases</b><br /> <p><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.<br /></p> <b>Problems During the PREPARE Phases</b><br /> <p><br />------------------------&lt; D038245 05/SEP/05 &gt;------------------------<br /><B>Problem with DVD mount paths</B><br />When you enter mount points in PREPARE, you may get the error message: SEVERE ERROR: unable to find directory<br />In this case, check whether you mount point is longer than 94 characters or contains blanks and special characters. In this case, shorten the name and remove blanks or special characters.<br /><br />-----------------------&lt; D038245 20/APR/05 &gt;---------------------------<br /><B>Phase RFCCHK_INI: Name or Password is Incorrect</B><br />In phase RFCCHK_INI you may get the error message \"Name or Password is Incorrect\".<br />If you replaced R3up after phase RFCCHK_INI with the latest version from SAP Service Marketplace, this error may also come up in other phases that connect to the system using RFC.<br />For more information, see Note <B>792850</B>. You may have to replace disp+work and restart the system.<br /><br />-----------------------&lt; D028310 NOV/03/04 &gt;--------------------<br /><B>Phase CONFCHK_IMP on Distributed Systems</B><br />Phase CONFCHK_IMP offers you a list of operating systems to select from<br />This list only contains one entry \"Linux\" which is valid for both Linux and Linux IA64.<br /><br />-----------------------&lt; D038245 APR/11/02 &gt;--------------------<br /><B>Termination in the TOOLIMPD3 phase</B><br />The TOOLIMPD3 phase terminates in the PREPARE module import. The following message appears in the log file: ABAP runtime error CALL_FUNCTION_NO_RECEIVER<BR/> Receiving data for unknown CPIC link XXXXXX.<br />Repeat the phase and continue with the upgrade.<br /><br />-----------------------&lt; D022256 SEP/04/00 &gt;--------------------<br /><B>For Windows NT 4.0 only</B><br />During PREPARE, a dialog box with the following error message may appear: 'The procedure entry point ... could not be located in the dynamic link library ... .',<br />In this case, import the latest DLLs using the R3DLLINS.EXE program. The program is available on the CD SAP Kernel in directory \\NT\\&lt;Processor type&gt;\\NTPATCH.<br />Reboot your machine and restart PREPARE with 'PREPARE repeat'.<br /><br />-----------------------------------------------------------------------<br /><br /><br /></p> <b>Problems During the Upgrade Phases</b><br /> <p><br />------------------------&lt; D028310 20/AUG/04 &gt;--------------------------<br />Phase: DIFFEXPDDIV<br />Note: 766379<br />Description:<br />Error message in log file DIFFEXPD.ELG (directory &lt;DIR_PUT&gt;/log):<br />INACTIVE DDIC VERSIONS-Export ERRORS and RETURN CODE in SAPEDDD622.QO1<br />~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br />2EETW190 \"TABT\" \"TESCL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\" has no active version.<br />...<br /><br />------------------------&lt; D025988 JUN/16/00 &gt;-------------------<br />Phase: PARDIST_SHD<br />Description: For Windows NT only<br />The upgrade is terminated during the PARDIST_SHD phase. The PCONUPG.ELG log file contains an incomplete error text which was extracted from the DS&lt;date&gt;.&lt;SID&gt; log file. Repeat the phase.<br /><br />--------------------&lt;changed D026178 JUN/25/04 &gt;--------------------<br />-------------------------&lt; D026178 JUN/07/04 &gt;----------------------<br />Phase: ACT_640<br />Description: If you have included Basis Support Package 03 in the upgrade, you may get the following error message: SHADOW IMPORT ERRORS and RETURN CODE in SAPKGPAC01.QO1<BR/> 1EEDO519 \"Table\" \"TPPROFILES_TYPE_PROFILE\" could not be activated.<br />The same error may appear for table \"TPPROFILES_TYPE_PROFILE_INFO\".<br />You can ignore the error. Repeat the phase to continue with the upgrade.<br /><br />------------------------&lt; D001330 MAR/19/02 &gt;-------------------<br />Phase: ACT_640<br />Note: 504892<br />Description: Errors in the SPDD comparison request<br />When you save the SPDD request, the system issues an TR067 error message: \"Extended transport control is not active -&gt; Only target systems possible.\"<br /><br />----------------------------------------------------------------------<br />Phase: PARCONV_UPG<br />Notes: 705724, 705733<br />Description: If you have not included SP02 in the upgrade, you will receive the error \"CONV ENTRY TBATG TABLMESYBODY - Unable to interpret \"000A \" as a number.\"<br />If you do not use Mobile Infrastructure, you can ignore this error.<br /><br />----------------------------------------------------------------------<br />Phase: IMPORT<br />Note: 88656<br />Description: You have included Suport Package SAPKB64012 and receive the following error message:<br />2EETW000 Table RSMPTEXTS~: Duplicate record during array insert occured.<br />Proceed as described in Note 88656, entry number 29.<br /><br />--------------------------&lt; D019416 19/MAR/04 &gt;-----------------------<br />Phase: TABIM_POST<br />Note: 718912<br />Description: Upgrade stops with error DI829. In this case, implement<br />the above note and continue with the upgrade. After the upgrade, you have to revert the modification.<br /><br />----------------------------------------------------------------------<br />Phase: XPRAS_UPG<br />Note: 778198<br />Description: Error message S&gt;801 in log file LONGPOST.LOG. For more<br />information, see the note above.<br /><br />----------------------------------------------------------------------<br />Phase: JOB_RDDNTPUR<br />Note: 699458<br />Description: If you get the error message<br />3PETG447 Table and runtime object \"UMG_TEST_A\" exist without DDIC reference (\"Log. pool\")<br />you can ignore it. For more information and a list of further tables for which you can ignore this error message, see the note above.<br /><br />--------------------------&lt; D025323 MAY/23/02 &gt;-----------------<br />Phase: CHK_POSTUP<br />Note: 197886<br />Description: If you have imported Notes 178631, 116095 or 69455 into your system before the upgrade, error messages for objects without DDIC reference appear in the log LONGPOST.LOG. Proceed as described in the Note.<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p> <b>VIII/ Problems After the Upgrade</b><br /> <p><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br /><br />-----------------------&lt; D035318 08/JUL/04 &gt;-------------------------<br /><B>Source Release lower than Basis 6.10: Codepage Conversion</B><br />In Release 6.10, the codepage administration has changed considerably. If you want to continue using the customer-defined codepages that start with \"9\" after the upgrade, you have to convert the codepages using report RSCP0126 after the upgrade.<br />For more information, see <B>Notes 485455</B> and <B>511732</B>.<br /><br />-----------------------&lt; D035318 04/FEB/04----------------------------<br /><B>Unicode Systems: Run Report RUTTTYPACT</B><br />If your system is a unicode system, you must run report RUTTTYPACT after the upgrade. To run the report, proceed as described in <B>note 544623 </B>.<br /><br />------------------------&lt; D020815 AUG/23/02 &gt;------------------------<br /><B>SPAU: Names of interface methods are truncated</B><br />Some methods (ABAP objects) that were modified and overwritten by the upgrade can be displayedin transaction SPAU with their names shortened to 30 characters.<br />As a result, the system may also incorrectly sort methods in SPAU under \"Deleted objects\".<br />Caution: Deleted objects are not displayed in the standard selection in SPAU. It is easily possible to overlook these!<br />For more information about the correction, see <B>Note 547773</B>.<br /><br />----------------------------------------------------------------------<br /><B>Linux: Importing the new saposcol version</B><br />For more information, see <B>Note 19227.</B><br /><br />----------------------------------------------------------------------<br /><B>ReliantUNIX: saposcol version 32-bit or 64-bit</B><br />For more information, see <B>Note 148926.</B><br /><br />----------------------------------------------------------------------<br /><B>Solaris: saposcol version 32-bit or 64- bit</B><br />For more information, see <B>Note 162980.</B><br /><br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p> <b>IX/ Chronological Summary</b><br /> <p><br />Date.....Topic..Short description<br />-----------------------------------------------------------------------<br />SEP/15/08..III..SDK Version 1.4.x for Upgrade Assistant<br />APR/23/07..III..Migrate data from table TVARV to TVARVC<br />MAR/02/07..VII..Phase RFCCHK_INI: Table RSMPTEXTS~: Duplicate record<br />NOV/28/06....V..Modification Adjustment Planning and Unicode Conversion<br />OCT/19/06..III..Data Management Planning - Link to SMP<br />SEP/15/06....V..Support Package SAPKB64018 - use corrected version<br />JUN/21/06..III..Entries for Parameter Input Module: Path Length<br />27/APR/06...VI..Handling of customer translation in the upgrade<br />NOV/29/05...II..Upgrade on Linux x86_64: Correct R3up Version<br />OCT/10/05...VI..Latest Upgrade Repairs for Correct Language Import<br />15/SEP/05....V..Back up customer-specific entries in table EDIFCT<br />05/SEP/05..VII..Problem with DVD mount paths<br />09/AUG/05..III..Opt. Follow-Up: Where-Used List<br />JUN/02/05....V..Do not include ABA Support Package 12 in the Upgrade<br />APR/20/05..VII..Phase RFCCHK_INI: Name or Password is Incorrect<br />MAR/18/05..III..Documentation: SAP Software on UNIX - OS Dependencies<br />MAR/11/05..III..Windows Guide Section on Database-Specific Parameters<br />FEB/25/05..III..Section \"SAP NW AS: J2EE Engine Installation Planning\"<br />FEB/09/05..III..Section: Making Entries for the Extension Module<br />FEB/02/05...II..LSMW now part of SAP_BASIS<br />JAN/21/05..VII..Phase XPRAS_UPG: Error S&gt;801<br />DEC/13/04...VI..Windows only: Shut Down IGS Before Starting PREPARE<br />NOV/03/04..VII..Phase CONFCHK_IMP on Distributed Systems<br />OCT/20/04....V..Function Groups in the Customer Name Space<br />OCT/20/04..III..Section: Phase JOB_RSVBCHCK2<br />AUG/20/04..VII..Phase: DIFFEXPDDIV<br />JUL/13/04...VI..Adjust Profile Parameter \"rsts/ccc/cachesize\"<br />JUL/08/04.VIII..Source Release lower than 6.10: Codepage conversion<br />JUL/06/04..III..Phase INITSHD: Instance Number of the Shadow Instance<br />JUN/07/04..VII..Phase ACT_640: SHADOW IMPORT ERRORS with Basis SP03<br />MAY/07/04..III..Implementing the Integrated SAP ITS<br />APR/19/04...VI..Unicode Systems: Downward Compatible Kernel 6.40<br />MAR/04/04..VII..Phase TABIM_POST: Error DI829<br />FEB/13/04..VII..Phase PARCONV_UPG: Table MESYBODY<br />FEB/13/04....V..SPDD - Return to Standard<br />FEB/04/04.VIII..Unicode Systems: Run Report RUTTTYPACT<br />FEB/04/04...VI..Unicode Systems: Report RUTTTYPACT<br />JAN/27/04....V..Import of Asian Languages: Change System Language<br />JAN/22/04..VII..Phase JOB_RDDNTPUR: Error 3PETG447<br />JAN/16/04...VI..Java Application Server<br />DEZ/03/03...II..Windows only: Execute program R3dllins.exe<br />SEP/23/03...VI..Database Archives During the Upgrade<br />SEP/23/03...VI..Space Requirements in the Database<br />SEP/17/03....I..R3up keyword<br />APR/24/03...VI..Upgrade on AIX: saposcol<br />DEC/10/02...VI..Upgrading with AIX 5.1<br />AUG/23/02.VIII..SPAU: Names of interface methods are truncated<br />JUL/23/02 ...I..Windows: Windows XP is not supported<br />JUL/19/02...II..Problems with the shadow instance<br />MAY/24/02...II..Corrections and repairs for the upgrade<br />MAY/23/02..VII..Phase CHK_POSTUP - objects without DDIC reference<br />APR/11/02..VII..Termination in the TOOLIMPD3 phase<br />APR/08/02..III..Preparation for reading the upgrade CDs<br />MAR/19/02..VII..Phase ACT_620: Errors in the SPDD comparison request<br />FEB/20/02...VI..Source releases on UNIX 32-bit or AIX 64-bit<br />OCT/19/00.VIII..Linux:&#x00A0;&#x00A0;Importing the new saposcol version<br />SEP/04/00..VII..Windows NT: Error message during PREPARE<br />FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Reliant UNIX<br />FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Solaris<br />----------------------------------------------------------------------<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D043830)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D031901)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000775047/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000775047/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821"}, {"RefNumber": "893352", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/893352"}, {"RefNumber": "885955", "RefComponent": "BC-UPG", "RefTitle": "Language content in DDIC import not imported (Upg 640/700)", "RefUrl": "/notes/885955"}, {"RefNumber": "865142", "RefComponent": "BC-MID-ALE", "RefTitle": "Customer-specific entries in EDIFCT are deleted", "RefUrl": "/notes/865142"}, {"RefNumber": "849925", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: termination in phase TABIM_UPG / SHADOW_IMPORT_UPG2", "RefUrl": "/notes/849925"}, {"RefNumber": "826528", "RefComponent": "BC-DB-MSS", "RefTitle": "Some profile parameters not recognized.", "RefUrl": "/notes/826528"}, {"RefNumber": "792850", "RefComponent": "BC-SEC-LGN", "RefTitle": "Preparing ABAP systems to deal with incompatible passwords", "RefUrl": "/notes/792850"}, {"RefNumber": "783308", "RefComponent": "BC-UPG", "RefTitle": "TLIBG inconsistencies before the upgrade", "RefUrl": "/notes/783308"}, {"RefNumber": "781092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrade to SAP Web AS 6.40 SR1 (iSeries", "RefUrl": "/notes/781092"}, {"RefNumber": "778198", "RefComponent": "BC-FES-ITS", "RefTitle": "SIAC_XPRA_CONVERT_FROM_4X_64, upgrade to Netweaver 04", "RefUrl": "/notes/778198"}, {"RefNumber": "766379", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/766379"}, {"RefNumber": "737099", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS upgrade step fails", "RefUrl": "/notes/737099"}, {"RefNumber": "718912", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Message DI829 during mass activation of lock objects", "RefUrl": "/notes/718912"}, {"RefNumber": "716378", "RefComponent": "BC-UPG-PRP", "RefTitle": "Missing libraries during Unicode upgrade", "RefUrl": "/notes/716378"}, {"RefNumber": "705943", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPDD - Activation program is started during reset", "RefUrl": "/notes/705943"}, {"RefNumber": "705733", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/705733"}, {"RefNumber": "705724", "RefComponent": "BC-MOB", "RefTitle": "Upgrade stops from 620 to 640 WebAS - adjustment of MESYBODY", "RefUrl": "/notes/705724"}, {"RefNumber": "699458", "RefComponent": "BC-I18", "RefTitle": "UPGRADEPHASE JOB_RDDNTPUR: \"UMG_TEST_A\" exist without DDIC", "RefUrl": "/notes/699458"}, {"RefNumber": "673066", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Upgrade to SAP Enterprise 4.7 (or Basis 6.20)", "RefUrl": "/notes/673066"}, {"RefNumber": "672651", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel.6.40", "RefUrl": "/notes/672651"}, {"RefNumber": "670644", "RefComponent": "BC-MOB", "RefTitle": "UPGRADE: SAP Mobile Infrastructure 2.5 - Composite Note", "RefUrl": "/notes/670644"}, {"RefNumber": "669656", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info about upgrade to SAP Web AS 6.40 MaxDB", "RefUrl": "/notes/669656"}, {"RefNumber": "669236", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to WEB AS 6.40 with MS SQL Server", "RefUrl": "/notes/669236"}, {"RefNumber": "664475", "RefComponent": "BC-UPG-RDM", "RefTitle": "iSeries: Additional information on upgrading to BW 3.5", "RefUrl": "/notes/664475"}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258"}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240"}, {"RefNumber": "662219", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 ORACLE", "RefUrl": "/notes/662219"}, {"RefNumber": "662191", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Additions to upgrade (based) on SAP Web AS 6.40", "RefUrl": "/notes/662191"}, {"RefNumber": "661569", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP Web AS 6.40: iSeries", "RefUrl": "/notes/661569"}, {"RefNumber": "661252", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/661252"}, {"RefNumber": "647130", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information for upgrading to Basis 6.40/INFORMIX", "RefUrl": "/notes/647130"}, {"RefNumber": "626408", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Upgrade: Restoring variants during the release upgrade", "RefUrl": "/notes/626408"}, {"RefNumber": "623723", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Upgrade: Application-specific problems", "RefUrl": "/notes/623723"}, {"RefNumber": "557314", "RefComponent": "BC-ABA-TO", "RefTitle": "As of Release 610: TVARV replaced with TVARVC", "RefUrl": "/notes/557314"}, {"RefNumber": "547773", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: interface method names are truncated", "RefUrl": "/notes/547773"}, {"RefNumber": "5470", "RefComponent": "BC-I18", "RefTitle": "Syslog E14: Reorganized Buffer RSCPCCC ...", "RefUrl": "/notes/5470"}, {"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623"}, {"RefNumber": "526694", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol based on AIX perfstat library", "RefUrl": "/notes/526694"}, {"RefNumber": "525677", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/525677"}, {"RefNumber": "511732", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/511732"}, {"RefNumber": "504892", "RefComponent": "BC-UPG", "RefTitle": "System Switch Upgrade and SPDD if ctc=1", "RefUrl": "/notes/504892"}, {"RefNumber": "502532", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/502532"}, {"RefNumber": "499708", "RefComponent": "BC-UPG", "RefTitle": "Additional information on upgrading to Basis 620 with AIX", "RefUrl": "/notes/499708"}, {"RefNumber": "496963", "RefComponent": "BC-UPG-RDM", "RefTitle": "32-bit source release in upgrades to 620", "RefUrl": "/notes/496963"}, {"RefNumber": "485741", "RefComponent": "BC-UPG", "RefTitle": "Processing of customer translations in the upgrade", "RefUrl": "/notes/485741"}, {"RefNumber": "485455", "RefComponent": "BC-I18", "RefTitle": "Change in code page structure for Release >= 6.10", "RefUrl": "/notes/485455"}, {"RefNumber": "430318", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/430318"}, {"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389"}, {"RefNumber": "304809", "RefComponent": "BC-OP-FTS-REL", "RefTitle": "SAPCAR does not start", "RefUrl": "/notes/304809"}, {"RefNumber": "29972", "RefComponent": "BC-INS", "RefTitle": "Instance numbers in a distributed system", "RefUrl": "/notes/29972"}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022"}, {"RefNumber": "211077", "RefComponent": "BC-UPG-PRP", "RefTitle": "Replacement of target release kernel for upgrade/EHPI", "RefUrl": "/notes/211077"}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886"}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227"}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980"}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926"}, {"RefNumber": "1170069", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1170069"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022 "}, {"RefNumber": "783308", "RefComponent": "BC-UPG", "RefTitle": "TLIBG inconsistencies before the upgrade", "RefUrl": "/notes/783308 "}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227 "}, {"RefNumber": "211077", "RefComponent": "BC-UPG-PRP", "RefTitle": "Replacement of target release kernel for upgrade/EHPI", "RefUrl": "/notes/211077 "}, {"RefNumber": "623723", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Upgrade: Application-specific problems", "RefUrl": "/notes/623723 "}, {"RefNumber": "699458", "RefComponent": "BC-I18", "RefTitle": "UPGRADEPHASE JOB_RDDNTPUR: \"UMG_TEST_A\" exist without DDIC", "RefUrl": "/notes/699458 "}, {"RefNumber": "485455", "RefComponent": "BC-I18", "RefTitle": "Change in code page structure for Release >= 6.10", "RefUrl": "/notes/485455 "}, {"RefNumber": "865142", "RefComponent": "BC-MID-ALE", "RefTitle": "Customer-specific entries in EDIFCT are deleted", "RefUrl": "/notes/865142 "}, {"RefNumber": "669656", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info about upgrade to SAP Web AS 6.40 MaxDB", "RefUrl": "/notes/669656 "}, {"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623 "}, {"RefNumber": "670644", "RefComponent": "BC-MOB", "RefTitle": "UPGRADE: SAP Mobile Infrastructure 2.5 - Composite Note", "RefUrl": "/notes/670644 "}, {"RefNumber": "661569", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP Web AS 6.40: iSeries", "RefUrl": "/notes/661569 "}, {"RefNumber": "664475", "RefComponent": "BC-UPG-RDM", "RefTitle": "iSeries: Additional information on upgrading to BW 3.5", "RefUrl": "/notes/664475 "}, {"RefNumber": "781092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrade to SAP Web AS 6.40 SR1 (iSeries", "RefUrl": "/notes/781092 "}, {"RefNumber": "485741", "RefComponent": "BC-UPG", "RefTitle": "Processing of customer translations in the upgrade", "RefUrl": "/notes/485741 "}, {"RefNumber": "5470", "RefComponent": "BC-I18", "RefTitle": "Syslog E14: Reorganized Buffer RSCPCCC ...", "RefUrl": "/notes/5470 "}, {"RefNumber": "672651", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel.6.40", "RefUrl": "/notes/672651 "}, {"RefNumber": "626408", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Upgrade: Restoring variants during the release upgrade", "RefUrl": "/notes/626408 "}, {"RefNumber": "726964", "RefComponent": "BC-INS", "RefTitle": "OBSOLETE: SAP Web AS 6.40 based ABAP installation/upgrade Asia", "RefUrl": "/notes/726964 "}, {"RefNumber": "669236", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to WEB AS 6.40 with MS SQL Server", "RefUrl": "/notes/669236 "}, {"RefNumber": "662219", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 ORACLE", "RefUrl": "/notes/662219 "}, {"RefNumber": "673066", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Upgrade to SAP Enterprise 4.7 (or Basis 6.20)", "RefUrl": "/notes/673066 "}, {"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389 "}, {"RefNumber": "496963", "RefComponent": "BC-UPG-RDM", "RefTitle": "32-bit source release in upgrades to 620", "RefUrl": "/notes/496963 "}, {"RefNumber": "304809", "RefComponent": "BC-OP-FTS-REL", "RefTitle": "SAPCAR does not start", "RefUrl": "/notes/304809 "}, {"RefNumber": "547773", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: interface method names are truncated", "RefUrl": "/notes/547773 "}, {"RefNumber": "792850", "RefComponent": "BC-SEC-LGN", "RefTitle": "Preparing ABAP systems to deal with incompatible passwords", "RefUrl": "/notes/792850 "}, {"RefNumber": "826528", "RefComponent": "BC-DB-MSS", "RefTitle": "Some profile parameters not recognized.", "RefUrl": "/notes/826528 "}, {"RefNumber": "647130", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information for upgrading to Basis 6.40/INFORMIX", "RefUrl": "/notes/647130 "}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886 "}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258 "}, {"RefNumber": "557314", "RefComponent": "BC-ABA-TO", "RefTitle": "As of Release 610: TVARV replaced with TVARVC", "RefUrl": "/notes/557314 "}, {"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821 "}, {"RefNumber": "716378", "RefComponent": "BC-UPG-PRP", "RefTitle": "Missing libraries during Unicode upgrade", "RefUrl": "/notes/716378 "}, {"RefNumber": "662191", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Additions to upgrade (based) on SAP Web AS 6.40", "RefUrl": "/notes/662191 "}, {"RefNumber": "737099", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS upgrade step fails", "RefUrl": "/notes/737099 "}, {"RefNumber": "885955", "RefComponent": "BC-UPG", "RefTitle": "Language content in DDIC import not imported (Upg 640/700)", "RefUrl": "/notes/885955 "}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240 "}, {"RefNumber": "778198", "RefComponent": "BC-FES-ITS", "RefTitle": "SIAC_XPRA_CONVERT_FROM_4X_64, upgrade to Netweaver 04", "RefUrl": "/notes/778198 "}, {"RefNumber": "849925", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: termination in phase TABIM_UPG / SHADOW_IMPORT_UPG2", "RefUrl": "/notes/849925 "}, {"RefNumber": "29972", "RefComponent": "BC-INS", "RefTitle": "Instance numbers in a distributed system", "RefUrl": "/notes/29972 "}, {"RefNumber": "718912", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Message DI829 during mass activation of lock objects", "RefUrl": "/notes/718912 "}, {"RefNumber": "705724", "RefComponent": "BC-MOB", "RefTitle": "Upgrade stops from 620 to 640 WebAS - adjustment of MESYBODY", "RefUrl": "/notes/705724 "}, {"RefNumber": "705943", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPDD - Activation program is started during reset", "RefUrl": "/notes/705943 "}, {"RefNumber": "499708", "RefComponent": "BC-UPG", "RefTitle": "Additional information on upgrading to Basis 620 with AIX", "RefUrl": "/notes/499708 "}, {"RefNumber": "526694", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol based on AIX perfstat library", "RefUrl": "/notes/526694 "}, {"RefNumber": "504892", "RefComponent": "BC-UPG", "RefTitle": "System Switch Upgrade and SPDD if ctc=1", "RefUrl": "/notes/504892 "}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980 "}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}