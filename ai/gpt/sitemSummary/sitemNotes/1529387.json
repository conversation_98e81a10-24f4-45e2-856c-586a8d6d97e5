{"Request": {"Number": "1529387", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 514, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009070672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001529387?language=E&token=8A44689AEF0041065AB7FE4D73025A98"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001529387", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001529387/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1529387"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "MDM-CLT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Client System Adapter"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP NetWeaver Master Data Management", "value": "MDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client System Adapter", "value": "MDM-CLT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MDM-CLT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1529387 - Tables MDMFDBEVENT, MDMFDBID, MDMFDBPR growing significantly"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note concerns using the MDM-specific transaction codes in the IDoc inbound processing without using MDM.<br/>The database tables MDMFDBID, MDMFDBPR, and MDMF<PERSON>BEVENT increase significantly. You cannot delete the tables with report RMDM_CLEAR_FEEDBACK (Note 706478).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>1. You do not use SAP MDM.<br/>2. ALE scenarios to exchange master data are set up in the system.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Use transaction WE20 to check whether one of the following transaction codes is entered as an inbound parameter for the message types ADRMAS, CLFMAS, CREMAS, DEBMAS, MATMAS, and so on.<br/><br/>BAPI_MDM      Inbound address IDocs: MDM Version<br/>BAPI_MDM_TA   Inbound tech. installations: MDM Version<br/>BAPI_MDM_MATERIAL_RT   Inbound ARTMAS: MDM Version<br/>BOM2          Inbound BOMMAT: MDM Version<br/>CRE2          MDM: Inbound vendor from MDS via XI<br/>DEB2          MDM: Inbound customer from MDS via XI<br/>DOC2          DOCMAS  Documents for MDM<br/>ECM2          Change management: MDM Plugin Version<br/>MAT2          MDM: Inbound material from MDS via XI<br/><br/>These are MDM-specific transaction codes with inbound processing developed especially for MDM. These inbound processings write entries (among other things) to the database tables MDMFDBID, MDMFDBPR, and MDMFDBEVENT. These table entries are processed only if MDM is in use.<br/><br/>If you do not use MDM, you should enter the default transactions codes for the partner profiles instead of the MDM transaction codes. The transaction code BAPI_MDM_TA for the message type MDM_EQUIPMENT_SAVEREPLICA does not have to be changed. The message type is used only by MDM.  The correct transaction codes for all other message types are:<br/><br/>BAPI for ADR2MAS, ADR3MAS, ADRMAS, ARTMAS<br/>BOMM for BOMMAT<br/>CRE1 for CREMAS<br/>DEBM for DEBMAS<br/>DOCM for DOCMAS<br/>ECMM for ECMMAS<br/>MATM for MATMAS<br/><br/>Change the settings in transaction WE20 accordingly.<br/><br/>If you have already maintained a large number of partner profiles in your system and it requires too much effort to change each individual profile, you can change the function module that belongs to a specific transaction code instead. For this, use transaction BD67.  Maintain the following entries:<br/><br/>Transaction code\t   Function module<br/>BAPI_MDM               BAPI_IDOC_INPUT1<br/>BAPI_MDM_MATERIAL_RT   BAPI_IDOC_INPUT1<br/>BOM2                   IDOC_INPUT_BOMMAT<br/>CRE2                   IDOC_INPUT_CREDITOR<br/>DEB2                   IDOC_INPUT_DEBITOR<br/>DOC2                   IDOC_INPUT_DOCMAS<br/>ECM2                   IDOC_INPUT_ECMMAS<br/>MAT2                   IDOC_INPUT_MATMAS01<br/><br/>If you have not yet generated partner profiles, delete the MDM-specific transaction codes in transactions WE42 and BD67 before the generation. The general transaction codes are determined automatically when new partner profiles are generated.<br/><br/>You can use the program ZMDM_DELETE_FEEDBACK to delete the entries in the tables MDMFDBID, MDMFDBPR, MDMFDBEVENT, and MDMCHP. The source code for this program is attached to this note.<br/></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D003292"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D034802)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001529387/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001529387/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2267295", "RefComponent": "LO-MD-MM", "RefTitle": "S4TWL - MDM 3.0 integration", "RefUrl": "/notes/2267295 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI_BASIS", "From": "2005_1_620", "To": "2005_1_700", "Subsequent": "X"}, {"SoftwareComponent": "PI_BASIS", "From": "2006_1_620", "To": "2006_1_710", "Subsequent": "X"}, {"SoftwareComponent": "PI_BASIS", "From": "701", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "PI_BASIS", "From": "711", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "PI_BASIS", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "PI_BASIS", "From": "740", "To": "740", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "PI_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0001529387/292"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}