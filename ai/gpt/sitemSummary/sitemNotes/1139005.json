{"Request": {"Number": "1139005", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 849, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016460942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001139005?language=E&token=786D115E856DA550E22BDAF28210EE41"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001139005", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001139005/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1139005"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 85}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "XAP-EM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Emissions Management (SAP xEM)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Collaborative Cross Applications", "value": "XAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Emissions Management (SAP xEM)", "value": "XAP-EM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP-EM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1139005 - SAP Environmental Compliance 3.0 Central Note"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains main release information for SAP Environmental Compliance 3.0.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;EC, xEM, Emissions Management, Environmental Compliance, Support Package, SP, Service Pack, SAP Environmental Performance 3.0, EEM, Energy Enterprise Management&#65279;</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Knowledge of the Installation, Upgrade &amp; Configuration Steps</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>SAP Environmental Compliance 3.0 requires a&#160;minimum Support Level of SP04 of SAP NetWeaver Application Server 7.5. Note that HANA DB is not supported.</p>\r\n<p>Find the information of all Support Packages available for SAP Environmental Compliance 3.0 in <a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/pam?hash=pvnr%3D01200314690900001673%26pt%3Dg%257Cs%26ainstnr%3D01200314694900007527\">Product Availability Matrix</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> The delivery strategy of patches for SAP Environmental Compliance 3.0 has changed with Support Package SP14. Find more details in SAP Note <a target=\"_blank\" href=\"/notes/1821681\">1821681</a>.</p>\r\n<p>Find latest information on languages supported by SAP Environmental Compliance 3.0 in <a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/pam?hash=pvnr%3D01200314690900001673%26pt%3Dt%257CLANG%26ainstnr%3D01200314694900007527\">Product Availabilty Matrix</a>.</p>\r\n<p>Refer to the implementation guide document at <a target=\"_blank\" href=\"http://help.sap.com/ec30\">http://help.sap.com/ec30</a>&#160;-&gt; Installation and Upgrade Information&#160;-&gt; <a target=\"_blank\" href=\"https://help.sap.com/http.svc/rc/59d212369cfb441e9ffce17f3fe5ac5c/3.0/en-US/EC30_ImpGuide.pdf\">Implementation Guide</a>.</p>\r\n<p>Download the installation files or Support Packages of SAP Environmental Compliance 3.0 from the download areas specified in <a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/pam?hash=pvnr%3D01200314690900001673%26pt%3Dsd%257Cswdc%26ainstnr%3D01200314694900007527\">Product Availability Matrix</a>.<br /><span style=\"text-decoration: underline;\">Note:</span> You can directly install latest Support Package and Patch Level of SAP Environmental Compliance 3.0 instead of applying each Support Package individually. The manual steps of each Support Package must be processed anyway.</p>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> Refer to SAP Note <a target=\"_blank\" href=\"/notes/1577275\">1577275</a>&#160;for latest information on BI content delivered with SAP Environmental Compliance 3.0. The content is delivered to folder \"<em>ABAP\\BI_Content\"</em> by default. Use BI installation code&#160;<em>7DE4447D5A</em>.<strong><br /></strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Updating SAP Environmental Compliance 2.0 to SAP Environmental Compliance 3.0</strong></p>\r\n<p>For further information refer to SAP Note <a target=\"_blank\" href=\"/notes/2133601\">2133601</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> Upgrade of BI Content from SAP Environmental Compliance 2.0 SP13 to SAP Environmental Compliance 3.0 in course of the SAP BI Upgrade from releases 6.20/6.40 to 7.0x is not supported. First upgrade SAP BI to release 7.0x and afterwards follow the instructions to upgrade the&#160;SAP Environmental&#160;Compliance&#160;BI content to&#160;release 3.0.</p>\r\n<p>&#160;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XAP-EM-INT (Emissions Management Integration)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054589)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054619)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139005/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3169056", "RefComponent": "XAP-EM", "RefTitle": "Migrate SAP EC 3.0 to SAP NetWeaver Developer Studio 7.5", "RefUrl": "/notes/3169056"}, {"RefNumber": "2204313", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP20: Corrections contained", "RefUrl": "/notes/2204313"}, {"RefNumber": "2133601", "RefComponent": "XAP-EM", "RefTitle": "Migration from SAP xEM 2.0 to SAP EC 3.0", "RefUrl": "/notes/2133601"}, {"RefNumber": "2122314", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP19: Corrections contained", "RefUrl": "/notes/2122314"}, {"RefNumber": "2122272", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP19 Installation Note", "RefUrl": "/notes/2122272"}, {"RefNumber": "1935780", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP16: Contained corrections", "RefUrl": "/notes/1935780"}, {"RefNumber": "1919805", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 Patch 4 Note", "RefUrl": "/notes/1919805"}, {"RefNumber": "1913426", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 Patch 3 Note", "RefUrl": "/notes/1913426"}, {"RefNumber": "1913424", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 9 Note", "RefUrl": "/notes/1913424"}, {"RefNumber": "1902148", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP16 installation note", "RefUrl": "/notes/1902148"}, {"RefNumber": "1900834", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 8 Note", "RefUrl": "/notes/1900834"}, {"RefNumber": "1898958", "RefComponent": "XAP-EM", "RefTitle": "Poor database performance when accessing EC data", "RefUrl": "/notes/1898958"}, {"RefNumber": "1898957", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 7 Note", "RefUrl": "/notes/1898957"}, {"RefNumber": "1895096", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 6 Note", "RefUrl": "/notes/1895096"}, {"RefNumber": "1893730", "RefComponent": "XAP-EM", "RefTitle": "Environmental Compliance Step by Step BI Configuration Guide", "RefUrl": "/notes/1893730"}, {"RefNumber": "1892106", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 Patch 1 Note", "RefUrl": "/notes/1892106"}, {"RefNumber": "1887052", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 5 Note", "RefUrl": "/notes/1887052"}, {"RefNumber": "1879150", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1879150"}, {"RefNumber": "1874218", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 4 Note", "RefUrl": "/notes/1874218"}, {"RefNumber": "1858675", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 3 Note", "RefUrl": "/notes/1858675"}, {"RefNumber": "1842644", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 installation note", "RefUrl": "/notes/1842644"}, {"RefNumber": "1837663", "RefComponent": "XAP-EM", "RefTitle": "EC Log Viewer doesnt support several server nodes in cluster", "RefUrl": "/notes/1837663"}, {"RefNumber": "1836025", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 2 Note", "RefUrl": "/notes/1836025"}, {"RefNumber": "1821681", "RefComponent": "XAP-EM", "RefTitle": "Software delivery strategy of Environmental Compliance 3.0", "RefUrl": "/notes/1821681"}, {"RefNumber": "1819798", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 1 Note", "RefUrl": "/notes/1819798"}, {"RefNumber": "1810160", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 6 Note", "RefUrl": "/notes/1810160"}, {"RefNumber": "1797493", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 5 Note", "RefUrl": "/notes/1797493"}, {"RefNumber": "1796766", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 4 Note", "RefUrl": "/notes/1796766"}, {"RefNumber": "1784616", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 3 Note", "RefUrl": "/notes/1784616"}, {"RefNumber": "1773908", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 installation note", "RefUrl": "/notes/1773908"}, {"RefNumber": "1773327", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 2 Note", "RefUrl": "/notes/1773327"}, {"RefNumber": "1771142", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 1 Note", "RefUrl": "/notes/1771142"}, {"RefNumber": "1764005", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 5 Note", "RefUrl": "/notes/1764005"}, {"RefNumber": "1764004", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 4", "RefUrl": "/notes/1764004"}, {"RefNumber": "1758257", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 3", "RefUrl": "/notes/1758257"}, {"RefNumber": "1742437", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 2", "RefUrl": "/notes/1742437"}, {"RefNumber": "1729028", "RefComponent": "XAP-EM", "RefTitle": "Update from EC 3.0 SP9 to a newer EC support package failed", "RefUrl": "/notes/1729028"}, {"RefNumber": "1727944", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Enterprise Energy Management initial data content", "RefUrl": "/notes/1727944"}, {"RefNumber": "1726367", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 1", "RefUrl": "/notes/1726367"}, {"RefNumber": "1716297", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 installation note", "RefUrl": "/notes/1716297"}, {"RefNumber": "1706265", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 4 Note", "RefUrl": "/notes/1706265"}, {"RefNumber": "1698042", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 3 Note", "RefUrl": "/notes/1698042"}, {"RefNumber": "1692628", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 2 Note", "RefUrl": "/notes/1692628"}, {"RefNumber": "1667786", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 1 Note", "RefUrl": "/notes/1667786"}, {"RefNumber": "1666327", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 installation note", "RefUrl": "/notes/1666327"}, {"RefNumber": "1648480", "RefComponent": "XX-SER-REL", "RefTitle": "Maintenance for SAP Business Suite 7 Software including SAP NetWeaver", "RefUrl": "/notes/1648480"}, {"RefNumber": "1635825", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Patch 3", "RefUrl": "/notes/1635825"}, {"RefNumber": "1631529", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Patch 2", "RefUrl": "/notes/1631529"}, {"RefNumber": "1612312", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Patch 1", "RefUrl": "/notes/1612312"}, {"RefNumber": "1612311", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Note", "RefUrl": "/notes/1612311"}, {"RefNumber": "1602821", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "UME Principal Update is not reflected in UME Cache", "RefUrl": "/notes/1602821"}, {"RefNumber": "1602286", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP09 PL02 Note", "RefUrl": "/notes/1602286"}, {"RefNumber": "1598056", "RefComponent": "XAP-EM", "RefTitle": "Environmental Compliance 3.0 Export Import tool", "RefUrl": "/notes/1598056"}, {"RefNumber": "1582235", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP09 PL01 Note", "RefUrl": "/notes/1582235"}, {"RefNumber": "1577275", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1577275"}, {"RefNumber": "1562009", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Note", "RefUrl": "/notes/1562009"}, {"RefNumber": "1562008", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP09 PL0 Note", "RefUrl": "/notes/1562008"}, {"RefNumber": "1555424", "RefComponent": "XAP-EM", "RefTitle": "Can not deploy EC 3.0 SP7 (or later releases) with JSPM", "RefUrl": "/notes/1555424"}, {"RefNumber": "1545654", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP7 Patch 2 Note", "RefUrl": "/notes/1545654"}, {"RefNumber": "1537589", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP7 Patch 1 Note", "RefUrl": "/notes/1537589"}, {"RefNumber": "1535489", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP08 Note", "RefUrl": "/notes/1535489"}, {"RefNumber": "1532805", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-On Compatibility of SAP NetWeaver 7.3", "RefUrl": "/notes/1532805"}, {"RefNumber": "1528170", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP6 Patch 2 Note", "RefUrl": "/notes/1528170"}, {"RefNumber": "1523661", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP6 Patch 1 Note", "RefUrl": "/notes/1523661"}, {"RefNumber": "1517403", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP07 Note", "RefUrl": "/notes/1517403"}, {"RefNumber": "1517139", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Installation Note: Import EC User Groups", "RefUrl": "/notes/1517139"}, {"RefNumber": "1503113", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP06 Note", "RefUrl": "/notes/1503113"}, {"RefNumber": "1500133", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 2.0 SP15 PL00", "RefUrl": "/notes/1500133"}, {"RefNumber": "1500103", "RefComponent": "XAP-EM", "RefTitle": "SAP MII 12.1 Actions for Environmental Compliance 3.0", "RefUrl": "/notes/1500103"}, {"RefNumber": "1491533", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP4 Patch 2 Note", "RefUrl": "/notes/1491533"}, {"RefNumber": "1486338", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP4 Patch 1 Note", "RefUrl": "/notes/1486338"}, {"RefNumber": "1483622", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP05 Note", "RefUrl": "/notes/1483622"}, {"RefNumber": "1469166", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP3 PL3", "RefUrl": "/notes/1469166"}, {"RefNumber": "1468349", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SAP Business Suite 7 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1468349"}, {"RefNumber": "1466804", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1466804"}, {"RefNumber": "1461732", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP4 Note", "RefUrl": "/notes/1461732"}, {"RefNumber": "1456746", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1456746"}, {"RefNumber": "1451677", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 7", "RefUrl": "/notes/1451677"}, {"RefNumber": "1449222", "RefComponent": "XAP-EM", "RefTitle": "Transfer of SAP Environmental Compliance 3.0 Master Data", "RefUrl": "/notes/1449222"}, {"RefNumber": "1447289", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP3 Note", "RefUrl": "/notes/1447289"}, {"RefNumber": "1445312", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 6", "RefUrl": "/notes/1445312"}, {"RefNumber": "1441756", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 4", "RefUrl": "/notes/1441756"}, {"RefNumber": "1436938", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 5", "RefUrl": "/notes/1436938"}, {"RefNumber": "1434886", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP3 PL1", "RefUrl": "/notes/1434886"}, {"RefNumber": "1431058", "RefComponent": "XAP-EM", "RefTitle": "Object Based Navigation in SAP Environmental Compliance 3.0", "RefUrl": "/notes/1431058"}, {"RefNumber": "1428852", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 3", "RefUrl": "/notes/1428852"}, {"RefNumber": "1428851", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 2", "RefUrl": "/notes/1428851"}, {"RefNumber": "1427501", "RefComponent": "XAP-EM", "RefTitle": "Environmental Compliance 3.0 XML Transfer Workbench", "RefUrl": "/notes/1427501"}, {"RefNumber": "1416985", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 1", "RefUrl": "/notes/1416985"}, {"RefNumber": "1415724", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Functional Documentation", "RefUrl": "/notes/1415724"}, {"RefNumber": "1406695", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2", "RefUrl": "/notes/1406695"}, {"RefNumber": "1401056", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP1 Patch 1", "RefUrl": "/notes/1401056"}, {"RefNumber": "1353368", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP1", "RefUrl": "/notes/1353368"}, {"RefNumber": "1346793", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP0 Patch 3", "RefUrl": "/notes/1346793"}, {"RefNumber": "1332535", "RefComponent": "XAP-EM", "RefTitle": "Download Instruction for SAP Environmental Compliance", "RefUrl": "/notes/1332535"}, {"RefNumber": "1311784", "RefComponent": "XAP-EM", "RefTitle": "MII 12.0 Actions for Environmental Compliance 3.0", "RefUrl": "/notes/1311784"}, {"RefNumber": "1307251", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP0 Patch 2", "RefUrl": "/notes/1307251"}, {"RefNumber": "1303115", "RefComponent": "XAP-EM", "RefTitle": "BI Additional Documentation for Environmental Compliance 3.0", "RefUrl": "/notes/1303115"}, {"RefNumber": "1297538", "RefComponent": "XAP-EM", "RefTitle": "SAP EC 3.0 adjustment of XSLT Templates for EC Reporting", "RefUrl": "/notes/1297538"}, {"RefNumber": "1297451", "RefComponent": "XAP-EM", "RefTitle": "Export/ Import SAP Environmental Compliance 3.0 Data", "RefUrl": "/notes/1297451"}, {"RefNumber": "1277367", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 Release Notes", "RefUrl": "/notes/1277367"}, {"RefNumber": "1177820", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance (xEM) 2.0 SP13 Central Note", "RefUrl": "/notes/1177820"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3169056", "RefComponent": "XAP-EM", "RefTitle": "Migrate SAP EC 3.0 to SAP NetWeaver Developer Studio 7.5", "RefUrl": "/notes/3169056 "}, {"RefNumber": "2841231", "RefComponent": "XAP-EM", "RefTitle": "Rendering Issues in Web Browsers", "RefUrl": "/notes/2841231 "}, {"RefNumber": "2797898", "RefComponent": "XAP-EM", "RefTitle": "Supported Releases of SAP Business Warehouse", "RefUrl": "/notes/2797898 "}, {"RefNumber": "2565049", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP24: Corrections contained", "RefUrl": "/notes/2565049 "}, {"RefNumber": "2406824", "RefComponent": "XAP-EM", "RefTitle": "Error during BI extraction: \"No Idocs generated;external system\"", "RefUrl": "/notes/2406824 "}, {"RefNumber": "2484011", "RefComponent": "XAP-EM", "RefTitle": "Deployment-Fehler beim Installieren von SAP Environmental Compliance 3.0", "RefUrl": "/notes/2484011 "}, {"RefNumber": "2422036", "RefComponent": "XAP-EM", "RefTitle": "Missing authorizations in SAP Environmental Compliance 3.0", "RefUrl": "/notes/2422036 "}, {"RefNumber": "2441709", "RefComponent": "XAP-EM", "RefTitle": "Error when executing the function GetTechInfo()", "RefUrl": "/notes/2441709 "}, {"RefNumber": "2436778", "RefComponent": "XAP-EM", "RefTitle": "Error while executing the data migrations \"Global Authorizations Migration\" and \"Facility Object Permission Migration\"", "RefUrl": "/notes/2436778 "}, {"RefNumber": "2434638", "RefComponent": "XAP-EM", "RefTitle": "Initial data cannot be imported or data migrations display an error after execution", "RefUrl": "/notes/2434638 "}, {"RefNumber": "2423339", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP23: Corrections contained", "RefUrl": "/notes/2423339 "}, {"RefNumber": "2416065", "RefComponent": "XAP-EM", "RefTitle": "Simultaneous closing of multiple exceptions", "RefUrl": "/notes/2416065 "}, {"RefNumber": "2393722", "RefComponent": "XAP-EM", "RefTitle": "Error when extracting facility hierarchies to Business Intelligence", "RefUrl": "/notes/2393722 "}, {"RefNumber": "2389135", "RefComponent": "XAP-EM", "RefTitle": "Error in pivot queries with query data sources of the type SQL", "RefUrl": "/notes/2389135 "}, {"RefNumber": "2380446", "RefComponent": "XAP-EM", "RefTitle": "Composite SAP Note for calculation topics in SAP Environmental Compliance 3.0", "RefUrl": "/notes/2380446 "}, {"RefNumber": "2376114", "RefComponent": "XAP-EM", "RefTitle": "Connecting multiple SAP BI systems to an SAP EC system", "RefUrl": "/notes/2376114 "}, {"RefNumber": "2342669", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP22: Corrections contained", "RefUrl": "/notes/2342669 "}, {"RefNumber": "2271170", "RefComponent": "XAP-EM", "RefTitle": "Value list \"Tier\" for parameters and consumptions", "RefUrl": "/notes/2271170 "}, {"RefNumber": "1384199", "RefComponent": "XAP-EM", "RefTitle": "Handle huge data amount in database tables using SAPTrans", "RefUrl": "/notes/1384199 "}, {"RefNumber": "2328104", "RefComponent": "XAP-EM", "RefTitle": "Error when extracting data using process chains", "RefUrl": "/notes/2328104 "}, {"RefNumber": "2306723", "RefComponent": "XAP-EM", "RefTitle": "Export generates a large volume of data", "RefUrl": "/notes/2306723 "}, {"RefNumber": "2286093", "RefComponent": "XAP-EM", "RefTitle": "Time information for consumption is not displayed in the consumption task", "RefUrl": "/notes/2286093 "}, {"RefNumber": "2284341", "RefComponent": "XAP-EM", "RefTitle": "Determining the date of an SAP EC report", "RefUrl": "/notes/2284341 "}, {"RefNumber": "2204315", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP20 Installation Note", "RefUrl": "/notes/2204315 "}, {"RefNumber": "2283936", "RefComponent": "XAP-EM", "RefTitle": "\"﻿Connection to partner timed out after 600s\" message when you execute the data migration", "RefUrl": "/notes/2283936 "}, {"RefNumber": "2283830", "RefComponent": "XAP-EM", "RefTitle": "Develop custom data sources for SAP Environmental Compliance", "RefUrl": "/notes/2283830 "}, {"RefNumber": "2264589", "RefComponent": "XAP-EM", "RefTitle": "Saving data does not require entry of comments", "RefUrl": "/notes/2264589 "}, {"RefNumber": "2234137", "RefComponent": "XAP-EM", "RefTitle": "The inheritance rules are not documented in the facility configuration", "RefUrl": "/notes/2234137 "}, {"RefNumber": "2279191", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP21: Corrections contained", "RefUrl": "/notes/2279191 "}, {"RefNumber": "2271537", "RefComponent": "XAP-EM", "RefTitle": "Hide additional hierarchy in facility navigation", "RefUrl": "/notes/2271537 "}, {"RefNumber": "2270086", "RefComponent": "XAP-EM", "RefTitle": "Several million change documents are not synchronized", "RefUrl": "/notes/2270086 "}, {"RefNumber": "2258732", "RefComponent": "XAP-EM", "RefTitle": "Translations of dialog for maintaining recurring tasks", "RefUrl": "/notes/2258732 "}, {"RefNumber": "2142592", "RefComponent": "XAP-EM", "RefTitle": "Overview of calculation functions for writing measurements, consumptions, emissions, and parameters", "RefUrl": "/notes/2142592 "}, {"RefNumber": "2216106", "RefComponent": "XAP-EM", "RefTitle": "Installing SAP Environmental Compliance 3.0 SPx the first time", "RefUrl": "/notes/2216106 "}, {"RefNumber": "2141599", "RefComponent": "XAP-EM", "RefTitle": "When you save the EC calculation, the system issues an error message about unknown characters", "RefUrl": "/notes/2141599 "}, {"RefNumber": "2204313", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP20: Corrections contained", "RefUrl": "/notes/2204313 "}, {"RefNumber": "2190110", "RefComponent": "XAP-EM", "RefTitle": "Deleting and saving data requires no entry of comments", "RefUrl": "/notes/2190110 "}, {"RefNumber": "2153004", "RefComponent": "XAP-EM", "RefTitle": "Migration of SAP Environmental Compliance 3.0 BI Content to a newer version", "RefUrl": "/notes/2153004 "}, {"RefNumber": "2148690", "RefComponent": "XAP-EM", "RefTitle": "Create own views for database tables", "RefUrl": "/notes/2148690 "}, {"RefNumber": "2145728", "RefComponent": "XAP-EM", "RefTitle": "Developing custom vAlgo functions in SAP Environmental Compliance", "RefUrl": "/notes/2145728 "}, {"RefNumber": "2132750", "RefComponent": "XAP-EM", "RefTitle": "Warnings during data import with EC export/import tool", "RefUrl": "/notes/2132750 "}, {"RefNumber": "2131868", "RefComponent": "XAP-EM", "RefTitle": "Changes to facility types or object-based authorizations or lists of values are not visible", "RefUrl": "/notes/2131868 "}, {"RefNumber": "2104933", "RefComponent": "XAP-EM", "RefTitle": "Authorization changes for EC users in SAP NetWeaver user management are not visible", "RefUrl": "/notes/2104933 "}, {"RefNumber": "2103090", "RefComponent": "XAP-EM", "RefTitle": "Series of task template of type \"recurrent\" cannot be deleted", "RefUrl": "/notes/2103090 "}, {"RefNumber": "2066725", "RefComponent": "XAP-EM", "RefTitle": "Composite SAP Note for BI topics in SAP Environmental Compliance 3.0", "RefUrl": "/notes/2066725 "}, {"RefNumber": "2092084", "RefComponent": "XAP-EM", "RefTitle": "Poor performance when EC transactional data read", "RefUrl": "/notes/2092084 "}, {"RefNumber": "2069049", "RefComponent": "XAP-EM", "RefTitle": "Developing your own e-mail notifications in SAP Environmental Compliance", "RefUrl": "/notes/2069049 "}, {"RefNumber": "2058297", "RefComponent": "XAP-EM", "RefTitle": "EC displays a 500 Internal Error", "RefUrl": "/notes/2058297 "}, {"RefNumber": "2054094", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 data updates", "RefUrl": "/notes/2054094 "}, {"RefNumber": "2029339", "RefComponent": "XAP-EM", "RefTitle": "User-defined task types in SAP Environmental Compliance 3.0", "RefUrl": "/notes/2029339 "}, {"RefNumber": "2029338", "RefComponent": "XAP-EM", "RefTitle": "Developing own BI extractors in SAP Environmental Compliance", "RefUrl": "/notes/2029338 "}, {"RefNumber": "2014757", "RefComponent": "XAP-EM", "RefTitle": "Archiving SAP Environmental Compliance data", "RefUrl": "/notes/2014757 "}, {"RefNumber": "2022726", "RefComponent": "XAP-EM", "RefTitle": "Developing own regulatory content provider in SAP Environmental Compliance", "RefUrl": "/notes/2022726 "}, {"RefNumber": "2016374", "RefComponent": "XAP-EM", "RefTitle": "The advanced search for archived tasks displays an error", "RefUrl": "/notes/2016374 "}, {"RefNumber": "2011853", "RefComponent": "XAP-EM", "RefTitle": "Errors in Environmental Compliance logs", "RefUrl": "/notes/2011853 "}, {"RefNumber": "1999354", "RefComponent": "XAP-EM", "RefTitle": "Problems during synchronization of EC change documents", "RefUrl": "/notes/1999354 "}, {"RefNumber": "1987042", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP17: Contained corrections", "RefUrl": "/notes/1987042 "}, {"RefNumber": "1986879", "RefComponent": "XAP-EM", "RefTitle": "Delta Content for migration from EC 2.0 to EC 3.0 SP16", "RefUrl": "/notes/1986879 "}, {"RefNumber": "1954794", "RefComponent": "XAP-EM", "RefTitle": "Transport of EC configuration and master data using your own number ranges", "RefUrl": "/notes/1954794 "}, {"RefNumber": "1933227", "RefComponent": "XAP-EM", "RefTitle": "Incorrectly calculated consumption values cannot be deleted in EC", "RefUrl": "/notes/1933227 "}, {"RefNumber": "1815779", "RefComponent": "XAP-EM", "RefTitle": "EC e-mail notification does not work, generates errors in logs", "RefUrl": "/notes/1815779 "}, {"RefNumber": "1919805", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 Patch 4 Note", "RefUrl": "/notes/1919805 "}, {"RefNumber": "1912671", "RefComponent": "XAP-EM", "RefTitle": "EC limit check text BI extractor extracts facilities wrong", "RefUrl": "/notes/1912671 "}, {"RefNumber": "1922760", "RefComponent": "XAP-EM", "RefTitle": "Increase speed when writing EC consumers", "RefUrl": "/notes/1922760 "}, {"RefNumber": "1925714", "RefComponent": "XAP-EM", "RefTitle": "WriteFacilityParam(): Incorrect split of facility parameters", "RefUrl": "/notes/1925714 "}, {"RefNumber": "1928337", "RefComponent": "XAP-EM", "RefTitle": "EAM notification from tasks does not close connection", "RefUrl": "/notes/1928337 "}, {"RefNumber": "1930484", "RefComponent": "XAP-EM", "RefTitle": "Deleted EC limit checks are extracted to BI", "RefUrl": "/notes/1930484 "}, {"RefNumber": "1931012", "RefComponent": "XAP-EM", "RefTitle": "File import from Excel with negative values for latitude and longitude does not work", "RefUrl": "/notes/1931012 "}, {"RefNumber": "1924083", "RefComponent": "XAP-EM", "RefTitle": "Information missing from e-mails for EC tasks", "RefUrl": "/notes/1924083 "}, {"RefNumber": "1854282", "RefComponent": "XAP-EM", "RefTitle": "EC phrases in SAP NetWeaver cluster with multiple server nodes are not synchronous", "RefUrl": "/notes/1854282 "}, {"RefNumber": "1926133", "RefComponent": "XAP-EM", "RefTitle": "Data import from Excel template does not work for permit", "RefUrl": "/notes/1926133 "}, {"RefNumber": "1927104", "RefComponent": "XAP-EM", "RefTitle": "Error when displaying EC task participations in e-mails", "RefUrl": "/notes/1927104 "}, {"RefNumber": "1913424", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 9 Note", "RefUrl": "/notes/1913424 "}, {"RefNumber": "1925630", "RefComponent": "XAP-EM", "RefTitle": "Displaying a report without the corresponding navigation", "RefUrl": "/notes/1925630 "}, {"RefNumber": "1923536", "RefComponent": "XAP-EM", "RefTitle": "EC BI extraction not working in NW cluster with many servers", "RefUrl": "/notes/1923536 "}, {"RefNumber": "1922284", "RefComponent": "XAP-EM", "RefTitle": "Responsible person for an exception is not notified", "RefUrl": "/notes/1922284 "}, {"RefNumber": "1913426", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 Patch 3 Note", "RefUrl": "/notes/1913426 "}, {"RefNumber": "1922759", "RefComponent": "XAP-EM", "RefTitle": "Restrict authority by ID in object restrictions", "RefUrl": "/notes/1922759 "}, {"RefNumber": "1915933", "RefComponent": "XAP-EM", "RefTitle": "Deleted UDF data is not extracted to BI", "RefUrl": "/notes/1915933 "}, {"RefNumber": "1913480", "RefComponent": "XAP-EM", "RefTitle": "Cannot create facility UTM coordinates correctly", "RefUrl": "/notes/1913480 "}, {"RefNumber": "1918992", "RefComponent": "XAP-EM", "RefTitle": "EERM consumptions and emissions Web service has errors", "RefUrl": "/notes/1918992 "}, {"RefNumber": "1882092", "RefComponent": "XAP-EM", "RefTitle": "Feature Release Notes for SAP EC 3.0 SP15 (Feature Pack 3)", "RefUrl": "/notes/1882092 "}, {"RefNumber": "1898597", "RefComponent": "XAP-EM", "RefTitle": "Excel upload for SAP Environmental Compliance permits", "RefUrl": "/notes/1898597 "}, {"RefNumber": "1895035", "RefComponent": "XAP-EM", "RefTitle": "Standard language of task names is deleted when saving", "RefUrl": "/notes/1895035 "}, {"RefNumber": "1914194", "RefComponent": "XAP-EM", "RefTitle": "Special characters in facility names not transferred to BI", "RefUrl": "/notes/1914194 "}, {"RefNumber": "1907735", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 Patch 2 Note", "RefUrl": "/notes/1907735 "}, {"RefNumber": "1887052", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 5 Note", "RefUrl": "/notes/1887052 "}, {"RefNumber": "1913251", "RefComponent": "XAP-EM", "RefTitle": "Multiline input field for multilingual description", "RefUrl": "/notes/1913251 "}, {"RefNumber": "1851446", "RefComponent": "XAP-EM", "RefTitle": "Deletion of data very slow after archiving", "RefUrl": "/notes/1851446 "}, {"RefNumber": "1900834", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 8 Note", "RefUrl": "/notes/1900834 "}, {"RefNumber": "1902661", "RefComponent": "XAP-EM", "RefTitle": "Label of UDF of text field type is placed incorrectly in EC", "RefUrl": "/notes/1902661 "}, {"RefNumber": "1908908", "RefComponent": "XAP-EM", "RefTitle": "Unable to create an EC Exception from Dashboard", "RefUrl": "/notes/1908908 "}, {"RefNumber": "1908534", "RefComponent": "XAP-EM", "RefTitle": "Task reference of EC measured value is not extracted", "RefUrl": "/notes/1908534 "}, {"RefNumber": "1892106", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 Patch 1 Note", "RefUrl": "/notes/1892106 "}, {"RefNumber": "1907737", "RefComponent": "XAP-EM", "RefTitle": "Data from EC is incomplete upon arrival in EEI", "RefUrl": "/notes/1907737 "}, {"RefNumber": "1903359", "RefComponent": "XAP-EM", "RefTitle": "Deletion of unnecessary database indexes of EC emissions", "RefUrl": "/notes/1903359 "}, {"RefNumber": "1893730", "RefComponent": "XAP-EM", "RefTitle": "Environmental Compliance Step by Step BI Configuration Guide", "RefUrl": "/notes/1893730 "}, {"RefNumber": "1905738", "RefComponent": "XAP-EM", "RefTitle": "EC Excel upload does not work with large no. of identifiers", "RefUrl": "/notes/1905738 "}, {"RefNumber": "1905810", "RefComponent": "XAP-EM", "RefTitle": "New EC exception has two identical exception assignees", "RefUrl": "/notes/1905810 "}, {"RefNumber": "1903845", "RefComponent": "XAP-EM", "RefTitle": "Consumptions also copied when copying EC facilities", "RefUrl": "/notes/1903845 "}, {"RefNumber": "1898958", "RefComponent": "XAP-EM", "RefTitle": "Poor database performance when accessing EC data", "RefUrl": "/notes/1898958 "}, {"RefNumber": "1902148", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP16 installation note", "RefUrl": "/notes/1902148 "}, {"RefNumber": "1861342", "RefComponent": "XAP-EM", "RefTitle": "EC20 migration tool: Data cannot be imported", "RefUrl": "/notes/1861342 "}, {"RefNumber": "1881431", "RefComponent": "XAP-EM", "RefTitle": "Delta Content for migration from EC 2.0 to EC 3.0 SP15", "RefUrl": "/notes/1881431 "}, {"RefNumber": "1899041", "RefComponent": "XAP-EM", "RefTitle": "EC authorization can be changed in production system", "RefUrl": "/notes/1899041 "}, {"RefNumber": "1898957", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 7 Note", "RefUrl": "/notes/1898957 "}, {"RefNumber": "1896121", "RefComponent": "XAP-EM", "RefTitle": "EC limit BI extractor extracts the facilities incorrectly", "RefUrl": "/notes/1896121 "}, {"RefNumber": "1895096", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 6 Note", "RefUrl": "/notes/1895096 "}, {"RefNumber": "1873534", "RefComponent": "XAP-EM", "RefTitle": "User-defined fields cannot be archived", "RefUrl": "/notes/1873534 "}, {"RefNumber": "1764207", "RefComponent": "XAP-EM", "RefTitle": "Archiving of EC consumption data restarts the server", "RefUrl": "/notes/1764207 "}, {"RefNumber": "1895552", "RefComponent": "XAP-EM", "RefTitle": "After saving the facility, the user cannot navigate", "RefUrl": "/notes/1895552 "}, {"RefNumber": "1842644", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP15 installation note", "RefUrl": "/notes/1842644 "}, {"RefNumber": "1893288", "RefComponent": "XAP-EM", "RefTitle": "Search for EC authorization profiles does not work", "RefUrl": "/notes/1893288 "}, {"RefNumber": "1891501", "RefComponent": "XAP-EM", "RefTitle": "Document details do not display the name of the originator", "RefUrl": "/notes/1891501 "}, {"RefNumber": "1468349", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SAP Business Suite 7 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1468349 "}, {"RefNumber": "1888376", "RefComponent": "XAP-EM", "RefTitle": "Entries in list of values are not marked for transport", "RefUrl": "/notes/1888376 "}, {"RefNumber": "1887822", "RefComponent": "XAP-EM", "RefTitle": "Measurement material is displayed in wrong language", "RefUrl": "/notes/1887822 "}, {"RefNumber": "1887050", "RefComponent": "XAP-EM", "RefTitle": "\"Sent From\" filter in email diagnostics does not work", "RefUrl": "/notes/1887050 "}, {"RefNumber": "1841093", "RefComponent": "XAP-EM", "RefTitle": "EC E-Mail notification doesn't have task name in the subject", "RefUrl": "/notes/1841093 "}, {"RefNumber": "1517139", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Installation Note: Import EC User Groups", "RefUrl": "/notes/1517139 "}, {"RefNumber": "1859287", "RefComponent": "XAP-EM", "RefTitle": "E-mail notification for EC warnings and EC error logs", "RefUrl": "/notes/1859287 "}, {"RefNumber": "1821681", "RefComponent": "XAP-EM", "RefTitle": "Software delivery strategy of Environmental Compliance 3.0", "RefUrl": "/notes/1821681 "}, {"RefNumber": "1879491", "RefComponent": "XAP-EM", "RefTitle": "Split of EC Facility Parameter values doesnt work correctly", "RefUrl": "/notes/1879491 "}, {"RefNumber": "1845486", "RefComponent": "XAP-EM", "RefTitle": "SAP MII 14.0 Actions for Environmental Compliance 3.0", "RefUrl": "/notes/1845486 "}, {"RefNumber": "1808740", "RefComponent": "XAP-EM", "RefTitle": "SAP MII 12.2 Actions for Environmental Compliance 3.0", "RefUrl": "/notes/1808740 "}, {"RefNumber": "1877649", "RefComponent": "XAP-EM", "RefTitle": "Information about unsaved data when creating permit", "RefUrl": "/notes/1877649 "}, {"RefNumber": "1874218", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 4 Note", "RefUrl": "/notes/1874218 "}, {"RefNumber": "1874217", "RefComponent": "XAP-EM", "RefTitle": "EC report displays: \"Filter type recipe is not supported\"", "RefUrl": "/notes/1874217 "}, {"RefNumber": "1858675", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 3 Note", "RefUrl": "/notes/1858675 "}, {"RefNumber": "1827610", "RefComponent": "XAP-EM", "RefTitle": "Synchronization of EC change documents runs for a very long time", "RefUrl": "/notes/1827610 "}, {"RefNumber": "1872820", "RefComponent": "XAP-EM", "RefTitle": "BI extraction of facility texts does not display ID in PSA", "RefUrl": "/notes/1872820 "}, {"RefNumber": "1827553", "RefComponent": "XAP-EM", "RefTitle": "Consumption, emissions & measure. val. tble display all data", "RefUrl": "/notes/1827553 "}, {"RefNumber": "1861537", "RefComponent": "XAP-EM", "RefTitle": "Batch recipes with several validity periods display errors", "RefUrl": "/notes/1861537 "}, {"RefNumber": "1859118", "RefComponent": "XAP-EM", "RefTitle": "Required-entry field check for user-defined fields with currency type", "RefUrl": "/notes/1859118 "}, {"RefNumber": "1836025", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 2 Note", "RefUrl": "/notes/1836025 "}, {"RefNumber": "1837663", "RefComponent": "XAP-EM", "RefTitle": "EC Log Viewer doesnt support several server nodes in cluster", "RefUrl": "/notes/1837663 "}, {"RefNumber": "1837144", "RefComponent": "XAP-EM", "RefTitle": "Changing numeric EC user-defined fields", "RefUrl": "/notes/1837144 "}, {"RefNumber": "1853744", "RefComponent": "XAP-EM", "RefTitle": "E-mail notification for completed EC tasks", "RefUrl": "/notes/1853744 "}, {"RefNumber": "1853534", "RefComponent": "XAP-EM", "RefTitle": "Environmental Compliance exception cannot be deleted", "RefUrl": "/notes/1853534 "}, {"RefNumber": "1855640", "RefComponent": "XAP-EM", "RefTitle": "It is not possible to create an EC requirement", "RefUrl": "/notes/1855640 "}, {"RefNumber": "1841095", "RefComponent": "XAP-EM", "RefTitle": "It is not possible to forbid data import in EC application", "RefUrl": "/notes/1841095 "}, {"RefNumber": "1851444", "RefComponent": "XAP-EM", "RefTitle": "Phrase always assigned to transport request during deletion", "RefUrl": "/notes/1851444 "}, {"RefNumber": "1850365", "RefComponent": "XAP-EM", "RefTitle": "Display of technical ID for UDF in EC reporting", "RefUrl": "/notes/1850365 "}, {"RefNumber": "1845293", "RefComponent": "XAP-EM", "RefTitle": "The search of EC Authority names searches in all languages", "RefUrl": "/notes/1845293 "}, {"RefNumber": "1842868", "RefComponent": "XAP-EM", "RefTitle": "EC Exception communications has unnecessary required fields", "RefUrl": "/notes/1842868 "}, {"RefNumber": "1729904", "RefComponent": "XAP-EM", "RefTitle": "Disable EC Change Document writing for Tranactional data", "RefUrl": "/notes/1729904 "}, {"RefNumber": "1842326", "RefComponent": "XAP-EM", "RefTitle": "EC tasks are generated when facility is saved with new tasks", "RefUrl": "/notes/1842326 "}, {"RefNumber": "1823098", "RefComponent": "XAP-EM", "RefTitle": "Poor perforamce during the detection of EC email alert items", "RefUrl": "/notes/1823098 "}, {"RefNumber": "1837665", "RefComponent": "XAP-EM", "RefTitle": "EC Permits and EC Requirement Sets don't have \"Notes\" field", "RefUrl": "/notes/1837665 "}, {"RefNumber": "1837741", "RefComponent": "XAP-EM", "RefTitle": "EC Requirement does not have a type field information", "RefUrl": "/notes/1837741 "}, {"RefNumber": "1836228", "RefComponent": "XAP-EM", "RefTitle": "Password management for EC checklist task", "RefUrl": "/notes/1836228 "}, {"RefNumber": "1819798", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 Patch 1 Note", "RefUrl": "/notes/1819798 "}, {"RefNumber": "1820909", "RefComponent": "XAP-EM", "RefTitle": "\"Task Multilanguage Migration\" may restart server", "RefUrl": "/notes/1820909 "}, {"RefNumber": "1831445", "RefComponent": "XAP-EM", "RefTitle": "Missing charge in batch calculation shows an exception (NPE)", "RefUrl": "/notes/1831445 "}, {"RefNumber": "1830119", "RefComponent": "XAP-EM", "RefTitle": "EC background job applications do not start automatically", "RefUrl": "/notes/1830119 "}, {"RefNumber": "1820908", "RefComponent": "XAP-EM", "RefTitle": "The requirement search displays incorrectly assigned sets", "RefUrl": "/notes/1820908 "}, {"RefNumber": "1826620", "RefComponent": "XAP-EM", "RefTitle": "EC task dialog does not display facilities in the table", "RefUrl": "/notes/1826620 "}, {"RefNumber": "1821744", "RefComponent": "XAP-EM", "RefTitle": "Task names and facility names are not translated in e-mails", "RefUrl": "/notes/1821744 "}, {"RefNumber": "1820964", "RefComponent": "XAP-EM", "RefTitle": "EC report cannot be sent by e-mail", "RefUrl": "/notes/1820964 "}, {"RefNumber": "1773908", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP14 installation note", "RefUrl": "/notes/1773908 "}, {"RefNumber": "1819799", "RefComponent": "XAP-EM", "RefTitle": "The selection lists of facility types is not sorted", "RefUrl": "/notes/1819799 "}, {"RefNumber": "1427501", "RefComponent": "XAP-EM", "RefTitle": "Environmental Compliance 3.0 XML Transfer Workbench", "RefUrl": "/notes/1427501 "}, {"RefNumber": "1818671", "RefComponent": "XAP-EM", "RefTitle": "Delta Content for migration from EC 2.0 to EC 3.0 SP14", "RefUrl": "/notes/1818671 "}, {"RefNumber": "1811219", "RefComponent": "XAP-EM", "RefTitle": "E-mail diagnosis tool in Environmental Compliance 3.0", "RefUrl": "/notes/1811219 "}, {"RefNumber": "1817491", "RefComponent": "XAP-EM", "RefTitle": "EC doesnt send an email to the additional task email address", "RefUrl": "/notes/1817491 "}, {"RefNumber": "1816297", "RefComponent": "XAP-EM", "RefTitle": "EC transport tool files cannot be exported", "RefUrl": "/notes/1816297 "}, {"RefNumber": "1716297", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 installation note", "RefUrl": "/notes/1716297 "}, {"RefNumber": "1816313", "RefComponent": "XAP-EM", "RefTitle": "Connection problem when calling the BNA Web service", "RefUrl": "/notes/1816313 "}, {"RefNumber": "1813147", "RefComponent": "XAP-EM", "RefTitle": "Documents for Environmental Compliance parameter management", "RefUrl": "/notes/1813147 "}, {"RefNumber": "1816162", "RefComponent": "XAP-EM", "RefTitle": "Consumption data Web services do not update additional logs", "RefUrl": "/notes/1816162 "}, {"RefNumber": "1813148", "RefComponent": "XAP-EM", "RefTitle": "Enhancements for the EC data import overview", "RefUrl": "/notes/1813148 "}, {"RefNumber": "1813145", "RefComponent": "XAP-EM", "RefTitle": "Export is activated for empty data table", "RefUrl": "/notes/1813145 "}, {"RefNumber": "1815131", "RefComponent": "XAP-EM", "RefTitle": "Do not save additional calculation results of consumptions", "RefUrl": "/notes/1815131 "}, {"RefNumber": "1815096", "RefComponent": "XAP-EM", "RefTitle": "Obsolete Source Category still appears in Facility Search", "RefUrl": "/notes/1815096 "}, {"RefNumber": "1814013", "RefComponent": "XAP-EM", "RefTitle": "User involvement is not specified in the e-mail", "RefUrl": "/notes/1814013 "}, {"RefNumber": "1813144", "RefComponent": "XAP-EM", "RefTitle": "CSV export for Environmental Compliance change documents", "RefUrl": "/notes/1813144 "}, {"RefNumber": "1809954", "RefComponent": "XAP-EM", "RefTitle": "Invalid units are ignored by the EC Web service", "RefUrl": "/notes/1809954 "}, {"RefNumber": "1810160", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 6 Note", "RefUrl": "/notes/1810160 "}, {"RefNumber": "1811547", "RefComponent": "XAP-EM", "RefTitle": "EC standard portal content OBN pages does not work correctly", "RefUrl": "/notes/1811547 "}, {"RefNumber": "1808321", "RefComponent": "XAP-EM", "RefTitle": "EC export/import tool supports new export option", "RefUrl": "/notes/1808321 "}, {"RefNumber": "1728609", "RefComponent": "XAP-EM", "RefTitle": "Import tool stops the import process due to duplicate key", "RefUrl": "/notes/1728609 "}, {"RefNumber": "1811128", "RefComponent": "XAP-EM", "RefTitle": "Search result of task templates shows some tasks twice", "RefUrl": "/notes/1811128 "}, {"RefNumber": "1802561", "RefComponent": "XAP-EM", "RefTitle": "Delta Content for migration from EC 2.0 to EC 3.0 SP13", "RefUrl": "/notes/1802561 "}, {"RefNumber": "1799882", "RefComponent": "XAP-EM", "RefTitle": "Russian 2TP Air Report changes for EC 3.0 SP13", "RefUrl": "/notes/1799882 "}, {"RefNumber": "1797493", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 5 Note", "RefUrl": "/notes/1797493 "}, {"RefNumber": "1800030", "RefComponent": "XAP-EM", "RefTitle": "EC consumption calculates lots of new consumptions endlessly", "RefUrl": "/notes/1800030 "}, {"RefNumber": "1784960", "RefComponent": "XAP-EM", "RefTitle": "Copying global parameters causes incorrect values", "RefUrl": "/notes/1784960 "}, {"RefNumber": "1801655", "RefComponent": "XAP-EM", "RefTitle": "Incorrect total formation in period balance and acct balance", "RefUrl": "/notes/1801655 "}, {"RefNumber": "1431058", "RefComponent": "XAP-EM", "RefTitle": "Object Based Navigation in SAP Environmental Compliance 3.0", "RefUrl": "/notes/1431058 "}, {"RefNumber": "1800630", "RefComponent": "XAP-EM", "RefTitle": "EC standard Portal Content does not provide OBN pages roles", "RefUrl": "/notes/1800630 "}, {"RefNumber": "1779410", "RefComponent": "XAP-EM", "RefTitle": "Search for EC calculations displays hidden source types", "RefUrl": "/notes/1779410 "}, {"RefNumber": "1798067", "RefComponent": "XAP-EM", "RefTitle": "Currencies could not be maintained within EC application", "RefUrl": "/notes/1798067 "}, {"RefNumber": "1763716", "RefComponent": "XAP-EM", "RefTitle": "Memory overflow due to BDF in reports or in Web Service", "RefUrl": "/notes/1763716 "}, {"RefNumber": "1791595", "RefComponent": "XAP-EM", "RefTitle": "Emission Accounting Transactions were not created", "RefUrl": "/notes/1791595 "}, {"RefNumber": "1796766", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 4 Note", "RefUrl": "/notes/1796766 "}, {"RefNumber": "1786008", "RefComponent": "XAP-EM", "RefTitle": "EEM BI extractor displays error when extracting data", "RefUrl": "/notes/1786008 "}, {"RefNumber": "1784616", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 3 Note", "RefUrl": "/notes/1784616 "}, {"RefNumber": "1790223", "RefComponent": "XAP-EM", "RefTitle": "Orphaned or empty transactions in Emissions Accounting", "RefUrl": "/notes/1790223 "}, {"RefNumber": "1778024", "RefComponent": "XAP-EM", "RefTitle": "EEM production data Web service do not update add. logs", "RefUrl": "/notes/1778024 "}, {"RefNumber": "1784572", "RefComponent": "XAP-EM", "RefTitle": "You cannot monitor EC data imports", "RefUrl": "/notes/1784572 "}, {"RefNumber": "1773327", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 2 Note", "RefUrl": "/notes/1773327 "}, {"RefNumber": "1781583", "RefComponent": "XAP-EM", "RefTitle": "Emission Accounting transfers val. incorrectly or not at all", "RefUrl": "/notes/1781583 "}, {"RefNumber": "1780293", "RefComponent": "XAP-EM", "RefTitle": "EC facilities are not displayed in the hierarchy in BI", "RefUrl": "/notes/1780293 "}, {"RefNumber": "1777160", "RefComponent": "XAP-EM", "RefTitle": "EC transactions cannot be exported to Excel", "RefUrl": "/notes/1777160 "}, {"RefNumber": "1776310", "RefComponent": "XAP-EM", "RefTitle": "\"Costs per unit\" field displayed in transaction management", "RefUrl": "/notes/1776310 "}, {"RefNumber": "1778696", "RefComponent": "XAP-EM", "RefTitle": "EC URL reports are not displayed subfolders", "RefUrl": "/notes/1778696 "}, {"RefNumber": "1778417", "RefComponent": "XAP-EM", "RefTitle": "Reference between EC tasks is not extracted", "RefUrl": "/notes/1778417 "}, {"RefNumber": "1773329", "RefComponent": "XAP-EM", "RefTitle": "Manual tasks cannot be deactivated", "RefUrl": "/notes/1773329 "}, {"RefNumber": "1673352", "RefComponent": "XAP-EM", "RefTitle": "EC Emission Accounting modifications", "RefUrl": "/notes/1673352 "}, {"RefNumber": "1773667", "RefComponent": "XAP-EM", "RefTitle": "Setting the completion date does not change the status", "RefUrl": "/notes/1773667 "}, {"RefNumber": "1771142", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP13 Patch 1 Note", "RefUrl": "/notes/1771142 "}, {"RefNumber": "1643762", "RefComponent": "XAP-EM", "RefTitle": "Russian 2TP-Reporting", "RefUrl": "/notes/1643762 "}, {"RefNumber": "1654350", "RefComponent": "XAP-EM", "RefTitle": "Slow performance of EC 3.0 transactional data web services", "RefUrl": "/notes/1654350 "}, {"RefNumber": "1768107", "RefComponent": "XAP-EM", "RefTitle": "Limit check without name can not be extracted to BI", "RefUrl": "/notes/1768107 "}, {"RefNumber": "1767281", "RefComponent": "XAP-EM", "RefTitle": "XSL transformation in EC reports does not work", "RefUrl": "/notes/1767281 "}, {"RefNumber": "1766318", "RefComponent": "XAP-EM", "RefTitle": "EC email notification shows ????? for Korean user name info", "RefUrl": "/notes/1766318 "}, {"RefNumber": "1763169", "RefComponent": "XAP-EM", "RefTitle": "Symbols in permit tree cannot be changed in EC", "RefUrl": "/notes/1763169 "}, {"RefNumber": "1764771", "RefComponent": "XAP-EM", "RefTitle": "Search for exception description not possible in EC", "RefUrl": "/notes/1764771 "}, {"RefNumber": "1764004", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 4", "RefUrl": "/notes/1764004 "}, {"RefNumber": "1640998", "RefComponent": "XAP-EM", "RefTitle": "EC email notification subject shows ?????? for Korean texts", "RefUrl": "/notes/1640998 "}, {"RefNumber": "1764005", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 5 Note", "RefUrl": "/notes/1764005 "}, {"RefNumber": "1757425", "RefComponent": "XAP-EM", "RefTitle": "SQL data source does not deliver value for text columns", "RefUrl": "/notes/1757425 "}, {"RefNumber": "1758060", "RefComponent": "XAP-EM", "RefTitle": "Exception creates admin user when assigning facilities", "RefUrl": "/notes/1758060 "}, {"RefNumber": "1658447", "RefComponent": "XAP-EM", "RefTitle": "EC web service shows wrong consumption overlapping errors", "RefUrl": "/notes/1658447 "}, {"RefNumber": "1758408", "RefComponent": "XAP-EM", "RefTitle": "EEM delta extractor does not return enough energy data to BI", "RefUrl": "/notes/1758408 "}, {"RefNumber": "1758257", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 3", "RefUrl": "/notes/1758257 "}, {"RefNumber": "1754581", "RefComponent": "XAP-EM", "RefTitle": "EC facility configuration displays error when saving", "RefUrl": "/notes/1754581 "}, {"RefNumber": "1753271", "RefComponent": "XAP-EM", "RefTitle": "The messages in the EC configuration are not visible", "RefUrl": "/notes/1753271 "}, {"RefNumber": "1639124", "RefComponent": "XAP-EM", "RefTitle": "Can not read the whole EC permit name and description", "RefUrl": "/notes/1639124 "}, {"RefNumber": "1500103", "RefComponent": "XAP-EM", "RefTitle": "SAP MII 12.1 Actions for Environmental Compliance 3.0", "RefUrl": "/notes/1500103 "}, {"RefNumber": "1735174", "RefComponent": "XAP-EM", "RefTitle": "External Reporting with SAP Environmental Compliance 3.0", "RefUrl": "/notes/1735174 "}, {"RefNumber": "1735282", "RefComponent": "XAP-EM", "RefTitle": "It is not possible to search EC units and dimensions by name", "RefUrl": "/notes/1735282 "}, {"RefNumber": "1658449", "RefComponent": "XAP-EM", "RefTitle": "Cannot extract task interested user to infosource /TDAG/EM_4", "RefUrl": "/notes/1658449 "}, {"RefNumber": "1734219", "RefComponent": "XAP-EM", "RefTitle": "Error during portal personalization of EC exceptions", "RefUrl": "/notes/1734219 "}, {"RefNumber": "1743846", "RefComponent": "XAP-EM", "RefTitle": "Time entered 23:59:59:999000 is changed to 00:00:00:000", "RefUrl": "/notes/1743846 "}, {"RefNumber": "1742437", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 2", "RefUrl": "/notes/1742437 "}, {"RefNumber": "1726779", "RefComponent": "XAP-EM", "RefTitle": "Create facilities with identical name is possible in EC", "RefUrl": "/notes/1726779 "}, {"RefNumber": "1740615", "RefComponent": "XAP-EM", "RefTitle": "Changed restriction filter is not taken over in EC reporting", "RefUrl": "/notes/1740615 "}, {"RefNumber": "1666327", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 installation note", "RefUrl": "/notes/1666327 "}, {"RefNumber": "1743848", "RefComponent": "XAP-EM", "RefTitle": "Exception extractor doesnt extract the assigned requirements", "RefUrl": "/notes/1743848 "}, {"RefNumber": "1734937", "RefComponent": "XAP-EM", "RefTitle": "Reporting generation job stopped when a report is errorneous", "RefUrl": "/notes/1734937 "}, {"RefNumber": "1687939", "RefComponent": "XAP-EM", "RefTitle": "Consumption table shows \"Error Reading Status: -1\"", "RefUrl": "/notes/1687939 "}, {"RefNumber": "1736514", "RefComponent": "XAP-EM", "RefTitle": "Missing Namespace /B123/ in BI example Content for NW7.30", "RefUrl": "/notes/1736514 "}, {"RefNumber": "1726367", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Patch 1", "RefUrl": "/notes/1726367 "}, {"RefNumber": "1731246", "RefComponent": "XAP-EM", "RefTitle": "Data migration \"UDF data cleanup\" could restart the server", "RefUrl": "/notes/1731246 "}, {"RefNumber": "1729028", "RefComponent": "XAP-EM", "RefTitle": "Update from EC 3.0 SP9 to a newer EC support package failed", "RefUrl": "/notes/1729028 "}, {"RefNumber": "1562009", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Note", "RefUrl": "/notes/1562009 "}, {"RefNumber": "1734380", "RefComponent": "XAP-EM", "RefTitle": "Error creating manually measurement values in EC tasks", "RefUrl": "/notes/1734380 "}, {"RefNumber": "1606835", "RefComponent": "XAP-EM", "RefTitle": "Can not add several materials to measurement task templates", "RefUrl": "/notes/1606835 "}, {"RefNumber": "1602286", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP09 PL02 Note", "RefUrl": "/notes/1602286 "}, {"RefNumber": "1727944", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Enterprise Energy Management initial data content", "RefUrl": "/notes/1727944 "}, {"RefNumber": "1709371", "RefComponent": "XAP-EM", "RefTitle": "Feature Release Notes for SAP EC 3.0 SP12 (Feature Pack 2)", "RefUrl": "/notes/1709371 "}, {"RefNumber": "1721490", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP12 Language Editing", "RefUrl": "/notes/1721490 "}, {"RefNumber": "1599101", "RefComponent": "XAP-EM", "RefTitle": "EC EHS Integration in ERP 6.0", "RefUrl": "/notes/1599101 "}, {"RefNumber": "1726201", "RefComponent": "XAP-EM", "RefTitle": "Can not open a facility document assigned from DMS upload", "RefUrl": "/notes/1726201 "}, {"RefNumber": "1726282", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 SP12 data migration label show wrong version \"SP11\"", "RefUrl": "/notes/1726282 "}, {"RefNumber": "1714426", "RefComponent": "XAP-EM", "RefTitle": "EC overdue task emails are send with priority medium", "RefUrl": "/notes/1714426 "}, {"RefNumber": "1722535", "RefComponent": "XAP-EM", "RefTitle": "Cannot report task users in EC data source business objects", "RefUrl": "/notes/1722535 "}, {"RefNumber": "1721116", "RefComponent": "XAP-EM", "RefTitle": "Cannot report facility property \"Status\" in EC data source", "RefUrl": "/notes/1721116 "}, {"RefNumber": "1715940", "RefComponent": "XAP-EM", "RefTitle": "Batch calculation doesnt read correct control device info", "RefUrl": "/notes/1715940 "}, {"RefNumber": "1707370", "RefComponent": "XAP-EM", "RefTitle": "Error reading Environmental Compliance facility type", "RefUrl": "/notes/1707370 "}, {"RefNumber": "1706265", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 4 Note", "RefUrl": "/notes/1706265 "}, {"RefNumber": "1714243", "RefComponent": "XAP-EM", "RefTitle": "The EC integration manager shows RFC connection errors", "RefUrl": "/notes/1714243 "}, {"RefNumber": "1693077", "RefComponent": "XAP-EM", "RefTitle": "Cannot create EC Authorization profile in Russian language", "RefUrl": "/notes/1693077 "}, {"RefNumber": "1707113", "RefComponent": "XAP-EM", "RefTitle": "Can not extract more than 1000 LDAP users from EC to BI", "RefUrl": "/notes/1707113 "}, {"RefNumber": "1665989", "RefComponent": "XAP-EM", "RefTitle": "Cannot complete EC task with empty value and without comment", "RefUrl": "/notes/1665989 "}, {"RefNumber": "1703269", "RefComponent": "XAP-EM", "RefTitle": "Unrestricted material search results in out-of-memory error", "RefUrl": "/notes/1703269 "}, {"RefNumber": "1701338", "RefComponent": "XAP-EM", "RefTitle": "Energy manager role has no access to parameters view", "RefUrl": "/notes/1701338 "}, {"RefNumber": "1667786", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 1 Note", "RefUrl": "/notes/1667786 "}, {"RefNumber": "1706266", "RefComponent": "XAP-EM", "RefTitle": "Error when displaying tasks on dashboard", "RefUrl": "/notes/1706266 "}, {"RefNumber": "1692628", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 2 Note", "RefUrl": "/notes/1692628 "}, {"RefNumber": "1698042", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Patch 3 Note", "RefUrl": "/notes/1698042 "}, {"RefNumber": "1698115", "RefComponent": "XAP-EM", "RefTitle": "EC Reporting cannot handle split facility parameters", "RefUrl": "/notes/1698115 "}, {"RefNumber": "1699975", "RefComponent": "XAP-EM", "RefTitle": "SAP NW logs shows 'BAPI_BW_FUNCTION_EXISTS' error messages", "RefUrl": "/notes/1699975 "}, {"RefNumber": "1701337", "RefComponent": "XAP-EM", "RefTitle": "XSD download servlet of EC reporting returns an error", "RefUrl": "/notes/1701337 "}, {"RefNumber": "1702744", "RefComponent": "XAP-EM", "RefTitle": "Logs of overlapped consumptions dont contain ID information", "RefUrl": "/notes/1702744 "}, {"RefNumber": "1685476", "RefComponent": "XAP-EM", "RefTitle": "EHS program deletes unchanged material classifiers", "RefUrl": "/notes/1685476 "}, {"RefNumber": "1693549", "RefComponent": "XAP-EM", "RefTitle": "Reporting of consumption material assignments does not work", "RefUrl": "/notes/1693549 "}, {"RefNumber": "1698532", "RefComponent": "XAP-EM", "RefTitle": "Error extracting classifiers in case of split EC facilities", "RefUrl": "/notes/1698532 "}, {"RefNumber": "1698114", "RefComponent": "XAP-EM", "RefTitle": "Cannot report facility property \"Cost Center\" in data source", "RefUrl": "/notes/1698114 "}, {"RefNumber": "1532805", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-On Compatibility of SAP NetWeaver 7.3", "RefUrl": "/notes/1532805 "}, {"RefNumber": "1691089", "RefComponent": "XAP-EM", "RefTitle": "EC limit check scheduler job remains in status \"Running\"", "RefUrl": "/notes/1691089 "}, {"RefNumber": "1690268", "RefComponent": "XAP-EM", "RefTitle": "EC Calculation scheduler does not calculate records", "RefUrl": "/notes/1690268 "}, {"RefNumber": "1654392", "RefComponent": "XAP-EM", "RefTitle": "Extractor ZEXT_EM_4B_D doesnt work after user is reassigned", "RefUrl": "/notes/1654392 "}, {"RefNumber": "1689619", "RefComponent": "XAP-EM", "RefTitle": "EEM delta data extraction process extracts on daily basis", "RefUrl": "/notes/1689619 "}, {"RefNumber": "1687371", "RefComponent": "XAP-EM", "RefTitle": "EEM Extractor extracts deleted records in initial load", "RefUrl": "/notes/1687371 "}, {"RefNumber": "1685612", "RefComponent": "XAP-EM", "RefTitle": "The restart of BI extractor does not show any log entries", "RefUrl": "/notes/1685612 "}, {"RefNumber": "1685233", "RefComponent": "XAP-EM", "RefTitle": "Restriction filter is not taken over in report regeneration", "RefUrl": "/notes/1685233 "}, {"RefNumber": "1684961", "RefComponent": "XAP-EM", "RefTitle": "Invalid EC tasks without owner and assignee can be created", "RefUrl": "/notes/1684961 "}, {"RefNumber": "1664821", "RefComponent": "XAP-EM", "RefTitle": "SAPTerm changes", "RefUrl": "/notes/1664821 "}, {"RefNumber": "1612311", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP11 Note", "RefUrl": "/notes/1612311 "}, {"RefNumber": "1684918", "RefComponent": "XAP-EM", "RefTitle": "Second level of EC sub tasks are not created automatically", "RefUrl": "/notes/1684918 "}, {"RefNumber": "1684917", "RefComponent": "XAP-EM", "RefTitle": "Error changing the EC master task template assignment", "RefUrl": "/notes/1684917 "}, {"RefNumber": "1663149", "RefComponent": "XAP-EM", "RefTitle": "Relevant tasks do not appear on EC dashboard of task owner", "RefUrl": "/notes/1663149 "}, {"RefNumber": "1682917", "RefComponent": "XAP-EM", "RefTitle": "BW configuration in EC list of values cannot be deleted", "RefUrl": "/notes/1682917 "}, {"RefNumber": "1669573", "RefComponent": "XAP-EM", "RefTitle": "Read all user cannot scroll in EC facility parameters table", "RefUrl": "/notes/1669573 "}, {"RefNumber": "1679371", "RefComponent": "XAP-EM", "RefTitle": "Cannot search emissions by ID in transactional data manager", "RefUrl": "/notes/1679371 "}, {"RefNumber": "1620641", "RefComponent": "XAP-EM", "RefTitle": "BI extracts wrong unit of EC parameter values", "RefUrl": "/notes/1620641 "}, {"RefNumber": "1656340", "RefComponent": "XAP-EM", "RefTitle": "Enterprise Energy Management does not extract 15 min values", "RefUrl": "/notes/1656340 "}, {"RefNumber": "1675434", "RefComponent": "XAP-EM", "RefTitle": "Missing default UDF data for new or modified UDF areas", "RefUrl": "/notes/1675434 "}, {"RefNumber": "1676434", "RefComponent": "XAP-EM", "RefTitle": "Error opening values of global parameter sets", "RefUrl": "/notes/1676434 "}, {"RefNumber": "1676548", "RefComponent": "XAP-EM", "RefTitle": "Not all user-defined data of splitted facilities is deleted", "RefUrl": "/notes/1676548 "}, {"RefNumber": "1676881", "RefComponent": "XAP-EM", "RefTitle": "Process chain reports error in case of parallel extraction", "RefUrl": "/notes/1676881 "}, {"RefNumber": "1646173", "RefComponent": "XAP-EM", "RefTitle": "No automatic start of EC accounting BI extraction classes", "RefUrl": "/notes/1646173 "}, {"RefNumber": "1665987", "RefComponent": "XAP-EM", "RefTitle": "EC does not extract deleted emissions in BW", "RefUrl": "/notes/1665987 "}, {"RefNumber": "1665913", "RefComponent": "XAP-EM", "RefTitle": "Consumption web service check ignores different tier methods", "RefUrl": "/notes/1665913 "}, {"RefNumber": "1662111", "RefComponent": "XAP-EM", "RefTitle": "Enterprise Energy Management delta extractor is very slow", "RefUrl": "/notes/1662111 "}, {"RefNumber": "1644327", "RefComponent": "XAP-EM", "RefTitle": "The EC E-Mail Scheduler doesn't bundle all e-mails correctly", "RefUrl": "/notes/1644327 "}, {"RefNumber": "1646224", "RefComponent": "XAP-EM", "RefTitle": "The EC task dashboard does not show any tasks", "RefUrl": "/notes/1646224 "}, {"RefNumber": "1667291", "RefComponent": "XAP-EM", "RefTitle": "Missing object based authorisation for EC external reports", "RefUrl": "/notes/1667291 "}, {"RefNumber": "1654219", "RefComponent": "XAP-EM", "RefTitle": "UDF of type Logical Expression doesnt work in EC reporting", "RefUrl": "/notes/1654219 "}, {"RefNumber": "1662189", "RefComponent": "XAP-EM", "RefTitle": "Missing release status export in Transactional Data Manager", "RefUrl": "/notes/1662189 "}, {"RefNumber": "1662528", "RefComponent": "XAP-EM", "RefTitle": "Cannot access data release view in EC facilities module", "RefUrl": "/notes/1662528 "}, {"RefNumber": "1656623", "RefComponent": "XAP-EM", "RefTitle": "Increase Performance of the EC calculation scheduler", "RefUrl": "/notes/1656623 "}, {"RefNumber": "1621030", "RefComponent": "XAP-EM", "RefTitle": "Configuration tool for customer database tables", "RefUrl": "/notes/1621030 "}, {"RefNumber": "1654877", "RefComponent": "XAP-EM", "RefTitle": "Importing uncertainty values of consumption parameters fails", "RefUrl": "/notes/1654877 "}, {"RefNumber": "1655598", "RefComponent": "XAP-EM", "RefTitle": "Error in permit tree when creating authority with empty name", "RefUrl": "/notes/1655598 "}, {"RefNumber": "1654612", "RefComponent": "XAP-EM", "RefTitle": "Cannot select Facility in EC Emission Accounting selector", "RefUrl": "/notes/1654612 "}, {"RefNumber": "1655258", "RefComponent": "XAP-EM", "RefTitle": "Can not delete EC Accounting transaction without status info", "RefUrl": "/notes/1655258 "}, {"RefNumber": "1653854", "RefComponent": "XAP-EM", "RefTitle": "Cannot search parameters in EC transactional data view", "RefUrl": "/notes/1653854 "}, {"RefNumber": "1646305", "RefComponent": "XAP-EM", "RefTitle": "Unused classes in Environmental Compliance 3.0", "RefUrl": "/notes/1646305 "}, {"RefNumber": "1643823", "RefComponent": "XAP-EM", "RefTitle": "Code optimization in EC Java Connector settings", "RefUrl": "/notes/1643823 "}, {"RefNumber": "1635825", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Patch 3", "RefUrl": "/notes/1635825 "}, {"RefNumber": "1635826", "RefComponent": "XAP-EM", "RefTitle": "Performance gain in overlapping check of EC web service", "RefUrl": "/notes/1635826 "}, {"RefNumber": "1633976", "RefComponent": "XAP-EM", "RefTitle": "Increase the row count of an EC Calculation script", "RefUrl": "/notes/1633976 "}, {"RefNumber": "1635201", "RefComponent": "XAP-EM", "RefTitle": "Change of user-defined fields validation in EC tasks", "RefUrl": "/notes/1635201 "}, {"RefNumber": "1638693", "RefComponent": "XAP-EM", "RefTitle": "EC read functions return a dimension if a unit is given", "RefUrl": "/notes/1638693 "}, {"RefNumber": "1631529", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Patch 2", "RefUrl": "/notes/1631529 "}, {"RefNumber": "1633310", "RefComponent": "XAP-EM", "RefTitle": "Function GetConsumValue returns no consumptions", "RefUrl": "/notes/1633310 "}, {"RefNumber": "1633243", "RefComponent": "XAP-EM", "RefTitle": "Documents are not copied in the Task Template copy process", "RefUrl": "/notes/1633243 "}, {"RefNumber": "1631427", "RefComponent": "XAP-EM", "RefTitle": "Slow performance of the EC function GetConsumValue", "RefUrl": "/notes/1631427 "}, {"RefNumber": "1631362", "RefComponent": "XAP-EM", "RefTitle": "Customizable block size in the EC calculation scheduler", "RefUrl": "/notes/1631362 "}, {"RefNumber": "1631454", "RefComponent": "XAP-EM", "RefTitle": "Increase performance of EC consumption import web-service", "RefUrl": "/notes/1631454 "}, {"RefNumber": "1627190", "RefComponent": "XAP-EM", "RefTitle": "Copy mode drop-down of consumptions tab is not accessible", "RefUrl": "/notes/1627190 "}, {"RefNumber": "1532423", "RefComponent": "XAP-EM", "RefTitle": "Cannot rename an EC Reporting Generation Variant restriction", "RefUrl": "/notes/1532423 "}, {"RefNumber": "1626714", "RefComponent": "XAP-EM", "RefTitle": "Tab name of user defined field areas can be saved empty", "RefUrl": "/notes/1626714 "}, {"RefNumber": "1612312", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP10 Patch 1", "RefUrl": "/notes/1612312 "}, {"RefNumber": "1626571", "RefComponent": "XAP-EM", "RefTitle": "Remove EC customer DB tables from the database whitelist", "RefUrl": "/notes/1626571 "}, {"RefNumber": "1621926", "RefComponent": "XAP-EM", "RefTitle": "Incorrect takeover of EC parameter sets in consumption tasks", "RefUrl": "/notes/1621926 "}, {"RefNumber": "1621925", "RefComponent": "XAP-EM", "RefTitle": "Increase the performance of the EC consumption web service", "RefUrl": "/notes/1621925 "}, {"RefNumber": "1616739", "RefComponent": "XAP-EM", "RefTitle": "EC facility search hit list displays all facility versions", "RefUrl": "/notes/1616739 "}, {"RefNumber": "1623797", "RefComponent": "XAP-EM", "RefTitle": "EEM extractor fails while extraction process is running", "RefUrl": "/notes/1623797 "}, {"RefNumber": "1624642", "RefComponent": "XAP-EM", "RefTitle": "Material Classifier Text Extractor (/TDAG/MCLASS_T) fails", "RefUrl": "/notes/1624642 "}, {"RefNumber": "1624193", "RefComponent": "XAP-EM", "RefTitle": "UDFs of facilities with several versions are not copied", "RefUrl": "/notes/1624193 "}, {"RefNumber": "1621510", "RefComponent": "XAP-EM", "RefTitle": "Limit check creates wrong limit report end date of emissions", "RefUrl": "/notes/1621510 "}, {"RefNumber": "1621677", "RefComponent": "XAP-EM", "RefTitle": "Missing of EC subtasks of master task in a workflow", "RefUrl": "/notes/1621677 "}, {"RefNumber": "1619228", "RefComponent": "XAP-EM", "RefTitle": "Can not delete EC requirement in the permit hierarchy", "RefUrl": "/notes/1619228 "}, {"RefNumber": "1620226", "RefComponent": "XAP-EM", "RefTitle": "Missing labels for EC citation monitoring properties", "RefUrl": "/notes/1620226 "}, {"RefNumber": "1596886", "RefComponent": "XAP-EM", "RefTitle": "Can not delete single consumption parameter values", "RefUrl": "/notes/1596886 "}, {"RefNumber": "1602290", "RefComponent": "XAP-EM", "RefTitle": "Wrong user defined field (UDF) data records in EC facilities", "RefUrl": "/notes/1602290 "}, {"RefNumber": "1598056", "RefComponent": "XAP-EM", "RefTitle": "Environmental Compliance 3.0 Export Import tool", "RefUrl": "/notes/1598056 "}, {"RefNumber": "1598167", "RefComponent": "XAP-EM", "RefTitle": "Copy hourly consumption values does not work with drop-down", "RefUrl": "/notes/1598167 "}, {"RefNumber": "1608630", "RefComponent": "XAP-EM", "RefTitle": "Can not save EC Integration at Control Device", "RefUrl": "/notes/1608630 "}, {"RefNumber": "1617279", "RefComponent": "XAP-EM", "RefTitle": "By changing UDF domain type EC could create orphan data", "RefUrl": "/notes/1617279 "}, {"RefNumber": "1618510", "RefComponent": "XAP-EM", "RefTitle": "Example XML definition files for the EC Export Import tool", "RefUrl": "/notes/1618510 "}, {"RefNumber": "1610219", "RefComponent": "XAP-EM", "RefTitle": "In the UDF Extraction Framework 0RECORDMODE was mandatory", "RefUrl": "/notes/1610219 "}, {"RefNumber": "1610248", "RefComponent": "XAP-EM", "RefTitle": "EC system shows incorrect references for new materials", "RefUrl": "/notes/1610248 "}, {"RefNumber": "1610330", "RefComponent": "XAP-EM", "RefTitle": "Extract multiple classifier values for one classifier type", "RefUrl": "/notes/1610330 "}, {"RefNumber": "1610482", "RefComponent": "XAP-EM", "RefTitle": "Text extractor fails while extraction process is running", "RefUrl": "/notes/1610482 "}, {"RefNumber": "1600007", "RefComponent": "XAP-EM", "RefTitle": "Can not change from Task status \"In Process\" to status \"New\"", "RefUrl": "/notes/1600007 "}, {"RefNumber": "1596750", "RefComponent": "XAP-EM", "RefTitle": "Emissions are not included when copying a facility", "RefUrl": "/notes/1596750 "}, {"RefNumber": "1598779", "RefComponent": "XAP-EM", "RefTitle": "Transactional data extraction to BI does not pick up changes made during extraction", "RefUrl": "/notes/1598779 "}, {"RefNumber": "1592218", "RefComponent": "XAP-EM", "RefTitle": "Completion date for canceled EC tasks cannot be removed", "RefUrl": "/notes/1592218 "}, {"RefNumber": "1602989", "RefComponent": "XAP-EM", "RefTitle": "EC User defined fields could generate obsolete data records", "RefUrl": "/notes/1602989 "}, {"RefNumber": "1603642", "RefComponent": "XAP-EM", "RefTitle": "Assignee cannot change consumption period of subsequent task", "RefUrl": "/notes/1603642 "}, {"RefNumber": "1603704", "RefComponent": "XAP-EM", "RefTitle": "My open exceptions doesn't show exceptions with groups", "RefUrl": "/notes/1603704 "}, {"RefNumber": "1595865", "RefComponent": "XAP-EM", "RefTitle": "Unrestricted facility search can cause out-of-memory error", "RefUrl": "/notes/1595865 "}, {"RefNumber": "1592697", "RefComponent": "XAP-EM", "RefTitle": "EC EHS integration program \"/TDAG/XEMSYNCUNIT\" doesnt work", "RefUrl": "/notes/1592697 "}, {"RefNumber": "1596864", "RefComponent": "XAP-EM", "RefTitle": "Changes of end critera and start date not detected properly", "RefUrl": "/notes/1596864 "}, {"RefNumber": "1596274", "RefComponent": "XAP-EM", "RefTitle": "EC EHS Integration shows duplicates of inserted classifiers", "RefUrl": "/notes/1596274 "}, {"RefNumber": "1566474", "RefComponent": "XAP-EM", "RefTitle": "Additional tasks created when removing tasks from facilities", "RefUrl": "/notes/1566474 "}, {"RefNumber": "1596865", "RefComponent": "XAP-EM", "RefTitle": "Change schedule of master task created incorrect sub task", "RefUrl": "/notes/1596865 "}, {"RefNumber": "1595429", "RefComponent": "XAP-EM", "RefTitle": "Error while searching parameters in transactional data view", "RefUrl": "/notes/1595429 "}, {"RefNumber": "1594961", "RefComponent": "XAP-EM", "RefTitle": "Can not download the result of the EC test data source", "RefUrl": "/notes/1594961 "}, {"RefNumber": "1593357", "RefComponent": "XAP-EM", "RefTitle": "EC User defined fields could generate obsolete data records", "RefUrl": "/notes/1593357 "}, {"RefNumber": "1584716", "RefComponent": "XAP-EM", "RefTitle": "Tree not updated when facility created from split facility", "RefUrl": "/notes/1584716 "}, {"RefNumber": "1592389", "RefComponent": "XAP-EM", "RefTitle": "Improve loading performance of the EC reporting hierarchy", "RefUrl": "/notes/1592389 "}, {"RefNumber": "1591745", "RefComponent": "XAP-EM", "RefTitle": "Synchronization error with spec. identifiers with language", "RefUrl": "/notes/1591745 "}, {"RefNumber": "1591482", "RefComponent": "XAP-EM", "RefTitle": "EC EHS integration does not update the material identifiers", "RefUrl": "/notes/1591482 "}, {"RefNumber": "1586818", "RefComponent": "XAP-EM", "RefTitle": "User can create EC scenarios in object based authorization", "RefUrl": "/notes/1586818 "}, {"RefNumber": "1582235", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP09 PL01 Note", "RefUrl": "/notes/1582235 "}, {"RefNumber": "1578870", "RefComponent": "XAP-EM", "RefTitle": "Incorrect handling of roles in object based authorizations", "RefUrl": "/notes/1578870 "}, {"RefNumber": "1586169", "RefComponent": "XAP-EM", "RefTitle": "Control efficiency is set to 0% after change on other object", "RefUrl": "/notes/1586169 "}, {"RefNumber": "1571120", "RefComponent": "XAP-EM", "RefTitle": "Increase performance in the EC facility hierarchy", "RefUrl": "/notes/1571120 "}, {"RefNumber": "1564718", "RefComponent": "XAP-EM", "RefTitle": "Error searching EC integrated tags with long names", "RefUrl": "/notes/1564718 "}, {"RefNumber": "1568084", "RefComponent": "XAP-EM", "RefTitle": "Saving a modified EC task template takes too long time", "RefUrl": "/notes/1568084 "}, {"RefNumber": "1582193", "RefComponent": "XAP-EM", "RefTitle": "Unrestricted task search could result an out-of-memory error", "RefUrl": "/notes/1582193 "}, {"RefNumber": "1582129", "RefComponent": "XAP-EM", "RefTitle": "Previous amount of recursive consumption task not populated", "RefUrl": "/notes/1582129 "}, {"RefNumber": "1531323", "RefComponent": "XAP-EM", "RefTitle": "Error when loading split facilities from the hitlist", "RefUrl": "/notes/1531323 "}, {"RefNumber": "1575765", "RefComponent": "XAP-EM", "RefTitle": "EC Citations have a wrong change document domain name", "RefUrl": "/notes/1575765 "}, {"RefNumber": "1509527", "RefComponent": "XAP-EM", "RefTitle": "EC SQL data source access only for EC tables and EC views", "RefUrl": "/notes/1509527 "}, {"RefNumber": "1577275", "RefComponent": "XAP-EM", "RefTitle": "EC 30 example BI Content for higher BI_CONT releases", "RefUrl": "/notes/1577275 "}, {"RefNumber": "1562008", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP09 PL0 Note", "RefUrl": "/notes/1562008 "}, {"RefNumber": "1569767", "RefComponent": "XAP-EM", "RefTitle": "Features Release Notes for SAP EC 3.0 SP09", "RefUrl": "/notes/1569767 "}, {"RefNumber": "1575312", "RefComponent": "XAP-EM", "RefTitle": "Avoid commas and semicolons in List of Values", "RefUrl": "/notes/1575312 "}, {"RefNumber": "1576061", "RefComponent": "XAP-EM", "RefTitle": "EC emission values are overwritten by web service", "RefUrl": "/notes/1576061 "}, {"RefNumber": "1575967", "RefComponent": "XAP-EM", "RefTitle": "Time information of hourly EC emission values is not visible", "RefUrl": "/notes/1575967 "}, {"RefNumber": "1568962", "RefComponent": "XAP-EM", "RefTitle": "Can't use multiple list of values in Object Based Permission", "RefUrl": "/notes/1568962 "}, {"RefNumber": "1561958", "RefComponent": "XAP-EM", "RefTitle": "EC User Reassignment uses the wrong Change Document domains", "RefUrl": "/notes/1561958 "}, {"RefNumber": "1567367", "RefComponent": "XAP-EM", "RefTitle": "Global Use Reassignment doesnt work for EC Reporting users", "RefUrl": "/notes/1567367 "}, {"RefNumber": "1572323", "RefComponent": "XAP-EM", "RefTitle": "EC function template WriteMeasurement() has wrong syntax", "RefUrl": "/notes/1572323 "}, {"RefNumber": "1572463", "RefComponent": "XAP-EM", "RefTitle": "Consumption based calculation appears in Read mode", "RefUrl": "/notes/1572463 "}, {"RefNumber": "1569023", "RefComponent": "XAP-EM", "RefTitle": "Calculation Engine doesnt detect Not a Number (NaN) values", "RefUrl": "/notes/1569023 "}, {"RefNumber": "1568374", "RefComponent": "XAP-EM", "RefTitle": "Missing recent Parameters in the EC Parameters Activity", "RefUrl": "/notes/1568374 "}, {"RefNumber": "1566142", "RefComponent": "XAP-EM", "RefTitle": "Task Instance Attribute /TDAG/TASKFIN in BI shows wrong date", "RefUrl": "/notes/1566142 "}, {"RefNumber": "1568298", "RefComponent": "XAP-EM", "RefTitle": "Integration settings are shown for all EC Parameter Types", "RefUrl": "/notes/1568298 "}, {"RefNumber": "1567889", "RefComponent": "XAP-EM", "RefTitle": "Warn user about possible overlapping Consumptions in Tasks", "RefUrl": "/notes/1567889 "}, {"RefNumber": "1516381", "RefComponent": "XAP-EM", "RefTitle": "Default language of Equation variable info isn't saved", "RefUrl": "/notes/1516381 "}, {"RefNumber": "1510085", "RefComponent": "XAP-EM", "RefTitle": "By regenerating Reports the restrictions are not taken over", "RefUrl": "/notes/1510085 "}, {"RefNumber": "1510022", "RefComponent": "XAP-EM", "RefTitle": "Cross references doesn't show used Materials in EC Equations", "RefUrl": "/notes/1510022 "}, {"RefNumber": "1564965", "RefComponent": "XAP-EM", "RefTitle": "EC Limit Diagnostic table export occurs an error", "RefUrl": "/notes/1564965 "}, {"RefNumber": "1412470", "RefComponent": "XAP-EM", "RefTitle": "Disable unsupported functionalities from the Task Template", "RefUrl": "/notes/1412470 "}, {"RefNumber": "1412364", "RefComponent": "XAP-EM", "RefTitle": "Tasks that are deleted appear in BI as undeleted", "RefUrl": "/notes/1412364 "}, {"RefNumber": "1502099", "RefComponent": "XAP-EM", "RefTitle": "Turkish language available in Environmental Compliance 3.0", "RefUrl": "/notes/1502099 "}, {"RefNumber": "1499982", "RefComponent": "XAP-EM", "RefTitle": "System suggests to create a new Task on non-recursion tasks", "RefUrl": "/notes/1499982 "}, {"RefNumber": "1499981", "RefComponent": "XAP-EM", "RefTitle": "Task Duplication and Task Template changes reverted", "RefUrl": "/notes/1499981 "}, {"RefNumber": "1494061", "RefComponent": "XAP-EM", "RefTitle": "Error 500: OBA references object that is not on target sys.", "RefUrl": "/notes/1494061 "}, {"RefNumber": "1493875", "RefComponent": "XAP-EM", "RefTitle": "UDF Fields type \"Numeric\" are not shown in the reporting", "RefUrl": "/notes/1493875 "}, {"RefNumber": "1493061", "RefComponent": "XAP-EM", "RefTitle": "No data entry possibility for Task Consumption", "RefUrl": "/notes/1493061 "}, {"RefNumber": "1491534", "RefComponent": "XAP-EM", "RefTitle": "UDF Values are overwritten in report for different domains", "RefUrl": "/notes/1491534 "}, {"RefNumber": "1491533", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP4 Patch 2 Note", "RefUrl": "/notes/1491533 "}, {"RefNumber": "1486338", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP4 Patch 1 Note", "RefUrl": "/notes/1486338 "}, {"RefNumber": "1482153", "RefComponent": "XAP-EM", "RefTitle": "Performance improvements for Object based Authorization", "RefUrl": "/notes/1482153 "}, {"RefNumber": "1482049", "RefComponent": "XAP-EM", "RefTitle": "Changed equitity share calculation in BI content", "RefUrl": "/notes/1482049 "}, {"RefNumber": "1482048", "RefComponent": "XAP-EM", "RefTitle": "Extraction: missed material quality for consumption data", "RefUrl": "/notes/1482048 "}, {"RefNumber": "1481168", "RefComponent": "XAP-EM", "RefTitle": "Referenced Consumption Material can be deleted", "RefUrl": "/notes/1481168 "}, {"RefNumber": "1444839", "RefComponent": "XAP-EM", "RefTitle": "Task Management: Task \"In Progress\" is causing duplication", "RefUrl": "/notes/1444839 "}, {"RefNumber": "1443900", "RefComponent": "XAP-EM", "RefTitle": "Task Status Analyzer: Checkboxes disappearing", "RefUrl": "/notes/1443900 "}, {"RefNumber": "1442289", "RefComponent": "XAP-EM", "RefTitle": "Task Text Extractor extracts Long Texts with underscore", "RefUrl": "/notes/1442289 "}, {"RefNumber": "1439959", "RefComponent": "XAP-EM", "RefTitle": "Task Status Analyzer: Messages for invalid search criterias", "RefUrl": "/notes/1439959 "}, {"RefNumber": "1439377", "RefComponent": "XAP-EM", "RefTitle": "Taskworkflow:Changes in Master task leads to invalid Subtask", "RefUrl": "/notes/1439377 "}, {"RefNumber": "1439373", "RefComponent": "XAP-EM", "RefTitle": "Obsolete LOVs appears in the group drop down of parameters", "RefUrl": "/notes/1439373 "}, {"RefNumber": "1438232", "RefComponent": "XAP-EM", "RefTitle": "Historical Truth in BI is not possible for flex. Hierarchies", "RefUrl": "/notes/1438232 "}, {"RefNumber": "1437940", "RefComponent": "XAP-EM", "RefTitle": "Master task can be completed with open subtask", "RefUrl": "/notes/1437940 "}, {"RefNumber": "1436991", "RefComponent": "XAP-EM", "RefTitle": "Extraction for /TDAG/FACIC_M fails", "RefUrl": "/notes/1436991 "}, {"RefNumber": "1436989", "RefComponent": "XAP-EM", "RefTitle": "Manual Task Workflow items does not create subtasks", "RefUrl": "/notes/1436989 "}, {"RefNumber": "1436228", "RefComponent": "XAP-EM", "RefTitle": "Creation of Task with \"One Task for all Facilities\" fails", "RefUrl": "/notes/1436228 "}, {"RefNumber": "1434484", "RefComponent": "XAP-EM", "RefTitle": "Task Template: Incomplete Informations on the history tab", "RefUrl": "/notes/1434484 "}, {"RefNumber": "1433188", "RefComponent": "XAP-EM", "RefTitle": "Deleting Task Instances can cause holes in the series", "RefUrl": "/notes/1433188 "}, {"RefNumber": "1432984", "RefComponent": "XAP-EM", "RefTitle": "Bad Performance in data load of the EEM Extractor", "RefUrl": "/notes/1432984 "}, {"RefNumber": "1533775", "RefComponent": "XAP-EM", "RefTitle": "Czech and Hungarian language now available SAP EC 3.0", "RefUrl": "/notes/1533775 "}, {"RefNumber": "1518298", "RefComponent": "XAP-EM", "RefTitle": "Reporting: Weighted Average causes \"0\" as result", "RefUrl": "/notes/1518298 "}, {"RefNumber": "1477790", "RefComponent": "XAP-EM", "RefTitle": "Task Template: Missing copy function for materials", "RefUrl": "/notes/1477790 "}, {"RefNumber": "1476411", "RefComponent": "XAP-EM", "RefTitle": "Performance improved Transactional Data Manager for Datarel.", "RefUrl": "/notes/1476411 "}, {"RefNumber": "1474674", "RefComponent": "XAP-EM", "RefTitle": "New Task Instance for Task Template ended with end by count", "RefUrl": "/notes/1474674 "}, {"RefNumber": "1474477", "RefComponent": "XAP-EM", "RefTitle": "Global Reassignment causes inconsistent BI data", "RefUrl": "/notes/1474477 "}, {"RefNumber": "1471706", "RefComponent": "XAP-EM", "RefTitle": "Datarelease Initial Status not correctly set in some cases", "RefUrl": "/notes/1471706 "}, {"RefNumber": "1468528", "RefComponent": "XAP-EM", "RefTitle": "Task Management: Changed Navigation for Master-/Subtask", "RefUrl": "/notes/1468528 "}, {"RefNumber": "1468432", "RefComponent": "XAP-EM", "RefTitle": "Task Series: 31th of Month cause problem with month < 31 day", "RefUrl": "/notes/1468432 "}, {"RefNumber": "1468139", "RefComponent": "XAP-EM", "RefTitle": "Task: Document changes were not detected correctly in EC3.0", "RefUrl": "/notes/1468139 "}, {"RefNumber": "1468138", "RefComponent": "XAP-EM", "RefTitle": "Batch Calculation: Wrong calculated controlled emission", "RefUrl": "/notes/1468138 "}, {"RefNumber": "1332535", "RefComponent": "XAP-EM", "RefTitle": "Download Instruction for SAP Environmental Compliance", "RefUrl": "/notes/1332535 "}, {"RefNumber": "1461732", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP4 Note", "RefUrl": "/notes/1461732 "}, {"RefNumber": "1461763", "RefComponent": "XAP-EM", "RefTitle": "Downpropagation with option \"to all child versions\" fails", "RefUrl": "/notes/1461763 "}, {"RefNumber": "1457041", "RefComponent": "XAP-EM", "RefTitle": "Duplicated Tasks for completed Task Instances", "RefUrl": "/notes/1457041 "}, {"RefNumber": "1456745", "RefComponent": "XAP-EM", "RefTitle": "Performance problem with SQL Datasource reports", "RefUrl": "/notes/1456745 "}, {"RefNumber": "1453979", "RefComponent": "XAP-EM", "RefTitle": "Consumption Task: Amount can be removed", "RefUrl": "/notes/1453979 "}, {"RefNumber": "1456322", "RefComponent": "XAP-EM", "RefTitle": "Task:Recursion series continues when end criteria is reached", "RefUrl": "/notes/1456322 "}, {"RefNumber": "1453517", "RefComponent": "XAP-EM", "RefTitle": "Task: Only single instance is created instead of multiple", "RefUrl": "/notes/1453517 "}, {"RefNumber": "1450126", "RefComponent": "XAP-EM", "RefTitle": "Downpropagation does not work when scenarios exists", "RefUrl": "/notes/1450126 "}, {"RefNumber": "1449357", "RefComponent": "XAP-EM", "RefTitle": "Data Entry tasks do not detect Material changes", "RefUrl": "/notes/1449357 "}, {"RefNumber": "1448595", "RefComponent": "XAP-EM", "RefTitle": "System suggests to create a new task on closed task template", "RefUrl": "/notes/1448595 "}, {"RefNumber": "1447219", "RefComponent": "XAP-EM", "RefTitle": "Downpropagation of future Facility Splits fails", "RefUrl": "/notes/1447219 "}, {"RefNumber": "1537018", "RefComponent": "XAP-EM", "RefTitle": "Em<PERSON> doesn't get deleted while deleting a task", "RefUrl": "/notes/1537018 "}, {"RefNumber": "1499871", "RefComponent": "XAP-EM", "RefTitle": "UDF Problems while changing EC Exception type", "RefUrl": "/notes/1499871 "}, {"RefNumber": "1499423", "RefComponent": "XAP-EM", "RefTitle": "Problems creating EC Consumption values at clock change", "RefUrl": "/notes/1499423 "}, {"RefNumber": "1482291", "RefComponent": "XAP-EM", "RefTitle": "Delete UDF values while changing an EC Exception type", "RefUrl": "/notes/1482291 "}, {"RefNumber": "1482288", "RefComponent": "XAP-EM", "RefTitle": "Appearance order for EC User Defined Field Areas", "RefUrl": "/notes/1482288 "}, {"RefNumber": "1479710", "RefComponent": "XAP-EM", "RefTitle": "Error in Task Status Analyzer search by User Defined Fields", "RefUrl": "/notes/1479710 "}, {"RefNumber": "1443635", "RefComponent": "XAP-EM", "RefTitle": "SQL Datasource returns an ORA-01747 error in EC log", "RefUrl": "/notes/1443635 "}, {"RefNumber": "1441537", "RefComponent": "XAP-EM", "RefTitle": "Limit Check Report Text changed", "RefUrl": "/notes/1441537 "}, {"RefNumber": "1441487", "RefComponent": "XAP-EM", "RefTitle": "Task Instance - Error Reading Requirement on Task Instance", "RefUrl": "/notes/1441487 "}, {"RefNumber": "1439242", "RefComponent": "XAP-EM", "RefTitle": "500 Internal Server Error - java.lang.NoClassDefFoundError", "RefUrl": "/notes/1439242 "}, {"RefNumber": "1438948", "RefComponent": "XAP-EM", "RefTitle": "Exception - Clear the End Date doesn't clear the db field", "RefUrl": "/notes/1438948 "}, {"RefNumber": "1438949", "RefComponent": "XAP-EM", "RefTitle": "Limit Reports missing in Excel Export", "RefUrl": "/notes/1438949 "}, {"RefNumber": "1427698", "RefComponent": "XAP-EM", "RefTitle": "Task Manager: Incorrect UDF display on Task Instance", "RefUrl": "/notes/1427698 "}, {"RefNumber": "1526090", "RefComponent": "XAP-EM", "RefTitle": "EC Change Document does not show all data", "RefUrl": "/notes/1526090 "}, {"RefNumber": "1447218", "RefComponent": "XAP-EM", "RefTitle": "UDF Configuration: Type Empty Line is missing in filter", "RefUrl": "/notes/1447218 "}, {"RefNumber": "1382998", "RefComponent": "XAP-EM", "RefTitle": "Changes for the Change Document Service", "RefUrl": "/notes/1382998 "}, {"RefNumber": "1471261", "RefComponent": "XAP-EM", "RefTitle": "Problems while searchig for Requirement or Req. Set Folders", "RefUrl": "/notes/1471261 "}, {"RefNumber": "1177820", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance (xEM) 2.0 SP13 Central Note", "RefUrl": "/notes/1177820 "}, {"RefNumber": "1461404", "RefComponent": "XAP-EM", "RefTitle": "Detailed Error message for xml Parser errors", "RefUrl": "/notes/1461404 "}, {"RefNumber": "1450988", "RefComponent": "XAP-EM", "RefTitle": "Export to Excel only exports the first 3 decimal places", "RefUrl": "/notes/1450988 "}, {"RefNumber": "1448845", "RefComponent": "XAP-EM", "RefTitle": "EAM Task value is missing from picklist", "RefUrl": "/notes/1448845 "}, {"RefNumber": "1448713", "RefComponent": "XAP-EM", "RefTitle": "Limit Diagnostics - Excel Report missing column", "RefUrl": "/notes/1448713 "}, {"RefNumber": "1535489", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP08 Note", "RefUrl": "/notes/1535489 "}, {"RefNumber": "1540481", "RefComponent": "XAP-EM", "RefTitle": "Validation of \"Hourly\" Consumption values does not work", "RefUrl": "/notes/1540481 "}, {"RefNumber": "1550963", "RefComponent": "XAP-EM", "RefTitle": "EC Facility Selector shows inactive facilities", "RefUrl": "/notes/1550963 "}, {"RefNumber": "1563403", "RefComponent": "XAP-EM", "RefTitle": "Can not create an EC Exception manually at the end of month", "RefUrl": "/notes/1563403 "}, {"RefNumber": "1556232", "RefComponent": "XAP-EM", "RefTitle": "LOVs marked obsolete are disappearing from the EC Classifier", "RefUrl": "/notes/1556232 "}, {"RefNumber": "1557869", "RefComponent": "XAP-EM", "RefTitle": "Language Drop Down in EC Report Wizard isn't shown correctly", "RefUrl": "/notes/1557869 "}, {"RefNumber": "1559663", "RefComponent": "XAP-EM", "RefTitle": "Task selection doesnt work correctly in EC Multivalue Popup", "RefUrl": "/notes/1559663 "}, {"RefNumber": "1559665", "RefComponent": "XAP-EM", "RefTitle": "Increase Performance in EC My Exceptions Overview", "RefUrl": "/notes/1559665 "}, {"RefNumber": "1540226", "RefComponent": "XAP-EM", "RefTitle": "EC Emission Task Wizard does not work correctly", "RefUrl": "/notes/1540226 "}, {"RefNumber": "1540230", "RefComponent": "XAP-EM", "RefTitle": "Error reading the Calculation Result dimension", "RefUrl": "/notes/1540230 "}, {"RefNumber": "1540480", "RefComponent": "XAP-EM", "RefTitle": "Deactivate \"Calculate\" Flag in the Consumption view", "RefUrl": "/notes/1540480 "}, {"RefNumber": "1540858", "RefComponent": "XAP-EM", "RefTitle": "Format Date and Time information in EC Reports correctly", "RefUrl": "/notes/1540858 "}, {"RefNumber": "1555475", "RefComponent": "XAP-EM", "RefTitle": "Error by translating an EC Requirement Set Folder name", "RefUrl": "/notes/1555475 "}, {"RefNumber": "1555549", "RefComponent": "XAP-EM", "RefTitle": "Can not close changed EC Permit Folders", "RefUrl": "/notes/1555549 "}, {"RefNumber": "1539454", "RefComponent": "XAP-EM", "RefTitle": "EC Subsequent Task is not generated correctly", "RefUrl": "/notes/1539454 "}, {"RefNumber": "1536090", "RefComponent": "XAP-EM", "RefTitle": "The cancel button does not work for EC Calculation jobs", "RefUrl": "/notes/1536090 "}, {"RefNumber": "1555424", "RefComponent": "XAP-EM", "RefTitle": "Can not deploy EC 3.0 SP7 (or later releases) with JSPM", "RefUrl": "/notes/1555424 "}, {"RefNumber": "1517403", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP07 Note", "RefUrl": "/notes/1517403 "}, {"RefNumber": "1539476", "RefComponent": "XAP-EM", "RefTitle": "BI extractor doesnt detect changed Tasks after user reassign", "RefUrl": "/notes/1539476 "}, {"RefNumber": "1549694", "RefComponent": "XAP-EM", "RefTitle": "Empty Report Data Sources are not shown in EC XML Reports", "RefUrl": "/notes/1549694 "}, {"RefNumber": "1545654", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP7 Patch 2 Note", "RefUrl": "/notes/1545654 "}, {"RefNumber": "1297451", "RefComponent": "XAP-EM", "RefTitle": "Export/ Import SAP Environmental Compliance 3.0 Data", "RefUrl": "/notes/1297451 "}, {"RefNumber": "1544352", "RefComponent": "XAP-EM", "RefTitle": "BI Text extractor leads sometimes to wrong results in the BI", "RefUrl": "/notes/1544352 "}, {"RefNumber": "1504403", "RefComponent": "XAP-EM", "RefTitle": "Task Selector in EC Reporting Data Source Bo does not work", "RefUrl": "/notes/1504403 "}, {"RefNumber": "1506682", "RefComponent": "XAP-EM", "RefTitle": "Facility Conf shows error when no initial data is available", "RefUrl": "/notes/1506682 "}, {"RefNumber": "1507644", "RefComponent": "XAP-EM", "RefTitle": "Deleted Material could lead to a dump in Data Release Def.", "RefUrl": "/notes/1507644 "}, {"RefNumber": "1503555", "RefComponent": "XAP-EM", "RefTitle": "Increase performance searching and loading EC Requirements", "RefUrl": "/notes/1503555 "}, {"RefNumber": "1503113", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP06 Note", "RefUrl": "/notes/1503113 "}, {"RefNumber": "1502367", "RefComponent": "XAP-EM", "RefTitle": "Increase performance while saving Facilities with Materials", "RefUrl": "/notes/1502367 "}, {"RefNumber": "1502366", "RefComponent": "XAP-EM", "RefTitle": "Exception filter in Advanced Search for EC Task Templates", "RefUrl": "/notes/1502366 "}, {"RefNumber": "1512869", "RefComponent": "XAP-EM", "RefTitle": "Exception Extended webservice doesn't update all information", "RefUrl": "/notes/1512869 "}, {"RefNumber": "1510658", "RefComponent": "XAP-EM", "RefTitle": "EC Permit Advanced Search does not return all Permits", "RefUrl": "/notes/1510658 "}, {"RefNumber": "1512964", "RefComponent": "XAP-EM", "RefTitle": "EC Exception web service does not check the incomming Types", "RefUrl": "/notes/1512964 "}, {"RefNumber": "1513618", "RefComponent": "XAP-EM", "RefTitle": "Misspelling in EC 3.0 portal page translation", "RefUrl": "/notes/1513618 "}, {"RefNumber": "1514687", "RefComponent": "XAP-EM", "RefTitle": "Can not delete the Material Assignment in the EC Facility", "RefUrl": "/notes/1514687 "}, {"RefNumber": "1517362", "RefComponent": "XAP-EM", "RefTitle": "Obsolete List of Values are shown in EC Facility Classifiers", "RefUrl": "/notes/1517362 "}, {"RefNumber": "1521480", "RefComponent": "XAP-EM", "RefTitle": "Time entered 23:59:59:999000 will be changed to 12:00:00:000", "RefUrl": "/notes/1521480 "}, {"RefNumber": "1521755", "RefComponent": "XAP-EM", "RefTitle": "EC Consumption Based Calculation with optional Measurements", "RefUrl": "/notes/1521755 "}, {"RefNumber": "1523176", "RefComponent": "XAP-EM", "RefTitle": "Consumptions in BW doesn't show deleted Material Classifiers", "RefUrl": "/notes/1523176 "}, {"RefNumber": "1523740", "RefComponent": "XAP-EM", "RefTitle": "EC Reporting generation does not close all used Connections", "RefUrl": "/notes/1523740 "}, {"RefNumber": "1523733", "RefComponent": "XAP-EM", "RefTitle": "EC Calculation dump is empty for erroneous results", "RefUrl": "/notes/1523733 "}, {"RefNumber": "1523660", "RefComponent": "XAP-EM", "RefTitle": "Increase Performance of EC Calculation store process", "RefUrl": "/notes/1523660 "}, {"RefNumber": "1524080", "RefComponent": "XAP-EM", "RefTitle": "EC Exceptions with future dates and different timezones", "RefUrl": "/notes/1524080 "}, {"RefNumber": "1526346", "RefComponent": "XAP-EM", "RefTitle": "EC Calculation dump is empty for erroneous Emission results", "RefUrl": "/notes/1526346 "}, {"RefNumber": "1527326", "RefComponent": "XAP-EM", "RefTitle": "Cannot save EC Reporting Data Source with UDF values", "RefUrl": "/notes/1527326 "}, {"RefNumber": "1527423", "RefComponent": "XAP-EM", "RefTitle": "Calc Function getMeasurement() shall return sorted results", "RefUrl": "/notes/1527423 "}, {"RefNumber": "1527432", "RefComponent": "XAP-EM", "RefTitle": "EC Report XML contains a non translated word \"and\"", "RefUrl": "/notes/1527432 "}, {"RefNumber": "1528170", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP6 Patch 2 Note", "RefUrl": "/notes/1528170 "}, {"RefNumber": "1529366", "RefComponent": "XAP-EM", "RefTitle": "Missing EC Calculation Function GetEmissionValueExtended()", "RefUrl": "/notes/1529366 "}, {"RefNumber": "1531259", "RefComponent": "XAP-EM", "RefTitle": "EC Calculation Scheduler does not calculate with order no.", "RefUrl": "/notes/1531259 "}, {"RefNumber": "1531262", "RefComponent": "XAP-EM", "RefTitle": "Missing a Time logger for EC Calculation Scheduler", "RefUrl": "/notes/1531262 "}, {"RefNumber": "1531325", "RefComponent": "XAP-EM", "RefTitle": "Can not leave the EC Permit Folder after changing the Name", "RefUrl": "/notes/1531325 "}, {"RefNumber": "1532606", "RefComponent": "XAP-EM", "RefTitle": "EC Function getTechInfo() doesn't support all Facility types", "RefUrl": "/notes/1532606 "}, {"RefNumber": "1532827", "RefComponent": "XAP-EM", "RefTitle": "Missing Calculation Frequency in EC Emission Tasks", "RefUrl": "/notes/1532827 "}, {"RefNumber": "1533071", "RefComponent": "XAP-EM", "RefTitle": "EC Consumption overlapping check in web-services doesnt work", "RefUrl": "/notes/1533071 "}, {"RefNumber": "1535031", "RefComponent": "XAP-EM", "RefTitle": "Erroneous calculated Consumption created new Consumptions", "RefUrl": "/notes/1535031 "}, {"RefNumber": "1534959", "RefComponent": "XAP-EM", "RefTitle": "EC Data Release Error during the Calculation", "RefUrl": "/notes/1534959 "}, {"RefNumber": "1537590", "RefComponent": "XAP-EM", "RefTitle": "Calculation Scheduler doesnt sort the Consumptions correctly", "RefUrl": "/notes/1537590 "}, {"RefNumber": "1539692", "RefComponent": "XAP-EM", "RefTitle": "Task Advanced Search clears the search criterias on closing", "RefUrl": "/notes/1539692 "}, {"RefNumber": "1500517", "RefComponent": "XAP-EM", "RefTitle": "Migration of EC database content from Release 2.0 to 3.0", "RefUrl": "/notes/1500517 "}, {"RefNumber": "1500083", "RefComponent": "XAP-EM", "RefTitle": "EC shows an error when accessing Consumptions tab", "RefUrl": "/notes/1500083 "}, {"RefNumber": "1500031", "RefComponent": "XAP-EM", "RefTitle": "No permission check by creating Parameters from popup", "RefUrl": "/notes/1500031 "}, {"RefNumber": "1500030", "RefComponent": "XAP-EM", "RefTitle": "\"No permission\" error message in EC is incorrect", "RefUrl": "/notes/1500030 "}, {"RefNumber": "1483622", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP05 Note", "RefUrl": "/notes/1483622 "}, {"RefNumber": "1490146", "RefComponent": "XAP-EM", "RefTitle": "Configuration of EC 3.0 Object Based Navigation portal pages", "RefUrl": "/notes/1490146 "}, {"RefNumber": "1490345", "RefComponent": "XAP-EM", "RefTitle": "By changing a Consumption Value user needs to add a comment", "RefUrl": "/notes/1490345 "}, {"RefNumber": "1488551", "RefComponent": "XAP-EM", "RefTitle": "Error while selecting an EC Facility from the hierarchy", "RefUrl": "/notes/1488551 "}, {"RefNumber": "1488643", "RefComponent": "XAP-EM", "RefTitle": "Can not download automatically a file from EC Report", "RefUrl": "/notes/1488643 "}, {"RefNumber": "1486574", "RefComponent": "XAP-EM", "RefTitle": "Time displayed in missing Emission error message is wrong", "RefUrl": "/notes/1486574 "}, {"RefNumber": "1486764", "RefComponent": "XAP-EM", "RefTitle": "Status Analyzer Search doesnt reset the selected Facilities", "RefUrl": "/notes/1486764 "}, {"RefNumber": "1487599", "RefComponent": "XAP-EM", "RefTitle": "Can not search by Limit period in EC Limit Diagnostics", "RefUrl": "/notes/1487599 "}, {"RefNumber": "1483542", "RefComponent": "XAP-EM", "RefTitle": "Create and Delete permissions are missing for EC Parameters", "RefUrl": "/notes/1483542 "}, {"RefNumber": "1485050", "RefComponent": "XAP-EM", "RefTitle": "Analytical Queries filtering does not work", "RefUrl": "/notes/1485050 "}, {"RefNumber": "1485075", "RefComponent": "XAP-EM", "RefTitle": "New Standard Pivot Query Report for EC Consumptions", "RefUrl": "/notes/1485075 "}, {"RefNumber": "1486337", "RefComponent": "XAP-EM", "RefTitle": "The EC Flexible Hierarchies extractor doesnt work correctly", "RefUrl": "/notes/1486337 "}, {"RefNumber": "1478987", "RefComponent": "XAP-EM", "RefTitle": "Object Based Permissions do not work for Facility Classifier", "RefUrl": "/notes/1478987 "}, {"RefNumber": "1479775", "RefComponent": "XAP-EM", "RefTitle": "Cannot open PDF file in automatically generated PDF Reports", "RefUrl": "/notes/1479775 "}, {"RefNumber": "1480366", "RefComponent": "XAP-EM", "RefTitle": "Check for overlapping Consumptions before calculate manually", "RefUrl": "/notes/1480366 "}, {"RefNumber": "1480773", "RefComponent": "XAP-EM", "RefTitle": "Inheritance Rules cannot be saved in Facility Configuration", "RefUrl": "/notes/1480773 "}, {"RefNumber": "1481497", "RefComponent": "XAP-EM", "RefTitle": "Object Based Permission for EC Calculations does not work", "RefUrl": "/notes/1481497 "}, {"RefNumber": "1481555", "RefComponent": "XAP-EM", "RefTitle": "The search does not work correctly in EC Manage Transports", "RefUrl": "/notes/1481555 "}, {"RefNumber": "1481797", "RefComponent": "XAP-EM", "RefTitle": "Enter Comment after deleting an EC Task Template", "RefUrl": "/notes/1481797 "}, {"RefNumber": "1482266", "RefComponent": "XAP-EM", "RefTitle": "Database error: \"100 open SQL statements in one connection\"", "RefUrl": "/notes/1482266 "}, {"RefNumber": "1483110", "RefComponent": "XAP-EM", "RefTitle": "Can not maintain Units for EC Calculation Local Values", "RefUrl": "/notes/1483110 "}, {"RefNumber": "1483683", "RefComponent": "XAP-EM", "RefTitle": "Overwrite a consumption entry using webservices doesn't work", "RefUrl": "/notes/1483683 "}, {"RefNumber": "1477091", "RefComponent": "XAP-EM", "RefTitle": "Increase performance searching or loading Tasks & Exceptions", "RefUrl": "/notes/1477091 "}, {"RefNumber": "1477083", "RefComponent": "XAP-EM", "RefTitle": "EC Limit Report check date/time cannot be sorted", "RefUrl": "/notes/1477083 "}, {"RefNumber": "1476712", "RefComponent": "XAP-EM", "RefTitle": "Unable to create an EC Exception from Dashboard", "RefUrl": "/notes/1476712 "}, {"RefNumber": "1476521", "RefComponent": "XAP-EM", "RefTitle": "Object Based Permission is missing for EC Citations", "RefUrl": "/notes/1476521 "}, {"RefNumber": "1474836", "RefComponent": "XAP-EM", "RefTitle": "EC allows overlapped Consumptions entered in UI and webserv.", "RefUrl": "/notes/1474836 "}, {"RefNumber": "1474181", "RefComponent": "XAP-EM", "RefTitle": "System shows PDF file extension while opening an XML Report", "RefUrl": "/notes/1474181 "}, {"RefNumber": "1473513", "RefComponent": "XAP-EM", "RefTitle": "Error while reviewing My Transport Activities of UDFs", "RefUrl": "/notes/1473513 "}, {"RefNumber": "1470818", "RefComponent": "XAP-EM", "RefTitle": "Avoid a new search after leaving a Task from Status Analyser", "RefUrl": "/notes/1470818 "}, {"RefNumber": "1470816", "RefComponent": "XAP-EM", "RefTitle": "EC Internal Server Error with ConcurrentModificatonException", "RefUrl": "/notes/1470816 "}, {"RefNumber": "1470752", "RefComponent": "XAP-EM", "RefTitle": "Incorrect EC Exception assignee name displayed in Facilities", "RefUrl": "/notes/1470752 "}, {"RefNumber": "1470248", "RefComponent": "XAP-EM", "RefTitle": "Analytical Query doesnt show correctly Facility Identifiers", "RefUrl": "/notes/1470248 "}, {"RefNumber": "1470027", "RefComponent": "XAP-EM", "RefTitle": "Increase Performance for searching Task Templates", "RefUrl": "/notes/1470027 "}, {"RefNumber": "1470026", "RefComponent": "XAP-EM", "RefTitle": "Task Instance opened from Status Analyzer could be locked", "RefUrl": "/notes/1470026 "}, {"RefNumber": "1469166", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP3 PL3", "RefUrl": "/notes/1469166 "}, {"RefNumber": "1469102", "RefComponent": "XAP-EM", "RefTitle": "Tasks aren't restricted correctly in EC Status Analyzer", "RefUrl": "/notes/1469102 "}, {"RefNumber": "1468047", "RefComponent": "XAP-EM", "RefTitle": "Task Instance opened from Status Analyzer could be locked", "RefUrl": "/notes/1468047 "}, {"RefNumber": "1467341", "RefComponent": "XAP-EM", "RefTitle": "Transport of deleted UDF element assignments do not work", "RefUrl": "/notes/1467341 "}, {"RefNumber": "1466327", "RefComponent": "XAP-EM", "RefTitle": "New warning message when a Task Template is deleted.", "RefUrl": "/notes/1466327 "}, {"RefNumber": "1466326", "RefComponent": "XAP-EM", "RefTitle": "Can not setup default values for EC Consumption Paramaters", "RefUrl": "/notes/1466326 "}, {"RefNumber": "1465470", "RefComponent": "XAP-EM", "RefTitle": "Do not allow to import EC data in inactive Facilities", "RefUrl": "/notes/1465470 "}, {"RefNumber": "1464873", "RefComponent": "XAP-EM", "RefTitle": "XML Data Source with no restrictions could occur errors", "RefUrl": "/notes/1464873 "}, {"RefNumber": "1464806", "RefComponent": "XAP-EM", "RefTitle": "EC Reporting Analytical Query Export to excel does not work", "RefUrl": "/notes/1464806 "}, {"RefNumber": "1464805", "RefComponent": "XAP-EM", "RefTitle": "Consumption Parameter values displayed wrong in EC Tasks", "RefUrl": "/notes/1464805 "}, {"RefNumber": "1464804", "RefComponent": "XAP-EM", "RefTitle": "Error opening Task Instance from Status Analyzer", "RefUrl": "/notes/1464804 "}, {"RefNumber": "1464803", "RefComponent": "XAP-EM", "RefTitle": "User with only view permissions should not lock Tasks", "RefUrl": "/notes/1464803 "}, {"RefNumber": "1462963", "RefComponent": "XAP-EM", "RefTitle": "Advanced Search for Reports does not show all Search types", "RefUrl": "/notes/1462963 "}, {"RefNumber": "1462889", "RefComponent": "XAP-EM", "RefTitle": "While pasting a List of Value system showes a general error", "RefUrl": "/notes/1462889 "}, {"RefNumber": "1461917", "RefComponent": "XAP-EM", "RefTitle": "Web Service error when EC Emissions are overlapped", "RefUrl": "/notes/1461917 "}, {"RefNumber": "1461913", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 standard reports could crash for emissions and consum", "RefUrl": "/notes/1461913 "}, {"RefNumber": "1460649", "RefComponent": "XAP-EM", "RefTitle": "Limit check start period before the requirement start date", "RefUrl": "/notes/1460649 "}, {"RefNumber": "1460414", "RefComponent": "XAP-EM", "RefTitle": "Locking mechanism for EC Tasks", "RefUrl": "/notes/1460414 "}, {"RefNumber": "1459983", "RefComponent": "XAP-EM", "RefTitle": "EC Consumption Task doesnt allow empty value without comment", "RefUrl": "/notes/1459983 "}, {"RefNumber": "1447289", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP3 Note", "RefUrl": "/notes/1447289 "}, {"RefNumber": "1463625", "RefComponent": "XAP-EM", "RefTitle": "Can not view comments from imported emissions via webservice", "RefUrl": "/notes/1463625 "}, {"RefNumber": "1459487", "RefComponent": "XAP-EM", "RefTitle": "Locking mechanism for Standard and Consumption Tasks", "RefUrl": "/notes/1459487 "}, {"RefNumber": "1459462", "RefComponent": "XAP-EM", "RefTitle": "Description is not taken over to manual created EC Task", "RefUrl": "/notes/1459462 "}, {"RefNumber": "1458431", "RefComponent": "XAP-EM", "RefTitle": "New EC Exception crashed when user canceled the process", "RefUrl": "/notes/1458431 "}, {"RefNumber": "1457730", "RefComponent": "XAP-EM", "RefTitle": "By deleting an EC Task,the system doesnt delete parameters", "RefUrl": "/notes/1457730 "}, {"RefNumber": "1457480", "RefComponent": "XAP-EM", "RefTitle": "Take Over button for Task Consumption Parameters doesnt work", "RefUrl": "/notes/1457480 "}, {"RefNumber": "1457139", "RefComponent": "XAP-EM", "RefTitle": "Remove EC Facility validity period columns for EC Tasks", "RefUrl": "/notes/1457139 "}, {"RefNumber": "1456952", "RefComponent": "XAP-EM", "RefTitle": "Changing of User Defined Fields validation in EC Tasks", "RefUrl": "/notes/1456952 "}, {"RefNumber": "1456735", "RefComponent": "XAP-EM", "RefTitle": "Technical Information not displayed in limit wizard", "RefUrl": "/notes/1456735 "}, {"RefNumber": "1456683", "RefComponent": "XAP-EM", "RefTitle": "Error while setting up automatic report generation", "RefUrl": "/notes/1456683 "}, {"RefNumber": "1456247", "RefComponent": "XAP-EM", "RefTitle": "Reports generated in background are not shown in \"MyReports\"", "RefUrl": "/notes/1456247 "}, {"RefNumber": "1455779", "RefComponent": "XAP-EM", "RefTitle": "Webservice xEM_TableLookup does not work", "RefUrl": "/notes/1455779 "}, {"RefNumber": "1454073", "RefComponent": "XAP-EM", "RefTitle": "New Object Based Permission \"Change all Task Instances\"", "RefUrl": "/notes/1454073 "}, {"RefNumber": "1454074", "RefComponent": "XAP-EM", "RefTitle": "EC doesnt show consumption parameter for calc. with versions", "RefUrl": "/notes/1454074 "}, {"RefNumber": "1452978", "RefComponent": "XAP-EM", "RefTitle": "Measurements from Tasks can be changed in Facility detail", "RefUrl": "/notes/1452978 "}, {"RefNumber": "1452613", "RefComponent": "XAP-EM", "RefTitle": "Description is not displayed on Triggered Task in Exceptions", "RefUrl": "/notes/1452613 "}, {"RefNumber": "1452399", "RefComponent": "XAP-EM", "RefTitle": "Navigate Permission in Facility Hierarchy is missing", "RefUrl": "/notes/1452399 "}, {"RefNumber": "1452253", "RefComponent": "XAP-EM", "RefTitle": "Subtask is incorrectly triggered from Master Task", "RefUrl": "/notes/1452253 "}, {"RefNumber": "1451677", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 7", "RefUrl": "/notes/1451677 "}, {"RefNumber": "1451471", "RefComponent": "XAP-EM", "RefTitle": "Incorrect Sort of Imported Data after Refresh", "RefUrl": "/notes/1451471 "}, {"RefNumber": "1451183", "RefComponent": "XAP-EM", "RefTitle": "New Calculation Information attributes for EC Reporting", "RefUrl": "/notes/1451183 "}, {"RefNumber": "1450207", "RefComponent": "XAP-EM", "RefTitle": "Assign Facility resp. users into the EC Exception automat.", "RefUrl": "/notes/1450207 "}, {"RefNumber": "1450116", "RefComponent": "XAP-EM", "RefTitle": "EC Exceptions with future dates", "RefUrl": "/notes/1450116 "}, {"RefNumber": "1449639", "RefComponent": "XAP-EM", "RefTitle": "EC Consumption Task displayed wrong Previous Amount", "RefUrl": "/notes/1449639 "}, {"RefNumber": "1449222", "RefComponent": "XAP-EM", "RefTitle": "Transfer of SAP Environmental Compliance 3.0 Master Data", "RefUrl": "/notes/1449222 "}, {"RefNumber": "1449039", "RefComponent": "XAP-EM", "RefTitle": "The material tab does not refresh for new Consumption Tasks", "RefUrl": "/notes/1449039 "}, {"RefNumber": "1446809", "RefComponent": "XAP-EM", "RefTitle": "Can not display document details in EC Tasks", "RefUrl": "/notes/1446809 "}, {"RefNumber": "1446222", "RefComponent": "XAP-EM", "RefTitle": "Unable to define Report Variant Filter Multilingual mames", "RefUrl": "/notes/1446222 "}, {"RefNumber": "1523661", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP6 Patch 1 Note", "RefUrl": "/notes/1523661 "}, {"RefNumber": "1445312", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 6", "RefUrl": "/notes/1445312 "}, {"RefNumber": "1445383", "RefComponent": "XAP-EM", "RefTitle": "Include Subordinate Drop down is missing in Facility Select", "RefUrl": "/notes/1445383 "}, {"RefNumber": "1444080", "RefComponent": "XAP-EM", "RefTitle": "Object Based Navigation for EC 3.0 UWL Items", "RefUrl": "/notes/1444080 "}, {"RefNumber": "1443343", "RefComponent": "XAP-EM", "RefTitle": "EC30 EHS Integration: System does not show logs correctly", "RefUrl": "/notes/1443343 "}, {"RefNumber": "1442680", "RefComponent": "XAP-EM", "RefTitle": "Obsolete Source Category still appears in Calculation Tree", "RefUrl": "/notes/1442680 "}, {"RefNumber": "1442013", "RefComponent": "XAP-EM", "RefTitle": "Consumption Task displays columns \"Mine\" and \"Waste-Code\"", "RefUrl": "/notes/1442013 "}, {"RefNumber": "1441992", "RefComponent": "XAP-EM", "RefTitle": "Consumption Tasks displayed Parameters incorrectly initially", "RefUrl": "/notes/1441992 "}, {"RefNumber": "1441756", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 4", "RefUrl": "/notes/1441756 "}, {"RefNumber": "1441483", "RefComponent": "XAP-EM", "RefTitle": "Limit Check - Email not sent for Facility Aggregation", "RefUrl": "/notes/1441483 "}, {"RefNumber": "1441378", "RefComponent": "XAP-EM", "RefTitle": "Calculation name of Emissions does not work correctly", "RefUrl": "/notes/1441378 "}, {"RefNumber": "1440799", "RefComponent": "XAP-EM", "RefTitle": "Error Saving EC Object Based Permission with Portal Role", "RefUrl": "/notes/1440799 "}, {"RefNumber": "1440795", "RefComponent": "XAP-EM", "RefTitle": "EC Facility Integration - Material name can not be read", "RefUrl": "/notes/1440795 "}, {"RefNumber": "1440252", "RefComponent": "XAP-EM", "RefTitle": "Incorrect name appears in all Portal Content OBN Page Names", "RefUrl": "/notes/1440252 "}, {"RefNumber": "1541462", "RefComponent": "XAP-EM", "RefTitle": "Duplicate rows in EC Analytical Queries when rollup by date", "RefUrl": "/notes/1541462 "}, {"RefNumber": "1439777", "RefComponent": "XAP-EM", "RefTitle": "Workprotect mode does not work for new created EC Exceptions", "RefUrl": "/notes/1439777 "}, {"RefNumber": "1438489", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 System does not show the EC Propties", "RefUrl": "/notes/1438489 "}, {"RefNumber": "1437492", "RefComponent": "XAP-EM", "RefTitle": "Deleted Hierarchy Nodes can not be transported via Activity", "RefUrl": "/notes/1437492 "}, {"RefNumber": "1436938", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 5", "RefUrl": "/notes/1436938 "}, {"RefNumber": "1436641", "RefComponent": "XAP-EM", "RefTitle": "Timestamp changes on released transport activity", "RefUrl": "/notes/1436641 "}, {"RefNumber": "1435948", "RefComponent": "XAP-EM", "RefTitle": "Web Application Server restarts after call Emission Detail", "RefUrl": "/notes/1435948 "}, {"RefNumber": "1435944", "RefComponent": "XAP-EM", "RefTitle": "Display user name incorrect in EC 3.0 Technical Information", "RefUrl": "/notes/1435944 "}, {"RefNumber": "1435907", "RefComponent": "XAP-EM", "RefTitle": "Calculation Scheduler Job does not work in EC 3.0 SP2", "RefUrl": "/notes/1435907 "}, {"RefNumber": "1435652", "RefComponent": "XAP-EM", "RefTitle": "Transport of EC Facility Type Configuration does not work", "RefUrl": "/notes/1435652 "}, {"RefNumber": "1435224", "RefComponent": "XAP-EM", "RefTitle": "Personalization for EC 3.0 Tasks & Exceptios is not possible", "RefUrl": "/notes/1435224 "}, {"RefNumber": "1434886", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP3 PL1", "RefUrl": "/notes/1434886 "}, {"RefNumber": "1432966", "RefComponent": "XAP-EM", "RefTitle": "Hide Display Content Tray in the EC 3.0 SP2 PL2 Dashboard", "RefUrl": "/notes/1432966 "}, {"RefNumber": "1432618", "RefComponent": "XAP-EM", "RefTitle": "Link to Manually created Task in Exception disappears", "RefUrl": "/notes/1432618 "}, {"RefNumber": "1428852", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 3", "RefUrl": "/notes/1428852 "}, {"RefNumber": "1428851", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 2", "RefUrl": "/notes/1428851 "}, {"RefNumber": "1539164", "RefComponent": "XAP-EM", "RefTitle": "Cannot load all EC Facilities without \"inactive\" permissions", "RefUrl": "/notes/1539164 "}, {"RefNumber": "1415724", "RefComponent": "XAP-EM", "RefTitle": "EC 3.0 Functional Documentation", "RefUrl": "/notes/1415724 "}, {"RefNumber": "1406695", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2", "RefUrl": "/notes/1406695 "}, {"RefNumber": "1382995", "RefComponent": "XAP-EM", "RefTitle": "Error regenerating EC Reports if restriction has null value", "RefUrl": "/notes/1382995 "}, {"RefNumber": "1523738", "RefComponent": "XAP-EM", "RefTitle": "Avoid overlapping EC Calculation Scheduler jobs", "RefUrl": "/notes/1523738 "}, {"RefNumber": "1353368", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP1", "RefUrl": "/notes/1353368 "}, {"RefNumber": "1346793", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP0 Patch 3", "RefUrl": "/notes/1346793 "}, {"RefNumber": "1311784", "RefComponent": "XAP-EM", "RefTitle": "MII 12.0 Actions for Environmental Compliance 3.0", "RefUrl": "/notes/1311784 "}, {"RefNumber": "1307251", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP0 Patch 2", "RefUrl": "/notes/1307251 "}, {"RefNumber": "1303115", "RefComponent": "XAP-EM", "RefTitle": "BI Additional Documentation for Environmental Compliance 3.0", "RefUrl": "/notes/1303115 "}, {"RefNumber": "1297538", "RefComponent": "XAP-EM", "RefTitle": "SAP EC 3.0 adjustment of XSLT Templates for EC Reporting", "RefUrl": "/notes/1297538 "}, {"RefNumber": "1277367", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 Release Notes", "RefUrl": "/notes/1277367 "}, {"RefNumber": "1516256", "RefComponent": "XAP-EM", "RefTitle": "Data Release does not work correctly for imported data", "RefUrl": "/notes/1516256 "}, {"RefNumber": "1466804", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP3 PL2", "RefUrl": "/notes/1466804 "}, {"RefNumber": "1537589", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP7 Patch 1 Note", "RefUrl": "/notes/1537589 "}, {"RefNumber": "1504483", "RefComponent": "XAP-EM", "RefTitle": "Tasks were not removed in case of reassignment of Facilities", "RefUrl": "/notes/1504483 "}, {"RefNumber": "1503033", "RefComponent": "XAP-EM", "RefTitle": "Improved loading time of Object Based Authorization Profiles", "RefUrl": "/notes/1503033 "}, {"RefNumber": "1503441", "RefComponent": "XAP-EM", "RefTitle": "Reporting Aggregate keyfigures based on an weighted average", "RefUrl": "/notes/1503441 "}, {"RefNumber": "1432710", "RefComponent": "XAP-EM", "RefTitle": "Predecessor Task cannot be saved", "RefUrl": "/notes/1432710 "}, {"RefNumber": "1437555", "RefComponent": "XAP-EM", "RefTitle": "Rendering of empty Report result produces Log entry.", "RefUrl": "/notes/1437555 "}, {"RefNumber": "1416985", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP2 Patch 1", "RefUrl": "/notes/1416985 "}, {"RefNumber": "1401056", "RefComponent": "XAP-EM", "RefTitle": "SAP Environmental Compliance 3.0 SP1 Patch 1", "RefUrl": "/notes/1401056 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}