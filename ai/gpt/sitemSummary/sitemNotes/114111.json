{"Request": {"Number": "114111", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 419, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000466552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000114111?language=E&token=091412FBB6E1FEC713B0E5BEF56C6BD4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000114111", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000114111/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "114111"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 24}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.03.2000"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "114111 - Recompilation of incomes from settlement documents RWMBON07"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>A recompliation of income data in sales statistics from settlement documents is not yet implemented in Releases 3.0F to 4.0B but it is absolutely necessary.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (volume-based rebate), recompilation, settlement documents, business volume data</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>To restore the business volume and income data in the LIS statistics after a possible error, a recompilation is recommended which includes two steps:</p> <OL>1. First, a recompilation of the business volume data from the relevant documents is carried out (report RWMBON08).</OL> <OL>2. In the second step the income data is restored in the statistics by reading the settled incomes from the settlement documents (report RWMBON07).</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Report RWMBON07 is delivered as of Release 4.5A and can be implemented in previous releases with this note.For this, replace the old program code by the attached source code.<br />Note that the version delivered with Hot Package 10 (4.0B) has an error. Implement source code in the current version or apply Hot Package 18.<br />Additional information for customers who have already implemented Note 97157 (Release 3.0F) or Note 112993 (Release 3.1I):<br />In this case, you must activate some lines by deactivation after replacing the entire source code.The guideline for this can be found before the corresponding source code which replaces the old one.<br /><br />Release 4.0B:<br />If you have already implemented the note, check that the source code correction from Note 176433 has been implemented (correction for this note).<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025477)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000114111/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000114111/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "375119", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating provisions for accrued inc. of parked credit memos", "RefUrl": "/notes/375119"}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047"}, {"RefNumber": "302145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "RMCENEUA: PIS, business volume data updated repeatedly", "RefUrl": "/notes/302145"}, {"RefNumber": "168242", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 4.0B", "RefUrl": "/notes/168242"}, {"RefNumber": "153054", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompltn of busnss volume data deletes all busnss vlme data", "RefUrl": "/notes/153054"}, {"RefNumber": "120610", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Data loss recompilation vendor volume data with short dump", "RefUrl": "/notes/120610"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199 "}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "375119", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating provisions for accrued inc. of parked credit memos", "RefUrl": "/notes/375119 "}, {"RefNumber": "153054", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompltn of busnss volume data deletes all busnss vlme data", "RefUrl": "/notes/153054 "}, {"RefNumber": "120610", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Data loss recompilation vendor volume data with short dump", "RefUrl": "/notes/120610 "}, {"RefNumber": "302145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "RMCENEUA: PIS, business volume data updated repeatedly", "RefUrl": "/notes/302145 "}, {"RefNumber": "168242", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 4.0B", "RefUrl": "/notes/168242 "}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F90", "URL": "/supportpackage/SAPKH30F90"}, {"SoftwareComponentVersion": "SAP_HR 30F", "SupportPackage": "SAPKE30F90", "URL": "/supportpackage/SAPKE30F90"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H69", "URL": "/supportpackage/SAPKH31H69"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H38", "URL": "/supportpackage/SAPKH31H38"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H38", "URL": "/supportpackage/SAPKE31H38"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H69", "URL": "/supportpackage/SAPKE31H69"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I40", "URL": "/supportpackage/SAPKH31I40"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I15", "URL": "/supportpackage/SAPKH31I15"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I15", "URL": "/supportpackage/SAPKE31I15"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I40", "URL": "/supportpackage/SAPKE31I40"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B10", "URL": "/supportpackage/SAPKH40B10"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B18", "URL": "/supportpackage/SAPKH40B18"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B10", "URL": "/supportpackage/SAPKE40B10"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B18", "URL": "/supportpackage/SAPKE40B18"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000114111/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}