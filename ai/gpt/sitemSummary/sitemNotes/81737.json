{"Request": {"Number": "81737", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 300, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014518372017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000081737?language=E&token=6429D06B4F40FEBE338A7336CAAB9775"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000081737", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000081737/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "81737"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1018}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "81737 - DB2-z/OS: APAR List"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n\r\n<p>Some of the products used for this platform need&#160;to be installed in order to run with SAP.</p>\r\n<p><strong>Important</strong><br />This SAP note is maintained by the joint IBM/SAP development team. All recommendations herein come from or are in accordance with IBM.</p>\r\n<p><strong>Product lifecycles</strong></p>\r\n<ul>\r\n<li>z/OS:&#160;<a target=\"_blank\" href=\"https://www.ibm.com/support/pages/lifecycle/details/?q45=X024277Z14891Q88&#126;N270728W93584E26&#126;B227540X25949K69\">https://www.ibm.com/support/pages/lifecycle/details/?q45=X024277Z14891Q88&#126;N270728W93584E26&#126;B227540X25949K69</a></li>\r\n<li>Db2:&#160;<a target=\"_blank\" href=\"https://www.ibm.com/support/pages/lifecycle/details/?q45=F813708O81095V07&#126;L182056Z52075A39&#126;Y500397H59081K65\">https://www.ibm.com/support/pages/lifecycle/details/?q45=F813708O81095V07&#126;L182056Z52075A39&#126;Y500397H59081K65</a></li>\r\n<li>Db2 Connect:&#160;<a target=\"_blank\" href=\"http://www-01.ibm.com/software/support/lifecycleapp/PLCDetail.wss?q45=B238262L17455M02&#126;I522216J10212H58&#126;O235076Y14930Y21&#126;S214235X04710T84\">https://www.ibm.com/support/lifecycle/search?q=DB2%20Connect%20Enterprise</a></li>\r\n</ul>\r\n<p><strong>Automated PTF Check</strong><br />The PTF check tool automatically retrieves the current SAP Note 81737 and the fix level file (SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/364109\">364109</a>) from the SAP Support Portal.<br />For details refer to SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/183311\">183311</a>.</p>\r\n<p><br /><br />Change history of this note:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Date</td>\r\n<td>Version</td>\r\n<td>Summary of what was changed</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2024</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>02/02</td>\r\n<td>1018</td>\r\n<td>Db2 13 function level 504 (FL504) certified</td>\r\n</tr>\r\n<tr>\r\n<td>01/23</td>\r\n<td>1015</td>\r\n<td>Successful tests of Db2 13 maintenance PUT levels 202308, 202309 and 202310 completed.<br />Db2 13 for z/OS: Recommended PUT Level 2310 (including all previous PUT Levels).<br />Next fix level file (note 364109)<br />New Db2 Connect CD with SB for JDBC (details in&#160;SAP Note&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/1927404\">1927404</a>&#160;)</td>\r\n</tr>\r\n<tr>\r\n<td>2023</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>12/12</td>\r\n<td>1014</td>\r\n<td>\r\n<p>Successful tests of Db2 12 maintenance PUT levels 202307, 202308 and 202309 completed.<br />Db2 12 for z/OS: Recommended PUT Level 2309 (including all previous PUT Levels).<br />Next fix level file (note 364109)</p>\r\n<p>Added PH58644 CLI AUTOMATED FAILBACK NOT WORKING</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>10/25</td>\r\n<td>1013</td>\r\n<td>\r\n<p>Successful tests of Db2 13 maintenance PUT levels 202305, 202306 and 202307 completed.<br />Db2 13 for z/OS: Recommended PUT Level 2307 (including all previous PUT Levels).<br />Next fix level file (note 364109)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>09/20</td>\r\n<td>1012</td>\r\n<td>\r\n<p>Successful tests of Db2 12 maintenance PUT levels 202304, 202305 and 202306 completed.<br />Db2 12 for z/OS: Recommended PUT Level 2306 (including all previous PUT Levels).<br />Next fix level file (note 364109)<br />New Db2 Connect CD with SB for JDBC (details in&#160;SAP Note&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/1927404\">1927404</a>&#160;)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>06/29</td>\r\n<td>1011</td>\r\n<td>\r\n<p>Successful tests of Db2 13 maintenance PUT levels 202302, 202303 and 202304 completed.<br />Db2 13 for z/OS: Recommended PUT Level 2304 (including all previous PUT Levels).<br />Next fix level file (note 364109)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>05/26</td>\r\n<td>1010</td>\r\n<td>\r\n<p>Successful tests of Db2 12 maintenance PUT levels 202301, 202302 and 202303 completed.<br />Db2 12 for z/OS: Recommended PUT Level 2303 (including all previous PUT Levels).<br />Next fix level file (note 364109)</p>\r\n<p>Added PH52821, PH54211, PH54507</p>\r\n<p>Removed references to z/OS 2.3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>05/04</td>\r\n<td>1009</td>\r\n<td>\r\n<p>Db2 13 function level 503 (FL503) certified</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>03/31</td>\r\n<td>1006</td>\r\n<td>\r\n<p>Successful tests of Db2 13 maintenance PUT levels 202211, 202212 and 202301 completed.<br />Db2 13 for z/OS: Recommended PUT Level 2301 (including all previous PUT Levels).<br />Next fix level file (note 364109)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>02/17</td>\r\n<td>1005</td>\r\n<td>\r\n<p>Successful tests of Db2 12 maintenance PUT levels 202210, 202211 and 202212 completed.<br />Db2 12 for z/OS: Recommended PUT Level 2212 (including all previous PUT Levels).<br />Next fix level file (note 364109)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>01/31</td>\r\n<td>1003</td>\r\n<td>\r\n<p>Successful tests of Db2 13 maintenance PUT levels 202206, 202207, 202208, 202209 and 202210 completed.<br />Db2 13 for z/OS: Recommended PUT Level 2210 (including all previous PUT Levels).<br />Next fix level file (note 364109)<br />Added z/OS System Automation APAR OA63994</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>01/03</td>\r\n<td>1002</td>\r\n<td>Db2 13 function level 502 (FL502) certified</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>DB2/390 MVS AIX service fix level patch OS/390 PTF APAR ++APAR APARFIX<br />DB2-z/OS z/OS DB2 Db2</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>===== Table of contents of SAP Note 81737 =====<br /></strong>- General remarks on highly recommended service level<br />- Web Sites<br />- SAP on Linux for IBM Z and z/VM<br />- Db2 for z/OS<br />&#160;&#160;&#160;&#160;&#160;&#160;- Db2 12 for z/OS<br />&#160;&#160;&#160;&#160;&#160;&#160;- Db2 13 for z/OS<br />&#160;&#160;&#160;&#160;&#160;&#160;- Db2 Connect<br />&#160;&#160;&#160;&#160;&#160;&#160;- IBM Db2 Analytics Accelerator<br />- z/OS and z/OS-based products<br />&#160;&#160;&#160;&#160;&#160;&#160;- z/OS (BCP Base; z/OS USS Kernel)<br />&#160;&#160;&#160;&#160;&#160; - DFSMS/MVS NFS&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;- TCP/IP<br /><br /><br /><strong>===== General remarks on highly recommended service level =====</strong></p>\r\n<p>IBM recommends that customers be on a reasonably current service level of Db2 and z/OS components. It is important to understand that the APARs listed in this note are just the ones that are known to be a definite problem when running SAP on Db2 for z/OS. This knowledge was derived from customers of SAP on Db2 for z/OS who ran into a problem and from internal tests. In parallel, the IBM development organizations owning these products provide a lot more fixes that we do not even know of, but that you should apply nevertheless. In particular, there are highly pervasive APARs (HIPERs) that IBM recommends to install, and those are not listed here, rather you should contact your IBM service contact to get a list of those APARs as well as a reasonably current service level in general.<br /><br />The APARs and service levels specified in this note are considered a highly recommended minimum for your system. If you do not apply this minimum service, you are running an unnecessary risk with your SAP System since other SAP customers had problems without this service and you may run into the same problems later. <br /><br />All&#160;APARs have been tested, and especially for each recommended service level, additional testing with SAP on Db2 for z/OS has been performed in order to minimize the risk that you introduce a problem to your systems when applying them. Nevertheless, it is good practice to first apply such service to your test system and only afterwards to your production system.<br />Please note that the service recommendations we make here are based on testing in our own environment. Your environment and applications will differ, and therefore, your results may also.<br /><br />This APAR list does not include all the prerequisite APARs or PTFs. At any given time a PTF from the list or some of its prerequisite PTFs can go PE (PTF in error) and that might not be reflected in this note.<br /><br />If IBM service cannot find a PTF for a particular APAR number, there is usually a ++APAR (a kind of preliminary PTF) available from the according IBM development laboratory. Please ask IBM service to obtain that ++APAR in this case. You may want to check with us through an SAP Customer Message to see whether this particular ++APAR has been tested with SAP on Db2 for z/OS.<br /><br />Use the listed APAR numbers to identify the most current service level (the newest PTFs) for your release. Note that we have also added the related PTF numbers.&#160;&#160;Please be aware that PTFs mentioned here may have been flagged PE or superseded by another PTF. It is your responsibility to make sure that the correct service level for the related APARs is installed on your system.<br /><br />At a given time, PTFs for a z/OS or Db2 release, which has not been certified yet for use with SAP, may already be mentioned in this note.&#160;&#160;This is done in order to allow customers a longer planning period when to install these PTFs.<br />Please check SAP's Product Availability Matrix (PAM) to find out which database (DB) and operating system combinations have already been released.<br />The PAM can be found at&#160;<a target=\"_blank\" href=\"http://support.sap.com/pam\">http://support.sap.com/pam</a>&#160;<br />Note that for access an identity is required.</p>\r\n<p><strong>===== Web Sites =====</strong></p>\r\n<p>Red Alerts for IBM z Systems and subscription service:<br /><a target=\"_blank\" href=\"http://www14.software.ibm.com/webapp/set2/sas/f/redAlerts/home.html\">http://www14.software.ibm.com/webapp/set2/sas/f/redAlerts/home.html</a><br />This service provides technical information for IT professionals who maintain IBM z Systems servers. You can find Red Alerts for the past year listed there, and you can also subscribe in order to receive an email with a link to support bulletins as they are released.<br /><br />Find more information on SAP on Db2 for z/OS at the SAP Community Network at:<br /><a target=\"_blank\" href=\"https://www.sap.com/community/topic/db2-for-zos.html\">https://www.sap.com/community/topic/db2-for-zos.html</a></p>\r\n<p><strong><br />===== SAP on Linux for IBM Z and z/VM =====</strong></p>\r\n<p><br />Refer to SAP Note <a target=\"_blank\" href=\"/notes/1452070\">1452070</a> for SAP on Linux for IBM Z and z/VM.</p>\r\n<p>When running Linux under z/VM, make sure that the PTFs for the following APAR are applied: <br />&#160;&#160; VM65992&#160; HIPERSOCKETS PERFORMANCE ISSUES ON SHORT BUSY<br />&#160;&#160;&#160;&#160;&#160; Release 640&#160;&#160; : UM35035</p>\r\n<p><br /><strong>===== Db2 for z/OS =====</strong></p>\r\n<p>The highly recommended minimum service is shown for each supported Db2 version below.<br />Service level YYMM may also be known as \"PUT level YYMM\" or \"source level YYMM\".<br />Even though this minimum level has been verified to be PE-free at the time it was created, it is possible that PTFs are recognized to be in error afterwards.&#160;&#160;If this is the case, the PTFs are flagged 'PE' ('PTF in error') in RETAIN, and you should contact IBM service before applying them in order to come to a decision on how to proceed.<br /><br />Especially if you are migrating to a new Db2 release in the first 18-24 months after GA of that release, we propose to install the recommended service level and maintenance quarterly. Also make sure to review Enhanced HOLDDATA (see link below) for critical HIPERs and PEs on a weekly basis, and expedite them into production.<br /><br /></p>\r\n<p>There are different ways to find new HIPER-APARs and to check PTFs<br />if they have gone PE:<br />-&#160; See the following web page for HOLDDATA for z/OS:<br />&#160; &#160;https://public.dhe.ibm.com/390holddata/390holddata.html<br />&#160; &#160;Enhanced holddata for z/OS allows e.g. to identify missing PE, <br />&#160; &#160;HIPER, and Security/Ingegrity fixes, and the report is specific to<br />&#160; &#160;the target system. Daily updates are available. The retrieving of updates<br />&#160; &#160;and the generation of reports can be automated.<br />or:<br />-&#160; See the following web page for instructions to logon to IBMLink:<br />&#160; &#160;http://www.ibmlink.ibm.com/<br />&#160; &#160;Once logged on, from the IBMLink Main Menu, select the option number for ServiceLink, from which you then select the ASAP (Automatic Software Alert Process) application, which lets you receive automatic notifications daily regarding critical service information, including software problems and corrections, for your products.</p>\r\n<p><br />Recommending this particular service level is based on&#160;tests in the SAP environment, conducted by SAP and IBM. The tests are performed quarterly, about 2-3 months after the quarterly service level becomes available.&#160; If you plan to use a higher service level, note that these tests have not been completed so far.</p>\r\n<p>The recommended service levels always contain all previous service levels up to and including the recommended level.</p>\r\n<p>Version that contains the last recommendation&#160;for DB2 10 for z/OS&#160;<br />&#160;&#160; SAP Note 81737 version 925, 11-09-2017</p>\r\n<p>Version that contains the last recommendation for Db2 11 for z/OS <br />&#160; &#160;SAP Note 81737 version 994, 04-12-2022</p>\r\n<p>Check out planned service availability dates for Db2 for z/OS at the link listed at the top of this note.<br /><br /><br /><strong>-----&#160;Db2 12 for z/OS -----</strong></p>\r\n<p>Note that Db2 12 for z/OS has been generally available (GA) since October 2016, and at the same time it has been SAP certified. This means that SAP applications could immediately run with Db2 12 for z/OS since GA.</p>\r\n<p>Refer to SAP Note 2302997 (Release of DB2 12 for SAP components) and see SAP Note 2303027 (Prereqs &amp; preparations for DB2 12).</p>\r\n<p>With Db2 12, function levels (FL) have to be certified, see SAP Note 2302997.</p>\r\n<p>The following Db2 12 function levels (FL) have been certified so far:<br />- 02-Dec-2021: FL510<br />- 16-Jul-2021: FL509<br />- 15-Feb-2021: FL508<br />- 05-Oct-2020: FL507<br />- 23-Jan-2020: FL506<br />- 01-Oct-2019: FL505<br />- 10-Jul-2019: FL504<br />- 24-Jan-2019: FL503<br />- 24-Sep-2018: FL502<br />- 18-Sep-2017: FL501<br />- 21-Oct-2016: FL500 (initial version)</p>\r\n<p>For information on migration see SAP Note 2303029 (Migration to DB2 12), and follow the link from there to the guide \"Migrating SAP Systems to DB2 12 for z/OS\".<br />To prepare for the required Db2 12 maintenance level make sure to install the listed service level and APARS.</p>\r\n<p>For the highly recommended minimum service level see below. Service level YYMM may also be known as \"PUT level YYMM\" or \"source level YYMM\".</p>\r\n<p>Even though this minimum level has been verified to be PE-free at the time it was created, it is possible that PTFs are recognized to be in error afterwards. See more details in the general \"Db2\" section above.</p>\r\n<p>ISISC has successfully tested the Db2 12 maintenance PUT levels 202307, 202308 and 202309.<br />These tests included maintenance for FL510. To see which function levels (FL) have been certified, view SAP Note 2302997 - Db2-z/OS:v12: Release of Db2 12 for SAP Components</p>\r\n<p>Information on next planned Db2 12 service level recommendations:<br />PUT level 202312 to be released by the end of January 2024.</p>\r\n<p>Current and recent recommended PUT Levels for Db2 12 for z/OS</p>\r\n<p>2309 current, since SAP Note 81737 version 1014, 12-12-2023<br />2306 SAP Note 81737 version 1012, 09-20-2023<br />2303 SAP Note 81737 version 1010, 05-25-2023<br />2212 SAP Note 81737 version 1004, 02-17-2023<br />2209 SAP Note 81737 version 1000, 12-23-2022<br />2206 SAP Note 81737 version 997, 08-12-2022<br />2202 SAP Note 81737 version 994, 04-12-2022</p>\r\n<p>APAR___ PTF____ Info________________________________________________</p>\r\n<p>.DB2/390 12.1___ PUT level 2309 is the tested and highly<br />. recommended minimum service level.<br />. The recommended service level contains all<br />. previous service levels up to and including the<br />. recommended level.<br />.<br />. Make sure to install the minimum service level<br />. plus all HIPER APARs<br />. plus the list of APARs in SAP Note 81737 (below)</p>\r\n<p>.FMID HDBCC10</p>\r\n<p>.FMID HIR2230</p>\r\n<p>Additional Db2 12 for z/OS APARs</p>\r\n<p>PTFs for the following Db2 APARs are required as soon as they become available. Check with IBM Service if you may obtain ++APARs until the PTF becomes available. Also, you may want to register with IBM Service to obtain them automatically once they become available.<br />APAR____ PTF____ Info________________________________________________<br />.FMID HDBCC10<br />.PH54211 V12/V13: REMOVING THE -DIS GROUP COMMAND FROM BEING<br />\\ CALLED WHEN INVOKING SYSPROC.ADMIN_COMMAND_DB2<br />\\ STORED PROCEDURE.<br />.PH54507 SERIOUS PERFORMANCE DEGRADATION WHEN RUNNING DB2 QUERIES<br />.PH58644 CLI AUTOMATED FAILBACK NOT WORKING</p>\r\n<p><strong>----- end of: Db2 12 for z/OS -----</strong></p>\r\n<p><strong><strong>-----&#160;Db2 13 for z/OS -----</strong></strong></p>\r\n<p>SAP released Db2 13 for z/OS for NetWeaver-based solutions on 24-Jun-2022.</p>\r\n<p>Refer to SAP Note 3152911 (Release of Db2 13 for SAP components) and SAP Note 3152993 (Prereqs &amp; preparations for Db2 13).</p>\r\n<p>With Db2 13, function levels (FL) have to be certified, see SAP Note 3152911.</p>\r\n<p>The following Db2 13 function levels (FL) have been certified so far:</p>\r\n<p>- 24-Jun-2022: FL501 (initial version)<br />- 03-Jan-2023: FL502<br />- 04-May-2023: FL503<br />- 02-Feb-2024: FL504</p>\r\n<p>For information on migration see SAP Note 3152955 (Migration to Db2 13), and follow the link in there to guide \"Migrating SAP Systems to Db2 13 for z/OS\".</p>\r\n<p>To prepare for the required Db2 13 maintenance level make sure to install the listed service level and APARS.</p>\r\n<p>ISISC has successfully tested the Db2 13 maintenance PUT levels 202308, 202309 and 202310.<br />These tests included maintenance for FL504. To see which function levels (FL) have been certified, view SAP Note 3152911</p>\r\n<p>Information on next planned Db2 13 service level recommendations:<br />PUT level 202401 to be released by the end of March 2024.</p>\r\n<p>Current and recent recommended PUT Levels for Db2 13 for z/OS:</p>\r\n<p>2310 current, since SAP Note 81737 version 1015 01-23-2024<br />2307 SAP Note 81737 version 1013 10-25-2023<br />2304 SAP Note 81737 version 1011, 06-29-2023<br />2301 SAP Note 81737 version 1006, 03-31-2023</p>\r\n<p>APAR___ PTF____ Info________________________________________________<br />.DB2/390 13.1___ PUT level 2310 is the tested and highly<br />. recommended minimum service level.<br />. The recommended service level contains all<br />. previous service levels up to and including the<br />. recommended level.<br />.<br />. Make sure to install the minimum service level<br />. plus all HIPER APARs<br />. plus the list of APARs in SAP Note 81737 (below)</p>\r\n<p>.FMID HDBDD10</p>\r\n<p>Additional Db2 13 for z/OS APARs</p>\r\n<p>PTFs for the following Db2 APARs are required as soon as they become available. Check with IBM Service if you may obtain ++APARs until the PTF becomes available. Also, you may want to register with IBM Service to obtain them automatically once they become available.<br />APAR____ PTF____ Info________________________________________________<br />.FMID HDBDD10<br />.PH54211 UI91988 V12/V13: REMOVING THE -DIS GROUP COMMAND FROM BEING<br />\\ CALLED WHEN INVOKING SYSPROC.ADMIN_COMMAND_DB2<br />\\ STORED PROCEDURE.<br />.PH54507 SERIOUS PERFORMANCE DEGRADATION WHEN RUNNING DB2 QUERIES</p>\r\n<p><strong><strong>----- end of: Db2 13 for z/OS -----</strong></strong></p>\r\n<p><strong>----- Db2 Connect -----</strong></p>\r\n<p>SAP uses IBM Data Server Driver for CLI/ODBC and JDBC/SQLJ (also named&#160;Db2 Connect) as database client for Db2 for z/OS.&#160;&#160; <br />It is&#160;recommended to <strong>use the latest certified level</strong>&#160;of IBM Data Server Driver.<br />Drivers certified by SAP apply to all Db2 for z/OS versions that are currently supported.</p>\r\n<p>Latest level released for usage by SAP:<br />- Db2 Connect 11.5 M9 FP0 + JDBC SB38015 - CD #51057392</p>\r\n<p>Details on additional APARs contained in Special Builds are documented in SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1927404\">1927404</a>.</p>\r\n<p>Both OEM and non-OEM customers can download IBM Data Server Drivers released for SAP from SAP Support Portal:&#160;&#160;&#160;<a target=\"_blank\" href=\"https://me.sap.com/softwarecenter\">https://me.sap.com/softwarecenter</a>&#160;<br />License files are provided to OEM customers only.</p>\r\n<p><strong>----- end of: Db2 Connect -----<br /></strong></p>\r\n<p>----- <strong>IBM Db2 Analytics Accelerator</strong> -----<br />Find current maintenance recommendations for IDAA&#160;in SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1649284\">1649284</a>.</p>\r\n<p>----- <strong>end of: IBM Db2 Analytics Accelerator</strong> -----</p>\r\n<p><strong>===== end of: Db2 for z/OS =====<br /></strong><br /><strong>===== z/OS and z/OS-based products =====</strong></p>\r\n<p>Before a z/OS version is released with the SAP system there are tests run by SAP on this z/OS version. Additionally highly recommended APARs on top are listed here in this note.<br /><strong>Check out planned service availability dates for z/OS releases under the link listed at the top of this note.</strong></p>\r\n<p>&#160;----- z/OS (BCP Base; z/OS USS Kernel) -----</p>\r\n<p>&#160;----- DBX - OPENMVS DEBUGGER -----</p>\r\n<p>&#160;z/OS&#160;&#160;&#160;&#160; 2.4____</p>\r\n<p>&#160;APAR___&#160; PTF____&#160; Info___________________________________________<br />.FMID&#160;&#160;&#160;&#160; HOT77C0<br />.OA59485&#160; UJ03953&#160; DBX -A MAY FAIL TO ATTACH TO A PROCESS ID WITH<br />\\&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; MSGFDBX0391 UNABLE TO SET MULTIPROCESS MODE</p>\r\n<p>&#160;----- DFSMS/MVS NFS -----</p>\r\n<p>&#160;----- Distrib. File Service (ZFS) -----</p>\r\n<p>&#160;----- Support for Unicode -----</p>\r\n<p>&#160;----- TCP/IP, Communications Server IP -----</p>\r\n<p>z/OS 2.40___</p>\r\n<p>APAR___ PTF____ Info___________________________________________<br />.FMID HIP6240<br />.PH38240 UI76984 CONNECTION IN CLOSEWAIT WITH PENDING READ EVENT<br />\\ WHEN USING AT-TLS</p>\r\n<p>z/OS 2.50___<br /> APAR___ PTF____ Info___________________________________________<br />.FMID HIP6250<br />.PH38240 UI76985 CONNECTION IN CLOSEWAIT WITH PENDING READ EVENT<br />\\ WHEN USING AT-TLS</p>\r\n<p>&#160;----- IBM System Automation for z/OS -----</p>\r\n<p>APAR___ PTF____ Info___________________________________________<br />.FMID HWRE410 IBM System Automation for z/OS V4.1; Base Auto. <br />.OA52307 UA91779 SA Z/OS V4R1 - APAR INTEGRATION AND GA FIXES<br />.OA54095 UA94895 SMALL ENHANCEMENTS<br />\\ Superseded by PTF .... UA95433 (OA55018)<br />.OA54684 UA97990 SMALL ENHANCEMENTS<br />.OA61966 UJ06473 THE SAP_OS390_CLUSTER_CONNECTOR.SH FAILS WITH ERROR:<br />\\ HOSTNAME OF APPSERVER INSTANCE COULD NOT BE<br />\\ DETERMINED FROM SAPLOCALHOST</p>\r\n<p>.FMID JWRE41F SA z/OS Extension<br />.OA52307 UA91780 SA Z/OS V4R1 - APAR INTEGRATION AND GA FIXES<br />.OA54095 UA94898 SMALL ENHANCEMENTS<br />.OA54684 UA97993 SMALL ENHANCEMENTS</p>\r\n<p>.FMID JWRE41C SA z/OS CICS Automation<br />.OA54095 UA94897 SMALL ENHANCEMENTS</p>\r\n<p>.FMID JWRE411 SA z/OS Base Automation Jpn - Kanji Support<br />.OA52307 UA91781 SA Z/OS V4R1 - APAR INTEGRATION AND GA FIXES<br />.OA54684 UA97994 SMALL ENHANCEMENTS</p>\r\n<p>APAR___ PTF____ Info___________________________________________<br />.FMID HWRE420 IBM System Automation for z/OS V4.2; Base Auto.<br />.OA58750 UJ02138 Z SYSTEM AUTOMATION V4R2 - GA FIXES<br />.OA61805 UJ06150 INGGROUP RESULTS IN AOF131I MESSAGE DUE TO<br />\\ NEW REQUIRED /APG<br />.OA61966 UJ06472 THE SAP_OS390_CLUSTER_CONNECTOR.SH FAILS WITH ERROR:<br />\\ HOSTNAME OF APPSERVER INSTANCE COULD NOT BE<br />\\ DETERMINED FROM SAPLOCALHOST<br />.OA63994 UJ92092 THE SHELL SCRIPT SAP_XPLATFORM IN ING_SAP.TAR<br />\\SHOULD BE CORRECTED</p>\r\n<p>APAR___ PTF____ Info___________________________________________<br />.FMID HWRE430 IBM System Automation for z/OS V4.3; Base Auto.<br />.OA63994 UJ92091 THE SHELL SCRIPT SAP_XPLATFORM IN ING_SAP.TAR<br />\\SHOULD BE CORRECTED</p>\r\n<p>.FMID JWRE42F SA z/OS Extension<br />.OA58750 UJ02139 Z SYSTEM AUTOMATION V4R2 - GA FIXES</p>\r\n<p><strong>== end of: z/OS and z/OS-based products ==</strong></p>\r\n<p><br />&#160;<strong>Machine readable stuff</strong></p>\r\n<p>.REPRL 7<br />.FIXLV 20240123<br />.REQID OS/390 4.27 HBB77C0 HOT77C0<br />.REQID OS/390 4.28 HBB77D0 HOT77C0<br />.REQID OS/390 5.29 HBB77E0 HOT77E0<br />.REQID z/OS 2.4 HBB77C0 HOT77C0<br />.REQID z/OS 2.5 HBB77D0 HOT77C0<br />.REQID z/OS 3.1 HBB77E0 HOT77E0<br />.REQID DB2CON 11.5 0000 0400 0600 0700 0800<br />.REQID DB212FL 100 500 501 502 503 504 505 506 507 508 509 510<br />.REQID DB213FL 100 500 501 502 503 504</p>\r\n<p>END_NOTE_CODE<br />UK&amp;NOTE_CODE&amp;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>n/a</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "DB2/390"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022624)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022624)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000081737/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2902423", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS: Support of APPLCOMPAT levels", "RefUrl": "/notes/2902423"}, {"RefNumber": "2303029", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v12: Migration to DB2 12", "RefUrl": "/notes/2303029"}, {"RefNumber": "2302997", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS:v12: Release of Db2 12 for SAP Components", "RefUrl": "/notes/2302997"}, {"RefNumber": "1850404", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Prereqs & preparations for DB2 11", "RefUrl": "/notes/1850404"}, {"RefNumber": "1850403", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Release of DB2 11 for SAP Components", "RefUrl": "/notes/1850403"}, {"RefNumber": "183311", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Automated PTF Check", "RefUrl": "/notes/183311"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3051958", "RefComponent": "BC-DB-DB2-DBA", "RefTitle": "Querying Db2 sap standard tables produces SQLCODE = -20008", "RefUrl": "/notes/3051958 "}, {"RefNumber": "3008941", "RefComponent": "BC-DB-DB2", "RefTitle": "SQL30108N A connection failed in an automatic client reroute environment", "RefUrl": "/notes/3008941 "}, {"RefNumber": "2985629", "RefComponent": "BC-DB-DB2-CCM", "RefTitle": "Dbacockpit \"Performance > Thread Activity\" does not report the SQL statement", "RefUrl": "/notes/2985629 "}, {"RefNumber": "2918427", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 REORG fails with abend code S04E REASON=X'00E40347'", "RefUrl": "/notes/2918427 "}, {"RefNumber": "1899645", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2: Error in phase MAIN_POSTP/SQLDB_TRUNCATE_DDXTF", "RefUrl": "/notes/1899645 "}, {"RefNumber": "2902128", "RefComponent": "BC-DB-DB2", "RefTitle": "Solman CCDB extractor programs dumping with runtime error 'STRING_OFFSET_TOO_LARGE'", "RefUrl": "/notes/2902128 "}, {"RefNumber": "2849731", "RefComponent": "BC-DB-DB2-UPG", "RefTitle": "SUM Upgrade:  Error copying table data table SPROXHDR", "RefUrl": "/notes/2849731 "}, {"RefNumber": "2786441", "RefComponent": "BC-DB-DB2", "RefTitle": "R3trans connect fails with SQL error 'SQL5005C'", "RefUrl": "/notes/2786441 "}, {"RefNumber": "2784330", "RefComponent": "BC-DB-DB2", "RefTitle": "SQLCODE = -270 reported when creating DB2 User Defined Functions (UDFs)", "RefUrl": "/notes/2784330 "}, {"RefNumber": "2757636", "RefComponent": "BC-DB-DB2-SYS", "RefTitle": "DSNB209I - Buffer manager cleanup message", "RefUrl": "/notes/2757636 "}, {"RefNumber": "2637250", "RefComponent": "BC-DB-DB2", "RefTitle": "Seeing error 'DSNU3330I' after applying DB2 11 z/OS PUT 1710 maintenance", "RefUrl": "/notes/2637250 "}, {"RefNumber": "2611182", "RefComponent": "BC-DB-DB2", "RefTitle": "IBM DB2 Administration Toolkit for z/OS  \"SAP Edition\"  for Db2 V12", "RefUrl": "/notes/2611182 "}, {"RefNumber": "2537733", "RefComponent": "BC-DB-DB2-UPG", "RefTitle": "Db2 z/OS: SQLCODE = -312 during activation of CDS view PVLOGPGMDAFX", "RefUrl": "/notes/2537733 "}, {"RefNumber": "3232404", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS: Adjustments to support of Db2 connect driver version", "RefUrl": "/notes/3232404 "}, {"RefNumber": "3152980", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v13: OEM installation Db2 13", "RefUrl": "/notes/3152980 "}, {"RefNumber": "3152955", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS:v13: Migration to Db2 13", "RefUrl": "/notes/3152955 "}, {"RefNumber": "3152993", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v13: Prereqs & preparations for Db2 13", "RefUrl": "/notes/3152993 "}, {"RefNumber": "3152911", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS:v13: Release of Db2 13 for SAP Components", "RefUrl": "/notes/3152911 "}, {"RefNumber": "2429167", "RefComponent": "FS-AM", "RefTitle": "Usage of account search in Fiori app for banking services from SAP - performance on different data bases", "RefUrl": "/notes/2429167 "}, {"RefNumber": "2429180", "RefComponent": "FS-AM", "RefTitle": "Usage of business partner search in Fiori apps for banking services from SAP - performance on different data bases", "RefUrl": "/notes/2429180 "}, {"RefNumber": "2723101", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS: RSDB2FIX corrections", "RefUrl": "/notes/2723101 "}, {"RefNumber": "2533653", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "Db2-z/OS: Installation of NW 7.52 fails in phase \"Install database client for ABAP\"", "RefUrl": "/notes/2533653 "}, {"RefNumber": "2369910", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/2369910 "}, {"RefNumber": "2303029", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v12: Migration to DB2 12", "RefUrl": "/notes/2303029 "}, {"RefNumber": "2303027", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v12: Prereqs & preparations for DB2 12", "RefUrl": "/notes/2303027 "}, {"RefNumber": "2302997", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS:v12: Release of Db2 12 for SAP Components", "RefUrl": "/notes/2302997 "}, {"RefNumber": "2196056", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS: SAP on Linux on KVM for IBM Z", "RefUrl": "/notes/2196056 "}, {"RefNumber": "1850406", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Migration to DB2 11", "RefUrl": "/notes/1850406 "}, {"RefNumber": "1850404", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Prereqs & preparations for DB2 11", "RefUrl": "/notes/1850404 "}, {"RefNumber": "1850403", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Release of DB2 11 for SAP Components", "RefUrl": "/notes/1850403 "}, {"RefNumber": "1925295", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: SAP Uninstall fails when dropping tablespaces", "RefUrl": "/notes/1925295 "}, {"RefNumber": "427748", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: CCMS corrections (6.10, 6.20, 6.40, 7.x)", "RefUrl": "/notes/427748 "}, {"RefNumber": "1480594", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Prereqs & preparations for DB2 10", "RefUrl": "/notes/1480594 "}, {"RefNumber": "1823660", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: High Availability with SA z/OS updates and fixes", "RefUrl": "/notes/1823660 "}, {"RefNumber": "1048303", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 5.x: Installation and upgrade", "RefUrl": "/notes/1048303 "}, {"RefNumber": "1452370", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Release of DB2 10 for SAP Components", "RefUrl": "/notes/1452370 "}, {"RefNumber": "1881267", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS: SAP Host Agent problem on z/OS", "RefUrl": "/notes/1881267 "}, {"RefNumber": "549268", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "SAP JCo 2.x release and support strategy", "RefUrl": "/notes/549268 "}, {"RefNumber": "1500074", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Installation & system copy with DB2 10", "RefUrl": "/notes/1500074 "}, {"RefNumber": "958253", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 10: Installation notes", "RefUrl": "/notes/958253 "}, {"RefNumber": "1486360", "RefComponent": "BC-DB-DB2-CCM", "RefTitle": "DB2-z/OS:v10: Readiness Check", "RefUrl": "/notes/1486360 "}, {"RefNumber": "534036", "RefComponent": "CA-LT-FRM", "RefTitle": "SAP LT: System settings for conversions", "RefUrl": "/notes/534036 "}, {"RefNumber": "1745672", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS: SAP R/3 release certification after z/OS 1.13", "RefUrl": "/notes/1745672 "}, {"RefNumber": "1753638", "RefComponent": "BC-OP-S390", "RefTitle": "z/OS: Enqueue Replication into System z Coupling Facility", "RefUrl": "/notes/1753638 "}, {"RefNumber": "1557416", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Recommended settings for Gigabit Ethernet", "RefUrl": "/notes/1557416 "}, {"RefNumber": "1309153", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: compression rate deteriorates after REORG on V9", "RefUrl": "/notes/1309153 "}, {"RefNumber": "746299", "RefComponent": "BC-OP-ZLNX-JSE", "RefTitle": "DB2-z/OS:Recommended Settings for IBM zLinux 142 Classic JVM", "RefUrl": "/notes/746299 "}, {"RefNumber": "1465174", "RefComponent": "BC-OP-ZLNX-JSE", "RefTitle": "DB2-z/OS:Overview of zLinux JVM for NetWeaver 6.40 and 7.0", "RefUrl": "/notes/1465174 "}, {"RefNumber": "1319038", "RefComponent": "BC-OP-ZLNX-JSE", "RefTitle": "DB2-z/OS:Recommended Settings for IBM 142 J9 JVM on zLinux", "RefUrl": "/notes/1319038 "}, {"RefNumber": "183311", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Automated PTF Check", "RefUrl": "/notes/183311 "}, {"RefNumber": "1650076", "RefComponent": "BC-OP-S390", "RefTitle": "DB2 z/OS: SAP on IBM zEnterprise(TM) BladeCenter® Extension", "RefUrl": "/notes/1650076 "}, {"RefNumber": "316353", "RefComponent": "BC-INS-MIG", "RefTitle": "INST: 4.6D SAP Basis - Heterogeneous System Copy", "RefUrl": "/notes/316353 "}, {"RefNumber": "1696924", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10:avoid DB2 broken pages (IBM APAR PM56535)", "RefUrl": "/notes/1696924 "}, {"RefNumber": "1452070", "RefComponent": "BC-OP-ZLNX", "RefTitle": "DB2-z/OS: SAP on Linux on IBM Z and z/VM", "RefUrl": "/notes/1452070 "}, {"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316 "}, {"RefNumber": "660528", "RefComponent": "BC-DB-DB2", "RefTitle": "Requirements to run SAP NetWeaver '04 on zSeries", "RefUrl": "/notes/660528 "}, {"RefNumber": "742335", "RefComponent": "BC-DB-DB2", "RefTitle": "zSeries: Problems with LOBs", "RefUrl": "/notes/742335 "}, {"RefNumber": "746924", "RefComponent": "BC-DB-DB2", "RefTitle": "Planning information for customers with SAP on zSeries", "RefUrl": "/notes/746924 "}, {"RefNumber": "1043951", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Release of DB2 V9 for SAP Components", "RefUrl": "/notes/1043951 "}, {"RefNumber": "728743", "RefComponent": "BC-DB-DB2", "RefTitle": "Series z: Release of DB2 V8 for SAP Components", "RefUrl": "/notes/728743 "}, {"RefNumber": "915482", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Automating DB failover", "RefUrl": "/notes/915482 "}, {"RefNumber": "1459078", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: EHPI / SAPup does an unwanted change of LOCKSIZE", "RefUrl": "/notes/1459078 "}, {"RefNumber": "1574858", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: SAP Appl.Server Policy Migration to SA z/OS V3.3", "RefUrl": "/notes/1574858 "}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084 "}, {"RefNumber": "1492800", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS saplicense and hybrid DbSl with DB2 10", "RefUrl": "/notes/1492800 "}, {"RefNumber": "1263782", "RefComponent": "BC-OP-ZLNX", "RefTitle": "DB2-z/OS: Recommended settings for HiperSockets (Linux on IBM Z)", "RefUrl": "/notes/1263782 "}, {"RefNumber": "892818", "RefComponent": "BC-OP-ZLNX", "RefTitle": "Application Servers on Linux for zSeries: Settings", "RefUrl": "/notes/892818 "}, {"RefNumber": "1528309", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Migration to DB2 10", "RefUrl": "/notes/1528309 "}, {"RefNumber": "386605", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Memory Management for Linux (32-bit)", "RefUrl": "/notes/386605 "}, {"RefNumber": "1032273", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Configuring DB2 V9", "RefUrl": "/notes/1032273 "}, {"RefNumber": "1512618", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: DB2 10 support for SMIGR_CREATE_DDL", "RefUrl": "/notes/1512618 "}, {"RefNumber": "113008", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Maintaining Catalog Statistics", "RefUrl": "/notes/113008 "}, {"RefNumber": "1473875", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Trusted Context", "RefUrl": "/notes/1473875 "}, {"RefNumber": "1420672", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: High Global Contention with z/OS 1.11 or OA27250", "RefUrl": "/notes/1420672 "}, {"RefNumber": "1406689", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:Virtualization kernel change affecting partitioning", "RefUrl": "/notes/1406689 "}, {"RefNumber": "848384", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Inconsistent fields with DDIC type RAW/LRAW/VARC", "RefUrl": "/notes/848384 "}, {"RefNumber": "717935", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: FTP replacement by Stored Procedures", "RefUrl": "/notes/717935 "}, {"RefNumber": "606682", "RefComponent": "BC-OP-ZLNX", "RefTitle": "High availability and automation solution for Linux zSeries", "RefUrl": "/notes/606682 "}, {"RefNumber": "682077", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Premigration checks for DB2 V8", "RefUrl": "/notes/682077 "}, {"RefNumber": "450949", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAP BW 2.1C Installation Asia", "RefUrl": "/notes/450949 "}, {"RefNumber": "327014", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Inst. NT dialog instances with OS/390 central insta", "RefUrl": "/notes/327014 "}, {"RefNumber": "311305", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.6D SAP Basis Installation on UNIX", "RefUrl": "/notes/311305 "}, {"RefNumber": "369391", "RefComponent": "BC-INS", "RefTitle": "SAP BW 2.1C Installation/Upgrade Asia", "RefUrl": "/notes/369391 "}, {"RefNumber": "390016", "RefComponent": "BW-SYS-DB-DB2", "RefTitle": "DB2-z/OS: BW: DB settings and performance", "RefUrl": "/notes/390016 "}, {"RefNumber": "750096", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: 6.40 downward compatible kernel", "RefUrl": "/notes/750096 "}, {"RefNumber": "387078", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Software on UNIX: OS Dependencies 4.6C SR2", "RefUrl": "/notes/387078 "}, {"RefNumber": "124562", "RefComponent": "BC-NET", "RefTitle": "Hostname resolution problems (DNS timeouts)", "RefUrl": "/notes/124562 "}, {"RefNumber": "914639", "RefComponent": "BC-DB-DB2-SYS", "RefTitle": "DB2-z/OS: SAPKH50010 SPAM Phase \"Main Import\" stands still", "RefUrl": "/notes/914639 "}, {"RefNumber": "826620", "RefComponent": "BW-SYS-DB-DB2", "RefTitle": "UD Connect to a DB2 Database on z/OS", "RefUrl": "/notes/826620 "}, {"RefNumber": "387074", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: R/3 4.6C SR2 Installation on UNIX", "RefUrl": "/notes/387074 "}, {"RefNumber": "544569", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.10", "RefUrl": "/notes/544569 "}, {"RefNumber": "517278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 3.1", "RefUrl": "/notes/517278 "}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941 "}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267 "}, {"RefNumber": "600824", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 4.0 / SRM Server 4.0", "RefUrl": "/notes/600824 "}, {"RefNumber": "603278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to SCM 4.0", "RefUrl": "/notes/603278 "}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516 "}, {"RefNumber": "776547", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: DB13 upload with stored procedure DSNACCDS", "RefUrl": "/notes/776547 "}, {"RefNumber": "750884", "RefComponent": "BC-OP-LNX", "RefTitle": "End of Maintenance by SuSE for SLES 7", "RefUrl": "/notes/750884 "}, {"RefNumber": "618675", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Bad performance in upgrade (RUN_INDC_UPG, ...)", "RefUrl": "/notes/618675 "}, {"RefNumber": "710466", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Upgrade to 6.40 - OS platforms not supported", "RefUrl": "/notes/710466 "}, {"RefNumber": "663492", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAP R/3 Enterprise on z/OS, UNIX and Windows", "RefUrl": "/notes/663492 "}, {"RefNumber": "649892", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: 6.30 Installation on z/OS, UNIX and Windows", "RefUrl": "/notes/649892 "}, {"RefNumber": "647882", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: PTF check in PREPARE overlooks missing PTFs", "RefUrl": "/notes/647882 "}, {"RefNumber": "598471", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: CCMS-Korrekturen (7.10)", "RefUrl": "/notes/598471 "}, {"RefNumber": "446226", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to EBP/CRM  3.0 SR1", "RefUrl": "/notes/446226 "}, {"RefNumber": "571110", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Add-on installation (zLinux, AIX, Solaris)", "RefUrl": "/notes/571110 "}, {"RefNumber": "520714", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Add. installation 46D Linux for zSeries app. server", "RefUrl": "/notes/520714 "}, {"RefNumber": "562203", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Large trace files during import", "RefUrl": "/notes/562203 "}, {"RefNumber": "100189", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Applying kernel patches", "RefUrl": "/notes/100189 "}, {"RefNumber": "399419", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: MCOD installation", "RefUrl": "/notes/399419 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}