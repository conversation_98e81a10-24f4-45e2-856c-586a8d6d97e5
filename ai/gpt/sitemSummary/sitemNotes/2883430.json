{"Request": {"Number": "2883430", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 234, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002883430?language=E&token=09932530C55B4CAEDAA3D200B12A6EC3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002883430", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002883430/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2883430"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP S/4HANA Data Migration Cockpit Content"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2883430 - SAP S/4HANA Cloud migration cockpit - Available fields per asset class for fixed asset data migration: AY 219: Field.... is not ready for input"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Asset data is being loaded into&#160;SAP S/4HANA via SAP S/4HANA migration cockpit and migration object <strong>Fixed asset (incl. balances)</strong>.</p>\r\n<p>Following error message us raised:</p>\r\n<p style=\"padding-left: 30px;\">AY 219: <strong>Field.... is not ready for input</strong></p>\r\n<p>( i.e. Field Inventory Number is not ready for input)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP S/4HANA Cloud, private edition (not valid for&#160;SAP S/4HANA Cloud, public edition)</li>\r\n</ul>\r\n<ul>\r\n<li>SAP S/4HANA</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>The screen layout of an asset depends on&#160;its asset class. Some fields that are visible for a specific asset class may not be visible for another asset class.&#160;If you&#160;enter data&#160;in&#160;a migration template field&#160;that is marked&#160;<em>hidden</em>&#160;or&#160;<em>read-only</em>&#160;for&#160;the asset class of your object, you will get the error message AY 219: <strong>Field.... is not ready for input</strong>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p><span style=\"text-decoration: underline;\">SAP S/4HANA:</span></p>\r\n<p>Open IMG, check and adapt your FI-AA customizing settings for&#160;<em>Screen Layout for Asset Master Data</em>&#160;-&gt;&#160;<em>Assigned Logical Field Groups</em></p>\r\n<p><span style=\"text-decoration: underline;\"><em>SAP S/4HANA Cloud</em>:</span></p>\r\n<p>On the SAP S/4HANA Product Assistant you will find&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/bc49894809864d03938df91a17ea28b7/SHIP/en-US\">Fixed Assets: Available Fields per Asset Class</a>&#160;with a list of delivered asset class screen layout settings for&#160;several SAP S/4HANA Cloud&#160;versions.&#160;The page&#160;includes the visible (ready for input) and not visible fields per asset class. You can also download the complete page as PDF document via the download button on the upper right corner of that Web page<sup>1</sup>.</p>\r\n<p><sup>1&#160;</sup><em>This Web page&#160;might mention releases of SAP software that are not yet available. SAP reserves the right to change the scope and the extent of the software to be delivered. Therefore, this document must be considered bearing this reservation in mind</em>.</p>\r\n<p>For the data migration, you&#160;<strong>can&#160;only enter data</strong>&#160;into the migration template&#160;<strong>fields</strong>&#160;that are&#160;&#160;<strong>marked&#160;<em>required</em>&#160;or&#160;<em>optional&#160;</em></strong>for the screen layout assigned to your asset class.</p>\r\n<p>You always&#160;<strong>have to provide</strong>&#160;data for fields marked as<strong>&#160;<em>required</em></strong>.</p>\r\n<p>If data is filled into fields marked as&#160;<strong><em>hidden</em>&#160;or&#160;<em>read-only</em>&#160;</strong>the API will throw&#160;<strong>error message AY 219</strong>: Field.... is not ready for input.</p>\r\n<ol>\r\n<li>Open the&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/bc49894809864d03938df91a17ea28b7/SHIP/en-US\">Fixed Assets: Available Fields per Asset Class</a><a target=\"_blank\" href=\"https://help.sap.com/viewer/bc49894809864d03938df91a17ea28b7/\"><br /></a></li>\r\n<li>Choose your SAP S/4HANA Cloud release</li>\r\n<li>Navigate to&#160;<em>Asset Class</em></li>\r\n<li>Get the screen layout rule of your asset class, for example <em>S100</em> for asset class <em>1000</em> (Real Estate (Land)).</li>\r\n<li>Navigate to&#160;<em>Screen Layout for Asset Master Data</em>&#160;-&gt;&#160;<em>Assigned Logical Field Groups</em></li>\r\n<li>Verify the&#160;<em>hidden</em>&#160;and&#160;<em>read-only</em>&#160;fields for layout rule <em>S100</em>. For example <em>Serial number</em>, <em>Inventory Indicator</em> and so on, are marked as&#160;<em>hidden</em>.</li>\r\n<li>Go to your Migration Template and fill the data only in those fields that are not marked as&#160;<em>hidden</em>&#160;or&#160;<em>read-only</em>.</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>Fixed assets, SAP S/4HANA migration cockpit, LTMC, Asset class, Fixed assets,&#160;AY 219, Field Original Asset is not ready for input</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I505571)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I505571)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883430/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "", "RefComponent": "", "RefTitle": "Fixed Assets: Available Fields per Asset Class", "RefUrl": "https://help.sap.com/viewer/bc49894809864d03938df91a17ea28b7/SHIP/en-US"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2720329", "RefComponent": "FI-AA-AA-A", "RefTitle": "Uploading Asset template in the Migration Cockpit results in error AY 219 Field not ready", "RefUrl": "/notes/2720329 "}, {"RefNumber": "2468501", "RefComponent": "CA-GTF-MIG", "RefTitle": "see 2883430 - SAP S/4HANA Cloud migration cockpit - Available fields per asset class for fixed asset data migration: AY 219: \"Field.... is not ready for input\"", "RefUrl": "/notes/2468501 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP S/4HANA all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}