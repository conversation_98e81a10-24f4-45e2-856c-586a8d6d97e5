{"Request": {"Number": "913171", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 334, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016040322017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000913171?language=E&token=6DBE8E3FF7438415535E8C77E40554DF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000913171", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "913171"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.02.2006"}, "SAPComponentKey": {"_label": "Component", "value": "PA-ESS-XX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Common Parts"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Management", "value": "PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Personal Information ESS scenarios", "value": "PA-ESS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-ESS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Common Parts", "value": "PA-ESS-XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-ESS-XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "913171 - Adapter enhancements for Allowed Infotype/subtype"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /><B>Note</B>: This note describes enhancements in the XSS Adapter which had no effect on the existing applications.&#x00A0;&#x00A0;Please ignore this Note if you are looking for solutions to problems in applications.</p> <UL><LI>Application developers want to use XSS Adapter to read master data for Infotypes.&#x00A0;&#x00A0;However, XSS adapter allowed read of master data only if there is an valid entry for Infotype/subtype present in the table T7XSSPERSUBTYPE.&#x00A0;&#x00A0;Application developers would like to override the default settings of T7XSSPERSUBTYPE.</LI></UL> <p></p> <UL><LI>Application developers want to use XSS Adapter to read master data which was valid in past (and is not valid currently). XSS Adapter does not read such master data.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Allowed Infotype/subtype<br />Reading of past data<br />Setting the begin date<br />CL_HRPA_PERNR_INFTY_XSS<br />IF_HRPA_PERNR_INFTY_XSS<br />Metadata<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><UL><LI>As per self-services requirement, XSS Adapter is designed to look into table T7XSSPERSUBTYPE to get information on which Infotype/subtype allowed to be read.&#x00A0;&#x00A0;Even though customers have a way to override this mechanism by implementing BADI, there was no way for application developers to override this mechanism.</LI></UL> <p></p> <UL><LI>As per self-services requirement, XSS Adapter is designed to ignore all infotype records which are not valid as on system date.&#x00A0;&#x00A0;Even though customers have a way to override this mechanism by implementing BADI, there was no way for application developers to override this mechanism.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />XSS adapter (IF_HRPA_PERNR_INFTY_XSS, CL_HRPA_PERNR_INFTY_XSS) was enhanced to include new methods and Function Modules as described below:<br /> <br /><B>Method SET_SUBTY:</B> This method has importing parameter, which could be used to set and override the Allowed Subtype content read from T7XSSPERSUBTYP.<br /><br /><B>Method SET_BEGDA:</B> This method has importing parameter, which could be used to set and override the date used to discard master data which is not valid on system date.<br /><br />These methods may be invoked right after the invocation of GET_INSTANCE Method in the adapter.<br /><br />Apart from these two methods in Adapter Class, this solution also provides Function Modules which may be used to access these newly provided methods in the Adapter Class. These Function Modules are within the Function Group HRXSS_PER_MAC.<br /><br /><B>Function Module:</B> HRXSS_PER_SET_SUBTY<br /><B>Function Module:</B> HRXSS_PER_SET_BEGDA<br /><br />These function modules may be executed right after execution of Function Module HRXSS_PER_INIT_PERNR<br /><br />Please apply relavent HR Support pack to get the corrections.<br /><br />Note:<br />_____<br /><br />The corrections are included in the note attachment as a .SAR file. Please refer to the notes 480180 and 13719 for information on downloading and installing changes from Note attachements.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-ESS-WD (ESS Web Dynpro)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I031727)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000913171/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913171/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "P7HK004966.sar", "FileSize": "22", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000018262006&iv_version=0002&iv_guid=3D0F1DE445002D4FA78F1F05D10498B7"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "920491", "RefComponent": "PA-ESS-XX", "RefTitle": "ESS:Change Own Data enhancement to allow multi record update", "RefUrl": "/notes/920491 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-HR", "From": "500", "To": "500", "Subsequent": "X"}, {"SoftwareComponent": "EA-HR", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-HR 500", "SupportPackage": "SAPKGPHC38", "URL": "/supportpackage/SAPKGPHC38"}, {"SoftwareComponentVersion": "EA-HR 500", "SupportPackage": "SAPKGPHC18", "URL": "/supportpackage/SAPKGPHC18"}, {"SoftwareComponentVersion": "EA-HR 600", "SupportPackage": "SAPKGPHD04", "URL": "/supportpackage/SAPKGPHD04"}, {"SoftwareComponentVersion": "EA-HR 600", "SupportPackage": "SAPKGPHD03", "URL": "/supportpackage/SAPKGPHD03"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}