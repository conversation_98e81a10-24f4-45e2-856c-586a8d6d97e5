{"Request": {"Number": "923610", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 344, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016058032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=4E1CD7B4B2AB98F998484A3F679B023B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "923610"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.11.2012"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Bank Analyzer"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Bank Analyzer", "value": "FS-BA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "923610 - Memory parameter recommendations for banking systems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains recommendations for memory configuration and memory management for Bank Analyzer and Banking Services systems.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Performance, EarlyWatch, GoingLive, memory management</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><b>1. Bank Analyzer 4.2 and 5.0</b><br /> <p>The Bank Analyzer system is installed as an add-on to a Business Warehouse (BW) system. However, the parameter settings for memory configuration and memory management deviate from the recommended settings for BW systems (see Note 192658). The Bank Analyzer system has virtually the same settings as an OLTP /R/3 system (see SAP Note 103747).<br />Recommendations for database settings for Bank Analyzer systems are available in Note 778700.<br /></p> <b>2. Banking Services systems FS-APPL 100, 200, 300 and 400</b><br /> <p>Banking Services is installed as an add-on in a NetWeaver system. The recommendations below also apply to Banking Services systems including Analytical Banking and Operational Banking.<br /></p> <b>GoingLive checks</b><br /> <p>As in the notes mentioned, the recommendations mentioned below are intended as initial settings before the production startup. After the production startup, you may have to adjust individual parameters.<br />To ensure a good performance after the production startup we recommend you use the services of SAP Support, for example, the GoingLive check.<br />As part of a GoingLive check for banking systems, you must schedule a GoingLive analysis session four to six weeks before the production startup and GoingLive verification session after the production startup, and this must be in the week after a period settlement.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Enter the following parameters as initial values. Check the parameter settings currently in use and make adjustments where necessary. This note is a combination of different notes concerning memory configuration and memory management. Therefore, consider the related notes that are listed at the end of this document.</p> <b>1.&#x00A0;&#x00A0;Memory Configuration</b><br /> <b>1.1 Buffer settings</b><br /> <p>ABAP/buffer size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1,000,000<br />rsdb/ntab/entrycount&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;30 000<br />rsdb/ntab/ftabsize&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;30 000<br />rsdb/ntab/sntabsize&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 500<br />rsdb/ntab/irbdsize&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 000<br />rtbb/buffer_length&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50 000<br />rtbb/max_tables&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 000<br />rsdb/cua/buffersize&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 000<br />zcsa/table_buffer_area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150 000 000<br />zcsa/db_max_buftab&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50,000<br />zcsa/presentation_buffer_area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20 000 000<br />ztta/dynpro_area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;800 000<br />sap/bufdir_entries&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10 000<br />zcsa/calendar_area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;500 000<br />nobuf/max_no_buffer_entries&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 000<br />rsdb/obj/buffersize&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40 000<br />rsdb/obj/max_objects&#x00A0;&#x00A0;between&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 13 000<br />rsdb/obj/large_object_size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 192<br />rsdb/esm/buffersize_kb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40 000<br />rsdb/esm/max_objects&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10 000<br />rsdb/otr/buffersize_kb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 096<br />zsca/calendar_ids&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200</p> <b>1.2 Memory Management</b><br /> <b>The following section contains the parameter recommendations for UNIX, AIX and Windows. Since Linux and Windows have Zero Administration Management, you should NOT set the parameters specified with (*). It is important that you read Notes 88416, 386605 and 941735.</b><br /><b>For IBM AS/400, IBM S/390, Digital Unix, HP-UX and SNI/SINIX, read the information in Note 103747, for iSeries read Note 808607.</b><br /> <p>em/initial_size_MB (*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;from 16,000 to 64,000<br />em/global_area_MB</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD><B>Unix:</B>10% of em/initial_size_MB, but 255 maximum</TD></TR> <TR><TD><B>Windows:</B> maximum of 10 % of em/address_space_MB</TD></TR> <TR><TD><B>AIX and 32-bit:</B> 255 - em/blocksize_KB / 1024</TD></TR> <TR><TD><B>AIX and 64-bit: </B>250 (the exact fomula is explained in Note 789477)</TD></TR> </TABLE> <p>em/address_space_MB</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD><B>Linux and Windows 32-bit</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;512</TD></TR> <TR><TD><B>Windows 64-bit</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4096</TD></TR> <TR><TD><B>Linux 64-bit</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 941735</TD></TR> </TABLE> <p>em/max_size_MB</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD><B>Windows 32-bit </B>20 000</TD></TR> <TR><TD><B>Windows 64-bit </B>100 000</TD></TR> <TR><TD><B>Linux </B>do not set (Notes 386605 and 941735)</TD></TR> </TABLE> <p>ztta/roll_first (*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1<br />ztta/roll_area (*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 500 000</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD><B>For AIX and 64-bit, refer also to Note 789477.</B></TD></TR> <TR><TD><B>For AIX and 32-bit, refer also to Note 95454.</B></TD></TR> <TR><TD></TD></TR> </TABLE> <p>abap/heaplimit (*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20 000 000<br />ztta/roll_extension(*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4,000,000,000 (64-bit)</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>can be reduced</TD></TR> <TR><TD><B>For AIX:</B> Refer to Notes 95454 and 124555</TD></TR> </TABLE> <p>abap/heap_area_dia(*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4,000,000,000 (64-bit)<br />abap/heap_area_nondia(*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;8,000,000,000 (64-bit)<br />abap/heap_area_total(*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;16,000,000,000 (64-bit)<br />em/blocksize_KB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 096 (64-bit)<br />rdisp/ROLL_SHM (*)  32,000<br />rdisp/ROLL_MAXFS (*)  32 000<br />rdisp/PG_SHM (*)  32,000<br />rdisp/PG_MAXFS (*)  32 000<br />rdisp/PG_LOCAL (*)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br />ztta/short_area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6,400,000<br />ES/TABLE (*)</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD><B>For AIX:</B>&#x00A0;&#x00A0;SHM_SEGS</TD></TR> <TR><TD><B>For UNIX:</B> UNIX_STD</TD></TR> </TABLE> <p>ES/SHM_SEG_SIZE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for AIX: &gt; 2 048<br />EM/TOTAL_SIZE_MB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for AIX: &gt; 16 348<br />ES/SHM_PROC_SEG_COUNT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4</p><div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD><B>SAP Kernel 6.40:</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;8</TD></TR> </TABLE> <p>ES/SHM_MAX_PRIV_SEGS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= ES/SHM_PROC_SEG_COUNT-1<br />ztta/max_memreq_MB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 048 maximum<br />abap/shared_objects_size_MB <B>64-bit:</B> 500<br /> <B>32-bit:</B> Default (= 20 MB) (1)<br /><B><U>Note the following:</U></B><br />1.) <B>Abap/shared_objects_size_MB:</B> Due to the limited address space, you have to select a lower value <B>for 32-bit systems</B>, so that, at the same time, there is still enough free conventional memory space that can be addressed. Increase the parameter in small increments, if the termination TSV_TNEW_PAGE_ALLOC_FAILED should occur (see Note 741864).<br />2.) <B>The following applies to 64-bit systems:</B><br />A.) em/initial_size must be greater than (total of all user contexts)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;+ em/global_area_MB + abap/shared_objects_size_MB<br />B.) For AIX: ES/SHM_SEG_SIZE must be greater than<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;em/global_area_MB + abap/shared_objects_size_MB<br />C.) For Linux and Windows: em_address_space_MB must be greater than<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ztta/roll_extension + em/global_area_MB +<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;abap/shared_objects_size_MB</p> <b>1.3 Client Server Communication</b><br /> <p>rdisp/wp_ca_blk_no&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 000<br />rdisp/appc_ca_blk_no&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 500 up to 2 000<br />rsts/ccc/cachesize&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 000 000<br />gw/max_conn&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 000<br />rdisp/tm_max_no&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 000<br />rdisp/max_comm_entries&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 000<br />rdisp/bufreftime&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;120<br />gw/max_overflow_size 25 000 000 up to 100 000 000<br />gw/max_sys&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 000<br />rdisp/max_arq&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= rdisp/max_comm_entries<br />gw/cpic_timeout&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;60</p><b>1.4 Enqueue</b><br /> <p>enque/table_size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;at least 100000</p> <b>1.5 Work Processes</b><br /> <p>rdisp/wp_no_dia&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;greater than the total of all<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;non-dialog work processes<br />rdisp/wp_no_btc<br />For application servers: 3 * number of CPUs on this server<br />For database servers:&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 * number of CPUs on this server<br />rdisp/wp_no_vb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= (rdisp/wp_no_btc) / 5<br />rdisp/wp_no_spo&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />rdisp/wp_no_enq&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />rdisp/elem_per_queue&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100000<br />ztta/parameter_area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;16 000 Non-Unicode systems<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;32 000 Unicode systems<br />rdisp/max_wprun_time in Analytical Banking&#x00A0;&#x00A0;&#x00A0;&#x00A0; 14400<br />rdisp/max_wprun_time in Operational Banking&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1000<br />rdisp/btctime&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;60<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FS-AM (Account Management)"}, {"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Responsible                                                                                         ", "Value": "D043048"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028825)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "95454", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/95454"}, {"RefNumber": "941735", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP memory management system for 64-bit Linux systems", "RefUrl": "/notes/941735"}, {"RefNumber": "90631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/90631"}, {"RefNumber": "88416", "RefComponent": "BC-OP-NT", "RefTitle": "Zero administration memory management for the ABAP server", "RefUrl": "/notes/88416"}, {"RefNumber": "871985", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Integer Overflow if em/address_space_MB > 4092", "RefUrl": "/notes/871985"}, {"RefNumber": "841944", "RefComponent": "BC-CST-MM", "RefTitle": "EgInit: EsCreateShared() failed. info='EG_GLOBAL_AREA'...", "RefUrl": "/notes/841944"}, {"RefNumber": "835474", "RefComponent": "BC-CST-MM", "RefTitle": "More than 32 GB extended memory", "RefUrl": "/notes/835474"}, {"RefNumber": "808607", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Memory management in a PASE-based system", "RefUrl": "/notes/808607"}, {"RefNumber": "789477", "RefComponent": "BC-CST-MM", "RefTitle": "Large extended memory on AIX (64-bit) as of Kernel 6.20", "RefUrl": "/notes/789477"}, {"RefNumber": "788688", "RefComponent": "FS-BA", "RefTitle": "System parameters in Bank Analyzer and more settings", "RefUrl": "/notes/788688"}, {"RefNumber": "785365", "RefComponent": "BC-CST-MM", "RefTitle": "Extended Global Memory (EG) increased from 2 GB to 8 GB", "RefUrl": "/notes/785365"}, {"RefNumber": "778700", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer: Database parameter Tool-BW", "RefUrl": "/notes/778700"}, {"RefNumber": "750456", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/750456"}, {"RefNumber": "741864", "RefComponent": "FIN-FB-MDF", "RefTitle": "FinBasis: Termination TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/741864"}, {"RefNumber": "74141", "RefComponent": "BC-CST-DP", "RefTitle": "Resource Management for tRFC and aRFC", "RefUrl": "/notes/74141"}, {"RefNumber": "712664", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712664"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "702728", "RefComponent": "BC-DB-DBI", "RefTitle": "Profile parameters for export/import buffer instances", "RefUrl": "/notes/702728"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "649613", "RefComponent": "BC-SRV-ALV", "RefTitle": "Export/import buffer SWAPS", "RefUrl": "/notes/649613"}, {"RefNumber": "552289", "RefComponent": "BC-CST-EQ", "RefTitle": "FAQ: R/3 lock management", "RefUrl": "/notes/552289"}, {"RefNumber": "519059", "RefComponent": "BC-CCM-BTC", "RefTitle": "FAQ: Background processing system", "RefUrl": "/notes/519059"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "480710", "RefComponent": "BC-DB-DBI", "RefTitle": "Profile parameters for table buffers (for SAP Kernel Release 7.2x and below)", "RefUrl": "/notes/480710"}, {"RefNumber": "425207", "RefComponent": "BC-CST-MM", "RefTitle": "SAP memory management, current parameter ranges", "RefUrl": "/notes/425207"}, {"RefNumber": "39412", "RefComponent": "BC-CST", "RefTitle": "How many work processes should be configured?", "RefUrl": "/notes/39412"}, {"RefNumber": "38682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/38682"}, {"RefNumber": "386605", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Memory Management for Linux (32-bit)", "RefUrl": "/notes/386605"}, {"RefNumber": "384971", "RefComponent": "BC-CST-DP", "RefTitle": "System parameter for high interface load <740 (kernel)", "RefUrl": "/notes/384971"}, {"RefNumber": "373986", "RefComponent": "BC-DB-DBI", "RefTitle": "Overflow of the export/import buffer", "RefUrl": "/notes/373986"}, {"RefNumber": "34533", "RefComponent": "BC-ABA-SC", "RefTitle": "Error SET_PARAMETER_MEMORY_OVERFLOW", "RefUrl": "/notes/34533"}, {"RefNumber": "329021", "RefComponent": "BC-CST-MM", "RefTitle": "SPOOL_INTERNAL_ERROR, ERROR => EmIAllocMmResourceEg", "RefUrl": "/notes/329021"}, {"RefNumber": "192658", "RefComponent": "BC-CST-MM", "RefTitle": "Setting parameters for BW systems", "RefUrl": "/notes/192658"}, {"RefNumber": "186475", "RefComponent": "BC-CST-UP", "RefTitle": "All update tasks are waiting with 'stopped CPIC' reason", "RefUrl": "/notes/186475"}, {"RefNumber": "167229", "RefComponent": "BC-ABA-SC", "RefTitle": "Memory too small for screen load", "RefUrl": "/notes/167229"}, {"RefNumber": "14754", "RefComponent": "BC-DB-DBI", "RefTitle": "Buffer synchronization profile parameters", "RefUrl": "/notes/14754"}, {"RefNumber": "146289", "RefComponent": "SV-PERF", "RefTitle": "Parameter Recommendations for 64-Bit SAP Kernel", "RefUrl": "/notes/146289"}, {"RefNumber": "1322182", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory consumption of ABAP Shared Objects", "RefUrl": "/notes/1322182"}, {"RefNumber": "108799", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How many spool work processes for each instance?", "RefUrl": "/notes/108799"}, {"RefNumber": "1085874", "RefComponent": "FS-BA-PM-AFP", "RefTitle": "Performance in International Financial Reporting Standards", "RefUrl": "/notes/1085874"}, {"RefNumber": "1085873", "RefComponent": "FS-BA-PM-CR", "RefTitle": "Performance in BaselII Capital Accord", "RefUrl": "/notes/1085873"}, {"RefNumber": "103747", "RefComponent": "SV-PERF", "RefTitle": "Performance: Parameter recommendations as of Release 4.0", "RefUrl": "/notes/103747"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "88416", "RefComponent": "BC-OP-NT", "RefTitle": "Zero administration memory management for the ABAP server", "RefUrl": "/notes/88416 "}, {"RefNumber": "425207", "RefComponent": "BC-CST-MM", "RefTitle": "SAP memory management, current parameter ranges", "RefUrl": "/notes/425207 "}, {"RefNumber": "480710", "RefComponent": "BC-DB-DBI", "RefTitle": "Profile parameters for table buffers (for SAP Kernel Release 7.2x and below)", "RefUrl": "/notes/480710 "}, {"RefNumber": "103747", "RefComponent": "SV-PERF", "RefTitle": "Performance: Parameter recommendations as of Release 4.0", "RefUrl": "/notes/103747 "}, {"RefNumber": "808607", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Memory management in a PASE-based system", "RefUrl": "/notes/808607 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1322182", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory consumption of ABAP Shared Objects", "RefUrl": "/notes/1322182 "}, {"RefNumber": "146289", "RefComponent": "SV-PERF", "RefTitle": "Parameter Recommendations for 64-Bit SAP Kernel", "RefUrl": "/notes/146289 "}, {"RefNumber": "788688", "RefComponent": "FS-BA", "RefTitle": "System parameters in Bank Analyzer and more settings", "RefUrl": "/notes/788688 "}, {"RefNumber": "741864", "RefComponent": "FIN-FB-MDF", "RefTitle": "FinBasis: Termination TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/741864 "}, {"RefNumber": "167229", "RefComponent": "BC-ABA-SC", "RefTitle": "Memory too small for screen load", "RefUrl": "/notes/167229 "}, {"RefNumber": "329021", "RefComponent": "BC-CST-MM", "RefTitle": "SPOOL_INTERNAL_ERROR, ERROR => EmIAllocMmResourceEg", "RefUrl": "/notes/329021 "}, {"RefNumber": "1085873", "RefComponent": "FS-BA-PM-CR", "RefTitle": "Performance in BaselII Capital Accord", "RefUrl": "/notes/1085873 "}, {"RefNumber": "1085874", "RefComponent": "FS-BA-PM-AFP", "RefTitle": "Performance in International Financial Reporting Standards", "RefUrl": "/notes/1085874 "}, {"RefNumber": "789477", "RefComponent": "BC-CST-MM", "RefTitle": "Large extended memory on AIX (64-bit) as of Kernel 6.20", "RefUrl": "/notes/789477 "}, {"RefNumber": "835474", "RefComponent": "BC-CST-MM", "RefTitle": "More than 32 GB extended memory", "RefUrl": "/notes/835474 "}, {"RefNumber": "386605", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Memory Management for Linux (32-bit)", "RefUrl": "/notes/386605 "}, {"RefNumber": "941735", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP memory management system for 64-bit Linux systems", "RefUrl": "/notes/941735 "}, {"RefNumber": "39412", "RefComponent": "BC-CST", "RefTitle": "How many work processes should be configured?", "RefUrl": "/notes/39412 "}, {"RefNumber": "74141", "RefComponent": "BC-CST-DP", "RefTitle": "Resource Management for tRFC and aRFC", "RefUrl": "/notes/74141 "}, {"RefNumber": "384971", "RefComponent": "BC-CST-DP", "RefTitle": "System parameter for high interface load <740 (kernel)", "RefUrl": "/notes/384971 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "785365", "RefComponent": "BC-CST-MM", "RefTitle": "Extended Global Memory (EG) increased from 2 GB to 8 GB", "RefUrl": "/notes/785365 "}, {"RefNumber": "519059", "RefComponent": "BC-CCM-BTC", "RefTitle": "FAQ: Background processing system", "RefUrl": "/notes/519059 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "552289", "RefComponent": "BC-CST-EQ", "RefTitle": "FAQ: R/3 lock management", "RefUrl": "/notes/552289 "}, {"RefNumber": "186475", "RefComponent": "BC-CST-UP", "RefTitle": "All update tasks are waiting with 'stopped CPIC' reason", "RefUrl": "/notes/186475 "}, {"RefNumber": "192658", "RefComponent": "BC-CST-MM", "RefTitle": "Setting parameters for BW systems", "RefUrl": "/notes/192658 "}, {"RefNumber": "108799", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How many spool work processes for each instance?", "RefUrl": "/notes/108799 "}, {"RefNumber": "14754", "RefComponent": "BC-DB-DBI", "RefTitle": "Buffer synchronization profile parameters", "RefUrl": "/notes/14754 "}, {"RefNumber": "871985", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Integer Overflow if em/address_space_MB > 4092", "RefUrl": "/notes/871985 "}, {"RefNumber": "34533", "RefComponent": "BC-ABA-SC", "RefTitle": "Error SET_PARAMETER_MEMORY_OVERFLOW", "RefUrl": "/notes/34533 "}, {"RefNumber": "702728", "RefComponent": "BC-DB-DBI", "RefTitle": "Profile parameters for export/import buffer instances", "RefUrl": "/notes/702728 "}, {"RefNumber": "778700", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer: Database parameter Tool-BW", "RefUrl": "/notes/778700 "}, {"RefNumber": "841944", "RefComponent": "BC-CST-MM", "RefTitle": "EgInit: EsCreateShared() failed. info='EG_GLOBAL_AREA'...", "RefUrl": "/notes/841944 "}, {"RefNumber": "649613", "RefComponent": "BC-SRV-ALV", "RefTitle": "Export/import buffer SWAPS", "RefUrl": "/notes/649613 "}, {"RefNumber": "373986", "RefComponent": "BC-DB-DBI", "RefTitle": "Overflow of the export/import buffer", "RefUrl": "/notes/373986 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FSAPPL", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "FSAPPL", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "FSAPPL", "From": "300", "To": "300", "Subsequent": ""}, {"SoftwareComponent": "BANK-ALYZE", "From": "42", "To": "42", "Subsequent": ""}, {"SoftwareComponent": "BANK-ALYZE", "From": "50", "To": "50", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}