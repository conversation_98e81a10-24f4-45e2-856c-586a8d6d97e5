{"Request": {"Number": "864234", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 627, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004888562017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000864234?language=E&token=14B3BA53689FA63148070A28D666D534"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000864234", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000864234/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "864234"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.07.2005"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "864234 - Clinical Order: Enhancement and Documentation of BAdIs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><UL><LI>The documentation of Business Add-Ins (BAdIs) N1_CORDER_PREALLOC, N1_CORDER_CHECK and N1_CORDER_USER_COMM is inadequate.</LI></UL> <UL><LI>You cannot add new order items with BAdI N1_CORDER_PREALLOC.</LI></UL> <UL><LI>You cannot exit the clinical order's dialog with BAdI N1_CORDER_USER_COMM.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Transaction N1CORD</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error, insufficient documentation</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The insufficient BAdI documentation was completed.<br />BAdIs N1_CORDER_USER_COMM and N1_CORDER_PREALLOC have been enhanced with one or two parameters. The new parameters are taken into account in the relevant methods.<br /><br />Before you execute the source code correction you have to perform the following activities:<br /><br />Change the interface of class method CL_ISH_PRC_CORDER-&gt;USER_COMMAND_BADI as follows:</p> <UL><LI>Call transaction SE24.</LI></UL> <UL><LI>Enter the name of class CL_ISH_PRC_CORDER in the \"Object Type\" input field and select \"Change\".</LI></UL> <UL><LI>Position the cursor on method USER_COMMAND_BADI and select \"Parameter(s)\" (=pushbutton).</LI></UL> <UL><LI>Enter a new line before \"E_RC\":</LI></UL> <UL><LI>Enter the following values in the new row:</LI></UL> <UL><UL><LI>Parameter: E_EXIT</LI></UL></UL> <UL><UL><LI>Type: EXPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Do not select</LI></UL></UL> <UL><UL><LI>Typing method: TYPE</LI></UL></UL> <UL><UL><LI>Associated type: ISH_ON_OFF</LI></UL></UL> <UL><UL><LI>Description: Exit dialog</LI></UL></UL> <UL><LI>Save active.</LI></UL> <p><br />Change the interface of BAdI interface method IF_EX_N1_CORDER_PREALLOC-&gt;PREALLOC as follows:</p> <UL><LI>Call transaction SE18.</LI></UL> <UL><LI>Enter value N1_CORDER_PREALLOC as the \"Definition Name\" and select \"Change\".</LI></UL> <UL><LI>Select the \"Interface\" tab page.</LI></UL> <UL><LI>Double-click on interface name IF_EX_N1_CORDER_PREALLOC.</LI></UL> <UL><LI>Select method PREALLOC and then \"Parameter(s)\".</LI></UL> <UL><LI>Enter a new line before the parameter E_RC:</LI></UL> <UL><LI>Enter the following values in the new line:</LI></UL> <UL><UL><LI>Parameter: IR_PRC_CORDER</LI></UL></UL> <UL><UL><LI>Type: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Do not select</LI></UL></UL> <UL><UL><LI>Typing method: TYPE REF TO</LI></UL></UL> <UL><UL><LI>Associated type: CL_ISH_PRC_CORDER</LI></UL></UL> <UL><UL><LI>description: IS-H: Process for processing a clinical order</LI></UL></UL> <UL><LI>Save active.</LI></UL> <p></p> <UL><LI>Call transaction SE18 again.</LI></UL> <UL><LI>Enter value N1_CORDER_PREALLOC as the \"Definition Name\" and select \"Change\".</LI></UL> <UL><LI>In the menu bar select Utilities -&gt; Regeneration to ensure that the BAdI is generated correctly.</LI></UL> <p><br />Change the interface of BAdI interface method IF_EX_N1_CORDER_USER_COMM-&gt;BEFORE_UCOMM as follows:</p> <UL><LI>Call transaction SE18.</LI></UL> <UL><LI>Enter value N1_CORDER_USER_COMM as the \"Definition Name\" and select \"Change\".</LI></UL> <UL><LI>Select the \"Interface\" tab page.</LI></UL> <UL><LI>Double-click on interface name IF_EX_N1_CORDER_USER_COMM.</LI></UL> <UL><LI>Select method BEFORE_UCOMM and then \"Parameter(s)\".</LI></UL> <UL><LI>Enter a new line before parameter I_VCODE:</LI></UL> <UL><LI>Enter the following values in the new line:</LI></UL> <UL><UL><LI>Parameter: IR_PRC_CORDER</LI></UL></UL> <UL><UL><LI>Type: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Do not select</LI></UL></UL> <UL><UL><LI>Typing method: TYPE REF TO</LI></UL></UL> <UL><UL><LI>Associated type: CL_ISH_PRC_CORDER</LI></UL></UL> <UL><UL><LI>Description: IS-H: Process for processing a clinical order</LI></UL></UL> <UL><LI>Enter a new line before parameter E_RC:</LI></UL> <UL><LI>Enter the following values in the new row:</LI></UL> <UL><UL><LI>Parameter: E_EXIT</LI></UL></UL> <UL><UL><LI>Type: EXPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Do not select</LI></UL></UL> <UL><UL><LI>Typing method: TYPE</LI></UL></UL> <UL><UL><LI>Associated type: ISH_ON_OFF</LI></UL></UL> <UL><UL><LI>Description: ON: Exit dialog</LI></UL></UL> <UL><LI>Save active.</LI></UL> <p></p> <UL><LI>Call transaction SE18 again.</LI></UL> <UL><LI>Enter value N1_CORDER_USER_COMM as the \"Definition Name\" and select \"Change\".</LI></UL> <UL><LI>In the menu bar select Utilities -&gt; Regeneration to ensure the BAdI is generated correctly.</LI></UL> <p><br />To import the documentation and example implementations for the BAdIs N1_CORDER_PREALLOC, N1_CORDER_CHECK and N1_CORDER_USER_COMM on your system, unzip the attached file HW864234_472.zip for i.s.h.med/IS-H Version 4.72.<br /><br />To import the example implementation for BAdI N1_CORDER_PREALLOC on your system, unzip the attached file HW864234_472_2.zip for i.s.h.med/IS-H Version 4.72.<br /><br />You should note that you can only download the attached files using the Service Marketplace and not using OSS (see also notes 480180 and 13719 regarding the importing of attachments).<br />Import the unzipped orders into your system one after another.<br /><br />See source code correction.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5019872"}, {"Key": "Processor                                                                                           ", "Value": "C5019872"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000864234/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000864234/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW864234_472.zip", "FileSize": "28", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000371352005&iv_version=0005&iv_guid=84806B9ECDDA2D409A66787700E21107"}, {"FileName": "HW864234_472_2.zip", "FileSize": "12", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000371352005&iv_version=0005&iv_guid=D447DC2950FB5949AF6E7784B3763E4A"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF11", "URL": "/supportpackage/SAPKIPHF11"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 1, "URL": "/corrins/0000864234/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "860908 ", "URL": "/notes/860908 ", "Title": "Clin. Order: BAdI N1_CORDER_USER_COMM - Insufficient Docu", "Component": "XX-PART-ISHMED"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}