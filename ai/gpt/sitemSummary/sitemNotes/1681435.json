{"Request": {"Number": "1681435", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2248, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001681435?language=E&token=428FD2097E3E1E4C88800A4C538C79A9"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001681435", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001681435/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1681435"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade Info"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade add-on components"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Upgrade", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade add-on components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1681435 - Upgrade NW703 to NW731 (SAP SEM 736, ERECRUIT, LSOFE)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1681435&TargetLanguage=EN&Component=BC-UPG-ADDON&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1681435/D\" target=\"_blank\">/notes/1681435/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /><br /><STRONG>Note no longer valid as new process (CISI) is available to rewrite the product version.</STRONG><br /><STRONG>SAP Note 1326576 Scenario 5.</STRONG><br /><br /><br /><br />You have installed SAP EHP3 FOR SAP NETWEAVER 7.00 (NW703) with SAP BW and the ERP add-ons SEM-BW, FINBASIS, LSOFE or ERECRUIT. You want to use the SAP HANA database. After SAP Note 1600929, SAP HANA is supported for SAP BW on SAP EHP1 FOR SAP NETWEAVER 7.3 (NW731).<br /><br />You must perform an upgrade (rewriting of the product version) to EHP 1 for SAP NetWeaver 7.30 ABAP (called SAP NW 731 in the following) in an EHP3 for SAP NetWeaver 7.00 (referred to as SAP NW 703 in the following) system in which the add-on components SEM-BW 736 and FINBASIS 736 or LSOFE 606 or ERECRUIT 606 are installed.<br /><br />There is no add-on-specific guide. This SAP Note contains the add-on-specific additional information about the upgrade.<br /></p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Upgrade, add-on, SEM-BW 736, FINBASIS 736, LSOFE 606, ERECRUIT 606 SAP_BS_FND, WEBCUIF, NetWeaver 731, NetWeaver 703<br /></p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>The following initial situation is supported for the upgrade to EHP1 for SAP NetWeaver 730 with SEM-BW 736, FINBASIS 736, ERECRUIT 606, LSOFE 606 SAP_BS_FND 731, MDG_FND 731, and WEBCUIF 731:<br /><br />EHP3 FOR SAP Netweaver 700 with SEM-BW 736, FINBASIS 736, ERECRUIT 606, LSOFE 606, SAP_BS_FND 731, MDG_FND 731 and WEBCUIF 731.<br /></p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>This SAP Note is constantly updated. Obtain the current version of this SAP Note when you start the upgrade.<br /><br /><br /><br /><STRONG></STRONG><br /><br /><br /><br />Content<br /><br />1. Change History<br />2. Important general information about the upgrade<br />3. Check before the upgrade<br />4. Additions to the upgrade<br />5. Problems after implementation<br />6. Actions after the upgrade<br />7. Language support<br /><br /></p> <b>1. Change History</b><br /> <p><br /> <B><U>DATUM Chapter Short Description</U></B><br /> 12.07.13 General Enhancement of the description<br /> 05.09.12 General Description enhancement<br /> 06.03.12 General Added: FINBASIS<br /> 20.02.12 General Added: ERECRUIT and LSOFE<br /> 16.02.12 Initial Initial Release<br /><br /></p> <b>2. Important general information about the upgrade</b><br /> <p><br /><br /></p> <UL><LI>If you use a dual stack (ABAP + JAVA), you must first perform a DUAL-SPLIT and then upgrade both systems separately. Reason: A dual stack split is not technically supported on NW731.</LI></UL> <UL><UL><LI>For more information, see SAP Notes 1655335 and 1561421.</LI></UL></UL> <UL><LI>When you upgrade from EHP3 FOR SAP NETWEAVER 7.0 (NW703) to EHP1 FOR SAP NETWEAVER 7.3 (NW731), the calculation of the required software components is handled as part of the maintenance procedure in the Maintenance Optimizer (MOPZ).<br /><br /><B><B>IMPORTANT:</B></B> For SUM to be able to process the information, the system must have a NetWeaver Support Package level of n-1 so that the MOPZ can include the latest released Support Package stack. Otherwise, SUM will not start any action.<br /><br />The upgrade requires the file Stack.xml, which must be created using the MOPZ. Due to the component structure of SEM-BW, FINBASIS, LSOFE, and ERECRUIT, there are a few points to keep in mind, which are described below:<br /><br />1. Preparation of the Solution Manager System</LI></UL> <UL><UL><LI>Note implementation:<br />Implement the following SAP Notes in your Solution Manager system if you have not yet implemented the corrections contained in this SAP Note with the relevant Support Package of ST 400:<br />1485385</LI></UL></UL> <p>           2. Configuration of the product system in the Solution Manager system using SAP SEM as an example (transaction SMSY)</p> <UL><UL><LI>Specify the following product versions for your system:<br /> SAP ERP 6.0<br /> EHP6 FOR SAP ERP 6.0<br /> SAP EHP3 FOR SAP NETWEAVER 7.0</LI></UL></UL> <UL><UL><LI>Mark the following instances as relevant:<br /> SAP SEM (for SAP ERP 6.0)<br /> SAP SEM (for EHP6 FOR SAP ERP 6.0)<br /> Application Server ABAP (for SAP EHP3 FOR SAP NETWEAVER 7.0)<br /><br />For FINBASIS, ERECRUIT, or LSOFE, you must mark the following instances as relevant:<br /> - FINBASIS: SAP FSCM - FSCM Server (ABAP)<br /> - ERECRUIT: SAP E-Recruiting<br /> - LSOFE: SAP Learning Solution Front End ABAP</LI></UL></UL> <p>           3. Create the maintenance transaction (transaction SOLMAN_WORKCENTER)</p> <UL><UL><LI>In the &quot;Enter Basic Data&quot; step, &quot;SAP EHP3 FOR SAP NETWEAVER 7.0&quot; must be selected as the product version. In the &quot;Update Options&quot; step, you choose &quot;Upgrade&quot; for this.</LI></UL></UL> <UL><UL><LI>In the step &quot;Select Target&quot;, the target &quot; SAP EHP1 FOR SAP NETWEAVER 7.3&quot; and the corresponding support package stack (minimum SPS 02) must be selected.</LI></UL></UL> <UL><UL><LI>Execute the maintenance transaction to save the generated stack.xml and, if necessary, download the files in your download basket.</LI></UL></UL> <UL><LI>Required SAP and add-on release level before the upgrade:<br />NW 703 with<br />   SEM-BW 736, FINBASIS 736, ERECRUIT 606, LSOFE 606<br />   MDG_FND 731<br />   SAP_BS_FND 731<br />   WEBCUIF 731</LI></UL> <p></p> <b>3. Check before the upgrade</b><br /> <UL><LI>Notes that you should obtain before the upgrade:<br />Add-ons: Conditions 70228<br />Overview note 1531022<br />Support Package Note tbd</LI></UL> <UL><LI>R3trans, tp, kernel, SPAM<br />In the source release of your system, use the current R3trans, tp, and kernel patch and import the latest SPAM update.</LI></UL> <UL><LI>Provision of the required component Support Packages<br />You can download the corresponding data files from SAP Service Marketplace.</LI></UL> <UL><UL><LI>See the corresponding documentation for the SL toolset:<br />https://service.sap.com/sltoolset => SUM (Software Update Manager)</LI></UL></UL><p></p> <b>4. Additions to the upgrade</b><br /> <UL><LI>Enhancements to Prepare<br />Phase IS_SELECT<br />The add-on components mentioned above may only be displayed with the status &quot;KEEP VERSION&quot;.</LI></UL> <UL><LI>Phase BIND_PATCH<br />This displays which Support Packages were found in the directory &lt;DIR_EPS_ROOT>/in and can be included in the upgrade.<br /></LI></UL> <b>5. Problems after implementation</b><br /> <p></p> <UL><LI>No known issues</LI></UL> <p></p> <b>6. Actions after the upgrade</b><br /> <p></p> <UL><LI>No add-on-specific actions known</LI></UL> <p></p> <b>7. Language support</b><br /> <b></b><br /> <UL><LI>All languages of SAP ERP 6.0 are supported.</LI></UL> <UL><LI>If you have imported a new standard language into your system after the upgrade, you must manually ensure that the corresponding language-dependent part of the add-ons is imported. For more information, see SAP Note 195442.</LI></UL> <p></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PE-LSO-TM (Training Management)"}, {"Key": "Other Components", "Value": "FIN-SEM (Strategic Enterprise Management)"}, {"Key": "Other Components", "Value": "SV-SMG-MAI (Maint. Optimizer replaced by Maint. Planner: BC-UPG-MP)"}, {"Key": "Other Components", "Value": "PA-ER (E-Recruiting)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (D041903)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (D041903)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001681435/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1326576", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP NetWeaver systems that contain SAP ERP software components", "RefUrl": "/notes/1326576 "}, {"RefNumber": "1600929", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP BW powered by SAP HANA DB: Information", "RefUrl": "/notes/1600929 "}, {"RefNumber": "1602357", "RefComponent": "FIN-SEM-BCS", "RefTitle": "SEM-BCS Upgrade: Software Component Dependencies", "RefUrl": "/notes/1602357 "}, {"RefNumber": "1491922", "RefComponent": "FIN-SEM-BCS", "RefTitle": "Relationship Between SEM-BCS and BI Release", "RefUrl": "/notes/1491922 "}, {"RefNumber": "1655335", "RefComponent": "BC-INS-DSS", "RefTitle": "Use Cases for Splitting Dual-Stack Systems", "RefUrl": "/notes/1655335 "}, {"RefNumber": "186299", "RefComponent": "FIN-SEM", "RefTitle": "Overview: Notes on Add-On SEM-BW", "RefUrl": "/notes/186299 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "FINBASIS", "From": "736", "To": "736", "Subsequent": ""}, {"SoftwareComponent": "FINBASIS", "From": "746", "To": "746", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "LSOFE", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "ERECRUIT", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "ERECRUIT", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "736", "To": "736", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "746", "To": "746", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1681435&TargetLanguage=EN&Component=BC-UPG-ADDON&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1681435/D\" target=\"_blank\">/notes/1681435/D</a>."}}}}