{"Request": {"Number": "949737", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 379, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005586252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000949737?language=E&token=80F91AEA2D88CF2E7CA521F6AE597834"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000949737", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000949737/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "949737"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.10.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-ABA-SC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Dynpro and CUA engine"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Runtime Environment - ABAP Language Issues Only", "value": "BC-ABA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Dynpro and CUA engine", "value": "BC-ABA-SC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA-SC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "949737 - Generating screen & CUA loads in own LUW"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Generating screen and CUA loads causes a serialization of flows, because the corresponding database tables are locked. This occurs in background processing in particular, and especially in objects that are used by a lot of programs at the same time.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MAXDB, DB2, LOB, deadlock, D342L, D345T, D347T, EUDB</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Since screen and CUA loads are generated and updated upon request, the generated runtime objects cannot be immediately persisted on the database because they were generally generated in an application LUW. In the process, a COMMIT would also complete the application LUW. Therefore, they are persisted only during the next implicit or explicit COMMIT from the application.<br />Until this happens, the object for the runtime representation on the database is locked for editing.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As far as possible, the runtime objects are generated in a separate work process and therefore in a separate LUW. This is why a COMMIT can occur directly after the creation, and the locks are released on the database.<br />This reduces the chances of simultaneous write access to the tables.<br /><br />This new function was included in Release 710 of the SAP kernel.<br />For Release 700, this function is available as of patch level 061.<br />For Release 6xx, the function is available in the downward-compatible 640 kernel as of patch level 133.<br /><br />Follow-on error 1<br />As a result of patches 700 61 and 640 133, deadlocks occurred on table D342L if a new kernel came across GUI status objects of an old load version and had to generate these GUI status objects.<br /><br />As a workaround, you can set the following profile parameter:<br />dynpro/generate_in_separate_luw = N<br />&#x00A0;&#x00A0;The parameter takes effect after you restart the system.<br /><br />The error (deadlocks on D342L) is corrected by the following patches:<br />640 138<br />700&#x00A0;&#x00A0;68<br />eliminates this error.<br /><br />Follow-on error 2<br />In heterogeneous (little/big endian) system landscapes, there were alternate DELETES of the D342L entries.<br />The error is corrected with kernel patch<br />640 163<br />eliminates this error.<br /><br />Follow-on error 3<br />The following section is only relevant for customers who use the MAXDB database.<br />As a result of patches 700 61 and 640 133, shared locks occur on the table DYNPLOAD when you use the MAXDB database. This is the case if there is an object in an obsolete version, which means that a regeneration is required. The problem solution is delivered with MaxDB Version 7.6.01 Build 02. (Internal information: SQLDBC PTS 1142573) or with the precompiler versions 7.4.03 Build 46 (PTS: 1145475) or Precompiler 7.5.00 Build 41 (PTS: 1145490).<br /><br />As a workaround, you can set the following profile parameter:<br />dynpro/generate_in_separate_luw = N<br /><br /><br />Follow-on error 4<br />The form routine INVALIDATE_BADI_CUA from the function pool SAPLPA_PACKAGE_SERVICES results in deadlocks on the tables D342L, D345T, D346T or D347T. For more information, also see Note 920730 or 1013336.<br /><br />This error is corrected with the following kernel patches and the correction instructions from Note 920730 or 1013336.<br />640 176<br />700 103<br /><br />Implement the attached correction instructions to avoid deadlocks on the tables EUDB, D345T, D346T, and D342L. Deadlocks may occur on the tables if you use GUI statuses with old source versions, and if a source version adjustment is required.<br /><br />If you use DB2 as database, also see Note 835507.<br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-SDB (MaxDB)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D001957)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000949737/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949737/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "920730", "RefComponent": "BC-DWB-UTL", "RefTitle": "Performance Problems with Enterprise Add On Switches", "RefUrl": "/notes/920730"}, {"RefNumber": "835507", "RefComponent": "BC-DB-DB2", "RefTitle": "sqlcode +100 (not found) when reading LOBs with UR", "RefUrl": "/notes/835507"}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "821701", "RefComponent": "BC-ABA-SC", "RefTitle": "Dynamic function texts", "RefUrl": "/notes/821701"}, {"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle enqueues", "RefUrl": "/notes/745639"}, {"RefNumber": "1451812", "RefComponent": "BC-ABA-SC", "RefTitle": "GUI status generation in a separate LUW", "RefUrl": "/notes/1451812"}, {"RefNumber": "1431795", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Patch<PERSON>/Patchcollections for 11.2.0.1", "RefUrl": "/notes/1431795"}, {"RefNumber": "1426979", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: SecureFiles - The new way to store LOB data", "RefUrl": "/notes/1426979"}, {"RefNumber": "1244963", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Read lock of RS cursor causes wait situation", "RefUrl": "/notes/1244963"}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996"}, {"RefNumber": "1169255", "RefComponent": "LO-MD-BOM", "RefTitle": "Status MVA0 of the user interface SAPLCSDI missing", "RefUrl": "/notes/1169255"}, {"RefNumber": "1166242", "RefComponent": "BC-DB-ORA", "RefTitle": "10g: \"enq: HW contention\" waits for LOB Inserts in ASSM TBS", "RefUrl": "/notes/1166242"}, {"RefNumber": "1139857", "RefComponent": "BC-DB-SDB", "RefTitle": "Lock problems when accessing table REPOLOAD/REPOSRC", "RefUrl": "/notes/1139857"}, {"RefNumber": "1086678", "RefComponent": "BC-ABA-SC", "RefTitle": "D345T updates", "RefUrl": "/notes/1086678"}, {"RefNumber": "1081770", "RefComponent": "BC-ABA-SC", "RefTitle": "GUI status version", "RefUrl": "/notes/1081770"}, {"RefNumber": "1070232", "RefComponent": "BC-DB-SDB", "RefTitle": "Various errors when you use SHAREDSQL and precompiler", "RefUrl": "/notes/1070232"}, {"RefNumber": "1063532", "RefComponent": "BC-ABA-SC", "RefTitle": "Deadlock when accessing D345T", "RefUrl": "/notes/1063532"}, {"RefNumber": "1028360", "RefComponent": "BC-ABA-SC", "RefTitle": "Locks on DYNPLOAD during parallel execution of reports", "RefUrl": "/notes/1028360"}, {"RefNumber": "1013336", "RefComponent": "BC-DWB-UTL", "RefTitle": "Performance problems with Enterprise Add-On Switches", "RefUrl": "/notes/1013336"}, {"RefNumber": "1009801", "RefComponent": "BC-ABA-SC", "RefTitle": "Incorrect multibyte texts on screens", "RefUrl": "/notes/1009801"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle enqueues", "RefUrl": "/notes/745639 "}, {"RefNumber": "1244963", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Read lock of RS cursor causes wait situation", "RefUrl": "/notes/1244963 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "1431795", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Patch<PERSON>/Patchcollections for 11.2.0.1", "RefUrl": "/notes/1431795 "}, {"RefNumber": "1426979", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: SecureFiles - The new way to store LOB data", "RefUrl": "/notes/1426979 "}, {"RefNumber": "1166242", "RefComponent": "BC-DB-ORA", "RefTitle": "10g: \"enq: HW contention\" waits for LOB Inserts in ASSM TBS", "RefUrl": "/notes/1166242 "}, {"RefNumber": "1086678", "RefComponent": "BC-ABA-SC", "RefTitle": "D345T updates", "RefUrl": "/notes/1086678 "}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996 "}, {"RefNumber": "1451812", "RefComponent": "BC-ABA-SC", "RefTitle": "GUI status generation in a separate LUW", "RefUrl": "/notes/1451812 "}, {"RefNumber": "1081770", "RefComponent": "BC-ABA-SC", "RefTitle": "GUI status version", "RefUrl": "/notes/1081770 "}, {"RefNumber": "1438425", "RefComponent": "BC-ABA-SC", "RefTitle": "Locks on table DYNPLOAD", "RefUrl": "/notes/1438425 "}, {"RefNumber": "1063532", "RefComponent": "BC-ABA-SC", "RefTitle": "Deadlock when accessing D345T", "RefUrl": "/notes/1063532 "}, {"RefNumber": "1013336", "RefComponent": "BC-DWB-UTL", "RefTitle": "Performance problems with Enterprise Add-On Switches", "RefUrl": "/notes/1013336 "}, {"RefNumber": "920730", "RefComponent": "BC-DWB-UTL", "RefTitle": "Performance Problems with Enterprise Add On Switches", "RefUrl": "/notes/920730 "}, {"RefNumber": "821701", "RefComponent": "BC-ABA-SC", "RefTitle": "Dynamic function texts", "RefUrl": "/notes/821701 "}, {"RefNumber": "1169255", "RefComponent": "LO-MD-BOM", "RefTitle": "Status MVA0 of the user interface SAPLCSDI missing", "RefUrl": "/notes/1169255 "}, {"RefNumber": "1139857", "RefComponent": "BC-DB-SDB", "RefTitle": "Lock problems when accessing table REPOLOAD/REPOSRC", "RefUrl": "/notes/1139857 "}, {"RefNumber": "1070232", "RefComponent": "BC-DB-SDB", "RefTitle": "Various errors when you use SHAREDSQL and precompiler", "RefUrl": "/notes/1070232 "}, {"RefNumber": "835507", "RefComponent": "BC-DB-DB2", "RefTitle": "sqlcode +100 (not found) when reading LOBs with UR", "RefUrl": "/notes/835507 "}, {"RefNumber": "1028360", "RefComponent": "BC-ABA-SC", "RefTitle": "Locks on DYNPLOAD during parallel execution of reports", "RefUrl": "/notes/1028360 "}, {"RefNumber": "1009801", "RefComponent": "BC-ABA-SC", "RefTitle": "Incorrect multibyte texts on screens", "RefUrl": "/notes/1009801 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64022", "URL": "/supportpackage/SAPKB64022"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70014", "URL": "/supportpackage/SAPKB70014"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71004", "URL": "/supportpackage/SAPKB71004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0000949737/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "700", "Number": "844988 ", "URL": "/notes/844988 ", "Title": "CONNE_IMPORT_CONVERSION_ERROR in import", "Component": "BC-DWB-TOO-MEN"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "991897 ", "URL": "/notes/991897 ", "Title": "Terminatns CONNE_IMPORT_CONVERSION_ERROR/IMPORT_FORMAT_ERROR", "Component": "BC-DWB-TOO-MEN"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}