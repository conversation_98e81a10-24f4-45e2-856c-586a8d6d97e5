{"Request": {"Number": "2493348", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 537, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019588202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002493348?language=E&token=06E8C02B617EAFDEB66C28DB93F2FCB2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002493348", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002493348/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2493348"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2493348 - SAP S/4HANA 1709: Release Information Note for Finance"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using or implementing&#160;SAP S/4HANA 1709. This SAP Note describes information that is&#160;especially relevant if you upgrade from an SAP ERP EHP release or an SAP S/4 HANA Finance release. Note: Additional information is also available in other Release Information Notes for Fiori Applications and for special business topics (these are partly referenced here as well).</p>\r\n<p>This SAP Note also provies restriction information at the time of release to customer (RTC) for <strong>SAP S/4HANA OP 1709</strong>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SFIN, on-premise, OP, S/4HANA on-premise</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>See below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Restrictions Relevant for&#160;Finance in SAP S/4HANA 1709</strong></p>\r\n<p><strong>General</strong></p>\r\n<p style=\"padding-left: 30px;\">For the following topics, see these SAP Notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Topic</strong></td>\r\n<td><strong>SAP Note</strong></td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">BW data sources that are no longer supported</span></td>\r\n<td>2270133</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Parallel valuation for activity based costing</span></td>\r\n<td>2270414</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Reference and simulation costing</span></td>\r\n<td>2349294</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Summarization hierarchies in Controlling</span></td>\r\n<td>2349282</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Replaced transaction codes and programs</span></td>\r\n<td>2270335</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Closing Cockpit with S/4HANA</span></td>\r\n<td>2332547</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Availability of general cost objects and cost object hierarchies</span></td>\r\n<td>2270411</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN-US\" style=\"font-size: 13.5pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Handling of historical data (cold store) in SAP Fiori apps</span></td>\r\n<td>\r\n<ul>\r\n<li>2058320</li>\r\n<li>2452160</li>\r\n<li>2452198</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>EC-CS</strong></p>\r\n<p style=\"padding-left: 30px;\">The consolidation solution EC-CS can be used for external (statutory) rendering of accounts as well as internal (management) reporting [1]. For the consolidation solution EC-CS, you can no longer define a custom characteristic using the MATNR data element or domain (or any derived value). If you are already using a custom characteristic based on the MATNR data element or domain, you cannot upgrade to SAP S/4HANA. Appropriate transition pre-checks are in place to give you the opportunity to check such a situation already&#160;at start release.</p>\r\n<p><strong>CO</strong></p>\r\n<ul>\r\n<li>The <em>Production Cost Analysis</em> (F1780) app supports a maximum of 16 cost component groups in the <em>Cost Component Group</em> overview screen, and cost component assignment for &#8220;Cost Comp. Grp 1&#8221; only. The app supports Legal Valuation only. Extensibility is not supported.</li>\r\n<li>The Work Center View of the <em>Production Cost Analysis</em> (F1780) app includes production orders and process orders only. This view doesn&#8217;t support overhead costs for production order at work center/operation level.</li>\r\n<li>In Event-Based Revenue Recognition, only project-based sales processes (professional services scenario) are supported. The Revenue Recognition app only supports single-level projects.</li>\r\n<li>In Event-Based Revenue Recognition for Projects/ Professional Services, target revenue calculations are not supported.</li>\r\n<li>In Professional Services, origin profit centers are not derived.</li>\r\n<li>The&#160;<em>Manage Internal Orders</em> app only supports&#160;one asset under construction (AUC) per investment order. You can navigate to the AUC after release and save this investment order. The app does not support multiple&#160;AUCs for one investment order.&#160;</li>\r\n</ul>\r\n<p><strong>Finance Fiori Apps that are not supported in&#160;SAP S/4HANA&#160;1709</strong></p>\r\n<p>The following SAP Fiori Apps are included in&#160;<strong>SP00 of UIAPFI70 500&#160;</strong>but cannot be used with Finance in&#160;SAP S/4HANA 1709.&#160;<br />Instead use the corresponding alternative Fiori application as listed below:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td class=\"xl65\" height=\"38\" width=\"172\"><strong>Fiori ID</strong></td>\r\n<td class=\"xl65\" width=\"483\"><strong>Fiori App Name</strong></td>\r\n<td class=\"xl65\" width=\"158\"><strong>App Type</strong></td>\r\n<td class=\"xl65\" width=\"551\"><strong>Corresponding Fiori App ID&#160;</strong><br /><strong>for Finance&#160;in SAP S/4HANA 1709</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1620</td>\r\n<td class=\"xl66\" width=\"483\">Activity Type&#160;</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1717</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1622</td>\r\n<td class=\"xl66\" width=\"483\">Controlling Document&#160;</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1720</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1623</td>\r\n<td class=\"xl66\" width=\"483\">Cost Center</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1721</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1624</td>\r\n<td class=\"xl66\" width=\"483\">Cost Center Group&#160;</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1722</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1626</td>\r\n<td class=\"xl66\" width=\"483\">Fixed Asset&#160;</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1684</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1627</td>\r\n<td class=\"xl66\" width=\"483\">G/L Account&#160;</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F0731A</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1628</td>\r\n<td class=\"xl66\" width=\"483\">Journal Entry</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F0717</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1631</td>\r\n<td class=\"xl66\" width=\"483\">Internal Order&#160;</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1729</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1632</td>\r\n<td class=\"xl66\" width=\"483\">Profit Center&#160;</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1730</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F1634</td>\r\n<td class=\"xl66\" width=\"483\">Statistical Key Figure</td>\r\n<td class=\"xl66\" width=\"158\">Fact Sheet</td>\r\n<td class=\"xl66\" width=\"551\">F1731</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0694</td>\r\n<td class=\"xl66\" width=\"483\">Aging Analysis</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1733</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0514</td>\r\n<td class=\"xl66\" width=\"483\">Bank Statement Monitor</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1734</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0685</td>\r\n<td class=\"xl66\" width=\"483\">Cash Discount Forecast</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1735</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0686</td>\r\n<td class=\"xl66\" width=\"483\">Cash Discount Utilization</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1736</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0747</td>\r\n<td class=\"xl66\" width=\"483\">Days Payable Outstanding Analysis</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1740</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0594</td>\r\n<td class=\"xl66\" width=\"483\">Future Payables</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1743</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0748</td>\r\n<td class=\"xl66\" width=\"483\">Invoice Processing Time</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1745</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0687</td>\r\n<td class=\"xl66\" width=\"483\">Overdue Payables</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1746</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0597</td>\r\n<td class=\"xl66\" width=\"483\">Overdue Receivables</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1747</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0855</td>\r\n<td class=\"xl66\" width=\"483\">Vendor Payment Analysis (Manual and Automatic Payments)</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1749 (Supplier Payment Analysis (Manual and Automatic Payments))</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0772</td>\r\n<td class=\"xl66\" width=\"483\">Vendor Payment Analysis (Open Payments)</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F1750 (Supplier Payment Analysis (Open Payments))</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0515</td>\r\n<td class=\"xl66\" width=\"483\">Bank Risk</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F0515A</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0517</td>\r\n<td class=\"xl66\" width=\"483\">Deficit Cash Pool</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F0517A</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0512</td>\r\n<td class=\"xl66\" width=\"483\">Liquidity Forecast</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">F0512A</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0513</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Actual Cash Flow / Cash Flow Analysis</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0513A</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0731</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Manage G/L Account Master</td>\r\n<td class=\"xl66\" width=\"158\">Transactional</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0731A</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0763</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Display Chart of Accounts</td>\r\n<td class=\"xl66\" width=\"158\">Transactional</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0763A</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0958</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">P&amp;L - Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0945</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0959</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Profit Centers - Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0944</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0960</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Market Segments - Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0943</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0961</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Projects - Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0942</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0963</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Cost Centers - Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0940</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F1582A</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Sales Orders - Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1582</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F1583A</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Functional Areas - Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1583</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F1710</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">P&amp;L Plan / Actuals</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F0927</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0603</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Days Sales Outstanding</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1741</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0604</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Future Receivables</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1744</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0605</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Days Beyond Terms</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1739</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0607</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Dunning Level Distribution</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1742</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0746</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Total Receivables</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1748</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0606</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Open Disputes</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1752</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0608</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Credit Limit Utilization</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1751</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0609</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">Promises to Pay</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1753</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">\r\n<p>F0745</p>\r\n</td>\r\n<td class=\"xl66\" width=\"483\">\r\n<p>Collection Progress</p>\r\n</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n<td class=\"xl66\" width=\"551\">\r\n<p>F1738</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following SAP Fiori Apps are included in&#160;<strong>SP00 of UIAPFI70 500 </strong>but cannot be used with&#160;Finance in&#160;SAP S/4HANA&#160;1709:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\" width=\"172\"><strong>Fiori ID</strong></td>\r\n<td class=\"xl65\" width=\"483\"><strong>Fiori App Name</strong></td>\r\n<td class=\"xl65\" width=\"158\"><strong>App Type</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0839</td>\r\n<td class=\"xl66\" width=\"483\">Margin Analysis</td>\r\n<td class=\"xl66\" width=\"158\">Analytical</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0828&#160;&#160;</td>\r\n<td class=\"xl66\" width=\"483\">Gross-to-Net Sales</td>\r\n<td class=\"xl66\" width=\"158\">CFO KPI</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0826</td>\r\n<td class=\"xl66\" width=\"483\">Revenue</td>\r\n<td class=\"xl66\" width=\"158\">CFO KPI</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0825</td>\r\n<td class=\"xl66\" width=\"483\">Net Sales</td>\r\n<td class=\"xl66\" width=\"158\">CFO KPI</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0824&#160;</td>\r\n<td class=\"xl66\" width=\"483\">Gross Margin</td>\r\n<td class=\"xl66\" width=\"158\">CFO KPI</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"172\">F0823&#160;</td>\r\n<td class=\"xl66\" width=\"483\">Net Margin</td>\r\n<td class=\"xl66\" width=\"158\">CFO KPI</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;For <em>Net Margin Results</em> (F0194) and <em>Profit Analysis&#160;</em>(F0684), please see the information in SAP Note&#160;2127080.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024637)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024637)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002493348/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2491467", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Restriction Note", "RefUrl": "/notes/2491467"}, {"RefNumber": "2455383", "RefComponent": "FI-FIO-AR", "RefTitle": "Release Information: Changes in SAP Fiori Content for Finance in SAP S/4HANA 1709", "RefUrl": "/notes/2455383"}, {"RefNumber": "2452198", "RefComponent": "FI-FIO-GL", "RefTitle": "Release Information Note: Display G/L Account Line Items - Posting View", "RefUrl": "/notes/2452198"}, {"RefNumber": "2452160", "RefComponent": "FI-FIO-GL", "RefTitle": "Release Information Note: Display G/L Account Line Items - Reporting View", "RefUrl": "/notes/2452160"}, {"RefNumber": "2058320", "RefComponent": "FI-FIO-GL", "RefTitle": "Release Information Note: Display G/L Account Balances", "RefUrl": "/notes/2058320"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2581947", "RefComponent": "CO-PC-OBJ-EBR", "RefTitle": "Event-Based Revenue Recognition in SAP S/4HANA: Supported Integration Scenarios", "RefUrl": "/notes/2581947 "}, {"RefNumber": "2560450", "RefComponent": "CO-FIO", "RefTitle": "My Projects app no longer displayed by default on the SAP Fiori Launchpad", "RefUrl": "/notes/2560450 "}, {"RefNumber": "2482453", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Release Information Note", "RefUrl": "/notes/2482453 "}, {"RefNumber": "2491467", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Restriction Note", "RefUrl": "/notes/2491467 "}, {"RefNumber": "2471772", "RefComponent": "FI-FIO-GL", "RefTitle": "Release Information Note: Account Determination for S/4HANA On-Premise edition 1709", "RefUrl": "/notes/2471772 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}