{"Request": {"Number": "755977", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 345, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015725892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000755977?language=E&token=A96BA147304BC38974B4727107E6DDAC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000755977", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000755977/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "755977"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.08.2021"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "755977 - ST12 \"ABAP Trace for SAP EarlyWatch/GoingLive\""}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Documentation for transaction ST12</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ST12 \"Single transaction analysis\" for SAP EarlyWatch/GoingLive, Addon ST-A/PI, ABAP trace, context trace</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Transaction ST12 \"Single transaction analysis\" for SAP EarlyWatch/GoingLive<br /><br />Note that the ST12 ABAP trace transaction is not officially documented and only available in English language. It is mainly intended for use by SAP or certified Service consultants during SAP Service Sessions (for example SAP GoingLive Check or Solution Management Optimization Services).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Contents</strong></p>\r\n<p>I.&#160;&#160;&#160;&#160; Introduction<br />II.&#160;&#160;&#160;&#160;Basics of ABAP trace (for beginners)<br />III.&#160;&#160; ST12 in comparison to SE30 (delta course)<br />IV.&#160;&#160;&#160;&#160;How to use ST12<br />V.&#160;&#160;&#160;&#160; Trouble-shooting<br />VI.&#160; &#160; Other blogs&amp;SAP notes reg. ST12<br /><br /></p>\r\n<p><strong>I. Introduction</strong></p>\r\n<ol><ol>1. Motivation</ol></ol>\r\n<p>ST12 was developed to promote the usage of ABAP trace, to integrate ABAP and performance traces (SQL Enqueue RFC, transaction ST05) and to make the tracing and analysis process faster and more convenient. ABAP trace with ST12 is the central entry point for performance analysis. It should be used to detect top-down any performance hotspot, for functional time distribution analysis, and to optimize ABAP/CPU bound issues. SQL trace should be used for DB bound issues.<br /><br />ST12 is similar to a combination of the standard ABAP and SQL trace<br />transactions SE30 and ST05.</p>\r\n<ol><ol>2. Overview</ol></ol>\r\n<p>ST12 combines ABAP and performance (SQL) trace into one transaction, with major functional enhancements especially for the ABAP trace part. In a joint switch on/off with the performance trace, ST12 allows to activate the ABAP trace <strong>for another user</strong>. Like this an SAP Service Engineer can trace a dialog transaction that is executed by a business user of the customer and does not need own sample data. ABAP and performance traces can be activated on another server or even <strong>on all servers</strong> to catch e.g. incoming RFCs.<br />ST12 makes it easy to keep valuable trace results and pass them on e.g. to SAP backoffice. The ABAP trace results are completely <strong> collected to database</strong>.&#160;&#160;For Performance trace ST12 remembers timeframe&amp;server, and one click navigates directly into the ST05 trace display on the proper server. Selected results form performance trace and other findings can be saved as annotation texts into a trace analysis.<br />The ST12 <strong>ABAP Trace Summary</strong> quickly shows the contribution of known expensive functionalities. It is also able to estimate the time contribution of certain programs, esp. user exits and customer coding.<br />With ST12 the <strong>program hierarchies</strong> can be analyzed in the aggregated ABAP trace 'per call'. Therefore the non-aggregated ABAP trace with its large trace file sizes is not needed and was omitted from ST12.<br /><br />ST12 allows to switch on/off and display ABAP traces like SE30 but</p>\r\n<ul>\r\n<li>without the non-aggregated ABAP trace</li>\r\n</ul>\r\n<ul>\r\n<li>with new possibilities to activate the trace</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>for a username &amp; task type</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>on all servers to catch e.g. an incoming RFC</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>trace BSP pages or many incoming RFCs</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>with new evaluation possibilities for the aggregated trace</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>top-down call tree</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>buttom-up call hierarchy</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>last changed by &amp; on</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>short texts for functional analysis</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>traces are stored centrally and permanently to DB</li>\r\n</ul>\r\n<ul>\r\n<li>with better support for 'Context trace' accross RFCs</li>\r\n</ul>\r\n<ol><ol>3. Availibility</ol></ol>\r\n<p>Transaction ST12 is available as of basis release 4.6B. It is delivered via the addon ST-A/PI (Application servicetools for EarlyWatch/GoingLive, see note 69455). The ST-A/PI version should be 01F* or higher.<br />The feature to switch on the ABAP trace for another user requires<br />-&gt; on basis 4.6*: Addon ST-A/PI &gt;= 01F*, Kernel 46D patchlevel &gt;= 1805<br />-&gt; on basis 6.x: Addon ST-A/PI &gt;= 01G*, Kernel 640 patchlevel &gt;= 83<br />-&gt; on basis 7.0 or higher: Addon ST-A/PI 01G*</p>\r\n<ol>4. Outlook: \"Single transaction analysis\"</ol>\r\n<p>It is planned for the future to include SQL/performance trace handling, statistical records and the SQLR transaction functionality into ST12.<br /><br /></p>\r\n<p><strong>II. Basics of ABAP trace (for beginners)</strong></p>\r\n<ol>1. General</ol>\r\n<p>ABAP traces measures two different things. The first are certain simple and possibly expensive ABAP statements like database accesses and statements on internal tables. These are easy to understand. The second are calls to modularization units like perform, call function/method, call screen or PAI PBO modules. These are complex because they are hierarchical containers and resemble nested russian puppets. Their hierarchies can branch and also merge again.</p>\r\n<ol>2. Gross time vs. Net time</ol>\r\n<p>For modularization unit calls, there exists a difference between gross and net time. Gross time is the summarized time over all call executions. Net time is the gross time<br />&#160;&#160;minus the time when this mod.unit calls other mod.units<br />&#160;&#160;minus the durations of simple statements that occur within this modularization unit AND that are explicitely measured.<br />The addition 'explicitely measured' implies that net time can depend on the measurement scope. E.g. with trace scope 'with internal tables' the net times of mod.units can be lower because durations of statements on internal tables are also deducted. Like this the total sum of net time percentages always remains 100%.<br />Gross times are used to get a top-down overview. Mod.unit names often give a good indication what happens below them, so that their gross time can be attributed to a certain functionality. Sorting by net times shows single expensive statements or mod.units that themselves consume much execution time.</p>\r\n<ol>3. Aggregation levels</ol>\r\n<p>The non-aggregated ABAP trace, which is not offered in ST12, would contain one line per statement/call execution.<br />'Per calling position' is the default in ST12. It aggregates the trace per sourcecode position of a statement or mod.unit call. A coding<br />&#160;&#160;line 150&#160;&#160;PERFORM A.<br />&#160;&#160;line 151&#160;&#160;DO 100 TIMES.<br />&#160;&#160;line 152&#160;&#160;&#160;&#160;PERFORM A.<br />&#160;&#160;line 153&#160;&#160;ENDDO.<br />would lead to two 'PERFORM A' lines in the trace with 1 and 100<br />executions and aggregated execution time. This aggregation still allows to analyze hierarchy relations.<br />'Full' aggregation is per statement and program. It is comparable to the statement summary for the SQL trace in transaction ST05.<br />For the aggregation 'Per modularization unit' see next chapter.</p>\r\n<ol>4. ABAP trace options</ol>\r\n<p>The flag 'with internal tables' extends the trace scope to statements on internal tables like read table, loop at or sort.<br />The flag 'Particular units' can be set in the 'Current mode' scenario in order to trace only one dialog step of a transaction. When the transaction is started from ST12, the ABAP trace does not yet start. It is activated before and deactivated after the dialog step using 'System-&gt;Utilities-&gt;Runtime analysis-&gt;Switch on/off'.<br />The 'Filter for program part' restricts the ABAP trace to the processing inside and below one specific modularization unit.</p>\r\n<ol>5. Comparison to the SQL trace with transaction ST05</ol>\r\n<p>SQL trace is written without aggregation. ST05 traces every action of a user on a server, ABAP trace only one user context or transaction. SQL trace needs to be switched off, ABAP trace ends with the traced transaction. ST05 writes trace files into the local filesystem and overwrites them circularily, ST12 stores its analyses centrally and permanently to database. SQL trace gives a buttom-up glimpse what the transaction is doing and is suitable for DB bound performance issues, ABAP trace provides a top-down overview and can detect any performance hotspot. For the issues detected with SQL trace often an easy technical tuning is possible, whereas ABAP issues often involve customer messages and coding changes.<br /><br /></p>\r\n<p><strong>III. ST12 in comparison to SE30 (delta course)</strong></p>\r\n<ol><ol>1. Simplifications in ABAP trace options</ol></ol>\r\n<p>The non-aggregated ABAP trace is not offered in ST12. One reason is that for most business transactions it grows too large, another that the new hierarchy analysis features in ST12 make it largely superfluous.<br />The start options are much simpler than in SE30. Per default one has aggregation 'per calling position' and a trace scope like in the DEFAULT variant in SE30, i.e. tracing modularization units and DB operations. For the flags 'With internal tables' and 'Particular units' see chapter above. Restriction to one modularization unit is possible. Some more rarely used options are available as a popup.</p>\r\n<ol>2. Trace start possibilities</ol>\r\n<p>Three scenarios are offered:</p>\r\n<ul>\r\n<li>The <strong>'User' scenario</strong> allows to activate the ABAP trace for the next action under a certain user name and tasktype (DIA BTC RFC UPD) on any application server or systemwide.<br />In slight difference to the SQL trace it does not trace everything under the username, but the trace is switched on for only one user context that does the next roll-in and that has the proper user &amp;tasktype. The trace then lasts until this user context/transaction is finished.<br />Choose <strong>'&lt; All Servers &gt;'</strong> in the server field in order to trace the next such action systemwide. The trace request is then distributed to all servers. On each refresh, ST12 checks whether a trace has started to run on any server. If yes, then '&lt; All Servers &gt;' is replaced by the explicit server name and trace requests on other servers are cleared. This enables e.g. to catch an incoming RFC or a batch job that starts on any server.<br />Prerequisite for the whole 'User' scenario is a kernel patch. See 'Availibility' above.</li>\r\n</ul>\r\n<ul>\r\n<li>The <strong>'Tasks&amp;HTTP'</strong> scenario is available as of SAP basis 6.10. It alloes to specify a max. number of ABAP trace activations=ABAP trace files, and is therfore suitable to trace many incoming RFCs or BSP pages, where every screen element makes an own call to the R/3 system.</li>\r\n</ul>\r\n<ul>\r\n<li>'Workprocess', like 'Parallel mode' of SE30, is used to trace parts of longrunning processing, esp. batch jobs, from an SM50 like process list.</li>\r\n</ul>\r\n<ul>\r\n<li>In the 'Current mode' scenario the transaction to be analyzed has to be started from ST12, so sample data are required like in SE30.</li>\r\n</ul>\r\n<ol><ol>3. Trace collection and administration of saved traces</ol></ol>\r\n<p>ST12 'collects' the ABAP trace from the local filesystem and stores it to database as a 'trace analysis'. This makes the trace centrally and permanently available, which is a great advantage when passing on the trace to other levels for further analysis. In the 'User' or 'Workprocess' scenario the asynchronous trace collection is triggered by pressing the 'End &amp; collect trace' button. Convenient search features are included.</p>\r\n<ol><ol>4. Evaluation</ol></ol>\r\n<p>Evaluate -&gt; ABAP Trace shows the aggregated hitlist. New features in comparison to SE30 are:<br /><br />&#160;&#160;a) Toggle between three aggregation levels<br />The entry display shows the trace 'Per calling position'. Using the first two buttons or the menu, one can switch to other aggregations. The second level is 'Full'.<br />The new aggregation <strong>'Per modularization unit'</strong> is a kind of mixture of both. On an upper level it shows only one line for every modularization unit. When such a line is expanded, the statements and calls inside this mod.unit are shown in aggregation 'per calling position'. Statements and calls outside of mod.units are grouped below dummy lines '&lt;program&gt; [outside of mod.units]'. The net times of simple statements are added to the net time of their upper level mod.unit. This aggregation is suitable to detect localized issues in single mod.units.<br /><br />&#160;&#160;b) Top down call tree and Buttom-up call hierarchy<br />Use the aggregation 'Per calling position' to analyze call hierarchy relations:<br />-&gt; '<strong>Buttom-up call hierarchy</strong>' works like a multi-level where-used search. The hierarchy above an entry is displayed in form of a swimming lane diagram. The empty diamond symbols show where a call is issued, the filled up/down triangles where it arrives. The small arrows between them are pure cosmetics, illustrating the call direction. The exact meaning is: \"Out of modularization unit &lt;empty diamond&gt; a call to modularization unit &lt;filled up/down triangle&gt; is issued.'.<br />-&gt; <strong>'Top down time split hierarchy'</strong> shows the &#126;30 most important below an entry in a swimming lane diagram. Importance is measured in terms of how much aggregated time flow they receive from the original entry. The subordinate modularization units are grouped into distinct branches, it is shown how they are linked to the original entry and where the time flow splits up (several emtpy diamonds in the line). This is very helpful for time distribution and for time-lost analysis.<br />-&gt; For the '<strong>Top down call tree</strong>', put the cursor on a modularization unit call (perform or call method/function/screen) and press the tree button. The hierarchy below is then displayed in a new column. All calls to this mod.unit are labeled '0', including the one where you put your cursor. '1' are statements inside this mod.unit, '2' the statements in mod.units one level below, and then iteratively down up to 30 levels. Letters are used to designate lower levels.<br /><br />In both cases the trace lines in the hierarchy are marked with color. The 'Only call tree/call hierarchy filter' button sets an ALV list filter so that only trace entries in the hierarchy are displayed. 'Off' buttons make the hierarchy columns disappear.<br />Remarks:<br />The hierarchy only considers calls to forms, methods, functions and call screen to PBO PAI modules. It does not go across submits or ABAP events.<br />The second restriction reflects that technically only the hierarchy relations one level up/down are known for sure. An example to illustrate: Assume an ABAP program has form routines A1 and A2 that both call a form B with a different input parameter. When called by A1, B calls a form C1. When called by A2, B calls a form C2. Now when you put the cursor on 'Perform A1', the top-down call tree will contain not only B and C1 but also C2, which is called by B but in reality not when B was called by A1. Likewise when you put the cursor on a 'Perform C1', a button-up call hierarchy will contain forms B and A1 but also A2.<br />It can also happen that although form B is called by form A1, B still appears higher in the list sorted by gross time because B is also called by A2.<br /> &#160;&#160;c) last changed by &amp; on<br />'Show/hide-&gt;Last changed by' retrieves 'Last changed by' usernames and change dates from the ABAP respository. For simple statements it displays the change info of the surrounding sourcecode include. For mod.unit calls the info relates to the target include that contains the called mod.unit. This makes it easy to detect any user exit or customer modification. Even customer claims that something would go slower since a certain date can be verified here.<br /><br />&#160;&#160;d) short texts for functional analysis<br />'Show/hide-&gt;Short texts' from the menu retrieves titles of functions, methods, function groups, reports, classes, dynpros or tables from the ABAP repository and displays them. Together with the techical names of forms functions etc. they provide the basis for a functional time distribution analysis. If English titles are not available, German or others are displayed.<br /><br />&#160;&#160;e) internal table names resolved, select statements compressed<br />Internal table names are shown as in the sourcecode. Open+Fetch+Close are aggregated to one line 'Select' etc.<br /><br />&#160;&#160;f) Other features<br />The header area in the evaluation screen can be collapsed. The 'Show/hide' menu allows to display the 2nd part of long call texts or the calling program and provides a convenient way to organize the columns. A 'Top500 calls filter' provides a faster trace display. Sourcecode display and the usual ALV features like sorting, filtering and sums are available like in SE30.</p>\r\n<ol><ol>5. 'Context trace' across RFCs and remote trace collection</ol></ol>\r\n<p>ABAP trace can be inherited via RFC, so that remote activities also get traced. As precondition, both origin and remote system need to have a basis release as of 6.10. The parameter rstr/accept_remote_trace has to be set to 'true' in the remote system. On basis releases 6.10 and 6.20 this parameter has to be changed in the profile maintenance RZ10, and the application server needs to be restarted to make it effective. From basis 6.40 it can be switched dynamically to 'true' using transaction RZ11. Note that it should not be set to 'true' permanently, since this might cause unwanted trace inheritance e.g. by certain external interfaces. Especially the permanent change in RZ10 should be changed back to 'false' after tracing is finished.<br />Start the trace from ST12 with the flag 'Context trace' on. When the original transaction makes RFCs, these RFCs then write own ABAP tracefiles into their server filesystems. To collect them, press 'Collect external traces' in the first line of ST12. Enter an RFC destination to the remote system. Enter a timeframe, or clear it to find all files. Then press 'Start' to search for suitable tracefiles on all servers of the remote system. Select the proper file and press 'Collect'. The tracefile is then fetched remotely and stored as a new trace analysis.</p>\r\n<ol><ol>6. ABAP trace summary</ol></ol>\r\n<p>It is planned to provide a summary for the ABAP trace as the SQLR does for the SQL trace, showing the percentages of logistics functions like pricing or availability check with the same function texts as in SQLR.<br /><br /></p>\r\n<p><strong>IV. When and how to use ST12 ABAP trace<br /></strong></p>\r\n<p>Transaction optimization in EarlyWatch/GoingLive used to rely heavily on the SQL trace. ABAP trace was recommended only to analyze gaps in the SQL trace or pure CPU issues.<br />This is no longer valid already since SAP release 4.6B. Especially for for detection and analysis of performance issues, ABAP trace is far more suitable than SQL trace or SQLR. ABAP trace with ST12 can and should be used to</p>\r\n<ul>\r\n<li>identify top-down any performance hotspot and get an exact functional time distribution</li>\r\n</ul>\r\n<ul>\r\n<li>find customer modifications and user exits</li>\r\n</ul>\r\n<ul>\r\n<li>detect issues in the call hierarchy</li>\r\n</ul>\r\n<ul>\r\n<li>search for localized technical tuning potential, e.g. CPU-expensive ABAP statements</li>\r\n</ul>\r\n<ol><ol>1. Top-down gross time analysis</ol></ol>\r\n<p>Sort by gross time. Concentrate on modularization units that take long enough to be worth for optimization (min. 5 % gross time) but are small enough to correspond to just one specific functionality. Use the top-down time split hierarchy or buttom-up call hierarchy buttons to find out which of these interesting entries are hierarchically related, in order to group them into distinct functional branches.<br />Then look at the form/method/function names and use 'Show/hide-&gt;Short texts' to get an idea what these distinct functional branches are doing. E.g. in sales order entry VA01 everything below function PRICING is pricing, function AVAILIBILITY_CHECK does the ATP check, function RV_TEXT_COPY is text detemination etc. Their gross percentages give you exactly the functional time distribution.<br />Now ask yourself whether one of them looks too expensive or strange. 50% gross time for function PRICING ? 95% for a form DYNAMIC_CREDIT_CHECK ? 91% for the DDIC function module DDIF_TABL GET in a PS transaction ?<br />Those are optimization candidates. On the other hand if time is well split over only the expected functions and nothing pops out, then there is no optimization potential.</p>\r\n<ol><ol>2. Userexit and modification check</ol></ol>\r\n<p>'Show/Hide-&gt;Last changed by' shows the change info for the (target) sourcecode include. Look for 'Last changed by user' &lt;&gt; 'SAP' and gross time &gt; 5%. If you find an entry, jump into the coding and make sure that the customer changes are in the relevant parts of the code. Also verify that it was not a manual implementation of a SAP note. Userexits and custom code are responsible for their whole gross time. Optimization is usually done by the customer.</p>\r\n<ol><ol>3. Net times analysis</ol></ol>\r\n<p>Sort by net time to search for localized technical tuning potential. The aggregation 'per modularization unit' can show forms/functions/methods that consume a long net time. A trace with scope 'with internal tables' can reveal single expensive ABAP statements like e.g. a slow 'read table' statement on a large internal table without 'binary search'. The recommendation would be to keep the table sorted and add the 'binary search' option. The ABAP trace provides also a convenient aggregated view on accesses to database or buffered tables and on RFCs, except frontend RFCs.</p>\r\n<ol><ol>4. Optimization possibilities</ol></ol>\r\n<p>In case you find high times on standard functionality like function PRICING, perform a functional optimization using ST14 and the GoingLive Optimization session checks.<br />You can search for performance-relevant SAP notes using names of modularization units in the hierarchy of the conspicuous functionalities that you noticed during your top-down gross time analysis.<br />Also consult the functional experts. If they also judge it strange that so much time is spent on such a functionality, open a customer message. E.g. in the case with 95% for DYNAMIC_CREDIT_CHECK, this led to a quick reply from SAP development just to take out one flag in customizing.<br />If you have ABAP knowledge, look at the numbers of execution and<br />jump into ABAP at different hierarchy levels. Coding parts that are processed very often should be reviewed. Check e.g. whether it would be better to centralize certain processing steps and do them once on a higher level. Another important strategy is to remember results in a buffer. In ABAP a buffer in the user contaxt is usually implemented using global internal tables in the top include of function groups. They are persistent throughout the user context and can be accessed from all function modules of the group. The logic is: a) Check if buffer filled. b) If yes, return results from buffer. c) If no, select or calculate results and store to buffer.<br />Optimization often involves code changes.<br /><br /></p>\r\n<p><strong>V. Trouble-shooting</strong></p>\r\n<p>Symptom:&#160;&#160;Negative or excessively long times in the ABAP trace ?<br />This can occur on certain operating systems with multiprocessors. Push the 'Further options' button in the ABAP trace options and select 'Low resolution'. Then repeat the trace.<br /><br />Symptom:&#160;&#160;Too long runtimes for the first few entries ?<br />Sometimes the first entries have incorrect gross/net times, even longer than the total execution time. Often these entries are screen modules (PAI PBO). However the gross times for the other entries below like forms, methods or functions are usually still correct and reliable. Just ignore these leading entries.<br /><br />Symptom:&#160;&#160;Tracefile overflow ?<br />Some rare programming techniques can cause additional trace file flushes and thus lead to an overflow even with the aggregated trace. Push the 'Further options' button in the ABAP trace options and increase the ABAP trace file size from 2 to max 50 MB.</p>\r\n<p>Symptom: Error message \"Record mismatch. ON STACK\"<br />Error messages like<br />ATRA error: SAPLFIPI 473: Record mismatch. ON STACK ID=M, SUBID=S,<br />come from SAP kernel. They indicate that program hierarchies were unexpectedly complex. For some commands or calls, the closing trace entries were not processed as expected. Therefore SAP kernel stopped the ABAP trace recording. The beginning of the processing may still be traced, and it still maybe valuable to evaluate if the interruption did not happen too early, compared to the total runtime.<br />A fix for such issues can only be provided via SAP kernel update. If you are already on latest kernel, and&#160;you want to have the error analyzed in more detail, then:<br />Take a NON-aggregated trace of the transaction/program using transaction SE30.(Tcode SE30 -&gt; \"Old SE30\").<br />If the same error occurs:<br />Display the ABAP trace file in transaction SE30.<br />When you are in the first screen \"Runtime analysis evaluation: Overview\", choose from the menu:&#160;Utilities-&gt;Dump (shrunk).<br />This should display a filtered low-level trace display that contains opening ABAP trace commands that did not have corresponding closing events. Open a customer incident on&#160;component&#160;BC-ABA-LA, and provide this overview into the message.&#160;ABAP trace support BC-ABA-LA can then try to provide a kernel patch to solve that kind of program hierarchy issue.</p>\r\n<p><strong>V. Other blogs&amp;SAP notes reg. ST12</strong></p>\r\n<p>Blogs:<br /><a target=\"_blank\" class=\"external-link\" href=\"http://scn.sap.com/community/abap/testing-and-troubleshooting/blog/2009/09/08/single-transaction-analysis-st12-getting-started\" rel=\"nofollow\">The Current Mode Trace<br /></a><a target=\"_blank\" class=\"external-link\" href=\"http://scn.sap.com/community/abap/testing-and-troubleshooting/blog/2009/09/22/st12--the-workprocess-trace\" rel=\"nofollow\">The Workprocess Trace</a><a target=\"_blank\" class=\"external-link\" href=\"http://scn.sap.com/community/abap/testing-and-troubleshooting/blog/2009/09/08/single-transaction-analysis-st12-getting-started\" rel=\"nofollow\"><br /></a><a target=\"_blank\" class=\"external-link\" href=\"http://scn.sap.com/community/abap/testing-and-troubleshooting/blog/2010/03/22/st12-tracing-user-requests-tasks-http\" rel=\"nofollow\">The Task and HTTP Trace</a><a target=\"_blank\" class=\"external-link\" href=\"http://scn.sap.com/community/abap/testing-and-troubleshooting/blog/2009/09/08/single-transaction-analysis-st12-getting-started\" rel=\"nofollow\"><br /></a><a target=\"_blank\" class=\"external-link\" href=\"http://scn.sap.com/community/abap/testing-and-troubleshooting/blog/2011/12/12/st12--schedule-traces\" rel=\"nofollow\">Schedule a trace<br /></a><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/ABAP/Single+Transaction+Analysis\">ST12 Wiki</a><a target=\"_blank\" class=\"external-link\" href=\"http://scn.sap.com/community/abap/testing-and-troubleshooting/blog/2009/09/08/single-transaction-analysis-st12-getting-started\" rel=\"nofollow\"><br /></a></p>\r\n<p>SAP notes:<br /><a target=\"_blank\" href=\"/notes/2436955\">2436955 - How to collect and analyze traces using ST12 (single transaction analysis)</a><br /><a target=\"_blank\" href=\"/notes/2535415\">2535415 - ST12 tracing pitfalls - no traces collected</a><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CCM-MON-TUN (Workload Monitoring Tool)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027971)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D027971)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000755977/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000755977/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1597364", "RefComponent": "BW-BCT-GEN", "RefTitle": "FAQ: BW-BCT: Extraction performance in source system", "RefUrl": "/notes/1597364"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3397173", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "ST12 Trace was not collected with an error ATRA: Size limit exceeded", "RefUrl": "/notes/3397173 "}, {"RefNumber": "2977673", "RefComponent": "BC-DB-ORA", "RefTitle": "ST12 trace size is not sufficient", "RefUrl": "/notes/2977673 "}, {"RefNumber": "2231445", "RefComponent": "PPM-PRO", "RefTitle": "How to create the perfect case for PPM component and subcomponents", "RefUrl": "/notes/2231445 "}, {"RefNumber": "2699939", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Emergency Suitcase", "RefUrl": "/notes/2699939 "}, {"RefNumber": "2693614", "RefComponent": "SV-PERF", "RefTitle": "Transaction ST12 does not exist", "RefUrl": "/notes/2693614 "}, {"RefNumber": "2399769", "RefComponent": "SV-PERF", "RefTitle": "General Performance: Troubleshooting Questions", "RefUrl": "/notes/2399769 "}, {"RefNumber": "2436955", "RefComponent": "SV-PERF", "RefTitle": "How to collect and analyze traces using ST12 (single transaction analysis)", "RefUrl": "/notes/2436955 "}, {"RefNumber": "2552308", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Poor performance of transaction code DB12", "RefUrl": "/notes/2552308 "}, {"RefNumber": "2299435", "RefComponent": "SCM-APO-SPP", "RefTitle": "How to create the perfect incident for Service Parts Planning (SCM-APO-SPP)", "RefUrl": "/notes/2299435 "}, {"RefNumber": "2540971", "RefComponent": "SRM-EBP-TEC-PFM", "RefTitle": "How to run a ST12 Performance Trace", "RefUrl": "/notes/2540971 "}, {"RefNumber": "2469206", "RefComponent": "BC-BW", "RefTitle": "How to collect a trace analysis for delta/full extraction in the source system", "RefUrl": "/notes/2469206 "}, {"RefNumber": "2529867", "RefComponent": "SD-BF-AC", "RefTitle": "How to create perfect case for R/3 ATP issues", "RefUrl": "/notes/2529867 "}, {"RefNumber": "2151502", "RefComponent": "SCM-ICH-MD", "RefTitle": "How to create the perfect case for SCM-ICH component and subcomponents", "RefUrl": "/notes/2151502 "}, {"RefNumber": "2475741", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "How to create the perfect case for SCM-APO-INT-SLS component and subcomponents", "RefUrl": "/notes/2475741 "}, {"RefNumber": "2475074", "RefComponent": "SCM-APO-ATP", "RefTitle": "How to create the perfect case for SCM-APO-ATP component and subcomponents", "RefUrl": "/notes/2475074 "}, {"RefNumber": "2456218", "RefComponent": "SV-PERF", "RefTitle": "Performance issue definition and determination", "RefUrl": "/notes/2456218 "}, {"RefNumber": "2418936", "RefComponent": "SV-PERF", "RefTitle": "High RFC time: Performance troubleshooting", "RefUrl": "/notes/2418936 "}, {"RefNumber": "1758890", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA: Information needed by Product/Development Support", "RefUrl": "/notes/1758890 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "1597364", "RefComponent": "BW-BCT-GEN", "RefTitle": "FAQ: BW-BCT: Extraction performance in source system", "RefUrl": "/notes/1597364 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}