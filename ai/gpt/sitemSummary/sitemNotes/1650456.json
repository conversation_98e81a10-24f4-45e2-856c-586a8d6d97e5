{"Request": {"Number": "1650456", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 319, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017339602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001650456?language=E&token=191004C3E282A6FFBC500B31FC760F8A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001650456", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001650456/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1650456"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 45}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1650456 - GBTRINT 100 - Installation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>For information about planning the installation of the ABAP-add-on for interface components on application back-end system, see SAP Note 1841471.</li>\r\n<li>In the following sections you find some specific information about the installation of the ABAP-add-on GBTRINT for SAP Global Batch Traceability (SAP GBT) and SAP Logistics Business Network, material traceability option.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAINT, add-on, GBTRINT 100, SAPK-100AGINGBTRINT, Logistics Business Network, material traceability</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\"></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Content</p>\r\n<ol>\r\n<li>Prerequisites for installing GBTRINT 100</li>\r\n<li>Preparing the installation</li>\r\n<li>Known errors</li>\r\n<li>After the installation</li>\r\n<li>Language support</li>\r\n<li>Uninstalling the GBTRINT 100 add-on</li>\r\n</ol>\r\n<p><strong>1. Prerequisites for installing GBTRINT 100</strong></p>\r\n<ul>\r\n<li>Component GBTRINT 100 can be installed only on SAP ERP systems and SAP S/4HANA.</li>\r\n<li>Before installing GBTRINT 100 you have to implement SAP Note 1593452 as a prerequisite on the SAP ERP system where you are installing the add-on.</li>\r\n<li>The installation of GBTRINT 100 component does not modify existing objects, customizing&#160;or other data in SAP ERP components.</li>\r\n<li>Required release<br />Installation of GBTRINT 100 components requires following components and Support Packages:</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"padding-left: 60px;\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Release</strong></td>\r\n<td><strong>Support Package</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>2023</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>2022</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>2021</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>2020</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>1909</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>1809</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>1709</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>1610</td>\r\n<td>Initial Shipment Stack</td>\r\n</tr>\r\n<tr>\r\n<td>SAP S/4HANA</td>\r\n<td>1511</td>\r\n<td>Feature Package Stack 02</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>608</td>\r\n<td>Support Package 01 or higher</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>607</td>\r\n<td>Support Package 01 or higher</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>606</td>\r\n<td>Support Package 01 or higher</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>605</td>\r\n<td>Support Package 01 or higher</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>604</td>\r\n<td>Support Package 04 or higher</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>603</td>\r\n<td>Support Package 05 or higher</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>602</td>\r\n<td>Support Package 06 0r higher</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>600</td>\r\n<td>Support Package 15 or higher</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li>If you have not yet installed the required support packages, you can include them in the installation of add-ons.</li>\r\n</ul>\r\n<ul>\r\n<li>Required support packages for integration to SAP applications:</li>\r\n<ul>\r\n<li>Minimum required support package for GBTRINT component for integration with <strong>SAP Logistics Business Network, material traceability option</strong> is SP25. <br />For a detailed overview about the supported functionality refer to SAP note&#160;<a target=\"_blank\" class=\"external-link\" href=\"/notes/2996976\" rel=\"nofollow noopener\" title=\"Follow link\">2996976</a></li>\r\n<li>Required support packages for the integration with <strong>SAP Global Batch Traceability:</strong><br />Integration of data from SAP ECC or SAP S/4HANA to SAP GBT using GBTRINT component works only if the support packages on both sides are compatible.<br />The following table provides the information&#160;of compatible support packages of GBTR and GBTRINT:&#160;</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"padding-left: 60px;\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Installed Support Package of GBTRINT 100</strong></td>\r\n<td><strong>Required Support Package for GBTR 100</strong></td>\r\n<td><strong><strong>Required Support Package for </strong>GBTR 200</strong></td>\r\n<td><strong><strong>Required Support Package for GBTR 300</strong></strong></td>\r\n</tr>\r\n<tr>\r\n<td>SP29 &amp; SP30 &amp; SP31</td>\r\n<td>Out of maintenance</td>\r\n<td>Out of maintenance</td>\r\n<td>SP08</td>\r\n</tr>\r\n<tr>\r\n<td>SP27 &amp; SP28</td>\r\n<td>SP13</td>\r\n<td>SP15</td>\r\n<td>SP07</td>\r\n</tr>\r\n<tr>\r\n<td>SP24 &amp; SP25 &amp; SP26</td>\r\n<td>SP13</td>\r\n<td>SP15</td>\r\n<td>\r\n<p>SP06</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SP23</td>\r\n<td>SP13</td>\r\n<td>SP14</td>\r\n<td>\r\n<p>SP05</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SP23</td>\r\n<td>SP13</td>\r\n<td>SP14</td>\r\n<td>\r\n<p>SP04</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SP22</td>\r\n<td>SP13</td>\r\n<td>SP13</td>\r\n<td>\r\n<p>SP03</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SP21</td>\r\n<td>SP13</td>\r\n<td>SP13</td>\r\n<td>\r\n<p>SP02</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SP20</td>\r\n<td>SP13</td>\r\n<td>SP12</td>\r\n<td>\r\n<p>SP01</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SP19</td>\r\n<td>SP13</td>\r\n<td>SP12</td>\r\n<td>\r\n<p>SP00</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SP18</td>\r\n<td>SP13</td>\r\n<td>SP10</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP17</td>\r\n<td>SP13</td>\r\n<td>SP09</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP16</td>\r\n<td>SP13</td>\r\n<td>SP08</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP15</td>\r\n<td>SP13</td>\r\n<td>SP07</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP14</td>\r\n<td>SP13</td>\r\n<td>SP06</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP13</td>\r\n<td>SP12</td>\r\n<td>SP05</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP12</td>\r\n<td>SP12</td>\r\n<td>SP04</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP11</td>\r\n<td>SP11</td>\r\n<td>SP03</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP10</td>\r\n<td>SP10</td>\r\n<td>SP02</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP09</td>\r\n<td>SP09</td>\r\n<td>SP01</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP08</td>\r\n<td>SP08</td>\r\n<td>SP00</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP07</td>\r\n<td>SP07</td>\r\n<td>GBTR 200 not supported</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP06</td>\r\n<td>SP06</td>\r\n<td>GBTR 200 not supported</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n<tr>\r\n<td>SP05</td>\r\n<td>SP05</td>\r\n<td>GBTR 200 not supported</td>\r\n<td>GBTR 300 not supported</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"longtext MonoSpace\">&#160;</div>\r\n<ul>\r\n<li>Implement SAP Note 2166008 to be able to&#160;connect SAP GBT to&#160;different support package levels&#160;of integration component GBTRINT. Then, the compatibility of the support packages on both sides is no longer necessary from a technical point of view. Nevertheless,&#160;a consistent representation of data in SAP GBT is&#160;only ensured if you&#160;use one of the combinations of support package levels listed in the table above. This SAP Note must only be implemented in the SAP GBT system. You have nothing to change in the system of the integration component.<br /><br /><strong>Note<br /></strong>Check the kernel version before implementing SAP Note 216608.</li>\r\n</ul>\r\n<p><br /><strong>2. Preparing the installation</strong></p>\r\n<p>For more information about planning the installation of the ABAP-add-on for interface components on application back-end system, see SAP Note 1841471.</p>\r\n<p>&#160;</p>\r\n<p>&#160;<strong>3. Known errors</strong></p>\r\n<p>ABAP / Syntax Generation errors</p>\r\n<ul>\r\n<li>Program /GBTINT/CL_EVENT_ACCESS=======CP, Include /GBTINT/CL_EVENT_ACCESS=======CU: Syntax error in line 000034<br />Type 'TT_CHVW_INC_PUR' is unknown<br />Program /GBTINT/CL_IM_EX_HU_SAVE======CP, Include /GBTINT/CL_EVENT_ACCESS=======CU: Syntax error in line 000034<br />Type 'TT_CHVW_INC_PUR' is unknownProgram /GBTINT/CL_IM_EX_MB_DOC=======CP,<br />Include /GBTINT/CL_EVENT_ACCESS=======CU: Syntax error in line 000034<br />Type 'TT_CHVW_INC_PUR' is unknown<br />Program /GBTINT/CL_TC_UPDATE_BWUL=====CP, Include /GBTINT/CL_TC_UPDATE_BWUL=====CU: Syntax error in line 000011<br />Type 'IF_EX_VB_UPDATE_BWUL' is unknown<br />Program /GBTINT/SAPLRFC_ACCESS, Include /GBTINT/CL_EVENT_ACCESS=======CU: Syntax error in line 000034<br />Type 'TT_CHVW_INC_PUR' is unknown<br />Program /GBTINT/R_INITIAL_LOAD: Syntax error in line 000014<br />Type 'TT_CHVW_INC_PUR' is unknown<br /><br /><strong>Solution:</strong> implementation of SAP Note <a target=\"_blank\" href=\"/notes/1593452\">1593452</a></li>\r\n</ul>\r\n<ul>\r\n<li>\r\n<p>Program /GBTINT/CL_ADR_ACCESS=======CP, Include /GBTINT/CL_ADR_ACCESS=======CM003: Syntax error in line 000111<br />Type 'CL_ABAP_DYN_PRG' is unknown<br />Program /GBTINT/CL_LOCATION_ACCESS=======CP, Include /GBTINT/CL_LOCATION_ACCESS=======CM003: Syntax error in line 000044<br />Type 'CL_ABAP_DYN_PRG' is unknown<br /><br /><strong>Solution:</strong> implementation of SAP Note <a target=\"_blank\" href=\"/notes/1487337\">1487337</a> - Downporting the class CL_ABAP_DYN_PRG</p>\r\n</li>\r\n</ul>\r\n<p><br /><strong>4. After the installation</strong></p>\r\n<p>SAP Notes for Software Component GBTRINT:</p>\r\n<p>For an overview about important SAP Notes for software components GBTR, GBTRINT, or other software components, see release information note 2019466 for SAP GBT 2.0, and&#160;release information note&#160;2566869&#160;for SAP GBT 3.0.</p>\r\n<p>Further information for SAP Logistics Business Network, material traceability option can be found on SAP help pages at <a target=\"_blank\" href=\"https://help.sap.com/mt\">https://help.sap.com/mt</a></p>\r\n<p><strong>5. Language support</strong></p>\r\n<p>GBTRINT 100 is delivered in following languages: German, English, French, Japanese, Russian, Portuguese, Chinese, Spanish. The languages are contained in the add-on package, and you do not need to import any additional language packages.</p>\r\n<p>&#160;</p>\r\n<p><strong>6. Uninstalling the GBTRINT 100 add-on</strong></p>\r\n<p>You can use the add-on installation tool to uninstall add-ons. Deletion of some add-ons is only possible if certain prerequisites apply, however. Make sure that you have read the uninstalling SAP Note for add-on GBTRINT 2149065 - <em>Uninstalling Add-on GBTRINT,&#160;</em>which describes the prerequisites and process in detail.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I349230"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027408)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001650456/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001650456/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2996976", "RefComponent": "SBN-LBN-MT", "RefTitle": "SAP Business Network Material Traceability: Integration to SAP S/4HANA and SAP ERP", "RefUrl": "/notes/2996976"}, {"RefNumber": "2566869", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Global Batch Traceability 3.0: RIN & Restrictions", "RefUrl": "/notes/2566869"}, {"RefNumber": "2019466", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Global Batch Traceability 2.0: RIN", "RefUrl": "/notes/2019466"}, {"RefNumber": "1841471", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ABAP add-ons for interface components", "RefUrl": "/notes/1841471"}, {"RefNumber": "1672747", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Global Batch Traceability 1.0: RIN", "RefUrl": "/notes/1672747"}, {"RefNumber": "1640901", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on GBTR", "RefUrl": "/notes/1640901"}, {"RefNumber": "1640900", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of the abap add-on GBTR 100", "RefUrl": "/notes/1640900"}, {"RefNumber": "1634215", "RefComponent": "CA-MDG-APP-BP", "RefTitle": "MDGS: Short Dump in IDoc Data Mapper BAdI implementation", "RefUrl": "/notes/1634215"}, {"RefNumber": "1593452", "RefComponent": "LO-BM-WUL", "RefTitle": "Enable enhancement of where-used list update process", "RefUrl": "/notes/1593452"}, {"RefNumber": "1532379", "RefComponent": "CA-MDG-APP-SUP", "RefTitle": "MDG: Corrections of the Data Distribution (SAP_APPL)", "RefUrl": "/notes/1532379"}, {"RefNumber": "1487337", "RefComponent": "BC-ABA-LA", "RefTitle": "Downporting the class CL_ABAP_DYN_PRG", "RefUrl": "/notes/1487337"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3385622", "RefComponent": "CA-GBT", "RefTitle": "\"Product ID cannot be changed\" error while posting /GBT/MATMAS Idoc", "RefUrl": "/notes/3385622 "}, {"RefNumber": "3386916", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2023: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/3386916 "}, {"RefNumber": "3106645", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2021: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/3106645 "}, {"RefNumber": "2996976", "RefComponent": "SBN-LBN-MT", "RefTitle": "SAP Business Network Material Traceability: Integration to SAP S/4HANA and SAP ERP", "RefUrl": "/notes/2996976 "}, {"RefNumber": "2974130", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2974130 "}, {"RefNumber": "2816584", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2816584 "}, {"RefNumber": "2657067", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2657067 "}, {"RefNumber": "2571649", "RefComponent": "CA-GBT", "RefTitle": "SAP Global Batch Traceability 3.0: Install. the SAP Library", "RefUrl": "/notes/2571649 "}, {"RefNumber": "2566869", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Global Batch Traceability 3.0: RIN & Restrictions", "RefUrl": "/notes/2566869 "}, {"RefNumber": "2238445", "RefComponent": "SCM", "RefTitle": "Integration of Supply Chain Management Applications to SAP S/4HANA", "RefUrl": "/notes/2238445 "}, {"RefNumber": "2019466", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Global Batch Traceability 2.0: RIN", "RefUrl": "/notes/2019466 "}, {"RefNumber": "2149065", "RefComponent": "LO-BM-GBT", "RefTitle": "Uninstalling Add-On GBTRINT", "RefUrl": "/notes/2149065 "}, {"RefNumber": "1672747", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Global Batch Traceability 1.0: RIN", "RefUrl": "/notes/1672747 "}, {"RefNumber": "1640901", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on GBTR", "RefUrl": "/notes/1640901 "}, {"RefNumber": "1640900", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installation of the abap add-on GBTR 100", "RefUrl": "/notes/1640900 "}, {"RefNumber": "1634215", "RefComponent": "CA-MDG-APP-BP", "RefTitle": "MDGS: Short Dump in IDoc Data Mapper BAdI implementation", "RefUrl": "/notes/1634215 "}, {"RefNumber": "1532379", "RefComponent": "CA-MDG-APP-SUP", "RefTitle": "MDG: Corrections of the Data Distribution (SAP_APPL)", "RefUrl": "/notes/1532379 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "GBTRINT", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}