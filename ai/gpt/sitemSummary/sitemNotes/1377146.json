{"Request": {"Number": "1377146", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 698, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008119902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001377146?language=E&token=F798256D6D004EBD51A097612A552412"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001377146", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001377146/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1377146"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.08.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1377146 - IS-H CH: Analysis Service Rules - Enhancements"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1377146&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1377146/D\" target=\"_blank\">/notes/1377146/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Switzerland (CH).<br />This SAP Note contains the following functional enhancements for the service rules C33 and C35 for the analysis list:<br /><br />Surcharge services:</p> <UL><LI>If you set an analysis service to which a case-related or order-related surcharge service is assigned to non-billable, the system does not cancel and regenerate the surcharge service, but assigns it to the next suitable analysis service. If there is no longer a billable service, the surcharge service is also set to non-billable.</LI></UL> <UL><LI>The system now also takes non-billable analysis services into account by the service rule, that is, for non-billable services, the system also surcharge services.</LI></UL> <UL><LI>For services for which the user department is not filled in the service master data, the Customizing entries that have an asterisk entry in the Department column now also apply.</LI></UL> <p><br />Cumulation:<br />You can now also enter a service group in the Benefit 1 column.<br /><br />For more information, see the documentation in the Implementation Guide for the relevant service rule.<br /></p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>ISH_CH_CHECK_ANALYSE_EXTRA, ISH_CH_CHECK_ANALYSE_KUMU<br />ISH_CH_CHECK_ANALYSE_MAX</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Change of Request</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before you implement the source code corrections, you must perform the following actions manually:</p> <OL>1. Unpack the attached files depending on the IS-H version:</OL> <OL><OL>a) HW1377146_472.zip for 4.72 AOP 01 - 34</OL></OL> <OL><OL>b) HW1377146_60.zip  for 6.0  AOP 01 - 19</OL></OL> <OL><OL>c) HW1377146_603.zip for 6.03 AOP 01 - 07</OL></OL> <OL><OL>d) HW1377146_604.zip for 6.04 AOP 01 - 06</OL></OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the contained requests (one workbench request for each development client) into your system.<br /></OL> <OL>3. Now implement the source code corrections.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C2754910)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001377146/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1377146_604.zip", "FileSize": "19", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000558052009&iv_version=0002&iv_guid=8A8C6894D0F7CB4283308704FA33518C"}, {"FileName": "HW1377146_472.ZIP", "FileSize": "14", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000558052009&iv_version=0002&iv_guid=F76723B190353B45BBC62BCBC804ACF2"}, {"FileName": "HW1377146_60.zip", "FileSize": "14", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000558052009&iv_version=0002&iv_guid=9AD24CD494FEAE409B33A6EBF5B86B6E"}, {"FileName": "HW1377146_603.zip", "FileSize": "14", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000558052009&iv_version=0002&iv_guid=FAACFA6BBA93F740B1611FF0BACD286E"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1419582", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Correction of Fee Recipient and Surcharge Service", "RefUrl": "/notes/1419582"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1419582", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Correction of Fee Recipient and Surcharge Service", "RefUrl": "/notes/1419582 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF35", "URL": "/supportpackage/SAPKIPHF35"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60020INISH", "URL": "/supportpackage/SAPK-60020INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60209INISH", "URL": "/supportpackage/SAPK-60209INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60308INISH", "URL": "/supportpackage/SAPK-60308INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60407INISH", "URL": "/supportpackage/SAPK-60407INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/0001377146/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 13, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "772125 ", "URL": "/notes/772125 ", "Title": "IS-H CH: Service Rules: Quantity Limitation, Anesthesia Rule", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "833546 ", "URL": "/notes/833546 ", "Title": "IS-H CH: Service Rule C19, C29 - TARMED Maximum", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "913283 ", "URL": "/notes/913283 ", "Title": "IS-H CH: Service Rule C18 - Assignment Type for TARMED", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "926472 ", "URL": "/notes/926472 ", "Title": "IS-H CH: C30 - Service Blocks", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1099059 ", "URL": "/notes/1099059 ", "Title": "IS-H CH: Tarmed Version 1.05 - Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1126817 ", "URL": "/notes/1126817 ", "Title": "IS-H CH: Tarmed Assignment KVG and UVG/MTK/IVG", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1157762 ", "URL": "/notes/1157762 ", "Title": "IS-H CH: Enhancement of Service Rule C23 Division Surcharge", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1366434 ", "URL": "/notes/1366434 ", "Title": "IS-H CH: Analysis List - Service Rules", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1369837 ", "URL": "/notes/1369837 ", "Title": "IS-H CH: Analysis List - Correction LR C33 / C35", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "913283 ", "URL": "/notes/913283 ", "Title": "IS-H CH: Service Rule C18 - Assignment Type for TARMED", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "926472 ", "URL": "/notes/926472 ", "Title": "IS-H CH: C30 - Service Blocks", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1099059 ", "URL": "/notes/1099059 ", "Title": "IS-H CH: Tarmed Version 1.05 - Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1126817 ", "URL": "/notes/1126817 ", "Title": "IS-H CH: Tarmed Assignment KVG and UVG/MTK/IVG", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1157762 ", "URL": "/notes/1157762 ", "Title": "IS-H CH: Enhancement of Service Rule C23 Division Surcharge", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1366434 ", "URL": "/notes/1366434 ", "Title": "IS-H CH: Analysis List - Service Rules", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1369837 ", "URL": "/notes/1369837 ", "Title": "IS-H CH: Analysis List - Correction LR C33 / C35", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1157762 ", "URL": "/notes/1157762 ", "Title": "IS-H CH: Enhancement of Service Rule C23 Division Surcharge", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1366434 ", "URL": "/notes/1366434 ", "Title": "IS-H CH: Analysis List - Service Rules", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1369837 ", "URL": "/notes/1369837 ", "Title": "IS-H CH: Analysis List - Correction LR C33 / C35", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1312767 ", "URL": "/notes/1312767 ", "Title": "IS-H CH: Cumulation Rule C10 - Old/New", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1366434 ", "URL": "/notes/1366434 ", "Title": "IS-H CH: Analysis List - Service Rules", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1369837 ", "URL": "/notes/1369837 ", "Title": "IS-H CH: Analysis List - Correction LR C33 / C35", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1377146&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1377146/D\" target=\"_blank\">/notes/1377146/D</a>."}}}}