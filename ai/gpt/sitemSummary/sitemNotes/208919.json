{"Request": {"Number": "208919", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 428, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001099512017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000208919?language=E&token=1EBC3047AE35E77488055FA5F8E4A505"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000208919", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000208919/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "208919"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2001-07-20"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CTS-CCO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Client Copy (For ByD issues select BC-TLM-CP)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Change and Transport System", "value": "BC-CTS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client Copy (For ByD issues select BC-TLM-CP)", "value": "BC-CTS-CCO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS-CCO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "208919 - CC-INFO: Performance as of Release 4.6"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL>1. It takes a long time to delete tables.<br />The problem can occur in a deletion run (transaction SCC5) and while copying (SCCL or SCC9) into a client that is not empty.</OL> <OL>2. If the database was not reorganized for a longer time or the statistics of the optimizer are obsolete, very long runtimes for inidividual tables may occur as of Release 4.6.</OL> <OL>3. In Releases 4.6C and 4.6D, longer runtimes occur when you copy with application data.</OL> <OL>4. In systems on DB2/390, substantial runtimes can occur when deleting and copying into a client that is not empty.</OL> <OL>5. Parallel processes hang with UPDATE CCSELTAB.<br /></OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SCCL, SCC9, SCC5</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. The problem occurs if you copy into a client which is not empty (Transaction SCCL and SCC9) or you want to delete the client (Transaction SCC5).</OL> <OL>2. As of Release 4.6, a new copying function is used which avoids problems with timeouts in the online mode and with rollback segments. This was required because of the implementation of the parallel processes since these must always be dialog processes.<br /><br />However, for fragmented tables, that means tables which are not sorted according to the primary key (in particular during other database activities concerning the tables just to be copied) very long runtimes can occur.</OL> <OL>3. In Releases 4.6C and 4.6D, the change documents are also copied. Since the relevant tables can be very large, long runtimes may occur.</OL> <OL>4. The optimizer of DB2/390 mistakenly opts for a FULL TABLE SCAN with block accesses. This substantially increases the runtime with large tables if there is a large client in the system with a smaller number than the number of the target client.</OL> <OL>5. Due to concurring accesses, 'LOCKWAITS' occur. This can be checked in Transaction DB01.<br /></OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. A correction that helps in most of the cases is available in the following Support Packages for deleting clients (transaction SCC5) and making local copies (SCCL) &#x00A0;&#x00A0; SAPKB46A16 ; SAPKB46B08 ; SAPKB46C01<BR/>For remote copies (transaction SCC9) not unti: &#x00A0;&#x00A0; SAPKB46B19 ; SAPKB46C09 ; SAPKB46D03You can also implement advance corrections 160732 (SCC5 and SCCL) and 203375 (SCC9) from Note 70643.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For more information please see Notes 70643 and 365304. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 365304 also contains special reports for deleting individual critical tables. <OL>2. Reorganize your database with SAPDBA export/import or as described in the documentation of your database and update the statistics of the optimizer. You can concentrate on the tables for which long runtimes occur. This measure has proven to be very effective contrary to the opinion of a few database specialists. You find additional information in Note 22941.<br /><br />Additionally, you have the option of using parallel processes. Two processes per available database CPU are optimal. However, as a rule, more than 8 processes do not yield further greater advantage.<br /><br />Perform a copy -as far as this is possible- at a time when the client copy has the database exclusively at its disposal.<br /><br />Solution 1 also contains an optimization which copies larger blocks in chunks. By doing so, a little more main memory is required on the application server. This improves performance.</OL> <OL>3. As of the following Basis Support Packages, change documents are copied no longer: <BR/> &#x00A0;&#x00A0;SAPKB46C08&#x00A0;&#x00A0; SAPKB46D03<BR/>Normally, copying change documents only makes sense in the case of a recovery (for information see Notes 180949 and 31496). Thus you can exclude tables CDCLS, CDHDR, PCDHDR, CDPOS and PCDPOS from copying according to Note 70290, if you have not yet imported the corresponding Support Package.</OL> <OL>4. This problem is solved with the advance corrections 262205 attached to this note. The correction is contained in the following Basis Support Packages: &#x00A0;&#x00A0; SAPKB46B28 ; SAPKB46C19 ; SAPKB46D09</OL> <OL>5. The below preliminary correction 226517 solves the problem. The correction is contained in the following Basis Support Packages:<br />&#x00A0;&#x00A0; SAPKB46B29 ; SAPKB46C20 ; SAPKB46D10</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I073741)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000208919/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000208919/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208919/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208919/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208919/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208919/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208919/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208919/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208919/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96296", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ERROR: Database problem in client copy", "RefUrl": "/notes/96296"}, {"RefNumber": "70643", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-TOPIC: <PERSON>lient Deletion (SCC5)", "RefUrl": "/notes/70643"}, {"RefNumber": "70290", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Exclude tables with RSCCEXPT", "RefUrl": "/notes/70290"}, {"RefNumber": "678942", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Performance with buffered tables", "RefUrl": "/notes/678942"}, {"RefNumber": "67205", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large and productive clients", "RefUrl": "/notes/67205"}, {"RefNumber": "601196", "RefComponent": "PP-BD-RTG", "RefTitle": "Client copy: Problems with report RCTXTCPY", "RefUrl": "/notes/601196"}, {"RefNumber": "589886", "RefComponent": "BC-SRV-SCR", "RefTitle": "CC-ERROR: Performance with the remote SAPscript copying", "RefUrl": "/notes/589886"}, {"RefNumber": "550226", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Copying the Customizing takes a long time", "RefUrl": "/notes/550226"}, {"RefNumber": "517589", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Client copy performance", "RefUrl": "/notes/517589"}, {"RefNumber": "365304", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Reports for deleting tables", "RefUrl": "/notes/365304"}, {"RefNumber": "31496", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Client deleted by mistake", "RefUrl": "/notes/31496"}, {"RefNumber": "22941", "RefComponent": "BC-DB-INF", "RefTitle": "Reorganization of tables and dbspaces!", "RefUrl": "/notes/22941"}, {"RefNumber": "212727", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: FAQs for parallel processes up to Release 4.6D", "RefUrl": "/notes/212727"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "180949", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CC-INFO: Change documents during client copy", "RefUrl": "/notes/180949"}, {"RefNumber": "161495", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-RELEASE: Error in client copy in 4.6A and 4.6B", "RefUrl": "/notes/161495"}, {"RefNumber": "159316", "RefComponent": "BC-DB-MSS", "RefTitle": "Reorganization of tables on SQL Server", "RefUrl": "/notes/159316"}, {"RefNumber": "159171", "RefComponent": "BC-DB-MSS", "RefTitle": "Recompilation of SQL Statements", "RefUrl": "/notes/159171"}, {"RefNumber": "155413", "RefComponent": "BC-DB-MSS", "RefTitle": "Analysis of slow SQL statements", "RefUrl": "/notes/155413"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "113008", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Maintaining Catalog Statistics", "RefUrl": "/notes/113008"}, {"RefNumber": "103747", "RefComponent": "SV-PERF", "RefTitle": "Performance: Parameter recommendations as of Release 4.0", "RefUrl": "/notes/103747"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2163425", "RefComponent": "BC-CTS-CCO", "RefTitle": "Recommendations for client copy performance improvement", "RefUrl": "/notes/2163425 "}, {"RefNumber": "103747", "RefComponent": "SV-PERF", "RefTitle": "Performance: Parameter recommendations as of Release 4.0", "RefUrl": "/notes/103747 "}, {"RefNumber": "550226", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Copying the Customizing takes a long time", "RefUrl": "/notes/550226 "}, {"RefNumber": "678942", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Performance with buffered tables", "RefUrl": "/notes/678942 "}, {"RefNumber": "70290", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Exclude tables with RSCCEXPT", "RefUrl": "/notes/70290 "}, {"RefNumber": "70643", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-TOPIC: <PERSON>lient Deletion (SCC5)", "RefUrl": "/notes/70643 "}, {"RefNumber": "67205", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large and productive clients", "RefUrl": "/notes/67205 "}, {"RefNumber": "365304", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Reports for deleting tables", "RefUrl": "/notes/365304 "}, {"RefNumber": "96296", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ERROR: Database problem in client copy", "RefUrl": "/notes/96296 "}, {"RefNumber": "161495", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-RELEASE: Error in client copy in 4.6A and 4.6B", "RefUrl": "/notes/161495 "}, {"RefNumber": "212727", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: FAQs for parallel processes up to Release 4.6D", "RefUrl": "/notes/212727 "}, {"RefNumber": "31496", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Client deleted by mistake", "RefUrl": "/notes/31496 "}, {"RefNumber": "159171", "RefComponent": "BC-DB-MSS", "RefTitle": "Recompilation of SQL Statements", "RefUrl": "/notes/159171 "}, {"RefNumber": "155413", "RefComponent": "BC-DB-MSS", "RefTitle": "Analysis of slow SQL statements", "RefUrl": "/notes/155413 "}, {"RefNumber": "159316", "RefComponent": "BC-DB-MSS", "RefTitle": "Reorganization of tables on SQL Server", "RefUrl": "/notes/159316 "}, {"RefNumber": "113008", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Maintaining Catalog Statistics", "RefUrl": "/notes/113008 "}, {"RefNumber": "180949", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CC-INFO: Change documents during client copy", "RefUrl": "/notes/180949 "}, {"RefNumber": "517589", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Client copy performance", "RefUrl": "/notes/517589 "}, {"RefNumber": "589886", "RefComponent": "BC-SRV-SCR", "RefTitle": "CC-ERROR: Performance with the remote SAPscript copying", "RefUrl": "/notes/589886 "}, {"RefNumber": "22941", "RefComponent": "BC-DB-INF", "RefTitle": "Reorganization of tables and dbspaces!", "RefUrl": "/notes/22941 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "601196", "RefComponent": "PP-BD-RTG", "RefTitle": "Client copy: Problems with report RCTXTCPY", "RefUrl": "/notes/601196 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B28", "URL": "/supportpackage/SAPKB46B28"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B29", "URL": "/supportpackage/SAPKB46B29"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C19", "URL": "/supportpackage/SAPKB46C19"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C20", "URL": "/supportpackage/SAPKB46C20"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D09", "URL": "/supportpackage/SAPKB46D09"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D10", "URL": "/supportpackage/SAPKB46D10"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 2, "URL": "/corrins/0000208919/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "46B", "Number": "350189 ", "URL": "/notes/350189 ", "Title": "CC-TOPIC: Error messages, client copy terminations", "Component": "BC-CTS-CCO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46D", "Number": "350189 ", "URL": "/notes/350189 ", "Title": "CC-TOPIC: Error messages, client copy terminations", "Component": "BC-CTS-CCO"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}