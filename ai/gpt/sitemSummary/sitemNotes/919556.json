{"Request": {"Number": "919556", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 474, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016049692017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000919556?language=E&token=C694036962447C85C3AB506B28FEF885"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000919556", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000919556/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "919556"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.11.2010"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-RU-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "Utilities"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Russia", "value": "XX-CSC-RU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-RU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-RU-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-RU-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Utilities", "value": "XX-CSC-RU-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-RU-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "919556 - Solution for Benefits - Russia and Ukraine"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>IS-U local enhancement - country specific functionality for Russia and Ukraine, installation and documentation.<br />Large group of residential customers in Russia and Ukraine is entitled to different kind of benefits - possibility to pay partialy for communal services. Benefit amounts are refunded to the utility companies by state, regional or municipal authority.<br />As the calculation, combination and assignement of the benefits is specific to the Russian market, new solution was developed.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Benefits, /SAPCE/IUEBEN2, /SAPCE/IUEBEN3, /SAPCE/IURU_BENEFITPOST</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have to install add-on CEEISUT rel.4.72 with current AOPs. It contains IS-U/CCS and IS-T localization for CEE countries.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note:</p> <UL><LI>These local enhancements are part of the Russian and Ukrainian country version of IS-U/CCS delivered in add-on CEEISUT</LI></UL> <UL><LI>Local support of SAP Russia and Ukraine is responsible for maintenance and hotline support of those enhancements for Russia and Ukraine only</LI></UL> <UL><LI>These enhancements are not released for any other countries.<br /></LI></UL> <p>***********************************************************************</p> <OL>1. Overview of enhancements</OL> <p><br />Benefits is additional solution for invoicing of the residential customers. Further, the report for transfer posting of benefits to the account of the authority is a part of the solution. Additional customizing and and settings are required, see below.<br />Whole application could be accessed through the following transations:<br />/SAPCE/IUEBEN2 - Maintain benefit eligibility<br />/SAPCE/IUEBEN3 - Display benefit eligibility<br />Note: To call transaction e.g./SAPCE/IUEBEN2, you have to write /n/SAPCE/IUEBEN2 in command field or add transaction to your favorites.<br />Report for transfer posting /SAPCE/IURU_BENEFITPOST can be run using transaction SA38.<br /><br />***********************************************************************</p> <OL>2. Solution-specific customizing</OL> <p><br />1. There are several events implementations which can be assigned by activation of the BC set /CEEISUT/ISU_RU_06 (transaction SCPR20). Please check the activation of your own implementations using transaction FQEVENTS to define free sequential numbers.<br />These Events implementation will be assigned:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Event</TH><TH ALIGN=LEFT> Function module</TH></TR> <TR><TD>0010</TD><TD> /SAPCE/FKRU_EVENT_0010_BPOS</TD></TR> <TR><TD>0020</TD><TD> /SAPCE/FKRU_EVENT_0020_BPOS</TD></TR> <TR><TD>0030</TD><TD> /SAPCE/FKRU_EVENT_0030_BPOS</TD></TR> <TR><TD>R433</TD><TD> /SAPCE/IURU_BEN_R433</TD></TR> <TR><TD>R434</TD><TD> /SAPCE/IURU_BEN_R434</TD></TR> <TR><TD>R471 </TD><TD> /SAPCE/IURU_BEN_REV_R471</TD></TR> <TR><TD>R472</TD><TD> /SAPCE/IURU_BEN_REV_R472</TD></TR> </TABLE> <p>If you do not use the Partial payment refundation of benefits (see the documentation), you can deactivate the implementation of the event 0020. This can slightly improve the performence of your system.<br /><br />2.&#x00A0;&#x00A0;An implementation of the enhancement EBIA0027 exists and should be activated manually.</p> <UL><UL><LI>\" Edit the program (include) ZXE20BU02 (transaction SE38 in the change mode).</LI></UL></UL> <UL><UL><LI>\" Insert following code</LI></UL></UL> <p><br />*&gt;&gt;&gt;&gt; START OF INSERTION &lt;&lt;&lt;&lt;<br />* Localization for RU and UA - Benefit's operand check<br />&#x00A0;&#x00A0; INCLUDE /sapce/iuru_benef_zxe20bu02.<br />*&gt;&gt;&gt;&gt; END OF INSERTION &lt;&lt;&lt;&lt;<br /></p> <UL><UL><LI>\" Save and Activate the program</LI></UL></UL> <UL><UL><LI>\" Activate the SAP enhancement EBIA0027 using transaction CMOD.</LI></UL></UL> <p><br /><br />***********************************************************************</p> <OL>3. Installation-specific customizing</OL> <p><br />Customizing of benefits in described in the documentation attached. You can access it using the Customizing path: SAP Utilities -&gt; Contract Billing -&gt; Billing Master Data -&gt; Benefits Russia and Ukraine<br /><br />***********************************************************************<br />Documentation<br />The documentation is attached to this note.<br />(If you have accessed this note through SAP Service Marketplace please see Tab-page Attachments.)</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-RU-FICA (use FI-LOC-CA-RU)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I033783)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I033783)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919556/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ISU_Documentation_Benefits.pdf", "FileSize": "546", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000092562006&iv_version=0005&iv_guid=D096616634DF1549A7CA6591DF86A679"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "960447", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "IS-U Solution for Subsidies - Ukraine", "RefUrl": "/notes/960447"}, {"RefNumber": "921035", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/921035"}, {"RefNumber": "1528404", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "IS-U UA localization: Benefit/subdidy post error", "RefUrl": "/notes/1528404"}, {"RefNumber": "1058127", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Subsidies, Benefits, and Social Benefits Reporting", "RefUrl": "/notes/1058127"}, {"RefNumber": "1057999", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1057999"}, {"RefNumber": "1057994", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Benefit Eligibility Transaction", "RefUrl": "/notes/1057994"}, {"RefNumber": "1057992", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Posting Benefits & Subsidies with VAT + Net Due Date", "RefUrl": "/notes/1057992"}, {"RefNumber": "1057797", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "New Functionality for Benefits Single Operand Usage", "RefUrl": "/notes/1057797"}, {"RefNumber": "1014954", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014954"}, {"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953"}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2501757", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FICA localization - country specific steps(BC Set activation replacement)", "RefUrl": "/notes/2501757 "}, {"RefNumber": "2166023", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "CEEISUT (UA/RU): Benefits getting calculated for all contracts", "RefUrl": "/notes/2166023 "}, {"RefNumber": "1014954", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014954 "}, {"RefNumber": "921035", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/921035 "}, {"RefNumber": "1528404", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "IS-U UA localization: Benefit/subdidy post error", "RefUrl": "/notes/1528404 "}, {"RefNumber": "1058127", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Subsidies, Benefits, and Social Benefits Reporting", "RefUrl": "/notes/1058127 "}, {"RefNumber": "960447", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "IS-U Solution for Subsidies - Ukraine", "RefUrl": "/notes/960447 "}, {"RefNumber": "1057797", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "New Functionality for Benefits Single Operand Usage", "RefUrl": "/notes/1057797 "}, {"RefNumber": "1057992", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Posting Benefits & Subsidies with VAT + Net Due Date", "RefUrl": "/notes/1057992 "}, {"RefNumber": "1057994", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Benefit Eligibility Transaction", "RefUrl": "/notes/1057994 "}, {"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953 "}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}