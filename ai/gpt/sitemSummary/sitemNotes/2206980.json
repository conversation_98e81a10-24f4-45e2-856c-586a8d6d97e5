{"Request": {"Number": "2206980", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 319, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018149692017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002206980?language=E&token=005C334FBE9E345B67EE0622F83A4B36"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002206980", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002206980/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2206980"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2022.05.20"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IM-GF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Management", "value": "MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "MM-IM-GF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM-GF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2206980 - Material Inventory Managment: change of data model in S/4HANA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to install SAP S/4HANA and need additional information how to adjust your customer enhancements, modifications or own functionalities&#160;to the new, simplified data model of SAP S/4HANA Supply Chain (MM -&#160;Inventory Management).</p>\r\n<p>You want to have information about what is different in SAP S/4HANA Supply Chain (MM -&#160;Inventory Management) compared to Suite on HANA MM-IM.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><span lang=\"EN-US\" style=\"line-height: 107%; font-family: 'Calibri',sans-serif; font-size: 11pt; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-bidi-theme-font: minor-bidi; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">S4TC, S/4 transition, MM-IM, Material Management</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have customer enhancements, modifications or own functionalities&#160;in the area of inventory management (component MM-IM) which were built for SAP ERP 6.0.</p>\r\n<p>You are using functionalities which behave different in SAP S/4HANA Supply Chain (MM -&#160;Inventory Management) compared to Suite on HANA.</p>\r\n<p>&#160;</p>\r\n<p>The SAP ERP 6.0 stock inventory management data model consists of the two document tables MKPF for document header information and MSEG for document item data. Additionally there were aggregated actual stock quantity data stored in several tables. Some of these tables do also store material master data attributes like the tables MARC, MARD and MCHB. Such tables with material master data attributes as well as actual stock quantities will be named as hybrid tables in the following. In contrast there are also tables like MSSA containing only aggregated actual stock quantities for sales order stock. Such tables will be called in the following as replaced aggregation tables.</p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">With S/4HANA this data model has been changed significantly. The new de-normalized table MATDOC has been introduced which contains the former header and item data of a material document as well as a lot of further attributes. Material document data will be stored in MATDOC only and not anymore in MKPF and MSEG. Additionally the aggregated actual stock quantities will not be persisted anymore in the hybrid or replaced&#160;aggregation tables. Instead, actual stock quantity data will be calculated on-the-fly from the new material document table MATDOC for which some of those additional special fields are used. Hence, with the new MM-IM data model the system will work on database level in an INSERT only mode without DB locks. Nevertheless, for stock decreasing processes there will be still ABAP locks to ensure stock consistency. A further advantage of the new MM-IM data model is the capability of simple and fast reporting because the most information&#160;is all in one place: MATDOC.</p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">All below mentioned tables of the SAP ERP 6.0&#160;product do still exist in S/4HANA as DDIC definition as well as database object and the hybrid tables will still be used to store the material master data attributes. For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables ensuring that each read access to one of the mentioned tables below still returns the data as before in SAP ERP 6.0. The CDS Views do the on-the-fly aggregation of actual stock quantities from the new MM-IM data model and join the master data attributes from the material master data table. Hence all customer coding reading data from those tables&#160;will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted.</p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">The affected tables are:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" class=\"confluenceTable\" style=\"white-space: normal; word-spacing: 0px; border-collapse: collapse; text-transform: none; color: #333333; text-align: left; font: 14px/20px Arial, sans-serif; widows: 1; margin: 10px 0px 0px; letter-spacing: normal; text-indent: 0px; font-size-adjust: none; font-stretch: normal; -webkit-text-stroke-width: 0px; -ms-overflow-x: auto;\">\r\n<tbody>\r\n<tr style=\"color: #333333;\"><th class=\"confluenceTh\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #000000; font-weight: bold; vertical-align: top; white-space: pre-wrap; background-color: #f0f0f0;\">\r\n<div class=\"tablesorter-header-inner\" style=\"color: #000000;\">Table</div>\r\n</th><th class=\"confluenceTh\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #000000; font-weight: bold; vertical-align: top; white-space: pre-wrap; background-color: #f0f0f0;\">Table description</th><th class=\"confluenceTh\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #000000; font-weight: bold; vertical-align: top; white-space: pre-wrap; background-color: #f0f0f0;\">\r\n<div class=\"tablesorter-header-inner\" style=\"color: #000000;\">DDL Source of CDS View for redirect</div>\r\n</th><th class=\"confluenceTh\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #000000; font-weight: bold; vertical-align: top; white-space: pre-wrap; background-color: #f0f0f0;\">\r\n<div class=\"tablesorter-header-inner\" style=\"color: #000000;\"><strong>View to read the content of the database table (w/o redirect to compatibility view)</strong></div>\r\n</th><th class=\"confluenceTh\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #000000; font-weight: bold; vertical-align: top; white-space: pre-wrap; background-color: #f0f0f0;\">\r\n<div class=\"tablesorter-header-inner\" style=\"color: #000000;\">View to read the master data attributes only</div>\r\n</th></tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MKPF</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Material document header</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MKPF</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MKPF</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSEG</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Material document item</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSEG</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSEG</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">\r\n<p style=\"margin: 0px; color: #333333; background-color: transparent;\">MARC</p>\r\n</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Plant Data for Material</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MARC</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MARC</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MARC_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MARD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Storage Location Data for Material</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MARD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MARD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MARD_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCHB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Batch stocks</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MCHB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MCHB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MCHB_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MKOL</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Special Stocks from Vendor</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MKOL</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MKOL</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MKOL_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSLB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Special Stocks with Vendor</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSLB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSLB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MSLB_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSKA</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Sales Order Stock</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSKA</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSKA</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MSKA_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSSA</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Total Customer Orders on Hand</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSSA</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSSA</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSPR</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Project Stock</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSPR</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSPR</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MSPR_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSSL</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Total Special Stocks with Vendor</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSSL&#160;</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSSL&#160;</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\"></td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSSQ</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Project Stock Total</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSSQ</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSSQ</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSKU</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Special Stocks with Customer</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSKU</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSKU</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">V_MSKU_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSTB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Stock in Transit</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSTB</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSTB</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSTE</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Stock in Transit to Sales and Distribution Document</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSTE</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSTE</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSTQ</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">Stock in Transit for Project</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSTQ</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSTQ</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCSD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Customer Stock</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MCSD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MCSD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCSD_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCSS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Total Customer Stock</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MCSS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MCSS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCSS_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSCD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Customer stock with vendor</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSCD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSCD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSCD_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSCS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Customer stock with vendor - Total</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSCS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSCS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSCS_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSFD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Sales Order Stock with Vendor</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSFD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSFD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSFD_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSFS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Sales Order Stock with Vendor - Total</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MFS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSFS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSFS_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSID</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Vendor Stock with Vendor</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSID</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSID</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSID_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSIS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Vendor Stock with Vendor - Total</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSIS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSIS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSIS_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSRD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Project Stock with Vendor</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSRD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSRD</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSRD_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSRS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">DIMP: Project Stock with Vendor - Total</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSRS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSRS</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSRS_MD</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">\r\n<p style=\"margin: 0px; color: #333333; background-color: transparent;\">MARCH</p>\r\n</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MARCH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MARCH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MARDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MARDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MARDH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCHBH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MCHBH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MCHBH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MKOLH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MKOLH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MKOLH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSLBH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSLBH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSLBH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSKAH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSKAH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSKAH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSSAH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSSAH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSSAH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSPRH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSPRH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSPRH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSSQH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSSQH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSSQH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSKUH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSKUH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSKUH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSTBH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSTBH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSTBH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSTEH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSTEH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSTEH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSTQH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSTQH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSTQH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCSDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MCSDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MCSDH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MCSSH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MCSSH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MCSSH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSCDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSCDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSCDH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSFDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSFDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSFDH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSIDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSIDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSIDH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n<tr style=\"color: #333333;\">\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">MSRDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">History</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_DDL_MSRDH</td>\r\n<td class=\"confluenceTd\" style=\"padding: 7px 10px; border: 1px solid #dddddd; border-image: none; text-align: left; color: #333333; vertical-align: top; white-space: pre-wrap;\">NSDM_MIG_MSRDH</td>\r\n<td class=\"confluenceTd\" style=\"text-align: center;\">-</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; text-align: left; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: transparent; -webkit-text-stroke-width: 0px;\">The hybrid tables of the former Industry Solution DIMP have now new tables containing the material master data only. The name of the new tables is presented in the right column of above table.</p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; text-align: left; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: transparent; -webkit-text-stroke-width: 0px;\">&#160;<span lang=\"EN-US\" style=\"font-size: 10.5pt; font-family: 'Arial',sans-serif; color: #333333; mso-ansi-language: EN-US;\">According to the fact that data will not persisted anymore in the header and item tables MKPF and MSEG the transaction DB15 behaves differently in the environment of archiving.</span></p>\r\n<p><span lang=\"EN-US\" style=\"font-size: 10.5pt; font-family: 'Arial',sans-serif; color: #333333; mso-ansi-language: EN-US;\">Transaction DB15, which allows the retrieval of statistical data for DB tables grouped by the archiving objects that refer to these tables, does not provide correct information for tables MKPF and MSEG. When selecting tables from which data is archived for archiving object MM_MATBEL, and navigating to &#8220;Online Space&#8221; or &#8220;Space Statistics&#8221; for tables MKPF or MSEG, the statistics &#8220;No. Records&#8221; and &#8220;Table Space&#8221; are shown in the result screen. These numbers are taken from the original tables MKPF and MSEG, and not calculated by redirecting the request to table MATDOC. Consequently, when executing archiving for arching object MM_MATBEL, this will have no effect on the numbers shown for tables MKPF and MSEG in transaction DB15.</span></p>\r\n<p><span lang=\"EN-US\" style=\"font-size: 10.5pt; font-family: 'Arial',sans-serif; color: #333333; mso-ansi-language: EN-US;\">Some of the hybrid tables contain a field with the semantic \"date of last change\" (in many cases field name is LAEDA). In the ERP solution this field has been updated with each material document posting. With the introduction of the new data model in S/4 the hybrid tables will not be updated with actual stock data anymore and also the field LAEDA will not be updated. Hence, the semantic of this field for the material master data hybrid tables changes to \"date of last change of master data\".</span></p>\r\n<div class=\"table-wrap\" style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; -ms-overflow-x: auto; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">One impact of the simplified MM-IM data model does exist if there are customer APPENDs or INCLUDEs&#160;with customer fields on the mentioned tables.&#160;The NetWeaver redirect capability requires that DB table and assigned proxy view is compatible in the structure: number of fields,&#160;their sequence and their type. Thus if there is an&#160;APPEND or INCLUDE&#160;on one of the above mentioned tables then the assigned DDL source of the CDS view must be made compatible. In some cases for S/4HANA on-premise 1511&#160;this does not require customer interaction especially in those cases where the append has been put at the end of a table which is strongly recommended (not somewhere in between which may happen if a table is composed by include structures like for MARC). For other cases and in general for S/4HANA on-premise 1610 the&#160;structure of the proxy view can be made compatible to the table by extension view. This extension view is always an extension to the above mentioned view in the DDL source of the CDS view used for redirect. In the extension view the fields have to be listed in exactly the same order as in the append.&#160;For more information about view extension see e.g.&#160;<a target=\"_blank\" class=\"external-link\" href=\"http://help.sap.com/abapdocu_740/en/index.htm?file=abencds_f1_extend_view.htm\" rel=\"nofollow\" style=\"color: #326ca6; text-decoration: none;\">SAP NetWeaver 7.4 documentation</a>.</p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">Another impact of the simplified MM-IM data model is a performance decrease of DB read operations on the above mentioned tables&#160;just because a data fetch on one of the mentioned tables is in S/4HANA slower than in SAP ERP 6.0 due to the on-the-fly aggregation and the JOIN operation. Hence performance critical customer coding may be adjusted to improve performance. Furthermore customer coding writing data to aggregated actual stock quantity or to the former document header or item table shall be adjusted!</p>\r\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CustomerAppendsontheformerdocumenttablesMKPFandMSEG\" style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong>1. Customer Appends</strong></div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">With SAP Note <a target=\"_blank\" href=\"/notes/2194618\">2194618</a> and <a target=\"_blank\" href=\"/notes/2197392\">2197392</a> SAP offers a check to be executed on the start release to identify&#160;APPEND issues described in the following sub chapters. Hence customer is not forced to scan all above listed tables manually.</div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">1.1 Customer Appends on the former document tables MKPF and MSEG</span></strong></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">If there are&#160;APPENDs&#160;on MKPF and MSEG where fields with the same fieldname do exist then there is a name conflict in case that the content of field MKPF-A is different from field MSEG-A (fields with same name and identical content do exist on MKPF and MSEG also in SAP Standard for performance reasons e.g. BUDAT).&#160;In this case it is&#160;required to add a further field A_NEW to the append, copy the data from A to A_NEW with a special customer program and then all coding sections, Dynpros, etc. need to be adjusted to use A_NEW and then field A needs to be dropped from the append. This must be done before migration from ERP 6.0 to&#160;S/4HANA.</span></span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">If the attributes in the APPENDs or INCLUDEs (e.g. CI_M*&#160;INCLUDEs as introduced with consulting note 906314) on&#160;table MKPF and MSEG&#160;do have a master data or process controlling character then the fields from the APPENDs/INCLUDEs need to be appended to the table MATDOC and the assigned proxy views can be made compatible via extension views.</span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">In case of a system conversion all these customer fields in such APPENDs or INCLUDEs need to be appended to table MATDOC during the ACT_UPG phase (SPDD). It has to be done in any case before the MM-IM converison program will be executed which move the data from MKPF and MSEG to MATDOC otherwise data in customer fields gets lost. The structure compatibility between table MKPF/MSEG and their assigned proxy view&#160;shall be created directly after system conversion by creating extend views, see&#160;&#160;note <a target=\"_blank\" href=\"/notes/2242679\">2242679</a>.</span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">Fields from APPENDs/INCLUDEs to table MKPF should be appended to sub structure NSDM_S_HEADER of table MATDOC.</span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">Fields from APPENDs/INCLUDEs </span>to table MSEG should be appended to sub structure NSDM_S_ITEM of table MATDOC.</span></span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"font-size: 14px; color: #333333; line-height: 1.4285; background-color: transparent;\"><span style=\"font-size: 14px; color: #333333; line-height: 1.4285; background-color: transparent;\">1.1.1 Customer include CI_COBL in table MSEG</span></span></strong></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">Table MSEG contains the customer include CI_COBL where customers can insert own fields. The CI_COBL include has been made available also in the new MM-IM data model with note <a target=\"_blank\" href=\"/notes/2240878\">2240878</a>. This note must be applied before the data migration starts in the ACT_UPG phase (SPDD); otherwise you may loose data. With the implemented CI_COBL the table MSEG and it's assigned proxy view is not compatible in their structure anymore. The structural compatibility can be re-created by applying note <a target=\"_blank\" href=\"/notes/2242679\">2242679</a>. This must be done directly after system conversion has been finished.</span></span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">If you later on like to make use of one of the fields in EXTEND VIEW for CI_COBL in the app \"Custom fields and logic\" then you need to follow&#160;the instructions given in this <a target=\"_blank\" href=\"https://help.sap.com/viewer/9a281eac983f4f688d0deedc96b3c61c/1610%20003/en-US/4accfedc4d2e49c1b321b6ebf288a430.html\">article in the SAP Help portal</a>. Basically you need to remove the field from the EXTEND VIEW for CI_COBL, save without activate and then add the field in the app \"Custom fields and logic\". This is valid for release 1709 and higher.</span></span></p>\r\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CustomerAppendsonthehybridandpureaggregatetables\" style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333;\">1.2 Customer Appends on the hybrid and&#160;replaced aggregation tables</span></strong></p>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<div id=\"SI1:Logistics_MM-IM-Datamodel-Fieldscontainingmaterialmasterdataattributes\" style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333;\">1.2.1 Fields containing material master data attributes</span></strong></div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333;\">If the append is not at the end of the hybrid table then the append should be moved to the end if possible and then no further action is required because the delivered DDL sources for the proxy views provide the $EXTENSION feature within S/4HANA on-premise 1511. Due to too many side effects like unpredictable&#160;sequence of fields from APPENDs, this has been changed with S/4HANA On-Premise 1610 where always an EXTEND VIEW for a CDS proxy view has to be created for an APPEND on a material master data table. For the DIMP tables the append has to be appended also to the new pure DIMP material master data tables.</span></div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333;\">The structural compatibility between table and CDS proxy view can be re-created by applying note&#160;<a target=\"_blank\" href=\"/notes/2242679\">2242679</a>. This must be done directly after system conversion has been finished (e.g. creating just an EXTEND VIEW with the customer fields using ABAP Development Tools for S/4HANA On Premise 1610 and higher).</span></div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333;\">For replaced aggregation tables appends with master data attributes are not supported. If such appends are really required in the customer processes then the approach described in the next chapter maybe feasible. In the core functionality of material document processing there will be no write process on these tables. Thus update of the fields in the appends requires maybe some additional customer coding.&#160;</span></div>\r\n<div id=\"SI1:Logistics_MM-IM-Datamodel-Fieldsrepresentingacustomerdefinedstocktypeorquantity/valuetobeaggregated\" style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333;\">1.2.2 Fields representing a customer defined stock type or quantity/value to be aggregated</span></strong></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">If own stock types or a dimension which needs to be aggregated have been introduced by the customer then the view stack of the CDS view assigned to the table with the additional stock type needs to be modified. Right now, there is no technology support for modification free enhancement. If the stock type has not been introduced by new entries or enhancements in the tables T156x (T156, T156SY, T156M, T156F)&#160;- which controls in the core functionality the mapping between a posting and a stock type - then&#160;the process logic needs to be adapted.</span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">1.3 Customer Appends on views</span></strong></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">There are several views in SAP Standard which also do have an assigned proxy view because the view provide actual stock quantity data. View and assigned proxy view must be compatible in structure too. If there are customer appends on such view the same rules as for tables apply. Views with assigned proxy compatibility view can be determined by searching via transaction SE16N in table DD02L with TABCLASS = VIEW and VIEWREF &lt;&gt; '' or you may use above mentioned check functionality in your start release.</span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">1.3.1 Customer views on MKPF/MSEG</span></strong></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">Views are database objects and thus a view is executed on the database. Because the table MKPF and MSEG will not contain&#160;data anymore (except legacy data from migration)&#160;such a customer view will never return any record. Such views have to be either adjusted by fetching data from table MATDOC or to be created new as DDL source with a different name. In the last case all usages of the old DDIC SQL view must be&#160;replaced by the new CDS view.</span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">1.3.2 Customer views on material master attributes</span></strong></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">Such views using only material master data attributes from the hybrid tables do not need to be changed.</span></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">1.3.3 Customer views using aggregated stock quantity data</span></strong></p>\r\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CodeOptimization(optional/recommended)\" style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">Customer views having at least one actual stock quantity aggregate cannot be used anymore because</div>\r\n<ul>\r\n<li>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">the field representing this aggregate on the database will be empty forever</div>\r\n</li>\r\n<li>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">the quantity must be aggregated from table MATDOC which is not possible with DDIC SQL views.</div>\r\n</li>\r\n</ul>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">Such views must be defined new as DDL source with a new name. Each of the above mentioned DDL sources can be used as template. All usages of the old DDIC SQL view must be replaced by the new CDS view.</div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<div style=\"font: 14px/20px Arial, sans-serif; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; z-index: 0; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"></div>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\"><strong><span style=\"color: #333333; line-height: 1.4285; font-size: 14px; background-color: transparent;\">2 Code adjustments and optimizations</span></strong></p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">Technically it is still possible to do DB write operations (INSERT, UPDATE, DELETE, MODIFY) on the tables MKPF, MSEG as well as the fields representing actual stock quantities in the hybrid and replaced aggregation tables. But such write operations are without any effect! Therefore write operations on&#160;MKPF, MSEG as well as the fields representing actual stock quantities in the hybrid and replaced aggregation tables shall be removed from customer coding. Write operations on the material master data attributes in the hybrid tables are still possible. Write operations on table MATDOC and your moved customer append fields&#160;are done by class CL_NSDM_STOCK.</p>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">DB read operations on the&#160;hybrid and replaced aggregation tables have a performance decrease. In general, it shall be avoided to read any stock quantities&#160;when only master data is required. Therefore it is recommended to adjust the customer coding in the following way:</p>\r\n<ul style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; list-style-type: disc; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">\r\n<li>If material master data as well as actual stock quantity data are required then the SELECT....&lt;table&gt; should be replaced by using a data access method&#160;from class CL_NSDM_SELECT_&lt;table&gt;. These classes provide access methods for single as well as array read operations. Those access methods shall not be used if the KEY table contains more than 1000 records to to limitations of the SELECT.....FOR ALL ENTRIES.</li>\r\n<li>If material master data are required then the SELECT....&lt;table&gt; should be replaced by SELECT....V_&lt;table&gt;_MD where V_&lt;table&gt;_MD is one of the above mentioned views for master data access. Alternatively corresponding material master data read methods in the class CL_NSDM_SELECT_&lt;table&gt; can be used (those access methods shall not be used if the KEY table contains more than 1000 records to to limitations of the SELECT.....FOR ALL ENTRIES.). Also the data type declarations should be adjusted from TYPE...&lt;table&gt; to TYPE...V_&lt;table&gt;_MD.</li>\r\n<li>If actual stock quantity data are required then the&#160;SELECT....&lt;table&gt; should be replaced by SELECT....NSDM_V_&lt;table&gt;_DIFF where NSDM_V_&lt;table&gt;_DIFF is one of the views in the view stack of the above mentioned proxy view.&#160;Also the data type declarations should be adjusted from TYPE...&lt;table&gt; to TYPE...NSDM_V_&lt;table&gt;_DIFF.</li>\r\n<li>For table MARC and field STAWN valid from S/4HANA On Premise 1610 please read note&#160;<a target=\"_blank\" href=\"/notes/2378796\">#mce_temp_url#</a></li>\r\n</ul>\r\n<p style=\"font: 14px/20px Arial, sans-serif; margin: 10px 0px 0px; color: #333333; text-transform: none; text-indent: 0px; letter-spacing: normal; word-spacing: 0px; white-space: normal; widows: 1; font-size-adjust: none; font-stretch: normal; background-color: #ffffff; -webkit-text-stroke-width: 0px;\">For performance critical coding parts these adjustments are strongly recommended. For non critical parts it is optional short term but recommended on long term.</p>\r\n<p style=\"margin: 0cm 0cm 8pt;\"><span lang=\"EN-US\" style=\"line-height: 107%; font-family: 'Arial',sans-serif; font-size: 10pt;\">To identify such locations, it is required to make use of the where-used functionality of transaction SE11 and considering other techniques like transaction CODE_SCANNER to find locations which SE11 cannot handle &#8211; like dynamic programming or native SQL statements.</span></p>\r\n<p style=\"margin: 0cm 0cm 8pt;\"><span lang=\"EN-US\" style=\"line-height: 107%; font-family: 'Arial',sans-serif; font-size: 10pt;\">Consider SAP Note <a target=\"_blank\" href=\"/notes/28022\">28022</a> if there are issues with the where-used functionality in the customer system. In the where-used dialog it is possible via the button \"Search Range\" to search specific for key words like SELECT, INSERT and so on.</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-IM-GF-MIG (Migration to the new material document data model)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D039514)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D039514)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002206980/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002206980/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002206980/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002206980/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002206980/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002206980/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002206980/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002206980/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002206980/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SI1_Logistics_MM_IM_New_Datamodel.pdf", "FileSize": "1131", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001625332015&iv_version=0020&iv_guid=6CAE8B3EABEB1ED694878D6F3ADA60C2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2378796", "RefComponent": "SLL-LEG-FUN-CLS", "RefTitle": "Material classification: Change in data model in SAP S/4HANA 1610", "RefUrl": "/notes/2378796"}, {"RefNumber": "2242679", "RefComponent": "MM-IM-GF", "RefTitle": "Redirect inconsistency - Proxy Substitution", "RefUrl": "/notes/2242679"}, {"RefNumber": "2240878", "RefComponent": "MM-IM-GF", "RefTitle": "MM-IM: Add customer include CI_COBL to MATDOC", "RefUrl": "/notes/2240878"}, {"RefNumber": "2197392", "RefComponent": "MM-IM-GF", "RefTitle": "Resolve findings of core ERP MM-IM S/4HANA pre checks", "RefUrl": "/notes/2197392"}, {"RefNumber": "2194618", "RefComponent": "MM-IM-GF", "RefTitle": "S4TC SAP_APPL - Checks for MM-IM", "RefUrl": "/notes/2194618"}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2595627", "RefComponent": "HAN-DB-ENG", "RefTitle": "Accessing table from SE16/SE16N shows different results to SAP HANA database", "RefUrl": "/notes/2595627 "}, {"RefNumber": "3216468", "RefComponent": "MM-IM-GF", "RefTitle": "Procurement - Inventory Management - Standard behaviors guide and How-to scenarios", "RefUrl": "/notes/3216468 "}, {"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002 "}, {"RefNumber": "2847109", "RefComponent": "XX-SER-MCC", "RefTitle": "MCC FAQ: S/4HANA On Premise", "RefUrl": "/notes/2847109 "}, {"RefNumber": "2753984", "RefComponent": "MM-IM-GF-BM", "RefTitle": "MCHB not updated with last change", "RefUrl": "/notes/2753984 "}, {"RefNumber": "3163754", "RefComponent": "MM-IM-GF", "RefTitle": "S/4HANA: History stock tables within the new data model for Inventory Management", "RefUrl": "/notes/3163754 "}, {"RefNumber": "3030981", "RefComponent": "MOB-APP-MAO-ERP", "RefTitle": "Stock exchange table is not getting updated", "RefUrl": "/notes/3030981 "}, {"RefNumber": "3023744", "RefComponent": "MM-IM-GF-SP", "RefTitle": "S/4: sales order/project stock postings into non-valuated GR blocked stock - special stock fields usage in table MATDOC", "RefUrl": "/notes/3023744 "}, {"RefNumber": "2995838", "RefComponent": "XX-CSC-RU-LO", "RefTitle": "J_3RMOBVEDH Stock Overview (New). Issues after adaptation of the report to the new Inventory Management data model", "RefUrl": "/notes/2995838 "}, {"RefNumber": "2753888", "RefComponent": "MM-IM-GF-MIG", "RefTitle": "Increase Precheck performance: skip KALNR-check (class CLS4SIC_MM_IM_SI1)", "RefUrl": "/notes/2753888 "}, {"RefNumber": "2713495", "RefComponent": "MM-IM-GF", "RefTitle": "S/4HANA: Performance issues in custom code when using the obsolete stock data model", "RefUrl": "/notes/2713495 "}, {"RefNumber": "2686694", "RefComponent": "MM-IM-GF", "RefTitle": "How-To: MSEG - DBSQL_REDIRECT_INCONSISTENCY", "RefUrl": "/notes/2686694 "}, {"RefNumber": "2600991", "RefComponent": "IS-ADEC-SUB", "RefTitle": "DI_PCS_MRP: S4HANA MM-IM data model improvements", "RefUrl": "/notes/2600991 "}, {"RefNumber": "2592627", "RefComponent": "LO-RFM-PUR-RRP", "RefTitle": "RRP: performance of MARC access", "RefUrl": "/notes/2592627 "}, {"RefNumber": "2569435", "RefComponent": "MM-IM-GF", "RefTitle": "Remove content from obsolete tables in MM-IM: Save DB memory and remove data that is not covered by Information Lifecycle Management", "RefUrl": "/notes/2569435 "}, {"RefNumber": "2493434", "RefComponent": "MM-IM-GF-MIG", "RefTitle": "Restriction note for inventory management data conversion", "RefUrl": "/notes/2493434 "}, {"RefNumber": "2425478", "RefComponent": "MM-IM-GF", "RefTitle": "Proxy substitution: Reset to SAP delivered proxy", "RefUrl": "/notes/2425478 "}, {"RefNumber": "2345668", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Resolve findings of ERP IS-OIL S/4HANA pre checks", "RefUrl": "/notes/2345668 "}, {"RefNumber": "2319579", "RefComponent": "MM-IM-GF", "RefTitle": "S4TWL - Performance optimizations within Material Document Processing - lock behavior and stock underrun protection", "RefUrl": "/notes/2319579 "}, {"RefNumber": "2236753", "RefComponent": "MM-IM-GF-MIG", "RefTitle": "S/4HANA MM-IM migration: error, post-processing and info messages", "RefUrl": "/notes/2236753 "}, {"RefNumber": "2281657", "RefComponent": "MM-IM-GF-MIG", "RefTitle": "S/4HANA MM-IM migration by SUM with sMIG feature", "RefUrl": "/notes/2281657 "}, {"RefNumber": "2270451", "RefComponent": "MM-IM-GF", "RefTitle": "MM-IM S/4HANA  customer fields in MKPF or MSEG are not updated with MB_CHANGE_DOCUMENT into MATDOC", "RefUrl": "/notes/2270451 "}, {"RefNumber": "2259038", "RefComponent": "MM-IM-GF-MIG", "RefTitle": "S/4HANA: Partitioning of table MATDOC", "RefUrl": "/notes/2259038 "}, {"RefNumber": "2249800", "RefComponent": "MM-IM-GF-MIG", "RefTitle": "MM-IM: Number of table entries to be migrated to S/4HANA", "RefUrl": "/notes/2249800 "}, {"RefNumber": "2246602", "RefComponent": "MM-IM-GF", "RefTitle": "Precompacting scheduling in case system performance gets slowed down during a posting period", "RefUrl": "/notes/2246602 "}, {"RefNumber": "2197392", "RefComponent": "MM-IM-GF", "RefTitle": "Resolve findings of core ERP MM-IM S/4HANA pre checks", "RefUrl": "/notes/2197392 "}, {"RefNumber": "2242679", "RefComponent": "MM-IM-GF", "RefTitle": "Redirect inconsistency - Proxy Substitution", "RefUrl": "/notes/2242679 "}, {"RefNumber": "2238690", "RefComponent": "MM-IM-GF-MIG", "RefTitle": "S/4HANA MM-IM migration by SUM", "RefUrl": "/notes/2238690 "}, {"RefNumber": "2197379", "RefComponent": "IS-ADEC-SSP", "RefTitle": "Resolve findings of DIMP specific MM-IM S/4HANA pre checks", "RefUrl": "/notes/2197379 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}