{"Request": {"Number": "1778939", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 665, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010511482017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001778939?language=E&token=14CCE6485C4201E139870508BA228F5A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001778939", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001778939/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1778939"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.06.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BW-PLA-IP-PF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Planning Functions and Planning Sequences"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning", "value": "BW-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integrated Planning", "value": "BW-PLA-IP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning Functions and Planning Sequences", "value": "BW-PLA-IP-PF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP-PF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1778939 - SAP HANA planning functions: Further developments"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note implements the planning function type for deleting invalid combinations in SAP HANA. In addition, all checks at SAP HANA level are moved to the latest time possible (before the data is written to the delta buffer).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Deferred checks</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Advance development<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <UL><LI>SAP NetWeaver BW 7.30<br /><br />Import Support Package 9 for SAP NetWeaver BW 7.30 (SAPKW73009) into your BW system. The Support Package is available when <B>SAP Note 1750249</B> \"SAPBWNews NW 7.30 BW ABAP SP9\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.31 (SAP NW BW 7.0 Enhancement Package 3)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 7 for SAP NetWeaver BW7. 31 (SAPKW73107) into your BW system. The Support Package is available when SAP Note 1782744 with the short text \"SAPBWNews NW BW7.31/7.03 ABAP SP7\", which describes this Support Package in more detail, is released for customers.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><B>You must first read SAP Note 875986, which provides information about transaction SNOTE.</B><br /><br />To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note still contains the words \"Preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D003259)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025308)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001778939/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001778939/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1793573", "RefComponent": "CRM-ANA-IMP-BI", "RefTitle": "TPM In-Memory Planning: Distribute values planning function", "RefUrl": "/notes/1793573"}, {"RefNumber": "1770553", "RefComponent": "BW-PLA-IP", "RefTitle": "PAK: Hana optimized planning notes in BW 7.30 SP9", "RefUrl": "/notes/1770553"}, {"RefNumber": "1753103", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 06", "RefUrl": "/notes/1753103"}, {"RefNumber": "1750249", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 09", "RefUrl": "/notes/1750249"}, {"RefNumber": "1741512", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 13", "RefUrl": "/notes/1741512"}, {"RefNumber": "1734666", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP11", "RefUrl": "/notes/1734666"}, {"RefNumber": "1732064", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 13", "RefUrl": "/notes/1732064"}, {"RefNumber": "1726731", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 30", "RefUrl": "/notes/1726731"}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199 "}, {"RefNumber": "1753103", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 06", "RefUrl": "/notes/1753103 "}, {"RefNumber": "1750249", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 09", "RefUrl": "/notes/1750249 "}, {"RefNumber": "1726731", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 30", "RefUrl": "/notes/1726731 "}, {"RefNumber": "1770553", "RefComponent": "BW-PLA-IP", "RefTitle": "PAK: Hana optimized planning notes in BW 7.30 SP9", "RefUrl": "/notes/1770553 "}, {"RefNumber": "1732064", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 13", "RefUrl": "/notes/1732064 "}, {"RefNumber": "1793573", "RefComponent": "CRM-ANA-IMP-BI", "RefTitle": "TPM In-Memory Planning: Distribute values planning function", "RefUrl": "/notes/1793573 "}, {"RefNumber": "1741512", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 13", "RefUrl": "/notes/1741512 "}, {"RefNumber": "1734666", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP11", "RefUrl": "/notes/1734666 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73009", "URL": "/supportpackage/SAPKW73009"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73107", "URL": "/supportpackage/SAPKW73107"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 4, "URL": "/corrins/0001778939/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Business Inform...|<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW73105 - SAPKW73106&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW73008 - SAPKW73008&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Use transaction SE91 to create message 007 in the message class RSPLF with the following text:<br/>\"Generated data is incorrect\".<br/>Set the \"Self-Explanatory\" indicator for the message.<br/><br/>---------------------------------------------------------------<br/>---------------------------------------------------------------<br/><br/>Use transaction SE91 to create message 000 in the message class RSPLS_CR with the following text:<br/>'&amp;1 &amp;2 &amp;3 &amp;4'.<br/>Set the \"Self-Explanatory\" indicator for the message.<br/><br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 28, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1745902 ", "URL": "/notes/1745902 ", "Title": "SAP HANA: Distributing with reference data", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1749178 ", "URL": "/notes/1749178 ", "Title": "SAP HANA: Expanding time intervals during reposting", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1751087 ", "URL": "/notes/1751087 ", "Title": "SAP HANA: Planning functions", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1753546 ", "URL": "/notes/1753546 ", "Title": "BW-IP: In-Memory planning: MP PartProvider projection", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1754635 ", "URL": "/notes/1754635 ", "Title": "BW-IP on HANA: Access planning engine version information", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1754671 ", "URL": "/notes/1754671 ", "Title": "BW-IP Performance: Prevent data access in CR implementation", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1755263 ", "URL": "/notes/1755263 ", "Title": "BW-IP: InMemory planning - preparation for buffer WRITE", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1755754 ", "URL": "/notes/1755754 ", "Title": "BW-IP Support: Log name of running FOX script", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1757535 ", "URL": "/notes/1757535 ", "Title": "BW-IP: InMemory Planung - Delivery w/ complete SID and KEY", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1760997 ", "URL": "/notes/1760997 ", "Title": "BW-IP: In-memory planning - Reference distribution (query)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1765262 ", "URL": "/notes/1765262 ", "Title": "Planning function error in SAP HANA", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1765946 ", "URL": "/notes/1765946 ", "Title": "BW-IP: InMemory planning - preparation for buffer WRITE (II)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1766980 ", "URL": "/notes/1766980 ", "Title": "BW-IP: In-memory planning - Reference distribution (query) 2", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1767098 ", "URL": "/notes/1767098 ", "Title": "Performance of planning functions", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1767530 ", "URL": "/notes/1767530 ", "Title": "BW-IP Performance: Optimization in CR_DERIVE", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1768610 ", "URL": "/notes/1768610 ", "Title": "BW-IP Performance: Optimization in CR_DERIVE (II)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1769684 ", "URL": "/notes/1769684 ", "Title": "BW-IP (PAK): Usage of keyfigures with even number of digits", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1770881 ", "URL": "/notes/1770881 ", "Title": "BW-IP (PAK): Strange error message in CR_CONTROLLER", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1771414 ", "URL": "/notes/1771414 ", "Title": "Planning functions: SAP HANA", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1773252 ", "URL": "/notes/1773252 ", "Title": "BW-IP performance (PAK): Infrastructure for deferred checks", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1774550 ", "URL": "/notes/1774550 ", "Title": "BW-IP (PAK): Lifecycle management of PE sessions in HANA", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1776135 ", "URL": "/notes/1776135 ", "Title": "HANA: Planning functions template sessions", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1776711 ", "URL": "/notes/1776711 ", "Title": "SAP HANA planning functions \"Distribute with Reference Data\"", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1778939 ", "URL": "/notes/1778939 ", "Title": "SAP HANA planning functions: Further developments", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1784628 ", "URL": "/notes/1784628 ", "Title": "BW-IP: Generating FOX for distribution acc. to ref. data", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1790754 ", "URL": "/notes/1790754 ", "Title": "BW-IP: In-memory planning disagg.-Char. rel., set values", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1791673 ", "URL": "/notes/1791673 ", "Title": "BW-IP:In-memory plng disaggregation-KID incomplete drilldown", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1795480 ", "URL": "/notes/1795480 ", "Title": "BW-IP: In-memory planning disaggr.- Missing char. derivation", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1751087 ", "URL": "/notes/1751087 ", "Title": "SAP HANA: Planning functions", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1753546 ", "URL": "/notes/1753546 ", "Title": "BW-IP: In-Memory planning: MP PartProvider projection", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1754635 ", "URL": "/notes/1754635 ", "Title": "BW-IP on HANA: Access planning engine version information", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1754671 ", "URL": "/notes/1754671 ", "Title": "BW-IP Performance: Prevent data access in CR implementation", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1755263 ", "URL": "/notes/1755263 ", "Title": "BW-IP: InMemory planning - preparation for buffer WRITE", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1755754 ", "URL": "/notes/1755754 ", "Title": "BW-IP Support: Log name of running FOX script", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1757535 ", "URL": "/notes/1757535 ", "Title": "BW-IP: InMemory Planung - Delivery w/ complete SID and KEY", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1760997 ", "URL": "/notes/1760997 ", "Title": "BW-IP: In-memory planning - Reference distribution (query)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1765262 ", "URL": "/notes/1765262 ", "Title": "Planning function error in SAP HANA", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1765946 ", "URL": "/notes/1765946 ", "Title": "BW-IP: InMemory planning - preparation for buffer WRITE (II)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1766980 ", "URL": "/notes/1766980 ", "Title": "BW-IP: In-memory planning - Reference distribution (query) 2", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1767098 ", "URL": "/notes/1767098 ", "Title": "Performance of planning functions", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1767530 ", "URL": "/notes/1767530 ", "Title": "BW-IP Performance: Optimization in CR_DERIVE", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1768610 ", "URL": "/notes/1768610 ", "Title": "BW-IP Performance: Optimization in CR_DERIVE (II)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1769684 ", "URL": "/notes/1769684 ", "Title": "BW-IP (PAK): Usage of keyfigures with even number of digits", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1770881 ", "URL": "/notes/1770881 ", "Title": "BW-IP (PAK): Strange error message in CR_CONTROLLER", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1771414 ", "URL": "/notes/1771414 ", "Title": "Planning functions: SAP HANA", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1773252 ", "URL": "/notes/1773252 ", "Title": "BW-IP performance (PAK): Infrastructure for deferred checks", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1774550 ", "URL": "/notes/1774550 ", "Title": "BW-IP (PAK): Lifecycle management of PE sessions in HANA", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1776135 ", "URL": "/notes/1776135 ", "Title": "HANA: Planning functions template sessions", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1776711 ", "URL": "/notes/1776711 ", "Title": "SAP HANA planning functions \"Distribute with Reference Data\"", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1778939 ", "URL": "/notes/1778939 ", "Title": "SAP HANA planning functions: Further developments", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1790754 ", "URL": "/notes/1790754 ", "Title": "BW-IP: In-memory planning disagg.-Char. rel., set values", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1791673 ", "URL": "/notes/1791673 ", "Title": "BW-IP:In-memory plng disaggregation-KID incomplete drilldown", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1795480 ", "URL": "/notes/1795480 ", "Title": "BW-IP: In-memory planning disaggr.- Missing char. derivation", "Component": "BW-PLA-IP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1800008", "RefTitle": "BUILD_PROLOGUE assign length 0", "RefUrl": "/notes/0001800008"}]}}}}}