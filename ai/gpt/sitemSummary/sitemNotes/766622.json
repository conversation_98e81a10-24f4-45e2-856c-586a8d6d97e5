{"Request": {"Number": "766622", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 537, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004193172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000766622?language=E&token=7DE728579990EB0848EA9B9E73ECFD8F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000766622", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000766622/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "766622"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-MM-IM"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Inventory Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Materials Management", "value": "BW-BCT-MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Inventory Management", "value": "BW-BCT-MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "766622 - Log file for delta extraction, application 03"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the Customizing Cockpit for the LO data extraction (LBWE), the delta update for the DataSources of inventory controlling (2LIS_03_BF, 2LIS_03_UM) is active. Despite this, documents that are updated in the R/3 system are not transferred to BW.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MCEXLOG, 2LIS_03_BF, 2LIS_03_UM, delta, MCEX_UPDATE_CALL_03, RMBWV303,<br />MCEX_UPDATE_03, MCEXLOG_03BF0, MCEXLOG_03UM0, log file, logging<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have ensured that no entries have been deleted from the RSA7 delta queue, the extraction queue (LBWQ) or the update program (SM13). The problem cannot be replicated, and you cannot determine why the documents were not transferred to BW.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note provides a logging function.<br /><br />The documents are entered as follows:</p> <UL><UL><LI>Directly after posting the application</LI></UL></UL> <UL><UL><LI>After the collective run picked up the documents from LBWQ or SM13<br /></LI></UL></UL> <p>The fields that are logged are defined in the MCEXLOG_03BF0 and MCEXLOG_03UM0 data structures. The data is stored in the MCEXLOG cluster table. You can use the RMCEXLOG report to display the log entries.<br /></p> <b>Carry out the following steps in the R/3 system:</b><br /> <OL>1. Create the MCEXLOG_03BF0 structure with the short description 'Log file structure for MCEX03BF'.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use the following components: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Component&#x00A0;&#x00A0;Component type<br />----------&#x00A0;&#x00A0;--------------<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;MCEXLOG_INFO<br />MBLNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MBLNR<br />MJAHR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MJAHR<br />BUDAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BUDAT<br />ZEILE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MBLPO<br />MATNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MATNR<br />WERKS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WERKS_D<br />LGORT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; LGORT_D<br />CHARG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CHARG_D<br /> <OL>2. Create the MCEXLOG_03UM0 structure with the short description 'Log file structure for MCEX03UM'.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use the following components: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Component&#x00A0;&#x00A0;Component type<br />----------&#x00A0;&#x00A0;--------------<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;MCEXLOG_INFO<br />BELNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BELNR_D<br />GJAHR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GJAHR<br />BLDAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BLDAT<br />BUDAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BUDAT<br />BUKRS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BUKRS<br />BWKEY&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BWKEY<br />MATNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MATNR<br />SAKTO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAKNR<br />NOPOS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MC_NOPOS<br /> <OL>3. Create the LMCEXF10 include with the type 'I - Include program' and the short text 'Include 'LMCEXF10' in the MCEX package and the application 'S - Basis (System)'. Implement the attached correction instructions.<br /></OL> <OL>4. Activate the logging function.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The logging function is activated or deactivated using an entry in the TMCEXLOG table.<br />Call Transaction SE16 and select 'Create Entries'. Enter the following values: <UL><UL><LI>MCAPP = 03 (Inventory Controlling)</LI></UL></UL> <UL><UL><LI>ACTIVE_DIA = X<br />if the documents are to be logged in dialog mode</LI></UL></UL> <UL><UL><LI>ACTIVE_UPD = X<br />if the collective run is to be logged</LI></UL></UL> <UL><UL><LI>KEEP = [1, 95]<br />Number of days after which the log files are deleted</LI></UL></UL> <UL><UL><LI>NEXT_REORG = Current date<br /><br /></LI></UL></UL> <b>Using the RMCEXLOG display report</b><br /> <p>Use Transaction SE38 to implement the RMCEXLOG report. You have the following selection options:</p> <UL><UL><LI>P_MCAPP&#x00A0;&#x00A0; Application</LI></UL></UL> <UL><UL><LI>P_PHASE&#x00A0;&#x00A0; Phase of the extraction process</LI></UL></UL> <UL><UL><LI>S_DATUM&#x00A0;&#x00A0; Log date</LI></UL></UL> <UL><UL><LI>S_UZEIT&#x00A0;&#x00A0; Log time<br /></LI></UL></UL> <p>Enter the application and the phase.<br /><br />For P_PHASE = 'D' (Dialog), the documents that are written by the corresponding transaction to the extraction queue or the update tables are displayed.<br />For P_PHASE = 'U' (Update), the documents that are transferred from the RMBWV303 collective run to the delta queue are displayed.<br />P_PHASE = 'E' (Delete logs) deletes the selected entries from the MCEXLOG table.<br /><br />After you execute (F8), the system displays an overview of the extract structures for which documents were logged. Click the extract structure to go to the document detail view.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D041263"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D041328)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000766622/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000766622/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766622/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766622/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766622/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766622/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766622/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766622/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766622/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "745788", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Non-cumulative mgmnt in BW: Verifying and correcting data", "RefUrl": "/notes/745788"}, {"RefNumber": "1169053", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Activating the extraction log file - Application 03", "RefUrl": "/notes/1169053"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1169053", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Activating the extraction log file - Application 03", "RefUrl": "/notes/1169053 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "745788", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Non-cumulative mgmnt in BW: Verifying and correcting data", "RefUrl": "/notes/745788 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI", "From": "2003_1_40B", "To": "2003_1_470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2004_1_40B", "To": "2004_1_500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI 2004_1_46C", "SupportPackage": "SAPKIPZI4A", "URL": "/supportpackage/SAPKIPZI4A"}, {"SoftwareComponentVersion": "PI 2004_1_500", "SupportPackage": "SAPKIPZI67", "URL": "/supportpackage/SAPKIPZI67"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "PI", "NumberOfCorrin": 5, "URL": "/corrins/0000766622/48"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}