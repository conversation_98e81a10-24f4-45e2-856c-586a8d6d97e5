{"Request": {"Number": "1572951", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1512, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009331182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001572951?language=E&token=D741CF36FFCBFA22D278612C00864D37"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001572951", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001572951/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1572951"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.03.2011"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1572951 - Dump DATASET_CANT_OPEN in RTCCTOOL"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Dump DATASET_CANT_OPEN<br />Unable to open file \"&lt;DIR_INST&gt;/SAP_AGS_OLCNT_VERIFY.pse\"<br />when executing RTCCTOOL or during SDCC download.<br />Termination occurs at statement<br />&#x00A0;&#x00A0;READ DATASET LF_PSE_FILEPATH<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INTO LT_PSE_FILE-BINDATA LENGTH LF_LEN.<br />in Form NXS_CHECK_N_UPDATE_SAPAGS_PSE.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RTCCTOOL, Service preparation check, DATASET_CANT_OPEN</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>ST-A/PI 01N* AND inconsistency exists that PSE File SAP_AGS_OLCNT_VERIFY.pse is missing in file system but exists in database</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The inconsistency that PSE SAP_AGS_OLCNT_VERIFY.pse exists in database tables but not in file system should normally not occur. In higher SAP releases even all PSE files in database are written to file system during every system restart. So this error is should not occur very often.<br /><br />To solve the error either:<br /><br />i) Implement the attached correction instruction using SNOTE.<br /><br /> or<br /><br />ii) [Manual workaround with debugger:<br />Set a breakpoint into report /SSA/NXS form NXS_CHECK_N_UPDATE_SAPAGS_PSE line 1312:<br />~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br />* SSFPSE_STORE FORM READPSE, SSFPSE_DOWNLOAD: read PSE from file system<br />&#x00A0;&#x00A0;CLEAR LF_LEN_FILE.<br />&#x00A0;&#x00A0;REFRESH LT_PSE_FILE.<br />&#x00A0;&#x00A0;CATCH SYSTEM-EXCEPTIONS OPEN_DATASET_NO_AUTHORITY = 266.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OPEN DATASET LF_PSE_FILEPATH IN BINARY MODE FOR INPUT.<br />&#x00A0;&#x00A0;ENDCATCH.<br />&#x00A0;&#x00A0;IF SY-SUBRC = 266.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;breakpoint here after the endcatch<br />~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br />At this breakpoint manually change sy-subrc to 4.<br />Then the program will avoid the short dump and will recreate the missing PSE file from the PSE content in database.]<br /><br />The error will be solved with the next supportpackage 1 for ST-A/PI 01N. Choose \"Reset to standard\" when you implement the supportpackage or a higher release.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027971)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031877)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001572951/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001572951/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001572951/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001572951/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001572951/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001572951/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001572951/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001572951/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001572951/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-A/PI", "From": "01N_46CBCO", "To": "01N_46CBCO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_46DBCO", "To": "01N_46DBCO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_610BCO", "To": "01N_610BCO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_620BCO", "To": "01N_620BCO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_640BCO", "To": "01N_640BCO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_46C_R3", "To": "01N_46C_R3", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_620_R3", "To": "01N_620_R3", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_46CAPO", "To": "01N_46CAPO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_46DAPO", "To": "01N_46DAPO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_620SCM", "To": "01N_620SCM", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_610CRM", "To": "01N_610CRM", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_620CR3", "To": "01N_620CR3", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_620CR4", "To": "01N_620CR4", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_640SCM", "To": "01N_640SCM", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_640ECC", "To": "01N_640ECC", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_700BCO", "To": "01N_700BCO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_700ECC", "To": "01N_700ECC", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_700SCM", "To": "01N_700SCM", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_710BCO", "To": "01N_710BCO", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_700CRM", "To": "01N_700CRM", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01N_700SOL", "To": "01N_700SOL", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-A/PI", "NumberOfCorrin": 1, "URL": "/corrins/0001572951/389"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}