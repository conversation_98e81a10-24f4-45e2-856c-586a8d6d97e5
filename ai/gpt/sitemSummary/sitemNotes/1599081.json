{"Request": {"Number": "1599081", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 344, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017259902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001599081?language=E&token=59BB6436067507E7756C248631BFBC07"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001599081", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001599081/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1599081"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AF-ARO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Asset Retirement Obligation Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Additional Functions", "value": "FI-AF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Retirement Obligation Management", "value": "FI-AF-ARO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AF-ARO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1599081 - FOM 604:Installation on SAP ECC 6.0 EHP4/5/6/7/8"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use transaction SAINT to install an add-on on Release SAP ERP Central Component ECC 600 EHP4 (referred to here as SAP ECC 600 EHP4).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAINT, add-on,Financial Obligation Management,PAM ,FOM 604 , ARO,SAPK-604AGINFOM, CD51041255 ,Enhancement Package,SAINT,ACP, FOM=======604</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note contains information about add-on installation using transaction SAINT<br />The technical name of SAP Asset Retirement Obligation Management is FOM 604. Therefore, the name FOM 604 is used below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note is updated on a regular basis. Make sure you have the current version of this note before you start the installation.<br /><br />Contents<br />1.Change history<br />2.Prerequisites for installing the Add-On FOM 604<br />3.Preparing the FOM 604 installation<br />4.Executing the FOM 604 installation<br />5.After you have installed FOM 604<br />6.Language support<br />7.Password<br /><br /><strong>1. Change history</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Date</td>\r\n<td>Topic</td>\r\n<td>Short Description</td>\r\n</tr>\r\n<tr>\r\n<td>29.06.2011</td>\r\n<td>Installation</td>\r\n<td>FOM 604 AOI</td>\r\n</tr>\r\n<tr>\r\n<td>22.07.2011</td>\r\n<td>&nbsp;</td>\r\n<td>ACP Created for EHP5</td>\r\n</tr>\r\n<tr>\r\n<td>03.10.2012&#160;</td>\r\n<td>&nbsp;</td>\r\n<td>ACP Created for EHP6</td>\r\n</tr>\r\n<tr>\r\n<td>17.10.2013&#160;</td>\r\n<td>&nbsp;</td>\r\n<td>ACP Created for EHP7</td>\r\n</tr>\r\n<tr>\r\n<td>23.05.2016</td>\r\n<td>&nbsp;</td>\r\n<td>ACP Created for EHP8</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><br /><strong>2. Prerequisites for installing the FOM 604</strong><br /><br />It is not possible to uninstall FOM.Before you install the FOM,keep in mind that you cannot uninstall ABAP add-ons.Further restrictions that concern the upgrade and maintenance of your SAP system and that occur as a result of installing an add-on are described in Release Strategy Note 1599156<br /><br /><strong>It is also now possible to install the Add-On FOM 604 on EHP5/6/7</strong> <strong>For this you must download the&#160;&#160;FOM=======604 from service market place</strong><br /><strong>FOM 604&#160;&#160;can now be installed on a fresh EHP5/6 system.For</strong> <strong>this reason, you must download an attribute change package (ACP) in </strong><strong>addition to the installation package of FOM 604.</strong> <strong>The new import conditions</strong> <strong>are shipped with this ACP. For more information about attribute change</strong> <strong>packages, see Note 1119856.</strong><br /><br /><strong>-&#160;&#160;To install the add-on with new conditions, proceed as follows:</strong><br /><strong>I.&#160;&#160;SPAM Version 25 is a minimum prerequisite for the processing of </strong><strong>ACPs.</strong><br /><strong>II. Download the file FOM=======604 from service market place</strong> <strong>https://service.sap.com/swdc -&gt;Software Downloads -&gt;SAP Software</strong> <strong>Distribution Center-&gt; Search for all Categories -&gt;FOM=======604</strong><br /><br /><strong>III.&#160;&#160;Please follow the instructions of note 1119856 to download the ACP file directly from Service Market Place.&#160;</strong><br /><br />Required release<br />You require SAP ERP Central Component 6.00 EHP4 or EHP5 or EHP6 or EHP7<br /><br />Import the latest SPAM/SAINT update<br />Make sure that you have installed the latest SPAM/SAINT update on your system. If a newer version is available on SAP Service Marketplace, import the new SPAM/SAINT update.<br /><br />Import the latest R3trans and tp. Ensure that you have imported the latest kernel version into your system. If a newer version is available on SAP Service Marketplace import the most recent kernel.<br /><br />Obtain the following notes before you begin the installation:<br />&#160;&#160; Add-ons: Conditions:&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;70228<br />&#160;&#160; Overview Note:&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1599155<br />&#160;&#160; Release strategy Note:&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1599156<br />&#160;&#160; AOP/CRT Note:&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1599082<br />&#160;&#160; Problems with transaction SAINT:&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;822380<br /><br />Prerequisites:<br />Check that your system meets the following prerequisites:<br /><br />I. Required Components and Support Packages on EHP4</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Component</td>\r\n<td>Release</td>\r\n<td>Support Package</td>\r\n</tr>\r\n<tr>\r\n<td>EA-APPL</td>\r\n<td>604</td>\r\n<td>SAPK-60403INEAAPPL</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>701</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>701</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />OR<br /><br />II. Required Components and Support Packages on EHP5</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Component</td>\r\n<td>Release</td>\r\n<td>Support Package</td>\r\n</tr>\r\n<tr>\r\n<td>EA-APPL</td>\r\n<td>605</td>\r\n<td>SAPSAPK-60503INEAAPPL</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>702</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>702</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><br />OR<br /><br />III. Required Components and Support Packages on EHP6</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Component</td>\r\n<td>Release</td>\r\n<td>Support Package</td>\r\n</tr>\r\n<tr>\r\n<td>EA-APPL</td>\r\n<td>606</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_ABA</td>\r\n<td>731</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>731</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />OR<br /><br />IV. Required Components and Support Packages on EHP7</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Component</td>\r\n<td>Release</td>\r\n<td>Support Package</td>\r\n</tr>\r\n<tr>\r\n<td>EA-APPL</td>\r\n<td>617</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>EA-FIN</td>\r\n<td>617</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Note (Relevant for EHP7): SAP ARO is integrated in SAP Financial Accounting. Technically the integration is processed via the Lease Accounting Engine. This engine currently supports the integration into General Ledger, Accrual Engine and SAP FI Asset Accounting. ERP60 EHP7 includes the new business function FIN_AA_PARALLEL_VAL, which is related to SAP FI Asset Accounting. The Lease Accounting Engine currently does not support this new business function.</strong><br /><strong>Hence if you are planning to use both SAP ARO and the new business function FIN_AA_PARALLEL_VAL, you cannot use the automatic integration of SAP ARO into SAP FI Asset Accounting. If you need to use the automatic integration into SAP FI Asset Accounting, you should use the existing functionality of SAP FI Asset Accounting without activating the business function FIN_AA_PARALLEL_VAL</strong></p>\r\n<p>OR<br /><br />V. Required Components and Support Packages on EHP8</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Component</td>\r\n<td>Release</td>\r\n<td>Support Package</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_APPL</td>\r\n<td>617</td>\r\n<td>SAPK-61801INSAPAPPL</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_FIN</td>\r\n<td>617</td>\r\n<td>SAPK-61801INSAPFIN</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>Note (Relevant for EHP8): Customers which decide to use SAP ARO in an ERP60 EHP8 system should ensure that <span style=\"text-decoration: underline;\">note 2264146 </span>and<span style=\"text-decoration: underline;\"> note 2287844</span> are implemented. Both notes are relevant for &#8222;one time postings&#8220; which are triggered via SAP ARO.Both notes are part of SAP_APPL Release 618 Support Package 2.</strong><br /><br /><br />If you have not yet installed these component Support Packages, you can include them when you install the FOM 604. For more information, see Note 83458.<br /><br />Additional Component Support Packages<br />The add-on FOM 604 does not contain modifications. You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section 'Required Components and Support Packages'.<br /><br />Additional information about the installation<br />CD material number add-on installation&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CD51041255<br /><br />o SAP Enterprise Extensions activation switch. The FOM 604 add-on requires the Enterprise Extension(s) EA-FIN.If you have not yet activated them, you must do so now. For more information, see Note 1049251..<br /><br /><strong>3 Preparing the FOM 604 Installation</strong><br /><br />Making the add-on FOM 604 available<br />The installation CD for FOM 604 is not automatically sent to all customers. Request the CD with material number CD51041255 from your local subsidiary or download the CD from SAP Service Marketplace.You find the CD in the SAP Service Marketplace:<br /><br />&#160;&#160; https://service.sap.com/swdc<br />&#160;&#160; -&gt; Download<br />&#160;&#160;&#160;&#160;-&gt; Installation &amp; Upgrade<br />&#160;&#160;&#160;&#160; -&gt;Browse our Download Catalog<br />&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Industry-specific Component<br />&#160;&#160;&#160;&#160;&#160;&#160; -&gt; SAP ASSET RETIRE OBLIG MGT<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;-&gt; SAP ASSET RETIRE OBLIG MGT 1.0<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Installation<br /><br />&#160;&#160;Log on as user:<br />&#160;&#160;&lt;sid&gt;adm on UNIX<br />&#160;&#160;&lt;SID&gt;OFR on AS/400<br />&#160;&#160;&lt;SID&gt;adm on Windows<br /><br />Switch to the &lt;DIR_EPS_ROOT&gt; directory of your SAP system (usually /usr/sap/trans/EPS). The &lt;DIR_EPS_ROOT&gt; directory is also displayed under DIR_EPS_ROOT after you execute the RSPFPAR report Go to the higher-level directory of &lt;DIR_EPS_ROOT&gt;. Unpack the SAR archive K-604AGINFOM on the CD with the following statement:<br /><br />SAPCAR -xvf /&lt;CD_DIR&gt;/DATA/K-604AGINFOM.SAR&#160;&#160;&#160;&#160;&#160;&#160;on UNIX<br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/DATA/K-604AGINFOM.SAR'&#160;&#160;on AS/400<br />SAPCAR -xvf &lt;CD_DRIVE&gt;:\\DATA\\K-604AGINFOM.SAR&#160;&#160;&#160;&#160;on Windows<br /><br />The CSN0120061532_0054284.PAT file should now be in the&lt;DIR_EPS_ROOT&gt;/in directory.<br /><br /><strong>4 Executing the FOM 604 installation</strong><br /><br />SAINT for installation of FOM 604<br /><br />User to be used<br />Log on to your SAP system in client 000 as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.<br /><br />Displaying the add-on installation package Call transaction SAINT and choose 'Start' and 'Load'. After the list of uploaded packages is displayed, you can return to the initial screen of transaction SAINT by choosing F3 or 'Back'.<br /><br />Starting the installation<br />Call transaction SAINT and choose 'Start'. Select the add-on FOM 604 and choose 'Continue'. If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the installation package, and it may also contain Support Packages and Add-On Support Packages. To start the installation process, choose 'Continue'. For more information,call transaction SAINT and choose 'Info' on the application toolbar. The system prompts you to enter a password. This password is provided below.<br /><br /><strong>5 After you have installed the FOM 604</strong><br /><br />The Documents for FOM 604 are available in English&#160;&#160;under the following link:<br />http://service.sap.com/swdc<br />&#160;&#160;&#160;&#160; &gt; Download<br />&#160;&#160;&#160;&#160; -&gt; Installation &amp; Upgrades<br />&#160;&#160;&#160;&#160;&#160;&#160; -&gt;Browse our Download Catalog<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Industry-specific Component<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Financial Obligation MGMT<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; SAP ASSET RETIRE OBLIG MGT 1.0<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; Installation<br /><br /><br />Importing Support Packages after the installation<br /><br />Importing Support Packages after the installation:<br />The FOM 604 add-on does not modify objects of any other component. So the additional support packages of other components can be imported without any such restrictions.<br /><br />Generation errors<br />While installing the Add-On EHP5 you may get the below mentioned generation error<br /><strong>Program /FOM/FIEHLIST, Include FIEHLO01: Syntax error in line 000019</strong> <strong>The FORM 'INIT_ALV_TREE' does not exist</strong><br /><strong>Note 1610290 has to be applied to resolve the generation error</strong><br /><br />Delivery Customizing<br />Delivery Customizing is imported into client 000 and may need to be copied to other clients. For more information, see Note 337623.<br /><br /><strong>6. Language support</strong><br />FOM 604 supports English, French, German and Russian.<br /><br />All the language-dependent parts of FOM are contained in the installation package of the add-on. If the relevant standard language exists in the system when you import FOM, the language part of the add-on is automatically imported. You do not need to import a language transport separately.<br /><br />If you import a new standard language into your system after installing the FOM , you must manually ensure that the corresponding language-dependent part of the add-on is imported. See Note 195442 for further information.<br /><br /><strong>7. Password</strong><br />During the installation, the system prompts you to enter a password. This password is: 5A91F9B256</p>\r\n<p>&#160;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I055836)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D065489)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001599081/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599081/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1599156", "RefComponent": "FI-AF-ARO", "RefTitle": "Release Startegy:SAP Asset Retirement Obligation Management", "RefUrl": "/notes/1599156"}, {"RefNumber": "1599155", "RefComponent": "FI-AF-ARO", "RefTitle": "SAP Asset Retirement Obligation Management :Overview Note", "RefUrl": "/notes/1599155"}, {"RefNumber": "1599082", "RefComponent": "FI-AF-ARO", "RefTitle": "FOM 604: Add-On Support Packages", "RefUrl": "/notes/1599082"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2148799", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information for components of SAP for Utilities together with SAP Simple Finance, on-premise edition 1503 or SAP S/4HANA Finance 1605", "RefUrl": "/notes/2148799 "}, {"RefNumber": "2050021", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information for components of SAP for Utilities together with Financials add-on for SAP Business Suite powered by SAP HANA 1.0 SP01", "RefUrl": "/notes/2050021 "}, {"RefNumber": "1599156", "RefComponent": "FI-AF-ARO", "RefTitle": "Release Startegy:SAP Asset Retirement Obligation Management", "RefUrl": "/notes/1599156 "}, {"RefNumber": "1599082", "RefComponent": "FI-AF-ARO", "RefTitle": "FOM 604: Add-On Support Packages", "RefUrl": "/notes/1599082 "}, {"RefNumber": "1599155", "RefComponent": "FI-AF-ARO", "RefTitle": "SAP Asset Retirement Obligation Management :Overview Note", "RefUrl": "/notes/1599155 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FOM", "From": "604", "To": "604", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}