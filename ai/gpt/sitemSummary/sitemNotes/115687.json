{"Request": {"Number": "115687", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 257, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000115687?language=E&token=22924B3FDAFFB8846252491C44E7855E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000115687", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000115687/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "115687"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Colombia"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Colombia", "value": "XX-CSC-CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "115687 - Available reports, forms preliminary Colombia."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Preliminary availablity of reports and forms for Colombia</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Extended withholding tax reporting.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Usage of 4.0B in Colombia.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please import the following transport to have preliminary the following objects (which are standard in 4.5A) available. Be aware that if a<br />hotpackage was applied after applying this transport, it can change the<br />mentioned objects at new. In any case of syntax errors of the named<br />objects after applying hotpackages, import the transport at new.<br />Objects named are standard delivery up from 4.5A. ----------------------------------------------------------------------<BR/> 1.Report RFKQST20 (for Colombia) and includes KQST2001,KQST2003,<BR/> &#x00A0;&#x00A0;FIWT0010 and the reports RFKQST10,30,50,60 which are not needed<BR/> &#x00A0;&#x00A0;for Colombia but contain the one same subroutine PERFORM included<BR/> &#x00A0;&#x00A0;instead of GET_POSTING_TIME new: PERFORM READ_T059P TABLES X_T059P.<BR/> &#x00A0;&#x00A0;Extended withholding tax report with variant for country CO<BR/> &#x00A0;&#x00A0;is RFKQST20,<BR/> &#x00A0;&#x00A0;which calls the respective below mentioned forms.<BR/> 2.Report : ZFCOTAXNO (described below) for tax number import.<BR/> &#x00A0;&#x00A0;(in standard 4.5A will be named with RFIDFORM).<BR/> 3.Form F_RFKQST20_CO_01 (template withholding tax general Colombia)<BR/> 4.Form F_RFKQST20_CO_02 (template WT-certificate IVA Colombia)<br />5.Form F_RFKQST20_CO_03 (template WT-certificate ICA Colombia)&#x00A0;&#x00A0;(be aware that forms are client dependend objects, means if You need<br />&#x00A0;&#x00A0; them in Your respective client in a different layout, You should<br />&#x00A0;&#x00A0; copy them and adapt there. Per default all forms are readed from<br />&#x00A0;&#x00A0; client 000, if they don't exist in respective client.)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Transport should be taken from SAPSERV3 path :<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;general/R3server/abap/note.0115687<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and contains two files :<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;K900155.LD1 and R900155.LD1.<br />Please be aware that these programs could be changed by hotpackages<br />imported afterwards, if there arrise any inconsistencies please<br />return via the version management these imported versions or<br />apply the note again.<br /><br />Data Dictionary change to be carried out:<br />----------------------------------------------------------------------<br />Please add to the fixed values of domain VAR02_KQST the value CO<br />via transaction SE11.<br />----------------------------------------------------------------------<br />The forms a taking the address informations for company code from the<br />data within table T001 and Address files.<br />Full adddress is included as follows:/*&#x00A0;&#x00A0; ,,&lt;A2&gt;Company:&lt;/&gt;<BR/> instead of:<BR/> /:&#x00A0;&#x00A0; Direction emisor/receiver (company) to be assigned<BR/> /:&#x00A0;&#x00A0; &amp;ADDR1_VAL-TITLE&amp;<BR/> /:&#x00A0;&#x00A0; &amp;ADDR1_VAL-NAME1&amp; &amp;ADDR1_VAL-NAME2&amp; &amp;ADDR1_VAL-NAME3&amp; &amp;VALUE&amp;<BR/> /:&#x00A0;&#x00A0; &amp;ADDR1_VAL-STREET&amp; &amp;ADDR1_VAL-PO_BOX&amp; &amp;ADDR1_VAL-POST_CODE1&amp;<BR/> /:&#x00A0;&#x00A0; &amp;ADDR1_VAL-REGION&amp;<BR/> /:&#x00A0;&#x00A0; &amp;ADDR1_VAL-CITY1&amp; &amp;ADDR1_VAL-CITY2&amp;<BR/> /:&#x00A0;&#x00A0; &amp;ADDR1_VAL-COUNTRY&amp;<BR/> /:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; DEFINE &amp;FNAME&amp; = 'CO_NIT'<BR/> /*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PERFORM READ_COMPANY_ATTRIBUTE IN PROGRAM RFIDFORM \"from 4.5A<BR/> /*&#x00A0;&#x00A0; In 4.0A/B programa ZfCOTAXNO is used (note 115687)<BR/> /:&#x00A0;&#x00A0; PERFORM TAX_CO_NUMBER IN PROGRAM ZFCOTAXNO<BR/> /:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;USING&#x00A0;&#x00A0;&#x00A0;&#x00A0;&amp;T001F-BUKRS&amp;<BR/> /:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;USING&#x00A0;&#x00A0;&#x00A0;&#x00A0;&amp;FNAME&amp;<BR/> /:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CHANGING &amp;VALUE&amp;<BR/> /:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ENDPERFORM<br />A2&#x00A0;&#x00A0;,,&lt;A2&gt;NIT.&lt;/&gt;&amp;VALUE&amp;,,<br />Please import the preliminary available objects acording to note 13719.<br /><br />Be aware that the report RFKQST20 could be changed after importing this<br />transport via hotpackages for 4.0B , so after applying hotpackages<br />check the report and if inconsistencies ocure reactivate the version<br />You imported via this note or import this transport another time.<br /><br />Before You could use the templates of the above mentioned forms with<br />the named report RFKQST20, You should carry out the customizing:<br />IMG -&gt; Financial Accounting&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; General Ledger Accounting<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Accounting transactions -&gt; Closing -&gt; Reporting -&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Extended Withholding Tax: Reporting<br />Just pass through all the steps of that path and configure the report.<br /><br />To get to the selection screen of report RFKQST20 the country variants<br />shortage 'CO', You should add in data dictionary to domain VAR02_KQST<br />fixed value 'CO'.This could be done via transaction SE11.<br />The access to that object of data dictionary is limited via key: object<br />entry: R3TR DOMA VAR02_KQST with respective access permission needed.<br /><br />-----------------------------------------------------------------------<br />Explanations for usage of tax ID's for Your respective company code :<br />-----------------------------------------------------------------------<br />1.Please create in T001J an entry CO_NIT length 13 for country CO<br /><br />2.Maintain in T001Z or via transaction OBY6 these tax numbers for Your<br />&#x00A0;&#x00A0;respective company codes<br />3.Program to bring the tax numbers to withholding SAPscript forms :<br />As in 4.0A/B the handling for the tax numbers of the company code is<br />is not programmed (in 4.5A You will find for that purpose the report<br />RFIDFORM) to usage in SAPscript forms, please create a small program,<br />as for example : <BR/> * For Colombia :<BR/> report zfcotaxno .<BR/> <BR/> form tax_co_number tables input_table&#x00A0;&#x00A0;structure itcsy<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; output_table structure itcsy.<BR/> <BR/> &#x00A0;&#x00A0;tables: t001z, lfb1.<BR/> &#x00A0;&#x00A0;data zbukr like lfb1-bukrs.<BR/> <BR/> &#x00A0;&#x00A0;read table input_table index 1.<BR/> &#x00A0;&#x00A0;zbukr = input_table-value.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;if not ( zbukr is initial )<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;and t001z-party ne 'co_nit'.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;select single * from t001z<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;where bukrs eq zbukr<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and&#x00A0;&#x00A0; party eq 'CO_NIT'.<BR/> &#x00A0;&#x00A0;endif.<BR/> <BR/> &#x00A0;&#x00A0;read table output_table index 1.<BR/> &#x00A0;&#x00A0;output_table-value = t001z-paval.<BR/> &#x00A0;&#x00A0;modify output_table index 1.<BR/> <BR/> endform.<BR/> <BR/> 5. In the sample (template) SAPscript forms delivered the programs are<BR/> &#x00A0;&#x00A0; called to fill the field of the tax number<BR/> <BR/> Sample for Colombia: in form F_RFKQST20_CO_01 window sender left<BR/> text element:<BR/> <BR/> <BR/> BOX INTENSITY 0<BR/> BOX XPOS&#x00A0;&#x00A0;'0' MM YPOS&#x00A0;&#x00A0;'0' MM WIDTH '123' MM HEIGHT '15' MM FRAME 2 TW<BR/> ,,&lt;A2&gt;&amp;T001-BUTXT&amp;&lt;/&gt;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;DEFINE &amp;FNAME&amp; = 'CO_NIT'<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;PERFORM READ_COMPANY_ATTRIBUTE IN PROGRAM RFIDFORM \"from 4.5A<BR/> In 4.0A/B following own program ZfCOTAXNO has to be used (note 115687)<BR/> PERFORM TAX_CO_NUMBER IN PROGRAM ZfCOTAXNO<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; USING&#x00A0;&#x00A0;&#x00A0;&#x00A0;&amp;T001F-BUKRS&amp;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; USING&#x00A0;&#x00A0;&#x00A0;&#x00A0;&amp;FNAME&amp;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; CHANGING &amp;VALUE&amp;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDPERFORM<BR/> ,,&lt;A2&gt;NIT.&lt;/&gt;&amp;VALUE&amp;,,<BR/> <BR/> 6.Be aware that You setup in customizing : General Ledger -&gt;<BR/> &#x00A0;&#x00A0;business transactions -&gt; Closing -&gt; Report -&gt; Withholding tax<BR/> &#x00A0;&#x00A0;the necessary setup.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-XX (Please use FI-LOC-I18 for I18N and cross-country issues)"}, {"Key": "Responsible                                                                                         ", "Value": "I014343"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000115687/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "110344", "RefComponent": "MM-IV-LIV", "RefTitle": "Extended withhldng tax in logistic invoice verfcatn", "RefUrl": "/notes/110344"}, {"RefNumber": "104201", "RefComponent": "XX-CSC-CO", "RefTitle": "Sample Chart of accounts for Colombia", "RefUrl": "/notes/104201"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "110344", "RefComponent": "MM-IV-LIV", "RefTitle": "Extended withhldng tax in logistic invoice verfcatn", "RefUrl": "/notes/110344 "}, {"RefNumber": "104201", "RefComponent": "XX-CSC-CO", "RefTitle": "Sample Chart of accounts for Colombia", "RefUrl": "/notes/104201 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}