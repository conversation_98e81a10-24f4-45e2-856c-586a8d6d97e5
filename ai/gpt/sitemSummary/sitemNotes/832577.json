{"Request": {"Number": "832577", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 323, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015872892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000832577?language=E&token=4496A3E98C18E1CB749F217B4CF042FA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000832577", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000832577/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "832577"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.05.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-DIC-AC"}, "SAPComponentKeyText": {"_label": "Component", "value": "ABAP Dictionary Activation and Conversion"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Dictionary and ABAP CDS", "value": "BC-DWB-DIC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-DIC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Dictionary Activation and Conversion", "value": "BC-DWB-DIC-AC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-DIC-AC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "832577 - Runtime error on Unicode - inconsistent table types"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have activated Unicode in the system.<br />Transactions and programs terminate with runtime errors because some table types have an inconsistent runtime object in the line length.<br />You can check the runtime object under \"Utilities\" in transaction SE11.<br /><br />The following types of inconsistencies may occur:<br />Type 1:&#x00A0;&#x00A0;The line length in the runtime object is larger than the line length generated from DD.<br />Type 2:&#x00A0;&#x00A0;The line length in the runtime object is smaller than the line length generated from DD.<br /><br />The relevant line type itself is always consistent and always has the greater value than the table length in the runtime object.<br /><br />As of kernel 640 patch number 69, this inconsistency is caught by the ABAP_runtime environment with the DDIC_TYPES_INCONSISTENT exception:<br />\"Inconsistency between the DDIC types ... \"<br />An inconsistency was found when the dictionary description of the data type was accessed.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>TABLEN, DDIC_TYPES_INCONSISTENT, table type, inconsistent<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem may be caused by the following:<br /><br />Cause 1: The RUTTTYPACT program was not executed when converting the system to Unicode.<br />When you install a Unicode system or convert a system to Unicode, the entire name tab of the dictionary is also converted.<br />All table types then have an \"F1\" inconsistency.<br />This inconsistency is easily eliminated with the RUTTTYPACT program.<br /><br />If you perform imports to the Dictionary before executing the RUTTTYPACT program, the nametab of the still inconsistent table types in the line length may be falsified after certain changes (\"F2\" inconsistencies).<br />These table types can no longer be corrected with the RUTTTYPACT program.<br />The longer you wait with executing the RUTTTYPACT program or the more imports to the Dictionary you perform before running the program, the more inconsistent table types will exist.<br /><br />Cause 2: Problems during the mass activation of dictionary objects<br />If the system behavior is unfavorable, problems may occur during the mass activation of a very large number of objects (database problems, lock problems or memory problems).<br />Mass activation is executed in parallel and it is distributed across several tasks.<br />If one of these tasks terminates, only some of the table types are activated.<br />As a result, the table types are inconsistent with the structures used.<br /><br />This effect occurs, for example, when you apply the following patches in one import:<br />&#x00A0;&#x00A0;SAPK-50007INSCMBASIS&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SCM_BASIS<br />&#x00A0;&#x00A0;SAPK-50008INSCMBASIS&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SCM_BASIS<br />&#x00A0;&#x00A0;SAPKY50007&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SCM<br />&#x00A0;&#x00A0;SAPKY50008&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SCM<br />&#x00A0;&#x00A0;SAPKIBIIP4&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;BI_CONT<br />&#x00A0;&#x00A0;SAPKIBIIP5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;BI_CONT<br />&#x00A0;&#x00A0;SAPKB70011&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SAP_BASIS<br />&#x00A0;&#x00A0;SAPKA70011&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SAP_ABA<br />&#x00A0;&#x00A0;SAPKIPYJ7B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;PI_BASIS<br />&#x00A0;&#x00A0;SAPKW70012&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SAP_BW<br />&#x00A0;&#x00A0;SAPKNA7008&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;SAP_AP<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If this problem is due to cause 1 above, execute the RUTTTYPACT program.<br />Its log file is stored in the DIR_HOME directory.<br /><br />To remove the remaining, inconsistent table types, you only need to activate these table types for both causes.<br /><br />The safest method is to use the RUTTTYPACT_OLD utility program to activate all of the table types.<br />It is very time-consuming because it activates many of the structures, tables and programs that you use.<br /><br />We recommend that you use the RUTTTYPACT2 program, which first checks all table types and only reactivates the inconsistent table types. This procedure is always more efficient.<br />The name of the log file in the database starts with \"RUTTTYPACT2\".<br /><br />If you do not yet have the RUTTTYPACT2 program, you can obtain it from the attachment to this note.<br />Download the archive file named B20K8A1TEG.SAR and unpack it.<br />Note 212876 describes how to unpack SAR archives.<br />Import the B20K8A1TEG transport as described in Note 13719.<br /><br />The RUTTTYPACT2 program requires Support Package SAPKB62046 in Release 620.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D033341"}, {"Key": "Processor                                                                                           ", "Value": "D033341"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000832577/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000832577/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "B20K8A1TEG.SAR", "FileSize": "6", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000029272005&iv_version=0009&iv_guid=0F120CE4C66D0240BABE9B84E589F2F8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "980578", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/980578"}, {"RefNumber": "980577", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/980577"}, {"RefNumber": "980576", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/980576"}, {"RefNumber": "980575", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/980575"}, {"RefNumber": "980574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/980574"}, {"RefNumber": "957483", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/957483"}, {"RefNumber": "957482", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/957482"}, {"RefNumber": "957300", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/957300"}, {"RefNumber": "957299", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/957299"}, {"RefNumber": "957298", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/957298"}, {"RefNumber": "947372", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947372"}, {"RefNumber": "909628", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/909628"}, {"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1056791", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1056791"}, {"RefNumber": "1056680", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1056680"}, {"RefNumber": "1051438", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1051438"}, {"RefNumber": "1034388", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1034388"}, {"RefNumber": "1023355", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1023355"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62051", "URL": "/supportpackage/SAPKB62051"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64012", "URL": "/supportpackage/SAPKB64012"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70001", "URL": "/supportpackage/SAPKB70001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}