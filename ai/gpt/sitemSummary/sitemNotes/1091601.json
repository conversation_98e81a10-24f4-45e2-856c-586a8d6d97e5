{"Request": {"Number": "1091601", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 467, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016368292017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=7C2150982BBF6E1CE3C6B06A19AC0B2D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1091601"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.04.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-SK"}, "SAPComponentKeyText": {"_label": "Component", "value": "Slovakia"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Slovakia", "value": "XX-CSC-SK", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-SK*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1091601 - Changeover to EUR in Slovakia and Slovak localization"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Specific functionality for Slovak localization is necessary to adapt for currency changeover for Slovakia.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EUR conversion, EUR changeover, Slovakia, Currency changeover, SKK<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>For Slovakia is localized functionality in different modules. This note covers information about required steps for currency changeover in localized functionality. This note doesn't describe module HR and the note describes only localization which is not delivered by standard support packages. The note is updated.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>I. Module FI</b><br /> <b>*****************</b><br /> <OL>1. <B>EC sales list</B></OL> <p>See the note 1142379</p> <OL>2. <B>Exchange rate from NBS</B></OL> <p>See the note 1142262</p> <OL>3. <B><B>Bank interface</B></B></OL> <p>A/ Program changes<br />Update the bank interface according to the notes. The corrected programs for bank inteface were published on 9.10.2007.</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>R/3</TH><TH ALIGN=LEFT> Release</TH><TH ALIGN=LEFT> Note</TH></TR> <TR><TD>-----------------------------------------------------</TD></TR> <TR><TD>R/3</TD><TD> 4.6C</TD><TD> 436363</TD></TR> <TR><TD>Enterprise</TD><TD> 4.70</TD><TD> 599812</TD></TR> <TR><TD>ECC</TD><TD> 5.00</TD><TD> 833888</TD></TR> <TR><TD>ECC</TD><TD> 6.00</TD><TD> 957032</TD></TR> </TABLE> <p><br />B/ Customizing<br />Update customizing in the table J_6G_EOB (Definition of external operations).<br /><br /></p> <b>II. Module FI-CA</b><br /> <b>*********************</b><br /> <p>A/ Program changes<br />Implement the tranports published in the notes or implement the required Support Package level for Add-On CEEISUT.</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>R/3</TH><TH ALIGN=LEFT> Release</TH><TH ALIGN=LEFT> SP level&#x00A0;&#x00A0; Note</TH></TR> <TR><TD>---------------------------------------------------------------</TD></TR> <TR><TD>INSURANCE</TD><TD> 4.64</TD><TD> -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 556147</TD></TR> <TR><TD>INSURANCE</TD><TD> 4.72</TD><TD> -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 761142</TD></TR> <TR><TD>INSURANCE</TD><TD> 6.00</TD><TD> -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1068693</TD></TR> <TR><TD></TD></TR> <TR><TD>IS-U/CCS</TD><TD> 4.64</TD><TD> -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 384823</TD></TR> <TR><TD>CEEISUT</TD><TD> 4.72</TD><TD> 14&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1004617</TD></TR> <TR><TD>CEEISUT</TD><TD> 6.00</TD><TD> 04&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1014951</TD></TR> </TABLE> <p><br />B/ Customizing<br />Update customizng in these tables/views. The customizing for postal checks fees is common with module HR.</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Release</TH><TH ALIGN=LEFT> Table/View</TH></TR> <TR><TD>---------------------------------------------------------------</TD></TR> <TR><TD>INSURANCE</TD><TD> 4.64 </TD><TD> T7SK52</TD></TR> <TR><TD>INSURANCE</TD><TD> 4.72</TD><TD> /SAPCE/FK_T7SK52</TD></TR> <TR><TD>INSURANCE</TD><TD> 6.00</TD><TD> /SAPCE/FK_T7SK52</TD></TR> <TR><TD></TD></TR> <TR><TD>IS-U/CCS</TD><TD> 4.64</TD><TD> T7SK52</TD></TR> <TR><TD>CEEISUT</TD><TD> 4.72</TD><TD> /SAPCE/FK_T7SK52</TD></TR> <TR><TD>CEEISUT</TD><TD> 6.00</TD><TD> /SAPCE/FK_T7SK52</TD></TR> </TABLE> <p><br /></p> <b>III. Module IS-U</b><br /> <b>*********************</b><br /> <p>Implement the steps how they're described in point II. Module FI-CA.<br />For part \"Budget billing procedure\" see the note 1132267.<br /><br /></p> <b>IV. Module IS-T</b><br /> <b>********************</b><br /> <p>Implement the steps how they're described in point II. Module FI-CA.<br /><br /></p> <b>V. Module FS-CD</b><br /> <b>********************</b><br /> <p>Implement the steps how they're described in point II. Module FI-CA.<br /><br /></p> <b>VI. Module BCA</b><br /> <b>*******************</b><br /> <p>A/ Program changes<br />There aren't any specific steps for currency changeover in Slovak localization.<br /><br />B/ Customizing<br />Update customizng in tables /CEEIB/SK_CHECK (Checks for Additional Fields) and /CEEIB/SK_PT (Payment Title).<br /><br /></p> <b>VII. Module Travel management</b><br /> <b>**********************************</b><br /> <p>A/ Program changes<br />Implement required correction requests to the system for relevant level of SP_HR-APPL or SAP_HR-EA-HR:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>R/3 </TH><TH ALIGN=LEFT> Release</TH><TH ALIGN=LEFT> SP level&#x00A0;&#x00A0;Note</TH></TR> <TR><TD>--------------------------------------------------------------</TD></TR> <TR><TD>R/3 </TD><TD> 4.6C</TD><TD> 128-55&#x00A0;&#x00A0; 698531</TD></TR> <TR><TD>Enterprise</TD><TD> 4.70x110</TD><TD> 73-73&#x00A0;&#x00A0;&#x00A0;&#x00A0;709678</TD></TR> <TR><TD>Enterprise</TD><TD> 4.70x200</TD><TD> 73-54&#x00A0;&#x00A0;&#x00A0;&#x00A0;749768</TD></TR> <TR><TD>ECC</TD><TD> 5.00</TD><TD> 39&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 833794</TD></TR> <TR><TD>ECC</TD><TD> 6.00</TD><TD> 22&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 952850</TD></TR> </TABLE> <p><br />B/ Customizing<br />Update customizng in the view /CEETMCZ/V_T706V (Per Diem for Pocket Money).<br /><br /></p> <b>VIII. Module FM - Public sector reporting</b><br /> <b>**********************************************</b><br /> <p>There aren't any specific steps for currency changeover in Slovak localization.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I400028"}, {"Key": "Processor                                                                                           ", "Value": "I003595"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "957032", "RefComponent": "FI-AP-AP-B1", "RefTitle": "Bank interface with DMEE Czech and Slovak", "RefUrl": "/notes/957032"}, {"RefNumber": "952850", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP ECC 6.0", "RefUrl": "/notes/952850"}, {"RefNumber": "833794", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP ECC 5.0", "RefUrl": "/notes/833794"}, {"RefNumber": "761142", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "Czech & Slovak bank interface for FI-CA - 4.7.200", "RefUrl": "/notes/761142"}, {"RefNumber": "749768", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP R/3 4.7x200", "RefUrl": "/notes/749768"}, {"RefNumber": "709678", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP R/3 4.7x110", "RefUrl": "/notes/709678"}, {"RefNumber": "698531", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP R/3 4.6C", "RefUrl": "/notes/698531"}, {"RefNumber": "556147", "RefComponent": "XX-CSC-SK-FICA", "RefTitle": "Slovak bank interface for FS-CD 4.6", "RefUrl": "/notes/556147"}, {"RefNumber": "384823", "RefComponent": "XX-CSC-SK-IS-U", "RefTitle": "Slovak bank interface for FI-CA 4.6", "RefUrl": "/notes/384823"}, {"RefNumber": "1142379", "RefComponent": "FI-GL-GL-F", "RefTitle": "EC sales list for Slovakia II", "RefUrl": "/notes/1142379"}, {"RefNumber": "1142262", "RefComponent": "FI-GL", "RefTitle": "Import of exchange rates file for Slovakia II", "RefUrl": "/notes/1142262"}, {"RefNumber": "1132267", "RefComponent": "XX-CSC-SK-IS-U", "RefTitle": "BBIC: Conversion to Euro in IS-U/T", "RefUrl": "/notes/1132267"}, {"RefNumber": "1068693", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "Czech & Slovak bank interface for FI-CA - ECC 6.0", "RefUrl": "/notes/1068693"}, {"RefNumber": "1038292", "RefComponent": "XX-CSC-SK", "RefTitle": "Collective note for Euro changeover in Slovakia", "RefUrl": "/notes/1038292"}, {"RefNumber": "1014951", "RefComponent": "BC-UPG-ADDON", "RefTitle": "CEEISUT 600: Component Support Packages", "RefUrl": "/notes/1014951"}, {"RefNumber": "1004617", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Add-on Support Packages for add-on SAP IS-UT CEE 472", "RefUrl": "/notes/1004617"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "952850", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP ECC 6.0", "RefUrl": "/notes/952850 "}, {"RefNumber": "1014951", "RefComponent": "BC-UPG-ADDON", "RefTitle": "CEEISUT 600: Component Support Packages", "RefUrl": "/notes/1014951 "}, {"RefNumber": "833794", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP ECC 5.0", "RefUrl": "/notes/833794 "}, {"RefNumber": "698531", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP R/3 4.6C", "RefUrl": "/notes/698531 "}, {"RefNumber": "709678", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP R/3 4.7x110", "RefUrl": "/notes/709678 "}, {"RefNumber": "749768", "RefComponent": "XX-CSC-CZ-TV", "RefTitle": "Travel management in CEE for SAP R/3 4.7x200", "RefUrl": "/notes/749768 "}, {"RefNumber": "1004617", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Add-on Support Packages for add-on SAP IS-UT CEE 472", "RefUrl": "/notes/1004617 "}, {"RefNumber": "1142379", "RefComponent": "FI-GL-GL-F", "RefTitle": "EC sales list for Slovakia II", "RefUrl": "/notes/1142379 "}, {"RefNumber": "1142262", "RefComponent": "FI-GL", "RefTitle": "Import of exchange rates file for Slovakia II", "RefUrl": "/notes/1142262 "}, {"RefNumber": "1038292", "RefComponent": "XX-CSC-SK", "RefTitle": "Collective note for Euro changeover in Slovakia", "RefUrl": "/notes/1038292 "}, {"RefNumber": "1068693", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "Czech & Slovak bank interface for FI-CA - ECC 6.0", "RefUrl": "/notes/1068693 "}, {"RefNumber": "761142", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "Czech & Slovak bank interface for FI-CA - 4.7.200", "RefUrl": "/notes/761142 "}, {"RefNumber": "1132267", "RefComponent": "XX-CSC-SK-IS-U", "RefTitle": "BBIC: Conversion to Euro in IS-U/T", "RefUrl": "/notes/1132267 "}, {"RefNumber": "384823", "RefComponent": "XX-CSC-SK-IS-U", "RefTitle": "Slovak bank interface for FI-CA 4.6", "RefUrl": "/notes/384823 "}, {"RefNumber": "556147", "RefComponent": "XX-CSC-SK-FICA", "RefTitle": "Slovak bank interface for FS-CD 4.6", "RefUrl": "/notes/556147 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "464", "To": "464", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}