{"Request": {"Number": "1707428", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 616, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010145242017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001707428?language=E&token=C7ECCCEEBEEF951301DE0F906417CEDB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001707428", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001707428/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1707428"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Exit added"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.04.2012"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-REO-BF"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Reorganization Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization", "value": "FI-GL-REO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization Basic Functions", "value": "FI-GL-REO-BF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO-BF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1707428 - BAdI for overwriting the transaction type"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In Customizing, you define the transaction type (field RMVCT) for acquisitions and retirements that is to be used to make a transfer posting for balances.<br />However, you want to override the transaction type for some of the balances to be transferred.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br />Reorganization<br />Reorg<br />Consolidation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The relevant function is missing.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The BAdI FAGL_R_REPOST has been added. Implement the attached correction instructions to make the BAdI available in your system.<br /><br />After you have implemented the correction instructions, you can implement the BAdI. To do this, proceed as follows:</p> <OL>1. Call transaction SE19.</OL> <OL>2. Under \"Create Implementation\", enter the following value in the \"New BAdI - Enhancement Spot\" field:<br />FAGL_REORGANIZATION</OL> <OL>3. Choose \"Create Impl.\".</OL> <OL>4. Enter a name for the enhancement implementation, for example, ZFAGL_REORGANIZATION.<br />Enter a short text of your choice.</OL> <OL>5. Enter a name in the \"BAdI Implementation\" field, for example, Z_FAGL_REORGANIZATION_REPOST.<br />Enter a name for the implementation class, for example, ZCL_FAGL_REORGANIZATION_REPOST.<br />Select the BAdI FAGL_R_REPOST.</OL> <OL>6. On the \"Enh. Spot Element Definitions\" tab page, expand the tree Z_FAGL_REORGANIZATION_REPOST and click \"Implementing Class\".<br />Double-click the following method: IF_FAGL_R_REPOST~CHANGE_MOVEMENT_TYPE<br />Implement the method to overwrite the transaction types according to your requirements.</OL> <OL>7. Save and activate all objects.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-REO (General Ledger Reorganization)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D041775)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021112)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001707428/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707428/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1627018", "RefComponent": "FI-GL-REO", "RefTitle": "Composite SAP Note for segment reorganization", "RefUrl": "/notes/1627018"}, {"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153 "}, {"RefNumber": "1627018", "RefComponent": "FI-GL-REO", "RefTitle": "Composite SAP Note for segment reorganization", "RefUrl": "/notes/1627018 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60509", "URL": "/supportpackage/SAPKH60509"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60604", "URL": "/supportpackage/SAPKH60604"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0001707428/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60508&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60601 - SAPKH60603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>You must create structures in the ABAP Dictionary:<br/><br/>Structure: FAGL_R_S_BALANCES_AGGR_DOC</P> <OL>1. Call transaction SE11.</OL> <OL>2. Select \"Data type\" and enter FAGL_R_S_BALANCES_AGGR_DOC. Choose \"Create\".</OL> <OL>3. In the dialog box that is displayed, select \"Structure\".</OL> <OL>4. Enter the following text as the short text:<br/>\"Balance to Be Transferred (Aggregated) - Document Header Data\"</OL> <OL>5. Enter the following fields:<br/>Component  Component Type<br/>&nbsp;&nbsp;BUKRS    BUKRS<br/>&nbsp;&nbsp;RLDNR  FAGL_RLDNR<br/>&nbsp;&nbsp;XPRIOR    FLAG<br/>&nbsp;&nbsp;RYEAR    RYEAR<br/>&nbsp;&nbsp;POPER    POPER</OL> <OL>6. Save and activate the structure. A dialog box is displayed in which  you must specify an enhancement category. Select \"Cannot Be Enhanced\".</OL> <OL>7. If you use Enhancement Package 5, select the package  FAGL_REORGANIZATION. If you use Enhancement Package 6, select the package FAGL_REORGANIZATION_FW.</OL> <P><br/><br/>Structure FAGL_R_S_BALANCES_AGGR_DOCLN<br/>Call transaction SE11.</P> <OL>8. Select \"Data type\" and enter FAGL_R_S_BALANCES_AGGR_DOCLN. Choose \"Create\".</OL> <OL>9. In the dialog box that is displayed, select \"Structure\".</OL> <OL>10. Enter the following text as the short text:<br/>\"Balance to Be Transferred (Aggregated) - Line Item Data\"</OL> <OL>11. Enter the following fields:<br/>Component  Component Type<br/>&nbsp;&nbsp;PSWSL    PSWSL<br/>&nbsp;&nbsp;RACCT    RACCT<br/>&nbsp;&nbsp;.INCLUDE   FAGL_R_MAP_SAP_INC<br/>&nbsp;&nbsp;OBJNR_SAP  FAGL_R_OBJNR<br/>&nbsp;&nbsp;OBJNR_CUST   FAGL_R_OBJNR<br/>&nbsp;&nbsp;GLATTR_VAL   FAGL_R_GLATTR_VAL<br/>&nbsp;&nbsp;MDATTR_NEW  FAGL_R_MDATTR_NEW</OL> <OL>12. Save and activate the structure. A dialog box is displayed in which  you must specify an enhancement category. Select \"Can be enhanced (character-type or numeric)\".</OL> <OL>13. If you use Enhancement Package 5, select the package  FAGL_REORGANIZATION. If you use Enhancement Package 6, select the package FAGL_REORGANIZATION_FW.</OL> <P><br/><br/>Structure FAGL_R_S_BALANCES_AGGR_ACCCR<br/>Call transaction SE11.</P> <OL>14. Select \"Data type\" and enter FAGL_R_S_BALANCES_AGGR_ACCCR. Choose \"Create\".</OL> <OL>15. In the dialog box that is displayed, select \"Structure\".</OL> <OL>16. Enter the following text as the short text:<br/>\"Balance to Be Transferred (Aggregated) - Amounts\"</OL> <OL>17. Enter the following fields:<br/>Component  Component Type<br/>&nbsp;&nbsp;CURTP    CURTP<br/>&nbsp;&nbsp;WAERS    WAERS<br/>&nbsp;&nbsp;WRBTR    WRBTR</OL> <OL>18. Choose the \"Currency/quantity fields\" tab page.<br/>Enter the reference table FAGL_R_S_BALANCES_AGGR_ACCCR and the reference  field WAERS for the field WRBTR.</OL> <OL>19. Save and activate the structure. A dialog box is displayed in which  you must specify an enhancement category. Select \"Can be enhanced (character-type or numeric)\".</OL> <OL>20. If you use Enhancement Package 5, select the package  FAGL_REORGANIZATION. If you use Enhancement Package 6, select the package FAGL_REORGANIZATION_FW.</OL> <P><br/><br/>Structure FAGL_R_S_BALANCES_AGGR<br/>Call transaction SE11.</P> <OL>21. Select \"Data type\" and enter FAGL_R_S_BALANCES_AGGR. Choose \"Create\".</OL> <OL>22. In the dialog box that is displayed, select \"Structure\".</OL> <OL>23. Enter the following text as the short text:<br/>\"Balance to Be Transferred (Aggregated)\"</OL> <OL>24. Enter the following fields (note the \"Group\" column):<br/>Component&nbsp;&nbsp;&nbsp;&nbsp;Component Type    Group<br/> .INCLUDE &nbsp;&nbsp; FAGL_R_S_BALANCES_AGGR_DOC KEY_AGGR_DOC<br/> .INCLUDE  &nbsp;&nbsp; FAGL_R_S_BALANCES_AGGR_DOCLN KEY_AGGR_DOCLN<br/> .INCLUDE &nbsp;&nbsp; FAGL_R_S_BALANCES_AGGR_ACCCR  ACCCR<br/> PSWBT &nbsp;&nbsp; PSWBT</OL> <OL>25. Choose the \"Currency/quantity fields\" tab page.<br/>Enter the reference table FAGL_R_S_BALANCES_AGGR and the reference field  PSWSL for the field PSWBT.</OL> <OL>26. Save and activate the structure. A dialog box is displayed in which  you must specify an enhancement category. Select \"Can be enhanced (character-type or numeric)\".</OL> <OL>27. If you use Enhancement Package 5, select the package  FAGL_REORGANIZATION. If you use Enhancement Package 6, select the package FAGL_REORGANIZATION_FW.</OL> <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60508&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60601 - SAPKH60603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Create a message.</P> <OL>1. Call transaction SE91.</OL> <OL>2. Select the message class FAGL_REORGANIZATION.<br/>Enter the message number 128.<br/>Choose \"Change\".</OL> <OL>3. Enter the following text as the short text:<br/>\"BAdI FAGL_R_REPOST is implemented multiple times\"</OL> <OL>4. Choose \"Save\".</OL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}