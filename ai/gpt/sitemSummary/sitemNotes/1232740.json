{"Request": {"Number": "1232740", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 260, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001232740?language=E&token=4F4F475236C01D23C9B4E32BCEC1993E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001232740", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001232740/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1232740"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "IS-HER-CM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Student Lifecycle Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Higher Education and Research", "value": "IS-HER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-HER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Student Lifecycle Management", "value": "IS-HER-CM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-HER-CM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1232740 - Special Development for PASSHE"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains a complete WebDynpro for ABAP Application - the Course Registration UI. This is a special development for a single pilot customer only (customer number 476933, State System Higher Education of the Commonwealth of Pennsylvania) and no other customer is allowed to deploy the application delivered herewith. The terms of usage are specified elsewhere and the present note deals only with the technical procedure required to deliver the application.<br /><br />The delivered application is built as a local development in the standard SAP namespace based on software component IS-PS-CA 603 (Enhancement Package 3 'EhP3'). None of the objects contained in the attached transport files modifies standard objects in any way.<br /><br /><br /><br />*** IMPORTANT ***<br />Since the Course Registration UI will become part of the standard product with IS-PS-CA 604 (Enhancement Package 4 'EhP4), all the local objects delivered with this note must be deleted from any customer system completely before upgrading to EhP4. No application data will be affected by this (all application data is stored in standard database tables from the software component IS-PS-CA). The critical consequence is that all screen configuration and possible customer enhancements will be lost. The Course Registration UI in EhP4 offers an enhanced set of functions and is technically based on a new framework. The local version for EhP3 and the standard version to be delivered in EhP4 are incompatible.<br /><br /><br /><br />*** PREREQUISITES ***<br />Before you can proceed with the import of the transport files attached, two activities must be performed:<br /><br />1. SAP note 1148446 must be applied to the target system (see related notes). This note contains some minor enahncements in the standard product required by the course registration UI.<br /><br />2. Two packages must be created manually as copies in the system (in transaction SE80 or SE21). All the objects from the transport request attached are assigned to one of these two packages. By creating the packages manually as local objects in your system, you can choose the appropriate transport attributes which suit your needs. These two packages and all objects contained in them will have to be deleted completely before upgrading to EhP4 (see above).<br /><br />The packages must be named PMIQ_WD_ST and PMIQ_ESOA_ST. They have to be created as normal packages (not main packages). You can choose a local software component (not IS-PS-CA) and a transport layer appropriate for your system.<br /><br /><br /><br />*** HOW TO PROCEED ***<br />The general procedure for the import is described in note 13719.<br /><br />As an attachment (see note 480180 for how to download attachments) to this note you will find a ZIP file (CourseRegUI.zip) with the required transport files:<br /><br />- the R3trans file which contains the actual data: R021917.EAV and<br />- the transport info file which contains control-data for the<br />&#x00A0;&#x00A0;transport: K021917.EAV<br /><br />The transport request from which the files were generated in the SAP development system is called EAVK021917.<br /><br /><br /><br />*** DIFFERENCES BETWEEN THE VERSIONS IN EHP3 OR EHP4 ***<br />As menitioned above, the standard version of the Course Registration UI delivered with EhP4 offers a wider range of technical and business features. The major differences are listed below:<br /></p> <UL><LI>List UIBB Framework (FPM).</LI></UL> <p> In EhP3, the list of courses is built using a normal table. The following key features of List UIBB Framework are therefore not available:</p> <UL><UL><LI>Re-configure the table feeder class. E.g. add customer's own table column.</LI></UL></UL> <UL><UL><LI>Filtering function</LI></UL></UL> <UL><UL><LI>Configure table layout via wysiwyg tool. E.g. change a column format, by changing text view to navigation link or image.</LI></UL></UL> <UL><UL><LI>Add new buttons. Customer can add new buttons to the table in order to include new functions or popup.</LI></UL></UL> <UL><LI>Form UIBB Framework(FPM)</LI></UL> <p> In EhP3, the detail view for course data is not based on Form UIBB. The following key features are therefore missing:</p> <UL><UL><LI>Re-configure the form feeder class. E.g. insert customer's own data.</LI></UL></UL> <UL><UL><LI>Configure form layout via wysiwyg tool. E.g. change the field sequence. Regroup fields. Change filed format, e.g. text -&gt; link.</LI></UL></UL> <p></p> <UL><LI>Tabbed UIBB Framework(FPM)</LI></UL> <p> In EhP3, the course detail is shown in a fact sheet mode. i.e. the detail for registration, module, section are shown all in one page.<br /> In EhP4, tabbed UIBB framework is used to show the details in several&#x00A0;&#x00A0;tabs.<br /> Functionality is basically the same but the UI style is different.<br /></p> <UL><LI>ABAP Composites/Business Abstraction Layer(SLCM)</LI></UL> <p> The local EhP3 version is not built as an ABAP composite. This means that the Business Abstraction Layer (implemented as a set of BAdIs) is not available yet in EhP3.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The Business Abstraction Layer (BAL)in EhP4 supports the option of running the course registration UI on a separate ERP installation communicating remotely with the backend (also an ERP installation). The BAL consists of a set of BAdIs with delivered standard implementations.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These BAdIs are the 'hub' for business data exchange, e.g. read student, module, booking info; change or delete booking; add/remove item from reg. cart;&#x00A0;&#x00A0;or query for courses. RFCs are called in the default implementation of these BAdIs.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These BAdIs enable customers to add additional functions into the BAdI. One example is the filtering of search results. Customers can implement additional filtering based on search criteria by overriding the default BAdI implementation.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Customers can also use their own RFCs to replace the standard ones in these BAdIs.&#x00A0;&#x00A0;Actually the business logic can be changed using these BAdIs.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is also possible to execute different functions in different RFC destinations (configurable via customizing). E.g. the backend system for the course query can be different from that for booking actions. This would be useful if customers have a special query service for courses in a different system.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <UL><LI>Memory Consumption(FPM):</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In EhP4, the FPM framework offers 'transient' functions, which can automatic release memory when a WD component is not in focus. This can greatly reduce the memory consumption of the application.&#x00A0;&#x00A0;As a consequence, the EhP4 version is more memory efficient compared with EhP3.<br /><br /></p> <UL><LI>UI Rendering Speed (Netweaver)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EhP4 is based on Netweaver 7. 01 , while EhP3 is based on Netweaver 7.0. &#x00A0;&#x00A0;In 7.01, Netweaver offers the so-called 'Light Speed' version of Web Dynpro, which can improve the speed of UI rendering.<br /><br /></p> <UL><LI>Move-up Function(SLCM)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This is not supported in the EhP3 version.<br /><br /></p> <UL><LI>No default message mapping content. Customers have to configure the mapping of messages from scratch.</LI></UL> <p><br /></p> <UL><LI>IDR (FPM)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No icons or links can be shown in the header part of the application in EhP3. Only text can be used.<br /></p> <UL><LI>Button activation(SLCM)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In EhP4, a button is automatically disabled when a function is not allowed, e.g. due to missing authorizations or check points errors.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In EhP3, buttons are always enabled. Users will notice that some actions are not allowed only when they try to execute the action.<br /><br /></p> <UL><LI>Registration Cart Check(FPM):</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In EhP4, when checking the content of a registration cart, error messages can be linked to the module which caused the error. There is a 'mouse over' effect on the module short name: the error message texts are shown directly as tooltip.<br /><br /><br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Studnet Lifecycle Management Special Development Pilot Application PASSHE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Not applicable</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>See explanation above</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D041406"}, {"Key": "Processor                                                                                           ", "Value": "D041406"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232740/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "CourseRegUI.zip", "FileSize": "1552", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000411702008&iv_version=0001&iv_guid=244D50D4DA300B4AA107E24FF9C86619"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-PS-CA", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}