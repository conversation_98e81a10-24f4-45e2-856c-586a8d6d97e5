{"Request": {"Number": "778198", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 443, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004272872017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000778198?language=E&token=48D7939260BD4A0D5AE2DCCBA4FDFB0D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000778198", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000778198/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "778198"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.08.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-ITS"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Internet Transaction Server"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Internet Transaction Server", "value": "BC-FES-ITS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-ITS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "778198 - SIAC_XPRA_CONVERT_FROM_4X_64, upgrade to Netweaver 04"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You execute the SIAC_XPRA_CONVERT_FROM_4X_64 report while performing an upgrade or while importing Support Package 05, and the system issues error message S&gt;801.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SIAC_XPRA_CONVERT_FROM_4X ITS MINICHAT PV3I TS_WS20000102H S&gt;802</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The storage structure for objects of the Internet Transaction Server was changed. The report converts the objects from the old storage structure to the new structure (see note 678904). Errors can occur during the conversion if:</p> <UL><UL><LI>parts of a service already exist in the new format but the main information is missing, or</LI></UL></UL> <UL><UL><LI>the data in the old storage structures seems to be incorrect.</LI></UL></UL> <p><br />The report triggers certain checks before the data is read to prevent a program crash.<br /><br />In certain cases, this is an error in the SIAC_XPRA_CONVERT_FROM_4X_64 conversion report when the message appears:<br />Among other things, the report converts language resource parameters. For this, an exception to the principle that services are not converted if parts already exist in the new format must be made.<br />Background: As a result of the XPRA run in translation systems, subsequent translation and the transport of translations, certain entries for language resources may exist in a system, even if the object itself might not yet exist.<br /><br />Known services for which the message appears are:</p> <UL><UL><LI>MINICHAT: This service is obsolete.</LI></UL></UL> <UL><UL><LI>PV3I: Inconsistent data for obsolete parts.</LI></UL></UL> <UL><UL><LI>TS_WS20000102H: This service is obsolete.</LI></UL></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The errors do not have to be corrected during the upgrade and can be ignored in many cases.<br />If you do not use the Internet Transaction Server, you can ignore the messages.<br />Import Support Packages SAPKB64010 and SAPKB64011 or implement the correction instructions. Then execute the SIAC_XPRA_CONVERT_FROM_4X_64 report again. Services that could not be converted anymore are displayed in the system log of the report. If you do not use the relevant services, you can ignore the messages.<br />You can convert the services that you need and that returned errors when you executed the SIAC_XPRA_CONVERT_FROM_4X_64 report into the new format using transaction SIAC1 (for more information, refer to note 774148). If the error is due to inconsistent data in the old format, the conversion may trigger a program crash. In this case, you should try to convert the objects of the service separately. So far, the error has only occurred with inconsistent data that belonged to parts of a service that were obsolete and, therefore, no longer required.<br />MINICHAT service: The service is obsolete. Therefore, you can ignore the error message.<br />Service PV3I: The parts known to be incorrect are obsolete. These parts are deleted as of Support Package SAPKE50012. If you do not want to import this Support Package, or you can, but need the service, you need to use Transaction SIAC1 to convert the other sub-objects into the new format separately.<br />Service TS_WS20000102H: This service is obsolete. Therefore, you can ignore the error message. For more information, see also note 789534.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5010299"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D001370)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000778198/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000778198/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000778198/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000778198/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000778198/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000778198/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000778198/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000778198/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000778198/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "933948", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/933948"}, {"RefNumber": "933947", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/933947"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "839820", "RefComponent": "BC-FES-ITS", "RefTitle": "longpost:801 Internet service SPIGITSFL_DEMO", "RefUrl": "/notes/839820"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "721993", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/721993"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "678904", "RefComponent": "BC-DWB-TOO-WAB", "RefTitle": "ITS - New storage structures as of SAP Web AS 6.40", "RefUrl": "/notes/678904"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "678904", "RefComponent": "BC-DWB-TOO-WAB", "RefTitle": "ITS - New storage structures as of SAP Web AS 6.40", "RefUrl": "/notes/678904 "}, {"RefNumber": "839820", "RefComponent": "BC-FES-ITS", "RefTitle": "longpost:801 Internet service SPIGITSFL_DEMO", "RefUrl": "/notes/839820 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64010", "URL": "/supportpackage/SAPKB64010"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64011", "URL": "/supportpackage/SAPKB64011"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 2, "URL": "/corrins/0000778198/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}