{"Request": {"Number": "1680045", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 470, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017382672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001680045?language=E&token=35B8B30199F0C3BC09EAE6FEC766C93F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001680045", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001680045/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1680045"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 548}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-INS-SWPM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Installation with Software Provisioning Manager"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation Tools (SAP Note 1669327)", "value": "BC-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation with Software Provisioning Manager", "value": "BC-INS-SWPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS-SWPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1680045 - Release Note for Software Provisioning Manager 1.0 (recommended: SWPM 1.0 SP40)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>************************************************************************************************************<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <strong>Software Provisioning Manager 1.0</strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br /><br />Software Provisioning Manager (SWPM) <strong>1.0</strong> supports the following provisioning scenarios for SAP systems based on SAP NetWeaver and S/4HANA<a target=\"_blank\" href=\"/notes/2381438\"><br /></a></p>\r\n<ul>\r\n<li>Installation of new systems, instances,&#160;and standalone engines</li>\r\n<li>System copy of existing systems</li>\r\n<li>Transformation of systems such as via System Rename and&#160;Dual-Stack Split</li>\r\n<li>Deletion of systems, instances, and standalone engines</li>\r\n</ul>\r\n<p><strong>For a list of all SAP products supported by&#160;<strong>Software Provisioning Manager 1.0, see&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/1cd327bf4d094211af157e69ff68db1b/CURRENT_VERSION/en-US/6c5727269f6e4a65acc714f353ac3348.html\">SAP Products Supported for System Provisioning Processes such as Installation, System Copy, System Rename, Dual-Stack Split, Using Software Provisioning Manager 1.0</a>.</strong></strong></p>\r\n<p><strong><strong><strong>For a list of SAP products supported&#160;<strong>by&#160;<strong>Software Provisioning Manager 1.0</strong></strong> for the respective</strong>&#160;databases and operating system, see the \"About this Document\" section in within the respective SWPM 1.0 guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/swpm10guides/c8ed609927fa4e45988200b153ac63d1.html\">https://help.sap.com/viewer/swpm10guides</a></strong></strong>.</p>\r\n<p><strong>Note:</strong> Do <strong>not</strong> use this SAP Note for installing, renaming, or copying an SAP system using Software Provisioning Manager <strong>2.0</strong>. For Software Provisioning Manager 2.0, use SAP Note <a target=\"_blank\" href=\"/notes/2568783\">2568783</a>&#160;instead.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Software Provisioning Manager 1.0; SWPM 1.0; release note; download; restrictions; installation; system rename; dual-stack split; system copy</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Remarks, annotations, and corrections discovered after the publication</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Note contains the following sections:<br /><br /><strong>1&#160;&#160; General Information</strong></p>\r\n<ul>\r\n<li><strong>Documentation</strong></li>\r\n<li><strong>Kernel Media</strong></li>\r\n<li><strong>Database-specific SAP Notes</strong></li>\r\n<li><strong>Restrictions</strong></li>\r\n</ul>\r\n<p><strong>2&#160;&#160; Planning and Preparation</strong></p>\r\n<p><strong>3&#160;&#160; Running the Software Provisioning Manager</strong></p>\r\n<p><strong>4&#160;&#160; Follow - Up Activities</strong></p>\r\n<p><br /><br /><strong>Contents</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 1822px; width: 1288px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>Date</strong></p>\r\n</td>\r\n<td><strong>Section</strong></td>\r\n<td>\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>12/FEB/24</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP40, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>19/DEC/23</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>RZ10 or SAPPFPA reports return Warning messages</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>22/NOV/23</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;IBM i: Database error SQL0443 -&#160;NOT AUTHORIZED TO USE QSYS/DSPPTF&#65279;</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>06/OCT/23</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP39, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>28/AUG/23</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>SAP Add. Appl. Server Installation on Windows for IBM i Db2</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>26/MAY/23</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP38, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>13/FEB/23</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP37, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>24/OCT/22</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>System provisioning of NetWeaver Java 7.50 SP0, S/4HANA 1709 Java stack and S/4HANA 1809 Java stack: SWPM fails with&#160;<em>java.lang.SecurityException</em> exception</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>18/OCT/22</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>DB2-z/OS: Installation of solutions based on SAP NetWeaver 7.00, ... (revised)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>10/OCT/22</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP36, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>01/JUN/22</p>\r\n</td>\r\n<td>1</td>\r\n<td>\r\n<p>Potential loss of file system content while using SWPM-based Rename and keeping the SAPSID (see SAP Note&#160;<a target=\"_blank\" href=\"/notes/3207660\">3207660</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>30/MAY/22</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>SAP System Installations on OS release IBM i V7R5</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>24/MAY/22</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP35, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>17/MAY/22</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>DB6: Step InstallDB2Software fails because db2prereqcheck returns DBT3507E due to missing mksh or psmisc packages</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>14/FEB/22</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP34, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>11/OCT/21</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP33, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>20/JUL/21</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: The program sapinst (the SWPM) breaks directly after the self-extract</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>21/JUN/21</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP32, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>15/FEB/21</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP31, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>05/OCT/20</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP30, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>17/SEP/20</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure that the SAPJVM 8.1 archive you use has a patch level of 63 or lower.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>17/SEP/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java:&#160;PAS installation fails due to SAPJVM Patch Level</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>17/SEP/20</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure you apply the latest Kernel and AS Java Patches</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>04/SEP/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>MSS: Connection problems during option \"Configure additional AlwaysOn Node\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>20/AUG/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>HANA: ABAP instance on Windows fails to start due to \"Failed locking the secure store.\" error using the latest HANA client version 2.4 or 2.5</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>14/JUL/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>java.lang.UnsupportedClassVersionError problems during installation or after manually updating an SAP System with HANA Client 2.5</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>25/JUN/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: The ASCS instance split may break for SAP systems based on NW 7.XX</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>17/JUN/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Workaround for \"GetSAPReleaseFromDB\" error:&#160;Moving a primary application server instance to a different host after DMO procedure</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>08/JUN/20</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP29, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>08/JUN/20</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Misleading info-file at&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter\">https://launchpad.support.sap.com/#/softwarecenter</a> &#160;about PL content</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>28/MAY/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: Old SAPJVM versions crashing on IBM POWER 9 hardware</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>28/MAY/20</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>IBM i: After installing a stack kernel 721 PL1300 or a stack kernel 722 PL900 a PXA error message occurs in the work processes</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>25/MAY/20</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>IBM i:&#160;After creating a new SAP System you find an ERROR trace in a log file of the work processes</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>05/MAY/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>parameter &#8220;autologout = 0&#8221; has been removed from &lt;sapsid&gt;adm C-shell scripts</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>09/MAR/20</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: System Rename does not support independent ASP</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>13/JAN/20</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0SP28, Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>23/DEC/19</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>SL Common GUI hangs when using Chrome</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>22/NOV/19</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: SWPM crashes with SIGSEGV due to high memory consumption</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>14/NOV/19</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Linux on IBM Power Systems (little endian) released for SAP ABAP systems based on SAP NetWeaver&#160;7.4&#160;and higher with SAP HANA or DB2 for z/OS</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>19/JUL/19</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>SAP System Installations on OS release IBM i V7R4</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>27/MAY/19</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP26,&#160;&#160;Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;26/APR/19</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>IBM i: When using kernel&#160;media <em>721 UC Kernel for SP22 - iSeries(os400)</em> (material number 51052417_4) replace the files SAPEXE.SAR and SAPEXEDB.SAR</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;21/JAN/19</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP25,&#160;&#160;Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;17/SEP/18</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP24,&#160;&#160;Initial delivery at RTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;10/SEP/18</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: Installation breaks with the error message \"Command FIXSAPOWN SID(*NONE) LIB('SAP&lt;SID&gt;LOAD') failed.\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;25/JUL/18</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: Installation breaks in the step createService with \"Could not start instance service of ...\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;25/JUN/18</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: Homogeneous system copy is&#160;broken for large save files</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;07/MAY/18</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP23</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;29/JAN/18</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: Installation or import fails due to coinciding table names</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;22/JAN/18</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: Standalone SAP WebDispatcher installation fails with \"TypeError: this.app has no properties\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;15/JAN/18</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP22</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;21/DEC/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>R3load aborts without error message when creating View CMRV_ODERADM_FIN</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;19/DEC/17</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Table splitting not allowed for Shell creation</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;15/DEC/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Software provisioning manager does not recognize correct HANA Client SPS12 media for AIX</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;27/SEP/17</p>\r\n</td>\r\n<td>1 (Restrictions)</td>\r\n<td>\r\n<p>System copy option \"Refresh Database\" Content is not released for SAP SCM.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;11/SEP/17</p>\r\n</td>\r\n<td>1 (Restrictions)</td>\r\n<td>\r\n<p>Oracle Database Vault is available but not yet released</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;11/SEP/17</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>Software Provisioning Manager aborts with error in phase Executing ABAP Report RUTDDLSCREATE</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;11/SEP/17</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;01/AUG/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: The SWPM breaks while checking the installation master media</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;17/JUL/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Up-to-date Installation using STACK_CONFIGURATION_FILE does not match options in welcome screen</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;22/MAY/17</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP20</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;16/MAY/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Windows: Error during uninstall in the context of a Database Migration Option (DMO) procedure</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;02/MAY/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i:&#160;Installation is aborted with function getpwuid( ) error \"A file or directory in the path name does not exist\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;28/APR/17</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>IBM i: After a distributed rename of a single instance, the other instances of the same SID and on the same host cannot be started anymore</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;06/APR/17</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>No file generated for Shell creation</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;28/MAR/17</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Too generic LABEL.ASC files matching SAPinst Package Requests</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;22/MAR/17</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>Up-to-date Installation ABAP: Implemented SAP Notes</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;06/MAR/17</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>MSS: Inconsistent Global Temporary Table (GTT) objects&#160;after hom. system copy</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;07/FEB/17</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Tampered Software Provisioning Manager</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;07/FEB/17</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release of Software Provisioning Manager 1.0 SP19</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;27/JAN/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Deviant&#160;procedure when using <em>Oracle 12c</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>05/JAN/17</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i: Installation is aborted because of a non-existing user account</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>20/DEC/16</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i:&#160;The <em>Additional Application Server on Windows:&#160;IBM DB2 for&#160;i</em> does not start</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>20/DEC/16</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i:&#160;The installation of an <em>Additional Application Server on Windows:&#160;IBM DB2 for&#160;i</em> fails in phase Install Basics</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>28/NOV/16</p>\r\n</td>\r\n<td>1</td>\r\n<td>\r\n<p>SAP kernel 745 is only supported with PLs higher than 200</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>11/NOV/16</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>IBM i: Make sure that the SWPM installation user (SAPIUSR) has correct profile settings</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>31/OCT/16</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>DB2-z/OS: Installation of solutions based on SAP NetWeaver 7.00, ... (added SAP Note&#160;2303045)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>26/OCT/16</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>IBM i:&#160;SWPM fails on step &#8222;R3load &#8211;testconnect&#8221;&#160;during installation&#160;with&#160;SAP Hana on IBM i</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;10/OCT/16</p>\r\n</td>\r\n<td>1</td>\r\n<td>\r\n<p>SAP NetWeaver MDM 7.1 Server&#160;SP17 installation and update&#160;moved to SWPM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;07/OCT/16</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release SL Toolset 1.0 SPS 18 with SWPM 1.0 SP 18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;07/OCT/16</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Mobile/Banking Options available in SAP NetWeaver Process Integration</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;05/OCT/16</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>DB2-z/OS: Additional application server (dialog) instance installation fails on Windows application server in domain</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;05/OCT/16</p>\r\n</td>\r\n<td>1</td>\r\n<td>\r\n<p>DB2-z/OS: CLI v11.1 required for SWPM SPS18 in general and&#160;SWPM SPS17 with&#160;AIX 7.2</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;28/SEP/16</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Update of SAP MMC failed</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;10/AUG/16</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>Missing entries in table BADIIMPL_ENH after installation of Business Suite 2016 (CRM/SRM/SCM 7.0 EHP4, ERP 6.0 EHP8) or&#160;S/4 Hana on-premise SR0</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;01/JUL/16</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>SAP Kernel 7.42: Apply SAP Note <a target=\"_blank\" href=\"/notes/2200230\">2200230</a> after installation</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;06/JUN/16</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release SL Toolset 1.0 SPS 17 with SWPM 1.0 SP 17</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>15/FEB/16</p>\r\n</td>\r\n<td>1-4</td>\r\n<td>\r\n<p>Release SL Toolset 1.0 SPS 16 with SWPM 1.0 SP 10</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>18/JAN/16</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>S/4HANA On-Premise 1511 SR0: Error during client copy</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>20/OCT/15</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>Release SL Toolset 1.0 SPS 15 with SWPM 1.0 SP 09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>14/OCT/15</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>Software provisioning for SAP systems optional standalone units on SAP HANA is now available on Debian for PowerPC (LINUX_PPC64)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>28/SEP/15</p>\r\n</td>\r\n<td>1</td>\r\n<td>\r\n<p>Release of Oracle replicate system rename</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>14/SEP/15</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>Release SL Toolset 1.0 SPS 14 with SWPM 1.0 SP 09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>18/AUG/15</p>\r\n</td>\r\n<td>1</td>\r\n<td>\r\n<p>New version of SAP kernel 7.42: 51049724_X replaces&#160;51049552_X.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>09/JUL/15</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>NW 7.4 SR2 Advanced Adapter Engine: Deploy Software Component &#8220;GWJPO&#8221; manually</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>20/MAY/15</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Updating R3load to the latest version</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>27/APR/15</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>Release SL Toolset 1.0 SPS 13 with SWPM 1.0 SP 08</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>20/MAR/15</p>\r\n</td>\r\n<td>3</td>\r\n<td>Errors related to report RUTCNVFUNCCRE1 in the SWPM log files</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>04/DEC/14</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>Corrupted Job definitions in installation export for ERP and SCM from Business Suite 2013 SR0, SR1 and SR2</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>27/NOV/14</p>\r\n</td>\r\n<td>3</td>\r\n<td>\r\n<p>Unicode conversion&#160;from IBM DB2 to any database using R3load 7.4x not possible</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>24/NOV/14</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>Release SL Toolset 1.0 SPS 12 with SWPM 1.0 SP 07</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>24/NOV/14</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>Further Automation for ABAP installation using stack XML file generated by the Maintenance Planner</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>10/NOV/14</p>\r\n</td>\r\n<td>2</td>\r\n<td>\r\n<p>Requesting an installation with media of old Support Releases</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>04/SEP/14</p>\r\n</td>\r\n<td>2</td>\r\n<td>IBM i: Java Installation - skip JDBC driver JTOpen version 8.3&#160;till 8.5</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>07/JUL/14</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>Release SL Toolset 1.0 SPS 11 with SWPM 1.0 SP 06</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>17/APR/14</p>\r\n</td>\r\n<td>3</td>\r\n<td>System Copy: Table depooling step/phase fails</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>15/APR/14</p>\r\n</td>\r\n<td>2</td>\r\n<td>SAP System Kernel Switch from 7.00/7.01/7.10/7.11 to 720(_EXT)</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>03/APR/14</p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>DB2-z/OS: Installation of solutions based on SAP NetWeaver 7.00, 7.01, 7.02, 7.10 or 7.11</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>18/MAR/14</td>\r\n<td>&nbsp;</td>\r\n<td>Release SL Toolset 1.0 SPS 10 with SWPM 1.0 SP 05</td>\r\n</tr>\r\n<tr>\r\n<td>09/JAN/14</td>\r\n<td>1</td>\r\n<td>Wrong information in the installation guide valid for SAP Systems based on SAP NetWeaver 7.1 and higher</td>\r\n</tr>\r\n<tr>\r\n<td>13/SEP/13</td>\r\n<td>3</td>\r\n<td>DB2-z/OS: J2EE server - update database statistics</td>\r\n</tr>\r\n<tr>\r\n<td>15/MAY/13</td>\r\n<td>3</td>\r\n<td>Table MLICHECK is reported as missing in database</td>\r\n</tr>\r\n<tr>\r\n<td>12/FEB/13&#160;&#160;</td>\r\n<td>2</td>\r\n<td>SAP Host Agent only: sapadm does not exist</td>\r\n</tr>\r\n<tr>\r\n<td>19/FEB/08&#160;</td>\r\n<td>3</td>\r\n<td>ABAP+Java: FSL-01003&#160;&#160;Unable to modify account user=\"&lt;sapsid&gt;adm\" uid=\"&lt;user ID of &lt;sapsid&gt;adm\"</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p><strong>1&#160;&#160;General Information</strong></p>\r\n<p><strong>++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>********************************</strong></p>\r\n<p><strong>Documentation</strong></p>\r\n<p><strong>********************************</strong></p>\r\n<p>Always use the latest version of the documentation available at <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Provisioning</p>\r\n<p>&#160;</p>\r\n<p><strong>********************************</strong></p>\r\n<p><strong>Kernel Media</strong></p>\r\n<p><strong>********************************</strong></p>\r\n<p>For downloading the complete kernel media, go to <a target=\"_blank\" href=\"https://support.sap.com/swdc\">https://support.sap.com/swdc</a>&#160;-&gt;&#160;https://support.sap.com/swdc -&gt; SUPPORT PACKAGES &amp; PATCHES -&gt; By Category -&gt; ADDITIONAL COMPONENTS -&gt;&#160; SAP KERNEL -&gt; SAP KERNEL 64-BIT UNICODE -&gt;&#160; SAP KERNEL &lt;Version&gt; 64-BIT UNICODE -&gt; &lt;Select your OS&gt; .</p>\r\n<p><strong>SAP Systems based on SAP NetWeaver 7.1 and Higher:&#160;</strong>You can&#160;continue to&#160;provide complete SAP Kernel media,&#160;but&#160;we recommend that you&#160;&#160;<strong>download the specific SAP Kernel archives</strong> for your SAP system installation.</p>\r\n<p>For more information, see the central SAP Notes:</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/2083594\">2083594&#160;</a>&#160;- SAP Kernel Versions and SAP Kernel Patch Levels</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/3116151\">3116151&#160;</a>&#160;- SP Stack Kernel Schedule Forecast</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/1744209\">1744209&#160;</a>&#160;- SAP Kernel 720, 721 and 722: Versions and Kernel Patch Levels</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/1969546\">1969546&#160;</a>&#160;- Release Roadmap for Kernel 74x and 75x</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/1802333\">1802333&#160;</a>&#160;- Finding information about regressions in the SAP kernel</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/19466\">19466&#160;</a>&#160;- Downloading SAP kernel patches</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/2966761\">2966761&#160;</a>&#160;- Overview of SAP Kernel Correction Archives</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/2966621\">2966621&#160;</a>&#160;- Overview of Kernel-Related Software Components</p>\r\n<p class=\"p\"><a target=\"_blank\" href=\"https://help.sap.com/docs/link-disclaimer?site=https://launchpad.support.sap.com/#/notes/953653\">953653&#160;</a>- Rolling Kernel Switch</p>\r\n\r\n<p>&#160;</p>\r\n\r\n<p><strong>**************************************</strong></p>\r\n<p><strong>SAP Notes for Special Implementation Types</strong></p>\r\n<p><strong>**************************************</strong></p>\r\n<p><strong>System Copy:&#160;<a target=\"_blank\" href=\"/notes/1738258\">1738258</a></strong></p>\r\n<p><strong>System Rename:&#160;<a target=\"_blank\" href=\"/notes/1619720\">1619720</a></strong></p>\r\n<p><strong>Dual-Stack Split:&#160;<a target=\"_blank\" href=\"/notes/1797362\">1797362</a></strong></p>\r\n<p><strong>********************************</strong></p>\r\n<p><strong>Database-Specific SAP Notes</strong></p>\r\n<p><strong>********************************</strong></p>\r\n<p><strong>SAP HANA Database:&#160;<a target=\"_blank\" href=\"/notes/2365849\">2365849</a></strong></p>\r\n<p><strong>SAP ASE:&#160;<a target=\"_blank\" href=\"/notes/1748888\">1748888</a></strong></p>\r\n<p><strong>SAP MaxDB:&#160;<a target=\"_blank\" href=\"/notes/2365014\">2365014</a></strong></p>\r\n<p><strong>Oracle Database:&#160;<a target=\"_blank\" href=\"/notes/2172935\">2172935</a></strong></p>\r\n<p><strong>IBM Db2 for UNIX, Linux, and Windows:&#160;<a target=\"_blank\" href=\"/notes/1707361\">1707361</a>&#160;(UNIX),&#160;<a target=\"_blank\" href=\"/notes/1724557\">1724557</a>&#160;(Windows)</strong></p>\r\n<p><strong>IBM Db2 for z/OS:&#160;<a target=\"_blank\" href=\"/notes/1500074\">1500074</a>&#160;(V10),&#160;<a target=\"_blank\" href=\"/notes/1850409\">1850409</a>&#160;(V11),&#160;<a target=\"_blank\" href=\"/notes/2303045\">2303045</a>&#160;(V12)</strong></p>\r\n<p><strong>IBM Db2 for i:&#160;<a target=\"_blank\" href=\"/notes/1748985\">1748985</a></strong></p>\r\n<p><strong>MS SQL Server:&#160;<a target=\"_blank\" href=\"/notes/1710994\">1710994</a></strong></p>\r\n<p><strong>********************************</strong></p>\r\n<p><strong>Restrictions</strong></p>\r\n<p><strong>********************************</strong><br /><br /></p>\r\n<p>Always use the latest version of the Software Provisioning Manager archive, available at <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter\">https://launchpad.support.sap.com/#/softwarecenter</a>&#160;. For more information about how to download and extract the Software Provisioning Manager archive, see the Software Provisioning Manager guides at <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Provisioning .</p>\r\n<p><strong>Currently, the following restrictions apply:</strong></p>\r\n<p><span style=\"text-decoration: underline;\">General Restrictions:</span></p>\r\n<ul>\r\n<li>Effective immediately, Software Provisioning Manager&#160;<strong>no longer supports Linux PPC64 big endian</strong>&#160;and the&#160;<strong>operating system versions listed in the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2998013\">2998013</a></strong>.</li>\r\n<ul>\r\n<li>If your current operating system is listed as deprecated in this SAP Note, we strongly recommend that you <strong>migrate to a supported platform</strong>.</li>\r\n<li>If you continue to use Software Provisioning Manager on <strong>Linux PPC64 big endian</strong>, you do so <strong>at your own risk and without support from SAP</strong>.</li>\r\n<li>No longer supported&#160;<strong>Linux PPC64 big endian</strong>&#160;and the&#160;<strong>operating system versions are still available with the \"frozen\" software provisioning manager 1.0 SP35 (<strong>see SAP Note&#160;<a target=\"_blank\" href=\"/notes/3220901\">3220901</a>).</strong></strong></li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Application Server ABAP based on SAP NetWeaver 7.10, 7.11, 7.20, 7.30, 7.40 SR1 are supported in mainstream maintenance only until the end of 2020. Extended maintenance will not be provided.</li>\r\n<li>Application Server Java based on SAP NetWeaver 7.10, 7.11, 7.20, 7.30, 7.31, 7.40, 7.40 SR1, 7.40 SR2 (except SAP Solution Manager 7.2 SR2 Java) are supported in mainstream maintenance only until the end of 2020. Extended maintenance will not be provided. For more information, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2980160\">2980160</a>. You can download the last published version of the guide set for the last Software Provisioning Manager 1.0 SP30 for out-of-maintenance products (SWPM10RMSP30_&lt;Version&gt;.SAR) from SAP Note&#160;<a target=\"_blank\" href=\"/notes/2980160\">2980160</a>. This guide set covers only the SAP product versions which have reached end of maintenance.</li>\r\n<li>SAP&#160;<strong>restricted</strong>&#160;the<strong>&#160;maintenance for operating system versions</strong>&#160;that have been initially released with SAP Kernel 7.20 but are&#160;<strong>no longer supported for SAP Kernel 7.40 and higher</strong>. See&#160;<strong>SAP Note&#160;<a target=\"_blank\" href=\"/notes/2505142\">2505142</a></strong>&#160;for more details.</li>\r\n<li>\r\n<p>Do&#160;<strong>not</strong>&#160;use this SAP Note for installing, renaming, copying, or splitting&#160;a&#160;<strong>Java or dual-stack</strong>&#160;system based on SAP NetWeaver&#160;<strong>7.0x</strong>.<br />SAP NetWeaver 7.0x Application Server Java reached end of maintenance by the end of 2017. SAP recommends upgrading to a more recent version. For more information, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2595196\">2595196</a>&#160;. Therefore, the last published version of the Java and dual-stack guides for the last Software Provisioning Manager 1.0 SP22 tool release are no longer available via the common access pages. You can access them via SAP Note&#160;<a target=\"_blank\" href=\"/notes/2595196\">2595196</a>&#160;.</p>\r\n</li>\r\n<li>\r\n<p>Products with the additional information \"<strong>SAP internal only\"</strong>&#160;on the Welcome screen are for SAP internal purposes only and&#160;<strong>not permitted to be used outside this purpose</strong>.</p>\r\n</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">SWPM Version-Specific Restrictions:</span></p>\r\n<ul>\r\n<li><strong>SWPM*.SAR</strong></li>\r\n<ul>\r\n<li><strong>The following products are out-of-maintenance, options will be removed from software provisioning manager 1.0 SP37, options are still available with the &#8220;frozen&#8221; software provisioning manager 1.0 SP35 (see SAP Note <a target=\"_blank\" href=\"/notes/3220901\">3220901</a>)):</strong></li>\r\n<ul>\r\n<li><strong>SAP S/4HANA, on-premise edition 1511 (OOM Dec 2020)</strong></li>\r\n<li><strong>SAP Business Suite powered by SAP HANA (OOM Dec 2020)</strong></li>\r\n<li><strong>EHP2 for SAP CRM 7.0 On HANA</strong></li>\r\n<li><strong>EHP6 for SAP ERP 6.0 On HANA</strong></li>\r\n<li><strong>EHP2 for SAP SCM 7.0 On HANA</strong></li>\r\n<li><strong>BW/4HANA 1.0 (OOM Dec 2021)</strong></li>\r\n<li><strong>S/4 HANA 1610 (OOM Dec 2021)</strong></li>\r\n</ul>\r\n<li><strong>SAP NetWeaver 7.3 on HANA is only supported for BW. </strong>For more information, see <strong>SAP Note&#160;<a target=\"_blank\" href=\"/notes/1600929\">1600929</a></strong>.</li>\r\n<li><strong>Always</strong><strong>&#160;check</strong>&#160;the Product Availability Matrix&#160; at: <strong><a target=\"_blank\" href=\"https://support.sap.com/pam\">https://support.sap.com/pam</a></strong></li>\r\n<li><strong>Diagnostics Agent:</strong> Check the&#160;<strong>release note <a target=\"_blank\" href=\"/notes/1858920\">1858920</a></strong> for tool-related restrictions.</li>\r\n<li><strong>Installation of SAP Solution Manager 7.2 SR1:</strong> At least Software Provisioning Manager 1.0 SP 10 PL7 (SWPM10SP10_7-20009697.SAR) or higher is required.&#160;</li>\r\n<li><strong>System Rename: </strong>Check the <strong>release note <a target=\"_blank\" href=\"/notes/1619720\">1619720</a></strong>&#160;for tool-related restrictions.</li>\r\n<li><strong>Dual-Stack Split:</strong> Check the <strong>release note <a target=\"_blank\" href=\"/notes/1797362\">1797362</a></strong>&#160;for tool-related restrictions.</li>\r\n<li><strong><span style=\"font-size: medium;\">Installation</span></strong> of <strong>additional application server Java (AS Java)</strong> on <strong>IBM DB2 for z/OS</strong> after <strong>dual-stack split</strong> is no longer restricted, you can do this now by following the instructions in SAP Note <a target=\"_blank\" href=\"/notes/2709131\">2709131</a>.</li>\r\n<li><strong><strong>Oracle Linux 5 only: </strong></strong>7.4* Kernel does not run, see<strong> <strong>SAP Note</strong> <a target=\"_blank\" href=\"/notes/1973403\">1973403</a> </strong>for more details.</li>\r\n<li><strong>SAP HANA DB only</strong>:</li>\r\n<ul>\r\n<li>Consider the restrictions listed in&#160;<strong>SAP Note <a target=\"_blank\" href=\"/notes/1718576\">1718576</a></strong>&#160;for system copy from SAP HANA based systems.</li>\r\n<li>For migration towards SAP HANA database, you must read <strong>SAP Note <a target=\"_blank\" href=\"/notes/1806935\">1806935</a></strong> to avoid data corruption.</li>\r\n<li>Use the \"Based on AS ABAP towards SAP HANA\" option in the <em>Welcome</em> screen</li>\r\n<li>Linux IA64 and HP-UX on PA-RISC 64-bit are not supported.</li>\r\n<li>SAP Solution Manager 7.1 for SAP HANA DB: High-availability with MS Failover Clustering on demand only.</li>\r\n</ul>\r\n<li><strong>Declustering and depooling:</strong> see <strong>SAP Note <a target=\"_blank\" href=\"/notes/1892354\">1892354</a></strong>&#160;until further notice.</li>\r\n<li>System copy option \"<strong>Refresh Database</strong> <strong>Content</strong>\" is currently&#160;<strong>not</strong> released for SAP SCM.</li>\r\n<li>\r\n<p><strong>System copy options for SAP Solution Manager 7.2 Java Support Release 1:</strong><br />Use these options only for SAP Solution Manager 7.2 Java&#160; lower than SP09.<br />For SAP Solution Manager 7.2 Java SP09 or higher, use the options of SAP NetWeaver 7.5 Java.</p>\r\n</li>\r\n<li>\r\n<p><strong>Options to install additional SAP system instances for SAP Solution Manager 7.2 Java Support Release 1:</strong><br />Use these options only for SAP Solution Manager 7.2 Java&#160; lower than SP09.<br />For SAP Solution Manager 7.2 Java SP09 or higher, use the options of SAP NetWeaver 7.5 Java.</p>\r\n</li>\r\n<li>User groups SAP_SAP_GlobalAdmin and SAP_SAP_LocalAdmin are no longer available.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>70SWPM*.SAR</strong></li>\r\n<ul>\r\n<li>Although <strong>Oracle Database Vault</strong> is already available in the Software Provisioning Manager and documented in the Software Provisioning Manager guides, it is not yet released for <strong>system rename</strong> until further notice.</li>\r\n<li><strong>Always check </strong>the Product Availability Matrix at: <strong><a target=\"_blank\" href=\"https://support.sap.com/pam\">https://support.sap.com/pam</a></strong></li>\r\n<li><strong>Diagnostics Agent:</strong> Check the&#160;<strong>release&#160;note <a target=\"_blank\" href=\"/notes/1858920\">1858920</a></strong> for tool-related restrictions.</li>\r\n<li><strong>System Rename: </strong>Check the <strong>release note <a target=\"_blank\" href=\"/notes/1619720\">1619720</a> </strong>for tool-related restrictions.</li>\r\n<li><strong>Dual-Stack Split: </strong>Check the <strong>release note <a target=\"_blank\" href=\"/notes/1797362\">1797362</a></strong> for tool-related restrictions.</li>\r\n<li><strong>Installation</strong> of <strong>additional application server Java (AS Java)</strong> on <strong>IBM DB2 for z/OS</strong> after <strong>dual-stack split</strong> is no longer restricted, you can do this now by following the instructions in <strong>SAP Note <a target=\"_blank\" href=\"/notes/2709131\">2709131</a></strong>.</li>\r\n<li>System copy option \"<strong>Refresh Database Content</strong>\" is currently&#160;<strong>not</strong> released for SAP SCM.</li>\r\n<li>User groups SAP_SAP_GlobalAdmin and SAP_SAP_LocalAdmin are no longer available.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p><strong>2&#160;&#160;Planning and Preparation</strong></p>\r\n<p><strong>+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p><strong>------------------------&lt;</strong><strong>********</strong><strong><strong>, 28/Aug/23 &gt;-------------------------</strong>&#160;</strong></p>\r\n<p><strong>SAP Add. Appl. Server Installation on Windows for IBM Db2 for i</strong></p>\r\n<p>On all Windows hosts running an SAP Add. Appl. Server for IBM Db2 for i, the following three files and directory caches must be set to 0:</p>\r\n<p>Run the commands in a PowerShell.</p>\r\n<p>1. Set-SmbClientConfiguration -FileInfoCacheLifetime 0</p>\r\n<p>2.&#160;Set-SmbClientConfiguration -FileNotFoundCacheLifetime 0</p>\r\n<p>3.&#160;Set-SmbClientConfiguration -DirectoryCacheLifetime 0</p>\r\n<p>For more information, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/3358301\">3358301&#160;- Windows/IBM i Synchronization Issue</a>.</p>\r\n<p><strong>------------------------&lt;</strong><strong>********</strong><strong><strong>, 30/May/22 &gt;-------------------------</strong>&#160;</strong></p>\r\n<p><strong>SAP System Installations on OS release IBM i V7R5</strong></p>\r\n<p>As for previous IBM i OS releases, you must make sure&#160;that&#160;the IBM i OS release V7R5 partition is prepared for SAP.<br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/3152440\">3152440</a> - IBM i: Known Issues with OS Release IBM i 7.5.</p>\r\n<p>On OS release IBM i V7R5, the following kernels are supported:</p>\r\n<ul>\r\n<li>Kernel 7.22 EXT for SAP systems based on SAP NetWeaver lower than 7.40</li>\r\n<li>Kernel 7.53 is for SAP systems based on SAP NetWeaver 7.40 and higher.</li>\r\n</ul>\r\n<p>For more information, see SAP Note <a target=\"_blank\" href=\"/notes/3153907\">3153907</a> - Support of the new OS Release IBM i 7.5.</p>\r\n<p>&#160;The software provisioning manager is using the stack kernel for installations. You must use the following SAP stack kernel patch level on IBM i V7R5:</p>\r\n<ul>\r\n<li>7.22 EXT: PL&#160; &#160;1200 (will be released soon) and higher</li>\r\n<li>7.53:&#160; &#160; &#160; &#160;PL&#160; &#160;1100 (already released) and higher</li>\r\n</ul>\r\n<p>If you use an older SAP stack kernel, you may experience an issue within your installation.</p>\r\n<p>For more information, see SAP Note SAP Note <a target=\"_blank\" href=\"/notes/3205488\">3205488</a> - IBM i: SAP System Installation on IBM i OS release V7R5 using unsupported old SAP stack kernel.</p>\r\n<p><strong>70SWPM</strong>: SAP system installations based on SAP NetWeaver 7.0x</p>\r\n<p>You can follow the generic instructions of the SAP installation documentation.<br />In the installation dialog phase the dialog \"SAP System &gt; Unpack Archives\" will ask you for newer kernel archives.<br />Put into this dialog the location of the archive files of the SAP Kernel 7.22 EXT PL 1200 and higher. (As long as the&#160;SAP Kernel 7.22 EXT PL 1200 is not available, you must use the&#160;SAP Note SAP Note&#160;<a target=\"_blank\" href=\"/notes/3205488\">3205488</a>&#160;- IBM i: SAP System Installation on IBM i OS release V7R5 using unsupported old SAP stack kernel.)</p>\r\n<p>You will find the current SAP Kernel 7.22 EXT archive files in this location:<br /><br />support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; Additional Components &gt; SAP KERNEL &gt; SAP KERNEL 64-BIT (UNICODE) &gt; SAP KERNEL 7.22 EXT 64-BIT (UC)<br /><br />Finally the installation can be continued as usual.</p>\r\n<p><strong>SWPM</strong>: SAP system installations based on SAP NetWeaver 7.XX</p>\r\n<p>You can follow the generic instructions of the SAP installation documentation.<br />When you are installing an SAP system based on SAP NW lower than 7.4 you can proceed as described for 70SWPM:<br />Download the archive files of the SAP Kernel 7.22 EXT PL 1200 and higher from the following location:</p>\r\n<p>support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; Additional Components &gt; SAP KERNEL &gt; SAP KERNEL 64-BIT (UNICODE) &gt; SAP KERNEL 7.22 EXT 64-BIT (UC)</p>\r\n<p>Put into the dialog \"Software Package Browser\" the kernel archive files of SAP Kernel 7.22 EXT PL 1200 and higher.&#160;(As long as the&#160;SAP Kernel 7.22 EXT PL 1200 is not available, you must use the&#160;SAP Note SAP Note&#160;<a target=\"_blank\" href=\"/notes/3205488\">3205488</a>&#160;- IBM i: SAP System Installation on IBM i OS release V7R5 using unsupported old SAP stack kernel.)&#160;<br /><br />When you are installing an SAP system based on SAP NW 7.4 and higher you proceed as follows:</p>\r\n<p>Download the archive files of the SAP Kernel 7.53 PL 1100 and higher from the following location:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; Additional Components &gt; SAP KERNEL &gt; SAP KERNEL 64-BIT (UNICODE) &gt; SAP KERNEL 7.53 64-BIT (UNICODE)&#65279;</span></p>\r\n<p>Put into the dialog \"Software Package Browser\" the kernel archive files of SAP Kernel 7.53 PL 1100 and higher.</p>\r\n<p>Make sure&#160;that&#160;you are using the SAPIGSEXE archive compatible to SAP Kernel 7.53:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; SAP Frontend Components &gt; SAP IGS &gt; SAP IGS 7.53 &gt; OS/400&#65279;</span></p>\r\n<p>Finally, the installation can be continued as usual.</p>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong>-------------------------------------&lt;D025095, SEP 17, 2020&gt;---------------------------------------------------------------------</strong></p>\r\n<p><strong>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure that the SAPJVM 8.1 archive you use has a patch level of 63 or lower.</strong></p>\r\n<p><strong>Note:</strong> SAPJVM 8.1 patch-level restrictions as defined in SAP Note <a target=\"_blank\" href=\"/notes/2951691\">2951691</a>&#160;do not apply if you are using SAP Netweaver 7.5 SP22 Java media.</p>\r\n<p>If you want to install an SAP system based on SAP NetWeaver 7.5 AS Java, you must make sure that the SAPJVM 8.1 archive you use has a patch level of 63 or lower. Otherwise you will run into an error while running the installation of the primary application server instance (see entry&#160;&#160;<strong>Running the Software Provisioning Manager (SWPM)</strong><strong>&#160;-&gt;&#160;PAS installation of an SAP Java system based on SAP NetWeaver 7.5 fails due to SAPJVM Patch Level&#160;</strong>in this SAP Note).</p>\r\n<ul>\r\n<li>If you use physical installation media (DVD or BlueRay, see section&#160;<em>Media Required for the Installation&#160;- Listed by SAP System Instance</em>&#160;in the installation guide for your &lt;OS&gt; and &lt;DB&gt;): Nothing to do, because physical installation media still contain a SAP Kernel patch level which implies a&#160;SAPJVM 8.1 archive with a patch level lower than 63.</li>\r\n<li>If you want to run an archive-based installation (see section&#160;<em>Downloading SAP Kernel Archives (Archive-Based Installation)&#160;</em>in the installation guide for your &lt;OS&gt; and &lt;DB&gt;): Make sure that you download&#160;SAPJVM 8.1 patch level of 63&#160;(SAPJVM8_63-&lt;...&gt;.SAR) or lower.</li>\r\n<li>If you want to perform an installation using a stack configuration file (see sections&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Installation Using a Stack Configuration File&#160;and&#160;Downloading Software Packages for a Maintenance Planner Transaction<em>&#160;&#65279;</em></span>in the installation guide for your &lt;OS&gt; and &lt;DB&gt;):&#160;Make sure that you download&#160;SAPJVM 8.1 patch level of 63&#160;(SAPJVM8_63-&lt;...&gt;.SAR) or lower.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">See also the following items within this SAP Note:&#160;</span></p>\r\n<p><strong>Running the Software Provisioning Manager (SWPM)&#160;-&gt;</strong>&#160;<strong>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java:&#160;</strong><strong>PAS installation fails due to SAPJVM Patch Level</strong></p>\r\n<p><strong>Follow-Up Activities</strong> <strong>-&gt;</strong>&#160;<strong>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure you apply the latest Kernel and AS Java Patches</strong></p>\r\n<p><strong><strong><strong><strong>------------------------&lt;D025095</strong></strong><strong><strong>, 08/Jun/20 &gt;-------------------------</strong>&#160;</strong></strong></strong></p>\r\n<p><strong>Misleading info-file at&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter\">https://launchpad.support.sap.com/#/softwarecenter</a> about PL content</strong></p>\r\n<p><span style=\"text-decoration: underline;\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text45\" style=\"text-align: left;\">Symptom:</span></span></p>\r\n<p><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text45\" style=\"text-align: left;\">The info file of the [70]SWPM 1.0 SP28 PL1-7 archive contains entries which only belong to PL8.</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" style=\"text-align: left;\">Solution:</span></span></p>\r\n<p><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" style=\"text-align: left;\">SAP recommends using always the latest available Support Package and Patch Level of [70]SWPM10SP&lt;No&gt;_&lt;PL&gt;_&lt;No&gt;.SAR.</span></p>\r\n<p><strong><strong><strong>------------------------&lt;D026110</strong></strong><strong><strong>, 14/Nov/19 &gt;-------------------------</strong>&#160;</strong></strong></p>\r\n<p><strong><strong>Linux on IBM Power Systems (little endian) released for SAP ABAP systems based on SAP NetWeaver&#160;<strong>7.4</strong>&#160;and higher with SAP HANA or DB2 for z/OS</strong></strong></p>\r\n<p>Support of Linux on IBM Power Systems (little endian) is&#160;released for SAP ABAP systems based on SAP NetWeaver&#160;7.4&#160;and higher with SAP HANA or DB2 for z/OS. For more information, see&#160;SAP Notes&#160;<a target=\"_blank\" href=\"/notes/2378874\">2378874</a>,&#160;&#160;<a target=\"_blank\" href=\"/notes/2695381\">2695381</a>, and <a target=\"_blank\" href=\"/notes/2618169\">2618169</a>.</p>\r\n<p><strong><strong>------------------------&lt;</strong></strong><strong>********</strong><strong><strong>, 19/Jul/19 &gt;-------------------------</strong>&#160;</strong></p>\r\n<p><strong>SAP System Installations on OS release IBM i V7R4</strong></p>\r\n<p>(latest change: 01/Oct/19)</p>\r\n<p>As for previous IBM i OS releases, you must make sure the IBM i OS release V7R4 partition is prepared for SAP.<br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2786037\">2786037</a> - IBM i: Known Issues with OS Release IBM i 7.4.</p>\r\n<p>On OS release IBM i V7R4, the following kernels are supported:</p>\r\n<ul>\r\n<li>Kernel 7.21 EXT and 7.22 EXT for SAP systems based on SAP Netweaver lower than 7.40</li>\r\n<li>Kernel 7.53 is for SAP systems based on SAP Netweaver 7.40 and higher.</li>\r\n</ul>\r\n<p>For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2790651\">2790651</a> - Support of the new OS Release IBM i 7.4.</p>\r\n<p>&#160;The software provisioning manager uses the stack kernel for installations. You must use the following SAP stack kernel patch level on IBM i V7R4:</p>\r\n<ul>\r\n<li>7.21 EXT: PL 1300 (released August 14, 2019)&#160;and higher</li>\r\n<li>7.22 EXT: PL&#160; &#160;900 (released September 27, 2019) and higher</li>\r\n<li>7.53&#160; &#160;&#160;&#160;&#160;&#160;&#160;: PL&#160; &#160;500 (released September 4, 2019) and higher</li>\r\n</ul>\r\n<p>If you use an older SAP stack kernel you may experience an issue within your installation.</p>\r\n<p>For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2817632\">2817632 </a>- IBM i: SAP System Installation on IBM i OS release V7R4 using unsupported old SAP stack kernel.</p>\r\n<p><strong>70SWPM</strong>: SAP system installations based on SAP NetWeaver 7.0x</p>\r\n<p>You can follow the generic instructions of the SAP installation documentation.<br />In the installation dialog phase the dialog \"SAP System &gt; Unpack Archives\" will ask you for newer kernel archives.<br />Put into this dialog the location of the archive files of the SAP Kernel 7.21 EXT PL 1300 and higher.<br />You will find the current SAP Kernel 7.21 EXT archive files in this location:<br /><br />support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; Additional Components &gt; SAP KERNEL &gt; SAP KERNEL 64-BIT (UNICODE) &gt; SAP KERNEL 7.21 EXT 64-BIT (UC)<br /><br />Finally the installation can be continued as usual.</p>\r\n<p><strong>SWPM</strong>: SAP system installations based on SAP NetWeaver 7.XX</p>\r\n<p>You can follow the generic instructions of the SAP installation documentation.<br />When you are installing an SAP system based on SAP NW lower than 7.4 you can proceed as described for 70SWPM:<br />Download the archive files of the SAP Kernel 7.21 EXT PL 1300 and higher from the following location:</p>\r\n<p>support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; Additional Components &gt; SAP KERNEL &gt; SAP KERNEL 64-BIT (UNICODE) &gt; SAP KERNEL 7.21 EXT 64-BIT (UC)</p>\r\n<p>Put into the dialog \"Software Package Browser\" the kernel archive files of SAP Kernel 7.21 EXT PL 1300 and higher.<br /><br />When you are installing an SAP system based on SAP NW 7.4 and higher you proceed as follows:</p>\r\n<p>Download the archive files of the SAP Kernel 7.53 PL 500 and higher from the following location:</p>\r\n<p>support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; Additional Components &gt; SAP KERNEL &gt; SAP KERNEL 64-BIT (UNICODE) &gt; SAP KERNEL 7.53 64-BIT (UNICODE)</p>\r\n<p>Put into the dialog \"Software Package Browser\" the kernel archive files of SAP Kernel 7.53 PL 500 and higher.</p>\r\n<p>Make sure you are using the SAPIGSEXE archive compatible to SAP Kernel 7.53:</p>\r\n<p>support.sap.com &gt; Download Software &gt; SUPPORT PACKAGES &amp; PATCHES &gt; By Category &gt; SAP Frontend Components &gt; SAP IGS &gt; SAP IGS 7.53 &gt; OS/400</p>\r\n<p>Finally, the installation can be continued as usual.</p>\r\n<p><strong>------------------------&lt; ********, 26/Apr/19 &gt;-------------------------</strong></p>\r\n<p><strong><strong>IBM i 7.3 &amp; </strong></strong><strong><strong>NW 7.0x based installation options only: </strong></strong><strong>When using kernel media <em>721 UC Kernel for SP22 - iSeries(os400)</em> (material number 51052417_4) replace the files SAPEXE.SAR and SAPEXEDB.SAR</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The kernel media <em>721 UC Kernel for SP22 - iSeries(os400)</em> has the stack kernel version 7.21 UC patchlevel 900. <br />For IBM i 7.3 you need at least stack kernel 7.21 UC patchlevel 1,000 and higher.&#160;<br />Using kernel version 7.21 UC patchlevel 900 will most probably lead into the&#160;following issue: <em>An installation option&#160;breaks in the step createService with \"Could not start instance service of ...\"<br /></em>This issue is described in section 3 of this SAP Note.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ul>\r\n<li>Download for a NetWeaver 7.0x based installation option the stack kernel 7.21 UC files SAPEXE.SAR and SAPEXEDB.SAR with a patchlevel 1,000 and higher.</li>\r\n<li>The software provisioning manager 70SWPM will ask in the dialog phase of the installation option not only for the kernel media but also&#160;for the files SAPEXE.SAR and SAPEXEDB.SAR.<br />In the dialog for additional kernel installation SAR files you put in the download directory of the files SAPEXE.SAR and SAPEXEDB.SAR. In this case the SAPEXE.SAR and SAPEXEDB.SAR files of the kernel media will be replaced by the downloaded kernel files&#160;and the installation&#160;step <em>createService</em> will run through successfully.&#160;</li>\r\n</ul>\r\n<p><strong>&#160;</strong></p>\r\n<p>------------------------&lt; D025095, 06/APR/17&gt;-------------------------</p>\r\n<p><strong>No file generated for Shell creation</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You are using Software Provisioning Manager for Shell creation but no files are generated during tool run.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Ensure that you have set the environment variable SAP_LT_SHELL_CREATION=TRUE before starting Software Provisioning Manager. For further problems create an incident at the application queue, e.g. CA-TDM-SHL.</p>\r\n<p>------------------------&lt; D037196, 19/DEC/17&gt;-------------------------</p>\r\n<p><strong>Table splitting not allowed for Shell creation</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You are using Software Provisioning Manager (SWPM) for database export for Shell creation and SWPM does not accept&#160;that *.WHR files already exist.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Make sure&#160;that you&#160;use the latest version of SWPM.</p>\r\n<p>&#160;</p>\r\n<p>------------------------&lt; D025095, 28/MAR/17&gt;-------------------------</p>\r\n<p><strong>Too generic LABEL.ASC files matching SAPinst Package Requests</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>Software Provisioning Manager reports an error message like the following:</p>\r\n<ul>\r\n<li>Last error reported by the step: Empty package path. DETAILS: An empty package path was returned by the media server.(Requested package : SCA ) SOLUTION: If Software Provisioning Manager is running with option SAPINST_SKIP_DIALOGS=true, run the service again without that option and you will be asked about the missing package location. If you want to automate the installation, make the package location visible to Software Provisioning Manager ( modify start_dir.cd or LABELIDX.ASC).</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Analysis:</span></p>\r\n<ul>\r\n<li>ABAP Language Media contains LABEL.ASC file in DATA_UNITS root directory with too generic definition, for example: \"SAP:::MM::D51048524_3:005056a214d6:NW740_SR2\" <br />and match therefore for package requests like <br />&lt;package mediaName=\"Java Component NW740 SR2 (folder JAVA_J2EE_OSINDEP_UT)\" name=\"SCA\" signed=\"true\" label=\"SAP:UT:SR2740:*:*:*\"/&gt;</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Solution:</span></span></p>\r\n<ul>\r\n<li>Make sure that you renamed LABEL.ASC to for instance&#160;_LABEL.ASC on the ABAP Language Media when making it available to Software Provisioning Manager.</li>\r\n</ul>\r\n<p>------------------------&lt; D025095, 07/FEB/17&gt;-------------------------</p>\r\n<p><strong>Tampered Software Provisioning Manager</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>Software Provisioning Manager reports an error message like the following:</p>\r\n<ul>\r\n<li>\"Verification of &lt;a Software Provisioning Manager file&gt; failed. Content has been tampered.\" or</li>\r\n<li>\"Data unit at &lt;a location of Software Provisioning Manager files&gt; is not signed&#160;with an official signature.\"</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ul>\r\n<li>Use the latest Software Provisioning Manager archive as downloaded and extracted from <a target=\"_blank\" href=\"http://support.sap.com/swdc\">http://support.sap.com/swdc</a> and do not change any of the extracted files.</li>\r\n<li>For options of deprecated Support Releases based on SAP NetWeaver 7.x,&#160;SAP Support should guide how to get the media in restricted availability and to start the sapinst executable from the Software Provisioning Manager archive with the first parameter <em>&lt;path and name&#160;of deprecated_product.catalog&gt;</em></li>\r\n<li>Only adjust XML control files in the &lt;Installation Directory&gt; when guided by SAP support.</li>\r\n<li>Report an incident when the error persists.</li>\r\n</ul>\r\n<p><br />------------------------&lt; ********, 11/NOV/16 &gt;-------------------------</p>\r\n<p><strong>IBM i: Make sure that the SWPM installation user (SAPIUSR) has correct profile settings</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You want to run Software Provisioning Manager on IBM i.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ul>\r\n<li>You must setup your SWPM installation user profile (for example SAPIUSR) correctly for SAP system installations to run successfully. In particular, the values of the attributes USRCLS, SPCAUT, OWNER, LANGID, CNTRYID, CCSID and LOCALE have to be set correctly.</li>\r\n<li>If you created your installation user as described in the installation documentation, this user is already correct. If you created your installation user using a different command than described in the installation guide, you must make sure this user is configured correctly for your next SAP system installation by calling the following command:<br /><code>CHGUSRPRF USRPRF(SAPIUSR) USRCLS(*SECOFR) TEXT('SAP installation user') SPCAUT(*USRCLS) OWNER(*USRPRF) LANGID(ENU) CNTRYID(US) CCSID(500) LOCALE(*NONE)</code></li>\r\n</ul>\r\n<p>------------------------&lt; D025095, 07/OCT/16 &gt;-------------------------</p>\r\n<p><strong>Mobile/Banking Options available in SAP NetWeaver Process Integration</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<ul>\r\n<li>No toplevel entry in Welcome screen for SAP NW Mobile/Banking 7.1 (including EHP1) with SWPM 1.0&#160; SP 18 and subsequent</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ul>\r\n<li>The options moved one level down to node SAP NetWeaver Process Integration 7.1 (including EHP1) as there better fitting to all available documentation for the related product versions.</li>\r\n</ul>\r\n<p>------------------------&lt; D025095, 13/NOV/17 &gt;-------------------------</p>\r\n<p><strong>Updating R3load to the latest version</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You are planning to you to use the latest R3load for an ABAP-based installation or system copy which is not part of a Kernel DVD or downloaded SAP Kernel archive or SAP system executable directory.</p>\r\n<p><span style=\"text-decoration: underline;\">Details:</span></p>\r\n<p>Note, that Software Provisioning Manager contains an up-to-date R3load, which will be used during installation or system copy when applicable, see the guide for more details.</p>\r\n<p>When the included R3load cannot be used, an update of R3load is in general recommended by SAP.&#160;It is&#160;absolutely required for the following cases:</p>\r\n<ul>\r\n<li>Using SAP NetWeaver 7.40 SPS 05 - SPS 07) or R3load 7.42 (SAP NetWeaver 7.40 SPS 08-SPS 09) in a heterogeneous system copy with Unicode conversion from IBM DB2. See also <strong>SAP Note&#160;</strong><a target=\"_blank\" href=\"/notes/2095316\">2095316</a>.</li>\r\n<li>Using SAP Kernel 7.21 or higher copying towards SAP HANA. See also <strong>SAP Note</strong> <a target=\"_blank\" href=\"/notes/1802965\">1802965</a></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>When this particular required R3load version is not available as part of the SAP NetWeaver Kernel archive, you can download it separately from SAP Service Marketplace and use the downloaded archive. For more information about how to use a specific R3load archive for a system copy procedure, see&#160;<strong>SAP Note</strong>&#160;<a target=\"_blank\" href=\"https://websmp130.sap-ag.de/sap/support/notes/2040866\">2040866</a>.</p>\r\n<p>&#160;</p>\r\n<p>------------------------&lt; D025095, 04/SEP/14 &gt;-------------------------</p>\r\n\r\n<p><strong>Requesting an installation with media of old Support Releases</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>SAP recommends that you use the latest Support Release of every product version. You can only request support for enabling a solution with the required lower Support Package level if you require for your new installation a lower Support Package level that corresponds to the Support Package level of existing SAP systems in your system landscape, and you cannot run a SAP system copy.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ol>\r\n<li>\r\n<p>Ask product support to create an incident on component XX-SER-SWFL-SHIP to enable you to use media with older installation content.</p>\r\n</li>\r\n<li>Then put the incident on component BC-INS-RMP to get support information on how to use the latest version of Software Provisioning Manager with the media of former Support Releases.</li>\r\n<li>SWPM media <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter/search/51054279\">51054279</a> containing also latest archives and older flavors that ran out of support.</li>\r\n</ol>\r\n<p>------------------------&lt; ********, 04/SEP/14&gt;-------------------------</p>\r\n<p><strong>IBM i: Java Installation - use JDBC driver JTOpen version 8.6 and higher and skip versions 8.3&#160;till 8.5</strong></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;[Update 30/Oct/15: driver versions 8.5 and&#160;8.6&#160;added][Update 18/Feb/15: driver version 8.4 added]&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You want to install an SAP Java System on IBM DB2 for i or an&#160;installation in progress fails with the following error:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;DB Error during import of J2EE_CONFIGENTRY</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Database access error: [SQL0423] Locator *N not valid. </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">OpenSQLExceptionCategories: [] (SQLState: 0F001, ErrorCode: -423)&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>An SAP Java System installation is not possible when using the current version for IBM Toolbox for Java (JTOpen) JDBC driver versions 8.3 and 8.4. Version 8.5 works fine for the installation but it can cause a different issue later when the SAP system is in use already. For more information, see&#160; <strong>SAP Note <a target=\"_blank\" href=\"/notes/654800\">654800</a>.</strong></p>\r\n<p>To find out the version of your JDBC driver, see <strong>SAP Note <a target=\"_blank\" href=\"/notes/1232613\">1232613</a></strong>.</p>\r\n<p>The current versions for IBM Toolbox for Java (JTOpen) version 8.3&#160;till&#160;version 8.5 need to be avoided.&#160;Instead use&#160;version 8.2 or version 8.6 and higher. If the installation has already been started, replace the JDBC driver (jt400.jar) found in /sapmnt/&lt;SID&gt;/jdbc/tbx and continue the installation.</p>\r\n<p>&#160;</p>\r\n<p><strong>++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p><strong>3&#160;&#160;Running the Software Provisioning Manager (SWPM)</strong></p>\r\n<p><strong>++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p><strong>-----------------------&lt;D035492 22/Nov/2023&gt;-------------------------</strong></p>\r\n<p><strong>IBM&#160;i&#160;7.3 only with PTF SI83511 installed:</strong></p>\r\n<p><strong><strong>Database error \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQL0443 (SQLSTATE 42501) - Routine Error on QDBSSUDF2: NOT AUTHORIZED TO USE QSYS/DSPPTF</span>\" occurs</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>While&#160;the SWPM is building up the SAP database library, the SQL error SQL0443 may occur.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>To&#160;solve<strong> database errors \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQL0443 (SQLSTATE 42501) - Routine Error on QDBSSUDF2: NOT AUTHORIZED TO USE QSYS/DSPPTF</span>\"</strong>&#160;when activating CDS views, grant *USE authority on DSPPTF to SAP user profiles,&#160;e.g.&#160;like&#160;this:</p>\r\n<p>&#160;&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"> &#160;&#160;&#160;GRTOBJAUT OBJ(QSYS/DSPPTF) OBJTYPE(*CMD) USER(R3GROUP) AUT(*USE)&#65279;</span></p>\r\n<p>For more information, see the SAP Note&#160;<a target=\"_blank\" href=\"/notes/3392566\">3392566</a>&#160;- IBM&#160;i: Not authorized to command DSPPTF.</p>\r\n<p><strong>------------------------&lt;I323815, 17/MAY/22&gt;-------------------------</strong></p>\r\n<p><strong>System provisioning of NetWeaver Java 7.50 SP0, S/4HANA 1709 Java stack and S/4HANA 1809 Java stack: SWPM fails with&#160;<em>java.lang.SecurityException</em>&#160;exception</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>During system provisioning scenarios of NetWeaver Java 7.50 SP0, S/4HANA 1709 Java stack and S/4HANA 1809 Java stack, SWPM fails with the following exception:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;[EXCEPTION]&#65279;</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;Caused by: com.sap.security.core.server.secstorefs.NoEncryptionException: Encryption or decryption is not possible because the full version of the SAP Java Crypto Toolkit was not found (iaik_jce.jar is required, iaik_jce_export.jar is not sufficient) or the JCE Jurisdiction Policy Files don't allow the use of the \"PbeWithSHAAnd3_KeyTripleDES_CBC\" algorithm.&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/3260641\">3260641</a>.</p>\r\n<p>------------------------&lt;C5012251, 17/MAY/22&gt;-------------------------</p>\r\n<p><strong>DB6: Step InstallDB2Software fails because db2prereqcheck returns DBT3507E due to missing mksh or psmisc packages.</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>If you are using Db2 11.5 FP7 or higher, you might encounter the following error when SWPM calls db2setup. This error can be found in the sapinst_dev.log or the db2setup.log:</p>\r\n<p>Summary of prerequisites that are not met on the current system:&#160;<br /> <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">DBT3507E &#160;The db2prereqcheck utility failed to find the following package or file: \"mksh\".&#160;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"> &#160; Aborting the current installation ...</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"> &#160; Run installation with the option \"-f sysreq\" parameter to force the installation.&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>If you are not installing IBM Tivoli System Automation for Multiplatforms for High Availability, then you can use the following workaround:</p>\r\n<p>In order to pass \"-f sysreq\" to the db2setup command in SWPM, you must stop SWPM, temporarily set the environment variable SAPINST_DB2SETUP_OPTIONS to &#8220;-f sysreq&#8221; for the user that calls the SWPM, and then restart the SWPM. Passing it as a parameter when calling sapinst will not work.</p>\r\n<p>------------------------&lt;********, 20/JUL/21&gt;-------------------------</p>\r\n<p>updated: 18/OCT/21</p>\r\n<p><strong>IBM i:&#160;<strong>The program sapinst (the SWPM) breaks directly after the self-extract</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The program sapinst (the SWPM) breaks directly after the self-extract in the initial starting phase. You can see the following lines on the (green) screen:</p>\r\n<p><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">[==============================] / extracting&#8230;&#160;&#160; done!</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ERROR: Setting native locale failed.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Choose different Locale Environment variables LC_ALL/LC_CTYPE&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>The program sapinst relies on the PASE locales en_US, which currently are not installed on your IBM i system. These US English locales are shipped <strong>only</strong>&#160;by the IBM i language package with the feature code <strong>2924</strong> (English). You must have installed English (2924) as a primary or secondary language on IBM i to be able to run the sapinst. How to install English as a secondary language is described in the installation guide for SAP on IBM i, in the section <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Installing English as a Secondary Language</span>. After English is available you can start the sapinst again. This time the software provisioning manager will find the PASE locales en_US and the error from above will not occur anymore. The installation will continue.</p>\r\n<p>-------------------------------------&lt;D025095, SEP 17, 2020&gt;---------------------------------------------------------------------</p>\r\n<p><strong>PAS installation of an SAP Java system based on SAP NetWeaver 7.5 fails due to SAPJVM Patch Level</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>PAS installation of an SAP Java system based on SAP NetWeaver 7.5 fails with an error like the following (see also SAP Note&#160;<a target=\"_blank\" href=\"/notes/2951691\">2951691</a>):</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;An error occurred while processing option &lt;...&gt; Primary Application Server Instance(Last error reported by the step: Execution of Deploy Controller Runner tool '/tmp/sapinst_instdir/&lt;...&gt;/JAVA/APP1/sapjvm/sapjvm_8/bin/java -classpath /usr/sap/install/&lt;...&gt;/JAVA/APP1/install/lib:/usr/sap/QD1/SYS/global/security/lib/tools/iaik_jce.jar:/usr/sap/&lt;SAPSID&gt;/SYS/global/security/lib/tools/iaik_jsse.jar:/usr/sap/&lt;SAPSID&gt;/SYS/global/security/lib/tools/iaik_smime.jar:/usr/sap/&lt;SAPSID&gt;/SYS/global/security/lib/tools/iaik_ssl.jar:/usr/sap/&lt;SAPSID&gt;/SYS/global/security/lib/tools/w3c_http.jar:/usr/sap/install/&lt;...&gt;/JAVA/APP1/install/lib/tc&#126;je&#126;cl_deploy.jar:/tmp/sapinst_instdir/&lt;...&gt;/INSTALL/DISTRIBUTED/JAVA/APP1/j2eeclient/sap.com&#126;tc&#126;exception&#126;impl.jar:/tmp/sapinst_instdir/&lt;...&gt;/INSTALL/DISTRIBUTED/JAVA/APP1/j2eeclient/sap.com&#126;tc&#126;je&#126;clientlib&#126;impl.jar:/tmp/sapinst_instdir/&lt;...&gt;/JAVA/APP1/j2eeclient/sap.com&#126;tc&#126;logging&#126;java&#126;impl.jar -Xmx256m -Ddc.api.verbose=false -d64 -d64 com.sap.engine.services.dc.api.cmd.Runner deploy -l /tmp/sapinst_instdir/&lt;...&gt;/JAVA/APP1/deploy.lst -e stop -u lower -w safety --lcm bulk -t 14400000 --host fs4433 --port 50104' aborts with return code 1. Error message is 'undefined. SOLUTION: Check '/usr/sap/install/&lt;...&gt;/INSTALL/DISTRIBUTED/JAVA/APP1/log/dc_log/deploy_api.0.log' and 'deploycontroller.log' for more information).&#65279;</span></p>\r\n<p>In the deploycontroller.log, you see an error like the following:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;[ Error ] Could not establish connection to AS Java on [fs4433:50104]. No alive connection. Check state of the server. Check if the host and port specified are correct and AS Java is up and running.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">[ Error ] +++ No response was received from server +++. Deploying offline for [14,401] seconds; timeout elapsed [1] seconds ago</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">[ Info ] Destroy command:[deploy]</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Exception in thread \"main\" java.lang.RuntimeException: com.sap.engine.services.dc.api.deploy.EngineTimeoutException: [ERROR CODE DPL.DCAPI.1041] Deploy API could not retrieve the Result after 14400000 ms.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">[...]&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ol>\r\n<li>Stop the SWPM.</li>\r\n<li>Make sure that the SAPJVM archive contained in the installation media you use has a patch level 63 or lower.</li>\r\n<li>Proceed as described in section <strong>Planning and&#160;Preparation -&gt;&#160;Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure that the SAPJVM 8.1 archive you use has a patch level of 63 or lower&#160;</strong>in this SAP Note.</li>\r\n<li>Restart the installation.</li>\r\n<li>After the installation has completed, make sure that you include the latest patch of the J2EE ENGINE SERVERCORE package and all dependent packages in your upgrade. For more information, see&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2951691\">2951691</a>. Then apply the latest Kernel and remaining Support Packages.</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">See also the following items within this SAP Note:</span></p>\r\n<p><strong>Planning and Preparation -&gt; <strong>&#160;&#160;</strong>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure that the SAPJVM 8.1 archive you use has a patch level of 63 or lower</strong></p>\r\n<p><strong><strong><strong><strong>Follow - Up Activities -&gt;&#160;<strong>&#160;</strong></strong>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure you apply the latest Kernel and AS Java Patches</strong></strong></strong></p>\r\n<p>------------------------&lt; D067737, 04/SEP/20&gt;-------------------------</p>\r\n<p><strong>MSS: Connection problems while running option \"Configure additional AlwaysOn Node\"</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<ol>\r\n<li>While running option&#160;\"Configure additional AlwaysOn Node\" you receive the following error message:<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em><br />&#65279;</em></span>and/or&#160;<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Creation of a new database is not allowed. DIAGNOSIS: You selected a new database, but for this type of installation an existing database is required. SOLUTION: Contact SAP support.&#65279;</span></li>\r\n<li>After the error message has been displayed, SQL Server is restarted by SWPM.</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ol>\r\n<li>Make sure that the current node (where you run the SWPM option) is still the primary node. If it is not, you need to perform a manual failover.</li>\r\n<li>After that you can continue/retry the SWPM execution.</li>\r\n</ol>\r\n<p>------------------------&lt; D070048, 28/AUG/20&gt;-------------------------</p>\r\n<p><strong>HANA: ABAP instance on</strong></p>\r\n<p><strong>&#160;Windows fails to start due to \"Failed locking the secure store.\" error using the latest HANA client version 2.4 or 2.5</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>During SWPM step \"start\" or during the SAP System startup on Microsoft Windows, the following error is observed in any of the dev_w(x) log files:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#65279;</em>C &#160;SQLCODE &#160; &#160;: -10757</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">C &#160;SQLERRTEXT : Secure store error: d:\\701\\w\\eq0qbiy32v\\src\\interfaces\\securestore\\impl\\securestore.cpp:473 - (91014\\</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">C &#160; &#160; &#160; &#160; &#160; &#160; &#160; )&#160;<strong>Failed locking the secure store</strong>. Failure reason: $reason$</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">B &#160;***LOG BY2=&gt;&#160;<strong>sql error -10757 performing CON</strong>&#160;[dbsh &#160; &#160; &#160; &#160; 1283]&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>At the moment, only the following workaround is available:</p>\r\n<p style=\"padding-left: 30px;\">delete lock file located at: C:\\ProgramData\\.hdb\\&lt;host&gt;\\&lt;WindowsUser-SID&gt;\\SSFS_HDB.LOC</p>\r\n<p style=\"padding-left: 30px;\">Where WindowsUser-SID is found using command: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">wmic useraccount where name='SAPService&lt;SID&gt;' get sid&#65279;</span></p>\r\n<p>When available, a permanent solution will be documented in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2937532\">2937532</a>.</p>\r\n<p>------------------------&lt; D070048, 14/JUL/20&gt;-------------------------</p>\r\n<p><strong><strong><strong>java.lang.UnsupportedClassVersionError p</strong>roblems during installation or after manually updating an SAP System with HANA Client 2.5</strong><br /></strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>During step importJavaDump or during the SAP System startup, the following error is observed:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;java.lang.UnsupportedClassVersionError: com/sap/db/jdbc/Driver has been compiled by a more recent version of the Java Runtime (class file version 52.0), this version of the Java Runtime only recognizes class file versions up to 50.0&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Java 6 is no longer supported in SAP HANA client 2.5 or newer version. For more information and solution, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2947762\" title=\"2947762  - SWPM: Support for SAP HANA Client version 2.5 and newer versions\">2947762</a>.</p>\r\n<p>------------------------&lt;********, 25/JUN/20&gt;-------------------------</p>\r\n<p><strong>IBM i: The ASCS instance split may break for SAP systems based on NW 7.XX</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The SWPM option \"Split Off ASCS Instance from Existing Primary Application Server Instance\" may fail in the two following steps.</p>\r\n<p>1. step <strong><em>createInstanceDirectories</em></strong>&#160;may break with following message:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;An error occurred while processing option Generic Options &gt; IBM Db2 for i &gt; Split Off ASCS Instance from Existing Primary Application Server Instance(Last error reported by the step: System call failed. DETAILS: Error 0 (0x00000000) (Error 0) in execution of system call 'getcwd' with parameter (), line (527) in file (/bas/749_COR/bc_749_COR/src/ins/SAPINST/impl/src/syslib/process/syuxccuren.cpp)&#65279;</span></p>\r\n<p>2. step <strong><em>startInstance</em></strong>&#160;may break with following message:</p>\r\n<p>An error occurred while processing option <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Generic Options &gt; IBM Db2 for i &gt; Split Off ASCS Instance from Existing Primary Application Server Instance(Last error reported by the step: Instance &lt;SAPSID&gt;/ASCSnn [STOP] did not start after 5:00 minutes. Giving up).&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>1. The step <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">createInstanceDirectories </span>breaks when the user profile &lt;SAPSID&gt;ADM is missing the supplemental group <em>sapinst</em>. To add the missing group <em>sapinst</em>, see <strong><a target=\"_blank\" href=\"/notes/1634671\">SAP Note 1634671</a></strong> -&#160;IBM i SAPinst: Can't create user profile handle for user</p>\r\n<p>2. The step <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">startInstance breaks</span> when the SAP system is missing the global security directory: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">/usr/sap/&lt;SAPSID&gt;/SYS/global/security</span>. To create the global security directory, see <strong><a target=\"_blank\" href=\"/notes/1639578\">SAP Note 1639578</a></strong> -&#160;SSFS as password store for primary database connect</p>\r\n<p>After you have used the SAP Note to solve the issue, you can continue the SWPM to finish the ASCS instance split.</p>\r\n<p>------------------------&lt;I323815, 17/JUN/20&gt;-------------------------</p>\r\n<p><strong>Workaround for \"GetSAPReleaseFromDB\" error:&#160;</strong><strong>Moving a primary application server instance to a different host after DMO procedure&#160;</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>While selecting &#8220;Custom&#8221; mode, SWPM prompts you to choose the &#8220;Secure Storage for Database Connection&#8221;.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>If \"ABAP SSFS\" has been configured, you can select it and proceed with the execution. Otherwise, you can leave &#8220;HANA Secure User Store (hdbuserstore)&#8221; to prevent some connectivity issues.</p>\r\n<p>For more information, see:</p>\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/2154997\">2154997</a> - Migration of hdbuserstore entries to ABAP SSFS</p>\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/2250144\">2250144</a> - FAQ: SAP HANA Secure User Store</p>\r\n<p>------------------------&lt;********, 28/MAY/20&gt;-------------------------</p>\r\n<p><strong>IBM i: Old SAPJVM versions crashing on IBM POWER 9 hardware</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>An SWPM operation is interrupted early at the first Java usage when using an old SAPJVM version on IBM POWER 9 hardware, for example SAPJVM 5 Patch 100. For example, this can happen in the dialog step &#8220;IBM Db2 for I Toolbox JDBC Driver&#8221;. The error message looks like this:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;ERROR 2020-01-22 10:00:00.000 (sapiusr/sapinst) id=modlib.jslib.childAppStatusError errno=MUT-03010<br /></span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Execution of the command \"/tmp/sapinst_instdir/NW711/DB4/COPY/SYSTEM/STD/AS/sapjvm/sapjvm_5/bin/java -classpath /tmp/SAP_JDBC_driver/jt400.jar -showversion -Xmx256m utilities.AboutToolbox\" finished with status TST_ERROR.&#65279;</span></p>\r\n<p>This error however could happen in different SWPM dialogs or steps depending on when the first usage of the SAPJVM occurs. The root cause is a crash in the JVM executable used for performing the actions described above. When executing java independently, the following crash occurs:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;$ ./java -version</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># A fatal error has been detected by the SAP Java Virtual Machine:</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#&#160; SIGILL (0x4) at pc=0x0700000010000298, pid=8560995, tid=258</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># address of faulting instruction: 0x0700000010000298</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># JRE version:&#160; (5.0_81-b10) (build )</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Java VM: SAP Java Server VM (5.1.100 25.31-b07 Feb 12 2015 02:08:19 - 51_REL - optU - aix ppc64 - 6 - bas2:233615 (mixed mode) compressed oops)</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Problematic frame:</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># v&#160; &#126;BufferBlob::config_dscr (sp=0x0000000180239880) (pc=0x0700000010000298)</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Will write core dump when quitting, default location: /usr/sap/sapinst/VB/100/sapjvm_5/bin/core or core.8560995</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># An error report file with more information is saved as:</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># /usr/sap/sapinst/VB/100/sapjvm_5/bin/hs_err_pid8560995.log</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># If you would like to submit a bug report, please visit:</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#&#160;&#160; <a target=\"_blank\" href=\"https://support.sap.com/incident\">https://support.sap.com/incident</a></span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Please save the hs_err file(s) and the written core dump for further error analysis</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># See SAP note 1500116 for further information on collecting core dumps after SAPJVM crashes</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">IOT/Abort trap (core dumped)&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Please use the latest available SAPJVM archive for IBM i. For example, for SAPJVM5 it can be downloaded using the following link:</p>\r\n<p><a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter/search/\">https://launchpad.support.sap.com/#/softwarecenter/search/</a></p>\r\n<p>To find the latest SAPJVM5 for IBM i, enter &#8220;SAP JVM 5.1&#8221; in the download search box. Then select SAP JVM 5.1 in &#8220;Choose the Next Category (1)&#8221; and select OS/400 in the filter combo box. The newest downloads are displayed at the very top of the list.</p>\r\n<p>-------------------------------------&lt;I323815, MAY 05, 2020&gt;---------------------------------------------------------------------</p>\r\n<p><strong>parameter &#8220;autologout = 0&#8221; has been removed from &lt;sapsid&gt;adm C-shell scripts</strong></p>\r\n<p>The defined parameter &#8220;autologout = 0&#8221; has been removed from &lt;sapsid&gt;adm C-shell scripts: .sapenv.csh and .sapenv_&lt;host&gt;.csh.</p>\r\n<p>------------------------&lt;********, 09/MAR/20&gt;-------------------------</p>\r\n<p><strong>IBM i: System Rename does not support independent ASP</strong></p>\r\n<p>Symptom:</p>\r\n<p>Renaming an SAP system installed on an independent ASP (system files and/or database and journal receiver libraries) is not supported up to SWPM 1.0 SP28, as documented in the SAP System Rename guide.</p>\r\n<p>Solution:</p>\r\n<ul>\r\n<li>Starting with SWPM 1.0 SP29 renaming a system installed on an independent ASP will be possible identically to renaming a system installed in SYSBASE.</li>\r\n<li>When using SWPM 1.0 SP28 or lower, it is necessary to an SAP System Copy with a new &lt;Target_SAPSID&gt;. The process is described in the SAP System Copy guide.</li>\r\n</ul>\r\n<p>------------------------&lt; I343882, 13/DEC/19&gt;-------------------------</p>\r\n<p><strong>SL Common GUI hangs when using Chrome</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You are running Software Provisioning Manager (SWPM) using Chrome als your web browser. Suddenly the SL Common GUI hangs, refreshing Chrome does not help.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>SAP is working upon a solution for this issue. As a workaround, proceed as follows:</p>\r\n<ol>\r\n<li>start a browser session with another supported web browser, such as Firefox or Internet Explorer 11</li>\r\n<li>Copy the URL from the Chrome address bar to the address bar of the new browser session.</li>\r\n<li>Continue running SWPM in the new browser session.&#160;</li>\r\n<li>Close the Chrome browser session.</li>\r\n</ol>\r\n<p>------------------------&lt; ********, 22/NOV/19&gt;-------------------------<br />[ extended by ********, 15/Jan/2020 ]</p>\r\n<p><strong>IBM i: SWPM crashes with SIGSEGV due to high memory consumption</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptoms:</span></p>\r\n<p>- SWPM process crashes with SIGSEGV signal.<br />- The SWPM browser window stays unchanged indefinitely.<br />- Log files (for example sapinst_dev.log or sapinst.log) end abruptly without any recognizable error message.<br />- A core dump file with the name \"core\" and a large size (approximately 10 GB) appears in the SWPM installation directory.<br />- A log file named \"hs_err_pid&lt;number&gt;.log\" appears in the SWPM installation directory. The log file starts with the following information:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;#</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># A fatal error has been detected by the SAP Java Virtual Machine:</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#&#160; SIGSEGV (0xb) at pc=&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;, pid=&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;, tid=&#8230;..</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># accessing faulting address: 0x0000000000000000</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># JRE version:&#160; (8.0.221) (build )</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Java VM: SAP Java Server VM (8.1.059 10.0.2+000, &lt;date and time&gt; - 81_REL - optU - aix ppc64 - 6 - bas2:316262 (mixed mode), tiered, compressed oops, parallel gc, aix-ppc64)</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Problematic frame:</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># C&#160; 0x&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;. (sp=0x&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;..,pc=0x&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;&#8230;.)</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Core dump will be written. Default location: /tmp/sapinst_instdir/&lt;subdirectory depending on product&gt;/core or core.&lt;pid&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># If you would like to submit a bug report, please visit:</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#&#160;&#160; https://support.sap.com/incident</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># Please save the hs_err file(s) and the written core dump for further error analysis</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># See SAP note 1500116 for further information on collecting core dumps after SAPJVM crashes</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># The crash happened outside the Java Virtual Machine in native code.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"># See problematic frame for where to report the bug.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">#&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Known causes:</span></p>\r\n<p>- The error is known to occur when an All in One Java installation (1) is interrupted and the Java system is forced to extract the files, then the installation is continued. In this case SWPM tries to update the ownership and permission information of files belonging to the system. Due to the large number of files SWPM's memory consumption rises to the point where the process crashes.<br />The last step executed by SWPM is \"setPermissions\" &#8211; this can be confirmed by finding the last occurrence of \"Execute step\" in sapinst_dev.log:</p>\r\n<p style=\"padding-left: 30px;\"><em>$ grep \"Execute step\" sapinst_dev.log | tail -n 1</em><br /><em>Execute <strong>step setPermissions</strong> of component |NW_Java_OneHost|ind|ind|ind|ind|0|0|NW_Onehost_System|ind|ind|ind|ind|onehost|0|NW_Unpack2|ind|ind|ind|ind|unpack|0</em></p>\r\n<p>- The error is also known to occur when a homogeneous ABAP SAP System Copy installation (2) is interrupted while restoring a very large database library. Due to the very large database library SWPM's memory consumption rises to the point where the process crashes.<br />The last step executed by SWPM is \"loadDatabaseCopy\" &#8211; this can be confirmed by finding the last occurrence of \"Execute step\" in sapinst_dev.log:</p>\r\n<p style=\"padding-left: 30px;\"><em>$ grep \"loadDatabaseCopy\" sapinst_dev.log | tail -n 1</em><br /><em>Execute <strong>step loadDatabaseCopy</strong> of component |NW_ABAP_OneHost|ind|ind|ind|ind|0|0|NW_Onehost_System|ind|ind|ind|ind|onehost|0|NW_CreateDBandLoad|ind|ind|ind|ind|createdbandload|0|NW_LoadSAVLIB_ABAP_DB4|ind|ind|ind|ind|loadsavlib_ABAP_db4|0|NW_LoadSAVLIB_DB4|ind|ind|ind|ind|loadsavlib_db4|0</em></p>\r\n<p><span style=\"text-decoration-line: underline;\">Solutions:</span></p>\r\n<p><strong>Solution (1)</strong>: When the error occurs in Step <em>setPermissions</em>:<br />- The file ownership and permissions need to be set manually in the file system. This can be done by executing the following commands in QP2TERM (replace &lt;SAPSID&gt; with the actual SAPSID of your system):</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;chown -R -H &lt;SAPSID&gt;ADM:R3GROUP /usr/sap/&lt;SAPSID&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">chmod -R -H 755 /usr/sap/&lt;SAPSID&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">chmod -R -H 755 /sapmnt/&lt;SAPSID&gt;/exe</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ls -lad /sapmnt/&lt;SAPSID&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">If /sapmnt/&lt;SAPSID&gt; is not a symbolic link, execute also:</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">chown -R -H &lt;SAPSID&gt;ADM:R3GROUP /sapmnt/&lt;SAPSID&gt;&#65279;</span></p>\r\n<p>- After this the installation can continue by manually setting the setPermissions step result to \"OK\" in keydb.xml.<br />Find the following text:</p>\r\n<p style=\"padding-left: 30px;\">&#65279;&#65279;&lt;![&#65279;CDATA[|NW_Java_OneHost|ind|ind|ind|ind|0|0|NW_Onehost_System|ind|ind|ind|ind|onehost|0|NW_Unpack2|ind|ind|ind|ind|unpack|0|setPermissions]]&gt;&#65279;&#65279;</p>\r\n<p>Then in the XML element below the finding replace \"[ERROR]\" with \"[OK]\":</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;&#160;&#160;&#160; &lt;fld name=\"STATUS\"&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;properties&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;property name =\"USED_HOSTNAME\" value =\"&#8230;\" /&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/properties&gt;</span><br />&#65279;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;strval&gt;&lt;![CDATA[ERROR]]&gt;&#65279;<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/strval&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160; &lt;/fld&gt;&#65279;</span></p>\r\n<p>Should be replaced with:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#65279;</em>&#160; &#160; &lt;fld name=\"STATUS\"&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;properties&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;property name =\"USED_HOSTNAME\" value =\"&#8230;\" /&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/properties&gt;</span><br />&#65279;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;strval&gt;&lt;![CDATA[OK]]&gt;&#65279;<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/strval&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160; &lt;/fld&gt;&#65279;</span></p>\r\n<p>- Restart SWPM and continue the installation process (make sure to select \"Continue previous run\" and not start the process from the beginning).</p>\r\n<p><strong>Solution (2)</strong>: When the error occurs in Step <em>loadDatabaseCopy</em>:<br />- The RSTLIB command in the step loadDatabaseCopy most probably did not finish successfully, and the SAP database library is not consistent. For that the SAP database library content must be deleted with CLRLIB:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;CLRLIB R3&lt;SAPSID&gt;DATA&#65279;</span></p>\r\n<p>- Fill the content of the SAP database library again using a manual RSTLIB command. Take the RSTLIB command from the end of the log file sapinst_dev.log in the step loadDatabaseCopy and call using the SAP installation user. The RSTLIB command will look like this:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;RSTLIB SAVLIB(R3&lt;SAPSID&gt;DATA) DEV(*SAVF) SAVF(&lt;savflib&gt;/R3&lt;SAPSID&gt;DATA) RSTLIB(R3&lt;SAPSID&gt;DATA) OPTION(*NEW) MBROPT(*ALL) ALWOBJDIF(*ALL) OMITOBJ((R3&lt;SAPSID&gt;DATA/QDFTJRN)) OUTPUT(*PRINT) RSTASPDEV(*SAVASPDEV) RSTASP(1)</span>&#160;<em> &#160; &#160;</em></p>\r\n<p>- After this the installation can continue by manually setting the loadDatabaseCopy step result to \"OK\" in keydb.xml.<br />Find the following text:</p>\r\n<p style=\"padding-left: 30px;\"><em>&#65279;&#65279;</em>&lt;![CDATA[|NW_ABAP_OneHost|ind|ind|ind|ind|0|0|NW_Onehost_System|ind|ind|ind|ind|onehost|0|NW_CreateDBandLoad|ind|ind|ind|ind|createdbandload|0|NW_LoadSAVLIB_ABAP_DB4|ind|ind|ind|ind|loadsavlib_ABAP_db4|0|NW_LoadSAVLIB_DB4|ind|ind|ind|ind|loadsavlib_db4|0|loadDatabaseCopy]]&gt;<em>&#65279;&#65279;</em></p>\r\n<p>Then in the XML element below the finding replace \"[ERROR]\" with \"[OK]\":</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#65279;</em>&#160;&#160;&#160; &lt;fld name=\"STATUS\"&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;properties&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;property name =\"USED_HOSTNAME\" value =\"&#8230;\" /&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/properties&gt;</span><br />&#65279;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;strval&gt;&lt;![CDATA[ERROR]]&gt;&#65279;<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/strval&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160; &lt;/fld&gt;&#65279;</span></p>\r\n<p>Should be replaced with:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;&#160; &#160; &lt;fld name=\"STATUS\"&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;properties&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;property name =\"USED_HOSTNAME\" value =\"&#8230;\" /&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/properties&gt;</span><br />&#65279;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;strval&gt;&lt;![CDATA[OK]]&gt;&#65279;<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;/strval&gt;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160; &lt;/fld&gt;&#65279;</span></p>\r\n<p>- Restart SWPM and continue the installation process (make sure to select \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Continue previous run</span>\" and not to start the process from the beginning).</p>\r\n<p>------------------------&lt; ********, 09/SEP/18 &gt;-------------------------</p>\r\n<p><strong>NW&#160;7.4 based SAP systems and higher only</strong></p>\r\n<p><strong>IBM i: Installation breaks with the error message \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Command FIXSAPOWN SID(*NONE) LIB('SAP&lt;SID&gt;LOAD') failed.</span>\"</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p dir=\"ltr\">When running the software provisioning manager following error message occurs: <em>Command FIXSAPOWN SID(*NONE) LIB('SAP&lt;SID&gt;LOAD') failed.</em></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Diagnosis</span>:</span></p>\r\n<p>The command FIXSAPOWN breaks because of the following root cause: \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Object R3GROUP in library *LIBL not found</span>\"<br />Initially on an <em>empty</em> IBM i host there is no user profile R3GROUP. This user profile is created by the first SAP installation option run.<br />Unfortunately, the library SAP&lt;SID&gt;LOAD is created before R3GROUP is created. When the owner of library SAP&lt;SID&gt;LOAD is switched to&#160;R3GROUP, this user profile cannot be found.<br />The command FIXSAPOWN is called too early. The command has to be called later after the needed user profile is already existing.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>As a workaround you have to create the user profile R3GROUP manually. The command CRTSAPUSR can create this user profile.<br />Call the following command: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><strong>&#65279;SAP&lt;SID&gt;IND/CRTSAPUSR USER(*GROUP) GID(&lt;Number&gt;)&#65279;</strong></span><br />Afterwards, you have to retry the current installation step. This time FIXSAPOWN will work.</p>\r\n<p><strong>Note: </strong>Make sure that the Group ID (GID) of the user profile R3GROUP is the same for all involved IBM i server. The GID is a number of the interval [1-4294967294].<br />If you do not set the GID, it will be done automatically. But the number automatically generated by the OS may not meet your requirements.</p>\r\n<p>When a fix is available for the SWPM the version will be marked here.</p>\r\n<p>------------------------&lt; ********, 25/JUL/18 &gt;-------------------------</p>\r\n<p><strong>IBM i 7.3 and higher only</strong></p>\r\n<p><strong>IBM i: An installation option&#160;breaks in the step createService with \"Could not start instance service of ...\"</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p dir=\"ltr\">An installation option breaks in the step createService.<br />The error message is similar to:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Could not start instance service of instance &lt;SID&gt;/&lt;Instance&gt;&#65279;</span></p>\r\n<p dir=\"ltr\">In the sapinst_dev.log you can find the following entry:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;Execution of the command \"/usr/sap/&lt;SID&gt;/&lt;Instance&gt;/exe/sapcontrol -prot NI_HTTP -nr &lt;Instance_Number&gt; -function StartService &lt;SID&gt;\" finished with return code 1. Output:</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">StartService</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">FAIL: Service start failed.&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Diagnosis</span>:</span></p>\r\n<p>The latest PTFs for IBM i 7.3 have a direct impact on the kernel tools for SAP on IBM i. Some commands cannot run in a multithreaded mode anymore. These commands have to run in single-threaded mode now.</p>\r\n<p>For this you must use the latest fixed kernel versions when running an installation option on IBM i. You find the minimum patch level number of the different kernel versions in the solution section below.<br />If you use already an older kernel, you&#160;can work around the issue in the step createService by performing some manual steps listed below in the&#160;workaround section.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>To prevent the issue described above you have to use the latest kernel version from the beginning when the software provisioning manager askes for SAPEXE.SAR and SAPEXEDB.SAR:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>kernel version</td>\r\n<td>min. patch level</td>\r\n</tr>\r\n<tr>\r\n<td>7.21</td>\r\n<td>1,000</td>\r\n</tr>\r\n<tr>\r\n<td>7.22</td>\r\n<td>600</td>\r\n</tr>\r\n<tr>\r\n<td>7.45</td>\r\n<td>700</td>\r\n</tr>\r\n<tr>\r\n<td>7.49</td>\r\n<td>500</td>\r\n</tr>\r\n<tr>\r\n<td>7.53</td>\r\n<td>200</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Workaround:</span></p>\r\n<p>If you want to keep the kernel you are currently using you must manually work around the issue:</p>\r\n<p>1. Stop the sapinst.<br />2. Login as your installation user in a different green screen session (5250 emulation).<br />3. Set the environment for the current SID using the following command:<br />&#160;&#160; <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CALL SAP&lt;SID&gt;IND/SAPINLPGM</span><br />4. Switch to QP2TERM and start the service manually with the command in the error message:<br />&#160;&#160; <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CALL QP2TERM</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160; /usr/sap/&lt;SID&gt;/&lt;Instance&gt;/exe/sapcontrol -prot NI_HTTP -nr &lt;Instance_Number&gt; -function StartService &lt;SID&gt;</span><br />&#160;&#160; (The sapcontrol command can also be copied directly from the sapinst_dev.log.)</p>\r\n<p>&#160; The console output should be:<br />&#160;&#160; <em>StartService</em>&#160;&#160;<br />&#160;&#160; <em>OK</em>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />5. Create a backup of the file keydb.xml in your current installation directory.<br />6. Edit the file keydb.xml:<br />&#160;&#160; Search for the string CDATA[ERROR] and replace it by CDATA[OK].<br />7. Logout of your current session after you have saved the file keydb.xml.<br />8. Go back to your old green screen session and start your installation option again.<br />9. Continue your current installation.</p>\r\n<p>The current step createService will not be rerun again and the installation option proceeds.<br />Be aware that this workaround has to be done for every instance where the step createService fails.</p>\r\n<p>------------------------&lt; ********, 25/JUN/18 &gt;-------------------------</p>\r\n<p><strong>IBM i: Homogeneous system copy is&#160;broken for large save files</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>A homogeneous system copy is started via the SWPM and the following error message is shown right after entering the save file in the related dialog:</p>\r\n<p>\"<em>Software Provisioning Manager cannot confirm that save file SAVFLIB/SAVFNAME&#160;contains objects saved from expected SAP system library DSTLIBNAME. The check returned library name: ERROR!</em></p>\r\n<p><em>Click <strong>OK</strong> to change input and check again or <strong>Cancel</strong> to skip checking.</em></p>\r\n<p><em><strong>Note:</strong> The command to be used for the restore of the source database relies on the correct calculation of this library name. If this check is ignored, the restore command in the next dialog needs to be modified.</em>\"</p>\r\n<p>The provided save file contains&#160;100,000 records or more, but otherwise completely valid. (The error does not occur if the save file contains less than 100,000 records.) The number of allowed records is related to your current configuration of the printer file QSYS/QPSRODSP.</p>\r\n<p><span style=\"text-decoration: underline;\">Workaround:</span></p>\r\n<p>1. Close SWPM and make sure that the sapinst process does not exist.<br />2. Temporarily change the maximal number of records to *NOMAX (remember to write down the original value to be restored later):<br />&#160;&#160;&#160; <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CHGPRTF FILE(QSYS/QPSRODSP) MAXRCDS(*NOMAX)</span><br />3. Start SWPM again and finish the system copy.<br />4. Restore the original value for the maximal number of records:<br />&#160;&#160;&#160; <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CHGPRTF FILE(QSYS/QPSRODSP) MAXRCDS(&lt;original value&gt;)&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>The&#160;correction&#160;is available with SWPM 1.0 SP23 patch level 5 and later.</p>\r\n<p>------------------------&lt; ********, 29/JAN/18 &gt;-------------------------</p>\r\n<p><strong>IBM i: Installation or import fails due to coinciding table names</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>An installation or import fails when calling R3load and installing SAP NetWeaver with Kernel 720/721/722.</p>\r\n<p>The error messages suggest inconsistencies in the table, primary key or index structure.</p>\r\n<p>&#160;</p>\r\n<p>For example:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;&#160; Table ... in ... (library name) does not have a primary or unique key.</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; Column ... not in table ... in ... (library name).&#65279;</span></p>\r\n<p>The library displayed in the error message is not the R3&lt;SAPSID&gt;DATA library but another</p>\r\n<p>one that comes up before R3&lt;SAPSID&gt;DATA in the library list of the &lt;SAPSID&gt;ADM user,</p>\r\n<p>for example QUSRSYS, and a table in it has identical name to a table in R3&lt;SAPSID&gt;DATA,</p>\r\n<p>for example CNVINFO.</p>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\">Details:</span></p>\r\n<p>R3load generates certain SQL queries using table names without the schema name. Under certain conditions</p>\r\n<p>unrelated tables in other libraries higher on the library list can override SAP tables with identical names.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution 1:</span></p>\r\n<p>1. Identify which is the conflicting non-SAP table. For example, in the following error message:</p>\r\n<p>&#160;&#160; <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQL0205 - Column SRTF2 not in table CNVINFO in QUSRSYS.&#65279;</span></p>\r\n<p>&#160;&#160;&#160;the table CNVINFO in library QUSRSYS is in conflict with CNVINFO in R3&lt;SAPSID&gt;DATA.</p>\r\n<p>2. Rename the conflicting non-SAP table to a name that does not exist in R3&lt;SAPSID&gt;DATA, for example to CNVINFO2.</p>\r\n<p>3. Continue the current installation or import process. The error should not occur anymore. Now the table will be found in R3&lt;SAPSID&gt;DATA correctly.</p>\r\n<p>4. After finishing successfully, rename the table back to the original name.</p>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\">Solution 2 (alternative to solution 1):</span></p>\r\n<p>Set the following environment variable for the installation user and continue afterwards your current installation or import option:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><strong>&#65279;ADDENVVAR ENVVAR(DBS_DB4_AUTO_DSQL_SCHEMA) VALUE('Y') REPLACE(*YES)&#65279;</strong></span></p>\r\n<p>For more information, see SAP Note 989943.</p>\r\n<p>&#160;</p>\r\n<p>------------------------&lt; ********, 22/JAN/18 &gt;-------------------------</p>\r\n<p><strong>IBM i: Standalone SAP WebDispatcher installation fails with \"TypeError: this.app has no properties\"</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom</span>:</p>\r\n<p>You install a standalone&#160;SAP WebDispatcher on the IBM i platform. The installation fails in the step \"sLoadKernel\" with the following error message (some parts of the text for example like the date, user name or line number may be different):</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;ERROR&#160;&#160;&#160;&#160;&#160; 2018-01-22 13:32:27.615 (sapiusr/sapinst) (startInstallation) [EJSGlobal.cpp:48] id=ejs.scriptMessage errno=FJS-00003 EJS_ErrorReporter</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TypeError: this.app has no properties (in script NW_Webdispatcher|ind|ind|ind|ind, line 9062: ???)&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Diagnosis:</span></p>\r\n<p>The problem is caused by an error in the Software Provisioning Manager.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>The problem will be fixed with the next patch release.</p>\r\n<p>As a workaround extract the file ILE_TOOLS (in capital letters)&#160; from webdisp(...).sar with SAPCAR -xf webdisp(...).sar ILE_TOOLS , then put this file into the SAPinst workdir directory and continue your current installation.</p>\r\n<p>&#160;</p>\r\n<p>----------------------------------&lt;D024110, 21/DEC/17&gt;-------------------------------------</p>\r\n<p><strong>R3load aborts without error message when creating View CMRV_ODERADM_FIN</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>R3load aborts without error message when creating View CMRV_ODERADM_FIN</p>\r\n<p><span style=\"text-decoration: underline;\">Diagnosis: </span></p>\r\n<p>The problem is caused by a segmentation violation in R3load. For unknown reasons it has only been detected so far when creating view CMRV_ODERADM_FIN . However it could hit other long DDL statements&#160; as well.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ol>\r\n<li>As user &lt;sapsid&gt;adm, &#160;change to sapinst_instdir and execute the following command: R3load -merge_only SAPVIEW.TSK <br /><span style=\"text-decoration: underline;\">Example on Windows:</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">cd \"C:\\Program Files\\sapinst_instdir\\SOLMAN72SR1\\SOLMAN72SR1\\DB6\\INSTALL\\STD\\ABAP\"</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">R3load -merge_only SAPVIEW.TSK</span><br /><span style=\"text-decoration: underline;\">Example on UNIX:</span><br /><br /></li>\r\n<li>Change the entry below in SAPVIEW.TSK (from 'err' to 'ign'): <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">V CMRV_ODERADM_FIN C ign&#65279;</span></li>\r\n<li>Repeat the step that had failed in Software Provisioning Manager.</li>\r\n<li>After the installation has finished, go to transaction SE11 or SE14 and activate the view CMRV_ODERADM_FIN.</li>\r\n</ol>\r\n<p>&#160;</p>\r\n<p>------------------------&lt; ********, 01/AUG/17 &gt;-------------------------</p>\r\n<p><strong>IBM i:&#160;</strong><strong>The SWPM breaks while checking the installation master media</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The SWPM breaks while checking the installation media.</p>\r\n<p>A message window of the SWPM&#160;shows&#160;an error message similar to this:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;An error occurred while processing option Dual-Stack Split &gt; IBM DB2 for i &gt; Keep Database &gt; Standard System or Distributed System &gt; Export Primary Application Server of Dual-Stack System( Last error reported by the step: Caught ESAPinstException in module call: The integrity check for [COMMON/INSTALL/UTL/sap.com&#126;sl.ut.infoprovider_lib.jar, COMMON/INSTALL/UTL/sap.com&#126;sl.ut.manager.offline.jar] of Software Provisioning Manager failed. DETAILS: The content has been tampered with and must not be used. SOLUTION: Ensure that you use the latest available version of Software Provisioning Manager downloaded from the SAP Service Marketplace and do not change its content..).The installation on Windows fails in the phase Unpack SAP archives with the error message:&#160;InternalError:&#160;too&#160;much&#160;recursion&#160;(in&#160;script&#160;NW_DI|ind|ind|ind|ind,&#160;line&#160;1171:&#160;???)&#160;&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ul>\r\n<li>Your current installation user does not fit to the requirements of the installation documentation, section \"Preparing an IBM i User Profile\". An internal code page mismatch causes a failure when facing a special characters like \"&#126;\" in a given file name.</li>\r\n<li>Adjust your installation user according to&#160;the documentation section&#160;\"Preparing an IBM i User Profile\".&#160;Afterwards, you can continue your current installation option.</li>\r\n</ul>\r\n<p>------------------------&lt;D025095, 18/JUL/17 &gt;----------------------------</p>\r\n<p><strong>Up-to-date Installation using STACK_CONFIGURATION_FILE does not match options in welcome screen</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You called<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#65279;</em>sapinst SAPINST_STACK_XML=&lt;your stack.xml&gt;&#65279;</span><br />but there are no options in the product.catalog file according to the information on&#160;the UI.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution</span></p>\r\n<ul>\r\n<li>Check that you used the correct version of Software Provisioning Manager, which was generated and downloaded together with the Stack Configuration File.</li>\r\n<li>For example: <em>SAP NetWeaver 7.5</em> options are only in <em>SWPM*.SAR</em>, but NOT offered in <em>70SWPM*.SAR</em>.</li>\r\n<li>For general remarks, see also SAP Note <a target=\"_blank\" href=\"/notes/2277574\">2277574 </a></li>\r\n</ul>\r\n<p>------------------------&lt;I072929, 16/MAY/17 &gt;----------------------------</p>\r\n<p>&#160;</p>\r\n\r\n<p><strong>Windows: Error during uninstall in the context of a Database Migration Option (DMO) procedure</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You are uninstalling an additional application server instance on Windows in the context of Database Migration Option following the documentation \"&#160;Database Migration Option: Target Database &lt;Target_DB&gt;\" at <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance -&gt; Database Migration Option with SUM -&gt; Guides&#160;for DMO.</p>\r\n<p>Due to Windows Error Reporting(WER) settings you&#160; get an \"Application Crash\" popup when&#160;the software provisioning manager&#160;calls R3load in the&#160;\"Define Parameters\"&#160;phase.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Ignore&#160;this error&#160;and continue.</p>\r\n<p>------------------------&lt; ********, 02/MAY/17 &gt;-------------------------</p>\r\n<p><strong>IBM i:&#160;Installation is aborted with&#160;function getpwuid( ) error \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A file or directory in the path name does not exist</span>\"</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The installation is aborted with the following error in the sapinst_dev.log:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;ERROR&#160;&#160;&#160;&#160;&#160; 2017-04-28 11:01:49.603 (sidadm/sidowner) (startInstallation) [/bas/749_REL/bc_749_REL/src/ins/SAPINST/impl/src/syslib/account/syuxcuser.cpp:2363]<br />id=common.functionReturn errno=FSH-00006 CSyUserImpl_getOsInfos(sapiusr, 147)<br />Return value of function getpwuid(147) is A file or directory in the path name does not exist.&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Details:</span></p>\r\n<ul>\r\n<li>The account name reported does exist, but the account executing the program at the time has no permission to execute the query the attributes for the requested account.</li>\r\n<li>This problem was introduced by a recently distributed PTF.<br />The problem is caused by an error in the IBM i operating system.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Workaround:</span></p>\r\n<ul>\r\n<li>Grant temporary read authority to user &lt;SID&gt;ADM with the following command:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;GRTOBJAUT OBJ(&lt;installation_user&gt;) OBJTYPE(*USRPRF) USER(&lt;sid&gt;ADM) AUT(*READ)&#65279;</span></li>\r\n<li>When the installation is done, revoke the granted permission with the following command:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#65279;</em>RVKOBJAUT OBJ(&lt;installation_user&gt;) OBJTYPE(*USRPRF) USER(&lt;sid&gt;ADM)&#65279;</span></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>The problem is fixed by installing the following PTF:</p>\r\n<p style=\"padding-left: 30px;\">For IBM i 7.3 -&#160;MF63565<br />For IBM i 7.2 -&#160;MF63566<br />For IBM i 7.1 -&#160;MF63567</p>\r\n<p>These PTFs can be applied Delayed or Immediate. When the PTF is active the installation process can be continued.</p>\r\n<p>------------------------&lt; D029385, 27/JAN/17&gt;-------------------------</p>\r\n<p><strong>Deviant procedure when using <strong>Oracle 12c</strong></strong></p>\r\n\r\n<p>If your&#160;target system is based on SAP NetWeaver CE 7.2, SAP NetWeaver 7.3 (Java, dual stack)&#160;, or SAP NetWeaver 7.3 EHP1 (Java, dual stack), and is to be installed with Oracle 12c, follow the instructions in SAP Note <a target=\"_blank\" href=\"/notes/2396282\">2396282</a>.</p>\r\n<p>------------------------&lt; ********, 05/JAN/17 &gt;-------------------------</p>\r\n<p><strong>IBM i: Installation is aborted because of a non-existing user account</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The installation is aborted with the following error in the sapinst_dev.log:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><code>&#65279;ERROR&#160;&#160;&#160;&#160;&#160; 2016-10-18 13:40:04.935 (qsecofr/sapinst) [/bas/749_COR/bc_749_COR/src/ins/SAPINST/impl/src/sapinst/CSiStepExecute.cpp:1112] id=controller.stepExecuted errno=FCO-00011<br />The step removeUsers with step key |NW_Java_OneHost|ind|ind|ind|ind|0|0|NW_Remove_Sapinst_Users|ind|ind|ind|ind|sapinstUsers|0|removeUsers was executed with status ERROR ( Last error reported by the step: Account user=\"&lt;some random data here&gt;\" does not exist.).&#65279;</code></span></p>\r\n<p><span style=\"text-decoration: underline;\">Details:</span></p>\r\n<ul>\r\n<li>The account name reported to be non-existing is not valid and contains random characters. The string can be different with each installation attempt.</li>\r\n<li>With each installation attempt the string can be different.</li>\r\n<li>The problem is caused by an error in the IBM i operating system.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>The problem is fixed by installing the following PTF:</p>\r\n<p style=\"padding-left: 30px;\">For IBM i R7V3 - SI63201<br />For IBM i R7V2 - SI63202<br />For IBM i R7V1 - SI63203</p>\r\n<p>When the PTF is active the installation process can be continued.</p>\r\n<p>------------------------&lt; ********, 20/DEC/16 &gt;-------------------------</p>\r\n<p><strong>IBM i:&#160;</strong><strong>The&#160;<em>Additional Application Server on Windows:&#160;IBM DB2 for i</em> does not start </strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The <em>Additional Application Server on Windows:&#160;IBM DB2 for&#160;i</em> does not start.</p>\r\n<p>Details:</p>\r\n<ul>\r\n<li>In the Windows Event Viewer, you can see the following entries under&#160;Windows&#160;Logs &gt;&#160;Application&#160;log:</li>\r\n<li><em>Cannot&#160;open&#160;Profile&#160;\\\\&lt;IBM i server&gt;\\sapmnt\\&lt;SID&gt;\\SYS\\profile\\&lt;SID&gt; Dnn_&lt;Windows_server&gt;.&#160;(Error&#160;5&#160;EIO*:&#160;Input/output&#160;error&#160;OR:&#160;Access&#160;is&#160;denied.)</em></li>\r\n<li>The installation on Windows fails in the phase <em>Unpack SAP archives</em> with the error message:&#160;<em>InternalError:&#160;too&#160;much&#160;recursion&#160;(in&#160;script&#160;NW_DI|ind|ind|ind|ind,&#160;line&#160;1171:&#160;???)&#160;</em></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Important remark: Before changing this setting make sure you have secured your system against known SMB attacks by installing the latest OS security patches from Microsoft! For more information see <strong>SAP Note <a target=\"_blank\" href=\"/notes/2473454\">2473454</a>.</strong></p>\r\n<p>A. The&#160;IBM&#160;i&#160;NetServer&#160;is&#160;not&#160;able&#160;to&#160;handle&#160;NTLMv2&#160;as&#160;authentication&#160;level.</p>\r\n<p style=\"padding-left: 30px;\">This&#160;means&#160;that the&#160;Network&#160;security&#160;on&#160;Windows&#160;has&#160;to&#160;be&#160;adjusted&#160;to&#160;fit&#160;to&#160;the&#160;IBM&#160;i&#160;host. The&#160;Local&#160;Security&#160;Policy&#160;on&#160;the&#160;Windows&#160;Application&#160;Server&#160;must&#160;be&#160;set&#160;as&#160;follows:</p>\r\n<ol style=\"padding-left: 30px;\">\r\n<li style=\"padding-left: 30px;\">Open&#160;Local&#160;Security&#160;Policy</li>\r\n<li style=\"padding-left: 30px;\">Go&#160;to&#160;Local&#160;Policies&#160;-&#160;Security&#160;Options</li>\r\n<li style=\"padding-left: 30px;\">Select&#160;Network&#160;security:&#160;LAN&#160;Manager&#160;authentication&#160;level,&#160;right&#160;click&#160;an&#160;click&#160;Properties</li>\r\n<li style=\"padding-left: 30px;\">Select&#160;option&#160;\"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Send&#160;LM&#160;&amp;&#160;NTLM&#160;-&#160;use&#160;NTLMv2&#160;session&#160;security&#160;if&#160;negotiated</span>\"</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">Now the Windows Appl. Server should be able to start.</p>\r\n<p>B. If your Global Host&#160;resides on IBM i V7R3 you have to&#160;fix the NetServer on IBM&#160;i too:</p>\r\n<ul>\r\n<li>Apply the PTF&#160;<span style=\"font-size: 13.5pt; font-family: 'Calibri',sans-serif; mso-ansi-language: DE; mso-bidi-font-family: 'Times New Roman'; mso-fareast-font-family: 'Times New Roman'; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><strong>MF63136</strong>.</span></li>\r\n</ul>\r\n<p>------------------------&lt; ********, 20/DEC/16 &gt;-------------------------</p>\r\n<p><strong>IBM i:&#160;</strong><strong>The installation of an <em>Additional Application Server on Windows:&#160;IBM DB2 for&#160;i</em> on a Windows Server 2016 fails in phase Install Basics</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p><strong>On IBM i V7R3 only: </strong>Software Provisioning Manager fails while calling sapcpe. <br />The user &lt;SAPSID&gt;ADM gets a timeout: The semaphore timeout period has expired.<br />You can find following entries in the log file sapinst_dev.log:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;TRACE&#160;&#160;&#160;&#160;&#160; 2016-12-12 15:44:49.362 (D_WDF_EM\\&lt;sid&gt;adm) [D:/depot/bas/749_REL/bc_749_REL/src/ins/SAPINST/impl/src/syslib/process/synxcchapp.cpp:657] CSyChildApplicationImpl::doStart()</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Trying to start <a target=\"_blank\" href=\"file://as0114/sapmnt/S73/SYS/exe/uc/NTAMD64/sapcpe.exe\">\\\\&lt;IBM i host&gt;\\sapmnt\\&lt;SID&gt;\\SYS\\exe\\uc\\NTAMD64\\sapcpe.exe</a> using CreateProcessAsUser&lt;token&gt;, NULL, &lt;command line for executable <a target=\"_blank\" href=\"file://as0114/sapmnt/S73/SYS/exe/uc/NTAMD64/sapcpe.exe\">\\\\&lt;IBM i host&gt;\\sapmnt\\&lt;SID&gt;\\SYS\\exe\\uc\\NTAMD64\\sapcpe.exe</a>, NULL, NULL, TRUE, 0x420, NULL, D:/usr/sap/&lt;SID&gt;/D00/work, &amp;StartupInfo, &amp;ProcessInfo)</span></p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ERROR&#160;&#160;&#160;&#160;&#160; 2016-12-12 15:45:53.498 (D_WDF_EM\\&lt;sid&gt;adm) [D:/depot/bas/749_REL/bc_749_REL/src/ins/SAPINST/impl/src/syslib/process/synxcchapp.cpp:668] id=syslib.sysCallFailedWithStackTrace errno=FSL-00009 CSyChildApplicationImpl::doStart()</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">System call failed. Error 2 (The system cannot find the file specified.) in execution of system call 'CreateProcessAsUser' with parameter (&lt;token&gt;, NULL, &lt;command line for executable <a target=\"_blank\" href=\"file://as0114/sapmnt/S73/SYS/exe/uc/NTAMD64/sapcpe.exe\">\\\\&lt;IBM i host&gt;\\sapmnt\\&lt;SID&gt;\\SYS\\exe\\uc\\NTAMD64\\sapcpe.exe</a>, NULL, NULL, TRUE, 0x420, NULL, D:/usr/sap/&lt;SID&gt;/D00/work, &amp;StartupInfo, &amp;ProcessInfo), line (668) in file (D:/depot/bas/749_REL/bc_749_REL/src/ins/SAPINST/impl/src/syslib/process/synxcchapp.cpp), stack trace: D:/depot/bas/749_REL/bc_749_REL/src/ins/SAPINST/impl/src/ejs/EJSController.cpp: 181: EJSControllerImpl::executeScript()</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">d:\\depot\\bas\\749_rel\\bc_749_rel\\src\\ins\\sapinst\\impl\\src\\ejs\\JSExtension.hpp: 1135: CallFunctionBase::call()&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span lang=\"EN-US\" style=\"font-family: 'Verdana',sans-serif; mso-ansi-language: EN-US;\">Details:</span></span></p>\r\n<p><span lang=\"EN-US\" style=\"font-family: 'Verdana',sans-serif; mso-ansi-language: EN-US;\">When multiple SMB connections are&#160;used in parallel to connect an IBM I V7R3 host in the same Windows session, all except the first one will be blocked until the first one is closed. The other connections may get a semaphore timeout. As SWPM needs multiple connections this will&#160;lead to an error.</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Apply the PTF&#160;<strong>MF63136</strong>.</p>\r\n<p>&#160;</p>\r\n<p>------------------------&lt; ********, 26/OCT/16 &gt;-------------------------</p>\r\n<p>&#160;<strong>IBM i:&#160;</strong><strong>SWPM fails on step &#8222;R3load &#8211;testconnect&#8221;&#160;during installation with SAP&#160;HANA on IBM&#160;i&#160;</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>During installation with option hdbclient in a central directory, R3load fails with error message:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;Could not load program /usr/sap/&lt;SAPSID&gt;/SYS/exe/uc/as400_pase_64/R3load:</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Dependent module libSQLDBCHDB.so could not be loaded.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Could not load module libSQLDBCHDB.so.&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ul>\r\n<li>Create a temporary link from the /usr/sap/&lt;SAPSID&gt;/hdbclient to the central directory:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ln &#8211;s /sapmnt/&lt;SAPSID&gt;/exe/uc/as400_pase_64/hdbclient /usr/sap/&lt;SAPSID&gt;/hdbclient&#65279;</span></li>\r\n<li>For for information, see <strong>SAP Note</strong> <a target=\"_blank\" href=\"/notes/2381669\">2381669</a>.</li>\r\n</ul>\r\n<p>------------------------&lt; D022631, 5/OCT/16 &gt;-------------------------</p>\r\n<p><strong>DB2-z/OS: Additional Application Server (Dialog) Instance installation fails on Windows Application Server in Domain</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>The installation of an additional application&#160;server &#160;(7.0X: dialog) instance on a Windows Application Server running in a Domain fails with&#160;a severe db error -89987 when starting the&#160;additional application&#160;server &#160;(7.0X: dialog) instance.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Follow the instructions in&#160;<strong>SAP Note</strong> <a target=\"_blank\" href=\"/notes/2359419\">2359419</a>.</p>\r\n<p>------------------------&lt; D025095, 28/SEP/16 &gt;-------------------------</p>\r\n<p><strong>Update of SAP MMC failed</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>Running msiexec failed with return code 1603: Fatal error during installation. <br />Commandline was msiexec.exe /norestart /L sapmmcX64u.log /i sapmmcX64u.msi /qn</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Uninstall SAP MMC and retry / continue with the chosen option in the software provisioning manager.</p>\r\n<p>------------------------&lt; ********, 20/MAR/15 &gt;-------------------------</p>\r\n<p><strong>Errors related to report RUTCNVFUNCCRE1 in the SWPM log files</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>After SWPM has completed successfully, you find error messages related to report RUTCNVFUNCCRE1 in the SWPM log files.</p>\r\n<p><span style=\"text-decoration: underline;\">Details:</span></p>\r\n<p>These errors&#160;were caused by performance problem in the CDS functions CURRENCY_CONVERSION and UNIT_CONVERSION. For more information, see <strong>SAP Note <a target=\"_blank\" href=\"/notes/2067294\">2067294</a>.</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>You can ignore these errors.</p>\r\n<p>------------------------&lt; D025095, 27/NOV/14 &gt;-------------------------</p>\r\n<p><strong>Unicode conversion&#160;from IBM DB2 to any database using R3load 7.4x not possible</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>Using R3load 7.41 (SAP NetWeaver 7.40 SPS 05 - SPS 07) or R3load 7.42 (SAP NetWeaver 7.40 SPS 08-SPS 09) in a heterogeneous system copy with unicode conversion from IBM DB2 the NAMETAB is corrupted the SWPM stops in the NAMETAB check phase with the error:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;/nuc/linuxppc64/dipgntab&#160;-rwr40&#160;-srctt&#160;DDNTT&#160;-srctf&#160;DDNTF&#160;-dsttt&#160;DDNTT&#160;-dsttf&#160;DDNTF&#160;-ttonly&#160;TT finished&#160;with&#160;status&#160;TST_ERROR&#65279;</span></p>\r\n<p>This is applicable for source database type:</p>\r\n<ul>\r\n<li>DB2 for zOS</li>\r\n<li>DB2 for i5</li>\r\n<li>\r\n<p>DB2 for LUW</p>\r\n</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Note: Current Software Provisioning Manager has included the latest R3load and uses that version if applicable. Check the guide for more details. If your copy / installation used nevertheless the above mentioned R3load versions, proceed as described in&#160;<strong>SAP Note&#160;<a target=\"_blank\" href=\"/notes/2095316\">2095316</a>.</strong></p>\r\n<p>------------------------&lt; I308750, 17/APR/14 &gt;-------------------------</p>\r\n<p><strong>System Copy towards SAP HANA: Table depooling step/phase fails </strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You have installed an SAP system based on:</p>\r\n<ul>\r\n<li>SAP Enhancement Package 1 of SAP NetWeaver 7.3 SP 10 or lower</li>\r\n<li>SAP NetWeaver 7.4 SP5 or lower</li>\r\n</ul>\r\n<p>During a system copy of a SAP system based on Enhancement Package 1 of SAP NetWeaver 7.3 or higher, the&#160;table depooling step/phase fails with&#160;an error like the following:</p>\r\n<ul>\r\n<li>Error executing report SNHI_DELIVERY_UNIT_MIGRATION: Invalid access with negative length to a string. Please check the SAP HANA Database content manually or run SNHI_DELIVERY_UNIT_MIGRATION again.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Apply <strong>SAP Note</strong> <a target=\"_blank\" href=\"/notes/1903295\">1903295</a>.</p>\r\n<p>------------------------&lt; C1079905, 13/SEP/13 &gt;------------------------</p>\r\n<p><strong>DB2-z/OS: J2EE server - update database statistics</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You want to update database statistics of an J2EE server connected to DB2 for z/OS.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>See <strong>SAP Note</strong> <a target=\"_blank\" href=\"/notes/1267933\">1267933</a>.<br /><br />------------------------&lt; D056915, 15/MAY/13 &gt;-------------------------</p>\r\n<p><strong>Table MLICHECK is reported as missing in database</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>If the system reports that the table MLICHECK is missing in the database, even though it has been deleted explicitly.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>See <strong>SAP Note</strong> <a target=\"_blank\" href=\"/notes/1523316\">1523316</a>.<br /><br /><br />-------------------------&lt;D045843, 19/FEB/08&gt;--------------------------</p>\r\n<p><strong>ABAP+Java: FSL-01003&#160;&#160;Unable to modify&#160;&#160;account user=\"&lt;sapsid&gt;adm\" uid=\"&lt;user ID of &lt;sapsid&gt;adm\"</strong><br /><br /><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;ERROR 2008-01-16 18:30:18</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">FSL-05014&#160;&#160;Command '/usr/sbin/usermod' with parameters '-G \"dba,oper,sapsys,sapinst\" &lt;sapsid&gt;adm' failed with return code 8: Login &lt;sapsid&gt;adm is currently in use</span><br /><br /><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ol>\r\n<li>Stop the ABAP stack as described in the installation guide (Additional Information -&gt; Starting and Stopping the SAP System)</li>\r\n<li>Open a command prompt and execute the command '/usr/sbin/usermod'</li>\r\n<li>Continue with the installation.</li>\r\n</ol>\r\n<p>&#160;</p>\r\n<p><strong>++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p><strong>4&#160;&#160;Follow - Up Activities</strong></p>\r\n<p><strong>++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++</strong></p>\r\n<p>-------------------------------------&lt;I323815, DEC 19, 2023&gt;---------------------------------------------------------------------</p>\r\n<p><strong>RZ10 or SAPPFPA reports return Warning messages</strong></p>\r\n<p>After provisioning of an SAP System, the RZ10 or SAPPFPA reports return Warning messages as follows:<br /> <br />&#8220;W:Unknown parameter dbms/name, a check can not be performed&#8221;<br /> <br />The &#8220;dbms/name&#8221; stands for property name defined within the profile. Most probably the property is determined obsolete. You can either fix them accordingly or use the version of the SWPM that handles them &#8211; as of SWPM 1.0 SP41 / SWPM 2.0 SP18.</p>\r\n<p>The parameter \"SAPFQDN\" appears in rare cases, for example if SAP_BASIS is 7.31. You can ignore the warning as it disappears in the higher releases of SAP_BASIS and SAP kernel.</p>\r\n<p>-------------------------------------&lt;D025095, SEP 17, 2020&gt;---------------------------------------------------------------------</p>\r\n<p><strong>Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure you apply the latest Kernel and AS Java Patches</strong></p>\r\n<p><strong>Note:</strong>&#160;SAPJVM 8.1 patch-level restrictions as defined in SAP Note <a target=\"_blank\" href=\"/notes/2951691\">2951691</a>&#160;do not apply if you are using SAP Netweaver 7.5 SP22 Java media.</p>\r\n<p>Since you ran the installation with a lower version of the SAPJVM archive and Java SCAs (see <strong>3 </strong><em><strong>Planning and Preparation</strong> -&gt; Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure that the SAPJVM 8.1 archive you use has a patch level of 63 or lower</em>), we strongly recommend that you apply the latest SAP Kernel version and AS Java patches. For more information, see section&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Applying the Latest Kernel and Support Package Stacks</span>&#160;in the installation guide.</p>\r\n<p>Make sure that you include the latest patch of the J2EE ENGINE SERVERCORE package and all dependent packages in your upgrade. For more information, see&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2951691\">2951691</a>.</p>\r\n<p>If you are running an installation using a Stack XML file, we strongly recommend that you continue with running the Software Update Manager which has been already started by the software provisioning manager. The Stack XML file contains the latest update information from the Maintenance Planner, about latest about the latest AS Java and SAP Kernel patches.</p>\r\n<p><span style=\"text-decoration: underline;\">See also the following items within this SAP Note:</span></p>\r\n<p><strong>Planning and Preparation </strong>-&gt;<strong> Installation of SAP systems based on SAP NetWeaver 7.5 AS Java: Make sure that the SAPJVM 8.1 archive you use has a patch level of 63 or lower</strong></p>\r\n<p><strong><strong><strong>Installation </strong></strong></strong>-&gt;<strong><strong><strong> Installation of SAP systems based on SAP NetWeaver 7.5 AS Java:&#160;</strong>PAS installation fails due to SAPJVM Patch Level</strong></strong></p>\r\n<p>&#160;</p>\r\n<p>------------------------&lt;********, 28/MAY/20&gt;-------------------------</p>\r\n<p><strong>IBM i: After installing a stack kernel 721 PL1300 or a stack kernel 722 PL900 a PXA error message occurs in the work processes&#160;</strong></p>\r\n<p><strong>SAP Kernel 721 or 721 EXT PL1300 and SAP Kernel 722 EXT or 722 EX2 PL900</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>After the installation of a stack kernel 721 PL 1300 or a stack kernel 722 PL900 a PXA error message occurs.</p>\r\n<p>You can see following trace in the log files of the work processes:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#65279;</em>A ***GENER* Trace switched on ***</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A ---PXA-------------------------------------------</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A PXA INITIALIZATION</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A PXA: Locked PXA-Semaphore.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A System page size: 4kb, total admin_size: 44804kb, dir_size: 43992kb.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A Attached to PXA (address 700000350000000 - 7000003749f0000, size 600000K, 1 fragments of 555196K )</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A PXA allocated (address 700000350000000, size 600000K)</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A *** using a guard page at PXA start to protect parameters and release info ***</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><strong>A PXA: mprotect (protect) failed with error: 13&#160;</strong>(address: 700000350000000; length: 1000)</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A Initializing unique PID = 3</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A abap/pxa = shared unprotect gen_remote</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A PXA: checking structure sizes: 888|272|16</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A PXA INITIALIZATION FINISHED</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">A ---PXA-------------------------------------------</span></p>\r\n<p>&#65279;<span style=\"text-decoration: underline;\">Solution:&#65279;</span>&#65279;</p>\r\n<p>Please update your SAP System kernel to the latest PL of SAP kernel available on the SAP Support Portal. For more information, see the&#160;<a target=\"_blank\" href=\"/notes/2870467\">SAP Note&#160;2870467 - PXA: mprotect returns errors on IBM i</a>. For more information how to update the SAP System Kernel on IBM i, see&#160;<a target=\"_blank\" href=\"/notes/1632755\">SAP Note&#160;1632755</a>.<a target=\"_blank\" href=\"/notes/2805638\"><br /></a></p>\r\n<p>Afterwards restart your SAP System again. Now the PXA error message is gone.</p>\r\n<p>------------------------&lt; ********, 25/MAY/20 &gt;----------------------------</p>\r\n<p><strong>IBM i:&#160;After creating a new SAP System you find an ERROR trace in a log file of the work processes</strong></p>\r\n<p><strong>Only SAP Kernel 721 EXT DVD for SWPM 1.0 SP28</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>After you have created a new SAP System (Installation or System Copy) using the SAP Kernel 721 EXT DVD for&#160;SWPM 1.0 SP28 you find an ERROR trace in a log file of the work processes.</p>\r\n<p>The ERROR trace looks like this:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;M *** ERROR =&gt; ROLL_SHM must be the same as ROLL_MAXFS on AS/400,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ROLL_SHM will be ignored and ROLL_MAXFS used instead. [thxxhead.c 21591]</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">M *** ERROR =&gt; PG_SHM must be the same as PG_MAXFS on AS/400,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">PG_SHM will be ignored and PG_MAXFS used instead. [thxxhead.c 21679]&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Diagnosis:</span></p>\r\n<p>The ERROR trace only occurs when you are using the SAP Kernel 721 EXT DVD for SWPM 1.0 SP28 (PL1300):<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">DVD 51054091_2: 721 EXT NUC Kernel for SWPM SP28 - iSeries(os400)</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">DVD 51054091_4: 721 EXT UC Kernel for SWPM SP28 - iSeries(os400)&#65279;</span></p>\r\n<p>Till now, no issue is known related to this ERROR trace. Internally the SAP System is using the correct values. There is no reason why the ERROR trace is written into the log file of the work process.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Please update your SAP Sytem kernel immediately to the latest PL of SAP 721 EXT kernel available on the SAP Support Portal.&#160;For more information how to update the SAP System Kernel on IBM i, see&#160;<a target=\"_blank\" href=\"/notes/1632755\">SAP Note&#160;1632755</a>.</p>\r\n<p>------------------------&lt; D024828, 11/09/17 &gt;-----------------------------</p>\r\n<p>Software Provisioning Manager aborts with error in phase Executing ABAP Report RUTDDLSCREATE</p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<ul>\r\n<li>Software Provisioning Manager aborts in phase Executing ABAP Report RUTDDLSCREATE with an error<em>.</em></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<ul>\r\n<li>See&#160;<strong>SAP Note <a target=\"_blank\" href=\"/notes/2562635\">2562635</a></strong> - Regenerate DDL Sources Failed for known issues</li>\r\n<li>You can accept the error by clicking <em>'OK'</em> &#8211; Software Provisioning Manager&#160;will proceed.</li>\r\n<li>After the&#160;installation has completed you have to run report RUTDDLSCREATE using variant SAP_CDSCRE in the&#160;background. This&#160;will take several hours to complete.</li>\r\n</ul>\r\n<p>------------------------&lt; ********, 28/04/17 &gt;----------------------------</p>\r\n<p><strong>IBM i:&#160;After a distributed rename of a single instance, the other instances of the same SAP system&#160;on the same host cannot be started anymore</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>After&#160;renaming&#160;a single instance of a distributed SAP system while keeping the SAPSID, the other instances&#160;on the same host of the same SAP system cannot be started anymore.&#160;(When the source and the target SAPSID are different this issue will not occur.)</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>When the SAP rename is&#160;done&#160;and the SAPSID is kept the same, the rename has to delete&#160;old objects in the library R3&lt;SAPSID&gt;400 like the subsystem description.&#160;For a distributed SAP rename only the old objects of the&#160;renamed instance should be deleted. Currently, for a distributed rename all objects of the library R3&lt;SAPSID&gt;400&#160;of all distributed instances&#160;on the same host&#160;are deleted.</p>\r\n<p>To create the missing objects of the other instances you have to call the command CRTSAPINST for every effected instance that cannot be started anymore. The command CRTSAPINST will repair the related instance.</p>\r\n<p>1) Sign-on with as &lt;SAPSID&gt;ADM</p>\r\n<p>2) <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CRTR3INST SID(&lt;SAPSID&gt;) INST(&lt;Instance_Number&gt;) ROLE(&lt;Instance_Role&gt;) CODEPAGE(&lt;*UNICODE or *ASCII&gt;) WITHCPE(&lt;*YES if SAPCPE is used&gt;)&#65279;</span></p>\r\n<p>3) Start your instance</p>\r\n<p><strong>Note:</strong> This issue is fixed with SWPM 1.0 SP20.</p>\r\n\r\n<p>------------------------&lt; D025095, 22/MAR/17 &gt;-------------------------</p>\r\n<p><strong>Up-to-date installation ABAP: implementation of SAP Notes</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<ul>\r\n<li>After having installed your SAP ABAP system with software provisioning manager 1.0 SP18 (all patch levels) SAP Note 2202020 implementation is incomplete.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Analysis:</span></p>\r\n<ul>\r\n<li>Object SDB2FHDB not implemented with&#160;related correction instruction 012006153241 00019262090003 after installing a ABAP system based on SAP NetWeaver 7.40 SR2 / SP8&#160;for HDB using the stack.xml.</li>\r\n<li>in ABAP when running SDB2FHDB: terminated with a runtime error PERFORM_NOT_FOUND (see ST22)</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Solution</span></p>\r\n<ul>\r\n<li>Deimplement <strong>SAP Note <a target=\"_blank\" href=\"/notes/2202020\">2202020</a></strong> and implement the latest version using transaction SNOTE before implementing further Support Packages with Software Update Manager.</li>\r\n</ul>\r\n<p>------------------------&lt; D050445, 06/MAR/17 &gt;-------------------------</p>\r\n<p><strong>MSS: Inconsistent Global Temporary Table (GTT) objects&#160;after&#160;hom. system copy</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>After having created&#160;your SAP system based on SAP NetWeaver 750/751 via hom. system copy, you might face error symptoms as described in SAP Note <a target=\"_blank\" href=\"/notes/2379616\">2379616</a>.</p>\r\n<p>The problem occurs only, if SWPM 1.0 SP18 PL1 or lower has been used to create the SAP system.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Apply the solution described in&#160;SAP Note <a target=\"_blank\" href=\"/notes/2379616\">2379616</a>.</p>\r\n<p>------------------------&lt;&#160;&#160;D019500, 10/AUG/16 &gt;-----------------------</p>\r\n<p>Missing entries in table BADIIMPL_ENH after installation of Business Suite 2016 (CRM/SRM/SCM 7.0 EHP4, ERP 6.0 EHP8) or&#160;S/4 HANA On-Premise SR0</p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>Missing entries in table BADIIMPL_ENH after installation of&#160;Business Suite 2016 (EHP8) or&#160;S/4 Hana on-premise SR0 :<br />Due to the missing entries in this table, BAdI Implementation cannot be selected in the IMG.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution: </span></p>\r\n<p>Run the report ENHS_BADI_ANALYZE_GENERATE_ALL with parameters CHK_BENH (\"Check Existence in IMG\") and GENERATE (\"Generate Inconsistent BAdIs\") flagged. For details see SAP Note 1656033 - Badi implementations cannot be selected in the IMG</p>\r\n<p>------------------------&lt;&#160;&#160;D022091, 01/JUL/16 &gt;-----------------------</p>\r\n\r\n<p><strong>SAP Kernel 7.42: Apply SAP Note <a target=\"_blank\" href=\"/notes/2200230\">2200230</a> after installation</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>Incorrect *.pse certificate files are generated during installations with early PLs of SAP Kernel 7.42.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Apply&#160;<strong>SAP Note <a target=\"_blank\" href=\"/notes/2200230\">2200230</a></strong>.</p>\r\n<p>------------------------&lt; D039493, 18/JAN/16 &gt;-----------------------</p>\r\n<p><strong>S/4HANA On-Premise 1511 SR0: Error during client copy</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>During a client copy, you get an error like the following :<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">/VSO/M_DATA_CLUS LE-DSD-VSO-MP DDIC Error</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">/VSO/M_GRAPHSTR&#160; LE-DSD-VSO-MP DDIC Error&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Proceed as described in <strong>SAP Note <a target=\"_blank\" href=\"/notes/1951247\">1951247</a></strong>.</p>\r\n<p>------------------------&lt;D045843, 09/JUL/15 &gt;-------------------------</p>\r\n<p><strong>NW 7.4 SR2 Advanced Adapter Engine: Deploy Software Component &#8220;GWJPO&#8221; manually</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>You are installing the SAP Netweaver 740 Product Instance &#8220;Advanced Adapter Engine Extended&#8221; from the NW 740 SR2 JAVA medium with the Material Number 51048524 (51048524_6 and 51048524_7).</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Make sure that you manually deploy&#160; Software Component &#8220;GWJPO&#8221; (located on the JAVA DVD at &#8220;DATA_UNITS/JAVA_J2EE_OSINDEP_UT/GWJPO08_0.SCA) after the software provisioning manager has completed the installation. This manual post-installation step is required because the deployment of this Software Component is currently skipped during the installation process.</p>\r\n<p>------------------------&lt;I308750, 04/DEC/14 &gt;-------------------------</p>\r\n<p><strong>Corrupted Job definitions in installation export for ERP and SCM from Business Suite 2013 SR0, SR1 and SR2</strong></p>\r\n<p dir=\"ltr\"><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p dir=\"ltr\">Corrupted Job definitions for EU_PUT and EU_REORG exist in the installation export for ERP and SCM from Business Suite 2013 SR0, SR1 and SR2 (see the details in <strong>SAP Note</strong> <span style=\"font-size: medium; font-family: Calibri,Japanese Gothic;\"><span style=\"font-size: medium; font-family: Calibri,Japanese Gothic;\"><a target=\"_blank\" href=\"/notes/2051503\">2051503</a>).</span></span></p>\r\n<p dir=\"ltr\"><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p dir=\"ltr\">Delete the corrupted jobs.</p>\r\n<p>------------------------&lt; D022631, 31/OCT/16 &amp; 18/OCT/22 &gt;-------------------------</p>\r\n\r\n<p><strong>DB2-z/OS: Installation of solutions based on SAP NetWeaver 7.00, 7.01 &amp; 7.02</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>This entry applies if you installed an SAP ABAP-only or&#160;SAP dual-stack systems based on <strong>SAP NetWeaver 7.00, 7.01, and 7.02 on DB2 for z/OS. </strong>You have set up the transport management and update to the minimum support package stack level required for these database versions.</p>\r\n<p><span style=\"text-decoration: underline;\">Details:</span></p>\r\n<p>After setting up the transport management system you need to &#8211; <strong>before doing anything else</strong> &#8211; immediately update the support package stack to the minimum level required for these database versions. This is necessary to adjust the ABAP core functionality to be fully operational with Db2 12 and higher.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>The process is described in detail in <strong>SAP Notes </strong><strong><a target=\"_blank\" href=\"/notes/2303045\">2303045</a></strong>&#160;(Db2 12 for z/OS) and&#160;<strong><a target=\"_blank\" href=\"/notes/3152939\">3152939</a></strong>&#160;(Db2 13 for z/OS). Only continue after successfully executing all steps listed in there!</p>\r\n<p><span style=\"font-size: medium;\">&#160;</span></p>\r\n<ul style=\"margin-top: 0in;\" type=\"disc\">\r\n<li class=\"MsoListParagraph\" style=\"margin-left: 0in; mso-list: l1 level1 lfo1;\"><span lang=\"EN-US\" style=\"font-size: 9.0pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US;\">SAP S/4HANA, on-premise edition 1511 (OOM Dec 2020)</span></li>\r\n<li class=\"MsoListParagraph\" style=\"margin-left: 0in; mso-list: l1 level1 lfo1;\"><span lang=\"EN-US\" style=\"font-size: 9.0pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US;\">SAP Business Suite powered by SAP HANA (OOM Dec 2020)</span></li>\r\n</ul>\r\n<ul style=\"margin-top: 0in;\" type=\"disc\">\r\n<li class=\"MsoListParagraph\" style=\"margin-left: .25in; mso-list: l0 level1 lfo2;\"><span lang=\"EN-US\" style=\"font-size: 9.0pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US;\">EHP2 for SAP CRM 7.0 On HANA</span></li>\r\n<li class=\"MsoListParagraph\" style=\"margin-left: .25in; mso-list: l0 level1 lfo2;\"><span lang=\"EN-US\" style=\"font-size: 9.0pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US;\">EHP6 for SAP ERP 6.0 On HANA</span></li>\r\n<li class=\"MsoListParagraph\" style=\"margin-left: .25in; mso-list: l0 level1 lfo2;\"><span lang=\"EN-US\" style=\"font-size: 9.0pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US;\">EHP2 for SAP SCM 7.0 On HANA</span></li>\r\n</ul>\r\n<ul style=\"margin-top: 0in;\" type=\"disc\">\r\n<li class=\"MsoListParagraph\" style=\"margin-left: 0in; mso-list: l1 level1 lfo1;\"><span lang=\"EN-US\" style=\"font-size: 9.0pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US;\">BW/4HANA 1.0 (OOM Dec 2021)</span></li>\r\n<li class=\"MsoListParagraph\" style=\"margin-left: 0in; mso-list: l1 level1 lfo1;\"><span lang=\"EN-US\" style=\"font-size: 9.0pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US;\">S/4 HANA 1610 (OOM Dec 2021)</span></li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-SDB-INS (Installation with MaxDB)"}, {"Key": "Other Components", "Value": "BC-INS-NT (SAP Netweaver based Solutions on Windows)"}, {"Key": "Other Components", "Value": "BC-DB-ORA-INS (Installation SAP System)"}, {"Key": "Other Components", "Value": "HAN-LM-INS-SAP (Installation of SAP Systems on HANA)"}, {"Key": "Other Components", "Value": "BC-INS-SRN (System Rename Utility)"}, {"Key": "Other Components", "Value": "BC-DB-MSS-INS (SQL Server Installation for SAP NetWeaver Products)"}, {"Key": "Other Components", "Value": "BC-INS-DSS (Dual-Stack Split Tool)"}, {"Key": "Other Components", "Value": "BC-DB-DB4 (DB2 for AS/400)"}, {"Key": "Other Components", "Value": "BC-DB-DB6-INS (Installation SAP System)"}, {"Key": "Other Components", "Value": "BC-INS-MIG (OS/DB Migrations with SWPM and DB refresh)"}, {"Key": "Other Components", "Value": "BC-INS-AS4 (Installation and Upgrade AS/400)"}, {"Key": "Other Components", "Value": "BC-DB-DB2-INS (DB2 for z/OS - Installation)"}, {"Key": "Other Components", "Value": "BC-INS-FWK (sapinst, slcb, elmbridge, elm replication service)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D035521)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035521)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001680045/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3260641", "RefComponent": "BC-INS-SWPM", "RefTitle": "Software Provisioning Manager (SWPM) shows errors in variety of steps in case of installation of NetWeaver Java 7.50 SP0, S/4HANA 1709 Java stack and S/4HANA 1809 Java stack", "RefUrl": "/notes/3260641"}, {"RefNumber": "2618169", "RefComponent": "BC-INS-UNX", "RefTitle": "Solution Manager 7.2 for Linux Power LE is not visible in  older versions of SWPM", "RefUrl": "/notes/2618169"}, {"RefNumber": "2473454", "RefComponent": "BC-OP-NT", "RefTitle": "Customer Guidance for WannaCrypt attacks", "RefUrl": "/notes/2473454"}, {"RefNumber": "2250144", "RefComponent": "HAN-DB-SEC", "RefTitle": "FAQ: SAP HANA Secure User Store", "RefUrl": "/notes/2250144"}, {"RefNumber": "2220627", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA LOBs", "RefUrl": "/notes/2220627"}, {"RefNumber": "929929", "RefComponent": "BC-INS-FWK", "RefTitle": "Latest SAPinst Patch", "RefUrl": "/notes/929929"}, {"RefNumber": "784118", "RefComponent": "BC-INS-MIG-MMA", "RefTitle": "System Copy Tools for ABAP Systems", "RefUrl": "/notes/784118"}, {"RefNumber": "3358301", "RefComponent": "BC-OP-NT", "RefTitle": "Windows/IBM i synchronization issue", "RefUrl": "/notes/3358301"}, {"RefNumber": "3220901", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP35 - covers no longer supported CPU and operation system versions", "RefUrl": "/notes/3220901"}, {"RefNumber": "3207660", "RefComponent": "BC-INS-SRN", "RefTitle": "Potential loss of file system content while using SWPM-based Rename and keeping the SAPSID", "RefUrl": "/notes/3207660"}, {"RefNumber": "3152939", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "DB2-z/OS:v13: Installation & system copy with Db2 13", "RefUrl": "/notes/3152939"}, {"RefNumber": "3145046", "RefComponent": "BC-CST-WDP", "RefTitle": "[CVE-2022-27656] Cross-Site Scripting (XSS) vulnerability in administration UI of SAP Webdispatcher and SAP Netweaver AS for ABAP and Java (ICM)", "RefUrl": "/notes/3145046"}, {"RefNumber": "2998013", "RefComponent": "BC-JVM-JRT", "RefTitle": "SAP JVM 8.1 As Runtime for SAP Tools - Additional Information", "RefUrl": "/notes/2998013"}, {"RefNumber": "2980160", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for SWPM10RMSP30_<Version>.SAR of Software Provisioning Manager 1.0 - Covers Options for no longer supported SAP Products in Software Provisioning Manager 1.0 and Access to Related Guides", "RefUrl": "/notes/2980160"}, {"RefNumber": "2951691", "RefComponent": "BC-I18-JAV", "RefTitle": "Upgrade of SAPJVM to SAP JVM(8.1.064) fails / Installation of  \"Application Server Java\" based on SAP JVM(8.1.064) fails", "RefUrl": "/notes/2951691"}, {"RefNumber": "2755706", "RefComponent": "BC-INS-SWPM", "RefTitle": "ABAP system provisioning using - SWPM 1.0 SP25 PL0 and PL1", "RefUrl": "/notes/2755706"}, {"RefNumber": "2725610", "RefComponent": "BC-SRV-NUM", "RefTitle": "NR: TIME_OUT dump during automatic interval creation", "RefUrl": "/notes/2725610"}, {"RefNumber": "2709131", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "DB2 z/OS: Installing Additional Application Server Instance to Java-based System after DSS has been performed", "RefUrl": "/notes/2709131"}, {"RefNumber": "2695381", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS: Release of Linux on IBM Power Systems Little Endian as an application server platform", "RefUrl": "/notes/2695381"}, {"RefNumber": "2595196", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *70JDS*.SAR of Software Provisioning Manager 1.0 - Covers No Longer Supported Java and Dual-Stack Options and Access to Guides for Software Provisioning Manager 1.0 Java and Dual Stack", "RefUrl": "/notes/2595196"}, {"RefNumber": "2568783", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 2.0 (recommended: SWPM 2.0 SP17)", "RefUrl": "/notes/2568783"}, {"RefNumber": "2505142", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *RMOS*.SAR of Software Provisioning Manager 1.0 - covers no longer supported operation system versions", "RefUrl": "/notes/2505142"}, {"RefNumber": "2393060", "RefComponent": "BC-INS-FWK", "RefTitle": "SAPinst Framework 749 Central Note", "RefUrl": "/notes/2393060"}, {"RefNumber": "2381438", "RefComponent": "HAN-DB-CLI", "RefTitle": "Incorrect or Missing LABEL.ASC files in HANA client installer archives on SMP in SPS12", "RefUrl": "/notes/2381438"}, {"RefNumber": "2379616", "RefComponent": "BC-DB-MSS", "RefTitle": "Error when accessing CDS views after a homogeneous system copy or refresh", "RefUrl": "/notes/2379616"}, {"RefNumber": "2378874", "RefComponent": "BC-OP-PLNX", "RefTitle": "Install SAP Solutions on Linux on IBM Power Systems (little endian)", "RefUrl": "/notes/2378874"}, {"RefNumber": "2365849", "RefComponent": "HAN-LM-INS-SAP", "RefTitle": "Installation of SAP Systems Based on SAP NetWeaver: SAP HANA Database", "RefUrl": "/notes/2365849"}, {"RefNumber": "2365014", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "Installation of SAP Systems Based on SAP NetWeaver: SAP MaxDB", "RefUrl": "/notes/2365014"}, {"RefNumber": "2305826", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "dipgntab - Initialization Error During Installation", "RefUrl": "/notes/2305826"}, {"RefNumber": "2303045", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "DB2-z/OS:v12: Installation & system copy with DB2 12", "RefUrl": "/notes/2303045"}, {"RefNumber": "2230669", "RefComponent": "BC-INS-SWPM", "RefTitle": "System Provisioning Using an Input Parameter File", "RefUrl": "/notes/2230669"}, {"RefNumber": "2200230", "RefComponent": "BC-CST-STS", "RefTitle": "Problems with use of system PKI", "RefUrl": "/notes/2200230"}, {"RefNumber": "2195019", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Using SAP kernel 748 and higher or SAPinst 749 and higher on RHEL 6, OL6 and SLES 11", "RefUrl": "/notes/2195019"}, {"RefNumber": "2173485", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2173485"}, {"RefNumber": "2172935", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Installation - SAP Systems based on SAP NetWeaver : Oracle Database", "RefUrl": "/notes/2172935"}, {"RefNumber": "2154997", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Migration of hdbuserstore entries to ABAP SSFS", "RefUrl": "/notes/2154997"}, {"RefNumber": "2095316", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "R3load corrupts DDNTF table when changing the database", "RefUrl": "/notes/2095316"}, {"RefNumber": "2067859", "RefComponent": "BC-SEC", "RefTitle": "Potential Exposure to Digital Signature Spoofing", "RefUrl": "/notes/2067859"}, {"RefNumber": "2040866", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "Update R3load for Software Provisioning Manager 1.0", "RefUrl": "/notes/2040866"}, {"RefNumber": "1973403", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "SWPM for Oracle Exadata or Oracle Database Appliance on Oracle Linux 5", "RefUrl": "/notes/1973403"}, {"RefNumber": "1953429", "RefComponent": "HAN-LM-INS", "RefTitle": "SAP HANA and SAP NetWeaver AS ABAP on one Server", "RefUrl": "/notes/1953429"}, {"RefNumber": "1951247", "RefComponent": "LE-DSD", "RefTitle": "Problem during client copy for tables /VSO/M_DATA_CLUS and /VSO/M_GRAPHSTR", "RefUrl": "/notes/1951247"}, {"RefNumber": "1931675", "RefComponent": "BC-CST-DP", "RefTitle": "IP multicast configuration for dispatcher wakeup mechanism", "RefUrl": "/notes/1931675"}, {"RefNumber": "1903295", "RefComponent": "BC-DWB-AIE-AHI", "RefTitle": "NHI-API: Runtime error during HANA Repository access", "RefUrl": "/notes/1903295"}, {"RefNumber": "1898687", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Merge start profile with instance profile (Linux/Unix OS)", "RefUrl": "/notes/1898687"}, {"RefNumber": "1895271", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1895271"}, {"RefNumber": "1892354", "RefComponent": "BC-INS-MIG", "RefTitle": "SAP Strategy for Cluster and Pool Tables", "RefUrl": "/notes/1892354"}, {"RefNumber": "1877731", "RefComponent": "BC-INS", "RefTitle": "Installed Software Information (ISI)", "RefUrl": "/notes/1877731"}, {"RefNumber": "1858920", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent installation with Software Provisioning Manager", "RefUrl": "/notes/1858920"}, {"RefNumber": "1844503", "RefComponent": "BC-XI-IBC", "RefTitle": "Create Communication Channels automatically in NW 7.4", "RefUrl": "/notes/1844503"}, {"RefNumber": "1844468", "RefComponent": "HAN-DB", "RefTitle": "Homogeneous system copy on SAP HANA", "RefUrl": "/notes/1844468"}, {"RefNumber": "1831911", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB: Error during installation of PI/CE 7.10 PI/CE 7.11", "RefUrl": "/notes/1831911"}, {"RefNumber": "1807334", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1807334"}, {"RefNumber": "1807325", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: R3ldctl cores in prepare database load", "RefUrl": "/notes/1807325"}, {"RefNumber": "1806935", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA: Corrupt database after R3load import", "RefUrl": "/notes/1806935"}, {"RefNumber": "1806816", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Media list for installations based on NW 7.3 incl. EHPs", "RefUrl": "/notes/1806816"}, {"RefNumber": "1806601", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Media list for installations based on NW 7.0 incl. EHPs", "RefUrl": "/notes/1806601"}, {"RefNumber": "1802965", "RefComponent": "HAN-DB", "RefTitle": "R3load on HANA: SAPVIEW.cmd (TSK) Error: object ... not in", "RefUrl": "/notes/1802965"}, {"RefNumber": "1799291", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Inst.Systems Based on NW 7.0 incl. EHPs: SAP ASE", "RefUrl": "/notes/1799291"}, {"RefNumber": "1797362", "RefComponent": "BC-INS-DSS", "RefTitle": "Dual-Stack Split for Systems Based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1797362"}, {"RefNumber": "1789561", "RefComponent": "BC-DWB-DIC", "RefTitle": "Cluster table is converted unexpectedly", "RefUrl": "/notes/1789561"}, {"RefNumber": "1783927", "RefComponent": "BC-INS-MIG", "RefTitle": "Prerequisites for table splitting with target SAP HANA database", "RefUrl": "/notes/1783927"}, {"RefNumber": "1776801", "RefComponent": "BC-INS", "RefTitle": "Inst.Systems Based on NW 7.0 incl. EHPs:IBM i", "RefUrl": "/notes/1776801"}, {"RefNumber": "1748985", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1748985"}, {"RefNumber": "1748888", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Inst.Systems Based on NW 7.3 and Higher: SAP ASE", "RefUrl": "/notes/1748888"}, {"RefNumber": "1738258", "RefComponent": "BC-INS-MIG", "RefTitle": "System Copy for Systems Based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1738258"}, {"RefNumber": "1724557", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.0 incl. EHPs: Windows Db2 LUW", "RefUrl": "/notes/1724557"}, {"RefNumber": "1724554", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.0 incl. EHPs: UNIX Db2 for LUW", "RefUrl": "/notes/1724554"}, {"RefNumber": "1723084", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1723084"}, {"RefNumber": "1723083", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1723083"}, {"RefNumber": "1718576", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Migration from SAP HANA to another database system", "RefUrl": "/notes/1718576"}, {"RefNumber": "1718414", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.0 incl. EHPs: SQL Server", "RefUrl": "/notes/1718414"}, {"RefNumber": "1718413", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.0 incl. EHPs: Windows", "RefUrl": "/notes/1718413"}, {"RefNumber": "1715048", "RefComponent": "HAN-LM-INS", "RefTitle": "BW 7.30 new features for installation or migration", "RefUrl": "/notes/1715048"}, {"RefNumber": "1714491", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1714491"}, {"RefNumber": "1710994", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.1 and higher: SQL Server", "RefUrl": "/notes/1710994"}, {"RefNumber": "1710950", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.1 and higher: Windows", "RefUrl": "/notes/1710950"}, {"RefNumber": "1707362", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.31 and Higher: Windows Db2 LUW", "RefUrl": "/notes/1707362"}, {"RefNumber": "1707361", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.31 and Higher: UNIX Db2 for LUW", "RefUrl": "/notes/1707361"}, {"RefNumber": "1706931", "RefComponent": "HAN-LM-INS-SAP", "RefTitle": "Inst. SAP Sys. Based on NW 7.3 and higher: SAP HANA DB, Win", "RefUrl": "/notes/1706931"}, {"RefNumber": "1706930", "RefComponent": "HAN-LM-INS-SAP", "RefTitle": "Inst. SAP Sys. Based on NW 7.3 and higher: SAP HANA DB, UNIX", "RefUrl": "/notes/1706930"}, {"RefNumber": "1706929", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "Inst. SAP Sys. Based on NW 7.1 and higher: SAP Max DB, Win", "RefUrl": "/notes/1706929"}, {"RefNumber": "1706928", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "Inst. SAP Sys. Based on NW 7.1 and higher: SAP Max DB, UNIX", "RefUrl": "/notes/1706928"}, {"RefNumber": "1704753", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst.Systems Based on NetWeaver on UNIX  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1704753"}, {"RefNumber": "1689942", "RefComponent": "BC-INS", "RefTitle": "vendor JDK required for sapinst with install./system copy", "RefUrl": "/notes/1689942"}, {"RefNumber": "1683455", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1683455"}, {"RefNumber": "1683454", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1683454"}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731"}, {"RefNumber": "1619720", "RefComponent": "BC-INS-SRN", "RefTitle": "System Rename for SAP Systems based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1619720"}, {"RefNumber": "1617021", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1617021"}, {"RefNumber": "1617020", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1617020"}, {"RefNumber": "1611802", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1611802"}, {"RefNumber": "1611801", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1611801"}, {"RefNumber": "1603686", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1603686"}, {"RefNumber": "1600929", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP BW powered by SAP HANA DB: Information", "RefUrl": "/notes/1600929"}, {"RefNumber": "1589311", "RefComponent": "BC-EHP-INS", "RefTitle": "Replacements of outdated tools via SL Toolset", "RefUrl": "/notes/1589311"}, {"RefNumber": "1554717", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Planning Information for SAP on SAP ASE", "RefUrl": "/notes/1554717"}, {"RefNumber": "1553301", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553301"}, {"RefNumber": "1548438", "RefComponent": "BC-INS-FWK", "RefTitle": "SAPinst Framework 720-2  Central Note", "RefUrl": "/notes/1548438"}, {"RefNumber": "1528297", "RefComponent": "BC-OP-NT", "RefTitle": "Merge start profile with instance profile", "RefUrl": "/notes/1528297"}, {"RefNumber": "1523316", "RefComponent": "BC-DWB-DIC", "RefTitle": "Table MLICHECK is reported as missing in database", "RefUrl": "/notes/1523316"}, {"RefNumber": "1434181", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1434181"}, {"RefNumber": "1434180", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1434180"}, {"RefNumber": "1267933", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: J2EE server - update database statistics", "RefUrl": "/notes/1267933"}, {"RefNumber": "1232613", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Incompatible JDBC driver version in use", "RefUrl": "/notes/1232613"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3081381", "RefComponent": "BC-INS", "RefTitle": "Software Provisioning Manager  1.0 - versions, download", "RefUrl": "/notes/3081381 "}, {"RefNumber": "1654937", "RefComponent": "BC-INS", "RefTitle": "How to correctly build the SAP Netweaver Kernel Media", "RefUrl": "/notes/1654937 "}, {"RefNumber": "1713277", "RefComponent": "BC-INS", "RefTitle": "SAP Netweaver 7.3 on IBM DB2 Installation reports Incorrect RDBMS Client Media.", "RefUrl": "/notes/1713277 "}, {"RefNumber": "2030584", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "Handing an Export failure due to a ' or \" in split field value of Where (WHR) clause.", "RefUrl": "/notes/2030584 "}, {"RefNumber": "2127760", "RefComponent": "BC-INS-MIG", "RefTitle": "STR files were not generated for REPOLOAD table after it was split using the R3TA table splitter (WHR).", "RefUrl": "/notes/2127760 "}, {"RefNumber": "3260641", "RefComponent": "BC-INS-SWPM", "RefTitle": "Software Provisioning Manager (SWPM) shows errors in variety of steps in case of installation of NetWeaver Java 7.50 SP0, S/4HANA 1709 Java stack and S/4HANA 1809 Java stack", "RefUrl": "/notes/3260641 "}, {"RefNumber": "1565076", "RefComponent": "BC-INS-SWPM", "RefTitle": "\"Missing\" installation service for 32-bit operating systems on Software Provisioning Manager Tool and on Installation Master DVDs", "RefUrl": "/notes/1565076 "}, {"RefNumber": "1875891", "RefComponent": "BC-INS-SWPM", "RefTitle": "Could not match the LABEL:LIVECACHE:LCA60:LC77:LINUX_X86_64", "RefUrl": "/notes/1875891 "}, {"RefNumber": "3056631", "RefComponent": "BC-INS-MIG", "RefTitle": "System Copy (and installation, dualstack split) of SOLMAN 7.2 AS Java based on SAP NetWeaver 7.40 and other AS Java systems based on SAP NetWeaver 7.1#, 7.31 or 7.40 - (out-of-maintenance since 12/31/2020)", "RefUrl": "/notes/3056631 "}, {"RefNumber": "2985898", "RefComponent": "BC-INS-SWPM", "RefTitle": "SAP Content Server installation failed with: \"Unknown client parameter item\"", "RefUrl": "/notes/2985898 "}, {"RefNumber": "2569199", "RefComponent": "BC-INS-DSS", "RefTitle": "NoClassDefFoundError exception for class com/sap/engine/offline/OfflineToolStart encountered during dual-stack split or system copy", "RefUrl": "/notes/2569199 "}, {"RefNumber": "2957941", "RefComponent": "BC-INS-MIG", "RefTitle": "Error message: \"File empty except for version information\"  during the System Copy import.", "RefUrl": "/notes/2957941 "}, {"RefNumber": "2939216", "RefComponent": "BC-INS-SWPM", "RefTitle": "ABAP SSFS option selected and cannot be changed in SWPM dialog for HANA DB", "RefUrl": "/notes/2939216 "}, {"RefNumber": "2497680", "RefComponent": "BC-INS-SWPM", "RefTitle": "Missing media Inst. Export ABAP750 S4FE, SAP:ASABAP:S4_751:EXPORT(1/3):*.*", "RefUrl": "/notes/2497680 "}, {"RefNumber": "2455721", "RefComponent": "BC-INS-NT", "RefTitle": "MS SQL Server patch level is too low. during System copy", "RefUrl": "/notes/2455721 "}, {"RefNumber": "2415511", "RefComponent": "BC-INS-FWK", "RefTitle": "UnknownHostException when executing sapinst / SWPM", "RefUrl": "/notes/2415511 "}, {"RefNumber": "2914341", "RefComponent": "BC-INS-SWPM", "RefTitle": "System copy for Business Suite JAVA stack", "RefUrl": "/notes/2914341 "}, {"RefNumber": "2874768", "RefComponent": "BC-INS-SWPM", "RefTitle": "How to install SAP Solution Manager 7.2 based on NW 7.4/7.5 AS Java on IBM PowerPC (PPC) Linux Little Endian (LE)", "RefUrl": "/notes/2874768 "}, {"RefNumber": "2825193", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS Watchdog is failing, (has GRAY stastus) in System start phase during installation with Software Provisioning Manager (SWPM Tool)", "RefUrl": "/notes/2825193 "}, {"RefNumber": "2822055", "RefComponent": "BC-INS-SWPM", "RefTitle": "SAP system <SID> is an ABAP or ABAP+Java system. Cannot convert this to a Java standalone system.", "RefUrl": "/notes/2822055 "}, {"RefNumber": "2792010", "RefComponent": "BC-INS-UNX", "RefTitle": "SWPM: \"The GUI could not be started because the environment variable DISPLAY is not set\"", "RefUrl": "/notes/2792010 "}, {"RefNumber": "2795915", "RefComponent": "BC-INS-MIG", "RefTitle": "Export is failing with \"SAP Java Instances Cannot Be Found\" error", "RefUrl": "/notes/2795915 "}, {"RefNumber": "2786040", "RefComponent": "BC-INS-SWPM", "RefTitle": "SWPM Error:  TypeError: instanceForDeploy has no properties - duiring IDM installation/update", "RefUrl": "/notes/2786040 "}, {"RefNumber": "2528718", "RefComponent": "BC-INS-SRN", "RefTitle": "Cannot find option to rename Additional Application Server Instances in SWPM", "RefUrl": "/notes/2528718 "}, {"RefNumber": "2767546", "RefComponent": "BC-DB-MSS", "RefTitle": "MS SQL Server collation error in SWPM", "RefUrl": "/notes/2767546 "}, {"RefNumber": "2207314", "RefComponent": "BC-DB-SDB", "RefTitle": "DOES_INSTALLATION_EXIST: MaxDB Software must be installed before this class can be instantiated", "RefUrl": "/notes/2207314 "}, {"RefNumber": "2205696", "RefComponent": "BC-INS-MIG", "RefTitle": "SWPM Error: Field MSGID: type BINARY does not have a devault value", "RefUrl": "/notes/2205696 "}, {"RefNumber": "2188831", "RefComponent": "BC-INS-MIG", "RefTitle": "Error while setting real user to [number] during System Copy Export", "RefUrl": "/notes/2188831 "}, {"RefNumber": "2089854", "RefComponent": "BC-INS-DSS", "RefTitle": "Dual Stack Split: arguments dbSidOrSrcObject and dbHome are not valid", "RefUrl": "/notes/2089854 "}, {"RefNumber": "2076364", "RefComponent": "BC-INS-UNX", "RefTitle": "SWPM Error: \"FSH-00006  Return value of function  getpwnam(root) is NULL\"", "RefUrl": "/notes/2076364 "}, {"RefNumber": "2025064", "RefComponent": "BC-I18", "RefTitle": "R3Load Error: (CNV) ERROR: There shall be only one Unicode in a database", "RefUrl": "/notes/2025064 "}, {"RefNumber": "2022318", "RefComponent": "BC-INS-SWPM", "RefTitle": "Error: \"java.lang.NoClassDefFoundError: iaik/utils/Base64Exception\" during SWPM installation", "RefUrl": "/notes/2022318 "}, {"RefNumber": "2022286", "RefComponent": "BC-INS-SWPM", "RefTitle": "System copy, export: java.io.IOException: Cannot run - Invalid program name *or* CreateProcess error=2", "RefUrl": "/notes/2022286 "}, {"RefNumber": "2022285", "RefComponent": "BC-INS-MIG", "RefTitle": "R3load error on import: (BUF) ERROR: rc = -11 CsDecompr", "RefUrl": "/notes/2022285 "}, {"RefNumber": "1988138", "RefComponent": "BC-INS", "RefTitle": "SWPM: <PERSON><PERSON><PERSON><PERSON><PERSON> crashes at step \"Checking National Language Support File Set\"", "RefUrl": "/notes/1988138 "}, {"RefNumber": "1977545", "RefComponent": "BC-INS-MIG", "RefTitle": "Unknown DDIC type \"<nn>\" for field <name> in table <tablename> during export preparation", "RefUrl": "/notes/1977545 "}, {"RefNumber": "1949033", "RefComponent": "BC-INS-UNX", "RefTitle": "SWPM Error: FCO-00011The step checkIfSIDADMCanAccessExportDir ...\"touch <export directory>/testFileForSAPinst\" finished with status TST_ERROR.", "RefUrl": "/notes/1949033 "}, {"RefNumber": "2179550", "RefComponent": "BC-CST-MS", "RefTitle": "Troubleshooting steps for wapsmod.ms.MsAttachFailed error: Connect to message server fails during installation or system copy", "RefUrl": "/notes/2179550 "}, {"RefNumber": "2498029", "RefComponent": "BC-INS-SWPM", "RefTitle": "SOLMAN installation Error: requested package Java Component NW740 SPS12 or NW750 (folder JAVA_J2EE_OSINDEP_UT) using standard installation option with or without STACK XML", "RefUrl": "/notes/2498029 "}, {"RefNumber": "2142461", "RefComponent": "BC-INS", "RefTitle": "How to start analyzing issues in SWPM", "RefUrl": "/notes/2142461 "}, {"RefNumber": "2170093", "RefComponent": "BC-INS-SWPM", "RefTitle": "Set breakpoints in SWPM Tool (sapinst) using Step State Editor", "RefUrl": "/notes/2170093 "}, {"RefNumber": "2358773", "RefComponent": "BC-INS-FWK", "RefTitle": "SWPM Tool (sapinst) broken down: A fatal error has been detected by the SAP Java Virtual Machine", "RefUrl": "/notes/2358773 "}, {"RefNumber": "1952422", "RefComponent": "BC-INS-MIG", "RefTitle": "ERROR: data conversion failed. rc = 2 during new system installation or system copy (export/import)", "RefUrl": "/notes/1952422 "}, {"RefNumber": "2746761", "RefComponent": "BC-INS-SWPM", "RefTitle": "Running multiple SWPM in parallel in the same host", "RefUrl": "/notes/2746761 "}, {"RefNumber": "2156377", "RefComponent": "BC-INS", "RefTitle": "Software Provisioning Manager failed in start/stop instance with: FAIL: cannot open /usr/sap/sapservices [No such file or directory]", "RefUrl": "/notes/2156377 "}, {"RefNumber": "2732487", "RefComponent": "BC-INS-JCI", "RefTitle": "Cannot change the Java database schema in SWPM", "RefUrl": "/notes/2732487 "}, {"RefNumber": "2624643", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "Unexpected token 'ver:' in <shell_export>/DDL<DB>.TPL", "RefUrl": "/notes/2624643 "}, {"RefNumber": "2705594", "RefComponent": "BC-INS-DSS", "RefTitle": "Unable to perform Dual stack Split with Netweaver Process Integration 7.4", "RefUrl": "/notes/2705594 "}, {"RefNumber": "2673908", "RefComponent": "BC-INS-SWPM", "RefTitle": "System call failed. DETAILS: Error 267 (0x0000010b) (The directory name is invalid.)", "RefUrl": "/notes/2673908 "}, {"RefNumber": "2664707", "RefComponent": "BC-INS-SWPM", "RefTitle": "ERROR: SWPM ORA-00904: \"LINKEDCPATH\": invalid identifier", "RefUrl": "/notes/2664707 "}, {"RefNumber": "2657037", "RefComponent": "BC-INS-SWPM", "RefTitle": "Install SAP BW/4HANA 1.0 SR1 with SWPM tool", "RefUrl": "/notes/2657037 "}, {"RefNumber": "2615828", "RefComponent": "BC-INS-SWPM", "RefTitle": "Archive file '(...).SAR' did not match", "RefUrl": "/notes/2615828 "}, {"RefNumber": "2591737", "RefComponent": "BC-INS-SWPM", "RefTitle": "CDS views are not created in a new installed SAP ABAP system", "RefUrl": "/notes/2591737 "}, {"RefNumber": "2581694", "RefComponent": "BC-INS-SWPM", "RefTitle": "PATH is not defined or have an empty value in environment - Error during SWPM execution", "RefUrl": "/notes/2581694 "}, {"RefNumber": "2525313", "RefComponent": "BC-INS-MIG", "RefTitle": "Test logon to SAP System <SID> failed during system copy", "RefUrl": "/notes/2525313 "}, {"RefNumber": "2532012", "RefComponent": "BC-DB-MSS-INS", "RefTitle": "SSL Security error while installing database instance on SQL Server", "RefUrl": "/notes/2532012 "}, {"RefNumber": "2448619", "RefComponent": "BC-INS-MIG", "RefTitle": "Error during system copy or installation: \"Unknown file format, format version specification is mandatory for STR files\"", "RefUrl": "/notes/2448619 "}, {"RefNumber": "2522735", "RefComponent": "HAN-LM-INS-SAP", "RefTitle": "How-To split a MCOD system running on SAP HANA", "RefUrl": "/notes/2522735 "}, {"RefNumber": "2516506", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent installation on Windows Server 2003 fails - SAP Solution Manager 7.1 and 7.2", "RefUrl": "/notes/2516506 "}, {"RefNumber": "2506838", "RefComponent": "BC-INS-DSS", "RefTitle": "Migration export LABEL.ASC file not accepted during Dual-Stack Split", "RefUrl": "/notes/2506838 "}, {"RefNumber": "2499779", "RefComponent": "BC-INS-NT", "RefTitle": "Rebuild MSCS Cluster Node 1 with SWPM tool", "RefUrl": "/notes/2499779 "}, {"RefNumber": "2498974", "RefComponent": "BC-INS-AS4", "RefTitle": "Return value of function getpwuid(147) is A file or directory in the path name does not exist - in SWPM on IBM i", "RefUrl": "/notes/2498974 "}, {"RefNumber": "2483535", "RefComponent": "BC-INS-MIG", "RefTitle": "How to find the \"Installation Media\" for SWPM", "RefUrl": "/notes/2483535 "}, {"RefNumber": "2361152", "RefComponent": "EP-PIN-FPN", "RefTitle": "FPN issues after a System Copy in SAP NetWeaver Portal 7.3x and Higher", "RefUrl": "/notes/2361152 "}, {"RefNumber": "2467797", "RefComponent": "BC-DB-MSS-INS", "RefTitle": "SWPM Error: Cannot connect to DB Host ; SQL Server does not exist or access denied.", "RefUrl": "/notes/2467797 "}, {"RefNumber": "2486182", "RefComponent": "BC-INS-SWPM", "RefTitle": "File <path>vcredist_x64.2013.exe in data unit is not signed during SCM Optimizer Installation", "RefUrl": "/notes/2486182 "}, {"RefNumber": "1890950", "RefComponent": "BC-DB-MSS", "RefTitle": "Generic Installation Options", "RefUrl": "/notes/1890950 "}, {"RefNumber": "2443132", "RefComponent": "BC-INS-SWPM", "RefTitle": "The chosen Software Provisioning Manager does not match the operating system version.", "RefUrl": "/notes/2443132 "}, {"RefNumber": "1988320", "RefComponent": "BC-INS", "RefTitle": "<PERSON><PERSON><PERSON> failed with error:\"FSL-00009  System call failed. Error 0 (Error 0) in execution of system call 'getcwd' ...\"", "RefUrl": "/notes/1988320 "}, {"RefNumber": "1322991", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "ZSCSinst Installationtool for SAP Central services on z/OS", "RefUrl": "/notes/1322991 "}, {"RefNumber": "1748566", "RefComponent": "BC-INS-SWPM", "RefTitle": "Internal Note for Software Provisioning Manager 1.0 Validation (current: SP41) and 2.0 (current: SP17)", "RefUrl": "/notes/1748566 "}, {"RefNumber": "3207660", "RefComponent": "BC-INS-SRN", "RefTitle": "Potential loss of file system content while using SWPM-based Rename and keeping the SAPSID", "RefUrl": "/notes/3207660 "}, {"RefNumber": "3139184", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: systemd integration for sapstartsrv and SAP Host Agent", "RefUrl": "/notes/3139184 "}, {"RefNumber": "3143497", "RefComponent": "BC-OP-NT", "RefTitle": "SAP Systems on Windows Server 2022", "RefUrl": "/notes/3143497 "}, {"RefNumber": "3052368", "RefComponent": "IS-CC", "RefTitle": "Technical Implementation of SAP CC 2020 FPS 2", "RefUrl": "/notes/3052368 "}, {"RefNumber": "3035232", "RefComponent": "MDM-FN-INS", "RefTitle": "MDM supporting kernel 7.22", "RefUrl": "/notes/3035232 "}, {"RefNumber": "3021835", "RefComponent": "IS-CC", "RefTitle": "Technical Implementation of SAP CC 2020 FPS 1", "RefUrl": "/notes/3021835 "}, {"RefNumber": "2980160", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for SWPM10RMSP30_<Version>.SAR of Software Provisioning Manager 1.0 - Covers Options for no longer supported SAP Products in Software Provisioning Manager 1.0 and Access to Related Guides", "RefUrl": "/notes/2980160 "}, {"RefNumber": "2855174", "RefComponent": "IS-CC", "RefTitle": "SAP CC 5.0 Support Package 6", "RefUrl": "/notes/2855174 "}, {"RefNumber": "2867947", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB:  NULL characters appearing in LCHAR fields after export", "RefUrl": "/notes/2867947 "}, {"RefNumber": "2879036", "RefComponent": "IS-CC", "RefTitle": "Technical Implementation of SAP CC 5.0 SP 6", "RefUrl": "/notes/2879036 "}, {"RefNumber": "2801877", "RefComponent": "IS-CC", "RefTitle": "Technical Implementation of SAP CC 5.0 SP 5", "RefUrl": "/notes/2801877 "}, {"RefNumber": "2772452", "RefComponent": "IS-CC", "RefTitle": "SAP CC 5.0 Support Package 5", "RefUrl": "/notes/2772452 "}, {"RefNumber": "2695381", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS: Release of Linux on IBM Power Systems Little Endian as an application server platform", "RefUrl": "/notes/2695381 "}, {"RefNumber": "2365849", "RefComponent": "HAN-LM-INS-SAP", "RefTitle": "Installation of SAP Systems Based on SAP NetWeaver: SAP HANA Database", "RefUrl": "/notes/2365849 "}, {"RefNumber": "2750628", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP Landscape Management System Copy scenario fails with SWPM error", "RefUrl": "/notes/2750628 "}, {"RefNumber": "1707361", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.31 and Higher: UNIX Db2 for LUW", "RefUrl": "/notes/1707361 "}, {"RefNumber": "1707362", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.31 and Higher: Windows Db2 LUW", "RefUrl": "/notes/1707362 "}, {"RefNumber": "1724554", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.0 incl. EHPs: UNIX Db2 for LUW", "RefUrl": "/notes/1724554 "}, {"RefNumber": "1724557", "RefComponent": "BC-DB-DB6-INS", "RefTitle": "Inst. Systems Based on NW 7.0 incl. EHPs: Windows Db2 LUW", "RefUrl": "/notes/1724557 "}, {"RefNumber": "2367941", "RefComponent": "MDM-FN-INS", "RefTitle": "MDM Servers Installation Moved to SWPM", "RefUrl": "/notes/2367941 "}, {"RefNumber": "1751271", "RefComponent": "BC-INS-AS4", "RefTitle": "Inst. SAP Sys. Based on NW 7.3 and Higher: SAP HANA DB, IBM i", "RefUrl": "/notes/1751271 "}, {"RefNumber": "2590779", "RefComponent": "BC-DB-LCA", "RefTitle": "Uninstallation of SAP liveCache/LCAPPS", "RefUrl": "/notes/2590779 "}, {"RefNumber": "2576786", "RefComponent": "BC-INS-JCI", "RefTitle": "Installation of S4H 1610 Java stack fails at UserCheck", "RefUrl": "/notes/2576786 "}, {"RefNumber": "2576676", "RefComponent": "BC-INS-JCI", "RefTitle": "Error while accessing secure store: SecStore.properties is invalid: software version 6.30.000.001 is incompatible with file version 7.00.000.001", "RefUrl": "/notes/2576676 "}, {"RefNumber": "2562635", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Regenerate DDL Sources Failed", "RefUrl": "/notes/2562635 "}, {"RefNumber": "1554717", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Planning Information for SAP on SAP ASE", "RefUrl": "/notes/1554717 "}, {"RefNumber": "2195019", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Using SAP kernel 748 and higher or SAPinst 749 and higher on RHEL 6, OL6 and SLES 11", "RefUrl": "/notes/2195019 "}, {"RefNumber": "1496410", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 6.x: Installation and Upgrade", "RefUrl": "/notes/1496410 "}, {"RefNumber": "2384179", "RefComponent": "BC-OP-NT", "RefTitle": "SAP Systems on Windows Server 2016", "RefUrl": "/notes/2384179 "}, {"RefNumber": "2393060", "RefComponent": "BC-INS-FWK", "RefTitle": "SAPinst Framework 749 Central Note", "RefUrl": "/notes/2393060 "}, {"RefNumber": "2422263", "RefComponent": "BC-INS", "RefTitle": "Updating SAP Identity Management 8.0 SP04 and higher using Software Provisioning Manager", "RefUrl": "/notes/2422263 "}, {"RefNumber": "2372790", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "R3load error during SWPM export: \"SQL file ... did not occur in 'SQLFiles.LST'", "RefUrl": "/notes/2372790 "}, {"RefNumber": "455195", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "R3load: Using TSK files", "RefUrl": "/notes/455195 "}, {"RefNumber": "2259748", "RefComponent": "BC-INS-JCI", "RefTitle": "Fix wrong instanceID using Software Provisioning Manager", "RefUrl": "/notes/2259748 "}, {"RefNumber": "2296259", "RefComponent": "BC-INS", "RefTitle": "Installing SAP Identity Management 8.0 SP04 and higher using Software Provisioning Manager", "RefUrl": "/notes/2296259 "}, {"RefNumber": "2277574", "RefComponent": "BC-INS-UDI", "RefTitle": "Central Note for Up-To-Date Installation using Maintenance Planner, Software Provisioning Manager and succeeding update tools", "RefUrl": "/notes/2277574 "}, {"RefNumber": "1704753", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst.Systems Based on NetWeaver on UNIX  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1704753 "}, {"RefNumber": "2230669", "RefComponent": "BC-INS-SWPM", "RefTitle": "System Provisioning Using an Input Parameter File", "RefUrl": "/notes/2230669 "}, {"RefNumber": "800791", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: In-place Code Page Conversion ASCII to Unicode", "RefUrl": "/notes/800791 "}, {"RefNumber": "2201060", "RefComponent": "BC-DB-MSS", "RefTitle": "Setting up Microsoft SQL Server 2016", "RefUrl": "/notes/2201060 "}, {"RefNumber": "2172935", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Installation - SAP Systems based on SAP NetWeaver : Oracle Database", "RefUrl": "/notes/2172935 "}, {"RefNumber": "2076081", "RefComponent": "BC-INS-JCI", "RefTitle": "Jload: special  usages", "RefUrl": "/notes/2076081 "}, {"RefNumber": "962955", "RefComponent": "BC-CST-NI", "RefTitle": "Use of virtual or logical TCP/IP host names", "RefUrl": "/notes/962955 "}, {"RefNumber": "2043509", "RefComponent": "HAN-LM-INS", "RefTitle": "SAP HANA and SAP NetWeaver Java on a Single Host", "RefUrl": "/notes/2043509 "}, {"RefNumber": "1476239", "RefComponent": "BC-INS-NT", "RefTitle": "SAP system migration to Windows 2008/2008 R2", "RefUrl": "/notes/1476239 "}, {"RefNumber": "1991638", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Supplementary SAP Note to SAP Note 1990894", "RefUrl": "/notes/1991638 "}, {"RefNumber": "1995569", "RefComponent": "BC-DB-MSS", "RefTitle": "MSS: Unknown attribute SAPNotes error", "RefUrl": "/notes/1995569 "}, {"RefNumber": "1966701", "RefComponent": "BC-DB-MSS", "RefTitle": "Setting up Microsoft SQL Server 2014", "RefUrl": "/notes/1966701 "}, {"RefNumber": "1895271", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Internal Note - SMDA installation with upcoming SWPM SPs", "RefUrl": "/notes/1895271 "}, {"RefNumber": "1858920", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent installation with Software Provisioning Manager", "RefUrl": "/notes/1858920 "}, {"RefNumber": "1589311", "RefComponent": "BC-EHP-INS", "RefTitle": "Replacements of outdated tools via SL Toolset", "RefUrl": "/notes/1589311 "}, {"RefNumber": "1877731", "RefComponent": "BC-INS", "RefTitle": "Installed Software Information (ISI)", "RefUrl": "/notes/1877731 "}, {"RefNumber": "1738258", "RefComponent": "BC-INS-MIG", "RefTitle": "System Copy for Systems Based on SAP NetWeaver  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1738258 "}, {"RefNumber": "1806816", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Media list for installations based on NW 7.3 incl. EHPs", "RefUrl": "/notes/1806816 "}, {"RefNumber": "1806601", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Media list for installations based on NW 7.0 incl. EHPs", "RefUrl": "/notes/1806601 "}, {"RefNumber": "1153713", "RefComponent": "BC-CCM-MC", "RefTitle": "Problems with SAP Management Console (Java)", "RefUrl": "/notes/1153713 "}, {"RefNumber": "1751798", "RefComponent": "BC-INS", "RefTitle": "Do not unpack kernel archives using Software Prov. Man. 1.0", "RefUrl": "/notes/1751798 "}, {"RefNumber": "1789561", "RefComponent": "BC-DWB-DIC", "RefTitle": "Cluster table is converted unexpectedly", "RefUrl": "/notes/1789561 "}, {"RefNumber": "1689942", "RefComponent": "BC-INS", "RefTitle": "vendor JDK required for sapinst with install./system copy", "RefUrl": "/notes/1689942 "}, {"RefNumber": "1802965", "RefComponent": "HAN-DB", "RefTitle": "R3load on HANA: SAPVIEW.cmd (TSK) Error: object ... not in", "RefUrl": "/notes/1802965 "}, {"RefNumber": "1783927", "RefComponent": "BC-INS-MIG", "RefTitle": "Prerequisites for table splitting with target SAP HANA database", "RefUrl": "/notes/1783927 "}, {"RefNumber": "1715048", "RefComponent": "HAN-LM-INS", "RefTitle": "BW 7.30 new features for installation or migration", "RefUrl": "/notes/1715048 "}, {"RefNumber": "1617021", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP NetWeaver BW 7.3: Windows / SAP HANA Database", "RefUrl": "/notes/1617021 "}, {"RefNumber": "1617020", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst. SAP NetWeaver BW 7.3: UNIX / SAP HANA Database", "RefUrl": "/notes/1617020 "}, {"RefNumber": "1611802", "RefComponent": "BC-INS-NT", "RefTitle": "SAP NetWeaver 7.3 EHP1 Installation on Windows: SAP MaxDB", "RefUrl": "/notes/1611802 "}, {"RefNumber": "1611801", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP NetWeaver 7.3 EHP1 Installation on UNIX: SAP MaxDB", "RefUrl": "/notes/1611801 "}, {"RefNumber": "1683455", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP NetWeaver BW 7.31: Windows / SAP HANA Database", "RefUrl": "/notes/1683455 "}, {"RefNumber": "1683454", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst. SAP NetWeaver BW 7.31: UNIX / SAP HANA Database", "RefUrl": "/notes/1683454 "}, {"RefNumber": "1434181", "RefComponent": "BC-INS-NT", "RefTitle": "Inst.Systems Based on NW 7.3: Windows / SAP MaxDB", "RefUrl": "/notes/1434181 "}, {"RefNumber": "1434180", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst.Systems Based on NW 7.3: UNIX / SAP MaxDB", "RefUrl": "/notes/1434180 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SOFTWARE_PROVISIONING_MANAGER", "From": "1.0", "To": "1.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP000", "SupportPackagePatch": "000011", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP000&patch_level=000011"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP004", "SupportPackagePatch": "000006", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP004&patch_level=000006"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP005", "SupportPackagePatch": "000004", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP005&patch_level=000004"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP009", "SupportPackagePatch": "000009", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP009&patch_level=000009"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP007", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP007&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP008", "SupportPackagePatch": "000005", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP008&patch_level=000005"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP006", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP006&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP002", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP002&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP010", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP010&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP017", "SupportPackagePatch": "000006", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP017&patch_level=000006"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP018", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP018&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP019", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP019&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP020", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP020&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP021", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP021&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP023", "SupportPackagePatch": "000009", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP023&patch_level=000009"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP022", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP022&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP024", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP024&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP026", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP026&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP027", "SupportPackagePatch": "000008", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP027&patch_level=000008"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP028", "SupportPackagePatch": "000008", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP028&patch_level=000008"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP029", "SupportPackagePatch": "000006", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP029&patch_level=000006"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP030", "SupportPackagePatch": "000006", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP030&patch_level=000006"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP025", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP025&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP031", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP031&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP032", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP032&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP033", "SupportPackagePatch": "000006", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP033&patch_level=000006"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP034", "SupportPackagePatch": "000006", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP034&patch_level=000006"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP035", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP035&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP036", "SupportPackagePatch": "000004", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP036&patch_level=000004"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP037", "SupportPackagePatch": "000006", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP037&patch_level=000006"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP038", "SupportPackagePatch": "000005", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP038&patch_level=000005"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP039", "SupportPackagePatch": "000005", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP039&patch_level=000005"}, {"SoftwareComponentVersion": "SOFTWARE PROVISIONING MGR 1.0", "SupportPackage": "SP040", "SupportPackagePatch": "000001", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018544&support_package=SP040&patch_level=000001"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}