{"Request": {"Number": "2346431", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 412, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018371532017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002346431?language=E&token=43A1A95AF83443471ED9ABF35B127B77"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002346431", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002346431/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2346431"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 107}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.12.2021"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2346431 - SAP S/4HANA 1610: Release Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to notes for applying Feature Package (FP)/Support Package (SP) Stacks of product version 'SAP S/4HANA 1610'.</p>\r\n<p><strong><strong>Note</strong>:</strong> This SAP Note is subject to change. Check this note for changes on a regular basis. All important changes made after release of a&#160;Feature Package (FP)/Support Package (SP) Stack are documented in section \"Changes made after Release of FP/SP Stack &lt;xx&gt;\".</p>\r\n<p><br /><strong>GENERAL INFORMATION</strong></p>\r\n<p>SAP S/4HANA 1610 will be in Maintenance until 31.12.2021. For further information about the&#160;maintenance strategy please refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/52505\">52505</a>&#160;and SAP note&#160;<a target=\"_blank\" href=\"/notes/2311392\">2900388</a>.<a target=\"_blank\" href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?view=bsp&amp;param=69765F6D6F64653D3030312669765F7361706E6F7465735F6B65793D30313130303033353837303030303932373037303230303126766965773D627370\"><br /></a></p>\r\n<p>We strongly recommend to upgrade your system to the latest&#160;SAP S/4HANA on-premise release.</p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>\r\n<p>Please refer to the specific documentation under section&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1909/en-US\">product documentation</a>. We recommend setting up your system to display a dynamic documentation, which reflects the latest Feature or Support Package Stack of this release. If you want to stay on Feature Package Stack 00 to 02 for a longer period, and if you also want to display the corresponding documentation for one of these Feature Package Stacks, you will find all necessary information in SAP note&#160;<a target=\"_blank\" href=\"/notes/2904428\">2904428</a>.</p>\r\n</li>\r\n</ul>\r\n<ul>\r\n<li>The supported Kernel versions are:&#160;SAP KERNEL 7.49 64-BIT UNICODE and SAP KERNEL 7.53 64-BIT UNICODE.</li>\r\n<li>For general restrictions as well as&#160;restrictions for the conversion to SAP&#160;S/4HANA 1610, please refer to SAP note <a target=\"_blank\" href=\"/notes/2333141\">2333141</a>.</li>\r\n<li>For release information and restrictions related to country-specific localization features, please refer to SAP note <a target=\"_blank\" href=\"/notes/2349004\">2349004</a>.</li>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version \"SAP FIORI FOR SAP S/4HANA 1610\".</li>\r\n<li>If you have add-ons installed on your system and/or want to use them on product version SAP S/4HANA 1610, please refer to SAP note <a target=\"_blank\" href=\"/notes/2214409\">2214409</a>.</li>\r\n<li>For process&#160;integration capabilities with other SAP on-premise solutions, please refer to SAP note <a target=\"_blank\" href=\"/notes/2376061\">2376061</a>.</li>\r\n<li>\r\n<p>Please be aware that&#160;SAP S/4HANA&#160;1610 is an Unicode-only release. Non-Unicode systems are not supported anymore.&#160;Hence upgrades of non-Unicode systems without prior Unicode conversion is not possible. For details see&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/012002523100009958832014E/\">Upgrade of non Unicode systems</a>&#160;or <a target=\"_blank\" href=\"https://service.sap.com/Unicode\">service.sap.com/Unicode </a>and SAP note <a target=\"_blank\" href=\"/notes/2033243\">2033243</a>.</p>\r\n</li>\r\n<li>A&#160;new inventory data model will be delivered. This new inventory data model has a lot of advantages at S4. With the S4 new inventory data model delta stock quantities instead of total stock quantities are transferred to APO. At APO code changes need to be implemented to get this delta stock quantities processed. SAP Note 2816388 contains a list of all SAP Notes to be implemented at APO to realize these code changes.</li>\r\n<li><strong>United Kingdom leaving the EU</strong>: For information on how a &#8220;hard Brexit&#8221; (= a no-deal scenario) would impact your <em>SAP S/4HANA </em>system, please see SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2749671\">2749671</a>.</strong></li>\r\n<li>For the implementation of ELSTER modules using the ERiC libraries and the corresponding impact on platform support, please see notes&#160;<a target=\"_blank\" href=\"/notes/2745249\">2745249</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2558316\">2558316</a>.</li>\r\n<li>\r\n<p>For more information regarding LEGAL VAT TAX CHANGE in Germany<strong> , please see SAP note </strong><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2934992\">2934992</a></span></p>\r\n</li>\r\n</ul>\r\n<p><strong><strong>Installation Information</strong></strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20000/en-US/INST_OP1610.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM) Support Package&#160;20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n</ul>\r\n<p><strong><strong><strong><strong>Upgrade&#160;Information</strong></strong></strong></strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20000/en-US/UPGR_OP1610.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) Support Package&#160;20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n</ul>\r\n<p><strong>SAP HANA database requirements</strong></p>\r\n<ul>\r\n<li>The minimum required revision is defined in the respective Support or Feature Package Stack chapter.</li>\r\n<li>Detailed information about SAP HANA 2.0 Revision and Maintenance Strategy can be found in SAP note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>.</li>\r\n<li>If you plan to upgrade your SAP HANA database to a newer revision or a newer available SPS level,</li>\r\n<ul>\r\n<li>Refer to SAP note <a target=\"_blank\" href=\"/notes/1906576\">1906576</a> for SAP HANA client and server cross-version compatibility and</li>\r\n<li>Refer to SAP note <a target=\"_blank\" href=\"/notes/2655761\">2655761</a> for restrictions and recommendations regarding specific&#160;revisions of SAP HANA database for use in SAP S/4HANA.</li>\r\n</ul>\r\n</ul>\r\n<p><strong><strong>Conversion&#160;Information</strong></strong></p>\r\n<ul>\r\n<li>\r\n<p><em>Please note that a system conversion to SAP S/4HANA 1511 and 1610 is no longer possible or supported</em><em></em></p>\r\n<ul>\r\n<li><em>for newly starting </em><em>system conversion </em><em>projects &#8211; independently of the Enhancement Package or SP level of the </em><em>SAP ERP source system.</em></li>\r\n<li>for any system which is below the following SAP ERP SP levels and have to go to S/4HANA 1511 and 1610, open a message on CA-TRS-PRCK</li>\r\n<li><em>for any system which is on the following SAP ERP SP levels or higher: 600 SP30, 602 SP20, 603 SP19, 604 SP20, 605 SP17, 606 SP20, 616 SP12, 617 SP15, 618 SP09</em></li>\r\n</ul>\r\n<p><em>You can convert to the successor product versions of SAP S/4HANA.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Feature Package Update (via Software Update Manager (SUM))</strong></p>\r\n<ul>\r\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\r\n</ul>\r\n<p><strong><strong>Support Package Stacks on SAP Support Portal</strong></strong></p>\r\n<p>You will find general information about Support Package Stacks&#160;on SAP Support Portal at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/patches/stacks.html\">support.sap.com/software/patches/stacks.html</a>. The Schedule for Support Package Stacks&#160;is available at <a target=\"_blank\" href=\"http://support.sap.com/maintenance-schedule\">support.sap.com/maintenance-schedule</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4HANA 1610</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to get additional information about product version SAP S/4HANA 1610.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;11 (10/2021)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a>&#160;which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note&#160;<a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339&#160;</a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a>&#160;also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note&#160;<a target=\"_blank\" href=\"/notes/2527538\">2527538</a>&#160;(AMDP Procedure Execution Fails With Column Store Error) and SAP note&#160;<a target=\"_blank\" href=\"/notes/2527648\">2527648</a>&#160;(Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2348661\">2348661</a>&#160;- Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note&#160;<a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.&#160;<br />Please be aware that your software level is not higher than Support Package Stack 11.</li>\r\n<li>For the system upgrade, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package&#160;11:</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;10 (04/2021)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a>&#160;which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note&#160;<a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339&#160;</a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a>&#160;also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note&#160;<a target=\"_blank\" href=\"/notes/2527538\">2527538</a>&#160;(AMDP Procedure Execution Fails With Column Store Error) and SAP note&#160;<a target=\"_blank\" href=\"/notes/2527648\">2527648</a>&#160;(Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2348661\">2348661</a>&#160;- Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note&#160;<a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.&#160;<br />Please be aware that your software level is not higher than Support Package Stack 11.</li>\r\n<li>For the system upgrade, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package&#160;10:</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;09 (10/2020)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a>&#160;which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note&#160;<a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339&#160;</a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a>&#160;also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note&#160;<a target=\"_blank\" href=\"/notes/2527538\">2527538</a>&#160;(AMDP Procedure Execution Fails With Column Store Error) and SAP note&#160;<a target=\"_blank\" href=\"/notes/2527648\">2527648</a>&#160;(Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2348661\">2348661</a>&#160;- Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note&#160;<a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.&#160;<br />Please be aware that your software level is not higher than Support Package Stack 11.</li>\r\n<li>For the system upgrade, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package&#160;09:</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;08 (04/2020)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a>&#160;which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note&#160;<a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339&#160;</a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a>&#160;also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note&#160;<a target=\"_blank\" href=\"/notes/2527538\">2527538</a>&#160;(AMDP Procedure Execution Fails With Column Store Error) and SAP note&#160;<a target=\"_blank\" href=\"/notes/2527648\">2527648</a>&#160;(Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2348661\">2348661</a>&#160;- Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note&#160;<a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.&#160;<br />Please be aware that your software level is not higher than Support Package Stack 10.</li>\r\n<li>For the system upgrade, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package&#160;08:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"154\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"147\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2880761\">2880761</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBT</p>\r\n</td>\r\n<td valign=\"top\" width=\"154\">\r\n<p>ST-A/PI</p>\r\n</td>\r\n<td valign=\"top\" width=\"147\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>2020-04-08</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2880801\">2880801</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBP</p>\r\n</td>\r\n<td valign=\"top\" width=\"154\">\r\n<p>ST-A/PI</p>\r\n</td>\r\n<td valign=\"top\" width=\"147\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>2020-04-08</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2886195\">2886195</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">Syntax error \"VBHDR_G does not exist\" in program /SDF/SAPLEWA</td>\r\n<td valign=\"top\" width=\"154\">ST-A/PI</td>\r\n<td valign=\"top\" width=\"147\">No</td>\r\n<td valign=\"top\" width=\"142\">2020-04-08</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong><strong>Important changes made after release of&#160;<strong>Support Package Stack 08</strong></strong></strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;07 (10/2019)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.23 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a>&#160;which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note&#160;<a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339&#160;</a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a>&#160;also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note&#160;<a target=\"_blank\" href=\"/notes/2527538\">2527538</a>&#160;(AMDP Procedure Execution Fails With Column Store Error) and SAP note&#160;<a target=\"_blank\" href=\"/notes/2527648\">2527648</a>&#160;(Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note&#160;<a target=\"_blank\" href=\"/notes/2348661\">2348661</a>&#160;- Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note&#160;<a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.&#160;<br />Please be aware that your software level is not higher than Support Package Stack 09.</li>\r\n<li>For the system upgrade, please refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong><strong>Important changes made after release of&#160;<strong>Support Package Stack 07</strong></strong></strong></p>\r\n<p><strong>2020-01-31:&#160;</strong><strong>United Kingdom leaving the EU</strong>:</p>\r\n<p>&#8226; For information about the United Kingdom leaving the EU with the Withdrawal Bill and the transition period, please see SAP note&#160;<a target=\"_blank\" href=\"/notes/2885225\">2885225</a>.</p>\r\n<p>&#8226; For information about how a &#8220;hard Brexit&#8221; (= a no-deal scenario) would impact your SAP S/4HANA system, please see SAP note&#160;<a target=\"_blank\" href=\"/notes/2749671\">2749671</a>.</p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;06 (04/2019)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section <a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.21. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SP01 Revision 12.04 or SPS02 Revision 23. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a target=\"_blank\" href=\"/notes/2348661\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note <a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br />Please be aware that your software level is not higher than Support Package Stack 08.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package Stack&#160;06</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;05 (11/2018)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section <a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a target=\"_blank\" href=\"/notes/2348661\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note <a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br />Please be aware that your software level is not higher than Support Package Stack 07.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package Stack&#160;05</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2694011\">2694011</a></span></td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">WTY: Dump \"CALL_FUNCTION_CONFLICT_LENG\" occurs on account document posting</span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">S4CORE</span></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>&#160;No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;22.11.2018</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2697405\">2697405</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">TrexViaDbsl: wrong schema name is set to temporary objects in TREX_EXT_AGGREGATE</span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">SAP_BASIS</span></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>&#160;Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;</strong>22.11.2018</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2722552\">2722552</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Runtime error SYSTEM_DATA_ALREADY_FREE during update of classifications</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_ABA</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-11-28</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2724147\">2724147</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Termination in the update of the classification</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_ABA</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-11-28</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2726975\">2726975</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Support of CLSD in SNOTE: Ignore all Changed by and Changed on data in the CI</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-12-11</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Important Changes made after Release of Feature Package Stack&#160;05</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/273869\">2738698</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>&#160;No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2019-01-11</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2324448\">2324448</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>S4 Release 1610: New inventory data model -&gt; Adjustments at APO to use the new locking free stock upate at liveCache</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SCMAPO</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>&#160;No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2019-01-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2767385\">2767385</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 874px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Melted variables showing initial values by mistake</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</td>\r\n<td valign=\"top\" width=\"126\">SAP_BW</td>\r\n<td valign=\"top\" width=\"175\">No</td>\r\n<td valign=\"top\" width=\"142\">2019-07-12</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;04 (05/2018)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section <a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a target=\"_blank\" href=\"/notes/2348661\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note <a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br />Please be aware that your software level is not higher than Support Package Stack 06.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM (instead of SUM)&#160;for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\r\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type &#8220;LOAD_PROGRAM_INTF_MISMATCH&#8221; is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package Stack&#160;04</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 874px;\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2618103\">2618103</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">ALV layout: Layouts cannot be saved from the 'Change Layout' dialog after an error message</span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS 7.51</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-05-11</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2624170\">2624170</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Conversion routine for DATUM data element in BC Set</span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS 7.51</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;</strong>2018-05-11</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2441447\">2441447</a></span></span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">&#160;<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">Authorization check enablement in Business Partner F4 search help</span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS 7.51</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2018-07-10</span></span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2658952\">2658952</a></span></span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">ESH - initial authorization index filling - error: \"Feature not supported\"/OLAP VIEW on SAP HANA 3.1</span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS 7.51</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;2018-07-10</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2660883\">2660883</a></span></span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">BP_EOP: Success Message is not displayed properly</span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS 7.51</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;2018-07-10</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2652897\">2652897</a></span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">&#160;<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;S<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">AP_UI</span></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160; 2018-07-23</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"/notes/2655756\">2655756</a></span></span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">&#160;<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">Tree UIBB: Conditional Formatting when master column has display type image</span></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;2018-07-23</span></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Important Changes made after Release of Feature Package Stack&#160;04</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/273869\">2738698</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>&#160;No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;11.01.2019</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;03 (10/2017)</strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section <a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack&#160;03 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2539548\">2539548</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a target=\"_blank\" href=\"/notes/2348661\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 21&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note <a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br />Please be aware that your software level is not higher than Support Package Stack 05.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>If you are using SPAM for the SP update (instead of SUM), please apply note <a target=\"_blank\" href=\"/notes/2474810\">2474810</a> before you start the SP update to SAP S/4HANA 1610 Support Package Stack 03 to avoid errors during &#160;DDIC_ACTIVATION of type &#8220;Key fields are not together at the beginning of the view\".</li>\r\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type &#8220;specify reference table AND reference field&#8221;, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\r\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\r\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type &#160;&#8220;The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use &#8220;Ignore generation errors&#8221;-option in SPAM and restart the SP update.</li>\r\n<li>If switch GLO_REP_EAPS_SFWS_03 is activated, you might encounter errors during AFTER_IMPORT_METHOD SCPR_SCP2_AFTER_IMPORT for the object R3TR SCP2&#160;GLO_REP_SK_03_IDREPFW_SELPA_C. In this case, please repeat the phase up to 2 times. In case repeating the phase twice does not solve the issue, please contact the SAP Support.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Support Package Stack&#160;03</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 874px;\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235373030323926\">2570029</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Connector creation: Dump due to memory overflow</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS 7.51</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-02-02</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2566812\">2566812</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Application area TM/Transportation Management</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS 7.51</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;</strong>2018-02-02</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2537567\">2537567</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>EHP8_SP08: Issue with workforce viewer application</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>EA-HRRXX 608<br />(valid to SP46)</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;</strong>2017-10-24</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2352024\">2352024</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Time dependant promotion data has not been deleted when transferring the data in parallel mode</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE <br />(SCM-FRE-ERP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-10-24</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2340156\">2340156</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>WRF_DISP_CON does not delete TD procurement data from F&amp;R</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE <br />(SCM-FRE-ERP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-10-24</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2652897\">2652897</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160; 2018-07-23</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2655756\">2655756</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;Tree UIBB: Conditional Formatting when master column has display type image</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-07-23</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Important Changes made after Release of Feature Package Stack&#160;03</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/273869\">2738698</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>&#160;No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;11.01.2019</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">FEATURE PACKAGE STACK&#160;02 (05/2017)</span></strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section <a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\">product documentation</a>.</li>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>\r\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\r\n</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Feature Package Stack&#160;02 of SAP S/4HANA 1610 in the backend requires Support Package Stack&#160;01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend&#160;(and vice versa).</li>\r\n<li>\r\n<p>Please refer to note <a target=\"_blank\" href=\"/notes/2400710\">2400710</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 01 (02/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</p>\r\n</li>\r\n<li>Visual theme &#8220;Belize&#8221; for SAP GUI for Windows&#8221;:<br />For the usage of SAP S/4HANA 1610 Feature Package Stack 02 with SAP GUI for Windows there are few prerequisites:</li>\r\n<ul>\r\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\r\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\r\n</ul>\r\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a target=\"_blank\" href=\"/notes/2348661\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\r\n<li>In case you are using roles from <strong>Retail</strong> in FIORI Launchpad and plan to upload the backend app descriptors for area S4RFM please check the instructions of note <a target=\"_blank\" href=\"/notes/2550359\">2550359</a>.</li>\r\n</ul>\r\n<p>Installation Requirements</p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 20&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note <a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br />Please be aware that your software level is not higher than Support Package Stack 04.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Feature Package Stack&#160;02</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 874px;\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td width=\"349\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2372221\">2372221</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Performance improvement on GT_ADDR_XPCPT</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p><strong>&#160;</strong>2017-05-09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2436731\">2436731</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Job scheduling failed due to invalid date</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-05-09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2445210\">2445210</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-05-09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2461676\">2461676</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-05-09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2467650\">2467650</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Authorization issues in viewing Document Info Record</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-05-09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2463740\">2463740</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-05-19</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2477735\">2477735</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Annotation API: enable cache usage for get_annos_mass</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-07-10</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2450514\">2450514</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2489305\">2489305</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Dump while inserting international address version for a person</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2485784\">2485784</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>ALV export: Cannot save to clipboard in browser</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2490652\">2490652</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Legacy DAC maps business key to initial BOPF key</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2491892\">2491892</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-07-27</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2500159\">2500159</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-08-28</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2520306\">2520306</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Condition table index not activated after upgrade</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-09-06</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2527709\">2527709</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-09-06</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2485570\">2485570</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2017-09-07</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2652897\">2652897</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160; 2018-07-23</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2655756\">2655756</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;Tree UIBB: Conditional Formatting when master column has display type image</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-07-23</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Important Changes made after Release of Feature Package Stack&#160;02</strong></p>\r\n<p><strong><strong><strong><strong><strong>2017-10-19: </strong></strong></strong></strong></strong>Inserted:In case you are using roles from <strong>Retail</strong> in FIORI Launchpad and plan to upload the backend app descriptors for area S4RFM please check the instructions of note <a target=\"_blank\" href=\"/notes/2550359\">2550359</a>.<br /><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<strong><strong><strong><strong><strong><strong><br /></strong></strong>2017-08-30</strong>: </strong></strong></strong>SAP HANA 1.0&#160;minimum required revision&#160;changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool. <strong><strong><br /></strong></strong><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.<strong> <br /></strong><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">FEATURE PACKAGE STACK&#160;01 (02/2017)</span></strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section <a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20001/en-US\">product documentation</a>.</li>\r\n<li>Feature Package Stack 01 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\r\n<ul>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA 1.0 Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note<a target=\"_blank\" href=\"/notes/2429281\"> 2429281</a> (S4H:SUM:XPRAS_AIMMRG:HANA deadlock dumps) when you upgrade your system with Software Update Manager (SUM) or apply Feature Package Stack 01 and your system is on HANA 2.0.</li>\r\n</ul>\r\n<ul>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>\r\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\r\n</li>\r\n</ul>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610.<br />Support Package Stack&#160;01 of SAP Fiori 2.0 for SAP S/4HANA on the frontend requires Feature Package Stack&#160;01 of SAP S/4HANA 1610 in the backend (and vice versa).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2400710\">2400710</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 01 (02/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\r\n<li>Please ensure to apply note <a target=\"_blank\" href=\"/notes/2344014\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\r\n<li>For systems with an active BW client, the implementation of Feature Package Stack 01 could abort due to issues with activation of queries REP_LUECSL and REP_BE_STRPWHLDGTAXITEM. In this case, please proceed as described in note <a target=\"_blank\" href=\"/notes/2429774\">2429774</a>&#160;and note <a target=\"_blank\" href=\"/notes/2436577\">2436577</a>.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20001/en-US/INST_OP1610_FPS01.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM)&#160;Support Package 20&#160;for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a target=\"_blank\" href=\"/notes/2408419\">2408419</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note <a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br />Please be aware that your software level is not higher than Support Package Stack 03.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20001/en-US/UPGR_OP1610_FPS01.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n</ul>\r\n<p><strong>Conversion&#160;Requirements</strong></p>\r\n<ul>\r\n<li>For the system conversion, please refer to the <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfe68bfa55e988410ee10000000a441470/1610%20001/en-US/CONV_OP1610_FPS01.pdf\">conversion guide&#160;</a>and SAP notes&#160;<a target=\"_blank\" href=\"/notes/2389794\">2389794</a>, <a target=\"_blank\" href=\"/notes/2389807\">2389807</a> and&#160;<a target=\"_blank\" href=\"/notes/2377310\"><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\" style=\"width: 79.06%;\">2377310</span> </a>(conversion-related information).</li>\r\n<li>You need at least Software Update Manager (SUM) Support Package&#160;20 for the conversion. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>For&#160;system conversions&#160;from an existing source release, please be aware that your software level is&#160;not higher than</li>\r\n<ul>\r\n<li>SAP ERP 6.0 SP Stack 28</li>\r\n<li>SAP enhancement package 2 for SAP ERP 6.0 SP Stack 18</li>\r\n<li>SAP enhancement package 3 for SAP ERP 6.0 SP Stack 17</li>\r\n<li>SAP enhancement package 4 for SAP ERP 6.0 SP Stack 18</li>\r\n<li>SAP enhancement package 5 for SAP ERP 6.0 SP Stack 15</li>\r\n<li>SAP enhancement package 6 for SAP ERP 6.0 SP Stack 18</li>\r\n<li>SAP enhancement package 6 for SAP ERP 6.0, version for SAP HANA SP Stack 10</li>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 13</li>\r\n<li>SAP enhancement package 8 for SAP ERP 6.0 SP Stack 05</li>\r\n<li>SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP Stack 10</li>\r\n<li>SAP Simple Finance, on-premise edition 1503 SP Stack 06</li>\r\n<li>SAP S/4HANA Finance 1605 SP Stack 05</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\r\n<ul>\r\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\r\n<li>Please apply the correction in note <a target=\"_blank\" href=\"/notes/2423846\">2423846</a> on your SAP S/4HANA 1610 Feature Package Stack 00 system before starting a Support Package update to Feature Package Stack 01.</li>\r\n<li>\r\n<p>In case you import SAP NetWeaver 7.51 Support Package Stack 01 and SAP S/4HANA 1610 Support Package Stack 01 including generation in SPAM, the Support Package Stack&#160;import stops with an RC 8 in the ABAP generation phase of the SAP_BASIS 751 Support Package 01, showing generation issues due to syntax errors for classes CL_IVE_INVOICEERPCRTRQ1_MAP and CL_IVE_INVOICEERPCRTRQ1_VAL (Type &#8216;CL_SAPPLCO_ACTION_CODE1&#8217; is unknown). This import issue can be overcome by restarting the import in SPAM.</p>\r\n</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;Feature Package Stack&#160;01</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 1093px;\">\r\n<tbody>\r\n<tr>\r\n<td width=\"102\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2399423\">2399423</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Dump in characteristic value assignment</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_ABA</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2405390\">2405390</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>MFLE: Short version in output conversion calculated incorrectly</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_ABA</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2396398\">2396398</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>After activating the draft, the draft UUID field of the returned active instance data is not initial</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2399372\">2399372</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Authorization check failed for SRT_SR_P</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2399477\">2399477</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>SAP GUI for HTML: &#126;singletransaction=3 allows /nTX</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2400809\">2400809</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2402373\">2402373</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>OBJECTS_OBJREF_NOT_ASSIGNED in class CL_SWF_UTL_WAPI_SERVICES</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2404762\">2404762</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Performance: SADL Metadata Load Is Calculated in Each Request</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2424813\">2424813</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Runtime error COMMIT_IN_POSTING in class CL_FDT_BACKGROUND</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2428086\">2428086</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Unable to Create New Line in Empty DataGrid</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BW</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2280748\">2280748</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2415736\">2415736</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Warranty : Post Versions to Reimburser fails via action A042 in transaction WTY</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2427850\">2427850</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p><strong>&#160;</strong>2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2429609\">2429609</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-02-21</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2372221\">2372221</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Performance improvement on GT_ADDR_XPCPT</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-04-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2461676\">2461676</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-04-24</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2445210\">2445210</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-05-08</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2467650\">2467650</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Authorization issues in viewing Document Info Record</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-05-08</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2463740\">2463740</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-05-19</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2477735\">2477735</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Annotation API: enable cache usage for get_annos_mass</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-07-10</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2450514\">2450514</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2489305\">2489305</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Dump while inserting international address version for a person</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2485784\">2485784</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>ALV export: Cannot save to clipboard in browser</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2490652\">2490652</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Legacy DAC maps business key to initial BOPF key</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2491892\">2491892</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-07-27</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2500159\">2500159</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-08-28</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2520306\">2520306</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Condition table index not activated after upgrade</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-09-06</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2527709\">2527709</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-09-06</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"102\">\r\n<p><a target=\"_blank\" href=\"/notes/2485570\">2485570</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"436\">\r\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\r\n</td>\r\n<td valign=\"top\" width=\"192\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"184\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"178\">\r\n<p>&#160;2017-09-07</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2652897\">2652897</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160; 2018-07-23</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2655756\">2655756</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;Tree UIBB: Conditional Formatting when master column has display type image</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-07-23</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Important Changes made after Release of Feature Package Stack&#160;01</strong></p>\r\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2018-07-03:</strong> </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP note <a target=\"_blank\" href=\"/notes/2655761\">2655761</a>&#160;(SAP S/4HANA - unrecommended revisions of SAP HANA database for use in SAP S/4HANA ) when you plan to upgrade to SAP HANA 2.0 SPS03 Revisions 3x.</p>\r\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<br /><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong>SAP HANA 1.0&#160;minimum required revision&#160;changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool.<br /><strong>2017-03-13: </strong>Inserted under General Considerations: Refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.<br /><strong>2017-03-16: </strong>Note <a target=\"_blank\" href=\"/notes/2436577\">2436577</a>&#160;inserted under General Conditions in section issues with active BW client.<br /><strong>2017-03-24: </strong>Deleted in General Considerations: According to the Product Availability Matrix for supported Database versions for the products SAP ERP 6.0 EHP 6 (or higher) and SAP S/4HANA 1511 the upgrade of an underlying SAP HANA Database from Release 1.0 to 2.0 can only be executed after the successfully completed technical conversion or upgrade process to S/HANA 1610 Feature Package Stack 01.<br /><strong>2017-03-29: </strong>SAP HANA 2.0 Minimum version changed to SP0 Revision 002.<br /><strong>2017-05-08:&#160;</strong>Inserted under Feature Package Update: 'It is recommed to use SUM to apply Feature Package Stacks.'<br /><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.<br /><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">FEATURE PACKAGE STACK&#160;00 (10/2016)</span></strong></p>\r\n<p><strong>General / Important Considerations</strong></p>\r\n<ul>\r\n<li>Please refer to the specific documentation under section <a target=\"_blank\" href=\"http://help.sap.com/s4hana_op_1610?current=s4hana_op_1610\">product documentation</a>.</li>\r\n<li>Feature Package Stack 00 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\r\n<ul>\r\n<li>SAP HANA 1.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 1.0&#160;is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>&#160;(SAP HANA Revision and Maintenance Strategy).</li>\r\n</ul>\r\n<li>SAP HANA 2.0</li>\r\n<ul>\r\n<li>The minimum required revision of SAP HANA 2.0&#160;is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available.&#160;For more details, please refer to&#160;note <a target=\"_blank\" href=\"/notes/2378962\">2378962</a>&#160;(SAP HANA 2.0 Revision and Maintenance Strategy).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2426477\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a target=\"_blank\" href=\"/notes/2426339\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\r\n<li>Furthermore, refer to the general restriction note&#160;<a target=\"_blank\" href=\"/notes/2333141\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\r\n<li>Please refer to note<a target=\"_blank\" href=\"/notes/2429281\"> 2429281</a> (S4H:SUM:XPRAS_AIMMRG:HANA deadlock dumps) when you upgrade your system with Software Update Manager (SUM) and your system is on HANA 2.0.</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions.&#160;</li>\r\n<li>\r\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\r\n</li>\r\n<li>\r\n<p>Please refer to SAP note <a target=\"_blank\" href=\"/notes/2655761\">2655761</a>&#160;(SAP S/4HANA - unrecommended revisions of SAP HANA database for use in SAP S/4HANA ) when you plan to upgrade to SAP HANA 2.0 SPS03 Revisions 3x.</p>\r\n</li>\r\n</ul>\r\n<li>\r\n<p>Please refer to SAP note <a target=\"_blank\" href=\"/notes/1906576\">1906576</a>&#160;(HANA client and server cross-version compatibility) if you would like to upgrade your SAP HANA Database to a newer revision or a newer available SPS level.</p>\r\n</li>\r\n</ul>\r\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer&#160;to SAP&#160;note&#160;<a target=\"_blank\" href=\"/notes/2356208\">2356208</a>&#160;which is the release information note for product version \"SAP FIORI FOR SAP S/4HANA 1610\".<br />Support Package Stack&#160;00 of SAP Fiori 2.0 for SAP S/4HANA on the frontend requires Feature Package Stack&#160;00 of SAP S/4HANA 1610 in the backend (and vice versa).</li>\r\n<li>Please refer to note <a target=\"_blank\" href=\"/notes/2328546\">2328546</a>&#160;(SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 00 (10/2016) content activation note<strong><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA; mso-bidi-font-family: 'Times New Roman';\">)</span></strong> for using the core configurator for your SAP S/4HANA on premise implementation and you need current installation and configuration data.</li>\r\n<li>Please ensure to apply note <a target=\"_blank\" href=\"/notes/2344014\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\r\n<li>If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1610 Feature Package Stack 00 and the target is&#160;SAP NetWeaver Support Package 01&#160;or higher. In this case, please implement note <a target=\"_blank\" href=\"/notes/2391758\">2391758</a>.</li>\r\n</ul>\r\n<p><strong>Installation Requirements</strong></p>\r\n<ul>\r\n<li>For the system installation, please refer to the <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20000/en-US/INST_OP1610.pdf\">installation guide</a>.</li>\r\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n<li>\r\n<p><em>Please refer to SAP note <a target=\"_blank\" href=\"/notes/306695\">306695</a>&#160;('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\r\n</li>\r\n</ul>\r\n<p><strong>Upgrade&#160;Requirements</strong></p>\r\n<ul>\r\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br />Please be aware that your software level is not higher than Feature Package Stack 02.</li>\r\n<li>For the system upgrade, please refer to the <a target=\"_blank\" href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20000/en-US/UPGR_OP1610.pdf\">upgrade guide</a>.</li>\r\n<li>You need at least Software Update Manager (SUM) Support Package&#160;20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a target=\"_blank\" href=\"https://support.sap.com/software/sltoolset.html\">support.sap.com/software/sltoolset.html</a>.</li>\r\n</ul>\r\n<p><strong>Notes to be applied on top of&#160;<strong>Feature Package Stack&#160;00</strong></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 874px;\">\r\n<tbody>\r\n<tr>\r\n<td width=\"118\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2338721\">2338721</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Program termination \"ASSIGN to a substring is not allowed\"</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2344368\">2344368</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>ECN IDoc processing fails because of missing data parts</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2345087\">2345087</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>BP_BAP: Missing values in required entry fields cause posting termination in mass processing</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2345102\">2345102</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>No valuation dialog box for entering effectivity parameter values</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2322771\">2322771</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>S4HANA SuccessFactors &amp; personnel number and the user name fields behavior in S4HANA</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_ABA, SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2284857\">2284857</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Number ranges - trace</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2342658\">2342658</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Note Implementation Failure due to Technical Languages(1Q,2Q,3Q,4Q,etc)</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2344014\">2344014</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>SPAU Adjustment for R3TR CLAS deliveries - adjustment of obsolete SAP notes deletes classes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2344436\">2344436</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>ATC: Table SATC_AC_OBJ_CTXT gets extremely large</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2345697\">2345697</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>BRF+ analytical decision table check Call CL_FDT_XS=&gt;GET_INSTANCE with RFC destination</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2345795\">2345795</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Workflow runtime ends with error WL821</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2346044\">2346044</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Exceptions were raised for unblocked addresses</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2346821\">2346821</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>SCWB - TLOGO Language(1Q,2Q,3Q,4Q etc) Filtering Fix</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2350429\">2350429</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>External view with more than 255 fields</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2351188\">2351188</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>BRF+ Anlytical function generation - Derive default schema dynamically</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2285661\">2285661</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Record of a time management infotype is exited even though system issues an error message</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2311339\">2311339</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>HR PAO: IT0019</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2313878\">2313878</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>HR PAO - validity period &amp;lt ;&gt; \"for all data\": Creation of new data record does not function correctly</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2335641\">2335641</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>HR PAO: Field PERID (IT 002) is not selected in the event of an input error</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2336154\">2336154</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>HR PAO: &#8216;Back&#8217; button (navigation to the overview page) is inactive</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2333704\">2333704</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>HR PAO: Program termination when you save infotype</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2340847\">2340847</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>HR PAO: Program termination when you save infotype</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2343342\">2343342</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Orders in cost distribution (IT0027/IT1018)</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_HRRXX</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2071826\">2071826</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Client copy for integrated SAP HANA liveCache</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2353319\">2353319</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Customizing Consistency Check for TAK01 in OKKP</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2354979\">2354979</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>SRFV_RPG_CAT2: Delete last record in Assign Report Categories to a Reporting Entity</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2355000\">2355000</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Parameter for ledger groups of underlying ledgers</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2359435\">2359435</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Client copy: Syntax error in program FINS_UPD_FINSC_001A_REP</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2362815\">2362815</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Material Ledger (ML) and Retail: tied empties: error CKMLMV 009 in LCKMLMVQUANTF06</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2364253\">2364253</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Syntax errors due to missing development package assignment</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2366738\">2366738</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>&#160;S/4HANA dump in CL_FAA_CFG_SERVICE-&gt;GET_LOCAL_CURR_TYPE_FROM_LDGRP if a ledger is not maintained correctly</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2367508\">2367508</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Correction for selection of changedocs in DIMP system</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2369405\">2369405</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Issue in Document Flow (SAP S/4HANA)</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2369962\">2369962</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Authorization issue and G/L not defaulting from PPOMA corrections</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2371490\">2371490</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Delivery date validation</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2371666\">2371666</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>MD01N: Setup of MRP-Records via PPH_SETUP_MRPRECORDS or PPH_SETUP_MRPRECORDS_SIMU incomplete</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2372008\">2372008</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Switch from DB_COMMIT to COMMIT WORK AND WAIT for consolidation of Data Aging carry forward records and archive representatives</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2372230\">2372230</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Fill BWTAR in data aging carry forward records and archive representatives</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2372605\">2372605</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Issue while creating BP role as a supplier in transaction BP</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2373940\">2373940</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Corrective measure for changes to the BOM Maintenance application</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2376505\">2376505</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Revise Payment Proposals: Cannot Mass Block Items for Payment</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2376747\">2376747</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Moving average price is not changed by goods receipt</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2377529\">2377529</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>V_T012 maintenance dialog does not exist</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2378915\">2378915</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Manage PIRs redirect of non active versions</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2379565\">2379565</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>The Incoterm 2 field is converted incorrectly</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2379790\">2379790</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Reading Purchase Order for multiple items in a PR</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2380548\">2380548</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Error during assignment to investment program item, maintenance order</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2381346\">2381346</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Characteristic based planning aborts for long material numbers</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2381849\">2381849</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Missing condition tables B082 and B083</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2382748\">2382748</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>KB11N: Short dump when ledger group filled</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2327999\">2327999</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>QuantityWare CDS Extensions For IS-OIL</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">IS-OIL</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2379816\">2379816</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Unicode conversion dump related to KONV</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">IS-OIL</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2172384\">2172384</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>J1INCHLN and J1INCHLC: Multiple section legal change</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">FI-CA</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-11-03</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2364845\">2364845</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Migration: E543(FINS_RECON) in RC3</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-11-08</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2384182\">2384182</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>S/4HANA 1511+1610: Material master maintenance: Dumps when using screen sequence DI</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2399372\">2399372</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Authorization check failed for SRT_SR_P</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-09</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2399423\">2399423</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Dump in characteristic value assignment</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-16</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2400809\">2400809</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-16</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2402373\">2402373</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>OBJECTS_OBJREF_NOT_ASSIGNED in class CL_SWF_UTL_WAPI_SERVICES</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-16</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2405390\">2405390</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>MFLE: Short version in output conversion calculated incorrectly</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-16</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2415736\">2415736</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Warranty : Post Versions to Reimburser fails via action A042 in transaction WTY</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-23</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2280748\">2280748</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-01-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2427850\">2427850</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-02-16</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2429609\">2429609</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-02-17</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2399477\">2399477</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>SAP GUI for HTML: &#126;singletransaction=3 allows /nTX</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-02-20</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2372221\">2372221</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Performance improvement on GT_ADDR_XPCPT</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-04-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2461676\">2461676</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-04-24</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2467650\">2467650</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Authorization issues in viewing Document Info Record</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-05-08</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2463740\">2463740</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-05-19</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2477735\">2477735</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Annotation API: enable cache usage for get_annos_mass</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-07-10</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2450514\">2450514</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2489305\">2489305</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Dump while inserting international address version for a person</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2485784\">2485784</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>ALV export: Cannot save to clipboard in browser</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2490652\">2490652</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Legacy DAC maps business key to initial BOPF key</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-07-12</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" style=\"width: 79.22%;\"><a target=\"_blank\" href=\"/notes/2491892\">2491892</a></span></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-07-27</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2500159\">2500159</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-08-28</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2520306\">2520306</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Condition table index not activated after upgrade</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-09-06</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2527709\">2527709</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>S4CORE</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-09-06</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2485570\">2485570</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>SAP_BASIS</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2017-09-07</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"82\">\r\n<p><a target=\"_blank\" href=\"/notes/2652897\">2652897</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160; 2018-07-23</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"82\">\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2655756\">2655756</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"349\">\r\n<p>&#160;Tree UIBB: Conditional Formatting when master column has display type image</p>\r\n</td>\r\n<td valign=\"top\" width=\"126\">\r\n<p>&#160;SAP_UI</p>\r\n</td>\r\n<td valign=\"top\" width=\"175\">\r\n<p>No</p>\r\n</td>\r\n<td valign=\"top\" width=\"142\">\r\n<p>&#160;2018-07-23</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>Notes to be applied on top of&#160;<strong>Feature Package Stack&#160;00 (only relevant for customers using SEM-BCS)</strong></strong></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 874px;\">\r\n<tbody>\r\n<tr>\r\n<td width=\"118\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2352766\">2352766</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Enhancement of MDF metadata for S/4 1610</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p style=\"padding-left: 60px;\">No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2367523\">2367523</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Enhancement of the SEM-BCS data model for S/4 HANA OP</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p style=\"padding-left: 60px;\">No</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-11-09</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>Notes to be applied on top of&#160;<strong>Feature Package Stack&#160;00 (only relevant for customers using </strong></strong>IS-RETAIL<strong><strong>)</strong></strong></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 874px;\">\r\n<tbody>\r\n<tr>\r\n<td width=\"118\">\r\n<p><strong>SAP Note</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>Manual Activities required</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p><strong>&#160;Added on</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2224330\">2224330</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Parameter list of append search help \"WRF_BETR_WHSH_APPEND\" differs from appending one</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p style=\"padding-left: 60px;\">Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-10-31</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"118\">\r\n<p><a target=\"_blank\" href=\"/notes/2393010\">2393010</a></p>\r\n</td>\r\n<td valign=\"top\" width=\"518\">\r\n<p>Negative stock for empties after stock transfer</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">S4CORE</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p style=\"padding-left: 60px;\">Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"238\">\r\n<p>&#160;2016-11-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Important Changes made after Release of Feature Package Stack&#160;00</strong></p>\r\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2020-05-11: </strong></strong></strong></strong></strong></strong></strong></strong></strong>Removed incorrect supported Kernel version&#160;SAP KERNEL 7.73 64-BIT UNICODE.</p>\r\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2019-04-01:</strong></strong></strong></strong></strong></strong></strong></strong></strong> Inserted:&#160;United Kingdom leaving the EU: For information on how a &#8220;hard Brexit&#8221; (= a no-deal scenario) would impact your <em>SAP S/4HANA </em>system, please see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2749671\">2749671</a>.<br /><br /><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SP note <a target=\"_blank\" href=\"/notes/2527538\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a target=\"_blank\" href=\"/notes/2527648\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.</p>\r\n<p><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong>SAP HANA 1.0&#160;minimum required revision&#160;changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool.<br /><strong>2016-11-18</strong>: Note 2224330 moved to list of notes which is only relevant for customers using IS-RETAIL.<br /><strong>2016-11-21: </strong>Note <a target=\"_blank\" href=\"/notes/2356364\">2356364</a>&#160;deleted in list of notes to be implemented on top of Feature Package Stack 00.<br /><strong>2016-12-08: </strong>Section<strong> '</strong>Please ensure to apply note <a target=\"_blank\" href=\"/notes/2344014\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.' inserted under 'General / Important Considerations'.<br /><strong>2016-12-20</strong>: 'SAP HANA 2.0 is not released for Feature Package Stack 00.' inserted under 'General / Important Considerations'.<br /><strong>2017-03-31: </strong>HANA 2.0 chapter inserted under 'General / Important Considerations'.<br /><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.<br /><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a target=\"_blank\" href=\"/notes/2487855\">2487855</a>&#160;(Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022712)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D071276)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002346431/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2480067", "RefComponent": "FI-LOC-SRF-RUN", "RefTitle": "Replacement of Existing Legal Reports with 'SAP Document and Reporting Compliance - Statutory Reports'", "RefUrl": "/notes/2480067"}, {"RefNumber": "2450293", "RefComponent": "CA-MDG", "RefTitle": "SAP Master Data Governance 9.0 SP03: Release Information Note", "RefUrl": "/notes/2450293"}, {"RefNumber": "2364981", "RefComponent": "XX-SER-REL", "RefTitle": "Release restrictions for NetWeaver 7.51 for S/4HANA ON-PREMISE 1610", "RefUrl": "/notes/2364981"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2410201", "RefComponent": "FIN-SEM-BCS", "RefTitle": "SEM-BCS: Recommended steps after an upgrade of SEM-BW / FINBASIS software components", "RefUrl": "/notes/2410201 "}, {"RefNumber": "2655761", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA - restrictions and recommendations regarding specific revisions of SAP HANA database for use in SAP S/4HANA", "RefUrl": "/notes/2655761 "}, {"RefNumber": "2620910", "RefComponent": "BC-OP-PLNX", "RefTitle": "SAP S/4HANA 1511, 1610, 1709, 1809 and SAP BW/4HANA 1.0, 2.0: Recommended and released Application Server Platforms", "RefUrl": "/notes/2620910 "}, {"RefNumber": "2260090", "RefComponent": "FI-FIO-GL", "RefTitle": "Release Information Note for Fiori app Audit Journal", "RefUrl": "/notes/2260090 "}, {"RefNumber": "2524661", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709 - SAP HANA Database Requirements", "RefUrl": "/notes/2524661 "}, {"RefNumber": "2499175", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on the update/upgrade to SAP NW AS for ABAP 7.52", "RefUrl": "/notes/2499175 "}, {"RefNumber": "2443806", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Gather Forecast Data 05/2017", "RefUrl": "/notes/2443806 "}, {"RefNumber": "2443807", "RefComponent": "PP-FIO-PN-ALN", "RefTitle": "Release Information Note for the Fiori application Analyze Allocation Production 05/2017", "RefUrl": "/notes/2443807 "}, {"RefNumber": "2443809", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Manage Forecast Projects 05/2017", "RefUrl": "/notes/2443809 "}, {"RefNumber": "2443851", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application View Forecast Results 05/2017", "RefUrl": "/notes/2443851 "}, {"RefNumber": "2443852", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Calculate Forecast 05/2017", "RefUrl": "/notes/2443852 "}, {"RefNumber": "2443853", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Approve and Publish Forecast 05/2017", "RefUrl": "/notes/2443853 "}, {"RefNumber": "2443854", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Upload Production Data 05/2017", "RefUrl": "/notes/2443854 "}, {"RefNumber": "2443855", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Analyze and Compare Results 05/2017", "RefUrl": "/notes/2443855 "}, {"RefNumber": "2443856", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Manage Hierarchy 05/2017", "RefUrl": "/notes/2443856 "}, {"RefNumber": "2443857", "RefComponent": "PP-FIO-PN-DEF", "RefTitle": "Release Information Note for the Fiori application View Deferment Events 05/2017", "RefUrl": "/notes/2443857 "}, {"RefNumber": "2443858", "RefComponent": "PP-FIO-PN-DEF", "RefTitle": "Release Information Note for the Fiori application Manage Work Order Deferment Events 05/2017", "RefUrl": "/notes/2443858 "}, {"RefNumber": "2443859", "RefComponent": "PP-FIO-PN-DEF", "RefTitle": "Release Information Note for the Fiori application Analyze Deferment 05/2017", "RefUrl": "/notes/2443859 "}, {"RefNumber": "2443860", "RefComponent": "PP-FIO-PN", "RefTitle": "Release Information Note for the Fiori application Fix Errors 05/2017", "RefUrl": "/notes/2443860 "}, {"RefNumber": "2443804", "RefComponent": "PP-FIO-PN", "RefTitle": "Release Information Notes for Common Reuse Library 05/2017", "RefUrl": "/notes/2443804 "}, {"RefNumber": "2443871", "RefComponent": "PP-FIO-PN-MES", "RefTitle": "Release Information Note for the Fiori application Capture Field Data 05/2017", "RefUrl": "/notes/2443871 "}, {"RefNumber": "2443808", "RefComponent": "PP-FIO-PN-FC", "RefTitle": "Release Information Note for the Fiori application Manage Forecast Access 05/2017", "RefUrl": "/notes/2443808 "}, {"RefNumber": "2444372", "RefComponent": "PP-PN", "RefTitle": "Release Information Note for Upstream Operations Management (UOM) - Upstream Oil & Gas 05/2017", "RefUrl": "/notes/2444372 "}, {"RefNumber": "2443810", "RefComponent": "PP-FIO-PN-ALN", "RefTitle": "Release Information Note for the Fiori application Allocate Production 05/2017", "RefUrl": "/notes/2443810 "}, {"RefNumber": "2420699", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Release of SAP HANA Database 2.0 for older SAP Versions", "RefUrl": "/notes/2420699 "}, {"RefNumber": "2408419", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA - Multi-Node Support", "RefUrl": "/notes/2408419 "}, {"RefNumber": "2431747", "RefComponent": "FI-GL-GL", "RefTitle": "General Ledger: Incompatible changes in S/4HANA compared to classic ERP releases", "RefUrl": "/notes/2431747 "}, {"RefNumber": "2426477", "RefComponent": "XX-SER-REL", "RefTitle": "Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2", "RefUrl": "/notes/2426477 "}, {"RefNumber": "2423846", "RefComponent": "CA-EPT-TAS", "RefTitle": "Stop of SPAM procedure - SPAM status: STOP", "RefUrl": "/notes/2423846 "}, {"RefNumber": "2372388", "RefComponent": "XX-SER-REL", "RefTitle": "SAP NetWeaver Application Server for ABAP 7.51 Innovation Package: Release Information Note", "RefUrl": "/notes/2372388 "}, {"RefNumber": "2333141", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1610: Restriction Note", "RefUrl": "/notes/2333141 "}, {"RefNumber": "2350408", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on the update/upgrade to SAP NW AS for ABAP 7.51 Innovation Package", "RefUrl": "/notes/2350408 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}