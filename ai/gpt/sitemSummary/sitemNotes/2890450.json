{"Request": {"Number": "2890450", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 348, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000218352020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002890450?language=E&token=321ED361B6EE7C31F784C8FD98278155"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002890450", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002890450/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2890450"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.04.2022"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2890450 - BP_SRV: Addition of data sets specific to Financial Services to the business partner data replication service (ratings, credit standing data, regulatory reporting data, and so on)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In S/4HANA, you also want to use the business partner data replication service (formerly known as the MDG service) for the replication of financial services business partner data.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Technical data:</strong></span></p>\r\n<p><strong>Service namespace:</strong> <br />http://sap.com/xi/SAP_BS_FND/MDG/Global2</p>\r\n<p><strong>Service interfaces:<br /></strong>BusinessPartnerSUITEBulkReplicateRequest_In<br />BusinessPartnerSUITEBulkReplicateRequest_Out</p>\r\n<p>The data sets of the Financial Services extension of the SAP Business Partner are not currently integrated in this service. This relates to the following data sets:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td width=\"170\">\r\n<p><strong>Data set</strong></p>\r\n</td>\r\n<td width=\"76\">\r\n<p><strong>Database table</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Financial Services Common</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BP001</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Employment Data</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BP011</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Fiscal Year Information</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BP021</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Credit Worthiness Data</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BP1010</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Ratings</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BP1012</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Business Partner is Bank</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BUT0BANK</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Tax Compliance Data</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BPTAXC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Reporting Data</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BP1030</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td width=\"170\">\r\n<p>Alias Names</p>\r\n</td>\r\n<td width=\"76\">\r\n<p>BKK21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>To replicate Financial Services data, you must use the old ABA replication services (ABABusinessPartnerIn, ABABusinessPartnerOut - namespace http://sap.com/xi/ABA) that are still technically available in S/4HANA but are no longer intended to be used. In particular, there is no service that can simultaneously replicate data for the Financial Services enhancement and data for customer/vendor integration to the business partner.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Business partner, data replication, http://sap.com/xi/SAP_BS_FND/MDG/Global2, BusinessPartnerSUITEBulkReplicateRequest_In, BusinessPartnerSUITEBulkReplicateRequest_Out,</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The relevant function is missing.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The development for integrating the above data sets into the business partner data replication service was completed by the end of 2019. The enhancement was implemented down to ERP 600 Enhancement Package 8 and is available in the following release versions/Support Packages:</p>\r\n<ul>\r\n<li>S4CORE 104 (S/4HANA 1909) - Support Package 2</li>\r\n<li>S4CORE 103 (S/4HANA 1809) - Support Package 4</li>\r\n<li>S4CORE 102 (S/4HANA 1709) - Support Package 6</li>\r\n<li>S4CORE 101 (S/4HANA 1610) - Support Package 8</li>\r\n<li>S4CORE 100 (S/4HANA 1511) - Support Package 10</li>\r\n<li>SAP_APPL 618 (ERP 600, Enhancement Package 8) - Support Package 15 if note&#x00A0;3187795 is applied in addition.</li>\r\n</ul>\r\n<p>We recommend that you import the enhancement via Support Package. However, you can also implement the enhancement by SAP Note, together with certain manual steps. For more information, see the referenced SAP Notes:</p>\r\n<p><strong>Technical prerequisites:</strong> <br />2799001 \"BP_SRV: Addition of Financial Services data sets to business partner data replication service - technical prerequisites<br />2889120 \"BP_SRV: UDO report for enhancement of BP (MDG) replication service with FS datasets\"<br /><br /><strong>Main functionality:</strong><br />2888623 \"BP_SRV:  Enhancement of Business Partner Data Replication Service (formerly known as MDG Service) with Financial Services Datasets\"<br /><br /><strong>Refer also to the following:</strong><br />2858939 \"BP_SRV: Activation of nodes specific to Financial Service (for example, rating, regulatory reporting data) in the business partner data replication service<br />--&gt; This SAP Note explains how incomplete business partner messages are sent to avoid data loss in system landscapes in which not all systems already have the service extension, and how you can actively intervene to enable the deletion of complete nodes/datasets.</p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033094)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D044196)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002890450/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002890450/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2889120", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: UDO report for the enhancement of the Business Partner Data replication service with Financial Services Datasets", "RefUrl": "/notes/2889120"}, {"RefNumber": "2888623", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Enhancement of Business Partner Data Replication Service (formerly known as MDG Service) with Financial Services Datasets", "RefUrl": "/notes/2888623"}, {"RefNumber": "2858939", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Activation of nodes specific to Financial Service (for example, rating, regulatory reporting data) in the business partner data replication service", "RefUrl": "/notes/2858939"}, {"RefNumber": "2799001", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Addition of Financial Services data sets to business partner data replication service - technical prerequisites", "RefUrl": "/notes/2799001"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2211665", "RefComponent": "XX-SER-REL", "RefTitle": "Release information for \"SAP for Banking\" in connection with \"SAP S/4HANA, on-premise edition 1511\"", "RefUrl": "/notes/2211665 "}, {"RefNumber": "2943552", "RefComponent": "XX-SER-REL", "RefTitle": "Release information for \"SAP for Banking\" in connection with \"SAP S/4HANA 2020\" (\"SAP S/4HANA, on-premise edition 2020\")", "RefUrl": "/notes/2943552 "}, {"RefNumber": "2891428", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Introduction of segment validation (MDG ReUse Validation) for Financial Services enhancements of the business partner data replication service", "RefUrl": "/notes/2891428 "}, {"RefNumber": "2799001", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Addition of Financial Services data sets to business partner data replication service - technical prerequisites", "RefUrl": "/notes/2799001 "}, {"RefNumber": "2889120", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: UDO report for the enhancement of the Business Partner Data replication service with Financial Services Datasets", "RefUrl": "/notes/2889120 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}