{"Request": {"Number": "1118395", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 534, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001118395?language=E&token=A08F2C8CDFCA6D198A45949D35B71460"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001118395", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001118395/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1118395"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "PY-IN"}, "SAPComponentKeyText": {"_label": "Component", "value": "India"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "India", "value": "PY-IN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-IN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1118395 - Enabling New GL functionality for India Payroll Localization"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>As of SAP Release ECC 6.0 (ERP 2005) the evaluation for the posting of payroll to accounting was adjusted to meet the current legal requirements by the <B>International Financial Reporting Standards (IRFS)</B>. The corresponding functionality of the international payroll has not been localized yet for the country version India.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>New GL, Posting to accounting, RPCIPE00, Posting, CODIST, Distribution of liabilities<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Changes to meet company specific legal practice<br /><br /><B>Minimum HR SP level Required:</B> SPKE60024 (HR SP 24)<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>New GL solution is enabled for India Payroll. It can be activated and configured for HCM part through following IMG activity:<br /><br />Payroll: India --&gt; Reporting for Posting Payroll Results to Accounting --&gt; General Ledger: Distribution of Liabilities According to Expenses<br /><br /><B>How to activate New GL solution:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to IMG activity: India --&gt; Reporting for Posting Payroll Results to Accounting --&gt; General Ledger: Distribution of Liabilities According to Expenses --&gt; Central Settings --&gt; Define Type of Distribution<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To distribute liabilities as per expenses, set the entry accordingly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For further information, refer to IMG documentation.<br /><br />Following are the changes/new objects introduced for this development:</p> <OL>1. <B>New wage types:</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Expenses which follow \"When Paid\" principle have now corresponding Carry Forward and Brought Forward wage types which can be configured for posting of arrear amounts (during retro payroll run) as required. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For example, Employee State Insurance (ESI) functionality specific wage types /AE2 and /ZE2 can be used now for this purpose. <OL>2. <B>New Payroll Function:</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Payroll function XCODI is enabled for India country version and is delivered in payroll subschema INE0 as commented. This payroll function must be uncommented in order to use New GL functionality. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;XCODI payroll function creates a new cluster table CODIST that contains ratio in which the expenses are present, and hence the order in which employer payables should be distributed during posting. <p><br /><br /><B>Known Limitations:</B></p> <OL>1. When distributing the liabilities to different accounting units, tiny differences for each personnel number can arise when rounding. These rounding differences are added to the largest of the amounts involved in the calculation. Clearing may therefore be necessary between the accounting units.</OL> <OL>2. The exact cost distribution of liabilities as per the expenses may not be possible in scenarios involving payments from multiple accounting assignments</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example scenario: An employee receives multiple payments (for example Salary, Bonus, Allowances) from multiple accounting assignments in a single pay period. The exact distribution of liabilities (for example Net Pay) as per the expenses may not be possible. <p><br /><br /><B>Important Note:</B></p> <OL>1. If you activating New GL functionality with option \"Distribution of Liabilities as per Expenses\" switch status, any retrospective payroll run performed on earlier periods (where payroll results where generated without this option being enable) will run as if this functionality does not exist. It continues the behavior similar to existing posting solution (without New GL being present). <B>Thus, from payroll results perspective, no migration activity is required.</B></OL> <OL>2. Further information on this solution can be found at -</OL> <OL><OL>a) SAP Note: 1039346 (F&amp;A ECC 6.0 - RPCIPE00 - distribution of liabilities)</OL></OL> <OL><OL>b) URL:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://help.sap.com/saphelp_erp60_sp/helpdata/en/2d/ 830e405c538f5ce10000000a155106/frameset.htm <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://help.sap.com/saphelp_erp60_sp/helpdata/en/c5/ 7ee342599e5904e10000000a155106/content.htm <p><br /><B>How to implement this Note:</B><br /><br />This Note is currently released for Pilot testing. You can get the changes by performing following steps (both the steps are required):</p> <OL>1. Implement the attachments of this Note</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The attachments can be downloaded from this note as described in Notes 480180 and 13719. The files are SAPCAR files. The software to download the files can be found in the SAP service marketplace. The filenames of the attachment are: <OL><OL>a) New_GL_SYST_1.SAR</OL></OL> <OL><OL>b) New_GL_SYST_2.SAR</OL></OL> <OL><OL>c) New_GL_CUST_1.SAR</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is recommended to implement these files in order as mentioned above. <OL>2. Implement the code correction instructions through transaction SNOTE.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I035117)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I035117)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118395/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "New_GL_SYST_1.SAR", "FileSize": "4", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000877402007&iv_version=0003&iv_guid=5647C0F6B7A295419B94E53BB446E700"}, {"FileName": "New_GL_SYST_2.SAR", "FileSize": "79", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000877402007&iv_version=0003&iv_guid=C815CF438345AB45BA910079B5D8D4FB"}, {"FileName": "New_GL_CUST_1.SAR", "FileSize": "49", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000877402007&iv_version=0003&iv_guid=8ABAD818A7AE5A4FA334E504E07647AD"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146"}, {"RefNumber": "1039346", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: RCIPE00/RPCIPE01 - distribution of liabilities", "RefUrl": "/notes/1039346"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1039346", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: RCIPE00/RPCIPE01 - distribution of liabilities", "RefUrl": "/notes/1039346 "}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HRCIN 600", "SupportPackage": "SAPK-60046INSAPHRCIN", "URL": "/supportpackage/SAPK-60046INSAPHRCIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 1, "URL": "/corrins/0001118395/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}