{"Request": {"Number": "525677", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 384, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000525677?language=E&token=F89558020B7908E69ACA60C6B27EFD34"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000525677", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000525677/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "525677"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "02.03.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade - general"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "525677 - Problems when starting shadow instance"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During the system switch upgrade, you cannot start the shadow instance or you cannot connect R3up after you start the shadow instance.<br />VERY IMPORTANT: AFTER THE PHASE STOP_SHDI_LAST IS COMPLETE, YOU CANNOT START THE SHADOW INSTANCE. ANY ERRORS THAT OCCUR AFTER THIS PHASE (EVEN IN PHASES SUCH AS SHADOW_IMPORT, AND SO ON)&#x00A0;&#x00A0;ARE NEVER DUE TO THE FACT THAT A SHADOW INSTANCE IS NOT RUNNING!<br /><br />This note also applies to the implementation of Enhancement Packages or pure Support Package stacks with the tool SAPehpi, starting with Basis release 700.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>START_SHDI_FIRST, ALTNTAB_FILL, downtime-minimized, resource-minimized<br />TEXTENV_INVALID, TEXTENV_CODEPAGE_NOT_ALLOWED</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the system switch upgrade, an SAP Web AS system (Basis system) is started in the phase START_SHDI_FIRST of the target release.<br />This occurs either</p> <UL><LI>alternating with the production system (if you use the resource-minimized strategy) or</LI></UL> <UL><LI>in parallel (if you use the downtime-minimized strategy).<br /></LI></UL> <p>In rare cases, the process of starting the shadow instance may fail. This note describes possible reasons for this.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. On HP-UX only:<br />On HP, in the script startsap for the shadow instance in:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;DIR_PUT&gt;/SID/homeshd/SID<br />the profile parameter \"SHLIB_PATH\" is set to &lt;DIR_PUT&gt;/exe, in other words, it is set to the new kernel directory. However, if the parameter \"LD_LIBRARY_PATH\" is also used in the environment, this parameter refers to the old kernel directory and overrides \"SHLIB_PATH\".<br /><br />Solution: Use \"unsetenv LD_LIBRARY_PATH\" or also set this parameter in the script startsap/stopsap for the shadow instance.<br /></OL> <OL>2. On UNIX only: Using logical host names<br />If the profile parameters rdisp/myname, SAPLOCALHOST and SAPLOCALHOSTFULL are set in the instance profile of the original instance and if these refer to the logical host name and not the physical host name, the shadow system starts in the phase START_SHDI_FIRST, but you cannot log on in the next phase (ALTNTAB_FILL) because the physical host name is entered for these parameters in the instance profile.<br /><br />To correct these parameters, proceed as follows:<br />(1) Stop the shadow system (see attachment).<br />(2) Adjust the parameters in the default or instance profile of the shadow system (see attachment).<br />(3) Start the shadow system (see attachment).<br /><br />The shadow instance should then run completely on the virtual host.<br />Then repeat the phase ALTNTAB_FILL.<br /></OL> <OL>3. In Source Release 31I only:<br />If you must manually restart the shadow instance (after a database crash, for example), set the environment parameter dbms_type before you restart the shadow system.<br /></OL> <OL>4. Resource-minimized strategy, UNIX:<br />A termination occurs on AIX 5.x 64 bit; this may also occur on other UNIX platforms.<br /><br />If the shadow instance is not started in the phase 'START_SHDI_FIRST', log on as the user &lt;sid&gt;adm and enter the following commands:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;cd /usr/sap/put/bin</OL> <OL>5. &#x00A0;&#x00A0;&#x00A0;&#x00A0;./R3up stopshd&#x00A0;&#x00A0; (as of Basis 700: ./SAPup stopshd)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;cleanipc &lt;SYSNO&gt; remove<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;cleanipc &lt;SHDSYSNO&gt; remove<br />where<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SYSNO = number of the original instance and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SHDSYSNO = number of the shadow instance<br /><br />Then repeat the phase 'START_SHDI_FIRST'.<br /></OL> <OL>6. The process of starting the shadow system fails in the phase START_SHDI_FIRST. in the trace files of the work processes (dev_w*) of the shadow system, the error message \"TEXTENV_INVALID\" is displayed.<br /><br />Check whether the profile parameter \"zcsa/system_language\" is set to a value other than D or E in the instance profile or default profile of the shadow system. If this is the case, set this parameter to D or E. Stop the shadow system (see attachment) and repeat the phase.</OL> <OL>7. The upgrade procedure checks for free ports for the shadow instance. If the SAP system is restarted after performing this check and before starting the shadow system, these previously free ports may be occupied and prevent the shadow system from being started. You can use a \"netstat\" command to determine whether this is the case, and you can start the shadow system by stopping the relevant process.<br /></OL> <OL>8. The following error message is displayed in the phase ALTNTAB_FILL:<br />&#x00A0;&#x00A0;\"RFC of \"subst_start_report_in_batch\" failed.<br />&#x00A0;&#x00A0; codepage 1100 is not released<br /><br />Furthermore, the error message \"TEXTENV_CODEPAGE_NOT_ALLOWED\" is displayed in the trace files of the work processes (dev_w*) of the shadow system.<br />This is due to an error in the NLS configuration of the shadow system. If this error occurs, proceed as follows:<br /><br />Log on directly to the database. Use the user or the database schema of the standard SAP system (this is generally SAPR3 or SAP&lt;SID&gt;).<br />Insert the following line into the table \"TCPDB~\":<br /><br />&#x00A0;&#x00A0;INSERT INTO \"TCPDB~\" VALUES ('1100','1100');<br />&#x00A0;&#x00A0; for MSSQL: INSERT INTO [TCPDB~] VALUES ('1100','1100')<br />&#x00A0;&#x00A0; with a final COMMIT.<br /><br />Stop and restart the shadow system (see attachment). Then continue the upgrade.<br /></OL> <OL>9. When you first log on to the shadow system as the user DDIC, you are prompted to enter a new password.<br />This error can only occur on UNIX and if you use the \"resource-minimized\" upgrade strategy. It is caused by the profile parameter \"login/password_expiration_time\" in the profile DEFAULT.PFL of the shadow instance. Do not change the password, but stop the shadow instance (see attachment), comment out this parameter, restart the shadow instance and log on again.<br /></OL> <OL>10. Error message:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exception \"VARIANT_INSERT_VARIANT_LOCKED\" raised,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;variant is locked by another process.<br /><br />The lock handler could not be reached. This occurs because the profile parameter \"rdisp/myname\" is entered in the instance profile of the shadow instance. This parameter is set in the default profile of the shadow instance. Therefore, comment out this parameter in the instance profile of the shadow instance. Stop and then restart the shadow instance as described in the attachment.<br /></OL> <OL>11. If you use a high availability solution or a switchover cluster, see also Note 96317.</OL> <p></p> <OL>12. Problem in the Batch/TemSe processing in the shadow system:<br />If the SYSLOG (sm21) contains error messages of the following type:<br />F60 TemSe object file name is too long<br />the reason is that the path (upgrade directory + TemSe output path + file name) is too long. Without the fixed parts, the length of the upgrade directory should only be 30 characters or shorter.<br />Solution:<br />(1) Stop the shadow system (see attachment).<br />(2) Adjust the parameters in the default or instance profile of the shadow system (see attachment).<br />In this case, you must set the following:<br />/rsts/files/root/G=/&lt;path shorter than 30&gt;/$(rsts/filename)<br />($(rsts/filename) must be attached.)<br />(3) Start the shadow system (see attachment).</OL> <p></p> <b>Attachment</b><br /> <b></b><br /> <b></b><br /> <p><B>Caution when using IBM i (AS/400, iSeries, i5/os)!</B><br />Before the actual command for starting and stopping the shadow instance for the platform IBM i, you must start a QP2TERM session and<br />supplement the execution environment, for example, in the following way:<br /><br />1. Perform the following steps as &lt;SID&gt;OFR or as &lt;SID&gt;ADM (depending on the user concept that is active).<br />2. Call QP2TERM.<br />Then in the QP2TERM session:<br />3. cd &lt;upgrade_directory&gt;/bin<br />4. export LIBPATH=.<br />5. Execute the command R3up/SAPup/SAPehpi specified below.<br />6. You can exit the QP2TERM session using F12/F3.<br /></p> <UL><LI>Starting the shadow instance</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Start R3up (as of Basis 700: SAPup or to import Enhancement Packages: SAPehpi) as the user &lt;sid&gt;adm as follows:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3up&#x00A0;&#x00A0;&#x00A0;&#x00A0;startshd&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPup&#x00A0;&#x00A0; startshd&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPehpi startshd<br /></p> <UL><LI>Stopping the shadow instance</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Start R3up (as of Basis 700: SAPup or to import Enhancement Packages: SAPehpi) as the user &lt;sid&gt;adm as follows:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3up&#x00A0;&#x00A0;&#x00A0;&#x00A0;stopshd&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPup&#x00A0;&#x00A0; stopshd&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPehpi stopshd<br /></p> <UL><LI>Configuring the shadow system:</LI></UL> <UL><UL><LI>Kernel of the shadow system: &lt;upgrade_directory&gt;/exe</LI></UL></UL> <UL><UL><LI>Profiles of the shadow system: &lt;upgrade_directory&gt;/&lt;SID&gt;/SYS/profile.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The default profile is DEFAULT.PFL, the instance profile is &lt;SID&gt;_DVEBMGS&lt;SHDSYSNO&gt;_&lt;host name&gt;.</p> <UL><UL><LI>Work directory (here, the trace files of the message server, gateway, dispatcher and the work processes of the shadow system are available): &lt;upgrade_directory&gt;/&lt;SID&gt;/DVEBMGS&lt;SHDSYSNO&gt;/work</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-EHP (Enhancement Package)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028310)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036390)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000525677/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000525677/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "988717", "RefComponent": "BC-DB-DBI", "RefTitle": "Syslog BYE: invalid interface parameter buf_area", "RefUrl": "/notes/988717"}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861"}, {"RefNumber": "96317", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/96317"}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513"}, {"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "913848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913848"}, {"RefNumber": "905029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905029"}, {"RefNumber": "890202", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/890202"}, {"RefNumber": "826487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826487"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "650162", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to R/3 WebAs 620 with BANK-TRBK 3.0", "RefUrl": "/notes/650162"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "518396", "RefComponent": "BC-UPG", "RefTitle": "Profiles during the upgrade", "RefUrl": "/notes/518396"}, {"RefNumber": "500181", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/500181"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "430318", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/430318"}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717"}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071"}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1156970", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 ABAP", "RefUrl": "/notes/1156970"}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841"}, {"RefNumber": "1095506", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.1 for banking services from SAP", "RefUrl": "/notes/1095506"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1071404", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071404"}, {"RefNumber": "1061649", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver Process Integration 7.1", "RefUrl": "/notes/1061649"}, {"RefNumber": "1039395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1039395"}, {"RefNumber": "1019585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1019585"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071 "}, {"RefNumber": "1156970", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 ABAP", "RefUrl": "/notes/1156970 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1095506", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.1 for banking services from SAP", "RefUrl": "/notes/1095506 "}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "1061649", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver Process Integration 7.1", "RefUrl": "/notes/1061649 "}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185 "}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070 "}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677 "}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783 "}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322 "}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513 "}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "988717", "RefComponent": "BC-DB-DBI", "RefTitle": "Syslog BYE: invalid interface parameter buf_area", "RefUrl": "/notes/988717 "}, {"RefNumber": "650162", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to R/3 WebAs 620 with BANK-TRBK 3.0", "RefUrl": "/notes/650162 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717 "}, {"RefNumber": "518396", "RefComponent": "BC-UPG", "RefTitle": "Profiles during the upgrade", "RefUrl": "/notes/518396 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}