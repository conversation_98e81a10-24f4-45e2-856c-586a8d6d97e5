{"Request": {"Number": "1422489", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 452, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016950992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=2738B278E2870B0985A801C853819D11"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1422489"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.12.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-FI-CA"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: Please use Component FI-CA instead"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Finance", "value": "XX-PROJ-FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: Please use Component FI-CA instead", "value": "XX-PROJ-FI-CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-FI-CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1422489 - FI-CA: Prerequisites for EC sales list"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes the prerequisites to be created so the data is recorded for the EC sales list in FI-CA.<br />As the functions for the EC sales list do not yet exist in FI-CA, the data for the EC sales list is not yet created. For this reason it is important that the relevant documents for the EC sales list are posted so that later they can be transferred. The functions for the EC sales list will be available at the beginning of 2010. Even if relevant postings exist before then, this is not a problem if the prerequisites mentioned here for the documents are fulfilled. A new initialization report that will find the relevant documents and include these in the record table for the EC sales list, will be provided.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>VAT 2010, place of service</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The EU guidelines 2008/8 will have effects on the processing of VAT for services transactions in Europe. Essential areas of change are, for example, the place of services and the transfer of tax due to the service recipient.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Prerequisites:</b><br /> <p>Prerequisites for the posting of documents that are relevant for the EC sales list:</p> <UL><LI>The document is not a template document (FKKKO-XMBEL = SPACE).</LI></UL> <UL><LI>The line item is not statistical (FKKOP-STAKZ = SPACE).</LI></UL> <UL><LI>The line item is not a down payment or down payment request (FKKOP-XANZA = SPACE).</LI></UL> <UL><LI>The line item contains a value-added tax code (FKKOP-MWSKZ &lt;&gt; SPACE).</LI></UL> <UL><LI>The value-added tax code contains an EU code. Define this in FI Customizing for the properties of the value-added tax code (transaction FTXP).</LI></UL> <UL><LI>After the functions for the EC sales list in FI-CA exist and have been implemented, you must activate the recording for every company code.</LI></UL> <p></p> <b>Additional information:</b><br /> <p>Additional information for the posting of documents that are relevant for the EC sales list:</p> <UL><LI>To ensure a data record is considered for the EC sales list, the business partner of the relevant line item must have defined a VAT ID number in the master data.</LI></UL> <UL><LI>The country of the business partner (that is used for the determination of the VAT ID number) is determined as follows: If the country that receives the service is filled in the business partner item of the document (FKKOP-LANDL), this country is used. If the country that receives the service is not filled, the country is determined from the address of the business partner.</LI></UL> <p></p> <b>Functions</b><br /> <p>The functions for the EC sales list will be available at the beginning of 2010. The following information can already be provided:</p> <UL><LI>New Customizing will be provided to activate the update of data for the EC sales list. The update can be activated for every company code. In the recording of the data for the EC sales list, the system will only consider postings to company codes that have been activated for the EC sales list.</LI></UL> <UL><LI>The data for the EC sales list will be updated during the posting and reversal of relevant documents in a new record table.</LI></UL> <UL><LI>An initialization report is available for relevant postings that were made before the new functions were implemented. This report examines the documents, and transfers them to the new record table if the prerequisites above are fulfilled. In addition, the system only considers business partners that have defined a VAT ID number in the master data.</LI></UL> <UL><LI>The EC sales list is created by the FI reports RFASLD20, RFASLD02 and RFASLM00. These reports call the FI-CA data using an RFC call.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-M-CA (Contract Accounts Receivable and Payable)"}, {"Key": "Other Components", "Value": "FI-CAX (Non-industry specific contract accounts receivable, payable)"}, {"Key": "Other Components", "Value": "IS-T-CA (Contract Accounting)"}, {"Key": "Other Components", "Value": "FS-CD (Collections and Disbursements)"}, {"Key": "Other Components", "Value": "IS-PS-CA (Public Sector Contract Accounting)"}, {"Key": "Other Components", "Value": "IS-U-CA (Contract Accounts Receivable and Payable)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036557)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036182)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1494352", "RefComponent": "XX-CSC-SI-IS-U", "RefTitle": "Slovenian specific functionality for IS-UT 604", "RefUrl": "/notes/1494352"}, {"RefNumber": "1487163", "RefComponent": "XX-CSC-AT-FI", "RefTitle": "Austria: EC VAT 2010 / EC Sales List enhancement for FI-CA", "RefUrl": "/notes/1487163"}, {"RefNumber": "1417611", "RefComponent": "FI-GL-GL-F", "RefTitle": "Composite note for VAT 2010", "RefUrl": "/notes/1417611"}, {"RefNumber": "1415060", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FI-CA: EC sales list", "RefUrl": "/notes/1415060"}, {"RefNumber": "1413492", "RefComponent": "FI-GL-GL-F", "RefTitle": "VAT 2010: Data transfer from FI-CA for EC sales lists", "RefUrl": "/notes/1413492"}, {"RefNumber": "1412883", "RefComponent": "IS-M-BF-PR", "RefTitle": "EU tax package 2010: Periodical Sales & Distribution billing", "RefUrl": "/notes/1412883"}, {"RefNumber": "1298748", "RefComponent": "FI-GL-GL-F", "RefTitle": "EU package for value-added tax, 2010", "RefUrl": "/notes/1298748"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1494352", "RefComponent": "XX-CSC-SI-IS-U", "RefTitle": "Slovenian specific functionality for IS-UT 604", "RefUrl": "/notes/1494352 "}, {"RefNumber": "1487163", "RefComponent": "XX-CSC-AT-FI", "RefTitle": "Austria: EC VAT 2010 / EC Sales List enhancement for FI-CA", "RefUrl": "/notes/1487163 "}, {"RefNumber": "1415060", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FI-CA: EC sales list", "RefUrl": "/notes/1415060 "}, {"RefNumber": "1413492", "RefComponent": "FI-GL-GL-F", "RefTitle": "VAT 2010: Data transfer from FI-CA for EC sales lists", "RefUrl": "/notes/1413492 "}, {"RefNumber": "1412883", "RefComponent": "IS-M-BF-PR", "RefTitle": "EU tax package 2010: Periodical Sales & Distribution billing", "RefUrl": "/notes/1412883 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FI-CA", "From": "471", "To": "471", "Subsequent": ""}, {"SoftwareComponent": "FI-CA", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "FI-CA", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "FI-CA", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "FI-CA", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "FI-CA", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}