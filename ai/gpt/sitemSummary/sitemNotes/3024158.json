{"Request": {"Number": "3024158", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 225, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000218472021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003024158?language=E&token=DC7848C36B9AE5E93437BAE1F047F4C4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003024158", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003024158/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3024158"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2023-07-05"}, "SAPComponentKey": {"_label": "Component", "value": "XX-S4C-OPR-INC"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Cloud Availability, Performance and Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Cloud", "value": "XX-S4C", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-S4C*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Cloud Main entry for Operations", "value": "XX-S4C-OPR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-S4C-OPR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Cloud Availability, Performance and Administration", "value": "XX-S4C-OPR-INC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-S4C-OPR-INC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3024158 - Blue-Green Deployment for Hotfix Collection for RISE with SAP S/4HANA Cloud"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are planning and coordinating operations related tasks, and/or are responsible for Business Configuration, and/or Extensibility for SAP S/4HANA Cloud and are looking for information on the Hotfix Collection process.</p>\r\n<p>This note provides information on changes related to the Hotfix patching for SAP S/4HANA Cloud. This SAP Note is subject to change, please mark it as a favorite and please check it regularly for updates.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RISE with SAP S/4HANA Cloud, SAP S/4HANA Cloud, essentials edition, Hotfix, Transport</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This applies to all customers subscribed to RISE with SAP S/4HANA Cloud, formerly known as SAP S/4HANA Cloud, essentials edition or SAP S/4HANA Cloud.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>With release 2105 Hotfix Collection 04, we introduce&#160;<strong>Blue-Green Software Deployment model</strong> for all RISE with SAP S/4HANA Cloud 3-System Landscape tenant types and RISE with SAP S/4HANA Cloud 2-System Landscape production tenants.</p>\r\n<p>For the RISE with SAP S/4HANA Cloud 2-System Landscape Quality and other non-productive system types, the <strong>standard deployment model </strong>(see <a target=\"_blank\" href=\"/notes/3042314\">Note 3042314</a>) remains.</p>\r\n<p>With <strong>Blue-Green Software Deployment</strong>, downtime for the productive tenant of SAP S/4HANA Cloud will be drastically reduced.</p>\r\n<p>SAP is reducing the actual average downtime for production from 2-3 hours to 1 hour. Over time, we will see further optimizations in the downtime for production.</p>\r\n<p><strong>Biweekly Contractual Maintenance Periods (CMP) and&#160;<strong>Limitations for customer activities during Hotfix Collection deployments (*)</strong>:</strong></p>\r\n<p>Please refer to the maintenance schedule (<a target=\"_blank\" href=\"https://www.sap.com/documents/2017/01/867629d8-a27c-0010-82c7-eda71af511fa.html\">here</a>) to be aware of the upcoming downtimes.</p>\r\n<p>For all&#160;RISE with SAP S/4HANA Cloud <strong>3-System Landscape</strong>&#160;Test and Production tenants and&#160;<strong>2-System Landscape</strong> Production tenants&#160;:</p>\r\n<div class=\"myNNFV2-container\">\r\n<div class=\"myNNFV2-table-responsive\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"myNNFV2-table\"><colgroup><col width=\"72\" /><col span=\"2\" width=\"244\" /></colgroup>\r\n<tbody>\r\n<tr>\r\n<td class=\"oa1\" height=\"36\" width=\"72\">\r\n<p>&#160; &#160; &#160; Phase</p>\r\n<p>Region</p>\r\n</td>\r\n<td class=\"oa1\" width=\"244\">\r\n<p>Uptime</p>\r\n</td>\r\n<td class=\"oa1\" width=\"244\">\r\n<p>Downtime*</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa1\" height=\"36\" width=\"72\"></td>\r\n<td class=\"oa1\" width=\"244\">\r\n<p>Limitations:</p>\r\n<ul>\r\n<li>No imports</li>\r\n<li>No Data migration (\"Migrate your data\" app is in read-only mode)</li>\r\n</ul>\r\n</td>\r\n<td class=\"oa1\" width=\"244\">\r\n<p>Limitations:</p>\r\n<ul>\r\n<li>No logon&#160;to system</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>MENA</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>FRI 04:00 UTC to FRI 19:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>FRI 19:00 UTC to 23:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>APJ</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>SAT 00:00 UTC to SAT 15:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>SAT 15:00 UTC to 19:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"29\" width=\"72\">\r\n<p>EMEA</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>SAT 07:00 UTC to SAT 22:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>SAT 22:00 UTC to SUN 02:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>AMER</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>SAT 13:00 UTC to SUN 04:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"244\">\r\n<p>SUN 04:00 UTC to 08:00 UTC</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>For&#160;all&#160;RISE with SAP S/4HANA Cloud&#160;<strong>3-System Landscape</strong>&#160;Development, Starter, Partner Demo tenants:</p>\r\n</div>\r\n</div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"myNNFV2-table\">\r\n<tbody>\r\n<tr>\r\n<td class=\"oa1\" height=\"36\" width=\"72\">\r\n<p>&#160; &#160; Phase</p>\r\n<p>Region</p>\r\n</td>\r\n<td class=\"oa1\" width=\"507\">\r\n<p>Uptime part 1</p>\r\n</td>\r\n<td class=\"oa1\" width=\"239\">\r\n<p>Uptime part 2</p>\r\n</td>\r\n<td class=\"oa1\" width=\"239\">\r\n<p>Downtime**</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa1\" height=\"36\" width=\"72\"></td>\r\n<td class=\"oa1\" width=\"507\">\r\n<p>Limitations:</p>\r\n<ul>\r\n<li>No ATO transport processing</li>\r\n<li>No Data migration (\"Migrate your data\" app is in read-only mode)</li>\r\n</ul>\r\n</td>\r\n<td class=\"oa1\" width=\"239\">\r\n<p>Limitations:</p>\r\n<ul>\r\n<li>No ATO transport processing</li>\r\n<li>No Data migration (\"Migrate your data\" app is in read-only mode)</li>\r\n<li>Customizing read-only</li>\r\n</ul>\r\n</td>\r\n<td class=\"oa1\" width=\"239\">\r\n<p>Limitations:</p>\r\n<ul>\r\n<li>No logon to system</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>MENA</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>FRI 04:00 UTC to FRI 10:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>FRI 10:00 UTC to FRI 19:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>FRI 19:00 UTC to 23:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>APJ</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>SAT 00:00 UTC to SAT 06:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>SAT 06:00 UTC to SAT 15:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>SAT 15:00 UTC to 19:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"29\" width=\"72\">\r\n<p>EMEA</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>SAT 07:00 UTC to SAT 13:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>SAT 13:00 UTC to SAT 22:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>SAT 22:00 UTC to SUN 02:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>AMER</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>SAT 13:00 UTC to SAT 19:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>SAT 19:00 UTC to SUN 04:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>SUN 04:00 UTC to 08:00 UTC</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>* In case maintenance activities don&#8216;t require complete business downtime window, systems are released earlier.&#160;</strong></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D043592)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D043592)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003024158/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3038415", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/3038415"}, {"RefNumber": "3042314", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "Hotfix Collection deployment for RISE with SAP S/4HANA Cloud", "RefUrl": "/notes/3042314"}, {"RefNumber": "3040585", "RefComponent": "BC-SRV-APS-EXT-SL", "RefTitle": "In-APP Extensibility and ATO transports are temporarily disabled", "RefUrl": "/notes/3040585"}, {"RefNumber": "3037850", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "SAP S/4HANA Cloud Hotfix Collection: Restriction for the Maintenance of Tax Types", "RefUrl": "/notes/3037850"}, {"RefNumber": "3037798", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "SAP S/4HANA Cloud Hotfix Collection: Restriction for G/L Account Maintenance", "RefUrl": "/notes/3037798"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2538700", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration Cockpit - File/Staging (Cloud / SAPSCORE)", "RefUrl": "/notes/2538700 "}, {"RefNumber": "3042314", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "Hotfix Collection deployment for RISE with SAP S/4HANA Cloud", "RefUrl": "/notes/3042314 "}, {"RefNumber": "3040585", "RefComponent": "BC-SRV-APS-EXT-SL", "RefTitle": "In-APP Extensibility and ATO transports are temporarily disabled", "RefUrl": "/notes/3040585 "}, {"RefNumber": "3037798", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "SAP S/4HANA Cloud Hotfix Collection: Restriction for G/L Account Maintenance", "RefUrl": "/notes/3037798 "}, {"RefNumber": "3037850", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "SAP S/4HANA Cloud Hotfix Collection: Restriction for the Maintenance of Tax Types", "RefUrl": "/notes/3037850 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}