{"Request": {"Number": "1241741", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 349, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007257022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=B2B131FBAE71EB3109FE9F5DEFDBFBE4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1241741"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.12.2008"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-MIG-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "general ledger data transfer"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "general ledger migration", "value": "FI-GL-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "general ledger data transfer", "value": "FI-GL-MIG-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-MIG-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1241741 - Problems w/ 3KEH/FAGL3KEH, particularly in migration phase 1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>With the implementation of Note 1138325, as well as Notes 820121 and 832776, the update logic in classic Profit Center Accounting (PCA) has changed. This is particularly problematic in phase 1 of the migration if month-end closings have to be executed in classic Profit Center Accounting in the same way as for times in classic FI.<br /><br />With the implementation of Note 1146585, the system may update the incorrect line items in classic PCA in phase 1 of the migration. For example, balance sheet account items without a profit center may be updated in classic PCA even though the relevant account is not entered in transaction 3KEH. In the case of receivables and/or payables, a standard transfer of receivables and payables during phase 1 of the migration using transaction 1KEK would result in duplicate data in classic PCA.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>new G/L, NewGL, migration, FAGL_DEFPRCTR_DOCUMENT_CHECK, T8A30, FAGL3KEH<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Your current release is Release ERP 2005 (ECC 6.0) and new general ledger accounting is active (automatically active in the case of a new ERP installation).<br /><br />or<br /><br />You upgraded to Release ERP 2005 (ECC 6.0) and are performing a migration to new general ledger accounting. The migration Customizing is available in the relevant system, and the migration date is before the posting date. In phase 1 of the migration, the new general ledger is technically deactivated.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The corrections contained in this note replace the corrections from Notes 820121, 832776, 1138325 and 1146585. After you implement the corrections from this note, the following posting logic applies:</p> <UL><LI>If new general ledger accounting is NOT active, no migration is planned and the classic Profit Center Accounting is active, then the system analyzes transactions 3KEH and 3KEI. A derived profit center is posted in classic Profit Center Accounting and is also returned to the AC interface.</LI></UL> <UL><LI>If new general ledger accounting is ACTIVE and classic Profit Center Accounting is active, the system analyzes transactions 3KEH and 3KEI. A derived profit center is posted ONLY in classic Profit Center Accounting and is NOT returned to the AC interface. If you want to post the same profit center in FI and in classic Profit Center Accounting, read the following information.</LI></UL> <p><br />!!! On November 19, 2008, the changes of the program LPCRWF03 were included in the corrections because the profit center derived in transaction 3KEH or 3KEI has not been updated in classic Profit Center Accounting.<br />!!! After you have manually implemented this note, immediately implement Note 1288700.<br /><br /><br /><br /><br />The following statements apply for Release ERP 2005 if new general ledger accounting is active and classic Profit Center Accounting is active:</p> <UL><LI>The entries in transaction 3KEH control the transfer of line items to classic Profit Center Accounting. Transactions 3KEH and 3KEI are used to set a default profit center, but ONLY in classic Profit Center Accounting. The profit center derived in transactions 3KEH and 3KEI may not be returned to the AC interface for reasons explained in Note 1138325.</LI></UL> <UL><LI>If no profit center is entered in classic Profit Center Accounting for a line item with profit accounts that are not cost elements, the system analyzes transactions 3KEH (and 3KEI) up to Release ERP 2004 (ECC 5.0) first. If a profit center is still not set, the dummy profit center is set for several transactions (see the include LPCRWFO1: FORM dummy_fuellen). The dummy profit center can hinder document splitting. Therefore, with the implementation of Notes 820121 and 832776, the dummy profit center is no longer set and the line item is no longer transferred to classic Profit Center Accounting. By deimplementing Notes 820121 and 832776 in Release ECC 6.0, the dummy profit center is derived again and the line item is reposted. However, if new general ledger accounting is active, the dummy profit center is transferred to classic Profit Center Accounting ONLY and is NOT returned to the AC interface, regardless of whether or not document splitting is used.</LI></UL> <UL><LI>If you want to define a default profit center, you can do this using the new transaction FAGL3KEH or using the BAdI FAGL_3KEH_DEFPRCTR (as a replacement for transaction 3KEI). This has the following effects:</LI></UL> <UL><UL><LI>For these accounts, the profit center in classic PCA is identical to the profit center in the new general ledger.</LI></UL></UL> <UL><UL><LI>If you have activated document splitting for the profit center/segment, the default profit center prevents document splitting for balance sheet accounts (profit accounts are usually assigned). For this reason, you may only define a default profit center for accounts for which you do NOT expect a cause-related account assignment by document splitting. This logic does not depend on how the default profit center was set (transaction FAGL3KEH, FI substitution, manually). Therefore, consider carefully the balance sheet accounts for which you enter a default profit center and test this thoroughly.</LI></UL></UL> <p><br /><br />Reconciliation of classic Profit Center Accounting and profit center scenario in new general ledger accounting:<br />The split information from document splitting (cause-related splitting of the profit center/segment) is not available in classic PCA. If you activate new general ledger accounting with the profit center scenario or the segment scenario, and activate document splitting with the profit center or the segment, some line items in new general ledger accounting are posted with different profit centers than are possible in classic PCA. Reconciliation is no longer possible for these accounts. Reconciliation is usually queried. Therefore, we recommend that you deactivate classic Profit Center Accounting after the migration has been completed successfully (for more information, see Note 826357).<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-FL (Flexible Structures)"}, {"Key": "Other Components", "Value": "EC-PCA-ACT (Actual Data)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D025743)"}, {"Key": "Processor                                                                                           ", "Value": "I036502"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "832776", "RefComponent": "EC-PCA-ACT", "RefTitle": "Records with initial profit center are created", "RefUrl": "/notes/832776"}, {"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357"}, {"RefNumber": "820121", "RefComponent": "FI-GL", "RefTitle": "Document splitting for segment does not work", "RefUrl": "/notes/820121"}, {"RefNumber": "1288700", "RefComponent": "EC-PCA-ACT", "RefTitle": "Dump in LPCRWF01 when migration plan exists", "RefUrl": "/notes/1288700"}, {"RefNumber": "1146585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1146585"}, {"RefNumber": "1138325", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "3KEH in phase 1 (migration)", "RefUrl": "/notes/1138325"}, {"RefNumber": "1133659", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Deriving default profit center in phase 1 (migration)", "RefUrl": "/notes/1133659"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357 "}, {"RefNumber": "1146585", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "3KEH: Profit center for classic PCA in phase 1 (migration)", "RefUrl": "/notes/1146585 "}, {"RefNumber": "1138325", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "3KEH in phase 1 (migration)", "RefUrl": "/notes/1138325 "}, {"RefNumber": "1288700", "RefComponent": "EC-PCA-ACT", "RefTitle": "Dump in LPCRWF01 when migration plan exists", "RefUrl": "/notes/1288700 "}, {"RefNumber": "1133659", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Deriving default profit center in phase 1 (migration)", "RefUrl": "/notes/1133659 "}, {"RefNumber": "820121", "RefComponent": "FI-GL", "RefTitle": "Document splitting for segment does not work", "RefUrl": "/notes/820121 "}, {"RefNumber": "832776", "RefComponent": "EC-PCA-ACT", "RefTitle": "Records with initial profit center are created", "RefUrl": "/notes/832776 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60016", "URL": "/supportpackage/SAPKH60016"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60206", "URL": "/supportpackage/SAPKH60206"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60305", "URL": "/supportpackage/SAPKH60305"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60403", "URL": "/supportpackage/SAPKH60403"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/**********/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "604", "Number": "1138325 ", "URL": "/notes/1138325 ", "Title": "3KEH in phase 1 (migration)", "Component": "FI-GL-MIG-GL"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1288700", "RefTitle": "Dump in LPCRWF01 when migration plan exists", "RefUrl": "/notes/0001288700"}]}}}}}