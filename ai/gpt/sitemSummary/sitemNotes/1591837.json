{"Request": {"Number": "1591837", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 255, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017247992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001591837?language=E&token=51BDAE3D1A44F42415D5F3A904054AE8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001591837", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001591837/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1591837"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analyzing Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1591837 - How to analyze query results"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>A query displays unexpected numbers. You would like to analyze the query result in detail and also compare the numbers with the booked values in the cube.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RSRT, key figure definition, LISTCUBE, query explain, wrong data, no data</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br />Analyzing a query result can become difficult and time consuming. The following guidance should help you to find out the source of unexpected values for key figures and also reduce the time needed for the investigation.<br /><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CONTENTS</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>[I]</strong></td>\r\n<td><a target=\"_self\" href=\"#I\">Introduction/Summary</a></td>\r\n</tr>\r\n<tr>\r\n<td><strong>[II]</strong></td>\r\n<td><a target=\"_self\" href=\"#II\">Query Monitor (RSRT)</a></td>\r\n</tr>\r\n<tr>\r\n<td><strong>[II.a]</strong></td>\r\n<td><a target=\"_self\" href=\"#IIa\">Technical Information &amp; Special Features</a></td>\r\n</tr>\r\n<tr>\r\n<td><strong>[II.b]</strong></td>\r\n<td><a target=\"_self\" href=\"#IIb\">Execute &amp; Debug</a></td>\r\n</tr>\r\n<tr>\r\n<td><strong>[II.c]</strong></td>\r\n<td><a target=\"_self\" href=\"#IIc\">Key Figure Definition</a></td>\r\n</tr>\r\n<tr>\r\n<td><strong>[III]</strong></td>\r\n<td><a target=\"_self\" href=\"#III\">Query Simplification</a></td>\r\n</tr>\r\n<tr>\r\n<td><strong>[IV]</strong></td>\r\n<td><a target=\"_self\" href=\"#IV\">Comparing Query Result with InfoProvider Data (LISTCUBE)</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<ul>\r\n<li><strong><a target=\"_blank\" name=\"I\"></a>&#65279;[I] Introduction/Summary</strong></li>\r\n</ul>\r\n<p><br />There are many causes for wrong or unexpected query results. The problem may be located on the data base or in the OLAP engine, or caused by the usage of special OLAP features, aggregates, BIA or the OLAP cache, just to mention some of them. In order to find out whether a query works correctly it is often also necessary to resolve formulas into manageable pieces and compare the numbers displayed for the basic (infoprovider) key figures with the data in the cube. Such a detailed analysis of queries can be done with the transactions <strong>RSRT</strong> in a very convenient way. If you want to check the data in the infoprovider the transaction <strong>LISTCUBE</strong> is normally the best choice.<br />It's also essentiel to understand the OLAP features which are used in the query. There are some very helpful notes explaining most of the important OLAP functionalities, they are listed under point [II.a]. You can also learn more about the OLAP engine by analyzing delivered sample queries: please review note <a target=\"_blank\" href=\"/notes/1508237\">1508237</a> about the transaction RSFC.&#160;The following WIKI page provides lots of information to various OT topics<br /><br /><a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/x/dwC-Dg\">OLAP Technology<br /></a><br />The main goal of this note is to show how the transaction RSRT ('Query Monitor'), and in particular the so called 'Key Figure Definition', can help you to at least narrow down the problem (see also note <a target=\"_blank\" href=\"/notes/729459\">729459</a>).<br />In general its recommended to proceed as follows when analyzing a query result:</p>\r\n<ul>\r\n<ul>\r\n<li>use the RSRT function 'Execute in Safe Mode' in order to exclude some of the potential sources of errors (point II.b)</li>\r\n<li>try to simplify the query (note <a target=\"_blank\" href=\"/notes/1125883\">1125883</a>). In particular switch off the function 'Calculate Result as...' for the affected key figure (see note <a target=\"_blank\" href=\"/notes/1151957\">1151957</a>!) (point III)</li>\r\n<li>save a bookmark which then can be used for further analysis</li>\r\n<li>please assure that you know how all the remaining features of your simplified query work (point II.a)</li>\r\n<li>use the 'Key figure Definition' for getting a deeper insight regarding the various calculation steps and the relevant filter values of the key figure (point II.c)</li>\r\n<li>compare the values of the basic key figures with the data in the cube using the transaction LISTCUBE (point IV)</li>\r\n</ul>\r\n</ul>\r\n<p>This rough step-by-step descrition is explained in detail in the following.</p>\r\n<ul>\r\n<li><strong><a target=\"_blank\" name=\"II\"></a>&#65279;[II] Query Monitor (RSRT)</strong></li>\r\n</ul>\r\n<p><br />With the help of the query monitor you can run and analyze queries without a BW front end. It offers a lot of helpful features like the option of getting some technical information displayed about the query. A general description of almost all available functions to the Query Monitor can be found in the online documentation under the link:<br /><a target=\"_blank\" href=\"https://help.sap.com/viewer/93bea049296a41508d972e117149f784/7.5.8/en-US/a02a183d30805c59e10000000a114084.html\">Query Monitor<br /></a>The transaction offers different modes('QueryDisplay') which depend on the BW release. E.g. they are called List, BexAnalyzer, HTML and ABAP BICS. Choose either the HTML(for BW releases &lt;BW75) or ABAP BICS(for BW releases &gt;BW74) mode&#160;since&#160;they are&#160;the most suitable one for analyzing queries.&#160;They also provide the possibility to save bookmarks (button 'Bookmark'). This is especially useful when an issue only occurs after some navigational steps. See <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/display/BI/Transaction+RSRT%3A+Query+Monitor\">Transaction RSRT: Query Monitor</a>&#160;for further details.&#160;<br />In case you have defined your own 'standard BW3x web template' please specify the delivered template 0adhoc in the HTML mode with the parameter template_id=0adhoc (in order to assure that the functions 'Key Figure Definition' and 'Detail' are also offered in the context menu). <br />In the following we only discuss the most important functions of RSRT needed for analyzing queries.</p>\r\n<ul>\r\n<li><strong><a target=\"_blank\" name=\"IIa\"></a>&#65279;[II.a] Technical Information &amp; Special Features</strong></li>\r\n</ul>\r\n<p><br />The button 'Technical Information' leads to a list of technical details of the query definition. Among others the following points are important (in particular when its about wrong numbers):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"2\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Query defined on MultiProvider</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1832801\">1832801</a>, <a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/x/1wCHEw\">SDN WIKI Article</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Query defined on VirtualProvider</td>\r\n<td><a target=\"_blank\" href=\"/notes/1592982\">1592982</a></td>\r\n</tr>\r\n<tr>\r\n<td>Formulas with Exception Aggregation</td>\r\n<td><a target=\"_blank\" href=\"/notes/1151957\">1151957</a> and <a target=\"_blank\" href=\"/notes/1861667\">1861667</a></td>\r\n</tr>\r\n<tr>\r\n<td>Virtual Char./Key Figures</td>\r\n<td><a target=\"_blank\" href=\"/notes/1717880\">1717880</a></td>\r\n</tr>\r\n<tr>\r\n<td>Constant Selection</td>\r\n<td><a target=\"_blank\" href=\"/notes/834996\">834996</a> and <a target=\"_blank\" href=\"/notes/1924842\">1924842</a></td>\r\n</tr>\r\n<tr>\r\n<td>Non-Cumulative Flag</td>\r\n<td><a target=\"_blank\" href=\"/notes/1548125\">1548125</a></td>\r\n</tr>\r\n<tr>\r\n<td>Not Visible Drilldown Characteristics</td>\r\n<td><a target=\"_blank\" href=\"/notes/1151957\">1151957</a></td>\r\n</tr>\r\n<tr>\r\n<td>Access Type for Result Values</td>\r\n<td><a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/x/wIMREw\">SDN WIKI Article</a></td>\r\n</tr>\r\n<tr>\r\n<td>Characteristics with hierarchy \"as posted\"</td>\r\n<td><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw73/helpdata/en/03/eca042bde0c611e10000000a1550b0/content.htm\">SAP Online Documentation</a></td>\r\n</tr>\r\n<tr>\r\n<td>Key Figures with Currencies</td>\r\n<td><a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/x/LgBhEg\">SDN WIKI Article</a></td>\r\n</tr>\r\n<tr>\r\n<td>Key Figures with Units</td>\r\n<td><a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/x/EoMWFg\">SDN WIKI Article</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<ul>\r\n<li><strong><a target=\"_blank\" name=\"IIb\"></a>&#65279;[II.b] Execute &amp; Debug</strong></li>\r\n</ul>\r\n<p><br />This button provides the possiblity to switch on and off some features like the usage of aggregates or the OLAP cache. Moreover, you can select from a long list of default breakpoints for debugging, but a discussion of these breakpoints would go beyond the scope of this note. The following options are in general helpful:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"2\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Do Not Use Aggregates</td>\r\n<td>-&gt; exclude the usage of aggregates (which may be inconsistent)</td>\r\n</tr>\r\n<tr>\r\n<td>Do Not Use BIA Index</td>\r\n<td>-&gt; exclude the usage of an BWA index (which may be inconsistent)</td>\r\n</tr>\r\n<tr>\r\n<td>No Parallel Processing</td>\r\n<td>-&gt; check whether the result changes when the Data Manager runs the queries in serial mode</td>\r\n</tr>\r\n<tr>\r\n<td>Multiprovider Explain</td>\r\n<td>-&gt; see notes <a target=\"_blank\" href=\"/notes/489135\">489135 </a>and <a target=\"_blank\" href=\"/notes/1832801\">1832801</a>, <a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/x/1wCHEw\">SDN WIKI Article</a></td>\r\n</tr>\r\n<tr>\r\n<td>Display SQL Query</td>\r\n<td>-&gt; check whether the statement meets your expectations (above all the 'where clause')</td>\r\n</tr>\r\n<tr>\r\n<td>Deactivate DB Optimizer Functions</td>\r\n<td>-&gt; if set, the ORACLE optimizer skips some complex functions; see also note <a target=\"_blank\" href=\"/notes/1681396\">1681396</a></td>\r\n</tr>\r\n<tr>\r\n<td>Do Not Use Cache</td>\r\n<td>-&gt; exclude the usage of OLAP cache objects (which may be inconsistent)</td>\r\n</tr>\r\n<tr>\r\n<td>Display Statistic Data</td>\r\n<td>-&gt; gives a quick overview which partproviders, BIA indexes or aggregates were read; see also notes <a target=\"_blank\" href=\"/notes/1681396\">1681396 </a>and <a target=\"_blank\" href=\"/notes/1879725\">1879725</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />The following processing sequence (very rough overview!) could be helpful to know when analyzing a query:</p>\r\n<ul>\r\n<ul>\r\n<li><strong>(1) </strong>query processes all used variables</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong>(2) </strong>system checks whether proper OLAP cache entry exits, if yes, the system proceeds with point 5</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong>(3) </strong>the Data Manager retrieves the data from the infoprovider (fact tables, aggregates, virtual providers, BIA index,..). The subqueries assigned to the various partproviders are executed in parallel. The data is handed over to the OLAP engine in packages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong>(4)</strong>the data records are checked by the OLAP engine; amongs others the user exits for virtual characteristics and key figures are executed</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong>(5)</strong>OLAP engine carries out calculation of calculated key figures and other features like exception aggregation</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong>(6) </strong>list calculations like 'Calculate Result as..' are carried out</li>\r\n</ul>\r\n</ul>\r\n<p><br />At the right side of the button 'Execute&amp;Debug' there is the possibilty to choose the function 'Execute in Safe Mode'. This is a convinient way to switch off all the functions from above which could lead to wrong numbers. In case the numbers are different when using this function, you have to rerun the query several times and deactivate these features one after the other in order to find the one causing the issue.</p>\r\n<ul>\r\n<li><strong><a target=\"_blank\" name=\"IIc\"></a>&#65279;[II.c] Key Figure Definition</strong></li>\r\n</ul>\r\n<p><br />In case the 'Safe Mode' delivers the same result it's time for using the key figure definition for the affected cell (context menu). Lets discuss this with the help of an example:<br />Query A uses a calculated key figure CKF which delivers unexpected numbers. Lets assume the key figure is defined the following way:<br />CKF = CKF1 %A CKF2<br />CKF1 = KF / RKF1<br />CKF2 = KF / RKF2<br />KF is a basic key figure of the infoprovider, CKF1,2,3 are calculated key figures.<br /><br />The 'Key Figure Definition' displays the result of the calculation in a hierarchically structured way:<br /><br />&#160;&#160;&#160;&#160;CKF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;40 %<br />&#160;&#160;&#160;&#160;... %A ...&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;40 %<br />&#160;&#160;&#160;&#160;&#160;&#160; CKF1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 2<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;'KF' / 'RKF1'&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 2<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;KF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1000 PC<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;KF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1000 PC<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;RKF1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;500 PC<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;KF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 500 PC<br />&#160;&#160;&#160;&#160;&#160;&#160; CKF2&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 5<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;'KF' / 'RKF1'&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;5<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; KF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1000 PC<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;KF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1000 PC<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; RKF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;200 PC<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;KF&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 200 PC<br /><br />For each step of the calculation we get a seperate row. The values for the used operators of one step are always listed below.<br />E.g.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>'KF' / 'RKF1'</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>KF</td>\r\n<td>&nbsp;</td>\r\n<td>1000 PC</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>RKF1</td>\r\n<td>500 PC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>means that the key figure KF is devided by the key figure RKF1 and that the following calculation was carried out: 2 = 1000/500. The most important point now is to figure out where exactly in this chain of computations the problem occurs. This could lead to a simplified scenario where only a sub formula has to be analyzed. Or it turns out that already a basis key figure used in the formula has an unexpected value.<br />At the bottom of this hierarchy you can find the values for the basis key figures of the infoprovider. If you use the context menu and choose 'Details' you get more helpful information displayed like the technical name of the infoobject and the relevant filter values. This information depends on the level of the 'calculation hierarchy':<br />-only for the basis key figures you get the technical names displayed, for Calculated and Restricted Key Figures some internal information is displayed which is normally only useful for debugging<br />-the filter values may change if various Restricted Key Figures are used in the formula<br /><br /><strong>Example:</strong><br /><br />Description&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;Field Name&#160;&#160;&#160;&#160; Value<br />Technical Attributes<br />Exception Aggregation of Formulas&#160; &#160;LAGGR01&#160;&#160;&#160;&#160;&#160;&#160; MAX( 0COUNTRY )<br />InfoObject&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;IOBJNM&#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;0AMOUNT<br /><br /><br />InfoObject InfoObjectType&#160;&#160;SelectionType&#160;&#160;+/-Sign&#160;&#160;Operator&#160;&#160;From&#160;&#160; To<br /><strong>Selection of Drilldown Characteristics</strong><br />0COUNTRY&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CHA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Include&#160;&#160;&#160;&#160;&#160;&#160;=&#160;&#160;&#160;&#160;&#160;&#160; AT<br /><strong>Dynamic Filter</strong><br />Material&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;CHA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;3&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Include&#160;&#160;between&#160;&#160;&#160;&#160;T09,&#160;&#160;T12<br /><strong>Fixed Filter</strong><br />0COUNTRY&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CHA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;3&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Include&#160;&#160;between&#160;&#160;&#160; AT,&#160;&#160; IE<br />0CALMONTH&#160;&#160;&#160;&#160;&#160;&#160; TIM&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Include&#160;&#160;&#160;&#160;&#160;&#160;=&#160;&#160;&#160;&#160;&#160;&#160; 10.2010<br /><strong>Selection 0003</strong><br />Material&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;CHA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Include&#160;&#160;&#160;&#160;&#160;&#160;=&#160;&#160;&#160;&#160;&#160;&#160; T09<br /><br />These filter restrictions can be used to compare the basic key figure result with the data in the infoprovider. Please note that you can see in this list which kind of filter restriction it actually is. We distinguish between 'global' filters (Selection of Drilldown Characteristics, Dynamic Filter, Fixed Filter) and 'local' filters (Selection 0003) from restricted key figures. Global filters are applied on all key figures (all structure elements/cells), the local filters are only relevant for the corresponding structure element. The intersection of these filter values are finally relevant for a certain cell.<br />Hence, all these filter values (from our example above) for a certain infoobject have to be connected with a logical 'AND'. This leads to the following filter:<br /><br />Material = T09&#160;&#160; (intersection of dynamic filter and local selection)<br />0Country = AT&#160;&#160;&#160;&#160;(intersection of fixed filter and drilldown char.)<br />0CALMONTH = 10.2010 (fixed filter)<br /><br />For the local selection the final set of all relevant local filter values (e.g. nested restricted key figures) are displayed. Please review note <a target=\"_blank\" href=\"/notes/351827\">351827 </a>where this procedure is discussed in detail for structure elements. E.g. please note:<br />-in case of excludes you have to know exactly how the system proceeds in order to avoid confusion.<br />-if you have more 'local' filter values for a key figure on different levels the system connects them with 'OR'!<br /><br /><strong>Restrictions:</strong><br /><br />Please note that the key figure definition cannot handle all situations and hence might display wrong numbers. Then you get warnings like the following which are issued when the function SUMGT is used in a key figure:<br />'The key figure definition shows another value for SUMGT(...)'(BRAIN 198)<br />'The key figure definition may not display the correct value for Formula ...'(BRAIN 197)<br />The key figure definition is kind of a simplified query valid only for a certain cell (see also note <a target=\"_blank\" href=\"729459\">729459</a>). Since SUMGT is supposed to return the overall result of the operand (meaning it refers to values from other cells) the key figure definition does not caluculate the right value.<br />Other examples:<br />-Calculate result as....<br />-SUMCT, SUMRT, %GT, %CT</p>\r\n<ul>\r\n<li><strong><a target=\"_blank\" name=\"III\"></a>&#65279;[III] Query Simplification</strong></li>\r\n</ul>\r\n<p><br />Please follow note <a target=\"_blank\" href=\"/notes/1125883\">1125883 </a>and try to simplify the query as much as possible. In case you have identified a suspicious sub formula in the key figure definition, please make a new key figure out of it in order to reduce the complexity of the formula.<br />In any case please check whether the functions 'Calculate Result as...', 'Calculate Single Values as...' or Cumulate are used for the affected key figure (see note <a target=\"_blank\" href=\"/notes/1151957\">1151957</a>)! You must deactive them in order to interpret the calculation of formulas correctly. Please also note that these so called list operations are not taken into account by the key figure definition !<br />In general its also recommended to switch off zero suppression and conditions.</p>\r\n<ul>\r\n<li><strong><a target=\"_blank\" name=\"IV\"></a>&#65279;[IV] Comparing Query Result with InfoProvider Data (LISTCUBE)</strong></li>\r\n</ul>\r\n<p><br />The most convenient way to check the values of basis key figures is to use the transaction <strong>LISTCUBE. </strong>Its recommended to choose the display of technical names of the infoobjects. If you enter a name under 'Name of ABAP Program' (note <a target=\"_blank\" href=\"/notes/1479893\">1479893</a>) the transaction generates a ABAP program that assembles the selection screen for the InfoProvider request. Such an explicit program name can be used to save variants and this is very useful in case of repeated identical requests (e.g. in the framework of an Support Message this can be very useful! In note <a target=\"_blank\" href=\"/notes/2055174\">2055174</a> you can&#160;find a detailed description with screenshots how to create a listcube report)&#160;). If the program name is not explicitly specified, a name is generated and the program is automatically deleted when the transaction ends.<br />Please carefully enter all the filter values (see point 'key figure definition') into this transaction and check whether the value from the query is confirmed. Please note that this transaction cannot handle hierarchy node restrictions. Hence, such restrictions have to be replaced (for analyzing purposes) in the query. If the value # (not assigned, SID=0) is used as a filter value its necessary to use the explicit selection option '=' for 'blank'.<br />Other important functions/flags:</p>\r\n<ul>\r\n<ul>\r\n<li>'Field Selection for Output': you can specify which infoobjects should be drilled down</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'Execute&amp;Debug': a part of the functions are offered which we know already from the same button in RSRT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'Use mat. aggregates': the usage of aggregates can be switched off</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'Use DB aggregation': if flagged, aggregation takes place over non selected fields</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'Max number of hits': the default value is 200; it might by necessary to increase this number (e.g. to get data from all partptoviders of an multicube displayed)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'display SQL query /Execution plan' - same function as in RSRT</li>\r\n</ul>\r\n</ul>\r\n<p><br />In case the infoprovider is a basis cube with non-cumulative key figures please take into account that the reference points are only displayed when you explicitly set the restriction 0recordtp=1 (see note <a target=\"_blank\" href=\"/notes/1548125\">1548125</a>).<br />In case of an virtual cube please review note <a target=\"_blank\" href=\"/notes/1592982\">1592982</a>.<br />Please note that LISTCUBE does not automatically restrict the infoobject <strong>0REQUID, </strong>you get also data from requests displayed which are not availabel for reporting! Hence it might be necessary to set a corresponding filter by hand in order to use the correct data set (for the comparision with a query).<br /><br /><br /><br /><br /><br /></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I022439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I022439)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001591837/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001591837/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2055174", "RefComponent": "BW-BEX-OT", "RefTitle": "How to create a listcube-report out of a keyfigure-definition shown in transaction RSRT", "RefUrl": "/notes/2055174"}, {"RefNumber": "891029", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSR00002: Virtual chars and key figures in reporting", "RefUrl": "/notes/891029"}, {"RefNumber": "834996", "RefComponent": "BW-BEX-OT", "RefTitle": "Probleme mit der Eigenschaft 'Konstante Selektion'", "RefUrl": "/notes/834996"}, {"RefNumber": "729459", "RefComponent": "BW-BEX-OT", "RefTitle": "Unexpected number", "RefUrl": "/notes/729459"}, {"RefNumber": "489135", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "MultiCube/MultiProvider \"Explain\" via RSRT", "RefUrl": "/notes/489135"}, {"RefNumber": "442987", "RefComponent": "BW-BEX-OT", "RefTitle": "Decimal places/rounding units of measure", "RefUrl": "/notes/442987"}, {"RefNumber": "351827", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data for multiple selection?", "RefUrl": "/notes/351827"}, {"RefNumber": "1717880", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual Key Figures & Characteristics in BW Queries", "RefUrl": "/notes/1717880"}, {"RefNumber": "1681396", "RefComponent": "BW-BEX-OT", "RefTitle": "Query Performance", "RefUrl": "/notes/1681396"}, {"RefNumber": "1592982", "RefComponent": "BW-BEX-OT-VC", "RefTitle": "VC: Query selections not passed to virtual cube", "RefUrl": "/notes/1592982"}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125"}, {"RefNumber": "1485729", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unposted values", "RefUrl": "/notes/1485729"}, {"RefNumber": "1479893", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE improvements", "RefUrl": "/notes/1479893"}, {"RefNumber": "1240163", "RefComponent": "BW-BEX", "RefTitle": "Amount too high by factor of 100 for currency HUF, JPY, KRW, JOD, CLP, IDR", "RefUrl": "/notes/1240163"}, {"RefNumber": "1151957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Interesting facts about the OLAP Processor/Analytic Engine", "RefUrl": "/notes/1151957"}, {"RefNumber": "1125883", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Simplified example query for analyzing problems", "RefUrl": "/notes/1125883"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2081346", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BW Queries: Calculations with Key Figures of type Date", "RefUrl": "/notes/2081346 "}, {"RefNumber": "1970714", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BW Query: Restricting Compounded Characteristics", "RefUrl": "/notes/1970714 "}, {"RefNumber": "2491946", "RefComponent": "BW-BEX-OT", "RefTitle": "Filters and Selections in Bex queries: Frequent Issues and Questions", "RefUrl": "/notes/2491946 "}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125 "}, {"RefNumber": "1240163", "RefComponent": "BW-BEX", "RefTitle": "Amount too high by factor of 100 for currency HUF, JPY, KRW, JOD, CLP, IDR", "RefUrl": "/notes/1240163 "}, {"RefNumber": "1717880", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual Key Figures & Characteristics in BW Queries", "RefUrl": "/notes/1717880 "}, {"RefNumber": "1151957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Interesting facts about the OLAP Processor/Analytic Engine", "RefUrl": "/notes/1151957 "}, {"RefNumber": "351827", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data for multiple selection?", "RefUrl": "/notes/351827 "}, {"RefNumber": "1681396", "RefComponent": "BW-BEX-OT", "RefTitle": "Query Performance", "RefUrl": "/notes/1681396 "}, {"RefNumber": "1592982", "RefComponent": "BW-BEX-OT-VC", "RefTitle": "VC: Query selections not passed to virtual cube", "RefUrl": "/notes/1592982 "}, {"RefNumber": "1485729", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unposted values", "RefUrl": "/notes/1485729 "}, {"RefNumber": "834996", "RefComponent": "BW-BEX-OT", "RefTitle": "Probleme mit der Eigenschaft 'Konstante Selektion'", "RefUrl": "/notes/834996 "}, {"RefNumber": "1479893", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE improvements", "RefUrl": "/notes/1479893 "}, {"RefNumber": "729459", "RefComponent": "BW-BEX-OT", "RefTitle": "Unexpected number", "RefUrl": "/notes/729459 "}, {"RefNumber": "1125883", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Simplified example query for analyzing problems", "RefUrl": "/notes/1125883 "}, {"RefNumber": "891029", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSR00002: Virtual chars and key figures in reporting", "RefUrl": "/notes/891029 "}, {"RefNumber": "442987", "RefComponent": "BW-BEX-OT", "RefTitle": "Decimal places/rounding units of measure", "RefUrl": "/notes/442987 "}, {"RefNumber": "489135", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "MultiCube/MultiProvider \"Explain\" via RSRT", "RefUrl": "/notes/489135 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}