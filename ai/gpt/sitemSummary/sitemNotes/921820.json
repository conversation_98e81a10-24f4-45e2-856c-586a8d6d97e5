{"Request": {"Number": "921820", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 960, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016054322017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=0E9516CB4B25EFFF49B94B3E95EED441"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "921820"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.09.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorizations"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorizations", "value": "BW-BEX-OT-OLAP-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "921820 - Information about authorization concept of BW 3.X systems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes the most important concepts and rules of the authorization check in the BW system. The information provided should help you analyze problems and create new BW authorizations. You can find further details about some of the specified points in the online documentation: help.sap.com - Documentation - SAP NetWeaver 2004 - Information Integration - SAP BW - Data Warehousing - Data Warehouse Management - Authorizations.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FAQ<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There are two different types of authorizations that play a role in BW: The reporting authorizations (that are checked in the OLAP processor) and the RS authorizations. The reporting authorization objects are defined in transaction RSSM and allow you to assign specific authorizations with reference to the data displayed in queries. The RS authorizations use delivered authorization objects which you can use, for example, to control the execution of queries (S_RS_COMP). You can find more information about this in Note 846839. This note deals only with <B>reporting</B> authorizations.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>(I)</B> We assume that you know the meaning of the following terms: Authorization object, field of an authorization object, authorization, authorization profile and role. For an InfoObject to be checked during the execution of a query, it must be authorization-relevant (see transaction RSD1 - 'Business Explorer' tab page) and it must exist in an authorization object. This authorization object must then be activated with reference to the required InfoProvider (RSSM - 'Checks for InfoProvider').<br /><br /><B>(II) </B>Authorization check:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>a.</B> When you execute a query, the system checks all authorization objects that are relevant, that is, all those that are activated for the InfoProvider (point I).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>b.</B> Each one of these authorization objects is checked <B>separately.</B> Only if they are <B>all</B> checked with a positive result, does the system display the data of the query (AND link for authorization objects).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>c.</B> The required selections (of the query) are compared with the existing authorizations (in the profiles) for each authorization object. If, for example, the query is restricted to a certain specification of an authorization-relevant InfoObject, the system searches for this exact value in the authorizations. In this case, it is sufficient if only one of the existing authorizations (in a certain profile) meets the requirements (OR link for authorizations).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>d.</B> During the check of one of these authorizations, <B> all</B> fields of the authorization object must be checked with a positive result <B>simultaneously</B> (AND link for check of the fields). This means that you can assign authorizations for certain characteristic value <B>combinations</B>. This is explained in more detail in point VIII.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>e.</B> During the check of a field in the authorization object (-&gt; corresponds to an InfoObject in the cube in most cases), the system first checks whether a satisfactory authorization exists with reference to the values of the InfoObject ('Flat list'). If this is not the case, the system uses hierarchy node authorizations (OR link for the check). The hierarchy authorizations are&#x00A0;&#x00A0;assigned to the 0TCTAUTHH technical InfoObject which must also exist in the authorization object (see also the online documentation (as described above) under: 'Maintain authorizations for hierarchies')<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><B>(III)</B> Hierarchy authorization: As mentioned above, the authorization with reference to hierarchies is controlled using the 0TCTAUTHH field. It is important that the check of hierarchy authorizations cannot be 'activated and deactivated'. If an InfoObject is 'authorization-relevant', when you are using hierarchies (for this InfoObject in a query), the system always checks for the corresponding hierarchy authorizations. If no hierarchy authorization exists, the query cannot be authorized.<br />Note: Hierarchy authorizations are \"stronger\" than 'flat list' authorizations. This means that a hierarchy authorization can authorize a flat selection in the query (if the single values are leaves of the authorized node). However, on the other hand, a 'flat list' authorization can <B>never</B> authorize a displayed hierarchy in the query.<br /><br /><B>(IV)</B> Authorizations do not work automatically as filters in a query. That is, the InfoObjects must be limited according to the authorizations of the user since otherwise the system issues the message 'No authorization'. You can implement an 'automatic function' for this by using authorization variables. These are variables that are filled automatically with the authorized values of the respective user (Processing type: Authorization). (Special case: See Note 668520)<br />In some cases, a query may return the expected result when you are using a hierarchy even without using authorization variables for restricted users. However, we <B>strongly</B> recommend<br />that you also use authorization variables for hierarchies.<br />If problems occur when using authorization variables, you must first determine the following: Were the variables filled incorrectly or was the resultant query selection checked incorrectly? First, replace the variable in the query with a fixed selection. You can find further information about this in the authorization log (see point X).<br />Authorization variables cannot take the colon authorization&#x00A0;&#x00A0;(see point VII) into account because when the variables are determined, it is not known whether or not a characteristic is drilled down.<br /><br /><B>(V)</B> Authorizations with reference to display attributes: Here, the principle applies that: An authorization-relevant display attribute is only authorized when the user has complete authorization for it! For more detailed information, see note 761089.<br /><br /><B>(VI</B>) Compatibility mode: For further information, you MUST read Note 728077.<br /><br /><B>(VII) </B>Colon as authorized value: This authorization is necessary to view the values of an authorization-relevant characteristic in aggregated form. You can find further information about this in Note 727354.<br /><br /><B>(VIII) </B>Multi-dimensional authorizations: As mentioned under II.d, you can authorize characteristic<B> combinations</B> if the authorization object has several fields. This is an important point which can also lead to conceptional problems.&#x00A0;&#x00A0;For further information about 'multi-dimensional authorizations', you MUST read Note 668520.<br /><br /><B>(IX)</B> Using exclude selections in queries: The following basic principle applies: An EXCLUDE selection is only authorized if the user has a full star authorization (\"All values\"). This check is \"too strict\"; however converting an 'excluding' query selection (EXCLUDE) into the corresponding inverse INCLUDE selection, could lead to considerable performance problems. Note 860488 describes an exception to this rule.<br /><br /><B>(X)</B> Authorization log: If you want to analyze the authorization check of reporting authorizations, you must use the authorization log, which you can activate in transaction RSSM. The information specified there is very helpful when you are analyzing problems and helps you to understand the authorization check better. For more information, you MUST read Note 790323 where you can find detailed explanations for the log. Also consider that there is no information for the check of reporting authorizations in the authorization trace of transactions ST01 and SU53 (see Note (846839).<br /><br /><B>(XI)</B> In an authorization profile, you have the option of using variables that are filled when you execute the query using a customer exit (called with I_STEP=0). The name of such a variable is formed from the character $ and the technical name of the variable, for example, $USERAUTH. Use the Query Designer to create the corresponding variable USERAUTH.<br />Important: Do <B>not</B> simultaneously use a variable of this type as a 'normal' selection variable in the query. This may work sometimes, but not always. You can find further information about this in the online documentation (see above) under 'Authorizations with variables'.<br /><br /><B>(XII)</B> Generation of authorizations: For more information, see Note 824500.<br /><br /><B>(XIII)</B> For queries on MultiProviders, you must activate the relevant authorization objects for this MultiProvider (in transaction RSSM). The setting for individual basis providers is not relevant.<br /><br /><B>(XIV)</B> When you create new InfoProviders, the system automatically activates all authorization objects that may be relevant for this InfoProvider. That is, the data in the cube is saved with regard to the reporting authorizations (Note 746811). Change these settings subsequently if required.<br /><br /><B>(XV)</B> Authorizations of compound characteristics: For more information, see Note 967403.<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I022439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I022439)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "967403", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Problems concerning compounding and analysis authorizations", "RefUrl": "/notes/967403"}, {"RefNumber": "955146", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSSM: Infoprovider not available for checking", "RefUrl": "/notes/955146"}, {"RefNumber": "860488", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check for selections with Excluding", "RefUrl": "/notes/860488"}, {"RefNumber": "846839", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Types of Authorizations in BW", "RefUrl": "/notes/846839"}, {"RefNumber": "790323", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The log for reporting authorizations in BW", "RefUrl": "/notes/790323"}, {"RefNumber": "761089", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization of display attributes", "RefUrl": "/notes/761089"}, {"RefNumber": "728077", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Compatibility modes in reporting authorization (only BW3.x)", "RefUrl": "/notes/728077"}, {"RefNumber": "727354", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Colon authorization during query execution", "RefUrl": "/notes/727354"}, {"RefNumber": "668520", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Multi-Dimensional Authorizations and Variables", "RefUrl": "/notes/668520"}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}, {"RefNumber": "727354", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Colon authorization during query execution", "RefUrl": "/notes/727354 "}, {"RefNumber": "728077", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Compatibility modes in reporting authorization (only BW3.x)", "RefUrl": "/notes/728077 "}, {"RefNumber": "668520", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Multi-Dimensional Authorizations and Variables", "RefUrl": "/notes/668520 "}, {"RefNumber": "846839", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Types of Authorizations in BW", "RefUrl": "/notes/846839 "}, {"RefNumber": "967403", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Problems concerning compounding and analysis authorizations", "RefUrl": "/notes/967403 "}, {"RefNumber": "955146", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSSM: Infoprovider not available for checking", "RefUrl": "/notes/955146 "}, {"RefNumber": "790323", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The log for reporting authorizations in BW", "RefUrl": "/notes/790323 "}, {"RefNumber": "860488", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check for selections with Excluding", "RefUrl": "/notes/860488 "}, {"RefNumber": "761089", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization of display attributes", "RefUrl": "/notes/761089 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}