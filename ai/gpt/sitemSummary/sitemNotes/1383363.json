{"Request": {"Number": "1383363", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 565, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008157902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001383363?language=E&token=AF202502D443F3137B9C254359C59E37"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001383363", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001383363/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1383363"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2009"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-JP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Japan"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Japan", "value": "RE-FX-LC-JP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-JP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1383363 - Agent commission in the payment charges report"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have run the Payment Charges Report for Japan (RE-FX Localization for Japan) and experienced the following issues:</p> <UL><LI>It does not allow the user to choose whether the agent commission shall be reported in the form 313 together with the payment charges or in the form 314 agent commission report.</LI></UL> <UL><LI>The agent commission is included in the summary payment charges report.</LI></UL> <UL><LI>Only those agent commissions are included that are below the payment limit. These should not be included, instead, the agent commissions above the payment limit has to be included.</LI></UL> <UL><LI>Although correctly the Rent payment category in case of Corporations is not taken into account for the payment charges report, it should be taken into account for the total number of payees and for the total payments amount, but it is not.</LI></UL> <UL><LI>Amounts equal to the payment limit are incorrectly included in the report.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, Localization, Japan, Payment Charges Report, REXCJPPAYMENTREP, Summary report, Agent commission, Payment limit, Land rent for corporation, Building Rent for corporation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>After you installed this note, the following modification will take place:</p> <UL><LI>The \"Define Company-Specific Data for Payment Report\" customizing activity was extended with a new checkbox: \"Report Agent Commission\".</LI></UL> <UL><LI>If you select the \"Report Agent Commission\" checkbox in customizing, the system calculates the agent commission and includes it in the Payment Charge Report for real estate charges (Form 313).</LI></UL> <UL><LI>Only the agent commissions above the payment limit are included in the payment charges report.</LI></UL> <UL><LI>In the summary report will not include the agent commission anymore.</LI></UL> <UL><LI>The payment categories rent for buildings and rent for lands are taken into account in the summary reports total number of payee and total payment amounts (regardless the payment limit)</LI></UL> <UL><LI>Total amounts equal to the payment limit are not included in the report anymore.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>First carry out the described manual steps and then install the implied correction instruction.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001383363/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001383363/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001383363/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001383363/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001383363/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001383363/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001383363/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001383363/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001383363/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Real_Estate_Payment_Charges.pdf", "FileSize": "82", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000387982009&iv_version=0004&iv_guid=********************************"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175"}, {"RefNumber": "1475800", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: Some records not taken into account", "RefUrl": "/notes/1475800"}, {"RefNumber": "1455668", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charge Report running the ALV in background", "RefUrl": "/notes/1455668"}, {"RefNumber": "1451291", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report for Japan - New Grouping", "RefUrl": "/notes/1451291"}, {"RefNumber": "1445195", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Handling Multi-landlord-agent in REXCJPPAYMENTREP", "RefUrl": "/notes/1445195"}, {"RefNumber": "1413531", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charge Report- agent comm., address, BP cat, limit", "RefUrl": "/notes/1413531"}, {"RefNumber": "1393969", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Contracts w/o agents are not selected", "RefUrl": "/notes/1393969"}, {"RefNumber": "1372797", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: BP other then landlord/agent is selected", "RefUrl": "/notes/1372797"}, {"RefNumber": "1367636", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: one-time posting not taken into account", "RefUrl": "/notes/1367636"}, {"RefNumber": "1365813", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: BP Category select. not taken into account", "RefUrl": "/notes/1365813"}, {"RefNumber": "1364702", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: flow-type vs. condition type", "RefUrl": "/notes/1364702"}, {"RefNumber": "1284016", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX JP: cost center address in Pyment Charge Report", "RefUrl": "/notes/1284016"}, {"RefNumber": "1125716", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report Japan - BP Role/Role Category", "RefUrl": "/notes/1125716"}, {"RefNumber": "1031686", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Changes after downgrade of the RE-FX Japan", "RefUrl": "/notes/1031686"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1455668", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charge Report running the ALV in background", "RefUrl": "/notes/1455668 "}, {"RefNumber": "1475800", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: Some records not taken into account", "RefUrl": "/notes/1475800 "}, {"RefNumber": "1451291", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report for Japan - New Grouping", "RefUrl": "/notes/1451291 "}, {"RefNumber": "1445195", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Handling Multi-landlord-agent in REXCJPPAYMENTREP", "RefUrl": "/notes/1445195 "}, {"RefNumber": "1413531", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charge Report- agent comm., address, BP cat, limit", "RefUrl": "/notes/1413531 "}, {"RefNumber": "1393969", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Contracts w/o agents are not selected", "RefUrl": "/notes/1393969 "}, {"RefNumber": "1372797", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: BP other then landlord/agent is selected", "RefUrl": "/notes/1372797 "}, {"RefNumber": "1125716", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Payment Charges Report Japan - BP Role/Role Category", "RefUrl": "/notes/1125716 "}, {"RefNumber": "1367636", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: one-time posting not taken into account", "RefUrl": "/notes/1367636 "}, {"RefNumber": "1365813", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: BP Category select. not taken into account", "RefUrl": "/notes/1365813 "}, {"RefNumber": "1364702", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: flow-type vs. condition type", "RefUrl": "/notes/1364702 "}, {"RefNumber": "1284016", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX JP: cost center address in Pyment Charge Report", "RefUrl": "/notes/1284016 "}, {"RefNumber": "1031686", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Changes after downgrade of the RE-FX Japan", "RefUrl": "/notes/1031686 "}, {"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD17", "URL": "/supportpackage/SAPKGPAD17"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60207INEAAPPL", "URL": "/supportpackage/SAPK-60207INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60306INEAAPPL", "URL": "/supportpackage/SAPK-60306INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 604", "SupportPackage": "SAPK-60405INEAAPPL", "URL": "/supportpackage/SAPK-60405INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 4, "URL": "/corrins/0001383363/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKGPAD16&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 602&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60206INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60305INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60404INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual activity separately in each system into which you transport the Note for  implementation.<br/><br/><B>A ) Create Data element REXCJPREPCOMM</B><br/><br/>1. Call the SE11 transaction.<br/>2. Select the Data type radiobutton, and enter REXCJPREPCOMM<br/>3. Press \"Create\".<br/>4. A dialog box appears, here select data element and press the continue button.<br/>5. The Change data element screen appears. here enter the following:<br/>Short Description:  \"Report Agent Commission\"<br/>On the Data type tab page:<br/>Domain:     \"RECABOOL\"<br/>On the field Label tab page:<br/>Short:   10 \"Rep.Ag.Com\"<br/>Medium:  16 \"Rep. Agent Comm.\"<br/>Long:  23 \"Report Agent Commission\"<br/>Heading:  10 \"Rep.Ag.Com\"<br/><br/>6. Activate the data element<br/><br/><B>B) Extend the TIVXCJPCCSET database table.</B><br/><br/>1. Call the SE11 transaction.<br/>2. Select the \"Database Table\" radiobutton and enter TIVXCJPCCSET.<br/>3. Press \"Change\".<br/>4. After the last component (ROUNDTYPE) enter the fieldname REPCOMM, and the data element REXCJPREPCOMM.<br/>5. Activate the database table.<br/><br/><B>C) Extend the V_TIVXCJPCCSET view.</B><br/><br/>1. Call the SE11 transaction.<br/>2. Select the \"View\" radiobutton and enter V_TIVXCJPCCSET.<br/>3. Press \"Change\".<br/>4. Go to the line after the last view field, REF_NUM_2, and press the \"Table Fields\".<br/>5. A dialog box appears, there select the field REPCOMM, and press ok.<br/>6. Activate the view.<br/>7. In change mode call from the menu Utilities -&gt; Table Maintenance Generator.<br/><br/>8. On the screen that appears press the change button. (F7)<br/>9. A dialog box appears: \"Change generation elements\". Here select the  \"Expert mode\" button, and on the next dialog box \"select all (F6)\", and press OK.<br/>This regenerates the function group and all manual changes are lost.  Therefore the manual entries in the flow logic and in the function group  are missing, so you have to reinsert them. (These manual entries take care of the automatic currency handling.)<br/><br/>10. Double click on the screen number of the overview screen. (press ok for the next dialog).<br/>11. In the flow logic, after the line \"FIELD V_TIVXCJPCCSET-REPCOMM .\" insert the following line:<br/><br/>&nbsp;&nbsp;&nbsp;&nbsp;module fill_currency.<br/><br/>12. Activate the program.<br/><br/>13. After that go to the function group 0TIVXCJPCCSET in transaction  SE80. Double click on its name and in the appearing dialog box select the \"Main program\" button.<br/>14. Enter the following lines at the end of SAPL0TIVXCJPCCSET include:<br/><br/>* Note 1383363<br/>&nbsp;&nbsp;INCLUDE L0TIVXCJPCCSETI01.&nbsp;&nbsp;\" For view of company code dependent data<br/>* Note 1383363<br/><br/>Activate it.<br/>15. Check that in the function group you still have the include file<br/>L0TIVXCJPCCSETI01. (the regeneration should have kept it.).<br/><br/>16. go to the include L0TIVXCJPCCSETTOP and add<br/>the following lines at the end of the include:<br/><br/>* Note 1383363<br/>DATA: gs_001 LIKE T001.<br/>* Note 1383363<br/><br/>After this activate the function group.<br/><br/><br/><B>D) Extend the structure REXCJP_PYMNTREC.</B><br/><br/>1. Call the SE11 transaction.<br/>2. Select the \"Data Type\"&nbsp;&nbsp;radiobutton and enter REXCJP_PYMNTREC.<br/>3. Press \"Change\".<br/>4. Go to the line after the last component in the structure, and enter  the component name \"PYMNTCATOK\" and for component type enter RECABOOL.<br/>5. Press enter.<br/>6. Activate the structure.<br/><br/><B>E) Maintain the error messages</B><br/><br/>1. Call the SE91 transaction<br/>2. Enter the message class name REXCJP. Press display.<br/>3. Select message Nr. 113, and press the \"selected entries\" button.<br/>4. change the message short text to the following:<br/>\"Payment category &amp;1 is not allowed for the BP type &amp;2 (BP &amp;3)\".<br/><br/>4. Save it.<br/><br/>5. Select message Nr. 133 and press the \"selected entries\" button.<br/>4. change the message short text change the text to the following:<br/>\"Total Payments (&amp;2) for vendor &amp;1 do not exceed the reporting limit (&amp;3)\"<br/>6. Save it.<br/><br/>7. Select message Nr. 134 and press the \"selected entries\" button.<br/>8. change the message short text change the text to the following:<br/>\"Total Agent commission (&amp;2) for vendor &amp;1 does not exceed the limit (&amp;3)\"<br/><br/>The further changes will be delivered via support package.<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1031686 ", "URL": "/notes/1031686 ", "Title": "Changes after downgrade of the RE-FX Japan", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1284016 ", "URL": "/notes/1284016 ", "Title": "RE-FX JP: cost center address in Pyment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1291097 ", "URL": "/notes/1291097 ", "Title": "Duplicated records in the Payment Charge Report", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1364702 ", "URL": "/notes/1364702 ", "Title": "REXCJPPAYMENTREP: flow-type vs. condition type", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1365813 ", "URL": "/notes/1365813 ", "Title": "REXCJPPAYMENTREP: BP Category select. not taken into account", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1367636 ", "URL": "/notes/1367636 ", "Title": "REXCJPPAYMENTREP: one-time posting not taken into account", "Component": "RE-FX-LC-JP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}