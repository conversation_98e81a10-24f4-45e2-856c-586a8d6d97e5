{"Request": {"Number": "766410", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 295, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015745882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000766410?language=E&token=E81DBF5E6E070A249E2089DDDB20CCBA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000766410", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000766410/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "766410"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.01.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-FP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Forms Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Forms Processing", "value": "BC-SRV-FP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-FP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "766410 - SAP Interactive Forms: XDC scenarios for printer control"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In SAP NetWeaver AS ABAP you create PDF-based print forms using the form solution 'SAP Interactive Forms by Adobe' You want to specifically control the print output of the forms.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Interactive Forms, PDF-based forms, Adobe Document Services, print forms, form printing, duplex</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP provides 'XDC files' for printing PDF-based print forms. You can adjust these files to control the document printing.<br /><br />You can currently make the following adjustments:</p>\r\n<ul>\r\n<li>Configuration of device options on new printers</li>\r\n<li>The paper tray you use is not the default tray [with form paper].</li>\r\n<li>Printing documents on paper with a particular size</li>\r\n<li>Stapling each copy of a multi-page document</li>\r\n<li>Printing several copies of a document instance and controlling the number of copies at runtime</li>\r\n<li>Duplex printing<br />As of SAP NetWeaver 2004 Support Package 18, and as of SAP NetWeaver 7.0 Support Package 05 to Support Package 13, this function is supported with the following restriction: You can only use duplex printing for the entire form.<br />As of SAP NetWeaver 7.0 Support Package 14, duplex printing is fully supported. For a more flexible layout of the forms, you require Adobe LiveCycle Designer as of Version 8.0. You then no longer require adjustments as described in the XDC scenarios.</li>\r\n<li>Accessing different paper trays<br />This function is supported as of SAP NetWeaver 7.0 Support Package 14. It is not supported in SAP NetWeaver 2004.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To edit the XDC files, you can download the XDC editor from the SAP Store at the Internet address <a target=\"_blank\" href=\"https://www.sap.com/developer/trials-downloads/additional-downloads.html\">https://www.sap.com/developer/trials-downloads/additional-downloads.html</a>. Enter \"XDC editor\" as the search term and download the trial version.<br /><br />The documentation for the XDC scenarios is attached to this SAP Note.</p>\r\n<p><br />An example for changing an XDC file for using different paper trays with step-by-step instructions is also attached to this SAP Note.<br /><br />Also note the following tips:</p>\r\n<ol>1. Never change the original XDC files but create a customer-specific copy with a changed file name. Perform the adjustments only in the customer-specific XDC file.</ol><ol><ol>2. In the customer-specific file, change the contents of the attributes \"name\" and \"id\" of the node &lt;xdc&gt;.</ol></ol><ol><ol>Example:</ol></ol><ol><ol>&lt;xdc xmlns=\"http://www.xfa. org/schem....\" id=\"123\" name=\"postscript\"&gt;</ol></ol><ol><ol>To</ol></ol><ol>&lt;xdc xmlns=\"http://www.xfa.org/schem....\" id=\"124\" name=\"postscript1\"&gt;</ol><ol>3. Since the Adobe Document Services cache the XDC files, you must restart the XML Form Module Service after a change.</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041271)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D062449)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000766410/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766410/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAPIFbA_Input_tray.pdf", "FileSize": "1787", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000349512004&iv_version=0022&iv_guid=D9AC65074F141840B2D97838170590F6"}, {"FileName": "XDC_Szenarios.pdf", "FileSize": "166", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000349512004&iv_version=0022&iv_guid=00109B36D66A1EEDA38399AD9DE196F2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "863894", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/863894"}, {"RefNumber": "863893", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/863893"}, {"RefNumber": "685571", "RefComponent": "BC-CCM-PRN", "RefTitle": "Printing SAP Interactive Forms by Adobe", "RefUrl": "/notes/685571"}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "1462047", "RefComponent": "BC-SRV-FP", "RefTitle": "Rebuild custom XDC scenario after upgrade/update", "RefUrl": "/notes/1462047"}, {"RefNumber": "1122142", "RefComponent": "XX-PART-ADB-IFM", "RefTitle": "Configuring for input tray and printer resident fonts", "RefUrl": "/notes/1122142"}, {"RefNumber": "1121176", "RefComponent": "BC-SRV-FP", "RefTitle": "Adobe LiveCycle Designer 8.0 - Installation - obsolete", "RefUrl": "/notes/1121176"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3360445", "RefComponent": "BC-SRV-FP", "RefTitle": "Restart of Forms Service in BTP - after SAP Note 766410", "RefUrl": "/notes/3360445 "}, {"RefNumber": "2882356", "RefComponent": "BC-SRV-FP", "RefTitle": "Error while uploading a custom XDC in ADS", "RefUrl": "/notes/2882356 "}, {"RefNumber": "2583181", "RefComponent": "BC-SRV-FP", "RefTitle": "ZPL Print Quantity command (^PQ) support", "RefUrl": "/notes/2583181 "}, {"RefNumber": "2290688", "RefComponent": "BC-SRV-FP", "RefTitle": "Change page print sequence using ADS", "RefUrl": "/notes/2290688 "}, {"RefNumber": "2430558", "RefComponent": "BC-SRV-FP", "RefTitle": "MICR Font on ADS using PCL format", "RefUrl": "/notes/2430558 "}, {"RefNumber": "3048106", "RefComponent": "XX-PART-ADB-IFM", "RefTitle": "Using printer resident fonts to print labels using Intermec printers", "RefUrl": "/notes/3048106 "}, {"RefNumber": "2279608", "RefComponent": "BC-SRV-FP", "RefTitle": "IFbA: XDC setting for Duplex printing ignored in bundling", "RefUrl": "/notes/2279608 "}, {"RefNumber": "2227897", "RefComponent": "BC-SRV-FP", "RefTitle": "IFbA: MICR/OCR font with Zebra printer", "RefUrl": "/notes/2227897 "}, {"RefNumber": "2111085", "RefComponent": "BC-CCM-PRN", "RefTitle": "Tray control in SAP", "RefUrl": "/notes/2111085 "}, {"RefNumber": "1942515", "RefComponent": "BC-SRV-FP", "RefTitle": "SAP IFbA: Cannot start XDC Editor (Eclipse) or the XDC file cannot be opened", "RefUrl": "/notes/1942515 "}, {"RefNumber": "1121176", "RefComponent": "BC-SRV-FP", "RefTitle": "Adobe LiveCycle Designer 8.0 - Installation - obsolete", "RefUrl": "/notes/1121176 "}, {"RefNumber": "1462047", "RefComponent": "BC-SRV-FP", "RefTitle": "Rebuild custom XDC scenario after upgrade/update", "RefUrl": "/notes/1462047 "}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "1122142", "RefComponent": "XX-PART-ADB-IFM", "RefTitle": "Configuring for input tray and printer resident fonts", "RefUrl": "/notes/1122142 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}