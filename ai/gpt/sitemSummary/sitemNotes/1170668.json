{"Request": {"Number": "1170668", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 264, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016519182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001170668?language=E&token=E02F403A249E1FB4A57EDE3F6FDA1FE6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001170668", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001170668/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1170668"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.01.2009"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SVD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Delivery (and Planning)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Delivery (and Planning)", "value": "SV-SMG-SVD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SVD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1170668 - The role of SAP Solution Manager in Remote Service Delivery"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SAP Remote Services are scheduled / should be delivered</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP GoingLive Service, Mission Critical Support, SAP EarlyWatch Alert</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP carries out the Remote SAP Support Services in the customer's SAP Solution Manager.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><STRONG>Introduction</STRONG><br />Business processes involve typically multiple systems within the customer IT landscape. It is not enough to focus on these single systems. Dependencies and interfaces between the different systems are crucial for mission critical operations. It is important to take a holistic view on the entire solution landscape to ensure the undisrupted business process flow.<br /><STRONG></STRONG><br /><STRONG>Service planning</STRONG><br />In the whole process of service planning, problem resolution and service delivery, the customer's SAP Solution Manager is the central communication platform for all involved parties. It provides transparency regarding all planned and conducted services and quick access to the risk mitigation plans resulting from these services.<br /><br /><STRONG>SAP EarlyWatch Alert</STRONG><br />One quality check highly recommended by SAP is SAP EarlyWatch Alert (EWA). EWA is an automatic tool that monitors the essential administrative areas of SAP components and keeps customers up-to-date on their performance and stability.<br />Since the components of critical business processes are often spread over different systems, it is important to gain an overview of the entire solution landscape. SAP EarlyWatch Alert for Solutions (EWAfS) provides customers with one consolidated report for all systems. The report reflects the overall status of the SAP solution by using traffic lights. It includes historical developments, aggregated solution KPIs and detailed statistics about dedicated systems of the solution. In addition, the solution-based report consolidates alerts generated by the regular EWA monitoring services and classifies them in order to identify potential areas for improvement, such as performance or stability.<br /><br />SAP EarlyWatch Alert runs in the customer's SAP Solution Manager. As a central point for administering your SAP solution, the SAP Solution Manager and SAP EarlyWatch Alert are an effective team. From one central point you can activate SAP EarlyWatch Alert for all components, determine how often it runs for each component, and view current and past reports. SAP reccomends that the transfer of SAP EarlyWatch Alerts to SAP is activated.<br /><br />In case of red SAP EarlyWatch Alerts, SAP is then able to schedule follow up activities. This can include communication with the customers and possible further investigation via an SAP EarlyWatch Service.<br /><br />Moreover, systems which are not based on the ABAP stack of SAP NetWeaver can exclusively be analyzed via the customer's&#x00A0;&#x00A0;SAP Solution Manager. As a prerequisite Solution Manager Diagnostics (SMD) has to be activated.<br /><br /><STRONG>SAP GoingLive Service</STRONG><br />Due to the increasing complexity in solution landscapes, pro-active services focus not only on individual systems but on your entire SAP Solution. SAP GoingLive Check for Solutions prepares your SAP Solution for the GoLive date and ensures that it runs with optimal performance, availability and maintainability<br /><br />SAP carries out the SAP GoingLive Check in the customer's SAP Solution Manager. During service delivery, the SAP GoingLive Check service engineer has access to important project information in SAP Solution Manager which saves time and enables SAP to carry out the service in a more efficient way. Moreover, non-ABAP components can exclusively be analyzed via SAP Solution Manager. As a prerequisite Solution Manager Diagnostics (SMD) has to be activated.<br /><br /><STRONG>Remote Service delivery</STRONG><br />SAP carries out the Remote SAP Support Services in the customer's SAP Solution Manager. During service delivery, the SAP Support engineer has access to important project information in SAP Solution Manager which saves time and enables SAP to carry out the service in a more efficient way.<br /><br /><B>Remote and safe access</B><br />SAP's support infrastructure gives experts access to the required tools and information at the customer from a remote location. This enables them to deliver services based on highly standardized processes which in the end lead to very detailed system analyses.<br /><br />The remote connection # an electronic channel # allows for a transfer of information in both directions. With the help of this information support engineers can give recommendations which customers can use for taking reactive and proactive measures to continuously improve their landscapes. The remote connection itself allows safe and dedicated transfer of data. Access records are&#x00A0;&#x00A0;kept for auditing purposes. To prevent incorrect changes to the system and to reduce the risk of a problem, authorization levels must be applied to the accounts of different system users to establish a clear segregation of duties. This can be achieved by following the authorization concepts of the individual applications. However, many areas, such as the operating system, make it quite difficult to give particular experts the exact level of authorization they need to complete their tasks. In this instance, it is simpler and more efficient for the service and support infrastructure to impose basic read-only access rights for critical areas of live applications.<br /><br /><B>Security aspects in Service Delivery</B><br />Support experts often rely on information of the customer's systems, applications, and business processes which might be sensitive therefore unauthorized access must be avoided. Allowing external support experts to access such confidential information is always at high risk for customers. However, an exchange of data is inevitable to guarantee the delivery of adequate services for each customer's specific needs. It is important that the customers themselves retain control over their systems and the related information. Security plays an important role in this context. Supporting the customer solution landscape requires a set of services and tools that allow for the controlling, tracing, viewing and reporting of this landscape. However, just like administrator functions are not normally available to end users, not all of the administrator functions should be available to a support person. Every time a support employee accesses a customer landscape, there are potentially legal implications and/or serious consequences resulting from this interaction, since the support employee may make changes to the customer's environment. To achieve a high level of security, it is crucial that the support infrastructure takes special care of the protection of sensitive customer data.<br /><br /><br /><B>Keeping the customers in control</B><br />Placing SAP Solution Manager at customer site is the best choice. Being located directly in the solution landscape, it is connected to all systems within this landscape, and important information can be collected and stored centrally.<br /><br />Customers always have access to the different systems from one single point of reference and can retain control over their data.<br />Not only the customer's own staff but service engineers are also able to access the collected information in a safe, fast and auditable way, which in the end leads to an easier and faster completion of services. If SAP Solution Manager is available directly at the customer site, the customer has full control on who may access the data. Moreover, customers can leverage all information and additional functionality and tools available on the platform. This helps them to standardize their own IT processes as well, like incident management, problem management or change control.<br /><br /><br /><STRONG>End-to-End Root Cause Analysis</STRONG><br />SAP Solution Manager addresses even more support relevant topics:<br />In today's distributed, multi-technology customer solutions with multi-channel access through diverse devices and client applications, analyzing the root cause of an incident requires a systematic top down approach to finally pinpoint to the root cause of an incident. End-to-End root cause analysis offers systematic analysis and resolution of incidents for a distributed mission critical customer environment. All tools are safe: they do not allow changes when used by SAP employees unless explicitly requested by the customer and enforced by the customer change management process<br /><br /><br /><STRONG>Use of root cause analysis tools in Service Delivery</STRONG><br />Together, remote access to root cause analysis tools and key technical information on the customer solution - as provided by the customer's SAP Solution Manager - help accelerate the delivery of support services and to reduce costs.<br /><br /><STRONG>Required infrastructure</STRONG><br />The support infrastructure consists of the local installation of SAP Solution Manager, the connection to SAP and various Support Systems at SAP. It provides the technical foundation for performing support services and for delivering services. Experts are given access to the information and systems they need, while communication between customers, partners, and SAP is improved. In order for experts to be able to efficiently complete the tasks assigned to them, a support infrastructure must meet the basic requirements of being standardized, centrally located, complete, accessible from a remote location, and secure. Customers have to retain control over confidential data and the support infrastructure has to be proactive and bidirectional.<br /><br /><STRONG>Additional Information</STRONG><br />In order to deliver support services SAP strongly recommends updating the customer's SAP Solution Manager to the latest Release and Support Package Stack. This ensures that the latest Service content is available and can be leveraged during Service Delivery.<br /><br /><br />Mission-Critical Operations Support<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SER (SAP Support Services)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D040768)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022821)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001170668/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001170668/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001170668/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001170668/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001170668/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001170668/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001170668/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001170668/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001170668/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "MCS_Whitepaper.pdf", "FileSize": "401", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000243612008&iv_version=0007&iv_guid=2A2B30F54341C3438715BD9C3E258C55"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "930747", "RefComponent": "SV-SMG", "RefTitle": "Service Delivery on SAP Solution Manager (Recommended Notes)", "RefUrl": "/notes/930747"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "394616", "RefComponent": "SV-SMG-INS", "RefTitle": "Release strategy for SAP Solution Manager", "RefUrl": "/notes/394616"}, {"RefNumber": "1657403", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1657403"}, {"RefNumber": "1293438", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1293438"}, {"RefNumber": "1172939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1172939"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}, {"RefNumber": "1145779", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1145779"}, {"RefNumber": "1117389", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Solution Manager: Solution for SAP Service Delivery", "RefUrl": "/notes/1117389"}, {"RefNumber": "1025530", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1025530"}, {"RefNumber": "1010428", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics", "RefUrl": "/notes/1010428"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "394616", "RefComponent": "SV-SMG-INS", "RefTitle": "Release strategy for SAP Solution Manager", "RefUrl": "/notes/394616 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1010428", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics", "RefUrl": "/notes/1010428 "}, {"RefNumber": "1442799", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Enterprise Support Report", "RefUrl": "/notes/1442799 "}, {"RefNumber": "1657403", "RefComponent": "SV-SMG-SVD", "RefTitle": "SAP Support Remote Services for SAP BusinessObjects IDD/EIM", "RefUrl": "/notes/1657403 "}, {"RefNumber": "930747", "RefComponent": "SV-SMG", "RefTitle": "Service Delivery on SAP Solution Manager (Recommended Notes)", "RefUrl": "/notes/930747 "}, {"RefNumber": "1293438", "RefComponent": "SV-SMG-DIA", "RefTitle": "Diagnostics for SAP Partner Products", "RefUrl": "/notes/1293438 "}, {"RefNumber": "1117389", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Solution Manager: Solution for SAP Service Delivery", "RefUrl": "/notes/1117389 "}, {"RefNumber": "1025530", "RefComponent": "SV-SMG-SER", "RefTitle": "Service Delivery with Solution Manager 7.0 (and 3.2) - FAQ", "RefUrl": "/notes/1025530 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}