{"Request": {"Number": "887916", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 581, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005082112017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000887916?language=E&token=A860B0AE44564185EAC985BCA3102B12"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000887916", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000887916/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "887916"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-PCF"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: People Centric UI Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: People Centric UI Framework", "value": "CRM-PCF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-PCF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "887916 - Correction CRM BSP Framework, SP06 (2)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Correction CRM BSP Framework, SP06 (2)</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Correction CRM BSP Framework, SP06 (2)</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Correction CRM BSP Framework, SP06 (2)</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Correction CRM BSP Framework, SP06 (2)<br /><br /></p> <b>Short Description</b><br /> <p><br />This note includes the following:</p> <UL><LI>Includes preparatory work for the activation of the new, high-performance State Manager which reduces database accesses. The actual activation is performed in a subsequent note.</LI></UL> <UL><LI>Fixes in MULTISELECT, so that whatever action performed after a COPY (e.g. another COPY or a DELETE), is only taking into context the newly COPIed object (without the COPIed from object).</LI></UL> <UL><LI>Makes sure that the default focus on row 1, in the DETAIL pane, takes into account the fact that the output might have an initial SORT as required by the application configuration. Without this check, default focus could be on any row, as the list always ended-up to be sorted after focus was set.</LI></UL> <p><br /><br />Before implementing the attached corrections, do the following:<br /></p> <b>A. Create the necessary Data Dictionary (DDIC) types in SE11:</b><br /> <p><br />1. Launch SE11, select the Data type radio button, type CRMT_BSP_CONTEXT_ATTR in the Data type field and press Create. Select  the Structure radio button in the Create Type dialog that appears and press OK. You now find yourself in the data type editor of SE11.<br /><br />2. Type \"CRM Context attribute\" (without quotes) in the Short Description field.<br /><br />3. In the Components tab, enter the following components in the grid:<br /><br /> - Component: NAME<br /> &#x00A0;&#x00A0;Component type: CRMT_BSP_CONTAINER_ATTR_NAME<br /> - Component: VALUE<br /> &#x00A0;&#x00A0;Component type: CRMT_BSP_CONTAINER_ATTR_VALUE<br /><br />4. Press the Activate button in the toolbar. From the Maintain Enhancement Category dialog that appears, select the Cannot Be Enhanced radio button and press Copy. When asked to save the new type, make sure that you save it in the CRM_BSP_FRAME_GENERIC package. The CRMT_BSP_CONTEXT_ATTR data type should now be active in the DDIC.<br /><br />5. Press the Exit button in the toolbar to go back to SE11's start screen. Type CRMT_BSP_CONTEXT_ATTR_T in the Data type field and select the Data type radio button. Then press Create. Select the Table type radio button from the Create Type dialog that appears and press OK. You now find yourself in the data type editor of SE11.<br /><br />6. Type \"CRM Context attribute table\" (without quotes) in the Short Description field.<br /><br />7. In the Line Type tab, enter CRMT_BSP_CONTEXT_ATTR in the Line Type field and select the Line Type radio button.<br /><br />8. Press the Activate button in the toolbar. When asked to save the new type, make sure that you save it in the CRM_BSP_FRAME_GENERIC package. The CRMT_BSP_CONTEXT_ATTR_T data type should now be active in the DDIC.<br /><br />9. Exit SE11.<br /></p> <b>B. Create the CL_CRM_BSP_CONTEXT class in SE80:</b><br /> <p><br />1. Launch SE80, press the Repository Browser button at the top-left corner of the SE80 window, select Package in the drop-down list box, type CRM_BSP_FRAME_GENERIC in the input field and press Enter. You should now see the CRM_BSP_FRAME_GENERIC package displayed in the tree at the bottom-left corner of the SE80 window.<br /><br />2. In the tree, navigate to the following path: CRM_BSP_FRAME_GENERIC/Class Library/Classes. You should now see a list of all the classes in the CRM_BSP_FRAME_GENERIC package displayed in the tree.<br /><br />3. Right-click on the Classes tree node and select Create from the context menu that appears. The Create Class dialog should now be displayed.<br /><br />4. In the Create Class dialog, type CL_CRM_BSP_CONTEXT in the Class field, type \"CRM Context class\" (without quotes) in the Description field, select Private from the Instantiation drop-down list box, and press Save. Make sure that you save the class in the CRM_BSP_FRAME_GENERIC package. The class edition screen should now be shown in SE80's main work area.<br /><br />5. From the menu bar, select Utilities -&gt; Settings... and select the ABAP Editor tab from the User-Specific Settings dialog that appears. Make sure that the Standard Line Lngth(72) checkbox is *not* checked. Press the OK button to save your changes.<br /><br />6. From the menu bar, select Goto -&gt; Public section. You should now see the public code of the CL_CRM_BSP_CONTEXT class in SE80's main work area. Select all the code and replace it with the whole contents of the cl_crm_bsp_context_public_section.txt file attached to this note. Press the Back button and save the class when asked.<br /><br />7. From the menu bar, select Goto -&gt; Private section. You should now see the private code of the CL_CRM_BSP_CONTEXT class in SE80's main work area. Select all the code and replace it with the whole contents of the cl_crm_bsp_context_privat_section.txt file attached to this note. Press the Back button and save the class when asked.<br /><br />8. Press the Local Type button in the toolbar. You should now see the local types of the CL_CRM_BSP_CONTEXT class in SE80's main work area. Select all the code and replace it with the whole contents of the cl_crm_bsp_context_local_types.txt file attached to this note. Press the Back button and save the class when asked.<br /><br />9. Activate the CL_CRM_BSP_CONTEXT class by pressing the Activate button in the toolbar.<br /><br />10. Exit SE80.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-GTF-PCF (People Centric UI Framework)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I801916)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I027446)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000887916/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000887916/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "cl_crm_bsp_context_local_types.txt", "FileSize": "1", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000515772005&iv_version=0006&iv_guid=7404FBC04A3767448E42C8A159B95DC0"}, {"FileName": "cl_crm_bsp_context_public_section.txt", "FileSize": "3", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000515772005&iv_version=0006&iv_guid=66CD1CD0168B8640B36E02AAD690B35F"}, {"FileName": "cl_crm_bsp_context_privat_section.txt", "FileSize": "2", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000515772005&iv_version=0006&iv_guid=3D1AFFA1396ECF42BD6A370687A867C0"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70006", "URL": "/supportpackage/SAPKA70006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 4, "URL": "/corrins/0000887916/44"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 8, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868404 ", "URL": "/notes/868404 ", "Title": "Correction CRM BSP Framework, SP05", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868693 ", "URL": "/notes/868693 ", "Title": "Correction CRM BSP Framework, SP05 (1)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871213 ", "URL": "/notes/871213 ", "Title": "Correction CRM BSP Framework, SP05 (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872178 ", "URL": "/notes/872178 ", "Title": "Correction CRM BSP Framework, SP05 (5)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873662 ", "URL": "/notes/873662 ", "Title": "Correction CRM BSP Framework, SP05 (8)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "880879 ", "URL": "/notes/880879 ", "Title": "Correction CRM BSP Framework, SP05 (11)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "881541 ", "URL": "/notes/881541 ", "Title": "Correction CRM BSP Framework, SP05 (12)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "885050 ", "URL": "/notes/885050 ", "Title": "Correction CRM BSP Framework, SP06 (01)", "Component": "CRM-PCF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}