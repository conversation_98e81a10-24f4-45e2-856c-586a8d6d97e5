{"Request": {"Number": "350011", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 397, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014911252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000350011?language=E&token=21E6F277C21C4ADCFB635806C0E3B815"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000350011", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000350011/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "350011"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.04.2007"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-SEM-BPS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Planning and Simulation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Enterprise Management", "value": "FIN-SEM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Planning and Simulation", "value": "FIN-SEM-BPS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM-BPS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "350011 - GoingLive Service - Preparations for BPS Benchmark Check"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to check the technical performance of your system with the delivered Business Content.<br />You want to compare the performance of your system with reference values in the GoingLive service session.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Performance benchmark check, GoingLive check, GO, UPC_BENCHMARK_EXECUTE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>First of all, the BW business content must be activated, transaction data must be extracted and the database statistics/index must be recalculated.</p> <OL>1. Select the InfoProvider 0SEMBPSPF from the Business Content:<br />(Call transaction RSA1 =&gt; Business Content =&gt; InfoProvider according to InfoAreas =&gt; search for 0SEMBPSPF =&gt; Copy to collective area using drag and drop, and activate).</OL> <OL>2. Set the InfoProvider to transactional: Use transaction SE38 to execute the report SAP_CONVERT_NORMAL_TRANS and enter 0SEMBPSPF as the selection.</OL> <OL>3. Maintain fiscal year variants: In the Implementation Guide (IMG) (transaction SPRO) under the menu option \"SAP Netweaver\", choose \"SAP BW =&gt; General BW Settings =&gt; Maintain Fiscal Year Variants\" and create an entry with the values FV = K4, Year-dependent = ' ', Calendar yr = 'X', Number of posting periods = 12 and Number of special periods = 4.</OL> <OL>4. Transport SEM Customizing to the productive client:<br />Call transaction BPS0 in the client 000 (delivery client) and select \"Tools =&gt; Transport\" in the menu. Select the planning area option, enter 4PERF001 as the planning area and select \"New transport including all planning levels\". Execute. Specify a transport request. Now log on to the productive client. In transaction SCC1, select the transport request and specify the option \"Including Request Subtasks\". You may need to use transaction SCC4 to set the role of the client temporarily to \"not productive\" to execute the transport.</OL> <OL>5. Change the planning area:<br />In transaction BPS0, change the 4PERF001 planning area and specify the option \"local\" under the point \"BW Installation\".</OL> <OL>6. Select the 0BPS_DATA InfoSource from the Business Content:<br />(RSA1 =&gt; Business Content =&gt; InfoSource according to application components =&gt; search for 0BPS_DATA =&gt; Copy into the collective area using drag and drop).</OL> <OL>7. Create a source system: File system of PC<br />(RSA1 =&gt; Business Content =&gt; Object Types =&gt; Source System =&gt; Select Objects =&gt; search for PC_FILE =&gt; Transfer into the collective area using drag and drop).</OL> <OL>8. Activate the selected objects.<br />If some objects are already active, these should not be reactivated, otherwise customer changes may be overwritten. The checkboxes next to the object indicate whether the objects can be activated or not: 'Transfer' specifies if it should be activated.</OL> <p></p> <OL>9. Create update rule: Template InfoSource 0BPS_DATA<br />(RSA1 =&gt; Modeling =&gt; InfoProvider =&gt; Search 0SEMBPSPF =&gt; Via InfoCube context menu =&gt; Create update rules =&gt; In the 'Data source' field select the InfoSource row and specify 0BPS_DATA as the name =&gt; Activate).</OL> <OL>10. Assign source system to the InfoSource and activate<br />(RSA1 =&gt; Modeling =&gt; InfoSource =&gt; Search 0BPS_DATA =&gt; Open context menu =&gt; Change =&gt; Expand transfer structure =&gt; Select PC_FILE source system =&gt; Transfer proposals =&gt; Activate).</OL> <OL>11. InfoCube: Switching on upload mode:<br />RSA1 =&gt; Select cube =&gt; Context menu =&gt; Switch transactional InfoCube =&gt; Select 'Transactional Cube can be loaded with data'.</OL> <OL>12. Load transaction data into the InfoCube<br />(RSA1 =&gt; Modeling =&gt; InfoSource =&gt; Search 0BPS_DATA =&gt; Expand tree until source system is visible =&gt; Display context menu =&gt; Create InfoPackage).<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Tab page&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Settings deviate from standard system</TH></TR> <TR><TD>Select data:&#x00A0;&#x00A0;&#x00A0;&#x00A0;--</TD></TR> <TR><TD>External data:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; File name (see below), File type: *.csv</TD></TR><TR><TD>External data parameter: Header lines which should be ignored = 1</TD></TR> <TR><TD>Processing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Only data targets</TD></TR> <TR><TD>Data targets:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --</TD></TR> <TR><TD>Update parameter: --</TD></TR> <TR><TD>Plan:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Immediately</TD></TR> <TR><TD></TD></TR> <TR><TD>The file names of the *.csv files are Transaction_data_Volumes.csv, Transaction_data_Prices.csv and Transaction_data_Masterdata.csv. These files are attached to this note. Load these three text files one after the other with the InfoPackage created in the InfoCube.</TD></TR> </TABLE></OL> <OL>13. Set up statistics and index:<br />Select cube =&gt; Administrate =&gt; Calculate statistics, set up index</OL> <OL>14. InfoCube: Switch on plan mode:<br />RSA1 =&gt; Select cube =&gt; Switch transactional InfoCube =&gt; Select 'Transactional Cube can be planned'.</OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following is the actual measurement. You can use the report UPC_BENCHMARK_EXECUTE to execute the scripts described in the following text for the automatic test execution.<br /></p> <b>The following steps are carried out in the GoingLive session of the SAP Service Engineer and therefore do not need to be activated by the customer.</b><br /> <OL>1. Run the scripts and compare the logs in the statistics (Note 340246) with the reference values. At the moment, there are two options available with which the test cases can be tested automatically. In variant 1 no data is saved so that the data basis is not changed. Every function is executed twice: The first time the data is selected from BW, the second time it runs directly on the buffer. To ensure that both functions execute an identical operation, a further function call is generally used that resets the change of the first function. In variant 2, data records are changed and written to the database. Since this is the main focus, only a planning function type is used.</OL> <OL>2. In the UPC_BENCHMARK_EXECUTE report, you can select variant 3. This test calls transaction BPS0 and starts the manual planning. However, you must exit the transaction manually (yellow arrow) before the next call can be made.</OL> <OL>3. The API is used to execute planning functions. Here, the system calls and executes various Basis functions. Currently, these are copy, delete, exit, revaluate, formula. The number of data records transferred to the function varies from 100 to 100,000 data records. The execution script is in the UPC_BENCHMARK_EXECUTE report. If this is not available in your system yet, you can find the source text to execute the benchmark in Note 381022. However, this source text does not contain the full range of functions that are in the delivered report.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-PLA-BPS (Business Planning and Simulation)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029488)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029488)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000350011/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000350011/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000350011/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000350011/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000350011/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000350011/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000350011/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000350011/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000350011/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Benchmark_Data.zip", "FileSize": "245", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008585962001&iv_version=0018&iv_guid=46B001C5E7B8054383928C64C65DF99C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "990000", "RefComponent": "FIN-SEM-BPS-BP", "RefTitle": "BW-BPS: Performance monitoring and analysis", "RefUrl": "/notes/990000"}, {"RefNumber": "540398", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Runtimes of planning functions in SEM-BPS", "RefUrl": "/notes/540398"}, {"RefNumber": "417091", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Optimize execution time of planning functions", "RefUrl": "/notes/417091"}, {"RefNumber": "381022", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Technical performance: <PERSON><PERSON><PERSON> for benchmark", "RefUrl": "/notes/381022"}, {"RefNumber": "340246", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Techn. performance: Overview of statistics", "RefUrl": "/notes/340246"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "340246", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Techn. performance: Overview of statistics", "RefUrl": "/notes/340246 "}, {"RefNumber": "417091", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Optimize execution time of planning functions", "RefUrl": "/notes/417091 "}, {"RefNumber": "381022", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Technical performance: <PERSON><PERSON><PERSON> for benchmark", "RefUrl": "/notes/381022 "}, {"RefNumber": "990000", "RefComponent": "FIN-SEM-BPS-BP", "RefTitle": "BW-BPS: Performance monitoring and analysis", "RefUrl": "/notes/990000 "}, {"RefNumber": "540398", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Runtimes of planning functions in SEM-BPS", "RefUrl": "/notes/540398 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "30A", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SEM-BW 30A", "SupportPackage": "SAPKGS3A04", "URL": "/supportpackage/SAPKGS3A04"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}