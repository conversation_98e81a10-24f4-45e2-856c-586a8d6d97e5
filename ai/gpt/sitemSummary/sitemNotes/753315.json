{"Request": {"Number": "753315", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 362, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004100292017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000753315?language=E&token=2337857C6DFB286210D25A4D21CFBE52"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000753315", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000753315/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "753315"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.08.2004"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "753315 - IS-H*MED: Authorization for Planning Authority"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains a new authorization object for restricting changes to the day-related planning authority.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Planning Authority, Authorization, Time Slot Modification</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Advance Delivery</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>How to import the note:<br /><br />You must create the following class methods:</p> <UL><LI>You call transaction SE24</LI></UL> <UL><LI>You enter the name of the class (CL_ISHMED_UTL_APMG) in the input field (Object Type) and select the \"Change\" function.</LI></UL> <UL><LI>You open the \"Methods\" tab page.</LI></UL> <UL><LI>You enter the following values in the first free row:</LI></UL> <UL><UL><LI>Method: CHECK_AUTH_N_1PLATH</LI></UL></UL> <UL><UL><LI>Type: Static Method</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: Authorization Check for Day-Related Planning Authority</LI></UL></UL> <UL><LI>You select the \"Parameters\" function (=pushbutton).<br />You enter the following values in the first free row:</LI></UL> <UL><UL><LI>Parameter: I_EINRI</LI></UL></UL> <UL><UL><LI>Typing: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select the checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing method: TYPE</LI></UL></UL> <UL><UL><LI>Associated type: EINRI</LI></UL></UL> <UL><UL><LI>Description: Institution</LI></UL></UL> <UL><LI>You enter the following values in the second new row:</LI></UL> <UL><UL><LI>Parameter: I_POBNR</LI></UL></UL> <UL><UL><LI>Type: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select the checkbox</LI></UL></UL> <UL><UL><LI>Optional: Select the checkbox</LI></UL></UL> <UL><UL><LI>Typing method: TYPE</LI></UL></UL> <UL><UL><LI>Associated Type: ISH_POBNR</LI></UL></UL> <UL><UL><LI>Description: Planning Object</LI></UL></UL> <UL><LI>You enter the following values in the third new row:</LI></UL> <UL><UL><LI>Parameter: I_PLNOE</LI></UL></UL> <UL><UL><LI>Type: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select the checkbox</LI></UL></UL> <UL><UL><LI>Optional: Select the checkbox</LI></UL></UL> <UL><UL><LI>Typing method: TYPE</LI></UL></UL> <UL><UL><LI>Associated type: N1PLNOE</LI></UL></UL> <UL><UL><LI>Description: Planning Organizational Unit</LI></UL></UL> <UL><LI>You enter the following values in the fourth new row:</LI></UL> <UL><UL><LI>Parameter: I_ACTVT</LI></UL></UL> <UL><UL><LI>Type: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select the checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing method: TYPE</LI></UL></UL> <UL><UL><LI>Associated type: ACTIV_AUTH</LI></UL></UL> <UL><UL><LI>Description: Activity</LI></UL></UL> <UL><LI>You enter the following values in the fifth new row:</LI></UL> <UL><UL><LI>Parameter: IR_OBJECT</LI></UL></UL> <UL><UL><LI>Type: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value:</LI></UL></UL> <UL><UL><LI>Optional: Select the checkbox</LI></UL></UL> <UL><UL><LI>Typing method: Type Ref To</LI></UL></UL> <UL><UL><LI>Associated type: OBJECT</LI></UL></UL> <UL><UL><LI>Description: Object Reference for Error Message</LI></UL></UL> <UL><LI>You enter the following values in the sixth new row:</LI></UL> <UL><UL><LI>Parameter: I_MSGTYPE</LI></UL></UL> <UL><UL><LI>Type: IMPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select the checkbox</LI></UL></UL> <UL><UL><LI>Optional: Select the checkbox</LI></UL></UL> <UL><UL><LI>Typing method: Type</LI></UL></UL> <UL><UL><LI>Associated type: SY-MSGTY</LI></UL></UL> <UL><UL><LI>Default value: 'E'</LI></UL></UL> <UL><UL><LI>Description: Message Type for Message Display</LI></UL></UL> <UL><LI>You enter the following values in the seventh new row:</LI></UL> <UL><UL><LI>Parameter: E_AUTH</LI></UL></UL> <UL><UL><LI>Type: EXPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select the checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing method: Type</LI></UL></UL> <UL><UL><LI>Associated type: ISH_TRUE_FALSE</LI></UL></UL> <UL><UL><LI>Description: Authorization Exists</LI></UL></UL> <UL><LI>You enter the following values in the eighth new row:</LI></UL> <UL><UL><LI>Parameter: E_RC</LI></UL></UL> <UL><UL><LI>Type: EXPORTING</LI></UL></UL> <UL><UL><LI>Pass value: Select the checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing method: Type</LI></UL></UL> <UL><UL><LI>Associated type: ISH_METHOD_RC</LI></UL></UL> <UL><UL><LI>Description: Return Code</LI></UL></UL> <UL><LI>You enter the following values in the ninth new row:</LI></UL> <UL><UL><LI>Parameter: CR_ERRORHANDLER</LI></UL></UL> <UL><UL><LI>Type: CHANGING</LI></UL></UL> <UL><UL><LI>Pass value:</LI></UL></UL> <UL><UL><LI>Optional: Select the checkbox</LI></UL></UL> <UL><UL><LI>Typing method: Type Ref To</LI></UL></UL> <UL><UL><LI>Associated type: CL_ISHMED_ERRORHANDLING</LI></UL></UL> <UL><UL><LI>Description: Instance for Error Handling</LI></UL></UL> <p><br />You must create the following new error message:</p> <UL><LI>You call transaction SE91</LI></UL> <UL><LI>You enter \"N1APMG_MED\" in the \"Message Class\" input field</LI></UL> <UL><LI>In the \"Number\" input field you enter \"031\" and select the \"Change\" function.</LI></UL> <UL><LI>You enter the following text in the \"Message Short Text\" field: You are not authorized for one of the planning authorities</LI></UL> <UL><LI>You select the \"Self-Explanatory\" checkbox</LI></UL> <UL><LI>You select the \"Long Text\" function and enter the following text under \"Diagnosis\":<br />You wish to change the division of the planning authorities in one or more planning objects. However, you are not authorized for this function for one or more of the planning objects called. These entries will not be displayed.</LI></UL> <UL><LI>You actively save the change</LI></UL> <p><br /><br />You receive the new authorization object and the maintenance of the authorization object use in transactions (transaction SU22) as follows:</p> <UL><LI>You unzip the attached file HW753315W_472.zip.</LI></UL> <UL><LI>You import the unzipped Workbench entry into your system.</LI></UL> <p><br />You receive the following changes concerning the insertion of the new authorization object into the collective profile N_ISHMED_ALL as follows:</p> <UL><LI>You unzip the attached file HW753315C_472.zip.</LI></UL> <UL><LI>You import the unzipped customizing order into your system.<br /></LI></UL> <p>You should note that you cannot download the unzipped files using OSS but only via the Service Marketplace (you should also refer to note numbers 480180 and 13719 on importing attachments).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5006701"}, {"Key": "Processor                                                                                           ", "Value": "C5043713"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000753315/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000753315/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW753315W_472.zip", "FileSize": "35", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000418472004&iv_version=0001&iv_guid=6F2611852F9AA54FBC224892F0F763B2"}, {"FileName": "HW753315C_472.zip", "FileSize": "18", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000418472004&iv_version=0001&iv_guid=5D39CB4BD91B984A9DBF879695976157"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF04", "URL": "/supportpackage/SAPKIPHF04"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 1, "URL": "/corrins/0000753315/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}