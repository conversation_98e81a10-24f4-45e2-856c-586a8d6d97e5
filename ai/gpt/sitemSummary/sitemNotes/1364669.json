{"Request": {"Number": "1364669", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 318, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016834152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001364669?language=E&token=3C43ED38121AAB18331693080852CAAB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001364669", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001364669/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1364669"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.01.2012"}, "SAPComponentKey": {"_label": "Component", "value": "LOD-ESO-ERP"}, "SAPComponentKeyText": {"_label": "Component", "value": "ERP Integration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "OnDemand", "value": "LOD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LOD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Sourcing <PERSON>", "value": "LOD-ESO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LOD-ESO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ERP Integration", "value": "LOD-ESO-ERP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LOD-ESO-ERP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1364669 - Integrating ERP with the SAP Sourcing (II)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to integrate purchasing processes between the SAP Sourcing Solution and an ERP system of release ECC 6.00, 6.02, 6.03 or 6.04.<br /><br />Important: This note is only relevant for the purpose described above. You must not apply this note if you do not use SAP Sourcing.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ERP-ES, integration, purchasing requisition, master agreement, RFX, E-Sourcing<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Missing functionality.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement one of the following support packages, depending on your system landscape:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Release SAP_APPL</TH><TH ALIGN=LEFT> Support Package</TH></TR> <TR><TD>6.00</TD><TD> SAPKH60016</TD></TR> <TR><TD>6.02</TD><TD> SAPKH60206</TD></TR> <TR><TD>6.03</TD><TD> SAPKH60305</TD></TR> <TR><TD>6.04</TD><TD> SAPKH60404</TD></TR> </TABLE> <p><br />After applying the pertaining support package you can activate the integration by maintaining the settings in the IMG under \"Integration with Other SAP Components --&gt; SAP Sourcing\".<br /><br /><B><U>We strongly recommend to apply the support package rather than installing the relevant objects described below. Due to the compexity and the import of a transport file we would like to highlight the fact, that any issue caused by this will be NOT part of SAP's standard product support. All request related to that issue will be treated as chargable consulting requests.</U></B><br /><br />If you cannot implement the aforementioned support packages due to limitations in your system landscape, you can implement the main part of the integration solution manually. <B>The manual implementation requires a transport file which you can request from SAP Support by opening a message on component MM-PUR-RFQ.</B><br /><br />SAP will provide support for the support package solution as well as for the preliminary one shipped with this note.<br /><br /><STRONG>Implementation Instructions</STRONG><br />It is important that you strictly keep the sequence of steps in order to avoid inconsistencies.</p> <OL>1. Ensure that the support package level of software component SAP_APPL in your system is in the following range:<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Release SAP_APPL</TH><TH ALIGN=LEFT> Support Package Range SAP_APPL</TH></TR> <TR><TD>6.00</TD><TD> SAPKH60001 - SAPKH60015</TD></TR> <TR><TD>6.02</TD><TD> SAPKH60201 - SAPKH60205</TD></TR> <TR><TD>6.03</TD><TD> SAPKH60301 - SAPKH60304</TD></TR> <TR><TD>6.04</TD><TD> SAPKH60401 - SAPKH60403</TD></TR> </TABLE></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must not implement the integration solution again if the support package level of your system is outside these ranges! <OL>2. Implement note 1329172 completely, including all described manual steps.</OL> <OL>3. Release the transport request you used for the implementation of note 1329172. Since some objects are overwritten during the import of the transport files in the next steps, there could be locking problems otherwise. As an alternative, you could use the same transport request for consolidation of the import of the transport files (see next steps).</OL> <OL>4. Store the transport file that you received from SAP on your local PC and extract the ZIP file. For further information how to do this, refer to note 480180.</OL> <OL>5. Move the R3trans file R000832.K7F to the folder \\usr\\sap\\trans\\data of the file system of your development system. Move the co-file K000832.K7F to the folder \\usr\\sap\\trans\\cofiles. Import the transport request K7FK000832 as described in note 13719.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If your system is a NON-unicode system run the import with option -dSLI=YES only. This prevents importing texts from languages which were not yet installed in the target system (not installed language texts could cause issues during unicode conversion later on). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note: Some of the objects which were already created with note 1329172 will be updated to their final state by this transport. This is required and the reason why note 1329172 has to be implemented <B>before </B>importing the transport files. <OL>6. The corrections implemented in step 2 und the new objects implemented in step 5 are now ready to be transported to your quality system.</OL> <OL>7. <B>Important</B>: The transport file must only be imported once! If it is imported later a second time, for example after applying corrections or after having implemented support packages in the meantime, the system could be damaged because objects which have been updated meanwhile could be overwritten and reset to an outdated state by mistake. <B>Consequently, delete the transport file once you have imported it into your system</B>. This measure prevents that it is imported again later accidently.</OL> <p></p> <b>Maintaining IMG Activities</b><br /> <p>The preliminary implementation described above contains all objects shipped with the aforementioned support packages, except the integration into IMG and into Easy Access menu. However, you can maintain the corresponding <B>customizing activities</B> by direct transaction calls. The corresponding documentation can be accessed by means of transaction SE61:</p> <UL><LI>Select entry HY SIMG Implementation Guide chapter (SIMG) from the drop-down list box for the document class.</LI></UL> <UL><LI>Choose the desired language from the listbox below.</LI></UL> <UL><LI>Enter the documentation object as described in the following list.</LI></UL> <p></p> <b>Settings for SAP Sourcing Integration</b><br /> <p>View: V_BBP_ES_SETTGS (to be maintained with transaction SM30)<br />Documentation Object: BBP_ES_SETTINGS<br />Executing this activity is mandatory in order to activate the solution.<br /></p> <b>Assign Condition Types</b><br /> <p>Viewcluster: VC_BBP_ES_CONMAP (use transaction SM34 for maintenance)<br />Documentation Object: BBP_ES_CONMAP<br />Executing this activity is mandatory if you want to use the integration for scheduling agreements.<br /></p> <b>BAdI: Adding Additional Data to the Customizing Data Extractor</b><br /> <p>BAdI-Definition: BADI_BBP_ES_ADDITIONAL_DATA (use transaction SE19 to create and maintain an implementation)<br />Documentation Object: BADI_BBP_ES_ADDITIONAL_DATA<br /></p> <b>BAdI: Download XML-Files to a Different Location</b><br /> <p>BAdI-Definition: BADI_BBP_ES_DOWNLAOD_XML (use transaction SE19 to create and maintain an implementation)<br />Documentation Object: BADI_BBP_ES_DOWNLAOD_XML<br /><br /><STRONG>Transactions Dedicated to the SAP Sourcing Integration</STRONG><br />Due to technical limitations the following transactions are shipped with the preliminary implementation but cannot be called from the Easy Access menu:<br /><br />BBP_ES_ANALYZE (Analyze RFC Data from SAP Sourcing)<br />BBP_ES_CUST_DOWNLOAD (Download Customizing Settings for SAP Sourcing)<br />BBP_ES_RFC_DELETE (Delete RFC Data Submitted from SAP Sourcing)<br />BBP_ES_SEARCH (Search for SAP Sourcing related Documents)<br /><br />The report documentation of each report provides a short overview on its functionality. Press the blue \"i\" button on the selection screen.<br /><br /><STRONG>Further Sources of Information</STRONG><br />Further information regarding the integration between ERP and SAP Sourcing can be found in the SAP Help Portal under SAP Business Suite --&gt; SAP Sourcing.<br /><br />The <B>configuration guide</B> to set up the integration is located on the SAP Service Marketplace with the direct link<br />http://service.sap.com/~form/sapnet?_SHORTKEY=01100035870000736013&amp;<br />Please also refer to note 1365842 for the latest updates.<br /><br />The <B>release notes</B> can be found in the respective system after the integration package has been implemented through Support Packages or by implementing the present note. Start transaction SE61, choose document class \"IN RELN Release Notes\" and select the desired language. Enter the document name ERP_ES_600_INT in all releases if you did the implementation by means of the present note. If the implementation was done through Support Packages the document name depends on the ERP release:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Release</TH><TH ALIGN=LEFT> Document Name</TH></TR> <TR><TD>6.00</TD><TD> ERP_ES_600_INT</TD></TR> <TR><TD>6.02</TD><TD> ERP_ES_602_INT</TD></TR> <TR><TD>6.03</TD><TD> ERP_ES_603_INT</TD></TR> <TR><TD>6.04</TD><TD> ERP_ES_604_INT</TD></TR> </TABLE> <p><br />Choose \"Display\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "LO-INT-ESO (SAP Sourcing Integration)"}, {"Key": "Other Components", "Value": "SRM-ESO-INT-ERP (ERP Integration for SAP Sourcing & CLM)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031315)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364669/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Transportation_Files_K7FK000832.zip", "FileSize": "810", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000423202009&iv_version=0004&iv_guid=EB93A6CEE6AEBB4B970A1492B69A9093"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1509572", "RefComponent": "MM-PUR-INT-ESO", "RefTitle": "Transfer of condition types from ERP to SAP Sourcing", "RefUrl": "/notes/1509572"}, {"RefNumber": "1458469", "RefComponent": "LOD-ODP-EXI", "RefTitle": "Integrating ERP with SAP Sourcing Wave 7", "RefUrl": "/notes/1458469"}, {"RefNumber": "1445779", "RefComponent": "LOD-ESO-ERP", "RefTitle": "Integrating ERP with SAP Sourcing (III)", "RefUrl": "/notes/1445779"}, {"RefNumber": "1430159", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Text with E-Sourcing vendor number is not created in ERP", "RefUrl": "/notes/1430159"}, {"RefNumber": "1413846", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Purchasing document creation fails with message M3 305", "RefUrl": "/notes/1413846"}, {"RefNumber": "1402292", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Changing the system name for SAP E-Sourcing 6.00", "RefUrl": "/notes/1402292"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1369122", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Changes in table BBP_ES_SETTINGS are not logged", "RefUrl": "/notes/1369122"}, {"RefNumber": "1365842", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Configuration Guide for Integration of SAP ERP/E-Sourcing W6", "RefUrl": "/notes/1365842"}, {"RefNumber": "1364357", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Defined language for material master transfer", "RefUrl": "/notes/1364357"}, {"RefNumber": "1329172", "RefComponent": "LOD-ESO-ERP", "RefTitle": "Integrating ERP with SAP Sourcing (I)", "RefUrl": "/notes/1329172"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1365842", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Configuration Guide for Integration of SAP ERP/E-Sourcing W6", "RefUrl": "/notes/1365842 "}, {"RefNumber": "1509572", "RefComponent": "MM-PUR-INT-ESO", "RefTitle": "Transfer of condition types from ERP to SAP Sourcing", "RefUrl": "/notes/1509572 "}, {"RefNumber": "1329172", "RefComponent": "LOD-ESO-ERP", "RefTitle": "Integrating ERP with SAP Sourcing (I)", "RefUrl": "/notes/1329172 "}, {"RefNumber": "1458469", "RefComponent": "LOD-ODP-EXI", "RefTitle": "Integrating ERP with SAP Sourcing Wave 7", "RefUrl": "/notes/1458469 "}, {"RefNumber": "1445779", "RefComponent": "LOD-ESO-ERP", "RefTitle": "Integrating ERP with SAP Sourcing (III)", "RefUrl": "/notes/1445779 "}, {"RefNumber": "1487336", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Extraction of payment terms with BBP_ES_CUST_DOWNLOAD", "RefUrl": "/notes/1487336 "}, {"RefNumber": "1430159", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Text with E-Sourcing vendor number is not created in ERP", "RefUrl": "/notes/1430159 "}, {"RefNumber": "1402292", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Changing the system name for SAP E-Sourcing 6.00", "RefUrl": "/notes/1402292 "}, {"RefNumber": "1413846", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Purchasing document creation fails with message M3 305", "RefUrl": "/notes/1413846 "}, {"RefNumber": "1364357", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Defined language for material master transfer", "RefUrl": "/notes/1364357 "}, {"RefNumber": "1369122", "RefComponent": "SRM-ESO-INT-ERP", "RefTitle": "Changes in table BBP_ES_SETTINGS are not logged", "RefUrl": "/notes/1369122 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1586893", "RefTitle": "Directory Traversal in LO-INT-ESO", "RefUrl": "/notes/0001586893"}]}}}}}