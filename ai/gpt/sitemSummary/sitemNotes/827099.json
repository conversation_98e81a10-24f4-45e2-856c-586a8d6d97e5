{"Request": {"Number": "827099", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 277, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004476512017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000827099?language=E&token=824C59BD63E605B395599EE1A7CE64DB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000827099", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000827099/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "827099"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "827099 - HR-PT: Leave Annual Summary - Compensation not displayed"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The compensation(Infotype 0416) is not displayed in RPLESUP0.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RPLESUP0; Leave Annual Summary; Mapa de f&#233;rias;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>July 19, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The correction described are included in a LCP. Please look below for the correction number.<br />The LCP includes:</p>\r\n<ul>\r\n<li>changes to include RPLESUPD, RPLESUPF, RPLESUPO;</li>\r\n</ul>\r\n<ul>\r\n<li>changes in type group: PPT04</li>\r\n</ul>\r\n<ul>\r\n<li>new entry in text symbols: 012 Compens</li>\r\n</ul>\r\n<p><br />The LCP's are the prefered method for implementing standard changes in your system or advance delivery attached in this note. To know which<br />files corresponding to your release, please see below:</p>\r\n<ul>\r\n<li>Release 4.5B: L4DK119946.CAR;</li>\r\n</ul>\r\n<ul>\r\n<li>Release 4.6B: L9BK126033.CAR;</li>\r\n</ul>\r\n<ul>\r\n<li>Release 4.6C: L9CK183434.CAR;</li>\r\n</ul>\r\n<ul>\r\n<li>Release 4.70: L6BK087676.CAR;</li>\r\n</ul>\r\n<p><br />Still, if you wish, you can implement them yourself by performing the source code corrections as indicated below.</p>\r\n<ul>\r\n<li>Type Group:PPT04</li>\r\n</ul>\r\n<p><br />TYPE-POOL PPT04 .<br /><br />...<br /><br />*Internal table to use in ALV - RPLESUP0<br />TYPES: BEGIN OF PPT04_LIST_HEADERDATA,<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;EXPAND&#160;&#160;TYPE C,&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; \"expand indicator<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;PERNR LIKE PERNR-PERNR,&#160;&#160;&#160;&#160;&#160;&#160; \"Personnel number<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ENAME LIKE P0001-ENAME,&#160;&#160;&#160;&#160;&#160;&#160; \"Employee name<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ANZHL LIKE P2006-ANZHL,&#160;&#160;&#160;&#160;&#160;&#160; \"Entitlement<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; TAKEN LIKE P2001-ABWTG,&#160;&#160;&#160;&#160;&#160;&#160; \"Taken<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; REST&#160;&#160;LIKE P2001-ABWTG,&#160;&#160;&#160;&#160;&#160;&#160; \"Rest<br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;COMPEN LIKE P0416-NUMBR,&#160;&#160;&#160;&#160;&#160;&#160;\"Compensation &lt;--insert this line</strong><br />&#160;&#160;&#160;&#160;&#160;&#160; END OF&#160;&#160;PPT04_LIST_HEADERDATA.<br /><br />...</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I823284)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000827099/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BC3", "URL": "/supportpackage/SAPKE45BC3"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BA5", "URL": "/supportpackage/SAPKE46BA5"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C96", "URL": "/supportpackage/SAPKE46C96"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47041", "URL": "/supportpackage/SAPKE47041"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50008", "URL": "/supportpackage/SAPKE50008"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 1, "URL": "/corrins/0000827099/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "638704 ", "URL": "/notes/638704 ", "Title": "HR-PT: Corrections to Leave Annual Summary (rplesup0)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "741439 ", "URL": "/notes/741439 ", "Title": "HR-PT: Corrections to Leave annual summary report", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "791564 ", "URL": "/notes/791564 ", "Title": "HR-PT: Compensation not correctly considered in RPLESUP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "504807 ", "URL": "/notes/504807 ", "Title": "HR-PT: General corrections to report RPLESUP0", "Component": "PY-PT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}