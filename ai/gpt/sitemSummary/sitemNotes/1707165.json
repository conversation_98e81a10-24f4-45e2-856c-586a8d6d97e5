{"Request": {"Number": "1707165", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 507, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010143852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=7C717F9F22A45824BDB21EB81446ABF0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1707165"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2012"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-SI-FICA"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-CA-SI"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Slovenia", "value": "XX-CSC-SI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-SI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-CA-SI", "value": "XX-CSC-SI-FICA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-SI-FICA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1707165 - ISO SEPA Credit Transfer for Slovenia"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>According to Slovenian bank standard ISO SEPA XML CT payment format is available for banks in Slovenia.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SEPA CT, FICA, Credit Transfer, Slovenia, SIFI-SEPA_40</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a new SEPA payment medium Credit Transfer v.4.0 (bank transfer) on the basis of pain.001.001.02 (ISO 20022).</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please install corresponding Support Package<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I071922)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I071922)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1707549", "RefComponent": "XX-CSC-SI-FICA", "RefTitle": "ISO SEPA DD,Direct Debit format for Slovenia", "RefUrl": "/notes/1707549"}, {"RefNumber": "1494352", "RefComponent": "XX-CSC-SI-IS-U", "RefTitle": "Slovenian specific functionality for IS-UT 604", "RefUrl": "/notes/1494352"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2147823", "RefComponent": "XX-CSC-SI-FICA", "RefTitle": "FI-CA SEPA Credit Transfer for Slovenia - missing address correction", "RefUrl": "/notes/2147823 "}, {"RefNumber": "1707549", "RefComponent": "XX-CSC-SI-FICA", "RefTitle": "ISO SEPA DD,Direct Debit format for Slovenia", "RefUrl": "/notes/1707549 "}, {"RefNumber": "1494352", "RefComponent": "XX-CSC-SI-IS-U", "RefTitle": "Slovenian specific functionality for IS-UT 604", "RefUrl": "/notes/1494352 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CEEISUT 604", "SupportPackage": "SAPK-60411INCEEISUT", "URL": "/supportpackage/SAPK-60411INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 606", "SupportPackage": "SAPK-60602INCEEISUT", "URL": "/supportpackage/SAPK-60602INCEEISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "CEEISUT", "NumberOfCorrin": 3, "URL": "/corrins/**********/532"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; CEEISUT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;IS-U and IS-T l...|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60410INCEEISUT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60601INCEEISUT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <OL>1. Change report texts of /SAPCE/FKSI_SEPA_CT_MOVE</OL> <P>Start transaction SE38 and for report /SAPCE/FKSI_SEPA_CT_MOVE.<br/><br/>Add Text elements by selection the option \"Text elements\" on the  selection screen. Press button \"Change\" and enter following elements on tabpage \"Text Symbols\" with maximum length entered:</P> <UL><LI>009 End test run 13 13</LI></UL> <UL><LI>010 Final run 9 9</LI></UL> <UL><LI>015 Identification contains incompatible characters. Overwrite with space? 70 132</LI></UL> <UL><LI>016 Reguh lines copied: 19 19</LI></UL> <UL><LI>017 Regup lines copied: 19 19</LI></UL> <UL><LI>018 Reguv lines copied: 19 19</LI></UL> <UL><LI>T00 Selection criteria 18 23</LI></UL> <UL><LI>T01 Processing control 18 19</LI></UL> <UL><LI>T03 Report output 13 21</LI></UL> <P><br/>Add Selection Texts on tabpage \"Selection Texts\":</P> <UL><LI>P_CLEAR Clear tables</LI></UL> <UL><LI>P_LAUFD Identification date</LI></UL> <UL><LI>P_LAUFI Identification</LI></UL> <UL><LI>P_RZAWEF Payment method for move</LI></UL> <UL><LI>P_RZAWET Payment method for replacement</LI></UL> <UL><LI>P_TEST Test run</LI></UL> <P><br/>Once done, please activate the content via key \"Ctrl+F3\" - icon \"Activate\"</P> <OL>2. Create transaction /SAPCE/FKSI_SEPA_CTM</OL> <P>Start transaction SE93 and enter:<br/>Transaction code: /SAPCE/FKSI_SEPA_CTM</P> <UL><LI>Short text: Moves content of FI-CA to FI tables</LI></UL> <UL><LI>Start object: Program and selection screen</LI></UL> <UL><LI>Classification: Professional User Transaction</LI></UL> <UL><LI>GUI support: SAPGUI for Windows</LI></UL> <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; CEEISUT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;IS-U and IS-T l...|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60410INCEEISUT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60601INCEEISUT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <OL>1. Create Data Element /SAPCE/FKSI_SEPA_MOVE_TEST</OL> <P>Start transaction SE11 and create data element<br/>/SAPCE/FKSI_SEPA_MOVE_TEST<br/>Short Text: Perform test run<br/>Domain: XFELD<br/>Labels: Short&nbsp;&nbsp;&nbsp;&nbsp; 10&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test run<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Medium&nbsp;&nbsp;&nbsp;&nbsp;15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test run<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Long&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;20&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Perform test run<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Heading&nbsp;&nbsp;30&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Perform test run<br/></P> <OL>2. Create search help</OL> <P>Start transaction SE11 and create search help /SAPCE/FKSI_LAUFDI<br/>Short description: Payment runs.<br/>Parameters:<br/><br/>Search help parameter: AKTYP<br/>IMP: checked<br/>EXP: checked<br/>LPos: 1<br/>SPos: 0<br/>SDis: unchecked<br/>Data element: AKTYP_KK<br/>Modified: checked<br/><br/>Search help parameter: LAUFD<br/>IMP: unchecked<br/>EXP: checked<br/>LPos: 2<br/>SPos: 1<br/>SDis: unchecked<br/>Data element: LAUFD_KK<br/>Modified: unchecked<br/><br/>Search help parameter: LAUFI<br/>IMP: unchecked<br/>EXP: checked<br/>LPos: 3<br/>SPos: 2<br/>SDis: unchecked<br/>Data element: LAUFI_PAY<br/>Modified: unchecked<br/></P> <OL>3. Create structure /SAPCE/FKSI_MOVE_PAR_LT</OL> <P>Start transaction SE11 and create new Structure with:<br/>Short Text: Slovenia: Move Parameters<br/>Enhancement Category: Can be enhanced (character-type or numeric)<br/>Fields:<br/><br/>LAUFD  LAUFD_KK<br/>LAUFI  LAUFI_PAY<br/><br/>Add search help SAPCE/FKSI_LAUFDI to both fields of /SAPCE/FKSI_MOVE_PAR_LT. For AKTYP parameter fill in the constant column  'PAYP', for other fields choose parameters of same name.</P> <OL>4. Add new messages</OL> <P>Start transaction SE91 and in message class /SAPCE/FKSI create following messages:</P> <OL><OL>a) ID: 010</OL></OL> <P>Message text: Data move with these parameters has already been performed.<br/>Self-explanatory: checked</P> <OL><OL>b) ID: 011</OL></OL> <P>Message text: Inconsistency in data - tables &amp;1 already contain inserting data.<br/>Self-explanatory: checked</P> <OL><OL>c) ID: 012</OL></OL> <P>Message text: No records for given input data.<br/>Self-explanatory: checked</P> <OL><OL>d) ID: 013</OL></OL> <P>Message text: Data move was completed successfully.<br/>Self-explanatory: checked</P> <OL><OL>e) ID: 015</OL></OL> <P>Message text: Action cancelled by the user.<br/>Self-explanatory: checked</P> <OL><OL>f) ID: 016</OL></OL> <P>Message text: Identification contains incompatible characters.<br/>Self-explanatory: checked<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "CEEISUT", "ValidFrom": "604", "ValidTo": "604", "Number": "1707165 ", "URL": "/notes/1707165 ", "Title": "ISO SEPA Credit Transfer for Slovenia", "Component": "XX-CSC-SI-FICA"}, {"SoftwareComponent": "CEEISUT", "ValidFrom": "606", "ValidTo": "606", "Number": "1707165 ", "URL": "/notes/1707165 ", "Title": "ISO SEPA Credit Transfer for Slovenia", "Component": "XX-CSC-SI-FICA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}