{"Request": {"Number": "3320010", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 248, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000682602023"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003320010?language=E&token=15075D5AA8240D56F2BCED8282FF03A1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003320010", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3320010"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CO-PA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Profitability Analysis"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Profitability Analysis", "value": "CO-PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3320010 - S4TWL - Profitability Segment Number Change of Type"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023 or a later release from SAP S/4HANA release 2022 or earlier.</p>\r\n<p>During this upgrade, the data type of the profitability segment number has been changed from data type NUMC length 10 to data type CHAR length 10.</p>\r\n<p>This enables the use of alphanumeric profitability numbers after the decimal profitability segments have run out.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Domain RKEOBJNR. Data element RKEOBJNR. Global field&#160;ProfitabilitySegment. Profitability Analysis.&#160;Margin Analysis. Costing-Based profitability analysis.</p>\r\n<p>Decommissioning of&#160;CDS&#160;field ProfitabilitySegment. Replace usages with ProfitabilitySegment_2</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023 or a later release from SAP S/4HANA release 2022 or earlier.&#160;The domain change is relevant for&#160;Margin Analysis and Costing-Based Profitability Analysis.</p>\r\n<p><strong>Task</strong>: Check that Profitability Analysis is activated in your system.</p>\r\n<p>Procedure: Go to the implementation guide using transaction SPRO --&gt; Controlling --&gt; Profitability Analysis --&gt; Flows of Actual Values --&gt; Activate Profitability Analysis. If there is a check mark in the columns &#8220;costing-based&#8221; or &#8220;account-based&#8221; this SAP Note is relevant for you.</p>\r\n<p><strong>Changes to Customer Coding Required</strong></p>\r\n<p>The&#160;domain change has several technical consequences which are explained in detail in SAP Note 3320427. This note contains a detailed list of the different technical consequences of the type change, and how you must adapt your coding. The two following aspects illustrate the necessary code changes:</p>\r\n<p><strong>Checks that a Profitability Segment is Assigned - IS INITIAL Checks</strong></p>\r\n<p>The ABAP statements \"Is initial\" and \"is not initial\" can no longer be used to check if fields of type RKEOBJNR are empty.</p>\r\n<p>The previous initial value of profitability segment number was '**********, while the new initial value will be 'space'. Since the existing database entries containing the old initial value are not updated, either the old or the new initial value can occur and must be checked. In order to encapsulate the complexity of the initial (or not initial) check we recommend to use the method&#160;cl_fco_copa_paobjnr=&gt;is_initial( ) to check if a field is empty.</p>\r\n<p><strong>Alphanumeric Values</strong></p>\r\n<p>In case you have already used more than 10% of the available profitability segment numbers, we suggest to prepare your code for alphanumeric numbers that you may require in the future. Please see the SAP Note&#160;3321347 for more details.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<h3 data-toc-skip>Business-Process Related Information</h3>\r\n<p>The change of the data type of profitability segment number has no business-related impact.</p>\r\n<h3 data-toc-skip><strong>Required and Recommended Actions</strong></h3>\r\n<p>To avoid any errors in the system behaviour due to the change of the data type in the profitability segment field, you need to adapt your customer coding for this change. The adaption of the initial value check needs to be carried out, regardless of whether you will be using the additional alphanumeric profitability segments or not.</p>\r\n<p><strong>Decommissioning of Field&#160;ProfitabilitySegment in C1 Released CDS Views</strong></p>\r\n<p>In order to identify coding that needs to be adapted, the&#160;Code Inspector checks are executed as part of the related simplification item. If you are using the decommissioned CDS field&#160;ProfitabilitySegment in customer-defined CDS Views, you must switch to the replacement field&#160;ProfitabilitySegment_2.</p>\r\n<p>This SAP Note only covers the actions that all customers must take. <em>For customers that require more profitability segment numbers see SAP Note &lt;needs to be created&gt;.</em></p>\r\n<p><span style=\"font-size: 16px; font-weight: bold;\">What to Do Before the Upgrade</span></p>\r\n<p>Please ensure that you update the check logic to the most recent version by implementing all relevant SAP Notes from SAP Note&#160;2436688,&#160; otherwise you might get wrong or missing check results.</p>\r\n<p>Whether you are using the initial check or select statement, please investigate if you have set PAOBJNR to 'empty' in your customer coding. The change of the underlying technical domain in the ABAP dictionary could lead to changed behavior when moving&#160;profitability segment numbers into different types, or when performing calculations.</p>\r\n<p>As described before, the initial value of the field PAOBJNR will be 'space' rather than '**********' for new profitability segments. However, old values on the database will not be updated during the upgrade process. This means that the code checking whether the field&#160;PAOBJNR is empty or not, needs to check for both values:</p>\r\n<p><strong>ABAP Code Examples</strong></p>\r\n<p>ABAP code such as</p>\r\n<p>&#160; &#160; <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">data: lv_paobjnr type rkeobjnr.<br /></span><span style=\"background-color: #f5e9ed; color: #2e2e2e;\"><br />&#160; &#160; &#160; &#160;...&#160; &#160;</span></p>\r\n<p><span style=\"background-color: #f5e9ed; color: #2e2e2e;\">&#160; &#160;<em>\" check if prof. segment isn initial</em></span><em style=\"color: #2e2e2e;\"><br /></em><span style=\"background-color: #f5e9ed; color: #2e2e2e;\">&#160; &#160; IF </span><span style=\"background-color: #f5e9ed; color: #2e2e2e;\">lv_paobjnr IS INITIAL.</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160; &#160; ...</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; ENDIF.</span></p>\r\n<p>has to be changed to</p>\r\n<p>&#160; &#160;&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">data: lv_paobjnr type rkeobjnr.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160;...&#160; &#160;</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160;<em>\" check if prof. segment is initial using predefined constant values&#160;<br />&#160; &#160; &#160;\" for either the new or the old initial value<br /></em></span><span style=\"background-color: #f5e9ed; color: #2e2e2e;\"><br />&#160; &#160; IF cl_fco_copa_paobjnr=&gt;is_initial( lv_paobjnr) = abap_true</span><span style=\"background-color: #f5e9ed; color: #2e2e2e;\">.</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160; &#160; ...</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; ENDIF.</span></p>\r\n<p>&#160;Likewise the IS NOT INITIAL check has to be changed as follows:</p>\r\n<p>&#160;&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">data: lv_paobjnr type rkeobjnr.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160; &#160;...&#160; &#160;</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160;&#160;<em>\" check if prof. segment is not initial<br /></em>&#160; &#160; IF&#160;lv_paobjnr IS NOT INITIAL.</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160; &#160; ...</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; ENDIF.&#65279;</span></p>\r\n<p>&#65279;has to be changed to&#65279;</p>\r\n<p>&#160;&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">data: lv_paobjnr type rkeobjnr.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160; &#160;...&#160; &#160;</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;<em>\" check if prof. segment is not initial using predefined constant values&#160;<br />&#160; &#160; &#160;\" for either the new and the old initial value</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; IF&#160;cl_fco_copa_paobjnr=&gt;is_initial( lv_paobjnr) = abap_false.</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; &#160; &#160; ...</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; &#160; ENDIF.&#65279;</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028268)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D058386)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003320010/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003320010/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3320427", "RefComponent": "CO-PA", "RefTitle": "Profitability Segment Number Change of Type - More details on the coding adjustments", "RefUrl": "/notes/3320427"}, {"RefNumber": "2436688", "RefComponent": "BC-DWB-CEX-CCM", "RefTitle": "Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App", "RefUrl": "/notes/2436688"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3386998", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2023: Release Information Note for Finance", "RefUrl": "/notes/3386998 "}, {"RefNumber": "3321347", "RefComponent": "CO-PA", "RefTitle": "Support for more Profitability Segment Numbers - Enabling Alphanumeric Profitability Segment Numbers", "RefUrl": "/notes/3321347 "}, {"RefNumber": "3320427", "RefComponent": "CO-PA", "RefTitle": "Profitability Segment Number Change of Type - More details on the coding adjustments", "RefUrl": "/notes/3320427 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}