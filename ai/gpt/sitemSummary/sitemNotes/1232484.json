{"Request": {"Number": "1232484", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 547, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016561272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001232484?language=E&token=94A952988264B9EC1E7FA76305041CC7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001232484", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001232484/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1232484"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.03.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-F"}, "SAPComponentKeyText": {"_label": "Component", "value": "Value Added Tax (VAT)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Value Added Tax (VAT)", "value": "FI-GL-GL-F", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-F*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1232484 - How to use the VAT due date, tax reporting date VATDATE"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>There is a new entry field (VATDATE) in the document header (BKPF) of the financial accounting document that can be used as VAT due date or tax reporting date.<br />When a document is being entered (for example FB01, FB60, MIRO) then the VATDATE can be entered or changed on the tax screen. A specific BADI proposes usually the posting date or document date.<br />Once the document is posted the VATDATE can also be changed using the change transaction FB02. This changeabilty must just be switched on in the usual way.<br />This note describes the features of this functionality and will be amended&#160;if necessary.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>VATDATE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The functionality is based on legal requirements in different countries. <br /><strong>Incomplete list of countries currently known: </strong><br /><strong>Poland, Hungary, Slovakia, Spain.</strong><br />For each country it must be checked whether the functionality fulfills the legal requirements.<br />The functionality has originally been implemented by note 1023317 in release ER2005, its enhancement packages and in ERP2004 and by note 1235415 in release 4.70 and 4.6C. As many objects are involved in the correction the functionality can only be imported by applying the corresponding support package.<br />For Reporting for Eastern European countries see note 1038448.<br /><br />In order to ensure data transport of the new field VATDATE some programs and structures must be regenerated. Therefore please run the following programs:<br />- SAPFACCG<br />- RFBIPPG0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>1. Activation of the functionality</ol>\r\n<p>The functionality can be activated per company code in the usual customizing for the company code 'Enter Global Parameters'.<br />(transaction OBY6 or SM30: View V_001_B)<br />For this purpose the new field XVATDATE has been added to table T001.</p>\r\n<ol>2. General rule</ol>\r\n<p>If the functionality is active then every financial accounting document that is tax relevant ( i.e. contains a tax code in a g/l-line item) must have a valid date in the field VATDATE. If the document is not tax relevant, or the functionality is off in the company code, the date will be initial.</p>\r\n<ol>3. Exchange rate determination for tax line items</ol>\r\n<p>There exists the well established functionality that the local (company code) currency amount of tax line items in a document can be calculated with a separate exchange rate, different from the exchange rate used for the other line items of the document. In addition to the already existing options the VATDATE can now also be used for the determination of the exchange rate.<br />(transaction OBC8 or SM30: View V_001_V)<br />For this purpose the new value '5' for domain TXKRS has been added.</p>\r\n<ol>4. BADI for determining and checking the VATDATE</ol>\r\n<p>a) Release ERP2005 (ECC 600) and higher<br />There is a new Enhancement Spot VATDATE_RULES with the BADI definition VATDATE_VALUES.<br />A default implementation is delivered: VATDATE_VALUES_DEFAULT_SAP<br />(Use transaction SE18 to see the details of the BADI or SE19 to see the implementations.)<br /><br />b) Release ERP2004<br />There is a new BADI definition VATDATE_VALUES.<br />A standard implementation is provided: VATDATE_VALUES_SAP<br /><br />c) Release 4.70 and 4.6C<br />There is a new BADI definition VATDATE_VALUES_46.<br />A standard implementation is provided: VATDATE_VALUES_SAP46<br /><br /><br />Common features of the BADI in all releases:<br />Two methods are provided:<br />- VATDATE_DETERMINE<br />- VATDATE_CHECK<br /><br />Purpose of the BADI:<br />Only the method VATDATE_CHECK is called from standard programs.<br />Within the default implemetation the method VATDATE_DETERMINE is called if the document requires a VATDATE but no value has been provided or could have been entered so far. The method VATDATE_DETERMINE determines the initial value that will be proposed, for example on the screens.<br />By default either posting date or document date is proposed, depending in your customizing in the company code(transaction OBCK).<br />A value that has been provided, proposed or entered will be checked in method VATDATE_CHECK whether it meets specific conditions or rules.<br />It is up to the customer to create his own implementation where these specific rules are defined. The default rule is that the VATDATE must not be less than the posting date and document date. A customized message is issued (FF 785) if it is less.<br /><br />Where has the BADI been implemented?<br />The BADI has been implemented in<br />- the function group TAX1 (Taxes)<br />- the program SAPMF05A (entering financial accounting documents, for example FB01)<br />- the program SAPMF05L (displaying/changing financial accounting documents, for example FB02)<br />- the function group RWCL (general interface to accounting, for example BAPIs, VF01)<br />- the function group SAPLFCJ_PROCESS_MANAGER (Cash Journal)<br />- the program SAPMFCJ0 (Cash Journal)<br />- the function group F040 (Parking or Preliminary Posting of accounting documents)<br />- the function group FACI (FI Interface)<br />- the function group FDCB (Dialog Components Subledger Accounting, for example FB60)</p>\r\n<ol>5. BADI for determining exchange rate for tax line items</ol>\r\n<p>a) Release ERP2005 (ECC 600) and higher<br />There is a new Enhancement Spot ESPOT_TAX_EXCHANGE_RATE with the BADI definition BADI_TAX_EXCHANGE_RATE.<br />A default implementation is delivered: TXKRS_SET_DEFAULT_VATDATE<br /><br />b) Release ERP2004 (ECC 500)<br />There is a new BADI definition TAX_EXCHANGE_RATE.<br />A standard implementation is provided: TXKRS_VATDATE_SAP<br />There an exchange is determined only if the VATDATE-functionality is active and only for certain reference procedures.<br /><br />c) Release 4.70 and 4.6C<br />There is a new BADI definition TAX_EXCHANGE_RATE_46.<br />A standard implementation is provided: TXKRS_VATDATE_SAP46<br /><br />Common features of the BADI in all releases:<br />Only one method is provided: TXKRS_SET<br /><br />Purpose of the BADI:<br />The purpose of the BADI is to set the exchange rate for tax line items.<br />The default implementation determines an exchange rate only for billing documents and cash journal documents.<br /><br />Where has the BADI been implemented?<br />The BADI has been implemented ONLY in function group RWCL, the general interface to accounting.<br /><br />Remark: When a financial accounting document is entered directly with a transaction (for example FB01, FB60, MIRO) then it is of course not necessary that this BADI is being called as the exchange rate determination works there in a different way.</p>\r\n<ol>6. Standard (tax) reports</ol>\r\n<p>Some&#160;standard (tax) reports support directly the selection of documents according to the VATDATE.<br />Examples: RFUMSV00, RFUMSV10, RFASLM00, RFASLD20,&#160;RFPUMS00.<br />For this purpose the field has been added to the logical database BRF.<br />But, in order to prevent that every report using BRF shows the select option for the VATDATE although it does not deal with taxes, the field has been added as hidden where it is switched off. For reports RFUMSV00 and RFUMSV10 it has been switched on permanently. But thereby, in other reports using BRF it can now easily be switched on via the DYNAMIC SELECTIONS.</p>\r\n<ol>7. Country specific (tax) reports</ol>\r\n<p>Some reports used mainly in Easter Europe have been adapted.<br />Example: RFUMSVHU, RFIDPL06.<br />See note 1038448 and related.</p>\r\n<ol>8. BAPIs</ol>\r\n<p>Only the most important Bapi BAPI_ACC_DOCUMENT_POST supports the entry of the VATDATE in its header structure BAPIACHE09.</p>\r\n<ol>9. Batch-Input</ol>\r\n<p>Batch-Input for FB01, FBV1&#160;is supported.</p>\r\n<ol>10. Document reversal</ol>\r\n<p>A reversed document has the same VATDATE as the original one. It can be changed with FB02.<br />In FB08 the VATDATE to be used for the reversal document can be entered.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160; 11. Populating BKPF-VATDATE in existing documents</p>\r\n<p>Check note 1038448.<br /><br /><span style=\"text-decoration: underline;\">Important:</span><br />For additional information please also refer to the PDF documentation \"Reporting_by_VAT_Due_Date.pdf\" attached to this Note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000160"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021772)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001232484/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001232484/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232484/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232484/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232484/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232484/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232484/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232484/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001232484/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Reporting_by_Tax_Date.pdf", "FileSize": "316", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000409262008&iv_version=0013&iv_guid=CC140942E5E0D547B32527BFBA39E37D"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1880631", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Incorrect tax rate with batch input", "RefUrl": "/notes/1880631"}, {"RefNumber": "1874119", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Deletion of VATDATE if there is no tax code in doc", "RefUrl": "/notes/1874119"}, {"RefNumber": "1842469", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Date missing in leading company code", "RefUrl": "/notes/1842469"}, {"RefNumber": "1778054", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE remains empty if only vendor data is entered", "RefUrl": "/notes/1778054"}, {"RefNumber": "1619358", "RefComponent": "FI-GL-GL-F", "RefTitle": "Down payment: Tax rate if BTE 2051 and VATDATE active", "RefUrl": "/notes/1619358"}, {"RefNumber": "1599179", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1599179"}, {"RefNumber": "1573940", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1573940"}, {"RefNumber": "1567029", "RefComponent": "FI-GL-GL-F", "RefTitle": "Badi VATDATE_RULES: VF11 w/ new cancellation, FB08 w/ input", "RefUrl": "/notes/1567029"}, {"RefNumber": "1533216", "RefComponent": "FI-GL-GL-F", "RefTitle": "Documentation Updates - Tax Date", "RefUrl": "/notes/1533216"}, {"RefNumber": "1527469", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE tax reporting date not active but ready for input", "RefUrl": "/notes/1527469"}, {"RefNumber": "1489350", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB02: Problems after changing tax reporting date", "RefUrl": "/notes/1489350"}, {"RefNumber": "1484985", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date filled even though document w/o tax code", "RefUrl": "/notes/1484985"}, {"RefNumber": "1441194", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE in FB02 cannot be changed due to BSET-STMDT", "RefUrl": "/notes/1441194"}, {"RefNumber": "1439166", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLD20: FI-CA interface and tax reporting date", "RefUrl": "/notes/1439166"}, {"RefNumber": "1409792", "RefComponent": "FI-GL-GL-F", "RefTitle": "Retaining tax reporting date even if tax base is zero", "RefUrl": "/notes/1409792"}, {"RefNumber": "1408702", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date is not transferrerd during reversal", "RefUrl": "/notes/1408702"}, {"RefNumber": "1404919", "RefComponent": "FI-GL-GL-F", "RefTitle": "Performance of BAdI VATDATE_VALUES and TAX_EXCHANGE_RATE", "RefUrl": "/notes/1404919"}, {"RefNumber": "1391393", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Missing reset if not tax-relevant", "RefUrl": "/notes/1391393"}, {"RefNumber": "1391341", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Incorrect initialization for direct input", "RefUrl": "/notes/1391341"}, {"RefNumber": "1381149", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUMSV00: Tax reporting date and electronic advance return", "RefUrl": "/notes/1381149"}, {"RefNumber": "1372794", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Incorrect initialization with list editing FBV2", "RefUrl": "/notes/1372794"}, {"RefNumber": "1366172", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB02: Update termination after changing row and VATDATE", "RefUrl": "/notes/1366172"}, {"RefNumber": "1357159", "RefComponent": "FI-GL-GL-F1", "RefTitle": "RFUMSV50: VATDATE in transf. postng docs for deferred tax", "RefUrl": "/notes/1357159"}, {"RefNumber": "1355851", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: BAdI in cash journal proposes incorrect date", "RefUrl": "/notes/1355851"}, {"RefNumber": "1354521", "RefComponent": "FI-GL-GL-F", "RefTitle": "FB10: Field VATDATE not on screen 137", "RefUrl": "/notes/1354521"}, {"RefNumber": "1353606", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/1353606"}, {"RefNumber": "1352384", "RefComponent": "FI-AF-DPC", "RefTitle": "Tax reporting date for down payment chains is missing", "RefUrl": "/notes/1352384"}, {"RefNumber": "1347046", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Field missing in batch input parking (FBV1)", "RefUrl": "/notes/1347046"}, {"RefNumber": "1338580", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "MR8M: Reporting date is incorrect", "RefUrl": "/notes/1338580"}, {"RefNumber": "1302818", "RefComponent": "FI-GL-GL-F", "RefTitle": "Unable to manually implement Notes 1023317 or 1235415", "RefUrl": "/notes/1302818"}, {"RefNumber": "1299302", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date is not saved", "RefUrl": "/notes/1299302"}, {"RefNumber": "1296949", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB02: Error F5 165 after changing VAT date", "RefUrl": "/notes/1296949"}, {"RefNumber": "1293848", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Error message FF 786 issued in AC interface", "RefUrl": "/notes/1293848"}, {"RefNumber": "1289293", "RefComponent": "XX-CSC-PL-FI", "RefTitle": "Polish VAT Workaround around: Maintenance finish", "RefUrl": "/notes/1289293"}, {"RefNumber": "1288600", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: No forgn curcy translation if tax entered manually", "RefUrl": "/notes/1288600"}, {"RefNumber": "1280152", "RefComponent": "XX-CSC-ES", "RefTitle": "Legal change on VAT tax date Spain", "RefUrl": "/notes/1280152"}, {"RefNumber": "1270222", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date: Manual implementation of Note 1023317", "RefUrl": "/notes/1270222"}, {"RefNumber": "1265692", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Entry overwritten due to missing tax code", "RefUrl": "/notes/1265692"}, {"RefNumber": "1235415", "RefComponent": "FI-GL-GL-F", "RefTitle": "Legal Change: Vat Due Date for release 4.6C and 4.70", "RefUrl": "/notes/1235415"}, {"RefNumber": "1179304", "RefComponent": "FI-GL-GL-F", "RefTitle": "Down payment clearing with own tax rate", "RefUrl": "/notes/1179304"}, {"RefNumber": "1162771", "RefComponent": "FI-GL-GL-F", "RefTitle": "FB02: No checks when changing tax reporting date", "RefUrl": "/notes/1162771"}, {"RefNumber": "1137492", "RefComponent": "FI-GL-GL-F", "RefTitle": "Changeability of tax reporting date", "RefUrl": "/notes/1137492"}, {"RefNumber": "1053111", "RefComponent": "XX-CSC-XX", "RefTitle": "VAT Due date check: modification for PL and CZ", "RefUrl": "/notes/1053111"}, {"RefNumber": "1038448", "RefComponent": "XX-CSC-PL", "RefTitle": "VAT due date: Country specific Reporting + Migration Tool", "RefUrl": "/notes/1038448"}, {"RefNumber": "1023317", "RefComponent": "FI-GL-GL-F", "RefTitle": "Legal Change: Vat Due date", "RefUrl": "/notes/1023317"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3204819", "RefComponent": "MM-IV-INT-TAX", "RefTitle": "Transaction MR8M - error message FF785 - SAP ERP & SAP S/4HANA", "RefUrl": "/notes/3204819 "}, {"RefNumber": "2119270", "RefComponent": "FI-GL-GL-F", "RefTitle": "Missing VATDATE in FI, not transferred from MM", "RefUrl": "/notes/2119270 "}, {"RefNumber": "2465416", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE change", "RefUrl": "/notes/2465416 "}, {"RefNumber": "2431362", "RefComponent": "XX-CSC-PL-FI", "RefTitle": "MIRO VATDATE - Reason for error FF785", "RefUrl": "/notes/2431362 "}, {"RefNumber": "2606470", "RefComponent": "XX-CSC-XX", "RefTitle": "ACR: Reporting Date field as the main selection criteria", "RefUrl": "/notes/2606470 "}, {"RefNumber": "2132446", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Ensure that all lines from ACCIT have the same value", "RefUrl": "/notes/2132446 "}, {"RefNumber": "1995942", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Document without BSET -> VATDATE if tax code in document", "RefUrl": "/notes/1995942 "}, {"RefNumber": "1991531", "RefComponent": "FI-GL-GL-F", "RefTitle": "VF11: FF786 because BKPF-VATDATE initial due to no BSET", "RefUrl": "/notes/1991531 "}, {"RefNumber": "1943239", "RefComponent": "XX-CSC-PL-FI", "RefTitle": "Validating tax reporting Date based on document date", "RefUrl": "/notes/1943239 "}, {"RefNumber": "1931772", "RefComponent": "XX-CSC-PL-FI", "RefTitle": "2014 VAT law changes, UE directive implementation", "RefUrl": "/notes/1931772 "}, {"RefNumber": "1923182", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Date missing in leading company code", "RefUrl": "/notes/1923182 "}, {"RefNumber": "1599179", "RefComponent": "MM-IV-LIV-PP", "RefTitle": "MIRO/MIR7: Document information for BAdI VATDATE_VALUES", "RefUrl": "/notes/1599179 "}, {"RefNumber": "1874119", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Deletion of VATDATE if there is no tax code in doc", "RefUrl": "/notes/1874119 "}, {"RefNumber": "1880631", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Incorrect tax rate with batch input", "RefUrl": "/notes/1880631 "}, {"RefNumber": "1842469", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Date missing in leading company code", "RefUrl": "/notes/1842469 "}, {"RefNumber": "1778054", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE remains empty if only vendor data is entered", "RefUrl": "/notes/1778054 "}, {"RefNumber": "1740995", "RefComponent": "XX-CSC-RAT", "RefTitle": "VAT Pro Rata: Tax Reporting Date", "RefUrl": "/notes/1740995 "}, {"RefNumber": "1235415", "RefComponent": "FI-GL-GL-F", "RefTitle": "Legal Change: Vat Due Date for release 4.6C and 4.70", "RefUrl": "/notes/1235415 "}, {"RefNumber": "1404919", "RefComponent": "FI-GL-GL-F", "RefTitle": "Performance of BAdI VATDATE_VALUES and TAX_EXCHANGE_RATE", "RefUrl": "/notes/1404919 "}, {"RefNumber": "308001", "RefComponent": "XX-CSC-SK", "RefTitle": "Slovak local enhancements, general information", "RefUrl": "/notes/308001 "}, {"RefNumber": "1573940", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Proposal of tax reporting date not required", "RefUrl": "/notes/1573940 "}, {"RefNumber": "1619358", "RefComponent": "FI-GL-GL-F", "RefTitle": "Down payment: Tax rate if BTE 2051 and VATDATE active", "RefUrl": "/notes/1619358 "}, {"RefNumber": "1567029", "RefComponent": "FI-GL-GL-F", "RefTitle": "Badi VATDATE_RULES: VF11 w/ new cancellation, FB08 w/ input", "RefUrl": "/notes/1567029 "}, {"RefNumber": "1162771", "RefComponent": "FI-GL-GL-F", "RefTitle": "FB02: No checks when changing tax reporting date", "RefUrl": "/notes/1162771 "}, {"RefNumber": "1409792", "RefComponent": "FI-GL-GL-F", "RefTitle": "Retaining tax reporting date even if tax base is zero", "RefUrl": "/notes/1409792 "}, {"RefNumber": "1023317", "RefComponent": "FI-GL-GL-F", "RefTitle": "Legal Change: Vat Due date", "RefUrl": "/notes/1023317 "}, {"RefNumber": "1299302", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date is not saved", "RefUrl": "/notes/1299302 "}, {"RefNumber": "1533216", "RefComponent": "FI-GL-GL-F", "RefTitle": "Documentation Updates - Tax Date", "RefUrl": "/notes/1533216 "}, {"RefNumber": "1527469", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE tax reporting date not active but ready for input", "RefUrl": "/notes/1527469 "}, {"RefNumber": "1484985", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date filled even though document w/o tax code", "RefUrl": "/notes/1484985 "}, {"RefNumber": "1489350", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB02: Problems after changing tax reporting date", "RefUrl": "/notes/1489350 "}, {"RefNumber": "1366172", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB02: Update termination after changing row and VATDATE", "RefUrl": "/notes/1366172 "}, {"RefNumber": "1296949", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB02: Error F5 165 after changing VAT date", "RefUrl": "/notes/1296949 "}, {"RefNumber": "1372794", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Incorrect initialization with list editing FBV2", "RefUrl": "/notes/1372794 "}, {"RefNumber": "1038448", "RefComponent": "XX-CSC-PL", "RefTitle": "VAT due date: Country specific Reporting + Migration Tool", "RefUrl": "/notes/1038448 "}, {"RefNumber": "1439166", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLD20: FI-CA interface and tax reporting date", "RefUrl": "/notes/1439166 "}, {"RefNumber": "1441194", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE in FB02 cannot be changed due to BSET-STMDT", "RefUrl": "/notes/1441194 "}, {"RefNumber": "1137492", "RefComponent": "FI-GL-GL-F", "RefTitle": "Changeability of tax reporting date", "RefUrl": "/notes/1137492 "}, {"RefNumber": "1391393", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Missing reset if not tax-relevant", "RefUrl": "/notes/1391393 "}, {"RefNumber": "1408702", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date is not transferrerd during reversal", "RefUrl": "/notes/1408702 "}, {"RefNumber": "1391341", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Incorrect initialization for direct input", "RefUrl": "/notes/1391341 "}, {"RefNumber": "1381149", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUMSV00: Tax reporting date and electronic advance return", "RefUrl": "/notes/1381149 "}, {"RefNumber": "1353606", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/1353606 "}, {"RefNumber": "1352384", "RefComponent": "FI-AF-DPC", "RefTitle": "Tax reporting date for down payment chains is missing", "RefUrl": "/notes/1352384 "}, {"RefNumber": "1357159", "RefComponent": "FI-GL-GL-F1", "RefTitle": "RFUMSV50: VATDATE in transf. postng docs for deferred tax", "RefUrl": "/notes/1357159 "}, {"RefNumber": "1355851", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: BAdI in cash journal proposes incorrect date", "RefUrl": "/notes/1355851 "}, {"RefNumber": "1347046", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Field missing in batch input parking (FBV1)", "RefUrl": "/notes/1347046 "}, {"RefNumber": "1354521", "RefComponent": "FI-GL-GL-F", "RefTitle": "FB10: Field VATDATE not on screen 137", "RefUrl": "/notes/1354521 "}, {"RefNumber": "1265692", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Entry overwritten due to missing tax code", "RefUrl": "/notes/1265692 "}, {"RefNumber": "1264280", "RefComponent": "XX-CSC-SI-FI", "RefTitle": "VAT procedure Slovenia effective since 1.1.2009", "RefUrl": "/notes/1264280 "}, {"RefNumber": "1338580", "RefComponent": "MM-IV-LIV-CAN", "RefTitle": "MR8M: Reporting date is incorrect", "RefUrl": "/notes/1338580 "}, {"RefNumber": "1293848", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: Error message FF 786 issued in AC interface", "RefUrl": "/notes/1293848 "}, {"RefNumber": "1302818", "RefComponent": "FI-GL-GL-F", "RefTitle": "Unable to manually implement Notes 1023317 or 1235415", "RefUrl": "/notes/1302818 "}, {"RefNumber": "1288600", "RefComponent": "FI-GL-GL-F", "RefTitle": "VATDATE: No forgn curcy translation if tax entered manually", "RefUrl": "/notes/1288600 "}, {"RefNumber": "1280152", "RefComponent": "XX-CSC-ES", "RefTitle": "Legal change on VAT tax date Spain", "RefUrl": "/notes/1280152 "}, {"RefNumber": "1270222", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax reporting date: Manual implementation of Note 1023317", "RefUrl": "/notes/1270222 "}, {"RefNumber": "1289293", "RefComponent": "XX-CSC-PL-FI", "RefTitle": "Polish VAT Workaround around: Maintenance finish", "RefUrl": "/notes/1289293 "}, {"RefNumber": "1179304", "RefComponent": "FI-GL-GL-F", "RefTitle": "Down payment clearing with own tax rate", "RefUrl": "/notes/1179304 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}