{"Request": {"Number": "916939", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 333, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005299482017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000916939?language=E&token=606BE8E682A96F6457D45F2EDECF2A07"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000916939", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000916939/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "916939"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.02.2006"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "916939 - Planning Grid - Appt Start Time Grid or Time Slot"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have planning objects with time slots of differing lengths.<br />In OU parameter N1PTTMNR you have entered the duration of one of the time slots as the value and cannot therefore create appointments for other time slots directly at the start of the time slot.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>N1PTTMNZT</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Enhancement</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Before you execute the source code correction you must make the following manual changes:</p> <b>Install a newer version of the planning grid, at least:</b><br /> <UL><LI>******** for Release 4.72</LI></UL> <UL><LI>******* for Release 6.00</LI></UL> <p>You can find further information on the installation of the planning grid and an overview of the current versions in note number 744441.<br /></p> <b>Enhance the value range of domain N1PARID:</b><br /> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Select \"Domain\" and enter the name of the domain N1PARID in the relevant field.</LI></UL> <UL><LI>Select \"Change\".</LI></UL> <UL><LI>Before the fixed value N1PTVNP insert a new row and enter the following values:</LI></UL> <UL><UL><LI>Fixed value: N1PTTMNZT</LI></UL></UL> <UL><UL><LI>Description: Planning Grid: Appt Start Time: Time Grid / Time Slot</LI></UL></UL> <UL><LI>Save and activate the domain.</LI></UL> <p></p> <b>Enhance class CL_ISHMED_MSCHED by adding the method USE_TIMESLOT_START_TIME:</b><br /> <UL><LI>Call transaction SE24.</LI></UL> <UL><LI>In the \"Object Type\" input field enter the name of the class CL_ISHMED_MSCHED and select \"Change\".</LI></UL> <UL><LI>Select the \"Methods\" tab page.</LI></UL> <UL><LI>Scroll to the last method and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Method: USE_TIMESLOT_START_TIME</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: Start Time for Appointment from Start Time of Time Slot</LI></UL></UL> <UL><LI>Select \"Parameters\".</LI></UL> <UL><LI>Enter the following values in the first free row:</LI></UL> <UL><UL><LI>Parameter: I_USE</LI></UL></UL> <UL><UL><LI>Type: Importing</LI></UL></UL> <UL><UL><LI>Pass Value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Do not select checkbox</LI></UL></UL> <UL><UL><LI>Typing Method: Type</LI></UL></UL> <UL><UL><LI>Associated Type: ISH_ON_OFF</LI></UL></UL> <UL><UL><LI>Default Value:</LI></UL></UL> <UL><UL><LI>Description: Use start time</LI></UL></UL> <UL><LI>Enter the following values in the second row:</LI></UL> <UL><UL><LI>Parameter: E_RC</LI></UL></UL> <UL><UL><LI>Type: Exporting</LI></UL></UL> <UL><UL><LI>Pass Value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Do not select checkbox</LI></UL></UL> <UL><UL><LI>Typing Method: Type</LI></UL></UL> <UL><UL><LI>Associated Type: ISH_METHOD_RC</LI></UL></UL> <UL><UL><LI>Default Value:</LI></UL></UL> <UL><UL><LI>Description: Return code</LI></UL></UL> <UL><LI>Enter the following values in the third row:</LI></UL> <UL><UL><LI>Parameter: CR_ERRORHANDLER</LI></UL></UL> <UL><UL><LI>Type: Changing</LI></UL></UL> <UL><UL><LI>Pass Value: Do not select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Select checkbox</LI></UL></UL> <UL><UL><LI>Typing Method: Type Ref To</LI></UL></UL> <UL><UL><LI>Associated Type: CL_ISHMED_ERRORHANDLING</LI></UL></UL> <UL><UL><LI>Default Value:</LI></UL></UL> <UL><UL><LI>Description: Instance for error handling</LI></UL></UL> <UL><LI>Save and activate the class.</LI></UL> <p></p> <b>Enhance class CL_ISHMED_MSCHED_PG b adding the attribute G_N1PTTMNZT:</b><br /> <UL><LI>Call transaction SE24.</LI></UL> <UL><LI>In the \"Object Type\" input field enter the name of the class CL_ISHMED_MSCHED_PG and select \"Change\".</LI></UL> <UL><LI>Select the \"Attributes\" tab page.</LI></UL> <UL><LI>Scroll to the last attribute and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Attribute: G_N1PTTMNZT</LI></UL></UL> <UL><UL><LI>Type: Instance Attribute</LI></UL></UL> <UL><UL><LI>Visibility: Private</LI></UL></UL> <UL><UL><LI>Read Only: Do not select checkbox</LI></UL></UL> <UL><UL><LI>Typing Method: Type</LI></UL></UL> <UL><UL><LI>Associated Type: ISH_ON_OFF</LI></UL></UL> <UL><UL><LI>Description: Start time for appointment from time grid / scheduling type</LI></UL></UL> <UL><UL><LI>Initial value:</LI></UL></UL> <UL><LI>Save and activate the class.</LI></UL> <p></p> <b>Enter documentation for OU parameter:</b><br /> <UL><LI>Unzip the attached file HW916939_472.zip for Release 4.72 or HW916939_600.zip for Release 6.00.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that you cannot download the attached files using OSS, only using the Service Marketplace (see also note numbers 480180 and 13719 on importing attachments).</p> <UL><LI>Import the unzipped orders into your system.</LI></UL> <p><br />See source code correction</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C5044749)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5048269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000916939/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916939/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "948745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Windows version *******", "RefUrl": "/notes/948745"}, {"RefNumber": "934036", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Zoomed Resource or Appt Start", "RefUrl": "/notes/934036"}, {"RefNumber": "920236", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning Grid: JAVA Version (4.72.8) - Appointment Start Tim", "RefUrl": "/notes/920236"}, {"RefNumber": "916938", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Windows version *******", "RefUrl": "/notes/916938"}, {"RefNumber": "916937", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Version ********", "RefUrl": "/notes/916937"}, {"RefNumber": "744441", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning grid: Versions/Installation Information", "RefUrl": "/notes/744441"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "744441", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning grid: Versions/Installation Information", "RefUrl": "/notes/744441 "}, {"RefNumber": "916938", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Windows version *******", "RefUrl": "/notes/916938 "}, {"RefNumber": "948745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Windows version *******", "RefUrl": "/notes/948745 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "934036", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Zoomed Resource or Appt Start", "RefUrl": "/notes/934036 "}, {"RefNumber": "916937", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Version ********", "RefUrl": "/notes/916937 "}, {"RefNumber": "920236", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning Grid: JAVA Version (4.72.8) - Appointment Start Tim", "RefUrl": "/notes/920236 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF14", "URL": "/supportpackage/SAPKIPHF14"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60004INISH", "URL": "/supportpackage/SAPK-60004INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0000916939/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}