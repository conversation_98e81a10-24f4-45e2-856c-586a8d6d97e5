{"Request": {"Number": "1352967", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 714, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007966432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=895F8F6344FD5D66F5101220BB9E3DFE"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1352967"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.09.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1352967 - IS-H CH: Swiss healthcare smart card - CH delta"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1352967&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1352967/D\" target=\"_blank\">/notes/1352967/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Switzerland (CH).<br />This SAP Note concerns the use of card data in the following functions:</p> <UL><LI>Manual entry of card data (Swiss and European healthcare smart card)</LI></UL> <UL><LI>Patient Search Based on Card Data</LI></UL> <UL><LI>Patient Creation with Card Data</LI></UL> <UL><LI>Patient master data comparison, if the card data differs from the saved patient master data in IS-H.</LI></UL> <UL><LI>Generation of insurance relationship proposals based on card data if there are no insurance relationships for the case.</LI></UL> <UL><LI>Electronic Invoice Creation</LI></UL> <UL><LI>Management of Card Data</LI></UL> <p><br />The IS-H prerequisites for connecting the electronic import of the Swiss healthcare smart card and the electronic coverage query are delivered with a later SAP Note.<br />IS-H 4.72 is an exception to this.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>KVG, Swiss healthcare smart card, EHIC</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Depending on the IS-H version, the following add-on service packs are required:</p> <UL><UL><LI>AOSP 02 for IS-H Version 6.04</LI></UL></UL> <UL><UL><LI>AOSP 03 for IS-H Version 6.03</LI></UL></UL> <UL><UL><LI>AOSP 16 for IS-H Version 6.00</LI></UL></UL> <UL><UL><LI>AOSP 31 for IS-H Version 4.72</LI></UL></UL><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before you implement the source code corrections, you must perform the following actions manually:<br />(When you do this, you must take into account the sequence of the manual activities specified here.)</p> <OL>1. Implement note 1352966 first.</OL> <OL>2. Enhance the table TNEHC_DOCTYPE <B>(only for IS-H Version 6.04)</B>:</OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Database table&quot; and enter the value TNEHC_DOCTYPE in the input field. Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Position the cursor on the first free cell at the end of the table and add the following component:</LI></UL></UL> <p>                     <U>Component Component Type</U><br />NWCH_DOCTYPE, NWCH_EHC_DOCTYPE</p> <UL><UL><LI>Save and activate the table.</LI></UL></UL> <OL>3. Unpack the attached files depending on the IS-H version:</OL> <OL><OL>a) HW1352967_472.zip and then HW1352967_472_Cust_1.zip to HW1352967_472_Cust_3.zip for 4.72 AOP 01 - 34</OL></OL> <OL><OL>b) HW1352967_600.zip and then HW1352967_600_Cust.zip for 6.00 AOP 01 - 19</OL></OL> <OL><OL>c) HW1352967_603.zip and then HW1352967_603_Cust.zip for 6.03 AOP 01 - 06</OL></OL> <OL><OL>d) HW1352967_604.zip and then HW1352967_604_Cust.zip for 6.04 AOP 01 - 06</OL></OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>4. Import the contained requests (for 4.72, a workbench request for the development client, a Customizing request for the Customizing client, and a workbench request for the Customizing client). For 6.00 and higher, add a workbench request for the development client and a workbench request for the Customizing client) into your system.<br />Note that the relevant request contained in the &quot;_xxx.zip&quot; file must be imported first and only then the relevant request contained in the &quot;_xxx_Cust.zip&quot; file - where available, first the request from the &quot;_xxx_Cust_1.zip&quot; file and then from the &quot;_xxx_Cust_2.zip&quot; file, and so on.</OL> <OL>5. You must now perform the following manual tasks:</OL> <p>              Depending on the system status in which you implement this SAP Note, one or the other adjustment may already exist. In this case, consider the respective point as completed. <OL><OL>a) Enhance the domain ISH_CRDTYPE:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Domain&quot; and enter the value ISH_CRDTYPE in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Choose the &quot;Value Range&quot; tab page.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the single value list.</LI></UL></UL> <UL><UL><LI>Enter the value &quot;CH_KVG&quot; as the fixed value and the value &quot;CH: Swiss Healthcare Smart Card&quot; as the short description.</LI></UL></UL> <UL><UL><LI>Save and activate the domain.</LI></UL></UL> <OL><OL>b) Define the new node &quot;Swiss Healthcare Smart Card&quot; in the Implementation Guide (IMG):</OL></OL> <UL><UL><LI>Call transaction SIMGH.</LI></UL></UL> <UL><UL><LI>Select &quot;SAP Healthcare - Industry-Specific Components for Hospitals&quot; as the IMG structure and choose &quot;Change IMG Structure&quot;.</LI></UL></UL> <UL><UL><LI>Go to the section SAP Healthcare - Industry-Specific Components for Hospitals => Communication.</LI></UL></UL> <UL><UL><LI>Choose the text of the last IMG activity of the category and choose &quot;Insert Structure Nodes at Same Level&quot;.</LI></UL></UL> <UL><UL><LI>Enter the node text &quot;Swiss Healthcare Smart Card&quot; and choose &quot;Continue&quot;.</LI></UL></UL> <UL><UL><LI>Save your entry. If you are asked for a package name, enter &quot;NCH1&quot;.</LI></UL></UL> <OL><OL>c) Define the new branch &quot;BAdI: Connection of Swiss Healthcare Smart Card&quot; in the Implementation Guide (IMG):</OL></OL> <UL><UL><LI>Call transaction SIMGH.</LI></UL></UL> <UL><UL><LI>Select &quot;SAP Healthcare - Industry-Specific Components for Hospitals&quot; as the IMG structure and choose &quot;Change IMG Structure&quot;.</LI></UL></UL> <UL><UL><LI>Branch to the section SAP Healthcare - Industry-Specific Components for Hospitals => Communication => Swiss Healthcare Smart Card defined above.</LI></UL></UL> <UL><UL><LI>Click the text of the category and choose &quot;Insert Activity as Subnode&quot;.</LI></UL></UL> <UL><UL><LI>In the &quot;IMG Activity&quot; frame, enter:</LI></UL></UL> <p>                    ID:          SIMG_CARD_READER_CH<br />Name: &quot;BAdI: Connection to Swiss Healthcare Smart Card&quot;</p> <UL><UL><LI>On the &quot;Document&quot; tab page, enter:</LI></UL></UL> <p>                    Document Class: SIMG<br />Document Name:   ISH_CARD_READER</p> <UL><UL><LI>On the &quot;Attributes&quot; tab page, enter:</LI></UL></UL> <p>                    The ID and description have the same values as above.<br />                    ASAP Roadmap ID:    257<br />Necessity:      Optional activity<br />Critical/Non-Critical: Non-Critical<br />Country Dependency:  Valid only for specified countries<br />List CH as the country.<br />Under &quot;Assigned Application Components&quot;, add the entry &quot;I010004207&quot;.</p> <UL><UL><LI>On the &quot;Maintenance Objects&quot; tab page, enter:</LI></UL></UL> <p>                    The ID and description have the same values as above.<br />                    Maintenance Object Type: Business Add-In - Definition<br />Assigned Objects: Definition: ISH_CARD_READER</p> <UL><UL><LI>Save your entry. If you are asked for a package name, enter &quot;NCH1&quot;.</LI></UL></UL> <OL><OL>d) Define the new branch &quot;Assign Document Categories&quot; in the Implementation Guide (IMG):</OL></OL> <UL><UL><LI>Call transaction SIMGH.</LI></UL></UL> <UL><UL><LI>Select &quot;SAP Healthcare - Industry-Specific Components for Hospitals&quot; as the IMG structure and choose &quot;Change IMG Structure&quot;.</LI></UL></UL> <UL><UL><LI>Branch to the section SAP Healthcare - Industry-Specific Components for Hospitals => Communication => Swiss Healthcare Smart Card defined above.</LI></UL></UL> <UL><UL><LI>Choose the text of the branch defined above &quot;BAdI: Connection of Swiss Healthcare Smart Card&quot; and choose &quot;Insert Activity at Same Level&quot;.</LI></UL></UL> <UL><UL><LI>In the &quot;IMG Activity&quot; frame, enter:</LI></UL></UL> <p>                    ID:          SIMG_NEHCDOC_CH<br />Description: &quot;Assign Document Categories&quot;</p> <UL><UL><LI>On the &quot;Document&quot; tab page, enter:</LI></UL></UL> <p>                    Document Class: SIMG<br />Document Name:   SIMG_NEHCDOC_CH</p> <UL><UL><LI>On the &quot;Attributes&quot; tab page, enter:</LI></UL></UL> <p>                    The ID and description have the same values as above.<br />                    ASAP Roadmap ID:    203<br />Necessity:      Optional activity<br />Critical/Non-Critical: Non-Critical<br />Country Dependency:  Valid only for specified countries<br />List CH as the country.<br />Under &quot;Assigned Application Components&quot;, add the entry &quot;I010004207&quot;.</p> <UL><UL><LI>On the &quot;Maintenance Objects&quot; tab page, enter:</LI></UL></UL> <p>                    The ID and description have the same values as above.<br />                    Maintenance Object Type: Customizing Object<br />Customizing Object: V_CH_NEHCDOC<br />Type: S<br />Transaction: SM30</p> <UL><UL><LI>Save your entry. Ignore the dialog box with the message &quot;Set logging indicator&quot;. If you are asked for a package name, specify &quot;NCH1&quot;.</LI></UL></UL> <OL><OL>e) If necessary, define the number range object ISH_CRD.</OL></OL> <UL><UL><LI>Call transaction SNRO.</LI></UL></UL> <UL><UL><LI>In the Object Name field, enter ISH_CRD. Choose &quot;Display&quot;.</LI></UL></UL> <UL><UL><LI>If the object already exists, you can skip the period. Otherwise, choose &quot;Create&quot;.</LI></UL></UL> <UL><UL><LI>Enter the following fillings:</LI></UL></UL> <p>                    Short text:                      Seq.no. EHC<br />Long text:                      Seq.no. Health Card<br />                    To Fiscal Year Ind.:        Off<br />Domain for number length:       ISH_LNCARD<br />Do Not Roll Intervals: Off<br />                    Number range transaction:      none<br />% Warning:                5.0<br />Main memory buffering:      on<br />Number of numbers in buffer:      1000</p> <UL><UL><LI>Save your entry. If you are asked for a package name, specify &quot;NPAS&quot;.</LI></UL></UL> <OL><OL>f) Set new TNPOL modules:</OL></OL> <UL><UL><LI>Go to transaction SM30.</LI></UL></UL> <UL><UL><LI>In the Table/View input field, enter TNPOL. Choose &quot;Maintain&quot;.</LI></UL></UL> <UL><UL><LI>Choose &quot;New Entries&quot; and enter the following two lines:</LI></UL></UL> <p>                     <U>Event Z No.  Module</U><br />PATIENT A 152 ISH_CH_NEHC_API_SAVE_CPB<br />RENAME_C 152 ISH_CH_NEHC_API_RENAME_C</p> <UL><UL><LI>Save your entries.</LI></UL></UL> <OL>6. Finally, implement the source code corrections.</OL> <OL>7. If you unpack the attachment HW1352967_doc.zip, you also receive a description of the <B>required Customizing</B> and user documentation about the functions of the Swiss healthcare smart card in your SAP system.</OL> <OL>8. If you use IS-H Version 6.03, you must also implement Notes 1359214 and 1359388.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C5058668)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1352967_604_Cust.zip", "FileSize": "156", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=24415029741F2449A0096C94908DE684"}, {"FileName": "HW1352967_600.zip", "FileSize": "270", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=1D037BF6D605194D8C173F9BA16C21E5"}, {"FileName": "HW1352967_603.zip", "FileSize": "307", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=7117B7744C0A334183BD3FFF65C01317"}, {"FileName": "HW1352967_doc.zip", "FileSize": "690", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=E86F3C9809EB204D9927420A6138E1C6"}, {"FileName": "HW1352967_472_Cust_1.zip", "FileSize": "2", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=5B77DF332498404BBE3602B349D2C6A9"}, {"FileName": "HW1352967_603_Cust.zip", "FileSize": "60", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=B4849384D6D51E49822FDB81A27BA0F3"}, {"FileName": "HW1352967_472_Cust_3.zip", "FileSize": "2", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=777B18D8BFF21F42B65C160DCB075C86"}, {"FileName": "HW1352967_472.zip", "FileSize": "270", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=7377CDFF23125345871EEAA9C7A90F4C"}, {"FileName": "HW1352967_600_Cust.zip", "FileSize": "83", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=D80E36532AF2C94C86EAF73F7FCB0578"}, {"FileName": "HW1352967_604.zip", "FileSize": "379", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=5F8FA784C6CA184988DCDD6EA5F58648"}, {"FileName": "HW1352967_472_Cust_2.zip", "FileSize": "2", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000293252009&iv_version=0008&iv_guid=4B3D69491ADC5D4EB7F7B269BAB7FA21"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1762232", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Invoice Form External Order - EAN Number", "RefUrl": "/notes/1762232"}, {"RefNumber": "1415051", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Transfer Address Data", "RefUrl": "/notes/1415051"}, {"RefNumber": "1396110", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Insurance Card - Memory Reorganization IS-H 4.72", "RefUrl": "/notes/1396110"}, {"RefNumber": "1390560", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Print Health Insurance Card - CH Delta", "RefUrl": "/notes/1390560"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1362657", "RefComponent": "IS-H-PM", "RefTitle": "IS-H CH: Swiss Healthcare Smart Card PI Connection SAP Delta", "RefUrl": "/notes/1362657"}, {"RefNumber": "1359388", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Swiss Insurance Card - CH Corr. 6.03", "RefUrl": "/notes/1359388"}, {"RefNumber": "1359214", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Swiss Healthcare Smart Card - SAP Corr. 6.03", "RefUrl": "/notes/1359214"}, {"RefNumber": "1355307", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Swiss Insurance Card PI Connection", "RefUrl": "/notes/1355307"}, {"RefNumber": "1352966", "RefComponent": "IS-H-PM", "RefTitle": "IS-H CH: Swiss Healthcare Smart Card - SAP Delta", "RefUrl": "/notes/1352966"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1762232", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Invoice Form External Order - EAN Number", "RefUrl": "/notes/1762232 "}, {"RefNumber": "1362657", "RefComponent": "IS-H-PM", "RefTitle": "IS-H CH: Swiss Healthcare Smart Card PI Connection SAP Delta", "RefUrl": "/notes/1362657 "}, {"RefNumber": "1415051", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Transfer Address Data", "RefUrl": "/notes/1415051 "}, {"RefNumber": "1352966", "RefComponent": "IS-H-PM", "RefTitle": "IS-H CH: Swiss Healthcare Smart Card - SAP Delta", "RefUrl": "/notes/1352966 "}, {"RefNumber": "1396110", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Insurance Card - Memory Reorganization IS-H 4.72", "RefUrl": "/notes/1396110 "}, {"RefNumber": "1390560", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Print Health Insurance Card - CH Delta", "RefUrl": "/notes/1390560 "}, {"RefNumber": "1355307", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Swiss Insurance Card PI Connection", "RefUrl": "/notes/1355307 "}, {"RefNumber": "1359388", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Swiss Insurance Card - CH Corr. 6.03", "RefUrl": "/notes/1359388 "}, {"RefNumber": "1359214", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Swiss Healthcare Smart Card - SAP Corr. 6.03", "RefUrl": "/notes/1359214 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF35", "URL": "/supportpackage/SAPKIPHF35"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60020INISH", "URL": "/supportpackage/SAPK-60020INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60209INISH", "URL": "/supportpackage/SAPK-60209INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60307INISH", "URL": "/supportpackage/SAPK-60307INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60406INISH", "URL": "/supportpackage/SAPK-60406INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/**********/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 23, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "835240 ", "URL": "/notes/835240 ", "Title": "IS-H CH: MEDIDATA 4.0 - New Version", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "850954 ", "URL": "/notes/850954 ", "Title": "IS-H CH: MEDIDATA 4.0 - New Version", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "882870 ", "URL": "/notes/882870 ", "Title": "IS-H CH: INXML 4.0 - EAN Number and Treatment Reason", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "885806 ", "URL": "/notes/885806 ", "Title": "IS-H CH: INXML - External Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "890081 ", "URL": "/notes/890081 ", "Title": "IS-H CH: INXML 4.0 - Sold-to Party, Length of Stay, EAN", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "922087 ", "URL": "/notes/922087 ", "Title": "IS-H CH: Customizing Rate Categories for Invoice (Print, INXML)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "935770 ", "URL": "/notes/935770 ", "Title": "IS-H CH: INXML - Problems with Multiple, Same IR", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "940069 ", "URL": "/notes/940069 ", "Title": "IS-H CH: Data Exchange - INXML Legal Representative", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "954442 ", "URL": "/notes/954442 ", "Title": "IS-H CH: INXML - Check Service Catalog Group of Canceled Services", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "968481 ", "URL": "/notes/968481 ", "Title": "IS-H CH: Data Exchange - INXML Comment Service Long", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "992365 ", "URL": "/notes/992365 ", "Title": "IS-H CH: INXML Comment Long Text of Insurance", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "999419 ", "URL": "/notes/999419 ", "Title": "IS-H CH: INXML - Error Determining External Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1007040 ", "URL": "/notes/1007040 ", "Title": "IS-H CH: INXML - Determine Entry Type", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1015998 ", "URL": "/notes/1015998 ", "Title": "IS-H CH: INXML - Validity Date for TNWCH_EDIKTR", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1119483 ", "URL": "/notes/1119483 ", "Title": "IS-H CH: INXML Tax Points Bastard/Percentage Surcharge Service", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1162263 ", "URL": "/notes/1162263 ", "Title": "IS-H CH: Implementation of New AHV Number", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1166717 ", "URL": "/notes/1166717 ", "Title": "IS-H CH: NV2000 Entry Check of New AHV Number", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1254089 ", "URL": "/notes/1254089 ", "Title": "IS-H CH: INXML EAN Number of Head Office", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1284747 ", "URL": "/notes/1284747 ", "Title": "IS-H: EDI for Ext. Order and SP (CH Delta)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1286924 ", "URL": "/notes/1286924 ", "Title": "IS-H: Correction of Syntax Check T049E for INXML/MEDIDATA", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1325849 ", "URL": "/notes/1325849 ", "Title": "IS-H CH: Change of Law/Treatment Reason for Invoice", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "882870 ", "URL": "/notes/882870 ", "Title": "IS-H CH: INXML 4.0 - EAN Number and Treatment Reason", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885806 ", "URL": "/notes/885806 ", "Title": "IS-H CH: INXML - External Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "890081 ", "URL": "/notes/890081 ", "Title": "IS-H CH: INXML 4.0 - Sold-to Party, Length of Stay, EAN", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "922087 ", "URL": "/notes/922087 ", "Title": "IS-H CH: Customizing Rate Categories for Invoice (Print, INXML)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "935770 ", "URL": "/notes/935770 ", "Title": "IS-H CH: INXML - Problems with Multiple, Same IR", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "940069 ", "URL": "/notes/940069 ", "Title": "IS-H CH: Data Exchange - INXML Legal Representative", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "954442 ", "URL": "/notes/954442 ", "Title": "IS-H CH: INXML - Check Service Catalog Group of Canceled Services", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "968481 ", "URL": "/notes/968481 ", "Title": "IS-H CH: Data Exchange - INXML Comment Service Long", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "992365 ", "URL": "/notes/992365 ", "Title": "IS-H CH: INXML Comment Long Text of Insurance", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "999419 ", "URL": "/notes/999419 ", "Title": "IS-H CH: INXML - Error Determining External Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1007040 ", "URL": "/notes/1007040 ", "Title": "IS-H CH: INXML - Determine Entry Type", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1015998 ", "URL": "/notes/1015998 ", "Title": "IS-H CH: INXML - Validity Date for TNWCH_EDIKTR", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1119483 ", "URL": "/notes/1119483 ", "Title": "IS-H CH: INXML Tax Points Bastard/Percentage Surcharge Service", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1162263 ", "URL": "/notes/1162263 ", "Title": "IS-H CH: Implementation of New AHV Number", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1166717 ", "URL": "/notes/1166717 ", "Title": "IS-H CH: NV2000 Entry Check of New AHV Number", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1254089 ", "URL": "/notes/1254089 ", "Title": "IS-H CH: INXML EAN Number of Head Office", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1284747 ", "URL": "/notes/1284747 ", "Title": "IS-H: EDI for Ext. Order and SP (CH Delta)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1286924 ", "URL": "/notes/1286924 ", "Title": "IS-H: Correction of Syntax Check T049E for INXML/MEDIDATA", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1325849 ", "URL": "/notes/1325849 ", "Title": "IS-H CH: Change of Law/Treatment Reason for Invoice", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1162263 ", "URL": "/notes/1162263 ", "Title": "IS-H CH: Implementation of New AHV Number", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1166717 ", "URL": "/notes/1166717 ", "Title": "IS-H CH: NV2000 Entry Check of New AHV Number", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1254089 ", "URL": "/notes/1254089 ", "Title": "IS-H CH: INXML EAN Number of Head Office", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1284747 ", "URL": "/notes/1284747 ", "Title": "IS-H: EDI for Ext. Order and SP (CH Delta)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1286924 ", "URL": "/notes/1286924 ", "Title": "IS-H: Correction of Syntax Check T049E for INXML/MEDIDATA", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1325849 ", "URL": "/notes/1325849 ", "Title": "IS-H CH: Change of Law/Treatment Reason for Invoice", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1254089 ", "URL": "/notes/1254089 ", "Title": "IS-H CH: INXML EAN Number of Head Office", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1284747 ", "URL": "/notes/1284747 ", "Title": "IS-H: EDI for Ext. Order and SP (CH Delta)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1286924 ", "URL": "/notes/1286924 ", "Title": "IS-H: Correction of Syntax Check T049E for INXML/MEDIDATA", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1325849 ", "URL": "/notes/1325849 ", "Title": "IS-H CH: Change of Law/Treatment Reason for Invoice", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1352967&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1352967/D\" target=\"_blank\">/notes/1352967/D</a>."}}}}