{"Request": {"Number": "1456827", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 467, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017010352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001456827?language=E&token=C43E80D9199A8733EFE977DE40127770"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001456827", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001456827/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1456827"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.02.2011"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-TR-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-TR"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Turkey", "value": "XX-CSC-TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-TR-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-TR", "value": "XX-CSC-TR-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1456827 - IS-U Loc. for TR - Address Update: FM-s + bug corrections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Problem 1.:<br />The problem was, that when there is a text update in the IMG (transaction SPRO) on the customizing tables /SAPCE/IUTR_COUV, /SAPCE/IUTR_TSV, and /SAPCE/IUTR_MVV (SAP Utilities -&gt; Master Data -&gt; Landerspecifika f&#x00FC;r T&#x00FC;rkei -&gt; Maintain Municipities/Villages), then the text changes should be normally reflected to the connection<br />object after when the quarterly adjustment programs are run. However, in the ADRC table the corresponding address number of the corresponding connection objects still have the old texts.<br /><br />*----------------------------------------------------------------------<br />Problem 2.:<br />The function modules SZRS_S_ADRSTREET_CREATE and SZRS_S_ADRCITY_CREATE update not only ADRCITY, ADRSTREET, ADRCITYPRT tables but also ADRDIF_ST, ADRDIF_CP difference tables. There is a function group named SZRC for city in SAP. These functions in this group create/update/delete city(update customizing table of city) and reflect the changed data to District/Street Difference Tables for Quarterly Adjustment.<br /><br />There was a lack of this kind of functionality for the turkish address fields like county, township and municipality/village.<br />New function modules had to be developed for the update of localization address master data tables like /SAPCE/IUTR_COU, /SAPCE/IUTR_COUT, /SAPCE/IUTR_TS, /SAPCE/IUTR_TST, /SAPCE/IUTR_MV, /SAPCE/IUTR_MVTX, /SAPCE/IUTR_MVTT, /SAPCE/IUTR_MVTY, /SAPCE/IUTR_DST, /SAPCE/IUTR_DST and /SAPCE/IUTR_DSTT and the District/Street Difference Tables for Quarterly Adjustment reports.<br /><br />New functions were needed for each localization tables to update the<br />localization tables and reflect the changed data to District/Street<br />Difference Tables for Quarterly Adjustment.<br />For example, county function should update /SAPCE/IUTR_COU, /SAPCE/IUTR_COUT tables and reflect this change to streets and districts tables.<br /><br />One function module was needed to be developed for county, one for township and one for village, etc. These function modules should insert/delete/update a record in the customizing localization tables and they should update District/Street Difference Tables for Quarterly Adjustment. Similiar interface of city and street functions was needed for each localization table functions.<br /><br />These functions will be used these functions in the integration between General Directorate of Civil Registration &amp; Nationality and ISU system. According to the update file taken from General Directorate of Civil Registration &amp; Nationality, the customer will update its address master data. After updating address master data, they will update the address of ISU objects such as connection object and premises by RSADRQU1,RSADRQU2 and RSADRQU3 reports.<br />*----------------------------------------------------------------------<br /><br />The two problems are in connection witch each other. The first one shows the problem in a single action and the second one is related to a mass update action of the turkish address fields.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-U TR localization; IS-U Turkey address management; County township Municipality/Village name update in standard tables; IMG address field name update; Update function modules for turkish address fields;<br />Quarterly Adjustment reports;</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>CEEISUT add-on pack with the following SP has to be installed:<br />CEEISUT 600 SP level 18<br />or<br />CEEISUT 604 SP level 07.<br /><br />The localized development for turkish address management works only with these corrections.<br />You have to install all the SP-s of CEEISUT add-on on the corresponding release till the SP number listed above and carry out<br />the necessary manual modification described in the attached PDF file<br />(Updated_Manual_Modification.pdf) in the attachment of note 1122857.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The solutions for both problem are delivered with support packages listed in the \"Reason and Prerequisites\" block.<br /><br />New function modules were developed in order to update turkish county,<br />township, and municipality/village master data tables. With these function modules the turkish localization address tables will be updated without taking a request. They will be updated with the government address data files. Therefore it will possible to update the address localization part of ADRC table by RSADRQU1,RSADRQU2 and RSADRQU3 (Quarterly Adjustment) reports.<br /><br />The errors of name update in ADRSTREET and ADRCITYPRT tables have been corrected and now the change in the description text of a turkish address field in SPRO will be appear at all BPs and Connection objects which have the given address (County / Township / Minucipality/Village)key.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I053163)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I053163)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001456827/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1126457", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U TR Address management corrections", "RefUrl": "/notes/1126457"}, {"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857 "}, {"RefNumber": "1126457", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U TR Address management corrections", "RefUrl": "/notes/1126457 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CEEISUT 472", "SupportPackage": "SAPK-47225INCEEISUT", "URL": "/supportpackage/SAPK-47225INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60018INCEEISUT", "URL": "/supportpackage/SAPK-60018INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 604", "SupportPackage": "SAPK-60407INCEEISUT", "URL": "/supportpackage/SAPK-60407INCEEISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}