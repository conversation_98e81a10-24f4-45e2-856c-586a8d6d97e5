{"Request": {"Number": "920416", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 321, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005379422017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=6530E439539FC8A6883AE9B6A3E665EB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "920416"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DBA-MPRO"}, "SAPComponentKeyText": {"_label": "Component", "value": "MultiProvider"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Basis", "value": "BW-WHM-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MultiProvider", "value": "BW-WHM-DBA-MPRO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA-MPRO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "920416 - Checking compounding consistency in MultiProviders"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The identification of characteristics and navigation attributes in MultiProviders must be consistent with regard to compounding (see below for more information and an example).<br /><br />If there is an error in Release BW 3.x, the system only issues a warning message to protect existing live installations.<br /><br />However, if there is inconsistent compounding (depending on the application, ID list and available master data), data from the incoming InfoProviders may be distorted. As a result, the MultiProvider may contain data records that do not physically exist anywhere in an InfoProvider containing data. We therefore decided to change this warning message to an error message with Release BW 7.x.<br /><br />As a result of this, MultiProviders configured in BW 3.x may be incorrect and can no longer be activated after an upgrade to BW 7.x.<br />To provide analysis options for the user if action is required, we created the RSCOMPCONS report, which lists MultiProviders that are inconsistent with regard to compounding. You must then edit and correct these MultiProviders using the maintenance transaction that you can call from the report directly.<br /><br />Example/chart for explanation:<br />==================================<br /><br />The COSTCENTER1 characteristic is compounded with the CO_AREA1 characteristic<br />(here with the format CO_AREA1 &lt;- COSTCENTER1).<br />Furthermore, the COSTCENTER2 characteristic references the COSTCENTER1 characteristic and the CO_AREA2 characteristic references the CO_AREA1 characteristic.<br />This format applies for the compounding: CO_AREA2 &lt;- COSTCENTER2.<br /><br />These four characteristics are now contained in a relevant InfoProvider and in the MultiProvider.<br />The following chart explains the subject of inconsistent compounding (the arrows depict the identifications in dialog processing):<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>MultiProvider</TH><TH ALIGN=LEFT> </TH><TH ALIGN=LEFT> Provider involved</TH></TR> </TABLE> <p>============================================================</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>CO_AREA1</TD><TD> &lt;&lt;-------</TD><TD> CO_AREA1</TD></TR> <TR><TD></TD><TD> </TD><TD> COSTCENTER1</TD></TR> <TR><TD></TD></TR> <TR><TD></TD><TD> </TD><TD> CO_AREA2</TD></TR> <TR><TD>COSTCENTER1</TD><TD> &lt;&lt;-------</TD><TD> COSTCENTER2</TD></TR> </TABLE> <p><br />Here, COSTCENTER2 is mapped to COSTCENTER1. As a result, the higher-level CO_AREA2 characteristic must also be mapped to CO_AREA1 in the InfoProvider concerned (because it is the higher-level characteristic for COSTCENTER1 in the MultiProvider).<br />&#x00A0;&#x00A0;However, this is not the case.&#x00A0;&#x00A0;The compounding consistency is violated. Correct the assignment as follows:<br /><br /> CO_AREA1  &lt;&lt;------- CO_AREA2<br /><br /><br /><br />An example may clarify the serious consequences of inconsistent assignments. If there is master data in the format:<br /><br /> CO_AREA   COSTCENTER<br />=============================================<br /> 1000   A<br /> 2000   B<br /> 2000   C<br /><br />this could create a data record with the following valued in the MultiProvider:<br /><br /> 1000   C<br /><br />&#x00A0;&#x00A0;This master record does not exist.<br />   <br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p> </p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Inconsistent compounding is admitted in Release BW 3.x<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If the RSCOMPCONS report does not yet exist in your system, use transaction SE38 to create the empty report and then copy the source code from the advance corrections. After you have copied the report, you must enter the following report texts (using Text elements -&gt; Text symbols):<br /><br />CH1 The following MultiProviders are inconsistent with regard to compounding<br />CH2 and must be corrected using Note 920416:<br />CH3 (You can start the maintenance dialog by double-clicking the name)<br />NOF No inconsistent MultiProvider was found<br /><br />Execute the RSCOMPCONS report specified above (transaction SE38). The report lists MultiProviders that are inconsistent with regard to compounding.<br />You must then correct and activate these MultiProviders using maintenance transaction /nrsdmpro. The steps to be carried out are specified in error messages with long texts that are displayed when the MultiProvider is checked.<br /><br />If you are using SAP NetWeaver 2004s (BI 7.0), see the option described in Note 931597.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D001998"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D037845)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990"}, {"RefNumber": "931597", "RefComponent": "BW-WHM-DBA-MPRO", "RefTitle": "MultiProvider with inconsistent identification", "RefUrl": "/notes/931597"}, {"RefNumber": "920513", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination message BRAIN A 152 (incorrect MultiProvider)", "RefUrl": "/notes/920513"}, {"RefNumber": "1632284", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Program RSCOMPCONS should generate warning messages", "RefUrl": "/notes/1632284"}, {"RefNumber": "1086744", "RefComponent": "BW-BEX-OT", "RefTitle": "MultiProvider inconsistency BRAIN A152 or R7I 135", "RefUrl": "/notes/1086744"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1632284", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Program RSCOMPCONS should generate warning messages", "RefUrl": "/notes/1632284 "}, {"RefNumber": "1086744", "RefComponent": "BW-BEX-OT", "RefTitle": "MultiProvider inconsistency BRAIN A152 or R7I 135", "RefUrl": "/notes/1086744 "}, {"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990 "}, {"RefNumber": "931597", "RefComponent": "BW-WHM-DBA-MPRO", "RefTitle": "MultiProvider with inconsistent identification", "RefUrl": "/notes/931597 "}, {"RefNumber": "920513", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination message BRAIN A 152 (incorrect MultiProvider)", "RefUrl": "/notes/920513 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "30B", "To": "30B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B38INVCBWTECH", "URL": "/supportpackage/SAPK-30B38INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B31", "URL": "/supportpackage/SAPKW30B31"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31025", "URL": "/supportpackage/SAPKW31025"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31025", "URL": "/supportpackage/SAPKW31025"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35017", "URL": "/supportpackage/SAPKW35017"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "NumberOfCorrin": 1, "URL": "/corrins/**********/654"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}