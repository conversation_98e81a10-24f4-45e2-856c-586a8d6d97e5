{"Request": {"Number": "1030497", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 432, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016242452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001030497?language=E&token=302CF4C772042686FB97889540B8D8EB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001030497", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001030497/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1030497"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.05.2011"}, "SAPComponentKey": {"_label": "Component", "value": "PSM-FM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Funds Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Public Sector Management", "value": "PSM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Funds Management", "value": "PSM-FM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM-FM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1030497 - SAP ERP 6.0: Public sector scenarios in new general ledger"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains information about the relevant functions in the new general ledger that are specific to the public sector. For Release SAP ERP 6.0, it also outlines the technical features in relation to the public sector.<br />In addition, this note describes the restrictions that are associated with integrating the Public Sector Collection and Disbursement (PSCD) solution into the new general ledger.<br />The last section of this note describes the options for migrating the classic general ledger to the new general ledger. It also contains information about the SAP General Ledger Migration Services.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>New general ledger, public sector, fund accounting, online payment update, migration</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are a public sector customer and you have activated the Enterprise Add-On EA-PS.<br />You want to use different public sector scenarios in the new general ledger in Release SAP ERP 6.0.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>1. Public Sector Management (PSM) and the new general ledger: Overview of functions</b><br /> <b></b><br /> <p>The new general ledger with Release <B>SAP ERP 6.0 up to and including Enhancement Package (EhP) 3</B> provides the following functions or scenarios that are specific to the public sector:<br /></p> <UL><LI>You can use the fund, grant or business area to create balance sheets.</LI></UL> <UL><UL><LI>You can use document splitting to define the fund characteristic and the grant characteristic as values for the balance sheet (=&gt; fund accounting).</LI></UL></UL> <UL><LI>You can create profit and loss statements (P&amp;L statement) for the fund, grant or functional area.</LI></UL> <UL><UL><LI>You can use fund accounting in the new general ledger.</LI></UL></UL> <UL><LI>You can update payments in Funds Management in real time.</LI></UL> <UL><UL><LI>You no longer have to work with the background program RFFMS200.</LI></UL></UL> <UL><UL><LI>Important: You can use the online payment update only if you are not already using the program RFFMS200, that is, if you execute a new installation.</LI></UL></UL> <p><br />The new general ledger as of Release <B>SAP ERP 6.0 Enhancement Package (EHP) 4</B> provides the following functions or scenarios that are specific to the public sector:</p> <UL><LI>Cash-basis accounting and cash flow reporting (Cash Flow)</LI></UL> <UL><UL><LI>Cash-basis accounting: A separate cash ledger in the general ledger in which expenditure and revenue take place only at the time when the money is paid out or is incoming.</LI></UL></UL> <UL><UL><LI>Cash Flow Reporting:&#x00A0;&#x00A0;Provides information about the payment flow (origin of money and usage) for a selected period (cash flow, direct method).</LI></UL></UL> <UL><LI>Partial payment using general ledger account assignment</LI></UL> <UL><UL><LI>You can use this function to partially pay your open binding types for each fund or each other clearing general ledger account assignment.</LI></UL></UL> <UL><LI>Functional area as document splitting characteristic of general ledger accounting.</LI></UL> <UL><UL><LI>You can define the functional area as the document splitting characteristic for general ledger accounting so that you can create financial statements according to functional area.</LI></UL></UL> <UL><LI>Average daily balances: Allocations</LI></UL> <UL><UL><LI>Average daily balances are calculated averages of a balance that exists in a specified period and that is subdivided into a group of balance sheet accounts (such as cash) according to the independent accounting unit (for example, fund).</LI></UL></UL> <UL><UL><LI>Average daily balances can be used as a tool to distribute assigned items (such as interest earnings or interest expenses) in provisional account assignments to the final independent accounting units.</LI></UL></UL> <UL><LI>Partly exempt organizations</LI></UL> <UL><UL><LI>You can use this function to display areas from organizations that are partly or completely exempt from sales tax.</LI></UL></UL> <p><br />The new general ledger as of Release <B>SAP ERP 6.0 Enhancement Package (EHP) 5</B> provides the following functions that are specific to the public sector:<br /></p> <UL><LI>Reassignment with the new general ledger accounting</LI></UL> <UL><UL><LI>Under certain circumstances, you can now reassign Funds Management account assignments for invoices (value type 54) in Funds Management even if you have activated the new general ledger accounting in your system. The emphasis here is on the reassignment in the context of the fiscal year change in Funds Management (FM).&#x00A0;&#x00A0;CAUTION: The mid-year reassignment of an FM account assignment that is updated and split in the new general ledger is currently not supported.</LI></UL></UL> <p></p> <UL><LI>Cash control (FMCC)</LI></UL> <UL><UL><LI>With cash control (FMCC), you can execute an availability check according to the criteria determined by you based on the existing cash balance (for example, check of the available cash balance for each fund for payments).</LI></UL></UL> <UL><LI>Open item management for zero balance clearing accounts.</LI></UL> <UL><UL><LI>For postings that lead to zero balance clearings in the new general ledger accounting or in special ledgers, corresponding clearing lines can now also be posted in the entry view in a separate document to accounts with open item management, and not just in the general ledger view, as it was before.</LI></UL></UL> <p></p> <UL><LI>Reporting for cash-basis accounting and cash flow reporting</LI></UL> <UL><UL><LI>A standard report and templates for cash-basis accounting and cash flow reporting that you can use for test and demo purposes and adjust to your specific needs is now delivered.</LI></UL></UL> <p><br /><br /></p> <b>2. Technical environment in the new general ledger (overview)</b><br /> <p>From a technical point of view, the new general ledger in <B>SAP ERP 6.0 up to Enhancement Package 3</B> provides the following features that are specific to the public sector:</p> <UL><LI>A set of tables that is specific to the public sector (including a separate totals table):</LI></UL> <UL><UL><LI>FMGLFLEXT (=&gt; totals table) with fund, grant, functional area and business area</LI></UL></UL> <UL><UL><LI>FMGLFLEXA (=&gt; standard line item table in the new general ledger)</LI></UL></UL> <UL><UL><LI>Note that Public Sector customers do not have to use the FMGLFLEX* (or PSGLFLEX*) set of tables. Instead, Public Sector customers can decide on the most suitable set of tables for their requirements in each case (see Note 1400563).</LI></UL></UL> <UL><UL><LI>The data to be migrated from the classic general ledger to the new general ledger is transferred (depending on the selected set of tables) from the table GLT0 (or another source ledger) to the FAGLFLEX* or FMGLFLEX* tables.</LI></UL></UL> <p></p> <UL><LI>Public Sector-specific scenarios in the new general ledger available for the set of tables FMGLFLEX* (or also for PSGLFLEX* as of&#x00A0;&#x00A0;Enhancement Package 4)</LI></UL> <UL><UL><LI>Fund accounting (PSM_FAC) for updating the fund characteristic, the functional area characteristic and the business area characteristic to the relevant general ledger</LI></UL></UL> <UL><UL><LI>Grants management (PSM_GM) for updating the grant characteristic</LI></UL></UL> <p><br />From a technical point of view, the new general ledger <B>as of SAP ERP 6.0</B> <B>Enhancement Package 4</B> provides the following features that are specific to the public sector:</p> <UL><LI>An additional set of tables that is specific to the public sector (including a separate totals table):</LI></UL> <UL><UL><LI>PSGLFLEXT (totals table) that contains all fields from FMGLFLEXT as well as the additional fields for the cash ledger: Expense account or revenue account and budget period.</LI></UL></UL> <UL><UL><LI>PSGLFLEXA (corresponding line item table in the new general ledger)</LI></UL></UL> <p></p> <UL><UL><LI>The data to be migrated from the classic general ledger to the new general ledger is transferred (depending on the selected set of tables) from the table GLT0 (or another source ledger) to the FAGLFLEX*, FMGLFLEX* or PSGLFLEX* tables.</LI></UL></UL> <p></p> <UL><LI>Additional Public Sector-specific scenarios in the new general ledger available for the set of tables PSGLFLEX*:</LI></UL> <UL><UL><LI>Cash ledger (PSM_CL) to update the expense account or revenue account characteristic in the relevant ledger in the general ledger.</LI></UL></UL> <UL><UL><LI>Budget period (PSM_BUDPER) to update the budget period characteristic.</LI></UL></UL> <p><br /><br />You can use transaction FAGL_GINS to add customer fields or SAP standard fields that are not contained in a delivered scenario to your set of tables for the new general ledger. However, there is a technical upper limit. This upper limit is described in Note 918679. If you want to prepare balance sheets according to the fund and the profit center, for example, you can include the profit center in the set of tables for the public sector and in the public sector scenarios.<br /><br />Further differences between Public Sector Management and other applications:</p> <UL><LI>Unlike the segment characteristic (and possibly the profit center characteristic), the fund characteristic and the grant characteristic are contained in other modules (=&gt; in Funds Management or Grants Management) and have an operational relevance in these modules that extends beyond the new general ledger. Examples: General availability control (=&gt; AVC) and reporting.</LI></UL> <UL><LI>The two characteristics (=&gt; fund and grant) are also contained in the totals tables as assignment object keys in applications other than their 'home applications', such as in CO - and as of SAP ERP 6.0 Enhancement Package 4 also in Treasury - or they are implicitly contained, for example, in FI-AA due to a derivation logic. As a result, they also affect processes in these other applications. You must take this into account when you create concepts.</LI></UL> <p></p> <b>3. Integration of the new general ledger into Public Sector Collection and Disbursement (PSCD)</b><br /> <p>Public Sector Collection and Disbursement (PSCD) and the new general ledger in <B>SAP ERP 6.0 up to and including Enhancement Package 3</B> (=&gt; technically, the solution for PSCD is based on the FI-CA component):</p> <UL><LI>The FI-CA component is integrated into the classic general ledger and into the new general ledger in the same way.</LI></UL><UL><LI>In PSCD, you can transfer individual postings to general ledger accounting in summarized form.</LI></UL> <UL><LI>In the case of the new general ledger, the system does not execute document splitting: The system creates (only) clearing lines for the entities for the balance sheet.</LI></UL> <UL><LI>You cannot use the account assignment element 'Segment' in PSCD.</LI></UL> <UL><LI>The account assignment element 'Grant' is not supported in PSCD.</LI></UL> <UL><LI>Tax accounts: You cannot use PSCD postings for an account assignment provision of tax account lines for the balance sheet.</LI></UL> <UL><UL><LI>If a PSCD customer requires tax accounts in their balance sheet, the customer cannot create balance sheets that are independent of the account assignment (for example, for the fund).</LI></UL></UL> <UL><LI>Bank accounts: Bank accounts are not provided with account assignments.</LI></UL> <UL><UL><LI>In general, balance sheets cannot be prepared for each account assignment.</LI></UL></UL> <UL><UL><LI>Workarounds: a) an individual bank account for each account assignment or b) a common 'Treasury Fund' for all postings to all bank accounts</LI></UL></UL> <UL><LI>Cash discount postings and tax adjustments:</LI></UL> <UL><UL><LI>PSCD cannot execute these postings correctly for each account assignment (that is, according to cause).</LI></UL></UL> <p></p> <b>Conclusion:</b><br /> <UL><LI>Even though the fund characteristic and the functional area characteristic are available in PSDC, balance sheet accounts and tax accounts are generally not split according to cause.</LI></UL><UL><LI>Public sector customers who use PSCD can create complete balance sheets for funds only if they do not require tax accounts in the balance sheets.</LI></UL> <p></p> <b>4. Migration to the new general ledger</b><br /> <b></b><br /> <b>General information:</b><br /> <p>In <B>SAP ERP 6.0 up to and including Enhancement Package 3</B>, you have the following migration options:</p> <UL><LI>Migration from the table GLT0 (classic general ledger) to the table FMGLFLEX*=&gt; Creating balance sheets/P&amp;L statement for each fund or grant condition: Introducing document splitting</LI></UL><UL><UL><LI>If you decide to use the FAGLFLEX* table framework in the new general ledger (for example, because you do not require Funds Management, but you use CO intensively), you can also migrate from GLTO to these tables.</LI></UL></UL> <UL><UL><LI>CAUTION: Tools for the changeover of the background program RFFMS200 (enhanced function) are available only as of Enhancement Package 4. However, if you are planning a changeover to the online payment update and you want to migrate to the new general ledger from a lower Enhancement Package, you should activate the document splitting characteristics FIPOS, KNBELNR, and VOBELNR (that are required for the online payment update) during the migration to the new general ledger (even though the actual changeover is possible at a later point in time only). Note that SAP does NOT provide tools to convert from the batch program RFFMS200 (regular function) to the batch program RFFMS200 (extended function) or for the online payment update.</LI></UL></UL> <UL><LI>From fund accounting (=&gt; Fund Accounting within a special ledger environment) to new general ledger accounting (see Note 1163997)</LI></UL> <p><br />With <B>SAP ERP 6.0</B> <B>Enhancement Package 4</B>, the following other migrations are supported:</p> <UL><LI>Migration from the table GLT0 (classic general ledger) to the table PSGLFLEX*.</LI></UL> <UL><LI>Changeover from the payment update into Funds Management with the (background) program RFFMS200 (enhanced function) to the online payment update</LI></UL> <UL><LI>Subsequent introduction of a cash ledger</LI></UL> <UL><LI>Creating financial statements for each functional area and combination of fund and budget period.</LI></UL> <UL><LI>Asset migration in fund accounting   This function supports the implementation of fund accounting for asset accounting if you implement the new general ledger accounting at the same time. This primarily involves the asset mass transfer with which the fund information and the balance carryforward transfer from the asset accounting for each independent accounting unit is added to the asset master records. This creates the opening balance sheet for the asset values for each fund.</LI></UL> <p><br /></p> <b>SAP General Ledger Migration Service:</b><br /> <p>The technical basic service that is mandatory (also for public sector customers) is based on standard migration scenarios and includes a scenario-specific general ledger migration cockpit and service sessions for quality assurance of the data and the migration project. The service sessions are performed by a member of the general ledger migration back office. This general ledger migration back office was set up specifically for this purpose. After you commission the service, the general ledger migration cockpit will be made available for you and the service sessions will be delivered following consultation with SAP.<br />More information is available on SAP Service Marketplace at <B>www.service.sap.com/glmig</B> or in Note 812919.<br /><br />Note that the SAP General Ledger Migration Service does not include consulting services. For consulting services, contact your local SAP Public Sector Consulting department.<br /><br />No (standard) migration packages that are specific to the public sector are provided. Public sector customers can also use the standard migration packages that are provided.<br /><br />Due to the new features that are specific to the public sector as described above, the migration is project-based and must be overseen by an experienced consultant (who has in-depth knowledge of the public sector and the new general ledger).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D032142)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028695)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001030497/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001030497/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "918679", "RefComponent": "FI-GL", "RefTitle": "NewGL: GH 707, permitted length for customer fields", "RefUrl": "/notes/918679"}, {"RefNumber": "906397", "RefComponent": "PSM-FA", "RefTitle": "ERP2004/05 Public Sector: Migration of new general ledger", "RefUrl": "/notes/906397"}, {"RefNumber": "812919", "RefComponent": "FI-GL-MIG", "RefTitle": "SAP ERP new general ledger: Migration", "RefUrl": "/notes/812919"}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146"}, {"RefNumber": "1400563", "RefComponent": "PSM-FM-UP-FI-PU", "RefTitle": "When should a PSM customer use FAGLFLEXT or FMGLFLEXT", "RefUrl": "/notes/1400563"}, {"RefNumber": "1227207", "RefComponent": "PSM-FM-UP-FI", "RefTitle": "Tax update or profit and loss adjustment in FM", "RefUrl": "/notes/1227207"}, {"RefNumber": "1163997", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration from FI-SL split ledger", "RefUrl": "/notes/1163997"}, {"RefNumber": "1060657", "RefComponent": "FI-GL", "RefTitle": "General ledger reporting by profit center in public sector", "RefUrl": "/notes/1060657"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2036717", "RefComponent": "FI-GL-FL", "RefTitle": "General Ledger Accounting (new) - Assigning scenario to ledger: Error FAGL_LEDGER_CUST137", "RefUrl": "/notes/2036717 "}, {"RefNumber": "1400563", "RefComponent": "PSM-FM-UP-FI-PU", "RefTitle": "When should a PSM customer use FAGLFLEXT or FMGLFLEXT", "RefUrl": "/notes/1400563 "}, {"RefNumber": "1060657", "RefComponent": "FI-GL", "RefTitle": "General ledger reporting by profit center in public sector", "RefUrl": "/notes/1060657 "}, {"RefNumber": "906397", "RefComponent": "PSM-FA", "RefTitle": "ERP2004/05 Public Sector: Migration of new general ledger", "RefUrl": "/notes/906397 "}, {"RefNumber": "1163997", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration from FI-SL split ledger", "RefUrl": "/notes/1163997 "}, {"RefNumber": "812919", "RefComponent": "FI-GL-MIG", "RefTitle": "SAP ERP new general ledger: Migration", "RefUrl": "/notes/812919 "}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146 "}, {"RefNumber": "1227207", "RefComponent": "PSM-FM-UP-FI", "RefTitle": "Tax update or profit and loss adjustment in FM", "RefUrl": "/notes/1227207 "}, {"RefNumber": "918679", "RefComponent": "FI-GL", "RefTitle": "NewGL: GH 707, permitted length for customer fields", "RefUrl": "/notes/918679 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-PS", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}