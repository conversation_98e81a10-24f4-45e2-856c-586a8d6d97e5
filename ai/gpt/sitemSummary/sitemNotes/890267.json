{"Request": {"Number": "890267", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 373, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015970632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000890267?language=E&token=8F55BBD9151898834FEF3CFBBE69D99C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000890267", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000890267/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "890267"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.01.2007"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-AT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Austria"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "RE-FX-LC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "890267 - Austria: Localization scope RE-FX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Localization scope of the new real estate solution RE-FX for Austria</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, localization, operating costs settlement, OC settlement, current occupancy principle, RMSP, main rent settlement, MR settlement, adjustment by fifteenths, Flexible Real Estate, Austria</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You consider using the new SAP Real Estate Management (Flexible Real Estate) in Austria. You want more information about the scope of the localization for Austria.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>ERP 2005 provides functional enhancements to meet Austria-specific legal requirements with SAP Real Estate Management. The following functions are supported:<br /> - Operating costs settlement in accordance with Austrian tenancy law<br /> - Main rent settlement<br /> - Correspondence<br /> - Adjustment by fifteenths<br /><br />Additionally, we released the third-party management function and the condominium owners association (COA) for Austria. For more information about this, see Note 517673.<br /><br />We plan to deliver the function for the Austria-specific tax processing of the reserve contributions in the next enhancement package. You must implement special correspondences for COA in the project.<br /><br /><B>Operating costs settlement in accordance with Austrian tenancy law</B><br /><br />The service charge settlement function was enhanced by the procedure \"current occupancy principle\", which supports the requirements for the calculation of the settlement result of the operating costs settlement in accordance with Austrian tenancy law.<br /><br />With the settlement according to \"current occupancy principle\", you have the option to enter a key date for the determination of the settlement receiver. If no occupancy contract exists for this key date, the settlement result is posted as a vacancy.<br /><br />In addition, you can define for the measurement type that the determination is to occur for a key date that is also to be determined during the settlement. The measurement amounts that are determined in this way are then set as assessment basis for the entire billing period for the calculation of the settlement result.<br /><br />If you use the procedure according to \"current occupancy principle\", the advance payments are to be posted as revenue. The determination of the settlement result then occurs as a balance from receivables and advance payments. To ensure that the system also takes imputed advance payments for vacant status periods into account, enter vacant advance payments must for the rental object and carry out vacancy postings. For the individual calculation of advance payments, a BAdI is available that permits a (modification-free) enhancement of the determination of the advance payments to be charged.<br /><br />The non-deductible input tax on non-opting tenants within the service charge settlement is determined and passed on based on an equal distribution of the operating costs and therefore of the non-deductible input tax to the entire billing period. Non-opting tenants are debited within the framework of the service charge settlement with the non-deductible input tax for the operating cost expenses allotted proportionally to their rental period. Other calculation procedures are not supported.<br /><br /><B>Main rent settlement</B><br /><br />The creation of the main rent settlement in accordance with the specifications of the Austrian tenancy law is supported.<br /><br />The settlement schema includes the structure of the main rent settlement. You can define several settlement schemata for simulations. The settlement is always carried out for one year.<br /><br />You can create the main rent settlement for an individual business entity, a building or several buildings of a business entity. The values updated in Controlling are the basis for the basis of the valuation in the main rent settlement. In addition, you can enter correction documents within the settlement for individual items.<br /><br /><B>Correspondence</B><br /><br />You can define different versions for the creation of the correspondence for the main rent settlement. For each version, the structure and level of detail of the outputs for the main rent settlement is defined. (short version, long version)<br /><br />Alternatively, the output is supported as Smart Form or as PDF.<br /><br /><B>Adjustment by fifteenths</B><br /><br />You can use the adjustment by fifteenths for the stable value guarantee of business room rents in accordance with the rules of the tenancy law if an immediate increase is not allowed, but must be distributed over a time period of 15 years instead.<br />The adjustment by fifteenths is supported as an enhancement of the existing adjustment methods. The calculation procedure is implemented within the BAdI for the rent adjustment.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-SC (Service Charge Settlement)"}, {"Key": "Responsible                                                                                         ", "Value": "I026449"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I001503)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000890267/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890267/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098"}, {"RefNumber": "1317711", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Localization: Adj. by fifteenths w/ stable value guarantee", "RefUrl": "/notes/1317711"}, {"RefNumber": "1317704", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Lokalisierung: Vorsteuerbehandlung Österreich", "RefUrl": "/notes/1317704"}, {"RefNumber": "1317679", "RefComponent": "RE-FX-LC-AT", "RefTitle": "COCP localization: Measurement on key date", "RefUrl": "/notes/1317679"}, {"RefNumber": "1122309", "RefComponent": "RE-FX", "RefTitle": "Localization: missing table entries for loc. countries", "RefUrl": "/notes/1122309"}, {"RefNumber": "1013178", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Austria: VAT of expense with for COA-management", "RefUrl": "/notes/1013178"}, {"RefNumber": "1013171", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Austria: Stable value guarantee procedure as per MRG", "RefUrl": "/notes/1013171"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098 "}, {"RefNumber": "1122309", "RefComponent": "RE-FX", "RefTitle": "Localization: missing table entries for loc. countries", "RefUrl": "/notes/1122309 "}, {"RefNumber": "1317704", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Lokalisierung: Vorsteuerbehandlung Österreich", "RefUrl": "/notes/1317704 "}, {"RefNumber": "1317711", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Localization: Adj. by fifteenths w/ stable value guarantee", "RefUrl": "/notes/1317711 "}, {"RefNumber": "1317679", "RefComponent": "RE-FX-LC-AT", "RefTitle": "COCP localization: Measurement on key date", "RefUrl": "/notes/1317679 "}, {"RefNumber": "1013178", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Austria: VAT of expense with for COA-management", "RefUrl": "/notes/1013178 "}, {"RefNumber": "1013171", "RefComponent": "RE-FX-LC-AT", "RefTitle": "Austria: Stable value guarantee procedure as per MRG", "RefUrl": "/notes/1013171 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}