{"Request": {"Number": "1354157", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 609, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007974112017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001354157?language=E&token=F04E811FC73744D0B1DD526B87B78789"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001354157", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001354157/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1354157"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.07.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1354157 - IS-H: ANA-LIST (CH delta)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1354157&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1354157/D\" target=\"_blank\">/notes/1354157/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Switzerland (CH).<br />This SAP Note concerns the delivery of the country-specific function &quot;Analysis List (Lab Tariff)&quot;.<br />For a technical description, see the attachment (Analysenliste.pdf).</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Analysis List, Lab Tariff</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Analytics List<br />Prerequisite: Note 1354156</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before you implement the source code corrections, you must perform the following actions manually:<br />When you do this, you must take into account the sequence of the manual activities specified here.</p> <OL>1. Implement note 1354156 first.</OL> <OL>2. Unpack the attached files depending on the IS-H version:</OL> <OL><OL>a) HW1354157_472.zip for 4.72 AOP 01 - 33</OL></OL> <OL><OL>b) HW1354157_600.zip for 6.00 AOP 01 - 18</OL></OL> <OL><OL>c) HW1354157_603.zip for 6.03 AOP 01 - 06</OL></OL> <OL><OL>d) HW1354157_604.zip for 6.04 AOP 01 - 05</OL></OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>3. Import the contained requests (one workbench request for each development client) into your system.<br /><br />When you import the attachment transports, generation errors may occur that are corrected after you implement the source code corrections from this SAP Note.</OL> <OL>4. You must now perform the following manual tasks:</OL> <OL><OL>a) You must change the data structure RNTPKCHX:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value RNTPKCHX in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the free line, enter the component name LABZUTYP and the component type NWCH_LABZUTYP.</LI></UL></UL><UL><UL><LI>Then position the cursor underneath in the next free line.</LI></UL></UL> <UL><UL><LI>In the free line, enter the component name LABZUTYP_X and the component type ISH_AKFELD.</LI></UL></UL> <UL><UL><LI>Save and activate the data structure.</LI></UL></UL> <OL><OL>b) You must change the data structure BAPI1300ADDCH:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value BAPI1300ADDCH in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the free line, enter the component name LAB_SURCHARGE_TYPE and the component type NWCH_LABZUTYP.</LI></UL></UL> <UL><UL><LI>Save and activate the data structure.</LI></UL></UL> <OL><OL>c) You must change the data structure BAPI1300ADDCHX:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value BAPI1300ADDCHX in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the free line, enter the component name LAB_SURCHARGE_TYPE and the component type NWCH_LABZUTYP.</LI></UL></UL> <UL><UL><LI>Then position the cursor underneath in the next free line.</LI></UL></UL> <UL><UL><LI>In the free line, enter the component name LAB_SURCHARGE_TYPEX and the component type BAPIUPDATE.</LI></UL></UL> <UL><UL><LI>Save and activate the data structure.</LI></UL></UL> <OL><OL>d) You must change the data structure RNWCHNLEI:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value RNWCHNLEI in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name BEAG and the component type NWCH_BEAG.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name LABTYP and the component type NWCH_LABTYP.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>e) You must change the database table NTPKCH:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Database table&quot; and enter the value NTPKCH in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the table.</LI></UL></UL> <UL><UL><LI>In the first free row, enter the field name LABZUTYP and the data element NWCH_LABZUTYP. Confirm your entry.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>f) Insert text symbols for the report RNWCHUDLS00:<br />(CAUTION: You only have to perform this manual activity for IS-H Version 6.04)</OL></OL> <UL><UL><LI>Call transaction SE80.</LI></UL></UL> <UL><UL><LI>In the input help of the upper of the two input fields, choose &quot;Program&quot;. Enter the program name RNWCHUDLS00 in the lower input field. Confirm your entry.</LI></UL></UL> <UL><UL><LI>Double-click the node &quot;RNWCHUDLS00&quot;.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Goto -> Text Elements -> Text Symbols&quot;.</LI></UL></UL> <UL><UL><LI>Click on &quot;Change&quot; button in the application toolbar.</LI></UL></UL> <UL><UL><LI>Position the cursor on the first free line.</LI></UL></UL> <UL><UL><LI>Enter the following values:                              Sym: 065<br />Text: Asgmt Svce Ctrl Switzerland (Analysis) => Business Area:<br />mLen: 54</LI></UL></UL> <UL><UL><LI>Position the cursor on the next free line.</LI></UL></UL> <UL><UL><LI>Enter the following values:<br />Sym: 066                                                    Text: Deleted assignments Svce Ctrl Switzerland (Department).... mLen: 54</LI></UL></UL> <UL><UL><LI>Save the change as active.</LI></UL></UL> <OL>5. Now implement the source code corrections.<br /></OL> <OL>6. Now add the following entry to the Implementation Guide (IMG):</OL> <OL><OL>a) Call transaction SIMGH.</OL></OL> <OL><OL>b) Select the IMG structure &quot;SAP Healthcare - Industry-Specific Components for Hospitals&quot; and choose &quot;Change IMG Structure&quot;.</OL></OL> <OL><OL>c) Go to SAP Healthcare - Industry-Specific Components for Hospitals => Hospital Basic Data => Service Master Data.</OL></OL> <OL><OL>d) Choose the text of the last CH IMG activity of the category and choose &quot;Insert Activity at Same Level&quot;.</OL></OL> <OL><OL>e) In the &quot;IMG Activity&quot; frame, enter:</OL></OL> <UL><UL><LI>ID:          ISH_CH_TNWCHFACH</LI></UL></UL> <UL><UL><LI>Name: &quot;CH: Maintain User Department&quot;</LI></UL></UL> <OL><OL>f) On the &quot;Document&quot; tab page, enter:</OL></OL> <UL><UL><LI>Document Class: SIMG</LI></UL></UL> <UL><UL><LI>Document Name:   ISH_CH_TNWCHFACH</LI></UL></UL> <OL><OL>g) On the &quot;Attributes&quot; tab page, enter:</OL></OL> <UL><UL><LI>For ID and Description, the same values as above.</LI></UL></UL> <UL><UL><LI>ASAP Roadmap ID:     203</LI></UL></UL> <UL><UL><LI>Necessity:       Optional activity</LI></UL></UL> <UL><UL><LI>Critical/Non-Critical: Non-Critical</LI></UL></UL> <UL><UL><LI>Country Dependency:  Valid only for specified countries</LI></UL></UL> <UL><UL><LI>List CH as the country.</LI></UL></UL> <UL><UL><LI>Under &quot;Assigned Application Components&quot;, add the entry &quot;I010004210&quot;.</LI></UL></UL> <OL><OL>h) On the &quot;Maintenance Objects&quot; tab page, enter:</OL></OL> <UL><UL><LI>For ID and Description, the same values as above.</LI></UL></UL> <UL><UL><LI>Maintenance object type:    Customizing object</LI></UL></UL> <UL><UL><LI>Customizing Object: TNWCHFACH</LI></UL></UL> <UL><UL><LI>Type:                S</LI></UL></UL> <UL><UL><LI>Transaction:        SM30</LI></UL></UL> <OL><OL>i) Save your entry. If you are asked for a package name, specify &quot;NCH1&quot;.</OL></OL> <p><br /><br />This means that the entire Analysis List (Lab Tariff) function is available.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C2754910)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C2754910)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001354157/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1354157_603.zip", "FileSize": "58", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306142009&iv_version=0003&iv_guid=6E115A26FA51D147976576070AA89F4A"}, {"FileName": "HW1354157_604.zip", "FileSize": "77", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306142009&iv_version=0003&iv_guid=9FA00D9D9ED49A4D84D96CA40C8E902D"}, {"FileName": "HW1354157_600.zip", "FileSize": "59", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306142009&iv_version=0003&iv_guid=8B0AF4FFAACDA442B80595AA7CB6417D"}, {"FileName": "HW1354157_472.zip", "FileSize": "60", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306142009&iv_version=0003&iv_guid=BE4CF86CE866984882A221B21160C289"}, {"FileName": "Analysenliste.PDF", "FileSize": "164", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306142009&iv_version=0003&iv_guid=D32CD6D6FD8D64408FEAA32B44F57DB9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1366434", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Analysis List - Service Rules", "RefUrl": "/notes/1366434"}, {"RefNumber": "1359544", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List (CUA Definitions)", "RefUrl": "/notes/1359544"}, {"RefNumber": "1357130", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List - Correction Package[1]", "RefUrl": "/notes/1357130"}, {"RefNumber": "1354156", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA LIST (SAP Delta)", "RefUrl": "/notes/1354156"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1366434", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Analysis List - Service Rules", "RefUrl": "/notes/1366434 "}, {"RefNumber": "1359544", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List (CUA Definitions)", "RefUrl": "/notes/1359544 "}, {"RefNumber": "1354156", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA LIST (SAP Delta)", "RefUrl": "/notes/1354156 "}, {"RefNumber": "1357130", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List - Correction Package[1]", "RefUrl": "/notes/1357130 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF34", "URL": "/supportpackage/SAPKIPHF34"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60019INISH", "URL": "/supportpackage/SAPK-60019INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60208INISH", "URL": "/supportpackage/SAPK-60208INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60307INISH", "URL": "/supportpackage/SAPK-60307INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60406INISH", "URL": "/supportpackage/SAPK-60406INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/0001354157/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 9, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "838177 ", "URL": "/notes/838177 ", "Title": "IS-H CH: Create Service Master with Template - NTPKCH", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "840356 ", "URL": "/notes/840356 ", "Title": "IS-H CH: Check Overhead Type in Activity Master API", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "933828 ", "URL": "/notes/933828 ", "Title": "IS-H AT: Service Master - Online Dialog - Create with Template", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "952834 ", "URL": "/notes/952834 ", "Title": "IS-H CH: Conversion of Drop-Off Unit for Pricing", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1084839 ", "URL": "/notes/1084839 ", "Title": "IS-H: External Order - Corrections Part 1", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1226193 ", "URL": "/notes/1226193 ", "Title": "IS-H CH: Decode Encrypted Chapter Numbers", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "933828 ", "URL": "/notes/933828 ", "Title": "IS-H AT: Service Master - Online Dialog - Create with Template", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "952834 ", "URL": "/notes/952834 ", "Title": "IS-H CH: Conversion of Drop-Off Unit for Pricing", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1021573 ", "URL": "/notes/1021573 ", "Title": "IS-H: Service Master - Incorrect Interval Is Presented", "Component": "IS-H-BD-SER"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1084839 ", "URL": "/notes/1084839 ", "Title": "IS-H: External Order - Corrections Part 1", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1226193 ", "URL": "/notes/1226193 ", "Title": "IS-H CH: Decode Encrypted Chapter Numbers", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1226193 ", "URL": "/notes/1226193 ", "Title": "IS-H CH: Decode Encrypted Chapter Numbers", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1354157&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1354157/D\" target=\"_blank\">/notes/1354157/D</a>."}}}}