{"Request": {"Number": "33319", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 223, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014382252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000033319?language=E&token=5E60F1D14D031635ABE1901EEF0E0E6C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000033319", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000033319/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "33319"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.05.2001"}, "SAPComponentKey": {"_label": "Component", "value": "BC-ABA-SC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Dynpro and CUA engine"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Runtime Environment - ABAP Language Issues Only", "value": "BC-ABA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Dynpro and CUA engine", "value": "BC-ABA-SC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA-SC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "33319 - Batch input: Backgr. runs diff. than in the dialog"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You run a batch input and this behaves differently in the background than it does online.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Batch inputshdb<br />Transaction recorder<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There can be several reasons for this:<br /></p> <OL>1. The batch input always runs in the dialog with the authorizations and settings of the logon user.<br />In the background the batch input runs with the user that you specified when creating a folder, in function module BDC_OPEN_GROUP as parameter USER.<br />This can be seen in Transaction SM35, overview of folders.<br />In the column Authorization you will find the username with which you are working in the background.<br /></OL> <OL>2. Dynpros that are entirely switched to inactive, i.e. that do not contain any input fields, active checkboxes or selection fields, will always have the cursor on a field through the SAPGui in the dialog. In the background there is no image output (no SAPGui) i.e. the cursor does not sit and problems may occur when you process the screen. The screen does not run any processing in the PAI and remains on its own.<br />The error message 'Batch input data for screen XXXXXXXX 9999 are not available' is the consequence in the background.<br />In this case take the service field BDC_CURSOR and set the cursor by hand on a field in the screen.<br /></OL> <OL>3. The application transaction behaves in the background (batch) differently than in normal dialog operation. Often the system flag SY-BATCH is then read at the value 'X' in the corresponding application program. The flow in the batch can be simulated in the dialog by setting a break point in the application program and changing the system flag SY-BATCH to the value 'X' in the ABAP debugger.<br /></OL> <OL>4. The application transaction uses frontend controls that cannot be processed in the background.<br />(see Note 311440 batch input and controls)<br /></OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>see above<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D034405)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000033319/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000033319/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033319/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033319/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033319/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033319/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033319/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033319/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033319/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "721791", "RefComponent": "PA-BC", "RefTitle": "Dump when running a batch input session in OM", "RefUrl": "/notes/721791"}, {"RefNumber": "68826", "RefComponent": "BC-DWB-UTL", "RefTitle": "Different screens in batch-input mode", "RefUrl": "/notes/68826"}, {"RefNumber": "634956", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Starting sessions indep.", "RefUrl": "/notes/634956"}, {"RefNumber": "592896", "RefComponent": "SCM-APO-MD-PPM", "RefTitle": "PPM maintenance: Mass maintenance of PPM plans", "RefUrl": "/notes/592896"}, {"RefNumber": "554139", "RefComponent": "BC-ABA-SC", "RefTitle": "FAQ 2: <PERSON><PERSON> input", "RefUrl": "/notes/554139"}, {"RefNumber": "491055", "RefComponent": "PA-PD-QR", "RefTitle": "Report RHXPE_EXPIRED_QUALI does not generate any spool list", "RefUrl": "/notes/491055"}, {"RefNumber": "45507", "RefComponent": "BC-ABA-SC", "RefTitle": "Processing batch input sessions in foreground", "RefUrl": "/notes/45507"}, {"RefNumber": "433137", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Simulating background mode", "RefUrl": "/notes/433137"}, {"RefNumber": "370958", "RefComponent": "PS-ST-WBS", "RefTitle": "Consulting note:Frequnt problems during batch input", "RefUrl": "/notes/370958"}, {"RefNumber": "311440", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input and controls", "RefUrl": "/notes/311440"}, {"RefNumber": "1355047", "RefComponent": "SCM-APO-INT-IMO", "RefTitle": "Batch input for CFM2/3", "RefUrl": "/notes/1355047"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1946288", "RefComponent": "BC-SRV-SCR", "RefTitle": "Cannot Change Editor to Graphical Editor in Call Transaction or Batch Input Mode", "RefUrl": "/notes/1946288 "}, {"RefNumber": "2529992", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW Background - Error \"Control Framework: Fatal error - GUI cannot be reached\"", "RefUrl": "/notes/2529992 "}, {"RefNumber": "2272160", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Creation of session without authorization user", "RefUrl": "/notes/2272160 "}, {"RefNumber": "1355047", "RefComponent": "SCM-APO-INT-IMO", "RefTitle": "Batch input for CFM2/3", "RefUrl": "/notes/1355047 "}, {"RefNumber": "433137", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Simulating background mode", "RefUrl": "/notes/433137 "}, {"RefNumber": "634956", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Starting sessions indep.", "RefUrl": "/notes/634956 "}, {"RefNumber": "481034", "RefComponent": "MM-PUR-GF-DTF", "RefTitle": "FAQ: Data transfer (batch input) in purchasing", "RefUrl": "/notes/481034 "}, {"RefNumber": "721791", "RefComponent": "PA-BC", "RefTitle": "Dump when running a batch input session in OM", "RefUrl": "/notes/721791 "}, {"RefNumber": "554139", "RefComponent": "BC-ABA-SC", "RefTitle": "FAQ 2: <PERSON><PERSON> input", "RefUrl": "/notes/554139 "}, {"RefNumber": "592896", "RefComponent": "SCM-APO-MD-PPM", "RefTitle": "PPM maintenance: Mass maintenance of PPM plans", "RefUrl": "/notes/592896 "}, {"RefNumber": "311440", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input and controls", "RefUrl": "/notes/311440 "}, {"RefNumber": "45507", "RefComponent": "BC-ABA-SC", "RefTitle": "Processing batch input sessions in foreground", "RefUrl": "/notes/45507 "}, {"RefNumber": "370958", "RefComponent": "PS-ST-WBS", "RefTitle": "Consulting note:Frequnt problems during batch input", "RefUrl": "/notes/370958 "}, {"RefNumber": "491055", "RefComponent": "PA-PD-QR", "RefTitle": "Report RHXPE_EXPIRED_QUALI does not generate any spool list", "RefUrl": "/notes/491055 "}, {"RefNumber": "68826", "RefComponent": "BC-DWB-UTL", "RefTitle": "Different screens in batch-input mode", "RefUrl": "/notes/68826 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}