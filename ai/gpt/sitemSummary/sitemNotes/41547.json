{"Request": {"Number": "41547", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 455, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014802912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000041547?language=E&token=53C2C20A6BF0BDC5BE54E176CAE714A1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000041547", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000041547/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "41547"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.09.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-PRN-SPO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Spool System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Print and Output Management", "value": "BC-CCM-PRN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Spool System", "value": "BC-CCM-PRN-SPO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN-SPO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "41547 - How does report RSPO0041 work?"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Report RSPO0041 is used to tidy up the spool database and to delete old spool requests. This must be done regularly in the productive system, as the spool database may only contain 32000 spool requests (possible as of Rel. 2.2E 99000, refer to Note 48284, as of Rel. 4.0A 2000000000).<br />Unfortunately, the selection screen for this report is slightly misleading, and an incorrectly filled selection screen can lead to unwanted results. This Note explains the report more precisely. Note 130978 provides a modified version of report RSPO0041!<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Delete spool requests</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Report RSPO0041 can be started via Transactions SE38 or SA38. In the spool administration, there is the menu point <B>Delete out-of-date spool requests</B>. This also calls report RSPO0041, but the selection screen is slightly different and does not have as many entry options.</p> <UL><LI>Firstly, enter the client in which the spool requests should be deleted. You can also use * to delete spool requests in all clients.</LI></UL> <UL><LI>Next is a field with the title 'All out-of-date spool requests'. This field refers to the <B>Expiration date</B> of the spool requests. All spool requests are deleted whose expiration date is passed and which are not being processed any more. The expiration date is set when generating spool requests, and has a default value of eight days after creation of the spool request. Transaction SP01 allows you to change the expiration date of a spool request.</LI></UL> <UL><LI>A block of three fields is generated. All fields refer to the <B>Creation date</B> of the spool request.<br />The upper field shows how old the spool requests must be to be deleted if one of the next two fields is marked.</LI></UL> <UL><UL><LI>If the middle field is selected, only spool requests which are older than the entered value and which have been printed successfully at least once, are deleted (and have been archived with the Hot Package mentioned here).</LI></UL></UL> <UL><UL><LI>When you select the lower field, <B>all</B> spool requests older than the given value (all spool requests with minimum age) are deleted.</LI></UL></UL> <UL><LI>The following block of three checkboxes controls processing of the report.</LI></UL> <UL><UL><LI>The most important of these fields is the bottom one. If it is checked, no spool requests are deleted, but a log is generated to show what would be deleted.</LI></UL></UL> <UL><UL><LI>The point <B>Log everything</B> makes sure that everything the report does is logged. This is set automatically if <B>Only log without deletion</B> is selected.</LI></UL></UL> <UL><LI>The field <B>COMMIT all ... spool requests</B> makes sure that the report sends a COMMIT to the database. This is done to stop the database rollback segments overflowing if a lot of very large spool requests are deleted. This can be prevented by reducing this value. However, this field is only effective as of Release 3.0, due to a programming error.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>No solution, as this Note only contains information.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025322)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002358)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000041547/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000041547/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "64333", "RefComponent": "BC-CCM-PRN", "RefTitle": "Changing default value for spool retention period", "RefUrl": "/notes/64333"}, {"RefNumber": "48400", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Reorganization of TemSe and spool", "RefUrl": "/notes/48400"}, {"RefNumber": "48284", "RefComponent": "BC-CCM-PRN", "RefTitle": "System cannot generate any more spool requests", "RefUrl": "/notes/48284"}, {"RefNumber": "4066", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/4066"}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083"}, {"RefNumber": "137751", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/137751"}, {"RefNumber": "130978", "RefComponent": "BC-CCM-PRN", "RefTitle": "RSPO1041 - alternative to RSPO0041", "RefUrl": "/notes/130978"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2457819", "RefComponent": "BC-CCM-PRN", "RefTitle": "Print and Output Management: Summary of Resource Links and Information", "RefUrl": "/notes/2457819 "}, {"RefNumber": "2675824", "RefComponent": "BC-CCM-PRN", "RefTitle": "Old Spools Not Deleted with RSPO0041 / RSPO1041 Variant Settings", "RefUrl": "/notes/2675824 "}, {"RefNumber": "2635724", "RefComponent": "BC-CCM-PRN", "RefTitle": "Monitor or Display Spool Range Limit and Status", "RefUrl": "/notes/2635724 "}, {"RefNumber": "2593715", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "(Internal KBA)  Spool: RT_INSERT Reports Error 128 for Table TSP02", "RefUrl": "/notes/2593715 "}, {"RefNumber": "2461604", "RefComponent": "BC-CCM-PRN", "RefTitle": "Exclude specific spools from RSPO1041 spool deletion job", "RefUrl": "/notes/2461604 "}, {"RefNumber": "48284", "RefComponent": "BC-CCM-PRN", "RefTitle": "System cannot generate any more spool requests", "RefUrl": "/notes/48284 "}, {"RefNumber": "48400", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Reorganization of TemSe and spool", "RefUrl": "/notes/48400 "}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083 "}, {"RefNumber": "130978", "RefComponent": "BC-CCM-PRN", "RefTitle": "RSPO1041 - alternative to RSPO0041", "RefUrl": "/notes/130978 "}, {"RefNumber": "64333", "RefComponent": "BC-CCM-PRN", "RefTitle": "Changing default value for spool retention period", "RefUrl": "/notes/64333 "}, {"RefNumber": "504952", "RefComponent": "BC-CCM-PRN", "RefTitle": "Composite SAP Note for spool and print", "RefUrl": "/notes/504952 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "30D", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "30F", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 30D", "SupportPackage": "SAPKH30D90", "URL": "/supportpackage/SAPKH30D90"}, {"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F87", "URL": "/supportpackage/SAPKH30F87"}, {"SoftwareComponentVersion": "SAP_HR 30F", "SupportPackage": "SAPKE30F87", "URL": "/supportpackage/SAPKE30F87"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H66", "URL": "/supportpackage/SAPKH31H66"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H66", "URL": "/supportpackage/SAPKE31H66"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I36", "URL": "/supportpackage/SAPKH31I36"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I36", "URL": "/supportpackage/SAPKE31I36"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B29", "URL": "/supportpackage/SAPKH40B29"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B29", "URL": "/supportpackage/SAPKE40B29"}, {"SoftwareComponentVersion": "SAP_APPL 45A", "SupportPackage": "SAPKH45A29", "URL": "/supportpackage/SAPKH45A29"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B11", "URL": "/supportpackage/SAPKH45B11"}, {"SoftwareComponentVersion": "SAP_BASIS 46A", "SupportPackage": "SAPKB46A03", "URL": "/supportpackage/SAPKB46A03"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}