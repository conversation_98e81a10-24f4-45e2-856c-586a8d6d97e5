{"Request": {"Number": "1338276", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 452, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007873882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001338276?language=E&token=BD067625DA71D21CCBF795BDCC99919C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001338276", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001338276/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1338276"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Pilot Release"}, "ReleasedOn": {"_label": "Released On", "value": "14.09.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-WEB"}, "SAPComponentKeyText": {"_label": "Component", "value": "BEx Web  ABAP Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BEx Web ABAP Runtime", "value": "BW-BEX-ET-WEB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-WEB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1338276 - Delete bookmarks created by stateless web application"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n\r\n<p>Cluster table RSZWOBJ contains large datasets due to increased usage of bookmarking and you wanted to delete the unwanted bookmarks and views that are used by the BW web application. Bookmarks are stored in several header tables and two cluster tables, RSZWOBJ and RSIXWWW with the key \"BM\". The storage size of the tables can increase quickly if user bookmarking and stateless web application are used heavily. This note deals specifically with the bookmarks inexplicitly generated by a stateless web application.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n\r\n<p>RSZWOBJ, RSZWBOOKMARK, bookmarks, storage, stateless=X, broadcasting, RSZWVIEW, STATELESS_NAV=X</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>    The RUNSCHED bookmark administration provides only the deletion and view for user created bookmarks, including the normal bookmarks created in a stateless web application. This will not show the application generated bookmarks and stateless bookmarks. Also, deletion of stateless application bookmarks from here deletes only the normal bookmark created by this application and its corresponding view and item tables. This does not delete the appliaction generated bookmarks and its links for a stateless application (STATELESS_NAV=X)<br /><br />Bookmarks are also created while broadcasting a report or a template. In such cases, the broadcaster will create the bookmark with stateless navigation flag as \"T\". Please refer to SAP help document at http://help.sap.com/saphelp_nw04/helpdata/en/33/51fa40badbf36fe10000000a1550b0/content.htm about reorganization of this type of BOOKMARKS.<br /><br />In case of a stateless web application, apart from the normal users bookmark creation, a stateless web application will create a single bookmark on the bookmark tables upon initial execution with STATELESS_NAV=X. This is to keep track of the navigation steps performed in the report. Successive bookmarking in the stateless application will create a normal bookmark record along with a stateless bookmark record. Eg: Initial run and bookmarking will create 3 bookmarks. Successive bookmarking will do 2 more and so on. Successive navigation like drilldown in a stateless application simply affects only view and item tables and not the bookmark table.<br /><br />All the navigation steps are stored as views in RSZWVIEW with BOOKMARK_COMP is \"X\". Entry in RSZWBOOKMARK is also made with STATELESS_NAV=X. Every navigation performed does not create entry in the RSZWBOOKMARK but RSZWVIEW is affected with a new entry. Again, if a bookmark is done on this stateless application, RSZWBOOKMARK gets an entry and RSZWVIEW gets two entries where STATELESS_NAV and BOOKMARK_COMP is also X. RSZWITEM header is also affected.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>BW 3.50</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Import Support Package 25 for Release 3.50 (BW3.50 Patch 25 or SAPKW35025) into your BW system. The Support Package will be available when note 1243557 with the short text \"SAPBWNews BW SP25 NetWeaver'04 Stack 25\", describing this Support Package in more detail, is released for customers.</p>\r\n<ul>\r\n<li>SAP NetWeaver BI 7.00</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Import Support Package 22 for SAP NetWeaver BI 7. 00(SAPKW70022) into your BI system. The Support Package will be available when note 1325072 with the short text \"SAPBINews NW BI 7.0 ABAP SP22\", describing this Support Package in more detail, is released for customers.<br /><br /><br />In urgent cases the correction instructions can be used.<br /><br />Run the report RSRD_STATELESS_BOOKMARK_DELETE (seen in SE80) to delete all temporary bookmarks created by stateless web appications. This report will delete all the temporary bookmarkid's, viewid's and item's from the header tables (RSZWBOOKMARK, RSZWVIEW, RSZWITEM) and the cluster table RSZWOBJ(vi), RSZWOBJ(bm) and RSZWOBJ(it). You can use the \"Test Call\" button to check your contents before deletion.<br /><br />The report gets the timestamp and all the views and bookmarks are deleted before this timestamp. Report mainly concentrates on the entries for stateless web application(STATELESS_NAV=X, BOOKMARK_COMP=X).<br /><br /><strong>Caution:</strong><br />Make sure that the contents you see are not needed because if you delete, there is no single way to recover them.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET-BC (Broadcasting)"}, {"Key": "DownPort/UpPort-WF", "Value": "UpPort check necessary"}, {"Key": "Release's validity for upgrade customers checked?", "Value": "checked"}, {"Key": "Responsible                                                                                         ", "Value": "I816090"}, {"Key": "Processor                                                                                           ", "Value": "I816670"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001338276/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "589899", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Deleting unusable entries in table RSZIXWWW", "RefUrl": "/notes/589899"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "589899", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Deleting unusable entries in table RSZIXWWW", "RefUrl": "/notes/589899 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 1, "URL": "/corrins/0001338276/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}