{"Request": {"Number": "691228", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 743, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015582022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000691228?language=E&token=E9111F8FDCC1B654D24EBAD95058D87B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000691228", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000691228/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "691228"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.09.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-ISR"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Retail and Consumer Products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail and Consumer Products", "value": "BW-BCT-ISR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "691228 - Composite SAP Note: RMCBINIT_BW stock initialization"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Until now, a number of basic notes were created about initializing stocks using the RMCBINIT_BW program (transaction MCNB) for BW. However, problems occurred repeatedly during the implementation of these notes.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RMCBINIT_BW, MCNB, 2LIS_03_BX</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note applies to all plug-in releases up to and including Plug-In 2003.1.<br />The note is NOT relevant for Plug-In 2004.1.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>An attachment (ZIP file) was added to this note. This ZIP file contains various documents that describe the exact procedure for importing the corrections. The attachments are only displayed if the note is selected in the SAP Service Marketplace (see note 422409).<br />CAUTION: After you have imported the source code corrections contained in the specified ZIP file, we recommend that you implement the following notes AFTER THAT:</p><UL><LI>438418</LI></UL> <UL><LI>726485</LI></UL> <UL><LI>756151</LI></UL> <UL><LI>762219</LI></UL> <UL><LI>763424</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-MM-IM (BW only - Inventory Management)"}, {"Key": "Other Components", "Value": "BW-BCT-ISR-LOG (BW only - Logistic)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D024566)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022697)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000691228/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000691228/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "initial_stocks_470.zip", "FileSize": "383", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000322202003&iv_version=0011&iv_guid=F00787E76D6A384AB941D642760A93C0"}, {"FileName": "Intitial_Stocks.zip", "FileSize": "383", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000322202003&iv_version=0011&iv_guid=754105A4BE47394D9E824D0E4CB9067B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "767786", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Stock in transit valued incorrectly with split valuation", "RefUrl": "/notes/767786"}, {"RefNumber": "756151", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "PI: No non-valuated stocks with stock initialization", "RefUrl": "/notes/756151"}, {"RefNumber": "684789", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "Plug: Addnl valuation class characteristic in initial stock", "RefUrl": "/notes/684789"}, {"RefNumber": "663681", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "PLUG: Rounding errors + valuation errors in initial stocks", "RefUrl": "/notes/663681"}, {"RefNumber": "640515", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "Sep. value evaluated special stocks incorrect", "RefUrl": "/notes/640515"}, {"RefNumber": "487043", "RefComponent": "BW-BCT-ISR", "RefTitle": "BW: Calculated sales order stocks and project stocks", "RefUrl": "/notes/487043"}, {"RefNumber": "438418", "RefComponent": "BW-BCT-ISR", "RefTitle": "BW: No unrestricted-use consignment stock in S278", "RefUrl": "/notes/438418"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "767786", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Stock in transit valued incorrectly with split valuation", "RefUrl": "/notes/767786 "}, {"RefNumber": "756151", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "PI: No non-valuated stocks with stock initialization", "RefUrl": "/notes/756151 "}, {"RefNumber": "684789", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "Plug: Addnl valuation class characteristic in initial stock", "RefUrl": "/notes/684789 "}, {"RefNumber": "663681", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "PLUG: Rounding errors + valuation errors in initial stocks", "RefUrl": "/notes/663681 "}, {"RefNumber": "640515", "RefComponent": "BW-BCT-ISR-LOG", "RefTitle": "Sep. value evaluated special stocks incorrect", "RefUrl": "/notes/640515 "}, {"RefNumber": "487043", "RefComponent": "BW-BCT-ISR", "RefTitle": "BW: Calculated sales order stocks and project stocks", "RefUrl": "/notes/487043 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI", "From": "2001_1_46B", "To": "2001_1_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2001_2_46B", "To": "2001_2_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2002_1_46B", "To": "2002_1_470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2002_2_40B", "To": "2002_2_470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2003_1_40B", "To": "2003_1_470", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2001_1_40B", "To": "2001_1_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2001_2_40B", "To": "2001_2_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2002_1_40B", "To": "2002_1_45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI 2003_1_40B", "SupportPackage": "SAPKIPZH17", "URL": "/supportpackage/SAPKIPZH17"}, {"SoftwareComponentVersion": "PI 2003_1_45B", "SupportPackage": "SAPKIPZH27", "URL": "/supportpackage/SAPKIPZH27"}, {"SoftwareComponentVersion": "PI 2003_1_46B", "SupportPackage": "SAPKIPZH37", "URL": "/supportpackage/SAPKIPZH37"}, {"SoftwareComponentVersion": "PI 2003_1_46C", "SupportPackage": "SAPKIPZH47", "URL": "/supportpackage/SAPKIPZH47"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}