{"Request": {"Number": "183379", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 524, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014742962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000183379?language=E&token=5C90C6EE3DEF66EF667CFAE9B79451EB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000183379", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000183379/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "183379"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 39}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.06.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "183379 - Composite SAP note subsequent settlement (Purchasing) 4.6"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>-- Composite SAP Note: Problems and errors in&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Subsequent Settlement (Purchasing)&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 4.6B/C&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Status December 20th, 2001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br /></p> <b>***********************************************************************</b><br /> <b>* Note:</b><br /> <b>*</b><br /> <b>* For safety reasons, SAP strongly recommends customers with</b><br /> <b>* mass data (large datasets * that might have to be subjected</b><br /> <b>* to a recompilation) not to go live with Subsequent Settlement</b><br /> <b>* under Release 4.6A.</b><br /> <b>* Only use Release 4.6A to prepare the implementation.</b><br /> <b>*</b><br /> <b>***********************************************************************</b><br /> <b>* European Monetary Union (note):</b><br /> <b>*</b><br /> <b>* 480984 Currency conversion, message MN 471</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Rebate arrangement currency cannot be changed)</b><br /> <b>*</b><br /> <b>* Incorrect document currency</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(incorrect postings in Financial Accounting, currencies mixed up)</b><br /> <b>*</b><br /> <b>* 452365 Rebate arrangement currency not copied to condition records</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(undesired currency in the condition record)</b><br /> <b>*</b><br /> <b>* 437429 Message SG105 List settlement documents, setting up incomes</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(REvenues in Financial Accounting and/or S074 incorrect)</b><br /> <b>*</b><br /> <b>* 400432 Expiring currencies, rebate arrangmnt maint. (purchasing, sales)</b><br /> <b>*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2</b><br /> <b>***********************************************************************</b><br /> <b>* Notes which you must also implement as a precaution as</b><br /> <b>* correcting the consequences of not doing so might prove to be</b><br /> <b>*</b><br /> <b>* 422649 Error in validity display (agrmt maintenance)- deletion</b><br /> <b>*</b><br /> <b>* 427415 No update for proforma documents into LIS</b><br /> <b>*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You absolutely need to implement this note (even if you are</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;not affected by the currency changeover)!</b><br /> <b>*</b><br /> <b>* 408521 MN495 (update simulation), MN514 (detailed statement)</b><br /> <b>*</b><br /> <b>* 387206 Setup of statistical data for agency business incorrect</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Release 4.6C)</b><br /> <b>*</b><br /> <b>* 394619 Provisions f income n cancelled if cancelling settlement doc</b><br /> <b>*</b><br /> <b>* 315769 Subseqnt updatng of bus.volume, price determtn goods receipt</b><br /> <b>*</b><br /> <b>* 368273 Income not updated for total income 0 Required fields</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;settlement document incorrect settlement</b><br /> <b>* 367386 ME21N field settlement group 1 not filled</b><br /> <b>*</b><br /> <b>205594 Subseqnt settlement: Update is</b><br /> <b>not</b><br /> <b>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; cancelled</b><br /> <b>*</b><br /> <b>* 357600 LIS Update/Business volume data for agency business</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;incompl.</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;business volume gets lost</b><br /> <b>*</b><br /> <b>* 335654 Incorr. val.w/ new price determintn for GR (Pric.date cat.5)</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;undone</b><br /> <b>*</b><br /> <b>* 355952 Updating simultn causes multiple</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;updating of business volume</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Settlement of icorrects business volume data and incomes</b><br /> <b>*</b><br /> <b>* 335654 problem with new price determination at the WE</b><br /> <b>*</b><br /> <b>* 211488 ERS for automatically created purchase order</b><br /> <b>*</b><br /> <b>* 305388 Subsequent business volume update in wrong period</b><br /> <b>*</b><br /> <b>* 303047 RMCENEUA - Error in selection by info structures</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Settlement of incorrect business volume data and incomes</b><br /> <b>*</b><br /> <b>* 204337 Message MN514, business volume update goods receipt</b><br /> <b>*</b><br /> <b>* 196526 No S111 index entries for subsequent business volume update</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;missing business volume updates</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;business volume data incomplete</b><br /> <b>*</b><br /> <b>* 197417 Subsequent Settlement: Message 06 167 during the settlement</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;to a rebate arrangement</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 4.6B</b><br /> <b>*</b><br /> <b>* 179864 PURCHIS - commitments - problems during IR or GR</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 4.6A and 4.6B</b><br /> <b>*</b><br /> <b>* 175341 Multiple updating income when changing customer billing doc * &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Required fields settlement document Multiple income update at</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Installing customer billing document change.</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Settlement of incorrect incomes !</b><br /> <b>*</b><br /> <b>* In Release 4.6A, you must implement Note 158572 Subsequent</b><br /> <b>* settlement and Enjoy purchase order MEPO.</b><br /> <b>* considering. Otherwise, business volume data might be damaged!</b><br /> <b>*</b><br /> <b>* 175160 PURCHIS-GR for several PO items with PO generation</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Purchase order generation</b><br /> <b>*</b><br /> <b>* 206060 Ind. 'Subseq. settlement', deviating vendor data.</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See also Note 214146!</b><br /> <b>***********************************************************************</b><br /> <p><br />This note only refers to Release 4.6. For Releases 3.0A to 3.1I refer to Note 40147, for Release 4.0B to Note 104668 and for Release 4.5B to Note 152725.</p> <b>General information</b><br /> <p>This note mainly points out the problems and errors that most frequently occur in connection with subsequent settlement (Purchasing). It should help you to analyze frequent problems and maybe to solve them.<br />You will find a list of all notes on the topic of subsequent settlement that refer to known problems in section \"Related Notes\". You will receive all notes on eliminating program errors in the next available Support Package.</p> <b>Handling</b><br /> <p>If you have problems in subsequent settlement (purchasing), by using this note you can first try too solve the problem yourself. Check whether one of the notes refers to your problem. Read the note and carry out the checks specified there. You may find references on notes to solve the error.<br />Make note that you can access various informative consultation notes concerning purchasing, goods receipts, and so on under search criteria MM-PUR* (components) and FAQ (search text).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, composite SAP Note<br />Transactions MEB1, MEB2, MEB3, MEB4, MEB6, MEB8, MEB9 MCE+, MEBA<br />Programs SAPMV13A, SAPMNB01, RWMBON01, RWMBON03, RWMBON04, RWMBON06, RWMBON08, RMCE0900.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. Notes on Problem solution/Consulting</OL> <p><br />167284 Subsequent Settlement: Some general notes (Q&amp;A's and Customizing)<br /><br />216962 Comparison of period-specific condition ignored in settlemnt</p> <UL><UL><LI>The note explains the connection between the business volume comparison and agreement and the settlement</LI></UL></UL> <p><br />75655 Error message VK358: Porg &amp; with CCode &amp; differs from Ccode of arrangement.<br />You are working with several company codes.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basic problem:</p> <UL><UL><LI>This means that your purchasing organization is not firmly assigned to a company code. See also Note 153694.</LI></UL></UL> <p><br />153694 Subsequent Settlement: Credit-side or debit-side settlement type</p> <UL><UL><LI>Note on the importance of settlement type</LI></UL></UL> <p><br />72199 Problem analysis update business volume data<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>Business volume data not updated for a purchase order</LI></UL></UL> <UL><UL><LI>Business volume data updated for a purchase order but the scale or condition basis has value zero</LI></UL></UL> <p><br />112413 Subsequent Settlement: Units of measure<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Possible problems when using units of measure</LI></UL></UL> <p><br />113031 Subsequent Settlement: Taxes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>This describes how tax is handled in the subsequent settlement process(calculation)</LI></UL></UL> <p><br />77258 Subsequent Settlement: Required fields settlement document<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>The following error messages occur in the settlement of an arrangement</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;055(00) \"Required entry not made\" or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;344 (00) \"No batch input data for screen SAPMM08R &amp;\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;not existing\" on<br /><br />80233 Debit-side settlement: Customizing, error message<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>Error messages of message class VF</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure in SD Customizing from point of view of subsequent settlement</LI></UL></UL> <p><br />73214 Subsequent settlement: Retrospective compilation / recompilation of business volume data<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>No update is carried out during the retrospective compilation of business volume data</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure when using function</LI></UL></UL> <UL><UL><LI>Functional restrictions of function</LI></UL></UL> <p><br />381831 Consignment processing and subsequent settlement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>No updating of business volume for settlement documents consignment processing</LI></UL></UL> <p><br />413786 Release notes 4.5 for subsequent settlement incomplete</p> <UL><UL><LI>External data transfer by means of proforma vendor billing documents</LI></UL></UL> <p><br />333914 Subsequent settlement: performance collective note<br /></p> <OL>2. Notes on known problems</OL> <p>See \"Related Notes\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "MEB4"}, {"Key": "Transaction codes", "Value": "MEB2"}, {"Key": "Transaction codes", "Value": "MEBA"}, {"Key": "Transaction codes", "Value": "MEB1"}, {"Key": "Transaction codes", "Value": "MEB3"}, {"Key": "Transaction codes", "Value": "MEB8"}, {"Key": "Transaction codes", "Value": "MEB6"}, {"Key": "Transaction codes", "Value": "MEB9"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023678)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000183379/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183379/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199"}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040"}, {"RefNumber": "502747", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement via credit memo: Message MN178", "RefUrl": "/notes/502747"}, {"RefNumber": "500644", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Business volume comparison and agreement: message MN242", "RefUrl": "/notes/500644"}, {"RefNumber": "496517", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/496517"}, {"RefNumber": "492607", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No message output with cancellation of settlement document", "RefUrl": "/notes/492607"}, {"RefNumber": "490728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Material description is confused in rebate arrangements", "RefUrl": "/notes/490728"}, {"RefNumber": "489318", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/489318"}, {"RefNumber": "486757", "RefComponent": "MM-PUR-GF-MAS", "RefTitle": "MEMASSPO: Settlement field (EBONF) not changeable", "RefUrl": "/notes/486757"}, {"RefNumber": "485429", "RefComponent": "LO-AB-RS", "RefTitle": "Subsequent settlement: Message SG105", "RefUrl": "/notes/485429"}, {"RefNumber": "485130", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Arrangement status for interim settlement set incorrectly", "RefUrl": "/notes/485130"}, {"RefNumber": "480984", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currency conversion, message MN 471", "RefUrl": "/notes/480984"}, {"RefNumber": "458695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Man. correction f. customer BillDoc income w/ partial settl.", "RefUrl": "/notes/458695"}, {"RefNumber": "457035", "RefComponent": "LO-AB-RS", "RefTitle": "Incorrect doc. status if document not relevant to accounting", "RefUrl": "/notes/457035"}, {"RefNumber": "454172", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "No text determination with billing interface", "RefUrl": "/notes/454172"}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365"}, {"RefNumber": "452056", "RefComponent": "LO-AB", "RefTitle": "Error handling when posting agency documents", "RefUrl": "/notes/452056"}, {"RefNumber": "445331", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currencies can be deleted in the mass maintenance", "RefUrl": "/notes/445331"}, {"RefNumber": "444449", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Missing data in user exit message determination", "RefUrl": "/notes/444449"}, {"RefNumber": "441313", "RefComponent": "LO-MD-RPC", "RefTitle": "Doc index generation: Variable key wrong for specific values", "RefUrl": "/notes/441313"}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493"}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324"}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429"}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199"}, {"RefNumber": "433752", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error when checking for release to accounting", "RefUrl": "/notes/433752"}, {"RefNumber": "427415", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for proforma documents into LIS", "RefUrl": "/notes/427415"}, {"RefNumber": "425076", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Changd businss vol. not takn into acc. for settlmnt accnting", "RefUrl": "/notes/425076"}, {"RefNumber": "425033", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem extending arrangements if arrangement contains EURO", "RefUrl": "/notes/425033"}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649"}, {"RefNumber": "413786", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Release notes 4.5 for subsequent settlement incomplete", "RefUrl": "/notes/413786"}, {"RefNumber": "408521", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN495 (update simulation), MN514 (detailed statement)", "RefUrl": "/notes/408521"}, {"RefNumber": "403734", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN242 upon settlement (different calculation rules)", "RefUrl": "/notes/403734"}, {"RefNumber": "402138", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tolerance during business volume comparison and agreement", "RefUrl": "/notes/402138"}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432"}, {"RefNumber": "399160", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "SM13: Update records in status INIT", "RefUrl": "/notes/399160"}, {"RefNumber": "399118", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msg NAA045 list output for arrangemts (RWMBON02, Trans.MEB5)", "RefUrl": "/notes/399118"}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739"}, {"RefNumber": "397517", "RefComponent": "LO-AB", "RefTitle": "RMCENEUB: Termination CONVT_NO_NUMBER", "RefUrl": "/notes/397517"}, {"RefNumber": "396955", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/396955"}, {"RefNumber": "396334", "RefComponent": "MM-PUR-VM", "RefTitle": "Selection according to document date in RMEBEIN3", "RefUrl": "/notes/396334"}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673"}, {"RefNumber": "394619", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/394619"}, {"RefNumber": "394577", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Vendor billing document not created for posting block", "RefUrl": "/notes/394577"}, {"RefNumber": "394082", "RefComponent": "LO-AB", "RefTitle": "Reference fields are not transferred to accounting", "RefUrl": "/notes/394082"}, {"RefNumber": "392778", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deletion report RV130006 terminates with message NAA507", "RefUrl": "/notes/392778"}, {"RefNumber": "392066", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Maximum values cannot be entered per calculation rule", "RefUrl": "/notes/392066"}, {"RefNumber": "392055", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Search help attachment in report RMCE0900", "RefUrl": "/notes/392055"}, {"RefNumber": "391793", "RefComponent": "LO-AB", "RefTitle": "Tax: inconsistent amounts for TxCd E1, N1 ...", "RefUrl": "/notes/391793"}, {"RefNumber": "388716", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "Value dates in the general billing interface", "RefUrl": "/notes/388716"}, {"RefNumber": "388626", "RefComponent": "MM-PUR-VM", "RefTitle": "Variant cannot be saved for reports", "RefUrl": "/notes/388626"}, {"RefNumber": "387206", "RefComponent": "LO-AB", "RefTitle": "Setup of statistical data for agency business incorrect", "RefUrl": "/notes/387206"}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044"}, {"RefNumber": "386460", "RefComponent": "LO-AB", "RefTitle": "Pro forma documents: updating cash management data", "RefUrl": "/notes/386460"}, {"RefNumber": "386157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Shrt dmp CONVT_OVERFLOW or BCD_FIELD_OVERFLOW durng settlmnt", "RefUrl": "/notes/386157"}, {"RefNumber": "385052", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect search help for the 'Rebate arrangement' field", "RefUrl": "/notes/385052"}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006"}, {"RefNumber": "382939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in agreement maintenance debit-side settlmt accounting", "RefUrl": "/notes/382939"}, {"RefNumber": "382829", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect repricing in subsequent settlement", "RefUrl": "/notes/382829"}, {"RefNumber": "382749", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Org. data of an arrangement not copied during creation", "RefUrl": "/notes/382749"}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831"}, {"RefNumber": "371737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Copy additional value days to credit memos", "RefUrl": "/notes/371737"}, {"RefNumber": "371643", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of income data aborts with error GENERATE_SUBP", "RefUrl": "/notes/371643"}, {"RefNumber": "368273", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income not updated for total income 0", "RefUrl": "/notes/368273"}, {"RefNumber": "367511", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "structure MCKONA for LIS filled incompletely", "RefUrl": "/notes/367511"}, {"RefNumber": "367386", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N field settlement group 1 not filled", "RefUrl": "/notes/367386"}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295"}, {"RefNumber": "364754", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect income update frm vendor billing docs. f. invoices", "RefUrl": "/notes/364754"}, {"RefNumber": "364746", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Creatng data recrds not possible in business volume comparsn", "RefUrl": "/notes/364746"}, {"RefNumber": "364725", "RefComponent": "LO-AB-RS", "RefTitle": "Cancelling settlements of arrangements", "RefUrl": "/notes/364725"}, {"RefNumber": "355952", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating simultn causes multiple updating of business volume", "RefUrl": "/notes/355952"}, {"RefNumber": "353614", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Messge MN582 displayed in rebate arrangmnt maintenance twice", "RefUrl": "/notes/353614"}, {"RefNumber": "353546", "RefComponent": "LO-AB-RS", "RefTitle": "Settlement of incorrect incomes via vendor billing documents", "RefUrl": "/notes/353546"}, {"RefNumber": "338522", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error NAA178 with BVC (create data records)", "RefUrl": "/notes/338522"}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231"}, {"RefNumber": "335806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termintn due to internal error saving bus.vol.comp.", "RefUrl": "/notes/335806"}, {"RefNumber": "335654", "RefComponent": "MM-PUR-PO", "RefTitle": "Incorr. val.w/ new price determintn for GR (Pric.date cat.5)", "RefUrl": "/notes/335654"}, {"RefNumber": "335506", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/335506"}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914"}, {"RefNumber": "328465", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/328465"}, {"RefNumber": "328464", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/328464"}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145"}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142"}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921"}, {"RefNumber": "325341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect exception processing of user exit LWBON003", "RefUrl": "/notes/325341"}, {"RefNumber": "324705", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msgs MN781 or MN164 with settlement of rebate arrangement", "RefUrl": "/notes/324705"}, {"RefNumber": "324686", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN145 during rebate arrangement settlement", "RefUrl": "/notes/324686"}, {"RefNumber": "324328", "RefComponent": "LO-AB", "RefTitle": "Error handling durg document creatn via API methods", "RefUrl": "/notes/324328"}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279"}, {"RefNumber": "320728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement partners are not copied", "RefUrl": "/notes/320728"}, {"RefNumber": "319666", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deadlock with subsequent business volume recompilation", "RefUrl": "/notes/319666"}, {"RefNumber": "317485", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect change update for purchase order", "RefUrl": "/notes/317485"}, {"RefNumber": "316290", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance of document index recompilation", "RefUrl": "/notes/316290"}, {"RefNumber": "315769", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of bus.volume, price determtn goods receipt", "RefUrl": "/notes/315769"}, {"RefNumber": "313199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect updating at the time of the purchase order", "RefUrl": "/notes/313199"}, {"RefNumber": "306173", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect specification of period-specific condition", "RefUrl": "/notes/306173"}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806"}, {"RefNumber": "305388", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent business volume update in wrong period", "RefUrl": "/notes/305388"}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047"}, {"RefNumber": "301595", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/repair reports, Release 4.6", "RefUrl": "/notes/301595"}, {"RefNumber": "300959", "RefComponent": "LO-AB", "RefTitle": "Incorrect LIS update for weights, volume, points", "RefUrl": "/notes/300959"}, {"RefNumber": "216962", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Comparison of period-specific condition ignored in settlemnt", "RefUrl": "/notes/216962"}, {"RefNumber": "216051", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN543, update agency business", "RefUrl": "/notes/216051"}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716"}, {"RefNumber": "212330", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rounding taxes in settlement at header level", "RefUrl": "/notes/212330"}, {"RefNumber": "212327", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement simulation conditions on header level incorrect", "RefUrl": "/notes/212327"}, {"RefNumber": "211488", "RefComponent": "MM-PUR-PO", "RefTitle": "ERS for automatically created purchase order", "RefUrl": "/notes/211488"}, {"RefNumber": "207212", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Busins vol.comp:settlement date incorrectly checked", "RefUrl": "/notes/207212"}, {"RefNumber": "207020", "RefComponent": "MM-PUR-VM", "RefTitle": "Creating document index (RMEBEIN3), restart impossible", "RefUrl": "/notes/207020"}, {"RefNumber": "206060", "RefComponent": "MM-PUR-PO", "RefTitle": "Ind. 'Subseq. settlement', deviating vendor data", "RefUrl": "/notes/206060"}, {"RefNumber": "205594", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Update is not cancelled", "RefUrl": "/notes/205594"}, {"RefNumber": "204337", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN514, business volume update goods receipt", "RefUrl": "/notes/204337"}, {"RefNumber": "202378", "RefComponent": "SD-BF-PR", "RefTitle": "Message VK758 or VK757 wh. changing condition value", "RefUrl": "/notes/202378"}, {"RefNumber": "202036", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination TSV_TNEW_PAGE_ALLOC_FAILED when you settle", "RefUrl": "/notes/202036"}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492"}, {"RefNumber": "200859", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Terms of Payment not transferred", "RefUrl": "/notes/200859"}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703"}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188"}, {"RefNumber": "200070", "RefComponent": "LO-AB", "RefTitle": "Info rec for variant/generc matl wrongly determined", "RefUrl": "/notes/200070"}, {"RefNumber": "199346", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Impossible to exit bus.volum comparison from menu", "RefUrl": "/notes/199346"}, {"RefNumber": "197417", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message 06167 when settling an arrangement", "RefUrl": "/notes/197417"}, {"RefNumber": "197007", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Incor decimals in amount of condit.rec (II)", "RefUrl": "/notes/197007"}, {"RefNumber": "196526", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No S111 index entries for subsequent business volume update", "RefUrl": "/notes/196526"}, {"RefNumber": "195730", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN684 in rebate arrangement settlement", "RefUrl": "/notes/195730"}, {"RefNumber": "193837", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Exiting 'Comparison of bus.vol.' w.yellow arrow", "RefUrl": "/notes/193837"}, {"RefNumber": "192924", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Status of incorrect NAST record not changed", "RefUrl": "/notes/192924"}, {"RefNumber": "192598", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Aggregation/sort levels maintaind incorrctly", "RefUrl": "/notes/192598"}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134"}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902"}, {"RefNumber": "189894", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement run number for mass billing arrangements", "RefUrl": "/notes/189894"}, {"RefNumber": "186453", "RefComponent": "LO-AB", "RefTitle": "S111 entries for items missing", "RefUrl": "/notes/186453"}, {"RefNumber": "186247", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorr. inc. distribution with recompilation / vend.bill.doc", "RefUrl": "/notes/186247"}, {"RefNumber": "184916", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income distribution in recompilation incorrect", "RefUrl": "/notes/184916"}, {"RefNumber": "184387", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Debtor w/ incorrct account group creates short dump", "RefUrl": "/notes/184387"}, {"RefNumber": "184340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Euro Workbench: Incorrect input check of arrangements", "RefUrl": "/notes/184340"}, {"RefNumber": "183099", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Exception GROUP_NOT_FOUND in SD_word_processing", "RefUrl": "/notes/183099"}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434"}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864"}, {"RefNumber": "170971", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MEBA: Business volume data deleted during check run", "RefUrl": "/notes/170971"}, {"RefNumber": "169822", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Archiving: Messages NAA534 and NAA535", "RefUrl": "/notes/169822"}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284"}, {"RefNumber": "162897", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect detailed statement", "RefUrl": "/notes/162897"}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580"}, {"RefNumber": "158572", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement and Enjoy purchase order MEPO", "RefUrl": "/notes/158572"}, {"RefNumber": "154601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequnt settlmnt:external data transfr cancellatn", "RefUrl": "/notes/154601"}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694"}, {"RefNumber": "150775", "RefComponent": "SD-BF-TP", "RefTitle": "Exception GROUP_NOT_FOUND in SD_WORD_PROCESSSING", "RefUrl": "/notes/150775"}, {"RefNumber": "136863", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "New DB secondary index AEB f.table EKBO", "RefUrl": "/notes/136863"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "388716", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "Value dates in the general billing interface", "RefUrl": "/notes/388716 "}, {"RefNumber": "367511", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "structure MCKONA for LIS filled incompletely", "RefUrl": "/notes/367511 "}, {"RefNumber": "371643", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of income data aborts with error GENERATE_SUBP", "RefUrl": "/notes/371643 "}, {"RefNumber": "313199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect updating at the time of the purchase order", "RefUrl": "/notes/313199 "}, {"RefNumber": "425076", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Changd businss vol. not takn into acc. for settlmnt accnting", "RefUrl": "/notes/425076 "}, {"RefNumber": "392778", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deletion report RV130006 terminates with message NAA507", "RefUrl": "/notes/392778 "}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006 "}, {"RefNumber": "382829", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect repricing in subsequent settlement", "RefUrl": "/notes/382829 "}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703 "}, {"RefNumber": "197007", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Incor decimals in amount of condit.rec (II)", "RefUrl": "/notes/197007 "}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434 "}, {"RefNumber": "212330", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rounding taxes in settlement at header level", "RefUrl": "/notes/212330 "}, {"RefNumber": "169822", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Archiving: Messages NAA534 and NAA535", "RefUrl": "/notes/169822 "}, {"RefNumber": "216962", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Comparison of period-specific condition ignored in settlemnt", "RefUrl": "/notes/216962 "}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493 "}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284 "}, {"RefNumber": "394577", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Vendor billing document not created for posting block", "RefUrl": "/notes/394577 "}, {"RefNumber": "485130", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Arrangement status for interim settlement set incorrectly", "RefUrl": "/notes/485130 "}, {"RefNumber": "489318", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Verbuchungsabbruch beim Anlegen von Absprachen", "RefUrl": "/notes/489318 "}, {"RefNumber": "486757", "RefComponent": "MM-PUR-GF-MAS", "RefTitle": "MEMASSPO: Settlement field (EBONF) not changeable", "RefUrl": "/notes/486757 "}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040 "}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199 "}, {"RefNumber": "371737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Copy additional value days to credit memos", "RefUrl": "/notes/371737 "}, {"RefNumber": "392066", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Maximum values cannot be entered per calculation rule", "RefUrl": "/notes/392066 "}, {"RefNumber": "317485", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect change update for purchase order", "RefUrl": "/notes/317485 "}, {"RefNumber": "328465", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Änderungsfortschreibung Bestellung ME22N fehlerhaft", "RefUrl": "/notes/328465 "}, {"RefNumber": "490728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Material description is confused in rebate arrangements", "RefUrl": "/notes/490728 "}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365 "}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694 "}, {"RefNumber": "492607", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No message output with cancellation of settlement document", "RefUrl": "/notes/492607 "}, {"RefNumber": "500644", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Business volume comparison and agreement: message MN242", "RefUrl": "/notes/500644 "}, {"RefNumber": "502747", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement via credit memo: Message MN178", "RefUrl": "/notes/502747 "}, {"RefNumber": "301595", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/repair reports, Release 4.6", "RefUrl": "/notes/301595 "}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145 "}, {"RefNumber": "325341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect exception processing of user exit LWBON003", "RefUrl": "/notes/325341 "}, {"RefNumber": "485429", "RefComponent": "LO-AB-RS", "RefTitle": "Subsequent settlement: Message SG105", "RefUrl": "/notes/485429 "}, {"RefNumber": "403734", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN242 upon settlement (different calculation rules)", "RefUrl": "/notes/403734 "}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231 "}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432 "}, {"RefNumber": "458695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Man. correction f. customer BillDoc income w/ partial settl.", "RefUrl": "/notes/458695 "}, {"RefNumber": "315769", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of bus.volume, price determtn goods receipt", "RefUrl": "/notes/315769 "}, {"RefNumber": "202036", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination TSV_TNEW_PAGE_ALLOC_FAILED when you settle", "RefUrl": "/notes/202036 "}, {"RefNumber": "441313", "RefComponent": "LO-MD-RPC", "RefTitle": "Doc index generation: Variable key wrong for specific values", "RefUrl": "/notes/441313 "}, {"RefNumber": "457035", "RefComponent": "LO-AB-RS", "RefTitle": "Incorrect doc. status if document not relevant to accounting", "RefUrl": "/notes/457035 "}, {"RefNumber": "316290", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance of document index recompilation", "RefUrl": "/notes/316290 "}, {"RefNumber": "368273", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income not updated for total income 0", "RefUrl": "/notes/368273 "}, {"RefNumber": "452056", "RefComponent": "LO-AB", "RefTitle": "Error handling when posting agency documents", "RefUrl": "/notes/452056 "}, {"RefNumber": "425033", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem extending arrangements if arrangement contains EURO", "RefUrl": "/notes/425033 "}, {"RefNumber": "480984", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currency conversion, message MN 471", "RefUrl": "/notes/480984 "}, {"RefNumber": "396955", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/396955 "}, {"RefNumber": "382749", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Org. data of an arrangement not copied during creation", "RefUrl": "/notes/382749 "}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142 "}, {"RefNumber": "445331", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currencies can be deleted in the mass maintenance", "RefUrl": "/notes/445331 "}, {"RefNumber": "444449", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Missing data in user exit message determination", "RefUrl": "/notes/444449 "}, {"RefNumber": "399160", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "SM13: Update records in status INIT", "RefUrl": "/notes/399160 "}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324 "}, {"RefNumber": "454172", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "No text determination with billing interface", "RefUrl": "/notes/454172 "}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649 "}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429 "}, {"RefNumber": "364746", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Creatng data recrds not possible in business volume comparsn", "RefUrl": "/notes/364746 "}, {"RefNumber": "353614", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Messge MN582 displayed in rebate arrangmnt maintenance twice", "RefUrl": "/notes/353614 "}, {"RefNumber": "324686", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN145 during rebate arrangement settlement", "RefUrl": "/notes/324686 "}, {"RefNumber": "306173", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect specification of period-specific condition", "RefUrl": "/notes/306173 "}, {"RefNumber": "212327", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement simulation conditions on header level incorrect", "RefUrl": "/notes/212327 "}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492 "}, {"RefNumber": "195730", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN684 in rebate arrangement settlement", "RefUrl": "/notes/195730 "}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902 "}, {"RefNumber": "189894", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement run number for mass billing arrangements", "RefUrl": "/notes/189894 "}, {"RefNumber": "184340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Euro Workbench: Incorrect input check of arrangements", "RefUrl": "/notes/184340 "}, {"RefNumber": "324705", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msgs MN781 or MN164 with settlement of rebate arrangement", "RefUrl": "/notes/324705 "}, {"RefNumber": "319666", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deadlock with subsequent business volume recompilation", "RefUrl": "/notes/319666 "}, {"RefNumber": "205594", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Update is not cancelled", "RefUrl": "/notes/205594 "}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188 "}, {"RefNumber": "186247", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorr. inc. distribution with recompilation / vend.bill.doc", "RefUrl": "/notes/186247 "}, {"RefNumber": "184916", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income distribution in recompilation incorrect", "RefUrl": "/notes/184916 "}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673 "}, {"RefNumber": "355952", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating simultn causes multiple updating of business volume", "RefUrl": "/notes/355952 "}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921 "}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806 "}, {"RefNumber": "305388", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent business volume update in wrong period", "RefUrl": "/notes/305388 "}, {"RefNumber": "216051", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN543, update agency business", "RefUrl": "/notes/216051 "}, {"RefNumber": "204337", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN514, business volume update goods receipt", "RefUrl": "/notes/204337 "}, {"RefNumber": "197417", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message 06167 when settling an arrangement", "RefUrl": "/notes/197417 "}, {"RefNumber": "196526", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No S111 index entries for subsequent business volume update", "RefUrl": "/notes/196526 "}, {"RefNumber": "388626", "RefComponent": "MM-PUR-VM", "RefTitle": "Variant cannot be saved for reports", "RefUrl": "/notes/388626 "}, {"RefNumber": "207020", "RefComponent": "MM-PUR-VM", "RefTitle": "Creating document index (RMEBEIN3), restart impossible", "RefUrl": "/notes/207020 "}, {"RefNumber": "335806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termintn due to internal error saving bus.vol.comp.", "RefUrl": "/notes/335806 "}, {"RefNumber": "338522", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error NAA178 with BVC (create data records)", "RefUrl": "/notes/338522 "}, {"RefNumber": "320728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement partners are not copied", "RefUrl": "/notes/320728 "}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044 "}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914 "}, {"RefNumber": "433752", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error when checking for release to accounting", "RefUrl": "/notes/433752 "}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739 "}, {"RefNumber": "427415", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for proforma documents into LIS", "RefUrl": "/notes/427415 "}, {"RefNumber": "386460", "RefComponent": "LO-AB", "RefTitle": "Pro forma documents: updating cash management data", "RefUrl": "/notes/386460 "}, {"RefNumber": "394082", "RefComponent": "LO-AB", "RefTitle": "Reference fields are not transferred to accounting", "RefUrl": "/notes/394082 "}, {"RefNumber": "364754", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect income update frm vendor billing docs. f. invoices", "RefUrl": "/notes/364754 "}, {"RefNumber": "387206", "RefComponent": "LO-AB", "RefTitle": "Setup of statistical data for agency business incorrect", "RefUrl": "/notes/387206 "}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864 "}, {"RefNumber": "200859", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Terms of Payment not transferred", "RefUrl": "/notes/200859 "}, {"RefNumber": "413786", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Release notes 4.5 for subsequent settlement incomplete", "RefUrl": "/notes/413786 "}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716 "}, {"RefNumber": "391793", "RefComponent": "LO-AB", "RefTitle": "Tax: inconsistent amounts for TxCd E1, N1 ...", "RefUrl": "/notes/391793 "}, {"RefNumber": "396334", "RefComponent": "MM-PUR-VM", "RefTitle": "Selection according to document date in RMEBEIN3", "RefUrl": "/notes/396334 "}, {"RefNumber": "408521", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN495 (update simulation), MN514 (detailed statement)", "RefUrl": "/notes/408521 "}, {"RefNumber": "399118", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msg NAA045 list output for arrangemts (RWMBON02, Trans.MEB5)", "RefUrl": "/notes/399118 "}, {"RefNumber": "335654", "RefComponent": "MM-PUR-PO", "RefTitle": "Incorr. val.w/ new price determintn for GR (Pric.date cat.5)", "RefUrl": "/notes/335654 "}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831 "}, {"RefNumber": "402138", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tolerance during business volume comparison and agreement", "RefUrl": "/notes/402138 "}, {"RefNumber": "382939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in agreement maintenance debit-side settlmt accounting", "RefUrl": "/notes/382939 "}, {"RefNumber": "136863", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "New DB secondary index AEB f.table EKBO", "RefUrl": "/notes/136863 "}, {"RefNumber": "192598", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Aggregation/sort levels maintaind incorrctly", "RefUrl": "/notes/192598 "}, {"RefNumber": "300959", "RefComponent": "LO-AB", "RefTitle": "Incorrect LIS update for weights, volume, points", "RefUrl": "/notes/300959 "}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295 "}, {"RefNumber": "353546", "RefComponent": "LO-AB-RS", "RefTitle": "Settlement of incorrect incomes via vendor billing documents", "RefUrl": "/notes/353546 "}, {"RefNumber": "386157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Shrt dmp CONVT_OVERFLOW or BCD_FIELD_OVERFLOW durng settlmnt", "RefUrl": "/notes/386157 "}, {"RefNumber": "200070", "RefComponent": "LO-AB", "RefTitle": "Info rec for variant/generc matl wrongly determined", "RefUrl": "/notes/200070 "}, {"RefNumber": "324328", "RefComponent": "LO-AB", "RefTitle": "Error handling durg document creatn via API methods", "RefUrl": "/notes/324328 "}, {"RefNumber": "392055", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Search help attachment in report RMCE0900", "RefUrl": "/notes/392055 "}, {"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199 "}, {"RefNumber": "397517", "RefComponent": "LO-AB", "RefTitle": "RMCENEUB: Termination CONVT_NO_NUMBER", "RefUrl": "/notes/397517 "}, {"RefNumber": "394619", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/394619 "}, {"RefNumber": "385052", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect search help for the 'Rebate arrangement' field", "RefUrl": "/notes/385052 "}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580 "}, {"RefNumber": "154601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequnt settlmnt:external data transfr cancellatn", "RefUrl": "/notes/154601 "}, {"RefNumber": "211488", "RefComponent": "MM-PUR-PO", "RefTitle": "ERS for automatically created purchase order", "RefUrl": "/notes/211488 "}, {"RefNumber": "202378", "RefComponent": "SD-BF-PR", "RefTitle": "Message VK758 or VK757 wh. changing condition value", "RefUrl": "/notes/202378 "}, {"RefNumber": "364725", "RefComponent": "LO-AB-RS", "RefTitle": "Cancelling settlements of arrangements", "RefUrl": "/notes/364725 "}, {"RefNumber": "367386", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N field settlement group 1 not filled", "RefUrl": "/notes/367386 "}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047 "}, {"RefNumber": "328464", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Nachträgliche Abrechnung: Warnmeldung MN 589", "RefUrl": "/notes/328464 "}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279 "}, {"RefNumber": "206060", "RefComponent": "MM-PUR-PO", "RefTitle": "Ind. 'Subseq. settlement', deviating vendor data", "RefUrl": "/notes/206060 "}, {"RefNumber": "184387", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Debtor w/ incorrct account group creates short dump", "RefUrl": "/notes/184387 "}, {"RefNumber": "207212", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Busins vol.comp:settlement date incorrectly checked", "RefUrl": "/notes/207212 "}, {"RefNumber": "199346", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Impossible to exit bus.volum comparison from menu", "RefUrl": "/notes/199346 "}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134 "}, {"RefNumber": "192924", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Status of incorrect NAST record not changed", "RefUrl": "/notes/192924 "}, {"RefNumber": "193837", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Exiting 'Comparison of bus.vol.' w.yellow arrow", "RefUrl": "/notes/193837 "}, {"RefNumber": "186453", "RefComponent": "LO-AB", "RefTitle": "S111 entries for items missing", "RefUrl": "/notes/186453 "}, {"RefNumber": "158572", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement and Enjoy purchase order MEPO", "RefUrl": "/notes/158572 "}, {"RefNumber": "162897", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect detailed statement", "RefUrl": "/notes/162897 "}, {"RefNumber": "170971", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MEBA: Business volume data deleted during check run", "RefUrl": "/notes/170971 "}, {"RefNumber": "150775", "RefComponent": "SD-BF-TP", "RefTitle": "Exception GROUP_NOT_FOUND in SD_WORD_PROCESSSING", "RefUrl": "/notes/150775 "}, {"RefNumber": "183099", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Exception GROUP_NOT_FOUND in SD_word_processing", "RefUrl": "/notes/183099 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}