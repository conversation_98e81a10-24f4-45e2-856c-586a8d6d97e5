{"Request": {"Number": "797147", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 329, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015805702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000797147?language=E&token=E5D1D0018B8A56C8C9A3AA0263A827A6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000797147", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000797147/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "797147"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 107}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.01.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-WILY"}, "SAPComponentKeyText": {"_label": "Component", "value": "Introscope by CA Technologies"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Introscope by CA Technologies", "value": "XX-PART-WILY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-WILY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "797147 - Introscope Installation for SAP Customers"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note contains last minute updates on access and the installation procedure of CA APM Introscope. It covers the Enterprise Manager, Agent, Workstation, and WebView installation.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Introscope CA Broadcom APM Application Performance Management Wily</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Licensing and Access</strong></p>\r\n<p><br />CA/Broadcom and SAP have signed an agreement allowing you to use Introscope with SAP-developed dashboards and instrumentation as part of SAP Solution Manager. This specific Introscope version build for SAP is refered to as Right-To-View (RTV) version of Introscope.<br /><br />The Right to View (RTV) version of CA APM Introscope is a restricted, read-only form of the full product and is bundled with SAP Solution Manager. With the RTV version, support is limited to products that are licensed and supported by SAP. The instrumentation, dashboards, Probe Builder Directives (PBDs), management modules, and Smartstor data contained within the RTV version of CA APM Introscope as provided by SAP is the intellectual property of SAP. Use of these functions is restricted by SAP and may only be used in an unrestricted manner by licensing SAP Extended Diagnostics by CA, foundation&#160;from SAP (SAP Note 1280961: SAP Extended Diagnostics by CA Wily).<br /><br />If you would like to increase the benefits of Introscope through custom dashboards and instrumentation, or extend Introscope's capabilities with Introscope PowerpacksTM, contact CA Broadcom.</p>\r\n<p>The formerly available&#160;product SAP Extended Diagnostics by CA Wily is no longer on SAP pricelist. But sou still can get the equivalent package from CA Broadcom directly.</p>\r\n<p><strong>Latest Release</strong></p>\r\n<p>Latest available release on Service Marketplace is Introscope 10.8.</p>\r\n<p>Release notes for Introscope 10.8: SAP Note&#160;<a target=\"_blank\" href=\"/notes/3247270\">3247270</a>.<a target=\"_blank\" href=\"/notes/2909673\"><br /></a></p>\r\n<p><span style=\"text-decoration: underline;\">Note</span>: Please read all release notes referenced in this note before upgrading to the latest release.</p>\r\n<p><strong>Installation</strong></p>\r\n<p>You will need the following files for an Introscope installation. The release notes for a each Introscope version specify the exact version and download location.</p>\r\n<p>1. Documentation</p>\r\n<p style=\"padding-left: 90px;\">b) All information, including the setup guide mentioned in b) and troubleshooting notes, is collected here:&#160;<a target=\"_blank\" href=\"https://support.sap.com/en/alm/solution-manager/expert-portal/introscope-enterprise-manager.html\">https://support.sap.com/en/alm/solution-manager/expert-portal/introscope-enterprise-manager.html</a></p>\r\n<p style=\"padding-left: 90px;\">a) <strong>Setup guide </strong>for SAP Solution Manager customers, version 10.8:&#160;<a target=\"_blank\" href=\"https://support.sap.com/content/dam/support/en_us/library/ssp/alm/sap-solution-manager/expert-portal/sap-introscope-installation-guide_108.pdf\">https://support.sap.com/content/dam/support/en_us/library/ssp/alm/sap-solution-manager/expert-portal/sap-introscope-installation-guide_108.pdf</a>.</p>\r\n<p style=\"padding-left: 90px;\">For the installation steps in detail please refer to this setup guide.</p>\r\n<ol><ol><ol></ol></ol></ol>\r\n<p><span style=\"font-size: 14px;\">2. Files for the central monitoring system:</span></p>\r\n<p style=\"padding-left: 90px;\">a) Introscope Enterprise Manager</p>\r\n<ol><ol></ol></ol>\r\n<p style=\"padding-left: 90px;\">b) Introscope Management Modules to be installed on top of&#160;Introscope Enterprise Manager</p>\r\n<p style=\"padding-left: 90px;\">c) Optional: Introscope Workstation</p>\r\n<p><span style=\"font-size: 14px;\">3. Introscope Agent files for managed systems:</span></p>\r\n<ol></ol><ol></ol>\r\n<p style=\"padding-left: 90px;\">a) ISAGENT_VIA_SM: Introscope agent for SAP Netweaver AS Java for automated installation via Solution Manager.</p>\r\n<p style=\"padding-left: 90px;\">b) ISAGENT_MIN_J5: Introscope agent for Java for manual installation.</p>\r\n<ol><ol><ol><ol><ol></ol></ol><ol></ol><ol><ol></ol></ol></ol></ol></ol>\r\n<p>In case of any problems with CA Introscope not covered in</p>\r\n<p><a target=\"_blank\" href=\"https://support.sap.com/en/alm/solution-manager/expert-portal/introscope-enterprise-manager.html\">https://support.sap.com/en/alm/solution-manager/expert-portal/introscope-enterprise-manager.html</a></p>\r\n<p>open a service message on component XX-PART-WILY.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-DIA (Diagnostics)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031001)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024220)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797147/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "992311", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/992311"}, {"RefNumber": "976054", "RefComponent": "SV-SMG-SER", "RefTitle": "Availability of EWA for Non ABAP components", "RefUrl": "/notes/976054"}, {"RefNumber": "943031", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/943031"}, {"RefNumber": "856909", "RefComponent": "XX-PART-WILY", "RefTitle": "Error after JDK upgrade on system with Wily Introscope", "RefUrl": "/notes/856909"}, {"RefNumber": "826671", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826671"}, {"RefNumber": "792999", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/792999"}, {"RefNumber": "724452", "RefComponent": "BC-JAS", "RefTitle": "Central Note for SAP NetWeaver Java Server 04/2004s", "RefUrl": "/notes/724452"}, {"RefNumber": "2246406", "RefComponent": "XX-PART-WILY", "RefTitle": "Disabling Introscope Java Agent in emergency case", "RefUrl": "/notes/2246406"}, {"RefNumber": "2222342", "RefComponent": "XX-PART-WILY", "RefTitle": "SAP Extended Diagnostics by CA, foundation - Introscope 9.7", "RefUrl": "/notes/2222342"}, {"RefNumber": "1926261", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926261"}, {"RefNumber": "1878193", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878193"}, {"RefNumber": "1868086", "RefComponent": "XX-PART-WILY", "RefTitle": "Solution Manager support for Introscope 9.1.5", "RefUrl": "/notes/1868086"}, {"RefNumber": "1843776", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1843776"}, {"RefNumber": "1806158", "RefComponent": "MFG-PCO", "RefTitle": "Plant Connectivity: Measuring performance with Introscope", "RefUrl": "/notes/1806158"}, {"RefNumber": "1790828", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1790828"}, {"RefNumber": "1751225", "RefComponent": "SV-SMG-DIA-WLY-EMS", "RefTitle": "Introscope Push", "RefUrl": "/notes/1751225"}, {"RefNumber": "1732061", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1732061"}, {"RefNumber": "1707161", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1707161"}, {"RefNumber": "1579474", "RefComponent": "XX-PART-WILY", "RefTitle": "Management Modules for Introscope delivered by SAP", "RefUrl": "/notes/1579474"}, {"RefNumber": "1565954", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 9 Release Notes", "RefUrl": "/notes/1565954"}, {"RefNumber": "1540591", "RefComponent": "SV-SMG-DIA-WLY", "RefTitle": "Wily Introscope Setup for SAP BOE 4.X", "RefUrl": "/notes/1540591"}, {"RefNumber": "1462699", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1462699"}, {"RefNumber": "1455248", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope for Commodity SL Native Java Server Process", "RefUrl": "/notes/1455248"}, {"RefNumber": "1438005", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Agent for Apache Tomcat", "RefUrl": "/notes/1438005"}, {"RefNumber": "1424719", "RefComponent": "SV-SMG-DIA-WLY", "RefTitle": "Wily Introscope Instrumentation for SAP J2EE Dispatcher", "RefUrl": "/notes/1424719"}, {"RefNumber": "1418638", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope Agent for IBM WebSphere Application Server", "RefUrl": "/notes/1418638"}, {"RefNumber": "1404944", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00/7.20 Usage of additional monitoring tools", "RefUrl": "/notes/1404944"}, {"RefNumber": "1388247", "RefComponent": "SV-SMG-DIA", "RefTitle": "RCA: Managed System Setup for SAP Business Objects Explorer", "RefUrl": "/notes/1388247"}, {"RefNumber": "1357901", "RefComponent": "SV-SMG-DIA", "RefTitle": "RCA: Managed System Setup for SAP BusinessObjects Enterprise", "RefUrl": "/notes/1357901"}, {"RefNumber": "1344725", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Agent on CE 7.1 EhP1: Memory Leak", "RefUrl": "/notes/1344725"}, {"RefNumber": "1331962", "RefComponent": "XX-PART-WILY", "RefTitle": "DB2-z/OS:Wily Introscope Agent for IBM J9 Hybrid JVM", "RefUrl": "/notes/1331962"}, {"RefNumber": "1322841", "RefComponent": "XX-PART-WILY", "RefTitle": "IBM i: Running Wily Introscope Enterprise Manager", "RefUrl": "/notes/1322841"}, {"RefNumber": "1306843", "RefComponent": "XX-PART-WILY", "RefTitle": "Required information when opening new Introscope message", "RefUrl": "/notes/1306843"}, {"RefNumber": "1291797", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1291797"}, {"RefNumber": "1280961", "RefComponent": "XX-PART-WILY", "RefTitle": "SAP Extended Diagnostics by CA Wily", "RefUrl": "/notes/1280961"}, {"RefNumber": "1273028", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 8 Release Notes", "RefUrl": "/notes/1273028"}, {"RefNumber": "1231208", "RefComponent": "SCM-APO-OPT", "RefTitle": "SCM Optimizer: Measuring performance with Introscope", "RefUrl": "/notes/1231208"}, {"RefNumber": "1169441", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope trace enabling for Duet End-to-End Trace", "RefUrl": "/notes/1169441"}, {"RefNumber": "1167033", "RefComponent": "SV-SMG-DIA-APP-OSD", "RefTitle": "New MDM Specific Commands for OS Command Console.", "RefUrl": "/notes/1167033"}, {"RefNumber": "1163836", "RefComponent": "BC-JAS", "RefTitle": "Solution Manager Diagnostics required for troubleshooting", "RefUrl": "/notes/1163836"}, {"RefNumber": "1159369", "RefComponent": "SV-SMG-DIA-SRV", "RefTitle": "Solution Manager Diagnostics: Introscope EM not connected", "RefUrl": "/notes/1159369"}, {"RefNumber": "1149214", "RefComponent": "BC-OP-LNX-JSEI", "RefTitle": "Wily Introscope Agent with IBM Java VM 1.4.2 (SR1x) x86_64", "RefUrl": "/notes/1149214"}, {"RefNumber": "1126554", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope .NET Agent", "RefUrl": "/notes/1126554"}, {"RefNumber": "1107279", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Agent: JMX Adapter retains memory", "RefUrl": "/notes/1107279"}, {"RefNumber": "1105109", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Enterprise Manager Troubleshooting", "RefUrl": "/notes/1105109"}, {"RefNumber": "1085464", "RefComponent": "XX-PART-WILY", "RefTitle": "Enable Duet JMS Queue Monitoring with Wily Introscope", "RefUrl": "/notes/1085464"}, {"RefNumber": "1064516", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1064516"}, {"RefNumber": "1015184", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope Agent for IBM J9 Hybrid VM", "RefUrl": "/notes/1015184"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3066886", "RefComponent": "SV-SMG-DIA-WLY-EMS", "RefTitle": "Introscope cannot start with error: Error inserting trace in database java.lang.NullPointerException", "RefUrl": "/notes/3066886 "}, {"RefNumber": "2614684", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope EM does not start due to wrong ELF data format", "RefUrl": "/notes/2614684 "}, {"RefNumber": "2785299", "RefComponent": "XX-PART-WILY", "RefTitle": "Admin user cannot be set as connection user when discovering Enterprise Manager - SAP Solution Manager 7.2", "RefUrl": "/notes/2785299 "}, {"RefNumber": "1831135", "RefComponent": "BI-BIP-DEP", "RefTitle": "Hot deploy of SAP BusinessObjects Business Intelligence Platform Management Modules fails with 'invalid dashboard' error", "RefUrl": "/notes/1831135 "}, {"RefNumber": "2534316", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 10.5 Release Notes for changes and open issues", "RefUrl": "/notes/2534316 "}, {"RefNumber": "2451758", "RefComponent": "XX-PART-WILY", "RefTitle": "SAP Extended Diagnostics by CA, foundation - Introscope 10.5", "RefUrl": "/notes/2451758 "}, {"RefNumber": "2374943", "RefComponent": "MFG-ME", "RefTitle": "Tools for monitoring SAP ME environments", "RefUrl": "/notes/2374943 "}, {"RefNumber": "1579474", "RefComponent": "XX-PART-WILY", "RefTitle": "Management Modules for Introscope delivered by SAP", "RefUrl": "/notes/1579474 "}, {"RefNumber": "2285460", "RefComponent": "XX-PART-WILY", "RefTitle": "SAP Extended Diagnostics by CA, foundation - Introscope 10.1", "RefUrl": "/notes/2285460 "}, {"RefNumber": "2285189", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 10.1 Release Notes for changes and open issues", "RefUrl": "/notes/2285189 "}, {"RefNumber": "1453216", "RefComponent": "SV-SMG-DIA", "RefTitle": "Introscope Agent Installation for SAP Convergent Charging", "RefUrl": "/notes/1453216 "}, {"RefNumber": "2222342", "RefComponent": "XX-PART-WILY", "RefTitle": "SAP Extended Diagnostics by CA, foundation - Introscope 9.7", "RefUrl": "/notes/2222342 "}, {"RefNumber": "2138309", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 9.7 Release Notes for changes and open issues", "RefUrl": "/notes/2138309 "}, {"RefNumber": "2071100", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 9.5 Release Notes", "RefUrl": "/notes/2071100 "}, {"RefNumber": "2034074", "RefComponent": "MFG-PCO", "RefTitle": "SAP Plant Connectivity: Performance measurement using Introscope", "RefUrl": "/notes/2034074 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "1273028", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 8 Release Notes", "RefUrl": "/notes/1273028 "}, {"RefNumber": "1565954", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 9 Release Notes", "RefUrl": "/notes/1565954 "}, {"RefNumber": "1126554", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope .NET Agent", "RefUrl": "/notes/1126554 "}, {"RefNumber": "1322841", "RefComponent": "XX-PART-WILY", "RefTitle": "IBM i: Running Wily Introscope Enterprise Manager", "RefUrl": "/notes/1322841 "}, {"RefNumber": "1404944", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00/7.20 Usage of additional monitoring tools", "RefUrl": "/notes/1404944 "}, {"RefNumber": "1418638", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope Agent for IBM WebSphere Application Server", "RefUrl": "/notes/1418638 "}, {"RefNumber": "1438005", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Agent for Apache Tomcat", "RefUrl": "/notes/1438005 "}, {"RefNumber": "1806158", "RefComponent": "MFG-PCO", "RefTitle": "Plant Connectivity: Measuring performance with Introscope", "RefUrl": "/notes/1806158 "}, {"RefNumber": "856909", "RefComponent": "XX-PART-WILY", "RefTitle": "Error after JDK upgrade on system with Wily Introscope", "RefUrl": "/notes/856909 "}, {"RefNumber": "1751225", "RefComponent": "SV-SMG-DIA-WLY-EMS", "RefTitle": "Introscope Push", "RefUrl": "/notes/1751225 "}, {"RefNumber": "1280961", "RefComponent": "XX-PART-WILY", "RefTitle": "SAP Extended Diagnostics by CA Wily", "RefUrl": "/notes/1280961 "}, {"RefNumber": "1540591", "RefComponent": "SV-SMG-DIA-WLY", "RefTitle": "Wily Introscope Setup for SAP BOE 4.X", "RefUrl": "/notes/1540591 "}, {"RefNumber": "1237887", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope 7.2.3 Release Notes", "RefUrl": "/notes/1237887 "}, {"RefNumber": "1357901", "RefComponent": "SV-SMG-DIA", "RefTitle": "RCA: Managed System Setup for SAP BusinessObjects Enterprise", "RefUrl": "/notes/1357901 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1231208", "RefComponent": "SCM-APO-OPT", "RefTitle": "SCM Optimizer: Measuring performance with Introscope", "RefUrl": "/notes/1231208 "}, {"RefNumber": "1388247", "RefComponent": "SV-SMG-DIA", "RefTitle": "RCA: Managed System Setup for SAP Business Objects Explorer", "RefUrl": "/notes/1388247 "}, {"RefNumber": "976054", "RefComponent": "SV-SMG-SER", "RefTitle": "Availability of EWA for Non ABAP components", "RefUrl": "/notes/976054 "}, {"RefNumber": "1462699", "RefComponent": "XX-PART-WILY", "RefTitle": "CA Wily Introscope for SAP VIM 5.2 by Open Text", "RefUrl": "/notes/1462699 "}, {"RefNumber": "1455248", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope for Commodity SL Native Java Server Process", "RefUrl": "/notes/1455248 "}, {"RefNumber": "1167033", "RefComponent": "SV-SMG-DIA-APP-OSD", "RefTitle": "New MDM Specific Commands for OS Command Console.", "RefUrl": "/notes/1167033 "}, {"RefNumber": "1424719", "RefComponent": "SV-SMG-DIA-WLY", "RefTitle": "Wily Introscope Instrumentation for SAP J2EE Dispatcher", "RefUrl": "/notes/1424719 "}, {"RefNumber": "724452", "RefComponent": "BC-JAS", "RefTitle": "Central Note for SAP NetWeaver Java Server 04/2004s", "RefUrl": "/notes/724452 "}, {"RefNumber": "1344725", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Agent on CE 7.1 EhP1: Memory Leak", "RefUrl": "/notes/1344725 "}, {"RefNumber": "1149214", "RefComponent": "BC-OP-LNX-JSEI", "RefTitle": "Wily Introscope Agent with IBM Java VM 1.4.2 (SR1x) x86_64", "RefUrl": "/notes/1149214 "}, {"RefNumber": "1331962", "RefComponent": "XX-PART-WILY", "RefTitle": "DB2-z/OS:Wily Introscope Agent for IBM J9 Hybrid JVM", "RefUrl": "/notes/1331962 "}, {"RefNumber": "886600", "RefComponent": "SV-SMG-DIA-APP-WA", "RefTitle": "Instrumentation WILY INTROSCOPE for SAP IPC 4.0", "RefUrl": "/notes/886600 "}, {"RefNumber": "1085464", "RefComponent": "XX-PART-WILY", "RefTitle": "Enable Duet JMS Queue Monitoring with Wily Introscope", "RefUrl": "/notes/1085464 "}, {"RefNumber": "1169441", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope trace enabling for Duet End-to-End Trace", "RefUrl": "/notes/1169441 "}, {"RefNumber": "1306843", "RefComponent": "XX-PART-WILY", "RefTitle": "Required information when opening new Introscope message", "RefUrl": "/notes/1306843 "}, {"RefNumber": "1015184", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope Agent for IBM J9 Hybrid VM", "RefUrl": "/notes/1015184 "}, {"RefNumber": "1237886", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope agent 7.2.2 retains memory", "RefUrl": "/notes/1237886 "}, {"RefNumber": "1163836", "RefComponent": "BC-JAS", "RefTitle": "Solution Manager Diagnostics required for troubleshooting", "RefUrl": "/notes/1163836 "}, {"RefNumber": "1159369", "RefComponent": "SV-SMG-DIA-SRV", "RefTitle": "Solution Manager Diagnostics: Introscope EM not connected", "RefUrl": "/notes/1159369 "}, {"RefNumber": "1105109", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Enterprise Manager Troubleshooting", "RefUrl": "/notes/1105109 "}, {"RefNumber": "1107279", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Agent: JMX Adapter retains memory", "RefUrl": "/notes/1107279 "}, {"RefNumber": "1021912", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Agent 7.1: Potential Memory Leak", "RefUrl": "/notes/1021912 "}, {"RefNumber": "792999", "RefComponent": "XX-PART-WILY", "RefTitle": "Wily Introscope Availability and Installation", "RefUrl": "/notes/792999 "}, {"RefNumber": "1107756", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope agent 7.1p5 may trigger explicit GC", "RefUrl": "/notes/1107756 "}, {"RefNumber": "892842", "RefComponent": "BC-INS", "RefTitle": "SAPCAR error: format error in header", "RefUrl": "/notes/892842 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}