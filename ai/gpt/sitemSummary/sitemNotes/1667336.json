{"Request": {"Number": "1667336", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 410, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017363462017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001667336?language=E&token=A640E725A3E74C480AF926B43F3DF7C9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001667336", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001667336/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1667336"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.04.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON"}, "SAPComponentKeyText": {"_label": "Component", "value": "CCMS Monitoring & Alerting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1667336 - CCMS Monitoring with Kernel 7.20 (DCK)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL><OL>a) According to SAP Notes 1629598, 1636252 you have installed the new Downward Compatible SAP Kernel 720 on a system that is either a monitored system or central monitoring system itself. After kernel upgrade you want to ensure that the CCMS monitoring will run in the correct way.</OL></OL> <OL><OL>b) In case of double-stack system you miss the local monitoring segment in RZ20</OL></OL> <OL><OL>c) You are missing DSR statistics of J2EE engine from the upgraded system.</OL></OL> <OL><OL>d) Log files of the CCMS agents (sapccm4x.log, sapccmsr.log, sapstartsrv_ccms.log) contain errors like:<br /><br />ERROR: Can not create CCMS_Tooldispatching: [102]&#x00A0;&#x00A0;TID [&lt;invalid TID&gt;: Monitoring Context] not found in monitoring segment.</OL></OL> <OL><OL>e) Log file of the work processes (dev_w) contain following errors:<br /><br />M&#x00A0;&#x00A0;*** ERROR =&gt; ThJAlReportData: TID [&lt;invalid TID&gt;: Monitoring Context] not found in monitoring segment. [rc=102] [thxxvmcalrt. 84]<br /><br />M&#x00A0;&#x00A0;*** ERROR =&gt; CCMS: SeekHeader: header []: wrong eyecatch: []) [alxxcore.c&#x00A0;&#x00A0; 2035]</OL></OL> <OL><OL>f) Moreover you are noticing that your scheduled batch jobs are not triggered by the batch scheduler. This is because the Control/Staus Area has been damaged by running standalone sapccmsr agent (with option -j2ee) in parallel with the integrated CCMS agent in sapstartsrv.<br /></OL></OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>sapccm4x sapccmsr sapstartsrv 720 DCK kernel upgrade</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Installing the new Downward Compatible SAP Kernel (DCK) 720 on all 7.x systems enabes the system to be remotely monitored using integrated CCMS agents in sapstartsrv instead of standalone CCMS agents (sapccm4x, sapccmsr [-j2ee]).<br />(http://help.sap.com/saphelp_nwpi71/helpdata/en/44/87655bdd0e12d0e10000000a422035/frameset.htm)<br />For a background information regarding redesign of the CCMS monitoring infrastructure in kernel 710, which is relevant also if you upgrade to 720 from releases lower than 710, check the note 1119735.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>When upgrading from 701 and lower kernel release to 720 DCK, please proceed according to the note 1119735 with relocation of content of the CCMS agent's directories. Before upgrading to 720 DCK kernel you have to unregister all standalone CCMS agents (sapccm4x, sapccmsr).<br /><br />Note: Please check note 1746016, there has been provided a fix of eye catch issue in CCMS agents.<br /><br />Note: Please check note 1760861 for info regarding DSR statistics, if the upgraded system is a double stack or CEN system is based on SAP_BASIS 7.00.<br /><br />Then proceed as follows for:<br /><br /><B>Local monitoring in the upgraded double-stack system:</B><br /><br />There is no need to perform any special steps, unless you already encountered any of symptoms above. Then the monitoring segment has been already damaged. Proceed as follows:</p> <OL>1. Stop the double stack instance, whose segment is corrupted.</OL> <OL>2. If still present, unregister all standalone CCMS agents (sapccmsr [-j2ee], sapccm4x):<br />sapccm4x -u pf=&lt;profile the agent starded with&gt;<br />sapccmsr -u pf=&lt;profile the agent starded with&gt; [-j2ee]</OL> <OL>3. If there is sapstartsrv running, that includes the integrated CCMS agent, stop it:<br />sapcontrol -nr &lt;instance number&gt; -function StopService</OL> <OL>4. Delete all AL* files in the CCMS working directory, see DIR_LOGGING parameter in the start profile, usually /usr/sap/&lt;SID&gt;/&lt;instance_name&gt;/log.</OL> <OL>5. In Unix system use cleanipc to remove the monitoring segment of the double stack instance: cleanipc &lt;instance number&gt; remove<br />In WinNT it might be necessary to restart the server.</OL> <OL>6. Start the stopped sapstartsrv services:<br />sapcontrol -nr &lt;instance number&gt; -function StartService &lt;SID&gt;</OL> <OL>7. Start the instance.<br />sapcontrol -nr &lt;instance number&gt; -function Start</OL> <p><br />Note: In step 4 there will be lost a setting of disabling MTE nodes. After restart the MTE nodes must be disabled again. There is currently no other workaround.<br /><br /><B>Central monitoring of the upgraded system from a remote system (CEN):</B><br /><br />The registration is now done centrally from the central system for all instances managed by an sapstartsrv and not via command line from each instance of the monitored system, as it was done before (with standalone sapccm4x or sapccmsr agents). The registration functionality in RZ21 has been delivered with SAP_BASIS 7.0 EhP1.<br /><br />Thus it depends from the SAP_BASIS release of the central system which procedure you have to follow:</p> <OL>1. If your CEN system is based on SAP_BASIS 7.00 (with or without Kernel 7.20), please follow SAP note 1368389 on all monitored systems independent on SAP Kernel release.</OL> <OL>2. If your CEN system is based on SAP_BASIS 7.0 EhP 1 and higher, all monitored systems (with SAP Kernel at least 7.10) are using the CCMS agent integrated in sapstartsrv, that is started default. However the registration on central system needs to be repeated. Please follow the instructions given on SAP Help (http://help.sap.com/saphelp_nwpi71/helpdata/EN/44/893e933dc912d3e10000000a422035/content.htm). This is necessary because of technical requirements - there must be generated logical ports on CEN to be able to communicate via web-service protocol that has been implemented by the integrated CCMS agents.</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I047532)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D056056)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001667336/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667336/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1760861", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR data in double stack with SAP_BASIS 7.00", "RefUrl": "/notes/1760861"}, {"RefNumber": "1746016", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: damaged monitoring segment reports eyecatch error", "RefUrl": "/notes/1746016"}, {"RefNumber": "1649773", "RefComponent": "BC-CCM-CNF-OPM", "RefTitle": "Syslog Q1O (configuration of work processes will be changed)", "RefUrl": "/notes/1649773"}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252"}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598"}, {"RefNumber": "1368389", "RefComponent": "BC-CCM-MON", "RefTitle": "Re-activating RFC-communication for CCMS Agents", "RefUrl": "/notes/1368389"}, {"RefNumber": "1243642", "RefComponent": "BC-CCM-BTC", "RefTitle": "Job scheduler does not run", "RefUrl": "/notes/1243642"}, {"RefNumber": "1119735", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agents: Upgrade of monitored systems from 7.0 to 7.1", "RefUrl": "/notes/1119735"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1368389", "RefComponent": "BC-CCM-MON", "RefTitle": "Re-activating RFC-communication for CCMS Agents", "RefUrl": "/notes/1368389 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1760861", "RefComponent": "BC-CCM-MON", "RefTitle": "Collecting DSR data in double stack with SAP_BASIS 7.00", "RefUrl": "/notes/1760861 "}, {"RefNumber": "1243642", "RefComponent": "BC-CCM-BTC", "RefTitle": "Job scheduler does not run", "RefUrl": "/notes/1243642 "}, {"RefNumber": "1119735", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agents: Upgrade of monitored systems from 7.0 to 7.1", "RefUrl": "/notes/1119735 "}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598 "}, {"RefNumber": "1746016", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: damaged monitoring segment reports eyecatch error", "RefUrl": "/notes/1746016 "}, {"RefNumber": "1649773", "RefComponent": "BC-CCM-CNF-OPM", "RefTitle": "Syslog Q1O (configuration of work processes will be changed)", "RefUrl": "/notes/1649773 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}