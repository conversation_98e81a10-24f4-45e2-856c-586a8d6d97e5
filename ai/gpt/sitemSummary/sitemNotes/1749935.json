{"Request": {"Number": "1749935", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 279, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017481622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001749935?language=E&token=FA1B246CC4F40B9AE2C7A000ED05FE65"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001749935", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001749935/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1749935"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 72}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02-12-2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SYB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite on Adaptive Server Enterprise"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite on Adaptive Server Enterprise", "value": "BC-DB-SYB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SYB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1749935 - SYB: Configuration Guide for SAP ASE 15.7"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to check whether&#160;your database configuration&#160;complies with SAP's requirements and recommendations.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP ASE, Sybase ASE, SAP Adaptive Server Enterprise, DBA Cockpit, DBACOCKPIT, CCMS, Database Monitoring</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The configuration described in this SAP Note is recommended for running SAP NetWeaver application servers on&#160;SAP Adaptive Server Enterprise version 15.7. For other database versions, see SAP Note <a target=\"_blank\" href=\"https://websmp130.sap-ag.de/sap(bD1lbiZjPTAwMQ==)/bc/bsp/spn/sapnotes/index2.htm?numm=1539124\">1539124</a>.</p>\r\n<p>Make sure to implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/2889537\">2889537</a>&#160;to ensure the Note download in DBA Cockpit is working.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The configuration requirements and recommendations specified in this SAP Note can be compared with your&#160;configuration of an SAP ASE database with the DBA Cockpit. This feature is available starting with the SAP_BASIS support package stacks:</p>\r\n<p>7.02 SP17<br />7.30 SP13<br />7.31 SP14<br />7.40 SP09</p>\r\n<p>To run the configuration check, open transaction DBACOCKPIT, switch to the <em>Database</em> tab, and select <em>Configuration</em> -&gt; <em>SAP Configuration Check </em>from the menu.</p>\r\n<p>The configuration requirements and recommendations in this SAP Note adhere to the following syntax:</p>\r\n<ul>\r\n<li>Curly brackets indicate logical groups of parameters.</li>\r\n<li>Lines starting with a \"greater than\" sign ('&gt;') contain a configuration rule (either a recommendation or a requirement).</li>\r\n<li>A rule consists of the name of the parameter, a relational operator, and a value.</li>\r\n<li>At the end of a rule, additional information is given in square brackets:</li>\r\n</ul>\r\n<p>REQ means this is a requirement. SAP will not be able to support customer installations without this setting.<br />REC means this is a recommendation. You can decide not to follow this rule, but for the majority of all installations this recommendation will give you better performance and/or supportability.<br /><br />Requirements and recommendations can refer to&#160;particular operating systems. This is indicated by a colon followed by an operating system identifier. As an example, [REC:AIX] indicates that this is a recommendation for AIX only. The following operating system identifiers are used:</p>\r\n<ul>\r\n<li>WIN</li>\r\n<li>AIX&#160;</li>\r\n<li>HPUX</li>\r\n</ul>\r\n<p>SAP recommends the following settings for SAP ASE 15.7 as the database for SAP NetWeaver application servers.<br /><br /><strong>Database Server Configuration</strong></p>\r\n<p>The <em>Configuration</em> group contains database configuration parameters that can be set using the DBA Cockpit.<br />{ Configuration<br /><br />The parameter <em>functionality group </em>sets several database parameters as a group. This parameter has to be set to 1 to run SAP applications.<br />&gt; enable functionality group = 1 [REQ]<br /><br />The individual parameters of this functionality group are:<br />&gt; enable inline default sharing = 1 [REQ]<br />&gt; select for update = 1 [REQ]<br />&gt; enable permissive unicode = 1 [REQ]<br />&gt; quoted identifier enhancements = 1 [REQ]<br />&gt; streamlined dynamic SQL = 1 [REQ]<br />&gt; suppress js max task message = 1 [REQ]</p>\r\n<p>It is required that you use the threaded kernel mode:<br />&gt; kernel mode = threaded [REQ]<br /><br />The data collection framework used to collect monitoring information for the DBA Cockpit and to schedule recurring DBA tasks needs the SAP ASE job scheduler in order to be enabled.<br />&gt; enable job scheduler = 1 [REQ]<br />&gt; job scheduler interval &lt;= 60 [REQ]<br />&gt; job scheduler tasks &gt;= 4 [REQ]<br />&gt; js job output width &gt;= 1024 [REQ]<br /><br />For the purpose of database monitoring and to ensure supportability of the database, it is necessary that you set the following parameters:<br />&gt; enable monitoring = 1 [REQ]<br />&gt; enable stmt cache monitoring = 1 [REQ]<br />&gt; errorlog pipe active = 1 [REQ]<br />&gt; deadlock pipe active = 1 [REQ]<br />&gt; sql text pipe active = 0 [REC]<br />&gt; plan text pipe active = 0 [REC]<br />&gt; lock timeout pipe active = 1 [REQ]<br />&gt; per object statistics active = 1 [REQ]<br />&gt; statement statistics active = 1 [REQ]<br />&gt; SQL batch capture = 1 [REQ]<br />&gt; wait event timing = 1 [REQ]<br />&gt; object lockwait timing = 1 [REQ]<br />&gt; process wait events =&#160;1 [REC]<br />&gt; enable metrics capture = 0 [REC]<br />&gt; enable cis = 1 [REQ]<br /><br />The statement pipe is not needed for SAP monitoring and it is recommended that you keep it switched off for performance reasons:<br />&gt; statement pipe active = 0 [REC]<br /><br />Pipes store messages used by the Data Collection Framework. It is necessary that you set the size&#160;to at least 500:<br />&gt; errorlog pipe max messages &gt;= 500 [REQ]<br />&gt; deadlock pipe max messages &gt;= 500 [REQ]<br />&gt; statement pipe max messages = 0<br />&gt; sql text pipe max messages = 0 [REC]<br />&gt; plan text pipe max messages = 0 [REC]<br />&gt; lock timeout pipe max messages &gt;= 500 [REQ]<br />&gt; max SQL text monitored &gt;= 2048 [REQ]<br /><br />SAP applications rely on strong password encryption:<br />&gt; FIPS login password encryption = 1[REQ]<br /><br />We recommend forcing the database not to send unencrypted passwords over the network:<br />&gt; net password encryption reqd &gt;= 2 [REC]<br /><br />The compression feature of SAP ASE is always used for SAP applications. Therefore, compression has to be enabled for the database server:<br />&gt; enable compression = 1 [REQ]<br />&gt; compression info pool size &gt;= 32768 [REQ]<br /><br />XML support is needed to run SAP applications:<br />&gt; enable xml = 1 [REQ]<br /><br />For Unicode, the following settings are required:<br />&gt; enable unicode normalization = 0 [REQ]<br />&gt; enable surrogate processing = 0 [REQ]<br />&gt; disable character set conversions = 0 [REQ]<br />&gt; enable unicode conversions = 1 [REQ]<br /><br />Default character set settings:<br />&gt; default character set id = 190 [REQ]<br />&gt; default language id = 0 [REQ]<br /><br />The maximum job output needs to be set to the maximum value allowed by SAP ASE:<br />&gt; maximum job output&#160;&lt;=&#160;16777216 [REQ]<br />&gt; maximum job output&#160;&gt;=&#160;16384 [REQ]<br /><br />A size of at least 512 MB memory is recommended for the procedure cache. Depending on your system's workload and the available memory, this may need to be increased:<br />&gt; procedure cache size &gt;= 262144 [REC]<br /><br />The statement cache needs to have at least 100 MB:<br />&gt; statement cache size &gt;= 51200 [REQ]<br /><br />For running SAP systems on SAP ASE, you are required to set row level locking:<br />&gt; lock scheme = datarows [REQ]<br /><br />This parameter is required for the SAP DDIC:<br />&gt; select on syscomments.text = 1 [REQ]<br /><br />It is recommended that you do not use lock promotion for SAP applications:<br />&gt; row lock promotion HWM = 2147483647 [REC]<br />&gt; row lock promotion LWM &gt;= 2147483646 [REC]<br /><br />The number of locks should be at least 1000000. Depending on your workload, a higher number of locks may be required.<br />&gt; number of locks &gt;= 1000000 [REC]<br />&gt; lock wait period = 1800 [REC]<br />&gt; lock spinlock ratio = 40 [REC]<br /><br />You need to set the lock hash table size according to the number of locks. It is recommended to set the lock hash table size to at least the number of locks in full millions times 8192. The value of this parameter should be a power of two, otherwise ASE will round it up to the next power of two and show a warning message.<br />&gt; lock hashtable size &gt;= @number of locks@&#160;/ 1000000 * 8192 [REC]<br />&gt; lock hashtable size &gt;= 8192 [REQ]<br /><br />The following settings affect memory usage of SAP ASE. The values given are required to allow SAP applications to run on SAP ASE:<br />&gt; number of open objects &gt;= 40000 [REQ]<br />&gt; number of open indexes &gt;= 30000 [REQ]<br />&gt; number of open partitions &gt;= 30000 [REQ]<br />&gt; number of alarms &gt;= 1000 [REQ]<br />&gt; number of aux scan descriptors &gt;= 1024 [REQ]<br />&gt; number of devices &gt;= 200 [REQ]<br />&gt; disk i/o structures &gt;= 4096 [REQ]<br />&gt; kernel resource memory &gt;= 32768 [REQ]<br />&gt; heap memory per user &gt;= 49152 [REQ]<br />&gt; size of unilib cache &gt;= 512000 [REC]<br /><br />The number of user connections needs to be adapted to your system size.&#160;<br />Recommendation for&#160;SAP NetWeaver ABAP: [total number of work processes * 4] <br />Recommendation for SAP NetWeaver Java:&#160;[number of server nodes * 50] <br />In both cases, use a minimum of 200 user connections:<br />&gt; number of user connections &gt;= 200 [REC]<br /><br />CPU grace time has to be set to 1000.<br />&gt; cpu grace time = 1000 [REC]<br /><br />The following settings are required for network communication:<br />&gt; max network packet size = 16384 [REQ]<br />&gt; default network packet size = 16384 [REQ]<br />&gt; additional network memory &gt;= 10485760 [REQ]<br />&gt; send doneinproc tokens =&#160;1 [REQ]<br /><br />For the recovery interval, note that the parameter name is misleading as the interval is based on an estimate of 1000 pages per minute, whereas the real rate is approximately 10x higher. The value given here represents roughly a 5-minute recovery window:<br />&gt; recovery interval in minutes &lt;= 60 [REC]<br /><br />Additional settings that are requirements or recommended for running SAP applications on SAP ASE:<br />&gt; disable varbinary truncation = 1 [REQ]<br />&gt; allocate max shared memory = 1 [REC]<br />&gt; optimizer level = ase_current [REQ]<br />&gt; enable literal autoparam = 0 [REQ]<br />&gt; enable semantic partitioning = 1 [REQ]<br />&gt; permission cache entries &gt;= 128 [REC]<br />&gt; enable java = 0 [REC]<br />&gt; deadlock checking period &gt;= 500 [REC]<br />&gt; read committed with lock = 0 [REQ]<br />&gt; auto query tuning = 0 [REQ]<br />&gt; max query parallel degree = 1 [REQ]<br />&gt; number of histogram steps = 100 [REC]</p>\r\n<p>The optimization goal&#160;must be either \"allrows_mix\" or \"sap_oltp\" for systems that are not SAP BW Systems.<br />optimization goal = allrows_mix [REQ]<br />&gt; optimization goal = sap_oltp [REQ]</p>\r\n<p>The optimization goal&#160;is recommended to be&#160;\"allrows_mix\" or \"sap_oltp\" for SAP BW Systems.<br />optimization goal = allrows_mix [REC:BI]<br />&gt; optimization goal = sap_oltp [REC:BI]<br />&gt; histogram tuning factor &gt;= 20 [REC]<br />&gt; capture compression statistics = 0 [REC]<br />&gt; print deadlock information = 1 [REQ]<br />&gt; min pages for parallel scan &gt;= 60000 [REC]<br />&gt; cpu accounting flush interval &gt;= 5000000 [REC]<br />&gt; i/o accounting flush interval &gt;= 5000000 [REC]<br />&gt; session tempdb log cache size = 32768 [REC]<br />&gt; number of large i/o buffers = 32 [REC]<br />&gt; enable housekeeper GC = 5 [REC]<br />&gt; enable housekeeper GC &gt;=&#160;1 [REQ]<br />&gt; sysstatistics flush interval = 5 [REC]<br />&gt; sysstatistics flush interval &gt;=&#160;1 [REQ]<br />&gt; global cache partition number &gt;= 8 [REC]<br />&gt; number of oam trips = 1 [REC]<br />&gt; number of index trips = 1 [REC]<br />&gt; user log cache spinlock ratio = 5 [REC]<br />&gt; default exp_row_size percent = 3 [REC]<br />&gt; housekeeper free write percent = 10 [REC]<br />&gt; number of pre-allocated extents = 32 [REC]<br />&gt;&#160;SQL Perfmon Integration = 0 [REC:WIN]<br />&gt; number of network tasks =&#160;4 [REC:LNX]<br />&gt; number of network tasks =&#160;4 [REC:SOL]<br />&gt; number of network tasks =&#160;4 [REC:HPUX]<br />&gt; number of network tasks =&#160;4 [REC:AIX]<br />&gt; number of network tasks =&#160;1 [REC:WIN]<br />&gt; max nesting level &gt;= 32 [REQ]<br />&gt; enable logins during recovery = 0 [REC]<br /><br /><br />Stack size needs to be set to the following values, depending on your operating system:<br />&gt; stack size &gt;= 151552 [REQ]<br />&gt; stack size &gt;= 194560 [REQ:AIX]<br />&gt; stack size &gt;= 409600 [REQ:HPUX]</p>\r\n<p>The stack guard size does not need special settings, the ASE default settings can be used.</p>\r\n<p>Set tape retention to at least 1 day, this avoids&#160;risk that dump files get overwritten accidentally when generated dump file names contain a timestring AND daylight saving reverts to standard time.</p>\r\n<p>&gt; tape retention in days &gt;= 1 [REQ]</p>\r\n<p>For systems running on HP-UX, it is necessary that you set the following parameter:<br />&gt; enable hp posix async i/o = 1 [REQ:HPUX]</p>\r\n<p>For systems that have HADR enabled, it is necessary that you set the following parameter:</p>\r\n<p>&gt; replication agent memory size &gt;= 16290 [REQ:REP]<br />}</p>\r\n<p><strong>Data Caches</strong></p>\r\n<p>Depending on the size and the workload, you should consider a larger default data cache, a separate large I/O cache pool, and a separate cache for the database log:<br />{ Data Caches<br />&gt; default data cache &gt;= 409600 [REQ]<br />}</p>\r\n<p><strong>Logging Error Messages</strong></p>\r\n<p>Using the stored procedure 'sp_altermessage', you can enable logging of certain error messages. Call the following command in isql:</p>\r\n<p><em>sp_altermessage &lt;message_number&gt;, 'with_log', 'true'</em></p>\r\n<p>The current setting for a specific message number can be checked using the following SQL command:</p>\r\n<p><em>SELECT CASE WHEN dlevel &amp; 128 = 0 THEN 'false' ELSE 'true' END from master..sysmessages where error = &lt;message_number&gt;</em></p>\r\n<p>{ Messages<br /><br />Message number 701 is raised when the procedure cache size is insufficient to run a request.<br />&gt; 701 = 'true' [REQ]<br /><br />Message number 1105 is raised when it is not possible to allocate additional space to store data or logs:<br />&gt; 1105 = 'true' [REQ]<br /><br />Message number 2901 is raised when the stack limit has been exceeded:<br />&gt; 2901 = 'true' [REQ]<br /><br />Message number 12205 is raised when a lock could not be set within the configured wait time:<br />&gt; 12205 = 'true' [REQ]<br />}</p>\r\n<p><strong>Database Options</strong></p>\r\n<p>Database options are set using the stored procedure <em>sp_dboption</em>. Set the following options&#160;for the databases &lt;SID&gt; and saptools. The current settings can be checked using the stored procedure <em>sp_helpdb</em>.<br />{ DB Options<br />&gt; ddl in tran = 1 [REQ; S:@SID]<br />&gt; ddl in tran = 1 [REQ; S:saptools]<br />&gt; ddl in tran = 1 [REQ; S:tempdb]<br />&gt; ddl in tran = 1 [REQ; S:saptempdb]<br />&gt; allow nulls by default = 1 [REQ; S:@SID]<br />&gt; allow nulls by default = 1 [REQ; S:saptools]<br />&gt; allow nulls by default = 1 [REQ; S:tempdb]<br />&gt; allow nulls by default = 1 [REQ; S:saptempdb]<br />&gt; allow wide dol rows = 1 [REQ; S:@SID]<br />&gt; allow wide dol rows = 1 [REQ; S:saptools]<br />&gt; allow wide dol rows = 1 [REQ; S:tempdb]<br />&gt; allow wide dol rows = 1 [REQ; S:saptempdb]<br />&gt; no free space acctg = 0 [REQ; S:@SID]<br />&gt; no free space acctg =&#160;0 [REQ; S:saptools]<br />&gt; unique auto_identity index = 0 [REQ; S:@SID]<br />&gt; unique auto_identity index = 0 [REQ; S:saptools]<br /><br />Set the following option for database &lt;SID&gt;:<br />&gt; deallocate first text page = 1 [REC; S:@SID]<br /><br />It is recommended that you set the 'abort tran on log full option', but this is not a requirement:<br />&gt; abort tran on log full = 1 [REC; S:@SID]<br /><br />Page compression can be set for all databases. This is strongly recommended for the SAP database:<br />&gt; page compress = 1 [REC; S:@SID]<br /><br /><br />For productive use, set the options for the &lt;SID&gt; database&#160;as follows:<br />&gt; enforce dump tran sequence = 1 [REQ; S:@SID]<br />&gt; full logging for all = 1 [REQ; S:@SID]<br />&gt; select into/bulkcopy/pllsort = 0 [REQ; S:@SID]<br />&gt; select into/bulkcopy/pllsort =&#160;1 [REQ; S:tempdb]<br />&gt; select into/bulkcopy/pllsort =&#160;1 [REQ; S:saptempdb]<br />&gt; trunc log on chkpt = 0 [REQ; S:@SID]<br /><br />Set the following option for database 'sybsystemdb':<br />&gt; trunc log on chkpt = 1 [REC; S:sybsystemdb]<br />}</p>\r\n<p><strong>Table Options</strong></p>\r\n<p>Table options are set&#160;using the stored procedure <em>sp_chgattribute</em>.&#160;To check the current settings of a table,&#160;use stored procedure <em>sp_help</em>.</p>\r\n<p>The following tables are known to be volatile. If&#160;'concurrency_opt_threshold'&#160;is set to&#160;'-1',&#160;the ASE optimizer will always perform an index scan&#160;without consideration of the table's statistics. More information on this can be found in SAP Note 2049506.</p>\r\n<p>{ Table Options<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.ARFCSSTATE]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.ARFCSDATA]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.ARFCRSTATE]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.QREFTID]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.RSBATCHCTRL]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.TRFCQSTATE]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.TRFCQINS]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.TRFCQIN]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.TRFCQOUT]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.TRFCQDATA]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.VBMOD]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.VBHDR]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3.VBDATA]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3DB.BC_MID_CON_TRFC]<br />&gt; concurrency_opt_threshold = -1 [REQ; T:@SID.SAPSR3DB.BC_MID_CON_BGRFC]<br />}</p>\r\n<p><strong><br />Additional settings</strong></p>\r\n<p>Version&#160;15.7.0.021 and higher:</p>\r\n<p>{ Configuration -- Version &gt;= 15.7.0.021<br />&gt; enable spinlock monitoring = 1 [REC]<br />&gt; enable plan sharing = 0 [REQ]<br />&gt; update statistics hashing = off [REQ]<br />&gt; enable concurrent dump tran = 1 [REC]<br />&gt; enable dump history = 1 [REQ]<br />}</p>\r\n<p>{DB Options -- Version &gt;= 15.7.0.021<br />&gt; deferred table allocation = 0 [REQ; S:@SID]<br />&gt; deferred table allocation = 0 [REQ; S:saptools]<br />}</p>\r\n<p>Version&#160;15.7.0.030 and higher:</p>\r\n<p>{ Configuration -- Version &gt;= 15.7.0.030<br />&gt; enable plan sharing = 0 [REQ]<br />&gt; update statistics hashing = partial [REC]<br />}</p>\r\n<p>Version 15.7.0.042 and higher:<br /><br />{ DB Options -- Version &gt;= 15.7.0.042<br />Wrong setting of the following parameter will block you from using your database dumps. See SAP Note <a target=\"_blank\" href=\"https://websmp130.sap-ag.de/sap(bD1lbiZjPTAwMQ==)/bc/bsp/spn/sapnotes/index2.htm?numm=1864348\">1864348</a> for details<br />&gt; deallocate first text page = 0 [REQ; S:@SID]<br />&gt; deallocate first text page = 0 [REQ; S:saptools]<br />&gt; deferred table allocation = 1 [REC; S:@SID]<br />}<br /><br />Version 15.7.0.043 and higher:<strong><br /></strong><br />{ DB Options -- Version &gt;= 15.7.0.043<br />&gt; deallocate first text page = 1 [REQ; S:@SID]<br />}</p>\r\n<p>Version 15.7.0.100 and higher:<strong><br /></strong><br />{ Configuration-- Version &gt;= 15.7.0.100<br />&gt; execution time monitoring = 1 [REQ]<br />&gt; enable concurrent dump tran = 1 [REQ]<br />}<br /><br />{ DB Options -- Version &gt;= 15.7.0.100<br />&gt; allow incremental dumps = 1 [REQ; S:@SID]<br />&gt; allow incremental dumps = 1 [REQ; S:saptools]<br />&gt; deallocate first text page = 1 [REC; S:@SID]<br />}</p>\r\n<p>Version 15.7.0.121 and higher:</p>\r\n<p>{ Configuration -- Version &gt;= 15.7.0.121<br />&gt; enable utility lvl 0 scan wait = 1 [REC]<br />&gt;&#160;enable utility lvl 0 scan wait = 1 [REQ;BI]<br />}</p>\r\n<p>Version 15.7.0.122 and higher:</p>\r\n<p>{ Configuration -- Version &gt;= 15.7.0.122<br />&gt; wait on uncommitted insert = 1 [REQ]<br />}</p>\r\n<p>Version 15.7.0.130 and higher:</p>\r\n<p>{ Configuration -- Version &gt;= 15.7.0.130<br />&gt; enable sticky statistics =&#160;0 [REQ]<br />&gt; cis connect timeout = 30 [REQ]<br />&gt;&#160;enable bulk inserts&#160;= 0 [REQ]<br />}</p>\r\n<p>Version 15.7.0.131 and higher:</p>\r\n<p>{ Configuration -- Version &gt;= 15.7.0.131<br />&gt; enable&#160;async database init&#160;=&#160;1 [REQ]<br />}</p>\r\n<p>Version 15.7.0.133 and higher:<br /><br />{ Configuration -- Version &gt;= 15.7.0.133<br />&gt; expand numeric truncated scale = 1 [REC]<br />}</p>\r\n<p>Version 15.7.0.137 and higher:</p>\r\n<p>{DB&#160;Options&#160;-- Version &gt;= 15.7.0.137<br />&gt; allow db suspect on rollback error = 1 [REQ:S:@SID]<br />}</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-SYB (BW on Adaptive Server Enterprise)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D046095)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I506697)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001749935/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001749935/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2889537", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: DBA Cockpit - Unable to Parse SAP Note to SAP Configuration Check", "RefUrl": "/notes/2889537"}, {"RefNumber": "1924067", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: sp_configure 'sysstatistics flush interval'", "RefUrl": "/notes/1924067"}, {"RefNumber": "1539124", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Database Configuration for SAP applications on SAP ASE", "RefUrl": "/notes/1539124"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3150579", "RefComponent": "BC-DB-SYB", "RefTitle": "SAP system does not start after Kernel Upgrade - SAP ASE for Business Suite", "RefUrl": "/notes/3150579 "}, {"RefNumber": "1804701", "RefComponent": "BC-DB-SYB", "RefTitle": "Dump tran fails with error 4208 - SAP ASE for Business Suite", "RefUrl": "/notes/1804701 "}, {"RefNumber": "1867120", "RefComponent": "BC-DB-SYB", "RefTitle": "How to solve warning message \"Increase the config parameter 'number of open XXX' to avoid descriptor reuse.\" - SAP ASE", "RefUrl": "/notes/1867120 "}, {"RefNumber": "2780687", "RefComponent": "BC-DB-SYB", "RefTitle": "EWA report recommendation: table option not set correctly - SAP ASE", "RefUrl": "/notes/2780687 "}, {"RefNumber": "2383860", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB_IDENTITY_COL does not exist, TABLE_INVALID_INDEX - SAP ASE for Business Suite", "RefUrl": "/notes/2383860 "}, {"RefNumber": "2914499", "RefComponent": "BC-DB-SYB", "RefTitle": "status2 bit in sysdatabases is showing as 65 - SAP ASE", "RefUrl": "/notes/2914499 "}, {"RefNumber": "2178977", "RefComponent": "BC-DB-SYB", "RefTitle": "Job scheduling fails on DBA Planning Calendar - SAP ASE for Business Suite", "RefUrl": "/notes/2178977 "}, {"RefNumber": "2906390", "RefComponent": "BC-DB-SYB", "RefTitle": "Deadlocks (SQL1205) reported on custom tables since migration to SAP ASE", "RefUrl": "/notes/2906390 "}, {"RefNumber": "2889158", "RefComponent": "BC-DB-SYB", "RefTitle": "SUM upgrade - cannot determine value: unexpected result 0 != 1 Error during phase PREP_GENCHECKS/SYB_CHECKS_GENERIC - SAP ASE for BS", "RefUrl": "/notes/2889158 "}, {"RefNumber": "2733961", "RefComponent": "BC-DB-SYB", "RefTitle": "Error SQL1767 Number of variable length columns exceeds limit of 254 for allpage locked tables. - SAP ASE for BS", "RefUrl": "/notes/2733961 "}, {"RefNumber": "2735906", "RefComponent": "BC-DB-SYB", "RefTitle": "Data collector 'Tables' is not working due to a message 233 - SAP ASE For Business Suite", "RefUrl": "/notes/2735906 "}, {"RefNumber": "2632877", "RefComponent": "BC-DB-SYB", "RefTitle": "The Volume to be overwritten on 'devname' has not expired: creation date on this volume is creation_date, expiration date is expiration_date - SAP ASE", "RefUrl": "/notes/2632877 "}, {"RefNumber": "2188214", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "How to analyse errors from a BW job running on SAP ASE", "RefUrl": "/notes/2188214 "}, {"RefNumber": "2052047", "RefComponent": "BC-DB-SYB", "RefTitle": "When run SQL Statements menu in DBACOCKPIT, '500 Connection timed out' message occurred  - SAP ASE for Business Suite", "RefUrl": "/notes/2052047 "}, {"RefNumber": "2134346", "RefComponent": "BC-DB-SYB", "RefTitle": "\"Invalid column length\" message and stacktrace happened - SAP ASE for Business Suite", "RefUrl": "/notes/2134346 "}, {"RefNumber": "2240787", "RefComponent": "BC-DB-SYB", "RefTitle": "XML for table BC_CPT_CORR_DEF could not be read from database - SAP ASE for Business Suite", "RefUrl": "/notes/2240787 "}, {"RefNumber": "2228134", "RefComponent": "BC-DB-SYB", "RefTitle": "Error 4002 \"Login failed\" when trying to connect to database - SAP ASE for Business Suite and SAP BW", "RefUrl": "/notes/2228134 "}, {"RefNumber": "2558414", "RefComponent": "BC-DB-SYB", "RefTitle": "'SAP configuration check' menu shows an incorrect Expected Value of stack size and stack guard size.", "RefUrl": "/notes/2558414 "}, {"RefNumber": "2626395", "RefComponent": "BC-DB-SYB", "RefTitle": "\"SAP Configuration Check\" for parameter 'enable async database init' is RED - SAP ASE for BS", "RefUrl": "/notes/2626395 "}, {"RefNumber": "2588615", "RefComponent": "BC-DB-SYB", "RefTitle": "Short dump with Runtime in Errors EXPORT_TABLE_UPDATE_CONFLICT - SAP ASE for Business Suite", "RefUrl": "/notes/2588615 "}, {"RefNumber": "2183108", "RefComponent": "BC-MID-RFC-QT", "RefTitle": "t/qRFC processing: general performance verifications", "RefUrl": "/notes/2183108 "}, {"RefNumber": "2495370", "RefComponent": "BC-DB-SYB", "RefTitle": "The EarlyWatch Alert is reporting a very high wait time for wait event 54 - SAP ASE for BS", "RefUrl": "/notes/2495370 "}, {"RefNumber": "1933195", "RefComponent": "BC-DB-SYB", "RefTitle": "CREATE INDEX or UPDATE STATISTICS with consumers clause is running slow - SAP ASE for Business Suite", "RefUrl": "/notes/1933195 "}, {"RefNumber": "3039503", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Change of ASE parameter 'update statistics hashing' from Required to Recommended in EarlyWatch Reports", "RefUrl": "/notes/3039503 "}, {"RefNumber": "2664265", "RefComponent": "BC-OP-HPX", "RefTitle": "SYB: Improving performance of Sybase ASE 15.7 and SAP ASE 16.0 on HP-UX with SCHED_NOAGE", "RefUrl": "/notes/2664265 "}, {"RefNumber": "2662578", "RefComponent": "BC-IAM-IDM", "RefTitle": "IDM performance issues on Sybase ASE 15.7 and SAP ASE 16.0", "RefUrl": "/notes/2662578 "}, {"RefNumber": "2494590", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Adaptions for Database Parameter Check for SAP ASE", "RefUrl": "/notes/2494590 "}, {"RefNumber": "1539124", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Database Configuration for SAP applications on SAP ASE", "RefUrl": "/notes/1539124 "}, {"RefNumber": "1924067", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: sp_configure 'sysstatistics flush interval'", "RefUrl": "/notes/1924067 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}