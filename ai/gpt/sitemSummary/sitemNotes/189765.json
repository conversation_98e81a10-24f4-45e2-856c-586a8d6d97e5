{"Request": {"Number": "189765", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 269, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000972582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000189765?language=E&token=01B39ECBC72A0979450CA5065225CF54"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000189765", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000189765/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "189765"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released Internally"}, "ReleasedOn": {"_label": "Released On", "value": "23.07.2000"}, "SAPComponentKey": {"_label": "Component", "value": "CO-PA-ACT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flow of actual values"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Profitability Analysis", "value": "CO-PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flow of actual values", "value": "CO-PA-ACT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PA-ACT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "189765 - Activity allocation: Cost component split in CO-PA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you allocate activities and processes in Profitability Analysis, the component splits for the prices can not be copied to various value fields.<br />Caution: Consultant note !<br />This Note is only released internally. It must not be released for customers. The changes to 4.6A and 4.6B may be installed by a SAP consultant in arrangement with development and/or by development itself. The installation of changes and maintenance requirements created due to the installation to 4.6A and 4.6B will be billed to the customer.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Price splitting</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Cause: function not supported at present</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>only 46A to 46B:</p> <OL>1. Download transport ALRK279258 from a sapserv&#x00A0;&#x00A0;from the folder 'general/R3server/abap/note.0189765' and import it into your system in accordance with Note 13719.</OL> <OL>2. Set the imported objects as being modified, so that they can be adjusted when you import Services Packs. Proceed in accordance with Note 183116 (Point 3).</OL> <OL>3. Now repair function group KCOB using transaction SE37 under utilities -&gt; repair function group, select all objects and then button 'Repair'.</OL> <OL>4. Implement the changes manually according to correction instructions specified here.</OL> <OL>5. Afterwards, generate program RK2Oxxxx, xxxx = customer's operating concern, as described as in the note 181996.</OL> <p><br />All releases (from 4.6A):</p> <OL>6. Run report RKE_ACTIVITY_COST_COMP_CONTROL and activate the transfer of price splitting in Profitability Analysis for your operating concern.</OL> <OL>7. Maintain the assignment of the splitting components to value fields using transaction KEICO. You need to also maintain the CO PA transfer structure, as this is where the quantity fields are found.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CO-PA-SPP (Sales and Profit Planning)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I021058)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000189765/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189765/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "418790", "RefComponent": "CO-PA-ACT", "RefTitle": "ALLOCATION: corr.rep. Cost comp split transfer > CO-PA", "RefUrl": "/notes/418790"}, {"RefNumber": "402531", "RefComponent": "CO-PA-ACT", "RefTitle": "TEMPLATE: long runtime and/or Time_out CPAE/CPPE", "RefUrl": "/notes/402531"}, {"RefNumber": "391023", "RefComponent": "CO-OM-ACT-F", "RefTitle": "Cost component split allocation in plan in CO-PA", "RefUrl": "/notes/391023"}, {"RefNumber": "385961", "RefComponent": "CO-PA-ACT", "RefTitle": "Error with template allocation cost component split", "RefUrl": "/notes/385961"}, {"RefNumber": "183116", "RefComponent": "BC-DWB-CEX", "RefTitle": "Reports for modifications of object lists", "RefUrl": "/notes/183116"}, {"RefNumber": "181996", "RefComponent": "CO-PA-ACT", "RefTitle": "Reposting revenues with profitability segment", "RefUrl": "/notes/181996"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "418790", "RefComponent": "CO-PA-ACT", "RefTitle": "ALLOCATION: corr.rep. Cost comp split transfer > CO-PA", "RefUrl": "/notes/418790 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "571788", "RefComponent": "CO-OM-ACT-F", "RefTitle": "Iterative price: Missing update in CO-PA", "RefUrl": "/notes/571788 "}, {"RefNumber": "391023", "RefComponent": "CO-OM-ACT-F", "RefTitle": "Cost component split allocation in plan in CO-PA", "RefUrl": "/notes/391023 "}, {"RefNumber": "385961", "RefComponent": "CO-PA-ACT", "RefTitle": "Error with template allocation cost component split", "RefUrl": "/notes/385961 "}, {"RefNumber": "402531", "RefComponent": "CO-PA-ACT", "RefTitle": "TEMPLATE: long runtime and/or Time_out CPAE/CPPE", "RefUrl": "/notes/402531 "}, {"RefNumber": "183116", "RefComponent": "BC-DWB-CEX", "RefTitle": "Reports for modifications of object lists", "RefUrl": "/notes/183116 "}, {"RefNumber": "181996", "RefComponent": "CO-PA-ACT", "RefTitle": "Reposting revenues with profitability segment", "RefUrl": "/notes/181996 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000189765/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}