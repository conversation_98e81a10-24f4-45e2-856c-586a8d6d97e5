{"Request": {"Number": "745346", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 418, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004044612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000745346?language=E&token=8165645C520301535FFA2552B71E2460"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000745346", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000745346/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "745346"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.07.2005"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-KR"}, "SAPComponentKeyText": {"_label": "Component", "value": "South Korea"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "South Korea", "value": "XX-CSC-KR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-KR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "745346 - RFIDYYWT - Korea Withholding tax legal Change"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>RFIDYYWT: Korea WHT Legal Change.<br />Withholding tax certificates and file are to be created.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RFIDYYWT, Korea Withholding tax, Legal change, DMEE, Smartforms</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Legal change in the withholding tax certificate and file formats.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The report RFIDYYWT will replace RFQSKR00 from release 46C and upwards.<br />All legal changes will be updated and delivered only in report RFIDYYWT.<br /><br />This note has two attachments:<br />Note745346_47.zip - For releases 47 and above.<br />Note745346.SAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;- For release 46C.<br /><br />Two smartforms (IDWTCERT_KR_01 &amp; IDWTCERT_KR_02) are used for the<br />withholding tax certificates.<br />Two dmee files ( KR_BUSINESSINCOME &amp; KR_OTHERINCOME) are used to create the dmee file.<br />The BADI Implementation for KOREA (IMPKR_IDWTREP) has changed in<br />release 47 and above. The BADI implementation for Korea has been<br />added in release 46C.<br /><br />Users in Release 47 and above should do the following steps to<br />implement the change:<br /><br />1. Download the zip file (Note745346_47.zip) to the local PC.<br />This zip file contains the following:<br />a. Smartform&#x00A0;&#x00A0;IDWTCERT_KR_01.xml<br />b: Smartform&#x00A0;&#x00A0;IDWTCERT_KR_02.xml<br />c: Smartstyle IDWTCERTSTYLE_KR.xml<br />d. DMEE tree&#x00A0;&#x00A0;KR_BUSINESSINCOME.xml<br />e. DMEE tree&#x00A0;&#x00A0;KR_OTHERINCOME.xml<br /><br />2. Upload the smartforms, smartstyle and the DMEE trees in your system. Upload the files as follows:<br />a. Smartforms:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Transaction -&#x00A0;&#x00A0;/nsmartforms.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Under the menu bar \"Utilities\", select \"Upload form\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Enter the name of the smartform and upload it.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Activate the smartforms.<br /><br />b. Smartstyle:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Transaction - /nsmartstyles<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Under the menu bar \"Utilities\", select \"Upload style\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Enter the smartstyle name and upload it.<br /><br />c. DMEE Files:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Transaction - /ndmee<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Under the menu bar \"Format tree\", select \"Upload XML File\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; A dialog box appears.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; In the dialog box, enter Location - P,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; XML File Name - &lt;Path in the local PC where the xml file is stored&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Activate the DMEE files.<br /><br />3. Apply the changes specific to Release 47 in the attached note.<br /><br /><br />Users in Release 46C, please follow the steps described below for<br />applying the changes:<br /><br />Please find attached the SAR file(Note745346.SAR) containing<br />the following:<br />a. DMEE tree&#x00A0;&#x00A0;KR_BUSINESSINCOME<br />b. DMEE tree&#x00A0;&#x00A0;KR_OTHERINCOME<br />c. Smartform&#x00A0;&#x00A0;IDWTCERT_KR_01<br />d. Smartform&#x00A0;&#x00A0;IDWTCERT_KR_02<br />e. smartstyle IDWTCERT_KR_STYLE<br />f. Table&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IDWTADD<br />g. Dataelemnt WT_FREE_DMBTR<br />h. Changed<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Table&#x00A0;&#x00A0;IDWTFIDOC<br /><br />To download and apply the SAR file please refer to notes 13719 and<br />480180.<br /><br />1. Data Elements to be added:<br />a.&#x00A0;&#x00A0;Name - WT_FREE_DMBTR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short Text&#x00A0;&#x00A0;&#x00A0;&#x00A0; : Free amount field for country specific<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Domain&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; :&#x00A0;&#x00A0;&#x00A0;&#x00A0;WERT8<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Field Label&#x00A0;&#x00A0;&#x00A0;&#x00A0;:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Length&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Field Label<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Amount<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Medium&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Amount<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Long&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Country specific amount<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Heading&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Country specific amount<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Development class&#x00A0;&#x00A0;:&#x00A0;&#x00A0;ID-FI-WT<br />Save and activate.<br /><br />2. Structure to be added:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Name - IDWTADD<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short Text :&#x00A0;&#x00A0;Country specific additional info for DME file<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Component&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Component Type<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_DATE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MLDDT_BSET<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_SECCO1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SECCO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_SECCO2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SECCO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_PYDT1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; AUGDT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_PYDT2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; AUGDT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_QBSHH&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; WT_TAX_AMT<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Currency/quantity fields:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; For component KR_WT_QBSHH,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ref. Field&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reference Table<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HWAER&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IDWTFIDOC<br /><br />Save and activate the structure.<br /><br />3. Modify the structure IDWTFIDOC:<br /><br /> Add the following components to the structure IDWTFIDOC:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Component&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Component Type<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WT_XDMBTR1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WT_FREE_DMBTR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WT_XDMBTR2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WT_FREE_DMBTR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_DATE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MLDDT_BSET<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_PYDT1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUGDT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_PYDT2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUGDT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_SECCO1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SECCO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KR_WT_SECCO2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SECCO<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Currency/quantity fields:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; For component KR_WT_QBSHH,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ref. Field&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reference Table<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HWAER&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IDWTFIDOC<br /><br />Save and activate the structure IDWTFIDOC.<br /><br />4. Create an implementation:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Implementation Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; :&#x00A0;&#x00A0;IMPKR_IDWTREP<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Implementation Short text&#x00A0;&#x00A0; :&#x00A0;&#x00A0;BADI implementation for Korea<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Development class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; :&#x00A0;&#x00A0;ID-FI-WT<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Select the checkbox \"Filter Dependent\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Filter Type&#x00A0;&#x00A0;:&#x00A0;&#x00A0; INTCA<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Filter Value :&#x00A0;&#x00A0; KR<br /><br />Save and activate the implementation<br /><br />5. Add the function Module:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function Module&#x00A0;&#x00A0;:&#x00A0;&#x00A0;IDKR_DMEE_VATNO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function group&#x00A0;&#x00A0; :&#x00A0;&#x00A0;KRGE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; :&#x00A0;&#x00A0;Localisation (S. Korea): VAT No.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Package&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;:&#x00A0;&#x00A0;ID-FI-KR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Parameters: (flag pass value for all)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TREE_TYPE&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0; DMEE_TREETYPE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TREE_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0; DMEE_TREEID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_ITEM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_PARAM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_UPARAM<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Export parameters:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;O_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;C_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;N_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P_VALUE<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tables:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TAB<br /><br />&#x00A0;&#x00A0; Save and activate the function module.<br />6. Add the function Module:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function Module&#x00A0;&#x00A0;:&#x00A0;&#x00A0;IDKR_DMEE_IDNO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function group&#x00A0;&#x00A0; :&#x00A0;&#x00A0;KRGE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; :&#x00A0;&#x00A0;Localisation (S. Korea): ID No.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Package&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;:&#x00A0;&#x00A0;ID-FI-KR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Parameters: (flag pass value for all)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TREE_TYPE&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0; DMEE_TREETYPE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TREE_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0; DMEE_TREEID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_ITEM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_PARAM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_UPARAM<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Export parameters:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;O_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;C_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;N_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P_VALUE<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tables:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TAB<br /><br />&#x00A0;&#x00A0; Save and activate the function module.<br /><br />7. Add the function Module:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function Module&#x00A0;&#x00A0;:&#x00A0;&#x00A0;IDKR_DMEE_TAXOFF<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function group&#x00A0;&#x00A0; :&#x00A0;&#x00A0;KRGE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; :&#x00A0;&#x00A0;Localisation (S. Korea): Tax office<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Package&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;:&#x00A0;&#x00A0;ID-FI-KR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Parameters: (flag pass value for all)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TREE_TYPE&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0; DMEE_TREETYPE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TREE_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0; DMEE_TREEID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_ITEM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_PARAM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_UPARAM<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Export parameters:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;O_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;C_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;N_VALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P_VALUE<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tables:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_TAB<br /><br />&#x00A0;&#x00A0; Save and activate the function module.<br /><br />After performing the above steps, Apply the changes specific to<br />release 46C attached in the note.<br /><br />Since a new implementation(IMPKR_IDWTREP) has been added,<br />the associated BADI classes need to be regenerated.<br /><br />After applying the corrections for 46C,<br />Please ensure that the BADI class is regenerated.<br /><br />You can regenerate the BADI class as follows:<br /><br /> 1. Transaction SE18 (Business Add in (BAdI) Definition Maintenance).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Enter the definition name IDWTREP_ADDFUNCINT. Use the menu option<br /> \"Utilities&#x00A0;&#x00A0; &gt; Regeneration\".&#x00A0;&#x00A0;Regenerate the BAdI definition<br /> IDWTREP_ADDFUNCINT.<br /><br /> 2. Transaction SE24 (Class Builder)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Enter the following object types and activate them (using the<br /> activation icon).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;a. CL_EX_IDWTREP_ADDFUNCINT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;b. IF_EX_IDWTREP_ADDFUNCINT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;c. Also activate the following if if you want to use the report for<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the countries below<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Korea:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CL_IM_IMPKR_IDWTREP<br /><br />The following is common to all releases:<br />For using the report, the following customising change needs to be<br />done:<br /><br />a. In transaction SPRO, access the output group customising through<br />&#x00A0;&#x00A0; the menu path&#x00A0;&#x00A0;IMG -&gt; Financial Accounting -&gt; Financial Accounting<br />&#x00A0;&#x00A0; global setting -&gt; withholding tax -&gt; extended withholding tax -&gt;<br />&#x00A0;&#x00A0; Generic withholding tax reporting -&gt; Define output groups.<br /><br />b. Choose process type/output group STD/KR1 and open the settings.<br />&#x00A0;&#x00A0; Under \"PrintOuts\" , \"Printout1\" for \"Smart Form\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Enter IDWTCERT_KR_01<br />&#x00A0;&#x00A0; Under \"DMEE Files\", 'Format tree\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Enter \"KR_BUSINESSINCOME\".<br />&#x00A0;&#x00A0; Similarly for process type/output group STD/KR2,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Enter IDWTCERT_KR_02 for the smartform and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KR_OTHERINCOME for the dmee file.<br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> K (I029154)"}, {"Key": "Processor                                                                                           ", "Value": "Srividya A (I027815)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000745346/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000745346/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Note745346.SAR", "FileSize": "119", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332652004&iv_version=0003&iv_guid=DC6972E6541FA44C92F3720938048BE3"}, {"FileName": "Note745346_47.zip", "FileSize": "62", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332652004&iv_version=0003&iv_guid=2CD209ACEDAB4046A1D39569F969E897"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364"}, {"RefNumber": "931810", "RefComponent": "XX-CSC-KR", "RefTitle": "Changes to Korean Smartform", "RefUrl": "/notes/931810"}, {"RefNumber": "927644", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Korean Legal Change 2006", "RefUrl": "/notes/927644"}, {"RefNumber": "866349", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Venezuela Legal Change - VAT WT certificate Numbering", "RefUrl": "/notes/866349"}, {"RefNumber": "777260", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Field Help for Natural Person Field (LFA1-STKZN) Out of Date", "RefUrl": "/notes/777260"}, {"RefNumber": "774851", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "South Korea: Withholding Tax Documentation", "RefUrl": "/notes/774851"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "927644", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Korean Legal Change 2006", "RefUrl": "/notes/927644 "}, {"RefNumber": "866349", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Venezuela Legal Change - VAT WT certificate Numbering", "RefUrl": "/notes/866349 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "931810", "RefComponent": "XX-CSC-KR", "RefTitle": "Changes to Korean Smartform", "RefUrl": "/notes/931810 "}, {"RefNumber": "774851", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "South Korea: Withholding Tax Documentation", "RefUrl": "/notes/774851 "}, {"RefNumber": "777260", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Field Help for Natural Person Field (LFA1-STKZN) Out of Date", "RefUrl": "/notes/777260 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C50", "URL": "/supportpackage/SAPKH46C50"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47023", "URL": "/supportpackage/SAPKH47023"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50007", "URL": "/supportpackage/SAPKH50007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0000745346/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}