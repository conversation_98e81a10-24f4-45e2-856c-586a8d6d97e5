{"Request": {"Number": "1504810", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 305, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008907362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001504810?language=E&token=08F7658A3D08DAAC8E6B957F3F6C4283"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001504810", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001504810/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1504810"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.11.2010"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1504810 - CWS: Surgeries View Type - Calculate Function"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains modifications concerning surgery planning.<br />The \"Calculate\" function has been revised.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Clinical Work Station, NWP1, Calculate OR Plan, Close Gaps, Surgery System</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br />Before you import this note, you must already have imported note numbers 1474463 and 1509485.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. See attachments</OL> <OL>2. Additionally required manual corrections for Release 6.04:</OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Create new data element:</B></p> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Select 'Data Type' and enter 'N1AUTOCALC_OP'.</LI></UL> <UL><LI>Choose 'Create'.</LI></UL> <UL><LI>Select 'Data Element' as the type and enter the text \"Automatically Execute Calculation\" as the 'Short Description'.</LI></UL> <UL><LI>On the 'Data Type' tab page enter the value XFELD in the 'Domains' field.</LI></UL> <UL><LI>On the 'Field Label' tab page enter the following values (Length and Text):</LI></UL> <UL><UL><LI>Short:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 10&#x00A0;&#x00A0;Calculate</LI></UL></UL> <UL><UL><LI>Medium:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 14&#x00A0;&#x00A0;Calculate</LI></UL></UL> <UL><UL><LI>Long:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 30&#x00A0;&#x00A0;Automatically Calculate</LI></UL></UL> <UL><UL><LI>Heading:&#x00A0;&#x00A0;50&#x00A0;&#x00A0;Automatically Execute Calculation</LI></UL></UL> <UL><LI>Save the data element.</LI></UL> <UL><LI>Select the 'Documentation' pushbutton, enter the following text and then activate the documentation:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use this option to determine whether the calculation of surgeries should automatically be executed following specific actions. The appointments of the surgeries concerned are changed. These actions are:</p> <UL><UL><LI>Change Appointment</LI></UL></UL> <UL><UL><LI>Create Appointment</LI></UL></UL> <UL><UL><LI>Cancel Appointment</LI></UL></UL> <UL><UL><LI>Start Surgery</LI></UL></UL> <UL><UL><LI>Set Time Stamps for OR Entry and OR Exit</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The \"Calculate\" function can also be selected manually in the function toolbar.</p> <UL><LI>Activate the data element.</LI></UL> <p><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Enhance structure by adding a component:</B></p> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Select 'Data Type' and enter RN1WPV011_SELSCREEN.</LI></UL> <UL><LI>Choose 'Change'.</LI></UL> <UL><LI>At the end of the list enter the value AUTOCALC as a new component and the component type N1AUTOCALC_OP.</LI></UL> <UL><LI>Save and activate the structure.</LI></UL> <p><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Create new text element for program:</B></p> <UL><LI>Call transaction SE38.</LI></UL> <UL><LI>Enter RN1WPVIEW011 as the program and select 'Text Elements' under Subobjects.</LI></UL> <UL><LI>Choose 'Change'.</LI></UL> <UL><LI>At the end of the list enter a new text icon with the following values:</LI></UL> <UL><UL><LI>Sym: 049</LI></UL></UL> <UL><UL><LI>Text: Automatically Execute Calculation</LI></UL></UL> <UL><UL><LI>Length: 50</LI></UL></UL> <UL><LI>Save and activate the text elements of the program.</LI></UL> <p></p> <OL>3. Implement the source code corrections.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5001669"}, {"Key": "Processor                                                                                           ", "Value": "C5001995"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001504810/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001504810/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1504810_B_604.zip", "FileSize": "76", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000508972010&iv_version=0003&iv_guid=0DFB1F1927FE964BA3221A996FA7B306"}, {"FileName": "HW1504810_604.zip", "FileSize": "226", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000508972010&iv_version=0003&iv_guid=A1C9F26AD5F6374E8B5B4C01A11887FC"}, {"FileName": "HW1504810_605.zip", "FileSize": "340", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000508972010&iv_version=0003&iv_guid=BB90EB4B51F27049A3B9ABFB2687D1E1"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1509485", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaption: Downporting Process Object", "RefUrl": "/notes/1509485"}, {"RefNumber": "1474463", "RefComponent": "XX-PART-ISHMED", "RefTitle": "RAD: Radiology WS - Corrections and Service Cancellation", "RefUrl": "/notes/1474463"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1474463", "RefComponent": "XX-PART-ISHMED", "RefTitle": "RAD: Radiology WS - Corrections and Service Cancellation", "RefUrl": "/notes/1474463 "}, {"RefNumber": "1509485", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaption: Downporting Process Object", "RefUrl": "/notes/1509485 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60414INISH", "URL": "/supportpackage/SAPK-60414INISH"}, {"SoftwareComponentVersion": "IS-H 605", "SupportPackage": "SAPK-60504INISH", "URL": "/supportpackage/SAPK-60504INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0001504810/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 16, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1279061 ", "URL": "/notes/1279061 ", "Title": "Service Management: 'Non-Covered' Indicator of Service", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1289725 ", "URL": "/notes/1289725 ", "Title": "Surg. System: Surgery View Type: Start Surgery - BAdI ISH_PR", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1328259 ", "URL": "/notes/1328259 ", "Title": "Surgery System: Call Surgery Work Station", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1355766 ", "URL": "/notes/1355766 ", "Title": "Surg. Syst.: Srg. V. Type: St. Srg. - BAdI ISH_PREREG_CHANGE", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1369061 ", "URL": "/notes/1369061 ", "Title": "Surg. System: Surg. Work Station - Intraopera. Service Entry", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1374498 ", "URL": "/notes/1374498 ", "Title": "Surgery System: Start Surgery - Emergency Surgery Created", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1402829 ", "URL": "/notes/1402829 ", "Title": "Surgery System: Surg. View Type - Wrong Appointment Sequence", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1414572 ", "URL": "/notes/1414572 ", "Title": "Surgery Monitor - Cancel Service", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1430207 ", "URL": "/notes/1430207 ", "Title": "Surg. Sys.: Surg. Plan - Recalc. after Apptmt/Surg. Cancel.", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1445878 ", "URL": "/notes/1445878 ", "Title": "Surgery System: Surgeries View Type - \"Calculate\" Function", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1460464 ", "URL": "/notes/1460464 ", "Title": "Surgery Times: Work Station-Related Time Entry not Possible", "Component": "XX-PART-ISHMED-DOC"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1484376 ", "URL": "/notes/1484376 ", "Title": "Clin. Order: Change/Save - Performance not Optimum", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1488342 ", "URL": "/notes/1488342 ", "Title": "Surgery Times: Incpnsistencies After EmR Input Cancellation", "Component": "XX-PART-ISHMED-DOC"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1493160 ", "URL": "/notes/1493160 ", "Title": "Surg. Sys.: Surg. View Type - New OR Plan after \"Cancel Apt\"", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1507365 ", "URL": "/notes/1507365 ", "Title": "Planning: Update Resource Data Is Incorrect", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1509485 ", "URL": "/notes/1509485 ", "Title": "Technical Adaption: Downporting Process Object", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1445878 ", "URL": "/notes/1445878 ", "Title": "Surgery System: Surgeries View Type - \"Calculate\" Function", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1460464 ", "URL": "/notes/1460464 ", "Title": "Surgery Times: Work Station-Related Time Entry not Possible", "Component": "XX-PART-ISHMED-DOC"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1488342 ", "URL": "/notes/1488342 ", "Title": "Surgery Times: Incpnsistencies After EmR Input Cancellation", "Component": "XX-PART-ISHMED-DOC"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1493160 ", "URL": "/notes/1493160 ", "Title": "Surg. Sys.: Surg. View Type - New OR Plan after \"Cancel Apt\"", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1507365 ", "URL": "/notes/1507365 ", "Title": "Planning: Update Resource Data Is Incorrect", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1509485 ", "URL": "/notes/1509485 ", "Title": "Technical Adaption: Downporting Process Object", "Component": "XX-PART-ISHMED"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}