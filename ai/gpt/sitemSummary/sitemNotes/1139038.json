{"Request": {"Number": "1139038", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 450, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006853832017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001139038?language=E&token=C9FBFC62F7AE39E0FD859036D23E3DB8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001139038", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001139038/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1139038"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.09.2010"}, "SAPComponentKey": {"_label": "Component", "value": "CA-DSG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Digital Signature"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Digital Signature", "value": "CA-DSG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-DSG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1139038 - Signature tool: Missing log display in SAP_ABA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are using the class-based signature tool to execute digital signatures for your applications. When you perform the signature process, the system records the relevant entries in the application log.<br />However, there is no standard application for selectively choosing and displaying these log entries.<br />This makes it more difficult to trace the time schedule of the signature process.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Digital signature, signature tool, log display, application log, DSAL, CJ, DSIGAL, DS_LOG_DISPLAY<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The standard SAP system assigns a log display of the application log to signature processes as transaction DSAL of the SAP_APPL software layer. Your system does not contain any SAP_APPL software layers.<br />The signature tool itself is part of the SAP_ABA software layer.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Release NetWeaver 7.00:<br />Import the Support Package that is relevant for this note.&#x00A0;&#x00A0;If, <B>contrary to the recommendations of SAP</B>, you want to implement the corrections contained in this note, carry out the manual pre-implementation and post-implementation steps specified in this note.<br /><br />Releases higher than NetWeaver 7.00:<br />Then implement the corrections from Note 1438530.<br /><br /><B><U>Valid for all releases</U></B>:<br /><br /><br />The implementation guide provides further information about including the signature tool. The relevant PDF file is attached to Note 700495.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026022)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D032278)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001139038/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001139038/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "700495", "RefComponent": "CA-DSG", "RefTitle": "Implementation of digital signature with help of signature tool", "RefUrl": "/notes/700495"}, {"RefNumber": "1742572", "RefComponent": "CA-DSG", "RefTitle": "BADI_DS_DSAL_FUNC: Implementation does not run", "RefUrl": "/notes/1742572"}, {"RefNumber": "1438530", "RefComponent": "CA-DSG", "RefTitle": "Dump in transaction DSLOG when dispaying comments", "RefUrl": "/notes/1438530"}, {"RefNumber": "1438527", "RefComponent": "CA-DSG", "RefTitle": "Dump in transaction DSAL when displaying comments", "RefUrl": "/notes/1438527"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1742572", "RefComponent": "CA-DSG", "RefTitle": "BADI_DS_DSAL_FUNC: Implementation does not run", "RefUrl": "/notes/1742572 "}, {"RefNumber": "700495", "RefComponent": "CA-DSG", "RefTitle": "Implementation of digital signature with help of signature tool", "RefUrl": "/notes/700495 "}, {"RefNumber": "1438527", "RefComponent": "CA-DSG", "RefTitle": "Dump in transaction DSAL when displaying comments", "RefUrl": "/notes/1438527 "}, {"RefNumber": "1438530", "RefComponent": "CA-DSG", "RefTitle": "Dump in transaction DSLOG when dispaying comments", "RefUrl": "/notes/1438530 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "710", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70023", "URL": "/supportpackage/SAPKA70023"}, {"SoftwareComponentVersion": "SAP_ABA 710", "SupportPackage": "SAPKA71006", "URL": "/supportpackage/SAPKA71006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 1, "URL": "/corrins/0001139038/44"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_ABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKA70022&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Call transaction SE80 for the package DS.<br/><br/>Maintain the following text symbols in the main program SAPLDSAL of the function group DSAL:<br/><br/>Symbol: COM  Text:&nbsp;&nbsp;Comment for Individual Signature mLen:&nbsp;&nbsp;40<br/>Symbol: DTS  Text:&nbsp;&nbsp;Date/Time/Signatory  mLen:&nbsp;&nbsp;60<br/>Symbol: OIL  Text:&nbsp;&nbsp;Object-Independent Logs mLen:&nbsp;&nbsp;50<br/>Symbol: SFS  Text:&nbsp;&nbsp;Selection for Signature Object: mLen:&nbsp;&nbsp;40<br/>Symbol: SIM  Text:&nbsp;&nbsp;Signature Method    mLen: 20<br/>Symbol: SIS  Text:&nbsp;&nbsp;Status of Signature Process  mLen:&nbsp;&nbsp;40<br/><br/>Save and activate your entries.<br/><br/>Call transaction SE93. Create the new report transaction DSLOG:<br/><br/>Transaction Code :  DSLOG<br/>Package:   DS<br/>Transaction text:  Signature Tool:&nbsp;&nbsp;Log Display<br/>Program:    RDSAL_DISPLAY_LOGS<br/>Selection screen:   1000<br/><br/>'Transaction classification' section:<br/><br/>Select the 'Professional User Transaction' option.<br/><br/>'GUI support' section:<br/><br/>Activate the checkboxes for 'SAP GUI for HTML' and 'SAP GUI for Windows'.<br/><br/>Save and activate your entries.<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_ABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKA70022&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Call transaction SE80 for the package DS. Make the following changes.<br/><br/><B><U>Exception class CX_DS_EXCEPTION</U></B>:<br/><br/>Create the new exception ID NO_SIGNID.<br/>Text: No signature ID was transferred for the signature process<br/><br/><B><U>Function group DSAL</U></B>:<br/><br/>Create the new function group DSAL.<br/>Text: Signature Tool:&nbsp;&nbsp;Log Function<br/><br/>Maintain the following GUI status within the function group DSAL:<br/><br/>Status 1110:<br/>Status type:&nbsp;&nbsp;Dialog status<br/>Description: Logs for Digital Signature (New)<br/><br/>'Function Keys' section:<br/><br/>Function Key: F2<br/>Function Code: DROPDOWN<br/>Function Text: Selection Criteria...<br/>Icon Name:  ICON_SELECTION<br/>Info. Text:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Selection Criteria for Signature Object (New)<br/>Fastpath: S<br/><br/>Function Key: F8<br/>Function Code:  SELE<br/>Function Text: Execute<br/>Icon Name:  ICON_EXECUTE_OBJECT<br/>Info. Text:  Execute Logging<br/>Fastpath:  E<br/><br/>'Standard Toolbar' section:<br/><br/>Maintain the relevant function codes for the navigation keys:<br/><br/>Function Code: BACK<br/>Function type: E (Exit command)<br/>Function Text: Back<br/>Fastpath: B<br/><br/>Function Code: ENDE<br/>Function type: E (Exit command)<br/>Function Text: Exit<br/>Fastpath: X<br/><br/>Function Code: CANC<br/>Function type: E (Exit command)<br/>Function Text: Cancel<br/>Icon Name:  ICON_CANCEL<br/>Fastpath: A<br/><br/>'Application Toolbar' section:<br/><br/>Assign the following function codes:<br/>Item 1: Function Code DROPDOWN<br/>Item 2: Function Code SELE<br/><br/>'Menu Bar' section:<br/><br/>'Program' menu:<br/>Code: SELE  Text:&nbsp;&nbsp;Execute<br/>Code: ENDE  Text: Exit<br/><br/>'Edit' menu:<br/><br/>Code: DROPDOWN Text:&nbsp;&nbsp;Selection Criteria...<br/>Code: CANC  Text:&nbsp;&nbsp;Cancel<br/><br/>'Goto' menu:<br/>Code: BACK  Text:&nbsp;&nbsp;Back<br/><br/>Status 2000:<br/>Description: Comment Display for Individual Signature<br/>Status type:&nbsp;&nbsp;Dialog window<br/><br/>'Function Keys' section:<br/><br/>Function Key: F12<br/>Function Code: EESC<br/>Function Text: Cancel<br/>Icon Name:  ICON_CANCEL<br/>Fastpath: A<br/><br/>Function Key: ENTER<br/>Function Code:  ENTE<br/>Function Text: Continue<br/>Icon Name:  ICON_OKAY<br/><br/>'Application Toolbar' section:<br/><br/>Assign the following function codes:<br/>Item 1: Function Code ENTE<br/>Item 2: Function Code EESC<br/><br/>Maintain the following GUI title in the function group DSAL:<br/><br/>Title number:&nbsp;&nbsp;1110 Title: Log Display (Digital Signature)<br/>Title number:&nbsp;&nbsp;2000&nbsp;&nbsp;Title:&nbsp;&nbsp;Comment for Digital Signature<br/><br/>Enhancement spot ES_DS_DSAL_DISPLAY_LOGS:<br/><br/>Create the enhancement spot ES_DS_DSAL_DISPLAY_LOGS as an enhancement (BAdI).<br/>Description: Log Display:&nbsp;&nbsp;BAdI for integrating customer functions<br/><br/>Maintain the following BAdI definition:<br/>BAdI: BADI_DS_DSAL_FUNC<br/>Description: Log Display:&nbsp;&nbsp;BAdI for Integration of Customer Functions<br/>Interface: IF_EX_DS_DSAL_FUNC<br/><br/>'Usability' section:<br/>Deselect all of the checkboxes.<br/><br/>'Instance Creation Mode' section:<br/>Select the 'Reusing Instantiation' option.<br/><br/>Save and activate your changes.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "1139038 ", "URL": "/notes/1139038 ", "Title": "Signature tool: Missing log display in SAP_ABA", "Component": "CA-DSG"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1742572", "RefTitle": "BADI_DS_DSAL_FUNC: Implementation does not run", "RefUrl": "/notes/0001742572"}]}}}}}