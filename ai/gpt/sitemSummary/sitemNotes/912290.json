{"Request": {"Number": "912290", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 365, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016038552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000912290?language=E&token=0FAE8F3C5BE03E4E4CABBFA42092083D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000912290", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000912290/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "912290"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.10.2007"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "912290 - Switzerland: Scope of localization RE-FX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note concerns the scope of localization of the new real estate solution RE-FX for Switzerland. This is the end of maintenance for local legal requirements in Classic RE for Switzerland.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, localization, rent adjustment according to Swiss law, fuel consumption calculation, individual heating expenses settlement with data medium exchange, leases in with ISR reference, various forms and correspondence, end of maintenance for Classic RE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are considering using new SAP Real Estate Management (Flexible Real Estate) in Switzerland. You want information about the scope of localization for Switzerland. You want to know when maintenance ends for Classic RE for Switzerland.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following functions are now available for Switzerland in RE-FX with SAP ECC Enterprise Extension Financials 6.0 (EA-FIN 600):<br /><br />Rent Adjustment Based On Swiss Law<br />With the rent adjustment based on Swiss law, you adjust net rent conditions based on the following changes:<br /><br />Relative factors<br />o Change to the mortgage rate<br />A change to the rent based on a mortgage rate change is calculated on the basis of the legal change record of the difference between the mortgage rate level that underlies the current rent and the mortgage rate level that is to be implemented. The mortgage rate corresponds to the mortgage rates of the Kantonalbank in the canton in which the business entity is located.<br /><br />o General operating and maintenance costs<br />The system makes the calculation using only the flat rates applied in practice for each year on the grounds of cost increases. These flat rates correspond to local percentage rates that refer to one year in each case. These percentage rates are entered centrally in the system and are assigned to the relevant adjustment rule.<br /><br />o General price increase (Purchasing power protection of the risk-bearing equity)<br />The system first determines the country index of the consumer prices at the time of the last rent increase, then the standard index on which the new increase is based. The resulting difference in points is converted to percent and the result of this calculation is multiplied by 40%. The value 40% is defined as an arithmetical value in Customizing and is adjustable in accordance with the legal changes.<br /><br />Absolute factors<br />In accordance with BGE 108 II 137 E. 1a, the market rent (prevailing rent) is generally qualified as an absolute rent determination method that cannot be accessed by the ROI check. Consideration of a rent adjustment because break-even gross ROI has been reached is also accepted as an absolute method. However, the absolute method is mostly applied without restrictions during the revenue check in accordance with Art. 269 OR (Net ROI).<br /><br />o Insufficient net ROI/gross ROI<br />The revenue expressed in percent (= ROI) is the relationship between the investment and the income that it generates. For real estate, there is a distinction between the gross revenue (relationship between asset costs and rent income) and the net revenue (relationship between owner financing and income after deduction of all expenses). The values calculated based on these formulas outside of the system are maintained as conditions in the rental objects with the relevant condition purpose.<br /><br />o Prevailing rent not achieved<br />This value is also defined in the context of a condition for the rental object. The calculation of any possible adjustment based on the prevailing rent takes place analogously to the calculation for the reserve.<br /><br />Fuel consumption calculation<br />In Switzerland, the fuel costs are settled according to consumption and not according to purchase with regard to the tenants. A special fuel consumption calculation according to the FIFO valuation procedure has been implemented so that you can settle the fuel costs outside of materials management (MM).<br /><br />Individual heating expenses settlement<br />The data medium exchange has been adjusted to the country specifications in the context of the individual heating expenses settlement.&#x00A0;&#x00A0;In Switzerland, the owner or their administration carries out the settlement (not the settlement company). Therefore, a separate data carrier has been implemented for Switzerland with measurement documents as its basis.<br /><br />Tenant rental agreement with ISR reference<br />Using this function, you can insert the ISR reference of the invoice (submitted by the customer) in the payment document from the periodic posting.<br />For this purpose, there is an additional function available to assign the reference numbers from ISR to the relevant document from the tenant rental agreement.<br /><br />Correspondence<br />In Switzerland, correspondence is specified as mandatory by the tenancy law. For the localization, the legally required documents have been implemented in the standard system. The following correspondence cases are now available for Switzerland:<br />o Rent change form<br />o Rent change form for graduated rent<br />o Rent change form for original rent<br />o Notice form by landlord<br />o Heating expenses settlement form/service charge settlement form<br />o Sales-based rent settlement form<br />o Invoice printout with ISR<br />o Different reminder procedures<br /></p> <b>End of maintenance for Classic RE Switzerland<br />The Swiss solution of Classic RE (all Switzerland-specific adjustments, including the above-mentioned correspondence topics) will be adjusted to meet the legal and local requirements regardless of the ERP releases until the end of 2009 only.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Technical maintenance takes place as described in Note 443311.</b><br /> <p>WW</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I020835)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I020835)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000912290/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000912290/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098"}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311"}, {"RefNumber": "1122309", "RefComponent": "RE-FX", "RefTitle": "Localization: missing table entries for loc. countries", "RefUrl": "/notes/1122309"}, {"RefNumber": "1033734", "RefComponent": "RE-FX-LC-CH", "RefTitle": "Ctry specification CH: BAdI impl for rounding to 5 centimes", "RefUrl": "/notes/1033734"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098 "}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311 "}, {"RefNumber": "1122309", "RefComponent": "RE-FX", "RefTitle": "Localization: missing table entries for loc. countries", "RefUrl": "/notes/1122309 "}, {"RefNumber": "1033734", "RefComponent": "RE-FX-LC-CH", "RefTitle": "Ctry specification CH: BAdI impl for rounding to 5 centimes", "RefUrl": "/notes/1033734 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}