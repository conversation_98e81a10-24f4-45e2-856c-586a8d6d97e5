{"Request": {"Number": "1125108", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 506, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019109722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001125108?language=E&token=9C9C7C5D57D0D24E0BB85C919E27B6FF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001125108", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001125108/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1125108"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.06.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorizations"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorizations", "value": "BW-BEX-OT-OLAP-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1125108 - Use of obsolete 3.x authorizations in BW 7.x"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You use the obsolete concept of the 3.x reporting authorizations in an SAP BW 7.x system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>It is technically possible to use the old concept of the BW 3.5 reporting authorizations in Release BW 7.0. However, you should only do this to facilitate the upgrade from 3.x to 7.0 and to correct a schedule. This enables you to carry out the technical upgrade in a first project phase. You can change the authorizations in a second project phase once any possible difficulties with the upgrade have been overcome and the BW 7.0 system is stable.<br /><br />After you change to the new 7.0 authorization concept, the advantages of the new concept are available. The following are examples: Authorization by navigation attributes, direct maintenance of hierarchy authorizations, improved performance when generating authorizations, full compatibility with the new planning functions, monitoring function, restriction of authorizations by time<br />For more information, see Note 923176.<br /><br />The change should take place as soon as possible after the upgrade, at the latest when structural changes to the authorization scenario are required.<br /><strong>Important: </strong>If you use the old 3.5 authorization concept in a BW 7.0 system, SAP does not accept <strong>any responsibility</strong> if errors occur after you make a change.<br />Such problems are solved with the provision of the new authorization concept.<br /><br />You can find information regarding the <strong>necessity</strong> of the change to the new 7.0 authorization concept in the following places:</p>\r\n<ol>1. SAP Note 923176</ol><ol>2. BW 7.0 online documentation in the section \"Data Warehouse Management -&gt; Authorizations -&gt; Analysis Authorizations: support will no longer be provided for the old concept.\"</ol><ol>3. NetWeaver Release Information: \"Analysis authorizations for BW Reporting (New)\".</ol><ol>4. The dialog box that is displayed each time you call obsolete transaction RSSM.</ol>\r\n<p><br />As of SAP BW 7.3, the old BW 3.5 authorization concept is technically no longer available.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>As soon as you have completed the technical upgrade successfully, the authorization concept should also be updated to the status 7.0.<br />SAP also provides a consulting package here:<br /><br /><strong>BI authorization migration service</strong><br />Is a complete package for planning and executing the BI authorization migration and includes</p>\r\n<ul>\r\n<li>Detailed planning of the migration including system analysis, concept adjustment, and project planning</li>\r\n</ul>\r\n<ul>\r\n<li>Migration of the old BW reporting authorizations to the new BW 7.0 analysis authorizations</li>\r\n</ul>\r\n<ul>\r\n<li>Test and GoLive support</li>\r\n</ul>\r\n<p><br />For more detailed information, contact the SAP Business Technology Factory at <span style=\"text-decoration: underline;\"><EMAIL></span>.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D040365)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001125108/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125108/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "923176", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Support situation authorization management BI70/NW2004s", "RefUrl": "/notes/923176"}, {"RefNumber": "61726", "RefComponent": "BC-SEC-USR", "RefTitle": "RSSM: No profile assignment if Central User Administration", "RefUrl": "/notes/61726"}, {"RefNumber": "2478384", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Reporting Authorizations", "RefUrl": "/notes/2478384"}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}, {"RefNumber": "1240660", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error generating authorizations: Inconsistency", "RefUrl": "/notes/1240660"}, {"RefNumber": "1157604", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 813 -only allowed to display the hierarchy to level xx", "RefUrl": "/notes/1157604"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2478384", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Reporting Authorizations", "RefUrl": "/notes/2478384 "}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}, {"RefNumber": "923176", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Support situation authorization management BI70/NW2004s", "RefUrl": "/notes/923176 "}, {"RefNumber": "1240660", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error generating authorizations: Inconsistency", "RefUrl": "/notes/1240660 "}, {"RefNumber": "61726", "RefComponent": "BC-SEC-USR", "RefTitle": "RSSM: No profile assignment if Central User Administration", "RefUrl": "/notes/61726 "}, {"RefNumber": "1157604", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 813 -only allowed to display the hierarchy to level xx", "RefUrl": "/notes/1157604 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}