{"Request": {"Number": "1694205", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 429, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017404332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001694205?language=E&token=725C9CD7D7B00D8A17B51305F9AAA4AD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001694205", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001694205/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1694205"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.10.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BW-PLA-IP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Integrated Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning", "value": "BW-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integrated Planning", "value": "BW-PLA-IP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1694205 - BW-IP: Preliminary clarification of Planning Application Kit (PAK) message"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>When you use the Planning Application Kit (PAK), a question or problem occurs. This SAP Note describes how you can proceed further to the analysis and which further information sources can be used. Furthermore, it tells you which information needs to be compiled to enable an analysis of the problem.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>PAK</p>\r\n<p>Internal PE error, 2048</p>\r\n<p>IF_RSPLS_CR_CONTROLLER~CHECK_HDB-01-</p>\r\n<p>Planning Application Kit</p>\r\n<p>Problem analysis</p>\r\n<p>Performance</p>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note provides information about how to get started with troubleshooting a problem in the Planning Application Kit (PAK) environment. It summarizes certain PAK characteristics and provides information about how to proceed with troubleshooting. However, it does not contain separate corrections for problems and is not a collective correction note. This SAP Note will be updated if there are enhancements or improvements with regard to the analysis of problems.</p>\r\n<p>You want to use the Planning Application Kit (PAK) for a planning application. This SAP Note provides approaches on how to contain a problem for conceivable scenarios. You may need to make settings. These settings are specified in the relevant places. If this is not sufficient to solve the problem, you should gather the specified trace files and attach them to a customer message along with a description of the system and of the scenario (see Section 4).</p>\r\n<p>The following topics are handled:</p>\r\n<ol>\r\n<li>Brief background information about how the PAK works</li>\r\n<li>Information about using and activating the PAK</li>\r\n<li>Information about scenarios where the PAK has been activated but one of these problems occurs:</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>A planning application terminates.</li>\r\n<li>The runtime of a planning application does not meet expectations.</li>\r\n<li>The result of a planning application appears to be incorrect.</li>\r\n</ol>\r\n<li>Required information for further problem analysis</li>\r\n</ol>\r\n<p>Appendix</p>\r\n<ol style=\"list-style-type: lower-roman;\">\r\n<li>Profiles for problem analysis</li>\r\n<li>Traces in SAP HANA</li>\r\n<li>Setting parameters in SAP HANA</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li>Brief background information about how the PAK works<br /><br />Regardless of whether the PAK is used, the saved data of InfoCubes and DataSource objects (DSOs) forms the starting point for the generation of new data records and the changing of existing data records of these data providers in integrated planning applications. Data generated in this way and initially only kept in the user context of the planner is taken into account for each data provider for subsequent calculations in the application in addition to the persisted data until it is either discarded by the planner again or saved in the relevant persistence and thus visible to other users, too. Calculations here might not only be planning functions; they might also be the calculations to be carried out during disaggregation in input-ready queries, for example. The integrated planning runtime environment ensures that the buffer data kept for each data provider in the user context complies with the consistency conditions that enable the saving of the data at any time. This applies both for modeled consistency conditions (characteristic relationships, data slices) and for generic consistency conditions (presence of master data and entries in master data ID (SID) tables, time consistency, and so on). <br /><br />The use of the PAK enables the use of the huge possibilities offered by the efficient data processing even of large datasets in SAP HANA by storing the not yet persisted data in buffers in the SAP HANA database, combining it with the persisted data, and carrying out planning operations using algorithms directly in SAP HANA. Furthermore, in this processing model, the data transport between the database and application servers is significantly reduced in some cases. Among other things, this is because the data for the display of results for volume-intensive calculations is displayed in greatly compressed form, and only this data is transported to the application server to be further processed there in Online Analytical Processing (OLAP). When you save, the buffers are saved in the relevant persistences.<br /><br />If the PAK is active, the integrated planning runtime decides for each data provider whether to create the buffers in the SAP HANA database or - as is the case for classic integrated planning - on the application server. For calculations, a decision must also be made as to whether the calculation takes place on the application server or in SAP HANA, which might give rise to the necessity for data transport between the application server and the database. This means that for applications in which only some calculations take place in SAP HANA, there might even be increased data transport volumes in individual cases.&#x00A0;<br /><br />If the function to be executed (planning function or query disaggregation) is implemented both in SAP HANA and in ABAP, the persistence of the data provider buffers decides where the function is executed. In the case of MultiProvider scenarios, just one buffer with persistence in ABAP results in the execution of the entire function in ABAP.<br /><br />In accordance with SAP Note 1637199, you can use user settings or system settings to cause classic BW-IP to be used as the processing model.<br /><br /></li>\r\n<li>Information about using and activating the PAK<br /><br />SAP Note 1637148 describes the technical prerequisites for the use of the PAK. SAP Note 1637199 tells you which prerequisites must be met for a certain planning scenario in order to benefit as much as possible from the use of the PAK. <em>Report RSPLS_PLANNING_ON_HDB_ANALYSIS</em> checks whether the SAP HANA scenario can run in as optimized a manner as possible. This can also be executed on a classic database.<br /><br /></li>\r\n<li>The PAK has been activated but a problem occurs:<br /><br />Comment: The keywords that are <span style=\"text-decoration: underline;\"><em>in italics and underlined</em></span> are explained in the appendix; texts <span style=\"text-decoration: underline;\"><strong>in bold and underlined</strong> </span>are information blocks explained in Section 4.<br /><br />Note the following: If settings are made (either for the ABAP user or in SAP HANA), these must be reset following the analysis.<br /><br />The analysis of a complex planning application can be significantly speeded up if it is simplified to the greatest extent possible. Knowledge on how the specific application works can be extremely helpful here, since processing can be rewarding far earlier if customer knowledge enables the application to be reduced to the simplest scenario that exhibits the problem.<br />Wherever possible, the problem should be isolated to a query with selections and navigation steps or to a planning sequence or to a /nrstt trace, since these can be analyzed without a further UI connection.<br /><strong>Procedure:</strong> Simplify the planning application to the greatest extent possible.<br /><br />Before carrying out a detailed analysis of a PAK problem, you should always see whether the problem also occurs in classic BW-IP if the data volume can be processed there. <br /><strong>Procedure:</strong> Switch to classic BW-IP (by setting the /nsu3 parameter RSPLS_HDB_SUPPORT to HDB_OFF), log on with the user again, and execute the application.<br />If the problem also occurs in classic BW-IP, it is due to a modeling problem, among other things. This SAP Note does not handle this kind of problem; the only exception to this is the case of performance problems.<br /><br /><br />It is then possible to distinguish between the following cases for the simplified application:</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li><strong>A planning application terminates.</strong><br />Various types of termination can occur:<br />- Error message in ABAP (T100)<br />- Dump in /nst22<br />- Index server crash dump in SAP HANA<br />- Out-of-memory termination in SAP HANA<br /><strong>Procedure:<br /></strong>If there are no SAP Notes for the keywords in the ABAP error message or ABAP dump, it might be the case that entries are written to the index server <em>trace&#x00A0;</em>(see <span style=\"text-decoration: underline;\"><em>Trace files</em></span>) at about the same time. Keywords that appear there can be used to search for SAP Notes. If an index server crash dump (see <span style=\"text-decoration: underline;\"><em>Trace files</em></span>) is generated, the corresponding crash dump file contains a section starting with CRASH_STACK. The text then contains sequentially numbered entries whose text can also be used to search for SAP Notes, if necessary. SAP Note <a target=\"_blank\" href=\"/notes/2375039\" title=\"2375039  - BW-PAK: Check result handler (help for troubleshooting)\">2375039</a>&#x00A0;should also be checked for check errors on consistent master data.&#x00A0;<br /><br />If the search for SAP Notes is unsuccessful and there is no error in the customer-specific code, proceed as follows:<br />Open an incident with the information from Section 4.<strong><br /></strong></li>\r\n<li><strong>The performance of a planning application does not meet expectations.</strong><br /><br />Regardless of whether the PAK is being used, the general observations on BW-IP planning applications as stated in SAP Note 1056259 and 1083175, for example, apply to a major extent.<br />In accordance with the comments above, the use of the PAK enables a significant performance advantage in the case of larger data volumes if the functions are primarily executed in SAP HANA. First, check whether the PAK is even used for the scenario.<br /><strong>Procedure:</strong> Activate the planning engine trace (that is, set /nsu3 parameter RSPLS_HDB_PE_TRACE to X), restart the application, then call /nse16 and display RSR_PE_LOG for the user in question.&#x00A0;If the ratio between the runtime sum displayed there and the total runtime is small, significant potions of the runtime are spent in ABAP. <br /><br />If the portion in SAP HANA is small, the application probably does not run completely in SAP HANA. <br /><strong>Procedure:</strong> <br />- Implement SAP Notes 1802463,&#x00A0;1895235, 1927308, 1927309, and 1931179 on static analysis (RSPLS_PLANNING_ON_HDB_ANALYSIS).<br />- Allow the static check to run and ensure in the application that the non-SAP-HANA parts are not too large.<br />- In /nsu3, set RS_DEBUGLEVEL to 2 and start the application. Pay attention to messages about the execution and data volumes.<br /><br />If volume-intensive parts still run in ABAP, the model must be checked.<br /><br />If the portion in SAP HANA is significant and the runtime there is high, you must carry out an SAP HANA runtime analysis:<br /><strong>Procedure:</strong> <br />If the runtimes for the stated data volume seem high, open a customer message with the specifications from Section 4.</li>\r\n<li><strong>The result of a planning application appears to be incorrect.</strong><br /><br />- If virtual master data/InfoProviders are used: Have you made sure that no null values are delivered? <br />If null values are delivered by a used view at present, this must be corrected, since null values are not allowed.<br /><br />- Are the deviations from the expected result explainable as rounding differences?<br />Small rounding deviations can occur through the use of different runtimes in the PAK/classic BW-IP.<br /><br />- If there is a distribution problem: Is this about the distribution of the remainder?<br />In the case of the distribution of the remainder, deviations between the classic BW-IP runtime and the PAK can occur. In the PAK, the distribution of the remainder is largely modeled on the classic BW-IP runtime; however, deviations can occur. In particular, a disaggregation in the query in classic BW-IP can have a more homogeneous effect than the execution in SAP HANA if either only remainders are present or if remainders are a significant part of the data to be distributed. This is not an error.<br /><br />If these points do not provide an explanation for the unexpected result, open a customer message with the specifications from Section 4.<br /><br /></li>\r\n</ol>\r\n<li>Compilation of required information for the problem analysis<br /><strong><br />Generally required information:<br /></strong>- Has the problem appeared recently/since the import of an ABAP Support Package/implementation of an SAP Note or an SAP HANA revision?<br />- Does the problem occur only for a specific user or on a specific node of a distributed SAP HANA landscape?<br />- <strong><span style=\"text-decoration: underline;\">System information</span></strong> (see below)<br />- <strong><span style=\"text-decoration: underline;\">Scenario description</span></strong>&#x00A0;(see below)<br />- Information about <strong><span style=\"text-decoration: underline;\">system access</span></strong>&#x00A0;(see below)<br />- How can the problem be reproduced (/nrstt, /nrsrt, /nrsplan)?<br />- With which user can the problem be reproduced? If you work with user-specific variable values (for example, in accordance with authorizations or in exits), these settings must be given to the support user.<br /><br /><strong>Additional information in the case of a termination:<br /></strong>- What is the symptom and which error message is sent?<br />- Check whether the problem also occurs with the <em><span style=\"text-decoration: underline;\">ABAP analysis profile</span></em>. If so, which error message is sent in this case?<br />-&#x00A0;Delete the Framework for Object Explosion (FOX) templates using the report RSPLS_DELETE_TEMPLATE_SESSIONS in transaction SE38 (/nse38).<br />- Activate the <span style=\"text-decoration: underline;\"><em>ABAP analysis profile</em> </span>for the user.<br />- Activate the <span style=\"text-decoration: underline;\"><em>SAP HANA analysis profile</em>.</span><br />- Execute the application: Start query navigation or the planning sequence or create an /nrstt trace.<br />- Deactivate the analysis profile in ABAP or SAP HANA.<br />- Play back the /nrstt trace with the user with which the trace was created: Can the error be reproduced?<br />- Play back the /nrstt trace with the support user: Can the error be reproduced? <br />- Note down the user and the start/end time stamps for the execution.<br />- Collect the SAP HANA traces in accordance with SAP Note 1732157.<br /><br /><strong><strong>Additional information if </strong>performance does not meet expectations:<br />-</strong>&#xFEFF; Do the runtimes fluctuate?<br />- Are other applications running in parallel with the planning application?<br />- Is virtualization in use?<br />- In the case of a distributed landscape, are the runtime problems on specific nodes only?<br />- /nstad and /nst05 data records can be useful here.<br />- What setting is used for the SAP HANA parameter num_parallel_fox?<br />- Are special SAP HANA parameters that might influence performance set (for example, the trace level)?<br />- Activate the <span style=\"text-decoration: underline;\"><em>ABAP performance profile</em> </span>for the user.<br />- Activate the <span style=\"text-decoration: underline;\"><em>SAP HANA performance profile.</em></span><br />- Execute the application: Start query navigation or the planning sequence or create an /nrstt trace.<br />- If necessary, observe the threads (tab performance, tab threads, refresh) in SAP HANA during the execution and note any anomalies.<br />- Deactivate the performance profile in ABAP or SAP HANA.<br />- Play back the /nrstt trace with the support user: Does the trace run through?<br />- Note down the user and the start/end time stamps for the execution.<br />- Collect the SAP HANA traces in accordance with SAP Note 1732157.<br /><br /><strong><strong><strong>Additional information if </strong></strong>the result does not meet expectations:</strong><br />- Which result does not meet expectations?<br />- What result do you get in the classic BW-IP runtime (/nsu3 parameter RSPLS_HDB_SUPPORT HDB_OFF)?<br />- Check whether the problem also occurs with the <em><span style=\"text-decoration: underline;\">ABAP analysis profile</span></em>.<br />-&#x00A0;Delete the Framework for Object Explosion (FOX) templates using the report RSPLS_DELETE_TEMPLATE_SESSIONS in transaction SE38 (/nse38).<br />- Activate the <span style=\"text-decoration: underline;\"><em>ABAP analysis profile</em> </span>for the user.<br />- Activate the <span style=\"text-decoration: underline;\"><em>SAP HANA analysis profile</em>.</span><br />- Execute the application: Start query navigation or the planning sequence or create an /nrstt trace.<br />- Deactivate the analysis profile in ABAP or SAP HANA.<br />- Play back the /nrstt trace with the user with which the trace was created: Can the error be reproduced?<br />- Play back the /nrstt trace with the support user: Can the error be reproduced? <br />- Note down the user and the start/end time stamps for the execution.<br />- Collect the SAP HANA traces in accordance with SAP Note 1732157 or 1894616.<br /><br /><span style=\"text-decoration: underline;\"><br /><strong>Scenario description</strong></span>:<br />Which modeling objects are used?<br />- InfoCubes/DataStore objects and so on that can/cannot be planned<br />- Aggregations levels for basis providers and/or MultiProviders or MultiProviders with aggregation levels<br />- Characteristic relationships incl. type, in particular exit/SQLScript<br />- Data slices incl. type, in particular exit<br />- Planning functions incl. planning function type, in particular exit/SQLScript<br />- Disaggregation in query<br />- Used UI (Analysis Office, Web...)<br />- Data volumes in application (existing data, processed data)<br />- Use of virtual characteristics/virtual (writable) InfoCubes?<br />- Other special features (special key figure types, such as average/max/min key figures, inverse formulas, cell locking, use of logging BAdI, and so on)<br /><br /><span style=\"text-decoration: underline;\"><strong>System information</strong></span>:<br />- ABAP Support Package<br />- Implemented SAP Notes<br />- SAP HANA revision (see the \"Release\" field in the database data block in \"System -&gt; Status\")<br />- Number of nodes in the SAP HANA installation (in a distributed system), displayed in /ndbacockpit or in the HANA Studio, for example<br /><br /><strong><span style=\"text-decoration: underline;\">System access</span></strong>:<br />- ABAP user with which the problem can be reproduced<br />- SAP HANA system access in accordance with SAP Note 1635304<br />- SAP HANA contact person for changing parameter in SAP HANA for analysis purposes, if necessary<br />- There are the following possibilities for alternative access to SAP HANA:<br />  /ndbacockpit: Restricted usability; frequent authorization problems for SQL statements<br /> . Windows Terminal Server (WTS) access; start the HANA Studio from there. Comment: Among other restrictions, only one user can start the HANA Studio on the WTS.<br /><br /></li>\r\n</ol>\r\n<p>Appendix:<br /><br /></p>\r\n<ol style=\"list-style-type: lower-roman;\">\r\n<li>Settings for the analysis:<br /><em><span style=\"text-decoration: underline;\">ABAP analysis profile</span></em>:<br />- /nsu3<br /> RS_DEBUGLEVEL 5<br /> RSPLS_HDB_SUPPORT HDB_ON<br /> RSPLS_HDB_CHECK HDB_IMMEDIATE<br /> RSPLS_HDB_PE_TRACE Y<br /><br /><span style=\"text-decoration: underline;\"><em>SAP HANA analysis profile </em></span>(do not forget to reset these settings, since some of them apply to all users)<br />Set the relevant trace parameters in indexserver.ini (display all components) as follows:<br /><ul>\r\n<li>HDB Studio in the following section: \"Trace Configuration -&gt; Database Trace\"</li>\r\n<li>In the SAP GUI transaction DBACOCKPIT: \"Diagnostic -&gt; Database Trace\"</li>\r\n<li>Alternatively, use SQL or the SQL Console (\"editor\") in the HDB Studio or transaction DBACOCKPIT.</li>\r\n</ul><br />&#x00A0;<br /><span style=\"text-decoration: underline;\"><em>ABAP performance profile</em></span>:<br />- /nsu3&#x00A0;&#x00A0;<br /> RS_DEBUGLEVEL 1<br /> RSPLS_HDB_SUPPORT HDB_ON<br /> RSPLS_HDB_CHECK HDB_DEFERRED<br /> RSPLS_HDB_PE_TRACE: X<br /><br /><span style=\"text-decoration: underline;\"><em>SAP HANA performance profile</em></span>:<br /><p>indexserver-&gt;calcengine:<br /> show_intermediate_results=1<br /> python trace=on<br />Activate the SAP HANA components <span style=\"text-decoration: underline;\"><em>PerfTrace and Profiler</em>.</span><br />Activate the performance trace (in the HANA Studio, see SAP Note 1787489).</p>\r\n</li>\r\n<li>Traces in SAP HANA<br />Information required in SAP HANA database traces for troubleshooting:</li>\r\n<ul>\r\n<li>Trace files: You can display these in HANA Studio/HDB Admin or in DBSCOCKPIT by means of diagnosis files and can download them locally.<br /><p>- What trace files are there?</p>\r\n<p>- indexserver*.trc<br />In the case of hard errors, there are even indexserver*crashdump*.trc and indexserver_alert*.trc trace files. If so, look at these files, since they contain extra, thread-specific information.<br /><br /><br />- *profiler*.trc</p>\r\n</li>\r\n<li>- In the case of distributed systems, there are trace files for each index server. In the SAP HANA studio, you can view these together and in sequence using merge diagnosis files. Furthermore, you can execute the scenario on a dedicated host using the parameter RSPLS_HDB_PE_HOST as of SAP Note 2040907.<br /><br /></li>\r\n</ul>\r\n<li>Setting parameters in SAP HANA<br /><br />Note: Parameter changes apply to all users; do not make any changes without agreeing with the customer, first.<br />Important: Following an analysis, you must reset the parameters to their previous values.<br /><br />The \"Admin\" view of the HANA Studio has a \"Configuration\" tab. Unless otherwise specified, the parameters mentioned in this SAP Note belong to the file \"indexserver.ini\" (subsections \"planningengine\", \"calcengine\", and \"global\").<br /><br />Once you find a parameter in a subsection, you can double-click it to maintain the value (if you have the required authorization). If the parameter is not present, you can choose \"Add Parameter\" from the context menu and enter the parameter together with its value.</li>\r\n</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "HAN-DB-ENG-PLE (SAP HANA Planning Engine)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022158)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029075)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001694205/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001694205/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1894616", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1894616"}, {"RefNumber": "1732157", "RefComponent": "HAN-DB", "RefTitle": "Collecting diagnosis information for SAP HANA [VIDEO]", "RefUrl": "/notes/1732157"}, {"RefNumber": "2375039", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-PAK: Check result handler (help for troubleshooting)", "RefUrl": "/notes/2375039"}, {"RefNumber": "2040907", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP (PAK) support: Planning on selected servers", "RefUrl": "/notes/2040907"}, {"RefNumber": "1973951", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1973951"}, {"RefNumber": "1931179", "RefComponent": "BW-PLA-IP", "RefTitle": "Further improvements in SWITCH class and PAK check report", "RefUrl": "/notes/1931179"}, {"RefNumber": "1927978", "RefComponent": "BW-PLA-IP", "RefTitle": "Condition 'getCalcScenario()->saveScenario()' failed", "RefUrl": "/notes/1927978"}, {"RefNumber": "1927309", "RefComponent": "BW-PLA-IP", "RefTitle": "Tracing for reason why PAK switch does not seem to work", "RefUrl": "/notes/1927309"}, {"RefNumber": "1927308", "RefComponent": "BW-PLA-IP", "RefTitle": "Enable PAK Analysis report for pretest on classic DB", "RefUrl": "/notes/1927308"}, {"RefNumber": "1895235", "RefComponent": "BW-PLA", "RefTitle": "Additional information in RSPLS_PLANNING_ON_HDB_ANALYSIS", "RefUrl": "/notes/1895235"}, {"RefNumber": "1824516", "RefComponent": "BW-BEX-OT", "RefTitle": "Missing analysis report for planning func.: SAP HANA - Mem", "RefUrl": "/notes/1824516"}, {"RefNumber": "1822059", "RefComponent": "BW-PLA-IP", "RefTitle": "Simplification of debugging of CL_RSR_STORE", "RefUrl": "/notes/1822059"}, {"RefNumber": "1802463", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Information and statistics runtime", "RefUrl": "/notes/1802463"}, {"RefNumber": "1787489", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database: Performance Trace", "RefUrl": "/notes/1787489"}, {"RefNumber": "1750231", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1750231"}, {"RefNumber": "1745094", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Support for Planning Engine: required authorizations", "RefUrl": "/notes/1745094"}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199"}, {"RefNumber": "1637148", "RefComponent": "BW-PLA-IP", "RefTitle": "BW on HANA: Activation of Planning Application Kit", "RefUrl": "/notes/1637148"}, {"RefNumber": "1635304", "RefComponent": "HAN", "RefTitle": "Central note for HANA support connections", "RefUrl": "/notes/1635304"}, {"RefNumber": "1633212", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP performance: In-memory planning for BW on SAP HANA (2)", "RefUrl": "/notes/1633212"}, {"RefNumber": "1083175", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Preliminary analysis of a performance problem", "RefUrl": "/notes/1083175"}, {"RefNumber": "1056259", "RefComponent": "BW-PLA-IP", "RefTitle": "Collective note: Performance of BI planning", "RefUrl": "/notes/1056259"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2291095", "RefComponent": "BW-PLA-IP", "RefTitle": "Statistics Events in Planning", "RefUrl": "/notes/2291095 "}, {"RefNumber": "1973951", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP internal: tips & tricks for PAK", "RefUrl": "/notes/1973951 "}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}