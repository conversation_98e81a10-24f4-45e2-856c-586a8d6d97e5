SAP Note 1879656 addresses a problem encountered with SAP HANA-optimized DataStores. This issue impacts changelog extraction, where no results are returned, and errors occur during rollback requests and MERGE operations involving tables with a history table.

Summary of the SAP Note:

Symptoms:
- Changelog extraction from SAP HANA-optimized DataStores yields no results.
- Errors are encountered during rollback operations and MERGE operations with tables that include a history table.

Reason:
The issue occurs due to two specific prerequisites:
1. The history table must have been created before Revision 45 (for example, by converting a standard DSO to an SAP HANA-optimized DataStore).
2. More than 2^31 (2 to the power of 31) commits have been executed in the HANA database.

Cause:
In HANA revisions prior to SP5 (Revision 45), the internal columns $validfrom$ and $validto$ in history tables were created with an INTEGER data type (4 Byte). After Revision 45, the data type was changed to BIGINT (8 Byte). With over 2^31 commits, the INTEGER data type leads to numeric overflow when selecting data, resulting in empty result sets and subsequently, no data loaded into data targets which makes them inconsistent with the source DSO.

Solution:
The note advises upgrading to HANA SP6 Revision greater than 60, which includes a migration procedure to convert the data type of $validfrom$ and $validto$ columns to BIGINT (8 Bytes). Until the exact revision number containing the migration is published, the note offers interim measures.

- For systems with SAP HANA-optimized DataStores having the 4 byte data type for $validfrom$ and $validto$ and the commit ID has not reached 2^31:
  - Upgrade HANA database to Revision 57 or 58.
  - Upgrade BW system to 7.30 SP8 or SP9, 7.31 SP7 or SP8, and implement SAP Notes 1849498 and 1849497. Alternatively, upgrade to 7.30 SP10 or 7.31 SP9, which contains relevant ABAP coding.
  
The note also recommends reading a blog for information on a new activation procedure for standard DSOs and how to convert SAP HANA-optimized DataStores to standard DSOs.

In cases where the measures cannot be applied, it instructs users to create a ticket under the component HAN-DB or HAN-DB-ENG-BW.

References in the note:
- SAP Note 1849498 provides guidance on reconverting SAP HANA-optimized DataStores back to classic DSOs.
- SAP Note 1849497 describes the optimization of standard DSOs without needing to convert them for SAP HANA.
- SAP Note 1523337 and SAP Note 1514967 serve as central notes for the SAP HANA database, providing overarching information about the database, hardware, software downloads, and related components.

The SAP Note concludes with instructions to create a ticket for further support if solutions cannot be implemented, emphasizing the importance of addressing the numeric overflow issue timely to prevent data inconsistency and to maintain system stability.