SAP Note 1975633 addresses a problem where not all Sales and Distribution (SD) conditions are considered during the generation of receivables, which can impact the accuracy of financial documents. This issue is relevant for the enrichment of Financial Accounting (FI) documents with reorganization characteristics in the simulation of document splitting, which is essential for determining higher-level objects in hierarchical structures.

The note highlights that the reports SAPFACCO0 and, since SAP Note 1971695, the function module 'RV_ACCOUNTING_DOCUMENT_CREATE', have been used to structure internal SAP interfaces with certain restrictions as detailed in SAP Note 1487345. Moreover, SAP Note 1801767 outlines the functional scope limitations such as dealing with sales and distribution documents with active revenue recognition, business processes, and invoice lists. These restrictions affect the profit center reorganization processes.

The solution proposed by the note is to implement the provided corrections, which would ensure the complete simulation of an invoice document, including all conditions—not just selected ones. This means statistical conditions, conditions for transfer prices, revenue recognition processes, and partial conditions for down payment clearing will be importable and simulative.

For those who wish to switch to using SAPFACC1 for these processes, the note suggests opening a customer message under the component FI-GL-REO-GL, and it recommends implementing the relevant Support Package or attached source code corrections to resolve the issue.

Additionally, the note references SAP Note 1971695, which deals with the 'NO_DOCUMENT_REQUIRED' runtime error related to profit center reorganization, and SAP Note 1471153, a composite note that serves as a central reference for profit center and Funds Management reorganization-related issues, offering guidance on related notes and implementation instructions.