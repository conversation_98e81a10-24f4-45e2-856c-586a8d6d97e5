SAP Note 1987194 addresses an issue where generating a certain object type leads to an "Assertion Failed" error in the method `CL_FAGL_R_OBJLIST->GET_OBJECT_INFO_P`. This problem arises when a user has manually reduced a standard hierarchy and a certain object refers to a non-existent node in the hierarchy (specifically, not a top-level or first-level object). As a result, an assertion violation occurs during the generation of the object list.

The note identifies the issue as a program error and prescribes the implementation of a correction note as the solution to this problem.

An example provided in the note explains the issue further. In the standard hierarchy, a hierarchy of financial objects (such as Fixed Assets, Purchase Orders Assigned, and Account Payables) is shown with a top-down relationship. The modified hierarchy in this example shows that the "Fixed Assets" (FA) level has been removed by the user. During the object generation process, the following actions take place:
- Generation of Account Payables (AP) objects excludes the objects from the top-level AP objects due to the hierarchy alteration.
- Generation of Purchase Orders Assigned (POA) objects includes the respective POA in the plan as a top-level object since the superior object FA is now missing from the hierarchy.
- Upon reassignment of POA, the associated AP is generated and reclassified as a second-level object.

The issue occurs when an object, like an AP, is expected to have a superior object (like FA), but because of the manual alteration of the hierarchy, the FA object is missing, triggering the error.

The note does not reference any other documents or notes. It directly prescribes a correction as the solution, implying that users experiencing this issue should apply the correction as described to resolve the assertion failure.