SAP Note 1888722 introduces a new 'Change' action in the MIGO transaction to enable users to modify certain fields in material documents and reprocess outputs already created for these documents. Users can edit fields like Material Slip, Doc. Header Text at the header level, and Unloading Point, Goods Recipient, and Item Text at the item level. When an output already exists (indicated by the flag GOHEAD-XNAPR), an Output tab with a 'Display Outputs' button is available to navigate to and change outputs if authorized. If no output exists, the tab is inaccessible, and outputs cannot be manually added, but automatic output processing can be triggered.

For changing a material document, users require specific authorization checks on objects concerning plant, movement type, and storage location activities. Without print authorization, outputs can only be viewed, not edited.

This change is part of a broader initiative to phase out older MB** transactions and replace them with MIGO, as discussed in SAP Note 1804812. The latter note details the limited maintenance of the MB transactions and their replacement by MIGO since SAP ERP Release 4.6C. Critical errors will still be corrected in MB transactions, and improvements are directed exclusively towards MIGO. For ERP 6.xx environments, including EHP7 and EHP8, transitioning from MB transactions to MIGO is advised, and the note also refers to BAPI_GOODSMVT_CREATE as an alternative for batch input methods.

The new functionality for changing material documents within MIGO is planned to be standard in EHP7 for SAP ERP 6.0 but needs to be activated explicitly in customizing. The solution in this SAP Note can be implemented in environments of EHP4 with support package level SAPKH60410 or higher and EHP5 with SAPKH60505 or higher. Users should utilize transaction SNOTE to apply the program changes.