SAP Note 1980489 deals with the introduction of a new feature in RESTGUI: a GET request for system information. The purpose of this feature is to provide system information such as kernel version, kernel patch level, ABAP SAP basis, and ABAP supported package. This information is to be returned in various specified formats, including XML, JSON, or plain text.

In order to implement this new feature, the SAP Note provides a direct solution which is to install a specific kernel patch. The details of the kernel patch to be installed should be referenced in the corresponding section of the note. However, the summary provided does not include the details of the patch. There are no other reference notes or help documents listed within this note.

In summary, installing the mentioned kernel patch will enable the capability to perform a GET systeminfo request in RESTGUI, which will yield important system information in the user's desired format. No further references or additional documentation are provided within the Note 1980489.