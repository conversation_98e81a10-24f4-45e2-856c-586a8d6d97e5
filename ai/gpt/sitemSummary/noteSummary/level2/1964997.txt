SAP Note 1964997 provides information on the enhancement of kernel statistics for RFC (Remote Function Call) subrecords. These changes maintain compatibility with previous rules for storing RFC statistics but include extended rules for determining which entries to store.

Previously, the number of entries saved under RFC subrecords was determined by the profile parameter `stat/rfcrec`, which defaults to '5'. It would result in saving only a maximum number of the most costly entries as determined by an internal rule.

The enhancement involves grouping function module calls by their names and saving a maximum defined by the new parameter `stat/rfc/distinct_depth` for each group. This allows the total number of entries that can be saved to grow indefinitely, rather than being capped at the value specified by `stat/rfcrec`. For example, with `stat/rfc/distinct_depth` set to '3', three entries per function module group would be saved. If set to '0', all RFC calls would be saved.

Transaction STAD is compatible with this new procedure, and to activate the new functionality, one should use the profile parameter `stat/rfc/distinct`. The default value for the new `stat/rfc/distinct_depth` parameter is the old `stat/rfcrec` parameter value. The parameters can be changed dynamically using the function module `TH_CHANGE_PARAMETER`.

A new dynamic profile parameter `stat/recex/memory_check` has been introduced for internal purposes to activate complex save checks when needed.

Additionally, the note mentions corrections to issues such as an ABAP server crash when statistical profile parameters were changed using transaction ST03.

The enhancements and corrections are delivered in specific patches for SAP Kernel 721 listed as Patch 215, 217, 223, 332, and 415, with each patch providing different parts of the functionality or corrections.

The note does not make any reference to other notes and is described purely as an enhancement, implying there are no prerequisites apart from implementing the new kernel patch at the specified level or above.