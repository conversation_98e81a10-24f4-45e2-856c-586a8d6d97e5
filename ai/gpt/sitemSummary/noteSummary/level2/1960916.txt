SAP Note 1960916 addresses a limitation in the system authorization trace tools transaction ST01 and the enhanced version StAuthTrace, where there is no indication if an authorization check was passed due to the permissions of a reference user or directly assigned permissions.

Previously, the trace showed additional information for deactivated authorizations, but not for those granted through a reference user. With this note's correction, a new additional information option "E - Authorized by reference user" will be displayed, indicating that the authorization was granted based on a reference user's authorization.

Users are instructed to import a kernel with the specified patch level from the "Support Packages & Patches" section to apply this correction. If a kernel patch level is not listed, users should regularly check the SAP Note until it becomes available.

After implementing the changes, the "Additional Info" column in StAuthTrace will display the text "Authorized by Reference User" and provide an F1 help explanation. This allows for better testing of roles by assigning a reference user with the necessary authorizations to a test user and analyzing the application with StAuthTrace activated. If the trace shows "E" for all checks or other existing values ("A" through "D"), it concludes that the role setup is correct and ready for assignment.

The note also references four other SAP Notes (1793298, 1707841, 1638729, 1603756) that address different improvements and bug fixes related to the StAuthTrace functionality, enhancing its capabilities to evaluate authorization checks system-wide and record them more efficiently.