SAP Note 3115636 addresses an issue related to the generation of the object type GLOI during profit center reorganization processes in financial documents (FI documents). The key points of the note are as follows:

Symptom:
- The system checks for customer or vendor line items during the generation of the GLOI object type.
- If these line items are found, the system simulates document splitting for the FI document.

Reason:
- The simulation of document splitting for FI documents where customer or vendor line items are already cleared can cause performance and memory issues.

Solution:
- The note provides a change in the system logic: for FI documents without any open customer or vendor line items, the document split will not be simulated anymore.
- This change will reduce the runtime during generation and reassignment of GLOI.
- Program corrections should be implemented before the generation process.

References:
The note also refers to SAP Note 1627018 which is a composite note providing a central reference for segment reorganization issues. Users are directed to this note for further guidance and are advised to check for potential additional licensing fees. Note 1627018 prompts users to refer to related notes for detailed solutions on segment reorganization.

In summary, SAP Note 3115636 outlines a more efficient approach for processing FI documents during profit center reorganization when customer/vendor line items have already been cleared. This approach avoids unnecessary simulations of document splits, thereby improving performance and avoiding memory issues. Users need to implement the provided program corrections to benefit from this change.