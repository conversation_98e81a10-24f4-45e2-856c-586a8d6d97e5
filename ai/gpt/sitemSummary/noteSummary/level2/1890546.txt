SAP Note 1890546 addresses the issue of spool requests in SAP systems that are restricted to a maximum size of 2 GB. When this limit is exceeded, spool requests cannot be processed completely. This limitation is due to the size counter of the TemSe (Temporary Sequential objects), which is used for managing spool data.

To resolve this limitation, the note provides a solution in the form of kernel patches. The application of these patches allows for the creation of a new TemSe part when the maximum size of 2 GB is reached, effectively removing the restriction on the spool request size.

The specific kernel patches mentioned in the note are:

- Kernel 721: disp+work package, patch 227
- Kernel 738: disp+work package, patch 55
- Kernel 740: disp+work package, patch 64
- Kernel 741: disp+work package, patch 30

Additionally, Note 1890546 references SAP Note 857075, which discusses a related issue where a core dump is generated due to TemSe objects reaching their size limit, and recommends solutions and preventative measures for handling spool requests and managing potential size issues.

The note also references SAP Note 1728283, which provides general information about the SAP Kernel 721 and its enhancements over previous versions. These enhancements include support for new features, security improvements, supportability enhancements, GUI-related advancements, performance improvements, and platform- and DB-specific optimizations. Note 1728283 also notes that Kernel 721 is fully compatible with older 7XX kernel versions and can serve as a full replacement for Kernel 720 and prior versions for specified NetWeaver-based systems.

It is important for system administrators and users to apply the appropriate kernel patches to remove the size limitation and avoid issues with processing large spool requests in their SAP systems.