SAP Note 2010512 addresses a functionality update related to the Material Master application within the SAP system. The note highlights the implementation of features that are designed to prevent users from blocking customers and vendors if they are still linked to materials—as competitors or manufacturers, respectively.

Key aspects of this SAP Note include the following:

1. The application will now check the Material Master records to ensure that a customer, identified as a competitor to certain materials, is not blocked if they are still assigned to those materials.

2. Similarly, the system will also prevent the blocking of a vendor if they are still listed as a manufacturer for any materials.

3. Customizing "Customer Master/Vendor Master Deletion" has been updated with specific entries to define and store application names and define application classes registered for the "End of Purpose" (EoP) check.

4. A new class, CL_WUC_LO_MD_MM_EOP_CHECK, termed "Where-Used-Check Material Master," has been introduced to facilitate the EoP checks for customer and vendor information within the Material Master.

5. These changes are available starting with release SAP_APPL 617 and support package 05.

6. However, it should be noted that as of SAP ERP 600 Enhancement Package 8, Support package 06, these particular checks and blocks are no longer required due to dedicated handling of blocked customers and vendors in the Material Master and Article Master, referenced in SAP Note 2419991. Consequently, the new functionality provided by this note is disabled from that SAP ERP version onwards. This means the system will carry on automatic resetting or removal of associations to blocked customers or vendors in the Material Master, without the need for the previously required EoP checks.