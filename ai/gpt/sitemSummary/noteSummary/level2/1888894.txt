SAP Note 1888894 addresses an enhancement in the logging mechanism within the SAP Gateway. Specifically, it pertains to the use of Action Z mentioned in SAP Note 1689663. 

Previously, the Action Z logging entry was made only when the simulation mode was activated for the SAP Gateway, which allowed the system to log actions where no matching rule in the reg_info or sec_info was found when an attempt to register or start was made. With this enhancement, the Z entry will be written to the log regardless of whether the simulation mode is active or not.

The purpose of this enhancement is to improve the logging features of the SAP Gateway, which can be instrumental in diagnosing issues related to the registration or starting of components in the absence of explicit rules.

To solve the issue described and implement the enhancement, the SAP Note advises to apply the necessary kernel corrections as specified under the "SP Patch Level" tab page.

The SAP Note 1888894 refers to SAP Note 1728283 for general information about the SAP Kernel 721, which includes various enhancements to the NetWeaver AS ABAP/Java from security to performance improvements. It also references SAP Note 1689663, which initially introduced the simulation mode for reg_info, sec_info, and prxy_info as a way to assist in the transition to using ACLs without affecting the productive operation.

Ultimately, users of the SAP Gateway are directed to update their system as per the instructions in this note to benefit from the improved logging capabilities brought about by the enhancements described.