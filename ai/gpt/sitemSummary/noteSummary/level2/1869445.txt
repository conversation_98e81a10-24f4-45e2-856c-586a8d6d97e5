SAP Note 1869445 addresses an issue where system dumps occur when documents or document lines that have already gone through reorganization are repeatedly generated in the accounts payable/accounts receivable (AP/AR) area. Specifically, the dumps happen when profit center cycles are set up in a way that allows a profit center to exist both as an old and new profit center in the reorganization plan (e.g., A -> B and B -> C). It has been noted that situations where the old profit center is the same as the new profit center (B -> B) had not been previously evaluated and thus could cause problems.

This note applies particularly to reorganizations of profit centers and segments in the context of SAP's New General Ledger (New GL). The symptom described is linked to a potential oversight in planning when a profit center is assigned as both the old and new profit center.

The root cause of the problem is tied to the fact that in the reorganization plan, an account assignment is entered as both an old and a new account assignment, which should be avoided. An example of such an entry would be to have a profit center PCA as an old profit center being reorganized to profit center PCB, and at the same time, PCB is also listed as an old profit center that is being reorganized, which can be incorrect.

The solution mentioned in the note is to implement the correction instructions attached to the note. These instructions are intended to fix the issue and prevent similar system dumps from occurring.

Additionally, references are given to three other SAP Notes that relate to profit center or segment reorganization, error message configurations, and a composite note for profit center and Funds Management reorganization that serves as a central reference point for related issues and their resolutions. These notes provide context, additional solutions, and configuration adjustments related to the problem described in SAP Note 1869445, helping users navigate and resolve issues tied to profit center assignments during cycle reorganizations.