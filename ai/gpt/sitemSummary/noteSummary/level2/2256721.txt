SAP Note 2256721 addresses an issue involving the BAPI_FIXEDASSET_OVRTAKE_CREATE, which is used during legacy asset transfer via tools like the Legacy System Migration Workbench (LSMW). The problem is that when performing mass transfers, IDocs that are expected to create legacy assets might be marked as successfully processed (status 53) even though some of the expected assets, particularly those without any values, are not created. This unexpected behavior occurs when both assets with and without values are processed in the same COMMIT operation if the package size is greater than 1. 

The root cause of the problem is identified in the note as a program error. The IDoc framework is designed to perform only one COMMIT WORK at the end of processing a package. If the package includes a fixed asset without values processed before a fixed asset with values, the update data for the asset without values gets deleted by the update of the asset with values.

The solution provided in the note is to implement some source code corrections. However, a temporary workaround is to reduce the package size to 1 during the IDoc processing to ensure that each asset is committed individually, preventing the deletion of the update data. This ensures that all assets, with or without values, are created correctly. The note does not reference other documents or notes.