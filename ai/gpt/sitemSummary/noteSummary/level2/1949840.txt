SAP Note 1949840 addresses a specific issue where the system experiences a dump during the process of generating receivables and payables. The dump occurs in the method `CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL`.

The note relates to scenarios involving the reorganization of profit centers and segments, as indicated by the terms used within the note, like "Profit center reorganization", "Segment reorganization", and "Reorg". It also refers to an SAP transaction/code `FAGL_REORGANIZATION 566`, implicating that this issue is tied to this specific reorganization function.

As a prerequisite to understanding and correcting the issue, users are pointed to SAP Note 1911623. Unfortunately, details of the cause are not provided in the SAP Note 1949840 description you've given, hence users would need to consult the referenced note for specifics.

To resolve the issue described in SAP Note 1949840, users are advised to implement program corrections. However, the details of these corrections are not explicitly given in your description, meaning users must obtain those directly from the SAP Note or related documentation. 

The note also references SAP Note 1471153, which is a composite note that collates all related documentation concerning reorganization of profit centers and Funds Management reorganizations within the new General Ledger (FI-GL) functionality. This composite note provides users with context, terms, important information about licensing, applicability, and instructions for checking related notes for further details on problems and solutions. It also instructs users on how to get support for the implementation.

In summary, SAP Note 1949840 deals with a software dump issue during AP/AR generation linked to reorganization processes within SAP, and it advises users to implement program corrections and consult related SAP Notes for full details on how to address the problem.