SAP Note Summary: 1985306 - Performance guide for goods movements

The SAP Note 1985306 addresses the issue of system performance during parallel goods movements, particularly when error messages M3 024 (material valuation data locked) or M3 023 (plant data locked) are displayed. The note clarifies that these messages are not program errors but are necessary locks from a business perspective to ensure the consistency of stock quantities and values.

The purpose of this note is to provide guidance and tools for customers and implementation partners to better understand system behavior and to minimize the exclusive lock time for a material through specific optimizations. Although these locks are justified, SAP acknowledges that performance can be improved by optimizing business processes and system interactions.

Key solutions outlined in the note for performance optimization include:

1. Use of late lock for goods movements, where exclusive locks are applied only when saving data.
2. Implementation of late lock for the material master, which allows certain material master data to be blocked in a shared mode.
3. Settings for material blocks in invoice verification to reduce overlapping blocks.
4. Preference for "S" price control for materials frequently posted in parallel to improve the chances of converting exclusive blocks to shared blocks.
5. Optimization of performance for customer-defined code within function modules related to goods movements.
6. Specific settings for SAP SCM APO integration with late block for goods movements.
7. Delaying the database update for the tables MBEW and MARD, based on SAP Note 1737609, to reduce the total blocking time on the database.
8. Adjustments to the waiting time for late blocks (differing for background and dialog users) utilizing SAP Note 1840264.
9. Reduction of exclusive block time for parallel goods movements via delivery as provided in SAP Note 1776807.
10. Batch locks for parallel goods movements with shared lock options utilizing SAP Note 1501121.
11. Unlocking of the plant segment (MARC) in Brazilian company codes with SAP Note 3106950.
12. Adjustments for goods movements in SAP S/4HANA, referring to SAP Notes like 2319579, 2267835, and more, for consulting and manual execution.
13. Recommendations for optimizing individual business processes through extensive testing.
14. Suggestions for seeking further support through the Continuous Quality Check & Improvement Services or local SAP consulting.

This note also contains several references to related SAP Notes that can provide additional information, improvements, and solutions to specific problems encountered during goods movements. It emphasizes that these are consulting notes which generally require manual executions and are to be handled by the local SAP consulting team or in-house consultants.

In summary, this SAP Note is a comprehensive guide aimed at improving performance during goods movements by minimizing material lock times and offering various strategies and optimizations to better align system parameters with business processes.