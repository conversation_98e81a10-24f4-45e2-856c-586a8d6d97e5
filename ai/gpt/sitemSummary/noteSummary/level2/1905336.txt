SAP Note 1905336 addresses the re-enabling of the XMLDIFF feature within the RESTGUI/ITS interface. This feature was previously disabled, and the note announces that it is available again. To resolve the issue, the note simply instructs the user to update the kernel to a specific patch, though it does not provide the exact patch level to which the user should update.

The note also references SAP Note 1728283, which provides general information about the SAP Kernel 721. This referenced note details the purpose and enhancements of Kernel 721, describing it as a replacement for Kernel 720 and older versions for SAP systems based on NetWeaver 7.00-7.31. Furthermore, it lists various enhancements that the newer 721 version provides over 720, which includes new functionalities, security improvements, supportability enhancements, GUI-related advancements, scalability and performance enhancements, technological updates, and specific optimizations for different platforms and databases.

Overall, SAP Note 1905336's intent is to inform users that the XMLDIFF feature in RESTGUI/ITS is available once more, and it advises them to update the kernel accordingly to make use of this feature. It also directs the user to SAP Note 1728283 for extensive details on SAP Kernel 721, underscoring the significance of using the up-to-date kernel version for the best performance and latest capabilities.