SAP Note 1863965 - Incorrect numbers of objects in reorganization plan

Summary:

- **Symptom**: Users encounter discrepancies between the total number of objects and objects not processed listed in the reorganization plan when dealing with certain object types, such as fixed assets. The numbers shown do not match the actual count of objects in the object list.

- **Other Terms**: The note mentions terms FAGL_R_PL_OBJLST and FAGL_R_PL_COUNT, which are likely technical identifiers related to the issue at hand.

- **Reason**: The root cause of the issue is identified as a program error.

- **Solution**: The recommended action for users is to apply an advance correction to address the problem.

The note does not provide the actual steps for the correction or detail the problem's technical aspects. Instead, it advises users to implement an unspecified advance correction, presumably provided in the note or related documentation.

The references to SAP Notes 1627018 and 1471153 provide additional context and resources for users dealing with segment reorganization and profit center and Funds Management reorganization. These composite notes serve as hubs, directing users to other relevant SAP Notes for specific issues related to reorganization within the SAP system.

In summary, SAP Note 1863965 alerts users to an error in the reorganization plan where the counts of certain objects are incorrect due to a program error and advises the implementation of an advanced correction to resolve the issue.