SAP Note 1849101 addresses an issue that occurs during the profit center reorganization process. Specifically, when users try to select Controlling (CO) objects, they may find that fewer objects are chosen by the system than expected. The affected objects are:

- Orders of order categories 1 - 40 (O01-O40)
- Work Breakdown Structure (WBS) elements
- Network activities (NWA)

The root cause of this selection issue is related to certain status fields in the object tables (e.g., AUFK-LOEKZ or AUFK-PHAS3) not being correctly populated by the relevant application components. The note references SAP Note 498387 for additional information regarding this problem.

The proposed solution to correct this issue is to implement the corrective actions described in the note or import the relevant Support Package. After applying the solution, the selection of CO objects during the profit center reorganization will only consider the JEST table for determining if objects are open or closed with respect to the reorganization.

The note also refers to SAP Note 1762222, which delves into the issue of selection criteria based on the system status in the JEST table. The aim is to ensure proper consideration of system statuses, such as 'Deletion indicator' (I0013), 'Closed' (I0046), or 'Deletion Flag' (I0076), which indicate that objects should not be selected for reorganization.

Lastly, SAP Note 1471153 is mentioned as a composite note which provides a central point of reference for various SAP Notes related to profit center and Funds Management reorganization processes. It offers guidance on prefixes used to identify notes for different types of reorganizations and details on prerequisites and solutions, along with advising on potential licensing fees and how to get support for implementing these features.