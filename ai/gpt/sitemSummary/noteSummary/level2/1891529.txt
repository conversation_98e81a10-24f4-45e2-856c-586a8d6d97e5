SAP Note 1891529 addresses the issue of reconverting SAP HANA-optimized DataStore Object (DSO) partitions that are part of a semantically partitioned object (SPO), which was not supported previously.

Summary of the Note:

**Symptom**: Initially, reconversion was not supported for SPO partitions that were SAP HANA-optimized.

**Other Terms**: Mentioned terms related to SPO, partitions, DSO, SAP HANA, reconversion, and related processes.

**Reason and Prerequisites**: The note is only applicable for systems using SAP HANA as their database platform.

**Solution**: The note introduces updated functionality for the program RSDRI_RECONVERT_DATASTORE which now supports the reconversion of SAP HANA-optimized DSO partitions that form part of an SPO. The reconversion process for an SPO follows the procedure outlined in SAP Note 1685280.

**Support Packages**: The note also details the specific Support Packages that need to be imported for different versions of SAP NetWeaver BW to enable this functionality:
- SAP NetWeaver BW 7.30: Import Support Package 11 (SAPKW73011), as detailed in SAP Note 1878293.
- SAP NetWeaver BW 7.31: Import Support Package 10 (SAPKW73110), as detailed in SAP Note 1882717.
- SAP NetWeaver BW 7.40: Import Support Package 5 (SAPKW74005), as detailed in SAP Note 1888375.

In cases where the issue is urgent, correction instructions can be implemented as an advance correction after reading SAP Note 1668882, which provides information about using transaction SNOTE.

The note implies that potentially the SAP Notes described above may already be available to customers before the mentioned Support Packages are officially released, and if so, the short text of these notes would contain the phrase "Preliminary version."