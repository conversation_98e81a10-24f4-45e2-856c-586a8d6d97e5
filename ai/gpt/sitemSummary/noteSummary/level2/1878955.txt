SAP Note 1878955 addresses an issue that occurs when trying to amend a purchase order following a reorganization that affects the Profit Center.

**Symptom**: After the reorganization of a Profit Center, changing a purchase order item (e.g., modifying the quantity) can cause problems if the system checks the account assignment objects using the original purchase order creation date. Two specific issues identified are:
   - The system may issue an error message (FAGL_ORG_UNITS001) if the new Profit Center was not valid as of the purchase order's original creation date.
   - If the account assignment object is a cost center, the Profit Center is derived again from the cost center master data as of the purchase order's document date and may be incorrectly overwritten in the purchase order item.

**Other Terms**: The note mentions terms ME22N (transaction code for changing a purchase order) and K_COBL_CHECK (account assignment check).

**Reason and Prerequisites**: The reason for these problems is identified as a program error.

**Solution**: Users are advised to implement program corrections to address these issues. Additionally, the note suggests organizational workarounds, such as:
   - Extending the validity of the new Profit Center to cover the creation dates of open purchase orders.
   - Where possible, replacing old purchase orders with new ones to avoid the issue.

References:
The note references SAP Note 1471153, which is a composite note providing guidance on profit center and Funds Management (FM) reorganization within the SAP system. This reference note includes identifiers for various reorganization-related notes, important licensing information, relevance for certain business functions, and guides for finding detailed information on identified problems and their solutions.

In conclusion, SAP Note 1878955 essentially directs users to correct a program error that affects purchase order changes after Profit Center reorganizations and offers temporary solutions to mitigate the problem.