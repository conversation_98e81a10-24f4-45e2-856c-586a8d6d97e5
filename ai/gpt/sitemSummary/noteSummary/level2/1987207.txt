SAP Note 1987207 addresses an issue in which, despite a status message indicating successful account reassignment during profit center reorganization, the document account is not correctly reassigned in the general ledger view (table FAGL_SPLINFO). The symptom is that the original PRCTR segment still appears in this table, while the document looks processed in the reorganization tables.

The note identifies the root cause as adjustments to the document split fields after the document's creation. These adjustments could lead to the processing of different document split characteristics during the reorganization than those used at the document's generation. This issue was previously highlighted in SAP Note 891144, which mentions that changes to document splitting characteristics can have critical side-effects.

The solution provided in Note 1987207 is a safeguard to prevent account reassignment of such open items by issuing error 568. A method to recognize such documents when generated has been proposed (CL_FAGL_R_SPLIT_REORG: COMPARE_REORG_SPL_WITH_SPLINFO), as addressed in SAP Note 2005825.

SAP advises ensuring that documents are adjusted to match current document split fields before initiating a PRCTR reorganization. However, SAP does not take responsibility for the adjustments to the document split fields and recommends seeking a consultant's help.

The note also instructs to implement all current SAP Notes referenced in SAP Note 1471153. This note is a composite note that guides users to manage profit center and Funds Management reorganizational adjustments within the new General Ledger functionality, listing corrections, and clarifying that additional license fees may be required.

In summary, SAP Note 1987207 deals with a specific issue of account reassignment failures during profit center reorganizations and guides users on how to avoid such issues through careful pre-reorganization adjustments and by implementing all related SAP Notes provided for additional corrective actions.