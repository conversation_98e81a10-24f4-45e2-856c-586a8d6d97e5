SAP Note 1948053 addresses an issue concerning inconsistent sites, or parts of sites, that cannot be resolved using the standard process for archiving and deletion. The note provides instructions for cases where the standard solution suggested by SAP Support, which involves creating a deletion report based on the sample report ERASE_SITE (from the package WFIL), is not feasible due to organizational or technical reasons.

The key points summarized from SAP Note 1948053 are as follows:

1. If there are inconsistent site parts in the system, SAP recommends using the report ERASE_SITE for deletion as a last resort after consulting with SAP Support.

2. The note includes a modification proposal for the ERASE_SITE report, which needs careful handling because it performs no checking for related data that might be affected by deletion. This means that using the modified ERASE_SITE without proper checks may lead to removing data that is interconnected with other important data in the SAP system, potentially causing further data consistency issues.

3. SAP strongly advises that sites should be deleted using the archiving function and not the ERASE_SITE report. For guidance on using the archiving function, SAP Note 690340 should be referred to.

4. For implementation, customers are instructed to create a new report using the modified source code provided in the note and to ensure that they implement an authorization check, which is not included in the proposed modification.

5. It is crucial to only use the ERASE_SITE report for deletion when expressly recommended by SAP to avoid unintended consequences.

Finally, the note does not reference any other documents or notes.