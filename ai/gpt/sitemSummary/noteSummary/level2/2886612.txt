SAP Note 2886612 addresses a legal change in Russia requiring specific information to be filled out in payment orders for payroll payments, in accordance with directives issued by the Central Bank of the Russian Federation. The note implies that this is relevant for users utilizing the Fiori App "Manage Additional Payment Attributes" within the SAP environment.

The symptoms addressed by this note revolve around two fields within a payment order:
1. Field 110 - "Payment type" should be set to "1" for budgetary compensation payments, as per legal document 383-P.
2. Field 20 - "Payment purpose" can take one of three values: "1", "2", or "3", as specified in legal document 5286-U dated October 14, 2019.

The note is intended for users who need to comply with these legal requirements in the context of payroll payments in Russia. The mentioned Fiori App is used to manage payment attributes in vendor-related transactions.

The solution provided by the note includes installing a support package or implementing correction instructions with manual steps. Once implemented, it allows users to enter the required values in fields 20 and 110 for payment orders produced by the Automatic Payment Program (transaction F110). Detailed steps guide users through the process of maintaining a business partner with specific settings for payroll payments, maintaining values for field 20 in the new maintenance view J_3RF_020_VAL_V, posting a vendor invoice or payment request, and managing payment attributes through the Fiori app.

The note ensures that after its application, additional attributes can be correctly managed to align with Russian regulations, thereby ensuring the legality and compliance of the payment orders.