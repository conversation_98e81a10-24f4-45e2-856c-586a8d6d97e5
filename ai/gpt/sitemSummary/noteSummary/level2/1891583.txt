SAP Note 1891583 describes a means to restrict user logons to an SAP application server during maintenance or other scenarios where you want to ensure controlled access. The note provides details on how to enable this restriction using a new profile parameter `login/server_logon_restriction`.

The key points of the note are:

- The feature to restrict logons is available as of SAP kernel releases 721, 740, 741, and subsequent versions starting from SAP_BASIS 731.
- Users can control access using the profile parameter `login/server_logon_restriction`. There are three possible settings for this parameter:
  - `0`: No logon restriction, all users can log on.
  - `1`: Only users with special rights can log on. This requires a security policy change using transaction SECPOL, where the attribute `SERVER_LOGON_PRIVILEGE` should be set to `1` for administrators.
  - `2`: Logon to the application server is completely restricted.
- There are instructions on how to check which users have been assigned a security policy using transaction SUIM.
- It is important to note that if the emergency user (`SAP*`) is active, it can always log on. The `SAP*` user is considered active if `login/no_automatic_user_sapstar` is set to `0` and not defined in SU01.

The note also references SAP Note 68048, which provides guidance on deactivating the `SAP*` emergency user in various SAP releases, and SAP Note 1728283, which contains general information about the SAP Kernel 721 and its enhancements.

In summary, SAP Note 1891583 gives SAP system administrators the guidance needed to control server access during maintenance by setting a profile parameter, informing them about the necessary kernel patch level and SAP_BASIS version, and highlighting the implications for the `SAP*` user.