SAP Note 1964587 addresses a specific software issue where a dump occurs in the method CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL during the generation of receivables and payables. The note is related to the reorganization of profit centers and segments, specifically identified by the terms PRCTR (Profit Center reorganization), SEG (Segment reorganization), and the reorg process (FAGL_REORGANIZATION 566).

For understanding the reason for this error and its prerequisites, the note directs users to refer to SAP Note 1911623. However, details regarding that note are not provided here.

The solution outlined in SAP Note 1964587 is straightforward – implement the program corrections. This implies that there are existing corrections available that address this issue, and customers encountering this problem should apply these corrections to their SAP systems.

In addition, SAP Note 1964587 references SAP Note 1471153, which is a composite note providing further details and related notes for solving issues connected to profit center and Funds Management (FM) reorganizations. SAP Note 1471153 serves as a central reference point to guide users on this topic, and it advises users to check for related notes using the specified prefixes based on the reorganization type. It also mentions potential additional license fees, prerequisites for using the reorganization features, and directs users to the related notes for problem-solving and implementation guidance. Users are also advised to check if they have SAP Note 1668882 in their system for the correct implementation of notes for various SAP Basis versions, and to open an incident with the BC-UPG-NA component for support if needed.

To summarize, SAP Note 1964587 deals with a program dumping issue during the generation of AP/AR documents arising from profit center or segment reorganizations and requires the users to implement the provided program corrections to resolve the problem.