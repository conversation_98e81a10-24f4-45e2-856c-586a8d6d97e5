SAP Note 1738494 addresses the desire to leverage new Unicode printing capabilities in SAP NetWeaver (NW). The note suggests that users interested in these new capabilities should consult an attached document that thoroughly describes the benefits, as well as provides a guide for installation and testing.

To support the implementation of the new Unicode printing capabilities, SAP Note 1738494 references two additional notes:

1. SAP Note 1728283 - This note offers a general overview of the SAP Kernel 721, which introduces various enhancements including those for Unicode printing. It explains that Kernel 721 is a replacement for Kernel 720 and is backward compatible with older kernel versions. Among the numerous improvements listed, the ability to handle Unicode printing is specifically noted. This kernel version also brings other new functionalities, security enhancements, supportability improvements, GUI-related advancements, scalability, and performance improvements, as well as platform- and database-specific features. More details on Kernel 721 can be found in the associated SAP Notes mentioned.

2. SAP Note 1716826 - This note details the usage of the downward-compatible Kernel 721 (EXT) for various SAP NetWeaver releases and provides guidance on how to implement this kernel version. It indicates that Kernel 721 (EXT) can be used as a replacement for older, out-of-maintenance kernels or as an upgrade from Kernel 720 (EXT). The note outlines clear benefits of the Kernel 721 (EXT) over its predecessors and provides instructions on installation, upgrade paths, and potential issues to watch out for.

In summary, SAP Note 1738494 serves as a pointer to detailed information on implementing new Unicode printing functions by referencing documents that explain the features and setup steps, as well as by indicating relevant SAP Notes on Kernel 721 which underpins this capability.