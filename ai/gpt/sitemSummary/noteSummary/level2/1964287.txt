SAP Note 1964287 addresses performance issues in transactions related to the creation and transfer of generic articles, such as MM41 (creation of a generic article), WAK20 (price activation of an action), and WESOUT (transfer of generic articles via Services). The problem occurs when the system handles materials that do not have associated purchasing info records. The existing system buffer attempts to prefetch purchasing info records for all materials in question but fails to remember that certain materials do not have such records, leading to unnecessary database postreads.

To address this, the solution delivered in the SAP ECC as of EHP7 Support Package includes an optimization where an additional global table is introduced in the function group ME05. This table is designed to keep track of materials that have no purchasing info records when accessed from the database, preventing repeated and wasteful checks to the database for these materials.

References to another SAP Note (1962074) are also provided. SAP Note 1962074 discusses performance optimizations in the Retail article master maintenance process, particularly transaction MM41. It details several specific optimizations, including changes to the system's approach to determining the supply source during article creation, improved access to the Listing Conditions SAP Retail Assortments table (WLK1), and buffering strategies to prevent redundant database reads. Additionally, it advises on deactivating unnecessary functionality to avoid superfluous reads from certain tables.

In summary, SAP Note 1964287 is focused on enhancing performance by improving how the system handles materials without purchasing info records through strategic buffering, reducing database access overhead. It is also related to performance improvements outlined in SAP Note 1962074, which focuses on optimizing different aspects of article master maintenance within the Retail environment.