SAP Note 1923499 addresses the issue where STATIC or Semi-dynamic Technical Bills of Material (TBOMs) record very few objects, despite a high branching level, when generated through transaction SOLAR01/SOLAR02 or the mass TBOM generation report 'AGS_BPCA_TBOM_STATIC_GEN' in SAP Solution Manager's Business Process Change Analyzer (BPCA).

The cause of this issue is that the STATIC TBOM relies on the where-used data, which is outdated. To resolve this issue, the note recommends updating the where-used index in the managed system as outlined in SAP Note 28022.

Furthermore, two additional SAP Notes are referenced for more information on the where-used index:

- SAP Note 2039618 discusses how to manage the growth of the table WBCROSSGT, which can happen when the where-used index is compiled after an upgrade. It also provides instructions on how to delete the index content if it's not needed in a production system and how to recompile it in a development system.

- SAP Note 1917231 focuses on improving the runtime performance of the where-used list and the EU_INIT job by optimizing database accesses.

Users who encounter TBOM recording issues due to outdated where-used data should first ensure their where-used index is current by following the guidance in these notes. This will allow for the generation of more comprehensive TBOMs in the Solution Manager.