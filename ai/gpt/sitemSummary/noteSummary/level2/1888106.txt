SAP Note 1888106 addresses an issue where the activation of a request fails for a Data Store Object (DSO) after converting a HANA-optimized DSO to a standard DSO, following the implementation of SAP Notes 1849497 and 1849498. The failure is indicated by an error message in transaction SM21 and the request log, mentioning that the table RSODSACTUPDTYPE does not exist or is not found.

The problem occurs in the BW on HANA environment and is caused by the table RSODSACTUPDTYPE being located in row-store instead of being in the column-store which it should be.

The resolution for this issue is to alter the table storage by executing an SQL statement in HANA Studio, which transfers the table RSODSACTUPDTYPE into the column-store. The precise SQL command to be executed is:

```sql
alter table SAPHBW.RSODSACTUPDTYPE column
```

(The user is instructed to replace SAP<PERSON>B<PERSON> with the appropriate schema owner name if necessary.)

After this operation, the activation of the DSO should proceed without errors.

The note also mentions that this issue has been addressed in the latest row-store list that is attached to SAP Note 1659383, which is used by SAPINST and the report RSDU_TABLE_CONSISTENCY.

Keywords associated with the note are row-store, column-store, alter table, SAPINST, and RSDU_TABLE_CONSISTENCY. There are no additional references provided in this note.