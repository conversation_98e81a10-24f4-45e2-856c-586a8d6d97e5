SAP Note 1860677 addresses the issue of missing Read Access Log (RAL) functions in an SAP system. The reason for this issue is that RAL has not been implemented.

To resolve the problem, the following solutions are provided:

1. Users are advised to apply the latest kernel patch. The specific patch for RAL should be sought in the SAP Service Marketplace, and applied as per the guidelines of SAP Note 19466. For users to check the minimum patch level required for their respective SAP release, they should refer to the "Support Package patch level" section within the note.

2. It is also recommended to use a specified Support Package or a higher one to ensure that the RAL functions are available.

Additionally, it is mentioned that RAL can be activated or deactivated with the dynamic profile parameter 'sec/ral_enabled_for_rfc'.

As a reference, SAP Note 1860677 links to SAP Note 1728283, which provides general information about SAP Kernel 721. This includes the purpose of the kernel, details about its replacement and deployment, as well as a list of enhancements that come with Kernel 721 over the previous Kernel 720. These enhancements range across new functionalities, security, supportability, GUI improvements, performance enhancements, technology updates and specific platform and database optimizations. Each of the mentioned enhancements is further detailed in their respective individual SAP Notes, providing users with a deeper understanding of the prerequisites and specific improvements.

In summary, SAP Note 1860677 guides users on how to implement RAL functions by updating the kernel patch and using the appropriate Support Packages, and offers the option for enabling or disabling RAL using a profile parameter.