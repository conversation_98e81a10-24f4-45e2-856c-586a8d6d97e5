SAP Note 1946054 provides details regarding the changes in transaction codes and programs within specific application areas (AC, CO, FI, AA, and FIN) after the installation of SAP Simple Finance on-premise edition, when compared to the previous EHP7 for SAP ERP 6.0. It describes which transactions and programs have been removed and what their newer counterparts are, including WebDynpro applications.

Key points to consider:

- SAP Simple Finance uses the SAP General Ledger (New GL) logic, and thus transactions from the classic General Ledger have been replaced with those from the New General Ledger. These replacements are not explicitly mentioned in the note but need to be taken into account.
- For those who cannot transition to SAP BPC optimized for SAP S/4HANA in the short term and need to continue using classic CO-OM planning functions, SAP provides modifications in several other SAP Notes.
- With SAP S/4HANA 1511, classic planning functions have been re-enabled but are not included in the SAP Easy Menu. Beware that they will update totals tables rather than the ACDOCP (new planning table available from SAP S/4HANA 1610), affecting the use of Fiori apps that are based on the new table.
- Customers formerly using BW-extractors for FI-GL plan data should transition to using SAP BPC optimized for SAP S/4HANA for recording plan data for FI and CO.
- Details on affected Asset Accounting transactions and their replacements can be found in the release notes for Asset Accounting, with links provided for both German (DE) and English (EN) versions.
  
The note also references other SAP Notes for additional guidance on related topics such as adjustments for customer-specific programs (SAP Note 1976487), information about new user interfaces for cost planning (SAP Notes 1880317, 1836149, 1777947, and 1719702), and initial steps with new planning interfaces.

Updates and changes have been made to this note since its initial release, including terminology updates and additional guidance on different SAP S/4HANA releases, indicating that the note is being kept up-to-date with the evolving SAP landscape.

This SAP Note is essential for SAP users and developers to understand the adjustments needed after migrating to SAP Simple Finance to ensure a smooth transition and proper functionality within the new system environment.