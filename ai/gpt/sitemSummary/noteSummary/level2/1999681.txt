SAP Note 1999681 addresses an issue where an ABAP program, when activated, displays a message stating that the program contains generation errors, despite the ABAP syntax check showing no errors. The cause of the error is identified as the use of a global ABAP class within the program that maintains a 'FRIEND' relationship with another ABAP class which has been deleted.

The solution to this problem involves an enhancement in the dependency management for precompiled headers concerning global classes with FRIEND relationships. The resolution requires the import of a new SAP kernel which includes a kernel patch labeled "ABAP-PH: Add missing FRIEND dependency", which addresses the error and prevents the incorrect generation error message from appearing upon activation of the ABAP program.

There are no additional references associated with this note.