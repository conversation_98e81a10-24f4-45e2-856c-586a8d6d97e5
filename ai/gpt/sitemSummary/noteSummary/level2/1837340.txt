SAP Note 1837340 highlights an issue and its solution regarding the transfer of billing documents to financial accounting, particularly in the context of an account assignment change during the reorganization of Work in Process (WIP) and SD objects. The note provides the following details:

**Symptom**: It's essential for all billing documents to be transferred to financial accounting before making any changes to the account assignment. This was not previously checked by the program.

**Other Terms**: The note includes terms such as Reorg, Reassignment of SD orders, FAGL_REORGANIZATION 343, Transfer billing documents, and Billing document transfer block.

**Reason and Prerequisites**: The lack of a query within the program to ensure the transfer of billing documents before an account assignment change is the issue that prompted the creation of this note.

**Solution**: Going forward, the system will verify whether SD orders or contracts have been billed before allowing a change in the account assignment. The note includes source code that needs to be implemented to enable this check. If an error occurs (FAGL_REORGANIZATION 343), users should release the billing document to financial accounting and then attempt the account assignment change again.

**References**: SAP Note 1837340 refers to several other SAP Notes:

1. SAP Note 1880282, which deals with the consistent reassignment of master data following profit center reorganizations and introduces the error message FAGL_REORGANIZATION 343.

2. SAP Note 1864097, which builds upon 1837340 and discusses the implications of changing the error message for billing document transfers to warnings.

3. SAP Note 1858541, which addresses a system dump (DBIF_RSQL_INVALID_RSQL) when changing account assignments for sales document items during profit center reorganization.

4. SAP Note 1471153, which is a composite note referring to a collection of SAP Notes related to profit center and Funds Management (FM) reorganization within SAP.

Overall, SAP Note 1837340 is focused on ensuring that billing documents are properly transferred to financial accounting to avoid issues during the reorganization process and making sure that these checks are implemented in the system to prevent future errors.