SAP Note 1955532 addresses a specific issue related to profit center reorganization within SAP, particularly in the determination of material stock for cost element category 90. Here's a summary of the content of the note:

**Symptom:**
Users have encountered unwanted balances in Profit Center Accounting due to the standard logic using only the "Material" object type for determining anonymous and special stocks, and always determining transfer postings with the profit center from the material stock. This is problematic when special stock is associated with sales and distribution documents or WBS elements that use the profit center of the relevant CO object, as defined by cost element category 90.

**Reason:**
There is a missing function in the system's standard logic.

**Solution:**
The note provides an enhancement to the program determining material stock. The solution involves two scenarios:
1. If cost element category 90 is *not* defined for the BSX account, the system behavior doesn't change.
2. If cost element category 90 *is* defined, and the new logic is activated using indicator REORG90_XXXX in the table CKMLMVADMIN for the controlling area, there is a change in how special stock is determined and posted. In this case, special stock is determined using the "Sales and Distribution Document" or "WBS Element" object type and transfer posted with the profit center from the CO object, rather than from the material master.

The note also includes important instructions for the implementation. Notably, activation of this note should not be done during an ongoing reorganization, and the reorganization plans must have the status "Closed" (i.e., status = 20).

The reference SAP Note 1471153 is a composite note that relates to the overall reorganization of profit centers and Funds Management within SAP, providing a central reference point for users and detailing prerequisites, applicable business function activations (FIN_GL_REORG_1 or PSM_FM_REASSIGN), and related terms for a better understanding of the reorganization process. It serves as a broader context for understanding the specific changes described in SAP Note 1955532.