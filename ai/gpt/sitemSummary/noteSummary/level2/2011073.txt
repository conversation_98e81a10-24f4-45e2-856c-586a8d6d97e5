SAP Note 2011073 outlines the functionality to block and simplify the deletion of customer and vendor master data in the FI (Financial Accounting) system using SAP Information Lifecycle Management (ILM). This is particularly relevant for complying with data protection laws by managing personal and person-related data.

Key points from SAP Note 2011073:

1. Purpose: The note describes how customer and vendor data blocking and deletion is facilitated through ILM, which is detailed in SAP Note 2007926. It also functions as a technical prerequisite for other corrections but does not provide any new functions itself.

2. Applicability: The changes apply to customer and vendor documents in the FI system, affecting master records, one-time documents, and documents related to alternative payers and payees.

3. Main Characteristics:
   - When a customer or vendor is blocked, all personal data, including their numbers, are hidden. Displaying, changing, or posting new documents with such blocked entities is disabled.
   - During the posting of documents by reference, any lines linked to blocked customers or vendors are omitted and not replicated from reference documents.
   - The system prevents the reset of clearing documents for accounts that are now blocked and does not allow the use of blocked data as templates for one-time account items.
   - Specific system messages are issued for blocked data (F5A451, F5351, F5A452).

4. Technical Details:
   - The support for simplified deletion is made possible with release SAP_FIN 617, Support Package 5, using the archiving object FI_DOCUMNT and its associated ILM object.

5. Other terms mentioned: Various technical terms and transactions are listed that relate to the FI document management, as well as terms associated with the end of the data's purpose (EoP).

6. References: There are no additional references linked within this SAP Note beyond the mention of SAP Note 2007926 for detailed description of the ILM deletion/blocking procedure.

This summarization focuses on highlighting the key functionalities enabled through the SAP Note 2011073 for blocking and deleting customer/vendor data in compliance with data lifecycle management policies.