SAP Note 1599168 addresses an issue relating to the reorganization of receivables and payables in the context of profit center (PRCTR) and segment (SEG) reorganization within New General Ledger Accounting (NewGL) in SAP. Users may encounter error message FAGL_REORGANIZATION 544 during this process.

**Symptom:**
When working with receivables or payables that have invoice-related items, such as partial payments and residual items, these items should normally be reorganized collectively. SAP performs a simulation of document splitting with extended attributes to ensure this collective reorganization. However, due to a program error, the simulation may fail for some invoice-related items, causing them to be displayed incorrectly in the reorganization plan. As a result, the whole payable or receivable might not be reorganized as one with all its related items, and the system incorrectly issues the error message FAGL_REORGANIZATION 544.

**Other Terms:**
Terms associated with this note include:
- Profit center reorganization
- Segment reorganization
- New General Ledger Accounting (NewGL)
- Realignment
- Reorg (reorganization)

**Reason and Prerequisites:**
The cause of the issue is a program error within the SAP system.

**Solution:**
The SAP Note advises implementing the correction instructions to resolve this issue. However, the note does not specify what these instructions are.

**References:**
This note refers to two other composite SAP Notes:
1. **SAP Note 1627018:** It provides guidance on segment reorganization and directs users to check related notes for solutions.
2. **SAP Note 1471153:** It offers information about profit center and Funds Management reorganization and advises users to refer to related notes for more details.

In summary, SAP Note 1599168 provides guidance on a specific error (FAGL_REORGANIZATION 544) that may occur during the reorganization of receivables or payables related to invoice items in the context of profit center or segment reorganization and the note instructs to implement correction instructions without explicitly stating them within the note.