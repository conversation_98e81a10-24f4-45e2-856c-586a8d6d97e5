SAP Note 1854561 describes enhancements to the authorization trace functionality, which allows users to set filters to limit recording to specific authorization checks that meet filter conditions. The authorization trace can be activated or deactivated using the profile parameter auth/authorization_trace, which can be configured in RZ11 as detailed in SAP Note 543164.

The trace collects authorization check data across all clients and users, storing it in a database table which supports the maintenance of authorization default values. Notably, certain transactions do not collect trace data, either due to their generic nature or because they're listed as exceptions. Examples include SE37, SE38, SA38, and others.

Kernel patch from SAP Note 1872459 adds SE24 to the exception list but also ignores the exception list when using trace with a filter. The filter is for examining particular scenarios and usually starts with an empty trace database table.

The values for the parameter auth/authorization_trace can be:
- N: Trace deactivated
- Y: Trace activated
- F: Trace activated with a filter, configurable via transaction STUSOBTRACE as per SAP Note 1847663, for investigating special scenarios with reduced data volume and performance impact.
- Blank: Activated in SAP systems and deactivated in customer systems based on system type; this is the default.

The note emphasizes the performance hit when activating the trace without filters and provides guidance on installing the kernel patch and applying ABAP corrections contained in Support Packages. However, setting auth/authorization_trace to F has no effect without setting filters using STUSOBTRACE.

References to other related notes are included, particularly regarding the significance and use of the profile parameter auth/authorization_trace (543164), the enhancement of class CL_START_AUTH_CHECK (1872459), guidelines for creating authorizations for system users during IDoc processing (1868691), enhancements to role menu maintenance in PFCG (1861370), filtering options for authorization trace recording (1847663), and general information about the SAP Kernel 721 (1728283).