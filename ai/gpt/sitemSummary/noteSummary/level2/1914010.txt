SAP Note 1914010 addresses various restrictions and differences when using the MRP Live planning tool on SAP HANA through transaction MD01N, compared to the classic MRP (Material Requirements Planning) process. Below is a summary of the key points:

**Symptoms and Differences:**
- Users may notice different behaviors between MRP Live and classic MRP.
- MRP Live does not support certain functions such as MRP lists (MD05).
- Some master data constellations cause automatic routing of materials from MRP Live to classic planning.
- BOM (Bill of Materials) explosion might vary between the two methods if the BOM includes parameter effectivity.
- External system integration (e.g., APO, IBP, MES) is not present in MRP Live.
- MD01N does not automatically redirect materials to classic MRP under some conditions, necessitating manual settings.

**Performance and Logic Placement:**
- MRP Live on HANA is designed for improved performance and is enabled through SQL-Script (AMDP) that runs directly on the HANA database server.

**Differences in Planning Results:**
- There may be discrepancies in planning results for materials between MRP Live and classic MRP, especially concerning lot-sizing procedures, number assignment, and BOM selection practices.

**Restrictions:**
- MRP Live on HANA has multiple restrictions, including unsupported lot-sizing procedures, scheduling agreements with certain document types, production version limitations, and planning of materials with special characteristics like parameter effectivity.
- Some materials will not be planned using MRP Live and require a manual setting to be planned using classic MRP logic.
- Other restrictions encompass issues such as forecasting, coverage profiles, discontinued materials, safety stock, direct procurement, characteristic-based forecasting, and limitations with MES and APO integration.
- Customizing settings such as "Planning File Entry for Termination" are not considered by MRP Live.

**SAP HANA-Specific Notes:**
The note references other SAP Notes that are related, which discuss more particular concerns such as parameter effectivity material planning, implementations for Variant Configuration, BOMs, Routings, Production Versions, and restrictions for the ERP add-on for embedded production planning and detailed scheduling (PP/DS).

**Conclusion:**
Overall, SAP Note 1914010 provides an overview of the functional limitations within MRP Live on HANA and how it differs from classic MRP. Organizations need to evaluate those differences and limitations, as well as the solutions presented, to plan materials effectively using MRP Live or decide when to revert to classic MRP. The note is a comprehensive guide to ensuring an optimal configuration for performance while considering the functionalities that need to be manually adjusted or are not available in the MRP Live environment.