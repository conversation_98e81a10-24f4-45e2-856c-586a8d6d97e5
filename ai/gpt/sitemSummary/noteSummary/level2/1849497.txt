SAP Note 1849497 discusses enhancements to the DataStore objects (DSOs) for systems using SAP HANA. It informs that with the implementation of the specified advance corrections or the import of certain Support Packages, it is no longer necessary to convert standard DSOs into HANA-optimized DSOs to achieve enhanced activation performance.

Key points from the note:

- Previously, there was a need to convert standard DSOs into SAP HANA-optimized DSOs for improved performance by changing the table layout and using a calculation view for the change log.
- After the implementation of advancements noted in this SAP Note, all standard DSOs will use a HANA-optimized activation and rollback process with comparable performance to previously optimized DSOs. This new method saves the change log in a transparent table, eliminating the need for layout conversion.
- The note provides recommendations to convert existing HANA-optimized DSOs back to classic DataStore objects for standardization purposes, citing SAP Note 1849498 for detailed procedures.
- Creating SAP HANA-optimized DSOs is no longer possible once this SAP Note is implemented, although existing optimized DSOs are still supported.
- SAP Note 1767880 is referenced for the concept of non-active data, which aids in keeping memory consumption at favorable levels.
- Solutions include importing the appropriate Support Packages for different versions of SAP NetWeaver BW:
  - For NW BW 7.30, import SP 10 (SAPKW73010) after release per SAP Note 1810084.
  - For NW BW 7.31, import SP 09 (SAPKW73109) after release per SAP Note 1847231.
  - For NW BW 7.40, import SP 04 (SAPKW74004) after release per SAP Note 1853730.
- In urgent cases, advanced correction instructions can be implemented.
- Users should read SAP Note 1668882 regarding transaction SNOTE before proceeding with advanced corrections.
- Some additional relevant information might be found in the preliminary versions of the SAP Notes mentioned above.

This note is significant for customers using SAP HANA, as it streamlines the performance optimization process of DSOs and simplifies the system landscape by eliminating the need for specialized HANA-optimized DSOs. It aims to provide similar performance benefits with a standard approach, which facilitates easier maintenance and standardization across the SAP BW system.