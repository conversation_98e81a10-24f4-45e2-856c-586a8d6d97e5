SAP Note 1960085 pertains to the action of generating kernel traces within the context of SAP Screen Personas. This note appears to be quite succinct and only addresses a specific situation when an SAP Screen Personas session needs to produce a kernel trace for debugging or monitoring purposes.

The symptom addressed by the note is simply the requirement for a kernel trace to be written during an SAP PERSONAS operation. Kernel traces are useful for identifying and understanding system behavior at a low level, especially useful in scenarios where standard error handling doesn't give enough information to diagnose a problem.

The solution provided is straightforward: it instructs the user to implement a display and work process (disp+work) that is at the patch level specified in the "Support Packages & Patches" section. However, the specifics of the patch level required are not included in the summary you provided.

No references to other SAP Notes or documentation are given in this note, which implies that this note is either self-contained or that the relevant patch level details would normally be found elsewhere, such as directly within the user's SAP Support Portal.

In summary, SAP Note 1960085 directs users to update their disp+work process to a specific patch level in order to enable kernel tracing for SAP Screen Personas. No further context, workaround, or detailed instructions are provided within the scope of the note summary you have provided.