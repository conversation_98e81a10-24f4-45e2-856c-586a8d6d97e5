SAP Note 1881515 addresses an issue where users are unable to change the assignment of a profit center in a cost center during the profit center reorganization process, which results in error message FAGL_REORGANIZATION 601. This error indicates that the change cannot be made and references SAP Note 1684679 for more information.

The note outlines that while the messages FAGL_REORGANIZATION 601, 603, and 605 can technically be converted from error messages to warning messages, which may seem like a possible workaround, it warns that doing such could lead to inconsistencies in the system. Therefore, SAP expressly does not accept any liability for issues that may arise from changing these messages and recommends against it.

Instead, the solution proposed in the note advises that when the business function FIN_GL_REORG_1 is activated, the profit center assignment should only be changed using the reorganization tool. If only the cost centers need to be reorganized, a reorganization plan should be created that includes only the cost centers, paying attention to the reorganization date and the correct old/new profit center combination.

To support users in achieving this, the note provides guidance on including cost centers in the reorganization plan, as described in the business function documentation.

Additionally, the note prompts users to implement source code corrections by importing a Support Package or by following the correction instructions. For further assistance, the note references follow-on SAP Note 1900735.

In summary, SAP Note 1881515 provides guidance on resolving the error FAGL_REORGANIZATION 601 that prevents the reassignment of profit centers within cost centers, emphasizing that care should be taken to use the reorganization tool correctly and consult SAP Development Support if planning to change error messages into warnings.