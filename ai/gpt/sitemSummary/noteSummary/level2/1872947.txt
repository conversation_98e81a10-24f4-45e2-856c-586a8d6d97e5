SAP Note 1872947 addresses an enhancement made to the Control Framework of the SAP Graphical User Interface (SAPGUI). The primary symptom that this note tackles is the repetitive resending of all screen elements to the SAPGUI after a system event of the Control Framework is handled. This behavior was present before the enhancement.

The reason for the enhancement lies in the introduction of new kernel functions that allow for a more efficient handling of Control Framework events. As a prerequisite to using this enhancement, users must have a dispatcher and work process (disp+work) component that is updated to the patch level specified in the note's "SP Patch Level" section; however, the specific patch level is not mentioned in the excerpt provided.

The solution outlined in the note is straightforward: users are advised to update their SAP systems to use a disp+work with the necessary patch level in order to take advantage of the enhancement. This will prevent the unnecessary resending of screen elements, presumably improving performance and user interaction with SAPGUI.

The note does not reference any other notes or documentation, suggesting that it is self-contained in terms of the information required to understand and apply the enhancement described.