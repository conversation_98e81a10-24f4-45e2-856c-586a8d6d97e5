SAP Note 1919888 addresses a common issue faced in debugging applications within an SAP production system, where the user of the application lacks the necessary authorizations and expertise to debug, and the support user who can debug does not have the required application authorizations.

Summary of the note:

**Symptom**
- Need to debug applications in production systems where the application user lacks debugging authorizations and expertise, and the support user has insufficient application authorizations.

**Other Terms**
- TPDA (third party debugging authorization), external debugging.

**Reason and Prerequisites**
- The function to allow a support user to debug another user's application was not implemented.

**Solution**
- A new method to activate debugging for another user using the OK code “/hext user= <user_name>” has been introduced.
- Specific conditions must be met to activate this feature:
  - The support user must have general debugging authorizations and the ability to debug the application user's programs.
  - The support user must have set breakpoints for the application user's session.
  - An active SAP GUI logon from the support user where breakpoints were set is required.
  - For SAP_BASIS 740 and above, specific patch levels must be present.

**Steps to Use the Feature**
1. The application user and support user arrange a debugging session, often remotely.
2. The support user sets the breakpoints and prompts the application user to start the debugging session.
3. The application user starts the application and enters “/hext user=B” to activate the debugger for support user B.
4. Upon hitting a breakpoint, the system halts and allows support user B to debug in a separate session.
5. The debugging session is terminated by either exiting the application or using the "/hx" OK code.

**Advantages of This Approach**
- Debugging by a third party can only be initiated by the application user, ensuring control and consent.
- The application user does not need any debugging authorization.
- The support user doesn't need application authorizations.
- All debugger actions by the support user are logged for accountability.
- Physical presence of the two users is not required, remote contact suffices.

**References**
The note references SAP Note 2099670, which provides corrections for issues in the ABAP Debugger, and SAP Note 1788514, which clarifies and corrects the authorization checks related to setting external breakpoints for debugging. These notes address issues related to the correct functioning of the debugger and authorization checks, and provide the context and prerequisites necessary to implement the solution of the main issue discussed in SAP Note 1919888.