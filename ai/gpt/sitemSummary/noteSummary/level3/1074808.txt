SAP Note 1074808 outlines the requirements for executing the SAP Change and Transport Analysis Session, which analyzes transport behavior in an SAP system. Here's a summary of the key points:

- Applicability: This note applies to SAP Solution Manager systems on release 7.0 or 7.10 with support package stack 7 or lower. For newer versions, refer to SAP Note 1873719.
- Purpose: To prepare for running the report /sdf/cmo_collect_data_new on the production system, which collects data from production, development, and QA systems.
- Prerequisites: 
  - Necessary RFC connections from production to development and QA systems.
  - Required authorizations for the data collection user across all systems involved in the transport landscape.
  - Certain support packages for Solution Tools Plug-Ins should be implemented in satellite systems (DEV, QAS, PRD).
  - Current support package level for service definitions in SAP Solution Manager.
  - Implementation of specific SAP Notes listed below in the managed systems.
  - Verification of requirements via the RTCCTOOL report on the production system.
- Required Solution Tools Plug-Ins:
  - ST-PI 2008_1* with at least support package 2.
  - ST-A/PI 01M* or higher based on the specific versions.
- Specific SAP Notes for ST-PI and ST-A/PI versions are listed, each with associated support package levels.
- Required authorizations in managed systems are contained in roles SAP_S_SWCM and SAP_SDCCN_ALL, to be uploaded and assigned using transaction PFCG.
- TMS Configuration: A basic setup of the Transport Management System is needed with TMSADM and TMSSUP connections among DEV, QAS, and PRD systems.
- SAP Solution Manager requirements:
  - Software component ST-SER 2010.1 as part of support package stack 23.
  - Activation of dynamic content update (refer to SAP Note 1143775).
  - Users with roles SAP_SMWORK_SDA and SAP_SOLMAN_ONSITE_COMP (refer to SAP Note 872800).
- Service connections and users need to be established to the development, QA, production systems, and to the SAP Solution Manager.

Customers should implement the specified configurations, plug-ins, notes, and authorizations to properly prepare for an effective Change and Transport Analysis Session.