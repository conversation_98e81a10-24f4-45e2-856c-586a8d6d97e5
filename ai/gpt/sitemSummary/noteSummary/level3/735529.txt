SAP Note 735529 addresses changes in the way material numbers are displayed and processed after installing the DIMP (Defense Industry Solutions) or DI Add-On to a standard SAP R/3 system. The note clarifies the differences between the standard SAP system and the DIMP system regarding material numbers and answers frequently asked questions relating to issues such as visible differences on the screen, customer reports, and handling of internal and external material numbers.

Key points from the note include:

1. Visible differences: The display width for material numbers on screens and reports is now determined by customizing settings, where previously it was always fixed at 18 characters. If the material number length is set to less than 40 characters in customizing, gray areas may appear on screens.

2. Customer reports: Custom reports need to be adjusted to accommodate longer material numbers if not using the ALV display method. Related instructions are provided in SAP Note 455492.

3. Internal and External material numbers: Distinctions are made between external material numbers (up to 40 characters, input by users) and internal material numbers (18 characters long but displayed with an output length of 40 characters). There’s a 1:1 relationship between them, and conversions are done through conversion exits.

4. Inconsistent material numbers: Error messages MGVNUM 002 or MGVNUM 005 indicate material number inconsistencies. The repair program MGVREPMATID can resolve these issues, with additional details in SAP Notes 524661 and 545839.

5. Internal material numbers with exclamation marks: Material numbers may have exclamation marks and can either be correct, depending on customizing settings, or a known issue addressed by SAP Note 1453646. SAP Note 803231 discusses related selection issues.

6. Multiple selection of material numbers: When developing custom programs that involve the selection of multiple material numbers, code should be extended to consider the increased length of material numbers or inclusion of MPNs.

7. Special characters in material numbers: Certain special characters are prohibited when creating material numbers, with instructions on how to handle occurrences of these characters in MPNs.

8. Manufacturer part number (MPN) conversion: Special features of MPNs are discussed, referencing SAP Note 615290.

9. Classification system: The use of long material numbers or MPNs is not compatible with the classification system, with additional information available in SAP Note 554086.

10. General restrictions: There are constraints when using long material numbers or MPNs, particularly with integration in CO and FI, and support in LIS. Problems in these areas should be reported to SAP.

11. Connection to BW: Long material numbers can be transferred to the BW system following the implementation of SAP Note 906913.

The note provides guidance for navigating changes brought by the DIMP Add-On and ensuring compatibility with various SAP system components and custom developments.