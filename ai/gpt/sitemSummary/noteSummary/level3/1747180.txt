SAP Note 1747180 provides guidance for implementing SMTP (Simple Mail Transfer Protocol) with TLS (Transport Layer Security) encryption and SMTP authentication for SAP systems. The note is relevant for systems that are on at least version 7.31 Support Package 6 with a 7.21 kernel or above.

Key points of the SAP Note:

- The feature for using SMTP with TLS and SMTP authentication is available from SAP NetWeaver 7.31 Support Package 6 and with a 7.21 kernel.
- For troubleshooting, reference is made to SAP Note 1702785.
- Since the online documentation for this feature wasn't available at the time, the note provides a description of how to configure it.
- Configuration involves setting the parameter icm/server_port_<xx> (AS ABAP) with options for TLS level and authentication mechanisms.
- Two examples of configuration strings are provided: Example A, which configures a required TLS encryption with no authentication, and Example B, which includes both TLS encryption and authentication requirements.
- Details on the TLS configuration options are outlined:
  - "0" meaning no TLS prompt,
  - "1" meaning the server will ask for TLS but will fall back to non-encrypted if not possible,
  - "2" meaning TLS is mandatory, and the connection will be terminated if not possible.
- Authentication mechanisms options provided are:
  - "PLAIN" for user/password authentication,
  - "EXTERNAL" for certificate-based authentication,
  - "NONE" for no authentication.
- AUTHUSERS option allows for specifying up to 10 authorized AS ABAP users.
- Notes on creating users for PLAIN authentication and assigning certificates for EXTERNAL authentication are included.

This SAP Note is vital for ensuring secure email communication via SMTP by using TLS for encryption and setting up the authentication of users in an SAP environment.