SAP Note 1809499 addresses a performance issue when using the BAPI GetMember with non-compounded keys (properties with the prefix "8"). The symptom observed is long runtimes due to a lack of optimization when restrictions are defined for the non-compounded key. Although the SAP Note 1508719 introduced this property and allowed for restrictions, only 'EQUAL' (EQ) and 'INCLUDING' (I) restrictions are being processed—other types like 'Greater Than' (GT), 'Between' (BT), or 'Excluding' (E) are ignored.

Moreover, when 'EQUAL' or 'INCLUDING' restrictions are used, an optimization is missing, leading to all members of an MDX dimension being read and only filtered afterward, causing inefficiency.

The solution provided in this SAP Note is an optimization to read significantly fewer members of an MDX dimension by properly applying restrictions for non-compounded keys. Users are advised to import the respective Support Package for their version of SAP NetWeaver BW that includes the optimization:

- For BW 7.00, import SP31 as described in SAP Note 1782745.
- For BW 7.01, import SP14 as described in SAP Note 1794836.
- For BW 7.02, import SP14 as described in SAP Note 1800952.
- For BW 7.11, import SP12 as described in SAP Note 1797080.
- For BW 7.30, import SP10 as described in SAP Note 1810084.
- For BW 7.31, import SP08 as described in SAP Note 1813987.
- For BW 7.40, import SP03 as described in SAP Note 1818593.

In urgent cases, correction instructions can be implemented in advance. However, it is important to first read SAP Note 875986 regarding the use of transaction SNOTE for implementing corrections.

The note indicates that, to provide information in advance, the above-mentioned Notes may be available before the actual release of the Support Package. If this is the case, the Notes will contain "Preliminary version" in the short text.

In summary, this SAP Note offers an optimization solution for long runtimes experienced with the BAPI GetMember when non-compounded keys are used. It also outlines the necessary Support Packages for different versions of SAP NetWeaver BW and advises on the handling of urgent issues through advance correction instructions.