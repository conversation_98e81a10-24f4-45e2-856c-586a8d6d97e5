SAP Note 113240 addresses an issue in SAP where users face a limitation when creating quantity-dependent material volume rebates. Specifically, the system only allows the use of units of measure for the rebate that are maintained as alternative units of measure for the material in question. If a user attempts to use a unit of the same dimension that is not maintained but could be convertible (e.g., liters vs. cubic meters), the system will generate error message VK070 and reject the entry.

The reason for this problem is that the system conducts a check to ensure that the unit of measure specified in the quantity-dependent condition is one of the alternative units recorded for the material. However, this check is too restrictive because it doesn't consider that units of the same dimension can be converted.

The note provides a solution for this issue: starting with Release 4.5B, the overly restrictive check has been corrected. In addition, users can implement a simple source code change in advance of the release to resolve the issue. The note implies that the corrections to the source code are provided within the note or through related documentation. 

Important keywords for this note are alternative unit of measure, error message VK070, quantity-dependent, material volume rebate, and unit conversion.