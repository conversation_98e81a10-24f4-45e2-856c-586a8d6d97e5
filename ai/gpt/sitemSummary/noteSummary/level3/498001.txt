SAP Note 498001 describes a scenario in the SAP system where the 'Vendor' key figure (LIFNR) in the DataSource 2LIS_03_BF is incorrectly showing the account number of the receiving vendor (EMLIF) instead of the expected vendor account number as entered in the document item (MSEG table). This issue can also be seen in standard analyses of Inventory Controlling (INVCO) where characteristics updated from LIFNR in the MCMSEG communication structure are showing EMLIF values.

The note clarifies that this is not an error, but rather intended system behavior based on the original design. It explains that for Business Information Warehouse and Logistics Information System statistics, only one field per document item is provided for the vendor. Because vendor number is a key selection and breakdown criterion, transferring additional vendor fields like EMLIF or LLIEF, which may not always be used, into the statistics is not considered practical.

In instances where a document item includes multiple vendor-related fields and a 'receiving vendor' is entered (as in third-party subcontracting processes), this vendor is seen as more relevant for MRP (Materials Requirements Planning) and materials management than the original vendor. Consequently, in such cases, the system deliberately replaces the value of the LIFNR field with that of the EMLIF field.

The note does not provide a direct solution but implies that this behavior is by design and there are no plans to change this functionality, thereby suggesting that users should adjust their expectations and reporting practices accordingly.