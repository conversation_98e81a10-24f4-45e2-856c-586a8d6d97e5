SAP Note 2226122 addresses the deprecation of the Multi-level Budget Structure (MBS) functionality within the Public Sector Management Funds Management Budget Control System (PSM-FM-BCS) in SAP S/4HANA. The key information from this note can be summarized as follows:

- Symptoms: This note is relevant for those who have implemented custom coding using the MBS objects.

- Reason: MBS is no longer supported in SAP S/4HANA. The budget maintenance functionality can still be used in BCS, but without the MBS.

- Solution: Organizations using MBS need to find new ways to meet their requirements within BCS without using MBS. The note does not provide a standard solution but suggests contacting SAP Product Management to discuss strategies for migrating away from MBS. It also advises against activating the business functions related to MBS (PSM_FM_BCS_MBS and PSM_FM_BCS_MBS2) for new customers of S/4HANA.

- Impact: Existing BCS budgeting documents created with MBS will still be valid and unchanged, meaning no data migration is required and custom code that reads from BCS budget document data does not need adjustment. However, the hierarchy definition and consistency rules tied to MBS will no longer be available.

- Additional Information: Customers are advised to use the transport piece list SI_PSM_MBS to identify any unsupported ABAP development objects in their custom code and adapt their code accordingly.

It references SAP Note 2190420, which provides broader recommendations for adapting custom ABAP code for S/4HANA, including the use of the ABAP Test Cockpit (ATC) for S/4HANA-specific checks and a need for a thorough testing and developers' assessment for adapting the code based on the new system's requirements.