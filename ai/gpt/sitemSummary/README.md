# Simplification item summarize 

首先用之前爬note的脚本把所有sitem相关的note都爬到json文件中， 脚本同样为Jay提供的[note-exporter](https://github.tools.sap/i/note-exporter)

当前所有的数据均来源

note number来源于sql：
```
select distinct sapnote from CALM_IMP_RC_CONTENT_SITEMNote where NoteType = 'BI' and loevm = ''
```


通过sql把simplification item和相关的activities从表中导出为csv：

sitem
```SQL
select item.GUID, titl.title, item.textId, note.sapnote from 
   CALM_IMP_RC_CONTENT_SITEMITEM as item 
   left join CALM_IMP_RC_CONTENT_SITEMNote as note on item.GUID = note.GUID and note.NoteType = 'BI' and note.loevm = '' 
   left join CALM_IMP_RC_CONTENT_SITEMTitle as titl on item.guid = titl.guid 
   where item.loevm = '' and item.procstatus = 'R'
   group by item.GUID, titl.title, item.textId, note.sapnote
```
activity:
```SQL
select act.GUID, typ.svalue as actTitle, pha.svalue as actPhase, req.svalue as actCondition, ACTADDINFO from
   CALM_IMP_RC_CONTENT_SITEMACTIVITY as act
   left outer join CALM_IMP_RC_CONTENT_SITEMDKV as typ on act.actType = typ.skey
        and typ.spras    = 'E'
        and typ.sdomain  = 'ACTIVIT_YTYPE_N'
        and typ.loevm    = ''
    left outer join CALM_IMP_RC_CONTENT_SITEMDKV as pha on  act.actPhase = pha.skey
        and pha.spras     = 'E'
        and pha.sdomain   = 'ACTIVIT_PHASE_N'
        and pha.loevm     = ''
    left outer join CALM_IMP_RC_CONTENT_SITEMDKV as req on act.actReq = req.skey
        and req.spras   = 'E'
        and req.sdomain = 'ACTIVITY_REQUIRED_N'
        and req.loevm   = ''
```
通过python脚本读取两个csv和对应的note的json文件， 将每个item的信息组织成如下格式,并保存到数据库的sitem_summary表的itemInfo字段，相关代码：[buildPrompt.py](buildPrompt.py)
```
    ID: SI010: CRM
    Title: Loyalty Management not supported in S4CRM
    Business Impact Note: 2692789
    
    2692789 - Loyalty Management not supported in S/4HANA
    
    <h3 data-toc-skip class="section" id="Symptom">Symptom</h3>
<p>You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. The following information on changed or deprecated functionality is relevant in this case.</p>
<h3 data-toc-skip class="section" id="Other Terms">Other Terms</h3>
<p>S4CRMTWL</p>
<h3 data-toc-skip class="section" id="Solution">Solution</h3>
<p><strong>Description</strong></p>
<p>Loyalty Management is not supported in S/4HANA.</p>
    
    Activities:
      No Activities
```
之后从数据库中将isNew字段为false的item查询出来，每条根据itemInfo和设定好的提示词发送给GPT进行总结，结果存储到itemSummary字段，相关代码[get_summary.py](get_summary.py)

当前使用的提示词如下：

"Assume you are an SAP expert, particularly adept at helping customers convert an SAP ERP 6.0 system to SAP S/4HANA. You are familiar with two proprietary terms. The first, SAP Notes, which describe a software issue and its solution, including the symptoms, the cause of the error, and the SAP release and support package level in which the error occurs. An SAP Note may also include workarounds and links to support packages that solve the problem. The second, Simplification Item, helps customers prepare for the transition to SAP S/4HANA and SAP BW/4HANA by providing a description of all relevant changes that might have an impact when converting from SAP ERP to SAP S/4HANA. A Simplification Item typically includes an ID, Title, Business Impact SAP Note, and Activities. I will provide you with a Simplification Item, including its corresponding Business Impact SAP Note, Activities, and other information, and some Simplification Item may have no activity. Your task is to first understand this Simplification Item, then give a summarize about this item only based on the information I give."

sitem_summary表中的数据已经导出到[sitem_summary.csv](sitem_summary.csv)

**更新：**

Business Impact Note 可能有reference to的其他SAP Note 或help document，本次更新将 References 也组织到提示词中。

reference to的其他SAP Note 也有可能有自己的references，考虑提示词限制以及实际note增长速率，读取三层reference，note数量增长约10倍。
我们认为选取三层reference已经足够。

提示词中加入References 之后导致有一些有很多references 的会超出GTP4的token长度限制。这里使用note的总结来替代note原文。

note的总结，以一个简单的例子说明，Business Impact Note 1 ref to SAP Note 2，SAP Note 2 ref to SAP Note 3，SAP Note 3 ref to SAP Note 4.
提前先总结SAP Note 4，然后基于SAP Note 4的总结结果，总结SAP Note 3，然后基于SAP Note 3的总结结果，总结SAP Note 2.
最后使用在提示词中的Reference就是SAP Note 2的总结结果。

提示词具体示例可在[sitem_summary_with3level_ref.csv](sitem_summary_with3level_ref.csv)中找到。

