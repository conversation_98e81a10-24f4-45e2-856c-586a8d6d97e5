import json
import os
import time
from selenium import webdriver
from bs4 import Beautiful<PERSON><PERSON>p
import requests
import io
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>

def get_text_from_pdf(url):
  r = requests.get(url)
  file = io.BytesIO(r.content)
  pdf = PdfReader(file)
  text = ''
  for page in pdf.pages:
    text += page.extract_text()
  return text

def get_text_from_html(url):
  browser.get(url)
  time.sleep(10)
  html = browser.page_source
  soup = BeautifulSoup(html, 'html.parser')
  return soup.get_text()

browser = webdriver.Chrome()

with open('other_ref_result_map.json', 'r') as f:
  map = json.load(f)

output_folder = "../refDocs"

for key, values in map.items():
  for value in values:
    filename = value['RefTitle'].replace('/', '_').replace('\\', '_').replace(':', ' ')
    filename = f'{filename}.txt'
    value['filename'] = filename
    if value['RefUrl'].endswith('.pdf'):
      page_content = get_text_from_pdf(value['RefUrl'])
    else:
      page_content = get_text_from_html(value['RefUrl'])
    
    filepath = os.path.join(output_folder, filename)
    with open(filepath, 'w') as txt_file:
      txt_file.write(str(page_content))
    print(f"Write to {filepath}")

with open('other_ref_result_map.json', 'w') as file:
    json.dump(map, file)

browser.quit()