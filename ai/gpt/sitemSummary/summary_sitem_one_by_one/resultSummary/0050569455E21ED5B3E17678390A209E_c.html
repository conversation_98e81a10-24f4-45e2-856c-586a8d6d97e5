<h3>SI8:MasterData_PM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267247">2267247 - S4TWL - Material Type SERV</a></strong></p>
<p><strong>Description:</strong></p>
<p>Material type "SERV" for services is introduced for Product Master in S/4HANA for simplification purposes. When you use material type SERV, some fields and departments that are irrelevant in S/4 are hidden from the screen. This gives all transactions relevant for material master, a leaner and simplified look.</p>
<p>The Product type SERV, as compared to the product type DIEN, has the product type group assigned (2-Services). SAP recommends that Cloud and On-Premise customers of S/4HANA use SERV over DIEN, particularly in scenarios such as <strong>lean service procurement</strong>. However, for scenarios where the subtleties of lean service procurement are not applicable, customers may use DIEN.</p>
<p><strong>Business Process Impact:</strong></p>
<p>A new material type SERV (Service Materials) is created with reduced user departments and fields in the classical transactions: MM01/MM02/MM03.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>Configurations required for SERV that are part of SET:</strong></p>
<ol start="1">
<li>Material type SERV(Table T134, delivery class: G)</li>
<ol start="1">
<li>This attribute of SERV is provided by SET content</li>
</ol>
<li>Screen sequence control(SPRO transaction: OMT3E</li>
<ol start="1">
<li>Table T133K "Influencing Factors: Screen Sequence No."  / Delivery Class: G </li>
<li> Tables T133S and T133T are also part of SET content</li>
</ol>
</ol>
<p><strong>Reference Notes:</strong> 2224251 - Material Master: Introduction of new Material Type SERV(Service Materials) in Transactions: MM01/MM02/MM03</p>