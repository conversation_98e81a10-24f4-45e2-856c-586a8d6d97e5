<h3>SI14: PROC_MM_IV_AUTH</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2271189">2271189 - S4TWL - Authorization Concept for Supplier Invoice</a></strong></p>
<p><strong>Description:</strong></p>
<p>The previous concept was based on checking header data against the item authorization object for all items. The system checked for all items if a user was allowed to complete certain activities in the plants. With the new concept, the behaviour of the system is more comprehensive. The new concept introduces a header authorization object that enables the system to perform the following authorization checks:
<ul>
<li>Check header data against the header authorization object (M_RECH_BUK). This authorization object checks the common activities a user performs for the header company code.</li>
<li>Check item data against the item authorization object (M_RECH_WRK). This authorization object checks the common activities a user performs for the plant for the respective item.</li>
</ul>
The new authority concept for supplier invoice enables a simplified handling especially for invoice items without PO reference.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>You must edit the user roles. Users working with supplier invoices, additionally need the authorizations based on M_RECH_BUK. The standard roles delivered with SAP S/4HANA contain this new authorization object. If the customer roles are derived from these standard roles, you must do the following:
<ul>
<li>Edit the values of the organizational levels for the new authorization object M_RECH_BUK.</li>
<li>Regenerate the profiles related to the roles using PFCG.</li>
</ul>
If you have created a role by yourself, you must manually insert the authorization object M_RECH_BUK into the role. You can do this preliminary work once SAP S/4HANA is available.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2242780">2242780 - Authorization Concept of Logistics Invoice Verification</a></p>