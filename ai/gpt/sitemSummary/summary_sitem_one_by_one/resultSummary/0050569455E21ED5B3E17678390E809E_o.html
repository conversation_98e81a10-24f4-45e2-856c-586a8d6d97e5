<h3>SI8: PROC_MM_PRICING</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2267442">2267442 - S4TWL - Pricing Data Model Simplification</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies. See the information provided in the Sales & Distribution area in simplification item "Data Model Changes in SD Pricing". On a basic level, this information is also relevant for the pricing in procurement.</p>
<p><strong>Business Process Impact:</strong></p>
<p>See simplification item Data Model Changes in SD Pricing.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>See simplification item Data Model Changes in SD Pricing. Carefully follow the instructions in SAP Notes 2188695, 2189301, and 2220005. Implementation of Custom Code Adaption should be done during the conversion project. This is mandatory. Data migration needs to be performed after the conversion project. This is also mandatory. Ensure Technical System Configuration is carried out during the conversion project as it is mandatory.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2188695">2188695 - WEGXX and STUFE fields are probably filled in the pricing table KONV</a>, <a href="https://me.sap.com/notes/2267308">2267308 - S4TWL - Data Model Changes in SD Pricing</a>, <a href="https://me.sap.com/notes/2189301">2189301 - Pricing table KONV has been enhanced with customer-specific fields</a>, <a href="https://me.sap.com/notes/2220005">2220005 - S/4 HANA: Data Model Changes in Pricing and Condition Technique</a>, <a href="https://me.sap.com/notes/2188735">2188735 - S4TC SAP_APPL - Checks for Pricing</a></p>