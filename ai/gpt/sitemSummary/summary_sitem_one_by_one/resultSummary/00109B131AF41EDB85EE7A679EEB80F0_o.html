<h3>SI21: AS_ABAP_WORKFLOW_AUTHORIZATION_CHANGES</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2979517">2979517 - ABAPTWL - Change of authorization checks SAP Business Workflow</a></strong></p>
<p><strong>Description:</strong></p>
<p>After an upgrade of your system you face authorization issues in reports or transactions of SAP Business Workflow. Especially this concerns helper reports or transactions like for example RSWD_MAINTAIN_USER_ATTR or RSWEQADMIN. Originally the concerned reports and transactions have been protected by an authorization check basing on the authorization group of the report. This check uses authorization object S_PROGRAM, which does not support checking the activity of a user action; that mean, no clear separation between display and change actions is possible.</p>
<p><strong>Business Process Impact:</strong></p>
<p>If you want to use the helper reports and transactions further, you have to change the authorizations of the user, which should be allowed to do so.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>From SAP_BASIS 756 onwards the authorization check by authorization object S_PROGRAM has been replaced by an authorization check on authorization object S_WF_ADM. This new authorization object has two authorization fields:
<ul>
<li>WF_GROUP</li>
<li>ACTVT</li>
</ul>
To a group all reports/transactions are assigned caring about the same topic. For example reports RSWF_SHOW_SWW_WIREGISTER (Registered Actions for Work Items) and RSWEQADMIN (Event Queue Administration) are both assigned to group 'EVENT'.
With the activity you can differentiate the actions, which are allowed to the user. For example by report RSWF_SHOW_SWW_WIREGISTER you can display the registered actions for work items, but you cannot change them. Therefore the display-activity is checked. But in report RSWEQADMIN several actions can be triggered, therefore it checks on the execute-activity.
Your roles for the user needing these reports or transactions need to be adapted accordingly.</p>
<p>Technical System Configuration during conversion project is mandatory. Adapt your roles and authorizations as described in SAP note 2979517.</p>