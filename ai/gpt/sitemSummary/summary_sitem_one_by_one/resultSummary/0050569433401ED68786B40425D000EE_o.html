<h3>SI8:CROSS_BC_CCM-BTC</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2318468">2318468 - S4TWL - JOB SCHEDULING</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case. SAP S/4HANA contains a new component which automatically schedules certain technical background jobs. Deleting or modifying such jobs manually (via transaction SM37) will not have a lasting effect since the automation mechanism will re-schedule these jobs with predefined attributes. Job Definitions can be customized or deactivated via transaction SJOBREPO. SAP S/4HANA Technical Job Repository takes care of scheduling (and de-scheduling) necessary standard jobs (as defined and delivered by SAP) automatically, without any user intervention. Nevertheless, in case of conflicts, it is possible to do customizations to job definitions.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>IMPORTANT: Before the upgrade/migration to SAP S/4HANA (i.e. in your original system), apply SNOTE correction from note 2499529. This correction ensures that automatic job scheduling of Technical Job Repository in the SAP S/4HANA system will be delayed even after the point in time when the technical upgrade is over. In other words, it ensures that no automatic job scheduling by Technical Job Repository occurs automatically after the conversion to SAP S/4HANA. Applying the note prevents for example that jobs automatically scheduled by Job Repository in SAP S/4HANA delete valuable data of your old system which the customer wants to retain after the technical upgrade phase.

IMPORTANT: After the upgrade/migration to SAP S/4HANA, apply the correction from note 2499529 also in your new system. This correction will ensure that automatic job scheduling by Technical Job Repository will commence once report BTCTRNS2 is executed by the conversion team. Be sure to have deactivated unwanted job definitions (for example SAP_REORG_JOBS) in SJOBREPO by that time.</p>
<p><strong>Reference Notes:</strong> 
<ul>
  <li><a href="https://launchpad.support.sap.com/#/notes/2499529">2499529 - Disable / Enable Job Repository scheduler</a></li>
  <li><a href="https://launchpad.support.sap.com/#/notes/2190119">2190119 - Background information about SAP S/4HANA technical job repository</a></li>
</ul>
</p>