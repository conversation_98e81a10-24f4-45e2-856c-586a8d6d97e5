<h3>SI3: GSFIN_LOCPTFM</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2469984">2469984 - S4TWL: Availability of Portuguese Public Sector Funds Management in SAP S/4HANA On-Premise 1709</a></strong></p>
<p><strong>Description:</strong></p>
<p>This SAP Note is relevant for you when your starting release for the system conversion is a SAP ERP release or a SAP S/4HANA Finance release, where you have the Localization for Portuguese Public Sector solution implemented in your system via transport solution or via LOCPTFM add-on.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The SAP LOCPTFM Add-on (software component LOCPTFM) has been integrated into SAP S/4HANA, as of release SAP S/4HANA 1709. However, the Portuguese Public Sector Funds Management features are available as of SAP S/4HANA 1709 FPS01 with the following restrictions:
<ul>
<li>As a Customer who used the integration between Logistics documents and Funds Management with LOCPTFM and is planning to move to SAP S/4HANA, please note that in SAP S/4HANA, the Funds Management integration with Materials Management has been enhanced to accommodate local version Portugal requirements. However, a document chain initiated with the LOCPTFM Add-on cannot be migrated to SAP S/4HANA and must first be closed in the Add-on system. This restriction does not apply to customers with new installations, or new document chains initiated in SAP S/4HANA.</li>
</ul>
For new processes (starting with the pre-commitment document) the new functionality can be used but not for an unfinished document chain.
</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>For detailed information on the system installation/conversion of the Portuguese Public Sector Funds Management, available in SAP S/4HANA 1709 FPS01, please see SAP Note <a href="https://me.sap.com/notes/2595915" target="_blank">2595915</a>, <em>Customer Installation/Conversion Procedures for Portuguese LOCPTFM in S/4HANA On-Premise.</em></p>
<p>Please note that with the integration of SAP LOCPTFM into SAP S/4HANA 1709, some ABAP objects of the respective LOCPTFM packages have become obsolete. Therefore, run the ABAP Test Cockpit (ATC) using the latest simplification item DB content and remove all detected usages of the LOCPTFM objects from your custom code.</p>
<p>
<strong>Activities:</strong>
<ul>
  <li><strong>Data cleanup / archiving:</strong> Document chains initiated with the LOCPTFM Add-on cannot be migrated to SAP S/4HANA and must first be closed in the Add-on system - valid up to SAP S/4HANA On-Premise 1709 FPS0. Afterwards MIGRATION REPORTS are available. The execution of report RPPFM_CHECK_ANLU is mandatory, prior to the migration.</li>
  <li><strong>Data correction:</strong> Document chains initiated with the LOCPTFM Add-on cannot be migrated to SAP S/4HANA and must first be closed in the Add-on system - valid up to SAP S/4HANA On-Premise 1709 FPS0. Afterwards MIGRATION REPORTS are available.</li>
  <li><strong>Miscellaneous:</strong> Backup the data contained in PFM* fields in the PFM_ANLU04 append structure prior to the migration. bear in mind that the PFM_ANLU04 append structure contained in the ANLU table may contain data which will be lost during migration as this append structure does not exist in SAP S/4HANA systems. If integration with PS: Business Function PSM_FM_LOC_PT switches an append structure to table PRPS, therefore, if you have the LOCPTFM Add-on installed in your system, the new Business Function PSM_FM_LOC_PT and the related switches must be created and activated before conversion to SAP S/4HANA.</li>
  <li><strong>Custom Code Adaption:</strong> Dependencies to the PFM_ANLU04 structure and any data elements or domains it contains; dependencies to the PFM_EXITS_AA function group. If you are using any of the development objects above, adapt your coding to ensure that these objects are no longer included in your custom code.</li>
  <li><strong>Customizing / Configuration:</strong> Update profile PFM352, Accounting Standardization System (ASS) , must be active prior to the upgrade</li>
  <li><strong>Customizing / Configuration:</strong> Customizing as per note 2595915 - Customer Installation/Conversion Procedures for Portuguese LOCPTFM in SAP S/4HANA On-Premise</li>
  <li><strong>Data migration:</strong> Execute Report PTFM_MM_MIG_EFD and Report PTFM_MM_MIG_PO. After the execution of the reports, the PO will be available for further processing and the localization fields will be visible according to the new mapping.</li>
  <li><strong>User Training:</strong></li>
</ul>
</p>