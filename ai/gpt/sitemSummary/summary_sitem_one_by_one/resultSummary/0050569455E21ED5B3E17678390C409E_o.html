<h3>SI9: SD_ODP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267351">2267351 - S4TWL - Operational Data Provisioning</a></strong></p>
<p><strong>Description:</strong></p>
<p>Operational Data Provisioning in SD (see SAP Help: <a href="http://help.sap.com/erp2005_ehp_06/helpdata/en/ad/61d700ac0c468ebf1db6c8996edc89/content.htm?frameset=/en/ad/61d700ac0c468ebf1db6c8996edc89/frameset.htm&amp;current_toc=/en/67/487f71731c40a0835653b13b32c4d8/plain.htm&amp;node_id=53&amp;show_children=false" target="_blank">Link</a>) is not available in SAP S/4HANA.</p>
<p>Simplification in SD Analytics follows the overall analytics strategy in SAP S/4HANA (see Simplification Item SD Analytics): The SAP S/4HANA Analytics approach is based on ODATA and Open CDS (also known as ABAP-managed Core Data Services (CDS)), which sit directly on top of the original database. Corresponding analytics content will be provided in SAP S/4HANA. This content will grow over time.</p>
<p>Successor: HANA Analytics</p>
<p><strong>CDS Basic Views Representing the Core Entities in SD</strong></p>
<p>The SD-specific business objects Sales Order, Customer Invoice, Outbound Delivery, and many others on top of database tables VBAK, LIKP, and VBRK are represented by CDS views that encompass access of the analytics CDS queries to the database in a uniform manner by offering semantic field names and associations to connected business objects as Customer, Material, and so on.</p>
<p>All analytics content will be built on top of these core entities.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No impact on business processes is expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If operational or analytical reporting is requested, it is recommended that the customer starts evaluating the use of HANA Analytics.</p>
<p><strong>How to Determine Relevancy</strong></p>
<p>This Transition Worklist Item is relevant if the customer has used Operational Data Provisioning in SD.</p>
<p>In SAP ERP the functionality was assigned to the Business Function LOG_SD_ANALYTICS_01.</p>
<p><strong>Required Activities:</strong></p>
<p>Custom Code Adaption during the conversion project is mandatory.</p>
<p><strong>Recommended Actions:</strong></p>
<p>Process Design / Blueprint after the conversion project is optional.<br>User Training during or after the conversion project is optional.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2228056">2228056 - SAP S/4HANA - Simplification Item SI9: SD_ODP - Operational Data Provisioning</a>
    <ul>
        <li><a href="https://launchpad.support.sap.com/#/notes/2190420">2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code</a></li>
    </ul>
</li>
</ul>