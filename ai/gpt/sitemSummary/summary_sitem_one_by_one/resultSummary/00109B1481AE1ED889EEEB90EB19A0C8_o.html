<h3>SI22: GENERIC_CHECKS</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2618018">2618018 - S4TWL - Generic Check for SAP S/4HANA Conversion and Upgrade</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are planning to do a system conversion from SAP ERP 6.0 or SAP S/4HANA Finance to SAP S/4HANA or a release upgrade SAP S/4HANA to a higher SAP S/4HANA release and want to learn about important things to know, critical preparation activities and checks to be done, which are directly related to the preparation of the system conversion or upgrade.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Unlike other Simplification Items, this Simplification Item does not describe a specific functional or technical change in SAP S/4HANA. But it is a collector for various generic, technical checks which need to run before a conversion or upgrade in SAP S/4HANA and which could affect any customer system - irrespective of the specific SAP S/4HANA target release. Many of these checks are not even SAP S/4HANA specific, but are checking for constellations which were already considered to be inconsistent in SAP ERP and could also have caused issues during an SAP ERP release upgrade. </p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
<ul>
<li>Custom Code Adaption: This simplification item serves as an anchor for assigning custom code check related content, which is not related to any specific functional or technical area (e.g., deletion of unused orphaned objects). This must be addressed before or during the conversion project as it is mandatory.</li>
<li>Miscellaneous: This item provides various generic, technical checks which need to run before a conversion or upgrade in SAP S/4HANA and which affect all customers - irrespective of the specific SAP S/4HANA target release. This must also be addressed before or during the conversion project as it is mandatory.</li>
</ul>
</p>