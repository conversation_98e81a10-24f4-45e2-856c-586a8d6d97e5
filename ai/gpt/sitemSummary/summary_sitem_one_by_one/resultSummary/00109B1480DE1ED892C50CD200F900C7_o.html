<h3>SI61: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2629241">2629241 - S4TWL - Segmentation (Upgrade SAP S/4HANA 1709 to 1809)</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system upgrade from SAP S/4HANA 1709 to SAP S/4HANA 1809, and you are using segmentation related business functionality. The data model of segmentation has changed from SAP S/4HANA 1709 to SAP S/4HANA 1809.</p>
<p><strong>Business Process Impact:</strong></p>
<p>This data model change does not have any business impact.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>In SAP S/4HANA 1809, ATP pool segment is enabled which is stored in table SGT_CATP. During the upgrade from 1709 to 1809 a migration report needs to be executed, which looks at 'ARUN/MRP/ATP: Requirement/Stock Allocation' node and populates the pool segment in table SGT_CATP. This is achieved via execution of XPRA report R_SGT_ATP_MIGRATION_XPRA.</p>
<p>In case the installation was originally converted from a SAP Fashion Management (SAP ERP 6.0 EHP7/EHP8) to SAP S/4HANA 1709 data contained in table SGT_CATP needs to be deleted via a pre-migration report R_SGT_POOL_SEGMENT_DELETE as ATP pool segment is not available in S/4HANA 1709.</p>
<p>This Simplification Item is relevant if segmentation is used. This can be checked via transaction SFW2 to check whether Business Function LOG_SEGMENTATION is activated.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2516743">2516743 - S4TWL - Fashion Functionality</a></p>