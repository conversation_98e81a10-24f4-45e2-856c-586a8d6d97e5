<h3>SI22: AS_ABAP_HANA_RULES_FRAMEWORK</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3243120">3243120 - S4TWL - Deprecation of SAP Hana Rules Framework</a></strong></p>
<p><strong>Description:</strong></p>
<p>The functionality to build and to model analytical rules via BRF+ and to push these rules down to HANA (SAP HANA Rules Framework) is deprecated now and will be removed as a comprised component in S/4HANA On-Premise 2023.</p>
<p><strong>Business Process Impact:</strong></p>
<p>It will not be possible to create analytical functions in BRF+ projects. Already created analytical functions will stop working with S/4HANA On-Premise 2023.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Please carry out a manual migration or remodeling from HRF (analytical function) to Business Rule Framework plus (BRF+).<br/>
For more details refer <a href="https://help.sap.com/docs/ABAP_PLATFORM_NEW/9d5c91746d2f48199bd465c3a4973b89/9a6b67ce7c26446483af079719edf679.html?version=201809.000" target="_blank">this documentation</a>.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2455694">2455694 - SAP HANA Rules Framework 1.0 SPS 11 Release Note</a></p>