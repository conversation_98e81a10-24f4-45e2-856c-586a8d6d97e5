<h3>SI63: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2637163">2637163 - S4TWL - Supply Assignment Preview (upgrade from S/4HANA 1709 to S/4HANA 1809)</a></strong></p>
<p><strong>Description:</strong></p>
<p>The data model of Supply Assignment Preview has changed from SAP S/4HANA 1709 to SAP S/4HANA 1809.</p>
<p><strong>Business Process Impact:</strong></p>
<p>This data model change does not have any business impact. In S/4HANA 1709 the preview mode could be executed in back order processing, the variant having checking rule as $PRE. In S/4HANA 1809 the preview mode can be executed in back order processing, when in the variant the new parameter "Assignment Preview" is set to active.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
There is no migration report for Supply Assignment Preview. Before the upgrade the old preview entries can be deleted (via transaction ARUNITA "Insight to Action", Execution Mode: Background, Selection Mode: Preview, Action: Unassign). After upgrade the back order processing needs to be executed again.
</p>
<p>This Simplification Item is relevant if Supply Assignment Preview functionality is used. This can be checked via transaction SE16N. This simplification item is relevant if there are entries in table ARUN_BDBS and with ARUN_MODE = 2 or in table ATP_BOP_VARIANT with ABOPCHECKINGRULE='$PRE' AND (ABOPEXECUTIONMETHOD = "FULL" OR ABOPEXECUTIONMETHOD = "ARUN").
</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2516743">2516743 - S4TWL - Fashion Functionality</a></p>
