<h3>SI30: PPM_SCHEDULE</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2630630">2630630 - S4TWL -  Copying of Actual Task Dates to Calculated Dates in SAP Portfolio and Project Management in SAP S/4HANA</a></strong></p>
<p><strong>Description:</strong></p>
<p>With SAP S/4HANA 1809, the default system behavior during task confirmation and project scheduling [in SAP Portfolio and Project Management] has changed as follows: 
When a task is confirmed, the actual dates are no longer copied to the calculated dates. As a result, actual dates are ignored by default when projects are scheduled.
This change takes effect if you have not implemented the BAdI DPR_SCHEDULING. If you have implemented this BAdI, the system behavior remains unchanged.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The default scheduling logic has changed since actual dates are no longer taken into account by default.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>You only need to take action if you have not implemented BAdI DPR_SCHEDULING and you do not want to use the new default behavior. Carry out one of the following steps if you still want to copy actual values to calculated values and take them into account during scheduling:</p>
<ul>
<li>By default the “Copy Actual to Planned” checkbox is displayed on the “Dates and Work” tab in the “Detail” view of the project header. Select this checkbox to activate the previous behavior for individual projects.</li>
<li>To return to the previous behavior for all projects, implement BAdI DPR_SCHEDULING. You do not need to implement the BAdI methods. Once the BAdI implementation is active, the “Copy Actual to Planned” checkbox is no longer available in the project header and actual dates are copied to calculated dates by default.</li>
</ul>
<p>For more information, see the documentation of the methods MOVE_ELEMENT and BUTTON4MOVE_ELEMENT in Customizing for SAP Portfolio and Project Management under Project Management -&gt; Structure -&gt; BAdI: Make Settings for Scheduling.</p>