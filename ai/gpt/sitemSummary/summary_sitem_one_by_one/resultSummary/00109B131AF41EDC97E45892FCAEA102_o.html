<h3>SI30: CT_DS_APPS</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/3133221">3133221 - S4TWL - Deprecation of Design Studio Apps</a></strong></p>
<p><strong>Description:</strong></p>
<p>As of SAP S/4HANA 2021, SAP Design Studio apps have been deprecated. This includes all Finance apps based on the Design Studio technology. For all deprecated SAP Design Studio apps, Web Dynpro data grid apps exist as alternatives. For a list of all deprecated SAP Design Studio apps and their alternatives refer to SAP note <a href="https://me.sap.com/notes/3081996">3081996 - Deprecation of SAP Design Studio Apps in SAP S/4HANA 2021</a>. As of SAP S/4HANA 2022, most of these SAP Design Studio apps have been set to obsolete. This includes most Finance apps based on the Design Studio technology. Refer to SAP note <a href="https://me.sap.com/notes/3170381">3170381</a> for more information.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Deprecated SAP Design Studio apps are no longer available by default on the SAP Fiori launchpad. However, you can still use them and also locate them using the app finder until they are deleted. Nevertheless, we strongly recommend switching to the SAP Web Dynpro successor apps as soon as possible. The Web Dynpro apps are already the default tiles on the SAP Fiori launchpad. Obsolete SAP Design Studio apps are no longer supported and have been deleted from the system. You can continue to use Design Studio reports that you created using the View Browser. However, you can no longer create new ones. We recommend creating Web Dynpro reports instead.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>For deprecated SAP Design Studio apps:</p>
<ul>
    <li>Evaluate the SAP Web Dynpro apps as alternatives to the SAP Design Studio apps and decide when to switch to the successor apps. However, this needs to happen the latest before the next release upgrade of your SAP S/4HANA system.</li>
    <li>If you use custom design studio apps note that they cannot be migrated automatically to Web Dynpro apps. You need to recreate them as Web Dynpro reports using the View Browser. Refer to SAP note 3081996 for details.</li>
</ul>
<p>For obsolete SAP Design Studio apps:</p>
<ul>
    <li>Switch to the SAP Web Dynpro apps provided as alternatives to the SAP Design Studio apps latest with your upgrade.</li>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/3112220">3112220 - FAQ: Web Dynpro Apps and Design Studio Apps in SAP S/4HANA and SAP S/4HANA Cloud</a>, <a href="https://me.sap.com/notes/3081996">3081996 - Deprecation of SAP Design Studio Apps in SAP S/4HANA 2021</a></p>