<h3>SI06: CM_COMMODITY_CURVES</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2551913">2551913 - S4TWL - CM: Simplified Commodity Curves</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA FPS1, on-premise edition. The master data of Commodity Curves have been simplified. The overall simplification of the data model in Commodity Management includes the deprecation of the Commodity ID. The only supported commodity Curve Category is '2 Based on DCS'. Commodity Curves are now defined and accessed using the DCS ID, MIC (optional), and Commodity Curve Type. For the usage of Commodity Curves within the Commodity Pricing Engine, it is no longer required to activate The Business Functions of Commodity Management for Treasury (FIN_TRM_COMM_RM). More details are available in Note 2553281, particularly in the chapter 'Conversion of Commodity Curves'.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The simplification impacts the Commodity Management data model by deprecating the Commodity ID and adopting a new method of defining and accessing Commodity Curves. Business processes related to Commodity Pricing Engines do not require certain legacy business functions.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Mandatory custom code adaptation and customizing/configuration are required either before or during the conversion project.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2553281">2553281 - Cookbook Deprecation of Commodity ID in Commodity Management</a></p>