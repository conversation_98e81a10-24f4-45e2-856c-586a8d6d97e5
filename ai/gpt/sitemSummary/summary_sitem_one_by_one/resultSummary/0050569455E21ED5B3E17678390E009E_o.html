<h3>SI4: PROC_MM_VM_SET</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267415">2267415 - Subsequent Settlement - Vendor Rebate Arrangements</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP S/4HANA, the Subsequent Settlement (MM-PUR-VM-SET) application is replaced by the new Contract Settlement (LO-GT-CHB) application. For this reason, the functional scope of subsequent settlement has been restricted, that is, in SAP S/4HANA, it is no longer possible to:</p>
<ul>
<li>Create new rebate arrangements using transaction MEB1 or</li>
<li>Extend existing rebate arrangements using transactions MEBV / MEB7 / MEBH</li>
</ul>
<p>Additionally, as a consequence of the material field length extension in the SAP S/4HANA landscape, the structure of table S111 was adjusted. This can have some impact if the business volume data has to be rebuilt in the SAP S/4HANA system.</p>
<p><strong>Business Process Impact:</strong></p>
<p>In SAP S/4HANA, Contract Settlement replaces Subsequent Settlement, which means that existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. Afterwards, new agreements can only be created based on condition contracts. Furthermore, if recompilation of the business volume data in SAP S/4HANA, on-premise edition 1511 is required, the index table S111 has to be rebuilt as described in SAP Note 73214 (Subseq.settl.: Retrospec.compltn/recompltn of busin.vol.data).</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>In SAP S/4HANA, there are no direct activities required for continuing the business process for subsequent settlement, with the only exception that the agreements have to be closed after the end of the validity period.</p>
<p>If a recompilation of the business volume data is required, the table S111 has to be rebuilt with the report RMEBEIN3 as described in SAP Note 73214.</p>
<p>**Process Design / Blueprint**: The rebate process will need to be redesigned based on condition contracts.</p>
<p>**Data migration**: If business has long-running rebate agreements that will continue to be active post system conversion to SAP S/4HANA, such agreements will have to be migrated to equivalent condition contracts.</p>
<p>**Custom Code Adaption**: Customer code will have to be adopted as per the new design for Rebate functionality using Condition Contracts.</p>
<p>**User Training**: Users need to be trained in using Condition contracts for their rebate scenarios.</p>
<p><strong>Reference Notes:</strong>
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/73214">73214 - Subseq.settl.:Retrospec.compltn/recompltn of busin.vol.data</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2194923">2194923 - S4TC SAP_APPL – Pre-Transition Checks for Subsequent Settlement</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2215220">2215220 - S/4 PreChecks: Information for Subsequent Settlement</a></li>
</ul>
</p>