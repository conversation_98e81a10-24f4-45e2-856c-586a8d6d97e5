<h3>SI7: SD_ANA</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267348">2267348 - S4TWL - Simplification in SD Analytics</a></strong></p>
<p><strong>Description:</strong></p>
<p>SD Analytics based on Logistics Information System (LIS) is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and its expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, SD Analytics based on LIS can be found under the ID 481. This means that you need to migrate from "SD Analytics based on Logistics Information System (LIS)" to its designated alternative functionality SAP S/4HANA Sales Analytics before the expiry of the compatibility pack license.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Simplification in SD Analytics follows the overall analytics strategy in SAP S/4HANA: Instead of prebuilt aggregates and/or redundant data for analytical purposes, the SAP S/4HANA Analytics approach is based on ODATA and Open CDS (also known as ABAP-managed Core Data Services (CDS)), which sit directly on top of the original database tables. Corresponding analytics content will be provided within SAP S/4HANA. This content will grow over time.</p>
<p>CDS Basic Views Represent the Core Entities in SD The SD-specific business objects Sales Order, Customer Invoice, Outbound Delivery, and many others on top of database tables VBAK, LIKP, and VBRK are represented by CDS views that encompass access of the analytics CDS queries to the database in a uniform manner. This is achieved by offering semantic field names and associations to connected business objects, such as Customer, Material, and so on. All analytics content will be built on top of these core entities. Incoming Sales Orders and Sales Volume Based on CDS Views The initially delivered analytics content consists of two CDS queries: Incoming Sales Orders and Sales Volume. These two exemplify the capability of SAP S/4HANA to report directly on the database, without any prebuilt aggregates, but with the possibility to make use of all dimensions for grouping, aggregation, and filtering on the fly. New Persistent Fields in the Database To make effective use of SAP S/4HANA's capabilities for SD Analytics, there are new persistent fields in the database (which, in the past, were only calculated on the fly): Sales Order Open Delivery Quantity and Amount (on schedule line level - VBEP) Sales Order Requested Quantity in Base Unit (on item level - VBAP) Further Usage of BW and LIS/SIS Data extraction from SAP S/4HANA to a customer-owned BW-Hub is still supported. See note <a href="https://launchpad.support.sap.com/#/notes/2500202">2500202</a> for further information about the relation of S/4HANA to BW. If you use a material number longer than 18 characters - see restriction note “Extended Material Number in LIS: SAP Note 2232362”.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>As SD Analytics based on LIS can be used until the expiry date of the compatibility pack license, it’s up to each customer to decide, when (with which of the coming SAP S/4HANA releases) they want to migrate to the designated alternative functionality. However, we strongly recommend doing a business evaluation and decision, at which point in time this will be done, early in advance to plan accordingly for the migration efforts. Customers using SD Analytics based on LIS and customers extracting data from SAP ERP to their own BW-Hub can continue with these processes in SAP S/4HANA considering the additional information in the referenced notes.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2500202">2500202 - S4TWL - BW Extractors in SAP S/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2269324">2269324 - Compatibility Scope Matrix for SAP S/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2227750">2227750 - SAP S/4HANA Simplification Item SI7: SD_ANA - Simplification in SD Analytics</a>