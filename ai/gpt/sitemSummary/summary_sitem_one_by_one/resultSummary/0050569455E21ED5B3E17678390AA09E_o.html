<h3>S12: MasterData</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2267295">2267295 - S4TWL - MDM 3.0 integration</a></strong></p>
<p><strong>Description:</strong></p>
<p>The MDM specific transaction codes for IDoc processing (inbound and extraction) are not available within SAP S/4HANA, on-premise and cloud edition. The item is valid starting with S/4 HANA release 1511 as well as for all newer releases.</p>
<p><strong>Business Process Impact:</strong></p>
<p>MDM 3.0 was discontinued. All known customer implementations have migrated to SAP NetWeaver MDM or SAP Master Data Governance, Central Governance. All known customer implementations have already replaced the SAP MDM 3.0 specific IDoc processing according to SAP note 1529387 "Tables MDMFDBEVENT, MDMFDBID, MDMFDBPR growing significantly".</p>
<p><strong>Required and Recommended Actions:</strong></p>
<ul>
  <li>Custom Code Adaption is mandatory during the conversion project. MDM 3.0 has been discontinued; all known customer implementations have migrated to SAP NetWeaver MDM or SAP Master Data Governance. All known customer implementations have already replaced the MDM specific IDoc processing according to SAP Note 1529387.</li>
  <li>Interface Adaption is mandatory during the conversion project.</li>
  <li>Customizing / Configuration is mandatory during the conversion project.</li>
  <li>Data cleanup / archiving is optional during the conversion project.</li>
  <li>User Training is mandatory during the conversion project.</li>
</ul>
<p><strong>Reference Notes:</strong>
<ul>
  <li><a href="https://me.sap.com/notes/1529387">1529387 - Tables MDMFDBEVENT, MDMFDBID, MDMFDBPR growing significantly</a></li>
</ul></p>