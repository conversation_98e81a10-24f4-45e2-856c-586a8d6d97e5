<h3>SI12: PROC_SLC</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2271188">2271188 - S4TWL - Dedicated SAP Supplier Lifecycle Management (SAP SLC) business processes</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>The following processes are supported with limitations:</p>
<p>- Supplier Evaluation</p>
<p>- Category Management</p>
<p>- Activity Management (internal)</p>
<p>The following processes of SAP Supplier Lifecycle Management (SAP SLC) are not available within SAP S/4HANA, on-premise edition 1511:</p>
<p> - All sell-side processes (e.g. Supplier Registration and Supplier Data Maintenance)</p>
<p> - Several buy-side processes (e.g. Supplier Hierarchies, Supplier Classification, Area of Validity in Supplier Evaluation, Supplier Qualification, Certificate Management, External Tasks in Activity Management)</p>
<p>It is a strategic decision of SAP to consolidate fuctionalities of Supplier Lifecycle Management (SLC) within SAP S/4HANA Supplier and Category Management together with SAP Ariba solutions.</p>
<p>The functional scope of Supplier and Category Management in SAP S/4HANA is currently not identical with the SAP SLC functionalities.</p>
<p>Coversion support is not available.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The functional scope of Supplier and Category Management in SAP S/4HANA is currently not identical with the SAP SLC functionalities. Strategic decisions about software and deployment must be made considering the limitations and differences between the platforms.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>We recommend that you evaluate on a project basis whether you want to use Supplier and Category Management in SAP S/4HANA or whether you want to use SAP SLC as a standalone system to leverage the full functional scope of SAP SLC.</p>
<p><strong>Activities:</strong></p>
<p><strong>Business Decision:</strong> Decide on your future software and deployment strategy for SAP Supplier Lifecycle Management (SAP SLC). Also refer to simplification item SI2: PROCR_SLC - S4TWL - Dedicated SAP Supplier Lifecycle Management (SAP SLC) business processes.</p>
<p><strong>Customizing / Configuration:</strong> Run conversion pre-checks and if requested, adjust SAP SLC customizing.</p>
<p><strong>Landscape Redesign:</strong> If you decide to use standalone SAP SLC solution, provide the additional SAP SLC system and implement SAP MDG-S MDG-S for supplier data integration.</p>
<p><strong>Implementation project required:</strong> If you decide to use Supplier and Category Management in SAP S/4HANA, implement the respective functionalities.</p>
<p><strong>Reference Notes:</strong> 
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2186288">2186288 - S4TC SRMSMC Master Check for S/4 System Conversion Checks</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2187627">2187627 - S/4 Transition: Activities after failed PreChecks</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2441434">2441434 - S4TC SAP Supplier Lifecycle Management Transition Prechecks (SIC)</a></li>
</ul>
</p>