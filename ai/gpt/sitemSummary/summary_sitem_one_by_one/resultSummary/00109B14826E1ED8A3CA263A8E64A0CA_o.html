<h3>SI1:_Logistics_General_ATT</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2668899">2668899 - S4TWL - Advanced Track & Trace</a></strong></p>
<p><strong>Description:</strong></p>
<p>You plan to convert an ECC or S/4 instance to S/4 HANA 1809 or higher. This requires a pre-check to be executed to ensure that your setup and components are compatible with the target release. Important Remark: The ERP functionality of Advanced Track and Trace for Pharmaceuticals is released for S/4 HANA 1809 FPS01 or higher. The ERP functionality of Advanced Track and Trace for Pharmaceuticals is not available for S/4 HANA 1809 SP00. Conversion from a supported ECC version: Conversion to S/4 HANA 1809 SP00 is not possible. Upgrade from a supported S/4 HANA version: Upgrade to S/4 HANA 1809 SP00 is not possible.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Having the ERP Add-on for Advanced Track and Trace installed (STTPEC) necessitates compatibility checks and corrections before proceeding with the conversion or upgrade.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Before implementing this note, please carefully read the related consulting note 2716224 - "S/4HANA 1809+ Conversion/Upgrade Guidelines for Advanced Track & Trace". Data model changes, retrofit, etc. are described in note 2716224.</p>
<p>During Software Upgrade / Maintenance, update to software version STTPEC 200 SP02 or higher if not yet implemented as a prerequisite for conversion or upgrade. Implement required versions of S/4HANA pre-checks.</p>
<p>During Customizing / Configuration, perform required DDIC and code changes according to SAP notes 2766712 and 2767892. Adjust customizing for used EAN categories and MM transactions.</p>
<p>For Data migration, implement, customize, and execute the data migration report /STTPEC/MIG_S4GTINSYNC before the start of technical conversion or upgrade downtime. Additionally, run the migration report /STTPEC/MIG_S4SYNCMEAN after technical conversion or upgrade.</p>
<p>Conduct Custom Code Adaption, performing custom code adjustments because of data model changes.</p>
<p>Ensure User Training on changes in business logics and transactions.</p>