<h3>SI8: IS_DIMP_AUT</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2273102">2273102 - S4TWL - BSP based Dealer Portal</a></strong></p>
<p><strong>Description:</strong></p>
<p>The automotive industry solution in SAP ERP Discrete Industries & Mill Products (ECC-DIMP) includes two different Dealer Portals. For simplification reasons, only one Dealer Portal solution is supported in SAP S/4HANA.<br>
The Dealer Portal based on Discrete Industries Web Interface (DIWI) and Business Server Pages (BSP) is not available in SAP S/4HANA.<br>
The successor Web Dynpro based Dealer Portal, introduced with SAP ERP 6.04 and accessible via SAP Netweaver Business Client, is available in SAP S/4HANA.<br>
The Web Dynpro based Dealer Portal allows managing vehicle sales, spare parts procurement, and warranty claim processing in collaboration with dealers.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The Dealer Portal based on Discrete Industries Web Interface (DIWI) and Business Server Pages is not available with SAP S/4HANA.<br>
The Web Dynpro based Dealer Portal is recommended as successor application.<br>
The user interface changes by the Dealer Portal switch, but the business processes are not affected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>In case the conversion to SAP S/4HANA starts from SAP ERP 6.04 (Enhancement Package 4) or higher, the migration to the Web Dynpro based Dealer Portal could be done before the system conversion. Otherwise, the migration to the Web Dynpro based Dealer Portal needs to be done after the system conversion to SAP S/4HANA.<br>
The pre-check will raise an error as long as the settings for the BSP based Dealer Portal, mentioned above to determine the relevancy, are available. Apply for an exemption for that check item after, or remove these settings for the BSP based Dealer Portal if the migration to the Web Dynpro based Dealer Portal could be done before the system conversion.</p>

<ul>
    <li>The user to partner mapping for the BSP based Dealer Portal to the one for the Web Dynpro based Dealer Portal with transaction DPCOMMON_MAP_U_P.</li>
    <li>The logical system to RFC destination mapping for the BSP based Dealer Portal to the one for the Web Dynpro based Dealer Portal with transaction DPCOMMON_MAP_S_R.</li>
    <li>The partner to system mapping for the BSP based Dealer Portal to the one for the Web Dynpro based Dealer Portal with transaction DPCOMMON_MAP_P_S.</li>
</ul>
<p>Provide training for dealer users regarding the Web Dynpro based Dealer Portal.<br>
Enhancements to the BSP based Dealer Portal have to be reimplemented for the Web Dynpro Dealer Portal, if applicable.</p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/2228244">2228244 - SAP S/4HANA Simplification: DIMP Automotive - Dealer portal (DIWI and BSP-based)</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2190420">2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code</a></p>