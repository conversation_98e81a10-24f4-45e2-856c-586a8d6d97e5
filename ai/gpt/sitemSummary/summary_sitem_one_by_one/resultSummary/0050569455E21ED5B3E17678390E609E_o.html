<h3>SI7: PROC_MDM&nbsp;</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2271184">2271184 - S4TWL - MDM Catalog</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>The SAP SRM MDM catalog is no more available in S/4HANA. He is replaced by the catalog search function as part of S/4HANA Self Service Procurement. In the first release of SAP S/4HANA, on-premise edition a limited functional scope might be available compared to the SAP SRM MDM catalog. An available SAP SRM MDM catalog instance can still be integrated with S/4HANA as a punch-out catalog via OCI.</p>
<p><strong>Business Process Impact:</strong></p>
<p>S/4HANA Self Service Procurement must be used instead of the SAP SRM MDM catalog, the content of which needs to be imported into S/4HANA via OCI5 (<a href="http://scn.sap.com/docs/DOC-35441" target="_blank">http://scn.sap.com/docs/DOC-35441</a>). Catalog content will be managed as part of S/4HANA Self Service Procurement.</p>
<p>Customers who are looking for a Catalog solution should take Ariba APC which is a comprehensive e-catalog solution.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Process Design / Blueprint (Before conversion project): The SAP SRM catalog MDM is not available in SAP S/4HANA. It's replaced by the catalog search function as part of SAP S/4HANA Self-Service Procurement.</p>
<p>Customizing / Configuration (Before or after conversion project): SAP S/4HANA Self-Service Procurement must be used instead of the SAP SRM MDM catalog.</p>
<p>Interface Adaption (Before or after conversion project - Conditional): Content needs to be imported into S/4HANA via OCI5</p>
<p>Data migration (During or after conversion project - Conditional): Set up a business user for SAP S/4HANA Self-Service Procurement.</p>
<p>Custom Code Adaption (During or after conversion project - Optional)</p>
<p>User Training (After conversion project - Optional)</p>