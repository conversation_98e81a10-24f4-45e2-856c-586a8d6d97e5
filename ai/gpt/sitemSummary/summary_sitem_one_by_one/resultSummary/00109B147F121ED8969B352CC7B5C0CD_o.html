<h3>SI1: PPDS_GENERAL</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2643483">2643483 - S4TWL - Performance optimizations for publication of planning orders</a></strong></p>
<p><strong>Description:</strong></p>
<p>PP/DS order publishing for planning orders (Planned orders, Purchase requisitions, stock transfer requisitions) is optimized to improve performance. This has resulted in changes in the way BAdI's/User exits are called during publication from PP/DS.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Advanced Planning (PP/DS) is used for production planning and planning receipts are integrated back to SAP S/4HANA from live-cache.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If none of the relevant BAdIs or user exits are implemented, no action is needed. The upgrade will be automatic and system will switch over to the new optimized logic.</p>
<p>If you have implemented an affected BAdI/user exits, the system will retain old publication logic post upgrade.</p>
<p>This check is made only once while publishing orders for the first time.</p>
<p>Post upgrade, you need to implement your custom code in the new BAdIs as well, in addition to the existing available publication BAdIs/user exits to support the optimized flow of planning orders. To switch further to optimized logic, an entry "X" needs to be made in the table /SAPAPO/PUB_CHK.</p>
<p>This Simplification Item is relevant if Advanced Planning (PP/DS) is used for production planning and planning receipts are integrated back to SAP S/4HANA from live-cache and relevant BAdIs/User exits for publishing of orders from PP/DS to PP are implemented.</p>
<p>The following relevant BAdIs:</p>
<ul>
    <li>/SAPAPO/DM_PO_CHANGE</li>
    <li>/SAPAPO/DM_PO_CHANG2</li>
    <li>/SAPAPO/CL_EX_CIF_OP</li>
    <li>/SAPAPO/DM_PO_MNTN</li>
    <li>/SAPAPO/CL_EX_CIF_IP</li>
    <li>/SAPAPO/SMOY_PUB_APP</li>
    <li>/SAPAPO/DM_CP_CBCLP</li>
</ul>
<p>The following relevant Customer exits:</p>
<ul>
    <li>EXIT_SAPLCRV5_001</li>
    <li>EXIT_/SAPAPO/SAPLCIF_RSV_001</li>
    <li>EXIT_SAPLCSLS_003</li>
    <li>EXIT_SAPLCPUR_001</li>
    <li>EXIT_/SAPAPO/SAPLCIF_PU_001'</li>
    <li>EXIT_SAPLCORD_005'</li>
    <li>EXIT_/SAPAPO/SAPLCIF_ORD_001'</li>
    <li>EXIT_SAPLCORD_002'</li>
    <li>EXIT_SAPLCORD_001'</li>
</ul>
<p>The following is the new BAdI that needed adaptation:</p>
<ul>
    <li>/SAPAPO/PPDS_ORDER_INT</li>
</ul>