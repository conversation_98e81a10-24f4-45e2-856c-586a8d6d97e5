<h3>SI64: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2624475">2624475 - SAP S/4HANA: Seasons Field Length Extension</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are going to use Season functionality in SAP S/4HANA 1809 or higher. This note collects information related to the Seasons field length extension. For more information, see the referenced notes.</p>
<p><strong>Business Process Impact:</strong></p>
<p>If your SAP S/4HANA system is embedded in a system landscape that contains other SAP or non-SAP solutions, integration may only be possible if the extended Season field length functionality is not activated in SAP S/4HANA. 
Integration between SAP S/4HANA 1809 and SAP Customer Activity Repository 3.0 (CARAB 2.0) does not work with extended Season field length.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>In case you want to integrate with solutions that can't process Seasons with extended field length, you can't activate field length extension for Seasons in customizing (Cross-Application Components -> General Application Functions -> Activate Extended Fields).</p>
<p>For further information regarding integration in a solution landscape, and how to adjust customer specific coding, please see reference note.</p>
<p>Please note, that currently integration scenarios with SAP Customer Activity Repository 3.0 (CARAB 2.0) and consuming applications are not supported. SAP plans to provide a note to enable such an integration scenario.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>1. <strong>Custom Code Adaption:</strong> You need to process your ALE change pointer entries related to Master Data Distribution completely upfront to the conversion otherwise there will be compatibility issues. See SAP Note 2649728 for details.</p>
<p>2. <strong>Business Decision:</strong> You need to decide if and when to switch on the Extended Seasons functionality. If your SAP S/4HANA system is embedded in a system landscape that contains other SAP or non-SAP solutions, integration may only be possible if the extended Season field length functionality is not activated in SAP S/4HANA.</p>
<p>3. <strong>Customizing / Configuration:</strong> If you have decided to switch on the Extended Seasons functionality -> perform the configuration. For technical details refer to SAP Note 2649728.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2649728">2649728 - S4TWL - Seasons Field Length Extension</a></p>