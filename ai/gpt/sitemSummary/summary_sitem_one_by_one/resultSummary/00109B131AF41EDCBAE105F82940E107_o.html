<h3>SI12: IS_DIMP_AUT</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3159740">3159740 - S4TWL - JIT Inbound</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The functionality of JIT Inbound as part of the automotive industry solution is not the target solution in SAP S/4HANA and therefore deprecated. Even if it is not yet defined when JIT Inbound will become obsolete and be replaced, which means it’s still in maintenance until communicated otherwise, it will receive no new features and innovations. SAP recommends implementing JIT Supply to Customer as the strategic next-generation solution.</p>
<p>JIT Supply to Customer supports the following key features of JIT Inbound:</p>
<ul>
<li>Receive EDI messages to create and update summarized JIT calls and sequenced JIT calls</li>
<li>Usage of sales scheduling agreements for JIT processing</li>
<li>Outbound delivery creation based on customer JIT calls</li>
<li>Action framework, defining allowed actions per internal processing status, change of internal processing status by action execution, action to be executed based on a change of the external status, interlinked actions, combining multiple actions to be processed together subsequently.</li>
<li>Monitoring of customer JIT calls</li>
<li>Demand analysis to compare forecast delivery schedules with customer JIT calls</li>
<li>Stock availability analysis</li>
<li>JIT delivery schedule creation based on customer JIT calls for improved material requirements planning</li>
<li>Production release and confirmation with goods movement control, with backflush execution in repetitive manufacturing and stock transfer between storage locations</li>
<li>Process customer JIT calls by scan/data entry</li>
<li>Processing reorder JIT calls</li>
<li>Extensibility options for processing JIT calls through EDI message, definition of custom-specific actions, extending SAP standard actions in pre-processing and post-processing, Component group determination</li>
</ul>
<p>JIT Supply to Customer differs from JIT Inbound in the following key features:</p>
<ul>
<li>No JIT master data buffers to be refreshed</li>
<li>Monitoring of EDI messages through Application Integration Framework (AIF)</li>
<li>Creating and processing of JIT packing groups as own business object</li>
<li>Processing of JIT delivery confirmations from customers for consumption-based billing or self-billing, using consignment fill-up and issue postings</li>
<li>Emergency correction of customer JIT calls not yet supported with JIT Supply to Customer</li>
<li>Forwarding sequenced JIT calls to external suppliers not yet supported with JIT Supply to Customer</li>
<li>Processing internal calls to sequenced JIT calls not supported with JIT Supply to Customer</li>
</ul>
<p>In addition, JIT Supply to Customer could be used side-by-side with JIT Inbound for migration purposes, using a conditional switch.</p>
<p>For more details on JIT Supply to Customer, refer to the product assistance of SAP S/4HANA: <a href="https://help.sap.com/viewer/feded183701e4ba0b42bc4fc31ad7a12/LATEST/en-US/bb8cc89be15e44d3abbe35a399904b3c.html" target="_blank">Link</a></p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>When the check item is relevant, there are two alternative options to proceed with the conversion to SAP S/4HANA:</p>
<ol>
<li>Evaluate and implement the next-generation Just-in-Time (JIT) Supply to Customer solution in SAP S/4HANA instead of continuing with JIT Inbound. The implementation of the new solution could be done stepwise using the conditional switch mentioned above, e.g. by plant and customer.</li>
<li>Apply for an exemption for this item in the SAP S/4HANA Simplification Item Check and continue with the current JIT Inbound implementation and consider a migration to the JIT Supply to Customer solution at a later point in time.</li>
</ol>
<p>
During the preparation and transition phase:
<ul>
<li>Decide if and when to migrate to the new JIT Supply to Customer functionality.</li>
<li>If you decided to move to JIT Supply to Customer, plan accordingly for an implementation project.</li>
</ul>
</p>