<h3>SI43: Logistics_PP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2941708">2941708 - 2941708 - S4TWL - Scheduling beyond year 2048</a></strong></p>
<p><strong>Description:</strong></p>
<p>You upgrade to SAP S/4HANA, on-premise edition 2021. The following SAP S/4HANA Transition Worklist item is applicable in this case. Domain CX_ZTPKT changed from INT4 to INT8. Manufacturing, maintenance, and network orders can be scheduled beyond the date 19.01.2048.</p>
<p><strong>Business Process Impact:</strong></p>
<p>There is no impact on existing business processes.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Check all occurrences in custom code where domain CX_ZTPKT or a data element using this domain is used.</p>
<ul>
<li>If the data declaration or interface parameter holds a point in time value (as seconds since 1.1.1980): Ensure that dependent types are also based on domain CX_ZTPKT.</li>
<li>In case the data declaration or interface parameter does <i>not</i> hold a point in time value: Change it to some other INT4 type not using domain CX_ZTPKT.</li>
</ul>
<p>Check all usages of function module DATE_TIME_CONVERT and ensure that the parameter POINT_IN_TIME can hold the value 253086767999 after the upgrade. For example by using domain CX_ZTPKT.</p>
<p><strong>Custom Code Adaption:</strong> Check custom code for impact by domain length extension.</p>