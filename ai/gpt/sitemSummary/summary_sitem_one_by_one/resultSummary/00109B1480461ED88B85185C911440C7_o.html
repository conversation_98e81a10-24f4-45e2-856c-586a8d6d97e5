<h3>SI59: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2627238">2627238 - S4TWL - Fashion changes in 1709 FPS02</a></strong></p>
<p><strong>Description:</strong></p>
<p>Certain fashion functionalities have changed in SAP S/4HANA for fashion for vertical business, when compared to SAP Fashion Management. Part of this Business Impact Note, we address two topics that are unrelated to each other.</p>
<p><strong>Business Process Impact:</strong></p>
<p>1) The following transactions are obsolete prior to SAP S/4HANA 1709 FPS02, and are available starting with SAP S/4HANA 1709 FPS02:
<ul>
<li>FSH_QDP Quantity Distribution Profile</li>
<li>FSH_PCW Production Control Workbench</li>
<li>The number range of object FSH_MPLND has been substituted by number range object FSH_MPLO. The number range intervals of object FSH_MPLO need to be maintained accordingly (transaction SPRO → Logistics - General → Fashion Management → Production Planning and Control → Define Number Range for Master Planned Order; or directly via transaction SNRO, enter FSH_MPLO for the object name).</li>
</ul>
2) The following transactions are obsolete prior to SAP S/4HANA 1909, and are available starting with SAP S/4HANA 1909:
<ul>
<li>FSH_PG_CTCT Maintain Condition Table</li>
<li>FSH_PG_CTFC Maintain Field Catalog</li>
<li>FSH_PG_CT_ACC_SEQ Maintenance of Access Sequences</li>
<li>FSH_PG_CT_COND_TYPE Maintain Condition Types in POG</li>
<li>FSH_PG_CT_DET_PROC Determination procedure maintenance</li>
<li>FSH_PG_CT_MGROUP Maintenance Group</li>
<li>FSH_POGD Delivery Date Determination</li>
<li>FSH_POGG Group Type Determination</li>
<li>FSH_POGO Order Type Determination</li>
<li>FSH_POGT PO Generation Tool</li>
<li>FSH_POGTWB Purchase Order Workbench</li>
<li>FSH_POHD Header Data Determination</li>
</ul>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Implemented business processes need to be adjusted according to the changes listed above.</p>
<p><strong>Process Design / Blueprint:</strong> Implemented business processes need to be adjusted according to the changes listed in SAP Note 2627238</p>
<p><strong>Data cleanup / archiving:</strong> Run cleanup program RFM_CLEANUP_POGT_OBJECTS to remove obsolete POGT program artifacts</p>
<p><strong>User Training:</strong> Users must be trained in new process designed for Fashion functionalities</p>
<p><strong>Customizing / Configuration:</strong> The number range intervals of object FSH_MPLO need to be maintained</p>
<p><strong>Reference Notes:</strong> 
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2516743">2516743 - S4TWL - Fashion Functionality</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2566648">2566648 - Delete FSH_CO09 user settings</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2532764">2532764 - SAP S/4HANA 1709 Feature Package Stack 00: Additional Release Information for Retail and Fashion</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2467506">2467506 - S4TWL - Order Allocation Run (ARun) in Fashion Management</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2481829">2481829 - S4TWL - Fashion Season Conversion (SAP ERP to SAP S/4HANA 1709)</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2590824">2590824 - Post upgrade clean up of obsolete objects generated by POGT</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2535093">2535093 - S4TWL - Season Active in Inventory Management</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2481891">2481891 - S4TWL - Season Conversion (SAP S/4HANA 1610 to 1709)</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2522971">2522971 - S4TWL - Segment Field Length Extension</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2465556">2465556 - S4TWL - Master Data for Order Allocation Run (ARun) in Fashion Management</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2465612">2465612 - S4TWL - Segmentation</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2468952">2468952 - S4TWL - Fashion Contract (Requirement Relevant Contracts) in SAP Fashion Management</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2727960">2727960 - S4TWL - Fashion Purchase Order Generation Tool</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2385984">2385984 - S4TWL - SAP S/4HANA Retail for Merchandise Management, SAP S/4HANA for Fashion and Vertical Business - Simplification Items</a></li>
</ul>
</p>