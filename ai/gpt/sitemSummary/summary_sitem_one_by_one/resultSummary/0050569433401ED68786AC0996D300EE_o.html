<h3>SI4: CT_OM</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2470711">2470711 - S4TWL - OUTPUT MANAGEMENT</a></strong></p>
<p><strong>Description:</strong></p>
<p>SAP S/4HANA output management is a modern solution for output related tasks. It takes the functionality from existing SAP output solutions and adds additional features like seamless integration into Fiori apps, flexible email configuration and email templates as well as the support of SAP Cloud Platform Forms by Adobe. SAP S/4HANA output management comes as an optional part of SAP S/4HANA. There are no mandatory actions for customers using this framework. All existing output management solutions like SD Output Control, FI Correspondence, FI-CA Print Workbench, and CRM Post-Processing are still supported. There are currently no plans to deprecate any of these solutions.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Business processes can benefit from one common output management solution across the product. Users always have the same look and feel and functionality, no matter if they perform output related tasks in Logistics, Procurement, or Financials. Also, an administrator only needs to setup and configure one output management framework. The SAP S/4HANA output management also offers at many places integration to standard extensibility, allowing business users to extend the solution without the need for an implementation project.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Business applications that already adopted to SAP S/4HANA output management should offer a switch to enable or disable the use of it.</p>
<p>Required actions for all business applications in scope:</p>
<ol>
<li>Define the active output management framework for the individual business applications.
<blockquote>
<ol>
<li>Open the customizing activity 'Manage Application Object Type Activation' under SAP Reference IMG -> Cross-Application Components -> Output Control.</li>
<li>Set the desired value in the column <em>Status</em> for all available <em>Application Object Types</em> by either changing an existing entry or by adding new entries.</li>
<li>All <em>Application Object Types</em> not listed in this customizing activity should have linked their corresponding simplification item to SAP Note <a href="https://me.sap.com/notes/2228611" target="_blank">2228611</a> providing individual information on how to enable or disable SAP S/4HANA output management. Please check the list of SAP Notes under section 'References' in this note.</li>
<li>If a business application is not listed in the customizing activity and has no simplification item or does not mention any changes regarding output management in its existing simplification item, it is likely not using SAP S/4HANA output management.</li>
</ol>
</blockquote>
</li>
<li>In case SAP S/4HANA output management is enabled, please refer to the SAP Note <a href="https://me.sap.com/notes/2228611" target="_blank">2228611</a> for more information and related notes for the technical setup and configuration.</li>
<li>In case SAP S/4HANA output management is not enabled or disabled, please setup the according other output management solution for the particular business application.</li>
</ol>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2228611">2228611 - Output Management in SAP S/4HANA</a>, <a href="https://me.sap.com/notes/2292681">2292681 - SAP S/4HANA output control - master form templates</a>, <a href="https://me.sap.com/notes/2294198">2294198 - SAP S/4HANA output control - customized forms</a>, <a href="https://me.sap.com/notes/2292571">2292571 - SAP S/4HANA output control - technical setup</a>, <a href="https://me.sap.com/notes/2292646">2292646 - SAP S/4HANA output control - form templates with fragments</a>, <a href="https://me.sap.com/notes/2292539">2292539 - SAP S/4HANA output control - configuration</a>