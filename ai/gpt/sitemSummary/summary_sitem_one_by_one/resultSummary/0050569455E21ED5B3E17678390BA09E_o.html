<h3>SI4: SD_CM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270544">2270544 - S4TWL - Credit Management</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA and you are using Credit Management (FI-AR-CR) on SAP ERP. In this scenario, the following SAP S/4HANA Transition Worklist item applies. Credit Management (FI-AR-CR) is not available as part of SAP S/4HANA. The functional equivalent in SAP S/4HANA is SAP Credit Management (FIN-FSCM-CR).</p>
<ul>
  <li>Risk category -> Risk class maintained for the whole business partner (not per customer & credit control area), as the probability of a credit default of your customer does depend on the characteristics of your customer and not your internal organizational structures.</li>
  <li>Maintenance of customer master data in business partner transaction</li>
  <li>The credit checks carried out for a customer depend on the assigned check rule in the business partner transaction. They do not depend on the risk class (formerly risk category).</li>
  <li>Releasing blocked documents via Case Management (documents the blocking reason)</li>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>There will be no change to your business processes because of the system conversion. However, some transactions become obsolete and are replaced by new transactions.</p>
<table border="1" cellpadding="0" cellspacing="0" class="table table-bordered table-striped col-resizeable">
  <tbody>
    <tr>
      <td valign="top" width="182">
        <p>Examples for replaced transactions in SAP S/4HANA</p>
      </td>
      <td valign="top" width="414">
        <p>For the maintenance of the credit account master data, transaction FD32 is replaced by transaction UKM_BP.</p>
        <p>For releasing credit-blocked sales orders, transaction VKM1 is replaced by transaction UKM_MY_DCDS.</p>
      </td>
    </tr>
    <tr>
      <td valign="top" width="182">
        <p>Transactions not available in SAP S/4HANA</p>
      </td>
      <td valign="top" width="414">
        <p>F.28 - Customers: Reset Credit Limit<br/>F.31 - Credit Management - Overview<br/>F.32 - Credit Management - Missing Data<br/>F.33 - Credit Management - Brief Overview<br/>F.34 - Credit Management - Mass Change<br/>FCV1 - Create A/R Summary<br/>FCV2 - Delete A/R Summary<br/>FCV3 - Early Warning List<br/>FD24 - Credit Limit Changes<br/>FD32 - Change Customer Credit Management (but FD33 still available for Migration checks)<br/>FDK43 - Credit Management - Master Data List<br/>S_ALR_87012215 - Display Changes to Credit Management<br/>S_ALR_87012218 - Credit Master Sheet<br/>VKM1 - Blocked SD Documents<br/>VKM2 - Released SD Documents<br/>VKM3 - Sales Documents <br/>VKM4 - SD Documents<br/>VKM5 - Deliveries</p>
      </td>
    </tr>
    <tr>
      <td valign="top" width="182">
        <p>Reports not available in SAP S/4HANA</p>
      </td>
      <td valign="top" width="414">
        <p>RFARI020 - FI-ARI: Extract from credit master data<br/>RFARI030 - FI-ARI: Import credit master data<br/>RFDFILZE - Credit Management: Branch/Head Office Reconciliation Program<br/>RFDKLI*NACC Reports<br/>RFDKLxx Reports</p>
      </td>
    </tr>
  </tbody>
</table>
<p>The following settings in Customizing are affected by the migration:</p>
<ul>
  <li>All IMG activities for SAP Credit Management are listed in Customizing under the following path: <em>Financial Supply Chain Management</em> > <em>Credit Management.</em></li>
  <li>The credit limit check for sales document types can only be specified as type D in SAP Credit Management or can be left empty (no credit limit check). The credit limit check types A, B, and C are no longer available.</li>
  <li>The credit check when changing critical fields has been removed from the SD side.</li>
  <li>The payment guarantee-related Letter of Credit has been removed from the SD side.</li>
  <li>A credit check on the basis of the maximum document value is not supported in SAP S/4HANA 1511 SP00 and SP01. It is supported as of SAP S/4HANA 1511 FPS2.</li>
</ul>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Migrating Credit Management is not needed. Review of BadIs “UKM_R3_ACTIVATE” and “UKM_FILL” is advisable. Think of using the default implementation provided by SAP. (FI-data will then be read real-time with the default implementation of BadI “UKM_R3_ACTIVATE”. The implementation of BadI “UKM_FILL” is then not needed anymore.)</p>
<p>1. Prerequisites for the migration to SAP Credit Management:</p>
<ul>
  <li>You have completed all documents related to payment guarantee Letter of Credit.</li>
  <li>You have completed the migration for Accounting.</li>
  <li>If you are running on your own code, you have eliminated the usage of SAP objects in your own code. For details on how to adapt user-defined customer code that relies on the changed objects, see the following SAP Notes:</li>
  <ul>
    <li>2227014 (Financials)</li>
    <li>2227014 (SD)</li>
  </ul>
</ul>
<p>2. Perform the recommended actions in the Task List PDF attached to this Note (only available in English). This list is only valid for the conversion of one-system landscapes.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2217124">2217124 - S/4 HANA: Credit Management Changes in SD</a>, <a href="https://launchpad.support.sap.com/#/notes/2227014">2227014 - S/4 HANA: Credit Management Changes in FI</a></p>