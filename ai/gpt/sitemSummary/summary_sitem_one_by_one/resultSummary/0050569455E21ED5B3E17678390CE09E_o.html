<h3>SI14: SD_BILLING_OM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2790427">2790427 - S4TWL - Billing Document Output Management</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP S/4HANA, a new output management approach has been implemented, but the old NAST technique is set as default. This note is relevant for you if your start release for the system conversion is an SAP ERP release. It is also relevant if your start release is SAP S/4HANA and you do not require "special" output channels. The new output management for SAP S/4HANA, called SAP S/4HANA output control, only supports the "pure" output channels, while the NAST-based output can also be used for several kinds of post-processing. SAP S/4HANA output control cannot be used if one of the following NAST transmission mediums is required:
<ul>
<li>8 Special function</li>
<li>9 Events (SAP Business Workflow)</li>
<li>A Distribution (ALE)</li>
<li>T Tasks (SAP Business Workflow)</li>
</ul>
In this case, please use NAST-based output management. If you don't need these kinds of "special" output channels, you can use the new SAP S/4HANA output control.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The business process will shift from NAST-based output management to SAP S/4HANA output control if you do not require specialized output channels. The new SAP S/4HANA output control provides a unified output management solution across the entire SAP S/4HANA landscape, offering improved extensibility, multi-tenancy capabilities, and configuration without modification. Configuration will be done via the Business Rule Framework plus (BRFplus) and involves different forms and techniques such as Adobe Document Services and SAP Cloud Platform Forms by Adobe.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Actions required for transitioning to the new SAP S/4HANA output control include:
<ul>
<li><strong>Business Decision:</strong> Make a decision to adopt the new output management solution. This decision is mandatory prior to starting the conversion project.</li>
<li><strong>Process Design / Blueprint:</strong> Optionally, design the process during or after the conversion project to integrate the new output management solution.</li>
<li><strong>Custom Code Adaption:</strong> Replace custom code for the previous NAST-based output management.</li>
<li><strong>Customizing / Configuration:</strong> Configure the new output management settings in SAP S/4HANA.</li>
<li><strong>Technical System Configuration:</strong> Set up the technical infrastructure required for the new output management solution.</li>
<li><strong>Landscape Redesign:</strong> Redesign the system landscape to accommodate the new output management if required.</li>
<li><strong>User Training:</strong> Provide optional training for users post-conversion to familiarize them with the new output management system.</li>
</ul></p>
<p><strong>Reference Notes:</strong> <ul><li><a href="https://launchpad.support.sap.com/#/notes/2228611">2228611 - Output Management in SAP S/4HANA</a></li><li><a href="https://launchpad.support.sap.com/#/notes/2292681">2292681 - SAP S/4HANA output control - master form templates</a></li><li><a href="https://launchpad.support.sap.com/#/notes/2294198">2294198 - SAP S/4HANA output control - customized forms</a></li><li><a href="https://launchpad.support.sap.com/#/notes/2292571">2292571 - SAP S/4HANA output control - technical setup</a></li><li><a href="https://launchpad.support.sap.com/#/notes/2292646">2292646 - SAP S/4HANA output control - form templates with fragments</a></li><li><a href="https://launchpad.support.sap.com/#/notes/2292539">2292539 - SAP S/4HANA output control - configuration</a></li></ul></p>