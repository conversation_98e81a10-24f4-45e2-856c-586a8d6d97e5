<h3>SI38: Logistics_PP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3196066">3196066 - SAP S/4HANA Simplification Item: Usage of Time Streams in Kanban</a></strong></p>
<p><strong>Description:</strong></p>
<p>In S/4HANA, persisted time streams are not longer used for Kanban time calculations. The usage of persisted time streams for time calculations based on factory calendars and shift definitions has been removed in Kanban. For Kanban Calculation, dynamic time streams are used which are generated on the fly based on the customizing settings. For the determination of delivery dates and for Kanban Summarized JIT Calls, a new calculation algorithm was introduced. In the customizing transactions OM19 ("Define Kanban Calculation Profiles") and OJI3 ("Define Time Definition"), the time stream entries in table TTSTR will still be saved and transported.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The change eliminates the use of persisted time streams for Kanban time calculations, thereby requiring dynamic time streams and a new calculation algorithm which could impact how Kanban processes are executed and managed.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If the SAP standard functionality for Kanban time calculation is used, there is no adoption needed. Customer specific modifications in the function group includes L0PK1F01, L0PK1F03, or LPABC1F01 that change the way how time streams are created will have no effect anymore on the Kanban time calculations. Additionally, customer-specific extensions/modifications of the time streams handling or usage in the Kanban process (PP-KAB) might need to be reimplemented. See SAP note 3196066.</p>