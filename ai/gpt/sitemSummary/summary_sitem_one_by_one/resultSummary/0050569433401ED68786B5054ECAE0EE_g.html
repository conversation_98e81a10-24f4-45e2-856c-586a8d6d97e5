<h3>SI9: CT_LSMW</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2287723">2287723 - LSMW in SAP S/4HANA on-premise (S4CORE)</a></strong></p>
<p><strong>Description:</strong></p>
<p>The Legacy System Migration Workbench (LSMW) is a tool used for data migration to SAP ERP systems. However, for data migration to SAP S/4HANA, SAP recommends using the SAP S/4HANA migration cockpit. The use of LSMW is restricted in SAP S/4HANA and might propose incorrect migration interfaces due to the changes in data models and interfaces in SAP S/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>LSMW is not the recommended tool for data migration to SAP S/4HANA. Using LSMW for this purpose may lead to errors and incorrect data migration. The simplification item provides alternative solutions and recommendations for data migration.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Before starting the conversion project, consider that the use of LSMW for data migration to SAP S/4HANA is not recommended and is at the customer's own risk. SAP recommends using the SAP S/4HANA migration cockpit instead. If you still plan to use LSMW, during the conversion project,  provide customer-specific adjustments or workarounds for restrictions on certain Batch Input or Direct Input programs.</p>
<p><strong>Reference Notes:</strong> 
<a href="https://me.sap.com/notes/2538170">2538170 - Migration of Retail Objects to SAP S/4HANA on-premise</a>, <a href="https://me.sap.com/notes/2747566">2747566 - SAP S/4HANA Migration Cockpit: Composite Note for Transfer Data Directly from SAP System</a>, <a href="https://me.sap.com/notes/2537549">2537549 - Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)</a>, <a href="https://me.sap.com/notes/2239701">2239701 - SAP Rapid Data Migration for SAP S/4HANA, on premise edition</a></p>
