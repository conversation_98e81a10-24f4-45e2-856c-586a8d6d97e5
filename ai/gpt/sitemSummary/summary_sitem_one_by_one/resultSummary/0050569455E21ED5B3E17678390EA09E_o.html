<h3>SI9: PROC_MM_OUTPUT</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267444">2267444 - S4TWL - Output Management Adoption in Purchase Order</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Value</strong></p>
<p>Additionally to the existing output management based on NAST a new Output Management solution is adopted S/4 HANA-wide. Thereby a unified solution can be used for all Output Management activities. The existing Output Management solution however still can be activated.</p>
<p><strong>Description</strong></p>
<p>With SAP S/4HANA a new Output Management approach is in place. By design, the new output management includes cloud qualities such as extensibility enablement, multi tenancy enablement and modification free configuration. Therefore, the complete configuration differs from the configuration that is used when output management is based on NAST. The configuration is based on BRF+ which is accessible for Customers. In SAP S/4HANA, the target architecture is based on Adobe Document Server and Adobe Forms only. For the form determination rules (along with other output parameters) BRF+ functionality is used (in this sense in combination with the message determination).</p>
<p>Nevertheless Output management based on NAST is still supported for new documents in purchase order. Purchase orders which are migrated from legacy systems and for which NAST based output has been determined, can be processed with the new and with NAST technology. The user can decide whether he wants to use for all new purchase orders the new output management or NAST. Therefore in Procurement for purchase order there is a need to adapt the configuration settings related to Output Management.</p>
<p>In purchase order it is recommended to use Adobe Forms but due to compatibility the form technologies known from Business Suite (like SmartForms, SapScript) are supported as well. This is also true for the print reports.</p>
<p>In Purchase order processing it is recommended to use Adobe Forms but due to compatibility the form technologies known from Business Suite (like SmartForms, SapScript) are supported as well. This is also true for the print reports.</p>
<p>In contrast to the output management based on NAST the new output management supports only the Channels PRINT, EMAIL, XML (for Ariba Network Integration) and IDOC (for on premise). Other channels are not supported by default.</p>
<p>Additional information to the new output management in SAP S4/HANA can be found in the general simplification item related to Output Management (Cross Topic Area)</p>
<p>Up to release OP1709 you can find more information about how to activate the NAST-based Output Management in the SAP Customizing Implementation Guide under Materials Management -&gt; Purchasing -&gt; Purchase Order -&gt; Activate or Deactivate SAP S/4HANA-Based Output Management.</p>
<p>As of release OP1809 you can find this customizing setting under Cross-Application components -&gt; Output Control -&gt; Manage Activation of Object Type. The NAST based output management is now the default solution, but a system upgrade will not change the previous selected output management.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Output Management for Purchase Order has to be configured. In cases where there is not customer specific configuration for the new output management for purchase orders, it will not be possible to print purchase orders or to send purchase orders via e-mail or XML.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>Business Decision (Before conversion project) - Mandatory:</strong> Additionally to the existing output management based on NAST a new Output Management solution is adopted S/4 HANA-wide. Thereby a unified solution can be used for all Output Management activities. The existing Output Management solution however still can be activated.</p>
<p><strong>Customizing / Configuration (During conversion project) - Conditional:</strong> Configure new output management.</p>
<p><strong>Custom Code Adaption (During conversion project) - Conditional:</strong> Replace custom code for previous NAST-based output management.</p>
<p><strong>Process Design / Blueprint (During or after conversion project) - Conditional:</strong> Additionally to the existing output management based on NAST a new Output Management solution is adopted S/4 HANA-wide. Thereby a unified solution can be used for all Output Management activities. The existing Output Management solution however still can be activated.</p>
<p><strong>Technical System Configuration (After conversion project) - Conditional:</strong> Configure technical infrastructure for new output management</p>
<p><strong>Landscape Redesign (After conversion project) - Conditional:</strong> If new output management is used, setup required technical infrastructure</p>
<p><strong>User Training (After conversion project) - Optional:</strong> </p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/2200404">2200404 - MM-Transition to S/4: New and old Output Management in PO</a>
</p>