<h3>SI17: <PERSON> Complaints</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267398">2267398 - S4TWL - Industry-Specific SD Complaint Handling</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the industry-specific SD complaints handling in ERP is not available within SAP S/4HANA. The functional equivalent to be used is the standard SD complaints handling (Transaction CMP_PROCESSING).</p>
<p><strong>Business Process Impact:</strong></p>
<p>The industry-specific SD complaints handling is related to business function LOG_SD_EACOMPLAINTS. Customers that have this business function active in SAP Business Suite start system can convert to SAP S/4HANA. However, independently of the activation status of the business function LOG_SD_EACOMPLAINTS, the corresponding functionality is not available in SAP S/4HANA. Customers that do not have the business function LOG_SD_EACOMPLAINTS active in the SAP Business Suite start release are not able to activate the business function after the conversion to SAP S/4HANA. This business function is defined as “obsolete” in SAP S/4HANA.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>None</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2239184">2239184 - SAP S/4HANA: SD - Complaints Handling</a>, <a href="https://launchpad.support.sap.com/#/notes/2190420">2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code</a></p>