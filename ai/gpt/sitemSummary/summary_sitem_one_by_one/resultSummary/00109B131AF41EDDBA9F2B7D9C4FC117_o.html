<h3>SI15: FIN_CO</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3320010">3320010 - S4TWL - Profitability Segment Number Change of Type</a></strong></p>
<p><strong>Description:</strong></p>
<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023 or a later release from SAP S/4HANA release 2022 or earlier. During this upgrade, the data type of the profitability segment number has been changed from data type NUMC length 10 to data type CHAR length 10. This enables the use of alphanumeric profitability numbers after the decimal profitability segments have run out.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The change of the data type of profitability segment number has no business-related impact.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>To avoid any errors in the system behaviour due to the change of the data type in the profitability segment field, you need to adapt your customer coding for this change. The adaption of the initial value check needs to be carried out, regardless of whether you will be using the additional alphanumeric profitability segments or not. In order to identify coding that needs to be adapted, the Code Inspector checks are executed as part of the related simplification item. If you are using the decommissioned CDS field ProfitabilitySegment in customer-defined CDS Views, you must switch to the replacement field ProfitabilitySegment_2.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
    <li><a href="https://launchpad.support.sap.com/#/notes/3320427">3320427 - Profitability Segment Number Change of Type - More details on the coding adjustments</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2436688">2436688 - Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App</a></li>
</ul>