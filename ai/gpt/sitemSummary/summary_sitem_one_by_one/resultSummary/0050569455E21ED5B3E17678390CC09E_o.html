<h3>SI13: SD_CAS</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2267375">2267375 - S4TWL - Sales Activities</a></strong></p>
<p><strong>Description:</strong></p>
<p>Sales Support: Computer-Aided Selling (SD-CAS) - (see SAP Help <a href="http://help.sap.com/saphelp_46c/helpdata/en/93/74356a546011d1a7020000e829fd11/content.htm" target="_blank">LINK</a>) is not available in SAP S/4HANA, because it is not the target architecture. We recommended that you use SAP CRM on-premise (side-by-side with SAP S/4HANA) or SAP Cloud for Customer.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Sales Support is no longer available in SAP S/4HANA. The creation of new business documents or editing of migrated business documents is not possible.</p>
<p>Transactions not supported in SAP S/4HANA:</p>
<p>OV/8&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Test Data - Transfer Data - Cond.</p>
<p>OV/9&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Display Test Data</p>
<p>OVCB&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Structure of Reporting View</p>
<p>OVCC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Assign View to User</p>
<p>OVCD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Definition of Reporting View</p>
<p>OVCE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Control Update of Perform.Measures</p>
<p>OVCF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Control Reporting - Info Blocks</p>
<p>OVCI&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; C SD Set Workflow Action box</p>
<p>OVL5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Maintain Transit Time</p>
<p>SDCAS_MCQ&nbsp;&nbsp;&nbsp;&nbsp; Call MC/Q From Address Selection</p>
<p>V+01&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Sales Call</p>
<p>V+02&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Telephone Call</p>
<p>V+03&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Sales Letter</p>
<p>V+11&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Direct Mailing</p>
<p>V+21&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Sales Prospect</p>
<p>V+22&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create Competitor</p>
<p>V+23&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Business Partner</p>
<p>VC_2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Customer Fact Sheet PDF Version</p>
<p>VC/1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; List of Customers (as of Release 1610)</p>
<p>VC/2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Customer Master Data Sheet (as of Release 1610) - Use Fiori App "Customer 360 Object Page" instead.</p>
<p>VC/A&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 01</p>
<p>VC/B&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 02</p>
<p>VC/C&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 03</p>
<p>VC/D&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 04</p>
<p>VC/E&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 05</p>
<p>VC/F&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 06</p>
<p>VC/G&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 07</p>
<p>VC/H&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 08</p>
<p>VC/I&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 09</p>
<p>VC/J&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales Activity Description 10</p>
<p>VC01&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Sales Activity</p>
<p>VC010102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Only Follow-up Activities</p>
<p>VC010103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Do not Delete Mail. Camp.+Addresses</p>
<p>VC010104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Internet mailing</p>
<p>VC01N&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Edit Sales Activity</p>
<p>VC01N_DRAG_KONTAKT&nbsp; Edit Sales Activity</p>
<p>VC01N_DRAG_MAILING&nbsp; Edit Sales Activity</p>
<p>VC01N_M&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Edit Mailing</p>
<p>VC02&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Change Sales Activity</p>
<p>VC03&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Display Sales Activity</p>
<p>VC05&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sales support monitor</p>
<p>VC06&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Parallel Processing for Address List</p>
<p>VC10&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Report Tree - Select Addresses</p>
<p>VC10_BMENU&nbsp;&nbsp; Area Menu for VC10</p>
<p>VC15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Crossmatching</p>
<p>VCR1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Competitive products</p>
<p>VCU3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Display Incompletion Log</p>
<p>VIM6&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Customer Exits: Data Selection</p>
<p>VN05&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; No.Assignment for Address List(SSup)</p>
<p>VN06&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create No.Interval-Sales Activities</p>
<p>VOC1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Customizing for List of Addresses</p>
<p>VUC2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Maintain Incompletion Log</p>
<p>VV51&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Create Output for Sales Activity</p>
<p>VV52&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Change Output: Sales Activity</p>
<p>VV53&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Display Output: Sales Activity</p>
<p>VVCB&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Maintain Activity Authorization</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If sales support functionality is requested, we recommend that customers start evaluating a side-by-side CRM on-premise scenario, or that they use Cloud for Customer.</p>
<p><strong>Reference Notes:</strong> 2214585 - Sales support not available</p>