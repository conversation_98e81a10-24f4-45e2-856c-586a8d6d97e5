<h3>SI18: AS_ABAP_TRANSCEIVER_INTEGRATION</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2879911">2879911 - Package BALI is missing</a></strong></p>
<p><strong>Description:</strong></p>
<p>The package BALI covering transceiver functionality of R/3 up-to release 6.10 is missing. Transceiver functionality is discontinued. The transceiver functionality has been withdrawn and has been replaced with new interfaces, see SAP Note 176927.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The discontinuation of the transceiver functionality impacts any business processes that relied on the programs, function groups, business object types, and workflow tasks associated with transceiver operations. The processes must transition to the new interfaces as described in the referenced notes.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Before or during the conversion project, it is mandatory to perform custom code adaptation to remove the usages of the deleted objects from your custom code.</p>
<p><strong>Reference Notes:</strong>
<ul>
    <li>176927 - Transceiver component becomes unnecessary</li>
    <li>211918 - KK1 is replaced by HR-PDC</li>
    <li>45694 - Transceiver installation</li>
    <li>357212 - PDC confirmation 4.6: Default values for services</li>
    <li>102484 - Problems with delta download CC2</li>
    <li>647145 - Setting up the HR-PDC interface</li>
    <li>406429 - Setting up the PP-PDC interface</li>
    <li>44103 - Setting up the PDC interface</li>
    <li>103376 - Transceiver coupld.exe under NT 4.0 terminates</li>
</ul>
</p>