<h3>SI17: Business partner data exchange between SAP CRM and S/4HANA</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2285062">2285062 - S4TWL: Business partner data exchange between SAPCRM and S/4 HANA, on-premise edition</a></strong></p>
<p><strong>Description:</strong></p>
<p>This SAP Note is relevant if you are using an SAP ERP release and support package that is lower than SAP enhancement package 6 for SAP ERP 6.0; SAP enhancement package 7 (SP11) for SAP ERP 6.0; or SAP enhancement package 8 (SP01) for SAP ERP 6.0. You are also using a live integration of SAP CRM and SAP ERP, planning to convert from SAP ERP to SAP S/4HANA, on-premise edition, and planning to activate the customer vendor integration (CVI) in SAP ERP and start a mass synchronization based on the customer master or vendor master to generate business partners.</p>
<p>In SAP S/4HANA, on-premise edition, the central business partner approach is mandatory, and the master data is processed using the SAP Business Partner with an activated CVI. The customer or vendor master is updated automatically in the background. If you have not worked with the CVI beforehand, you need to activate it and generate business partners before converting your system to SAP S/4HANA. You also have to set up the business partner data exchange between SAP S/4HANA and SAP CRM.</p>
<p><strong>Business Process Impact:</strong></p>
<p>There is no effect on business processes if the settings are performed as described in this SAP Note.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Note that you must perform the pre-conversion actions described in this SAP Note before activating the CVI and starting mass synchronization.</p>
<p><strong>Reference Notes:</strong></p>
<p>
<a href="https://launchpad.support.sap.com/#/notes/2283695">2283695 - Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2420959">2420959 - AdressGUID RFC BADI for CVI_MAPPER</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2286880">2286880 - BP data exchange between SAP S/4HANA, on-premise edition and SAP CRM: SEPA and partner functions</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2421803">2421803 - Industry sector data replication issue</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2283810">2283810 - Customizing settings for business partner data exchange between SAP S/4 HANA, on-premise edition and SAP CRM</a><br>
<a href="https://launchpad.support.sap.com/#/notes/1968132">1968132 - Business partner replication between CRM and ECC with active CVI</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2231667">2231667 - Restrictions for integration of SAP S/4HANA, on-premise edition with SAP CRM</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2289876">2289876 - Integration SAP S/4HANA, on-premise edition with SAP CRM: User Exits DE_BALE and DE_AALE not available in S/4HANA, on-premise edition</a>
</p>