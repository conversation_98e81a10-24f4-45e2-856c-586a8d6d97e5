<h3>SI20: AS_ABAP_WORKFLOW_USER_JOBS</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2568271">2568271 - Change of workflow system user and workflow system jobs with S/4HANA On-Premise 1709</a></strong></p>
<p><strong>Description:</strong></p>
<p>Starting with S/4 Hana OnPremise 1709 the workflow system user and workflow system jobs changed. The workflow system user is called SAP_WFRT now instead of WF-BATCH. The workflow system jobs start with SAP_WORKFLOW now and are scheduled automatically by "Technical Job Repository", transaction SJOBREPO. Starting with S/4 Hana OnPremise 1809 the workflow system jobs are scheduled under the user, under which the system jobs of Technical Job Repository run. This might be a different user than SAP_WFRT.</p>
<p><strong>Business Process Impact:</strong></p>
<p>SAP Business Workflow does not continue after upgrade.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>1. Deschedule the "old" workflow jobs in <strong>all system clients</strong>, in case these are still scheduled from the time before the upgrade. These are the following jobs:<br/>SWWDHEX<br/>SWWERRE<br/>SWWCOND<br/>SWWWIM<br/>SWEQSRV<br/>SWWCLEAR</p>
<p>2. Make sure that user SAP_WFRT exists in the system and has role SAP_BC_BMT_WFM_SERV_USER_PLV01 assigned.</p>
<p>3. Check role SAP_BC_BMT_WFM_SERV_USER_PLV01 e.g. in transaction PFCG:<br/>Ensure that there is a green traffic light on tab "Authorizations". Regenerate the profile if necessary.<br/>Afterwards check the traffic light on the tab "User". Run the "User Comparison" if the traffic light is not green.</p>
<p>4. In "Technical Job Repository" (transaction SJOBREPO) the automatic scheduling of jobs must be switched on. Note 2190119 - Background information about S/4HANA technical job repository - describes the prerequisites needed.</p>
<p>5. Wait for the next run of system job "R_JR_BTCJOBS_GENERATOR" for the Technical Job Repository. It runs by default every hour and schedules all new workflow system jobs starting with SAP_WORKFLOW.<br/>After the workflow jobs are scheduled, you will see green lights in transaction SWU3.</p>
<p>6. Job SAP_WORKFLOW_RESTART is automatically scheduled in the system. It runs once a day and restarts all workflows in error status automatically. These are workflows, you can find in transaction SWPR. If you do not want these workflows to be restarted, please deactivate the job in transaction SJOBREPO.</p>
<p><strong>Reference Notes:</strong>


<p><a href="https://launchpad.support.sap.com/#/notes/2190119">2190119 - Background information about SAP S/4HANA technical job repository</a></p>
<p><a href="https://launchpad.support.sap.com/#/notes/2731999">2731999 - Assign custom step user for Technical Job Repository (SJOBREPO)</a></p>
<p><a href="https://launchpad.support.sap.com/#/notes/16083">16083 - Standard jobs, reorganization jobs</a></p>