<h3>SI3: MasterData_PM</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2267138">2267138 - S4TWL - Simplified Product Master Tables related to OMSR transaction</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP S/4HANA, the tables related to OMSR transaction ("Field groups and Field selection for data screens") such as T130F (Table of Field attributes), T130A (Table of Material master field selection), T133F (Table of Material master field selection part2), etc., has delivery class E (E = Control table, SAP and customer have separate key areas). In SAP Business Suite, the delivery class of these tables is G (G= Customizing table, protected against SAP Update, only INS all).</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Customizing the field selection for data screens in SAP namespace is <strong>not</strong> recommended anymore.</p>
<p>To customize the field selection settings, kindly follow the below process.</p>
<ol>
<li>In OMSR/OMS9 transaction, select the field ref. you want to change.</li>
<li>Click on <strong>Copy As</strong> icon or press F6.</li>
<li>Enter the Field reference ID in allowed namespace.</li>
<li>Make the required changes for the field groups and press enter.</li>
<li>Click on <strong>Save.</strong></li>
<li>The created field reference can be assigned to required material type / plant.</li>
<ul>
<li>To assign it to a material type, go to transaction OMS2.</li>
</ul>
</ol>
<p>Open the required material type, update the <strong>Field reference</strong>. Click on Save.</p>
<ol>
<ul>
<li>To assign it to a plant, go to transaction OMSA.</li>
</ul>
</ol>
<p>Add an entry by clicking <strong>New entries</strong>. Click on Save.</p>
<p>In S/4HANA the recommendation is to adjust the field references from a transaction based approach to a material type approach. In the SAP standard, the transaction based field references were set to optional, so that the field selection can be controlled via the field references of the material type.<br/>In case you continue to work with a transaction based approach, you need to double-check the modifications you made to these field references after each upgrade, since the modifications to these field references might be overwritten with the SAP standard.<br/>Changing the field selection group also has to be double-checked after each upgrade, since the modifications of the field selection groups might also be overwritten with the SAP standard.</p>