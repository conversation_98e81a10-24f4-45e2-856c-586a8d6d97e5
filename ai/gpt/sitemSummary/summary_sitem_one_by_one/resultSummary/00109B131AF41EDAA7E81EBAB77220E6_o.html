<h3>SI27: MasterData_BP</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2928897">2928897 - S4TWL - Removal of Gender Domain Fixed Values</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>You are looking to maintain additional gender values such as Neutral or Diverse in SAP Business Partner (BP). It was not possible to maintain gender values other than Male, Female and Unknown in a Business Partner so far. With S/4HANA On-premise 2020, in addition to the existing values Male, Female and Unknown, two new gender values are available in Business Partner master data:</p>
<p>Nonbinary - an identity other than male or female. For example, Divers in Germany.<br/>Not specified - any identity with legal rights; but chooses not to specify the gender</p>
<p>To support new gender codes, the existing fixed values for gender related domains in Business Partner data model have been replaced with new value tables. These value tables will hold gender codes Male, Female and Unknown (existing from older releases) and new gender codes Nonbinary and Not specified (new from S/4HANA 2020)</p>
<p><strong>Business Process Impact:</strong></p>
<p>All business processes which involve maintenance of Business Partner Master Data and in that specifically the Gender information for the Business Partner are affected. This includes primarily Business Partner maintenance via:</p>
<ul>
<li>Transaction BP</li>
<li>Fiori Apps for Business Partner, Customer and Supplier maintenance</li>
<li>Supported external interfaces for like oData, SOAP, iDocs</li>
<li>Released APIs</li>
</ul>
<p>SAP delivered business processes have been adjusted to make use of new value tables for gender codes instead of domain fixed values.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Attached PDF document titled 'Cookbook_GenderDomainChanges.pdf' describes in detail the how the custom adoption has to be done for the data model changes listed above. Usages of fixed values of domains AD_SEX, BU_SEXID and SEXKZ needs to be replaced with values from check tables TSAD15 and TB995. Ensure that your business processes (also in connected systems) are prepared to handle the new gender codes properly.</p>
<p><strong>Reference Notes:</strong></p>
<p><a href="https://me.sap.com/notes/2816791">2816791 - S/4HANA - New Gender Values in SAP NetWeaver Business Partner</a></p>