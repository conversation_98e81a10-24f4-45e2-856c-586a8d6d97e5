<h3>SI02: TM_CUSTOMIZING_2</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2639847">2639847 - 2639847 - S4TWL : Impact on TM process with conversion of SAP ERP or upgrade of S/4HANA 1511/1610/1709 to S/4HANA 1809</a></strong></p>
<p><strong>Description:</strong></p>
<p>You use SAP ERP Enhancement Package&nbsp;4, 5, 6, 7 or 8 or S/4HANA 1511 or S/4HANA 1610 or S/4HANA 1709 for posting of forwarding settlement and internal settlement document in Sales and Distribution (SD). As a part of integration of SAP TM, there are changes to the IMG activities that are used for the forwarding settlement and internal settlement integration.&nbsp; These changes need to be evaluated for impact prior to the system conversion to S/4 HANA 1809 or above.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Forwarding settlement document or internal settlement document from standalone SAP TM is transferred to S/4HANA&nbsp;1809 for posting using A2A service interface: CustomerFreightInvoiceRequestSUITERequest_In_V1.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>For the upgrade to be successful, it is recommended that the RFC destination for the TM system is maintained as a combination of logical system and client separated by an underscore '_'( LOGSYS_CLIENT)</p>
<p>However, during near zero downtime (NZDT), it may be permissible to skip maintaining the RFC destination. This could lead to encountering an error during Simplification Item(SI) check, which can subsequently be ignored or bypassed. In this scenario please ensure the customizing is manually maintained in the target system.</p>
<p>In order to ensure that the data available in TCM_C_SLS_MAP table is consistent enough to be copied into the target table /SCMTMS/C_CFIR_T via an XPRA which will be executed during the upgrade / system conversion process, it is required that the source table does not contain:</p>
<p>1. Entries where FWSD type is blank</p>
<p>2. Entries for the same&nbsp;FWSD type to different sales document type / billing type / billing item type, and pricing procedure.</p>
<p>3. Entries where there are different division and distribution channel maintained.</p>
<p>4. Entries where the logical system is blank.</p>
<p>5. Entries where the ECC sales organization, FWSD&nbsp;category is blank.</p>
<p>If you have any of the above combinations of data in the source table, Review these entries to adjust the right settings either by:</p>
<p>1. Deleting the duplicate FWSD type records</p>
<p>2. Deleting the records with blank FWSD type or specifying the FWSD type in such entries. To add the FWSD type where FWSD type is blank, you will need to copy such records in the source table (TCM_C_SLS_MAP) and enter the FWSD type before saving and then followed by deletion of records which are with blank FWSD types.</p>
<p>3. Maintain the same division and distribution channel for all the entries in the source table.</p>
<p>4. Maintain an appropriate logical system for all the entries.</p>
<p>5. Maintain the ECC sales organization and the FWSD category for all the entries.</p>
<p>6. In the table TCM_C_SLS_MAP, maintain the mapping of TM sales org with the ECC sales org which is the 'BSG Org ID' maintained in the TM org model</p>
<p>In order for the entries to be copied from the tables for TCM_C_TCD_MAP to the TCM_C_SLSASSGN tables it is required that the data in the source tables does not contain:</p>
<p>1. Entries where the logical system is blank</p>
<p>2. Entries where the TM sales organization is blank</p>
<p>3. Entries where the controlling area, internal order and cost center are blank</p>
<p>If you have any of the above combinations of data in the source table, Review these entries to adjust the right settings either by:</p>
<p>1. Maintain the appropriate logical system</p>
<p>2. Maintain the&nbsp;TM sales organization</p>
<p>3. Maintain the&nbsp;controlling area, internal order and cost center for all entries</p>
<p>In order for the entries to be copied from the tables for&nbsp;TCM_C_TCDPURMAP to the TCM_C_PURASSGN tables it is required that the data in the source tables does not contain:</p>
<p>1. Entries where the logical system is blank</p>
<p>2. Entries where the TM purchase organization is blank</p>
<p>3. Entries where the controlling area, internal order and cost center are blank</p>
<p>If you have any of the above combinations of data in the source table, Review these entries to adjust the right settings either by:</p>
<p>1. Maintain the appropriate logical system</p>
<p>2. Maintain the&nbsp;TM purchase organization</p>
<p>3. Maintain the&nbsp;controlling area, internal order and cost center for all entries</p>
<p><strong>NOTE:</strong></p>
<p>Before conversion, please also check if there are FWSDs or internal settlement documents in the source SAP TM system which are not yet posted to SAP ERP or the S/4 HANA system where these combinations in conflict&nbsp;are required for posting of the settlement document. If these settlement documents are posted post conversion, the new tables would be used for posting data.</p>
<p><strong>How to determine relevancy:</strong></p>
<p>In order to ensure that the data available in TCM_C_SLS_MAP table is consistent enough to be copied into the target table /SCMTMS/C_CFIR_T via an XPRA which will be executed during the upgrade / system conversion process, it is required that the source table doesn't contain:</p>
<p>1. Entries where FWSD type is blank</p>
<p>2. Entries for the same&nbsp;FWSD type but mapped to different sales document type/ billing type / billing item type and pricing procedure.</p>
<p><strong>Activities:</strong></p>
<ul>
<li>Data correction (Before or during conversion project, Mandatory): Run conversion pre-check and correct any found inconsistencies</li>
<li>Custom Code Adaption (During conversion project, Mandatory)</li>
<li>Business Operations (Before or during conversion project, Mandatory): Transfer open forwarding settlement documents or internal settlement documents from connected SAP TM.</li>
</ul>