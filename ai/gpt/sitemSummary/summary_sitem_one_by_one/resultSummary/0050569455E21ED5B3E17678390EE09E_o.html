<h3>SI11: PROC_MM_DYN</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267449">2267449 - S4TWL - Classic MM-PUR GUI Transactions replacement</a></strong></p>
<p><strong>Description:</strong></p>
<p>Classic SAP ERP Materials Management (MM) transactions and BAPIs for the business objects Purchase Order, Purchase Requisition, and Supplier Invoice have been replaced by the corresponding transactions and BAPIs made available with SAP R/3 Enterprise 4.70. With SAP S/4HANA, on-premise, these classic MM transactions and BAPIs are NO longer supported. The replacements that are available since SAP R/3 Enterprise 4.70 shall be used.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
  See SAP notes:
  <ul>
    <li>1803189: FAQ: End of Support of ME21, ME51, and BAPI_PO_CREATE etc.</li>
    <li>144081: Replacing MR01 / Functions of MR1M (Release 4.6)</li>
    <li>Specific data retrieval using BAPI_PO_GETDETAIL1 compared to BAPI_PO_GETDETAIL is limited and hence new BAPI_PO_GETDETAIL1 may consume more time.<br/>To improve response time, use BAdI me_bapi_po_cust method text_output if text data retrieval is not required.</li>
  </ul>
  <br/>
  <ul>
    <li><strong>Custom Code Adaption</strong> - Replace custom code referring to old BAPIs.</li>
    <li><strong>Customizing / Configuration</strong> - Optional.</li>
    <li><strong>User Training</strong> - Optional.</li>
  </ul>
</p>
<p><strong>Reference Notes:</strong> 
<ul>
  <li>1803189 - FAQ: End of Support of ME21, ME51, BAPI_PO_CREATE and Archiving Reports etc.</li>
  <li>217437 - ME21N/ME51N: Batch input and CATT not possible</li>
  <li>329437 - ME21N: Functions not available</li>
  <li>52505 - Support after end of mainstream maintenance or extended maintenance</li>
  <li>210502 - Incomplete documentation about single-screen applications in purchasing</li>
  <li>577398 - Enjoy PO BAPIs in SAP R/3 Release 4.6B</li>
  <li>493318 - FAQ: Purchase requisition (ME51N, ME52N, ME53N)</li>
  <li>491789 - FAQ: Purchase order (ME21N, ME22N, ME23N)</li>
  <li>144081 - Replacing MR01/functions of MR1M (Release 4.6)</li>
  <li>509915 - MRBR Invoices from R/2 are not selected</li>
  <li>487386 - MR01: Purchase order history incorrect</li>
  <li>428445 - Problems with MR01 in 4.6C (missing CO documents, etc.)</li>
  <li>402655 - MR08 : Error M8135 occurs.</li>
  <li>525451 - MR01: no line items according to HP31</li>
  <li>205311 - Extended withholding tax: Error 7Q328 or F5787</li>
  <li>338085 - MIRO/FB60: Direct posting to tax account</li>
  <li>381593 - MIRO/MIR7/MIRA: Batch Input is not possible</li>
  <li>539793 - MIRO: User-specific document type</li>
  <li>314609 - Logistics invc verifctn: Trnsctn MIRO replaces MR1M</li>
  <li>305277 - Check for duplicate credit memos</li>
  <li>304275 - Australia: Handling of GST on freight in Purchasing</li>
  <li>116272 - Replacing MR01/functions of MR1M (4.5B)</li>
  <li>904652 - MIRO: Different from FB60</li>
  <li>740877 - Cancellation of obsolete documents</li>
  <li>127366 - Replacing MR01/Functions of MR1M (Release 4.0B)</li>
  <li>544831 - IS Retail:Menu w/ incorrect entries for invoice verification</li>
</ul>
</p>