{"guid": "901B0E6C72791ED7A3DCBA21E10D20CE", "sitemId": "SI2: Loans_CML_Banking", "sitemTitle": "S4TWL - CML-specific functions with regard to collaterals and collateral objects", "note": 2369934, "noteTitle": "2369934 - S4TWL - CML-specific functions with regard to collaterals and collateral objects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RE, Classic, RE-FX Real Estate Management, RE Classic, RE-Classic</p>\n<p>Land Register, Land Registry</p>\n<p>Business Partner</p>\n<p>CML collaterals and collateral objects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>not</span> available in SAP S/4HANA (including the SAP Simple Finance editions).</p>\n<p>(Remark: See also the entry with the subject \"Real Estate Classic\" in the \"Simplification List for SAP S/4HANA, on-premise edition 1511\".)</p>\n<p>Therefore it's <span>not</span> allowed to use CML-specific functions with regard to collaterals and collateral objects in the context of SAP S/4HANA (including the SAP Simple Finance editions).</p>\n<p>Examples for transactions, which must not be used, are:</p>\n<ul>\n<li>Object Processing (Create, Change and Display) - FNO1, FNO2 and FNO3</li>\n<li>Collateral Value Calculation (Create, Change and Display) - FN61, FN62 and FN63</li>\n<li>Collateral (Create, Change and Display) - FNO5, FNO6 and FNO7</li>\n</ul>\n<p>Of course all customizing transactions and settings, which are related to the CML-specific functions with regard to collaterals and collateral objects, must not be used, too.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.</p>\n<p>The transition from the CML-specific functions with regard to collaterals and collateral objects to FS-CMS must take place <span>before</span> the transformation to SAP S/4HANA.</p>\n<p>In principle such a transition consists of two steps:</p>\n<ul>\n<li>Implementation (i.e. configuration and customizing etc.) of FS-CMS</li>\n<li>Migration of data from FS-CML to FS-CMS</li>\n</ul>\n<p> </p>", "noteVersion": 3, "refer_note": [{"note": "2211665", "noteTitle": "2211665 - Release information for \"SAP for Banking\" in connection with \"SAP S/4HANA, on-premise edition 1511\"", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning to use \"SAP S/4HANA, on-premise edition 1511\" in connection with applications or solutions from the \"SAP for Banking\" area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The applications or solutions listed below from the \"SAP for Banking\" area which are part of the \"SAP S/4HANA, on-premise edition are 1511\" delivery, have been released for use in \"SAP S/4HANA, on-premise edition 1511\":</p>\n<ul>\n<li>\n<div><span class=\"urTxtStd urVt1\">FS-BP Business Partner</span></div>\n</li>\n<li><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">IS-B-BCA Bank Current Accounts</span></span></li>\n<li><span class=\"urTxtStd urVt1\">FS-CML Consumer and Mortgage Loans</span></li>\n<li><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">FS-CMS Collateral Management</span></span></li>\n<li>\n<div><span class=\"urTxtStd urVt1\">FS-RBD Value Adjustment</span></div>\n</li>\n<li>\n<div><span class=\"urTxtStd urVt1\">AC-INT-ECS Error Correction and Suspense Accounting</span></div>\n</li>\n</ul>\n<p>Take into account the following restrictions:</p>\n<ul>\n<li><span class=\"urTxtStd urVt1\">FS-CML Consumer and Mortgage Loans</span>: The release is restricted to the following product categories:</li>\n<ul>\n<li>300 (Mortgage Loan)</li>\n<li>310 (Borrower's Note Loans)</li>\n<li>320 (Policy Loan)</li>\n<li>330 (General Loan)</li>\n<li>340 (Consumer Loan)</li>\n</ul>\n<li><span class=\"urTxtStd urVt1\">FS-CML Consumer and Mortgage Loans</span>: The release is restricted to Germany, Austria, and Switzerland. For other countries, a deployment analysis and a feasibility study must be conducted first.</li>\n<li>FS-CML Consumer and Mortgage Loans: It is important to note that the application component FI (Financial Accounting) <span>no longer</span> supports the calculation of interest on arrears at customer level (transaction F.24 or report RFDUZI00). For this, application FS-CML has been providing transaction FIOA for the calculation of interest on arrears for quite some time.</li>\n<li>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>no longer</span> available. This is due to the entry with the subject \"Real Estate Classic\" in the \"Simplification List for SAP S/4HANA, on-premise edition 1511\". At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.</li>\n<li>FS-RBD Value Adjustment: The release is restricted to Germany. For other countries, a deployment analysis and a feasibility study must be conducted first, whereby it must be ensured that identified (country-specific) requirements can be implemented via the customer-specific implementation project.</li>\n<li><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">IS-B-BCA Bank Current Accounts: The release is restricted to Germany, Austria, and Switzerland. For other countries, a deployment analysis and a feasibility study must be conducted first.</span></span></span></li>\n<li><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">IS-B-BCA Bank Current Accounts: The functions listed in SAP Note 2193911 are <span>not</span> supported.</span></span></span></li>\n<li><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">FS-BP business partner: </span></span></span></span></span>If the application \"SAP Master Data Governance\" (SAP MDG) is active in the environment of the SAP business partner, a distribution or replication of business partner data using the message type \"ABABusinessPartner\" is <span>not</span> supported. As a result, the integration scenario \"BusinessPartnerReplication\" that is based on SAP NetWeaver Process Integration (PI / XI) and message type ABABusinessPartner cannot be used. (Additional key words: Namespace \"http://sap.com/xi/ABA\", message interfaces \"ABABusinessPartnerIn\" and \"ABABusinessPartnerOut\").<br/>Remark: This restriction no longer applies as of Release SAP S/4HANA 2021. As of this release, the data sets of the FSBP are integrated in the \"BusinessPartnerSUITEBulkReplicateRequest\" message types. As a result, it is no longer required to use the message type \"http://sap.com/xi/ABA\". For more information, see SAP Note 3102366. See also SAP Note 2890450.<br/>In addition, the parallel use of the two message types \"ABABusinessPartner\" and \"BusinessPartnerSUITEBulkReplicateRequest\" is <span>not</span> supported.</li>\n<li>FS-BP business partner: In accordance with SAP Note 2448350, data dependent on differentiation categories in FS-BP (for example, ratings dependent on the differentiation category) is <span>no longer</span> supported in SAP S/4HANA.</li>\n</ul>\n<p>In addition, the GL interface of the Bank Analyzer has been released in connection with \"SAP S/4HANA, on-premise edition 1511\" via the enterprise service \"<span 'times=\"\" 11pt;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" font-size:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">AccountingDocumentERPBulkNotification_In</span>\".</p>\n<p>SAP Note 2543469 contains additional information on the use of SAP extractors (SAP data sources).</p>\n<p>The application \"FS-CYT Capital Yield Tax Management\" is a separate software component (that is, it is not a part of the \"SAP S/4HANA, on-premise edition 1511\" delivery). As of Support Package 8 for Release \"SAP Capital Yield Tax Management for Banking 8.0\", FS-CYT can be used as an add-on for \"SAP S/4HANA, on-premise edition 1511\".</p>\n<p>As of Release 8.0 and Support Package 03, the application \"SAP Payment Engine\" (FS-PE Payment Engine) is released for integration with \"SAP S/4HANA, on-premise edition 1511\".</p>\n<p>The application \"Multi Currency Accounting for Banks\" (<span class=\"urTxtStd urVt1\">FI-GL-CU-MCA</span>) is released for use as of Support Package 02 for \"SAP S/4HANA on-premise edition 1511\". However, not the following restriction: To be able to use this application, you require the \"New G/L\". For more information, see also SAP Note 2236517.</p>\n<p>The application \"Average Daily Balance\" (<span class=\"urTxtStd urVt1\">FI-GL-GL-ADB</span>) is released for use as of Support Package 02 for \"SAP S/4HANA on-premise edition 1511\". However, note the following restriction: To be able to use this application, you require the \"New G/L\". In addition, monthly submitter scenarios and value correction scenarios are <strong><span>not</span></strong> supported. For more information, see also SAP Note 2231634.</p>\n<p>If you use the application FI-CA (Contract Accounts Receivable and Payable), note that a maximum of one FI-CA-based industry solution can be active for each system (for example, either FI-CAX or FS-CD). The following FI-CA-based industry solutions exist: FI-CAX, FS-CD, IS-U, RM-CA, PSCD.</p>\n<p>In addition, note that the applications or solutions listed below have <span><strong>not</strong></span> been released for use in \"SAP S/4HANA, on-premise edition 1511\":</p>\n<ul>\n<li>SAP Leasing as well as the Lease Accounting Engine (<span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">CRM-LAM Leasing, </span></span><span class=\"urTxtStd urVt1\">FI-LA Lease Accounting Engine</span>)</li>\n<li>All SEM Banking components (<span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">IS-B-DP Transaction Datapool, </span></span><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">IS-B-PA Profit Analyzer, </span></span><span class=\"urTxtStd urVt1\"><span class=\"urTxtStd urVt1\">IS-B-RA Risk Analyzer, </span></span><span class=\"urTxtStd urVt1\">IS-B-SA Strategy Analyzer</span>)</li>\n<li>\n<div><span class=\"urTxtStd urVt1\">FS-TXS Funding Management</span></div>\n</li>\n</ul>\n<p>In addition, note that customer-specific implementations or customer-specific source codes must not reference applications or solutions that have not been released for use in \"SAP S/4HANA, on-premise edition 1511\". The use of development objects (for example, APIs) of applications or solutions that have not been released is therefore not permitted.</p>\n<p> </p>", "noteVersion": 13, "refer_note": [{"note": "2189824", "noteTitle": "2189824 - SAP S/4HANA, on-premise edition 1511: Release Information Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This Release Information Note (RIN) contains information and references to notes for applying Feature Package (FP)/Support Package (SP) Stacks of product version \"SAP S/4HANA, on-premise edition 1511\".</p>\n<p>Check this note for changes on a regular basis. All changes made after release of a Feature Package (FP)/Support Package (SP) Stack are documented in section \"Changes made after Release of FP/SP Stack &lt;xx&gt;\".</p>\n<p><strong><strong>Note</strong>:</strong> This SAP Note is subject to change.</p>\n<p><br/><strong>GENERAL INFORMATION</strong></p>\n<p>SAP S/4HANA, on-premise edition 1511 is out of Maintenance since 31.12.2020. For further information about the maintenance strategy please refer to SAP note <a href=\"/notes/52505\" target=\"_blank\">52505</a> and SAP note <a href=\"/notes/2311392\" target=\"_blank\">2900388</a>.<a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?view=bsp&amp;param=69765F6D6F64653D3030312669765F7361706E6F7465735F6B65793D30313130303033353837303030303932373037303230303126766965773D627370\" target=\"_blank\"><br/></a></p>\n<p>We strongly recommend to upgrade your system to the latest SAP S/4HANA on-premise release.</p>\n<p><strong>Installation &amp; System Conversion Information</strong></p>\n<ul>\n<li>You need at least Software Provisioning Manager (SWPM) SP20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <strong><a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a></strong><strong>.</strong></li>\n<li>Please consider note <strong><a href=\"/notes/2311392\" target=\"_blank\">2311392</a> </strong>for the SAP S/4HANA, on-premise edition 1511 installation procedure.</li>\n<li>\n<p>Please note that a system conversion to SAP S/4HANA 1511 and 1610 is no longer possible or supported</p>\n<ul>\n<li>for newly starting system conversion projects – independently of the Enhancement Package or SP level of the SAP ERP source system.</li>\n<li>for any system which is below the following SAP ERP SP levels and have to go to S/4HANA 1511 and 1610, open a message on CA-TRS-PRCK</li>\n<li>for any system which is on the following SAP ERP SP levels or higher: 600 SP30, 602 SP20, 603 SP19, 604 SP20, 605 SP17, 606 SP20, 616 SP12, 617 SP15, 618 SP09</li>\n</ul>\n<p>You can convert to the successor product versions of SAP S/4HANA.</p>\n</li>\n<li>If you have add-ons installed on your system and/or want to use them on product version \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note<strong><a href=\"/notes/2214409\" target=\"_blank\">2214409</a></strong><strong>.</strong></li>\n<li>For the installation, please refer to the specific documentation under section<strong> '</strong><strong><a href=\"http://help.sap.com/s4hana_op_1511\" target=\"_blank\">product documentation</a></strong><strong>' </strong>and SAP note<strong> </strong><strong><a href=\"/notes/0002321671\" target=\"_blank\">2321671</a></strong><strong> (conversion-related information).</strong></li>\n<li>Please note that new installations are only possible with Support Release 1 (SR1) which is based on Feature Package Stack 02.</li>\n<li>After installation of SAP S/4HANA, on-premise edition 1511 Feature Package Stack level 00 or 01 several customizing piece lists are missing in the system. The missing customizing piece lists can be transported via note <a href=\"/notes/2278104\" target=\"_blank\">2278104</a> into the system.</li>\n</ul>\n<p><strong>SAP HANA database requirements</strong></p>\n<ul>\n<li>The minimum required revision is defined in the respective Support or Feature Package Stack chapter.</li>\n<li>Detailed information about SAP HANA 2.0 Revision and Maintenance Strategy can be found in SAP note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a>.</li>\n<li>If you plan to upgrade your SAP HANA Database to a newer revision or a newer available SPS level,</li>\n<ul>\n<li>Refer to SAP note <a href=\"/notes/1906576\" target=\"_blank\">1906576</a> for SAP HANA client and server cross-version compatibility and</li>\n<li>Refer to SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> for restrictions and recommendations regarding specific revisions of SAP HANA database for use in SAP S/4HANA.</li>\n</ul>\n</ul>\n<p><strong>Further Important Considerations &amp; Notes</strong></p>\n<ul>\n<li>For general restrictions, please refer to SAP note <a href=\"/notes/2214213\" target=\"_blank\">2214213</a>.</li>\n<li>For release information and restrictions concerning topic \"globalization\", please refer to SAP note <a href=\"/notes/2228890\" target=\"_blank\">2228890</a>.</li>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".</li>\n<li>For process integration capabilities with other SAP on-premise solutions, please refer to SAP note <a href=\"/notes/2241931\" target=\"_blank\">2241931</a>.</li>\n<li>Please be aware that SAP S/4HANA on premise edition 1511 is an Unicode-only release. Non-Unicode systems are not supported anymore. Hence upgrades of non-Unicode systems without prior Unicode conversion is not possible. For details see <a href=\"https://service.sap.com/~sapidb/012002523100009958832014E/\" target=\"_blank\">Upgrade of non Unicode systems</a> or <a href=\"https://service.sap.com/Unicode\" target=\"_blank\">service.sap.com/Unicode </a>and SAP note <a href=\"/notes/2033243\" target=\"_blank\">2033243</a>.</li>\n<li>The supported Kernel versions are: SAP KERNEL 7.45 64-BIT UNICODE, SAP KERNEL 7.49 64-BIT UNICODE and SAP KERNEL 7.53 64-BIT UNICODE.</li>\n<li><strong>United Kingdom leaving the EU</strong>: For information on how a “hard Brexit” (= a no-deal scenario) would impact your <em>SAP S/4HANA </em>system, please see SAP Note <strong><a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.</strong></li>\n<li><strong>For more information regarding LEGAL VAT TAX CHANGE in Germany<strong> , please see SAP note </strong><span><a href=\"/notes/2934992\" target=\"_blank\">2934992</a></span></strong></li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA On-Premise; SAP S/4HANA, on-premise edition 1511;</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to get additional information about product version \"SAP S/4HANA, on-premise edition 1511\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>FEATURE PACKAGE STACK 00 (11/2015)</span></strong></p>\n<p>Information and references to Feature Package Stack 00 removed on 08 September 2016, as Feature Package Stack 00 is no longer available for download. <strong> </strong></p>\n<p><strong><span>FEATURE PACKAGE STACK 01 (02/2016)</span></strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section '<a href=\"http://help.sap.com/s4hana_op_1511_001?current=s4hana_op_1511_002\" target=\"_blank\">product documentation</a>'.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Feature Package Stack 01 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>Support Package Stack 01 of \"SAP Fiori 1.0 for SAP S/4HANA\" on the frontend requires Feature Package Stack 01 of \"SAP S/4HANA, on-premise edition 1511\" in the backend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2246714\" target=\"_blank\">2246714</a>, (SAP S/4HANA On-Premise 1511 - SAP S/4HANA Server SPS01 (02/2016) content activation note), when you are using the core configurator for your SAP S/4HANA.</li>\n<li>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\n<li>If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1511 Feature Package Stack 01 and the target is SAP NetWeaver Support Package 07 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n<li>\n<p><em></em><strong>United Kingdom leaving the EU</strong>: For information on how a “hard Brexit” (= a no-deal scenario) would impact your SAP S/4HANA system, please see SAP Note <a href=\"/notes/2749671\" target=\"_blank\">2749671</a><strong>.</strong></p>\n</li>\n<li>\n<p>For the implementation of ELSTER modules using the ERiC libraries and the corresponding impact on platform support, please see notes <a href=\"/notes/2745249\" target=\"_blank\">2745249</a> and <a href=\"/notes/2558316\" target=\"_blank\">2558316</a>.</p>\n</li>\n</ul>\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Feature Package Stack 01.</li>\n<li>Please refer to note <a href=\"/notes/2265804\" target=\"_blank\">2265804</a> (XPRA generates log entry during SP update).</li>\n<li>If you plan to import SAP_BW 750 SP04 in your system without updating component S4CORE to SP02 in the same queue, please implement note <a href=\"/notes/2290115\" target=\"_blank\">2290115</a> (Syntax error for structure PRH_CPET_PERIODDET_PRCQUOT after applying note 2286922). <strong> </strong></li>\n<li>Please refer to note <a href=\"/notes/2274264\" target=\"_blank\">2274264</a> (Missing Views in DB02).</li>\n</ul>\n<p><strong>Notes to be applied on top of <strong>Feature Package Stack 01:</strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"682\">\n<p><strong>Description</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/1957910\" target=\"_blank\">1957910</a></p>\n</td>\n<td width=\"682\">\n<p>Directory traversal in BC-CCM-FIL</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2150804\" target=\"_blank\">2150804</a></p>\n</td>\n<td width=\"682\">\n<p>get_auth_values: Suppression of equivalent authorizations</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2219213\" target=\"_blank\">2219213</a></p>\n</td>\n<td width=\"682\">\n<p>No results for search with attribute that has no value; sorting problem in search with extended search attributes</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2219946\" target=\"_blank\">2219946</a></p>\n</td>\n<td width=\"682\">\n<p>CPE: Price Fixation Exercise with 0,00 is not considered (PI_BASIS)</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2221472\" target=\"_blank\">2221472</a></p>\n</td>\n<td width=\"682\">\n<p>Incorrect handling of \"to\" values in BICS search UIBB</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2221877\" target=\"_blank\">2221877</a></p>\n</td>\n<td width=\"682\">\n<p>Connector generation and virtual models</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2221910\" target=\"_blank\">2221910</a></p>\n</td>\n<td width=\"682\">\n<p>Check of master data read class not possible</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2222561\" target=\"_blank\">2222561</a></p>\n</td>\n<td width=\"682\">\n<p>ESH: Authorizations for S_ESH_CONN and S_ESH_CATEG are not updated</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2223158\" target=\"_blank\">2223158</a></p>\n</td>\n<td width=\"682\">\n<p>ES CDS: 'No authorization for ... connectors'</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2223513\" target=\"_blank\">2223513</a></p>\n</td>\n<td width=\"682\">\n<p>Dump in Web Dynpro Component C_SRT_MESSAGE_LIST</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2223603\" target=\"_blank\">2223603</a></p>\n</td>\n<td width=\"682\">\n<p>Handling of initial values</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2224044\" target=\"_blank\">2224044</a></p>\n</td>\n<td width=\"682\">\n<p>Webdynpro Value Suggest: Error if value suggest is disabled via table DSHCONFIG</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2224686\" target=\"_blank\">2224686</a></p>\n</td>\n<td width=\"682\">\n<p>ESH: Assignment of categories in ESH cockpit fails</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2224839\" target=\"_blank\">2224839</a></p>\n</td>\n<td width=\"682\">\n<p>Correction of FLE Adjustments in BAPI_OBJCL_GETCLASSES</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2225055\" target=\"_blank\">2225055</a></p>\n</td>\n<td width=\"682\">\n<p>SNOTE: Missing error message if package does not exist</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2225270\" target=\"_blank\">2225270</a></p>\n</td>\n<td width=\"682\">\n<p>WDA: SAP Fiori launchpad: In the case of applications with the URL parameter SAP_XAPP_STATE, methods of the interface IF_WD_FLP_API result in a termination</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2225294\" target=\"_blank\">2225294</a></p>\n</td>\n<td width=\"682\">\n<p>SNOTE: Termination of posting due to defective software component stacks</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2225542\" target=\"_blank\">2225542</a></p>\n</td>\n<td width=\"682\">\n<p>Error during import of software component with included software component</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2225565\" target=\"_blank\">2225565</a></p>\n</td>\n<td width=\"682\">\n<p>Analytical GUIBB - URL parameters for variable screen</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2232474\" target=\"_blank\">2232474</a></p>\n</td>\n<td width=\"682\">\n<p>Incorrect issue of error message FGV003: \"Period 012 is not valid in financial year variant K4\".</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2233026\" target=\"_blank\">2233026</a></p>\n</td>\n<td width=\"682\">\n<p>Classification Mapper Input and Export Parameters should be passed as variable</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2233210\" target=\"_blank\">2233210</a></p>\n</td>\n<td width=\"682\">\n<p>FPM List UIBB (old List UIBB): OVS value help: Changelog is not complete</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2234476\" target=\"_blank\">2234476</a></p>\n</td>\n<td width=\"682\">\n<p>CPE Quotation Rule Relevant fields disappears after change</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2236109\" target=\"_blank\">2236109</a></p>\n</td>\n<td width=\"682\">\n<p>Analytical GUIBB - Semantic objects for variables</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2236774\" target=\"_blank\">2236774</a></p>\n</td>\n<td width=\"682\">\n<p>Design Studio correction for sFIN (NW 7.50 SP1 Patch1)</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2237136\" target=\"_blank\">2237136</a></p>\n</td>\n<td width=\"682\">\n<p>Fix 'Exchange rate' if 'Exercise UoM' is same as 'Quotation UoM'</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2237376\" target=\"_blank\">2237376</a></p>\n</td>\n<td width=\"682\">\n<p>Task Gateway: Reading work item details for resubmitted work item might return error message</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2241307\" target=\"_blank\">2241307</a></p>\n</td>\n<td width=\"682\">\n<p>Analytical GUIBB - Dump while using intent based navigat.</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2245874\" target=\"_blank\">2245874</a></p>\n</td>\n<td width=\"682\">\n<p>Application jobs/application logs - Version 1.7.8</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2246238\" target=\"_blank\">2246238</a></p>\n</td>\n<td width=\"682\">\n<p>Termination during execution of input help for 0CLIENT (client)</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2246712\" target=\"_blank\">2246712</a></p>\n</td>\n<td width=\"682\">\n<p>SAP GUI F4 Control: Search help is not getting instantiated in web gui</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2254555\" target=\"_blank\">2254555</a></p>\n</td>\n<td width=\"682\">\n<p>Dump during access to classifications</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2260731\" target=\"_blank\">2260731</a></p>\n</td>\n<td width=\"682\">\n<p>Application jobs/application logs - Version 1.7.9</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2271007\" target=\"_blank\">2271007</a></p>\n</td>\n<td width=\"682\">\n<p>Messages regarding an active document instance are not returned from draft actions EDIT and ACTIVATION</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2272709\" target=\"_blank\">2272709</a></p>\n</td>\n<td width=\"682\">\n<p>Variables are not replaced in the text of messages that are returned from draft actions EDIT and ACTIVATION</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><a href=\"/notes/2232524\" target=\"_blank\">2232524</a></p>\n</td>\n<td width=\"682\">\n<p>Service lines disappear during the change mode when using Work Order (WEBGUI)</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2235089\" target=\"_blank\">2235089</a></p>\n</td>\n<td width=\"682\">\n<p>Issues with Create Purchase Requisition application</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2241902\" target=\"_blank\">2241902</a></p>\n</td>\n<td width=\"682\">\n<p>Posting G/L Documents Reuse Library - Corrections for SAP Fiori for S/4HANA Finance, on-premise Edition 1602 SP1 and SAP Fiori S/4HANA on-premise Edition 1511 SP1</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2246004\" target=\"_blank\">2246004</a></p>\n</td>\n<td width=\"682\">\n<p>S4H Adobe forms not working in QE4</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2243862\" target=\"_blank\">2243862</a></p>\n</td>\n<td width=\"682\">\n<p>More than 12 items (Servcie Lines) disappear when using PM (WEBGUI)</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2248702\" target=\"_blank\">2248702</a></p>\n</td>\n<td width=\"682\">\n<p>Incompatible call of RFC VBDBDM_DATA_MAINTAIN_RFC - VBTYP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2249407\" target=\"_blank\">2249407</a></p>\n</td>\n<td width=\"682\">\n<p>Compatibility issues of structure BAPISOLDTO</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2254182\" target=\"_blank\">2254182</a></p>\n</td>\n<td width=\"682\">\n<p>Enable Screen Variants for Valuation Posting</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2258298\" target=\"_blank\">2258298</a></p>\n</td>\n<td width=\"682\">\n<p>Audit Journal: Multireferenced invoices can't be displayed</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2259744\" target=\"_blank\">2259744</a></p>\n</td>\n<td width=\"682\">\n<p>\"My Purch. Doc. Items\" and \"Manage Purchase Orders\": no result when search with supplier</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2262777\" target=\"_blank\">2262777</a></p>\n</td>\n<td width=\"682\">\n<p>Rename duplicate fields in Journal Entry Fact Sheet App</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2262941\" target=\"_blank\">2262941</a></p>\n</td>\n<td width=\"682\">\n<p>Rename duplicate fields in G/L Account Fact Sheet app</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2263957\" target=\"_blank\">2263957</a></p>\n</td>\n<td width=\"682\">\n<p>Manage Purchase Requisitions: Unable to create draft purchase order for Purchase Requisitions</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2264601\" target=\"_blank\">2264601</a></p>\n</td>\n<td width=\"682\">\n<p>Error in CAT2 : Pernr is not active on a particular date</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2265586\" target=\"_blank\">2265586</a></p>\n</td>\n<td width=\"682\">\n<p>My Purchase Requisitions Worklist: PR list issues</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2266418\" target=\"_blank\">2266418</a></p>\n</td>\n<td width=\"682\">\n<p>Invalid certificate ID for Software Certification in Portugal in SAP S/4HANA, on-premise edition 1511</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2267760\" target=\"_blank\">2267760</a></p>\n</td>\n<td width=\"682\">\n<p>My Purchase Requisition Work list issues</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2268673\" target=\"_blank\">2268673</a></p>\n</td>\n<td width=\"682\">\n<p>\"My Purchasing Document Items\"- Purchase Requisitions and Invoice are not shown correctly in list</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2268700\" target=\"_blank\">2268700</a></p>\n</td>\n<td width=\"682\">\n<p>Display Financial Statement shows incorrect data</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2269062\" target=\"_blank\">2269062</a></p>\n</td>\n<td width=\"682\">\n<p>Programmabbruch in Servicemeldung bei Anzeige Belegfluß</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2269398\" target=\"_blank\">2269398</a></p>\n</td>\n<td width=\"682\">\n<p>Account Assignment corrections</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2269505\" target=\"_blank\">2269505</a></p>\n</td>\n<td width=\"682\">\n<p>My Purchase Requisitions Worklist: Duplicate records appear for Purchase Requisitions with multiple items</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2269970\" target=\"_blank\">2269970</a></p>\n</td>\n<td width=\"682\">\n<p>Purchase Requisition header status shown as Rejected during creation</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2271810\" target=\"_blank\">2271810</a></p>\n</td>\n<td width=\"682\">\n<p>Account assignment changes</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2272096\" target=\"_blank\">2272096</a></p>\n</td>\n<td width=\"682\">\n<p>S4HANA - VMS : Characteristic value is missed due to non-adjustment of Material Field Length Extension</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2272149\" target=\"_blank\">2272149</a></p>\n</td>\n<td width=\"682\">\n<p>Discarding draft results into deleting the Active Purchase Requisition</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Additional notes to be implemented on top of Feature Package Stack 01 <br/>(update after Feature Package Stack 01 release information has been released):</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><a href=\"/notes/2274197\" target=\"_blank\">2274197</a></td>\n<td>Ledger/company settings</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2362815\" target=\"_blank\">2362815</a></td>\n<td>Material Ledger (ML) and Retail: tied empties: error CKMLMV 009 in LCKMLMVQUANTF06</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2372605\" target=\"_blank\">2372605</a></td>\n<td>Issue while creating BP role as a supplier in transaction BP</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2369405\" target=\"_blank\">2369405</a></td>\n<td>Issue in Document Flow (SAP S/4HANA)</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2384182\" target=\"_blank\">2384182</a></td>\n<td>S/4HANA 1511+1610: Material master maintenance: Dumps when using screen sequence DI</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2399372\" target=\"_blank\">2399372</a></td>\n<td>Authorization check failed for SRT_SR_P</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2400809\" target=\"_blank\">2400809</a></p>\n</td>\n<td>\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2427850\" target=\"_blank\">2427850</a></p>\n</td>\n<td>\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td>\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2445210\" target=\"_blank\">2445210</a></p>\n</td>\n<td>\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></p>\n</td>\n<td>\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></p>\n</td>\n<td>\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td>\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> The implementation of the following notes requires manual activities:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><a href=\"/notes/2222894\" target=\"_blank\">2222894</a></p>\n</td>\n<td width=\"682\">\n<p>RFC communication error to S/4HANA classification system</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2268608\" target=\"_blank\">2268608</a></p>\n</td>\n<td width=\"682\">\n<p>Prozess 'Organisationseinheit anlegen' (HRPAO_CREATE_ORGUNIT): Zuordnung der Regel 'ACCOUNT_ASSIGNMENT' zu Infotyp 1008 fehlt</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2399423\" target=\"_blank\">2399423</a></p>\n</td>\n<td width=\"682\">\n<p>Dump in characteristic value assignment</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td width=\"682\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2429609\" target=\"_blank\">2429609</a></p>\n</td>\n<td width=\"682\">\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 01</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-12-12: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong> SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) relenvant for migration to SAP S/4HANA and SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).<br/><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<strong><strong><strong><strong><strong><br/>2017-08-30</strong>: </strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 102.2 to 122.05 to synchronize with requirements in SUM tool.<br/><strong>2017-03-31: </strong>SAP HANA 2.0 chapter inserted under 'General / Important Considerations'.<br/><strong>2017-04-07: </strong>Inserted: If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1511 Feature Package Stack 01 and the target is SAP NetWeaver Support Package 07 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.<br/><strong>2017-05-08: </strong>Inserted under Feature Package Update: 'It is recommed to use SUM to apply Feature Package Stacks.'<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>\n<p><strong><span>FEATURE PACKAGE STACK 02 (05/2016)</span></strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section '<a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>'.</li>\n</ul>\n<ul>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n</ul>\n<ul>\n<li>Feature Package Stack 02 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n<li>In case you use SAP HANA 1.0 Revision 111 please be aware of note <a href=\"/notes/2275220\" target=\"_blank\">2275220</a> for your installation/conversion.</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>Support Package Stack 02 of \"SAP Fiori 1.0 for SAP S/4HANA\" on the frontend requires Feature Package Stack 02 of \"SAP S/4HANA, on-premise edition 1511\" in the backend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2303306\" target=\"_blank\">2303306</a>, (SAP S/4HANA On-Premise 1511 - SAP S/4HANA Server SPS02 (05/2016) content activation note), when you are using the core configurator for your SAP S/4HANA.</li>\n<li>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\n<li>If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1511 Feature Package Stack 02 and the target is SAP NetWeaver Support Package 07 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>Please note that a new installation is only possible with Support Release (SR1) which is based on Feature Package Stack 02. </li>\n</ul>\n<p> </p>\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Feature Package Stack 02.</li>\n<li>Please refer to note <a href=\"/notes/2274264\" target=\"_blank\">2274264</a> (Missing Views in DB02).</li>\n</ul>\n<p><strong>Notes to be applied on top of Feature Package Stack 02:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"118\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2274300\" target=\"_blank\">2274300</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Changes for unicode compatible BC-SET upload and download</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2287446\" target=\"_blank\">2287446</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SQL exception for SQL statement on CDS view using a table function that does not exist in the database</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2288597\" target=\"_blank\">2288597</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Material Field Length Extension related correction in ECM for BOR, Change Document, IDoc compatibility</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2291317\" target=\"_blank\">2291317</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Creation of connectors - regeneration of missing indexes: Termination with type conflict</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2303617\" target=\"_blank\">2303617</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>TPM100: Various errors when running on Hana Database</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2310718\" target=\"_blank\">2310718</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Short dump while accessing Fiori based S4H Purchasing applications</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2312131\" target=\"_blank\">2312131</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Conversion Unit check</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2314971\" target=\"_blank\">2314971</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Purchase Requisition is not displayed when opened from My PR app</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-05-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2326555\" target=\"_blank\">2326555</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Syntax Error in CL_SADL_GW_GENERIC_DPC</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-07-25</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2335769\" target=\"_blank\">2335769</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Odata V4 Decoupling</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-07-25</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2362815\" target=\"_blank\">2362815</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Material Ledger (ML) and Retail: tied empties: error CKMLMV 009 in LCKMLMVQUANTF06</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-04</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372605\" target=\"_blank\">2372605</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Issue while creating BP role as a supplier in transaction BP</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-05</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2369405\" target=\"_blank\">2369405</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Issue in Document Flow (SAP S/4HANA)</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-07</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2344014\" target=\"_blank\">2344014</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SPAU Adjustment for R3TR CLAS deliveries - adjustment of obsolete SAP notes deletes classes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-26</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2284857\" target=\"_blank\">2284857</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Number ranges - trace</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-26</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2346821\" target=\"_blank\">2346821</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SCWB - TLOGO Language(1Q,2Q,3Q,4Q etc) Filtering Fix</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-26</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2350429\" target=\"_blank\">2350429</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>External view with more than 255 fields</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-26</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345087\" target=\"_blank\">2345087</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BP_BAP: Missing values in required entry fields cause posting termination in mass processing</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-26</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2355000\" target=\"_blank\">2355000</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Parameter for ledger groups of underlying ledgers</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2364253\" target=\"_blank\">2364253</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Syntax errors due to missing development package assignment</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2380548\" target=\"_blank\">2380548</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Error during assignment to investment program item, maintenance order</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2367508\" target=\"_blank\">2367508</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Correction for selection of changedocs in DIMP system</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2379565\" target=\"_blank\">2379565</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>The Incoterm 2 field is converted incorrectly</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2381346\" target=\"_blank\">2381346</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Characteristic based planning aborts for long material numbers</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2172384\" target=\"_blank\">2172384</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>J1INCHLN and J1INCHLC: Multiple section legal change</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-03</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2384182\" target=\"_blank\">2384182</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S/4HANA 1511+1610: Material master maintenance: Dumps when using screen sequence DI</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399372\" target=\"_blank\">2399372</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Authorization check failed for SRT_SR_P</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399423\" target=\"_blank\">2399423</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump in characteristic value assignment</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2400809\" target=\"_blank\">2400809</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2427850\" target=\"_blank\">2427850</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2429609\" target=\"_blank\">2429609</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-17</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-04-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span 'times=\"\" 107%;=\"\" ar-sa;=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-ascii-theme-font:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" mso-hansi-theme-font:=\"\" new=\"\" roman';=\"\"><a href=\"/notes/2445210\" target=\"_blank\"><span arial',sans-serif;\"=\"\">2445210</span></a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><span 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</span></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><span 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">BRFplus: Numeric Comparison doesn't work within IF-FORMULA</span></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><span 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ALV export: Cannot save to clipboard in browser</span></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 02</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-12-12: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) relenvant for migration to SAP S/4HANA and SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).<br/><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<strong><strong><strong><strong><strong><strong><br/>2017-08-30</strong>: </strong></strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 102.2 to 122.05 to synchronize with requirements in SUM tool.<br/><strong>2016-11-03</strong>: System Conversion: Note 2269871 replaced by note <a href=\"/notes/0002321671\" target=\"_blank\">2321671</a>. Feature Package Update: Note <a href=\"/notes/2265804\" target=\"_blank\">2265804</a> (XPRA generates log entry during SP update) removed.<br/><strong>2017-03-31: </strong>SAP HANA 2.0 chapter inserted under 'General / Important Considerations'.<br/><strong>2017-04-07: </strong>Inserted: If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1511 Feature Package Stack 02 and the target is SAP NetWeaver Support Package 07 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.<br/><strong>2017-05-08: </strong>Inserted under Feature Package Update: 'It is recommed to use SUM to apply Feature Package Stacks.'<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>\n<p><strong><span>SUPPORT PACKAGE STACK 03 (11/2016)</span></strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Support Package Stack 03 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>Support Package Stack 03 of \"SAP Fiori 1.0 for SAP S/4HANA\" on the frontend requires Support Package Stack 03 of \"SAP S/4HANA, on-premise edition 1511\" in the backend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2373459\" target=\"_blank\">2373459</a>, (SAP S/4HANA, on-premise edition 1511 - SAP S/4HANA ON-PREMISE 1511 FP stack 03 (11/2016) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\n<li>If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1511 Support Package Stack 03 and the target is SAP NetWeaver Support Package 07 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 03.</li>\n<li><span>Prior</span> to the Update to Support Package Stack 3 please refer to the following note:</li>\n<ul>\n<li>Note <a href=\"/notes/2388040\" target=\"_blank\">2388040</a> (SAP S/4HANA on-premise edition 1511: Clean-up of CDS view inconsistencies prior to the update to SP Stack 3)</li>\n</ul>\n<li><span>After</span> the Update to Support Package Stack 3 please refer to following notes:</li>\n<ul>\n<li>[Relevant if started from SR1] In case the log file for SAPR-80003INISPSCA shows the error message “A3EEFDT_TRANSPORT 015 BRF+: Foreign lock &lt;…&gt; FDT source/target objects could not be locked” repeat the XPRA phase.</li>\n<li>Note <a href=\"/notes/2274264\" target=\"_blank\">2274264</a> (Missing Views in DB02)</li>\n<li>Note <a href=\"/notes/2387681\" target=\"_blank\">2387681</a> (Audit Journal DCL error message)</li>\n</ul>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 03:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"117\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2338666\" target=\"_blank\">2338666</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Passive substitutions in task gateway (backend part)</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/0002366619\" target=\"_blank\">2366619</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Data element MANDT (005056912EC51ED68685D408FB2C3AC3)</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2367479\" target=\"_blank\">2367479</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>TIMESTAMP</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2372605\" target=\"_blank\">2372605</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Issue while creating BP role as a supplier in transaction BP</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2379565\" target=\"_blank\">2379565</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>The Incoterm 2 field is converted incorrectly</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2380548\" target=\"_blank\">2380548</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Error during assignment to investment program item, maintenance order</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2381346\" target=\"_blank\">2381346</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Characteristic based planning aborts for long material numbers</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2172384\" target=\"_blank\">2172384</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>J1INCHLN and J1INCHLC: Multiple section legal change</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>FI-CA</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-11-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2397320\" target=\"_blank\">2397320</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Architecture: 'Measurement Is Overwritten Manually' indicator removed</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2016-12-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2384182\" target=\"_blank\">2384182</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>S/4HANA 1511+1610: Material master maintenance: Dumps when using screen sequence DI</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2399372\" target=\"_blank\">2399372</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Authorization check failed for SRT_SR_P</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2399423\" target=\"_blank\">2399423</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Dump in characteristic value assignment</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2400809\" target=\"_blank\">2400809</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2415736\" target=\"_blank\">2415736</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Warranty : Post Versions to Reimburser fails via action A042 in transaction WTY</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-01-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-01-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2427850\" target=\"_blank\">2427850</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-02-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2429609\" target=\"_blank\">2429609</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-02-17</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2428086\" target=\"_blank\">2428086</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Unable to Create New Line in Empty DataGrid</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BW</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-02-20</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-04-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2445210\" target=\"_blank\">2445210</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"117\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"367\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"189\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"420\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"234\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Support Package Stack 03</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-12-12: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) relenvant for migration to SAP S/4HANA and SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).<strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><br/>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<br/><strong><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 102.2 to 122.05 to synchronize with requirements in SUM tool.<br/><strong>2017-03-31: </strong>SAP HANA 2.0 chapter inserted under 'General / Important Considerations'.<br/><strong>2017-04-07: </strong>Inserted: If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1511 Support Package Stack 03 and the target is SAP NetWeaver Support Package 07 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>\n<p><strong><span>SUPPORT PACKAGE STACK 04 (05/2017)</span></strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n</ul>\n<ul>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n</ul>\n<ul>\n<li>Support Package Stack 04 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0\r\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n</li>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 04 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).<br/>Please refer to note <a href=\"/notes/2373459\" target=\"_blank\">2373459</a>, (SAP S/4HANA, on-premise edition 1511 - SAP S/4HANA ON-PREMISE 1511 FP stack 03 (11/2016) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 04.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 04:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2445210\" target=\"_blank\">2445210</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2428086\" target=\"_blank\">2428086</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Unable to Create New Line in Empty DataGrid</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BW</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-05-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-05-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-08-28</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Support Package Stack 04</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-12-12: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) relenvant for migration to SAP S/4HANA and SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).<strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><br/>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<br/><strong><strong><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 102.2 to 122.05 to synchronize with requirements in SUM tool.<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 05 (11/2017)</strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n</ul>\n<ul>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n</ul>\n<ul>\n<li>Support Package Stack 05 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 05 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2557998\" target=\"_blank\">2557998</a>, (SAP S/4HANA 1511 - SAP S/4HANA 1511 SP Stack 05 (11/2017) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 05.</li>\n<li>If you are using SPAM for the SP Update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP Update to SAP S/4HANA 1511 Support Package Stack 05 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 05:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235373030323926\" target=\"_blank\">2570029</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Connector creation: Dump due to memory overflow</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BASIS 7.50</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-02-02</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2566812\" target=\"_blank\">2566812</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Application area TM/Transportation Management</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BASIS 7.50</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-02-02</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2537567\" target=\"_blank\">2537567</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>EHP8_SP08: Issue with workforce viewer application</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>EA-HRRXX 608<br/>(valid to SP46)</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2017-11-14</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Support Package Stack 05</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-12-12: </strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) relenvant for migration to SAP S/4HANA and SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 11.01.2019</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 06 (05/2018)</strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Support Package Stack 06 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 05 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2557998\" target=\"_blank\">2557998</a>, (SAP S/4HANA 1511 - SAP S/4HANA 1511 SP Stack 05 (11/2017) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 05.</li>\n<li>If you are using SPAM for the SP Update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP Update to SAP S/4HANA 1511 Support Package Stack 05 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p> </p>\n<p><strong>Notes to be applied on top of Support Package 06:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2618103\" target=\"_blank\">2618103</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ALV layout: Layouts cannot be saved from the 'Change Layout' dialog after an error message</span></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BASIS 7.50</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2624170\" target=\"_blank\">2624170</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Conversion routine for DATUM data element in BC Set</span></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>SAP_BASIS 7.50</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-05-08</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2441447\" target=\"_blank\">2441447</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Authorization check enablement in Business Partner F4 search help</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.50</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2658952\" target=\"_blank\">2658952</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>ESH - initial authorization index filling - error: \"Feature not supported\"/OLAP VIEW on SAP HANA 3.1</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.50</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2660883\" target=\"_blank\">2660883</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>BP_EOP: Success Message is not displayed properly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_ABA 7.50</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-07-10</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Support Package Stack 06</strong></p>\n<p><strong>2018-07-03:</strong> SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> (SAP S/4HANA - unrecommended revisions of SAP HANA database for use in SAP S/4HANA ) when you plan to upgrade to SAP HANA 2.0 SPS03 Revisions 3x.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 11.01.2019</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 07 (11/2018)</strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Support Package Stack 06 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 05 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2557998\" target=\"_blank\">2557998</a>, (SAP S/4HANA 1511 - SAP S/4HANA 1511 SP Stack 05 (11/2017) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 05.</li>\n<li>If you are using SPAM for the SP Update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP Update to SAP S/4HANA 1511 Support Package Stack 05 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p> </p>\n<p><strong>Notes to be applied on top of Support Package 07:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2697405\" target=\"_blank\">2697405</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> TrexViaDbsl: wrong schema name is set to temporary objects in TREX_EXT_AGGREGATE</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p> SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p> Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-10-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> </p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> </p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> </p>\n<p><strong>Important Changes made after Release of Support Package Stack 07</strong></p>\n<p><strong>Notes to be applied </strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2722552\" target=\"_blank\">2722552</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Runtime error SYSTEM_DATA_ALREADY_FREE during update of classifications</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-11-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2724147\" target=\"_blank\">2724147</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Termination in the update of the classification</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-11-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2694011\" target=\"_blank\">2694011</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">WTY: Dump \"CALL_FUNCTION_CONFLICT_LENG\" occurs on account document posting</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-12-07</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2726975\" target=\"_blank\">2726975</a></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Support of CLSD in SNOTE: Ignore all Changed by and Changed on data in the CI</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">SAP_BASIS</span></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-12-11</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 11.01.2019</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 08 (04/2019)</strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Support Package Stack 06 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.21. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS01 Revision 12.04 or SPS02 Revision 23. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 05 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2557998\" target=\"_blank\">2557998</a>, (SAP S/4HANA 1511 - SAP S/4HANA 1511 SP Stack 05 (11/2017) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 05.</li>\n<li>If you are using SPAM for the SP Update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP Update to SAP S/4HANA 1511 Support Package Stack 05 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p> </p>\n<p><strong>Important Changes made after Release of Support Package Stack 08</strong></p>\n<p><strong>Notes to be applied</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2767385\" target=\"_blank\">2767385</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Melted variables showing initial values by mistake</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BW</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2019-07-12</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>SUPPORT PACKAGE STACK 09 (10/2019)</strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Support Package Stack 09 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.23. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 05 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2557998\" target=\"_blank\">2557998</a>, (SAP S/4HANA 1511 - SAP S/4HANA 1511 SP Stack 05 (11/2017) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 05.</li>\n<li>If you are using SPAM for the SP Update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP Update to SAP S/4HANA 1511 Support Package Stack 05 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p> </p>\n<p><strong>Important Changes made after Release of Support Package Stack 09</strong></p>\n<p><strong>2020-01-31: </strong><strong>United Kingdom leaving the EU</strong>:</p>\n<p>• For information about the United Kingdom leaving the EU with the Withdrawal Bill and the transition period, please see SAP note <a href=\"/notes/2885225\" target=\"_blank\">2885225</a>.</p>\n<p>• For information about how a “hard Brexit” (= a no-deal scenario) would impact your SAP S/4HANA system, please see SAP note <a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.</p>\n<p><strong><strong> </strong></strong></p>\n<p><strong>SUPPORT PACKAGE STACK 10 (04/2020)</strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Support Package Stack 10 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.27. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 05 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2557998\" target=\"_blank\">2557998</a>, (SAP S/4HANA 1511 - SAP S/4HANA 1511 SP Stack 05 (11/2017) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 05.</li>\n<li>If you are using SPAM for the SP Update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP Update to SAP S/4HANA 1511 Support Package Stack 05 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 10:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2880761\" target=\"_blank\"> </a></p>\n</td>\n<td valign=\"top\" width=\"349\"></td>\n<td valign=\"top\" width=\"154\"></td>\n<td valign=\"top\" width=\"147\"></td>\n<td valign=\"top\" width=\"142\"></td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>SUPPORT PACKAGE STACK 11 (10/2020)</strong></p>\n<p><strong>General / Important Considerations</strong>:</p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1511_002\" target=\"_blank\">product documentation</a>.</li>\n<li>For supported application server platforms, please refer to the <a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3D1511%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73554900100900000398%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix</a>. When planning your system landscape(s) for SAP S/4HANA 1511, you may want to take the available application server platforms for the next releases of SAP S/4HANA into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> for details.</li>\n<li>Support Package Stack 09 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is 122.27. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2445826\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2445826</a> to get more details on Support for SAP HANA 2 in SAP S/4HANA 1511 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA system on SAP HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n<li>Please refer to SAP note <a href=\"/notes/2574281\" target=\"_blank\">2574281</a> (Update on Table During S4 Migration Fails With NOT NULL Constraint Violation) when your migrate to SAP S/4HANA and the system is on SAP HANA 2.0 Revisions &lt;= 022.00 (SPS02).</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for \"SAP S/4HANA, on-premise edition 1511\", please refer to SAP note <a href=\"/notes/2214245\" target=\"_blank\">2214245</a> which is the release information note for product version \"SAP Fiori 1.0 for SAP S/4HANA\".<br/>SAP S/4HANA, on-premise edition 1511 Support Package Stack 05 in the backend requires Support Package Stack 03 or higher of SAP Fiori 1.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2557998\" target=\"_blank\">2557998</a>, (SAP S/4HANA 1511 - SAP S/4HANA 1511 SP Stack 05 (11/2017) content activation note), when you are using SAP Activate for your SAP S/4HANA on premise implementation.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Installation (via Software Provisioning Manager (SWPM)):</strong></p>\n<ul>\n<li>Please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1511%20002/en-US/INST_OP1511_FPS02.pdf\" target=\"_blank\">installation guide</a> under section product documentation.</li>\n</ul>\n<p><strong>Support Package Update:</strong></p>\n<ul>\n<li>Please be aware that Feature Package Stack 01 and higher include component version ST-A/PI 01S_731. If this component version is not installed in your system, you have to use the add-on installation tool (transaction SAINT) instead of the Support Package Manager (transaction SPAM) for the update from Feature Package Stack 00 to Support Package Stack 05.</li>\n<li>If you are using SPAM for the SP Update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP Update to SAP S/4HANA 1511 Support Package Stack 05 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 10:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2880761\" target=\"_blank\">2880761</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBT</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>ST-A/PI</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2020-04-01</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2880801\" target=\"_blank\">2880801</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBP</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>ST-A/PI</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2020-04-01</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2886195\" target=\"_blank\">2886195</a></p>\n</td>\n<td valign=\"top\" width=\"349\">Syntax error \"VBHDR_G does not exist\" in program /SDF/SAPLEWA</td>\n<td valign=\"top\" width=\"154\">ST-A/PI</td>\n<td valign=\"top\" width=\"147\">No</td>\n<td valign=\"top\" width=\"142\">2020-04-01</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 135}, {"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}, {"note": "2270318", "noteTitle": "2270318 - S4TWL - SEM Banking", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SEM Banking</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The SEM Banking functionality is <span>not</span> included in SAP S/4HANA.</p>\n<p>Larger parts of the SEM Banking functionality have already been replaced by the Bank Analyzer (part of the Banking platform \"Banking Services from SAP\") or other applications, but it's important to note there are still functions, which have no successor.</p>\n<p>A technical disablement of SEM Banking was done as follows:</p>\n<ul>\n<li>The SEM Banking menu was disabled (hidden)</li>\n<li>The SEM Banking IMG was disabled (hidden)</li>\n<li>Central SEM Banking transactions will terminate with an \"A-message\"</li>\n</ul>\n<p>It's not allowed to execute SEM Banking transactions anymore.</p>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>The whole SEM Banking application with its parts \"Datapool\", \"Profit Analyzer\", \"Risk Analyzer\" and \"Strategy Analyzer\" won't be offered in the context of SAP S/4HANA.</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p><strong> Transaction not available in SAP S/4HANA</strong></p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>All SEM Banking related transaction codes for \"Datapool\", \"Profit Analyzer\", \"Risk Analyzer\" and \"Strategy Analyzer\".</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Users of SEM Banking have to evaluate alternatives to the usage of SEM Banking in SAP S/4HANA systems (e.g. replacement of SEM Banking functionality by the Bank Analyzer or keeping SEM Banking on a separate ERP installation).</p>\n<p>Moreover the option to archive (delete) obsolete SEM Banking data (after having switched off SEM Banking and before migrating the system to SAP S/4HANA) should be considered, in order to avoid that unnecessary data is kept in the SAP S/4HANA environment. Possible candidates are Banking operating concerns (as part of the SEM Banking Profit Analyzer) or variable transactions (as part of the SEM Banking Datapool), but please note that a customer-specific analysis is mandatory, in order to ensure that only obsolete data is going to be archived (deleted).</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>﻿There is an active risk management area and / or there is an active Banking operating concern in the system</span></p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>For more details see SAP Note 2211665</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 6}, {"note": "2543469", "noteTitle": "2543469 - \"SAP for Banking\": SAP extractors in connection with \"SAP S/4HANA on-premise edition\"", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning to use SAP extractors (SAP data sources) in \"SAP S/4HANA on-premise edition\".</p>\n<p>In this context, you want to know which of the SAP extractors provided in the SAP ERP 6.0 environment are also released in \"SAP S/4HANA on-premise edition\".</p>\n<p>This SAP Note provides information about the SAP extractors of the following applications in the \"SAP for Banking\" area:</p>\n<ul>\n<li>FS-BP Business Partner</li>\n<li>FS-CML Consumer and Mortgage Loans</li>\n<li>FS-CMS Collateral Management</li>\n<li>SEM Banking components (IS-B-DP Transaction Datapool, IS-B-PA Profit Analyzer, IS-B-RA Risk Analyzer, IS-B-SA Strategy Analyzer) </li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP extractors, SAP data sources, BW extractors</p>\n<p>FS-BP, FS-CML, FS-CMS, SEM Banking</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>General remarks:</p>\n<ul>\n<li>The entire SEM Banking application (including its parts \"Transaction Datapool\", \"Profit Analyzer\", \"Risk Analyzer\", and \"Strategy Analyzer\") is <span>no longer</span> available in \"SAP S/4HANA on-premise edition\" (see also SAP Note <a href=\"/notes/2270318\" target=\"_blank\">2270318</a>).<br/>Consequently, the SAP extractors of the SEM Banking application are <span>not</span> released in \"SAP S/4HANA on-premise edition\" either.</li>\n</ul>\n<p>The following SAP extractors are <span>not</span> released in \"SAP S/4HANA on-premise edition\":</p>\n<ul>\n<li>0BA_PARTNR</li>\n<li>0BA_RBPROD</li>\n<li>0BA_RBPROD_TEXT</li>\n<li>0BA_RKALRG</li>\n<li>0BA_RKALRG_TEXT</li>\n<li>0CML_ENCUMBRANCE</li>\n</ul>\n<p>The following SAP extractors are only released <span>with restrictions</span> in \"SAP S/4HANA on-premise edition\":</p>\n<ul>\n<li>In accordance with SAP Note <a href=\"/notes/2448350\" target=\"_blank\">2448350</a>, differentiation category-dependent data (for example, differentiation category-depending ratings) are no longer supported in SAP S/4HANA. This is also reflected in the SAP extractors.</li>\n<ul>\n<li>\n<p>0FS_BP_RATING</p>\n</li>\n<li>\n<p>0FS_BP_RATING_TEXT</p>\n</li>\n<li>\n<p>0FS_BP_RATPROC_TEXT</p>\n</li>\n<li>\n<p>0FS_BP_TENDENCY_TEXT</p>\n</li>\n</ul>\n</ul>\n<p> </p>", "noteVersion": 2}, {"note": "2236517", "noteTitle": "2236517 - Multi Currency Accounting for SAP Simple Finance and S/4HANA OP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the business function FIN-GL-MCA that was out of scope for SAP S/4HANA Finance and SAP S/4HANA OP until now.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Multi Currency Accounting, SFIN 2.0, SAP Simple Finance, MCA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Information</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the respective support package. MCA works now with New G/L only. Classic G/L is no longer supported as SAP S/4HANA OP and SAP S/4HANA Finance only support New G/L and Unified Document architecture.</p>", "noteVersion": 3}, {"note": "2231634", "noteTitle": "2231634 - Use component 'Average Daily Ledger'", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the business function FIN_GL_ADB for component ADB (Average Daily Balances) in SAP S/4HANA Finance and S/4HANA OP.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SFIN 2.0, Simple Finance</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the corresponding support package.</p>\n<p>The following changes have been implemented:</p>\n<p>The ADB key figure calculation has been adapted to use a daily and a monthly ledger with the Universal Joirnal table ACDOCA. An average ledger is no longer needed and obsolete for ADB. <span ar-sa;\"=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">Transaction GADBCOR to correct Monthly Averages, Monthly Submitter of precalculated Averages  and transaction GADBAVG for Monthly Average Calculation are obsolet and no longer available. </span></p>", "noteVersion": 6}, {"note": "2193911", "noteTitle": "2193911 - IS-B-BCA – Functions no longer supported in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For the use of SAP S/4HANA, the following BCA function has been deactivated:</p>\n<ul>\n<li>Service layer for Client Advisor Applications, package /FSCAA/CSL</li>\n<li>EFT file transfer (DTA format support), package FKBFORMAT and FKBDISPATCH</li>\n<li>Interface for GEVA application, package FKBZ</li>\n<li>US function, package BCA_US, (except holds HOLDS)</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deprecation, S/4, cloud, EFT, GEVA, BCA_US, Client Advisor Applications, service layer, BCA_US_F9MN, HOLDS, SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Adjustments for functions that are no longer supported in SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions or import the specified support package.</p>", "noteVersion": 3}, {"note": "2369934", "noteTitle": "2369934 - S4TWL - CML-specific functions with regard to collaterals and collateral objects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RE, Classic, RE-FX Real Estate Management, RE Classic, RE-Classic</p>\n<p>Land Register, Land Registry</p>\n<p>Business Partner</p>\n<p>CML collaterals and collateral objects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>not</span> available in SAP S/4HANA (including the SAP Simple Finance editions).</p>\n<p>(Remark: See also the entry with the subject \"Real Estate Classic\" in the \"Simplification List for SAP S/4HANA, on-premise edition 1511\".)</p>\n<p>Therefore it's <span>not</span> allowed to use CML-specific functions with regard to collaterals and collateral objects in the context of SAP S/4HANA (including the SAP Simple Finance editions).</p>\n<p>Examples for transactions, which must not be used, are:</p>\n<ul>\n<li>Object Processing (Create, Change and Display) - FNO1, FNO2 and FNO3</li>\n<li>Collateral Value Calculation (Create, Change and Display) - FN61, FN62 and FN63</li>\n<li>Collateral (Create, Change and Display) - FNO5, FNO6 and FNO7</li>\n</ul>\n<p>Of course all customizing transactions and settings, which are related to the CML-specific functions with regard to collaterals and collateral objects, must not be used, too.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.</p>\n<p>The transition from the CML-specific functions with regard to collaterals and collateral objects to FS-CMS must take place <span>before</span> the transformation to SAP S/4HANA.</p>\n<p>In principle such a transition consists of two steps:</p>\n<ul>\n<li>Implementation (i.e. configuration and customizing etc.) of FS-CMS</li>\n<li>Migration of data from FS-CML to FS-CMS</li>\n</ul>\n<p> </p>", "noteVersion": 3}, {"note": "3102366", "noteTitle": "3102366 - BP_MDG: Integration of some datasets/tables into MDG und MDC", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The following datasets / database tables of the Financial Services enhancement of the SAP Business Partner were missing in \"Master Data Governance\" and \"Master Data Consolidation and Mass Processing\" (MDC) solutions:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Table/dataset</strong></td>\n<td><strong> Description</strong></td>\n</tr>\n<tr>\n<td>BP001</td>\n<td>General Financial Services Data</td>\n</tr>\n<tr>\n<td>BP011</td>\n<td>Employment Data</td>\n</tr>\n<tr>\n<td>BP021</td>\n<td>Fiscal Year Information </td>\n</tr>\n<tr>\n<td>BP1010</td>\n<td>Credit Standing Data</td>\n</tr>\n<tr>\n<td>BP1012</td>\n<td>Ratings </td>\n</tr>\n<tr>\n<td>BP1030</td>\n<td>Regulatory Reporting Data </td>\n</tr>\n<tr>\n<td>BKK21</td>\n<td>Alias Names/Other Names</td>\n</tr>\n<tr>\n<td>BPTAXC</td>\n<td>Business Partner Tax Compliance </td>\n</tr>\n<tr>\n<td>BUT0BANK</td>\n<td>Partner is Bank </td>\n</tr>\n</tbody>\n</table></div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Business Partner, Financial Services, Geschäftspartner, MDG, MDC, BP_MDC, BP001, BP011, BP021, BP1010, BP1012, BP1030, BKK21, BPTAXC, BUT0BANK</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Missing functionality</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The missing datasets have been integrated into Master Data Governance (MDG) and Master Data Consolidation and Mass Processing (MDC) in two development phases in releases S/4HANA 2020 on Premise and S/4HANA 2021 on Premise. In the following table you find the integrated datasets with</p>\n<p>- Technical table name and description</p>\n<p>- Cardinality (how many entries per bp are possible - 1:1 means that only the bp number is key field apart from the client)</p>\n<p>- For which partner categories can the dataset be maintained (P - Person / O - Organization / G - Group)</p>\n<p>- In which release was the integration delivered (OP2020 - S/4HANA On Premise 2020 / OP2021 - S/4HANA On Premise 2021)</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td class=\"xl26\" height=\"42\" width=\"120\"><strong>Table name</strong></td>\n<td class=\"xl18\" width=\"216\"><strong>Description</strong></td>\n<td class=\"xl18\" width=\"74\"><strong>Cardinality</strong></td>\n<td class=\"xl23\" width=\"124\"><strong>Possible partner categories</strong></td>\n<td class=\"xl23\" width=\"103\"><strong>Delivery</strong></td>\n</tr>\n<tr>\n<td class=\"xl19\" height=\"26\">BP1010</td>\n<td class=\"xl20\">Creditworthiness</td>\n<td class=\"xl30\">1 : 1</td>\n<td class=\"xl23\">P/O/G</td>\n<td class=\"xl23\">OP2020</td>\n</tr>\n<tr>\n<td class=\"xl21\" height=\"26\">BP1012</td>\n<td class=\"xl22\">Ratings</td>\n<td class=\"xl31\">1 : n</td>\n<td class=\"xl23\">P/O/G</td>\n<td class=\"xl23\">OP2020</td>\n</tr>\n<tr>\n<td class=\"xl19\" height=\"26\">BP001</td>\n<td class=\"xl20\">Financial Services Details</td>\n<td class=\"xl30\">1 : 1</td>\n<td class=\"xl24\">P/O/G</td>\n<td class=\"xl24\">OP2021</td>\n</tr>\n<tr>\n<td class=\"xl19\" height=\"26\">BUT0BANK</td>\n<td class=\"xl20\">Partner is Bank</td>\n<td class=\"xl30\">1 : 1</td>\n<td class=\"xl23\">O</td>\n<td class=\"xl23\">OP2021</td>\n</tr>\n<tr>\n<td class=\"xl19\" height=\"26\">BP1030</td>\n<td class=\"xl20\">Financial Services Reporting</td>\n<td class=\"xl30\">1 : 1</td>\n<td class=\"xl23\">P/O/G</td>\n<td class=\"xl23\">OP2021</td>\n</tr>\n<tr>\n<td class=\"xl21\" height=\"26\">BP011</td>\n<td class=\"xl22\">Employment Overview</td>\n<td class=\"xl31\">1 : n</td>\n<td class=\"xl23\">P</td>\n<td class=\"xl23\">OP2021</td>\n</tr>\n<tr>\n<td class=\"xl21\" height=\"26\">BP021</td>\n<td class=\"xl22\">Fiscal Year Information</td>\n<td class=\"xl31\">1 : n</td>\n<td class=\"xl23\">O</td>\n<td class=\"xl23\">OP2021</td>\n</tr>\n<tr>\n<td class=\"xl21\" height=\"26\">BKK21</td>\n<td class=\"xl22\">Additional Names</td>\n<td class=\"xl31\">1 : n</td>\n<td class=\"xl23\">P/O/G</td>\n<td class=\"xl23\">OP2021</td>\n</tr>\n<tr>\n<td class=\"xl21\" height=\"26\">BPTAXC*</td>\n<td class=\"xl22\">Tax Compliance</td>\n<td class=\"xl31\">1 : n</td>\n<td class=\"xl23\">P/O/G</td>\n<td class=\"xl23\">OP2021</td>\n</tr>\n</tbody>\n</table></div>\n<div>\n<p>*Tax Compliance data is not active in standard delivery, activation of business function CA_FSBP_TAX_C necessary via transaction SFW5)</p>\n<p>Please have a look into note <a href=\"https://i7p.wdf.sap.corp/sap(bD1kZSZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3232323133393826\" target=\"_blank\">2221398 </a>in addition, this note contains an attachment with the details about the supported FSBP fields.</p>\n</div>", "noteVersion": 2}, {"note": "2890450", "noteTitle": "2890450 - BP_SRV: Addition of data sets specific to Financial Services to the business partner data replication service (ratings, credit standing data, regulatory reporting data, and so on)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In S/4HANA, you also want to use the business partner data replication service (formerly known as the MDG service) for the replication of financial services business partner data.</p>\n<p><span><strong>Technical data:</strong></span></p>\n<p><strong>Service namespace:</strong> <br/>http://sap.com/xi/SAP_BS_FND/MDG/Global2</p>\n<p><strong>Service interfaces:<br/></strong>BusinessPartnerSUITEBulkReplicateRequest_In<br/>BusinessPartnerSUITEBulkReplicateRequest_Out</p>\n<p>The data sets of the Financial Services extension of the SAP Business Partner are not currently integrated in this service. This relates to the following data sets:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"170\">\n<p><strong>Data set</strong></p>\n</td>\n<td width=\"76\">\n<p><strong>Database table</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Financial Services Common</p>\n</td>\n<td width=\"76\">\n<p>BP001</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Employment Data</p>\n</td>\n<td width=\"76\">\n<p>BP011</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Fiscal Year Information</p>\n</td>\n<td width=\"76\">\n<p>BP021</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Credit Worthiness Data</p>\n</td>\n<td width=\"76\">\n<p>BP1010</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Ratings</p>\n</td>\n<td width=\"76\">\n<p>BP1012</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Business Partner is Bank</p>\n</td>\n<td width=\"76\">\n<p>BUT0BANK</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Tax Compliance Data</p>\n</td>\n<td width=\"76\">\n<p>BPTAXC</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Reporting Data</p>\n</td>\n<td width=\"76\">\n<p>BP1030</p>\n</td>\n</tr>\n<tr>\n<td width=\"170\">\n<p>Alias Names</p>\n</td>\n<td width=\"76\">\n<p>BKK21</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>To replicate Financial Services data, you must use the old ABA replication services (ABABusinessPartnerIn, ABABusinessPartnerOut - namespace http://sap.com/xi/ABA) that are still technically available in S/4HANA but are no longer intended to be used. In particular, there is no service that can simultaneously replicate data for the Financial Services enhancement and data for customer/vendor integration to the business partner.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Business partner, data replication, http://sap.com/xi/SAP_BS_FND/MDG/Global2, BusinessPartnerSUITEBulkReplicateRequest_In, BusinessPartnerSUITEBulkReplicateRequest_Out,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The relevant function is missing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The development for integrating the above data sets into the business partner data replication service was completed by the end of 2019. The enhancement was implemented down to ERP 600 Enhancement Package 8 and is available in the following release versions/Support Packages:</p>\n<ul>\n<li>S4CORE 104 (S/4HANA 1909) - Support Package 2</li>\n<li>S4CORE 103 (S/4HANA 1809) - Support Package 4</li>\n<li>S4CORE 102 (S/4HANA 1709) - Support Package 6</li>\n<li>S4CORE 101 (S/4HANA 1610) - Support Package 8</li>\n<li>S4CORE 100 (S/4HANA 1511) - Support Package 10</li>\n<li>SAP_APPL 618 (ERP 600, Enhancement Package 8) - Support Package 15 if note 3187795 is applied in addition.</li>\n</ul>\n<p>We recommend that you import the enhancement via Support Package. However, you can also implement the enhancement by SAP Note, together with certain manual steps. For more information, see the referenced SAP Notes:</p>\n<p><strong>Technical prerequisites:</strong> <br/>2799001 \"BP_SRV: Addition of Financial Services data sets to business partner data replication service - technical prerequisites<br/>2889120 \"BP_SRV: UDO report for enhancement of BP (MDG) replication service with FS datasets\"<br/><br/><strong>Main functionality:</strong><br/>2888623 \"BP_SRV:  Enhancement of Business Partner Data Replication Service (formerly known as MDG Service) with Financial Services Datasets\"<br/><br/><strong>Refer also to the following:</strong><br/>2858939 \"BP_SRV: Activation of nodes specific to Financial Service (for example, rating, regulatory reporting data) in the business partner data replication service<br/>--&gt; This SAP Note explains how incomplete business partner messages are sent to avoid data loss in system landscapes in which not all systems already have the service extension, and how you can actively intervene to enable the deletion of complete nodes/datasets.</p>\n<p> </p>\n<p> </p>", "noteVersion": 3}, {"note": "2448350", "noteTitle": "2448350 - S4TWL - Differentiation Category Dependent FS Datasets Not Available in S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to S/4 HANA On Premise 1610 or higher. In this release differentiation-category dependent Financial Services data cannot be read or maintained any more.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Financial Services Business Partner, FSBP, FS-BP, Ratings, BP1012, Differentiated Attribute, BP1013, Additional Information, BP3100, Differentiation Category, Differentiation Criterion</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The recommended general replication service for distribution of business partners between systems (BusinessPartnerSUITEBulkReplicateRequest) does not support the header attribute \"Differentiation Category\". This means that through this replication service, differentiation-category dependent data cannot be processed, as long as these data depend on the differentiation category header attribute of the business partner. Because of this within Financial Services extension of SAP Business Partner the following datasets will not be maintainable any more with a differentiation category:</p>\n<ul>\n<li>BP1012 - Ratings</li>\n<li>BP1013 - Differentiated Attribute</li>\n<li>BP3100 - Additional information</li>\n</ul>\n<p>Differentiation-Category dependent maintenance within Financial Services Business Partner roles (for example, FS0003) is not possible any more.</p>\n<p>Note that differentiation-independent maintenance of the three datasets is (of course) still possible.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Differentiation Category Dependent FS Datasets Not Available in S/4HANA.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Differentiation category dependent datasets can save data depending on so-called differentiation categories (for example, different affiliates of a company - each affiliate could save a separate rating for the business partner). This was possible for Ratings (database table BP1012), Additional Information (database table BP3100) and Differentiated Attributes (database table BP1013).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>No action necessary when no differentiation category dependent data have been used in former release. Only customers who have used differentiation category dependent datasets in pre-upgrade release might have the need for taking actions. For all other customers the differentiation-category dependent datasets must not be used.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This is relevant for all S/4HANA customers who have used differentiation-category dependent datasets in the former releases. This can be checked by executing the \"number of entries\" function in transaction SE16 for all three mentioned database tables with the following selection criteria:</p>\n<p>Select \"not equal to\" space in the corresponding differentiation category field (<em>DFTVAL </em>for table BP1013, <em>DIFTVAL </em>for table BP1012, <em>CRITER</em> for table BP3100) and press button \"number of entries\". If at least one entry is found in any of the tables there was a usage of differentiation-category dependent data.</p>\n<p>Exception: Customers who have used application Credit Management may have differentiation-category dependent data in tables BP1012 and BP3100. These customers can continue to use credit management and maintain differentiation-category dependent data. The restriction is only valid for customers who do not use credit management.</p>\n<p><strong>Activities</strong></p>\n<p>Customers who have differentiation-category dependent data in one or more of the three tables should check if these entries are still needed. Only in case this question is answered with yes the differentiation-dependency can be re-activated by executing the following steps:</p>\n<ol>\n<li>Call up transaction SM30, enter view V_BPCONSTANTS and click <strong>Change</strong>.</li>\n<li>Select constant 'FSDT' and enter constant value '1'.</li>\n<li>Save the entry.</li>\n</ol>\n<p>With this, the differentiation-category dependent datasets are available again in all channels (BDT - transaction BP and direct input, BAPI/API, FSBP_READ-function modules).</p>\n<p><strong>Additional Information</strong></p>\n<p>The restriction that the differentiation category header attribute is not supported by the central replication service has been considered in the simplification implementation. Even if a customer re-activates the differentiation-category dependent datasets, they are not dependent on the exixstence of a role in table BUT100 with filled key field DFVAL. With this the data can be replicated to other systems of a system landscape.</p>\n<p> </p>", "noteVersion": 3}]}, {"note": "2144579", "noteTitle": "2144579 - Release Information for SAP for Banking together with SAP Simple Finance, On-Premise Edition 1503 & 1605", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You plan to run SAP Simple Finance, on-premise edition 1503 (formerly known as \"SAP Simple Finance add-on 2.0 for SAP Business Suite powered by SAP HANA\") or 1605 for Banking.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span>The following restrictions have to be considered</span>:</p>\n<ul>\n<li>Average Daily Balances: The calculation of averages like Month-to-Date, Quarter-to-Date and Year-to-Date is not covered in the SAP Simple Finance, on-premise edition.<br/>Remark: Resolution with support package 05 of release SAP Simple Finance, on-premise edition 1503 (or support package 03 of release SAP Simple Finance, on-premise edition 1605), but the following restrictions remain valid:</li>\n<ul>\n<li>ADB monthly submitter scenarios and ADB value correction scenarios are <span>not</span> supported</li>\n<li>Only New G/L is supported</li>\n<li>Please consider SAP note 2231634, too.</li>\n</ul>\n<li>Multi Currency Accounting for Banks is not released based on the SAP Simple Finance, on-premise edition.<br/>Remark: Resolution with support package 05 of release SAP Simple Finance, on-premise edition 1503 (or support package 03 of release SAP Simple Finance, on-premise edition 1605), but the following restrictions remain valid:\r\n<ul>\n<li>Only New G/L is supported</li>\n<li>Please consider SAP note 2236517, too.</li>\n</ul>\n</li>\n<li>SAP Leasing for Banking (including Lease Accounting Engine) is <span>not</span> released based on the SAP Simple Finance, on-premise edition.</li>\n<li>SAP SEM Banking components (Datapool, Profit, Risk, Strategy Analyzer) are <span>not</span> released based on the SAP Simple Finance, on-premise edition.</li>\n<li>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>no longer</span> available. This is due to the entry with the subject \"Real Estate Classic\" in the \"Simplification List for SAP S/4HANA\". At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.</li>\n<li>FS-CML Consumer and Mortgage Loans: It is important to note that the application component FI (Financial Accounting) <span>no longer</span> supports the calculation of interest on arrears at customer level (transaction F.24 or report RFDUZI00). For this, application FS-CML has been providing transaction FIOA for the calculation of interest on arrears for quite some time.</li>\n</ul>\n<p> Remark: Remediation of Field Length topic was not approached in combination with the SAP Simple Finance, on-premise edition.</p>\n<p><span>Additional Information about the validity of the note: </span></p>\n<p>Component FS-TXS (FUNDMGMT): Starting with release SAP Funding Management 2.0 (only separate installation possible – independent of ERP installation)</p>\n<p>Component FS-CYT (CYT): Starting with SAP Capital Yield Tax Management for Banking 8.0 SP 2</p>\n<p>Components FS-AM, FS-BA (FSAPPL): banking services from SAP 8.0 (only separate installation possible – independent of ERP installation)</p>\n<p> </p>", "noteVersion": 10, "refer_note": [{"note": "2233539", "noteTitle": "2233539 - Release Scope Information: SAP S/4HANA Finance 1605", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP S/4HANA Finance, on-premise edition comes with simplified architecture, optimized financial accounting processes and supports the operational finance experts driving efficiency and compliance. It is a system of records based on line items as a single source of truth for operational reporting and planning.</p>\n<p>The SAP S/4HANA Finance is a new product, powered by the in-memory capabilities of SAP HANA, that allows you to use in-memory technology to further optimize Financials core business processes. SAP HANA comprises the required hardware as well as the SAP HANA appliance software (SAP HANA database and data replication tools, for example). Please note that the measured performance depends on configuration, data volumes, and data constellation.</p>\n<p>The <strong>SAP S/4HANA Finance 1605</strong> is the successor of SAP Simple Finance, on-premise edition 1503 (corresponding SAP Note: <a href=\"/notes/2119188\" target=\"_blank\">2119188</a>). The product name and numbering of product versions of SAP Simple Finance add-on was changed in 2015, details refer to SAP Note: <a href=\"/notes/2171868\" target=\"_blank\">2171868</a></p>\n<p>The SAP S/4HANA Finance is technically an exchange add-on, which is deployed to SAP enhancement package 8 for SAP ERP 6.0 on a HANA installation. It is an alternative to the classical ERP Financials core applications of SAP enhancement package 8 for SAP ERP 6.0. You cannot use both in parallel within the same SAP ERP installation.</p>\n<p>For details on industry solutions, add-ons, or functions already supported by the SAP S/4HANA Finance see below and check back regularly for updates.</p>\n<p>The SAP S/4HANA Finance is a net new product and not the legal successor for Financials in SAP ERP. Therefore it is not delivered under any maintenance agreement for SAP ERP.</p>\n<p>In case you are using Financials applications with SAP enhancement package 8 for SAP ERP (or previous SAP ERP product versions) or SAP Simple Finance add-on 1.0, a migration project to the SAP S/4HANA Finance 1605 is required.</p>\n<p><br/>For more information, see <em>SAP Help portal at <a href=\"http://help.sap.com/sfin200\" target=\"_blank\">http://help.sap.com/sfin300</a> -&gt; Installation and Upgrade Information -&gt; Migration Guide -&gt; &lt;Language&gt; -&gt; <strong>Migration to SAP Accounting powered by SAP HANA</strong> </em>and the <em><strong>Administrator's Guide for SAP S/4HANA Finance</strong> 1605 at <a href=\"http://help.sap.com/sfin200\" target=\"_blank\">http://help.sap.com/sfin300</a>.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SFINANCIALS, SFIN, supported industries, supported enterprise extensions, supported business processes, supported add-ons, supported partner add-ons</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note provides an overview on the industries, enterprise extensions, add-ons and financials business processes supported with the SAP S/4HANA Finance 1605.</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Release Information - SAP S/4HANA Finance 1605:</strong></p>\n<p><strong>New features and enhancements in Support Package 05 (11/2016)</strong></p>\n<ul>\n<li>Enhanced Functions of Document Management of Bank Account, details refer to SAP Note <a href=\"/notes/2332327\" target=\"_blank\">2332327</a></li>\n<li>Subsequent Implementation of a further Accounting Principle, details refer to SAP note <a href=\"/notes/2351790\" target=\"_blank\">2351790</a></li>\n</ul>\n<p><strong>Financials processes released for usage with SAP S/4HANA Finance 1605:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"542\"/></colgroup>\n<tbody>\n<tr>\n<td height=\"20\" width=\"542\"><strong>Business Process</strong></td>\n<td height=\"20\" width=\"542\"><strong>Hints</strong></td>\n</tr>\n<tr>\n<td height=\"20\" width=\"542\">Accounts Payable</td>\n<td height=\"20\" width=\"542\"></td>\n</tr>\n<tr>\n<td height=\"20\">Accounts Receivable</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Audit Support</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Cash Management (SAP Cash Management)</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Central Payments with In-House Cash</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Credit Evaluation and Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Electronic Tax Declaration</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">FI-AR: Collections Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">FI-AR: Credit Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">FI-AR: Dispute Resolution</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Fixed Asset Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Internal Payments with In-House Cash</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Liquidity Management (SAP Cash Management)</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Local Close</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">New General Ledger and Profit Center Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Operational Treasury Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Overhead Cost Management and ABC/M</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Parallel Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Processing Outbound Payments Using Bank Communication Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Order with Sales Order Controlling</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Order without Sales Order Controlling</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Stock with Order Manufacturing</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Stock with Repetitive Manufacturing</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Profitability Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Tax Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Treasury Risk Management</td>\n<td height=\"20\"></td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>With SAP S/4HANA Finance 1605 a transition to the following applications is automatically part of the SAP S/4HANA Finance migration:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"206\"/> <col width=\"800\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl66\" height=\"40\" width=\"206\">New General Ledger</td>\n<td class=\"xl65\" width=\"800\">Classic General Ledger is mostly automatically transformed into a basic implementation of New General Ledger (G/L in SAP Accounting powered by SAP HANA)</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\" width=\"206\">New Asset Accounting</td>\n<td class=\"xl65\" width=\"800\">Classic Asset Accounting is mostly automatically transformed into the New Asset Accounting</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"40\" width=\"206\">SAP Cash Management</td>\n<td class=\"xl65\" width=\"800\">Classic Cash Management is replaced by the new SAP Cash Management. <br/>SAP Cash Management requires a separate license. Refer to SAP Note <a href=\"/notes/2044295\" target=\"_blank\">2044295 </a>for more information.</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>The SAP S/4HANA Finance 1605 does not support the following functionalities from SAP enhancement package 8 for SAP ERP 6.0:</strong></p>\n<ul>\n<li>Classic Real Estate Management (RE); only RE-FX is supported (see SAP Note 1944871)</li>\n<li>Activity Based Costing (CO-OM-ABC) using delta versions</li>\n<li>General cost object and cost object hierarchies </li>\n<li>in New Asset Accounting: integration to LAE (Lease Accounting Engine); for more information, see SAP Help portal at <a href=\"http://help.sap.com/sfin300\" target=\"_blank\">http://help.sap.com/sfin300</a> -&gt; Application Help: SAP ERP Central Component -&gt; Accounting -&gt; SAP Simple Finance -&gt; Migration to SAP Accounting powered by SAP HANA -&gt; Migration to New Asset Accounting</li>\n<li>General ledger: balance sheet planning, reports for comparing planning data and actual data in Profit Center Accounting - applications can be reactivated using SAP Note <a href=\"/notes/2253067\" target=\"_blank\">2253067</a></li>\n<li>New General Ledger: subsequent implementation of online document split and ledgers, the transformation of parallel accounts into ledger approach (scenarios 6, 7 and 8) and the change in leading valuation (Business function FIN_GL_CHNG_LEAD_VAL) are not supported</li>\n<li>Profit Center- and Segment Reorganization: The functionality of Profit Center- and Segment Reorganization is not supported in the context of the Universal Journal. As per the financials processes listed above as released for usage with SAP S/4HANA Finance, Profit Center and Segment Reorganization” are not part of the released scope. These reorganization functions are offered within the product portfolio of SAP Landscape Transformation (SAP LT) – see SAP notes <a href=\"/notes/1534197\" target=\"_blank\">1534197</a> and <a href=\"/notes/1686874\" target=\"_blank\">1686874</a> for further information. <strong> </strong></li>\n</ul>\n<p><strong>The SAP S/4HANA Finance 1605 differs from SAP enhancement package 8 for SAP ERP 6.0:</strong></p>\n<ul>\n<li>CO-OM planning, P&amp;L planning and profit center planning are now covered by Integrated Business Planning (SAP Note <a href=\"/notes/2081400\" target=\"_blank\">2081400</a>).</li>\n<ul>\n<li>CO-OM planning: in case you do not want to use „Integrated Business Planning for Finance” but classic CO-OM planning functions, you may apply the modifications as indicated in SAP Note <a href=\"/notes/1946054\" target=\"_blank\">1946054</a>. With these modifications the deactivation of the old CO-OM planning transactions can be removed.</li>\n</ul>\n<li>Refer to SAP Note <a href=\"/notes/1946054\" target=\"_blank\">1946054</a> for a detailed comparison of transaction codes / reports in SAP enhancement package 7 for SAP ERP 6.0 and SAP S/4HANA Finance.</li>\n<li>Material Ledger: <br/>- Actual costing relevant key figures are not updated for non-actual costing materials („Price Determination: Transaction-Based\"). Therefore transaction CKM3N „Material Price Analysis\" provides only the view „Price History\" for non-actual costing materials. The view „Price Determination Structure\" is not available.<br/>- Material Ledger currency settings for new installations / newly added company codes: currency settings may not differ from the one centrally defined in Accounting and Controlling.<br/><em>- </em>Material Ledger currency settings for migration scenario: currency settings have to be a subset of currencies defined in Accounting and Controlling.</li>\n</ul>\n<p><strong>SAP ERP Enterprise Extensions are not completely released with the SAP S/4HANA Finance 1605:</strong></p>\n<p>You can install SAP S/4HANA Finance 1605 and use it with the following SAP ERP 6.0 Enterprise Extensions:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"68\"><strong>Extension</strong></td>\n<td class=\"xl65\" width=\"637\"><strong>Title</strong></td>\n</tr>\n<tr>\n<td height=\"20\">EA-CP</td>\n<td>Consumer Products</td>\n</tr>\n<tr>\n<td height=\"20\">EA-DFP</td>\n<td>DefenseForces&amp;PublicSecurity</td>\n</tr>\n<tr>\n<td height=\"20\">EA-FIN</td>\n<td>Financials Extension (required)</td>\n</tr>\n<tr>\n<td height=\"20\">EA-FRC</td>\n<td>FERC: Regulatory Reporting</td>\n</tr>\n<tr>\n<td height=\"20\">EA-FS</td>\n<td>Financial Services, for processes Operational Treasury Management and Treasury Risk Management</td>\n</tr>\n<tr>\n<td height=\"20\">EA-GLT</td>\n<td>Global Trade Management</td>\n</tr>\n<tr>\n<td height=\"20\">EA-HR</td>\n<td>Human Capital Management</td>\n</tr>\n<tr>\n<td height=\"20\">EA-ICM</td>\n<td>Incentive and Sales Force Mgmt</td>\n</tr>\n<tr>\n<td height=\"20\">EA-ISE</td>\n<td>Industry-Spec. Sales Enhancement</td>\n</tr>\n<tr>\n<td height=\"20\">EA-PLM</td>\n<td>PLM Extension</td>\n</tr>\n<tr>\n<td height=\"20\">EA-PS</td>\n<td>Public Services (details refer to SAP Note <a href=\"/notes/2148944\" target=\"_blank\">2148944</a>)</td>\n</tr>\n<tr>\n<td height=\"20\">EA-RET</td>\n<td>Retail</td>\n</tr>\n<tr>\n<td height=\"20\">EA-SCM</td>\n<td>SCM Extension</td>\n</tr>\n<tr>\n<td height=\"20\">EA-TRV</td>\n<td>Travel Management Extension</td>\n</tr>\n</tbody>\n</table></div>\n<p>You can check this in transaction SFW5 -&gt; node ENTERPRISE_EXTENSIONS -&gt; only the above listed can be combined together with SAP S/4HANA Finance.</p>\n<p><strong>SAP ERP Industry Solutions released with the SAP S/4HANA Finance 1605:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"254\"/> <col width=\"267\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"254\"><strong>Industry Solution</strong></td>\n<td class=\"xl65\" width=\"267\"><strong>Related release information</strong><br/> [SAP Note]</td>\n</tr>\n<tr>\n<td height=\"20\">Aerospace &amp; Defense</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Automotive</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Banking</td>\n<td><a href=\"/notes/2144579\" target=\"_blank\">2144579</a> </td>\n</tr>\n<tr>\n<td height=\"20\">Chemicals</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Consumer Products</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Defense and Security</td>\n<td><a href=\"/notes/2148994\" target=\"_blank\">2148944</a></td>\n</tr>\n<tr>\n<td height=\"20\">Engineering Construction &amp; Operations</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Healthcare</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Higher Education &amp; Research</td>\n<td><a href=\"/notes/2148994\" target=\"_blank\">2148944</a></td>\n</tr>\n<tr>\n<td height=\"20\">High Tech</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Industrial Machinery &amp; Components</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Insurance</td>\n<td><a href=\"/notes/2306126\" target=\"_blank\">2306126</a></td>\n</tr>\n<tr>\n<td height=\"20\">Life Sciences</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Media</td>\n<td><a href=\"/notes/2324698\" target=\"_blank\">2324698</a></td>\n</tr>\n<tr>\n<td height=\"20\">Mill Products</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Mining</td>\n<td>\n<p>Released</p>\n</td>\n</tr>\n<tr>\n<td height=\"20\">Oil &amp; Gas</td>\n<td>\n<p>Released</p>\n</td>\n</tr>\n<tr>\n<td height=\"20\">Public Sector</td>\n<td>\n<p><a href=\"/notes/2148944\" target=\"_blank\">2148944</a></p>\n</td>\n</tr>\n<tr>\n<td height=\"20\">Professional Services</td>\n<td>Released </td>\n</tr>\n<tr>\n<td height=\"20\">Retail (including Fashion Management)</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Sports &amp; Entertainment</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Telecommunications</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Travel &amp; Transportation</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Utilities</td>\n<td><a href=\"/notes/2148799\" target=\"_blank\">2148799</a></td>\n</tr>\n<tr>\n<td height=\"20\">Wholesale Distribution</td>\n<td>Released<a href=\"/notes/2148799\" target=\"_blank\"><br/></a></td>\n</tr>\n</tbody>\n</table></div>\n<p>To cover the released industry solutions and financials processes the SAP S/4HANA Finance can be combined with a subset of SAP enhancement package 8 for SAP ERP 6.0 software components.<br/>Refer to SAP Note <a href=\"/notes/2235351\" target=\"_blank\">2235351</a> for a list of these software components.</p>\n<p><strong>SAP and partner add-ons to ERP 6.0 are not all released with the SAP S/4HANA Finance 1605:</strong></p>\n<ul>\n<li><em>SAP add-ons to ERP 6.0</em> released for usage with the SAP S/4HANA Finance 1605: see SAP Note <a href=\"/notes/2235351\" target=\"_blank\">2235351</a></li>\n<li><em>Partner add-ons to ERP 6.0</em> released for usage with the SAP S/4HANA Finance 1605: see SAP Note <a href=\"/notes/2235354\" target=\"_blank\">2235354</a></li>\n</ul>\n<p>The SAP S/4HANA Finance 1605 might not be installable if any add-on, that is not released, is already included in your current SAP ERP installation.</p>\n<p><strong>Current Release Restrictions for SAP S/4HANA Finance 1605:</strong></p>\n<ul>\n<li>The limitation regarding the functionality of “Transfer Prices” valuating the transfer of goods or services according to a legal, group or profit center valuation approach in SAP Simple Finance, on-premise edition 1503 <strong>has been removed</strong> in SAP S/4HANA Finance 1605, so that transfer prices are now supported.</li>\n<li>The functionality of \"Multiple Valuation of Cost of Goods Manufactured\" providing parallel valuations in CO according to multiple accounting principles is not yet supported.</li>\n</ul>\n<p>At the time of the release of SAP S/4HANA Finance 1605 limitations existed concerning the productive usage of certain functions. Refer to SAP Note <a href=\"/notes/2267147\" target=\"_blank\">2267147</a> for more information.</p>", "noteVersion": 7}]}, {"note": "2484134", "noteTitle": "2484134 - Release information for \"SAP for Banking\" in connection with \"SAP S/4HANA 1709\" (\"SAP S/4HANA, on-premise edition 1709\")", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning to use \"SAP S/4HANA 1709\" (\"SAP S/4HANA, on-premise edition 1709\") in connection with applications or solutions from the \"SAP for Banking\" area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With regard to the release and the restrictions/limitations, the statements already made in SAP Note 2211665 also apply for \"SAP S/4HANA 1709\" (\"SAP S/4HANA, on-premise edition 1709\").</p>\n<p>In addition, the following information/restrictions/limitations are to be taken into account:</p>\n<ul>\n<li>FS-CML Consumer and Mortgage Loans: There is no integration with the application \"Central Payment for SAP Central Finance\", that is, the function provided by the application \"Central Payment for SAP Central Finance\" is <span>not</span> available in connection with FS-CML.<br/>Also see SAP Note 2346233 at this point.</li>\n<li>The application \"FS-CYT Capital Yield Tax Management\" is a separate software component (that is, it is not a part of the \"SAP S/4HANA 1709\" (\"SAP S/4HANA, on-premise edition 1709\") delivery). As of Support Package 12 for the Release \"SAP Capital Yield Tax Management for Banking 8.0\", FS-CYT can be used as an add-on for SAP S/4HANA 1709\" (\"SAP S/4HANA, on-premise edition 1709\").</li>\n<li>As of Release 8 and Support Package 7, the application \"SAP Payment Engine\" (FS-PE Payment Engine) is released for integration with \"SAP S/4HANA 1709\" (\"SAP S/4HANA, on-premise edition 1709\").</li>\n<li>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>no longer</span> available.<br/>At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.<br/>Also see SAP Note 2369934 at this point.</li>\n<li>FS-TXS SAP Funding Management: SAP Funding Management (FS-TXS) is <span>not</span> released for use in \"SAP S/4HANA 1709\" (\"SAP S/4HANA, on-premise edition 1709\").<br/>Comment: A separate installation of SAP Funding Management in combination with a remote communication to FS-CML is permitted as of SAP Funding Management 3.0 Support Package 06.</li>\n</ul>", "noteVersion": 4, "refer_note": [{"note": "2491467", "noteTitle": "2491467 - SAP S/4HANA 1709: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA 1709 , respectively you are planning or running a conversion to SAP S/4HANA 1709.</p>\n<p>This SAP Note, which is subject to change, informs you about all restrictions in this release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restrictions, SAP S/4HANA 1709, SAP S/4HANA 1709 FPS1,SAP S/4HANA 1709 FPS2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides information about the restrictions that exist for SAP S/4HANA 1709 and further SP's.</p>\n<p>Note: This SAP Note is subject to change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span>Restrictions for SAP S/4HANA 1709 FPS2 (for previous stacks refer to Change Log section below)</span></p>\n<ul>\n<ul>\n<li>Please also check the linked SAP Notes for restrictions.</li>\n</ul>\n</ul>\n<div class=\"myNNFV2Text sapUiSelectable\">\n<ul>\n<li>In SAP S/4HANA, the maximum length of the material number has been extended to 40 characters. <em>Long material number-</em>related restrictions are described in SAP Note <a href=\"/notes/2233100\" target=\"_blank\">2233100</a>.</li>\n<li>The integrated solution of Service Parts Management using SAP CRM Order Management with an active Service Parts Management configuration (Direct Delivery Scenario) in combination with S/4HANA is not released with SAP S/4HANA 1610.</li>\n<li>The attached Business Functions are not released with SAP S/4HANA 1709</li>\n<li>It is not permitted to switch them on, even if this is technically possible (see <a href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001713722017&amp;iv_guid=00109B36D6221ED9B6A91BE8047BC0D3&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B3343188F734378A3730B4CACF8A77AA4C4A372DF309CBAD28F22F2EF02E8F2FB2080A2E2ACB88F42B2BF70B492C77750ECD4CD2750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\" target=\"_blank\">attachment</a>). Additional information regarding Always-Off and Always-On Business Functions can be found in SAP Notes <a href=\"/notes/2240359\" target=\"_blank\">2240359</a> and <a href=\"/notes/2240359\" target=\"_blank\">2240360</a>. If a business function was switched on in the start release system, but defined as always_off in SAP S/4HANA, then a system conversion is not yet possible with this release at that current point in time. If a business function was switched off in the start release system, but defined as always_on in the target release, then the business function will be automatically activated during the conversion.</li>\n<li>There are limitations for packaging material with more than 35 characters in combination with Returnable Packaging Logistics (application component MM-IM-RL or IS-A-RL). These limitations are:</li>\n<ul>\n<li>EDI processing of returnable packaging account statements (message type ACCSTA). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.</li>\n<li>EDI processing of returnable packaging account statement requests (message type ACCSTAREQ). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.</li>\n</ul>\n<li>The released information for add-ons can be found in SAP Note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</li>\n<li>Account Numbers: In case of usage of the Best Practices with demo data, Account Numbers need to consist of 10 digits. They need to consist of numeric digits only. No alphanumeric characters are allowed at the moment. Examples of valid account numbers: \"*********; **********\".</li>\n</ul>\n<p>Application <em>View Browser</em> (CA-GTF-VDM-VB): Creating, Editing, and Deleting of a tile to the catalog for analytical queries is not supported by this application though mentioned in Product Assistance (<a href=\"https://help.sap.com/viewer/6b356c79dea443c4bbeeaf0865e04207/1709%20000/en-US/0bde695751505c08e10000000a441470.html\" target=\"_blank\">reference</a>). The documentation will be corrected in the next release.</p>\n<ul>\n<li> In S/4HANA On Premise, Event-Based Revenue Recognition is not available for Sell from Stock scenarios.</li>\n<li>The Fiori app \"Revenue Recognition (Event-Based) - Sales Orders (F2441)\" is only available for cloud customers. It cannot be used, and there is no support in On-premise installations.</li>\n<li>Only Project Based Services are supported with Event-Based Revenue Recognition, see also note <a href=\"/notes/2493348\" target=\"_blank\">2493348</a> - SAP S/4HANA 1709: Release Information Note for Finance.</li>\n<li> Further area-specific restrictions for SAP S/4HANA 1709 are described - amongst others - in the linked SAP Notes, e.g.:</li>\n</ul>\n</div>\n<div class=\"myNNFV2-table-responsive\">\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"myNNFV2-table\"><colgroup> <col width=\"277\"/> <col width=\"65\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl67\" height=\"20\" width=\"277\"><strong>Area</strong></td>\n<td class=\"xl67\" width=\"65\"><strong>SAP Note</strong></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Master Data Governance</td>\n<td class=\"xl66\">\n<div class=\"WordSection1\">\n<p><a href=\"/notes/2495849\" target=\"_blank\">2495849<br/></a><a href=\"/notes/2893888\" target=\"_blank\">2893888</a><a href=\"/notes/2495849\" target=\"_blank\"><br/></a></p>\n</div>\n</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Aerospace &amp; Defense (A&amp;D)</td>\n<td class=\"xl66\"><a href=\"/notes/2496784\" target=\"_blank\">2496784</a><a href=\"/notes/2349454\" target=\"_blank\"><br/></a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Automotive</td>\n<td class=\"xl66\"><a href=\"/notes/2492542\" target=\"_blank\">2492542</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Mill Products</td>\n<td class=\"xl66\"><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\"><a href=\"/notes/2495974\" target=\"_blank\">2495974</a></span></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Banking</td>\n<td class=\"xl66\"><a href=\"/notes/2484134\" target=\"_blank\">2484134</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Public Sector</td>\n<td class=\"xl66\"><a href=\"/notes/2494273\" target=\"_blank\">2494273</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LOB Financials</td>\n<td class=\"xl66\"><a href=\"/notes/2493348\" target=\"_blank\">2493348</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB Manufacturing: QM</td>\n<td class=\"xl66\"><a href=\"/notes/2345333\" target=\"_blank\">2345333</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB Manufacturing: MM-IM</td>\n<td class=\"xl66\">\n<p><a href=\"/notes/2345321\" target=\"_blank\">2345321<br/></a><a href=\"/notes/2440007\" target=\"_blank\" title='2440007  - Error in Fiori App \"Post Goods Receipt for Purchase Order\" (F0843) due to bug inSAP HANA 2.0 SPS 00 Database Revision 001'>2440007</a><a href=\"/notes/2345321\" target=\"_blank\"><br/></a></p>\n</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB CEC</td>\n<td class=\"xl66\"><a href=\"/notes/2324473\" target=\"_blank\">2324473</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Globalization</td>\n<td class=\"xl66\"><a href=\"/notes/2500467\" target=\"_blank\">2500467</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Best Practices Framework</td>\n<td class=\"xl66\"><a href=\"/notes/2473714\" target=\"_blank\">2473714</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Environment, Health and Safety (EHS)</td>\n<td class=\"xl66\"><a href=\"/notes/2383548\" target=\"_blank\">2383548</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Global Trade Service (GTS)</td>\n<td class=\"xl66\"><a href=\"/notes/2492762\" target=\"_blank\">2492762</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Waste and Recycling</td>\n<td class=\"xl66\"><a href=\"/notes/2437332\" target=\"_blank\" title=\"2437332  - SAP S/4HANA, SAP Waste and Recycling: Activation SAP Note\">2437332 </a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Defense and Security</td>\n<td class=\"xl66\"><a href=\"/notes/2495442\" target=\"_blank\">2495442</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Consumer Products</td>\n<td class=\"xl66\"><a href=\"/notes/2608127\" target=\"_blank\">2608127</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Sales</td>\n<td class=\"xl66\">\n<p><a href=\"/notes/2348936\" target=\"_blank\">2348936</a><br/><a href=\"/notes/2369405\" target=\"_blank\">2369405</a></p>\n</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">SAP Hybris Billing</td>\n<td class=\"xl66\"><a href=\"/notes/2351374\" target=\"_blank\">2351374</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Oil and Gas</td>\n<td class=\"xl66\"><a href=\"/notes/2496468\" target=\"_blank\">2496468</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Project Systems</td>\n<td class=\"xl66\"><a href=\"/notes/2502040\" target=\"_blank\">2502040</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Integration</td>\n<td class=\"xl66\"><a href=\"/notes/2495662\" target=\"_blank\">2495662</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Extended Warehouse Management</td>\n<td class=\"xl66\"><a href=\"/notes/2494704\" target=\"_blank\">2494704</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">advanced Available-to-Promise (aATP)</td>\n<td class=\"xl66\"><a href=\"/notes/2518072\" target=\"_blank\">2518072</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Production Planning and Detailed Scheduling</td>\n<td class=\"xl66\"><a href=\"/notes/2496856\" target=\"_blank\">2496856</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">\n<p>Catch Weight Management (CWM)</p>\n</td>\n<td class=\"xl66\">\n<p><a href=\"/notes/2478683\" target=\"_blank\">2478683</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>\r\nPlease also check the linked SAP Notes for restrictions.</div>\n<div class=\"sapUiVlt sapuiVlt\">\n<ul>\n<ul></ul>\n<li>Restrictions for conversions to SAP S/4HANA 1709:</li>\n<ul>\n<li>Conversion from source releases below SAP ERP 6.0 EHP 5 (SAP_APPL 605) is supported for systems containing vendors with assigned contact but you need to ensure to update to the following minimum SP levels upfront. In addition, you need to implement corrective SAP Note <a href=\"/notes/2383051\" target=\"_blank\">2383051</a> and further corrections into the system. For this purpose please get in contact with SAP via customer incident on component AP-MD-BF-SYN. The minimum SP levels are:</li>\n<ul>\n<li>SAP ERP 6.0 SP20</li>\n<li>EHP2 FOR SAP ERP 6.0 SP10</li>\n<li>EHP3 FOR SAP ERP 6.0 SP09</li>\n<li>EHP4 to EHP8 FOR SAP ERP 6.0 No Minimum SP required.</li>\n</ul>\n<li>This restriction is not relevant for systems which do not have vendors with assigned contacts. You can also verify for existence of records in table KNVK where LIFNR has non initial values (KNVK-LIFNR &lt;&gt; ‘’).</li>\n</ul>\n</ul>\n</div>\n<div class=\"sapUiVlt sapuiVlt\">\n<ul>\n<ul>\n<li>Conversions for DIMP-LAMA: see SAP Note <a href=\"/notes/2384347\" target=\"_blank\">2384347</a></li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Restrictions in conversion paths (the following add-ons cannot be converted to SAP S/4HANA 1610 in one step but require an intermediate upgrade step):</li>\n<ul>\n<li>The Israeli Annexing Transport solution (see SAP Note <a href=\"/notes/2298073\" target=\"_blank\">2298073</a> for more details)</li>\n<li>SRM_SERVER 550/SRM_PLUS 550 (see SAP Note <a href=\"/notes/2251946\" target=\"_blank\">2251946</a> for more details)</li>\n<li>SAP Financial Closing cockpit 1.0 (see SAP Note 2513315 for more details)</li>\n</ul>\n<li>For restrictions concerning Cost Of Goods Manufactured (COGM) please see SAP Note <a href=\"/notes/2270414\" target=\"_blank\">2270414</a>.</li>\n<li>For restrictions concerning New Inventory Management Data Model, see SAP Note <a href=\"/notes/2493434\" target=\"_blank\">2493434</a>.</li>\n<li>Please also check the linked SAP Notes for restrictions.</li>\n</ul>\n</ul>\n<ul>\n<li>Performance Restrictions:</li>\n<ul>\n<li>SAP S/4HANA on-premise 1709: <a href=\"/notes/2519264\" target=\"_blank\">2519264</a></li>\n</ul>\n</ul>\n<p><strong>Change Log </strong></p>\n<p>Restrictions resolved with SAP S/4HANA 1709 FPS1:</p>\n<ul>\n<li>If you plan a conversion from a system with Add-on SAP AGRICULT CONTRACT MGMT installed, please get in contact with SAP via customer incident on component LO-AGR-CC and short text “Migration to Agriculture Contract on Management for S/4 HANA:\"</li>\n<li>If you are migrating letters of credit (SD-FT) to Trade Finance with SAP S/4HANA 1709, please consider the incoterms-related restrictions described in SAP Note 2517188. Those restrictions are planned to be solved with SAP S/4HANA 1709 FPS01.</li>\n<li>If you have postings for stock-in-transit (Business Function “LOG_MM_SIT”) for valuated materials with split valuation in your system, a conversion to SAP S/4HANA is currently only possible if the corrections of SAP Note <a href=\"/notes/2533235\" target=\"_blank\">2533235</a> exist in the system prior to phase PARRUN_MKPF1_S4.</li>\n</ul>\n<p>Restrictions resolved with SAP S/4HANA 1709 FPS2:</p>\n<ul>\n<li>If you have history table entries in aggregates (without having a current entry) that have to be inverted, the determination of cost estimate number might fail during conversion. In this case, please apply note 2592521 and restart the conversion.</li>\n</ul>\n<p>Restrictions newly added:</p>\n<ul>\n<li>[2020-06-25] Restriction SAP Note <a href=\"/notes/2893888\" target=\"_blank\">2893888</a> for SAP S/4HANA MDM added<em><br/></em></li>\n</ul>\n</div>", "noteVersion": 23}, {"note": "2346233", "noteTitle": "2346233 - Switch on Central Payment for Central Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You can switch on Central Payment functionality via implementing this Note.</p>\n<p>Central Payment is released in S/4HANA 1709 with the status “Released with Restrictions, which is described in SAP Note 2827364.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Central Finance, Central Payment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Prerequisites:</p>\n<p>You have applied the following SAP Notes or corresponding support packages in the Central Finance system:</p>\n<ul>\n<li>2219063 (Enable clearing transfer processing)</li>\n<li>2264469 (Preparation for implementation)</li>\n<li>2264491 (Implementation of Central Payment functionality)</li>\n<li>2318610 (Implementation of SEPA / Credit Card functionalities)</li>\n<li>2608312 (Switchable Authorization checks in Central Finance)</li>\n<li>2871310 (Central Payment: Common Constants)</li>\n<li>2927018 (Enhancement: Central Payment Utility Class in Central Finance System)</li>\n<li>3117154 (Implementation Tips of Cross-System Process Control for Central Payment)</li>\n<li>2923352 (Making CSPC Mandatory for Central Payment)</li>\n<li>2963154 (Central Payment: Mass Processing of Historical Open Items (Central))</li>\n</ul>\n<p>You have applied the following SAP Notes or corresponding support packages in the source systems:</p>\n<ul>\n<li>2292043 (Enable Clearing Transfer in Source System)</li>\n<li>2264977 (Preparation in Source System)</li>\n<li>2624732 (Central Payment: Set ALE-Extern in down payment request)</li>\n<li>2630672 (Central Payment: Enable Reversal of Down Payment Request in the Source System(s))</li>\n<li>2727392 (Central Finance - Central Payment: Enabling Technical Clearing for Deferred Tax Account in the Source System)</li>\n<li>2775572 (Remove CFin Specific Code from Function FI_IDOC_PREPARE)</li>\n<li>2267623 (DDIC preparation in Source System)</li>\n<li>2267824 (Implementation of Central Payment in Source System)</li>\n<li>2775690 (Transferring the Value of ACCIT-AUGBL to BSEG-AUGBL)</li>\n<li>2775019 (Setting Open Item to Technical Cleared In the Source System)</li>\n<li>2871310 (Central Payment: Common Constants)</li>\n<li>3003193 (Central Payment: Document Incorrectly 'Technically Cleared' in Central Finance System)</li>\n<li>3008379 (Reversal Document Is Replicated Incorrectly to the Central Finance System)</li>\n<li>3071513 (Item that Should be Cleared by Itself is Set 'Technically Cleared')</li>\n<li>2487776 (Bug fixing for function module FIN_CFIN_CPCTL_GEN)</li>\n<li>2318106 (Implementation of SEPA / Credit Card functionalities in Source System)</li>\n<li>2495144 (Switchable Authorization checks for RFC in Central Finance)</li>\n<li>2699290 (Set Open Items as Technically Cleared in Reverse Process for Central Payment case)</li>\n<li>3425924 (BSEG-MWSKZ Updating in Vendor/Customer Item with Blank Instead of '**')</li>\n<li>3438472 (Open Items under Company Code without Central Payment are Technically Cleared in Cross-Company Documents)</li>\n</ul>\n<p>You have installed SAP System Landscape Transformation Server Content according to SAP Note 2494127.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Complete the following steps:</p>\n<ol>\n<li>Implement the above-mentioned prerequisite SAP Notes.</li>\n<li>Implement this SAP Note in the Central Finance system.</li>\n<li>Implement SAP Note 3134706 in the Central Finance system.</li>\n<li>Implement the relevant configuration and the Customizing activities for Central Payment according to <a href=\"https://help.sap.com/viewer/26c2d5e366bc44c1a98f2a9212a0c49d/1709%20000/en-US/143d4389a91241dbaf3a2c30c3455fbc.html\" target=\"_blank\">Online Help</a>.</li>\n</ol>", "noteVersion": 66}]}, {"note": "2661159", "noteTitle": "2661159 - Release information for \"SAP for Banking\" in connection with \"SAP S/4HANA 1809\" (\"SAP S/4HANA, on-premise edition 1809\")", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning to use \"SAP S/4HANA 1809\" (\"SAP S/4HANA, on-premise edition 1809\") in connection with applications or solutions from the \"SAP for Banking\" area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With regard to the release and the restrictions/limitations, the statements already made in SAP Note 2211665 also apply for \"SAP S/4HANA 1809\" (\"SAP S/4HANA, on-premise edition 1809\").</p>\n<p>In addition, the following information/restrictions/limitations are to be taken into account:</p>\n<ul>\n<li>FS-CML Consumer and Mortgage Loans: There is no integration with the application \"Central Payment for SAP Central Finance\", that is, the function provided by the application \"Central Payment for SAP Central Finance\" is <span>not</span> available in connection with FS-CML.<br/>Also see SAP Note 2346233 at this point.</li>\n<li>The application \"FS-CYT Capital Yield Tax Management\" is a separate software component (that is, it is not a part of the \"SAP S/4HANA 1809\" (\"SAP S/4HANA, on-premise edition 1809\") delivery). As of Support Package 13 for the Release \"SAP Capital Yield Tax Management for Banking 8.0\", FS-CYT can be used as an add-on for \"SAP S/4HANA 1809\" (\"SAP S/4HANA, on-premise edition 1809\").</li>\n<li>As of Release 8.0 and Support Package 08, the application \"SAP Payment Engine\" (FS-PE Payment Engine) is released for integration with \"SAP S/4HANA 1809\" (\"SAP S/4HANA, on-premise edition 1809\").</li>\n<li>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>no longer</span> available.<br/>At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.<br/>Also see SAP Note 2369934 at this point.</li>\n<li>FS-TXS SAP Funding Management: SAP Funding Management (FS-TXS) is <span>not</span> released for use in \"SAP S/4HANA 1809\" (\"SAP S/4HANA, on-premise edition 1809\").<br/>Comment: A separate installation of SAP Funding Management in combination with a remote communication to FS-CML is permitted as of SAP Funding Management 3.0 Support Package 07.</li>\n</ul>", "noteVersion": 1, "refer_note": [{"note": "2659710", "noteTitle": "2659710 - SAP S/4HANA 1809: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note informs you about all restrictions in this release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restrictions, SAP S/4HANA 1809</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides information about the restrictions that exist for the SAP S/4HANA 1809 release.</p>\n<p><strong><strong>Note</strong>:</strong> This SAP Note is subject to change. Check this SAP Note for changes on a regular basis. All important changes are documented in section \"Important Changes after Release of SAP S/4HANA 1809\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>General Restrictions:</span></strong></p>\n<ul>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">In SAP S/4HANA the maximum length of the material number has been extended to 40 characters. Long material number-related restrictions are described in SAP Note <a href=\"/notes/2233100\" target=\"_blank\">2233100</a>.</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">In SAP S/4HANA 1809 currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. Related restrictions are described in SAP Note <a href=\"/notes/2601956\" target=\"_blank\">2601956</a>.</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">The extensibility framework is currently not integrated in the <em>privacy by default</em> functionality of S/4HANA. Do not use this functionality for the processing of personal data if data protection legislation is applicable.</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">The released information for add-ons can be found in SAP Note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">The attached Business Functions are not released with SAP S/4HANA 1809.       </span></li>\n<ul type=\"circle\">\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">It is not permitted to switch them on, even if it is technically possible (see <a href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001649302018&amp;iv_guid=00109B36D5C21ED9B6A913D3169F60CF&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334B68CF734378A3730B48A77740C09C82D2D09F228F5F2CB490B378BA80C742BF2F12A2C282E09F4CFCF0D8C30F6310C8F30D7750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\" target=\"_blank\">attachment</a>). Additional information regarding Always-Off and Always-On Business Functions can be found in SAP Notes <a href=\"/notes/2240359\" target=\"_blank\">2240359</a> and <a href=\"/notes/2240360\" target=\"_blank\">2240360</a>.</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">If a Business Function was switched on in the start release system, but defined as always off in SAP S/4HANA, then a system conversion is not yet possible with this release at that current point in time.</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">If a Business Function was switched off in the start release system, but defined as always on in the target release, then the business function will be automatically activated during the conversion.<br/></span></li>\n</ul>\n<li>\n<div class=\"MsoNormal\"><span arial',sans-serif;\"=\"\"><span arial',sans-serif;\"=\"\">Restrictions for ABAP Platform 1809 as part of SAP S/4HANA 1809 are described in SAP Note <a href=\"/notes/2670347\" target=\"_blank\">2670347</a>. </span></span></div>\n</li>\n<li><span arial',sans-serif;\"=\"\"><span arial',sans-serif;\"=\"\">Restrictions for Java components as part of SAP S/4HANA 1809 are described in SAP Note <a href=\"/notes/2670319\" target=\"_blank\">2670319</a>.</span></span></li>\n<li><span arial',sans-serif;\"=\"\"><span arial',sans-serif;\"=\"\">The SAP S/4HANA 1809 skills provided for SAP CoPilot have functional issues when used with SAP CoPilot 1808. SAP plans to resolve these with SAP S/4HANA 1809 Feature Pack Stack 1 so that they work with SAP CoPilot 1811.</span></span></li>\n<li><span arial',sans-serif;\"=\"\"><span arial',sans-serif;\"=\"\">Restrictions apply to the usage of the CDS View Replication functionality; for details refer to SAP Note <a href=\"/notes/2769627\" target=\"_blank\">2769627</a></span></span></li>\n</ul>\n<p><strong><span><strong>Conversion Restrictions:</strong></span></strong></p>\n<p class=\"MsoNormalCxSpFirst\"><span arial',sans-serif;\"=\"\">Restrictions for system conversions to SAP S/4HANA 1809:</span></p>\n<ul type=\"disc\">\n<li class=\"MsoNormalCxSpMiddle\"><span arial',sans-serif;\"=\"\">The conversion from source releases below SAP ERP 6.0 EHP 5 (SAP_APPL 605) is supported for systems containing vendors with assigned contact but you need to ensure to update to the following minimum SP levels upfront.<br/></span><span arial',sans-serif;\"=\"\">In addition, you need to implement corrective SAP Note <a href=\"/notes/2383051\" target=\"_blank\">2383051</a> and further corrections into the system. For this purpose, please get in contact with SAP via customer incident on component AP-MD-BF-SYN.<br/>The minimum SP levels are: </span></li>\n</ul>\n<ol type=\"1\">\n<ul type=\"circle\"><ol type=\"1\">\n<li class=\"MsoNormalCxSpMiddle\"><span arial',sans-serif;\"=\"\">SAP ERP 6.0 SP20</span></li>\n<li class=\"MsoNormalCxSpMiddle\"><span arial',sans-serif;\"=\"\">EHP2 FOR SAP ERP 6.0 SP10</span></li>\n<li class=\"MsoNormalCxSpMiddle\"><span arial',sans-serif;\"=\"\">EHP3 FOR SAP ERP 6.0 SP09</span></li>\n<li class=\"MsoNormalCxSpMiddle\"><span arial',sans-serif;\"=\"\">EHP4 to EHP8 FOR SAP ERP 6.0 No minimum SP required.</span></li>\n</ol></ul>\n</ol>\n<p class=\"MsoNormalCxSpMiddle\"><span arial',sans-serif;\"=\"\">  This is not relevant for systems which do not have vendors with assigned contacts. You can also verify for existence of records in table KNVK where LIFNR has non-initial values (KNVK-LIFNR &lt;&gt; ‘’).</span></p>\n<ul type=\"disc\">\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">Restrictions in conversions for DIMP-LAMA: see SAP Note <a href=\"/notes/2384347\" target=\"_blank\">2384347</a>.</span><span arial',sans-serif;\"=\"\"><br/></span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">The following add-ons cannot be converted to SAP S/4HANA 1809 in one step but require an intermediate upgrade step:</span></li>\n<ul type=\"circle\">\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">The Israeli Annexing Transport solution (see SAP Note <a href=\"/notes/2298073\" target=\"_blank\">2298073</a> for more details)</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">SRM_SERVER 550/SRM_PLUS 550 (see SAP Note <a href=\"/notes/2251946\" target=\"_blank\">2251946</a> for more details)</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">SAP Financial Closing cockpit 1.0 (see SAP Note <a href=\"/notes/2513315\" target=\"_blank\">2513315</a> for more details)</span></li>\n<li class=\"MsoNormal\">SAP Revenue Accounting and Reporting 1.0, 1.1, 1.2 (see SAP Note <a href=\"/notes/2675360\" target=\"_blank\">2675360</a> for more details)</li>\n</ul>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">For restrictions concerning Cost Of Goods Manufactured (COGM) please see SAP Note <a href=\"/notes/2270414\" target=\"_blank\">2270414</a>.</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">For restrictions concerning New Inventory Management Data Model, see SAP Note <a href=\"/notes/2493434\" target=\"_blank\">2493434</a>.  </span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">Platform Restrictions: Upgrades and conversions of start systems based on 'IBM i' are planned to be supported. The related SUM 2.0 SP5 guide update is planned for end of May 2019.<br/></span></li>\n</ul>\n<p><strong><span>Performance Restrictions:</span></strong></p>\n<ul type=\"disc\">\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">Performance restrictions for SAP S/4HANA 1809 can be found in SAP Note <a href=\"/notes/2696716\" target=\"_blank\">2696716</a>.<br/></span></li>\n</ul>\n<p><strong><span>Core Solution Restrictions:</span></strong></p>\n<ul>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">In SAP S/4HANA on-premise, Event-Based Revenue Recognition is not available for Sell from Stock scenarios. Refer to SAP Note <a href=\"/notes/2581947\" target=\"_blank\">2581947</a>. </span><span>The Fiori app \"Revenue Recognition (Event-Based) - Sales Orders\" (F2441) is only available for cloud customers. It  cannot be used, and there is no support in on-premise installations.           </span></li>\n<li class=\"MsoListParagraph\"><span arial',sans-serif;\"=\"\">Only Project Based Services are supported with Event-Based Revenue Recognition, see also SAP Note <a href=\"/notes/2696359\" target=\"_blank\">2696359</a>. </span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">Restrictions related to Responsibility Management:</span></li>\n<ul type=\"circle\">\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">Custom Responsibility definitions can be customized only for the following team categories: </span><span arial',sans-serif;\"=\"\">PROC (Procurement), CPROC (Central Procurement), and SCR1 (Change Record Discrete Industry).</span></li>\n<li class=\"MsoNormal\"><span arial',sans-serif;\"=\"\">You as data controller are responsible for ensuring that the data used in responsibility definitions in teams are managed in accordance with any applicable legal requirements or business needs, such as data protection legislation or data life cycle requirements. Responsibility definitions in the Fiori app \"Manage Teams and Responsibilities\" (F2412) are currently not integrated in <em>privacy by default</em> functionality. Therefore, the responsibility definitions in teams should not be used for the processing of personal data if this processing falls under any applicable data protection legislation.<br/></span></li>\n</ul>\n<li>\n<div><span>Further area-specific restrictions for SAP S/4HANA 1809 are described in the following SAP Notes:</span></div>\n</li>\n</ul>\n<div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"311\">\n<p><strong>Area</strong></p>\n</td>\n<td width=\"123\">\n<p><strong>Restrictions SAP Note(s)</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Master Data Governance</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2656712\" target=\"_blank\">2656712</a><br/><a href=\"/notes/2656693\" target=\"_blank\">2656693<br/></a><a href=\"/notes/2893888\" target=\"_blank\">2893888</a><a href=\"/notes/2656693\" target=\"_blank\"><br/></a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>LoB Finance</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2696359\" target=\"_blank\">2696359</a><br/><a href=\"/notes/2661581\" target=\"_blank\">2661581</a><br/><a href=\"/notes/2668594\" target=\"_blank\">2668594</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>LoB Manufacturing</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2668349\" target=\"_blank\">2668349</a><br/><a href=\"/notes/2668384\" target=\"_blank\">2668384</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>LoB Supply Chain - TM<br/>LoB Supply Chain - PPDS<br/>LoB Supply Chain - EWM</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2663403\" target=\"_blank\">2663403</a><br/><a href=\"/notes/2666947\" target=\"_blank\">2666947</a><br/><a href=\"/notes/2668150\" target=\"_blank\">2668150</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>LoB Sales and Distribution</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2348936\" target=\"_blank\">2348936</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>LoB Commerce- SAP Hybris Billing</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2351374\" target=\"_blank\">2351374</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>advanced Available-to-Promise (aATP)</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2642047\" target=\"_blank\">2642047</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>International Trade</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2669431\" target=\"_blank\">2669431</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Globalization</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2668307\" target=\"_blank\">2668307</a></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Best Practices Content Framework</p>\n</td>\n<td width=\"123\">\n<p><span><a href=\"/notes/2691784\" target=\"_blank\">2691784</a></span></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</div>\n<div> </div>\n<div> </div>\n<div> </div>\n<div> </div>\n<p><strong><span>Industry Solution Restrictions:</span></strong></p>\n<p><span> </span></p>\n<p><span>SAP S/4HANA 1809 supports the following industries:</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p><strong>Industry</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><strong>Restrictions <br/>SAP Note</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Aerospace &amp; Defense (A&amp;D)</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2668085\" target=\"_blank\">2668085</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Automotive</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2668075\" target=\"_blank\">2668075</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Banking</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2661159\" target=\"_blank\">2661159</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Catch Weight Management (CWM)</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2671323\" target=\"_blank\">2671323</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Consumer Products</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2668465\" target=\"_blank\">2668465</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Defense and Security</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2667104\" target=\"_blank\">2667104</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Mill Products</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2665709\" target=\"_blank\">2665709</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Product Compliance</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2667853\" target=\"_blank\">2667853</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Public Sector</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2662228\" target=\"_blank\">2662228</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>For the restriction on SAP Patient Management (IS-H) see SAP Note <a href=\"/notes/2689873\" target=\"_blank\">2689873</a>.</li>\n<li>Do not use the SAP S/4HANA 1809 Feature Package Stack 01 release of Utilities Product Integration Layer (UPIL).</li>\n</ul>\n<p> </p>\n<p><strong><span arial',sans-serif;=\"\" black;\"=\"\" color:=\"\"><span>Important Changes after Release of SAP S/4HANA 1809:<br/></span></span></strong></p>\n<p>[2019-01-30] Restriction on UPIL added as of FPS1. <br/> [2018-10-24] Restriction related to SAP Revenue Accounting and Reporting 1.0, 1.1, 1.2 added.<br/> [2018-10-04] Restriction related to SAP CoPilot 1808 added. <br/> [2018-09-21] The conversion-related restriction for start releases with CPM 2.0 SP08 is resolved.<br/> [2019-04-29] CDS View Replication Restriction SAP Note <a href=\"/notes/2769627\" target=\"_blank\">2769627</a> added as of FPS2.<br/>[2020-06-25] Restriction SAP Note 2893888<a href=\"/notes/2893888\" target=\"_blank\">2893888</a> for SAP S/4HANA MDM added</p>\n<p> </p>\n<p> </p>", "noteVersion": 64}]}, {"note": "2059467", "noteTitle": "2059467 - Release Information for SAP for Banking together with SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You plan to run \"SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA\" for Banking.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Starting with ERP 6.0 EHP7 SP05, you can use the \"SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA\" SP02 for Banking.</p>\n<p><span>Restrictions</span>:</p>\n<ul>\n<li>Average Daily Balances: The calculations of averages like Month-to-Date, Quarter-to-Date and Year-to-Date is not covered in the SAP Simple Finance add-on.</li>\n<li>Multi Currency Accounting for Banks and Error Correction and Suspense Accounting are not released based on the SAP Simple Finance add-on.</li>\n<li>Cash Management cannot be used in installations with FI-CA and the SAP Simple Finance add-on in one system.</li>\n<li>Loans Management for Banking – Suite Edition (Consumer and Mortgage Loans - CML). The connection of CML to SAP Cash Management powered by SAP HANA is not released based on the SAP Simple Finance add-on.<br/>Remark: Resolution with Support Package 3 of the \"SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA\"</li>\n<li>The integration of CYT (Capital Yield Tax Management) with CML and CFM (FIN-FSCM-TRM - Treasury und Risk Management) is not released based on the SAP Simple Finance add-on.</li>\n<li>SAP Leasing for Banking (including Lease Accounting Engine) is not released based on the SAP Simple Finance add-on.</li>\n<li>SAP SEM Banking components (Datapool, Profit, Risk, Strategy Analyzer) are not released based on the SAP Simple Finance add-on.</li>\n<li>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>no longer</span> available. This is due to the entry with the subject \"Real Estate Classic\" in the \"Simplification List for SAP S/4HANA\". At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.</li>\n<li>FS-CML Consumer and Mortgage Loans: It is important to note that the application component FI (Financial Accounting) <span>no longer</span> supports the calculation of interest on arrears at customer level (transaction F.24 or report RFDUZI00). For this, application FS-CML has been providing transaction FIOA for the calculation of interest on arrears for quite some time.</li>\n</ul>\n<p> Remark: Remediation of Field Length topic was not approached in combination with the SAP Simple Finance add-on.</p>\n<p><span>Additional Information about the validity of the note: </span></p>\n<p>Components IS-B-BCA, FS-CML, FS-CMS, FS-BP, FS-RBD (EA-FINSERV): Starting with release ERP 6.0 EHP 7 SP 5</p>\n<p>Component FS-TXS (FUNDMGMT): Starting with release SAP Funding Management 2.0 (only separate installation possible – independent of ERP installation)</p>\n<p>Component FS-CYT (CYT): Starting with SAP Capital Yield Tax Management for Banking 8.0 SP 2</p>\n<p>Components FS-AM, FS-BA (FSAPPL): banking services from SAP 8.0 (only separate installation possible – independent of ERP installation)</p>\n<p> </p>\n<p> </p>", "noteVersion": 10}, {"note": "2328754", "noteTitle": "2328754 - Release information for \"SAP for Banking\" in connection with \"SAP S/4HANA 1610\" (\"SAP S/4HANA, on-premise edition 1610\")", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning to use \"SAP S/4HANA 1610\" (\"SAP S/4HANA, on-premise edition 1610\") in connection with applications or solutions from the \"SAP for Banking\" area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With regard to the release and the restrictions/limitations, the statements already made in SAP Note 2211665 also apply for \"SAP S/4HANA 1610\" (\"SAP S/4HANA, on-premise edition 1610\").</p>\n<p>In addition, the following information/restrictions/limitations are to be taken into account:</p>\n<ul>\n<li>\n<div>FS-CML Consumer and Mortgage Loans: There is no integration with the application \"Central Finance Central Payment (Pilot Release)\", that is, the function provided by the application \"Central Finance Central Payment (Pilot Release)\" is <span>not</span> available in connection with FS-CML.<br/>Also see SAP Note 2346233 at this point.</div>\n</li>\n<li>The application \"FS-CYT Capital Yield Tax Management\" is a separate software component (that is, it is not a part of the \"SAP S/4HANA 1610\" (\"SAP S/4HANA, on-premise edition 1610\") delivery). As of Support Package 10 for the Release \"SAP Capital Yield Tax Management for Banking 8.0\", FS-CYT can be used as an add-on for SAP S/4HANA 1610\" (\"SAP S/4HANA, on-premise edition 1610\").</li>\n<li>As of Release 8.0 and Support Package 6, the application \"SAP Payment Engine\" (FS-PE Payment Engine) is released for integration with \"SAP S/4HANA 1610\" (\"SAP S/4HANA, on-premise edition 1610\"). </li>\n</ul>", "noteVersion": 4, "refer_note": [{"note": "2333141", "noteTitle": "2333141 - SAP S/4HANA 1610: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA 1610, respectively you are planning or running a conversion to SAP S/4HANA 1610.</p>\n<p>This SAP Note, which is subject to change, informs you about all restrictions in this release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restrictions, SAP S/4HANA 1610</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note provides information about the restrictions that exist for SAP S/4HANA 1610.</p>\n<p>Note: This SAP Note is subject to change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<ul>\n<li>SAP S/4HANA 1610 supports the following industries/solutions:</li>\n<ul>\n<li>Consumer Products</li>\n<li>Wholesale</li>\n<li>Life Sciences</li>\n<li>Aerospace &amp; Defense (A&amp;D)</li>\n<li>HighTech</li>\n<li>Industrial Machinery &amp; Components (IM&amp;C)</li>\n<li>Automotive</li>\n<li>Chemicals</li>\n<li>Mining</li>\n<li>Mill Products</li>\n<li>Utilities</li>\n<li>Banking</li>\n<li>Insurance</li>\n<li>Public Sector</li>\n<li>Engineering, Construction &amp; Operations (EC&amp;O)</li>\n<li>Professional Services</li>\n<li>Telecommunication</li>\n<li>Sports &amp; Entertainment</li>\n<li>Transportation &amp; Logistics</li>\n<li>Contract Accounts Receivable and Payable (FI-CA)</li>\n<li>Higher Education and Research</li>\n<li>Defense and Security</li>\n<li>Oil and Gas</li>\n<li>Retail<br/>Please also check the linked SAP Notes for restrictions.</li>\n</ul>\n<li>In SAP S/4HANA, the maximum length of the material number has been extended to 40 characters. <em>Long material number-</em>related restrictions are described in SAP Note <a href=\"/notes/2233100\" target=\"_blank\">2233100</a>.</li>\n<li>The integrated solution of Service Parts Management using SAP CRM Order Management with an active Service Parts Management configuration (Direct Delivery Scenario) in combination with S/4HANA is not released with SAP S/4HANA 1610.</li>\n<li>The attached Business Functions are not released with SAP S/4HANA 1610. It is not permitted to switch them on, even if this is technically possible (see <a href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000721462016&amp;iv_guid=00109B36BC8E1ED9B3F9C1950A6060E1&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334318AF734378A3730B4AA2C0EA8CC0FAE4C37092E702B0D37AA4A3676F6764DB24C76B60C8C32C94D2D7749CC31ADF2090BD7750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\" target=\"_blank\">attachment</a>). Additional information regarding Always-Off and Always-On Business Functions can be found in SAP Notes <a href=\"/notes/2240359\" target=\"_blank\">2240359</a> and <a href=\"/notes/2240360\" target=\"_blank\">2240360</a>. If a business function was switched on in the start release system, but defined as always_off in SAP S/4HANA, then a system conversion is not yet possible with this release at that current point in time. If a business function was switched off in the start release system, but defined as always_on in the target release, then the business function will be automatically activated during the conversion.</li>\n<li>There are limitations for packaging material with more than 35 characters in combination with Returnable Packaging Logistics (application component MM-IM-RL or IS-A-RL). These are:</li>\n<ul>\n<li>EDI processing of returnable packaging account statements (message type ACCSTA). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.</li>\n<li>EDI processing of returnable packaging account statement requests (message type ACCSTAREQ). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.</li>\n</ul>\n<li>The released information for Add-Ons can be found in SAP Note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</li>\n<li>Account Numbers: In case of usage of the Best Practices with demo data, Account Numbers need to consist of 10 digits. They need to consist of numeric digits only. No alphanumeric characters are allowed at the moment. Examples of valid account numbers: \"*********; **********\".</li>\n<li>Further area-specific restrictions for SAP S/4HANA 1610 are described - amongst others - in the linked SAP Notes, e.g.:</li>\n<ol>\n<li>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"277\"/> <col width=\"65\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl67\" height=\"20\" width=\"277\"><strong>Area</strong></td>\n<td class=\"xl67\" width=\"65\"><strong>SAP Note</strong></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Master Data Governance</td>\n<td class=\"xl66\"><a href=\"/notes/2349002\" target=\"_blank\">2349002</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Aerospace &amp; Defense (A&amp;D)</td>\n<td class=\"xl66\"><a href=\"/notes/2349454\" target=\"_blank\">2349454</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Automotive</td>\n<td class=\"xl66\"><a href=\"/notes/2347206\" target=\"_blank\">2347206</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Mill Products</td>\n<td class=\"xl66\"><a href=\"/notes/2229398\" target=\"_blank\">2229398</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Banking</td>\n<td class=\"xl66\"><a href=\"/notes/2328754\" target=\"_blank\">2328754</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Public Sector</td>\n<td class=\"xl66\"><a href=\"/notes/2348636\" target=\"_blank\">2348636</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Finance</td>\n<td class=\"xl66\"><a href=\"/notes/2344977\" target=\"_blank\">2344977</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB Manufacturing: QM</td>\n<td class=\"xl66\"><a href=\"/notes/2345333\" target=\"_blank\">2345333</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB Manufacturing: MM-IM</td>\n<td class=\"xl66\">\n<p><a href=\"/notes/2345321\" target=\"_blank\">2345321<br/></a><a href=\"/notes/2440007\" target=\"_blank\" title='2440007  - Error in Fiori App \"Post Goods Receipt for Purchase Order\" (F0843) due to bug inSAP HANA 2.0 SPS 00 Database Revision 001'>2440007</a><a href=\"/notes/2345321\" target=\"_blank\"><br/></a></p>\n</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB CEC</td>\n<td class=\"xl66\"><a href=\"/notes/2231667\" target=\"_blank\">2231667</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Globalization</td>\n<td class=\"xl66\"><a href=\"/notes/2349004\" target=\"_blank\">2349004</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Best Practices Framework</td>\n<td class=\"xl66\"><a href=\"/notes/2348479\" target=\"_blank\">2348479</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Environment, Health and Safety (EHS)</td>\n<td class=\"xl66\"><a href=\"/notes/2383548\" target=\"_blank\">2383548</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Global Trade Service (GTS)</td>\n<td class=\"xl66\"><a href=\"/notes/2382239\" target=\"_blank\">2382239</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Waste and Recycling</td>\n<td class=\"xl66\"><a href=\"/notes/2232552\" target=\"_blank\">2232552</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Defense and Security</td>\n<td class=\"xl66\"><a href=\"/notes/2339505\" target=\"_blank\">2339505</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Consumer Products</td>\n<td class=\"xl66\"><a href=\"/notes/2355560\" target=\"_blank\">2355560</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Sales</td>\n<td class=\"xl66\">\n<p><a href=\"/notes/2348936\" target=\"_blank\">2348936</a><br/><a href=\"/notes/2369405\" target=\"_blank\">2369405</a></p>\n</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">SAP Hybris Billing</td>\n<td class=\"xl66\"><a href=\"/notes/2351374\" target=\"_blank\">2351374</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Oil and Gas</td>\n<td class=\"xl66\"><a href=\"/notes/2349061\" target=\"_blank\">2349061</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Project Systems</td>\n<td class=\"xl66\"><a href=\"/notes/2353392\" target=\"_blank\">2353392</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Integration</td>\n<td class=\"xl66\"><a href=\"/notes/2376061\" target=\"_blank\">2376061</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Extended Warehouse Management</td>\n<td class=\"xl66\"><a href=\"/notes/2347770\" target=\"_blank\">2347770</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">advanced Available-to-Promise (aATP)</td>\n<td class=\"xl66\"><a href=\"/notes/2343524\" target=\"_blank\">2343524</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">\n<p>Available-to-Promise (ATP)</p>\n</td>\n<td class=\"xl66\"><a href=\"/notes/2517708\" target=\"_blank\">2517708</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Production Planning and Detailed Scheduling</td>\n<td><a href=\"/notes/2382787\" target=\"_blank\">2382787</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Extended Warehouse Management (EWM)</td>\n<td class=\"xl66\"><a href=\"/notes/2347770\" target=\"_blank\">2347770</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Catch Weight Management (CWM)</td>\n<td class=\"xl66\"><a href=\"/notes/2357827\" target=\"_blank\">2357827</a></td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li></li>\n<li>Please also check the linked SAP Notes for restrictions.</li>\n</ol>\n<ul>\n<ul></ul>\n</ul>\n<li>Restrictions for conversions to SAP S/4HANA 1610:</li>\n<ul>\n<ul>\n<li>Conversion from source releases below SAP ERP 6.0 EHP 5 (SAP_APPL 605) is supported for systems containing vendors with assigned contact but you need to ensure to update to the following minimum SP levels upfront. In addition, you need to implement corrective SAP Note <a href=\"/notes/2383051\" target=\"_blank\">2383051</a> and further corrections into the system. For this purpose please get in contact with SAP via customer incident on component AP-MD-BF-SYN. The minimum SP levels are: <br/> SAP ERP 6.0 SP20<br/> EHP2 FOR SAP ERP 6.0 SP10<br/> EHP3 FOR SAP ERP 6.0 SP09</li>\n<li>EHP4 FOR SAP ERP 6.0 No Minimum SP required.</li>\n<li>   This is not relevant for systems which do not have vendors with assigned contacts( You can also verify  for existence of records in table KNVK where LIFNR has non initial values (KNVK-LIFNR &lt;&gt; ‘’).</li>\n</ul>\n</ul>\n<ul>\n<li>Conversions for DIMP-LAMA: see SAP Note <a href=\"/notes/2384347\" target=\"_blank\">2384347</a></li>\n<li>Restrictions in conversion paths (the following ADD-Ons cannot be converted to SAP S/4HANA 1610 in one step but require an intermediate upgrade step):</li>\n<ul>\n<li>The Israeli Annexing Transport solution (see SAP Note <a href=\"/notes/2298073\" target=\"_blank\">2298073</a> for more details)</li>\n<li>SRM_SERVER 550/SRM_PLUS 550 (see SAP Note <a href=\"/notes/2251946\" target=\"_blank\">2251946</a> for more details)</li>\n</ul>\n<li>For restrictions concerning Cost Of Goods Manufactured (COGM) please see SAP Note <a href=\"/notes/2270414\" target=\"_blank\">2270414</a>.</li>\n<li>For restrictions concerning New Inventory Management Data Model , see Sap Note <a href=\"/notes/2493434\" target=\"_blank\">2493434</a>.</li>\n<li>Customers which are already using PRF (Procurement Reporting Framework) or are planning to use it after the conversion / upgrade to SAP S/4HANA have to convert / upgrade to SAP S/4HANA 1610 FPS02 or higher. For more information see SAP Note <a href=\"/notes/2481876\" target=\"_blank\">2481876</a>.</li>\n<li>Please also check the linked SAP Notes for restrictions.</li>\n</ul>\n<li>Platform restrictions:</li>\n<ul>\n<li>Conversions on Sybase: SAP Notes <a href=\"/notes/2383862\" target=\"_blank\">2383862</a> and <a href=\"/notes/2384739\" target=\"_blank\">2384739</a><br/>Please also check the linked SAP Notes for restrictions.</li>\n<li>Restrictions for SAP S/4HANA 1610 on SAP HANA 2.0:</li>\n<ul>\n<li>Restriction in LoB Manufacturing MM-IM, see SAP Note <a href=\"/notes/2440007\" target=\"_blank\" title='2440007  - Error in Fiori App \"Post Goods Receipt for Purchase Order\" (F0843) due to bug inSAP HANA 2.0 SPS 00 Database Revision 001'>2440007</a></li>\n</ul>\n</ul>\n<li>SAP HANA 2.0 SPS01 Revision 10: </li>\n<ul>\n<li>Usage of LiveCache not supported </li>\n<li>Migration from HANA 1.0 to 2.0 on Power platform not supported</li>\n</ul>\n<li>SAP HANA 2.0 SPS01 Revision 11: \r\n<ul>\n<li>New installations and conversions using DMO (<a href=\"/notes/2478183\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2478183</a>)</li>\n</ul>\n</li>\n</ul>\n</ul>\n<p> </p>", "noteVersion": 29}]}, {"note": "2270550", "noteTitle": "2270550 - S4TWL - Real Estate Classic", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RE, Classic, real estate management, REMICL, RE-FX Real Estate Management, RE Classic, RE-Classic</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP Real Estate Management is the real estate solution from SAP. From SAP ECC up to and including SAP ERP 6.0, SAP has provided two versions of the real estate solution. The older version is \"Real Estate Classic (RE Classic)\" and the new version is \"Real Estate Flexible (RE-FX)\" (see SAP Note <a href=\"/notes/443311\" target=\"_blank\">443311</a>).</p>\n<p>The editions SAP S/4HANA on premise 1511 and SAP S/4HANA Finance 1503 (and all other OP versions) do not support Real Estate Classic (RE Classic). The business requirements can be covered by using Real Estate Flexible (RE-FX) functionality. As RE-FX is the more up-to-date solution and has a much broader functional scope (see SAP Note<a href=\"/notes/517673\" target=\"_blank\"> 517673</a>), only RE-FX is included as the RE component in the SAP S/4HANA on-premise edition.</p>\n<p><strong>Business Process related information</strong></p>\n<p><strong>Before</strong> the system conversion to SAP S/4HANA, on-premise edition 1511 (or SAP S/4HANA Finance edition 1503, or higher releases), customers who use RE Classic need to migrate RE Classic to RE-FX as part of a migration project. It is not possible to migrate after switching to SAP S/4HANA. Customers have to be aware, that it is not possible anymore to view and display classic RE data in S/4 HANA.</p>\n<p>Migrating data from Classic RE to RE-FX is not a simple technical conversion. Similar to a legacy data transfer, the migration must be planned as a project and executed. In this sense, the migration programs are the technical support for the data transfer project. A migration project can be more or less time consuming depending on how complex your data is, the capacity in which you work with customer developments and the extent to which you use the RE-FX functions.</p>\n<p><br/>For more information, see SAP Note <a href=\"/notes/828160\" target=\"_blank\">828160</a>.</p>", "noteVersion": 5, "refer_note": [{"note": "828160", "noteTitle": "828160 - Migration from Classic RE to RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Migration tools</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Classic RE, RE-FX, migration<br/>FAQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Migration from Classic RE to RE-FX</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>As of Release SAP ERP 6.0 (previously SAP ERP 2005 / SAP ECC 6.00, Financials Extension, EA-FIN 600), you can migrate your data from Classic RE to RE-FX. SAP provides migration programs to help you convert Customizing and application data to the new tables in RE-FX, if a 1:1 conversion is possible. For more information, see the presentation attached to this SAP Note.<br/><br/>Migrating data from Classic RE to RE-FX is not a simple technical conversion. Similar to a legacy data transfer, the migration must be planned as a project and executed. In this sense, the migration programs are the technical support for the data transfer project. A migration project can be more or less time consuming depending on how complex your data is, the capacity in which you work with customer developments and the extent to which you use the RE-FX functions.<br/><br/>For this reason, you have to carefully plan the migration from Classic RE to RE-FX, particularly with regard to the following points:</p>\n<ul>\n<li>Familiarizing yourself with the structures and concepts of the new application RE-FX</li>\n</ul>\n<ul>\n<ul>\n<li>relevant training of the project team and (later) the user, and</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>testing the RE-FX functions</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>comparing the processes used currently by the customer in Classic RE with the processes in RE-FX</li>\n</ul>\n</ul>\n<ul>\n<li>Changing the correspondence letters. Note that you can no longer print SAP script forms from RE-FX. This means that you are required to convert all RE forms and RE-specific dunning forms into PDF-based forms or SAP Smart Forms.</li>\n</ul>\n<ul>\n<li>Changing customer developments and modifications so that the functions covered by this are also available in the migrated system</li>\n</ul>\n<ul>\n<li>Note that the migration programs do not currently convert all data. Some functions are not included in the standard migration; these include:</li>\n</ul>\n<ul>\n<ul>\n<li>Land use management add-on (LUM), see SAP Note 977295 for more information </li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Condominium owner administration add-on (WEG)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Corporate Real Estate Management add-on (CRE)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Quarter days add-on</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Input tax correction data (correction items, correction object), see SAP Note 1043260 for more information.</li>\n</ul>\n</ul>\n<p>You must read the attached PDF document. This document describes how you should proceed and the errors that can occur. Print out this file and read it before you start the migration.<br/><br/>Before you make the first test migration:</p>\n<ul>\n<li>Import the most up-to-date Support Package status (at least ECC 6.00 Support Package 10) and the SAP Notes based on this status about component RE-FX-MI.</li>\n</ul>\n<p><br/>To test the actual conversion:</p>\n<ul>\n<li>Call transaction REMICL. In the background, you can use transaction REMICLBATCH instead. The steps that require manual actions (for example, settings in the IMG) must always be carried out online.</li>\n</ul>\n<ul>\n<li>Call the individual migration points step by step. Read the information. These explain how each step works. Check the log after every step.</li>\n</ul>\n<ul>\n<li>If errors occur during migration and if it is not immediately clear to you how you should resolve them, search the attached documentation for the error number. However, note that the documentation does not describe every possible error.<br/>If, after having read the documentation and the error message long text, you are still unable to resolve the error, contact SAP Support.</li>\n</ul>\n<p><strong>Experiences and recommendations from previous migration projects</strong></p>\n<ul>\n<li>User exits in the migration: see SAP Note 1079141</li>\n</ul>\n<ul>\n<li>Business partners, and addresses:</li>\n</ul>\n<ul>\n<ul>\n<li>The FS04 pushbutton in the BPCONSTANTS table must be set to 1: see also SAP Note 1065388.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>To avoid problems during the address transfer, deactivate the address check: see SAP Note 1032896.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>You are using the General Real Estate Contract, and have assigned a customer or a vendor in the conditions. In this case, use a customer-specific program to ensure that the TR business partner can be created for these accounts before the migration. See also SAP Note 1090827.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>You have created customer and vendor accounts for business partners. These both have bank details with the same ID (for example 0001), but have different account data. In this case, use a customer-specific program before the migration to ensure that different bank details for the same business partner also have different account IDs. You can use a naming convention to help, for example for the account ID 0001 of the vendor account, you can create the account ID K001 in the business partner. During the migration, ensure that the account ID 0001 is replaced with K001 for vendor conditions. You can do this by using the user exit in SAP Note 1079141.</li>\n</ul>\n</ul>\n<ul>\n<li>Accrual/deferral: See SAP Note 980104.</li>\n</ul>\n<ul>\n<li>Service charge settlement: Advance payments posted for non-billed periods are transferred to the \"Advance payments legacy data\" table. This data can only be transferred if the relevant contracts have a corresponding settlement participation, that is, the billing structure must be fully created before the migration so that advance payments are recognized correctly.</li>\n</ul>\n<ul>\n<li>In some cases, inconsistenices can occur in the occupancy history. See SAP Note 1111447 with regards to this.</li>\n</ul>\n<ul>\n<li>To migrate conditions that do not occur in the contract validity period, see SAP Note 997076.</li>\n</ul>\n<ul>\n<li>To improve performance.</li>\n</ul>\n<ul>\n<ul>\n<li>During the transfer, you must keep the database indexes of RE tables up to date. </li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>CO settlement rules: See SAP Note 946523.</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>SAP S/4 HANA release upgrade</strong></p>\n<p>Classic RE cannot be used under SAP S/4HANA and is not released. Existing Classic RE customers must migrate to RE-FX before upgrading to the solution \"SAP S/4HANA, on-premise edition\". Note that the display and call of Classic RE data in SAP S/4 HANA are no longer possible.</p>\n<p> </p>\n<p> </p></div>", "noteVersion": 24}, {"note": "1944871", "noteTitle": "1944871 - SAP Simple Finance, On-Premise Edition: Removing RE Classic", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP Note has been replaced by <a href=\"/notes/2270550\" target=\"_blank\">2270550</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, SFINANCIALS, RE, Classic, real estate management, REMICL, S/4 HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Product strategy</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>See <a href=\"/notes/2270550\" target=\"_blank\">2270550</a></p>", "noteVersion": 6}, {"note": "517673", "noteTitle": "517673 - Flexible Real Estate: Functions and restrictions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note describes which functions are available in the component flexible Real Estate Management (RE-FX) in which release, and which release restrictions apply.<br/><br/></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Flexible Real Estate, Classic Real Estate, Corporate Real Estate, Commercial Real Estate, Residential Real Estate, ramp up, scope of functions</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>1. General information</p>\n<p>You are considering using flexible Real Estate Management (RE-FX). This SAP Note informs you about the important functions that flexible Real Estate Management contains in the following releases:</p>\n<ul>\n<li>Financials Extension Set 1.10 for SAP R/3 Enterprise</li>\n<li>Financials Extension Set 2.00 for SAP R/3 Enterprise</li>\n<li>SAP ERP 2004</li>\n<li>SAP ERP 6.0</li>\n<li>S/4 HANA (see SAP Note 2254013)</li>\n</ul>\n<p><br/>This SAP Note provides an overview of the functions, but is not necessarily complete. If you have detailed questions, we recommend that you request a feasibility study from SAP Consulting or an SAP Implementation Partner.</p>\n<p>2. Explanation of the release descriptions</p>\n<p>For more information about the specified releases, the release strategy and technical prerequisites, see SAP Note 443311.</p>\n<p>3. Usage restriction for Release 1.10 </p>\n<p>Keep in mind that the Flexible Real Estate Management in Financials Extension Set 1.10 for SAP R/3 Enterprise is no longer released. This is due to the limited functional scope of this version and the basic changes of the user interfaces for subsequent releases, which would require additional training.  Therefore, Flexible Real Estate Management in Financials Extension Set 1.10 for SAP R/3 Enterprise is not suitable for implementation and we no longer maintain it.</p>\n<p>4. International availability</p>\n<p>The following section gives some descriptions about country-specific functions.  However, these are not described completely in this SAP Note. For information about the availability of SAP Real Estate Management for different countries, see SAP Note 771098.</p>\n<p>5. Adjustments to the solution and programming interfaces</p>\n<p>For information about customer-specific adjustments to the solution with the SAP standard tools BAdI and BDT, see SAP Note 690900, and for information about programming interfaces, see SAP Note 782947.</p>\n<p>6. Additional information</p>\n<p>If you are interested in using the solution, visit SAP Service Marketplace (http://service.sap.com/re) and the SAP Help Portal (http://help.sap.com/) and read the information available there.</p>\n<p>For individual questions, use your SAP Account Manager to contact the SAP Product Sales in your country. </p>\n<p>For general questions regarding this SAP Note, please contact Tom Anderson (<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>) or Marc Hoffmann (<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1. Functional scope of Flexible Real Estate in SAP R/3 Enterprise Financials Extension Set 2.00 </p>\n<p>This release contains the following functions:</p>\n<p>a) Usage view</p>\n<p>In the usage view, you display the objects for the business usage of your real estate.  These include business entities, buildings, properties and rental objects.  Rental objects can be classic rental units, pooled spaces or rental spaces with reference to a pooled space. </p>\n<p>b) Contract management</p>\n<p>With the real estate contract, you can manage all contracts of Real Estate Management, including lease-in contracts, lease-out contracts and general customer or vendor contracts.  You can assign any number of rental objects from different business entities and buildings to a real estate contract.  In addition, you can define (\"cut\") rental spaces with reference to a pooled space when you create a contract.  The real estate contract also enables you to monitor tasks with the reminders function.</p>\n<p>c) Rental accounting</p>\n<p>As in Classic Real Estate, you can create a cash flow from the conditions defined in the contract, which you can use to trigger postings in SAP Financial Accounting (FI).  By means of the partners assigned in the contract, the system determines and posts customer or vendor accounts in FI-AR or FI-AP. </p>\n<p>d) Architecture</p>\n<p>If you want to display characteristics of the architectural view of a real estate, a structure of architectural objects is available in addition to the objects of the usage view mentioned above.  You can define these freely and use them independently of the company code at all hierarchy levels of the system.  Within the architectural view, you have the option of automatically creating and updating functional locations in SAP Plant Maintenance (OM/CS).  The connection between the architectural view and the usage view of real estate objects is comprehensively supported.  For example, you can assign several architectural rooms to one pooled space.  To define pooled spaces and rental spaces, you can refer to the architectural view.  For example, you can define the rooms of the architectural view that comprise a rental space or a pooled space.  This function is especially relevant for the management of real estates for your own use (corporate usage), but this function is also interesting for externally rented buildings if the buildings are not rented as a whole entity. </p>\n<p>e) Correspondence</p>\n<p>To create correspondence from RE-FX, use SAP Smart Forms.  RE-FX no longer supports SAP script technology.  Sample correspondence cases are delivered by SAP for Smart Forms. Compared to the conventional SAP script technology, SAP Smart Forms have the advantage that you can design the letters in a graphic editor and you can display them immediately. As of SAP ERP 6.0, you can use PDF-based forms in the Flexible Real Estate Management (see below). </p>\n<p>f) Business partner</p>\n<p>RE-FX uses SAP Business Partner instead of the Treasury Business Partner used before.</p>\n<p>g) RE Navigator</p>\n<p>You can edit all master data objects, contracts, and settlement units in a central hierarchical view.  From this view, you can navigate across all objects. In addition, a variety of useful functions for the object processing is available here.</p>\n<p>h) Direct input or transfer tools for data from external systems and migration functions from RE-FX of Release 4.6C or lower:</p>\n<p>BAPIs are available to transfer data from external systems.  These BAPIs are linked to the Data Transfer Workbench (SXDA).  Migration tools for the automatic conversion of data from SAP Real Estate Management to SAP R/3 4.7 Core, SAP R/3 4.6C or lower (Classic RE) are not yet available, but you can set up a new client on the basis of this data as part of an implementation project. </p>\n<p>i) Input tax distribution</p>\n<p>You can now perform input tax distribution according to the option rate determined for each real estate object and post the relevant values in Financial Accounting.</p>\n<p>j) Reporting</p>\n<p>Reports for evaluating the occupancy, the measurements and conditions, the master data and the service charge settlement are available in the ECC (Enterprise Core Components).  To evaluate actual and target values from Controlling (CO), extractors are available for Real Estate Management, as well as InfoCubes, queries and workbooks in Business Warehouse (BW). Contract evaluations now support the selection according to partners and real estate objects.</p>\n<p>k) Service charge settlement</p>\n<p>Due to the new master data structure, you can settle contracts with several rental objects using the new service charge settlement.  The most important functions of the service charge settlement are supported in the same way as in Classic Real Estate.  Useful enhancements include the settlement in steps with a restart function, the simulation of service charge settlements with all evaluations, and the fixing of results without posting.</p>\n<p>l) Rent adjustment as free adjustment and as index adjustment</p>\n<p>You can find adjustment methods for the \"Free adjustment\" and \"Index rent adjustment\" methods in the system.  In addition to the main program, which controls the adjustment selection and adjustment processing, the correspondence templates required for the communication with your tenants are also available.</p>\n<p>m) Entry and settlement of sales-based rents</p>\n<p>You can define as many sales-based rent agreements as you want in a contract.  These agreements contain rules for determining sales-based rent (sales rules) and the sales to be reported (reporting rules).  You can use a sales report to calculate as many sales-based conditions as you want. The reporting rules can contain as many material groups (sales types) as you want.  In addition, you manage minimum rents and advance payments for sales-based rent as separate conditions.  To do this, functions for editing contract data, entering sales reports, as well as functions for the sales-based rent settlement, relevant reporting and correspondence templates for communication with tenants or landlords are available in the system. </p>\n<p>n) Security deposit management</p>\n<p>Using security deposit management, you can define different types of security deposit agreements, as well as specifying when, in which form and by whom the security deposits were provided.  In addition to security deposits that are predefined in the contract, you can calculate the agreed security deposit depending on the contract conditions (the rent, for example).  Furthermore, your agreement can state that the agreed security deposit must also be adjusted in the case of a condition adjustment (for example, when you increase a rent).</p>\n<p>o) Integration in SAP Controlling (CO)</p>\n<p>The system supports the update of actual values as well as manual planning of costs, activity inputs and revenues.  In addition, the standard CO copy functions and the transfer of values from the cash flow to CO plan values are available as planning aids.  The CO settlement with real estate object as sender and receiver is also available. Note that net costs are passed on only once for all CO settlement functions and assessment functions. (Proportional tax is taken into account only if it is contained in the sender object as cost). </p>\n<p>p) Reminders</p>\n<p>You can use the reminders functions for all real estate objects (not only for contracts).  In addition, the system automatically copies the reminder dates to SAP Appointment Calendar.</p>\n<p>2. Functional scope of Flexible Real Estate in SAP ERP 2004</p>\n<p>After the upgrade you must perform a conversion.  Refer to the release notes, in particular to \"Basic changes to the master data tables\" (RE_ECC500_BD_CHANGE) and SAP Note 681951.</p>\n<p>This release contains the following new functions:</p>\n<p>a) Accruals and deferrals (delivered in the Support Package)</p>\n<p>You can use the functions for accruals and deferrals to assign cross-period revenues or costs period-specifically in financial accounting.  Each accruals and deferrals run creates relevant documents about all items that belong to the previous period or the current period after the reference period.  This may created many documents. </p>\n<p>Therefore, for system performance reasons, we <span>strongly</span> recommend that you do not use the option of daily accruals and deferrals.</p>\n<p>b) Enhanced link between architectural view and usage view</p>\n<p>The optimized link between the architectural view and the usage view allows you to refer to the architectural objects (rooms, for example) and their measurement when you define rental spaces and pooled spaces. </p>\n<p>c) Reason for vacancy</p>\n<p>You can specify a time-dependent reason for vacancy for the posting parameters.  This enables you to call up alternative account determinations and cost center determinations for each reason for vacancy.</p>\n<p>d) Rent adjustment according to representative list of rents</p>\n<p>You can now also make rent adjustments according to representative lists of rents.  The delivery Customizing contains sample entries for local representative lists of rents. In comparison with Classic RE, the system can display qualified representative lists of rents, in addition to the already existing control table that you can use to display the special features of the relevant local representative list of rents.  The fixtures and fittings characteristics of the rental object were enhanced so that you can assign valuation points and valuation factors to them, depending on the assigned representative list of rents.</p>\n<p>e) Rent adjustment using comparative apartments</p>\n<p>Based on the main program that already exists, you can now also make rent adjustments using comparative apartments.  These comparative apartments can be both apartments from the owned real estate and external comparative apartments owned by other landlords.</p>\n<p>f) Sales grading agreement</p>\n<p>In the new data entry function for graduated rents, you can set the conditions based on the existing data about the number of gradings and the interval between the gradings. </p>\n<p>g) Contract measurements, distribution according to equivalence numbers</p>\n<p>You can define time-dependent measurement amounts for contracts, and you can then use these measurement amounts as calculation base for the condition amount.  Generally you specify these amounts for a specific object (real estate object, order, WBS element or cost center).  You can use the defined amount to calculate condition amounts or to distribute the condition amount that you want to post to the objects (distribution according to the equivalence number).</p>\n<p>For service charge settlement, contract measurements are significant if </p>\n<p>- for the apportionment method, you have to use a measurement type that depends usually on the contract (number of persons, for example).  You usually define these measurement amounts in the contract only, and not for the rental object.</p>\n<p>- for example, if the actual measurement amount for the rental object does not match the (historical) amount in the contract.  In this case, the service charge settlement ignores the measurement amount for the rental object and instead uses the measurement amount defined in the contract.  With a new rental, do not define this measurement amount in the contract so that the system uses the \"correct\" measurement amount for the rental object. </p>\n<p>h) Definition of sets (summary of objects according to criteria required)</p>\n<p>You can combine master data into sets. Sets are groups of objects that you want to reference in reporting and in CO (for example, when you apportion and plan costs).  Therefore, for example, you can define all objects that are relevant for you under a certain aspect once, and access them under this name in most evaluation programs and processing programs.  On the one hand, you can define sets in the RE Navigator, on the other hand, you can also use special reports, which you can schedule in the background so that the sets are automatically updated on a regular basis.</p>\n<p>i) Enhanced assignment of meters for the consumption-dependent service charge settlement</p>\n<p>As of SAP ERP 2004, you have additional options to assign meters:</p>\n<p>- Now you can also assign meters to settlement units and to master settlement units.</p>\n<p>- You can also assign meters to pooled spaces.  They are then distributed to the assigned rental spaces. </p>\n<p>j) Current occupancy principle for service charge settlement (country-specific requirement in Austria)</p>\n<p>As of SAP ERP 2004, you have the option of fully apportioning the service charges of a settlement period to the current tenant regardless of whether or not this tenant used the area in the period concerned.  This apportionment method applies to Austria.  For more information, see SAP Note 890267.</p>\n<p>k) Interfaces for posting in SAP Financial Accounting</p>\n<p>Up to now, essential functions of SAP Financial Accounting were not defined specifically for Real Estate Management.  As of SAP ERP 2004, the number of the real estate contract is included in the incoming payment transaction.  In addition, a new function is available for displaying the tenant account.</p>\n<p>l) Installment agreement and deferral</p>\n<p>To map an installment agreement, you can subsequently split an open item in Financial Accounting into partial amounts.</p>\n<p>m) Enhancement of the integration for SAP Plant Maintenance (PM/CS)</p>\n<p>In Release 1.10, you have the option of assigning functional locations from PM to the real estate objects of the usage view and architectural view. As of SAP ERP 2004, you have the following additional options:</p>\n<p> - You can create malfunction reports for SAP Plant Maintenance directly from real estate objects.</p>\n<p>- From the real estate objects, you can display messages that have been entered and the resulting maintenance orders.</p>\n<p>- You can use a reporting enhanced with data from SAP Plant Maintenance in SAP Business Information Warehouse (BW).</p>\n<p>n) Enhanced integration with SAP Controlling (CO)</p>\n<p>As of SAP ERP 2004, you have additional options for using the integration with SAP Controlling:</p>\n<p>- The apportionment functions of CO are available for real estate objects.</p>\n<p>- You can transfer measurements of real estate objects or real estate contracts to CO as statistical key figures.</p>\n<p>o) Integration with SAP Asset Accounting and SAP Project Management</p>\n<p>You can link the RE-FX usage objects to assets from SAP Asset Accounting or to the WBS elements from the SAP Project System.  You can evaluate these master data links in BW Reporting. </p>\n<p>p) Standard evaluations in reporting</p>\n<p>In reporting, the following evaluations are now also available:</p>\n<p>- CO line items</p>\n<p>- Security deposit agreements with actual security deposits </p>\n<p>- Notice and renewal </p>\n<p>q) Enhancements in BW Reporting</p>\n<p>As of SAP ERP 2004, you have additional evaluation options in BW:  </p>\n<p>- Costs for maintenance orders (SAP Plant Maintenance (PM))  </p>\n<p>- Depreciation for real estate objects (SAP Asset Accounting (FI-AA))  </p>\n<p>- Costs for internal orders of RE objects (SAP Controlling (CO))</p>\n<p>r) Invoice printout</p>\n<p>You can now print the rent invoice from the periodic postings and send it to the tenants.  The invoice document items are grouped together. The system assigns the invoice number on the basis of the RE document.</p>\n<p>s) Quarter days (relevant in UK)</p>\n<p>In the UK and in some other countries it is usual to divide the annual rent into equal partial amounts, which are to be paid for periods of different length.  In the UK, these fixed periods are called quarter days.  For details about this function and the restrictions, see SAP Note 914067. </p>\n<p>t) Archiving</p>\n<p>You can use the standard SAP tools for archiving to archive RE documents and cash flows. A simple deletion function for master data is also available. You can use this function to delete master data that you inadvertently created as long as no dependent structures use these objects.</p>\n<p>3. Functional scope of Flexible Real Estate in SAP ERP 6.0</p>\n<p>a) Migration functions from Classic Real Estate to Flexible Real Estate</p>\n<p>As of SAP ERP 6.0, tools are available to migrate your data from Classic Real Estate to Flexible Real Estate automatically to a large extent. </p>\n<p>b) Adjustment measure</p>\n<p>The adjustment measure provides the option of defining new rents or rent increases for any rental objects and to perform an adjustment based on your definitions.</p>\n<p>In particular, the adjustment measure permits adjustments of conditions due to the following: </p>\n<p>- Modernization measures  </p>\n<p>- Expert opinions  </p>\n<p>- Special assessments (Condominium Owners Association (COA))  </p>\n<p>You can use the adjustment measure if you specified the rent for certain objects according to defined procedures and you want to announce and perform an adjustment to this rent.  You can limit the rent adjustment by a voluntary rent waiver. To do this, define capping provisions and limits.  If the limits are exceeded, the system creates a <strong>waiver condition</strong> and assigns it to the rental objects or to the contracts.</p>\n<p>c) Real estate search</p>\n<p>The real estate search allows you to adjust rental requests with vacant objects and vice versa.  From the rental request, you can create lease abstracts, contract offers and lease-outs.  You can use the following individual functions: </p>\n<p>- You can create <strong>RE search requests</strong>.</p>\n<p>- You can transfer rental objects to <strong>offered objects</strong>.</p>\n<p>- You can search for a suitable offered object for an RE search request.</p>\n<p>- You can search for a suitable RE search request for an offered object.</p>\n<p>- You can print out a lease abstract.  </p>\n<p>- You can create and edit a <strong>contract offer</strong> for a rental object.</p>\n<p>- You can create a real estate contract from the contract offer.</p>\n<p>d) Input tax treatment of CO objects</p>\n<p>You can now also perform the input tax distribution for real estate-related costs that are not assigned directly to the real estate object, but to other objects.  These objects (functional location, WBS elements, CO orders, and so on) can be linked directly or indirectly to real estate objects.  For example, maintenance orders are generally linked using a functional location assigned to the real estate object.  You can assign internal orders and projects directly to the real estate object.  The rules that apply to the real estate objects also apply to the input tax distribution of such real estate-related costs.  For the assigned real estate object, define an option rate method that you can use to determine the option rates.</p>\n<p>e) One-time postings</p>\n<p>It is easier to use one-time postings than the interface of Financial Accounting (FI) to enter documents, and this method is adjusted to suit the requirements of Real Estate Management.  Therefore, the system hides the complexity of the posting process for the end user.</p>\n<p>For one-time postings, you must first define <strong>posting activities</strong> in Customizing. In the application, the person responsible specifies the posting activity and the company code.  Using the settings in Customizing for the posting activity, the system creates one or more documents that the person responsible then completes, for example by entering the invoiced amount or the actual real estate object.  You can use the one-time postings, for example, for the following postings:</p>\n<p>- Posting vendor invoices with reference to real estate objects  </p>\n<p>- Posting one-time receivables for customer contract partners  </p>\n<p>- Posting costs of the COA  </p>\n<p>These are the postings contained in the standard system.</p>\n<p>f) Tax summarization for FI documents</p>\n<p>You now have the option of summarizing the tax items within FI documents.</p>\n<p>This is a legal requirement in different countries (including Austria and Italy).</p>\n<p>The system summarizes line items according to the following criteria:</p>\n<p>- Underlying tax code  </p>\n<p>- Tax category of the account (G/L account or reconciliation account)  </p>\n<p>- Tax jurisdiction key (if this exists) </p>\n<p>- Account type, gross indicator and net indicator of the condition  </p>\n<p>- Currency  </p>\n<p>The system creates a summation tax line for each of these summarized lines.  In particular, tax items on G/L account lines and on subledger account lines are displayed separately. </p>\n<p>g) FI-CA integration (with restricted release)</p>\n<p>By integrating FI-CA, PS-CD customers (contract accounts receivable and payable for public sector) have the option of using their open item account accounting for real estate contracts also. If you use this integration, the system posts the postings (that you usually perform using the FI accounts receivable accounting (FI-AR)) to contract accounts in PS-CD. In this case, a contract partner can have a separate account for each real estate contract.</p>\n<p>Prerequisites:</p>\n<p>- You activated the Public Services business function set (EA-PS).</p>\n<p>- An activation for each company code is possible.</p>\n<p>Note that we have not yet released this function for use.  We will not release this function until after a current pilot project has been completed.</p>\n<p>h) Surcharges for the service charge settlement</p>\n<p>You can now calculate surcharges in the service charge settlement and add these to the settlement result.</p>\n<p>This procedure is in particular useful in the following cases:</p>\n<p>- Apportionment loss risk (price-controlled living space in Germany) </p>\n<p>- Management costs surcharge (country specifications for Switzerland) </p>\n<p>This is calculated for all rental objects.  The amount of the surcharge varies depending on whether it is an internal or external contract.</p>\n<p>i) Enhanced integration with SAP Controlling (CO)</p>\n<p>The indirect activity allocation provides the option of automatic activity allocation (similar to the apportionment) on the basis of actual values, statistical key figures, fixed portions, and so on. </p>\n<p>In addition, you can now indirectly determine the activity quantity of a sender from the activity inputs of the receivers. </p>\n<p>i) Enhancements in reporting</p>\n<p>In the SAP information system, the following additional reports are available:</p>\n<p>- Line item vendor, accruals and deferrals, cash flow  </p>\n<p>- Evaluations for the land use management  </p>\n<p>- CO reports </p>\n<p>By means of SAP query, you can use CO reports for costs and revenues and for statistical key figures for the first time in the Online Transaction Processing system (OLTP).  (Previously, these reports were available in SAP BW only.)</p>\n<p>k) Correspondence</p>\n<p>As of SAP ERP 6.0, you can use PDF-based forms in Flexible Real Estate Management. PDF-based forms are provided with the Adobe solution \"Interactive Forms\". You can use them as an alternative to SAP Smart Forms. Therefore, the SAP Smart Form sample forms delivered in SAP ERP 2004 are also available in SAP ERP 6.0 as PDF-based forms. In addition, the following new correspondence cases are contained in SAP ERP 6.0:</p>\n<p>- Lease abstract and contract offer in Real Estate Search</p>\n<p>- Master data summaries for land use management </p>\n<p>- COA settlement, annual budget and condominium ownership settlement</p>\n<p>If you use individual Smart Forms that already exist, you can continue to use these without any changes.  However, keep in mind that as of SAP ERP 6.0, we no longer develop SAP Smart Form sample forms that were previously delivered in Flexible Real Estate Management and no longer deliver the new correspondence cases specified above as SAP Smart Forms.  However, you can still develop your own forms using SAP Smart Forms.</p>\n<p>In RE-FX, you can set whether you want each form to be a Smart Form or a PDF-based form. If you went live before SAP ERP 6.0, for example, you can continue to use Smart Forms for the previously used forms and use PDF-based forms only for new correspondence cases.</p>\n<p>Keep in mind that Smart Forms and PDF-based forms use completely different technology. To decide which technology you would like to use, familiarize yourself with the special features of the two tools.  For information about restrictions on the use of PDF technology, see SAP Notes 766410, 894389 and 1009567 (and related SAP Notes).</p>\n<p>Refer especially to the section \"High-volume printing\" in SAP Note 894389. This section explains that SAP Interactive Forms by Adobe is not released for productive mass printing scenarios because of performance problems with some print forms that were delivered with SAP ERP 6.0. Therefore, if you have correspondence cases such as the service charge settlement for which mass documents were created at a particular time, you must first evaluate whether you can use PDF-based forms.</p>\n<p>l) Separation of assets, third party management, management of condominium owners' associations (COA) (released for Germany only)</p>\n<p>Using third party management, you can manage real estate of third party owners as of SAP ERP 6.0.  As a legal basis, the owner must create a mandate.  The system displays each mandate and the dependent real estate objects and real estate processes in a separate company code.  In this way, the mandate forms the basis of the separation of assets and the independent settlement.  The COA mandate is a special form of the mandate.</p>\n<p>For COA management, you can use the following functions:</p>\n<p>- For COA management, you can create annual budgets, manage assessment contracts, and perform accounting and COA settlements. </p>\n<p>- For the management of contract relationships between owner and tenant (individual condominium management), you can manage lease-outs and perform accounting and service charge settlement for the tenants.</p>\n<p>m) Land Use Management, management of parcels, tracts of land and local subdistricts (released only for Germany)</p>\n<p>You can now manage master data and processes from the area of Land Use Management. This includes: </p>\n<p>- Land register pages from the land register, including ownership relationships, types of possession and associated rights, charges, restrictions and liens</p>\n<p>- Parcels from the real estate cadaster</p>\n<p>- Entries from other public registers such as development plans, easements, contaminations of sites and so on</p>\n<p>- Contract management using changes of holdings such as purchase and land lease</p>\n<p>- Contract management for the holdings usage such as rent and leasehold</p>\n<p>Land Use Management is integrated with the following components:</p>\n<p>- Asset Accounting (FI-AA) for the valuation of land for accounting purposes</p>\n<p>- Financial Accounting (FI) and Controlling (CO) for the posting of property taxes and fees and contracts</p>\n<p>- Plant Maintenance (PM/CS) for the management of technical activities </p>\n<p>Note that this function was defined according to German law only. Therefore, it is released for use in this jurisdiction only.  There is no plan to release this function for other countries.</p>\n<p>n) Data Retention Tool for RE-FX</p>\n<p>Income tax laws require you to retain certain financial documents for the tax return in sequential file format. As of SAP ERP 6.0, the Data Retention Tool (DART) supports this requirement in the area of RE-FX.  Using this tool, you can extract master data and flow data to sequential files and display them.  By exporting the data, you can perform an evaluation using programs of third party suppliers.</p>\n<p>o) Connection to regulatory reporting for insurance companies (BaFin - German Federal Financial Supervisory Authority - connection)</p>\n<p><br/>As of Support Package 6 and higher for SAP ERP 6.0, the connection to regulatory reporting for insurance companies (BAFin connection) is available according to German requirements.</p>\n<p>p) Extended withholding tax</p>\n<p>Extended withholding tax is available for Spain and Portugal. For more information, see SAP Note 771098.</p>\n<p>q) Input tax correction according to German sales/purchase tax law (UStG § 15a) (correction items)</p>\n<p>The function is generally available in the standard system as of November 2008 for Release ERP 6.0 and for all related Enhancement Packages. For details about the delivery, see SAP Note 1240658.</p>\n<p>Note that this solution only complies with German statutory requirements.</p>\n<p>According to German sales/purchase tax law (§15a UStG), the original input tax deduction must be corrected within the specified timeframe, in case the principles for the assumed input tax deduction opportunity are changed following an input tax deduction.</p>\n<p>The input tax correction is performed for the first time after the start of the actual usage of the fixed asset. The input tax is corrected depending on the changes in the option rate. This means that over a period of up to ten years, the pro rata difference is posted between the actual input tax deducted and the input tax that is liable for deduction according to the current option rate.</p>\n<p>Note that this solution only complies with German statutory requirements.</p>\n<p>r) Archiving enhancements</p>\n<p>You can now archive master data.</p>\n<p>4. Function scope of Flexible Real Estate in SAP Enhancement Package 2 for SAP ERP 6.0</p>\n<p>a) Long-term seating arrangements</p>\n<p>You can enter and plan long-term usage of rooms and other real estate objects by persons (for example, employees and contractors). You can settle the usage costs internally using Customizing or externally using the real estate contract.</p>\n<p>Note that no reversal function exists yet for the cost allocation in Enhancement Package 2.</p>\n<p>b) Move planning</p>\n<p>You can use this function to plan and activate moves of persons who reserve reservation objects on a long-term basis. You can also plan move-ins and move-outs of persons for whom no long-term arrangements have been made yet or who do no longer require a long-term arrangement. In addition, you can delegate moves of specific persons to other assignment planners.</p>\n<p>c) Room reservation</p>\n<p>You can use this process to make short-term reservations for rooms or other real estate objects or to reserve the rooms for single appointments or recurring appointments. In addition, you can place an order for additional services; for example, you can order lunches, additional chairs or equipment and settle the incurred costs accordingly. The cost allocation can be used for monitoring the costs.</p>\n<p>d) Master data: Graphical integration</p>\n<p>In addition to BAPIs, you can now use interfaces to integrate the display of graphics software (CAD, GIS).</p>\n<p>e) Rent adjustment according to cost efficiency analysis</p>\n<p>The rent adjustment according to cost efficiency analysis (CEA) is used to determine the cost rent for publicly funded accommodations. In this process, capital costs, current expenses and financing plans are taken into account. In addition, you can specify that the system considers specific amenities level factors (for example, advantages and disadvantages with regard to the location) for the cost efficiency analysis.</p>\n<p>f) Conditions in foreign currency</p>\n<p>You can define conditions for contracts, rental objects or contract offers that differ from the local currency of the company code. Depending on the translation rules and exchange rates, the system automatically carries out a translation within the different processes (for example, cash flow transaction, periodic postings, service charge settlement, and so on).</p>\n<p>g) Condition split</p>\n<p>You can distribute condition-based payments to several partners with a vendor account or partners with a customer account (for example, landlord, tenant and subsidizer). In particular, these requirements apply for the USA (key word: \"multiple vendors\").</p>\n<p>h) Enhancements for one-time conditions</p>\n<p>You can define a validity period for one-time conditions and manually enter a due date that is different from the frequency term. The validity period can also be used to carry out accruals and deferrals for one-time conditions.</p>\n<p>i) Posting of sales-based settlement using cash flow</p>\n<p>In the sales-based rent agreements of the real estate contract, you can specify that the receivables from the sales-based rent are also transferred to the cash flow and therefore posted using periodic postings.</p>\n<p>j) Accrual/deferral of service charges costs that have not been settled yet (unfinished services)</p>\n<p>This function to allows you to enter accruals/deferrals for the balance sheet of service charges that have not been settled yet. In addition to determining the amounts to be accrued, you can correct and post the amounts and dissolve the accruals/deferrals that were carried out.</p>\n<p>k) Contract: Different posting term for service charge settlement (changed)</p>\n<p>Unlike the posting rules of your service charge advance payments, this function allows you to use your own payment methods, dunning data, account determinations, and so on, when posting the results of a service charge settlement.</p>\n<p>l) Enhancements of CO integration for Funds Management (FM)</p>\n<p>When you transfer plan cash flows to cost element planning, the FM account assignments can now be applied. Using the relevant FM function, you can transfer the planned values from CO to the relevant FM account assignments.</p>\n<p>m) Integration to cash management and forecast</p>\n<p>Cash management and forecast provides a liquidity forecast of the receivables and payables resulting from real estate contracts. This forecast displays the planned items resulting from the cash flow. During periodic postings in RE-FX, these items are transferred to cash management and forecast.</p>\n<p>n) Accrual/deferral of tax amounts</p>\n<p>In addition to the net amounts, you can also accrue the tax amounts of conditions. Accrued amounts can be posted directly to tax accounts or clearing accounts.</p>\n<p>o) Info system: Item overview for several contracts</p>\n<p>Using the new item overview report, you can report open and cleared items for several customer contracts.</p>\n<p>p) Other enhancements of the real estate contract</p>\n<p>- Multiple assignment of objects: When you process real estate contracts, you can assign a rental object to several object groups so that the periods overlap.</p>\n<p>- Locking critical data for backdated changes: You can lock fields of the contract processing to avoid any backdated cash flow-relevant changes that would lead to subsequent receivables or credit memos.</p>\n<p>q) Country specifications for Austria: VAT calculation for condominium owners' associations (COA)</p>\n<p>This enhancement allows you to calculate the sales tax for expenses from the maintenance reserve of rental objects of a COA. You can pass on the amounts to the individual owners.</p>\n<p>5. Function scope of Flexible Real Estate in SAP Enhancement Package 3 for SAP ERP 6.0</p>\n<p>The Real Estate functions delivered with SAP Enhancement Package 3 for SAP ERP 6.0 contain enhancements that were implemented within the integration to Funds Management.</p>\n<p>a) Funds reservation with approval workflow</p>\n<p>You can use this approval workflow to display an approval procedure for funds that are fixed over a longer period of time using lease-ins.</p>\n<p>b) Creating plan values with regard to Funds Management (FM) account assignments from contracts</p>\n<p>The existing function for creating CO plan data from the cash flow of real estate contracts were enhanced so that FM account assignments (funds, grants and functional area) are transferred to CO plan data.</p>\n<p>c) Simulation of FM account assignment derivation</p>\n<p>In the overview for the contract, a new report is available that displays the simulated derivations of FM account assignments.</p>\n<p>6. Function scope of Flexible Real Estate in SAP Enhancement Package 4 for SAP ERP 6.0</p>\n<p>The Real Estate functions delivered with SAP Enhancement Package 4 for SAP ERP 6.0 are grouped into Commercial and Corporate Real Estate Management, which can be activated using the business functions RE_GEN_CI_2 and RE_CRE_MISC.</p>\n<p>Within Commercial Real Estate Management, the following enhancements have been made:</p>\n<p>a) Enhancements to land use management (country-specific)</p>\n<p>- New \"Function\" field in the parcel</p>\n<p>This field allows you - in the same way as the function for usage objects - to implement an additional attribute assignment of parcels with custom characteristics.</p>\n<p>- Simplified key for parcel</p>\n<p>An option is provided to use a simplified key for parcels that is not subject to cadastral classifications.</p>\n<p>- New reports</p>\n<p>Various new reports are available.</p>\n<p>b) Enhancements to the rent adjustment according to the cost efficiency analysis</p>\n<p>- Change management of expense items</p>\n<p>A transaction is provided for the mass change of current expenses of adjustment measures for the cost efficiency analysis.</p>\n<p>- Reporting</p>\n<p>Various reports are available so that you can execute general evaluations for several cost efficiency analyses.</p>\n<p>c) Enhancements to contract and conditions</p>\n<p>- Possession dates for rental objects </p>\n<p>In addition to the runtime definition of contracts and contract offers, you can define possession dates for contract objects. The possession dates define the period in which the object is released for usage. The start possession date may be before the contract start; the end possession date may be after the contract start.</p>\n<p>- Requested notice date</p>\n<p>If notice is given for contracts, you can enter a requested notice date that is before the expiration of the regular period of notice. This date is also taken into account in the real estate search.</p>\n<p>d) Enhancements to the service charge settlement</p>\n<p>- Rounding of receivable amount or credit balance amount</p>\n<p>For service charge settlements for which receivables and credit balances from advance payments are posted in an amount, you can define whether and how the result is to be rounded.</p>\n<p>- New status monitor for settlement periods of settlement units</p>\n<p>- Cross-settlement evaluations for service charge settlement</p>\n<p>e) Business Intelligence</p>\n<p>During the extraction of master data and flow data from flexible Real Estate Management to Business Intelligence, the system transfers additional master data attributes for usage objects (for example, the reason for vacancy of a rental object). New DataSources are also available for extracting data in Business Intelligence.</p>\n<p>f) Other enhancements</p>\n<p>- Periodic postings: Selection according to condition type and condition purpose</p>\n<p>- Evaluations for sales-based settlement</p>\n<p>The display has been adjusted to the general output of evaluations in RE-FX and therefore provides flexible enhancement options and options for the time slot method.</p>\n<p>- Reporting: Option rate data for usage objects</p>\n<p>A report is available for evaluating option rate relationships for real estate objects. This data can also be supplied for evaluations according to the German Principles of Data Access and Verifiability (GDPdU).</p>\n<p>- BAdI for option rate determination</p>\n<p>- Input tax correction (country-specific)</p>\n<p>If you change the principles for the originally assumed input tax deduction option after an input tax deduction, you can adjust the deducted value using the input tax correction. The input tax correction is performed at the earliest after the actual usage of the fixed asset and is dependent on the option rate change.</p>\n<p>- Filter in management of time slots</p>\n<p>In a report, regardless of the original selection, you can take different time intervals into account without starting the report again.</p>\n<p>- RE Navigator</p>\n<p>For a real estate object, the system now displays the existing registrations, and a copy function using drag and drop is available in the navigation bar.</p>\n<p>Within Corporate Real Estate Management, the following enhancements have been made:</p>\n<p>g) Degrees of occupancy for continuous occupancy</p>\n<p>For persons who occupy more or less than one location continuously (for example, part-time employees), you can specify a degree of occupancy for continuous occupancies. The degree of occupancy directly affects the utilization of reservation objects and is taken into account during the price calculation. If persons occupy more than one location, the BAdI BADI_REOR_PO_OCCQUOTE must be implemented.</p>\n<p>h) Occupancy evaluation and vacancy evaluation</p>\n<p>You can use this report to execute detailed evaluations for continuous occupancy. You can display an overview of the current and historical occupancy, the utilization of capacities and the existing vacancies.</p>\n<p>i) Different move dates</p>\n<p>In the case of a move, you can define individual move dates for all persons moving. This means that you no longer have to create a move plan for each move-out date or move-in date.</p>\n<p>j) Postings for continuous occupancies and reservations</p>\n<p>You can now use two separate transactions for the posting of reservations and continuous occupancies. In particular, transactions are provided for reversing costs of continuous occupancies and reservations. The new transactions can be called simultaneously for several reservation objects.</p>\n<p>7. Functional scope of Flexible Real Estate in SAP Enhancement Package 5 for SAP ERP 6.0</p>\n<p>a) Tax transfer in the service charge settlement</p>\n<p>For input tax opting tenants, in the service charge settlement, you can pass on the exact amount of input tax from the original document as value-added tax (VAT) - for example, full VAT rate for services, reduced rate for energy or none for property taxes and fees - proportionally in the service charge settlement.</p>\n<p>b) Surcharges based on contract conditions in the service charge settlement</p>\n<p>In the service charge settlement, you can levy surcharges that are determined as a percentage of any contract conditions you choose.</p>\n<p>c) Heating days for consumption-independent costs in the service charge settlement</p>\n<p>In the service charge settlement, you can now also distribute consumption-independent costs, such as those for the maintenance of heating, in a weighted manner according to heating days in the case of a tenant changeover.</p>\n<p>d) Direct posting from the real estate contract</p>\n<p>You can now create periodic postings for the current contract directly from the real estate contract in order to ensure prompt correction of the open items in the case of contract changes.</p>\n<p>e) Integrated posting</p>\n<p>To reduce the volume of documents, you can now group postings from the partner-related cash flow and from the object cash flow in one document.</p>\n<p>f) Accruals and deferrals by freely definable posting periods</p>\n<p>Accruals and deferrals were previously always performed on the basis of calendar months. You now have the option of accruing and deferring by posting periods that do not correspond to calendar months. This enhancement is available with SAP Note 1428133 for ERP 6.0 and higher and all available enhancement packages.</p>\n<p>g) Time-dependent assignment of rental objects to COA objects in the third-party management</p>\n<p>You can now make time-dependent assignments of rental objects from object mandates to COA objects. This enables you to assign more than one object from the manager company code (or from an object mandate) and from the condominium owner company code respectively in the case of rental objects. SAP Note 1396584 already provides this enhancement for Enhancement Package 4 for SAP ERP 6.0.</p>\n<p>h) Defining capping rules at the start of the rent adjustment</p>\n<p>In addition to the procedure-dependent capping rules, you can also define individual capping rules at the start of the adjustment run.</p>\n<p>8. Functional scope of flexible Real Estate in SAP Enhancement Package 6 for SAP ERP 6.0</p>\n<p>a) Integrated planning for real estate objects</p>\n<p>Planned activities for real estate objects can now be transferred directly to cost center planning as scheduled activities. In the process, the plan value is calculated from the activity type and the rate defined for the cost center.</p>\n<p>b) Indexed sales grading</p>\n<p>Sales grading in the sales-based rent agreement can now be coupled with an index rule and adjusted as part of the index-linked rent adjustment.</p>\n<p>c) SEPA mandates</p>\n<p>The SAP module for processing SEPA debit memos and SEPA bank transfers within the Single Euro Payments Area (SEPA) has now been enhanced for Real Estate requests.</p>\n<p>The following enhancements have been made for customer activities, in particular:</p>\n<ul>\n<ul>\n<li>Managing the SEPA mandate in the posting term in the contract</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Integrating the SEPA mandate into the payment data for a one-time posting</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Enhancing the bank details for the SAP business partner</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Storing the SEPA mandate in the RE document</li>\n</ul>\n</ul>\n<p>For more detailed information about this, see SAP Note 1678321.</p>\n<p>d) Enhancements for one-time postings (RERAOP)</p>\n<p>Postings with net amounts and statistical account assignments are now also possible for one-time postings.</p>\n<p>e) Contract-independent option rate</p>\n<p>Currently, all objects rented in a contract are subject to the same option rate. This means that the option rate of a rental object is basically determined depending on the occupancy contract.</p>\n<p>Since it can be useful in some cases not to pass on the option rate of the contract to the object, you can now control if the occupancy contract is not to be taken into account during the determination of the option rate of the rental object.</p>\n<p>For more detailed information about this, see SAP Note 1596008.</p>\n<p>f) Service charge group in reports and search helps for service charge settlement (SCS)</p>\n<p>The service charge group has been added to the reports for the SCS. In addition, the service charge group has also been integrated into the search help for the master data for the SCS.</p>\n<p>For more detailed information about this, see SAP Note 1529396.</p>\n<p>g) Implementation of the \"Date Until Which the Cash Flow Is Locked\" field</p>\n<p>By setting this date, you can prevent planned records from originating for past periods during cash flow generation.</p>\n<p>For more detailed information, see SAP Note 1635539.</p>\n<p>h) Performance improvements</p>\n<p>For the following time-intensive processes, parallel processing has been enabled:</p>\n<ul>\n<ul>\n<li>Periodic Posting: Contracts (RERAPP)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Reverse Contract Postings (RERAPPRV)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Periodic Posting: Rental Objects (Vacancies) (RERAVP)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Reverse Vacancy Postings (RERAVPRV)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Input Tax Distribution (REITDS)</li>\n</ul>\n</ul>\n<p>i) SAP Business Information Warehouse (BW)</p>\n<p>Migration of data flow under 3.x to SAP BW 7.0 technology</p>\n<p>j) Localization for France</p>\n<p>For more detailed information about this, see SAP Note 1561158.</p>\n<p>k) SAP Common Area Maintenance Expense Recovery (CAM)</p>\n<p>Mapping the service charge settlement according to the USA standard</p>\n<p>There is now an add-on solution for this. For more detailed information about this, see SAP Note 1610085.</p>\n<p>9. Functions that are not included</p>\n<p>In SAP ERP 6.0 and SAP Enhancement Packages for SAP ERP 6.0, the functions listed below are not provided in RE-FX. This list is not final. If functions are missing from this list, you cannot assume that they are contained in the delivery. This list does not mean that these functions will be available in subsequent releases.</p>\n<p>a) Rental forecast</p>\n<p>b) Country-specific processing</p>\n<p>Country-specific legal requirements in real estate law - if they are not mentioned individually.  For information about country-specific releases, languages and localization, see SAP Note 771098.</p>\n<p>c) Proportional clearing of split advance payments in the service charge settlement</p>\n<p>This is the pro rata distribution of the amount paid in advance if the calculation period of the advance payment exceeds a settlement period.</p>\n<p>d) Terms of use for multiple usage</p>\n<p>e) Completion of the PS-CD connection</p>\n<p>f) Mapping of the official or public law view of Real Estate Management, including the management of tasks of the survey office and land registry and the calculation of property taxes and other taxes and fees such as agricultural subsidies and so on.</p>\n<p>g)<strong></strong> Subsequent implementation of a further accounting principle. Note that implementation of a further (subsequent) accounting principle (as a separate ledger) is currently not supported for RE-FX using the <em>\"Subsequent Implementation of a Further Accounting Principle\"</em> tool. For more information, see the following documentation: <a href=\"https://help.sap.com/doc/57c2fc57bde70f70e10000000a44147b/3.6/en-US/frameset.htm\" target=\"_blank\">https://help.sap.com/doc/57c2fc57bde70f70e10000000a44147b/3.6/en-US/frameset.htm#</a></p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 82}]}, {"note": "2495864", "noteTitle": "2495864 - <PERSON><PERSON><PERSON> CML - Pre-transition check for RE Classic", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You intend to convert your ERP system to SAP S/4HANA.</p>\n<p>FS-CML Consumer and Mortgage Loans in the context of SAP S/4HANA: The CML-specific functions with regard to collaterals and collateral objects are <span>no longer</span> available. This is due to the entry with the subject \"Real Estate Classic\" in the \"Simplification List for SAP S/4HANA, on-premise edition 1511\". At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC</p>\n<p>S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions or import the specified Support Package.</p>\n<p>This note provides class CL_FVD_S4TC_CHK, that is called from the class CLS4H_CHECKS_RE_CLASSIC created by the note <a href=\"/notes/2339534\" target=\"_blank\">2339534</a>.\r\n<script language=\"JavaScript\" src=\"https://i7p.wdf.sap.corp/sap/public/bc/ur/sap_secu.js\" type=\"text/javascript\"></script>\n</p>\n<p>The class CL_FVD_S4TC_CHK checks, if CML is still using RE-Classic data.</p>", "noteVersion": 5, "refer_note": [{"note": "2339534", "noteTitle": "2339534 - <PERSON><PERSON><PERSON> SAP_APPL – Pre-transition check for Classic RE", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your ERP system to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC<br/>S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note. The checks are executed during the transition process.</p>\n<p>See SAP Note 2270550.</p>", "noteVersion": 5}]}, {"note": "2805619", "noteTitle": "2805619 - Release information for \"SAP for Banking\" in connection with \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\")", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning to use \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\") in connection with applications or solutions from the \"SAP for Banking\" area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With regard to the release and the restrictions/limitations, the statements already made in SAP Note 2211665 also apply for \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\").</p>\n<p>In addition, the following information/restrictions/limitations are to be taken into account:</p>\n<ul>\n<li>FS-CML Consumer and Mortgage Loans: There is no integration with the application \"Central Payment for SAP Central Finance\", that is, the function provided by the application \"Central Payment for SAP Central Finance\" is <span>not</span> available in connection with FS-CML.<br/>Also see SAP Note 2346233 at this point.</li>\n<li>The application \"FS-CYT Capital Yield Tax Management\" is a separate software component (that is, it is not a part of the \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\") delivery).<br/>Note that all FS-CYT releases up to and including \"SAP Capital Yield Tax Management for Banking 8.0\" <span>must not</span> be used as add-ons to \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\").<br/>Instead, the new solution \"SAP Capital Yield Tax Management for Banking for SAP S/4HANA\" is to be used. Regarding availability and prerequisites for use, refer to the documentation of this solution.</li>\n<li>As of Release 8.0 and Support Package 08 (or as of Release 9.0 and Support Package 07), the application \"SAP Payment Engine\" (FS-PE Payment Engine) is released for integration with \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\").</li>\n<li>FS-CML Consumer and Mortgage Loans: The CML-specific functions with regard to collaterals and collateral objects are <span>no longer</span> available.<br/>At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.<br/>Also see SAP Note 2369934 at this point.</li>\n<li>FS-TXS SAP Funding Management: SAP Funding Management (FS-TXS) is <span>not</span> released for use in \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\").<br/>Comment: A separate installation of SAP Funding Management in combination with a remote communication to FS-CML is permitted as of SAP Funding Management 3.0 Support Package 09.</li>\n<li>All SEM Banking components (for example: IS-B-DP transaction data pool, IS-B-PA Profit Analyzer, IS-B-RA Risk Analyzer, IS-B-SA Strategy Analyzer), are <span>not</span> released for the use in \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\").<br/>Also see SAP Note 2270318 at this point.</li>\n<li>SAP Leasing and the Lease Accounting Engine (CRM-LAM Leasing, FI-LA Lease Accounting Engine) are <span>not</span> released for the use in \"SAP S/4HANA 1909\" (\"SAP S/4HANA, on-premise edition 1909\").</li>\n<li>FS-BP business partner: The fields XUBNAME (\"User Name in User Master Record\") and PERS_NR (\"Personnel Number\") of the database table BP001 are no longer available. For more information, see SAP Note 2900607.</li>\n</ul>", "noteVersion": 2, "refer_note": [{"note": "2799003", "noteTitle": "2799003 - SAP S/4HANA 1909: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note informs you about all restrictions in this release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restrictions, SAP S/4HANA 1909, SAP S/4HANA 1909 FPS01, SAP S/4HANA 1909 FPS02</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides information about the restrictions that exist for the SAP S/4HANA 1909 release.</p>\n<p><strong><strong>Note</strong>:</strong> This SAP Note is subject to change. Check this SAP Note for changes on a regular basis. All important changes are documented in section \"Important Changes after Release of SAP S/4HANA 1909\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>General Restrictions:</strong></p>\n<ul>\n<li>In SAP S/4HANA the maximum length of the material number has been extended to 40 characters. Long material number-related restrictions are described in SAP Note <a href=\"/notes/2233100\" target=\"_blank\">2233100</a>.</li>\n<li>In SAP S/4HANA 1909 currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. Related restrictions are described in SAP Note <a href=\"/notes/2601956\" target=\"_blank\">2601956</a>.</li>\n<li>The extensibility framework is currently not integrated in the <em>privacy by default</em> functionality of S/4HANA. Do not use this functionality for the processing of personal data if data protection legislation is applicable.</li>\n<li>The released information for add-ons can be found in SAP Note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</li>\n<li>The attached Business Functions are not released with SAP S/4HANA 1909.       </li>\n<ul>\n<li>It is not permitted to switch them on, even if it is technically possible (see <a href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001120422019&amp;iv_guid=00109B36D5821ED9B6C57E32CE6E40DC&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334318CF734378A3730B4F28F88F4712A73F5762E31AD34CDCA08CE2BCEB34C71F14A0A094876F7CD8D4CB12834F14EAA34F2D5750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\" target=\"_blank\">attachment</a>). Additional information regarding Always-Off and Always-On Business Functions can be found in SAP Notes <a href=\"/notes/2240359\" target=\"_blank\">2240359</a> and <a href=\"/notes/2240360\" target=\"_blank\">2240360</a>.</li>\n<li>If a Business Function was switched on in the start release system, but defined as always off in SAP S/4HANA, then a system conversion is not yet possible with this release at that current point in time.</li>\n<li>If a Business Function was switched off in the start release system, but defined as always on in the target release, then the Business Function will be automatically activated during the conversion.</li>\n</ul>\n<li>Restrictions for ABAP Platform 1909 as part of SAP S/4HANA 1909 are described in SAP Note <a href=\"/notes/2794219\" target=\"_blank\">2794219</a></li>\n<li>Restrictions for Java components as part of SAP S/4HANA 1909 are described in SAP Note <a href=\"/notes/2794270\" target=\"_blank\">2794270</a></li>\n<li>For CoPilot skills available with S/4HANA 1909 and related restrictions refer to SAP Note <a href=\"/notes/2818565\" target=\"_blank\">2818565</a>.</li>\n<li>Restrictions apply to the usage of the CDS View Replication functionality; for details refer to SAP Note <a href=\"/notes/2769627\" target=\"_blank\">2769627</a>.</li>\n</ul>\n<p><strong>Conversion Restrictions:</strong></p>\n<p>Restrictions for system conversions to SAP S/4HANA 1909:</p>\n<ul>\n<li>The conversion from source releases below SAP ERP 6.0 EHP 5 (SAP_APPL 605) is supported for systems containing vendors with assigned contact but you need to ensure to update to the following minimum SP levels upfront.<br/>In addition, you need to implement corrective SAP Note <a href=\"/notes/2383051\" target=\"_blank\">2383051</a> and further corrections into the system. For this purpose, please get in contact with SAP via customer incident on component AP-MD-BF-SYN.<br/>The minimum SP levels are: </li>\n</ul>\n<ol>\n<ul><ol>\n<li>SAP ERP 6.0 SP22</li>\n<li>EHP2 FOR SAP ERP 6.0 SP10</li>\n<li>EHP3 FOR SAP ERP 6.0 SP09</li>\n<li>EHP4 to EHP8 FOR SAP ERP 6.0 No minimum SP required.</li>\n</ol></ul>\n</ol>\n<p>   This is not relevant for systems which do not have vendors with assigned contacts. You can also verify for existence of records in table KNVK where LIFNR has non-initial values (KNVK-LIFNR &lt;&gt; ‘’).</p>\n<ul>\n<li>Restrictions in conversions for DIMP-LAMA: see SAP Note <a href=\"/notes/2384347\" target=\"_blank\">2384347</a>.</li>\n<li>The following add-ons cannot be converted to SAP S/4HANA 1909 in one step but require an intermediate upgrade step:</li>\n<ul>\n<li>The Israeli Annexing Transport solution (see SAP Note <a href=\"/notes/2298073\" target=\"_blank\">2298073</a> for more details)</li>\n<li>SRM_SERVER 550/SRM_PLUS 550 (see SAP Note <a href=\"/notes/2251946\" target=\"_blank\">2251946</a> for more details)</li>\n<li>SAP Financial Closing cockpit 1.0 (see SAP Note <a href=\"/notes/2513315\" target=\"_blank\">2513315</a> for more details)</li>\n<li>SAP Revenue Accounting and Reporting 1.0, 1.1, 1.2 (see SAP Note <a href=\"/notes/2804474\" target=\"_blank\">2804474</a> for more details)</li>\n</ul>\n<li>For restrictions concerning Cost Of Goods Manufactured (COGM) please see SAP Note <a href=\"/notes/2270414\" target=\"_blank\">2270414</a>.</li>\n<li>For restrictions concerning New Inventory Management Data Model, see SAP Note <a href=\"/notes/2493434\" target=\"_blank\">2493434</a>.</li>\n<li>\n<p>SAP S/4HANA Migration Cockpit: with SAP S/4HANA 1909 FPS01, there is an issue with the consistency of the migration objects that affects the migration approach \"Migrate Data Directly from SAP system\". This means that new projects cannot be created for the following scenarios:</p>\n<ul>\n<li>SAP Apparel and Footwear (SAP AFS) to SAP S/4HANA</li>\n<li>SAP ERP to SAP S/4HANA</li>\n</ul>\n<p>Projects created using SAP S/4HANA 1909 FPS00 are not affected. You can continue to work with such projects. For information about how to resolve this issue, see SAP Note <a href=\"/notes/2819445\" target=\"_blank\">2819445</a>.</p>\n</li>\n</ul>\n<p><strong>Performance Restrictions:</strong></p>\n<ul>\n<li>Performance restrictions for SAP S/4HANA 1909 can be found in SAP Note <a href=\"/notes/2837072\" target=\"_blank\">2837072</a>.</li>\n</ul>\n<p><strong>Core Solution Restrictions:</strong></p>\n<ul>\n<li>In SAP S/4HANA on-premise, Event-Based Revenue Recognition is not available for Sell from Stock scenarios. Refer to SAP Note <a href=\"/notes/2581947\" target=\"_blank\">2581947</a>. The Fiori app \"Revenue Recognition (Event-Based) - Sales Orders\" (F2441) is only available for cloud customers. It  cannot be used, and there is no support in on-premise installations.           </li>\n<li>Only Project Based Services are supported with Event-Based Revenue Recognition, see also SAP Note <a href=\"/notes/2800818\" target=\"_blank\">2800818</a>. </li>\n<li>Restrictions related to Responsibility Management: You as data controller are responsible for ensuring that the data used in responsibility definitions in teams is managed in accordance with any applicable legal requirements or business needs, such as data protection legislation or data life cycle requirements. Responsibility definitions in the Fiori app \"Manage Teams and Responsibilities\" are currently not integrated in privacy by default functionality. Therefore, the responsibility definitions in teams should not be used for the processing of personal data if this processing falls under any applicable data protection legislation.</li>\n<li>Information regarding Fiori application 'Manage Product Master' (Master Data Maintenance – Product Master): in the Fiori application Manage Product Master for valuation and supply planning you must maintain field values of length less than nine for price fields. This is the field length that is supported until the S/4HANA 2020 release. The system will not process the values in these fields in case you specify a value that is nine or more than nine digits.</li>\n<li>\n<div>Further area-specific restrictions for SAP S/4HANA 1909 are described in the following SAP Notes:</div>\n</li>\n</ul>\n<div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"311\">\n<p><strong>Area</strong></p>\n</td>\n<td width=\"123\">\n<p><strong>Restrictions SAP Note(s)</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Master Data Governance</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2816557\" target=\"_blank\">2816557</a><a href=\"/notes/2656693\" target=\"_blank\"><br/></a><a href=\"/notes/2392135\" target=\"_blank\">2392135<br/></a><a href=\"/notes/2816571\" target=\"_blank\">2816571</a></p>\n<p><a href=\"/notes/2893888\" target=\"_blank\">2893888</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>BRIM</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2351374\" target=\"_blank\">2351374</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>SAP Product Costing</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2807519\" target=\"_blank\">2807519</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>LoB Supply Chain - TM<br/>LoB Supply Chain - PP/DS<br/>LoB Supply Chain - EWM</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2813859\" target=\"_blank\">2813859</a><br/><a href=\"/notes/2825650\" target=\"_blank\">2825650</a> <br/><a href=\"/notes/2806070\" target=\"_blank\">2806070</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>advanced Available-to-Promise (aATP)</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2802208\" target=\"_blank\">2802208</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Intrastat</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2815977\" target=\"_blank\">2815977</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Globalization</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2826929\" target=\"_blank\">2826929</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Best Practices Content Framework</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2801944\" target=\"_blank\">2801944</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Migration Cockpit</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2925705\" target=\"_blank\">2925705</a></p>\n</td>\n</tr>\n<tr>\n<td width=\"311\">\n<p>Predictive Accounting</p>\n</td>\n<td width=\"123\">\n<p><a href=\"/notes/2931453\" target=\"_blank\">2931453</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</div>\n<div></div>\n<div>\n<p><strong>Industry Solution Restrictions:</strong></p>\n</div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p><strong>Industry</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><strong>Restrictions <br/>SAP Note</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Aerospace &amp; Defense (A&amp;D)</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2825852\" target=\"_blank\">2825852</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Automotive</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2825796\" target=\"_blank\">2825796</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Banking</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2805619\" target=\"_blank\">2805619</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Defense and Security</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2810120\" target=\"_blank\">2810120</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Mill Products</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2829102\" target=\"_blank\">2829102</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Public Sector</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2805659\" target=\"_blank\">2805659</a></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"325\">\n<p>Investigative Case Management</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><a href=\"/notes/2838859\" target=\"_blank\">2838859</a> <br/>(resolved with SAP Note <a href=\"/notes/2846640\" target=\"_blank\">2846640</a>)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong>Important Changes after Release of SAP S/4HANA 1909:</strong></p>\n<p>[2019-11-06] Restriction related to ICM resolved with SAP Note <a href=\"/notes/2846640\" target=\"_blank\">2846640</a>.<strong> </strong></p>\n<p>[2020-02-10] Information regarding Fiori application 'Manage Product Master' (Master Data Maintenance – Product Master) added.</p>\n<p>[2020-02-10] Restriction for SAP S/4HANA Migration Cockpit added as of FPS01.</p>\n<p>[2020-05-05] Restriction for SAP S/4HANA MDM added as of FPS02.</p>\n<p>[2020-06-04] Restriction for SAP S/4HANA Migration Cockpit added as of FPS02.</p>\n<p>[2020-06-04] Restriction for Predective accounting added as of FPS02.</p>\n<p>[2020-07-01] Restriction for SAP S/4HANA Migration Cockpit is resolved with <a href=\"/notes/2887066\" target=\"_blank\" title=\"2887066  - Collective Content correction TCI for 1909 FPS00 and FPS01 (AFS and ERP)\">2887066</a>.</p>\n<p>[2024-07-25] The minimum SP level of SAP ERP 6.0 to run a conversion to SAP S/4HANA has been increased from SP20 to SP22</p>\n<p> </p>", "noteVersion": 25}, {"note": "2900607", "noteTitle": "2900607 - Simplification Item S4TWL - Business User integration of Business Partners with user name and personnel number", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You execute a system conversion to S/4HANA, On-Premise-Edition 1909 or higher. In S/4HANA the fields \"user name\" (XUBNAME) and \"personnel number\" (PERS_NR) from table BP001 of the Financial Services enhancement of the SAP Business Partner are not available. The corresponding information needs to be retrieved from HR. Because of this a business partner, in which one of the two fields is not initial, needs to get connected to an HR person during the system conversion. This note describes activities that need to be executed in the source system before conversion in order to ensure a smooth conversion process and correct assignment between business partner, HR person and user name.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Business for Financial Services, Business Partner, SAP BP, FSBP, BP001, user, user name, personal number, XUBNAME, PERS_NR, HR Integration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The fields BP001-XUBNAME and BP001-PERS_NR are not available any more in S/4HANA. Applications that have accessed these fields need to access the corresponding HR tables now, for example via CDS view I_BUSINESSUSER. This is ensured by the corresponding applications in separate notes. To make sure that this access is successful the corresponding bps need to be connected to an HR person or, in case HR is not active in your system, to the corresponding object in the FIORI app to maintain employees. This will happen during the system conversion and will only work without error if some preconditions are fulfilled. These pre-conditions need to be checked before the upgrade and will be integrated into the simplification item check.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Procedure for determining the relevance</strong></p>\n<p>The check only needs to be executed for a business partner if there is at least one entry in table BP001 for this business partner that contains a non-initial value in one of the fields XUBNAME or PERS_NR.</p>\n<p><strong>Check details</strong></p>\n<p>A check class will be integrated into the Simplification Item Check Report /SDF/RC_CHECK_START. Technically this class is delivered with note 2907836. This class checks if all pre-conditions are fulfilled so that the integration of the corresponding BPs into HR or HR app are met. In case of errors the migration to S/4HANA can be started only after these errors have been solved.</p>\n<p>The following checks are executed centrally in all clients of your system (apart from 000 and 066) and per client for all business partners that were found by the above condition:</p>\n<p>I. Generic checks (independent if HR is active or not):</p>\n<ol>\n<li>For each business partner found in BP001 an entry in the header table BUT000 must exist. <br/>[This check is a pre-caution as we had few customers with this kind of inconsistency in the past]</li>\n<li>The corresponding business partner needs to be a person (partner category 1).</li>\n<li>The corresponding business partner needs to be assigned to a role with role category \"Employee\" (role category BUP003 in table TB003 - can be maintained via view V_TB003 in transaction SM30).</li>\n<li>In case there is an entry in table USR21 for the user name from BP001-XUBNAME, the following conditions dependent on field USR21-IDADTYPE must be fulfilled:<br/>IDADTYPE 00: The GUID value in USR21-BPPERSON must be initial<br/>IDADTYPE 01 and 03: This IDADTYPE must not be assigned<br/>IDADTYPE 02 and 04: The GUID in USR21-BPPERSON must not be initial and must be equal to the PARTNER_GUID in table BUT000 for the corresponding business partner.</li>\n<li>The user name (BP001-XUBNAME) and personnel number (BP001-PERS_NR) of the corresponding business partner must not be assigned to several business partners.</li>\n</ol>\n<p>II. Additional checks for the case that HR is not active:</p>\n<ol>\n<li>If BP001-XUBNAME is not initial: A central person for the business partner number with assigned user name exists in HR table HRP1001 and the user name needs to be identical to BP001-XUBNAME.</li>\n<li>If BP001-XUBNAME is not initial: A central person for the business partner user name exists in HR table HRP1001 and the business partner assigned to this central person must be identical to BP001-PARTNER.</li>\n</ol>\n<p>III. Additional checks for the case that HR is active:</p>\n<ol>\n<li>If BP001-PERS_NR is not initial: the value in field PERS_NR must be numeric and have a maximum length of 8 digits</li>\n<li>The business partner must be assigned to a central person in table HRP1001 via business partner number in field SOBID (and values SCLAS = 'BP', RSIGN = 'B', RELAT = '207' and the corresponding plan variant - field PLVAR - from table T77S0 for the client)</li>\n<li>A central person for the business partner with assigned personnel number must exist in table HRP1001 and the personal number needs to be identical to BP001-PERS_NR.</li>\n<li>The central person assigned to the bp needs to have an entry in HR table PA0105 and the personnel number needs to be identical to BP001-PERS_NR</li>\n<li>If BP001-PERS_NR is not initial: <br/>For this PERS_NR exactly one central person must exist in table HRP1001 which is assigned to the same business partner, and <br/>for this PERS_NR a user name in table PA0105 must exist with the same user name as in BP001-XUBNAME</li>\n<li>If BP001-XUBNAME is not initial: <br/>An entry for this user name needs to exist in table PA0105 with the same PERS_NR and the user name needs to be identical to BP001-XUBNAME, and <br/>for this user a central person in table HRP1001 needs to exist with the same personnel number assigned to the same business partner.</li>\n</ol>\n<p>In all cases an error is returned when the condition is not met</p>\n<p><strong>Necessary activities in case of errors from the check class</strong></p>\n<p>The check class CLS4SIC_FSBP_BP001_HR is provided by SAP and integrated into report /SDF/RC_START_CHECK. This means that the relevance check can be started via this report. Further activities are only necessary in case the check class returns at least one error. These activities are described in the following:</p>\n<p>I. Generic checks</p>\n<ol>\n<li>In case no BUT000 entry exists for a BP001-entry: execute report FS_BP_DELETE_ORPHANED_DATASETS in the affected client to delete orphaned BP001 entries.</li>\n<li>Business partner is not of category \"Person\" (bp category 1): For such business partners you need to delete the two fields from table BP001.</li>\n<li>Role employee is not assigned to the relevant bps: If this check returns an error you need to create a role with role category \"employee\" (BUP003) for this bp in table BUT100. In the simple case you can use role BUP003. Mass creation of role BUP003 or a custom role can be achieved using transaction MASS for object type BUS1006 (Business Partner). Alternatively create a small report in customer name space and call BAPI_BUPA_ROLE_ADD_2 in it.</li>\n<li>Inconsistencies in table USR21: You need to correct the concerned entries from table USR21 in the following way: <br/>IDADTYPE 00: delete the GUID value in field USR21-BPPERSON<br/>IDADTYPE 01 and 03: delete the complete entry from table USR21<br/>IDADTYPE 02 and 04: enter the entry from BUT000-PARTNER_GUID in field USR21-BPPERSON</li>\n<li>The same user name or the same personnel number is assigned to several business partners: check the assignments and adapt them in a way that every user name and personnel number is only assigned once in table BP001.</li>\n</ol>\n<p>II. Additional checks in case HR is not active:</p>\n<ol>\n<li>If BP001-XUBNAME is not initial: Make sure that a central person for the business partner number with assigned user name exists in HR table HRP1001 and the user name in HRP1001 is identical to BP001-XUBNAME.</li>\n<li>If BP001-XUBNAME is not initial: Make sure that a central person for the business partner user name exists in HR table HRP1001 and the business partner assigned to this central person is identical to BP001-PARTNER.</li>\n</ol>\n<p>III. Additional checks in case HR is active:</p>\n<ol>\n<li>If BP001-PERS_NR is not initial: make sure that the value in field PERS_NR is numeric and has a maximum length of 8 digits or delete the field BP001-PERS_NR for this business partner.</li>\n<li>Create a central person for this business partner in table HRP1001 via business partner number in field SOBID (and values SCLAS = 'BP', RSIGN = 'B', RELAT = '207' and the corresponding plan variant - field PLVAR - from table T77S0 for the client)</li>\n<li>Assign the personnel number from BP001-PERS_NR to corresponding field of the central person entry in table HRP1001.</li>\n<li>Create an entry in table PA0105 for the central person and make sure that the personnel number is identical to BP001-PERS_NR.</li>\n<li>If BP001-PERS_NR is not initial: <br/>Make sure that for this PERS_NR exactly one central person exists in table HRP1001 which is assigned to the same business partner, and <br/>for this PERS_NR a user name in table PA0105 exists with the same user name as in BP001-XUBNAME.</li>\n<li>If BP001-XUBNAME is not initial: <br/>Make sure that an entry for this user name exists in table PA0105 with the same PERS_NR in which the user name is identical to BP001-XUBNAME, and <br/>for this user a central person in table HRP1001 exists with the same personnel number assigned to the same business partner.</li>\n</ol>\n<p><strong>Further actions some time after the conversion</strong></p>\n<p>After the conversion all entries in the two fields BP001-PERS_NR and BP001-XUBNAME (including the flags BP001-HR_ORG_FLAG and BP001-STAFF_GRP need to be deleted.</p>\n<p>Before you do this you should wait some time in order to be sure that the user name and personal number are fetched correctly from HR or the HR app by the applications.</p>\n<p>The deletion should be done at the latest, before you start planning the next upgrade to a higher release level of S/4HANA.</p>", "noteVersion": 2}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Since CML-specific functions with regard to collaterals and collateral objects are not available in SAP S/4HANA, you should redesign your business process for the use of FS-CMS."}, {"Activity": "Customizing / Configuration", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Configure FS-CMS."}, {"Activity": "Data migration", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Migrate data to FS-CMS - SAP Collateral Management."}, {"Activity": "Custom Code Adaption", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "Read the SAP Note 2211665 before conversion as it contains case-related information that might require additional steps."}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}