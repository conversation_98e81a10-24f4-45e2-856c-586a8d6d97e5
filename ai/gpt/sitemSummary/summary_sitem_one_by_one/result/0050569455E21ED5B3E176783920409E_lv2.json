{"guid": "0050569455E21ED5B3E176783920409E", "sitemId": "SI1_FIN_GL", "sitemTitle": "S4TWL - GENERAL LEDGER", "note": 2270339, "noteTitle": "2270339 - S4TWL - General <PERSON><PERSON>", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Business Value</strong></strong></p>\n<p>The universal journal significantly changes the way transactional data is stored for financial reporting. It offers huge benefits in terms of the ability to harmonize internal and external reporting requirements by having both read from the same document store where the account is the unifying element.</p>\n<p>You still create general journal entries in General Ledger Accouting, acquire and retire assets in Asset Accounting, run allocation and settelement in Controlling, capitalize research and development costs in Investment Management, and so on, but in reporting, you read from one source, regardless of weather you want to supply data to your consolidation system, report to tax authotities, or make internal mangement decisions.</p>\n<p><strong>Description</strong></p>\n<p>General Ledger in S/4H is based in the Universal Journal; the line items are stored in the new database table ACDOCA, optimized to SAP HANA.</p>\n<p>You can migrate Ledgers of New General Ledger of the Business Suite or the Classic General Ledger GL to the Universal Journal in S/4H. Please note, that the number of Ledgers and the configuration of its currency will stay; it is not possible to introduce new ledgers or new currencies during the migration to S/4H. If Classic General Ledger was used, the ledger '00' is migrated to the new leading ledger '0L' of the universal journal.</p>\n<p>As Controlling is now part of the Universal Journal as well, now all CO internal actual postings are visible in General Ledger as well. The secondary cost elements are part of the chart of accounts. There is no longer a need for Co realtime integration for transfer of secondary CO postings to NewGL or the Reconciliation Ledger of Classic GL.</p>\n<p>It is not possible to introduce new currencies in GL during the migration to S/4H. The order (resp. technical fieldnames in table ACDOCA) of the currencies might change during the migration from Classic or New GL, as the the currency type of the controlling are will determine the second currency (fieldname KSL), but the compatibility view for classic or New GL guarantee that the reporting based on these structures shows the same result as before.</p>\n<p>Classic Report Writer/Painter and drilldown reports will continue to show the data from the universal journal in separate views, so reports in Financial Accounting will show only the fields formerly available in FAGLFLEXT and reports in Controlling will show only the fields formerly available in COSP/COSS. To see the combined data you are recommended to work with the new Fiori reports that show all the fields in the universal journal.</p>\n<p>The upgrade to S/4HANA is not a 'normal' upgrade but a system conversion which comes along with some incompatible changes. The incompatible changes refer to the usage of summary and index tables and the update of table BSEG and further changes are described in detail in the note 2431747 \"General Ledger: Incompatible Changes in S/4HANA compared to classic ERP releases\".</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Mandatory application configuration steps related to General Ledger in the IMG (transaction SPRO):</p>\n<ul>\n<li>Folder <em>Preparations and Migration of Customizing</em><br/>Please use the step \"Check Customizing Settings Prior to Migration\" (transaction FINS_MIG_PRECHECK) and clean up all inconsistencies prior to the migration of Customizing and ransactional data.  </li>\n<li>Folder <em>Preparation and Migration of Customizing for the General Ledger</em><br/>The step \"Migrate General Customizing\" migrates all important customizing around ledgers, currencies and CO integration. It is important to carry out this step before manual changes are done in the img activities below this activity.<br/>Therefore we recommend to go though all activites of \"Preparation and Migration of Customizing for the General Ledger\" in its sequence in the UI.<br/>As last step perform \"Execute Consistency Check of General Ledger Settings\" (transaction FINS_CUST_CONS_CHK). If this step yields errors for prdouctively used company codes, plese solve these errors before you start the migration of transactional data in Folder <em>Data Migration</em>. If you are getting errors for non-productively used company codes, you can exclude these by activating the flag \"Template\" in view V_001_TEMPLATE (transaction SM30).</li>\n</ul>\n<p> It is recommended to investigate the use of the new reports that show all fields in the universal journal to achieve maximum benefit from the conversion.</p>\n<p>If you have not used the New General Ledger before, that means that you still have used the Classic General Ledger before migrating to SAP S/4 HANA, please be aware that the handling of the Foreign Currency Evaluation, the Line Item Display and the Balance Carry Forward has slightly changed. Please make yourself familiar with the new handling of them in NewGL before the Migration.</p>", "noteVersion": 9, "refer_note": [{"note": "2579584", "noteTitle": "2579584 - Recommendations for Usage of Reports in Financial Reporting in S/4 HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With SAP S/4 HANA, SAP changed the data model in Finance to bring together data from several application components into one item table (the universal journal). This means that all report users are looking at different aggregations of the same dataset (single source of truth) and there is no need to pre-aggregate data or reconcile different data sources. The reporting applications are being rebuilt to take advantage of the new data model and extended to include, for example, additional currencies or additional fields and to provide a modern user experience (SAP Fiori).</p>\n<p>SAP delivers many reporting applications that are designed to cover the main reporting requirements and offers a variety to tools that allow customers to enhance the reports delivered by SAP or build their own reports based on the CDS views delivered by SAP.</p>\n<p>This note explains how to use the different reporting tools and describes the reporting apps delivered for CO and FI in SAP S/4HANA  (from  1610 OP).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>REPORTPAINTER, REPORTWRITER, CDS, Embedded Analytics, Plan-Actual-Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For the areas CO-OM, CO-PC, CO-PA, FI-GL, EC-PCA, and FI-AA SAP delivers:</p>\n<ul>\n<li>Fiori apps for financial reporting</li>\n<li>Fiori based key user tools that allow the creation of customer-specific CDS query based reports and allow to enhance SAP delivered reports. <br/>These CDS queries can also be used in a variety of analytical applications outside of S/4HANA like SAP Analysis for Microsoft Office or SAP Analytics Cloud.</li>\n</ul>\n<p>Alongside these new reporting capabilities, existing reporting tools are still supported to ensure that upgrade projects can make the transition over time and continue to take advantage of their earlier investments:</p>\n<ul>\n<li>Report Writer, Report Painter, Drill-Down Reporting</li>\n<li>SAPGUI Applications for line items</li>\n</ul>\n<p>The existing tools have been adapted and optimized to work with the new HANA based architecture, but cannot leverage the HANA architecture completely due to the constraints in the original tool architecture.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP recommends a gradual switch to the new CDS based reporting with the FIORI user interface.</p>\n<p>Please find here more details and links to further information on the following topics:</p>\n<ol>\n<li><a href=\"#Fiori_apps\" target=\"_self\">Fiori apps for reporting</a></li>\n<li><a href=\"#Key_user_tools\" target=\"_self\">Fiori based key user tools that allow the creation of own CDS based reports</a></li>\n<li><a href=\"#Advantages\" target=\"_self\">Advantages of CDS based reporting</a></li>\n<li><a href=\"#ReportWriter\" target=\"_self\">Report Writer, Report Painter, Drilldown Reporting and SAPGUI Applications for line items</a></li>\n</ol>\n<p><br/><strong><a name=\"Fiori_apps\" target=\"_blank\"></a></strong>﻿<strong>1. Fiori apps for reporting</strong></p>\n<p>You can find detailed information on the new reports in the Fiori Apps Library by searching for the roles General Ledger Accountant, Controller, Inventory Accountant, Asset Accountant, and so on: <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/home\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/home</a></p>\n<p>To gain an impression of the new options, use this video to show how to display and compare G/L account balances: <a href=\"https://help.sap.com/viewer/0fc4f335190447c2992391835eb508cd/3.6/en-US/0e2382534c07ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/0fc4f335190447c2992391835eb508cd/3.6/en-US/0e2382534c07ff4fe10000000a44176d.html</a></p>\n<p>Further examples of the new reporting apps are:</p>\n<ul>\n<li>“Display Financial Statement” <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0708')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0708')/S9OP</a></li>\n<li>“Trial Balance”  <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0955')/W11\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0955')/W11</a></li>\n<ul>\n<li>Video: SAP S/4HANA Analyze Financial Balance Sheet - Profit and Loss <a href=\"https://www.youtube.com/watch?v=YIfHsp2AUio&amp;feature=youtu.be\" target=\"_blank\">https://www.youtube.com/watch?v=YIfHsp2AUio&amp;feature=youtu.be</a></li>\n</ul>\n<li>“Display G/L Account Line Items”  <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F2217')/S18\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F2217')/S18</a></li>\n<li>“Display G/L Account Balances”  <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0707')/S7OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0707')/S7OP</a></li>\n<li>“Cost Centers - Plan/Actual” <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0949A')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0949A')/S9OP</a></li>\n<li>“Internal Orders - Plan/Actual YTD”   <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0929A')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0929A')/S9OP</a></li>\n<li>“Profit Centers - Plan/Actual with Currency Translation” <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0938A')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0938A')/S9OP</a></li>\n<li>Blog: SAP S/4HANA Cloud for Finance: Balance Reporting – Part 2: Standard reports and standard API for balance reporting <a href=\"https://blogs.sap.com/2019/02/19/sap-s4hana-cloud-for-finance-balance-reporting-part-2-standard-reports-and-standard-api-for-balance-reporting/\" target=\"_blank\">https://blogs.sap.com/2019/02/19/sap-s4hana-cloud-for-finance-balance-reporting-part-2-standard-reports-and-standard-api-for-balance-reporting/</a></li>\n</ul>\n<p><strong><a name=\"Key_user_tools\" target=\"_blank\"></a></strong>﻿<strong>2. Fiori based key user tools that allow the creation of own CDS based reports</strong></p>\n<p>The term “S/4HANA Embedded Analytics” is used to describe the new CDS based reporting capabilities within S/4HANA. This blog provides more information about the basic ideas: <a href=\"https://blogs.sap.com/2016/05/27/getting-started-with-s4-hana-embedded-analytics/\" target=\"_blank\">https://blogs.sap.com/2016/05/27/getting-started-with-s4-hana-embedded-analytics/</a></p>\n<p>The term “key user tools” is used to describe the toolsets that will ultimately replace Report Writer, Report Painter, Drill-Down, and so on. This blog provides an introduction to the key user tools. <a href=\"https://blogs.sap.com/tag/key-user-tools/\" target=\"_blank\">https://blogs.sap.com/tag/key-user-tools/</a></p>\n<p>If you want to create your own reports, use the app “Custom Analytical Query” to define a CDS query. In this blog we explain how to use this tool to create a cost center report with hierarchies for cost centers and cost elements (accounts) and information on plan values and actual values: <a href=\"https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/\" target=\"_blank\">https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/</a></p>\n<p>You can use the CDS query that you have defined using the “Custom Analytical Query” app in different user interfaces. For a simple grid based application, embed the query in the  “Design Studio” as described here: <a href=\"https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/\" target=\"_blank\">https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/</a></p>\n<p>Information how you can create your own balance report can be found here: <a href=\"https://blogs.sap.com/2019/03/15/sap-s4hana-cloud-for-finance-balance-reporting-part-3-custom-report-creation-for-balance-reporting/\" target=\"_blank\">https://blogs.sap.com/2019/03/15/sap-s4hana-cloud-for-finance-balance-reporting-part-3-custom-report-creation-for-balance-reporting/</a></p>\n<p>The same CDS query also can be used to create an “Analytical List Page” (ALP), a graphic in an “Overview Page” or a “Smart Business” analytical application. Info on ALP is provided in this blog: <a href=\"https://blogs.sap.com/2016/11/03/sap-fiori-2.0-a-primer-on-embedded-analytics/\" target=\"_blank\">https://blogs.sap.com/2016/11/03/sap-fiori-2.0-a-primer-on-embedded-analytics/</a>.</p>\n<p>Alternatively, if you want to use this CDS query in an Excel based user interface, you can also use “SAP Analysis for Microsoft Office” to display this query.</p>\n<p>Configuration guides that describes the necessary steps to define own Fiori based analytical applications can be found in the “SAP Best Practices for analytics with SAP S/4HANA”: <a href=\"https://rapid.sap.com/bp/#/BP_S4H_ANA\" target=\"_blank\">https://rapid.sap.com/bp/#/BP_S4H_ANA</a><br/>Detailed steps for the key user tools are described in this file: <a href=\"https://support.sap.com/content/dam/SAAP/Sol_Pack/Library/Configuration/2PJ_S4CLD1711_BB_ConfigGuide_EN_XX.docx\" target=\"_blank\">https://support.sap.com/content/dam/SAAP/Sol_Pack/Library/Configuration/2PJ_S4CLD1711_BB_ConfigGuide_EN_XX.docx</a></p>\n<p><strong><a name=\"Advantages\" target=\"_blank\"></a>﻿﻿3. Advantages of CDS based reporting</strong></p>\n<p><span>More detail in the reports:</span><br/>The universal journal in SAP S/4HANA contains all reporting dimensions (account, profit center, company code, cost center, and so on). The line items cannot be consumed directly in a report but must be accessed via CDS views that allow you to link the transactional data with the related master data information, including texts (available in multiple languages), attributes (e.g. the name of the cost center manager) and hierarchies. SAP delivers CDS views for the transactional data and the master data that you can directly use to create your own CDS queries.</p>\n<p><span>Customer enhancements in master data automatically available in reporting:</span><br/>CDS based reporting supports the usage of custom fields in SAP delivered or customer defined reports. Here you can find an example for the master data extensibility: <a href=\"https://blogs.sap.com/2017/07/12/new-video-extending-master-data-for-fixed-assets-in-sap-s4hana-cloud/\" target=\"_blank\">https://blogs.sap.com/2017/07/12/new-video-extending-master-data-for-fixed-assets-in-sap-s4hana-cloud/</a></p>\n<p><span>Definition of CDS queries allows usage in multiple analytic application:</span><br/>Once you have defined a CDS query, you might choose from a variety of user interfaces according to the use case and the expectations of the user. The same CDS query can be used for detailed drill down analysis with a multi-dimensional grid and to define a traffic light or a graphic in the “Smart Business” framework. Other usages are “Analytical List Page” (ALP), SAP Analysis for Office or even SAP Analytics Cloud.</p>\n<p><strong><a name=\"ReportWriter\" target=\"_blank\"></a>﻿4. Report Writer, Report Painter and SAPGUI Applications for line items</strong></p>\n<p>Report Writer, Report Painter, Drill-Down Reporting, and the SAPGUI Applications for line items are still available in the S/4HANA System. These reports are still supported and have been technically adapted and optimized for the new architecture.</p>\n<p>For technical reasons, these classic reporting tools can only partial benefit from the new HANA capabilities. <br/>- The advantages of more detailed reporting with the usage of line item information is not available for Report Painter and Report Writer, since the report definition continues to be based on the old data structures.<br/>- Report Painter and Report Writer Reports cannot benefit from the new extensibility concept.<br/>- CDS based queries uses an architecture that is designed specifically to work with HANA. Therefore CDS based reporting might in many cases offer superior performance compared to Report Painter or Report Writer.</p>\n<p>Therefore going forward we recommend to create new reports using the CDS based reporting tools instead of using Report Painter or Report Writer.</p>", "noteVersion": 6, "refer_note": [{"note": "2623507", "noteTitle": "2623507 - Fiori Multidimensional Reporting in S/4 HANA onPremise using custom analytical queries", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP delivers in S/4 HANA various Fiori Multidimensional Reports which allow analyzing the data in a flexible way (e.g. by easily adding dimensions and measures to the rows or columns).</p>\n<p>Customers and Partners can also create Fiori Multidimensional Reports based on an Analytcial Query and visualize them in the Fiori Launchpad. SAP offers for this puppose in S/4 HANA on Premise 2 generic player applications which require mainly the Analytical Query as parameter: a Web Dynpro based reporting application and a SAPUI5 based reporting application (this reporting tool is obsolete since S/4 HANA on Premise 2021). The following describes how to configure these 2 reporting applications in Fiori Launchpad (FLP). (Please note: The Analytical Query can be either a ABAP CDS View - exposed as anyltical query - or a BW Query.)</p>\n<p> </p>\n<p><strong>1. Web Dynpro based reporting application for S/4 HANA (\"Web Dynpro Data Grid\")</strong></p>\n<p>(To be used as default reporting tool in S/4 HANA)</p>\n<p><strong>a) FLP Target Mapping</strong></p>\n<p>Semantic Object: &lt;choose a valid Semantic Object&gt;</p>\n<p>Action: &lt;arbitary action&gt;</p>\n<p>Application Type: Web Dynpro</p>\n<p>Title: &lt;arbitary title&gt;</p>\n<p>Application: FPM_BICS_OVP</p>\n<p>Configutation: FPM_BICS_OVP</p>\n<p>System Alias: &lt;RFC Connection to the backend, e.g. S4FIN; can be maintained via IMG (<em>Manage RFC Destinations</em>) or via the transaction SM59&gt;</p>\n<p>Device Types: Desktop</p>\n<p>Parameters:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Name</strong></td>\n<td><strong>Mandatory</strong></td>\n<td><strong>Value</strong></td>\n<td><strong>Default Value</strong></td>\n</tr>\n<tr>\n<td>bsa_query</td>\n<td>false</td>\n<td> </td>\n<td>&lt;ID of the custom analytical query&gt; (Please note: if an ABAP CDS View is exposed as anayltical query then you should use for the ID of the query the syntax 2C&lt;SQL View Name of the CDS View&gt;; e.g. the ID of the query is 2CCFITRIALBALQ0001 if the SQL View Name is CFITRIALBALQ0001)</td>\n</tr>\n<tr>\n<td>sap-ui-tech-hint</td>\n<td>true</td>\n<td>WDA</td>\n<td> </td>\n</tr>\n<tr>\n<td>sap-ushell-next-navmode</td>\n<td>false</td>\n<td> </td>\n<td>explace</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>b) FLP Tile Configuration</strong></p>\n<p>Title: &lt;use the same Title as defined in the Target Mapping&gt;</p>\n<p>Information: &lt;Accessible&gt; (recommended setting)</p>\n<p>Semantic Object: &lt;use the same Semantic Object as defined in the Target Mapping&gt;</p>\n<p>Action: &lt;use the same Action as defined in the Target Mapping&gt;</p>\n<p>Parameters: sap-ui-tech-hint=WDA</p>\n<p> </p>\n<p><strong>2. SAPUI5 based generic reporting application for S/4 HANA (\"Design Studio Data Grid\") - available since SAPUI5 1.48 (S/4 HANA on Premise 1709)</strong></p>\n<p>(This reporting tool is obsolete since S/4 HANA on Premise 2021)</p>\n<p><strong>a) FLP Target Mapping</strong></p>\n<p>Semantic Object: &lt;choose a valid Semantic Object&gt;</p>\n<p>Action: &lt;arbitary action&gt;</p>\n<p>Application Type: SAPUI5 Fiori App</p>\n<p>Title: &lt;arbitary title&gt;</p>\n<p>URL: /sap/bc/ui5_ui5/sap/FIN_DS_ANALYZE</p>\n<p>ID: fin.acc.query.analyze</p>\n<p>Device Types: Desktop</p>\n<p>Parameters:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Name</strong></td>\n<td><strong>Default Value</strong></td>\n</tr>\n<tr>\n<td>XQUERY</td>\n<td>&lt;ID of the custom analytical query&gt; (Please note: if an ABAP CDS View is exposed as anayltical query then you should use for the ID of the query the syntax 2C&lt;SQL View Name of the CDS View&gt;; e.g. the ID of the query is 2CCFITRIALBALQ0001 if the SQL View Name is CFITRIALBALQ0001)</td>\n</tr>\n<tr>\n<td>XSEMANTIC_OBJECTS</td>\n<td>*</td>\n</tr>\n<tr>\n<td>XSYSTEM</td>\n<td>&lt;SAP System Alias (to the backend system), e.g. S4FIN; can be maintained via IMG (<em>Manage SAP System Aliases</em>) or via the transaction SM30 (View /IWFND/V_DFSYAL)&gt;</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>b) FLP Tile Configuration</strong></p>\n<p>Title: &lt;use the same Title as defined in the Target Mapping&gt;</p>\n<p>Semantic Object: &lt;use the same Semantic Object as defined in the Target Mapping&gt;</p>\n<p>Action: &lt;use the same Action as defined in the Target Mapping&gt;</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>(No correction required.)</p>", "noteVersion": 4}, {"note": "2535903", "noteTitle": "2535903 - How to create your custom CDS Queries and leverage existing CDS Reporting Queries in S/4HANA Cloud and On Premise 1709 and subsequent OP releases", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to create your own Custom CDS queries to fullfil customer requierements or want to leverage existing CDS queries as a tile on the Fiori launchpad. This note is relevant for S/4HANA Cloud and S/4HANA OnPremise 1709 and subsequent OP releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Core Data Services</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Information</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span>One simple option to leverage an existing CDS query for reporting is to create a new tile in the Fiori Launchpad Designer following note ##2623507. The list of existing CDS queries can be found below.</span></p>\n<p><span>A different option is to create your own custom CDS query that can be accessed via a tile on the Fiori launchpad, follow the guide in the attachment or link </span><a href=\"https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/\" target=\"_blank\">https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/</a><span> Instead of using a custom CDS view as described in the guide, you use the following CDS Cubes as Data Views:</span><br/><span>Actuals: I_JournalEntryItemCube</span><br/><span>Plan/Actuals: I_ActualPlanJrnlEntryItemCube</span><br/><span>If you want to leverage an existing CDS Queries, you can mark an existing CDS Query and click \"Copy\" instead of \"Create\" in the Query Designer (--&gt; tile \"Custom Analytical Queries\") as described in step 2 of the guide linked above.</span></p>\n<p>The following CDS Queries are available:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Report Title</strong></td>\n<td><strong>Subtitle</strong></td>\n<td><strong>CDS Query</strong></td>\n</tr>\n<tr>\n<td>Cost Centers</td>\n<td>Actuals</td>\n<td>C_CostCenterQ2001</td>\n</tr>\n<tr>\n<td>Cost Centers</td>\n<td>Plan/Actual</td>\n<td>C_CostCenterPlanActQ2001</td>\n</tr>\n<tr>\n<td>Projects</td>\n<td>Actuals</td>\n<td>C_ProjectQ2201</td>\n</tr>\n<tr>\n<td>Projects</td>\n<td>Plan/Actual</td>\n<td>C_ProjectPlanActQ2201</td>\n</tr>\n<tr>\n<td>Projects</td>\n<td>Baseline/EAC/Ongoing</td>\n<td>C_ProjectPlanActQ2203</td>\n</tr>\n<tr>\n<td>Internal Orders</td>\n<td>Plan/Actual</td>\n<td>C_InternalOrderPlanActQ2101</td>\n</tr>\n<tr>\n<td>Market Segments</td>\n<td>Actuals</td>\n<td>C_MarketSegmentQ2501</td>\n</tr>\n<tr>\n<td>Market Segments</td>\n<td>Plan/Actual</td>\n<td>C_MarketSegmentPlanActQ2501</td>\n</tr>\n<tr>\n<td>Profit Centers</td>\n<td>Actuals</td>\n<td>C_ProfitCenterQ2701</td>\n</tr>\n<tr>\n<td>Profit Centers</td>\n<td>Plan/Actual</td>\n<td>C_ProfitCenterPlanActQ2701</td>\n</tr>\n<tr>\n<td>P&amp;L</td>\n<td>Actuals</td>\n<td>C_ProfitAndLossQ2901</td>\n</tr>\n<tr>\n<td>P&amp;L</td>\n<td>Plan/Actual</td>\n<td>C_ProfitAndLossPlanActQ2903</td>\n</tr>\n<tr>\n<td>Functional Areas</td>\n<td>Actual</td>\n<td>C_FunctionalAreaQ2801</td>\n</tr>\n<tr>\n<td>Functional Areas</td>\n<td>Plan/Actual</td>\n<td>C_FunctionalAreaPlanActQ2801</td>\n</tr>\n<tr>\n<td>Sales Orders</td>\n<td>Actuals</td>\n<td>C_SalesOrderQ2301</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that for P&amp;L Plan/Actual (CDS query C_ProfitAndLossPlanActQ2903) SAP delivers a Fiori app tile starting with OP1909.</p>", "noteVersion": 6}]}, {"note": "2219527", "noteTitle": "2219527 - Notes about using views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS in customer-defined programs in SAP S/4HANA Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In your customer-defined programs, you use the compatibility views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS to determine FI line items.</p>\n<p>The performance of the compatibility views is not sufficient.</p>\n<p>You want to optimize these accesses.</p>\n<p>You want to understand the data model behind the compatibility views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Compatibility views, SAP Simple Finance, SFIN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP_FIN 700 replaced the index tables BSID, BSAD, BSIK, BSAK, BSIS, and BSAS with views. The views are slower than the replaced tables (partly due to various factors).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Data model of compatibility views</strong></p>\n<p>In SAP S/4HANA Finance, the partially redundant tables BSID, BSAD, BSIK, BSAK, BSIS, and BSAS were replaced with views of the same name. This ensures that all programs that execute read accesses on BSID, BSAD, BSIK, BSAK, BSIS, and BSAS continue to work without any loss of function.</p>\n<p>The views obtain your data from the tables BKPF and BSEG. With SAP Note 2207950, a LEFT OUTER JOIN is used in the views instead of an INNER JOIN. Consequently, in the case of read accesses where only fields from the table BSEG are requested, the database does not have to execute the JOIN, thus improving performance.</p>\n<p>In addition, the views contain a UNION ALL with the relevant BCK table (for example, BSAD_BCK) for reading partially archived documents. In the case of BSID and BSIK, the UNION ALL is required for technical reasons only (so that the views in the ABAP Dictionary obtain the correct data elements). With SAP Note 2207950, the UNION ALL for the views BSIK and BSID is removed in SAP_FIN 730 (SAP S/4HANA Finance 1605) and S4CORE 100 (SAP S/4HANA on-Premise). This improves the performance of both views. In SAP_FIN 720 (SAP S/4HANA Finance 1503), the UNION ALL for the views BSIK or BSID cannot be removed because, at the very least, NetWeaver 750 is required.</p>\n<p><strong>Tips for using the compatibility views</strong></p>\n<ul>\n<li>In most cases, read accesses from the compatibility views can remain unchanged. If, however, the performance of your programs is worse than it was before the upgrade, note the following:</li>\n<li>You can rewrite the SELECTs directly on BSEG or BKPF/BSEG. This is useful in the following cases:</li>\n</ul>\n<ol><ol>\n<li>In SAP_FIN 720, the UNION ALL consumes additional runtime for the views BSID and BSIK. A direct SELECT on the join BSEG/BKPF is more efficient.</li>\n<li>You do not use archiving. Then, the UNION ALL with the BCK table is useless and consumes only runtime.</li>\n<li>You require data from the tables BSID and BSAD (or BSIK/BSAK).</li>\n</ol></ol>\n<ul>\n<li>Avoid a large number of small SELECTs. Preferably, all data should be read with a single access. To prevent a memory overflow in the case of large datasets, you can use the cursor technique (OPEN CURSOR ... FOR SELECT ...).</li>\n<li>Enter a field list. Avoid SELECT *. In particular, this applies if the field list is restricted to the fields in the table BSEG. For more information, see SAP Note 2207950.</li>\n</ul>\n<p><strong>Example</strong></p>\n<p>The attachment contains a code snippet that clarifies the tips described above.</p>", "noteVersion": 2}, {"note": "2431747", "noteTitle": "2431747 - General Ledger: Incompatible changes in S/4HANA compared to classic ERP releases", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to know which incompatible changes exist in S/4HANA compared to 'classic' ERP releases in the area of General Ledger.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Piece lists <span 'times=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-us;=\"\" lang=\"EN-US\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">SI_GENERAL_LEDGER, SI_GENERAL_LEDGER_W, SI_GENERAL_LEDGER_RW, SI_FIN_DATA_MODEL, SI_MATERIAL_LEDGER.<br/>Simplification Items SI1_FIN_GL, SI1_FIN_General, FIN_MISC_ML.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>S/4HANA is a new product compared to the classic ERP releases like EHP618. So the upgrade to S/4HANA is not a 'normal' upgrade but a system conversion which comes along with some incompatible changes.</p>\n<p>The two products S/4HANA Finance (formerly called <em>SAP Simple Finance</em> or sFIN) and S/4HANA are identical from General Ledger perspective. But of course the features and functions contained in each release differ in the following sense:</p>\n<ul>\n<li>S/4HANA 1610 contains enhancements compared to S/4HANA Finance 1605.</li>\n<li>S/4HANA Finance 1605 contains enhancements compared to S/4HANA 1511.</li>\n<li>S/4HANA 1511 contains enhancements compared to S/4HANA Finance 1503.</li>\n</ul>\n<p>That's why this note refers to some notes which were created for S/4HANA Finance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>There are different kinds of incompatible changes:</p>\n<ol>\n<li>Many summary tables have been replaced by database views on line item tables. As a consequence it does not make sense any more to insert, delete or update records in these summary tables. Affected summary table in General Ledger is table GLT0. For details please refer to notes <a href=\"/notes/1976487\" target=\"_blank\">1976487</a> and <a href=\"/notes/2221298\" target=\"_blank\">2221298</a>.</li>\n<li>The view GLT0_AEDAT does not exist any more.</li>\n<li>Several line item tables that contained line item data redundantly for some purpose have been replaced by database views on the line item table BSEG and on the new central line item table ACDOCA: <br/>- Tables used for open-item management are now views on table BSEG and others like BKPF: Affected tables are BSIS, BSAS, BSIK, BSAK, BSID, BSAD, BSIM, FAGLBSIS, FAGLBSAS.<br/>- Table FAGLFLEXA is now a view on ACDOCA and some C-tables.<br/>For details refer to notes <a href=\"/notes/1976487\" target=\"_blank\">1976487</a> and <a href=\"/notes/2219527\" target=\"_blank\">2219527</a><span class=\"urTxtStd urVt1\">.</span><br/>The new table ACDOCA is important because it is the line item table of the \"Universal Journal Entry\": It is the unified persistency of the line items of several applications for actuals (not plan data): <br/>Read-access to some tables of other applications in Accounting has been replaced by views on the new central table ACDOCA: SELECT statement from these tables return data from table ACDOCA! The following applications are affected:<br/>- In Controlling the entries of tables COEP, COSS, COSP for value type 04 (table field WRTTP = 04) are now contained only in table ACDOCA; see notes <a href=\"/notes/1976487\" target=\"_blank\">1976487</a> and <a href=\"/notes/2185026\" target=\"_blank\">2185026</a> for more details. <br/>- In Material Ledger the table MLIT was replaced by a view on table ACDOCA in some S/4HANA release and other changes were done. See notes <a href=\"/notes/2292381\" target=\"_blank\">2292381</a>, <a href=\"/notes/2332591\" target=\"_blank\">2332591</a>, <a href=\"/notes/2354768\" target=\"_blank\">2354768</a>.<br/>- In Fixed Assets tables ANEP and others have been replaced by views on table ACDOCA, see note <a href=\"/notes/2270387\" target=\"_blank\">2270387</a>.<br/><br/>In general different techiques are used in the system to replace the read-access to a table by a view: <br/>a) Some tables were converted into database views. For example table BSIS: Displaying table BSIS with transaction SE11 shows that BSIS is not a transparent table but a DDL SQL view.<br/>b) Some tables are still displayed as transparent tables in transaction SE11, but for read-access a proxy object was entered in transaction SE11 in the menu <em>extras -&gt; proxy object</em>. For example for table COEP: The proxy object is V_COEP_VIEW which is the name of a DDL source. DDL sources can be displayed with ABAP Development Tools (=ADT, ABAP in Eclipse) or in some newer releases also with transaction SE11 by entering the name of the DDL source in the view field. The content of the old table COEP can be displayed by using the view V_COEP_ORI.</li>\n<li>Table BSEG contains <em>not</em> all G/L postings any more in S/4HANA: The long term vision is that table BSEG will be used only for the following purposes:</li>\n</ol>\n<ul>\n<ul>\n<li><span>Open item management: Postings that are not related to open items will not create entries in table BSEG any more on the long run.</span></li>\n<li><span>Manual postings in FI: Postings that are performed in FI, for example using transaction FB01, will still create entries in table BSEG, because for such postings, the entries in table BSEG represent the original document. </span></li>\n</ul>\n</ul>\n<p><span>As a first step towards this vision some posting processes do not create entries in table BSEG any more. They only create entries in table ACDOCA instead. Postings without entry in table BSEG can be identified by BKPF-BSTAT = 'U'. For example the following posting processes are affected:</span></p>\n<ol><ol>\n<li>Postings from Controlling like assessments or distributions (transaction KSU5, KSV5, KB11N,...).<br/>Exception: If the posting is cross-company code then BSEG entries are created.</li>\n<li>Foreign currency valuation (transaction FAGL_FCV) and foreign currency translation (transaction FAGL_FC_TRANS as of S4CORE 103).<br/>For this transaction, the creation of BSEG entries can be switched-on again using BAdI BADI_FINS_FCV_BSTAT. For details see note <a href=\"/notes/2670040\" target=\"_blank\">2670040</a>.</li>\n<li>General Ledger assessments and distributions (transactions FAGLGA15, FAGLGA35).<br/>For those transactions, the creation of BSEG entries can be switched-on again using BAdI BADI_FINS_ACDOC_BSTAT. For details see note <a href=\"/notes/2400235\" target=\"_blank\">2400235</a>.<br/>The full list of posting processes that do not create BSEG entries any more are listed in note <a href=\"/notes/2383115\" target=\"_blank\">2383115</a>.</li>\n</ol></ol>\n<p>Postings with BKPF-BSTAT value ‘U’ are non-taxable. These postings do not create entries in tables BSEG or BSET. In table ACDOCA, tax fields, such as ACDOCA-MWSKZ, remain empty if the BKPF-BSTAT value is ‘U’.</p>\n<p><span>If you have programs where all postings, including postings with BKPF-BSTAT = 'U', shall be processed in BSEG format, then the source code of these programs needs to be adopted: The source code where the SELECT statements from table BSEG are performed, need to be enhanced: In addition to selecting line items from table BSEG, the line items for postings with BKPF-BSTAT = 'U' need to be selected from table ACDOCA. For mapping the result from structure ACDOCA to BSEG, the method CL_FINS_ACDOC_TRANSFORM_UTIL-&gt;TRANSFORM_ACDOCA_TO_BSEG can be useful. Also method CL_FINS_GET_BSEG-&gt;MAP_ACDOCA_TO_BSEG_EXT might be useful in some cases.</span></p>\n<p>Useful can also be the DDL source I_JournalEntryOperationalView. In transaction SE11 you can find this view with name IFIJRNLENTOPV. This view selects data from ACDOCA and joins fields from table BSEG and BSEG_ADD.</p>\n<p><strong>Remark:</strong> If you have several ledgers in G/L, table ACDOCA contains also postings that were done only into the non-leading ledger.</p>\n<p>Such postings have BKPF-BSTAT = L. They have no entry in BSEG, instead they have an entry in table BSEG_ADD (and in ACDOCA).</p>\n<p>If you are going to use the I_JournalEntryOperationalView CDS view or IFIJRNLENTOPV, be aware that these objects use the authorization checks described in Note <a href=\"/notes/2608006\" target=\"_blank\">2608006</a>.</p>\n<p>It is <strong>not</strong> recommended that you simply replace all your SELECT FROM BSEG statements by SELECT FROM ACDOCA statements without investigating the purpose of the individual SELECT statements. Reasons:</p>\n<ul>\n<ul>\n<li><span>Table ACDOCA does not contain all fields of table BSEG; so it could occur that the SELECT from table ACDOCA does not return all the info that you might require in your program.</span></li>\n<li><span>Assume the program that performs currently the SELECT from table BSEG also performs UPDATEs on the selected BSEG records. If you replace this SELECT so that it selects the data from ACDOCA and converts the selected data into BSEG format, this means that this logic pretends that BSEG entries exist despite this is not the case. As a consequence the updates will fail if no BSEG entry exists.</span></li>\n</ul>\n</ul>\n<p><br/><span>Nevertheless you might want to add some fields in table ACDOCA that are actually only contained in BSEG, for example field XREF1. You can add these fields as extension fields (in customer namesapce, for example as ZZXREF1) to table ACDOCA. The procedure is described in note </span><a href=\"/notes/2453614\" target=\"_blank\">2453614</a><span>: You add the fields to include INCL_EEW_ACDOCA and fill them using BAdI BADI_FINS_ACDOC_POSTING_EVENTS.</span></p>\n<p><span>But it is </span><strong>not</strong><span> recommended to add all missing fields from table BSEG as extension fields to table ACDOCA: Reasons:</span></p>\n<ul>\n<ul>\n<li><span>Some fields of table BSEG are updated by FI programs, for example the dunning level (BSEG-MANST). In table ACDOCA the corresponding extension field would not be updated. </span></li>\n<li><span>If the document split is active, usually ACDOCA contains more entries compared to table BSEG because some line items are splitted. But this splitting is done only for the amount fields that are available in ACDOCA as standard fields. If you add amount fields from BSEG to ACDOCA as extension fields, for example field BSEG-SKNTO, the amount of those extension fields would not be splitted: Aggregating data that were selected from ACDOCA would yield too high amounts for such extension fields.</span></li>\n<li><span>In table BSEG the amount fields have positive sign whereas in table ACDOCA amounts can be negative! </span></li>\n<li><span>For example the view IFIJRNLENTOPV (see above) returns the amount fields from table ACDOCA, i.e. negative signs can occur. </span></li>\n</ul>\n</ul>\n<ol start=\"5\">\n<li>New General Ledger Accounting is obsolete in S/4HANA. It has been replaced by the Unified Journal Entry. The corresponding tables FAGLFLEXA, FAGLFLEXT, JVGLFLEXA, JVGLFLEXT, FMGLFLEXA, FMGLFLEXT are obsolete. Their content is now contained in table ACDOCA. After an upgrade to S/4HANA, the content of the mentioned obsolete tables is migrated into table ACDOCA.</li>\n<li>Read-access to these tables has been replaced by database views. This applies for example for table FAGLFLEXA: SELECT statement from this table return data from table ACDOCA! To select data from the original table FAGLFLEXA, the view V_FAGLFLEXA_ORI has to be used. For details please refer to notes <a href=\"/notes/1976487\" target=\"_blank\">1976487</a> and <a href=\"/notes/2221298\" target=\"_blank\">2221298</a>.<br/>On a technical level, the Unified Journal Entry in S/4HANA reuses some parts of New General Ledger Accounting. That's why in S/4HANA in the content of table FAGL_ACTIVEC some fields are filled with a value that cannot and may not be changed: For example FAGL_ACTIVEC-ACTIVE is = X.</li>\n<li>The tables FAGLFLEXP, FMGLFLEXP, JVGLFLEXP for planning data are obsolete, see note <a href=\"/notes/2253067\" target=\"_blank\">2253067</a>.</li>\n<li>In S/4HANA there are new customizing tables. This has some implications:</li>\n<ul>\n<li>Some old customizing tables have become obsolete or partly obsolete. In General Ledger, the following tables are affected: T881 (partly obsolete), T881T (text table for T881, partly obsolete), T882G (obsolete). Instead of using SELECT statements, read-access to these tables shall be done by using the methods<br/>CL_FINS_ACDOC_UTIL=&gt;GET_T881_EMU <br/>CL_FINS_ACDOC_UTIL=&gt;GET_T881T_EMU<br/>CL_FINS_ACDOC_UTIL=&gt;GET_T882G_EMU.</li>\n<li>From S/4HANA release 1511 onward, the customizing table T001A has been replaced by a database view on new customizing tables. As a consequence, insert, update or delete statements will have no effect on the content that T001A displays. The original old table content can be displayed using database view FINSV_T001A_ORI.</li>\n<li>Due to the fact that Controlling (and other applications) are now unified with the General Ledger, the content of some customizing tables must be in sync with some other customizing tables. For example:</li>\n<ul>\n<li>The fiscal year variant in the company code (table T001) must be identical with the fiscal year variant of the controlling area (table TKA01). Before S/4HANA only the posting periods needed to be identical, but not the special periods.</li>\n<li>For each company code that exists in table T001 there must be an entry in table FINSC_LD_CMP for the leading ledger.<br/>As a consequence inserting or updating records in table T001, for example in customer-developed source code, is critical because such database operations can lead to a difference compared to the content of table FINSC_LD_CMP. <br/>Read-access to table T001 is uncritical in general: the content of this table is still valid. However, the entries in table T001 are only valid for the leading ledger. Now, the posting period variant (T001-OPVAR) and fiscal year variant (T001-PERIV) are maintained also in customizing table FINSC_LD_CMP. In this table the settings are maintained on level company code and ledger. It should be checked whether reading from T001 is still the right approach for the intended purpose.</li>\n<li>The currency type of the controlling area (table field TKA01-CTYP) is also stored in table field FINSC_LD_CMP-CURTPK. The field FINSC_LD_CMP-CURPOSK contains the information whether this currency type is defined as additional FI currency in table/view T001A.<br/>As a consequence updating the content of field CTYP in records of table TKA01 is critical because such database operations can lead to a difference to the content of table FINSC_LD_CMP in fields CURTPK and CURPOSK. <br/>Read-access to table TKA01 is uncritical: The content of this table is still valid.</li>\n<li>Also inserting or deleting records from table TKA02 (assignment of company codes to controlling areas) could cause a mismatch with table FINSC_LD_CMP, because the fields FINSC_LD_CMP-CURTPK and potentially FINSC_LD_CMP-CURPOSK would need to be updated.<br/>Read-access to table TKA02 is uncritical: The content of this table is still valid.</li>\n<li>Inserts and updates of records of the tables T001, TKA01, TKA02, TKA09, TCVPROFD, FCML_MLCO, FINSC_LD_CMP are always critical because there exist some \"hidden\" tables that need to be updated, too. For example tables FINSC_001A_REP, FINSC_CMP_VERSNC,  FINSC_CMP_VERSND. <br/>The logic how these tables are updated or filled are implemented in after-save exits in the corresponding customizing views. For example in customizing views V_001_B, FINSV_LD_CMP, V_TKA02, V_TKA01_GD, V_TKA01_GD, V_TKA02, V_FCML_MLCO_COFI, V_TCVPROFD. You can study the corresponding program logic by using transaction SE54 -&gt; Environment -&gt; Events for these views. The corresponding event is 02 \"After Save\". For view FINSV_LD_CMP this is form after_save_ld_cmp in include LFINS_LEDGER_CUSTF01.<br/>The tables tables FINSC_001A_REP, FINSC_CMP_VERSNC,  FINSC_CMP_VERSND have delivery class L or A and are not transported by customizing transports. Instead they are filled in each system by after-import methods. It is important that you transport the changed table entries using the correct views for which the corresponding after-import methods are registered in transaction SOBJ. For example for transporting entries of tables T001 and FINSC_LD_CMP the views V_001_B and FINSV_LD_CMP shall be used: The entry in the customizing transport should look like this: R3TR VDAT V_001_B and R3TR VDAT FINSV_LD_CMP in order to trigger the after-import methods in the target system!</li>\n<li>The secondary cost elements are not only stored in cost element tables CSKA, CSKB of CO, but also in the tables SKA1 and SKB1 of the G/L account. As a consequence, read-access to SKA1 and SKB1 will in general return also secondary cost elements; this is different to releases before S/4HANA.</li>\n<li>See also note <a href=\"/notes/2192251\" target=\"_blank\">2192251</a> for more details.<br/>These dependencies are the reason why write access (insert, update or delete) to those tables is critical: Creating new entries can lead to inconsistencies. </li>\n</ul>\n<li>The real-time integration CO-FI is obsolete since CO and G/L are now per definition always integrated. The corresponding customizing tables are obsolete. These are tables FAGLCOFIVARC, FAGLCOFICCODEC.</li>\n<li>Also the reconciliation ledger 3A in cost element accounting and the corresponding reconciliation posting (transaction KALC) are obsolete since CO and G/L are now per definition always integrated.</li>\n<li>As a consequence also the account determination for reconciliation postings, transaction OK17 and the optionally used substitution for application CO, call-up point 60 (see transaction OKC9), are obsolete. This missing account determination in S/4HANA might require some changes in your business processes e.g in case you are using CO assessments posting to  secondary cost elements which can now in G/L not be posted to anymore directly by e.g. FB01 and G/L allocations. Using CO distributions (on primary cost elements) instead of CO assessments (on secondary cost elements) might be an option in this case.</li>\n<li>Postings in Controlling on secondary cost elements appear now in the balance sheet reporting in FI with their secondary cost elements: The secondary cost elements are now at the same time G/L accounts: The secondary cost elements can now be displayed in FI for example with transaction FS00. More information on secondary cost elements in the General Ledger and how you can exclude them from G/L reporting you can find in note <a href=\"/notes/2527020\" target=\"_blank\">2527020</a>.</li>\n<li>New General Ledger Accounting is obsolete in S/4HANA. As a consequence also the ledger scenarios are obsolete, table FAGL_LEDGER_SCEN: In S/4HANA all fields are contained in all ledgers. See also note <a href=\"/notes/2405794\" target=\"_blank\">2405794</a> about a program error with respect to this table.<br/>The full list of all customizing tables where incompatible changes were done is contained in note <a href=\"/notes/2192251\" target=\"_blank\">2192251</a>. The note 2192251 explains in details what has changed in the customizing tables.</li>\n</ul>\n<li>In general, the architectural changes in Financials in S/4HANA imply some restrictions and necessary adaptions in customizing. Corresponding checks are delivered with the S/4HANA System Conversion Check framework, see note <a href=\"/notes/2182725\" target=\"_blank\">2182725</a><span class=\"urTxtStd\">.</span><span class=\"urTxtStd\"> The General-Ledger-specific checks for this framework are contained in note </span><a href=\"/notes/2240666\" target=\"_blank\">2240666</a><span class=\"urTxtStd\">. In case this framework issues warnings messages or errors concerning General Ledger, you will find the solution instructions in note </span><a href=\"/notes/2245333\" target=\"_blank\">2245333</a><span class=\"urTxtStd\">.<br/>Note: <br/>- If you want to perform only the checks that are relevant for Accounting (including General Ledger) instead of all checks in this framework, you can do this with note </span><a href=\"/notes/2176077\" target=\"_blank\">2176077</a><span class=\"urTxtStd\">.<br/>- If you want to perform only the checks that are relevant for General Ledger instead of all checks in this framework, you can do this with note </span><a href=\"/notes/2129306\" target=\"_blank\">2129306</a><span class=\"urTxtStd\">.</span></li>\n<li><span class=\"urTxtStd\">In release S/4HANA 1610 parallel buffering with <em>Italian Solution </em>is switched-on for the number range of journal entries, number range object RF_BELEG.</span><span class=\"urTxtStd\">This change is not an incompatible change, but additional gaps in the numbering of journal entries can occur. A new transaction is provided in standard to explain these additional gaps. For details see corresponding FAQ note <a href=\"/notes/2376829\" target=\"_blank\">2376829</a><span class=\"urTxtStd\">.</span></span></li>\n</ol>\n<p><span class=\"urTxtStd\">The attached document <a class=\"urLnk\" href=\"https://i7p.wdf.sap.corp/sap/bc/bsp/sno/ui/attachment.htm?iv_key=002075125900000018652017&amp;iv_version=0001&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F49333537B188F734378A3730B4F2AA4A760E8FF774AAB44C0E72CC8E2F282C49490FC9CA7249AC0CCB0F0D0EA80A0B0E0F76B7CCB6D0750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00&amp;iv_guid=6EAE8B27F6791ED6BDF2C271526B20C7\" id=\"DEFAULT_ATTACHMENT_C_attachments_1_1\" tabindex=\"0\" target=\"_blank\">2015_04_07_EKT_Architecture_final.pdf</a> contains detailed information about the underlying concepts of the Unified Journal Entry. This file is also available via the SAP Learning Hub. For more information about the SAP Learning Hub, click the link below <a href=\"http://service.sap.com/~sapidb/012002523100000886992015E.pdf\" target=\"_blank\">http://service.sap.com/~sapidb/012002523100000886992015E.pdf .</a><br/></span></p>\n<p>Additional information on the changes in S/4HANA you can find in the S/4HANA migration guide in the <a href=\"https://uacp2.hana.ondemand.com/doc/saphelp_sfin200/2.5/en-US/87/2f6152b82bf35fe10000000a423f68/frameset.htm\" target=\"_blank\">SAP help portal</a> .</p>\n<p>There is also a <a href=\"https://performancemanager5.successfactors.eu/sf/learning?destUrl=https%3A%2F%2Fsap.plateau.com%2Flearning%2Fuser%2Fdeeplink_redirect.jsp%3FlinkId%3DCATALOG_SIMPLE_SEARCH%26keywords%3DEKT_SFIN1503SPOC%26fromSF%3DY&amp;company=SAP&amp;_s.crb=ZIyEodDsAW09vONR5KvajjwZA%252fs%253d\" target=\"_blank\">virtual learning room </a>that targets consultants.</p>\n<p> </p>", "noteVersion": 35, "refer_note": [{"note": "2182725", "noteTitle": "2182725 - S4TC Delivery of the SAP S/4HANA System Conversion Checks for SAP S/4HANA 1511 or 1610", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Checks have to be executed before the conversion to SAP S/4HANA, if all preliminary steps in the source system have been performed.</p>\n<p><strong><strong>Note: </strong>The check report delivered via this note was exclusively used for system conversions to SAP S/4HANA 1511 and SAP S/4HANA 1610. As system conversions to SAP S/4HANA 1511 are no longer supported since May 2018 (and respectively to SAP S/4HANA 1610 since May 2019), this report and this note are obsolete and will be removed in the near future.</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note as well as the SAP notes mentioned in the manual activities document.</p>\n<p>For further information please refer to the respective SAP S/4HANA conversion procedure guide.</p>\n<p>The report R_S4_PRE_TRANSITION_CHECKS, which is delivered with this SAP note,</p>\n<ul>\n<li>can be executed <strong>standalone</strong> and calls all available pre–conversion checks which are delivered with the SAP notes mentioned in the manual activities document of this note.</li>\n<li>can be executed as often as required. When called in 'Simulation Mode', nothing is persisted, otherwise the report output is saved as 'Application Log' entry. Use the respective display option on the report selection screen to search for respective application log entries and to display them.</li>\n<li>is called automatically in the conversion procedure by SUM (Software Update Manager) to execute the pre-conversion checks.</li>\n</ul>\n<p>When you execute the pre–conversion checks <strong>standalone </strong>using report R_S4_PRE_TRANSITION_CHECKS:</p>\n<ul>\n<li>We recommend to always choose the option 'Simulation Mode', so that all available pre–conversion checks are executed despite of erroneous or missing pre–check classes.</li>\n<li>We recommend to save the entries on the selection screen of report R_S4_PRE_TRANSITION_CHECKS as a report variant.</li>\n<li>We recommend to execute the report as batch job using the above mentioned report variant.</li>\n<li>You can ignore error messages in the pre–conversion check result list concerning missing pre–conversion check classes. Only when the checks are executed by SUM, information about software components that do not require a pre–conversion check class is available.</li>\n</ul>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed for the very first time, field labels for the selection screen fields are missing, because such texts can not be delivered via correction instruction. But these texts are generated for the current logon language and persisted during this very first report execution.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed <strong>with the option 'Check Class Consistency Check'</strong>, the report only checks if the respective check class methods can be dynamically called, <strong>but the real pre-conversion checks are <span><em>not executed</em></span></strong>.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed and the option <strong>'Pre-Conversion </strong><strong>Check Results' </strong>is selected:</p>\n<ul>\n<li>If the checkbox ‘Simulation Mode’ is selected:</li>\n<ul>\n<li><strong>no</strong> application log entry is persisted, the output is shown on screen (online execution) or in spool (batch execution).</li>\n<li>consistency check errors <strong>are ignored</strong>, all usable checks are executed.</li>\n</ul>\n<li>If the checkbox ‘Simulation Mode’ is not selected:</li>\n<ul>\n<li>The output is only persisted as application log.</li>\n<li>consistency check errors <strong>are not ignored, </strong>checks are only executed if all checks are consistent.</li>\n</ul>\n</ul>\n<p>If one of the dynamically called pre-check classes returns more than 10000 check result lines, a respective error message is written into the check result (and the huge amount of more than 10000 lines is ignored in the result list in order to prevent an internal memory overflow of the used application log functionality). Contact in such a case the pre-check class responsible so that the class code can be corrected. Use the application component that is mentioned in the pre-check result to create an incident.</p>", "noteVersion": 56}, {"note": "2253067", "noteTitle": "2253067 - Object changes for reactivation of G/L Planning", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note contains all object changes for the reactivation of G/L Planning.</p>\n<p>You can find more information about this in SAP Note 2474069.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Missing functionality</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Support package version or correction</p>\n<p>Then execute the program RGBCFL01 (transaction SA38) to regenerate the relevant programs. You must also do this in the production system (after importing the transports from the test system). Make sure that no productive data is posted.</p>", "noteVersion": 20}, {"note": "2354768", "noteTitle": "2354768 - S4TWL - Technical Changes in Material Ledger with Actual Costing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition, higher or equal to on-premise 1610. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ML, Actual Costing, CKM3, CKMLCP, CKMLCPAVR, MLDOC, MLDOC_CCS, MLAST, T001W, MGVUPD, plant, WERKS_D, BWKEY, CKMLQS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Material Ledger Actual Costing has been activated already in the system before the system conversion to SAP S/4HANA.</p>\n<p>You can verify if Material Ledger Actual Costing is active for one or more plants via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO)</p>\n<p>-&gt; Controlling</p>\n<p>  -&gt; Product Cost Controlling</p>\n<p>    -&gt; Actual Costing/Material Ledger</p>\n<p>      -&gt; Actual Costing</p>\n<p>        -&gt; Activate Actual Costing</p>\n<p>        -- Activate Actual Costing</p>\n<p>=&gt; Checkbox 'Act.Costing' (Updating Is Active in Actual Costing)</p>\n<p>When this checkbox is marked, this means that Material Ledger Actual Costing is active in this plant.</p>\n<p>Technically this can be verified via database table T001W, field MGVUPD. 'X' means that Material Ledger Actual Costing is active in this plant.</p>\n<p>In addition, the table T001W shows the link between the plant and the valuation area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With S/4HANA the data model for material ledger data has been changed significantly, especially when actual costing is active.</p>\n<p>The main purpose for the changed data model is:</p>\n<ul>\n<li><span>Simplified Data Model</span></li>\n<li><span>Simplification of Calculation Logic</span></li>\n<li><span>Leverage the Strength of HANA</span></li>\n</ul>\n<p><strong><strong>Business Process related information</strong></strong></p>\n<ul>\n<li><span>Reduce complexity and improve runtime in the Material Ledger Costing Cockpit for Actual Costing</span></li>\n<li><span>Example: 4 separate process steps ('Single-Level Price Determination', 'Multilevel Price Determination', 'Revaluation of Consumption', 'WIP Revaluation' ) are merged to one process step ('Settlement')</span></li>\n<li><span>New 2-dimensional distribution logic to avoids rounding errors</span></li>\n<li>\n<div class=\"O2\">Less \"not-distributed\" values</div>\n</li>\n<li><span>No lock conflicts caused by material movements (in current period)</span></li>\n<li><span>Change of standard price for materials and activities within the period is supported</span></li>\n</ul>\n<p><strong><strong>Required and Recommended Action(s)</strong></strong></p>\n<ul>\n<li>Deactivation of the statistical moving average is not mandatory in SAP S/4HANA, but is nevertheless recommended in order to achieve a significant increase of transactional data throughput for goods movements. It is important to note that the deactivation is not reversible. For further details regarding this matter please see description in SAP Support Note 2267835. With SAP S/4HANA, on-premise edition 1610, this option also includes materials with Moving Average price control and all special stock types. Additional details can be found in SAP Support Note 2338387.</li>\n<li>Separate currency customizing of Material Ledger (transactions OMX2 / OMX3) has become obligatory, Material Ledger is acting on a subset of the currencies defined for Financials. There is no default Material Ledger Type “0000” anymore.</li>\n<li>It is not allowed to use an ML Type that references to currency settings defined in FI or CO (flags “Currency Types from FI” resp. “Currency Types from CO”). Instead you have to define explicitly the currency and valuation types that are relevant for Material Ledger.</li>\n<li>During system conversion material ledger type '0000' (if existing) will be exchanged by new specific material ledger types. (Example Material Ledger Type 9300 for Currency Type 10 + 30)</li>\n<li>Steps to be executed for new valuation areas created after system conversion in S/4HANA: 1. Assign the currency and valuation types that are relevant for Material Ledger using transaction OMX2. 2. Afterwards assign this ML Type to your valuation area using transaction OMX3.</li>\n<li>Before system conversion is started, all Material Ledger costing runs, no matter if actual costing (transaction CKMLCP) or alternative valuation run (transaction CKMLCPAVR) need to be finished (e.g. step 'post closing' successfully executed, no error, no materials with status 'open'). Reason: After system conversion to SAP S/4HANA it will not be possible to do any changes on costing runs created before system conversion. You can create new costing runs for previous periods or for the current period, after data conversion in the new system, but it is important to have no incomplete costing runs, where just some steps have been started. This means, if you are doing a conversion in an open period (e.g. in the middle of a month), do not start a costing run in the old system on the old release, for this open period. You can create the costing run for this period later, after system conversion in the new system.</li>\n<li>It is not allowed to change Material Ledger costing runs, nor to run steps of Material Ledger costing runs during the process of system conversion.</li>\n<li>It is not allowed to activate or deactivate Material Ledger Actual Costing during the process of system conversion for one or more plants.</li>\n</ul>\n<p><strong>Details:</strong></p>\n<p><strong>Simplified Data Model</strong></p>\n<ul>\n<li><strong>Tables MLDOC and MLDOCCCS</strong></li>\n<ul>\n<li>The new Material Ledger Document tables MLDOC and MLDOCCCS replace most former periodic tables (MLHD, MLIT, MLPP, MLPPF, MLCR, MLCRF,<br/>MLKEPH, CKMLPP, CKMLCR, MLCD, CKMLMV003, CKMLMV004, CKMLPPWIP, CKMLKEPH). For more information refer to note 2352383.</li>\n<li>Some of the former periodic tables are still required for the following purposes:</li>\n<ul>\n<li>The Material Ledger Closing Document is stored in the former ML document tables (ML*)</li>\n<li>Standard Price. Periodic Unit Price and Price Control are still managed in table CKLMLCR</li>\n<li>Cost Component Split for Prices is still managed in tables CKMLPRKEKO and CKMLPRKEPH (But the values to Price Type 'B')</li>\n</ul>\n<li>The design of tables MLDOC/ MLDOCCCS allows storing both line item and aggregated data.</li>\n<li>Periodic transactions (goods movements, activity confirmation, etc.) update tables MLDOC/ MLDOCCCS with line item data using the INSERT ONLY principle.</li>\n<li>Material Ledger Period Close creates (run-dependent) settlement records in tables MLDOC/ MLDOCCCS to store the results from Actual Costing. These records are stored under the costing run key (field ‘Run Reference’) so that they are ‘visible’ only by the corresponding costing run. In this way different runs (e.g. a periodic run and an AVR) can store their results independently from each other.</li>\n<li>Important design changes\r\n<ul>\n<li>Single- and multilevel differences are no longer distinguished. In table MLDOC all price/exchange rate differences are stored in fields PRD/KDM. In table MLDOCCCS the CCS for price/exchange rate differences are stored under the CCS types E/F (field MLCCT).</li>\n<li>The CCS is stored in a table format using the cost component (field ELEMENT) as additional key. This allows increasing the number of cost components without modification.</li>\n<li>The CCS for preliminary valuation is always stored</li>\n</ul>\n</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li><strong>Tables MLDOC_EXTRACT and MLDOCCCS_EXTRACT</strong></li>\n<ul>\n<li>The table MLDOC_EXTRACT holds information about quantity and valuation changes. Transaction based updates, like goods movements or invoice receipts, usually update tables MLDOC and MLDOC_EXTRACT in parallel. But table MLDOC_EXTRACT can be compressed. After compression, the table will contain only one entry per cost estimate number, period and currency type valuation view. Table MLDOC_EXTRACT allows fast and efficient calculation of total quantity and total value, by cumulating all records for specific cost estimate number(s). The same is true for table MLDOCCCS_EXTRACT which has been designed for holding cost component information. During period shift (transaction MMPV) the tables MLDOC_EXTRACT and MLDOCCCS_EXTRACT will be compressed automatically for periods older than previous period. Manual compression via program FCML4H_RMLDOC_EXTRACT_COMPRESS or via function module FCML4H_MLDOC_EXTRACT_COMPRESS is possible and recommended in case of very high number of material movements.</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li><strong>Table MLRUNLIST</strong></li>\n<ul>\n<li>\n<p>Table MLRUNLIST replaces the former table CKMLMV011 (Object List for Costing Run), but has some additional functions:</p>\n<ul>\n<li>Materials and activities selected for a costing run are stored under the corresponding run id.</li>\n<li>It manages the period status of materials and activities</li>\n</ul>\n</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>Data Conversion</strong></p>\n<p>In case Material Ledger Actual Costing has been activated already in the system, in one or more valuation areas,  before the system conversion to SAP S/4HANA (when the system is e.g. on release <span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text27-__clone37\">SAP ERP 6.0 or on SAP Simple Finance)</span>, the tables MLDOC, MLDOCCCS, MLDOC_EXTRACT and MLDOCCCS_EXTRACT are filled automatically with data, during Migration Step M10: Migrate Material Ledger Master Data for the valuation areas, where Material Ledger Actual Costing has been activated. The data will be created starting from last period of year before previous year until the current period in the system. All material related goods movements or price changes from previous years will be converted to the MLDOC tables as if the goods movement or the price change has taken place in the last period of year before previous year. The data of the current year and of the previous year will be converted not based on single material documents or price change documents, but there will be one entry per period, per currency type and valuation view, per category (e.g. \"Receipts\"), per process category (e.g. \"Production\") and per production process.</p>\n<p>Data related to ML Alternative Valuation Runs is usually not converted automatically during Migration Step M10. It is possible to convert data related to ML Alternative Valuation Runs with report FCML4H_MIGR_AVR_INVENTORY (To be executed later-on in the S/4HANA system, after migration has been completed.). Only data related to \"COGM costing runs\" (costing runs in accordance with the business function FIN_CO_COGM/multiple valuation of cost of goods manufactured, that were created in the source system prior to the migration) is converted automatically during Migration Step M10: Migrate Material Ledger Master Data.</p>\n<p><strong>Functional changes/improvements</strong></p>\n<p><strong>Material Price Analysis (Transaction CKM3):</strong></p>\n<ul>\n<li>\n<div>No separate view for Cost Components, but integrated in main screen</div>\n</li>\n<li>\n<div>Flag for selection of cost components not relevant for material valuation, or only  cost components relevant for material valuation; by default selection of inventory relevant cost component split. This behavior can be enhanced by implementing SAP Support Note 2467398 (further details are described within this note)</div>\n</li>\n<li>\n<div>Display of WIP Reduction for material; by default WIP Reduction is hidden; If WIP reduction is displayed, both WIP reduction and consumption of WIP for order are shown in opposite sign in different folder.</div>\n</li>\n<li>\n<div>Plan/Actual Comparison is removed in new CKM3</div>\n</li>\n<li>\n<div>Technically, data are retrieved from table MLDOC, MLDOCCCS and MLDOC_EXTRACT and MLDOCCCS_EXTRACT</div>\n</li>\n</ul>\n<p>Remark: Data older than last period of year before previous year (related to current period in the system for a specific company code, at the time of system conversion) cannot be displayed via transaction CKM3 because the 'old' data has not been converted to the MLDOC-tables. To show data older than last period of year before previous year you can use transaction CKM3OLD('Material Price Analysis') or CKM3PHOLD ('Material Price History'). To display single material ledger documents, created before system conversion, you can also use transaction CKM3OLD. The system conversion will not convert single material ledger documents, but there will be one entry per category, process category and procurement alternative/process, with aggregated quantities, amounts and price/currency differences of single material ledger documents. So CKM3 will show data also on this level for migrated data.</p>\n<p>CKM3 will show all details on single material ledger documents for postings created after system conversion.</p>\n<p>CKM3OLD will not show postings created after system conversion</p>\n<p> </p>\n<p><strong>ML Actual Costing Cockpit (Transaction CKMLCPAVR):</strong></p>\n<ul>\n<li>﻿In the transactions CKMLCP and CKMLCPAVR, there is an additional parameter “Application”. This means that the application can be chosen so that it is possible to process Alternative Valuation Runs via the transaction CKMLCP and Actual Costing Runs via the transaction CKMLCPAVR.</li>\n<li>In the toolbar of the transactions CKMLCP and CKMLCPAVR, there is a new button next to the “Display &lt;-&gt; Change”-Button to switch the application from “Costing Run” to “Run Reference” and back.</li>\n<li>When the application is switched to “Run Reference”, a run reference can be created, changed or displayed. A run reference is a reference that contains all settings of an Alternative Valuation Run. It can be used when creating an Alternative Valuation Run but it is only mandatory for creating Alternative Valuation Runs for Parallel COGM. It can also be created for a single period run, for year-to-date run or for a rolling run.</li>\n<li>There are 2 options for creating an AVR:\r\n<ul>\n<li>Create a “Classic AVR” which is the same like before.</li>\n<li>Create an AVR with run reference which means the settings are taken from the run reference and can’t be changed. Only the plants must be chosen.</li>\n</ul>\n</li>\n<li>Some of the programs and therefore also the steps in the section “Processing” have changed. The new steps of an ML costing run are:\r\n<ul>\n<li>Selection (program FCML4H_SELECTION)</li>\n<li>Preparation (program FCML4H_PREPROCESSOR)</li>\n<li>Settlement (program FCML4H_SETTLEMENT)</li>\n<li>Post Closing (program FCML4H_POST_CLOSING)</li>\n</ul>\n</li>\n<li>It is no longer possible to use delta posting runs.</li>\n<li>Additionally to the status of the materials, the status of the activity types is displayed in the section “Processing”. The button “Activity Types” for displaying the activity types value flow has been removed.</li>\n<li>The section “Costing Results” has been removed. It will be replaced by the report FCML4H_RUN_DISPLAY_MATERIALS that displays a material list with status. It will called via the button “Results” in the section “Processing”.</li>\n</ul>\n<p> </p>\n<ul>\n<li>The classical CKMLCP steps <em>Single Level Price Determination, Multilevel Price Determination, Revaluation of Consumption </em>and <em>WIP Revaluation </em>have been replaced by the new step <em>Settlement</em> which essentially does all cost allocations and actual price calculations. Additionally the step <em>Preparation</em> is required to prepare the data (e.g. reading of apportionment structures, actual activity prices, cost sequence determination).</li>\n<li>A change of standard price for materials and activities within the period is now supported</li>\n<li>Easier reprocessing: If a material is reprocessed by settlement the depending materials on higher costing levels which need to be reprocessed are recognized automatically</li>\n<li>Consumption Price Differences (i.e. Price Differences updated in Consumption Folder) are now taken into account</li>\n<li>A new 2-dimensional distribution logic avoids rounding errors. The new ‘CKM3’ will match vertically and horizontally even on level of Cost Components</li>\n<li>Price Limiter Logic is accurate on level of Cost Component Split</li>\n<li>Materials and Activity Types are widely treated as equivalent objects. The cost allocation logic is essentially the same. The same reports (e.g. CKM3, CKMVFM) can be used for both of<br/>them (-&gt; to be enabled yet!)</li>\n<li>Alternative Valuation Run (AVR):\r\n<ul>\n<li>The new AVR do no longer copy the data into a separate key area. (Only the settlement records are stored under the AVR ‘Run Reference’, see above). In particular, <br/>the cumulation of several period is done ‘On-the-fly’.</li>\n<li>The former step <em>Cumulate Data</em> is no longer needed</li>\n<li>The complex rescale logic, which adjusts all materials data to a common preliminary AVR price, is no longer needed.</li>\n<li>All AVR can now handle WIP</li>\n<li>Post Closing:\r\n<ul>\n<li>Post Closing uses a ‘push logic’ (in accordance to the new Settlement). This means that the price differences rolled from an input to the output are now posted <br/>in the closing document of the input (material/activity)</li>\n<li>For activity types (and business processes) a new posting has been introduced. See the documentation in transaction OBYC for the new transaction key PRL: <br/><em>Price differences for activity types or business processes. During settlement in the material ledger, these price differences are posted from cost centers <br/>(Transaction/Account Modification GBB/AUI) to the material ledger (transaction PRL) and then further allocated to the receivers (finished products/WIP).</em></li>\n</ul>\n</li>\n</ul>\n</li>\n</ul>\n<p> </p>\n<p><strong>Restrictions</strong></p>\n<ul>\n<li>AVR Delta Posting Logic is not available</li>\n<li>\n<p>The BAdI BADI_ENDING_INV for external valuation is currently not available. Static external valuation (for ending inventory or cumulated inventory) based on table CKMLPR_EB is available with note 2613656</p>\n</li>\n<li>The BAdI CKMLAVR_SIM has been replaced by the BAdI FCML4H_MODIFY_VALUES. For more information refer to the following note:<br/>    2876478 Documentation for BAdI FCML4H_MODIFY_VALUES<br/><br/></li>\n<li>The BAdI CKML_SETTLE (see note 527278) is currently not available.<br/><br/></li>\n<li>Material Ledger Costing Runs and Alternative Valuation Runs, which have been created before the system conversion cannot be changed nor reversed any more after the conversion to SAP S/4HANA<br/><br/></li>\n<li>Material Ledger Valuated Quantity Structure (Transaction CKMLQS) is available after implementing the following notes (, or related support package): <br/>    2378468 Enable CKMLQS based on table MLDOC<br/>    2495950 CKMLQS: Connection to CKM3</li>\n</ul>", "noteVersion": 29}, {"note": "2292381", "noteTitle": "2292381 - Changed behavior for materials with transaction-based price determination", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You face one of the following situations for a Material Ledger active material (MLMAA = 'X') with price determination \"transaction-based\" (MLAST = '2'):</p>\n<ul>\n<li>\n<div>you miss an Material Ledger update document in CKM3 for a supplier invoice, production order settlement or a material debit/credit</div>\n</li>\n<li>you miss actual costing related data in a Material Ledger update document</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MLAST = '2'</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Beginning with S/4HANA Finance on-premise edition 1503 several simplifications were introduced for Material Ledger active materials with price determination \"transaction-based\" (MLAST = '2'). The following changes were made:</p>\n<ul>\n<li>Actual costing relevant key figures are not updated in tables CKMLPP and CKMLCR.</li>\n<li>No update document data are persisted in tables MLHD, MLIT, MLPP, MLPPF, MLCR and MLCRF. Instead read access compatibility views V_MLHD, V_MLIT, V_MLPP and V_MLCR are assigned in DDIC to these tables. The compatibility views reconstruct inventory valuation relevant data based on postings on inventory accounts recorded in table ACDOCA (KTOSL = 'BSX'). No read access compatibility views exist for tables MLPPF and MLCRF, means no actual costing relevant key figures were reconstructed at all.</li>\n<li>Transaction CKM3 \"Material Price Analysis\" provides only the view \"Price History\", the view Price Determination Structure\" is not available.</li>\n</ul>\n<p>The read access compatibility views for Material Ledger update documents do not support following fields for materials with price determination \"transaction-based\" (MLAST = '2'):</p>\n<ul>\n<li>Table MLHD: field PVERS.</li>\n<li>Table MLIT: fields BWMOD, PTYP_KAT, PTYP_BVALT, PTYP_PROC, BEWARTGRP and CSPLIT.</li>\n<li>Table MLPP: fields BKLAS, XKONCHK, STATUS, MODIF, XCKMM, XCLEAN and XAVRSIM.</li>\n<li>Table MLCR: fields BNBTR, EXBWR, VKWRT, VKWRA, EXVKW and VKSAL_OLD.</li>\n</ul>\n<p>The read access compatibility views  for materials with price determination \"transaction-based\" (MLAST = '2') evaluate only postings on inventory accounts (ACDOCA-KTOSL ='BSX'), means that no data are reconstructed for business transactions that did not post on inventory account. This is the case e.g. for supplier invoices, production order settlements and material debit/credit posted for materials with price control 'S' (standard price) or posted for materials with price control 'V' (moving average) and zero-stock situation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Information about changed system behavior provided.</p>\n<p>See also section to Material Ledger in release scope information (note 2119188).</p>", "noteVersion": 1}, {"note": "2129306", "noteTitle": "2129306 - Check Customizing Settings Prior to Upgrade to S/4HANA Finance or S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The program FINS_MIG_PRECHECK_CUST_SETTNGS provided by this note checks the consistency of your ledger, company code and controlling area settings to determine if a migration to SAP S/4HANA  is possible.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Journal Entry, Ledger, Parallel Valuation, Unified Journal Entry.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This report checks if your settings can automatically be migrated to the new customizing tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please perform the following steps in your classic ERP prior to the upgrade to S/4HANA Finance or S/4HANA:</p>\n<ol>\n<li>First apply the corrections that are provided in note 2240666: The note 2240666 contains the ABAP check class that is called by the ABAP program FINS_MIG_PRECHECK_CUST_SETTNGS.</li>\n<li>Then apply the corrections of this note 2129306 to create the report.</li>\n</ol>\n<p>Afterwards you can start the program FINS_MIG_PRECHECK_CUST_SETTNGS to perform the checks. In case the result list of this program contains error messages, you can find information about root cause and potential solution in note 2245333.</p>\n<p>The program FINS_MIG_PRECHECK_CUST_SETTNGS shall be started in all clients &lt;&gt; 000: In client 000 no checks will be performed, since the customizing content in client 000 will be overwritten anyway by the sFIN content during the upgrade to sFIN.</p>\n<p>After your system was upgraded to S/4HANA Finance or S/4HANA, please check whether the corrections of note 2209641 are relevant, since the report can also be executed after the upgrade.</p>", "noteVersion": 23}, {"note": "2245333", "noteTitle": "2245333 - Pre-Transition Checks for FIN: Error Messages and Possible Solutions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are getting messages / error messages from programs that are checking the prerequisites whether a migration to an S/4HANA or S/4HANA Finance 1503 release (or higher) is possible.</p>\n<p>You are either<br/>- manually executing a check program or<br/>- the check program is called during a technical system conversion from the SUM tool (Software Upgrade Manager)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ABAP class CLS4H_CHECKS_FI_GL, CLS4SIC_FI_GL, ZFINS_MIG_PRECHCK_CUST_SETTNGS, FINS_MIG_PRECHCK_CUST_SETTNGS, S4TC FIN GL, Journal Entry, Ledger, Parallel Valuation, Unified Journal Entry.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The <em>Pre-Transition Checks for FIN </em>are checking whether your customizing settings can automatically be migrated to the new S/4HANA customizing tables.</p>\n<p>These checks need to be performed <span>before the upgrade</span>.</p>\n<ul>\n<li>For conversion to S/4HANA Finance (aka sFIN):<br/>You can use transaction <strong>FINS_MIG_PRECHECK</strong> or report <strong>FINS_MIG_PRECHECK_CUST_SETTNGS</strong> (refer to SAP Note <a href=\"/notes/2129306\" target=\"_blank\">2129306</a>) to perform the pre-checks for General Ledger, the Check Sub-ID cannot be displayed in the result list. In this case, the message text should be used to find the documentation of the issue.<br/> </li>\n<li>For conversion to releases before SAP S/4HANA 1709:<br/>You can use the S/4HANA System Conversion Checks to perform the pre-checks for General Ledger. Refer to SAP Note <a href=\"/notes/2182725\" target=\"_blank\">2182725</a> for the corresponding check framework and to SAP Note <a href=\"/notes/2240666\" target=\"_blank\">2240666</a> for the FIN-specific checks.<br/><br/>The FIN-specific checks are -in contrary to checks for applications other than FIN- <strong>not</strong> performed during the technical system conversion (SUM tool). This means that error messages occurring in these pre-transition checks will <strong>not</strong> prevent the upgrade from a technical point of view. For example, the intention of some of the error messages is to inform the customer that some changes in customizing will be required after converting (upgrading) the system to S/4HANA.<br/> </li>\n<li>For conversions to SAP S/4HANA 1709 onwards:<br/>The checks are also performed during the technical system conversion (SUM tool). If error messages occur in these pre-transition checks during the system conversion, they can be skipped: They are sent by the system with <span>return code = 7</span> which means \"Skippable Error\", not \"Error\". <br/>The intention of messages with return code 7 is just to inform the customer that some changes in customizing will be required <em>after</em> upgrading to S/4HANA. They shall not block the upgrade: The upgrade can continue despite messages with return code 7 occurred.<br/>Note that after the upgrade the errors will be sent again when the following customizing activity is performed, see transaction SPRO: <em>Conversion of Accounting to SAP S/4HANA -&gt; Preparations and Migration of Customizing -&gt; Check Customizing Settings Prior to Migration</em>.<br/><br/>If error messages occur with <span>return code = 8</span> which means critical \"Error\", then SUM stopps. Normally this situation should never occur if you have executed report /SDF/RC_START_CHECK before SUM. It can only happen, if the report /SDF/RC_START_CHECK was started for another release and/or for another support package than the technical system conversion via SUM tool at a later stage! Also maintained exceptions will only work if the technical system conversion via SUM tool is started for the same release/support package then the report /SDF/RC_START_CHECK was executed for.<br/>If the database is locked because of SUM technology, please reset SUM, do the application specific changes and start SUM again.<br/><br/>Implement the SI-Check Framework in your system. The first version of this framework is provided by SAP Note <a href=\"/notes/2503309\" target=\"_blank\">2503309</a>. Further versions will be delivered by other SAP Notes.<br/><br/>Evaluate or perform the checks with report <strong>/SDF/RC_START_CHECK</strong>. The item ‘Financials - General Ledger’ (ID <em>SI1_FIN_GL</em>) provides the pre-checks for General Ledger. In the detail check results, each finding is assigned to a Check Sub-ID. The Check Sub-ID can be used to find the documentation of the identified issue in the list below. For each issue the potential root cause is explained and - if possible - a recommendation how to resolve it, is provided.<br/>  <br/>The report /SDF/RC_START_CHECK performs the checks in all clients of the system except clients 000 and 066: The customizing settings of clients 000 and 066 are not checked, because client 000 is a technical client that contains the SAP-delivered default customizing content and client 066 is the Early Watch Client that also does not need to be checked. As a consequence there is also no need for a customer to correct any customizing settings in those two clients. The SUM tool performs the checks in the same way.<br/>The Check Sub-IDs FI_GL_AGG_SEVERITY, FI_GL_START and FI_GL_END are used to group the check results per client. </li>\n</ul>\n<p> </p>\n<p><span><strong>Grouping of check results per client</strong></span></p>\n<p><strong><em>Check Sub-ID: FI_GL_AGG_SEVERITY, Message: Maximum severity of occurred messages for client xxx. See SAP Note 2245333 for possible solutions</em></strong></p>\n<p>This check provides the aggregated severity of the checks for one specific client. Refer to subordinated checks for details.<br/><br/></p>\n<p><strong><em>Check Sub-ID: FI_GL_START, Message: Begin of check results for client xxx</em></strong></p>\n<p>This check sub-id represents the starting point of the check results for one specific client. <br/><br/></p>\n<p><strong><em>Check Sub-ID: FI_GL_END, Message: End of check results for client xxx</em></strong></p>\n<p>This check sub-id represents the end point of the check results for one specific client.<br/><br/><br/></p>\n<p><span><strong>Issues and recommendations</strong></span></p>\n<p><strong><em>Check Sub-ID: FI_GL_01, Message: Currency types for FI and CO do not exist (Valuation Area XX)</em></strong></p>\n<p>Are currency types of the company code configured at all in the valuation area of ML?</p>\n<p>For each valuation area, currency types must be configured. The currency types are assigned via the Material-Ledger-Type. The Material-Ledger-Type is then assigned to the valuation area (transaction OMX3). The message indicates that currency types of the company code had not been found maintained in the valuation area. The check will look for currency types maintained for the company code as follows:</p>\n<ul>\n<li>Currency types in FI (original table T001A, in S/4HANA the content can be displayed via view FINSV_T001A_ORI)</li>\n<li>Currency types of the CO area, the company code is assigned to. The relevant settings can be found in transaction OKKP.<br/>If a currency and valuation profile is maintained for the CO area and if the flag ‘Active’ is set, the currency types will be taken from the currency and valuation profile. Otherwise the currency type is taken from the controlling area currency type field. If the flag ‘Diff. CCode Currency’ is set, currency type 10 is added by default.</li>\n</ul>\n<p><em><br/><strong>Check Sub-ID: FI_GL_02, Message: Currency types for ML do not exist (Valuation Area XX)</strong><br/></em></p>\n<p>Are currency types for the valuation area of ML configured at all?</p>\n<p>For each valuation area, currency types must be configured. The currency types are assigned via the Material-Ledger-Type. The Material-Ledger-Type is then assigned to the valuation area (transaction OMX3). The message indicates that there are no currency types found for the valuation area at all. In the customizing for the material ledger (transaction OMX2) the flags ‘CT from FI’, ‘CO CrcyTyp’ and ‘Manual’ define, how currency types are identified.</p>\n<ul>\n<li>If flag ‘CT from FI’ is set, the currency types defined as additional local currencies for the company code are used (transaction OB22).</li>\n<li>If flag ‘CO CrcyTyp’ is set, the currency types from the Currency and Valuation Profile are used.</li>\n<li>If flag ‘Manual’ is set, the currency types must be maintained in transaction OMX2 per Material-Ledger-Type.</li>\n</ul>\n<p><em><strong><br/>Check Sub-ID: FI_GL_03, Message: Ledger XX is being used productively. Refer to SAP Note 2277580<br/></strong></em></p>\n<p>Is one of ledgers 1L, 1D, 0D or 0M used productively?</p>\n<p>The error message is only relevant in case you are going to upgrade to S/4HANA 1511. By mistake SAP has delivered a deletion transport with S/4HANA OnPremise 1511 SP0 and SP1 that will delete these ledgers and their customizing settings from some customizing tables. Customizing table T881 is affected as well. This table is critical since it has delivery class E which means that the deletion transport is not only imported in client 000 but is cascaded into all clients. SAP note <a href=\"/notes/2277580\" target=\"_blank\">2277580</a> explains the actions which must be performed.<br/><br/><br/></p>\n<p><em><strong>Check Sub-ID: FI_GL_04, Message: Activate the company code validation (CO area XXXX / FiscYr YYYY)</strong></em></p>\n<p>Is the indicator company code validation set in the controlling area?</p>\n<p>When a posting is made in a company code in S/4HANA, then the assigned cost center, internal order etc. <strong>must</strong> belong to the same company code (with the exception of statistical cost assignments which can also belong to another company code). This is a necessary restriction because of the shared database tables in component FI-GL (FI general ledger) and CO (Controlling) for postings. To ensure this, set the indicator \"Company Code Validation\" (TKA00-RKBUK) in the year-dependent settings in transaction OKKP: You can find this indicator in transaction OKKP in section \"Activate components/control indicators\". The indicator has the field label \"CoCd Validation\".<br/>This restrictions results from the requirement that General Ledger data and CO data shall be reconciled at all time. This restriction did already apply to ERP systems if either the reconciliation ledger or real-time CO-FI reconciliation were used.</p>\n<p>Possible workarounds in S/4HANA that enable the customer to keep on performing this type of 'cross-company' postings are described in note <a href=\"/notes/2450376\" target=\"_blank\">2450376</a>.</p>\n<p>Note:</p>\n<ul>\n<li>After the upgrade to S/4HANA, the customer performs a migration of the transactional data to S/4HANA. During this migration, also the existing postings are migrated: The postings of FI (table BSEG etc.) and CO (table COEP etc.) are unified into the universal journal (table ACDOCA). If the company code is different in a posting between FI and CO, the migration program generates additional line items in table ACDOCA that invert the balance of the CO line item. This ensures that the FI balances are not changed  by the migration. The migrated CO items are still visible in CO compatibility reporting. For more detailed information please see note 2408083 point 9 and the PDF attached in note 2408083.</li>\n<li>With former classical ERP releases, there were some controlling areas delivered as default customizing content where this indicator is not set. For example, controlling areas ES01, HU01, MCA, SLE0. You will need to set this indicator manually for the affected controlling areas even if you are not using these controlling areas.</li>\n<li>The creation and usage of new accounting objects are necessary as you can't change the company code in accounting objects with posted data.</li>\n</ul>\n<p><br/><em><strong>Check Sub-ID: FI_GL_05, Message: Activate control indicator \"All Currencies\" (CO area XXXX / FiscYr YYYY)</strong></em></p>\n<p>Is the indicator all currencies set in the controlling area?</p>\n<p>In GL all postings require a transaction currency. As a consequence, in S/4HANA CO postings require a transaction currency, too. Setting the indicator ‘All Currencies’ (TKA00-ALLEW) in the year-dependent settings in transaction OKKP is recommended. This indicator can be found in transaction OKKP in section \"Activate components/control indicators\". If this indicator is not set, the system sends a warning message to inform the user, that in CO postings a transaction currency is recorded despite this indicator is not set. Often the indicator ‘All Currencies’ is read-only in transaction OKKP because old data exist. So this indicator cannot be set by a user. The warning message can be ignored in this case.</p>\n<p><em><strong><br/>Check Sub-ID: FI_GL_06, Message: Business function \"Parallel Valuation of COGM\" is active in XXXX. Migration is not possible</strong></em></p>\n<p>Parallel Valuation of Costs of Goods Manufactured (COGM) is supported from release S/4HANA 1610 support package 1 on. But only the parallel ledger solution is supported, <span>not the parallel account solution</span>.</p>\n<p>If you are using the parallel ledger solution in COGM you can ignore this message that is raised by the customizing consistency check and you can go on with the migration.<br/><br/>If you are using the parallel account solution in COGM, thus a not-supported function in S/4 at the moment, productively, then you should not upgrade to S/4HANA.<br/><br/>The Sub-ID FI_GL_06 checks whether COGM is active. It is checked whether an active currency and valuation profile with valuation type 5, 6 or 7 exist. You can check the active status of the currency and valuation profile with transaction 8KEQ and the content with transaction 8KEM.<br/>! Please check the following: Are all COGM valuation types that you want to use configured in 1. the currency and valuation profile (TA 8KEM), 2. as CO-version (TA OKEQ) and 3. in COGM 'Set Up Parallel Valuation of Cost of Goods Manufactured for Company Codes' (TA FCML0)? For example, a missing entry in the currency and valuation profile will lead to error FINS_ACDOC_CUST349 after the migration. Please contact your consultant or SAP Support to preferably overcome the situation <span>before</span> migration.</p>\n<p>In case you are using the parallel account solution, you might also get the Sub-IDs FI_GL_32 or FI_GL_33. Please see details below.</p>\n<p>Still supported is the assignment of accounts to Accounting Principles and the Accounting Principle Check in Document. This can be used without COGM, but in order to see and use the IMG transactions it is required to activate the COGM Business Function.</p>\n<p> </p>\n<p><em><strong>Check Sub-ID: FI_GL_07, Message: Transfer prices is active in CO area XXXX. Migr. is possible to certain rel.</strong></em></p>\n<p>Parallel Valuations, which means Profitcenter Valuation (Transfer Prices) or Group Valuation, are supported from release S/4HANA Finance 1605 on.</p>\n<p>To which release are you planning to migrate?<br/>If parallel valuations are active and you want to migrate to a release where Parallel Valuations are released, then you can ignore the warning and can go on with the migration. Otherwise you should not upgrade to S/4HANA.<br/>You can check whether a currency and valuation profile is activated for a controlling area with transaction 8KEQ.</p>\n<p>Are you planning to use transfer prices in S/4HANA?<br/>If yes, please have a look at note 2882025 \"Multiple valuation approaches/transfer prices in SAP S/4HANA, on-premise edition\".<br/><br/>If no, please consider the following: As you can't deactivate Parallel Valuations in S/4HANA currently ( -&gt; see note 2882025), you have to deactivate the function in the ECC environment. In this case please have a look at note 175758 \"Deactivating transfer prices/multiple valuation approaches\".</p>\n<p> </p>\n<p><em><strong>Check Sub-ID: FI_GL_08, Message: CO version XXXX/YYYY is an ABC delta version and cannot be used in sFIN. Migration is not possible</strong></em></p>\n<p>ABC Delta Versions are <strong>not</strong> supported in S/4HANA. The migration of this functionality is not possible. For additional information please have a look at note 2270408 \"S4TWL - Activity-Based Costing\".</p>\n<p>The Sub-ID FI_GL_08 is a skippable error but will lead to the error FGL_PRE_CHECK 010 after the upgrade.</p>\n<p><span>Case A) If you are using ABC Delta Versions in ECC productively or if you used ABC Delta Versions in the past and the corresponding customizing of the CO delta versions still exist (table TKA09 - field REFVS filled)</span><br/>PREVIOUS RECOMMENDATION: you can either skip this message during the upgrade (Skippable Error - return code 7). Or you can delete the information of the reference version and therefore get rid of the error message in ECC. Nevertheless we advice to keep the version customizing for ABC Delta Versions in the system. Data of ABC Delta Versions should not be migrated later on if version is not defined in customizing 'Define Ledger for CO Version'. Possible errors in data migration regarding ABC delta versions can be accepted (e.g. R21 - FINS_FI_MIG 122).<br/>In order to delete the information of the reference version go to the version maintenance (transaction OKEQ or OKEVN) -&gt; Controlling Area Settings for the version and scroll to the right side. Delete the content of field Reference version. To do so you have to de-flag the 'Actual' flag too.</p>\n<p>CURRENT RECOMMENDATION: If in S/4HANA old data of ABC delta versions exist in CO tables and the version customizing exist, this might lead to problems in the migration of data. Possible errors in data migration regarding ABC delta versions can be accepted, but depending on the migration phase not only one error is thrown but several ones (e.g. if checks on balances are done - error per balance). Because of this the current recommendation is to delete the existing data for this ABC Delta versions and also the ABC delta versions customizing <strong>in ECC!</strong> Please follow note 3126356. If you have problems please open a SAP incident - component CO-OM-ABC.<br/> <br/><span>Case B) If you get Sub-ID FI_GL_08 for a version that is not really used </span><span>(test version which has an entry for field 'reference version' by accident)</span></p>\n<p>We recommend to delete existing data and the complete version in ECC. Please follow note 3126356. If you have problems please open a SAP incident - component CO-OM-ABC.</p>\n<p><em><strong>Check Sub-ID: FI_GL_09, Message: BAdI FCOM_EXT_LEDGER has been implemented. Migration not possible</strong></em></p>\n<p>Are User Exits or BAdIs implemented that are possibly incompatible with the ledger concept of S/4HANA?</p>\n<p>This check affects the BAdI FCOM_EXT_LEDGER. This BAdI cannot be used in S/4HANA, since parallel ledgers in CO are not yet supported in S/4HANA.</p>\n<p><em><strong><br/>Check Sub-ID: FI_GL_10, Message: BAdI FCOM_VORGN_4_EXT_LDR has been implemented. Migration might not be possible<br/></strong></em></p>\n<p>Are User Exits or BAdIs implemented that are possibly incompatible with the ledger concept of S/4HANA?</p>\n<p>This check affects the BAdI FCOM_VORGN_4_EXT_LDR. Implementations for this BAdI are only forbidden if also the BAdI FCOM_EXT_LEDGER is implemented. Otherwise they are uncritical.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_11, Message: BAdI DIP_AD010002_SEL has been implemented. Migration might not be possible</strong></em></p>\n<p>Are User Exits or BAdIs implemented that are possibly incompatible with the ledger concept of S/4HANA?</p>\n<p>This check affects the BAdI DIP_AD010002_SEL. If you don't have customer own implementations but only standard implementations PS_INTERCOMPANY_SEL and/or PS_TIME_EXP_SEL exist, you can ignore the warning message. In case you have a customer own implementation, please pay attention to the following:</p>\n<p>In S/4HANA this BAdI does not support records with WRTTP &lt;&gt; '04' any more. In S/4HANA implementations of this BAdI are forbidden which modify the table ET_COSL in such a way that also records with WRTTP &lt;&gt; '04' are selected. That is, no records may be appended to table ET_COSEL with ET_COSEL-FIELD = 'WRTTP' where ET_COSEL-SIGN/OPTION/LOW/HIGH are filled in a way so that records with WRTTP &lt;&gt; '04' would be selected.</p>\n<p><em><strong>Check Sub-ID: FI_GL_12, Message: Customer-Exit AD010002 (ABAP Report cd_customer_exit_ad010002) has been implemented. Migration might not be possible</strong></em></p>\n<p>Are User Exits or BAdIs implemented that are possibly incompatible with the ledger concept of S/4HANA?</p>\n<p>This check affects the Customer Enhancement AD010002 (EXIT_SAPLAD1C_002 / Include ZXAD1U03).</p>\n<p>In S/4HANA this Customer-Exit does not support records with WRTTP &lt;&gt; '04' any more:<br/>In S/4HANA implementations of this Customer-Exi are forbidden which modify the table ET_COSL in such a way that also records with WRTTP &lt;&gt; '04' are selected. That is, no records may be appended to table ET_COSEL with ET_COSEL-FIELD = 'WRTTP' where ET_COSEL-SIGN/OPTION/LOW/HIGH are filled in a way so that records with WRTTP &lt;&gt; '04' would be selected.</p>\n<p><em><strong><br/>Check Sub-ID: FI_GL_13, Message: Company code XXXX and contr. area YYYY have different fiscal year variants VV and WW<br/></strong></em></p>\n<p>Are the fiscal year variants in the company code master data identical with the fiscal year variant of the controlling area?</p>\n<p>In classical ERP systems, it was allowed that they differ in the number of special periods. But this difference is not allowed in S/4HANA anymore, since FI and CO are sharing the same database table for storing postings (table ACDOCA): The fiscal year variants must be identical in FI-GL and CO.</p>\n<p>If different fiscal year variants are used between FI and CO, then do the following: Either change the fiscal year variant in the company code by using transaction SM30 for view V_001_B or change the fiscal year variant in the controlling area by using transaction OKKP.</p>\n<p>You must use the fiscal year variant which has the higher number of special periods. Otherwise your postings might not fit to the customizing any more.</p>\n<p>Example: If FI uses K3 and CO uses K4, then replace K3 with K4 in FI. If you would enter K3 in CO then all the CO postings that have already been done in the special period number 4 should actually not exist according to the new fiscal year variant K3 - which would be a mismatch.</p>\n<p>Note: With some former classical ERP releases, there was some default customizing delivered where the fiscal year variants differ between FI and CO, for example company code CN01 and controlling area CN01. For company code CN01 it is recommended to enter the value K4 in the company code (view V_001_B).<br/><br/><br/></p>\n<p><em><strong>Check Sub-ID: FI_GL_14, Message: Functional area derivation logic will change in S/4HANA and S/4HANA Finance</strong></em></p>\n<p>Is the 'old' logic for functional area derivation (call-up point 0005) still used? No, the substitution FI/0005 is not supported any longer in S/4HANA.</p>\n<p>In S/4HANA the functional area is derived in posting transactions when the user presses \"Enter\". As substitution for deriving the functional area, substitution FI/0006 can be used in S/4HANA, see transaction</p>\n<ul>\n<li>GGB1 'Change substitutions',</li>\n<li>OBBZ 'Activate substitution for company code' (old call-up point 0005),</li>\n<li>SM30 for view V_T001Q_FI_0006 'Activate substitution for company code' (new call-up point 0006).</li>\n</ul>\n<p>The 'old' derivation logic from releases before New GL worked differently: The functional area was derived as late as at \"Save\" and the substitution FI/0005 was used.</p>\n<p>If you are using substitution FI/0005, then you will need to manually create substitution FI/0006 entries and activate it in the relevant company codes. An automatic migration of substitution FI/0005 to FI/0006 is unfortunately not possible. You can already create new substitution FI/0006 entries before you migrate to S/4HANA.</p>\n<p>Technically, in S/4HANA the indicator FAGL_ACTIVEC-FAREA_FROM_COBL is set.<br/><br/><br/></p>\n<p><em><strong>Check Sub-ID: FI_GL_15, Message: Substitution FI/0005 will be replaced by FI/0006</strong></em></p>\n<p>Is the 'old' logic for functional area derivation (call-up point 0005) still used? No, the substitution FI/0005 is not supported any longer in S/4HANA.</p>\n<p>In S/4HANA the functional area is derived in posting transactions when the user presses \"Enter\". As substitution for deriving the functional area, substitution FI/0006 can be used in S/4HANA, see transaction</p>\n<ul>\n<li>GGB1 'Change substitutions',</li>\n<li>OBBZ 'Activate substitution for company code' (old call-up point 0005),</li>\n<li>SM30 for view V_T001Q_FI_0006 'Activate substitution for company code' (new call-up point 0006).</li>\n</ul>\n<p>The 'old' derivation logic from releases before New GL worked differently: The functional area was derived as late as at \"Save\" and the substitution FI/0005 was used.</p>\n<p>If you are using substitution FI/0005, then you will need to manually create substitution FI/0006 entries and activate it in the relevant company codes. An automatic migration of substitution FI/0005 to FI/0006 is unfortunately not possible. You can already create new substitution FI/0006 entries before you migrate to S/4HANA.</p>\n<p>Technically, in S/4HANA the indicator FAGL_ACTIVEC-FAREA_FROM_COBL is set.<br/><br/><br/></p>\n<p><em><strong>Check Sub-ID: FI_GL_16, Message: Deactivate subst. FI/0005 using transaction OBBZ: Set Activtn Level = 0</strong></em></p>\n<p>Is the substitution FI/0005 still active? This check is performed in S/4 HANA only.</p>\n<p>In S/4HANA the functional area is derived in posting transactions when the user presses \"Enter\". As substitution for deriving the functional area, substitution FI/0006 can be used in S/4HANA, see transaction</p>\n<ul>\n<li>GGB1 'Change substitutions',</li>\n<li>OBBZ 'Activate substitution for company code' (old call-up point 0005),</li>\n<li>SM30 for view V_T001Q_FI_0006 'Activate substitution for company code' (new call-up point 0006).</li>\n</ul>\n<p>The 'old' derivation logic from releases before New GL worked differently: The functional area was derived as late as at \"Save\" and the substitution FI/0005 was used.</p>\n<p>If you are using substitution FI/0005, then you will need to manually create substitution FI/0006 entries and activate it in the relevant company codes. An automatic migration of substitution FI/0005 to FI/0006 is unfortunately not possible. You can already create new substitution FI/0006 entries before you migrate to S/4HANA.</p>\n<p>Technically, in S/4HANA the indicator FAGL_ACTIVEC-FAREA_FROM_COBL is set.<br/><br/><br/></p>\n<p><em><strong>Check Sub-ID: FI_GL_17, Message: BAdI 'FAGL_COFI_ACCOUNT_SB' has been implemented. This BAdI will not be processed anymore</strong></em></p>\n<p>Are BAdIs for the real-time integration CO-FI implemented?</p>\n<p>These are not processed any more in S/4HANA, since the real-time integration CO-FI is obsolete: In S/4HANA the CO document is identical to the FI document. As a consequence, the system cannot support BAdIs anymore which would prevent the FI doc from being created or which would modify the FI doc only without modifying the CO document. Affected BAdIs:</p>\n<ul>\n<li>FAGL_COFI_ACCIT_MOD<br/>Online Update CO-&gt;FI:Change CO/FI Doc. LI Before Summarizatn</li>\n<li>FAGL_COFI_LNITEM_SEL<br/>Online Update CO-&gt;FI: Select CO/FI Document Line Items</li>\n<li>FAGL_COFI_ACCOUNT_SB<br/>CO-FI Integration: Extended Account Determination (Actual/Plan)</li>\n<li>FAGL_COFI_ACDOC_MOD<br/>Online Update CO-&gt;FI: Change Accounting Document Before Posting</li>\n</ul>\n<p><em><strong><br/>Check Sub-ID: FI_GL_18, Message: BAdI ' 'FAGL_COFI_ACDOC_MOD' ' has been implemented. This BAdI will not be processed anymore<br/></strong></em></p>\n<p>Are BAdIs for the real-time integration CO-FI implemented?</p>\n<p>These are not processed any more in S/4HANA, since the real-time integration CO-FI is obsolete: In S/4HANA the CO document is identical to the FI document. As a consequence, the system cannot support BAdIs anymore which would prevent the FI doc from being created or which would modify the FI doc only without modifying the CO document. Affected BAdIs:</p>\n<ul>\n<li>FAGL_COFI_ACCIT_MOD<br/>Online Update CO-&gt;FI:Change CO/FI Doc. LI Before Summarizatn</li>\n<li>FAGL_COFI_LNITEM_SEL<br/>Online Update CO-&gt;FI: Select CO/FI Document Line Items</li>\n<li>FAGL_COFI_ACCOUNT_SB<br/>CO-FI Integration: Extended Account Determination (Actual/Plan)</li>\n<li>FAGL_COFI_ACDOC_MOD<br/>Online Update CO-&gt;FI: Change Accounting Document Before Posting</li>\n</ul>\n<p><span>Note that in postings from Controlling also the substitutions in FI are not processed any more in S/4HANA for the same reason.<br/>These are the substitutions for application area FI, call-up point 1, 2 and 3.</span></p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_19, Message: BAdI XXXX has been implemented. This BAdI will not be processed anymore</strong></em></p>\n<p>Are BAdIs for the real-time integration CO-FI implemented?</p>\n<p>These are not processed any more in S/4HANA, since the real-time integration CO-FI is obsolete: In S/4HANA the CO document is identical to the FI document. As a consequence, the system cannot support BAdIs anymore which would prevent the FI doc from being created or which would modify the FI doc only without modifying the CO document. Affected BAdIs:</p>\n<ul>\n<li>FAGL_COFI_ACCIT_MOD<br/>Online Update CO-&gt;FI:Change CO/FI Doc. LI Before Summarizatn</li>\n<li>FAGL_COFI_LNITEM_SEL<br/>Online Update CO-&gt;FI: Select CO/FI Document Line Items</li>\n<li>FAGL_COFI_ACCOUNT_SB<br/>CO-FI Integration: Extended Account Determination (Actual/Plan)</li>\n<li>FAGL_COFI_ACDOC_MOD<br/>Online Update CO-&gt;FI: Change Accounting Document Before Posting</li>\n</ul>\n<p><span>Note that in postings from Controlling also the substitutions in FI are not processed any more in S/4HANA for the same reason.<br/>These are the substitutions for application area FI, call-up point 1, 2 and 3.</span></p>\n<p><em><strong><br/>Check Sub-ID: FI_GL_20, Message: Message text from function module FAGL_GET_LEADING_LEDGER<br/></strong></em></p>\n<p>Real-time integration CO-FI: Does the ledger group contain the leading ledger?</p>\n<p>In ECC, a ledger group had to be maintained in the variant definition for Real-time integration  (SPRO -&gt; Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Ledgers -&gt; Real-Time Integration of Controlling with Financial Accounting -&gt; Define Variants for Real-Time Integration). If you had chosen a ledger group which didn't contain the leading ledger, error message FAGL_LEDGER_CUST148 was sent. However, it was possible to switch off the message in the message control (transaction OBA5).</p>\n<p>In S/4HANA the line item tables for Controlling and General Ledger, tables COEP and FAGLFLEXA, were unified into the new table ACDOCA. The intention behind is that there is no separation any more between CO and FI-GL. In order to enable the system to emulate the content of table COEP a ledger must be specified from which the system shall read the CO relevant data from table ACDOCA. This means that this ledger must contain all CO relevant postings. The restriction is that this ledger must be the leading ledger. Otherwise inconsistencies in the applications Real Estate, Joint Venture, Public Sector, Contract Accounting and potentially others could occur. Since you do currently not post the CO-internal allocations into the leading ledger, the leading ledger is incomplete: It cannot act as source ledger from which the system could read all CO postings in order to emulate the content of table COEP.</p>\n<p>Please contact your consulting partner or SAP DMLT/SLO (notes 481938 / 39919) to discuss possible solutions.</p>\n<p>Note: In case an error with check sub-ID FI_GL_20 is shown in the result, please ensure that the corrections of SAP Note <a href=\"/notes/2613787\" target=\"_blank\">2613787</a>  <span class=\"urTxtStd\">are implemented.</span></p>\n<p><em><strong><br/>Check Sub-ID: FI_GL_21, Message: Real-time integration CO-FI: Ledgergroup XX contains not leading ldgr YY</strong></em></p>\n<p>Real-time integration CO-FI: Does the ledger group contain the leading ledger?</p>\n<p>In ECC, a ledger group had to be maintained in the variant definition for Real-time integration  (SPRO -&gt; Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Ledgers -&gt; Real-Time Integration of Controlling with Financial Accounting -&gt; Define Variants for Real-Time Integration). If you had chosen a ledger group which didn't contain the leading ledger, error message FAGL_LEDGER_CUST148 was sent. However, it was possible to switch off the message in the message control (transaction OBA5).</p>\n<p>In S/4HANA the line item tables for Controlling and General Ledger, tables COEP and FAGLFLEXA, were unified into the new table ACDOCA. The intention behind is that there is no separation any more between CO and FI-GL. In order to enable the system to emulate the content of table COEP a ledger must be specified from which the system shall read the CO relevant data from table ACDOCA. This means that this ledger must contain all CO relevant postings. The restriction is that this ledger must be the leading ledger. Otherwise inconsistencies in the applications Real Estate, Joint Venture, Public Sector, Contract Accounting and potentially others could occur. Since you do currently not post e.g. the CO-internal allocations into the leading ledger, the leading ledger is incomplete: It cannot act as source ledger from which the system could read all CO postings in order to emulate the content of table COEP.</p>\n<p>Please contact your consulting partner or SAP DMLT/SLO (notes 481938 / 39919) to discuss possible solutions.</p>\n<p><em><strong><br/>Check Sub-ID: FI_GL_22, Message: Currency type XX cannot be used in FI by company code YYYY</strong></em></p>\n<p>Is the currency type of a controlling area correct?</p>\n<p>Check the currency type in transaction OKKP. The currency type of a controlling area must have a legal valuation view. This means that the currency type must end with a ‘0’ e.g. 30, 20, 10.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_23, Message: Curr. Type XX of ledger YY, comp.code ZZZZ is not defined in leading ledger.</strong></em></p>\n<p>Is the currency type of the second FI currency for the combination of company code ZZZZ and ledger YY defined in the leading ledger?</p>\n<p>The currency types of the second and third FI currency of a non-leading ledger must be defined in the leading ledger. This can be checked in transactions V_FAGL_T882G and OB22 in ERP and FINSC_LEDGER in S/4 HANA.</p>\n<p>This is an inconsistent situation that must be corrected in ECC before the S/4migration. Not only customizing changes are necessary but also correction of historical postings. Please contact your consulting partner or SAP DMLT/SLO (notes 481938 / 39919).</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_24, Message: Curr. Type XX of ledger YY, comp.code ZZZZ is not defined in leading ledger.</strong></em></p>\n<p>Is the currency type of the third FI currency for the combination of company code ZZZZ and ledger YY defined in the leading ledger?</p>\n<p>The currency types of the second and third FI currency of a non-leading ledger must be defined in the leading ledger. This can be checked in transactions V_FAGL_T882G and OB22 in ERP and FINSC_LEDGER in S/4 HANA.</p>\n<p>This is an inconsistent situation that must be corrected in ECC before the S/4migration. Not only customizing changes are necessary but also correction of historical postings. Please contact your consulting partner or SAP DMLT/SLO (notes 481938 / 39919).</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_25, Message: No currency settings found for company code XXXX; ledger YY not supported</strong></em></p>\n<p>Are the currency settings for the company code maintained correctly?</p>\n<p>The company code XXXX is assigned to ledger YY with more than one currency (table T882G). However, the company code does not exist in original table T001A (in S/4 HANA the content can be displayed via view FINSV_T001A_ORI). Therefore, the currency settings cannot be found.</p>\n<p>Either the assignment of the company code to the ledger must be deleted (ERP: IMG activity 'Define and Activate Non-Leading Ledgers', S/4 HANA: IMG activity 'Define Settings for Ledgers and Currency Types' (transaction FINSC_LEDGER)) or local currencies for the company code must be maintained (e.g. IMG activity ‘Define currencies of leading ledger’). In case postings exist already for the affected company code you must not change the configuration without adjustment of the posted data. Otherwise, the client becomes inconsistent and follow up processes will not work properly.</p>\n<p>With some former ERP releases, there was - by mistake - some banking industry-specific customizing delivered to all customers: Ledgers 0D, 0M, 1D, 1L, ledger groups 0D, 0M, 1D, 1L, 0MCA, 1MCA and company codes MCA1, MCA2, MCA3, MCA4. In most customer installations, this content is unwanted. In some S/4HANA releases this unwanted content also exists (it \"survived\"). This content can cause error messages in consistency checks. If errors appear for this content, please delete this content by applying the correction instructions of notes <a href=\"/notes/1990560\" target=\"_blank\">1990560</a> and <a href=\"/notes/2159137\" target=\"_blank\">2159137</a>. Afterwards execute the ABAP program GLE_MCA_DELETE_LEDGERS as mentioned in note 1990560.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_26, Message: Assignment of company code XXXX to controlling area YYYY is inconsistent</strong></em></p>\n<p>Is the company code assigned to a controlling area correctly?</p>\n<p>There is an entry in database table TKA02 indicating that the company code XXXX is assigned to controlling area YYYY. However, controlling area YYYY does not exist. This is an inconsistency in table TKA02. The inconsistent entry has to be deleted from TKA02.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_27, Message: Allocation indicator missing in CO area</strong></em></p>\n<p>Is the company code assigned to a controlling area correctly?</p>\n<p>There is an entry in database table TKA02 indicating that the company code XXXX is assigned to controlling area YYYY. However, the entry for controlling area YYYY in database table TKA01 indicates that there is no company code assigned to the controlling area. Use transaction OKKP to regenerate the assignment of the company code to the controlling area.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_28, Message: Valuation areas XX and YY in company code ZZZZ have different ML crcy types</strong></em></p>\n<p>Are the currency settings of your Material Ledger compatible with the new S/4HANA tables?</p>\n<p>In S/4HANA Finance 1503 and S/4HANA 1511 migration supports only those system setups where ML currency setting is identical to or is a subset of the currencies defined for accounting and controlling. If you use additional currencies only in ML, you should upgrade to an upper S/4HANA or S/4HANA Finance release.</p>\n<p>All valuation areas within a company code must have the same ML currency settings. This restriction is valid for all S/4HANA and S/4HANA Finance releases.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_29, Message: ML currency type XX does not match crcy type in FI and CO (val. Area YY)</strong></em></p>\n<p>Are the currency settings of your Material Ledger compatible with the new S/4HANA tables?</p>\n<p>In S/4HANA Finance 1503 and S/4HANA 1511 migration supports only those system setups where ML currency setting is identical to or is a subset of the currencies defined for accounting and controlling. If you use additional currencies only in ML, you should upgrade to an upper S/4HANA or S/4HANA Finance release.</p>\n<p>All valuation areas within a company code must have the same ML currency settings. This restriction is valid for all S/4HANA and S/4HANA Finance releases.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_30, Message: Field XX of index table YY is missing in table BESEG. See SAP Note 2191738</strong></em></p>\n<p>Are there custom fields added to index tables BSIS, BSAS, BSIK, BSAK, BSID, BSAD, FAGLBSIS or FAGLBSAS which have not been added to BSEG?</p>\n<p>Please read the explanation in SAP note <a href=\"/notes/2191738\" target=\"_blank\">2191738</a> regarding custom fields in these index tables. The check has identified that there is a custom field in an index table which is not available in table BSEG. Follow the instructions of SAP Note 2191738 to resolve the issue.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_31, Message: Field XX of index table YY is missing in table BSEG_ADD. See SAP Note 2191738</strong></em></p>\n<p>Are there custom fields added to index tables BSIS, BSAS, BSIK, BSAK, BSID, BSAD, FAGLBSIS or FAGLBSAS which have not been added to BSEG_ADD?</p>\n<p>Please read the explanation in SAP note <a href=\"/notes/2191738\" target=\"_blank\">2191738</a> regarding custom fields in these index tables. The check has identified that there is a custom field in an index table which is not available in table BSEG_ADD. Follow the instructions of SAP Note 2191738 to resolve the issue.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_32, Message: Multiple acctg princ. in CO (COGM): BAdI implementation found</strong></em></p>\n<p>Parallel Valuation of Costs of Goods Manufactured (COGM) are supported from release S/4HANA 1610 support package 1 on. But only the parallel ledger solution is supported, <span>not the parallel account solution</span>.</p>\n<p>If you are using the parallel account solution, thus a not-supported function in S/4 at the moment, productively, then you should not upgrade to S/4HANA.</p>\n<p>Sub-ID 32 contains a generic check whether COGM is active with parallel accounts. It checks whether there exists an implementation of BAdI BADI_FCML_ACCOUNT_MODIFICATION. BAdI BADI_FCML_ACCOUNT_MODIFICATION could be used to modify the mapping of a cost element to a G/L account. However, in S/4 HANA a cost element is a G/L account itself. A mapping to a different G/L account is not possible anymore.</p>\n<p>If COGM is active, which means an active currency and valuation profile with valuation type 5, 6 or 7 exist, you might also get the Sub-IDs FI_GL_06. You can check the active status of the currency and valuation profile with transaction 8KEQ and the content with transaction 8KEM.</p>\n<p>Still supported is the assignment of accounts to Accounting Principles and the Accounting Principle Check in Document. This can be used without COGM, but in order to see and use the IMG transactions it is required to activate the COGM Business Function.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_33, Message: Multiple acctg princ. in CO (COGM): accounts approach used in CoCode</strong></em></p>\n<p>Parallel Valuation of Costs of Goods Manufactured (COGM) are supported from release S/4HANA 1610 support package 1 on. But only the parallel ledger solution is supported, not the parallel account solution!</p>\n<p>If you are using the parallel account solution, thus a not-supported function in S/4 at the moment, productively, then you should not upgrade to S/4HANA.</p>\n<p>Sub-ID 33 contains a company code specific check whether COGM is active with parallel accounts. This check is done based on table TACPAK_ACP_MAP.<br/>Is the ‘Multiple accounting principle in CO (COGM)’ active and is the parallel account mapping used to assign different G/L accounts?</p>\n<p>The parallel accounts approach of COGM could map a cost element to a different G/L account. However, in S/4 HANA a cost element is a G/L account itself. A mapping to a different G/L account is not possible anymore.</p>\n<p>If COGM is active, which means an active currency and valuation profile with valuation type 5, 6 or 7 exist, you might also get the Sub-IDs FI_GL_06. You can check the active status of the currency and valuation profile with transaction 8KEQ and the content with transaction 8KEM.</p>\n<p>Still supported is the assignment of accounts to Accounting Principles and the Accounting Principle Check in Document. This can be used without COGM, but in order to see and use the IMG transactions it is required to activate the COGM Business Function.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_34, Message: Account determination for transfer of CO postings into FI (transaction OK17) is not called anymore.</strong></em></p>\n<p>Is there a rule in the account determination which changes the original G/L account of a CO posting to a different G/L account?</p>\n<p>During the transfer of a CO posting to FI the G/L accounts could be changed by a rule in the account determination. However, in S/4 HANA a cost element is a G/L account itself. A mapping to a different G/L account is not possible anymore.</p>\n<p><br/><em><strong>Check Sub-ID: FI_GL_35, Message: Substitution for Event 60 (Reconciliation ledger: Acct determ.) is not called anymore.</strong></em></p>\n<p>Is the CO substitution for event 60 (Reconciliation ledger: Acct determ.) activated?</p>\n<p>The event 60 for CO substitutions is not called anymore. This substitution could be used to map a cost element to a different G/L account. This kind of substitution is not allowed anymore as in S/4 HANA a cost element is a G/L account itself. A mapping to a different G/L account is not possible anymore.<br/><br/></p>\n<p><em><strong>Check Sub-ID: FI_GL_36, Message: Curr. type XX of CO area YYYY is not defined for company code ZZZZ.</strong></em></p>\n<p>Two checks are performed that can lead to FI_GL_36:<br/>- Is the currency type of the CO area defined as currency type of the company code?<br/>- Is the currency type of the CO area maintained in table T030H for all relevant G/L accounts and currencies (table T030H: Account Determination for Open Item Exch.Rate Differences)?</p>\n<p>During S/4 migration, the currecy type of the controlling area is included in the Company Code Settings for the Ledger customizing (transaction finsc_ledger) as Global Currency Type. If the currency type of the CO area YYYY is not 10, for posting/clearing purpose it has to be defined either as currency type of company code ZZZZ itself or taken into account in the maintenance of table T030H (for the chart of account configured for company code ZZZZ). Table T030H can be maintained via Customizing activity 'Define Accounts for Exchange Rate Differences' or directly via view V_T030H. The maintenance in table T030H can be carried out via explicit specification of the currency type or maintained with initial currency type (standard case).</p>\n<p>If the currency type of the CO area YYYY is 20 and the currency key is the same like currency type 30, then you can think about changing the currency type of the CO area from 20 to 30 in the ECC system according to note 119428. In ECC, the currency type 20 is only known by cost accounting, whereas currency type 30 is known generally in financial accounting. Therefore, data transmission via the modules CO, AM, and FI works without any difficulties if type 30 is used by all. Otherwise currency type 20 will be your Global currency Type in S/4HANA for the company codes assigned to the controlling area with 20.</p>\n<p>Please discuss and analyse a possible change with your FI/CO consultants during your preparation project for the migration.<br/>If Material Ledger is active with CT 10 and CT 20, consider, that after a change of the CT from 20 to 30 in transaction OKKP, the material ledger is not automatically changed! If you are interested to have the currency type 30 in Material ledger too, please discuss and analyse with your ML consultant. In ECC, you have the chance to deactivate the Material Ledger, do the currency type change from 20 to 30 in ML, and to activate the Material Ledger again. Please have a look at note 425487 and discuss with your ML consultant about the consequences (data loss).<br/>! This change is no longer possible in S/4HANA!</p>\n<p><strong>!Consider!</strong>: If a currency type used in controlling (e.g. currency type 30) was defined as local currency/integrated currency in FI in ERP (transaction OB22), then of course journal entries balance to zero for this currency type after the migration for all documents.<br/>If the currency type 30 was NOT defined as local currency/integrated currency in FI in ERP (transaction OB22) but you intend to use currency type 30 for reporting puposes in S/4HANA, so expect the journal entries to have a zero-balance for all documents and therefore also per company code for currency type 30, then currently you only have the technical possibility to introduce the currency type 30 as integrated FI currency in ERP before the migration (as long as the introduction of an integrated FI currency is not possible in S/4HANA - note 2334583) -&gt; note 39919.<br/>Note 2588120 - Differences in currency handling between SAP S/4HANA and SAP ERP</p>\n<p> </p>\n<p><em><strong>Check Sub-ID: FI_GL_37, Message: Curr. type XX for transf. pr. is not defined (CO area YYYY, CoCode XXXX).</strong></em></p>\n<p>CO area YYYY is using a CO version relevant for actual costs with valuation views 'Group Valuation' or 'Profit Center Valuation'. Was the currency type corresponding to the valuation view defined as currency type of the company code or was it maintained in table T030H for all relevant G/L accounts and currencies?</p>\n<p>The currency type corresponding to the valuation view is derived from the currency type defined for th CO area. Example: If the currency type is 10, the currency type corresponding to valuation view 'Group Valuation' is 11 and for 'Profit Center Valuation' it is 12 (for currency type 30 it would be 31 or 32). The currency type of the valuation view must be maintained  as currency type of company code ZZZZ itself or in table T030H (for the chart of account configured for company code ZZZZ). Table T030H can be maintained via Customizing activity 'Define Accounts for Exchange Rate Differences' or directly via view V_T030H.</p>\n<p><strong><em>Check Sub-ID: FI_GL_38, Message: Field XXXX of table YYYY configured incorrectly (include ZZZZ).</em></strong></p>\n<p>You added fields to your GL data model by adding fields directly via SE11. E.g. you added field ZZCNTRY (XXXX) to include CI_FAGLFLEX04 (ZZZZ) which is contained in table FAGLFLEXA (YYYY). There are no field movements defined for these fields. This can create an inconsistency and possibly data loss during data migration.</p>\n<p>There are two scenarios:</p>\n<ol>\n<li>You created the fields but did not actually use them, i.e. they are empty in all data records.<br/>In previous versions of this SAP Note, it was recommended that you consider deleting the fields. <br/>Unfortunately, deleting fields often leads to table conversions and activation problems. Furthermore, these fields can already be used in follow-on processes in Customizing. This leads to further problems. Therefore, fields should only be deleted if there are no alternatives to this, or if the fields have not been inserted correctly using transaction SE11. In this case, we will release a note (SAP Note 3261768) in the first week of November 2022 where we distinguish the different cases.</li>\n<li>The fields have been in use and data has been posted to them.<br/>The solution depends on whether or not you have already performed the technical upgrade.</li>\n</ol>\n<ul>\n<ul>\n<li>Before the technical upgrade:</li>\n<ul>\n<li>Start transaction FAGL_GINS.</li>\n<li>Specify the corresponding totals table.</li>\n<li>Choose function Change (Shift+F1).</li>\n<li>Add a space behind the first field name.</li>\n<li>Choose function Activate (Shift+F4).</li>\n<li>This will generate the corresponding entries for the field movement (FAGL_FIELD_MOVEC).</li>\n</ul>\n<li>After the technical upgrade:</li>\n<ul>\n<li>Implement SAP Note <a href=\"/notes/2707728\" target=\"_blank\">2707728</a>.</li>\n<li>Start program ZZ_FAGL_CREATE_FIELDMOVEC to create the corresponding entries for the field movement (FAGL_FIELD_MOVEC).</li>\n</ul>\n</ul>\n</ul>\n<p> </p>\n<p><span><strong>Note:</strong></span></p>\n<ul>\n<li>In S/4HANA and S/4HANA Finance, ALE scenarios in CO are not fully supported:\r\n<ul>\n<li>Up to S/4HANA Finance 1503 support package stack 1505:<br/>System cannot act as ALE receiver system for CO line items. Reason is that in S/4HANA FI and CO line items share the same database table in which the postings are stored - and as a consequence it is not supported to receive (=post) CO line items only - without the corresponding line items in FI. </li>\n<li>Up from S/4HANA Finance 1503 support package stack 1508:<br/>System can act as ALE receiver system for CO line items.</li>\n<li>All support package stacks:<br/>System cannot act as ALE sender system for FI line items which would be created from CO-FI real time integration within an SAP business suite system. Reason is that such additional FI line items are not necessary in an S/4HANA Finance 1503 system.</li>\n<li>It cannot be checked by this report whether such ALE scenarios are used. This must be checked manually by the customer.</li>\n</ul>\n</li>\n<li>ALE scenarios for distributing CO master data like cost centers are still supported.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>See above.</p>\n<p> </p>", "noteVersion": 71}, {"note": "2346431", "noteTitle": "2346431 - SAP S/4HANA 1610: Release Information Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This Release Information Note (RIN) contains information and references to notes for applying Feature Package (FP)/Support Package (SP) Stacks of product version 'SAP S/4HANA 1610'.</p>\n<p><strong><strong>Note</strong>:</strong> This SAP Note is subject to change. Check this note for changes on a regular basis. All important changes made after release of a Feature Package (FP)/Support Package (SP) Stack are documented in section \"Changes made after Release of FP/SP Stack &lt;xx&gt;\".</p>\n<p><br/><strong>GENERAL INFORMATION</strong></p>\n<p>SAP S/4HANA 1610 will be in Maintenance until 31.12.2021. For further information about the maintenance strategy please refer to SAP note <a href=\"/notes/52505\" target=\"_blank\">52505</a> and SAP note <a href=\"/notes/2311392\" target=\"_blank\">2900388</a>.<a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?view=bsp&amp;param=69765F6D6F64653D3030312669765F7361706E6F7465735F6B65793D30313130303033353837303030303932373037303230303126766965773D627370\" target=\"_blank\"><br/></a></p>\n<p>We strongly recommend to upgrade your system to the latest SAP S/4HANA on-premise release.</p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>\n<p>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1909/en-US\" target=\"_blank\">product documentation</a>. We recommend setting up your system to display a dynamic documentation, which reflects the latest Feature or Support Package Stack of this release. If you want to stay on Feature Package Stack 00 to 02 for a longer period, and if you also want to display the corresponding documentation for one of these Feature Package Stacks, you will find all necessary information in SAP note <a href=\"/notes/2904428\" target=\"_blank\">2904428</a>.</p>\n</li>\n</ul>\n<ul>\n<li>The supported Kernel versions are: SAP KERNEL 7.49 64-BIT UNICODE and SAP KERNEL 7.53 64-BIT UNICODE.</li>\n<li>For general restrictions as well as restrictions for the conversion to SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a>.</li>\n<li>For release information and restrictions related to country-specific localization features, please refer to SAP note <a href=\"/notes/2349004\" target=\"_blank\">2349004</a>.</li>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version \"SAP FIORI FOR SAP S/4HANA 1610\".</li>\n<li>If you have add-ons installed on your system and/or want to use them on product version SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</li>\n<li>For process integration capabilities with other SAP on-premise solutions, please refer to SAP note <a href=\"/notes/2376061\" target=\"_blank\">2376061</a>.</li>\n<li>\n<p>Please be aware that SAP S/4HANA 1610 is an Unicode-only release. Non-Unicode systems are not supported anymore. Hence upgrades of non-Unicode systems without prior Unicode conversion is not possible. For details see <a href=\"https://service.sap.com/~sapidb/012002523100009958832014E/\" target=\"_blank\">Upgrade of non Unicode systems</a> or <a href=\"https://service.sap.com/Unicode\" target=\"_blank\">service.sap.com/Unicode </a>and SAP note <a href=\"/notes/2033243\" target=\"_blank\">2033243</a>.</p>\n</li>\n<li>A new inventory data model will be delivered. This new inventory data model has a lot of advantages at S4. With the S4 new inventory data model delta stock quantities instead of total stock quantities are transferred to APO. At APO code changes need to be implemented to get this delta stock quantities processed. SAP Note 2816388 contains a list of all SAP Notes to be implemented at APO to realize these code changes.</li>\n<li><strong>United Kingdom leaving the EU</strong>: For information on how a “hard Brexit” (= a no-deal scenario) would impact your <em>SAP S/4HANA </em>system, please see SAP Note <strong><a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.</strong></li>\n<li>For the implementation of ELSTER modules using the ERiC libraries and the corresponding impact on platform support, please see notes <a href=\"/notes/2745249\" target=\"_blank\">2745249</a> and <a href=\"/notes/2558316\" target=\"_blank\">2558316</a>.</li>\n<li>\n<p>For more information regarding LEGAL VAT TAX CHANGE in Germany<strong> , please see SAP note </strong><span><a href=\"/notes/2934992\" target=\"_blank\">2934992</a></span></p>\n</li>\n</ul>\n<p><strong><strong>Installation Information</strong></strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20000/en-US/INST_OP1610.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong><strong><strong><strong>Upgrade Information</strong></strong></strong></strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20000/en-US/UPGR_OP1610.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong>SAP HANA database requirements</strong></p>\n<ul>\n<li>The minimum required revision is defined in the respective Support or Feature Package Stack chapter.</li>\n<li>Detailed information about SAP HANA 2.0 Revision and Maintenance Strategy can be found in SAP note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a>.</li>\n<li>If you plan to upgrade your SAP HANA database to a newer revision or a newer available SPS level,</li>\n<ul>\n<li>Refer to SAP note <a href=\"/notes/1906576\" target=\"_blank\">1906576</a> for SAP HANA client and server cross-version compatibility and</li>\n<li>Refer to SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> for restrictions and recommendations regarding specific revisions of SAP HANA database for use in SAP S/4HANA.</li>\n</ul>\n</ul>\n<p><strong><strong>Conversion Information</strong></strong></p>\n<ul>\n<li>\n<p><em>Please note that a system conversion to SAP S/4HANA 1511 and 1610 is no longer possible or supported</em><em></em></p>\n<ul>\n<li><em>for newly starting </em><em>system conversion </em><em>projects – independently of the Enhancement Package or SP level of the </em><em>SAP ERP source system.</em></li>\n<li>for any system which is below the following SAP ERP SP levels and have to go to S/4HANA 1511 and 1610, open a message on CA-TRS-PRCK</li>\n<li><em>for any system which is on the following SAP ERP SP levels or higher: 600 SP30, 602 SP20, 603 SP19, 604 SP20, 605 SP17, 606 SP20, 616 SP12, 617 SP15, 618 SP09</em></li>\n</ul>\n<p><em>You can convert to the successor product versions of SAP S/4HANA.</em></p>\n</li>\n</ul>\n<p><strong>Feature Package Update (via Software Update Manager (SUM))</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n</ul>\n<p><strong><strong>Support Package Stacks on SAP Support Portal</strong></strong></p>\n<p>You will find general information about Support Package Stacks on SAP Support Portal at <a href=\"https://support.sap.com/software/patches/stacks.html\" target=\"_blank\">support.sap.com/software/patches/stacks.html</a>. The Schedule for Support Package Stacks is available at <a href=\"http://support.sap.com/maintenance-schedule\" target=\"_blank\">support.sap.com/maintenance-schedule</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA 1610</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to get additional information about product version SAP S/4HANA 1610.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>SUPPORT PACKAGE STACK 11 (10/2021)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 11.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 11:</strong></p>\n<p><strong>SUPPORT PACKAGE STACK 10 (04/2021)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 11.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 10:</strong></p>\n<p><strong>SUPPORT PACKAGE STACK 09 (10/2020)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 11.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 09:</strong></p>\n<p><strong>SUPPORT PACKAGE STACK 08 (04/2020)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 10.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 08:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2880761\" target=\"_blank\">2880761</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBT</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>ST-A/PI</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2020-04-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2880801\" target=\"_blank\">2880801</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBP</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>ST-A/PI</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2020-04-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2886195\" target=\"_blank\">2886195</a></p>\n</td>\n<td valign=\"top\" width=\"349\">Syntax error \"VBHDR_G does not exist\" in program /SDF/SAPLEWA</td>\n<td valign=\"top\" width=\"154\">ST-A/PI</td>\n<td valign=\"top\" width=\"147\">No</td>\n<td valign=\"top\" width=\"142\">2020-04-08</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong><strong>Important changes made after release of <strong>Support Package Stack 08</strong></strong></strong></p>\n<p><strong>SUPPORT PACKAGE STACK 07 (10/2019)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.23 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 09.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong> </strong></p>\n<p><strong><strong>Important changes made after release of <strong>Support Package Stack 07</strong></strong></strong></p>\n<p><strong>2020-01-31: </strong><strong>United Kingdom leaving the EU</strong>:</p>\n<p>• For information about the United Kingdom leaving the EU with the Withdrawal Bill and the transition period, please see SAP note <a href=\"/notes/2885225\" target=\"_blank\">2885225</a>.</p>\n<p>• For information about how a “hard Brexit” (= a no-deal scenario) would impact your SAP S/4HANA system, please see SAP note <a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.</p>\n<p><strong>SUPPORT PACKAGE STACK 06 (04/2019)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.21. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP01 Revision 12.04 or SPS02 Revision 23. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 08.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 06</strong></p>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 05 (11/2018)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 07.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 05</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2694011\" target=\"_blank\">2694011</a></span></td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">WTY: Dump \"CALL_FUNCTION_CONFLICT_LENG\" occurs on account document posting</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">S4CORE</span></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 22.11.2018</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2697405\" target=\"_blank\">2697405</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">TrexViaDbsl: wrong schema name is set to temporary objects in TREX_EXT_AGGREGATE</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">SAP_BASIS</span></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>22.11.2018</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2722552\" target=\"_blank\">2722552</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Runtime error SYSTEM_DATA_ALREADY_FREE during update of classifications</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-11-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2724147\" target=\"_blank\">2724147</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Termination in the update of the classification</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-11-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2726975\" target=\"_blank\">2726975</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Support of CLSD in SNOTE: Ignore all Changed by and Changed on data in the CI</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-12-11</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Feature Package Stack 05</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2019-01-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2324448\" target=\"_blank\">2324448</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>S4 Release 1610: New inventory data model -&gt; Adjustments at APO to use the new locking free stock upate at liveCache</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SCMAPO</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2019-01-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2767385\" target=\"_blank\">2767385</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"349\">\n<p>Melted variables showing initial values by mistake</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</td>\n<td valign=\"top\" width=\"126\">SAP_BW</td>\n<td valign=\"top\" width=\"175\">No</td>\n<td valign=\"top\" width=\"142\">2019-07-12</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 04 (05/2018)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 06.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 04</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2618103\" target=\"_blank\">2618103</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ALV layout: Layouts cannot be saved from the 'Change Layout' dialog after an error message</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-05-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2624170\" target=\"_blank\">2624170</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Conversion routine for DATUM data element in BC Set</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2018-05-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2441447\" target=\"_blank\">2441447</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Authorization check enablement in Business Partner F4 search help</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">2018-07-10</span></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2658952\" target=\"_blank\">2658952</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ESH - initial authorization index filling - error: \"Feature not supported\"/OLAP VIEW on SAP HANA 3.1</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> 2018-07-10</span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2660883\" target=\"_blank\">2660883</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">BP_EOP: Success Message is not displayed properly</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> 2018-07-10</span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S<span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">AP_UI</span></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">  2018-07-23</span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2655756\" target=\"_blank\">2655756</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Tree UIBB: Conditional Formatting when master column has display type image</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> 2018-07-23</span></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Feature Package Stack 04</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 11.01.2019</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 03 (10/2017)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 05.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p> </p>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM for the SP update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP update to SAP S/4HANA 1610 Support Package Stack 03 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n<li>If switch GLO_REP_EAPS_SFWS_03 is activated, you might encounter errors during AFTER_IMPORT_METHOD SCPR_SCP2_AFTER_IMPORT for the object R3TR SCP2 GLO_REP_SK_03_IDREPFW_SELPA_C. In this case, please repeat the phase up to 2 times. In case repeating the phase twice does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 03</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235373030323926\" target=\"_blank\">2570029</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Connector creation: Dump due to memory overflow</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-02-02</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2566812\" target=\"_blank\">2566812</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Application area TM/Transportation Management</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2018-02-02</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2537567\" target=\"_blank\">2537567</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>EHP8_SP08: Issue with workforce viewer application</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>EA-HRRXX 608<br/>(valid to SP46)</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-10-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2352024\" target=\"_blank\">2352024</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Time dependant promotion data has not been deleted when transferring the data in parallel mode</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE <br/>(SCM-FRE-ERP)</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-10-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2340156\" target=\"_blank\">2340156</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>WRF_DISP_CON does not delete TD procurement data from F&amp;R</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE <br/>(SCM-FRE-ERP)</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-10-24</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Feature Package Stack 03</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 11.01.2019</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong><span>FEATURE PACKAGE STACK 02 (05/2017)</span></strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\n</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Feature Package Stack 02 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>\n<p>Please refer to note <a href=\"/notes/2400710\" target=\"_blank\">2400710</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 01 (02/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</p>\n</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Feature Package Stack 02 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n<li>In case you are using roles from <strong>Retail</strong> in FIORI Launchpad and plan to upload the backend app descriptors for area S4RFM please check the instructions of note <a href=\"/notes/2550359\" target=\"_blank\">2550359</a>.</li>\n</ul>\n<p>Installation Requirements</p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 04.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p> </p>\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n</ul>\n<p><strong>Notes to be applied on top of Feature Package Stack 02</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2436731\" target=\"_blank\">2436731</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Job scheduling failed due to invalid date</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2445210\" target=\"_blank\">2445210</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2461676\" target=\"_blank\">2461676</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2467650\" target=\"_blank\">2467650</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Authorization issues in viewing Document Info Record</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2463740\" target=\"_blank\">2463740</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-19</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\"><a href=\"/notes/2477735\" target=\"_blank\">2477735</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Annotation API: enable cache usage for get_annos_mass</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2489305\" target=\"_blank\">2489305</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Dump while inserting international address version for a person</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2490652\" target=\"_blank\">2490652</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Legacy DAC maps business key to initial BOPF key</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2491892\" target=\"_blank\">2491892</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2520306\" target=\"_blank\">2520306</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Condition table index not activated after upgrade</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2527709\" target=\"_blank\">2527709</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2485570\" target=\"_blank\">2485570</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-09-07</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 02</strong></p>\n<p><strong><strong><strong><strong><strong>2017-10-19: </strong></strong></strong></strong></strong>Inserted:In case you are using roles from <strong>Retail</strong> in FIORI Launchpad and plan to upload the backend app descriptors for area S4RFM please check the instructions of note <a href=\"/notes/2550359\" target=\"_blank\">2550359</a>.<br/><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<strong><strong><strong><strong><strong><strong><br/></strong></strong>2017-08-30</strong>: </strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool. <strong><strong><br/></strong></strong><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.<strong> <br/></strong><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.</p>\n<p><strong><span>FEATURE PACKAGE STACK 01 (02/2017)</span></strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20001/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>Feature Package Stack 01 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note<a href=\"/notes/2429281\" target=\"_blank\"> 2429281</a> (S4H:SUM:XPRAS_AIMMRG:HANA deadlock dumps) when you upgrade your system with Software Update Manager (SUM) or apply Feature Package Stack 01 and your system is on HANA 2.0.</li>\n</ul>\n<ul>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\n</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610.<br/>Support Package Stack 01 of SAP Fiori 2.0 for SAP S/4HANA on the frontend requires Feature Package Stack 01 of SAP S/4HANA 1610 in the backend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2400710\" target=\"_blank\">2400710</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 01 (02/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\n<li>For systems with an active BW client, the implementation of Feature Package Stack 01 could abort due to issues with activation of queries REP_LUECSL and REP_BE_STRPWHLDGTAXITEM. In this case, please proceed as described in note <a href=\"/notes/2429774\" target=\"_blank\">2429774</a> and note <a href=\"/notes/2436577\" target=\"_blank\">2436577</a>.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20001/en-US/INST_OP1610_FPS01.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 03.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20001/en-US/UPGR_OP1610_FPS01.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong>Conversion Requirements</strong></p>\n<ul>\n<li>For the system conversion, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfe68bfa55e988410ee10000000a441470/1610%20001/en-US/CONV_OP1610_FPS01.pdf\" target=\"_blank\">conversion guide </a>and SAP notes <a href=\"/notes/2389794\" target=\"_blank\">2389794</a>, <a href=\"/notes/2389807\" target=\"_blank\">2389807</a> and <a href=\"/notes/2377310\" target=\"_blank\"><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\">2377310</span> </a>(conversion-related information).</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the conversion. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>For system conversions from an existing source release, please be aware that your software level is not higher than</li>\n<ul>\n<li>SAP ERP 6.0 SP Stack 28</li>\n<li>SAP enhancement package 2 for SAP ERP 6.0 SP Stack 18</li>\n<li>SAP enhancement package 3 for SAP ERP 6.0 SP Stack 17</li>\n<li>SAP enhancement package 4 for SAP ERP 6.0 SP Stack 18</li>\n<li>SAP enhancement package 5 for SAP ERP 6.0 SP Stack 15</li>\n<li>SAP enhancement package 6 for SAP ERP 6.0 SP Stack 18</li>\n<li>SAP enhancement package 6 for SAP ERP 6.0, version for SAP HANA SP Stack 10</li>\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 13</li>\n<li>SAP enhancement package 8 for SAP ERP 6.0 SP Stack 05</li>\n<li>SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP Stack 10</li>\n<li>SAP Simple Finance, on-premise edition 1503 SP Stack 06</li>\n<li>SAP S/4HANA Finance 1605 SP Stack 05</li>\n</ul>\n</ul>\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n<li>Please apply the correction in note <a href=\"/notes/2423846\" target=\"_blank\">2423846</a> on your SAP S/4HANA 1610 Feature Package Stack 00 system before starting a Support Package update to Feature Package Stack 01.</li>\n<li>\n<p>In case you import SAP NetWeaver 7.51 Support Package Stack 01 and SAP S/4HANA 1610 Support Package Stack 01 including generation in SPAM, the Support Package Stack import stops with an RC 8 in the ABAP generation phase of the SAP_BASIS 751 Support Package 01, showing generation issues due to syntax errors for classes CL_IVE_INVOICEERPCRTRQ1_MAP and CL_IVE_INVOICEERPCRTRQ1_VAL (Type ‘CL_SAPPLCO_ACTION_CODE1’ is unknown). This import issue can be overcome by restarting the import in SPAM.</p>\n</li>\n</ul>\n<p><strong>Notes to be applied on top of Feature Package Stack 01</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"102\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2399423\" target=\"_blank\">2399423</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Dump in characteristic value assignment</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2405390\" target=\"_blank\">2405390</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>MFLE: Short version in output conversion calculated incorrectly</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2396398\" target=\"_blank\">2396398</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>After activating the draft, the draft UUID field of the returned active instance data is not initial</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2399372\" target=\"_blank\">2399372</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Authorization check failed for SRT_SR_P</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2399477\" target=\"_blank\">2399477</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>SAP GUI for HTML: ~singletransaction=3 allows /nTX</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2400809\" target=\"_blank\">2400809</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2402373\" target=\"_blank\">2402373</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>OBJECTS_OBJREF_NOT_ASSIGNED in class CL_SWF_UTL_WAPI_SERVICES</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2404762\" target=\"_blank\">2404762</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Performance: SADL Metadata Load Is Calculated in Each Request</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2424813\" target=\"_blank\">2424813</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Runtime error COMMIT_IN_POSTING in class CL_FDT_BACKGROUND</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2428086\" target=\"_blank\">2428086</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Unable to Create New Line in Empty DataGrid</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BW</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2415736\" target=\"_blank\">2415736</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Warranty : Post Versions to Reimburser fails via action A042 in transaction WTY</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2427850\" target=\"_blank\">2427850</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2429609\" target=\"_blank\">2429609</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-04-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2461676\" target=\"_blank\">2461676</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-04-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2445210\" target=\"_blank\">2445210</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2467650\" target=\"_blank\">2467650</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Authorization issues in viewing Document Info Record</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2463740\" target=\"_blank\">2463740</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-05-19</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\"><a href=\"/notes/2477735\" target=\"_blank\">2477735</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Annotation API: enable cache usage for get_annos_mass</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2489305\" target=\"_blank\">2489305</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Dump while inserting international address version for a person</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2490652\" target=\"_blank\">2490652</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Legacy DAC maps business key to initial BOPF key</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2491892\" target=\"_blank\">2491892</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2520306\" target=\"_blank\">2520306</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Condition table index not activated after upgrade</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2527709\" target=\"_blank\">2527709</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2485570\" target=\"_blank\">2485570</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-09-07</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 01</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2018-07-03:</strong> </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> (SAP S/4HANA - unrecommended revisions of SAP HANA database for use in SAP S/4HANA ) when you plan to upgrade to SAP HANA 2.0 SPS03 Revisions 3x.</p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<br/><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool.<br/><strong>2017-03-13: </strong>Inserted under General Considerations: Refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.<br/><strong>2017-03-16: </strong>Note <a href=\"/notes/2436577\" target=\"_blank\">2436577</a> inserted under General Conditions in section issues with active BW client.<br/><strong>2017-03-24: </strong>Deleted in General Considerations: According to the Product Availability Matrix for supported Database versions for the products SAP ERP 6.0 EHP 6 (or higher) and SAP S/4HANA 1511 the upgrade of an underlying SAP HANA Database from Release 1.0 to 2.0 can only be executed after the successfully completed technical conversion or upgrade process to S/HANA 1610 Feature Package Stack 01.<br/><strong>2017-03-29: </strong>SAP HANA 2.0 Minimum version changed to SP0 Revision 002.<br/><strong>2017-05-08: </strong>Inserted under Feature Package Update: 'It is recommed to use SUM to apply Feature Package Stacks.'<br/><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>\n<p><strong><span>FEATURE PACKAGE STACK 00 (10/2016)</span></strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1610?current=s4hana_op_1610\" target=\"_blank\">product documentation</a>.</li>\n<li>Feature Package Stack 00 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note<a href=\"/notes/2429281\" target=\"_blank\"> 2429281</a> (S4H:SUM:XPRAS_AIMMRG:HANA deadlock dumps) when you upgrade your system with Software Update Manager (SUM) and your system is on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\n</li>\n<li>\n<p>Please refer to SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> (SAP S/4HANA - unrecommended revisions of SAP HANA database for use in SAP S/4HANA ) when you plan to upgrade to SAP HANA 2.0 SPS03 Revisions 3x.</p>\n</li>\n</ul>\n<li>\n<p>Please refer to SAP note <a href=\"/notes/1906576\" target=\"_blank\">1906576</a> (HANA client and server cross-version compatibility) if you would like to upgrade your SAP HANA Database to a newer revision or a newer available SPS level.</p>\n</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version \"SAP FIORI FOR SAP S/4HANA 1610\".<br/>Support Package Stack 00 of SAP Fiori 2.0 for SAP S/4HANA on the frontend requires Feature Package Stack 00 of SAP S/4HANA 1610 in the backend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2328546\" target=\"_blank\">2328546</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 00 (10/2016) content activation note<strong><span 'times=\"\" ar-sa;=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';\"=\"\">)</span></strong> for using the core configurator for your SAP S/4HANA on premise implementation and you need current installation and configuration data.</li>\n<li>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\n<li>If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1610 Feature Package Stack 00 and the target is SAP NetWeaver Support Package 01 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20000/en-US/INST_OP1610.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Feature Package Stack 02.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20000/en-US/UPGR_OP1610.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong>Notes to be applied on top of <strong>Feature Package Stack 00</strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"118\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2338721\" target=\"_blank\">2338721</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Program termination \"ASSIGN to a substring is not allowed\"</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2344368\" target=\"_blank\">2344368</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ECN IDoc processing fails because of missing data parts</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345087\" target=\"_blank\">2345087</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BP_BAP: Missing values in required entry fields cause posting termination in mass processing</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345102\" target=\"_blank\">2345102</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>No valuation dialog box for entering effectivity parameter values</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2322771\" target=\"_blank\">2322771</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S4HANA SuccessFactors &amp; personnel number and the user name fields behavior in S4HANA</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA, SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2284857\" target=\"_blank\">2284857</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Number ranges - trace</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2342658\" target=\"_blank\">2342658</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Note Implementation Failure due to Technical Languages(1Q,2Q,3Q,4Q,etc)</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2344014\" target=\"_blank\">2344014</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SPAU Adjustment for R3TR CLAS deliveries - adjustment of obsolete SAP notes deletes classes</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2344436\" target=\"_blank\">2344436</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ATC: Table SATC_AC_OBJ_CTXT gets extremely large</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345697\" target=\"_blank\">2345697</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BRF+ analytical decision table check Call CL_FDT_XS=&gt;GET_INSTANCE with RFC destination</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345795\" target=\"_blank\">2345795</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Workflow runtime ends with error WL821</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2346044\" target=\"_blank\">2346044</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Exceptions were raised for unblocked addresses</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2346821\" target=\"_blank\">2346821</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SCWB - TLOGO Language(1Q,2Q,3Q,4Q etc) Filtering Fix</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2350429\" target=\"_blank\">2350429</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>External view with more than 255 fields</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2351188\" target=\"_blank\">2351188</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BRF+ Anlytical function generation - Derive default schema dynamically</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2285661\" target=\"_blank\">2285661</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Record of a time management infotype is exited even though system issues an error message</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2311339\" target=\"_blank\">2311339</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: IT0019</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2313878\" target=\"_blank\">2313878</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO - validity period &amp;lt ;&gt; \"for all data\": Creation of new data record does not function correctly</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2335641\" target=\"_blank\">2335641</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: Field PERID (IT 002) is not selected in the event of an input error</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2336154\" target=\"_blank\">2336154</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: ‘Back’ button (navigation to the overview page) is inactive</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2333704\" target=\"_blank\">2333704</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: Program termination when you save infotype</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2340847\" target=\"_blank\">2340847</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: Program termination when you save infotype</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2343342\" target=\"_blank\">2343342</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Orders in cost distribution (IT0027/IT1018)</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2071826\" target=\"_blank\">2071826</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Client copy for integrated SAP HANA liveCache</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2353319\" target=\"_blank\">2353319</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Customizing Consistency Check for TAK01 in OKKP</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2354979\" target=\"_blank\">2354979</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SRFV_RPG_CAT2: Delete last record in Assign Report Categories to a Reporting Entity</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2355000\" target=\"_blank\">2355000</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Parameter for ledger groups of underlying ledgers</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2359435\" target=\"_blank\">2359435</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Client copy: Syntax error in program FINS_UPD_FINSC_001A_REP</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2362815\" target=\"_blank\">2362815</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Material Ledger (ML) and Retail: tied empties: error CKMLMV 009 in LCKMLMVQUANTF06</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2364253\" target=\"_blank\">2364253</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Syntax errors due to missing development package assignment</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2366738\" target=\"_blank\">2366738</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p> S/4HANA dump in CL_FAA_CFG_SERVICE-&gt;GET_LOCAL_CURR_TYPE_FROM_LDGRP if a ledger is not maintained correctly</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2367508\" target=\"_blank\">2367508</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Correction for selection of changedocs in DIMP system</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2369405\" target=\"_blank\">2369405</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Issue in Document Flow (SAP S/4HANA)</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2369962\" target=\"_blank\">2369962</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Authorization issue and G/L not defaulting from PPOMA corrections</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2371490\" target=\"_blank\">2371490</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Delivery date validation</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2371666\" target=\"_blank\">2371666</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>MD01N: Setup of MRP-Records via PPH_SETUP_MRPRECORDS or PPH_SETUP_MRPRECORDS_SIMU incomplete</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372008\" target=\"_blank\">2372008</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Switch from DB_COMMIT to COMMIT WORK AND WAIT for consolidation of Data Aging carry forward records and archive representatives</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372230\" target=\"_blank\">2372230</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Fill BWTAR in data aging carry forward records and archive representatives</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372605\" target=\"_blank\">2372605</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Issue while creating BP role as a supplier in transaction BP</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2373940\" target=\"_blank\">2373940</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Corrective measure for changes to the BOM Maintenance application</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2376505\" target=\"_blank\">2376505</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Revise Payment Proposals: Cannot Mass Block Items for Payment</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2376747\" target=\"_blank\">2376747</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Moving average price is not changed by goods receipt</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2377529\" target=\"_blank\">2377529</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>V_T012 maintenance dialog does not exist</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2378915\" target=\"_blank\">2378915</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Manage PIRs redirect of non active versions</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2379565\" target=\"_blank\">2379565</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>The Incoterm 2 field is converted incorrectly</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2379790\" target=\"_blank\">2379790</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Reading Purchase Order for multiple items in a PR</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2380548\" target=\"_blank\">2380548</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Error during assignment to investment program item, maintenance order</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2381346\" target=\"_blank\">2381346</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Characteristic based planning aborts for long material numbers</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2381849\" target=\"_blank\">2381849</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Missing condition tables B082 and B083</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2382748\" target=\"_blank\">2382748</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>KB11N: Short dump when ledger group filled</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2327999\" target=\"_blank\">2327999</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>QuantityWare CDS Extensions For IS-OIL</p>\n</td>\n<td valign=\"top\" width=\"238\">IS-OIL</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2379816\" target=\"_blank\">2379816</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Unicode conversion dump related to KONV</p>\n</td>\n<td valign=\"top\" width=\"238\">IS-OIL</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2172384\" target=\"_blank\">2172384</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>J1INCHLN and J1INCHLC: Multiple section legal change</p>\n</td>\n<td valign=\"top\" width=\"238\">FI-CA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-03</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2364845\" target=\"_blank\">2364845</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Migration: E543(FINS_RECON) in RC3</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2384182\" target=\"_blank\">2384182</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S/4HANA 1511+1610: Material master maintenance: Dumps when using screen sequence DI</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399372\" target=\"_blank\">2399372</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Authorization check failed for SRT_SR_P</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399423\" target=\"_blank\">2399423</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump in characteristic value assignment</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2400809\" target=\"_blank\">2400809</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2402373\" target=\"_blank\">2402373</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>OBJECTS_OBJREF_NOT_ASSIGNED in class CL_SWF_UTL_WAPI_SERVICES</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2405390\" target=\"_blank\">2405390</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>MFLE: Short version in output conversion calculated incorrectly</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2415736\" target=\"_blank\">2415736</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Warranty : Post Versions to Reimburser fails via action A042 in transaction WTY</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2427850\" target=\"_blank\">2427850</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2429609\" target=\"_blank\">2429609</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-17</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399477\" target=\"_blank\">2399477</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SAP GUI for HTML: ~singletransaction=3 allows /nTX</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-20</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-04-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2461676\" target=\"_blank\">2461676</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-04-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2467650\" target=\"_blank\">2467650</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Authorization issues in viewing Document Info Record</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2463740\" target=\"_blank\">2463740</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-05-19</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\"><a href=\"/notes/2477735\" target=\"_blank\">2477735</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Annotation API: enable cache usage for get_annos_mass</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2489305\" target=\"_blank\">2489305</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump while inserting international address version for a person</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2490652\" target=\"_blank\">2490652</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Legacy DAC maps business key to initial BOPF key</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2491892\" target=\"_blank\">2491892</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2520306\" target=\"_blank\">2520306</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Condition table index not activated after upgrade</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2527709\" target=\"_blank\">2527709</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2485570\" target=\"_blank\">2485570</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-09-07</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong><strong>Notes to be applied on top of <strong>Feature Package Stack 00 (only relevant for customers using SEM-BCS)</strong></strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"118\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2352766\" target=\"_blank\">2352766</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Enhancement of MDF metadata for S/4 1610</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2367523\" target=\"_blank\">2367523</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Enhancement of the SEM-BCS data model for S/4 HANA OP</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-09</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong><strong>Notes to be applied on top of <strong>Feature Package Stack 00 (only relevant for customers using </strong></strong>IS-RETAIL<strong><strong>)</strong></strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"118\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2224330\" target=\"_blank\">2224330</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Parameter list of append search help \"WRF_BETR_WHSH_APPEND\" differs from appending one</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2393010\" target=\"_blank\">2393010</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Negative stock for empties after stock transfer</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-21</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 00</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2020-05-11: </strong></strong></strong></strong></strong></strong></strong></strong></strong>Removed incorrect supported Kernel version SAP KERNEL 7.73 64-BIT UNICODE.</p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2019-04-01:</strong></strong></strong></strong></strong></strong></strong></strong></strong> Inserted: United Kingdom leaving the EU: For information on how a “hard Brexit” (= a no-deal scenario) would impact your <em>SAP S/4HANA </em>system, please see SAP Note <a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.<br/><br/><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.</p>\n<p><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool.<br/><strong>2016-11-18</strong>: Note 2224330 moved to list of notes which is only relevant for customers using IS-RETAIL.<br/><strong>2016-11-21: </strong>Note <a href=\"/notes/2356364\" target=\"_blank\">2356364</a> deleted in list of notes to be implemented on top of Feature Package Stack 00.<br/><strong>2016-12-08: </strong>Section<strong> '</strong>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.' inserted under 'General / Important Considerations'.<br/><strong>2016-12-20</strong>: 'SAP HANA 2.0 is not released for Feature Package Stack 00.' inserted under 'General / Important Considerations'.<br/><strong>2017-03-31: </strong>HANA 2.0 chapter inserted under 'General / Important Considerations'.<br/><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>", "noteVersion": 107}, {"note": "2527020", "noteTitle": "2527020 - G/L accounts with the G/L account type \"Secondary Costs\" in general ledger accounting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In SAP S/4HANA, the journal enters all posting-relevant transactions in financial accounting (FI) and Controlling (CO) as posting documents. For more information about the introduction of the journal, see SAP Note 2428741.</p>\n<p>As a result of this approach, all postings to G/L accounts with the G/L account time \"Secondary Costs\" are visible in general ledger accounting. This is in contrast to SAP Business Suite, where postings to secondary cost elements are not visible in general ledger accounting.</p>\n<p>This SAP Note describes how you can handle this difference.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>All postings to G/L accounts with the G/L account type \"Secondary Costs\" are always generated with debit and credit in the same G/L account. Thus, these postings always balance to zero from the account view.<br/><br/></li>\n<li>From the controlling view (segment, profit center, and so on), the amounts might naturally not balance to zero, since the debit side might be posted to segment A, for example, and the credit side to segment B.<br/>If the real-time integration of FI and CO is active in SAP Business Suite (this corresponds to the situation in an S/4HANA system where FI and CO are integrated automatically by the journal), a posting of this kind is also transferred to general ledger accounting. The difference is that the SAP Business Suite transfer is summarized and is made to an alternative parallel account (SAP Implementation Guide -&gt; Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Real-Time Integration of Controlling with Financial Accounting -&gt; Define Account Determination for Real-Time Integration).<br/>If the G/L accounts with the G/L account type \"Secondary Costs\" are listed in reporting in the same place as the corresponding parallel accounts, there are no longer any decisive differences between SAP Business Suite and S/4HANA. <br/><br/></li>\n<li>If you want to completely ignore postings to G/L accounts with the G/L account type \"Secondary Costs\" in general ledger reporting, you must make sure that these postings always balance to zero from the controlling view. You can use the following procedure to do this:</li>\n<ol>\n<li>You define your own P&amp;L statement account type (SAP Implementation Guide -&gt; Financial Accounting -&gt; General Ledger Accounting -&gt; Master Data -&gt; G/L Accounts -&gt; Preparations -&gt; Define Retained Earnings Account) with your own retained earnings account and assign this P&amp;L statement account type to all G/L accounts with the G/L account type \"Secondary Costs\".</li>\n<li>You define your own company code clearing account and define this for cross-company-code processes to G/L accounts with the G/L account type \"Secondary Costs\" (SAP Implementation Guide -&gt; Controlling -&gt; Cost Center Accounting -&gt; Manual Actual Postings -&gt; Additional Transaction-Related Postings -&gt; Assign Intercompany Clearing Accounts).<br/>Note the following:<br/>This configuration is visible only if ENTERPRISE_BUSINESS_FUNCTIONS \"<span class=\"SNO_DNT\" translate=\"no\">FINS_CO_ICO_PROC_ENH_101_RS</span>\" is active (SAP Customizing Implementation Guide -&gt; Activate Business Functions).</li>\n<li>You define your own document splitting zero-balance clearing account and define this for the account key \"001\" (SAP Implementation Guide -&gt; Financial Accounting -&gt; General Ledger Accounting -&gt; Business Transactions -&gt; Document Splitting -&gt; Define Zero-Balance Clearing Account).<br/>Note the following:<br/>This account key must be assigned to the business transaction variant <span class=\"SNO_DNT\" translate=\"no\">2000-0002</span>.</li>\n<li>Assign the business transaction variant <span class=\"SNO_DNT\" translate=\"no\">2000-0001</span> to the document types for CO postings with secondary costs (SAP Customizing Implementation Guide &gt; Financial Accounting &gt; General Ledger Accounting &gt; Business Transactions &gt; Document Splitting &gt; Classify Document Types for Document Splitting).<br/>In the case of postings with this special business transaction variant, the system forms separate zero-balance clearing items with the zero-balance clearing account for the account key assigned to the business transaction variant <span class=\"SNO_DNT\" translate=\"no\">2000-0002</span> for secondary costs. However, for posting lines that do not post to secondary cost types, the formation of the zero-balance clearing items takes place in the same document with the zero-balance clearing account for the account key assigned to the business transaction variant <span class=\"SNO_DNT\" translate=\"no\">2000-0001</span>.</li>\n<li>In reporting, you always group the G/L accounts with the G/L account type \"Secondary Costs\" with your G/L accounts listed above. As a result, there is always a zero balance per account assignment for these groupings.</li>\n</ol></ol>", "noteVersion": 2}, {"note": "2192251", "noteTitle": "2192251 - Transporting Settings Between S/4HANA Finance (or S/4HANA) and Classic ERP System", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to transport configuration settings, for example the content of customizing tables, between an S/4HANA Finance (or S/4HANA) system and a classic SAP ERP system.<br/>In Financial Accounting there are some customizing tables for which such transports will cause severe inconsistencies or other problems. <br/>This can occur in both directions: In case you transport from classic ERP to S/4HANA Finance (or S/4HANA) or in the opposite direction.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Your system landscape contains</p>\n<p>a) some systems which either are S/4HANA Finance or S/4HANA systems and</p>\n<p>b) some other systems that are on classic SAP ERP systems and</p>\n<p>c) you intend to transport configuration settings between these systems.</p>\n<p>For example, this is the case for the duration of a migration project migrating from classic SAP ERP to SAP Simple Finance, during which the production support landscape is still on classic ERP, the project development system is on S/4HANA Finance or S/4HANA and you need to retrofit changes from the production support environment to the project development system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Transporting table entries or other objects between SAP systems that are on different releases is in general problematic. This is described in the note <a href=\"/notes/1090842\" target=\"_blank\">1090842</a>.</p>\n<p>A special aspect in S/4HANA Finance is that the ABAP repository objects that belong to Financials are technically on a newer release compared to the \"classic\" ERP releases: Software component SAP_FIN is now on release 720 or 730. <br/><br/>Transporting content of the following tables is problematic. The tables are identical in S/4HANA Finance (from release 1503 onward) and S/4HANA (from release 1511 onward). <br/>Note: The term S/4HANA is used in the following as synonym for S/4HANA and S/4HANA Finance:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Table</strong></td>\n<td><strong>Description </strong></td>\n<td><strong>Status in S/4HANA</strong></td>\n<td><strong>Comment for S/4HANA</strong></td>\n<td><strong>Comment for Transport</strong><br/><strong>Classic ERP -&gt; S/4HANA</strong></td>\n<td><strong>Comment for Transport</strong><br/><strong>S/4HANA-&gt; Classic ERP</strong></td>\n</tr>\n<tr>\n<td>T881</td>\n<td>Ledger Master</td>\n<td>Partly obsolete</td>\n<td>\n<p>This table has been replaced by the new table FINSC_LEDGER for all entries that represent G/L ledgers: <br/>- For G/L ledgers, T881 is not evaluated any more during postings. <br/>- For other ledgers, for example special ledgers (FI-SL), PCA ledger (EC-PCA), enterprise consolidation ledgers (EC-CS), the table T881 is still evaluated.<br/>- There are some legacy tools that still evaluate the T881 table also fro G/L ledgers. Examples are report writer, drill-down reporting.</p>\n</td>\n<td>Importing table entries in S/4HANA that represent G/L ledgers will cause inconsistencies, since in S/4HANA also the entry in the new table FINSC_LD_CMP must be created or updated.</td>\n<td>In S/4HANA there is no IMG actity any more to update or transport T881 for G/L ledgers. </td>\n</tr>\n<tr>\n<td>FINSC_LEDGER</td>\n<td>Universal Journal Entry Ledger</td>\n<td>New</td>\n<td>Replaces T881 for G/L ledgers.</td>\n<td>Not possible, since the table does not exist in classic ERP.</td>\n<td>Will lead to import errors, since the table does not exist in classic ERP.</td>\n</tr>\n<tr>\n<td>T882G</td>\n<td>Company Code - Ledger Assignment</td>\n<td>Obsolete</td>\n<td>The table is replaced by FINSC_LD_CMP. T882G is not updated any more.</td>\n<td>Import in S/4HANA should have no effect in G/L, but there are some legacy tools which still perform SELECTs on T882G in S/4HANA.</td>\n<td>In S/4HANA there is no IMG actity any more to update or transport T882G. </td>\n</tr>\n<tr>\n<td>FINSC_LD_CMP</td>\n<td>CompCode-Dependent Settings for Universal Journal Entry Ledger</td>\n<td>New</td>\n<td>Replaces T882G. in contrary to T882G this new tables contains also entries for the leading ledger.</td>\n<td>Not possible, since the table does not exist in classic ERP.</td>\n<td>Will lead to import errors, since the table does not exist in classic ERP.</td>\n</tr>\n<tr>\n<td>T001A</td>\n<td>Additional Local Currencies Control for Company Code</td>\n<td>From release S/4HANA 1511 on: Obsolete<br/><br/>Before release S/4HANA 1511: Coupled with table FINSC_LD_CMP</td>\n<td>\n<p>From release S/4HANA 1511 on, this table is replaced by FINSC_LD_CMP. Technically, SELECTs from T001A are re-directed to a view on the new table FINSC_LD_CMP by the database interface. See transaction SE11 for T001A: Goto-&gt;Proxy object.</p>\n<p>Before release S/4HANA 1511, the content of this table is synched with table FINSC_LD_CMP for the leading ledger: The content of currency fields like FINSC_LD_CMP-CURTPO, -CURPOSO, -CURTPK etc. is synched with T001A.</p>\n</td>\n<td>\n<p>From release S/4HANA 1511 on the import in S/4HANA will have no effect, since T001A is not evaluated any more.</p>\n<p>Before release S/4HANA 1511 the import in S/4HANA will cause severe inconsistencies, since the content of table FINSC_LD_CMP is not updated.</p>\n</td>\n<td>From release S/4HANA 1511 on the export from an S/4HANA system is not possible any more.<br/><br/>Before release S/4HANA 1511 T001A is filled correctly and can be exported.</td>\n</tr>\n<tr>\n<td>T001</td>\n<td>Company Codes</td>\n<td>Coupled with table FINSC_LD_CMP</td>\n<td>\n<p>The content of table T001 must be in sync with table FINSC_LD_CMP: In detail:</p>\n<p>- For each entry in table T001 there must be an entry in table FINSC_LD_CMP for the leading ledger; this affects INSERTs as well as DELETEs.<br/>- For the leading ledger, the content of fields T001-OPVAR and T001-PERIV are redundantly stored in table FINSC_LD_CMP.</p>\n</td>\n<td>Import in S/4HANA will cause a mismatch with table FINSC_LD_CMP, since FINSC_LD_CMP is not updated by the import.</td>\n<td>Ok, no issues.</td>\n</tr>\n<tr>\n<td>TKA01</td>\n<td>Controlling Areas</td>\n<td>Coupled with table FINSC_LD_CMP</td>\n<td>The content of field TKA01-CTYP is redundantly stored in table FINSC_LD_CMP for the leading ledger.</td>\n<td>Import in S/4HANA will cause a mismatch with table FINSC_LD_CMP, since FINSC_LD_CMP is not updated by the import.</td>\n<td>Ok, no issues.</td>\n</tr>\n<tr>\n<td>TKA02</td>\n<td>Controlling Area Assignment</td>\n<td>Coupled with table FINSC_LD_CMP</td>\n<td>INSERTs and DELETEs in this table lead to UPDATEs of some fields of table FINSC_LD_CMP, for example fields CURTPK, CURPOSK etc.</td>\n<td>Import in S/4HANA will cause a mismatch with table FINSC_LD_CMP, since FINSC_LD_CMP is not updated by the import.</td>\n<td>Ok, no issues.</td>\n</tr>\n<tr>\n<td>SKA1</td>\n<td>G/L Account Master (Chart of Accounts)</td>\n<td>DDIC-Structure and table content changed</td>\n<td>There is the new field SKA1-GLACCOUNT_TYPE. The table contains not only the G/L accounts, but also the former secondary cost elements.</td>\n<td>Import in S/4HANA will not work because of the new field GLACCOUNT_TYPE.<br/>But: Distribution of G/L accounts via ALE into an S/4HANA system works.</td>\n<td>Import in classic ERP will not work because of the new field GLACCOUNT_TYPE.</td>\n</tr>\n<tr>\n<td>SKB1</td>\n<td>G/L Account Master (Company Code)</td>\n<td>Table content changed</td>\n<td>The table contains not only the G/L accounts, but also the former secondary cost elements.</td>\n<td>Import in S/4HANA would technically work, but since the GL account must also exist in table SKA1, transporting SKB1 entries does probably not make sense. <br/>But: Distribution of G/L accounts via ALE into an S/4HANA system works.</td>\n<td>Import in classic ERP system will cause inconsistencies: The export from S/4HANA will contain the secondary cost elements which are not expected in classic ERP.</td>\n</tr>\n<tr>\n<td>CSKA</td>\n<td>Cost Elements (Data Dependent on Chart of Accounts)</td>\n<td>Unchanged</td>\n<td>Tables CSKA and CSKB can be updated simultaneously with tables SKB1 and SKA1 by transaction FS00. The tables CSKA and CSKB contain the same content as in classic ERP. They are evaluated like in classic ERP. There is no separate UI any more for maintaining CSKA or CSKB.</td>\n<td>Importing (secondary) cost elements in S/4HANA will require manual rework with transaction FS00 to create the corresponding G/L account in SKA1 and SKB1 (optionally). Technically this is not regarded as an inconsistency. But for a user the missing G/L account might be regarded as an inconsistency, because actual postings in CO will not be possible with this cost element.</td>\n<td>Ok.</td>\n</tr>\n<tr>\n<td>CSKB</td>\n<td>Cost Elements (Data Dependent on Controlling Area)</td>\n<td>Unchanged</td>\n<td>See comments for table CSKA.</td>\n<td>See comments for table CSKA.</td>\n<td>Ok.</td>\n</tr>\n<tr>\n<td>FAGL_ACTIVEC</td>\n<td>Activation of New General Ledger</td>\n<td>Unchanged</td>\n<td>NewGL must be active in S/4HANA: The table must contain an entry with field ACTIVE = 'X'.</td>\n<td>Ok, with the restriction that the entry may not be deleted or updated to ACTIVE = ' '.</td>\n<td>Ok.</td>\n</tr>\n<tr>\n<td>TCKMHD</td>\n<td>Description of Material Ledger Type</td>\n<td>Unchanged</td>\n<td>Table contains the definition which currencíes are used in material ledger.</td>\n<td>If flag \"Use Currency Types from FI\" and/or \"Use Currency Types from Controlling\" is marked =&gt; no problems<br/>If flag  \"Currency Types defined manually\" ==&gt; see comment for table TCKMIT.</td>\n<td>Ok.</td>\n</tr>\n<tr>\n<td>TCKMIT</td>\n<td>Currency Types and Valuation Categories for Mat. Ledger Type</td>\n<td>Unchanged</td>\n<td>Table contains the list of manually defined currency types.</td>\n<td>\n<p>Contains the list of own currencies for a material ledger.</p>\n<p>1)<br/>The list of these own currencies must be a subset the currencies in FI and CO.</p>\n<p>2)<br/>Currency Types for \"Group valuation\"  or \"Profit center Valuation\" are not allowed.</p>\n<p>Otherwise the migration of the ledger customizing (transaction FINSC_MIG_LEDGER_CUST) is not possible.</p>\n</td>\n<td>Ok.</td>\n</tr>\n<tr>\n<td>\n<p>T093</p>\n</td>\n<td>\n<p>Real and derived depreciation areas</p>\n</td>\n<td>\n<p>New field ACC_PRINCIPLE</p>\n</td>\n<td>\n<p>Contents of table change a lot.</p>\n</td>\n<td>\n<p>Importing in S/4HANA will not work because:</p>\n<ol>\n<li>The new field ACC_PRINCIPLE is introduced and is a mandatory field for depreciation areas;</li>\n<li>The content of S/4HANA is different as classic ERP;</li>\n</ol></td>\n<td>\n<p>Importing in classic ERP will not work because of the new field ACC_PRINCIPLE.</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>T093A</p>\n</td>\n<td>Real depreciation area</td>\n<td>Unchanged</td>\n<td>Table T093A will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093A manually via IMG.</td>\n<td>Importing in classic ERP will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093A manually via IMG.</td>\n</tr>\n<tr>\n<td>\n<p>T093B</p>\n</td>\n<td>Company code-related depreciation area specifications</td>\n<td>Unchanged</td>\n<td>Table T093B will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093B manually via IMG.</td>\n<td>Importing in classic ERP will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093B manually via IMG</td>\n</tr>\n<tr>\n<td>\n<p>T093U</p>\n</td>\n<td>Options for asset legacy data transfer</td>\n<td>Unchanged</td>\n<td>Table T093U will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093U manually via IMG.</td>\n<td>Importing in classic ERP will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093U manually via IMG.</td>\n</tr>\n<tr>\n<td>T093D</td>\n<td>Control dep. posting</td>\n<td>Unchanged</td>\n<td>Table T093D will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093D manually via IMG.</td>\n<td>Importing in classic ERP will cause table inconsistency with table T093 because it is not possible to delete/add depreciation areas to table T093D manually via IMG.</td>\n</tr>\n<tr>\n<td>T093S</td>\n<td>Value Field Names for Depreciation Areas</td>\n<td>Unchanged</td>\n<td>Table T093S will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will cause table inconsistency with T093 because it is not possible to delete/add depreciation areas to table T093S manually via IMG.</td>\n<td>Importing in classic ERP will cause table inconsistency with T093 because it is not possible to delete/add depreciation areas to table T093S manually via IMG.</td>\n</tr>\n<tr>\n<td>T082AVIEWB</td>\n<td>View Authorization for Depreciation Areas</td>\n<td>Unchanged</td>\n<td>Table T082AVIEWB  will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will cause table inconsistency with T093 because the added/deleted area in T093 cannot be added/deleted into/from table T082AVIEWB manually via IMG activity.</td>\n<td>Importing in classic ERP will cause table inconsistency with T093 because the added/deleted area in T093 cannot be added/deleted into/from table T082AVIEWB manually via IMG activity</td>\n</tr>\n<tr>\n<td>ANKB</td>\n<td>Asset class: depreciation area</td>\n<td>Unchanged</td>\n<td>Table ANKB will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will cause table inconsistency with T093 because it is not possible to add/delete depreciation areas into/from table ANKB manually via IMG activity.</td>\n<td>Importing in classic ERP will cause table inconsistency with T093. There is no possibilty to add/delete depreciation areas manually into/from table ANKB via IMG-activity.</td>\n</tr>\n<tr>\n<td>T090M</td>\n<td>Table for maximum depreciation amounts</td>\n<td>Unchanged</td>\n<td>Table T090M can be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will be possible. But there is a manual step to be done to go to the IMG activity -&gt; Asset Accounting (New) -&gt; Depreciation -&gt; Valuation Methods -&gt; Further settings -&gt; Define Maximum Base Value: check whether the deleted area exists in the table and delete the table entry accordingly.</td>\n<td>Importing in classic ERP will be possible. But there is a manual step to be done to go to the IMG activity -&gt; Asset Accounting -&gt; Depreciation -&gt; Valuation Methods -&gt; Further settings -&gt; Define Maximum Base Value: check whether the deleted area exists in the table and delete the table entry  accordingly</td>\n</tr>\n<tr>\n<td>T093Y</td>\n<td>Shortened fiscal year</td>\n<td>Unchanged</td>\n<td>Table T093Y can be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will be possible. But there is a manual step to be done to go to the IMG activity -&gt; Asset Accounting (New) -&gt; General Valuation -&gt;Fiscal Year Specification -&gt; Shortenend Fiscal Year -&gt; define Reduction Rules for Shortened Fiscal Year: check whether the deleted area exists in the table and delete the table entry accordingly.</td>\n<td>Importing in classic ERP will be possible. But there is a manual step to be done to go to the IMG activity -&gt; Asset Accounting-&gt; General Valuation -&gt;Fiscal Year Specification -&gt; Shortenend Fiscal Year -&gt; define Reduction Rules for Shortened Fiscal Year: check whether the deleted area exists in the table and delete the table entry accordingly.</td>\n</tr>\n<tr>\n<td>TABWU</td>\n<td>Special Treatment of Retirements</td>\n<td>Unchanged</td>\n<td>Table TABWU can be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA will be possible. But there is a manual step to be done to go to the IMG activity -&gt; Asset Accounting (New) -&gt; Transactions -&gt; Retirements -&gt; Gain/Loss Posting -&gt; Determine Posting Variants: check whether the deleted area exists in the table and delete the table entry  accordingly.</td>\n<td>Importing in classic ERP will be possible. But there is a manual step to be done to go to the IMG activity -&gt; Asset Accounting -&gt; Transactions -&gt; Retirements -&gt; Gain/Loss Posting -&gt; Determine Posting Variants: check whether the deleted area exists in the table and delete the table entry accordingly.</td>\n</tr>\n<tr>\n<td>TABWA</td>\n<td>Transaction types/dep. areas</td>\n<td>Obsolet since EHP7 if Business Function FIN_AA_PARALLEL_VAL is active and the Softswitch T093_BSN_FUNC-PARALLEL_VAL has status '2' (Active)</td>\n<td>The table content is obsolet. Table TABWA will be updated simultaneously with table T093 via transaction code OADB.</td>\n<td>Importing in S/4HANA should have no effect in FI-AA.</td>\n<td>Importing in classic ERP is not suggested. In S/4HANA there is no IMG activity any more to update or transport TABWA.</td>\n</tr>\n<tr>\n<td>T096</td>\n<td>Chart of depreciation</td>\n<td>New field PARVAL_SCENARIO</td>\n<td>Depending on the settings of T093 the field PARVAL_SCENARIO will be set accordingly.</td>\n<td>Importing in S/4HANA will not work because of the new field PARVAL_SCENARIO which is set automatically by changing T093.</td>\n<td>Importing in classic ERP will not work because of the new field PARVAL_SCENARIO.</td>\n</tr>\n<tr>\n<td>T093_BSN_FUNC</td>\n<td>ERP Business Function for FI-AA</td>\n<td>New field PARALLEL_VAL</td>\n<td>New field PARALLEL_VAL introduced to the table since SAP_APPL617</td>\n<td>Importing in S/4HANA will not work because of the new field PARALLEL_VAL.</td>\n<td>Importing in classic ERP will not work because the new field PARALLEL_VAL doesn't exit in system older then SAP_AAPL617.</td>\n</tr>\n<tr>\n<td>T095_ACI</td>\n<td>Technical Clearing Account for Integrated Asset Acquisition</td>\n<td>New</td>\n<td>New customizing table since SAP_APPL617.</td>\n<td>Importing in S/4HANA is not possible, since the table T095_ACI does not exist in classic ERP.</td>\n<td>Importing in classic ERP will lead to import errors, since the table T095_ACI does not exist in classic ERP.</td>\n</tr>\n<tr>\n<td>T093C</td>\n<td>Company codes in Asset Accounting</td>\n<td>New field REVDMETH</td>\n<td>New field REVDMETH introduced to the table T093C since SAP_APPL617.</td>\n<td>Importing in S/4HANA will not work because of the new field REVDMETH.</td>\n<td>\n<p>Importing in classic ERP will not work because the new field REVDMETH doesn't exit in system older then SAP_APPL617.</p>\n</td>\n</tr>\n<tr>\n<td>FGL_BCF_PRE</td>\n<td>Preview Result Table Temporary</td>\n<td>NEW</td>\n<td>This table is designed to keep preview data</td>\n<td>Importing in S/4HANA is not possible, since the table FGL_BCF_PRE does not exist in classic ERP.</td>\n<td>\n<p>Importing in classic ERP will lead to import errors, since the table FGL_BCF_PRE does not exist in classic ERP.</p>\n</td>\n</tr>\n<tr>\n<td>FAC_OPP_JOB</td>\n<td>Updating Records for Posting Period Job</td>\n<td>NEW</td>\n<td>this table is designed to keep job related data, including parameters and time.</td>\n<td>Importing in S/4HANA is not possible, since the table FAC_OPP_JOB does not exist in classic ERP.</td>\n<td>\n<p>Importing in classic ERP will lead to import errors, since the table FAC_OPP_JOB does not exist in classic ERP.</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Notes:</p>\n<ul>\n<li>In case of an upgrade to S/4HANA new S/4HANA tables are filled by the migration of the ledger customizing, transaction FINS_MIG_LEDGER_CUST.</li>\n<li>In S/4HANA there are additional new customizing tables which are not listed above. These additional tables are needed for functionality that is new in S/4HANA compared to classic ERP, that is, there is no interference between S/4HANA and classic ERP. Examples:</li>\n<ul>\n<li>Extension ledgers<br/>Related customizing table: FINSC_LEDGER_REP.</li>\n<li>Customer-defined currency types<br/>Related customizing tables: FINSC_CURTYPE, FINSC_001A.</li>\n<li>Assignment of accounting principles to ledgers and company codes<br/>Related customizing table: FINSC_LD_CMP_AP.</li>\n<li>Other technical changes like the introduction of a field catalog, table FINSC_ACDOC_FCT.</li>\n</ul>\n<li>In S/4HANA there are some features obsolete compared to classic ERP. The corresponding customizing activities have been removed from the IMG tree, so they cannot be accessed by the user any more; examples are:</li>\n<ul>\n<li>Real-time integration CO-FI<br/>Related customizing tables: FAGLCOFI* like FAGLCOFIVARC, FAGLCOFICCODEC etc.</li>\n<li>New G/L scenarios<br/>Related customizing tables: FAGL_SCENARIO, FAGL_LEDGER_SCEN.</li>\n</ul>\n<li>Despite customizing transports (transport requests containing R3TR TABU SKA1 or SKB1 in the object list) does not work for G/L accounts, they can nevertheless be distributed via <strong>ALE</strong> (Application Link Enabling) from a classic ERP system to S/4HANA, for example from a MDG-F 'Master Data Governance - Financials' system: In S/4HANA there is an ALE inbound logic that fills the new field SKA1-GLACCOUNT_TYPE.</li>\n<li>Regarding <strong>CO-PA data structures, customizing and master data</strong> it is highly recommended to <strong>NOT</strong> transport any settings between an S/4HANA system and a classic ERP system (e.g. via transport tool in transaction KE3I). <strong>This can cause unpredictible results. </strong>The corresponding tables and other transport objects are not contained in the above list of tables.</li>\n</ul>", "noteVersion": 13}, {"note": "2156822", "noteTitle": "2156822 - S/4HANA Finance: Mapping of ACDOCA fields to old FI/CO tables", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You need to know how fields of the universal journal entry table ACDOCA map to old tables. E.g. because you need to change a custom view to select data directly from ACDOCA instead of COEP, which is actually a compatibility view on ACDOCA.</p>\n<p>You need to know which new fields were added to table ACDOCA in which release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The attachment provides a list of all ACDOCA fields as of sFIN OP 1503 SP1, and their mapping to BSEG, COEP and FAGLFLEXA.</p>\n<p>Fields introduced in S/4HANA Finance 1605, S/4HANA 1610 and S/4HANA 1709 have been added to the attachment with version 3 of this note.</p>\n<p>Fields introduced in S/4HANA 1809, 1909 and 2020 have been added to the attachment with version 4 of this note.</p>\n<p>Fields introduced in S/4HANA 2021, 2022 and 2023 have been added to the attachment with version 7 of this note.</p>", "noteVersion": 7}, {"note": "2185026", "noteTitle": "2185026 - Compatibility views COSP, COSS, COEP, COVP: How do you optimize their use?", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In your customer-specific programs, you use the compatibility views COSP, COSS, COEP, and COVP to determine CO-relevant documents or totals records.</p>\n<p>The performance of the compatibility views is insufficient.</p>\n<p>You want to optimize these accesses.</p>\n<p>You want to understand the data model behind the compatibility views.</p>\n<p>They terminate when you use them in transaction SE16 if datasets are large.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>COSSA, COSPA, SELECT, ABAP, SQL error 2048</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For sFIN 1503 (SAP_FIN 720), the controlling document was transferred to the new table ACDOCA.</p>\n<p>For sFIN 1.0 (SAP_FIN 700), the totals tables COSP and COSS were replaced by views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In principle, you can optimize selection using compatibility views in four ways:</p>\n<ol>\n<li>By optimizing the selection conditions when the compatibility views are used in the program.</li>\n<li>By replacing the selection of compatibility views with adequate replacement CDS views that, for example, have the same structure (identical field string) as the compatibility views but sometimes have a reduced scope of functionality. This means that they are faster than the delivered compatibility views.</li>\n<li>By replacing the selection for the CO-relevant actual postings with direct access to the new line item table ACDOCA. Reading the other data from the relevant tables.</li>\n<li>By replacing the selection of compatibility views with a standard ABAP class delivered by SAP.</li>\n</ol>\n<p>The third option is recommended long term - especially for actual postings from ACDOCA.</p>\n<p> </p>\n<p><span>Usage is not suitable in SE16:</span></p>\n<p>The compatibility views are generally <span>not suitable</span> for the selection of large datasets, top N, or similar queries using transaction SE16. This can cause a memory overflow, among other issues. Depending on the information you need, use the tables ACDOCA, COSP_BAK, or COSS_BAK or the view V_COEP_ORI instead in SE16, with the suitable selection criteria described here. The following information about the selection criteria you should avoid when using the compatibility views in ABAP source code generally also applies to their use in SE16.</p>\n<p> </p>\n<p><span>Change as of SAP_FIN 730 and S4CORE 101:</span></p>\n<ul>\n<li>As of this release, the compatibility views also support actual versions &lt;&gt; '000'. Background: Transfer prices are now supported in Controlling.</li>\n<li>Customizing table FINSC_CMP_VERSNC for linking the version and ledger replaces the table FINSC_LD_CMP. Important: For data selections in Version \"000\", you can simply replace the table. For data selections with actual version &lt;&gt; '000', this does not suffice.</li>\n</ul>\n<p>See the section entitled \"Actual versions &lt;&gt; '000'\".</p>\n<p> </p>\n<p> </p>\n<p>First, the data model changed in SFIN will be described below.</p>\n<p><span><strong>Data model of the compatibility views in sFIN</strong></span></p>\n<p><strong>The compatibility views replace the tables COSP and COSS and are used to redirect read accesses for the table COEP and the ABAP Dictionary view COVP. This ensures that <span>all</span> programs that access COSP, COSS, COEP, and COVP in read mode continue to work without losing any functions.</strong></p>\n<p>Compatibility views are built as CDS views (DDL views).</p>\n<p>Tip: The compatibility views and their implementation as a hierarchy of subviews can be completely viewed only using the workbench \"ABAP in Eclipse\". You can find the compatibility views in the packages KACC_ERP50 (for COSP, COSS, COEP) and KACC (for COVP). Only a restricted display is possible in transaction SE11.</p>\n<p><strong>SAP_FIN 700</strong></p>\n<p>For SAP_FIN 700, the former totals tables COSP and COSS were already replaced by CDS views of the same name (DDL views) - also called compatibility views. During the installation/upgrade, the system automatically transfers previous data from COSP and COSS to the relevant, new tables COSP_BAK and COSS_BAK.</p>\n<p>These compatibility views COSP and COSS calculate the actual postings from the line item table COEP ad hoc and read the other data from the new tables COSP_BAK and COSS_BAK.</p>\n<p>Note for posting:</p>\n<ul>\n<li>All of the data with the value type &lt; &gt; 04 and &lt; &gt; 11 is still written to the tables COSP_BAK and COSS_BAK.</li>\n<li>You are not allowed to directly add, change, or delete data with a value type = 04 and = 11.</li>\n<li>SQL operations for data manipulation in COSP and COSS are no longer possible. They lead to a syntax error.</li>\n</ul>\n<p>This is ensured by using the classes CL_FCO_COSP_UPDATE and CL_FCO_COSS_UPDATE instead of direct INSERT, UPDATE, DELETE, MODIFY commands using SQL on the previous tables COSP and COSS. Important: These classes do not ensure that the actual costs of the value type 04 and 11 are updated to the table COEP (or ACDOCA as of SAP_FIN 720). Use the available interfaces (such as BAPIs) for your customer-specific update of these actual costs.</p>\n<p><strong>Data source for COSP and COSS (700):</strong></p>\n<ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = 04 (current value) </li>\n<li>WRTTP = 11 (statistical current value)</li>\n</ul>\n<li>From the tables COSP_BAK or COSS_BAK:</li>\n<ul>\n<li>All other WRTTPs</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>SAP_FIN 720 and higher</strong></p>\n<p>As of SAP_FIN 720, the previous actual postings from the table COEP are transferred to the table ACDOCA.</p>\n<p>The table COEP is <span>automatically</span> redirected to the view V_COEP only for <span>read accesses (ABAP Open SQL statement SELECT)</span>. In the same way, the ABAP Dictionary view COVP is automatically redirected to the CDS view V_COVP.</p>\n<p>As such, the table COEP remains intact.</p>\n<p>Important tips for the redirection:</p>\n<ul>\n<li>This redirection is visible in transaction SE11 in the menu under \"Utilities-&gt; Runtime Object-&gt;Display\": In the table \"Header of active runtime object\", you will find the view that is redirected to in the column \"Physical table\".</li>\n<li>The <span>redirection</span> is <span>not active</span> in SAP HANA objects such as CDS views, calculation views, analytical views, and attribute views. A selection in COEP is not redirected in SAP HANA views but selects the data from the original table. </li>\n<li>You <span>must</span> activate the redirection in SAP NetWeaver 7.40 as a step in the migration or in the basic Customizing of the general ledger. Otherwise, no postings can be made in the system.</li>\n</ul>\n<p>The compatibility view V_COEP calculates the actual postings from the line item table ACDOCA ad hoc and reads the other data that was previously posted and continues to be posted in COEP from the table COEP. The compatibility view V_COVP calculates the actual postings from the line item table ACDOCA ad hoc and reads the other data from the tables COEP and COBK. This ensures that <span>all</span> programs that use a SELECT operation in COEP and COVP continue to work with no functional compromises.</p>\n<p>If you want to know which data is in the physical table COEP, use the CDS view V_COEP_ORI. This view selects the data directly from the table COEP. (Reason: Generally, the redirection, as mentioned above, is not active within CDS views.)</p>\n<p><span>Important:</span> Change of COSP or COSS in SAP_FIN 720: Due to the transfer of the CO actual postings to ACDOCA, since SAP_FIN 720 the views COSP/COSS have read the actual postings of a value type = 04 from ACDOCA and not from COEP accordingly (as in SAP_FIN 700, see above).</p>\n<p>Note for posting:</p>\n<ul>\n<li>The actual postings (value type 04 and relevant statistical line items of the value type 11) from the table COEP are written in one shared line item to the table ACDOCA.</li>\n<li>All of the data with the value type &lt; &gt; 04 is still written to the table COEP.</li>\n<li>The statistical actual postings (value type 11) are written to both ACDOCA and COEP. The CO compatibility views always select this data from COEP for performance reasons. The new report (VDM, SAP Fiori) reads this data from ACDOCA.</li>\n<li>You are not allowed to directly add, change, or delete data with a value type = 04 in COEP.</li>\n<li>The table for the document header, COBK, is still updated for <span>all</span> CO-relevant actual postings.</li>\n<li>CO-relevant postings in ACDOCA that are entered in SAP_FIN 720 always have a document header in BKPF as well.</li>\n<li>Therefore, migrated CO documents from the legacy system have only one document header in BKPF if a connection to the FI document was found in the migration. As mentioned, the COBK entry is <span>always</span> available.</li>\n</ul>\n<p><strong><strong><strong>Data source for</strong> COSP and COSS (720 et seq.)</strong>:</strong></p>\n<ul>\n<li>From the table ACDOCA:</li>\n<ul>\n<li>WRTTP = 04 (Actual) </li>\n</ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = 11 (Statistical Actual)</li>\n</ul>\n<li>From the tables COSP_BAK and COSS_BAK:</li>\n<ul>\n<li>All other WRTTPs</li>\n</ul>\n</ul>\n<p><strong><strong>Data source for</strong> COEP (720 et seq):</strong></p>\n<ul>\n<li>From the table ACDOCA:</li>\n<ul>\n<li>WRTTP = 04 (Actual) </li>\n</ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = '11' (statistical actuals); note: For technical performance reasons, this data is read from COEP and not from ACDOCA.</li>\n<li>All other WRTTPs</li>\n</ul>\n</ul>\n<p><strong><strong>Data source for</strong> COVP (720 et seq):</strong></p>\n<ul>\n<li>From the table ACDOCA:</li>\n<ul>\n<li>WRTTP = 04 (Actual) </li>\n</ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = '11' (statistical actuals); note: For performance reasons, this data is read from COEP and not from ACDOCA, where it should actually also be available.</li>\n<li>All other WRTTPs</li>\n</ul>\n<li>The document header is still from the table COBK.</li>\n</ul>\n<p> </p>\n<p> </p>\n<p>The three ways of optimizing the selection are listed below:</p>\n<p><span><strong>1</strong></span><strong><span>st way: Optimize the selection conditions</span> </strong></p>\n<p>A general recommendation cannot be given because the performance of the SELECT command depends on the system properties, the system settings, and the data volume in the tables ACDOCA, COEP, COBK, COSP_BAK, COSS_BAK, and all other tables involved. Test these alternatives before you use them in your production system.</p>\n<ul>\n<li>Avoid the JOIN operation of large tables (MARA, CE4xxxx, CKIS, CSKS, for example) with compatibility views.</li>\n<li>Avoid SELECT * ... . Instead, use specific, restricted field lists in the SELECT statement.</li>\n<li>Avoid a large number of SELECT SINGLE or SELECT commands with very detailed selection conditions on the one hand (only for one CO object (OBJNR) or only one cost element (KSTAR), for example). Package the SELECT command. Package sizes of 50 - 1000 CO objects or cost elements have proved to be sensible. Proceed in the same way for other selection criteria - except for the value type (WRTTP). The optimal package size depends on the conditions mentioned above.</li>\n<li>Avoid a SELECT command with very large WHERE conditions on the other hand (more than 1000 CO objects or more than 1000 cost elements, for example). Package here as described in the previous point.</li>\n<li>The usage of RANGES in the WHERE condition appears better in performance measurements than the usage of the SELECT in a LOOP.</li>\n<li>Avoid the FOR ALL ENTRIES command. In particular, FOR ALL ENTRIES with an internal table from which more than one field value is queried in the WHERE condition is extremely performance critical. Package here as described in the previous point.</li>\n<ul>\n<li>Example: SELECT ... FOR ALL ENTRIES in LT_ABC WHERE objnr = LT_ABC-objnr AND vrgng = LT_ABC-vrgng. Avoid conditions of this kind. Instead, use separate RANGES tables for OBJNR and VRGNG in the WHERE condition.</li>\n<li>If you have programmed a selection for a compatibility view with several value types, split this selection into several selections - split according to value type (field WRTTP) - and then merge the results in the program. That is sometimes faster than a selection using the compatibility view with all value types. Particularly split after:</li>\n<li>Value type 04, separate</li>\n<li>Value type 11, separate</li>\n<li>All other value types together, separate</li>\n</ul>\n<li>Avoid using selections such as \"SELECT objnr INTO ld_objnr UP TO 1 ROWS FROM COEP\" WHERE OBJNR = I_OBJNR or similar to determine whether one entry or no entry is delivered.</li>\n<ul>\n<li>Perform these queries for data of the value type 04 directly on ACDOCA. <span>Never</span> read data of the value type 04 from COEP, COSP_BAK, or COSS_BAK.</li>\n<li>For the <span>other value types</span>, select directly from the relevant original tables:</li>\n<ul>\n<li>COEP via view V_COEP_ORI, (not for 04)</li>\n<li>COBK (not for 04)</li>\n<li>COSP_BAK (not for 04, 11)</li>\n<li>COSS_BAK (not for 04, 11)</li>\n</ul>\n<li>Example: See the function module K_OBJECT_DATA_EXIST following the implementation of SAP Note 2160629.</li>\n</ul>\n<li> Avoid selections without the specification of the fiscal year and period. Wherever possible, restrict the period.</li>\n</ul>\n<p> </p>\n<p><span><strong>2nd way: Selection with replacement views </strong></span></p>\n<p>Until now, SAP has only delivered replacement views with a reduced scope of functionality for COVP.</p>\n<p>Instead of COVP, you can use optimized replacement views that have the same structure as COVP but have better performance because some technical fields are not calculated - SAP Note 2159922 (\"sFIN: Performance of COVP\"):</p>\n<ul>\n<li>If you do not need the values of the fields OBJNR_N1, OBJNR_N2, OBJNR_N3, and TIMESTMP of COVP for real actual postings, use V_COVP_KAEP instead of COVP.</li>\n<li>See SAP Note 2178343 for an example implementation of the alternative use of these views in the standard program of the line item display.</li>\n</ul>\n<p> </p>\n<p>The optimized views V_COSP_R and V_COSS_R are available for COSP and COSS. They have identical scopes of functionality that provide improved performance in the case of a large data volume. Please see SAP Note 2222535 in this regard.</p>\n<p>For small and medium-sized data volumes, access via COSP and COSS provides better performance.</p>\n<p> </p>\n<p><span><strong>3</strong><strong>rd way: Selection from ACDOCA</strong></span></p>\n<p>Note that only the CO-relevant actual postings from the table ACDOCA are to be determined. This corresponds to the data that earlier was saved in the old tables COEP, COSP, and COSS under value type 04 and 11. Data of the value type 11 is also written in the table COEP for performance reasons.</p>\n<p>a) If you change your selection to ACDOCA, but still further process this data in your program using old structures (COEP, COSSA, COSPA, and so on), you need the mapping of the field names of the CO-relevant fields from ACDOCA with the fields from the previous line item table COEP. You can find this mapping in SAP Note 2156822 (\"sFIN: Mapping of ACDOCA fields to old FI/CO tables\"). This SAP Note provides information about the assignment of the partially different field names in the tables.</p>\n<ul>\n<li>Example: COEP-KSTAR (\"Cost Element\") corresponds 1:1 to the ACDOCA-RACCT (\"Account Number\") </li>\n</ul>\n<p>b) However, some fields of the compatibility views are not transferred 1:1 from a field of ACDOCA, but are calculated depending on various fields.</p>\n<ul>\n<li>Example: \"Total Value in Object currency\" COEP-WOGBTR generally corresponds to the field \"Value in Local Currency\" ACDOCA-HSL. Exception: If ACDOCA-RCO_OCUR is filled, the field COEP-WOGBTR is adopted from the \"Value in CO Object Currency\" field ACDOCA-CO_OSL.</li>\n</ul>\n<p> </p>\n<p><strong>Calculating COEP for value type 04 from ACDOCA</strong> (for Version '000'):</p>\n<ul>\n<li>To generate the COEP line items from ACDOCA, refer to</li>\n<ul>\n<li>SAP Note 2156822 (\"sFIN: Mapping of ACDOCA fields to old FI/CO tables\") (-&gt; for a)) and</li>\n<li>to the CDS view V_COEP_ACDOCA \"Mapping from ACDOCA to COEP\", which calculates most of the fields of the table COEP ( -&gt; for b)). This view is also used in the view hierarchy of V_COEP. You can determine everything apart from the calculation of the fields OBJNR_N1, OBJNR_N2, OBJNR_N3, and TIMESTMP here.</li>\n</ul>\n<li>To determine the CO-relevant document lines from ACDOCA, use the JOIN operation from the view V_COEP_ACDOCA or from the example below.</li>\n<ul>\n<li>Primarily, you set filters:</li>\n<ul>\n<li>For the fields CO_BELNR and CO_BUZEI. Both must be filled.</li>\n<li>For the CO-relevant ledger (ACDOCA-RLDNR). Your determine the relevant ledger per company code and CO version (normally version '000') from the customizing table FINSC_LD_CMP. <span>Important:</span> As of SAP_FIN 730 and S4CORE 101, you can no longer use the table FINSC_LD_CMP. In these releases, you must use the new table FINSC_CMP_VERSNC.</li>\n</ul>\n<li>If ACCASTY is filled, a posting that corresponds to the value type 04 is concerned (Warning: ACCASTY = space would be a posting of the cost element type 90. These are purely statistical). Use Customizing table FINSC_LD_CMP (as of SAP_FIN 730 or S4CORE 101: use the table FINSC_CMP_VERSNC) to determine the CO-relevant ledger (CO Version '000' is assigned to this ledger; see the migration IMG: \"Preparations and Migration of Customizing -&gt; Preparations and Migration of Customizing for General Ledger -&gt; Define Ledger for CO Version\")</li>\n</ul>\n<li>Recommendation for COEP-TIMESTMP (Format: Number of seconds since January 1, 1990 * 10000): In the customer program, use ACDOCA-TIMESTAMP (format: JJJJMMTThhmmss) instead, which uses one different format but is otherwise the same.</li>\n<li>In CO, you have the option of updating an object currency that is different than the posting currency. If you use this function, these values are in the field ACDOCA-CO_OSL. The relevant currency key is in ACDOCA-RCO_OCUR. If you do not use this free object currency function, you will find the so-called \"object currency\" (posted formerly in COEP-WOGBTR) in the field ACDOCA-HSL.</li>\n</ul>\n<p>Example of the filtering of CO-relevant document line items from the table ACDOCA for value type 04:</p>\n<p><strong>Data: </strong>lt_acdoca <strong>type table of </strong>acdoca.</p>\n<p>select * <strong>from </strong>acdoca <strong>as</strong> a</p>\n<p><strong>inner join</strong> finsc_ld_cmp <strong>as</strong> v <strong>on</strong> a<strong>~</strong>rbukrs <strong>=</strong> v<strong>~</strong>bukrs <strong>and</strong> a<strong>~</strong>rldnr <strong>=</strong> v<strong>~</strong>rldnr <strong>and </strong>v<strong>~</strong>versn <strong>ne space </strong>\"Important: or table FINSC_CMP_VERSNC</p>\n<p><strong>into corresponding fields of table </strong>lt_acdoca</p>\n<p><strong>where</strong> a<strong>~</strong>co_belnr <strong>ne space and</strong> a<strong>~</strong>co_buzei <strong>ne </strong>'000' <strong>and not</strong> a<strong>~</strong>accasty <strong>= space. </strong></p>\n<p> </p>\n<p><strong>Calculating COEP for value type 11 from ACDOCA</strong> (for Version '000'):</p>\n<p>This calculation is very complicated. Furthermore, it is no longer possible to determine an object currency of the statistical CO objects that differs from the real posting from the table ACDOCA.</p>\n<p>Recommendation: Therefore, directly use the table COEP to evaluate data of the value type 11. To do this, directly use the view V_COEP_ORI in ABAP.</p>\n<p><strong>Calculating COEP for additional value types:</strong></p>\n<p>Recommendation: To do this, directly use the view V_COEP_ORI in ABAP.</p>\n<p> </p>\n<p><strong>Calculating COSP/COSS for value type '04' from ACDOCA</strong> (for Version '000'):</p>\n<p>At this point, the determination of the diverse amounts and quantities (controlling area currency, transaction currency, object currency, and so on) should not be reproduced for the periods 1-16 and their aggregation. If you require it, refer to the programming of the view hierarchy of COSP and COSS.</p>\n<p><span>Recommendation:</span></p>\n<ol>\n<li>The on-the-fly calculations and aggregations of the line items are often an important factor in the insufficient performance of COSP and COSS. For customer programs, therefore, it is generally recommended that you <span>no longer</span> <span>use</span> the structures <span>COSP and COSS</span> and the data that must be aggregated for them for performance reasons. Instead, you should further process the data of the line items directly from ACDOCA.</li>\n<li>The example below shows which complex selection from ACDOCA (different from when actual line items are generated) may be necessary to determine the data foundation of the actual costs for the totals. If you cannot avoid using the structures COSP/COSS, thoroughly test your converted program with regards to its functionality and performance before you use it productively. </li>\n</ol>\n<p>To determine the data of the value type 04 that is relevant for COSP and COSS from ACDOCA, you must also read the adjustment postings from the \"Migration of Balances\" (see Customizing), except for the selection in accordance with \"Calculating the values of COEP of value type 04 from the values of ACDOCA\". These adjustment postings are created during the migration if, for example, you had already archived COEP line items using the archiving object CO_ITEM but had not archived the relevant totals. Therefore, these adjustment postings are relevant only if you have carried out a migration.</p>\n<p><span>Calculating the former lines from COSP/COSS for value type 04 from ACDOCA (for Version '000'):</span></p>\n<ul>\n<li>The balance adjustments do not have a CO document number in the field CO_BELNR and do not have a document header in COBK and BKPF because they are pure adjustment postings.</li>\n<li>To determine the CO-relevant adjustment postings from ACDOCA, follow the example below:</li>\n<ul>\n<li>The CO-relevant balance adjustments have the value ACDOCA-BSTAT = C and ACDOCA-MIG_SOURCE = C.</li>\n<li>The balance adjustments that are relevant for COSP and for COSS depend on the business transaction ACDOCA-VRGNG (JOIN with the table TJ01 and check of the indicator TJ01-COSP or TJ01-COSS for the relevant results analysis of the transaction for COSP or COSS).</li>\n<li>As a rule, the logical system of a posting in ACDOCA must correspond to the logical system of the client and controlling area (JOIN with TKA01 and T000).</li>\n</ul>\n<li>The filter criteria of the CO line items correspond to those from the point \"Calculating the values of COEP of value type 04 from the values of ACDOCA\".</li>\n</ul>\n<p> </p>\n<p>In the following example, the syntactically correct selection of the CO-relevant primary costs and secondary costs in <span>one</span> SELECT statement is illustrated.</p>\n<p>Example of the filtering of the necessary CO-relevant balance adjustments and the necessary CO relevant line items from ACDOCA for <span>value type 04 and COSP</span>:</p>\n<p><strong>Data: </strong>lt_acdoca <strong>type table of </strong>acdoca.</p>\n<p>select * <strong>from </strong>acdoca <strong>as</strong> a</p>\n<p><strong>inner join</strong> finsc_ld_cmp <strong>as</strong> v <strong>on</strong> a<strong>~</strong>rbukrs <strong>=</strong> v<strong>~</strong>bukrs <strong>and</strong> a<strong>~</strong>rldnr <strong>=</strong> v<strong>~</strong>rldnr <strong>and </strong>v<strong>~</strong>versn <strong>= </strong>'000' \"Important: or table FINSC_CMP_VERSNC</p>\n<p><strong>inner join</strong> tj01 <strong>as</strong> t <strong>on</strong> t<strong>~</strong>vrgng <strong>=</strong> a<strong>~</strong>vrgng <strong>and</strong> t<strong>~</strong>xcosp <strong>= </strong>'X' <strong>and</strong> t<strong>~</strong>xcoss = ' '</p>\n<p><strong>inner join</strong> tka01 <strong>as</strong> tk<strong> on</strong> tk~kokrs = a~kokrs</p>\n<p><strong>inner join</strong> t000 <strong>as</strong> s <strong>on</strong> ( s<strong>~</strong>logsys <strong>=</strong> a<strong>~</strong>logsyso <strong>or</strong> a<strong>~</strong>logsyso <strong>= </strong>' ' <strong>or</strong></p>\n<p><strong></strong><span><span>( </span></span><span><span>(</span></span><strong><strong><span><strong><span> </span></strong></span></strong><span></span></strong>a~accasty = 'KS' <strong>or</strong> a~accasty = 'KL' ) <strong>and</strong> ( s~logsys = tk~logsystem <strong>or</strong> tk~logsystem = ' ' ) )</p>\n<p>)</p>\n<p><strong>into corresponding fields of table @</strong>lt_acdoca</p>\n<p><strong><strong>where</strong></strong>(( a~co_belnr <strong><strong>ne </strong></strong>' '<strong><strong> and</strong></strong>a~co_buzei<strong> <strong>ne </strong></strong>'000'<strong> <strong>and not</strong></strong>a~accasty = ' ')<strong><strong> or </strong></strong></p>\n<p>( a<strong>~</strong>bstat <strong>= </strong>'C' <strong>and</strong> a<strong>~</strong>mig_source <strong>= </strong>'C' ))</p>\n<p><strong>and </strong>s~mandt = @sy-mandt.</p>\n<p> </p>\n<p>Example of the filtering of the necessary CO relevant balance adjustments and the necessary CO relevant line items (together) from ACDOCA for the <span>value type 04 and COSS</span>:</p>\n<p><strong>Data: </strong>lt_acdoca <strong>type table of </strong>acdoca.</p>\n<p>select * <strong>from </strong>acdoca <strong>as</strong> a</p>\n<p><strong>inner join</strong> finsc_ld_cmp <strong>as</strong> v <strong>on</strong> a<strong>~</strong>rbukrs <strong>=</strong> v<strong>~</strong>bukrs <strong>and</strong> a<strong>~</strong>rldnr <strong>=</strong> v<strong>~</strong>rldnr <strong>and </strong>v<strong>~</strong>versn <strong>= </strong>'000' \"Important: or table FINSC_CMP_VERSNC</p>\n<p><strong>inner join</strong> tj01 <strong>as</strong> t <strong>on</strong> t<strong>~</strong>vrgng <strong>=</strong> a<strong>~</strong>vrgng <strong>and</strong> t<strong>~</strong>xcosp <strong>= </strong>' ' <strong>and</strong> t<strong>~</strong>xcoss = 'X'</p>\n<p><strong>inner join</strong> tka01 <strong>as</strong> tk<strong> on</strong> tk~kokrs = a~kokrs</p>\n<p><strong>inner join</strong> t000 <strong>as</strong> s <strong>on</strong> ( s<strong>~</strong>logsys <strong>=</strong> a<strong>~</strong>logsyso <strong>or</strong> a<strong>~</strong>logsyso <strong>= </strong>' ' <strong>or</strong></p>\n<p><strong></strong><span><span>( </span></span><span><span>(</span></span><strong><strong><span><strong><span> </span></strong></span></strong><span></span></strong>a~accasty = 'KS' <strong>or</strong> a~accasty = 'KL' ) <strong>and</strong> ( s~logsys = tk~logsystem <strong>or</strong> tk~logsystem = ' ' ) )</p>\n<p>)</p>\n<p><strong>into corresponding fields of table @</strong>lt_acdoca</p>\n<p><strong><strong>where</strong></strong>(( a~co_belnr <strong><strong>ne </strong></strong>' '<strong><strong> and</strong></strong>a~co_buzei<strong> <strong>ne </strong></strong>'000'<strong> <strong>and not</strong></strong>a~accasty = ' ')<strong><strong> or </strong></strong></p>\n<p>( a<strong>~</strong>bstat <strong>= </strong>'C' <strong>and</strong> a<strong>~</strong>mig_source <strong>= </strong>'C' ))</p>\n<p><strong>and </strong>s~mandt = @sy-mandt.</p>\n<p> </p>\n<p>Tips for possible performance optimizations in the determination of CO-relevant data from ACDOCA in the form of the earlier totals structures:</p>\n<p>Sometimes, it is better to split the JOIN conditions with the tables T000, TKA01, TJ01, and FINSC_LD_CMP (or FINSC_CMP_VERSNC) into separate SELECTS and to use the results in the WHERE condition of the SELECT * FROM ACDOCA. Please check these options.</p>\n<ul>\n<li>Drag the selection of the T000-LOGSYS (with TKA01) in ABAP before the SELECT on ACDOCA and use LOGSYS in the WHERE condition of the SELECT on ACDOCA.</li>\n<li>If you must generate data for \"COSS\" and \"COSP\" but the distinction between primary and secondary costs is irrelevant, you can refrain from the JOIN with the table TJ01, which only controls which data (ACDOCA-VRGNG) belongs in COSP and in COSS.</li>\n<li>If you use just one or only a few company codes, you can place the determination of the CO-relevant ledger from FINSC_LD_CMP (or table FINSC_CMP_VERSNC) into a separate SELECT earlier on and specify the result in the WHERE condition of the SELECT * FROM ACDOCA.</li>\n</ul>\n<p> </p>\n<p><strong>Actual version</strong><strong>s &lt;&gt; '000' </strong>(as of SAP_FIN 730 and S4CORE 101)</p>\n<ul>\n<li>In principle, as of this release, the compatibility views also support actual versions &lt;&gt; '000'.</li>\n<li>In the table FINSC_LD_CMP, the version is no longer assigned to the ledger. This is now configured in the Customizing table FINSC_CMP_VERSNC. If you use the earlier table FINSC_LD_CMP for this purpose, a syntax error occurs. </li>\n<li>For data selections in Version \"000\", you can simply replace the previous table FINSC_LD_CMP with FINSC_CMP_VERSNC.</li>\n<li>For data selections with actual version &lt;&gt; '000', this does not suffice: In the table FINSC_CMP_VERSNC system, the assignment of ACDOCA value columns to the COSP, COSS, and COEP value columns is now also defined for the data in CO version &lt;&gt; '000’.</li>\n</ul>\n<p>Problem with full values in ACDOCA and earlier delta values in CO* tables:</p>\n<p>Note the following: Previously, the values in actual versions &lt;&gt; '000’ were saved in the former CO* tables as delta values (not full values). The actual full value in an actual version 'nnn' is always determined from the sum of the relevant database value in Version '000' and the database value in Version 'nnn'. This means that database values in the actual version 'nnn' must never be considered on their own.</p>\n<p>In the table ACDOCA, the data in different actual versions is always saved as a full value, either</p>\n<ul>\n<li>in the same ledger but in other value columns within ACDOCA</li>\n</ul>\n<p>or</p>\n<ul>\n<li>in separate ledgers (for each version).</li>\n</ul>\n<p>As mentioned above, the assignment of ACDOCA value columns to the COSP, COSS, and COEP value columns is configured in the Customizing table FINSC_CMP_VERSNC.</p>\n<p>The compatibility views use a complex logic for actual versions &lt;&gt; '000' to calculate delta values again - while using the Customizing table FINSC_CMP_VERSNC.</p>\n<p> </p>\n<p>For these reasons, we advise against calculating the CO delta values on the basis of the type of compatibility view:</p>\n<ul>\n<li>Instead, use the full values directly from ACDOCA while taking the Customizing table FINSC_CMP_VERSNC into consideration, which informs you which ACDOCA value column contains the appropriate value.</li>\n<li>Alternatively: SAP Note 2388871 also makes the access classes from SAP Note 2261720 available for actual versions &lt;&gt; '000'.</li>\n</ul>\n<p> </p>\n<p><span><strong>4th way: Alternative access classes</strong></span></p>\n<p>SAP Note <strong>2261720</strong> introduces new access classes that can be used as a replacement for the selection of COEP, COSS, and COSP. In some circumstances, this is easier than programming the corresponding SELECTS themselves. The advantage associated with using these access classes is that they output data in the structure COEP, COSS, and COSP. However, from a performance and technical perspective, it is still more favorable to make your specific selections directly in ACDOCA and avoid conversions to the structures COEP, COSS, and COSP. Of course, this requires that your programs forgo the structures COEP, COSS, and COSP.</p>\n<p>SAP Note 2388871 also makes the access classes from SAP Note 2261720 available for actual versions &lt;&gt; '000'.</p>", "noteVersion": 11}, {"note": "2608006", "noteTitle": "2608006 - Universal Journal Authorizations In Virtual Data Model", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A new authorization concept was introduced to the part of the Core Data Services based Virtual Data Model that exposes Universal Journal data.</p>\n<p>A central role plays the new authorization object (F_ACDOCA_C - FIN Authorization Context for DCLs) which is used to distinguish between the roles that would like to access the same data with different authorizations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>F_ACDOCA_C; VDM; Virtual Data Model; CDS; Core Data Service; DCL; AcDocA; AcDocP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The universal journal is the basis of an integrated accounting system in which financial accounting and management accounting data are recorded in a single chart of accounts. Since all financial data is based on the same line items, no reconciliation between financial accounting and management accounting is ever required.<br/>Permanent reconciliation is achieved by bringing together the following components:</p>\n<ul>\n<li>General Ledger Accounting (FI-GL)</li>\n<li>Asset Accounting (FI-AA)</li>\n<li>Controlling (CO)</li>\n<li>Profitability Analysis (CO-PA)</li>\n</ul>\n<p>The universal journal is integrated only with account-based profitability analysis. It is not integrated with costing-based profitability analysis. However, costing-based profitability analysis can be run in parallel.</p>\n<ul>\n<li>Material Ledger (CO-PC-ACT)</li>\n</ul>\n<p>Even though the data is stored in a universal journal entry (table ACDOCA), and only one virtual data model applies, different end users with different business roles need to access the same data with different authorizations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please find the details regarding the \"Universal Journal Authorizations\" concept in the attached document.</p>\n<p><strong>Further Information</strong></p>\n<ul>\n<li>Please check the access controls in your installation for the most up to date list of authorization objects that are checked for each authorization context</li>\n<li>An official help.sap.com documentation is in preparation</li>\n</ul>", "noteVersion": 1}, {"note": "2408083", "noteTitle": "2408083 - FAQ: Data Model of S/4HANA Finance Data Migration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You would like to know technical details about data migration in S/4HANA Finance, especially about the changes in the data model.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li>How are BSEG (ANEP, COEP, FAGLFLEXA, …) records migrated into table ACDOCA?</li>\n<li>What is the meaning of ACDOCA fields MIG_SOURCE and BSTAT?</li>\n<li>Table COEP (FAGLFLEXA, …) appears empty in SE16 before migration – why?</li>\n<li>How are ACDOCA records exposed in the compatibility views for COEP, FAGLFLEXA etc.?</li>\n<li>Why is there a migration of balances?</li>\n<li>Can I check the correctness of data migration by comparing the number of records in the compatibility views COEP, FAGLFLEXA, … to the original tables?</li>\n<li>How are customer extensions (CI_COBL, append structures, …) handled by data migration?</li>\n<li>How can I adapt my custom code to the data model changes?</li>\n<li>Why are BSEG and COEP records merged/not merged into a single ACDOCA record?</li>\n<li>How are ACDOCA fields filled in case multiple source records are merged into a single ACDOCA record?</li>\n<li>Why is migration creating document numbers that start with characters like ********** or **********?</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li><strong>How are BSEG (ANEP, COEP, FAGLFLEXA, …) records migrated into table ACDOCA?<br/></strong>Data migration tries to match BSEG and COEP records that were created by the same transaction (prima nota), resulting in single ACDOCA records representing both FI and CO, but this is not always possible. NewG/L records, however, are always matched to the corresponding BSEG records, whereas FI-AA items cannot be matched. In case the documents of a transaction can be matched, CO and FI-AA items that are not matched inherit at least the FI document number, i.e. they are appended to the FI document and correction items are added to guarantee a zero balance. In case the match even fails on document level, e.g. for CO internal postings without realtime integration to FI, the CO or FI-AA items are migrated as standalone items, using a technical document number and no document header.<br/>The attachment provides some example data constellations and resulting ACDOCA records. </li>\n<li><strong>What is the meaning of ACDOCA fields MIG_SOURCE and BSTAT?<br/></strong>MIG_SOURCE is filled for migrated ACDOCA records and empty for newly created records. It indicates where the ACDOCA records was migrated from. <br/>BSTAT = C identifies balance carry forward items and items from migration of balances.<strong><br/></strong>See attachment slide 2 for further details.</li>\n<li><strong>Table COEP (ANEP, FAGLFLEXA, …) appears empty in SE16 before migration – why?<br/></strong>Several tables are replaced by a compatibility view in S/4HANA finance. Select statements on these tables are redirected to this compatibility view which actually selects the data from ACDOCA and other tables – see note 1976487 for further details. You have to execute data migration to fill ACDOCA, therefore, to populate the compatibility views. If you want to look at the data in the old tables you can use the corresponding “ORI” view, e.g. V_COEP_ORI or V_FAGLFLEXA_ORI, listed in note 1976487 for each table.</li>\n<li><strong>How are ACDOCA records exposed in the compatibility views for COEP, FAGLFLEXA etc.?<br/></strong>COEP records of value type 04 are read from ACDOCA by the compatibility view V_COEP, other value types are still read from physical table COEP. There is no filter on ACDOCA-MIG_SOURCE as the compatibility view has to provide migrated and newly created line items. ACDOCA records are rather selected by fields CO_BUZEI, ACCASTY and OBJNR. <br/><br/>The NewG/L compatibility views like FAGLFLEXA expose all ACDOCA records except for correction records created by the migration of balances (BSTAT = C in periods &gt; 0). This includes items migrated from COEP and correction items (MIG_SOURCE = R). As a consequence, more items might be shown in the compatibility view of FAGLFLEXA than in the old table (V_FAGLFLEXA_ORI). In addition, field COST_ELEM is obsolete, There is only a single G/L account field, RACCT, in ACDOCA. This is exposed to both RACCT and COST_ELEM in FAGLFLEXA and FAGLFLEXT.</li>\n<li><strong>Why is there a migration of balances?<br/></strong>There can be differences between the aggregated line items and the old totals tables, e.g. due to data archiving of line items. In addition, standalone items from CO or FI-AA (as described in question 1) can add to the G/L totals. These differences need to be corrected by additional line items in ACDOCA in the migration of balances step, to ensure that FI-AA, CO and G/L balances are not changed by data migration.<br/>See slide 6 ff. of the attachment for further details and examples.</li>\n<li><strong>Can I check the correctness of data migration by comparing the number of records in the compatibility views COEP, FAGLFLEXA, … to the original tables?<br/></strong>Due to the complexity of the data model changes there is no possibility to do simple cross checks like counting the number of records to verify the correctness of the data migration. In many data constellations these numbers will differ. Instead, you have to rely on the reconciliation checks R23 and R24 included in the data migration.</li>\n<li><strong>How are customer extensions (CI_COBL, append structures, …) handled by data migration?<br/></strong>Coding block extension fields in Include Structure CI_COBL are automatically contained in ACDOCA and considered by data migration. In case you have created append structures for BSEG or COEP, consider note 2160045.</li>\n<li><strong>How can I adapt my custom code to the data model changes?<br/></strong>See these notes:<br/>1976487 Information about adjusting customer-specific programs to the simplified data model in SAP Simple Finance<br/>2076652 SAP Simple Finance: SAP Note for adjustment of customer-specific programs for aging <br/>2185026 Compatibility views COSP, COSS, COEP, COVP: How do you optimize their use?<br/>2219527 Notes about using views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS in customer-defined programs in SAP S/4HANA Finance<br/>2221298 Notes about using views GLT0, FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, and JVGLFLEXT in custom programs in SAP S/4HANA Finance<br/>2156822 sFIN: Mapping of ACDOCA fields to old FI/CO tables</li>\n<li><strong>Why are BSEG and COEP records merged/not merged into a single ACDOCA record?<br/></strong>BSEG and COEP items are merged into a single ACDOCA record if – and only if – these prerequisites are met:</li>\n<ul>\n<li>FI and CO documents have to be from the same original transaction, i.e. AWTYP and AWKEY have to match</li>\n<li>Company code, G/L account and cost element, business area, partner business area and account assignments have to match</li>\n<li>Transaction currency codes and all amounts have to match. There can also be a 1:n match BSEG:COEP in case of summarization in FI, in this case the total amounts of the n COEP items have to match the amounts of the BSEG item.</li>\n</ul>\n<li><strong>How are ACDOCA fields filled in case multiple source records are merged into a single ACDOCA record?<br/></strong>Note 2156822 shows which ACDOCA fields are contained in BSEG and COEP or in BSEG and NewG/L tables. If multiple source tables are merged into a single ACDOCA record, we have to apply priority rules for fields that exist in several source tables as in some cases fields can be initial in some of the source tables. All of the fields used to match the BSEG and COEP records have to be identical, of course. For the remaining fields, these rules apply:</li>\n<ul>\n<li>For fields that exist in BSEG and COEP only, but not in NewG/L, COEP has priority with the exception of POPER which is always taken from FI (relevant for period 13-16 only in case CO has a different fiscal year variant)</li>\n<li>If fields exist in NewG/L and COEP, NewG/L has priority, except for the following fields where CO has priority: MSL, RCNTR, SCNTR and the optional NewG/L fields ZZAUFNR, ZZHRKFT, ZZPRZNR, ZZPS_PSP_PNR, ZZWERKS, ZZKOSTL and ZZPKOSTL</li>\n<li>If fields exist in NewG/L and BSEG but the NewG/L scenario wasn’t active before, the field value will be taken from BSEG as all NewG/L scenarios are active in S/4HANA finance</li>\n</ul>\n<li><strong>Why is migration creating document numbers that start with characters like ********** or **********?<br/></strong>Data migration has to create several types of ACDOCA items that cannot be linked to a document header in table BKPF and thus have to use a technical document number.</li>\n<ul>\n<li>Line item migration, for example, uses such document numbers for standalone CO documents or FI-AA documents that cannot be matched to any FI document. The document number of these items starts with C unless C* was already used as an external FI document number before migration, in which case the next free character is used instead of C.</li>\n<li>Migration of balances creates all correction items with a technical document number starting with D or the next free character.</li>\n</ul>\n</ol>", "noteVersion": 7}, {"note": "2332591", "noteTitle": "2332591 - S4TWL - Technical Changes in Material Ledger", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ML, Document, CKMB, CKM3, Period Totals, LBKUM, SALK3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This simplification makes it obligatory to use the Material Ledger (ML) which is now part of the standard and automatically active in all SAP S/4HANA systems.</p>\n<p>Actual Costing (including Actual Cost Component Split) is still optional.</p>\n<p>When an existing SAP system is converted to SAP S/4HANA, the Material Ledger will be activated during the migration process (if not already active). If any additional plants are added at a later point in time, the material ledger has to be activated for those plants (valuation areas) manually via transaction OMX1.</p>\n<p>As a consequence of the close integration of Material Ledger into sFIN processes, further simplification, refactoring, and process redesign has been implemented. This has incompatible effects especially on database table design and therefore direct SQL accesses to the affected tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The content of most of the former Material Ledger database tables is now stored in table ACDOCA which allows simpler and faster (HANA optimized) access to the data. The attributes of the ML data model that are relevant for the inventory subledger functionality are now part of table ACDOCA. The former tables are obsolete. Therefore the following tables must not be accessed anymore via SQL statements. In most cases a corresponding access function or class has been provided that must be used instead.</p>\n<ul>\n<li>Period Totals and Prices\r\n<ul>\n<li>CKMLPP “Material Ledger Period Totals Records Quantity”</li>\n<li>CKMLCR “Material Ledger: Period Totals Records Values”</li>\n<li>Access function: CKMS_PERIOD_READ_WITH_ITAB</li>\n</ul>\n</li>\n<li>Material Ledger Document and associated views\r\n<ul>\n<li>MLHD</li>\n<li>MLIT</li>\n<li>MLPP</li>\n<li>MLPPF</li>\n<li>MLCR</li>\n<li>MLCRF</li>\n<li>MLCRP</li>\n<li>MLMST</li>\n<li>Access functions: CKML_F_DOCUMENT_READ_MLHD, CKML_F_DOCUMENT_READ_MLXX</li>\n<li>Further tables (with no access function): MLKEPH, MLPRKEKO, MLPRKEPH</li>\n<li>Obsolete database views: MLXXV, MLREPORT, MLREADST</li>\n</ul>\n</li>\n<li>Index for Accounting Documents for Material\r\n<ul>\n<li>CKMI1</li>\n<li>Obsolete, no further access possible</li>\n</ul>\n</li>\n</ul>\n<p>In addition, some further simplifications have to be taken into account:</p>\n<ul>\n<li>Separate currency customizing of Material Ledger (transactions OMX2 / OMX3) is now mandatory. The Material Ledger acts on the currencies defined for the leading ledger in Financials.<br/>There is no default Material Ledger Type “0000” anymore.<br/>Customizing in Financial applications allows you to assign more than three currency and valuation types as being relevant in your company code. As the Material Ledger still supports only three currency and valuation types, it is no longer allowed to use an ML Type that references currency settings defined in FI or CO (flags “Currency Types from FI” and “Currency Types from CO”). Instead you have to explicitly define the currency and valuation types that are relevant for the Material Ledger.<br/>Steps to be executed:<br/>1. Use transaction OMX2 to define the currency and valuation types that are relevant for the Material Ledger.<br/>2. Then use transaction OMX3 to assign this ML Type to your valuation area.<br/><br/>See also note <a href=\"/notes/2291076\" target=\"_blank\">https://i7p.wdf.sap.corp/sap/support/notes/2291076</a></li>\n<li>Material Price Analysis (transaction CKM3 / CKM3N)<br/>The transaction CKM3/CKM3N was refactored and now provides a simplified and improved view of materials in plants with active Actual Costing. It replaces the former CKM3 view Price Determination Structure.<br/>The former CKM3 Price History view is still available via transaction CKM3PH for all materials (independent of price determination control and active Actual Costing). <br/>All other views formerly offered by CKM3/CKM3N are no longer available.</li>\n<li>For details about new Actual Costing simplification, please check the corresponding upgrade information SAP Note.</li>\n</ul>", "noteVersion": 3}, {"note": "2270387", "noteTitle": "2270387 - S4TWL - Asset Accounting: Changes to Data Structure", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have been using classic Asset Accounting until now and will be using new Asset Accounting in SAP S/4HANA.</p>\n<p>Other scenarios (such as the switch from SAP ERP 6.0, EhP7 with new Asset Accounting to SAP S/4HANA) are not considered here.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA; Asset Accounting; ACDOCA; compatibility view</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To be able to use new Asset Accounting in SAP S/4HANA, you have to also use new General Ledger Accounting. If you were using classic General Ledger Accounting before the conversion to SAP S/4HANA, new General Ledger Accounting is activated automatically with the conversion.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The data structures of Asset Accounting have changed as follows:</p>\n<ul>\n<li>Actual data:\r\n<ul>\n<li>Actual values from tables ANEP, ANEA, ANLP, ANLC are saved in table ACDOCA in new Asset Accounting.<br/>The values from table ANEK are saved in tables BKPF and ACDOCA in new Asset Accounting.<br/>Postings are still made to the table BSEG (exceptions see note 2383115)</li>\n<li>You can continue to use your existing reports and developments with the help of compatibility views. More details about this can be found below.</li>\n</ul>\n</li>\n<li>Statistical data and plan data:\r\n<ul>\n<li>Statistical values (for example, for tax reasons) from tables ANEP, ANEA, ANLP and ANLC, are saved in new Asset Accounting in table FAAT_DOC_IT.</li>\n<li>Planned values from tables ANLP and ANLC are saved in table FAAT_PLAN_VALUES in new Asset Accounting.</li>\n</ul>\n</li>\n</ul>\n<h3 data-toc-skip=\"\">Compatibility Views: System and User Usage</h3>\n<p>The Asset Accounting tables ANEA, ANEP, ANEK, ANLC and ANLP are <strong>no</strong> longer updated in SAP S/4HANA.</p>\n<p>However, to be able to continue using the reports of Asset Accounting and your own developments, selections from these tables are automatically diverted to the corresponding compatibility views. These compatibility views make the information from the new data store available in the format of the original tables. If it should be necessary to read the contents of the original tables for a particular reason, you can do this using DDIC views (for example, FAAV_ANEA_ORI). If you use selections from ANLP please refer to note 2297425 to check some specific limitations.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"120\">\n<p><strong> </strong></p>\n</td>\n<td colspan=\"2\" valign=\"top\" width=\"240\">\n<p><strong>Compatibility Views</strong></p>\n</td>\n<td colspan=\"2\" valign=\"top\" width=\"241\">\n<p><strong>Access to Original Table</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"120\">\n<p><strong>Table</strong></p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p><strong>DDL Source</strong></p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p><strong>DDIC View</strong></p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p><strong>DDL Source</strong></p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p><strong>DDIC View</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"120\">\n<p>ANEA</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANEA</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANEA</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANEA_ORI</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANEA_ORI</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"120\">\n<p>ANEP</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANEP</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANEP</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANEP_ORI</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANEP_ORI</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"120\">\n<p>ANEK</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANEK</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANEK</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANEK_ORI</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANEK_ORI</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"120\">\n<p>ANLC</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANLC</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANLC</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANLC_ORI</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANLC_ORI</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"120\">\n<p>ANLP</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANLP</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANLP</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAA_ANLP_ORI</p>\n</td>\n<td valign=\"top\" width=\"120\">\n<p>FAAV_ANLP_ORI</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>The tables listed above are part of the existing DDIC view. Selections from the DDIC views listed below are automatically diverted to the corresponding compatibility view.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p><strong> </strong></p>\n</td>\n<td colspan=\"2\" valign=\"top\" width=\"401\">\n<p><strong>Compatibility View</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p><strong>DDIC View</strong></p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p><strong>DDL Source</strong></p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p><strong>DDIC View</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p>V_ANEPK</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAA_V_ANEPK</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAAV_V_ANEPK</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p>ANEKPV</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAA_ANEKPV</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAAV_ANEKPV</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p>V_ANLSUM_2</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAA_V_ANLSUM_2</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAAV_V_ANLSUM_2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p>V_ANLAB</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAA_V_ANLAB</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAAV_V_ANLAB</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p>V_ANLSUM_1</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAA_V_ANLSUM_1</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAAV_V_ANLSUM_1</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"200\">\n<p>V_ANLSUM_5</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAA_V_ANLSUM_5</p>\n</td>\n<td valign=\"top\" width=\"200\">\n<p>FAAV_V_ANLSUM_5</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4}, {"note": "2176077", "noteTitle": "2176077 - Check report for SAP S/4HANA Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For SAP S/4HANA Finance, the following versions have been introduced:</p>\n<ul>\n<li>SAP S/4HANA Finance 1605, details refer to 2235268</li>\n<li>SAP Simple Finance, on-premise edition 1503, details refer to 2117481</li>\n</ul>\n<p>The attached analysis reports were created for a first preliminary check of the installation prerequisites. It provides you with an overview of the installation prerequisites before you implement the above two products.</p>\n<p>Since the release rollouts of the respective software components are updated continuously, we recommend that you download the most recent version via SNOTE before executing the analysis report.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Financials, SAP SFINANCIALS 2.0, SAP SFINANCIALS 3.0, upgrade, SFIN, SAP ERP 6.0, S/4HANA Finance</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Before you install the SAP S/4HANA Finance 1605 or SAP Simple Finance on-premise edition 1503 and begin a test migration, you must check certain prerequisites.</p>\n<p>The note 2176077 is only for install base customer. For new install customer, there is no need to apply for this note.</p>\n<p>The note 2176077 have the dependency on note 1939592, note 2129306 and note 1976131. And these notes should be applied beforehand.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can use the below program to perform the check:</p>\n<p><strong>If you upgrade to SAP S/4HANA Finance 1605, please use the program FINS_PRE_CHECK_SFIN_1605 </strong></p>\n<p>(1) Collecting general information of system usage</p>\n<p>- How large the volume of data to be migrated is: entries in ANLP/BSEG/COEP/MLIT/CKMLCR/CKMLPP</p>\n<p>- Whether the General Ledger Accounting (new) is already active.<br/>Background: During the installation of the SAP S/4HANA Finance 1605, the General Ledger Accounting (new) is technically introduced in the system. This means that customers who previously worked with the classic General Ledger Accounting must set a minimum of settings for the General Ledger Accounting (new) in Customizing that specify</p>\n<p>(2) Check functional Restrictions / Differences compared to ERP 6.0 EhP7 or higher, i.e. classic Financials</p>\n<p>- Using the checkbox for starting RASFIN_MIGR_PRECHECK, you can schedule the check report RASFIN_MIGR_PRECHECK as a background job (the job is executed directly).</p>\n<p>The check will be whether you are using components in Asset Accounting (AA) that have not been released for the financial add-on (see SAP Notes 1939592 and 2270550 for RE).</p>\n<p>- Using the checkbox for \"Reports/Transactions are replaced\", you can get the replacement list for SAP S/4HANA Finance 1605 (see SAP Note 1946054)</p>\n<p>- Using the checkbox for \"Check Cash Management\", you will know whether the Cash and Liquidity Management are active per company code (see SAP Note 2265615)</p>\n<p>(3) Check the installed components:</p>\n<p>Using the checkbox \"Enterprise Extensions\", you can list all active Enterprise Extensions/Enterprise business functions and industry solutions. They must be checked against SAP Notes 2235351 and 2233539.</p>\n<p>With the checkbox for \"Addons\", you can check whether the software components installed in the customer system have been released for the SAP S/4HANA Finance 1605. Since the release rollouts of the respective software components are updated continuously, we recommend that you download the most recent version via SNOTE before executing the analysis report.</p>\n<p> (4) Check customer-specific source code</p>\n<p>Via the import of the SAP S/4HANA Finance 1605, some tables are replaced by views that have the same name. For additional information about the affected tables, see SAP Note 1976487. <br/><br/>With the checkbox for checking views, you can check whether customer-defined views for the affected tables exist in the customer namespace. These views must be replaced using an Open SQL SELECT or by calling a read module because the ABAP Dictionary does not support any database views that are based on other views.   <br/><br/>With the checkbox for checking source code, you can check whether write accesses to the affected tables exist in the customer namespace. Essentially, a where-used list is made for the database operations INSERT/DELETE/UPDATE and MODIFY. The program, the program line, and the found source code line are listed.</p>\n<p>(5) Data migration pre-checks</p>\n<p>Check the consistency of your ledger, company code and controlling area settings to determine if a migration to SAP S/4HANA Finance 1605 is possible. (See SAP Note 2129306)</p>\n<p>Using the checkbox for starting FINS_MIG_PRECHECK_CUST_SETTNGS, you can schedule the check report FINS_MIG_PRECHECK_CUST_SETTNGS as a background job (the job is executed directly).</p>\n<p>(6) Quick compare version of check report code</p>\n<p>If you want to quickly check whether report code is the last version in your system, you can compare the last updated date (you can find it in comments of report title) with notes release date.</p>\n<p> </p>\n<p><strong>If you upgrade to SAP Simple Finance on-premise edition 1503, please use program FINS_PRE_CHECK_SFIN_1503.</strong></p>\n<p> (1) Collecting general information of system usage</p>\n<p>- How large the volume of data to be migrated is: entries in ANLP/BSEG/COEP/MLIT/CKMLCR/CKMLPP</p>\n<p>- Whether the General Ledger Accounting (new) is already active.<br/>Background: During the installation of the Simple Financial on-premise edition 1503, the General Ledger Accounting (new) is technically introduced in the system. This means that customers who previously worked with the classic General Ledger Accounting must set a minimum of settings for the General Ledger Accounting (new) in Customizing that specify</p>\n<p>(2) Check functional Restrictions / Differences compared to ERP 6.0 EhP7, i.e. classic Financials</p>\n<p>- Using the checkbox for starting RASFIN_MIGR_PRECHECK, you can schedule the check report RASFIN_MIGR_PRECHECK as a background job (the job is executed directly).</p>\n<p>The check will be whether you are using components in Asset Accounting (AA) that have not been released for the financial add-on (see SAP Notes 1939592 and 1944871 for RE).</p>\n<p>- Using the checkbox for \"Reports/Transactions are replaced\", you can get the replacement list for Simple Finance on-premise edition 1503 (see SAP Note 1946054)</p>\n<p>- Using the checkbox for \"Check Cash Management\", you will know whether the Cash and Liquidity Management are active per company code (see SAP Note 2149337)</p>\n<p>(3) Check the installed components:</p>\n<p>Using the checkbox \"Enterprise Extensions\", you can list all active Enterprise Extensions/Enterprise business functions and industry solutions. They must be checked against SAP Notes 2103558 and 2119188.</p>\n<p>With the checkbox for \"Addons\", you can check whether the software components installed in the customer system have been released for the SAP Simple Finance on-premise edition 1503. Since the release rollouts of the respective software components are updated continuously, we recommend that you download the most recent version via SNOTE before executing the analysis report.</p>\n<p> (4) Check customer-specific source code</p>\n<p>Via the import of the SAP Simple Finance on-premise edition 1503, some tables are replaced by views that have the same name. For additional information about the affected tables, see SAP Note 1976487. <br/><br/>With the checkbox for checking views, you can check whether customer-defined views for the affected tables exist in the customer namespace. These views must be replaced using an Open SQL SELECT or by calling a read module because the ABAP Dictionary does not support any database views that are based on other views.   <br/><br/>With the checkbox for checking source code, you can check whether write accesses to the affected tables exist in the customer namespace. Essentially, a where-used list is made for the database operations INSERT/DELETE/UPDATE and MODIFY. The program, the program line, and the found source code line are listed.</p>\n<p>(5) Data migration pre-checks</p>\n<p>Check the consistency of your ledger, company code and controlling area settings to determine if a migration to Simple Finance on-premise edition 1503 is possible. (See SAP Note 2129306)</p>\n<p>Using the checkbox for starting FINS_MIG_PRECHECK_CUST_SETTNGS, you can schedule the check report FINS_MIG_PRECHECK_CUST_SETTNGS as a background job (the job is executed directly).</p>\n<p>(6) Quick compare version of check report code</p>\n<p>If you want to quickly check whether report code is the last version in your system, you can compare the last updated date (you can find it in comments of report title) with notes release date.</p>", "noteVersion": 55}]}, {"note": "2076652", "noteTitle": "2076652 - SAP Simple Finance: SAP Note for adjustment of customer-specific programs for aging", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have installed SAP Simple Finance and want to use SAP Data Aging for the FI document. To do so, you must adjust customer-specific programs or modifications.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, SAP simple Finance, data aging</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use SAP Data Aging for the FI document.</p>\n<p>You have implemented customer-specific accounting developments (in particular in the application components AC, FI, CO, and FIN) that were developed for SAP ERP 6.0.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Adjustments are required in the case of access to totals in FI and direct archive accesses.</p>\n<p><strong>1. Read accesses to totals in FI</strong></p>\n<p> The following FI totals tables have been replaced by views of the same name:</p>\n<p> GLT0, <PERSON><PERSON><PERSON><PERSON>XT, KNC1, KNC3, LFC1, LFC3</p>\n<p>These views calculate the total from the line items that might already be located in the \"cold\" area of the database and are not selected by default. For this reason, a control of the access to the cold area of the database must be implemented for accesses to the old data in customer-specific programs.</p>\n<p>To access the cold area of the database, you must first specify the temperature on the basis of the selection conditions and pass these to the database by means of a kernel call.</p>\n<p><strong>1.1 Determination of places to be adjusted</strong></p>\n<p>You can use the Code Inspector to find all affected parts of the code. Proceed as follows:</p>\n<ol>\n<li>Call transaction SCI and define a new variant. In the definition of the variant, set the indicator for searching for DB operations in the search functions and enter the list of affected objects (GLT0 and so on).</li>\n<li>Create an object set that contains all of your programs, classes, function groups, and so on.</li>\n<li> Create a new inspection and execute it. The result is a list of all database accesses to the totals tables and index tables removed in Smart Financials.</li>\n</ol>\n<p>Remember that generic database accesses that cannot be found using standard methods are possible too.</p>\n<p>Example: SELECT * FROM (lv_tabname) INTO TABLE lt_table.</p>\n<p><strong> 1.2 Determination of temperature</strong></p>\n<p>You can derive a lower estimation of the temperature from the selection conditions for the fiscal year. To take a deviating fiscal year into account, you can also include the ledger and company code (otherwise, the least favorable situation is assumed).</p>\n<p> To do so, call the static method GET_TEMPERATURE_DOCUMENT of the class CL_FINS_FI_AGING_UTILITY.</p>\n<p><strong> 1.3 Setting the temperature </strong></p>\n<p>Depending on the situation, you set the temperature on the basis of the class CL_ABAP_SESSION_TEMPERATURE or CL_ABAP_STACK_TEMPERATURE.</p>\n<p><strong> 1.3.1 Stack temperature</strong></p>\n<p>You use this method if you call a self-written routine in your program (form routine, function module, method...) in which the call of the earlier totals table is located. The set temperature is valid until you leave the routine and is then reset to the previous value.</p>\n<p>Call the static method SET_TEMPERATURE of the class CL_ABAP_STACK_TEMPERATURE and pass the temperature calculated in 1.2.</p>\n<p><strong> 1.3.2 Session temperature</strong></p>\n<p>The use of this method is required if you are not in a routine (that is, in the main program of a report). This method also makes sense if you want to set the temperature for the entire program directly after START-OF-SECLECTION on the basis of the selection conditions.</p>\n<p>First, obtain an instance of the class using the static method GET_SESSION_HANDLE of the class CL_ABAP_SESSION_TEMPERATURE. You are only allowed to call this method once in a session. You can then call the method SET_TEMPERATURE of the instance and pass the temperature calculated in 1.2.</p>\n<p><strong> 2. Archive accesses</strong></p>\n<p>With the FI document in SAP Simple Finance, archiving was replaced by aging; however, access to archived files is still supported. For this reason, old data can be located in the archive or in the cold area of the database. In all places where you previously accessed the archive, you must now access the cold area of the database. If you use standard SAP function modules for this, no adjustment is required.</p>\n<p>You can find the relevant source code sections by carrying out a text search:</p>\n<ul>\n<li>Name of the archiving object FI_DOCUMNT</li>\n<li>Name of the field catalog of the archiving object FI_DOCUMNT: You can find the defined field catalogs by calling transaction SARJ and choosing \"Environment -&gt; Field Catalogs\".</li>\n<li>Name of the archive information structures of the archiving object: Transaktion SARJ</li>\n</ul>\n<p>Alternatively, use a where-used list:</p>\n<ul>\n<li>Use access for the unction module AS_API_READ (based on the field catalogs)</li>\n<li>Use access for the function module AS_API_SYSTEM_SELECT (based on the archive index)</li>\n<li>Use access for the function module ARCHIVE_OPEN_FOR_READ (based on the archiving object)</li>\n</ul>\n<p>Note that there are other archive access possibilities (not recommended by SAP). A use access for the module ARCHIVE_READ_OBJECT or ARCHIVE_READ_OBJECT_BY_OFFSET gives a full picture but without the possibility of a simple filter for relevant places.</p>\n<p>Depending on the situation, both of the methods mentioned in 1.3 can be used: The previous SELECT on the database is adjusted at the start of the selections so that it also reads cold data OR - in addition to the archive access - you access the cold area by means of Open SQL and the same selection conditions, ignoring hot data. Depending on whether the SELECTs or archive access take place in the main program or in a routine (or depending on where you want to make the adjustment), you use CL_ABAP_SESSION_TEMPERATURE or CL_ABAP_STACK_TEMPERATURE. Particularly if you have written your own archiving read modules, it is probably most sensible to directly adjust these modules.</p>\n<p> To calculate the temperature, you use the method GET_TEMPERATURE_DOCUMENT of the class CL_FINS_AGING_UTILITY (as described in 1.2) if there is a selection condition for the fiscal year. If there is a condition for the posting date or clearing date, you can use this to determine the temperature more precisely (Note that a condition such as \"open items on the key date\" can be interpreted as a condition for the clearing date.) In particular, this is useful if there is no condition for the fiscal year. To do this, use the method GET_TEMPERATURE_DOCUMENT_MIX of the same class. If no condition exists, you can also use the method SET_COLD of one of the two kernel methods, with which you take all data into account. This should be avoided but is acceptable because the user has explicitly requested old data.</p>", "noteVersion": 2}, {"note": "2221298", "noteTitle": "2221298 - Notes about using views GLT0, FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, and JVGLFLEXT in custom programs in SAP S/4HANA Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In your custom programs, you use the compatibility views GLT0, FAGLFLEXT, FMG<PERSON>LEXT, PSGLFLEXT, and JVGLFLEXT to determine balances in the general ledger.</p>\n<p>The performance of the compatibility views is not sufficient.</p>\n<p>You want to optimize these accesses.</p>\n<p>You want to understand the data model behind the compatibility views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Compatibility views, SAP Simple Finance, SFIN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>With SAP_FIN 700, the totals tables FAGLFLEXT and GLT0 were replaced by views of the same name.</p>\n<p>In SAP_FIN 720, further totals tables were replaced in G/L by compatibility views:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Table</td>\n<td>View</td>\n</tr>\n<tr>\n<td>FMGLFLEXT </td>\n<td>FGLV_FMGLFLEXT</td>\n</tr>\n<tr>\n<td>PSGLFLEXT</td>\n<td>FGLV_PSGLFLEXT</td>\n</tr>\n<tr>\n<td>JVGLFLEXT</td>\n<td>FGLV_JVGLFLEXT</td>\n</tr>\n<tr>\n<td>ZZ&lt;CUST&gt;T</td>\n<td>ZFGLV_GLTT_C&lt;number&gt;</td>\n</tr>\n</tbody>\n</table></div>\n<p>The views are slower than the replaced tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Data model of compatibility views</strong></p>\n<p>The partially redundant totals tables GLT0, FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, and JVGLFLEXT and custom totals tables in the general ledger were replaced by compatibility views in <em>SAP S/4HANA Finance</em>. This ensures that all programs that execute read accesses on the totals tables mostly continue to work without any drop in function.</p>\n<p>The fields OBJNR00, OBJNR01, and so on (the object number) are set to zero in the compatibility views for performance reasons. They do not correspond to the original content of the totals table.</p>\n<p>The views acquire their data from the journal entry table ACDOCA using a hierarchy of views. Here, for example, is the view FAGLFLEXT and underlying views:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Table/view</td>\n<td>Read from</td>\n<td>DDL source</td>\n</tr>\n<tr>\n<td>\n<p>View FAGLFLEXT</p>\n</td>\n<td>\n<p>FGLV_FAGLFLEXT</p>\n</td>\n<td>\n<p>V_FAGLFLEXT_DDL</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_FAGLFLEXT</p>\n</td>\n<td>\n<p>FGLV_GLTT2</p>\n</td>\n<td>\n<p>FGL_FAGLFLEXT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLTT2</p>\n</td>\n<td>\n<p>FGLV_GLTT1</p>\n</td>\n<td>\n<p>FGL_GLTT2</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLTT1</p>\n</td>\n<td>\n<p>FGLV_GLTT0</p>\n</td>\n<td>\n<p>FGL_GLTT1</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLTT0</p>\n</td>\n<td>\n<p>FGLV_GLSI_ACD</p>\n</td>\n<td>\n<p>FGL_GLTT0</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLSI_ACD</p>\n</td>\n<td>Tables ACDOCA,<br/>FINSC_LEDGER_REP,<br/>FINSC_LD_CMP</td>\n<td>FGL_GLSI_ACD</td>\n</tr>\n</tbody>\n</table></div>\n<p>The tables FINSC_LEDGER_REP and FINSC_LD_CMP reproduce the functions of the extension ledger.</p>\n<p>In SAP_FIN 720, the DDL source V_xxx_DDL (for example, V_FAGLFLEXT_DDL) also contains a UNION ALL with the corresponding BCK table (for example, FAGLFLEXT_BCK). This dummy UNION is required in SAP NetWeaver 740 to obtain the correct data elements in the compatibility view.</p>\n<p>The compatibility view FAGLFLEXT was already introduced in SAP_FIN 700. During the upgrade to SAP S/4HANA Finance. the table FAGLFLEXT is renamed as FAGLFLEXT_BCK, thus freeing the namespace for the view FAGLFLEXT. The table FAGLFLEXT_BCK is read during the migration and is then obsolete. In the same way, this applies to the view GLT0, too.</p>\n<p>In SAP_FIN 720, compatibility views were added for the other totals tables in the general ledger. These views have a different name (for example, the table FMGLFLEXT and the corresponding proxy view FGLV_FMGLFLEXT). Each Open SQL SELECT from ABAP is forwarded to the compatibility view (proxy view) in the database interface. The link from the table to the corresponding compatibility view (also called REDIRECT) is established during the data migration (IMG activity <em>Migration from SAP ERP to SAP Accounting powered by SAP HANA -&gt; Migration -&gt; Regenerate CDS Views and Field Mapping</em>).</p>\n<p>The view names for totals tables in the customer namespace are numbered in accordance with the pattern ZFGLV_GLTT_C&lt;number&gt;. The exact name is in the column VIEWREF of the table DD02L.</p>\n<p><strong>Tips for adjustment of custom programs</strong></p>\n<p>If you actively use SAP Data Aging for the FI document, read access adjustments are required. See SAP Note 2076652.</p>\n<p>Assignments using the group names PERIOD_DATA, TSL, HSL, KSL, OSL, MSL, and FIX must be reprogrammed. See SAP Note 1976487.</p>\n<p>The view hierarchies of the compatibility views are all structured similarly. The line items from the table ACDOCA are aggregated, the posting periods are switched from rows to columns (for example, TSL to TSLVT, TSL01, TSL02 etc.), and the column RPMAX is calculated. This transformation takes a relatively large amount of runtime, so we recommend that you select the data directly from the table ACDOCA. We therefore recommend that you read general ledger balances via the function module FINS_ACDOCA_BW_EXTRACT_DATA to achieve optimum performance when accessing the database.</p>\n<p>The compatibility views for the journal entry tables FAGLFLEXA, FMGLFLEXA, PSGLFLEXA, and JVGLFLEXA and for custom journal entry tables in the general ledger are, however, problem-free from a performance point of view and can still be used in custom programs.</p>\n<p><strong>Example</strong></p>\n<p>The attachment ZFINS_SEL_GLT0_FROM_ACDOCA.txt contains the example program ZFINS_SEL_GLT0_FROM_ACDOCA to illustrate the use of the function module FINS_ACDOCA_BW_EXTRACT_DATA to select ACDOCA data in the format of the table GLT0 (with period field POPER). Adjust this accordingly.</p>\n<p> </p>", "noteVersion": 6}, {"note": "2575530", "noteTitle": "2575530 - Performance after Upgrade to S/4 HANA in Accounting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You recently upgraded from ERP or SuiteOnHANA to S/4 HANA and you experience a decrease in performance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Universal Journal, ACDOCA, READ_OPTI, T811FLAGS, Report Painter, Report Writer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable the Finance solution to be enhanced with new capabilities and efficiently utilize new technologies such as in-memory, we have developed a fundamentally new architecture.</p>\n<p>First we redesigned the data model and provided various applications with a common data basis (Universal Journal), which enables much of the detailed information from the business processes to be stored in journal entries. Furthermore, wherever possible we dispensed with redundancies or even eliminated existing ones. All this has enabled radically new insights into financial data with unprecedented levels of detail.</p>\n<p>These and other changes form the basis for significant new capabilities (such as parallel currencies, parallel ledgers, and extensibility) which we have developed and will continue to develop. Step by step, we are enhancing our applications with these capabilities and gradually adapting them to take full advantage of the potentials inherent in the new architecture. We have already converted many applications. These approaches have opened up completely new perspectives such as simulation and prediction.</p>\n<p>Despite all these changes, we have not neglected the needs of our customers who have been using our solutions before. In order to smooth the path for them to transition to S/4HANA, we are employing the technology of compatibility views which compute the legacy data format at runtime. In some cases, this can lead to performance differences against ERP. To ensure that our customers can profit from enhanced insights into their data and increased performance, we recommend that customer programs be adapted to the new data basis.</p>\n<p>In view of the above developments and enhancements, in some cases (often depending on the actual constellation of customer data) the performance of some of our applications cannot be compared directly with that of ERP. For a list of adjustments that have been made up to now, see the attachment to this Note.</p>\n<p><br/><br/></p>", "noteVersion": 3, "refer_note": [{"note": "2261720", "noteTitle": "2261720 - Access classes for COEP, COSS, and COSP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have performance problems in customer-defined programs when accessing CO single records or totals records via K_XXX_READ_MULTI modules or compatibility views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, COEP, COSS, COSP, CL_FCO_COEP_READ, CL_FCO_COSP_READ, CL_FCO_COSS_READ, K_COSSA_READ_MULTI, K_COSPA_READ_MULTI, K_COEP_READ_MULTI</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement the SAP Note. A new interface is then available to you.</p>\n<p><strong><span>Explanation</span></strong></p>\n<p>The classes are suitable for data retrieval in customer programs, in particular if the K_xxx_READ_MULTI function modules have been used until now. They have the benefit that the tables are wrapped and the data fields conform to the CO conventions.</p>\n<p>The optimization in the classes works if you select exactly with one value type. In particular for the value types 04 and 11, extremely favorable accesses can be carried out in some cases. Despite this, it is important that the selection is as restricted as possible - ideally the period, too.</p>\n<p>There are three methods for the direct replacement of the aforementioned function modules: CL_FCO_COEP_READ=&gt;READ_COEP, CL_FCO_COSP_READ=&gt;READ_COSPA, and CL_FCO_COSS_READ=&gt;READ_COSSA. These methods are a little slower than the other methods (see below) but have the benefit of offering the same interface as the function modules.</p>\n<ol>\n<li>Class CL_FCO_COEP_READ</li>\n<ol>\n<li>GET_FILTER_FROM_COSEL: Converts a table from the line type COSEL to the filter structure of the type TY_FILTER as is required by the other methods.</li>\n<li>READ_COEP: Reading the data of the view COEP (with optimization for when just one value type is requested) - this method is compatible with the aforementioned function modules - requires more main memory.</li>\n<li>READ_04_AS_COEP: Reading the data of the view COEP for the value type 4, return in COEP format - requires more main memory</li>\n<li>READ_04_AS_COEPX: Reading the data of the view COEP for the value type 4, return in the internal format T_COEPXL - saves main memory</li>\n<li>READ_11_AS_COEP: Reading the data of the view COEP for the value type 11, return in COEP format</li>\n<li>READ_NN_AS_COEP: Reading the data of the view COEP for value types other than 4 and 11, return in COEP format</li>\n</ol>\n<li>Class CL_FCO_COSP_READ</li>\n<ol>\n<li>GET_FILTER_FROM_COSEL: Converts a table from the line type COSEL to the filter structure of the type TY_FILTER as is required by the other methods.</li>\n<li>READ_COSPA: Reading the data of the view COSP (with optimization for when just one value type is requested) - this method is compatible with the aforementioned function modules.</li>\n<li>READ_04_AS_COSPA: Reading the data of the view COSP for the value type 4, return in COSPA format - requires more main memory</li>\n<li>READ_04_AS_COEPX: Reading the data of the view COSP for the value type 4, return in TT_COEPX format - is significantly faster and requires less main memory</li>\n<li>READ_11_AS_COSPA: Reading the data of the view COSP for the value type 11, return in COSPA format</li>\n<li>READ_NN_AS_COSPA: Reading the data of the view COSP for value types other than 4 and 11, return in COSPA format</li>\n</ol>\n<li>Class CL_FCO_COSS_READ</li>\n<ol>\n<li>GET_FILTER_FROM_COSEL: Converts a table from the line type COSEL to the filter structure of the type TY_FILTER as is required by the other methods.</li>\n<li>READ_COSPA: Reading the data of the view COSP (with optimization for when just one value type is requested) - this method is compatible with the aforementioned function modules.</li>\n<li>READ_04_AS_COSPA: Reading the data of the view COSP for the value type 4, return in COSSA format - requires more main memory</li>\n<li>READ_04_AS_COEPX: Reading the data of the view COSP for the value type 4, return in TT_COEPX format - is significantly faster and requires less main memory</li>\n<li>READ_11_AS_COSPA: Reading the data of the view COSP for the value type 11, return in COSSA format</li>\n<li>READ_NN_AS_COSPA: Reading the data of the view COSP for value types other than 4 and 11, return in COSSA format</li>\n</ol></ol>\n<p><span><strong>Restrictions</strong></span></p>\n<ol>\n<li>Only version 000 is supported.</li>\n<li>Transfer prices and parallel valuation are not supported.</li>\n<li>Customer-defined fields in the view COEP are not supported.</li>\n<li>Packaging is not supported.</li>\n<li>The class CL_FCO_COEP_READ requires a lot of main memory for the value type 04. Restrict the selection to the required data records.</li>\n<li>The addition of certain data fields from the table COKEY using the field HRKFT is not always performed.</li>\n</ol>", "noteVersion": 3}, {"note": "2570011", "noteTitle": "2570011 - Further Performance Improvements New Read-API", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the new read API CL_FCO_COSP_READ or CL_FCO_COSS_READ and you experience long runtimes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>T811FLAGS, READ_OPTI, READ_OPTI_NO_CDS, RK_COMPARE_READ_APIS, CL_FCO_COSP_READ, CL_FCO_COSS_READ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement this SAP note.</p>\n<p> </p>", "noteVersion": 4}, {"note": "2314542", "noteTitle": "2314542 - Performance Optimization for K_COSPA_READ_MULTI and K_COSSA_READ_MULTI", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In some cases the performance of function modules K_COSPA_READ_MULTI and K_COSSA_READ_MULTI can be enhanced.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, READ_OPTI, CL_FCO_COSP_READ, CL_FCO_COSS_READ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>When you use Transfer Prices / Parallel Valuation you must implement 2388871 and 2570011 before turning on the optimized read. Otherwise you will receive wrong results.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement the following SAP notes: 2462432, 2432089, 2388871, 2261720, 2299870 and 2275731, as well as 2544417 and 2544926 (if valid for your release) and 2570011. This will add a read optimization for some cases.</p>\n<p>Please use transaction SE16 to add the following entry to table T811FLAGS: <br/>tab=KARS<br/>field=READ_OPTI<br/>valmin=X</p>\n<p>Please note the following restrictions:</p>\n<ol>\n<li>The optimization may change the order of datasets returned. This should not affect SAP programs.</li>\n<li>The optimization needs slightly more memory.</li>\n<li>The optimization does not work when you pass the T_HDB_FIELDS parameter.</li>\n</ol>\n<p>If the above topics cause problems, please delete the above entry from T811FLAGS.</p>\n<p>If you use Transfer Prices / Parallel Valuation you have to implement at least SAP notes 238871, 2544417 (S4CORE 101 only), 2544926 (others than S4CORE 101). It is strongly recommended to implement the latest SAP notes affecting the classes CL_FCO_READ_HELPER, CL_FCO_COSP_READ and CL_FCO_COSS_READ.</p>", "noteVersion": 9}]}, {"note": "1976487", "noteTitle": "1976487 - Information about adjusting customer-specific programs to the simplified data model in SAP Simple Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have installed SAP Simple Finance, on-premise edition or plan to do so. You require further information about how you can adjust your customer-specific programs or modifications to the new simplified data model in SAP Simple Finance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Migration, 1402, 1503</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have developed customer-specific accounting developments (in particular in the application components AC, FI, CO, FIN) that were developed for SAP ERP 6.0.</p>\n<p>SAP Simple Finance offers a simplified data model. In addition to the usual SPAU/SPDD activities, this might require special adjustments to customer-specific developments or modifications.</p>\n<p><strong>Task</strong>: Check whether the customer uses the affected tables or views in their own source code.</p>\n<p><strong>Procedure</strong>: See chapter 1.5</p>\n<p><strong>Rating</strong>:</p>\n<p>At least one of the tables or views is used in the customer-specific source code -&gt; YELLOW</p>\n<p>None of the tables or views is used in the customer-specific source code -&gt; GREEN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>1. Adjustment of customer-specific development objects </strong></p>\n<p><strong>1.1. Write access to totals tables and index tables in FI and CO</strong></p>\n<p>In SAP Simple Finance, the following tables have been replaced by views of the same name (Some views contain additional fields compared to the replaced tables, but no fields are missing.):</p>\n<p>COSP<br/>COSS<br/>BSIS<br/>BSAS<br/>FAGLBSIS<br/>FAGLBSAS<br/>BSIK<br/>BSAK<br/>BSID<br/>BSAD<br/>FAGLFLEXT<br/>LFC1<br/>LFC3<br/>KNC1<br/>KNC3<br/>GLT0</p>\n<p>These views of the same name select data from the line item tables and convert this into the format of the replaced tables so that all read accesses still work.</p>\n<p>However, no write accesses may take place - the views are read-only. Remove all write accesses to these objects from your source code (operations INSERT, UPDATE, DELETE, and MODIFY).</p>\n<p><strong><strong>1.2 Read accesses to totals tables and index tables in FI and CO</strong></strong></p>\n<p>See the following SAP Notes:</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><span class=\"urTxtStd urVt1\">2219527</span></td>\n<td> </td>\n<td>Notes about using views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS in customer-defined programs in SAP S/4HANA Finance<a class=\"urLnk urVt1\" href=\"/notes/2219527\" id=\"CORR_GR_C_wu_2219527\" target=\"_blank\"></a></td>\n</tr>\n<tr>\n<td><span class=\"urTxtStd urVt1\">2221298</span></td>\n<td> </td>\n<td>Notes about using views GLT0, FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, and JVGLFLEXT in custom programs in SAP S/4HANA Finance<a class=\"urLnk urVt1\" href=\"/notes/2221298\" id=\"CORR_GR_C_wu_2221298\" target=\"_blank\"></a></td>\n</tr>\n<tr>\n<td><span class=\"urTxtStd urVt1\">2185026</span></td>\n<td> </td>\n<td>Compatibility views COSP, COSS, COEP, and COVP. How can their use be optimized?<a class=\"urLnk urVt1\" href=\"/notes/2105948\" id=\"CORR_GR_C_wu_2105948\" target=\"_blank\"></a></td>\n</tr>\n</tbody>\n</table></div>\n<p>SELECTs from views of the same name with the addition ORDER BY PRIMARY KEY result in a syntax error in Release S/4H Finance 1503. These must be replaced by an explicit field list, for example, ORDER BY BUKRS HKONT AUGDT AUGBL ZUONR GJAHR BELNR BUZEI. In later releases, you can implement SAP Note 2489438 so that ORDER BY PRIMARY KEYs no longer result in a syntax error. For the view FAGLFLEXT, however, an explicit field list must still be used. See also SAP Note 2629757.</p>\n<p><strong>1.3 Views for totals tables and index tables</strong></p>\n<p>If, in the ABAP Dictionary, you have defined database views for one of the tables that has been eliminated in SAP Simple Finance, you must remove these views. The ABAP Dictionary does not support database views based on other views.</p>\n<p>Example:</p>\n<p>You have created a database view called Z_BKPF_BSIS with the base tables BKPF and BSIS. In SAP Simple Finance, BSIS is a view. As a result, Z_BKPF_BSIS would be based on the BSIS view. This is not supported.</p>\n<p>Proceed as follows to find affected views in your namespace.</p>\n<ol>\n<li>Use the where-used list in transaction SE11. One after the other, enter the affected objects (COSP, COSS, BSIS, and so on) into the table field.</li>\n<li>Call the where-used list (Ctrl+Shift+F3) and set the \"Views\" indicator only.</li>\n<li>Execute the where-used list.</li>\n</ol>\n<p>You must remove these database views from the ABAP Dictionary and replace the selection from the views by an Open SQL SELECT or a read module call.</p>\n<p><strong>1.4 Aging</strong></p>\n<p>If you actively use SAP data aging for the FI document, you have to make further adjustments. See SAP Note 2076652.</p>\n<p><strong>1.5 Finding the affected parts of the code</strong></p>\n<p>You can use the Code Inspector to find all affected parts of the code. Proceed as follows:</p>\n<ol>\n<li>Call transaction SCI and define a new variant. In the definition of the variant, set the indicator for searching for DB operations in \"Search Functs.\". Enter the list of affected objects (COSP, COSS, BSIS etc.).</li>\n<li>Create an object set that contains all of your programs, classes, function groups, and so on.</li>\n<li>Create a new inspection and execute it. The result is a list of all database accesses to the totals tables and index tables removed in Simple Finance.</li>\n</ol>\n<p>Remember that generic database accesses that cannot be found using standard methods are possible too.</p>\n<p>Example: UPDATE (lv_tabname) FROM lt_index.</p>\n<p><strong>1.6 SAP Simple Finance, on-premise edition 1503 </strong></p>\n<p>Additional compatibility views were implemented in SAP Simple Finance, on-premise edition 1503. The technology is different than in the previous release. The views do not have the same name as the original table. Instead, in the database interface (DBI), all SELECTs are redirected to compatibility views that receive data in the same structure from the new data model (mainly from the new table ACDOCA). The process of redirecting SELECTs is also called REDIRECT.</p>\n<p>Table -&gt; View</p>\n<p>ANEA -&gt; FAAV_ANEA<br/>ANEK -&gt; FAAV_ANEK<br/>ANEP -&gt; FAAV_ANEP<br/>ANLC -&gt; FAAV_ANLC<br/>ANLP -&gt; FAAV_ANLP<br/>BSIM -&gt; V_BSIM<br/>CKMI1 -&gt; V_CKMI1<br/>COEP -&gt; V_COEP<br/>FAGLFLEXA -&gt; FGLV_FAGLFLEXA<br/>MLCD -&gt; V_MLCD<br/>MLCR -&gt; V_MLCR<br/>MLHD -&gt; V_MLHD<br/>MLIT -&gt; V_MLIT<br/>MLPP -&gt; V_MLPP<br/>T012K -&gt; V_T012K_BAM<br/>T012T -&gt; V_T012T_BAM<br/>FMGLFLEXA -&gt; FGLV_FMGLFLEXA<br/>FMGLFLEXT -&gt; FGLV_FMGLFLEXT<br/>PSGLFLEXA -&gt; FGLV_PSGLFLEXA<br/>PSGLFLEXT -&gt; FGLV_PSGLFLEXT<br/>JVGLFLEXA -&gt; FGLV_JVGLFLEXA<br/>JVGLFLEXT -&gt; FGLV_JVGLFLEXT<br/>ZZ&lt;CUST&gt;A -&gt; ZFGLV_GLSI_C&lt;number&gt;<br/>ZZ&lt;CUST&gt;T -&gt; ZFGLV_GLTT_C&lt;number&gt;</p>\n<p><em>The ZZ* tables are General Ledger Accounting (new) line item tables and totals tables in the customer namespace. They can be identified with a SELECT on the table T800A with the restriction TTYPE IN ( 'TT', 'SI' ) AND GLFLEX = 'X' AND INACTIVE = ''.</em></p>\n<p>The views select data from the table ACDOCA and other tables and convert it into the format of the original tables so that all read accesses continue to work.</p>\n<p>The write accesses (INSERT, UPDATE, DELETE, and MODIFY operations) must be converted as described in the following sections.</p>\n<p><em><strong>1.6.1 General ledger</strong></em></p>\n<p>Write accesses to the line item tables FAGLFLEXA, FMGLFLEXA, PSGLFLEXA, JVGLFLEXA, and ZZ&lt;CUST&gt;A must be converted to the table ACDOCA. Most of the fields in the table ACDOCA match the fields in the General Ledger Accounting (new) line item tables. However, there are a few exceptions:</p>\n<ul>\n<li>ACTIV is BTTYPE in ACDOCA.</li>\n<li>RVERS is obsolete.</li>\n<li>DOCNR is obsolete.</li>\n<li>COST_ELEM is obsolete. There is only one field for the account: RACCT.</li>\n<li>LOGSYS is AWSYS in ACDOCA. </li>\n<li>Optional fields (for example, ZZHOART) have a different name in the table ACDOCA. The mapping is implemented in the method GET_ACTIVE_OPT_FIELDS of the class CL_FINS_FI_UTILITY.</li>\n</ul>\n<p>We advise against converting write accesses directly to the table ACDOCA. Instead, use the new ABAP class CL_FINS_ACDOC_CHANGE for all write accesses to ACDOCA.</p>\n<p>Write accesses to the totals tables FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, JVGLFLEXT, and ZZ&lt;CUST&gt;T must be removed without being replaced.</p>\n<p>Assignments using the group names PERIOD_DATA, TSL, HSL, KSL, OSL, MSL, and FIX must be reprogrammed.<br/>Example: The following code results in SY-SUBRC 4 in SAP Simple Finance.<br/>FIELD-SYMBOLS &lt;ls&gt; TYPE faglflext.<br/>ASSIGN COMPONENT 'HSL' OF STRUCTURE &lt;ls&gt; TO FIELD-SYMBOL(&lt;ls_hsl&gt;).</p>\n<p>In SAP Simple Finance, compatibility views for the tables FAGLFLEXA/T, FMGLFLEXA/T, PSGLFLEXA/T, JVGLFLEXA/T, and ZZ&lt;CUST&gt;A/T return data for all general ledgers. Make sure that all read accesses restrict the field RLDNR. <br/>Example:<br/>Before the upgrade to SAP Simple Finance, you had two ledgers, 0L and 1N, persisted in separate tables (0L in FAGLFLEXA/T and 1N in ZZFLEXA/T). Following an upgrade, a SELECT from FAGLFLEXA/T returns data for 0L <span>and</span> 1N. The same is true for ZZFLEXA/T.</p>\n<p>For certain posting documents, there is no entry view in transactions FB03 and FB03L. For these documents, there are only entries in BKPF/ACDOCA but not in BSEG/BSEG_ADD (see SAP Note</p>\n<p>2297729).</p>\n<p>1. Intracompany CO processes</p>\n<p>2. Foreign currency valuation (FAGL_FCV)</p>\n<p>3. GL allocations</p>\n<p>4. SAP Fiori app for foreign currency valuation</p>\n<p><strong>1.6.2 COEP</strong></p>\n<p>Use the new ABAP class CL_FCO_COEP_UPDATE for all write accesses to COEP outside the software component SAP_FIN. This ensures that actual postings in COEP are no longer updated. Important: This class does not transfer the alternative posting to the table ACDOCA.</p>\n<p>You must therefore remove write accesses to the line item tables COEP if you previously changed document line items of the value type 04 (\"Actual postings\"): Actual postings of the value type 04 are no longer supported in the table COEP. (In other words, the compatibility views COEP and COVP no longer read actual postings from COEP. Instead, they are calculated from ACDOCA.) Write accesses for value types other than \"04\" are still possible.</p>\n<p>We strongly advise against converting write accesses directly to the table ACDOCA because most of the field names in COEP differ from those in ACDOCA and, to some extent, cannot be mapped on a 1:1 basis. In addition, the new posting logic in ACDOCA differs significantly from the posting logic in COEP. Therefore, a customer-specific program cannot simply be converted to the table ACDOCA.</p>\n<p><strong>1.6.3 Asset Accounting</strong></p>\n<p>You must remove write accesses to the tables ANEA, ANEP, ANEK, ANLC, ANLP without replacing them.</p>\n<p><strong>1.6.4 Material ledger</strong></p>\n<p>You must remove write accesses to the ML document tables (MLHD, MLIT, MLPP, MLCR, MLCD) if you previously changed documents of the transaction type \"UP\" (\"Update\"). Write accesses for other transaction types are still possible.</p>\n<p>The compatibility views from ACDOCA are used to calculate update documents. We strongly advise against converting write accesses directly to the table ACDOCA because most of the field names in the ML document tables differ from those in ACDOCA and, to some extent, cannot be mapped on a 1:1 basis. Therefore, a customer-specific program cannot simply be converted to the table ACDOCA.</p>\n<p>Write accesses to the table CKMI1 must be removed. Data is calculated from ACDOCA using a compatibility view. We strongly advise against switching write accesses directly to the table ACDOCA since most of the field names of the index table differ from those of ACDOCA and sometimes cannot be mapped on a 1:1 basis. Therefore, a customer-specific program cannot simply be converted to the table ACDOCA.</p>\n<p><strong>1.6.5 T012K/T012T</strong></p>\n<p>Write accesses to the tables T012T and T012K must be converted to the tables FCLM_BAM_AMD, FCLM_BAM_AMD_CUR, FCLM_BAM_AMT_T, and FCLM_BAM_ACLINK2.<br/>We recommend that you perform write accesses via the BOR object FCLM_CR only. Otherwise, the approval processes configured in bank account management are not taken into account.</p>\n<p>The manner in which the IBAN of the house bank account is stored has been changed. It is no longer in the table TIBAN, but in the IBAN field of the table FCLM_BAM_AMD. As a result, accesses using the function modules</p>\n<p>READ_IBAN_EXT<br/>READ_IBAN_FROM_DB<br/>READ_IBAN<br/>READ_IBAN_INT<br/>SEARCH_FOR_IBAN</p>\n<p>no longer work. To read the IBAN of a house bank account, you must replace these modules as follows (old name with the attached _HBA):</p>\n<p>READ_IBAN_EXT_HBA<br/>READ_IBAN_FROM_DB_HBA<br/>READ_IBAN_HBA<br/>READ_IBAN_INT_HBA<br/>SEARCH_FOR_IBAN_HBA</p>\n<p><strong>1.6.6 Views on redirected tables</strong></p>\n<p>If, in the ABAP Dictionary, you have defined database views for one of the tables listed in Section 1.6, you must remove these views. Otherwise, you will access data in decommissioned tables.</p>\n<p>You must remove these database views from the ABAP Dictionary and replace the selection from the views by an Open SQL SELECT or a read module call. Alternatively, you can create new CDS views.</p>\n<p>The attached file lists all views that have been replaced in the standard system with equivalent ABAP source code or a new CDS view. REDIRECT technology is used to redirect the SELECTs to the new CDS views. The REDIRECT procedure cannot be used for customer-defined views.</p>\n<p>Example: The compatibility view V_COVP was created for the view COVP. In the case of SELECT FROM COVP, the SELECTs are redirected to V_COVP.</p>\n<p><strong>1.7 S/4HANA readiness checks</strong></p>\n<p>Hits from the S/4HANA readiness check \"S/4HANA: Search for ABAP Dictionary enhancements\" that reference this SAP Note can be ignored and can be suppressed by implementing correction note 2522926.</p>", "noteVersion": 21, "refer_note": [{"note": "2191738", "noteTitle": "2191738 - Error FGL_PRE_CHECK 030 in S/4HANA Pre-Transition Check", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You get the error message FGL_PRE_CHECK 030 (Field ** of index table ** is missing in table **. See SAP Note 2191738) in the S/4HANA Pre-Transition Check for FIN, executed before the upgrade to S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FGL_PRE_CHECK030, BSIS, BSAS, BSIK, BSAK, BSID, BSAD, FAGLBSIS, FAGLBSAS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>Before the Technical Upgrade</span></strong></p>\n<ol>\n<li>Add each missing index table field to the table BKPF (if it is a header field) or to the table BSEG/BSEG_ADD (if it is a line item field).</li>\n<li>Develop and execute a program to fill these fields in BKPF/BSEG/BSEG_ADD from the index tables.</li>\n</ol>\n<p>If the field length of the index table field is greater than 10 characters, this procedure is only possible in case BSEG is not a cluster table anymore. In contrast to Transparent Tables, Cluster Tables restrict the field length to 10 characters.<br/>During technical upgrade BSEG is tranformed into a Tranparent Table. As the error message in the Pre-Transistion check is skippable, you can add the fields after the technical upgrade and follow the steps described in the following section.<br/>Steps to be performed in order to skip a check are described in the blog accessible via the first link in note 2399707. If error message is not skippable, corrections contained in note 2716937 are possibly not implemented in your system.</p>\n<p><strong>After the Technical Upgrade</strong></p>\n<p>If you skip the error in your ERP system before the technical upgrade, the new views (see below) cannot be activated since the necessary fields are not available in the underlying tables. In this case you need to perform the following steps:</p>\n<ol>\n<li>Add the missing index table fields to the table BKPF (if it is a header field) or to the table BSEG/BSEG_ADD (if it is a line item field).</li>\n<li>Manually activate the views BSIS, BSAS, BSIK, etc. that could not be activated as part of the technical upgrade.</li>\n<li>Develop and execute a program to fill these fields in BKPF/BSEG/BSEG_ADD from the *_BCK tables.</li>\n</ol>\n<p><strong>Background</strong></p>\n<p>The index tables BSIS, BSAS, BSIK etc. get renamed from BSIS_BCK, BSAS_BCK, BSIK_BCK etc. during upgrade.</p>\n<p>Tables BSIS, BSAS, BSIK etc. are then replaced with <span>views</span> BSIS, BSAS, BSIK etc., also known as <em>compatibility views</em> or <em>equally-named views</em>.</p>\n<p>These views select data from tables BKPF/BSEG/BSEG_ADD. All fields added by the customer or partner to BSIS, BSAS, BSIK etc. must also exist in BKPF or BSEG/BSEG_ADD, otherwise it is not possible to create these views and the upgrade runs into errors.</p>\n<p>The step \"<em>Develop and execute a program to fill these fields in BKPF/BSEG/BSEG_ADD</em>\" of the solution proposal is necessary to prevent a data loss.</p>", "noteVersion": 7}, {"note": "2160045", "noteTitle": "2160045 - S/4HANA Finance: Fields of Appends to COEP and BSEG missing in table ACDOCA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are upgrading to sFIN from a system where customer fields have been added to COEP or BSEG via an Append Structure or by modification. These fields are missing in table ACDOCA.</p>\n<p>If you have added these fields to table ACDOCA via an Append Structure directly, there is a syntax error in class ZCL_FINS_MIG_UJ_HDB_GENERATED: \"SQLSCRIPT message: return type mismatch ...\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>AMDP_NATIVE_DBCALL_FAILED, CX_AMDP_NATIVE_DBCALL_FAILED, SQL code of the database: 1306</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Coding block extension fields in Include Structure CI_COBL are automatically contained in ACDOCA and considered by data migration. This is the recommended extensibility approach for the unified journal and you don't have to perform manual actions for data migration. The S/4 HANA extensibility concept is supported as of S/4HANA Finance 1605 and S/4HANA 1511 and provides an alternative extension of table ACDOCA only based on Extension Include INCL_EEW_ACDOC. This include already exists in sFIN 1503, however, and can already be used there for customer fields.</p>\n<p>The use of Append Structures directly on table ACDOCA is not supported. Adding an Append Structure directly to table ACDOCA will result in the syntax error mentioned above. You should create an Append Structure only if you require fields from old Append Structures to BSEG or COEP that can't be moved to CI_COBL. In that case, however, you have to create an Append Structure to Structure<strong> INCL_EEW_ACDOC</strong> and put the fields there. The migration program will then consider these fields in the line item migration, i.e. fields with identical name in BSEG/COEP and in the Append to INCL_EEW_ACDOC will be migrated from BSEG/COEP to ACDOCA automatically.</p>\n<p>If you face a syntax error in generated class ZCL_FINS_MIG_UJ_HDB_GENERATED, it can be re-generated by report FINS_MIG_UJ_MAPPING_GENERATE after creating the Append Structure to Structure INCL_EEW_ACDOC.</p>", "noteVersion": 6}, {"note": "2186803", "noteTitle": "2186803 - SAP Simple Finance, OP: Treatment of NewGL tables and customer enhancements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are upgrading to SAP Simple Finance, on-premise edition from a system in which you are using a customer enhancements in the NewGL. This note describes how the NewGL tables and their potential customer enhancements are handled while the upgrade.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The customer enhancements of the Standard SAP NewGL tables are taken into account and included into the corresponding compatibility views by means of generation tool (transaction FINS_MIG_REGENERATE).</p>\n<p>The compatibility views for the customer-specific NewGL tables are generated as well based on the corresponding table structures (transaction FINS_MIG_REGENERATE). The names for generated compatibility views are composed according to the following patterns:</p>\n<p>- ZFGLV_GLSI_C&lt;number&gt;  for line item tables</p>\n<p>- ZFGLV_GLTT_C&lt;number&gt;  for totals tables.</p>\n<p>The numbering and the assignment to the corresponding tables can be found in the table FGLT_GLTAB.</p>\n<p>In order to enable a selection from the new data source a redirection from the NewGL tables to the corresponding compatibility views is established in the Data Dictionary automatically.</p>\n<p>The old data in the NewGL tables can still be displayed by means of the CDS-Views named V_&lt;tablename&gt;_ORI.</p>\n<p>After the upgrade to SAP Simple Finance, OP is finished it is not possible anymore to extent the existing NewGL tables.</p>", "noteVersion": 1}]}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Implementation project required", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Data cleanup / archiving", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Data correction", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Process Design / Blueprint", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Fiori Implementation", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": ""}]}