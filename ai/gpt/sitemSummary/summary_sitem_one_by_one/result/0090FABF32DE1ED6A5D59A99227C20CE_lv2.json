{"guid": "0090FABF32DE1ED6A5D59A99227C20CE", "sitemId": "SI31: Logistics_General", "sitemTitle": "S4TWL - Seasonal Procurement", "note": 2368913, "noteTitle": "2368913 - S4TWL - Seasonal Procurement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p><q class=\"SAPXDPScreenElement\" title=\"Object name\">Seasonal procurement</q> covers purchasing processes that are specially designed to suit the requirements of seasonal procurement of retail goods, for example, from the area of fashion.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, seasonal procurement is not available anymore.</p>\n<p>Software packages WRF_APC, WRF_AT_GENERATE, WRF_PRC_CON_APPL, WRF_PRC_CTR_APPL, WRF_PRC_OTB_APPL, WRF_PRC_POHF_APPL, WRF_SEAS.</p>\n<p>Transactions: WRFAPC*, WA10, WCOC*, WPCTR*, WOTB*, WPOHF*, WRFROCAT, WOOS.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Processes for fashion retailers that are available already or wíll be released in the future should be used for seasonal procurement. Planning solutions SAP Merchandising Planning for Retail, and SAP Assortment Planning for Retail should be considered as well.</p>\n<p>In case you reuse ABAP objects of packets WRF_AT_GENERATE, WRF_PRC_BASIC_APPL, WRF_PRC_BASIC_DDIC, WRF_PRC_CON_APPL, WRF_PRC_CON_DDIC, WRF_PRC_EM_AI, WRF_PRC_MAIN, WRF_PRC_OTB_APPL, WRF_PRC_OTB_DDIC, WRF_PRC_POHF_APPL, WRF_PRC_POHF_APPL_SFWS_SC, WRF_PRC_POHF_DDIC, WRF_PRC_POHF_DDIC_SFWS_UI_VS, WRF_SEAS in your custom code, please see attached note</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if seasonal procurement is used. <br/>This is the case if transactions, WRFAPC01, WRFAPC02, WRFAPC03, WRFAPC11, WRFAPC12, WRFAPC14, WRFAPC15, WRFAPC16, WRFAPC17, WRFAPC22, WRFAPC23, WRFAPC24, WRFAPC25, WRFAPC26, WRFAPC27, WRFAPC30, WRFAPC35, WRFAPC36, WRFAPC37, WRFAPC40, WA10, WCOC, WCOCS, WOTB1, WOTB2, WOTB3, WOTB4, WOTB6, WPCTRA, WPCTRD, WPCTRQ, WPOHF1, WPOHF2C, WPOHF2D, WPOHF2DS, WPOHF2X, WPOHF2XS, WPOHF3C, WPOHF3D, WPOHF3DS, WPOHF3X, WPOHF3XS, WPOHF4C, WPOHF4D, WPOHF4DS, WPOHF4X, WPOHF4XS, WPOHF7, WPOHF8, WRFROCAT, WOOS are used.</p>\n<p>This also can be checked via transaction SE16N. Enter table WRF_APC_PLH and check whether there are any entries.<br/>This also can be checked via transaction SE16N. Enter table EKKO and check whether there are entries with field POHF_TYPE (Seasonal Purchase Order Processing) is not equal blank.</p>", "noteVersion": 3, "refer_note": [{"note": "2543543", "noteTitle": "2543543 - Restrictions for BW extractors relevant for S/4HANA in the area of Retail", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of Retail as part of the product version SAP S/4HANA, on-premise edition 1610 and higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, IS-Retail</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not supported any longer:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"10\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>DataSource</td>\n<td>Appl.component</td>\n<td>Restriction</td>\n<td>Comment</td>\n<td>Related SI</td>\n</tr>\n<tr>\n<td>0FIPMATNR_ATTR</td>\n<td>LO-RFM-PUR-FIP and MM-PUR-FIP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368747\" target=\"_blank\">2368747</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_RMA_VAL_TRAN</td>\n<td>LO-RFM-OBS and LO-LIS-DC</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368736\" target=\"_blank\">2368736</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S202</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S207</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S208</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S212</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S214</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S219</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_RMA_MBEW_TRAN</td>\n<td>LO-RFM-OBS and MM-IM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368736\" target=\"_blank\">2368736</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_STOREGA_ATTR</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_STOREGR_TEXT</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT1_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER1_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT1_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER1_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT2_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER2_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT2_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER2_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT3_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER3_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT3_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER3_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT4_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER4_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT4_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER4_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT5_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER5_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT5_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER5_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT6_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER6_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT6_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER6_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT7_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER7_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT7_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER7_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_HIER_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_HIEID_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_HIER_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_HIEID_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_ROLE_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_PRODUCT_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_CDTH_HIER</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_PRODUCT_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_STRAT_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S278</td>\n<td>CA-OIW</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 2LIS_03_BX</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2370131\" target=\"_blank\">2370131</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_SEASON_TEXT</td>\n<td>CA-OIW</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 0RF_SEASON_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2481829\" target=\"_blank\">2481829</a></span></span></td>\n</tr>\n<tr>\n<td>0RF_OAPC_MPA</td>\n<td>LO-RFM-OBS and LO-MD-RA</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 0RF_OAPCMPA_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368913\" target=\"_blank\">2368913</a></span></span></td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4, "refer_note": [{"note": "2500202", "noteTitle": "2500202 - S4TWL - BW Extractors in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p dir=\"ltr\">Customers considering moving to SAP S/4HANA (on premise) seek information whether BW extractors (aka data sources) known form SAP ERP still work, to be able to evaluate the potential impact on the BW environment when moving to SAP S/4HANA. To meet this requirement, SAP reviewed the status and the attached list (MS Excel document) provides the information that we have per extractor. The results are valid from SAP S/4HANA 1709 (on premise) until further notice or otherwise and in most cases apply to SAP S/4HANA 1610 and SAP S/4HANA 1511 as well (see respective flags in attached MS Excel document).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW, Extractors, Datasources, SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p dir=\"ltr\">Parts of this information are already available in CSS and can be found through 2333141 - SAP S/4HANA 1610: Restriction Note. The status of the extractors is collected in one list (the attached MS Excel document), see the attached MS Powerpoint document for information how the list is typically used. Not all extractors that are technically available in SAP S/4HANA are covered, SAP is planning to provide clarifications for additional extractors and share new versions of the list via this note. Hence it is recommended to review this note for updates.</p>\n<p>The information in this file is dated as of 10.09.2018. Please acknowledge that S/4HANA functionality is set forth in the S/4HANA Feature Scope Description. All business functionality not set forth therein is not licensed for use by customer.</p>\n<p><em>Please note that extractors in the namespace 0BWTC*, 0TCT* and 8* are related to the \"Embedded BW\" that is offered as a technology component within the S/4HANA software stack. They are all not explicitly whitelisted in this SAP Note as they are not part of the delivered Business Content by the S/4HANA Lines of Business but can be used for extraction. </em><em> </em></p>\n<ul>\n<li><em>0BWTC* and 0TCT* are extractors providing technical statistical information from the Embedded BW such as query runtime statistics or data loading statistics. </em></li>\n<li><em>8* are <a href=\"https://help.sap.com/viewer/64e2cdef95134a2b8870ccfa29cbedc3/7.5.6/en-US/4c1a1b9054914c86e10000000a42189e.html\" target=\"_blank\">Export DataSources</a> for transferring business data from InfoProviders of the “Embedded BW” system to a target system</em></li>\n<ul>\n<li><em>For a SAP BW target systems this is achieved by the <a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.5.6/en-US/4a1411c3174f0452e10000000a421937.html\" target=\"_blank\">SAP Source System</a> type </em></li>\n<li><em>For a data transfer via the <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/2.0.1/en-US/c6afacb707764885a6fb62f511c24f34.html\" target=\"_blank\">ODP Source System</a> type (only option in SAP BW/4HANA target system) these Export DataSources are obsolete and invisible. Instead, the ODP-BW context is used. For more information on Operational Data Provisioning see the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">ODP FAQ document</a>.</em></li>\n</ul>\n</ul>\n<p><em> For more information and positioning on the \"Embedded BW\" see:</em> <a href=\"https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html\" target=\"_blank\"><em>https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html</em></a></p>\n<p> For more information on partner registered data sources and to find partner details from partner namespaces, please create a message on component XX-SER-DNSP.</p>\n<p><strong>This is a collective note, containing information of several industries and areas. In case issues arrise with extractors listed in the Excel, or missing extractors, please do raise an incident on the APPLICATION COMPONENT of the respective EXTRACTOR. This is the <span>only</span> way to ensure that your incident reaches the responsible desk in the shortest time. You can find this in column E, 'Appl.component' in the attached excel document. Do not use the component from this note. Thank you!</strong></p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Details can be found in the respective note per area:</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Item Area - Line of Business</strong></td>\n<td><strong>Note number for details</strong></td>\n</tr>\n<tr>\n<td>Asset Management</td>\n<td>\n<p><a href=\"/notes/2299213\" target=\"_blank\">2299213</a> - Restrictions for BW-Extractors in S/4HANA in the Enterprise Asset Management domain (EAM)</p>\n</td>\n</tr>\n<tr>\n<td>Business Process Management</td>\n<td>\n<p><a href=\"/notes/2796696\" target=\"_blank\">2796696</a> - Restrictions for BW extractors relevant for S/4HANA as part of SAP S/4HANA, on-premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Customer Services</td>\n<td>\n<p><a href=\"/notes/2533548\" target=\"_blank\">2533548</a> - Restrictions for BW-Extractors in S/4HANA in the CS (Customer Service) area</p>\n</td>\n</tr>\n<tr>\n<td>Enterprise Portfolio and Project Management</td>\n<td>\n<p><a href=\"/notes/2496759\" target=\"_blank\">2496759</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Enterprise Portfolio and Project Management</p>\n</td>\n</tr>\n<tr>\n<td>Financials</td>\n<td>\n<p><a href=\"/notes/2270133\" target=\"_blank\">2270133</a> - Restrictions for BW extractors relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</p>\n</td>\n</tr>\n<tr>\n<td>Flexible Real Estate</td>\n<td>\n<p><a href=\"/notes/2270550\" target=\"_blank\">2270550</a> - S4TWL - Real Estate Classic</p>\n</td>\n</tr>\n<tr>\n<td>Global Trade</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Globalization Services Finance</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\">2559556</a> - Restrictions for BW extractors in Financial Localizations relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</td>\n</tr>\n<tr>\n<td>Human Resources</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\"> </a></td>\n</tr>\n<tr>\n<td>Incident Management and Risk Assessment </td>\n<td> <a href=\"/notes/2267784\" target=\"_blank\">2267784</a> - S4TWL - Simplification in Incident Management and Risk Assessment</td>\n</tr>\n<tr>\n<td>Master Data</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\">2498786</a> - Data Sources supported by Central Master Data in S/4HANA</p>\n<p><a href=\"/notes/2576363\" target=\"_blank\" title=\"2576363  - Data Sources supported by Central Master Data in S/4HANA\">2576363</a> - Data Sources supported by Master Data Governance in S/4HANA</p>\n</td>\n</tr>\n<tr>\n<td>Procurement</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2504508\" target=\"_blank\">2504508</a> - Restrictions for BW Extractors relevant for S/4 HANA Procurement as part of SAP S/4HANA, on premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Produce</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2499728\" target=\"_blank\">2499728</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Detailed Scheduling <br/><a href=\"/notes/2499716\" target=\"_blank\">2499716</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Control <br/><a href=\"/notes/2499589\" target=\"_blank\">2499589</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Quality Management<br/><a href=\"/notes/2499310\" target=\"_blank\">2499310</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Inventory Management</p>\n</td>\n</tr>\n<tr>\n<td>Sales and Distribution</td>\n<td><a href=\"/notes/2498211\" target=\"_blank\">2498211</a> - Restrictions for BW extractors relevant for S/4HANA Sales as part of SAP S/4HANA, on-premise edition 1709</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Extended Warehouse Management</p>\n</td>\n<td>\n<p><a href=\"/notes/2552797\" target=\"_blank\">2552797</a> List of BI Data Sources used in EWM<br/><a href=\"/notes/2382662\" target=\"_blank\">2382662</a> List of BI Data Sources from SCM Basis used in EWM Context</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Transportation Management</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Master data governance for Finance</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Item Area - Industry</strong></p>\n</td>\n<td><strong>Note number for details</strong>                                                                                                                  </td>\n</tr>\n<tr>\n<td>DIMP Automotive</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Defense and Security</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\">2544193</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Defense &amp; Security</p>\n<p><a href=\"/notes/2273294\" target=\"_blank\">2273294</a> - S4TWL - BI content, Datasources and Extractors for DFPS</p>\n</td>\n</tr>\n<tr>\n<td>Financial Services                                        </td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\">2543469</a> - \"SAP for Banking\": SAP extractors in connection with \"SAP S/4HANA on-premise edition</p>\n<p><span><a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235343631303226\" target=\"_blank\">2546102</a></span> - \"SAP for Insurance\": SAP extractors in connection with \"SAP S/4HANA on-premise edition“</p>\n</td>\n</tr>\n<tr>\n<td>Higher Education</td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>IS Healthcare</td>\n<td>-</td>\n</tr>\n<tr>\n<td>IS Utilities</td>\n<td>\n<p><a href=\"/notes/2270505\" target=\"_blank\">2270505</a> - S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)</p>\n</td>\n</tr>\n<tr>\n<td>Oil and Gas</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Collection and Disbursement (PSCD)</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Management (PSM)</td>\n<td><a href=\"/notes/2556359\" target=\"_blank\">2556359</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Public Sector Management</td>\n</tr>\n<tr>\n<td>IS Retail</td>\n<td><a href=\"/notes/2543543\" target=\"_blank\">2543543</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Retail</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> The classification scheme is:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"4\" cellspacing=\"1\" class=\"table table-bordered table-striped col-resizeable\" dir=\"ltr\">\n<tbody>\n<tr>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Current status                             </strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Status for Publication</strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\"><strong>Description                                                             </strong></p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 and works without any restrictions compared to ERP</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working – regeneration of extractor and check of BW content based on this DS is needed                                                                                          .</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is working, because of data model changes, it is recommended to check the upward dataflow.</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"3\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 but works with noteworthy restrictions; e.g. not all fields are available</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative exists</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but an alternative exists, such as a new extractor, CDS view</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative planned</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but equivalent available on roadmap for future release</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Deprecated</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS obsolete</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is obsolete - legacy extractors</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - no alternative exists</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but no alternative exists</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Generated Data Source</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Because of the nature of the extractors, being generated in the system, we cannot whitelist those in general. Experience so far showed that they should be working without restrictions.</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not relevant for BW extraction</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not relevant</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Datasource is available in ROOSOURCE, however, not to be used for extraction by BW.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 41}]}, {"note": "2330577", "noteTitle": "2330577 - SAP S/4HANA Retail : Checks for Fashion Management Functionalities", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to migrate an existing SAP ERP for Retail system to an SAP S/4HANA Retail system. To check whether the prerequisites for the EA-RETAIL software component are met for an SAP S/4HANA Retail migration, some preparatory checks must be done in the existing SAP ERP for the retail system. The checks will be executed for following Fashion Management functionalities:</p>\n<ol>\n<li>Quota scale</li>\n<li>Seasonal procurement</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span>S4TC, Fashion Management Functionalities Migration to S4HANA,  </span><span arial;\"=\"\" mso-bidi-font-family:=\"\" verdana',sans-serif;=\"\">S/4</span><span verdana',sans-serif;\"=\"\">4HANA </span><span arial;\"=\"\" mso-bidi-font-family:=\"\" verdana',sans-serif;=\"\">precheck master check class EA-RETAIL, C</span><span 10.0pt;=\"\" arial;\"=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" verdana',sans-serif;=\"\">heck ID SAP_EA_RETAIL_FSH.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li>In SAP S/4HANA Retail, the <em>quota scale</em> functionality is no longer available. It has been replaced by the <em>distribution curve</em> functionality in SAP S/4HANA Retail.</li>\n<li>In SAP S/4HANA Retail, <em>seasonal procurement</em> is no longer available. The seasonal purchase orders would be treated as standard purchase orders.</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>Implement all SAP Notes that contain the individual prechecks in your system.</li>\n<li>See SAP Note <a href=\"/notes/2328046\" target=\"_blank\">2328046</a> to resolve the warning messages issued by this check.</li>\n</ol>\n<p> </p>\n<p> </p>", "noteVersion": 8, "refer_note": [{"note": "2343515", "noteTitle": "2343515 - SAP S/4HANA Retail : Checks for Fashion Management Functionalities", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>During the migration of SAP ERP for Retail to S/4, prechecks would be carried out for following Fashion Management related functionalities:</p>\n<ul>\n<li>Check if quota scales functionality is used in system. </li>\n<li>Check if Seasonal Procurement functionality is used in system.</li>\n</ul>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<div class=\"longtext\">\n<p><span>S4TC; Fashion Management Functionalities Migration to S4HANA</span></p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In S4HANA Quota scales functionality is no longer available. Quota scales is replaced by Distribution Curves in S4HANA.</p>\n<p>In S4HANA, Seasonal Procurement is no longer available. The seasonal purchase orders would be treated as standard purchase orders.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This note refers to check id :   SAP_EA_RETAIL_FSH.</p>\n<p>Please see SAP Note -  2328046 in order to resolve the warning messages issued by this precheck.</p>", "noteVersion": 2}, {"note": "2339361", "noteTitle": "2339361 - S4 PreChecks EA-RETAIL: Checks for Retail Store Fiori app", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the migration of SAP ERP for Retail to S/4, prechecks would be carried out for following Retail Store Fiori applications:</p>\n<ul>\n<li>Transfer Stock</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>EA-RETAIL, Retail Store, Fiori, Transfer Stock</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4 HANA, the primary key of the table TRF_DOC_DETAILS has been changed. Old data in the table cannot be used anymore in SAP S/4 HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This note refers to check id: SAP_EA_RETAIL_FIO.</p>\n<p>Please see SAP Note -  2339756 in order to resolve the warning messages issued by this precheck</p>", "noteVersion": 3}, {"note": "2339756", "noteTitle": "2339756 - S4 PreChecks EA-RETAIL: Descriptions of checks for Retail Store Fiori app", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For migrating to the target system S/4 HANA OP 1610, the class check is used. And the following message could return from the class execution.</p>\n<ul>\n<li>1. The check returns error message saying 'CLIENT: XXX, incompleted Transfer Stock data in staging tables cannot be migrated to S/4. Please finish the incompleted Transfer Stock tasks in Transfer Stock Fiori application.' </li>\n<li>2. The check returns error rmessage saying 'CLIENT: XXX, cannot clean up Transfer Stock data in staging tables before migrating to S/4. Please run the cleanup job to clean up the data in staging table.'</li>\n</ul>\n<p>For migrating to the target system S/4 HANA OP 1709, the simplified check is used. A few table checks are defined in transition DB and message could return by this tool when the pre-check step is executed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, EA-RETAIL, Transfer Stock, SAP_EA_RETAIL_FIO, Retail Store</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Transfer Stock: In S4HANA, the primary key of the table TRF_DOC_DETAILS has been changed. Old data in the table cannot be used anymore in S4HANA. To avoid data inconsistence we suggest to delete the data in the following tables TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS in source system before the migration or in target system after migration.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For migrating to the target system S/4 HANA OP 1610, please do the following on source system:</p>\n<ul>\n<li>For symptom 1: Use Transfer Stock Fiori application to complete the Open/Rejected transfer stock documents by either submit the transfers or delete them.</li>\n<li>For symptom 2: Use the function module - RETAIL_ST_TS_CLEANUP_STAGE_TAB to clean up the staging tables of Transfer Stock at ERP Retail system.</li>\n</ul>\n<p>Note: you can dalso use any other tools to directly delete all the entries in the following tables - TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS instead of using the Fiori application or FM mentioned above.</p>\n<p>For migrating to the target system S/4 HANA OP 1709, please use the following steps on source system (before migration) or target system (after migration) to clean up the entries in the following tables - TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS:</p>\n<ul>\n<li>Step 1: Use Transfer Stock Fiori application to complete the Open/Rejected transfer stock documents by either submit the transfers or delete them.</li>\n<li>Step 2: Use the function module - RETAIL_ST_TS_CLEANUP_STAGE_TAB to clean up the staging tables of Transfer Stock at ERP Retail system.</li>\n</ul>\n<p>Note: you can dalso use any other tools to directly delete all the entries in the following tables - TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS instead of using the Fiori application or FM mentioned above.</p>", "noteVersion": 2}, {"note": "2326521", "noteTitle": "2326521 - S4TC EA_RETAIL master check class for S/4 system transformation checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to migrate an existing SAP ERP for Retail system to an S/4 system. To check whether the prerequisites for the software component EA-RETAIL are met for an S/4 migration, some preparatory checks must be carried out in the existing SAP ERP for Retail system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 precheck master check class EA-RETAIL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Also implement all SAP Notes that contain the individual prechecks in your system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instructions in your system.</p>", "noteVersion": 13}]}, {"note": "2328046", "noteTitle": "2328046 - Quota Scale and Seasonal Procurement Migration in S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to migrate the quota scale and seasonal procurement data from an existing <strong>SAP ERP for Retail</strong> system to an <strong>SAP S/4HANA Retail</strong> system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, Seasonal Procurement, Quota Scales, Migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP ERP for Retail, quota scales are used in different applications like - prepack article creation, allocation table, to distribute the quantities to the variant. In SAP S/4HANA Retail, quota scales are deprecated and distribution curves will be used. For this reason, quota scale master data have to be converted to distribution curve master data.</p>\n<p>In SAP ERP for Retail, special purchasing processes use seasonal procurement. In SAP S/4HANA Retail, seasonal procurement functionality is not supported. Hence if the seasonal purchase orders have to be used in a S/4 HANA system they must be converted to standard purchase orders.</p>\n<p><strong>Prerequisites</strong></p>\n<p>Please implement the bug fixes for quota scale migration report in SAP Note <strong>2380068</strong> before executing the migration for quota scales(required only if migrating to S4HANA 1610 release).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span>To migrate quota sales to distribution curves, and seasonal purchase orders to standard purchase orders, follow the manual instructions.</span></p>\n<p><span>Note - In case there are quota scales in your system that are created for characteristics that do not use new tables for characteristic value storage (WRF_CHARVAL, WRF_CHARVALT etc), then you would have to additionally migrate these characteristics and characteristic values so as to make the distribution curves work. The detailed steps for this migration is described in SAP Note <strong>2122891</strong>.</span></p>", "noteVersion": 4}, {"note": "2383533", "noteTitle": "2383533 - S4TWL - Retail Deprecated Applications Relevance for Custom Code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related business applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management some applications and the respective development objects are not available anymore. <br/>This might be relevant for customer specific coding. If customer specific coding re-used those development objects in ERP, the coding needs to be adjusted accordingly.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Adjust customer specific coding accordingly.</p>", "noteVersion": 2}], "activities": [{"Activity": "Implementation project required", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "If you choose to implement planning solutions like SAP Merchandise Planning for Retail and SAP Assortment Planning to replace seasonal procurement functionality then the activity is applicable."}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Seasonal Procurement process will have to be redesigned using processes available for fashion retailers or Planning Solutions like SAP Merchandising Planning for Retail, and SAP Assortment Planning."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If customer specific coding re-used development objects in ERP which are no longer available in SAP S4/HANA, the coding needs to be adjusted accordingly."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users need to be trained as per new process designed for Seasonal Procurement"}]}