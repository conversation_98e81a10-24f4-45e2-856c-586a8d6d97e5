{"guid": "0050569455E21ED5B3E176783911009E", "sitemId": "SI1: Logistics_MM-IM", "sitemTitle": "S4TWL - DATA MODEL IN INVENTORY MANAGEMENT (MM-IM)", "note": 2206980, "noteTitle": "2206980 - Material Inventory Managment: change of data model in S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP S/4HANA and need additional information how to adjust your customer enhancements, modifications or own functionalities to the new, simplified data model of SAP S/4HANA Supply Chain (MM - Inventory Management).</p>\n<p>You want to have information about what is different in SAP S/4HANA Supply Chain (MM - Inventory Management) compared to Suite on HANA MM-IM.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span 'times=\"\" 11pt;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" font-size:=\"\" lang=\"EN-US\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-ascii-theme-font:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" mso-hansi-theme-font:=\"\" new=\"\" roman';=\"\">S4TC, S/4 transition, MM-IM, Material Management</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have customer enhancements, modifications or own functionalities in the area of inventory management (component MM-IM) which were built for SAP ERP 6.0.</p>\n<p>You are using functionalities which behave different in SAP S/4HANA Supply Chain (MM - Inventory Management) compared to Suite on HANA.</p>\n<p> </p>\n<p>The SAP ERP 6.0 stock inventory management data model consists of the two document tables MKPF for document header information and MSEG for document item data. Additionally there were aggregated actual stock quantity data stored in several tables. Some of these tables do also store material master data attributes like the tables MARC, MARD and MCHB. Such tables with material master data attributes as well as actual stock quantities will be named as hybrid tables in the following. In contrast there are also tables like MSSA containing only aggregated actual stock quantities for sales order stock. Such tables will be called in the following as replaced aggregation tables.</p>\n<p>With S/4HANA this data model has been changed significantly. The new de-normalized table MATDOC has been introduced which contains the former header and item data of a material document as well as a lot of further attributes. Material document data will be stored in MATDOC only and not anymore in MKPF and MSEG. Additionally the aggregated actual stock quantities will not be persisted anymore in the hybrid or replaced aggregation tables. Instead, actual stock quantity data will be calculated on-the-fly from the new material document table MATDOC for which some of those additional special fields are used. Hence, with the new MM-IM data model the system will work on database level in an INSERT only mode without DB locks. Nevertheless, for stock decreasing processes there will be still ABAP locks to ensure stock consistency. A further advantage of the new MM-IM data model is the capability of simple and fast reporting because the most information is all in one place: MATDOC.</p>\n<p>All below mentioned tables of the SAP ERP 6.0 product do still exist in S/4HANA as DDIC definition as well as database object and the hybrid tables will still be used to store the material master data attributes. For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables ensuring that each read access to one of the mentioned tables below still returns the data as before in SAP ERP 6.0. The CDS Views do the on-the-fly aggregation of actual stock quantities from the new MM-IM data model and join the master data attributes from the material master data table. Hence all customer coding reading data from those tables will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted.</p>\n<p>The affected tables are:</p>\n<div class=\"table-responsive\"><table class=\"confluenceTable\">\n<tbody>\n<tr><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\">Table</div>\n</th><th class=\"confluenceTh\">Table description</th><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\">DDL Source of CDS View for redirect</div>\n</th><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\"><strong>View to read the content of the database table (w/o redirect to compatibility view)</strong></div>\n</th><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\">View to read the master data attributes only</div>\n</th></tr>\n<tr>\n<td class=\"confluenceTd\">MKPF</td>\n<td class=\"confluenceTd\">Material document header</td>\n<td class=\"confluenceTd\">NSDM_DDL_MKPF</td>\n<td class=\"confluenceTd\">NSDM_MIG_MKPF</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSEG</td>\n<td class=\"confluenceTd\">Material document item</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSEG</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSEG</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">\n<p>MARC</p>\n</td>\n<td class=\"confluenceTd\">Plant Data for Material</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARC</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARC</td>\n<td class=\"confluenceTd\">V_MARC_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MARD</td>\n<td class=\"confluenceTd\">Storage Location Data for Material</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARD</td>\n<td class=\"confluenceTd\">V_MARD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCHB</td>\n<td class=\"confluenceTd\">Batch stocks</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCHB</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCHB</td>\n<td class=\"confluenceTd\">V_MCHB_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MKOL</td>\n<td class=\"confluenceTd\">Special Stocks from Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MKOL</td>\n<td class=\"confluenceTd\">NSDM_MIG_MKOL</td>\n<td class=\"confluenceTd\">V_MKOL_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSLB</td>\n<td class=\"confluenceTd\">Special Stocks with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSLB</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSLB</td>\n<td class=\"confluenceTd\">V_MSLB_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKA</td>\n<td class=\"confluenceTd\">Sales Order Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKA</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKA</td>\n<td class=\"confluenceTd\">V_MSKA_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSA</td>\n<td class=\"confluenceTd\">Total Customer Orders on Hand</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSA</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSA</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSPR</td>\n<td class=\"confluenceTd\">Project Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSPR</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSPR</td>\n<td class=\"confluenceTd\">V_MSPR_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSL</td>\n<td class=\"confluenceTd\">Total Special Stocks with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSL </td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSL </td>\n<td class=\"confluenceTd\"></td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSQ</td>\n<td class=\"confluenceTd\">Project Stock Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSQ</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSQ</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKU</td>\n<td class=\"confluenceTd\">Special Stocks with Customer</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKU</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKU</td>\n<td class=\"confluenceTd\">V_MSKU_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTB</td>\n<td class=\"confluenceTd\">Stock in Transit</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTB</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTB</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTE</td>\n<td class=\"confluenceTd\">Stock in Transit to Sales and Distribution Document</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTE</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTE</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTQ</td>\n<td class=\"confluenceTd\">Stock in Transit for Project</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTQ</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTQ</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSD</td>\n<td class=\"confluenceTd\">DIMP: Customer Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSD</td>\n<td class=\"confluenceTd\">MCSD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSS</td>\n<td class=\"confluenceTd\">DIMP: Total Customer Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSS</td>\n<td class=\"confluenceTd\">MCSS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSCD</td>\n<td class=\"confluenceTd\">DIMP: Customer stock with vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSCD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSCD</td>\n<td class=\"confluenceTd\">MSCD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSCS</td>\n<td class=\"confluenceTd\">DIMP: Customer stock with vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSCS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSCS</td>\n<td class=\"confluenceTd\">MSCS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSFD</td>\n<td class=\"confluenceTd\">DIMP: Sales Order Stock with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSFD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSFD</td>\n<td class=\"confluenceTd\">MSFD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSFS</td>\n<td class=\"confluenceTd\">DIMP: Sales Order Stock with Vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MFS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSFS</td>\n<td class=\"confluenceTd\">MSFS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSID</td>\n<td class=\"confluenceTd\">DIMP: Vendor Stock with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSID</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSID</td>\n<td class=\"confluenceTd\">MSID_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSIS</td>\n<td class=\"confluenceTd\">DIMP: Vendor Stock with Vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSIS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSIS</td>\n<td class=\"confluenceTd\">MSIS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSRD</td>\n<td class=\"confluenceTd\">DIMP: Project Stock with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSRD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSRD</td>\n<td class=\"confluenceTd\">MSRD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSRS</td>\n<td class=\"confluenceTd\">DIMP: Project Stock with Vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSRS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSRS</td>\n<td class=\"confluenceTd\">MSRS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">\n<p>MARCH</p>\n</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARCH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARCH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MARDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCHBH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCHBH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCHBH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MKOLH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MKOLH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MKOLH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSLBH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSLBH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSLBH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKAH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKAH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKAH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSAH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSAH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSAH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSPRH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSPRH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSPRH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSQH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSQH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSQH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKUH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKUH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKUH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTBH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTBH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTBH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTEH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTEH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTEH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTQH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTQH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTQH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSSH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSSH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSSH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSCDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSCDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSCDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSFDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSFDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSFDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSIDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSIDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSIDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSRDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSRDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSRDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n</tbody>\n</table></div>\n<p>The hybrid tables of the former Industry Solution DIMP have now new tables containing the material master data only. The name of the new tables is presented in the right column of above table.</p>\n<p> <span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">According to the fact that data will not persisted anymore in the header and item tables MKPF and MSEG the transaction DB15 behaves differently in the environment of archiving.</span></p>\n<p><span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">Transaction DB15, which allows the retrieval of statistical data for DB tables grouped by the archiving objects that refer to these tables, does not provide correct information for tables MKPF and MSEG. When selecting tables from which data is archived for archiving object MM_MATBEL, and navigating to “Online Space” or “Space Statistics” for tables MKPF or MSEG, the statistics “No. Records” and “Table Space” are shown in the result screen. These numbers are taken from the original tables MKPF and MSEG, and not calculated by redirecting the request to table MATDOC. Consequently, when executing archiving for arching object MM_MATBEL, this will have no effect on the numbers shown for tables MKPF and MSEG in transaction DB15.</span></p>\n<p><span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">Some of the hybrid tables contain a field with the semantic \"date of last change\" (in many cases field name is LAEDA). In the ERP solution this field has been updated with each material document posting. With the introduction of the new data model in S/4 the hybrid tables will not be updated with actual stock data anymore and also the field LAEDA will not be updated. Hence, the semantic of this field for the material master data hybrid tables changes to \"date of last change of master data\".</span></p>\n<div class=\"table-wrap\"></div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>One impact of the simplified MM-IM data model does exist if there are customer APPENDs or INCLUDEs with customer fields on the mentioned tables. The NetWeaver redirect capability requires that DB table and assigned proxy view is compatible in the structure: number of fields, their sequence and their type. Thus if there is an APPEND or INCLUDE on one of the above mentioned tables then the assigned DDL source of the CDS view must be made compatible. In some cases for S/4HANA on-premise 1511 this does not require customer interaction especially in those cases where the append has been put at the end of a table which is strongly recommended (not somewhere in between which may happen if a table is composed by include structures like for MARC). For other cases and in general for S/4HANA on-premise 1610 the structure of the proxy view can be made compatible to the table by extension view. This extension view is always an extension to the above mentioned view in the DDL source of the CDS view used for redirect. In the extension view the fields have to be listed in exactly the same order as in the append. For more information about view extension see e.g. <a class=\"external-link\" href=\"http://help.sap.com/abapdocu_740/en/index.htm?file=abencds_f1_extend_view.htm\" rel=\"nofollow\" target=\"_blank\">SAP NetWeaver 7.4 documentation</a>.</p>\n<p>Another impact of the simplified MM-IM data model is a performance decrease of DB read operations on the above mentioned tables just because a data fetch on one of the mentioned tables is in S/4HANA slower than in SAP ERP 6.0 due to the on-the-fly aggregation and the JOIN operation. Hence performance critical customer coding may be adjusted to improve performance. Furthermore customer coding writing data to aggregated actual stock quantity or to the former document header or item table shall be adjusted!</p>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CustomerAppendsontheformerdocumenttablesMKPFandMSEG\"></div>\n<div></div>\n<div><strong>1. Customer Appends</strong></div>\n<div>With SAP Note <a href=\"/notes/2194618\" target=\"_blank\">2194618</a> and <a href=\"/notes/2197392\" target=\"_blank\">2197392</a> SAP offers a check to be executed on the start release to identify APPEND issues described in the following sub chapters. Hence customer is not forced to scan all above listed tables manually.</div>\n<div></div>\n<p><strong><span>1.1 Customer Appends on the former document tables MKPF and MSEG</span></strong></p>\n<p><span><span>If there are APPENDs on MKPF and MSEG where fields with the same fieldname do exist then there is a name conflict in case that the content of field MKPF-A is different from field MSEG-A (fields with same name and identical content do exist on MKPF and MSEG also in SAP Standard for performance reasons e.g. BUDAT). In this case it is required to add a further field A_NEW to the append, copy the data from A to A_NEW with a special customer program and then all coding sections, Dynpros, etc. need to be adjusted to use A_NEW and then field A needs to be dropped from the append. This must be done before migration from ERP 6.0 to S/4HANA.</span></span></p>\n<p><span>If the attributes in the APPENDs or INCLUDEs (e.g. CI_M* INCLUDEs as introduced with consulting note 906314) on table MKPF and MSEG do have a master data or process controlling character then the fields from the APPENDs/INCLUDEs need to be appended to the table MATDOC and the assigned proxy views can be made compatible via extension views.</span></p>\n<p><span>In case of a system conversion all these customer fields in such APPENDs or INCLUDEs need to be appended to table MATDOC during the ACT_UPG phase (SPDD). It has to be done in any case before the MM-IM converison program will be executed which move the data from MKPF and MSEG to MATDOC otherwise data in customer fields gets lost. The structure compatibility between table MKPF/MSEG and their assigned proxy view shall be created directly after system conversion by creating extend views, see  note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a>.</span></p>\n<p><span>Fields from APPENDs/INCLUDEs to table MKPF should be appended to sub structure NSDM_S_HEADER of table MATDOC.</span></p>\n<p><span><span><span>Fields from APPENDs/INCLUDEs </span>to table MSEG should be appended to sub structure NSDM_S_ITEM of table MATDOC.</span></span></p>\n<p><strong><span><span>1.1.1 Customer include CI_COBL in table MSEG</span></span></strong></p>\n<p><span><span>Table MSEG contains the customer include CI_COBL where customers can insert own fields. The CI_COBL include has been made available also in the new MM-IM data model with note <a href=\"/notes/2240878\" target=\"_blank\">2240878</a>. This note must be applied before the data migration starts in the ACT_UPG phase (SPDD); otherwise you may loose data. With the implemented CI_COBL the table MSEG and it's assigned proxy view is not compatible in their structure anymore. The structural compatibility can be re-created by applying note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a>. This must be done directly after system conversion has been finished.</span></span></p>\n<p><span><span>If you later on like to make use of one of the fields in EXTEND VIEW for CI_COBL in the app \"Custom fields and logic\" then you need to follow the instructions given in this <a href=\"https://help.sap.com/viewer/9a281eac983f4f688d0deedc96b3c61c/1610%20003/en-US/4accfedc4d2e49c1b321b6ebf288a430.html\" target=\"_blank\">article in the SAP Help portal</a>. Basically you need to remove the field from the EXTEND VIEW for CI_COBL, save without activate and then add the field in the app \"Custom fields and logic\". This is valid for release 1709 and higher.</span></span></p>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CustomerAppendsonthehybridandpureaggregatetables\"></div>\n<p><strong><span>1.2 Customer Appends on the hybrid and replaced aggregation tables</span></strong></p>\n<div></div>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-Fieldscontainingmaterialmasterdataattributes\"><strong><span>1.2.1 Fields containing material master data attributes</span></strong></div>\n<div><span>If the append is not at the end of the hybrid table then the append should be moved to the end if possible and then no further action is required because the delivered DDL sources for the proxy views provide the $EXTENSION feature within S/4HANA on-premise 1511. Due to too many side effects like unpredictable sequence of fields from APPENDs, this has been changed with S/4HANA On-Premise 1610 where always an EXTEND VIEW for a CDS proxy view has to be created for an APPEND on a material master data table. For the DIMP tables the append has to be appended also to the new pure DIMP material master data tables.</span></div>\n<div><span>The structural compatibility between table and CDS proxy view can be re-created by applying note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a>. This must be done directly after system conversion has been finished (e.g. creating just an EXTEND VIEW with the customer fields using ABAP Development Tools for S/4HANA On Premise 1610 and higher).</span></div>\n<div><span>For replaced aggregation tables appends with master data attributes are not supported. If such appends are really required in the customer processes then the approach described in the next chapter maybe feasible. In the core functionality of material document processing there will be no write process on these tables. Thus update of the fields in the appends requires maybe some additional customer coding. </span></div>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-Fieldsrepresentingacustomerdefinedstocktypeorquantity/valuetobeaggregated\"></div>\n<p><strong><span>1.2.2 Fields representing a customer defined stock type or quantity/value to be aggregated</span></strong></p>\n<p><span>If own stock types or a dimension which needs to be aggregated have been introduced by the customer then the view stack of the CDS view assigned to the table with the additional stock type needs to be modified. Right now, there is no technology support for modification free enhancement. If the stock type has not been introduced by new entries or enhancements in the tables T156x (T156, T156SY, T156M, T156F) - which controls in the core functionality the mapping between a posting and a stock type - then the process logic needs to be adapted.</span></p>\n<p><strong><span>1.3 Customer Appends on views</span></strong></p>\n<p><span>There are several views in SAP Standard which also do have an assigned proxy view because the view provide actual stock quantity data. View and assigned proxy view must be compatible in structure too. If there are customer appends on such view the same rules as for tables apply. Views with assigned proxy compatibility view can be determined by searching via transaction SE16N in table DD02L with TABCLASS = VIEW and VIEWREF &lt;&gt; '' or you may use above mentioned check functionality in your start release.</span></p>\n<p><strong><span>1.3.1 Customer views on MKPF/MSEG</span></strong></p>\n<p><span>Views are database objects and thus a view is executed on the database. Because the table MKPF and MSEG will not contain data anymore (except legacy data from migration) such a customer view will never return any record. Such views have to be either adjusted by fetching data from table MATDOC or to be created new as DDL source with a different name. In the last case all usages of the old DDIC SQL view must be replaced by the new CDS view.</span></p>\n<p><strong><span>1.3.2 Customer views on material master attributes</span></strong></p>\n<p><span>Such views using only material master data attributes from the hybrid tables do not need to be changed.</span></p>\n<p><strong><span>1.3.3 Customer views using aggregated stock quantity data</span></strong></p>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CodeOptimization(optional/recommended)\">Customer views having at least one actual stock quantity aggregate cannot be used anymore because</div>\n<ul>\n<li>\n<div>the field representing this aggregate on the database will be empty forever</div>\n</li>\n<li>\n<div>the quantity must be aggregated from table MATDOC which is not possible with DDIC SQL views.</div>\n</li>\n</ul>\n<div>Such views must be defined new as DDL source with a new name. Each of the above mentioned DDL sources can be used as template. All usages of the old DDIC SQL view must be replaced by the new CDS view.</div>\n<div></div>\n<div></div>\n<p><strong><span>2 Code adjustments and optimizations</span></strong></p>\n<p>Technically it is still possible to do DB write operations (INSERT, UPDATE, DELETE, MODIFY) on the tables MKPF, MSEG as well as the fields representing actual stock quantities in the hybrid and replaced aggregation tables. But such write operations are without any effect! Therefore write operations on MKPF, MSEG as well as the fields representing actual stock quantities in the hybrid and replaced aggregation tables shall be removed from customer coding. Write operations on the material master data attributes in the hybrid tables are still possible. Write operations on table MATDOC and your moved customer append fields are done by class CL_NSDM_STOCK.</p>\n<p>DB read operations on the hybrid and replaced aggregation tables have a performance decrease. In general, it shall be avoided to read any stock quantities when only master data is required. Therefore it is recommended to adjust the customer coding in the following way:</p>\n<ul>\n<li>If material master data as well as actual stock quantity data are required then the SELECT....&lt;table&gt; should be replaced by using a data access method from class CL_NSDM_SELECT_&lt;table&gt;. These classes provide access methods for single as well as array read operations. Those access methods shall not be used if the KEY table contains more than 1000 records to to limitations of the SELECT.....FOR ALL ENTRIES.</li>\n<li>If material master data are required then the SELECT....&lt;table&gt; should be replaced by SELECT....V_&lt;table&gt;_MD where V_&lt;table&gt;_MD is one of the above mentioned views for master data access. Alternatively corresponding material master data read methods in the class CL_NSDM_SELECT_&lt;table&gt; can be used (those access methods shall not be used if the KEY table contains more than 1000 records to to limitations of the SELECT.....FOR ALL ENTRIES.). Also the data type declarations should be adjusted from TYPE...&lt;table&gt; to TYPE...V_&lt;table&gt;_MD.</li>\n<li>If actual stock quantity data are required then the SELECT....&lt;table&gt; should be replaced by SELECT....NSDM_V_&lt;table&gt;_DIFF where NSDM_V_&lt;table&gt;_DIFF is one of the views in the view stack of the above mentioned proxy view. Also the data type declarations should be adjusted from TYPE...&lt;table&gt; to TYPE...NSDM_V_&lt;table&gt;_DIFF.</li>\n<li>For table MARC and field STAWN valid from S/4HANA On Premise 1610 please read note <a href=\"/notes/2378796\" target=\"_blank\">#mce_temp_url#</a></li>\n</ul>\n<p>For performance critical coding parts these adjustments are strongly recommended. For non critical parts it is optional short term but recommended on long term.</p>\n<p><span 10pt;\"=\"\" arial',sans-serif;=\"\" font-size:=\"\" lang=\"EN-US\">To identify such locations, it is required to make use of the where-used functionality of transaction SE11 and considering other techniques like transaction CODE_SCANNER to find locations which SE11 cannot handle – like dynamic programming or native SQL statements.</span></p>\n<p><span 10pt;\"=\"\" arial',sans-serif;=\"\" font-size:=\"\" lang=\"EN-US\">Consider SAP Note <a href=\"/notes/28022\" target=\"_blank\">28022</a> if there are issues with the where-used functionality in the customer system. In the where-used dialog it is possible via the button \"Search Range\" to search specific for key words like SELECT, INSERT and so on.</span></p>", "noteVersion": 20, "refer_note": [{"note": "2242679", "noteTitle": "2242679 - Redirect inconsistency - Proxy Substitution", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>After migration to S/4HANA OP1511 you get the runtime error DBSQL_REDIRECT_INCONSISTENCY  'A table could not be redirected' while accessing table MARC, MBEW, EBEW, OBEW, QBEW, MKPF or MSEG in ABAP.</p>\n<p>This error message may also be thrown for the tables MARD, MCHB, MSSQ, MSSL, MSSA, MSPR, MSLB, MSKU, MSKA or MKOL after migration to S/4HANA OP1610 or higher.</p>\n<p>The error analysis describes that a setting in the dictionary stipulates that proxy data object \"&lt;AbapCatalogViewName&gt;\" is used to execute table \"&lt;table&gt;\" at runtime. The definition of \"&lt;table&gt;\" and \"&lt;AbapCatalogViewName&gt;\" are not consistent however, meaning that the operation had to be terminated.</p>\n<p>If you check the table in the dictionary via SE11 you get an error message like:</p>\n<ul>\n<li>DT 342 MSEG and proxy object NSDM_V_MSEG have different numbers of columns</li>\n<li>DT 338 Column ZZFIELD exists in MSEG but not in NSDM_V_MSEG or has a different position there</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MKPF, MSEG, MARC, MBEW, EBEW, OBEW, QBEW, MARD, MCHB, MSSQ, MSSL, MSSA, MSPR, MSLB, MSKU, MSKA, MKOL, CDS proxy view; Append, $EXTENSION, EXTEND VIEW, DT342, DT338</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Background information:</strong></p>\n<p>The MM-IM data model in S/4HANA has been changed compared to ERP/SoH. Actual stock data will not be stored anymore in the former key figure material master data tables and the material document will not be stored anymore in the tables MKPF (header data) and MSEG (item data). Instead the single table MATDOC has been introduced which stores the material document and from which the actual stock data will be calculated on-the-fly because each material document is a change of stock. More details are described in note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a>.</p>\n<p>To ensure compatibility for customer coding there are proxy objects assigned to the former document tables and the material master key figure tables (use SE11 -&gt; Extras -&gt; Proxy object). These proxy objects are CDS views which read the data from table MATDOC instead from the \"old\" tables. The assignment requires that the proxy view is identical to the table regarding fields and the sequence of fields.</p>\n<p>Issues may occur if the tables have been enhanced by APPENDs because in this case the structure compatibility between table and assigned proxy obejct is broken.</p>\n<p> </p>\n<p><strong>For S/4HANA OP1511 all support and feature packages</strong>:</p>\n<p>For most of the tables the structural compatibility is still ensured because the delivered proxy CDS view uses the $EXTENSION keyword at the end which adds fields from APPENDs at the end of a table also at the end of the proxy CDS view. This is the case for the proxy CDS views for the tables MARD, MCHB, MSSQ, MSSL, MSSA, MSPR, MSLB, MSKU, MSKA or MKOL.</p>\n<p>Affected are customer appends on tables MARC, MBEW, EBEW, OBEW, QBEW, MKPF or MSEG or implementation of include structure CI_COBL in MSEG because for those tables the $EXTENSION mechanism does not work in any case due to:</p>\n<ul>\n<li>\n<div>MARC and xBEW are build of a structure which itself is build by structures and fields. If the APPEND <strong>is done in one of those sub-structures</strong> then the consequence is that the fields from the APPEND is somewhere in between in the table and hence appending fields at the end of the assigned proxy view will lead to a structure mismatch.</div>\n</li>\n<li>\n<div>MSEG contains the custumer include CI_COBL somewhere in the middle of the table definition. Hence by implementing CI_COBL the fields are also somehwere in the middle of the table and hence appending fields at the end of the assigned proxy view will lead to a structure mismatch.</div>\n</li>\n</ul>\n<p> </p>\n<p><strong>For S/4HANA OP1610 and higher</strong>:</p>\n<p>For S/4HANA OP1610 the $EXTENSION keyword has been removed from the proxy CDS views of tables MARD, MCHB, MSSQ, MSSL, MSSA, MSPR, MSLB, MSKU, MSKA or MKOL. This has been done due to too many side effects like unpredictable sequence of fields from APPENDs.</p>\n<p>Hence, additionally to the OP1511 case, customer appends on tables MARD, MCHB, MSSQ, MSSL, MSSA, MSPR, MSLB, MSKU, MSKA or MKOL are affected.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p> </p>\n<p>Follow the instructions given in the next blocks and import the program NSDM_PROXY_SUBSTITUTION via SNOTE if this program is not available in your system (delivered with S/4HANA OP1511 SP01).</p>\n<p><strong>For S/4HANA OP1511:</strong></p>\n<p><strong>To make it pretty clear: the below mentioned steps are not required if you have an APPEND structure directly at the end of the tables MARC or xBEW in the table.</strong> This case is automatically handled by the $EXTENSION keyword in the CDS proxy views delivered by SAP. If SE11 reports inconsistency between table and proxy object then please do a re-activation of the proxy view (use ABAP Development Tools for this) and then a re-activation of the table.</p>\n<p><strong>The steps 1 and 2.1 as well as 2.2 below must be executed in cases where:</strong></p>\n<ul>\n<li>\n<div><strong>the APPEND is in some of the structures building MARC or xBEW.</strong></div>\n</li>\n<li>\n<div><strong>or there is an APPEND in table MKPF or MSEG.</strong></div>\n</li>\n<li>\n<div><strong>or for table MSEG with implemented CI_COBL include.</strong></div>\n</li>\n</ul>\n<p> </p>\n<p><strong>For S/4HANA OP1610 and higher:</strong></p>\n<p><strong>Only the step 1 below must be executed in cases where:</strong></p>\n<ul>\n<li>\n<div><strong>the APPEND is in some of the structures building MARC or xBEW.</strong></div>\n</li>\n<li>\n<div><strong>or there is an APPEND structure in the tables MKPF, MSEG, MARC (at the end of table MARC itself), MARD, MCHB, MSSQ, MSSL, MSSA, MSPR, MSLB, MSKU, MSKA or MKOL.</strong></div>\n</li>\n<li>\n<div><strong>or for table MSEG with implemented CI_COBL include.</strong></div>\n</li>\n</ul>\n<p> </p>\n<p><strong>1. Create an EXTEND VIEW</strong></p>\n<p>For APPENDs on structures of tables for the above described cases use ABAP Development Tools (ADT) Core Data Services (CDS) and create a new DDL source (ABAP Core Data Services - Data Definition) with name <em>&lt;Name of your append&gt;_DDL</em> (how to install ABAP in Eclipse is described in note <a href=\"/notes/1718399\" target=\"_blank\" title=\"1718399  - RTC of ABAP Development Tools for SAP NetWeaver\">1718399</a>)<em>. </em>Enter a description like 'Extension view for Append &lt;Name of your append&gt;'.<em> S</em>elect the template 'Extend View'  and adopt the proposed coding <em>EXTEND VIEW &lt;proxy view name for table&gt; with &lt;Name of your append&gt;_E .<strong> </strong></em></p>\n<ul>\n<li>The <em>&lt;proxy view name for MARC&gt;</em> is NSDM_E_MARC.</li>\n<li>The <em>&lt;proxy view name for xBEW&gt;</em> is MBV_xBEW.</li>\n<li>The <em>&lt;proxy view name for MSEG&gt;</em> is NSDM_E_MSEG.</li>\n<li>The <em>&lt;proxy view name for MKPF&gt;</em> is NSDM_E_MKPF.</li>\n<li>The <em>&lt;proxy view name for Mxyz&gt;</em> is NSDM_E_Mxyz.</li>\n</ul>\n<p>Enter all the fields from the APPEND in this extend proxy view. If you have several APPENDs in one table (may happen if a table is composed by sub-structures or for MSEG an implemented CI_COBL include as well as an APPEND on table MSEG), then all fields from all your APPENDs shall be inserted in the customer CDS proxy view.</p>\n<p>Example for MARC:</p>\n<div class=\"table-responsive\"><table cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><em>@AbapCatalog.sqlViewAppendName:'<strong>&lt;Name of your append&gt;_V'</strong></em></td>\n</tr>\n<tr>\n<td><em>@EndUserText.label:'Extension view for Append &lt;Name of your append&gt;'</em></td>\n</tr>\n<tr>\n<td><em>@AccessControl.authorizationCheck:#NOT_REQUIRED</em></td>\n</tr>\n<tr>\n<td><em><strong>EXTEND VIEW </strong>NSDM_E_MARC <strong>with</strong> <strong>&lt;Name of your append&gt;_E </strong></em></td>\n</tr>\n<tr>\n<td><em>{</em></td>\n</tr>\n<tr>\n<td><em><strong>&lt;your field list&gt;</strong></em></td>\n</tr>\n<tr>\n<td><em>}</em></td>\n</tr>\n</tbody>\n</table></div>\n<p>If you have implemented the CI_COBL customer include structure of table MSEG than do the same as above. Additionally you need note <a href=\"/notes/2240878\" target=\"_blank\">2240878</a> to migrate the CI_COBL data of MSEG to MATDOC. If you later on like to make use of one of the fields in EXTEND VIEW for CI_COBL in the app \"Custom fields and logic\" then you need to follow the instructions given in this <a href=\"https://help.sap.com/viewer/9a281eac983f4f688d0deedc96b3c61c/1610%20003/en-US/4accfedc4d2e49c1b321b6ebf288a430.html\" target=\"_blank\">article in the SAP Help</a> portal. Basically you need to remove the field from the EXTEND VIEW for CI_COBL, save without activate and then add the field in the app \"Custom fields and logic\" (valid for release 1709 and higher).</p>\n<p> </p>\n<p><strong>2. Correct sequence of fields for S/4HANA OP1511</strong></p>\n<p><strong>The below mentioned steps shall be executed only in the case described above where</strong></p>\n<ul>\n<li><strong>an APPEND exist in the sub-structures building the tables MARC, xBEW</strong></li>\n<li><strong>or if there is an APPEND on table MKPF or MSEG</strong></li>\n<li><strong>or in case of the CI_COBL implementation for table MSEG.</strong></li>\n</ul>\n<p><strong>These steps shall not be executed in case of an APPEND directly in tables MARC, MARD, MCHB, MSSQ, MSSL, MSSA, MSPR, MSLB, MSKU, MSKA or MKOL.</strong></p>\n<p><strong>2.1. Implement the program NSDM_PROXY_SUBSTITUTION</strong></p>\n<p>Implement the new program NSDM_PROXY_SUBSTITUTION delivered with this note as below if it is not available in your system (S/4HANA OP1511 SP00).</p>\n<p><strong>2.2. Execute the program NSDM_PROXY_SUBSTITUTION</strong></p>\n<p>Run the report to perform the additional manual adjustments.</p>\n<p>Select the table to adjust, enter a <em>&lt;name&gt;</em> with customer namespace for the new CDS view and an explanatory text which will become part of the short description '<em>&lt;table&gt;: &lt;explanatory text&gt;</em>'.</p>\n<p>This adjustment program creates a new CDS proxy view <em>&lt;name&gt;_E</em> with AbapCatalog name <em>&lt;name&gt;_V </em> and DDL <em>&lt;name&gt;_DDL</em>, which SELECTs data from the underlying delivered CDS proxy view but where the fields are sequenced according to their order in the table to ensure compatibility between table and proxy object. This new CDS proxy view will then be assigned to the table thereafter. The new CDS proxy view will be inserted in a workbench transport request if necessary.</p>\n<p> </p>\n<p> </p>", "noteVersion": 16, "refer_note": [{"note": "2323957", "noteTitle": "2323957 - Proxy Substitution during update of S/4HANA 1511 SP00/SP01 to SP02 or higher", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are migrating from S/4HANA OP 1511 SP00 or SP01 to SP02 or higher. During update - usually in the ACT_UPG phase - you get error messages that a customer generated proxy CDS view can not be activated because some /BEV/* fields do not exist in the source table.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MSEG; Upgrade; ACT_UPG; NSDM_PROXY_SUBSTITUTION; SUM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p></p>\n<p>S/4HANA 1511 has been delivered with the technical objects of the former MM-IM-ED functionality. Part of this are 4 fields /BEV2/* appended to table MSEG. With S/4HANA 1511 SP02 MM-IM-ED has been deprecated, hence those /BEV2/* fields are not available anymore in table MSEG. See simplification note 2224144.</p>\n<p>Customers already having SP00 or SP01 running and having an own proxy view object assigned to MSEG run into trouble during upgrade from SP00 or SP01 to SP02 or higher because the SE11 definition of MSEG does not have the /BEV2/* fields but the runtime object has them and due to the assigned proxy object there is a match between the proxy object and the runtime object but a mismatch to the SE11 definition of table MSEG.</p>\n<p>Thus a re-generation of the customer proxy does not work and an activation of table MSEG does also not work.</p>\n<p>With this correction the coupling between the table and the assigned customer CDS proxy view will be canceled and the customer CDS proxy will be deleted.</p>\n<p>After successful upgrade you have to call the program NSDM_PROXY_SUBSTITUTION again to re-create and re-assign the customer CDS proxy view to the table.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply this correction in your system via SNOTE. If you face this issue during update then please apply this correction also on the shadow system. In this case you maybe need to apply this correction manually. Therefore it is recommended to already apply the correction before the update!</p>\n<p>Note: this note has two notes from NetWeaver as prerequsite: 2264298 and 2328360. They need to be applied first.</p>\n<p>Note: this note contains three corrections which have to be applied as well as a manual activity to create message texts.</p>\n<p> </p>\n<p><strong>After you have applied the correction please proceed as follows:</strong></p>\n<ol>\n<li>During the update phase ACT_UPG execute program NSDM_PROXY_SUBSTITUTION in the <strong>shadow system</strong>, mark MSEG, enter the name of your CDS proxy view, mark \"Overwrite existing DDL\" and execute the program. This will un-assign and delete the customer CDS proxy view.</li>\n<li>Ignore possible further complains about inactive or inconsistent customer CDS proxy view in the shadow system when the update continues.</li>\n<li>After update execute program NSDM_PROXY_SUBSTITUTION again, mark MSEG, enter the name of your CDS proxy view, mark \"Overwrite existing DDL\" and execute the program. This will re-create and assign the customer CDS proxy view.</li>\n</ol>", "noteVersion": 8}, {"note": "2217299", "noteTitle": "2217299 - Inventory Valuation (part of Materials Management - Inventory Management) : Change of data model in S/4HANA 1511", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP S/4HANA 1511 Inventory Management and need additional information how to adjust your customer enhancements, modifications or own functionalities to the new, simplified <strong>Inventory Valuation</strong> data model.</p>\n<p><strong>If you want to install SAP S/4HANA 1610, please ignore this SAP note and refer to the</strong> <strong>SAP note 2337368</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition, S4HANA 1511, Migration, S4 HANA, MM-IM, Material Management, Inventory Valuation, Material Valuation, MBEW, compatibility view</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have customer enhancements, modifications or own functionalities in the area of inventory valuation (component MM-IM-GF-VAL) which were built for SAP ERP 6.0.</p>\n<p>Before S/4HANA, the inventory valuation tables xBEW(H) (tables: EBEW, EBEWH, MBEW, MBEWH, OBEW, OBEWH, QBEW, QBEWH) contain transactional as well as master data attributes.</p>\n<p>With S/4HANA, the inventory valuation tables do still exist as DDIC definition as well as database object. However, they will only be used to store material master data attributes. The transactional fields LBKUM SALK3 and SALKV will be retrieved from the Material Ledger. Hence, those fields are not updated anymore in the original xBEW(H) tables. As a consequence, the above mentioned tables need to be updated less often, which leads to a higher throughput due to less database locks.</p>\n<p>For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables ensuring that each read access to one of the mentioned tables still returns the data as before. The CDS-views consist of database joins in order to retrieve both master data from the original xBEW(H) table and transactional data from Material Ledger tables.</p>\n<p>Hence all customer coding, reading data from those tables, will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted, if transactional fields are affected.</p>\n<p>The following table gives an overview of the new relevant database objects in the S/4HANA Inventory Valuation data model:</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"5\" cellspacing=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table             </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Table description   </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL source of CDS view for redirect (proxy view)    </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong><strong>Redirected view in ABAP</strong></strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read the content of the original database table (w/o redirect to proxy view)    </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read master data attributes only    </strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEWH_MD</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 1: Affected tables with information about corresponding proxy objects, views for accessing original database table and views for reading master data only</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>One impact of the simplified MM-IM Inventory Valuation data model does exist if there are customer appends on the mentioned tables. The NetWeaver redirect capability requires that database table and assigned proxy view is compatible in the structure: number of fields, their sequence and their type. Thus, if there is an append on one of the above mentioned tables then the assigned DDL source of the CDS proxy view must be made compatible.</p>\n<p>Another impact of the simplified inventory valuation data model is a performance decrease of database read operations on the above mentioned tables just because a data fetch on one of the mentioned tables is in S/4HANA slower than in SAP ERP 6.0 due to JOIN operations. Hence performance critical customer coding may be adjusted to improve performance.</p>\n<p> </p>\n<p><strong>1. Customer appends on xBEW(H) tables</strong></p>\n<p>With SAP Note <a class=\"external-link\" href=\"/notes/2194618\" target=\"_blank\">2194618</a> SAP offers a pre-check to be executed on the start release to identify append issues described in the following sub chapters. Hence customer is not forced to scan all above listed tables manually.</p>\n<p>Please note that this check contains not only the xBEW(H)-tables. Instead, it also analyzes other MM-IM tables.</p>\n<p> </p>\n<p>If the pre-check identifies appends on an xBEW(H) tables, the corresponding CDS views, listed in the following table, needs to be enhanced or replaced.</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"5\" cellspacing=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table with customer appends</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL sources of CDS-views which needs to be enhanced or replaced in case of customer appends</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_EBEW_EXT</p>\n<p>-       MBV_EBEW_ML_ONLY</p>\n<p>-       MBV_EBEW_BASIS</p>\n<p>-       MBV_EBEW_CASE</p>\n<p>-      <strong> MBV_EBEW</strong> (Proxy view --&gt; same structure and order as EBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_EBEWH_EXT</p>\n<p>-       MBV_EBEWH_ASSOC</p>\n<p>-       MBV_EBEWH_ML_ONLY</p>\n<p>-       MBV_EBEWH_BASIS</p>\n<p>-       MBV_EBEWH_CASE</p>\n<p>-       <strong>MBV_EBEWH</strong> (Proxy view --&gt; same structure and order as EBEWH table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_MBEW_EXT</p>\n<p>-       MBV_MBEW_ML_ONLY</p>\n<p>-       MBV_MBEW_BASIS</p>\n<p>-       MBV_MBEW_CASE</p>\n<p>-       MBV_MBEW_MOTH_SEG</p>\n<p>-       <strong>MBV_MBEW</strong> (Proxy view --&gt; same structure and order as MBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_MBEWH_EXT</p>\n<p>-       MBV_MBEWH_ASSOC</p>\n<p>-       MBV_MBEWH_ML_ONLY</p>\n<p>-       MBV_MBEWH_BASIS</p>\n<p>-       MBV_MBEWH_CASE</p>\n<p>-       MBV_MBEWH_MOTH_SEG</p>\n<p>-       <strong>MBV_MBEWH</strong> (Proxy view --&gt; same structure and order as MBEWH table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_OBEW_EXT</p>\n<p>-       MBV_OBEW_ML_ONLY</p>\n<p>-       MBV_OBEW_BASIS</p>\n<p>-       MBV_OBEW_CASE</p>\n<p>-      <strong> MBV_OBEW</strong> (Proxy view --&gt; same structure and order as OBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_OBEWH_EXT</p>\n<p>-       MBV_OBEWH_ASSOC</p>\n<p>-       MBV_OBEWH_ML_ONLY</p>\n<p>-       MBV_OBEWH_BASIS</p>\n<p>-       MBV_OBEWH_CASE</p>\n<p>-       <strong>MBV_OBEWH</strong> (Proxy view --&gt; same structure and order as OBEWH table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_QBEW_EXT</p>\n<p>-       MBV_QBEW_ML_ONLY</p>\n<p>-       MBV_QBEW_BASIS</p>\n<p>-       MBV_QBEW_CASE</p>\n<p>-       <strong>MBV_QBEW</strong> (Proxy view --&gt; same structure and order as QBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_QBEWH_EXT</p>\n<p>-       MBV_QBEWH_ASSOC</p>\n<p>-       MBV_QBEWH_ML_ONLY</p>\n<p>-       MBV_QBEWH_BASIS</p>\n<p>-       MBV_QBEWH_CASE</p>\n<p>-       <strong>MBV_QBEWH</strong> (Proxy view --&gt; same structure and order as QBEWH table necessary)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 2: Affected CDS views which needs to be enhanced or replaced in case of customer appends</em><em></em><em> </em></p>\n<p> </p>\n<p><strong>1.1 Fields containing material master data attributes</strong></p>\n<p>If customer appends only contain material master data attributes, the views could be made compatible either by extension views and view replacements.</p>\n<ol>\n<li>\n<p>Extension view (recommended, if feasible)<br/>The structure of the proxy view and the xBEW(H) table can be made compatible by extension views. Extension views take care that fields, defined in the extension view, are appended at the end of the extended view. Consequently, it is necessary to move existing customer appends between SAP fields, for instance part of different includes, to the end of the corresponding xBEW(H) table. If this is not feasible, the views have to be replaced as described in option 2.<br/>An extension view needs to be defined for each CDS-view, listed in table 2, for the corresponding xBEW(H) table.<br/>The fields in the extension view of the proxy CDS view (bold in table 2), have to be listed in exactly the same order as in the customer append. For more information about view extension see  <a href=\"http://help.sap.com/abapdocu_740/en/index.htm?file=abencds_f1_extend_view.htm\" rel=\"nofollow\" target=\"_blank\">SAP NetWeaver 7.4 documentation</a></p>\n</li>\n<li>\n<p>Replacement of CDS proxy view (bold in table 2) by a new CDS proxy view in customer namespace (Please refer to SAP Note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a> )</p>\n<p>If option 1 is not feasible, for instance due to unmovable customer appends between SAP fields, a new CDS proxy view needs to be created in customer namespace. This view must contain the same fields and types in the same order as the corresponding xBEW(H) table. The newly created CDS view must be specified as a new proxy view via transaction SE11 --&gt; Menu --&gt; Extras --&gt; Proxy Object.</p>\n<p>For the other CDS views in the view-hierarchy the exact order of the fields does not matter. Hence option 1 is applicable. Consequently, in some cases it could be an option to combine the first and second approach, for instance extension view for sub-CDS views in the view-hierarchy and view replacement for the CDS proxy view.</p>\n</li>\n</ol>\n<p> </p>\n<p><strong>1.2 Fields representing a customer defined quantity/value to be aggregated</strong></p>\n<p>If customer appends contain aggregated fields, the proxy view (same approach as described for material master data attributes) and most probably also the views in the view-hierarchy needs to be replaced by new CDS views in customer namespace .</p>\n<p>In most cases extension views cannot be used, since currently they do not support aggregate expressions.</p>\n<p> </p>\n<p><strong>1.3 Customer appends on views</strong></p>\n<p>There are several views in SAP Standard which also do have an assigned proxy view because the view reads transactional data from an xBEW(H) table. Since transactional data are not updated anymore in the xBEW(H) table, view accesses are redirected to corresponding proxy views.</p>\n<p>View and assigned proxy view must be compatible in structure too. If there are customer appends on such view the same rules as for tables apply. Views with assigned proxy compatibility view are identified by the provided pre-check (SAP note <a class=\"external-link\" href=\"/notes/2194618\" target=\"_blank\">2194618</a>).</p>\n<p> </p>\n<p><strong>1.3.1 Customer views using material master attributes</strong></p>\n<p>Such views using only material master data attributes from the xBEW(H) tables do not need to be changed.</p>\n<p> </p>\n<p><strong>1.3.2 Customer views using transactional attributes</strong></p>\n<p>Customer views, accessing xBEW(H) tables and having at least one transactional attribute (LBKUM, SALK3, SALKV or own customer transactional fields)  need to be re-build by creating a new CDS view with a new name.</p>\n<p>Instead of accessing the original xBEW(H) table, the corresponding DDL source, listed in table 1, needs to be accessed.</p>\n<p>There are two options how to handle accesses to the old DDIC SQL view:</p>\n<ol>\n<li>Redirect accesses to new CDS proxy view via transaction SE11 (Extras --&gt; Proxy Object)</li>\n<li>Code adaption of accesses to old SQL DDIC SQL view</li>\n</ol>\n<p> </p>\n<p><strong>2 Code adjustments and optimizations</strong></p>\n<p>Technically it is still possible to do database write operations (INSERT, UPDATE, DELETE, MODIFY) on transactional fields. But such write operations are without any effect, since transactional data is read from the Material Ledger!</p>\n<p>Therefore, write operations on xBEW(H) tables, affecting transactional fields, shall be removed from customer coding.</p>\n<p>Write operations on material master data attributes shall still happen on the xBEW(H) tables.</p>\n<p> </p>\n<p>DB read operations on the xBEW(H) tables have a performance decrease, since transactional data is retrieved from the Material Ledger via database joins. Consequently, it shall be avoided to read transactional data when only master data is required. Therefore it is recommended to adjust the customer coding in the following way:</p>\n<ul>\n<li>If material master data as well transactional data is required then the SELECT &lt;table&gt; should be replaced by using a data access method from class CL_MBV_SELECT_&lt;table&gt;. These classes provide access methods for single (SINGLE_READ) as well as array (ARRAY_READ) read operations.</li>\n<li>If only material master data is required then the SELECT &lt;table&gt; should be replaced by SELECT on view V_&lt;table&gt;_MD. Alternatively, corresponding material master data read methods (READ_MASTER_DATA) in the class CL_MBV_SELECT_&lt;table&gt; can be used. Also the data type declarations should be adjusted from TYPE &lt;table&gt; to TYPE V_&lt;table&gt;_MD.</li>\n<li>If only transactional fields are required then the SELECT &lt;table&gt; could be replaced by using the data access methods ARRAY_READ_TRANSACTIONAL_DATA and SINGLE_READ_TRANSACTIONAL_DATA from class CL_MBV_SELECT_&lt;table&gt;. Currently, the methods for retrieving transactional fields do not have a better performance than the methods ARRAY_READ and SINGLE_READ in class CL_MBV_SELECT_&lt;table&gt;. However, this might change in the following releases.</li>\n</ul>\n<p> </p>\n<p>For performance critical coding parts these adjustments are strongly recommended. For non critical parts it is optional short term but recommended on long term.</p>\n<p>To identify such locations, it is required to make use of the where-used functionality of transaction SE11 and considering other techniques like transaction CODE_SCANNER to find locations which SE11 cannot handle – like dynamic programming or native SQL statements.</p>\n<p>Consider SAP Note <a class=\"external-link\" href=\"/notes/28022\" target=\"_blank\">28022</a>, if there are issues with the where-used functionality in the customer system. In the where-used dialog it is possible via the button \"Search Range\" to search specific for key words like SELECT, INSERT and so on.</p>", "noteVersion": 6}, {"note": "2335305", "noteTitle": "2335305 - Proxy Substitution for table MKPF", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Dump during access to table MKPF with reason DBSQL_REDIRECT_INCONSISTENCY or you get an error message during upgrade that table MKPF and proxy object \"NSDM_V_MKPF\" have different numbers of columns.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MKPF, NSDM_V_MKPF, NSDM_E_MKPF, DBSQL_REDIRECT_INCONSISTENCY</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have an APPEND on table MKPF or an Include structure in an APPEND on table MKPF and this table has an CDS proxy view assigned where the structure of MKPF a the proxy object do not fit according to your enhancement of the table.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the assigned correction.</p>\n<p>Create an EXTEND VIEW for NSDM_E_MKPF as already descibed in note 2242679 for e.g. table MSEG.</p>\n<p>Execute the program NSDM_PROXY_SUBSTITUTION for table MKPF.</p>", "noteVersion": 2}, {"note": "2206980", "noteTitle": "2206980 - Material Inventory Managment: change of data model in S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP S/4HANA and need additional information how to adjust your customer enhancements, modifications or own functionalities to the new, simplified data model of SAP S/4HANA Supply Chain (MM - Inventory Management).</p>\n<p>You want to have information about what is different in SAP S/4HANA Supply Chain (MM - Inventory Management) compared to Suite on HANA MM-IM.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span 'times=\"\" 11pt;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" font-size:=\"\" lang=\"EN-US\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-ascii-theme-font:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" mso-hansi-theme-font:=\"\" new=\"\" roman';=\"\">S4TC, S/4 transition, MM-IM, Material Management</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have customer enhancements, modifications or own functionalities in the area of inventory management (component MM-IM) which were built for SAP ERP 6.0.</p>\n<p>You are using functionalities which behave different in SAP S/4HANA Supply Chain (MM - Inventory Management) compared to Suite on HANA.</p>\n<p> </p>\n<p>The SAP ERP 6.0 stock inventory management data model consists of the two document tables MKPF for document header information and MSEG for document item data. Additionally there were aggregated actual stock quantity data stored in several tables. Some of these tables do also store material master data attributes like the tables MARC, MARD and MCHB. Such tables with material master data attributes as well as actual stock quantities will be named as hybrid tables in the following. In contrast there are also tables like MSSA containing only aggregated actual stock quantities for sales order stock. Such tables will be called in the following as replaced aggregation tables.</p>\n<p>With S/4HANA this data model has been changed significantly. The new de-normalized table MATDOC has been introduced which contains the former header and item data of a material document as well as a lot of further attributes. Material document data will be stored in MATDOC only and not anymore in MKPF and MSEG. Additionally the aggregated actual stock quantities will not be persisted anymore in the hybrid or replaced aggregation tables. Instead, actual stock quantity data will be calculated on-the-fly from the new material document table MATDOC for which some of those additional special fields are used. Hence, with the new MM-IM data model the system will work on database level in an INSERT only mode without DB locks. Nevertheless, for stock decreasing processes there will be still ABAP locks to ensure stock consistency. A further advantage of the new MM-IM data model is the capability of simple and fast reporting because the most information is all in one place: MATDOC.</p>\n<p>All below mentioned tables of the SAP ERP 6.0 product do still exist in S/4HANA as DDIC definition as well as database object and the hybrid tables will still be used to store the material master data attributes. For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables ensuring that each read access to one of the mentioned tables below still returns the data as before in SAP ERP 6.0. The CDS Views do the on-the-fly aggregation of actual stock quantities from the new MM-IM data model and join the master data attributes from the material master data table. Hence all customer coding reading data from those tables will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted.</p>\n<p>The affected tables are:</p>\n<div class=\"table-responsive\"><table class=\"confluenceTable\">\n<tbody>\n<tr><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\">Table</div>\n</th><th class=\"confluenceTh\">Table description</th><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\">DDL Source of CDS View for redirect</div>\n</th><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\"><strong>View to read the content of the database table (w/o redirect to compatibility view)</strong></div>\n</th><th class=\"confluenceTh\">\n<div class=\"tablesorter-header-inner\">View to read the master data attributes only</div>\n</th></tr>\n<tr>\n<td class=\"confluenceTd\">MKPF</td>\n<td class=\"confluenceTd\">Material document header</td>\n<td class=\"confluenceTd\">NSDM_DDL_MKPF</td>\n<td class=\"confluenceTd\">NSDM_MIG_MKPF</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSEG</td>\n<td class=\"confluenceTd\">Material document item</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSEG</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSEG</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">\n<p>MARC</p>\n</td>\n<td class=\"confluenceTd\">Plant Data for Material</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARC</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARC</td>\n<td class=\"confluenceTd\">V_MARC_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MARD</td>\n<td class=\"confluenceTd\">Storage Location Data for Material</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARD</td>\n<td class=\"confluenceTd\">V_MARD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCHB</td>\n<td class=\"confluenceTd\">Batch stocks</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCHB</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCHB</td>\n<td class=\"confluenceTd\">V_MCHB_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MKOL</td>\n<td class=\"confluenceTd\">Special Stocks from Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MKOL</td>\n<td class=\"confluenceTd\">NSDM_MIG_MKOL</td>\n<td class=\"confluenceTd\">V_MKOL_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSLB</td>\n<td class=\"confluenceTd\">Special Stocks with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSLB</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSLB</td>\n<td class=\"confluenceTd\">V_MSLB_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKA</td>\n<td class=\"confluenceTd\">Sales Order Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKA</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKA</td>\n<td class=\"confluenceTd\">V_MSKA_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSA</td>\n<td class=\"confluenceTd\">Total Customer Orders on Hand</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSA</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSA</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSPR</td>\n<td class=\"confluenceTd\">Project Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSPR</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSPR</td>\n<td class=\"confluenceTd\">V_MSPR_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSL</td>\n<td class=\"confluenceTd\">Total Special Stocks with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSL </td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSL </td>\n<td class=\"confluenceTd\"></td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSQ</td>\n<td class=\"confluenceTd\">Project Stock Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSQ</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSQ</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKU</td>\n<td class=\"confluenceTd\">Special Stocks with Customer</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKU</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKU</td>\n<td class=\"confluenceTd\">V_MSKU_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTB</td>\n<td class=\"confluenceTd\">Stock in Transit</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTB</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTB</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTE</td>\n<td class=\"confluenceTd\">Stock in Transit to Sales and Distribution Document</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTE</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTE</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTQ</td>\n<td class=\"confluenceTd\">Stock in Transit for Project</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTQ</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTQ</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSD</td>\n<td class=\"confluenceTd\">DIMP: Customer Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSD</td>\n<td class=\"confluenceTd\">MCSD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSS</td>\n<td class=\"confluenceTd\">DIMP: Total Customer Stock</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSS</td>\n<td class=\"confluenceTd\">MCSS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSCD</td>\n<td class=\"confluenceTd\">DIMP: Customer stock with vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSCD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSCD</td>\n<td class=\"confluenceTd\">MSCD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSCS</td>\n<td class=\"confluenceTd\">DIMP: Customer stock with vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSCS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSCS</td>\n<td class=\"confluenceTd\">MSCS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSFD</td>\n<td class=\"confluenceTd\">DIMP: Sales Order Stock with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSFD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSFD</td>\n<td class=\"confluenceTd\">MSFD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSFS</td>\n<td class=\"confluenceTd\">DIMP: Sales Order Stock with Vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MFS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSFS</td>\n<td class=\"confluenceTd\">MSFS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSID</td>\n<td class=\"confluenceTd\">DIMP: Vendor Stock with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSID</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSID</td>\n<td class=\"confluenceTd\">MSID_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSIS</td>\n<td class=\"confluenceTd\">DIMP: Vendor Stock with Vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSIS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSIS</td>\n<td class=\"confluenceTd\">MSIS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSRD</td>\n<td class=\"confluenceTd\">DIMP: Project Stock with Vendor</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSRD</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSRD</td>\n<td class=\"confluenceTd\">MSRD_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSRS</td>\n<td class=\"confluenceTd\">DIMP: Project Stock with Vendor - Total</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSRS</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSRS</td>\n<td class=\"confluenceTd\">MSRS_MD</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">\n<p>MARCH</p>\n</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARCH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARCH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MARDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MARDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MARDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCHBH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCHBH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCHBH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MKOLH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MKOLH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MKOLH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSLBH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSLBH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSLBH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKAH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKAH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKAH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSAH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSAH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSAH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSPRH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSPRH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSPRH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSSQH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSSQH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSSQH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSKUH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSKUH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSKUH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTBH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTBH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTBH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTEH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTEH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTEH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSTQH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSTQH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSTQH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MCSSH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MCSSH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MCSSH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSCDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSCDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSCDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSFDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSFDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSFDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSIDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSIDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSIDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">MSRDH</td>\n<td class=\"confluenceTd\">History</td>\n<td class=\"confluenceTd\">NSDM_DDL_MSRDH</td>\n<td class=\"confluenceTd\">NSDM_MIG_MSRDH</td>\n<td class=\"confluenceTd\">-</td>\n</tr>\n</tbody>\n</table></div>\n<p>The hybrid tables of the former Industry Solution DIMP have now new tables containing the material master data only. The name of the new tables is presented in the right column of above table.</p>\n<p> <span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">According to the fact that data will not persisted anymore in the header and item tables MKPF and MSEG the transaction DB15 behaves differently in the environment of archiving.</span></p>\n<p><span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">Transaction DB15, which allows the retrieval of statistical data for DB tables grouped by the archiving objects that refer to these tables, does not provide correct information for tables MKPF and MSEG. When selecting tables from which data is archived for archiving object MM_MATBEL, and navigating to “Online Space” or “Space Statistics” for tables MKPF or MSEG, the statistics “No. Records” and “Table Space” are shown in the result screen. These numbers are taken from the original tables MKPF and MSEG, and not calculated by redirecting the request to table MATDOC. Consequently, when executing archiving for arching object MM_MATBEL, this will have no effect on the numbers shown for tables MKPF and MSEG in transaction DB15.</span></p>\n<p><span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">Some of the hybrid tables contain a field with the semantic \"date of last change\" (in many cases field name is LAEDA). In the ERP solution this field has been updated with each material document posting. With the introduction of the new data model in S/4 the hybrid tables will not be updated with actual stock data anymore and also the field LAEDA will not be updated. Hence, the semantic of this field for the material master data hybrid tables changes to \"date of last change of master data\".</span></p>\n<div class=\"table-wrap\"></div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>One impact of the simplified MM-IM data model does exist if there are customer APPENDs or INCLUDEs with customer fields on the mentioned tables. The NetWeaver redirect capability requires that DB table and assigned proxy view is compatible in the structure: number of fields, their sequence and their type. Thus if there is an APPEND or INCLUDE on one of the above mentioned tables then the assigned DDL source of the CDS view must be made compatible. In some cases for S/4HANA on-premise 1511 this does not require customer interaction especially in those cases where the append has been put at the end of a table which is strongly recommended (not somewhere in between which may happen if a table is composed by include structures like for MARC). For other cases and in general for S/4HANA on-premise 1610 the structure of the proxy view can be made compatible to the table by extension view. This extension view is always an extension to the above mentioned view in the DDL source of the CDS view used for redirect. In the extension view the fields have to be listed in exactly the same order as in the append. For more information about view extension see e.g. <a class=\"external-link\" href=\"http://help.sap.com/abapdocu_740/en/index.htm?file=abencds_f1_extend_view.htm\" rel=\"nofollow\" target=\"_blank\">SAP NetWeaver 7.4 documentation</a>.</p>\n<p>Another impact of the simplified MM-IM data model is a performance decrease of DB read operations on the above mentioned tables just because a data fetch on one of the mentioned tables is in S/4HANA slower than in SAP ERP 6.0 due to the on-the-fly aggregation and the JOIN operation. Hence performance critical customer coding may be adjusted to improve performance. Furthermore customer coding writing data to aggregated actual stock quantity or to the former document header or item table shall be adjusted!</p>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CustomerAppendsontheformerdocumenttablesMKPFandMSEG\"></div>\n<div></div>\n<div><strong>1. Customer Appends</strong></div>\n<div>With SAP Note <a href=\"/notes/2194618\" target=\"_blank\">2194618</a> and <a href=\"/notes/2197392\" target=\"_blank\">2197392</a> SAP offers a check to be executed on the start release to identify APPEND issues described in the following sub chapters. Hence customer is not forced to scan all above listed tables manually.</div>\n<div></div>\n<p><strong><span>1.1 Customer Appends on the former document tables MKPF and MSEG</span></strong></p>\n<p><span><span>If there are APPENDs on MKPF and MSEG where fields with the same fieldname do exist then there is a name conflict in case that the content of field MKPF-A is different from field MSEG-A (fields with same name and identical content do exist on MKPF and MSEG also in SAP Standard for performance reasons e.g. BUDAT). In this case it is required to add a further field A_NEW to the append, copy the data from A to A_NEW with a special customer program and then all coding sections, Dynpros, etc. need to be adjusted to use A_NEW and then field A needs to be dropped from the append. This must be done before migration from ERP 6.0 to S/4HANA.</span></span></p>\n<p><span>If the attributes in the APPENDs or INCLUDEs (e.g. CI_M* INCLUDEs as introduced with consulting note 906314) on table MKPF and MSEG do have a master data or process controlling character then the fields from the APPENDs/INCLUDEs need to be appended to the table MATDOC and the assigned proxy views can be made compatible via extension views.</span></p>\n<p><span>In case of a system conversion all these customer fields in such APPENDs or INCLUDEs need to be appended to table MATDOC during the ACT_UPG phase (SPDD). It has to be done in any case before the MM-IM converison program will be executed which move the data from MKPF and MSEG to MATDOC otherwise data in customer fields gets lost. The structure compatibility between table MKPF/MSEG and their assigned proxy view shall be created directly after system conversion by creating extend views, see  note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a>.</span></p>\n<p><span>Fields from APPENDs/INCLUDEs to table MKPF should be appended to sub structure NSDM_S_HEADER of table MATDOC.</span></p>\n<p><span><span><span>Fields from APPENDs/INCLUDEs </span>to table MSEG should be appended to sub structure NSDM_S_ITEM of table MATDOC.</span></span></p>\n<p><strong><span><span>1.1.1 Customer include CI_COBL in table MSEG</span></span></strong></p>\n<p><span><span>Table MSEG contains the customer include CI_COBL where customers can insert own fields. The CI_COBL include has been made available also in the new MM-IM data model with note <a href=\"/notes/2240878\" target=\"_blank\">2240878</a>. This note must be applied before the data migration starts in the ACT_UPG phase (SPDD); otherwise you may loose data. With the implemented CI_COBL the table MSEG and it's assigned proxy view is not compatible in their structure anymore. The structural compatibility can be re-created by applying note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a>. This must be done directly after system conversion has been finished.</span></span></p>\n<p><span><span>If you later on like to make use of one of the fields in EXTEND VIEW for CI_COBL in the app \"Custom fields and logic\" then you need to follow the instructions given in this <a href=\"https://help.sap.com/viewer/9a281eac983f4f688d0deedc96b3c61c/1610%20003/en-US/4accfedc4d2e49c1b321b6ebf288a430.html\" target=\"_blank\">article in the SAP Help portal</a>. Basically you need to remove the field from the EXTEND VIEW for CI_COBL, save without activate and then add the field in the app \"Custom fields and logic\". This is valid for release 1709 and higher.</span></span></p>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CustomerAppendsonthehybridandpureaggregatetables\"></div>\n<p><strong><span>1.2 Customer Appends on the hybrid and replaced aggregation tables</span></strong></p>\n<div></div>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-Fieldscontainingmaterialmasterdataattributes\"><strong><span>1.2.1 Fields containing material master data attributes</span></strong></div>\n<div><span>If the append is not at the end of the hybrid table then the append should be moved to the end if possible and then no further action is required because the delivered DDL sources for the proxy views provide the $EXTENSION feature within S/4HANA on-premise 1511. Due to too many side effects like unpredictable sequence of fields from APPENDs, this has been changed with S/4HANA On-Premise 1610 where always an EXTEND VIEW for a CDS proxy view has to be created for an APPEND on a material master data table. For the DIMP tables the append has to be appended also to the new pure DIMP material master data tables.</span></div>\n<div><span>The structural compatibility between table and CDS proxy view can be re-created by applying note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a>. This must be done directly after system conversion has been finished (e.g. creating just an EXTEND VIEW with the customer fields using ABAP Development Tools for S/4HANA On Premise 1610 and higher).</span></div>\n<div><span>For replaced aggregation tables appends with master data attributes are not supported. If such appends are really required in the customer processes then the approach described in the next chapter maybe feasible. In the core functionality of material document processing there will be no write process on these tables. Thus update of the fields in the appends requires maybe some additional customer coding. </span></div>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-Fieldsrepresentingacustomerdefinedstocktypeorquantity/valuetobeaggregated\"></div>\n<p><strong><span>1.2.2 Fields representing a customer defined stock type or quantity/value to be aggregated</span></strong></p>\n<p><span>If own stock types or a dimension which needs to be aggregated have been introduced by the customer then the view stack of the CDS view assigned to the table with the additional stock type needs to be modified. Right now, there is no technology support for modification free enhancement. If the stock type has not been introduced by new entries or enhancements in the tables T156x (T156, T156SY, T156M, T156F) - which controls in the core functionality the mapping between a posting and a stock type - then the process logic needs to be adapted.</span></p>\n<p><strong><span>1.3 Customer Appends on views</span></strong></p>\n<p><span>There are several views in SAP Standard which also do have an assigned proxy view because the view provide actual stock quantity data. View and assigned proxy view must be compatible in structure too. If there are customer appends on such view the same rules as for tables apply. Views with assigned proxy compatibility view can be determined by searching via transaction SE16N in table DD02L with TABCLASS = VIEW and VIEWREF &lt;&gt; '' or you may use above mentioned check functionality in your start release.</span></p>\n<p><strong><span>1.3.1 Customer views on MKPF/MSEG</span></strong></p>\n<p><span>Views are database objects and thus a view is executed on the database. Because the table MKPF and MSEG will not contain data anymore (except legacy data from migration) such a customer view will never return any record. Such views have to be either adjusted by fetching data from table MATDOC or to be created new as DDL source with a different name. In the last case all usages of the old DDIC SQL view must be replaced by the new CDS view.</span></p>\n<p><strong><span>1.3.2 Customer views on material master attributes</span></strong></p>\n<p><span>Such views using only material master data attributes from the hybrid tables do not need to be changed.</span></p>\n<p><strong><span>1.3.3 Customer views using aggregated stock quantity data</span></strong></p>\n<div id=\"SI1:Logistics_MM-IM-Datamodel-CodeOptimization(optional/recommended)\">Customer views having at least one actual stock quantity aggregate cannot be used anymore because</div>\n<ul>\n<li>\n<div>the field representing this aggregate on the database will be empty forever</div>\n</li>\n<li>\n<div>the quantity must be aggregated from table MATDOC which is not possible with DDIC SQL views.</div>\n</li>\n</ul>\n<div>Such views must be defined new as DDL source with a new name. Each of the above mentioned DDL sources can be used as template. All usages of the old DDIC SQL view must be replaced by the new CDS view.</div>\n<div></div>\n<div></div>\n<p><strong><span>2 Code adjustments and optimizations</span></strong></p>\n<p>Technically it is still possible to do DB write operations (INSERT, UPDATE, DELETE, MODIFY) on the tables MKPF, MSEG as well as the fields representing actual stock quantities in the hybrid and replaced aggregation tables. But such write operations are without any effect! Therefore write operations on MKPF, MSEG as well as the fields representing actual stock quantities in the hybrid and replaced aggregation tables shall be removed from customer coding. Write operations on the material master data attributes in the hybrid tables are still possible. Write operations on table MATDOC and your moved customer append fields are done by class CL_NSDM_STOCK.</p>\n<p>DB read operations on the hybrid and replaced aggregation tables have a performance decrease. In general, it shall be avoided to read any stock quantities when only master data is required. Therefore it is recommended to adjust the customer coding in the following way:</p>\n<ul>\n<li>If material master data as well as actual stock quantity data are required then the SELECT....&lt;table&gt; should be replaced by using a data access method from class CL_NSDM_SELECT_&lt;table&gt;. These classes provide access methods for single as well as array read operations. Those access methods shall not be used if the KEY table contains more than 1000 records to to limitations of the SELECT.....FOR ALL ENTRIES.</li>\n<li>If material master data are required then the SELECT....&lt;table&gt; should be replaced by SELECT....V_&lt;table&gt;_MD where V_&lt;table&gt;_MD is one of the above mentioned views for master data access. Alternatively corresponding material master data read methods in the class CL_NSDM_SELECT_&lt;table&gt; can be used (those access methods shall not be used if the KEY table contains more than 1000 records to to limitations of the SELECT.....FOR ALL ENTRIES.). Also the data type declarations should be adjusted from TYPE...&lt;table&gt; to TYPE...V_&lt;table&gt;_MD.</li>\n<li>If actual stock quantity data are required then the SELECT....&lt;table&gt; should be replaced by SELECT....NSDM_V_&lt;table&gt;_DIFF where NSDM_V_&lt;table&gt;_DIFF is one of the views in the view stack of the above mentioned proxy view. Also the data type declarations should be adjusted from TYPE...&lt;table&gt; to TYPE...NSDM_V_&lt;table&gt;_DIFF.</li>\n<li>For table MARC and field STAWN valid from S/4HANA On Premise 1610 please read note <a href=\"/notes/2378796\" target=\"_blank\">#mce_temp_url#</a></li>\n</ul>\n<p>For performance critical coding parts these adjustments are strongly recommended. For non critical parts it is optional short term but recommended on long term.</p>\n<p><span 10pt;\"=\"\" arial',sans-serif;=\"\" font-size:=\"\" lang=\"EN-US\">To identify such locations, it is required to make use of the where-used functionality of transaction SE11 and considering other techniques like transaction CODE_SCANNER to find locations which SE11 cannot handle – like dynamic programming or native SQL statements.</span></p>\n<p><span 10pt;\"=\"\" arial',sans-serif;=\"\" font-size:=\"\" lang=\"EN-US\">Consider SAP Note <a href=\"/notes/28022\" target=\"_blank\">28022</a> if there are issues with the where-used functionality in the customer system. In the where-used dialog it is possible via the button \"Search Range\" to search specific for key words like SELECT, INSERT and so on.</span></p>", "noteVersion": 20}]}, {"note": "2194618", "noteTitle": "2194618 - <PERSON>4TC SAP_APPL - Checks for MM-IM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to execute a transition of your ERP system to S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note. The checks will be executed during the transition process.</p>", "noteVersion": 36}, {"note": "2240878", "noteTitle": "2240878 - MM-IM: Add customer include CI_COBL to MATDOC", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The MM-IM data model in SoH composed by the two document tables MKPF and MSEG has been changed in S/4HANA to a new model consisting of a single de-normalized table with name MATD<PERSON>. Also the actual stock quantity data will not be stored anymore in the material master aggreagte tables but calculated on-the-fly from MATDOC.</p>\n<p>The table MSEG contains a so called customer include. This include is missing in the new data model and hence during migration data loss may occure. Because table MATDOC is iteslf composed by include structures, this customer include has to be inserted in one of those structures.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MM-IM; MSEG; MATDOC; CI_COBL; Customer include</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>CI_COBL include is not included in the new data model</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Please note that this solution is only valid for S4HANA 1511 (S4CORE 100)</strong></p>\n<p>Enter transaction SE11.</p>\n<p>Enter \"NSDM_S_ITEM\" as Data type and press Change. Navigate to the last field of the structure and set the cursor after this field.</p>\n<p>Go to the menu -&gt; Edit -&gt; Include -&gt; Insert and then enter CI_COBL as Structure. Press Enter and then activate the changes.</p>\n<p>As NSDM_S_ITEM is one of the structures of which MATDOC is composed now CI_COBL is part of table MATDOC and hence fields of this customer include will be set during conversion as well.</p>", "noteVersion": 4}, {"note": "28022", "noteTitle": "28022 - Customer system: Where-used list for SAP Objects", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The where-used list for SAP objects is incomplete in the customer system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>() EUNOTE, EUWORKBENCH, ES244</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In a customer system, you cannot be sure that the where-used list for SAP objects is complete. The where-used list is normally only set up for customer-specific objects and is updated incrementally (see Note 18023).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If required, the necessary index can also be created for the where-used list of SAP objects. To do this, you must start the report SAPRSEUB (in the background).<br/>Make sure that the job EU_INIT does not run in parallel.<br/><br/>Due to the size of the index, we recommend that you set up the index in development systems only. Depending on the size of the SAP System, additional memory requirement of up to 1 GByte is required. The report SAPRSEUB has a very long runtime and can run up to 100 hours (5 days).<br/><br/>In general, the following applies: The system does not automatiically update the index for the where-used list with a new upgrade or when you import a support package or enhancement package. You may have to start the report SAPRSEUB again.<br/><br/></p>\n<p>For more information on the job EU_INIT, see SAP Note 2234970.</p></div>", "noteVersion": 12, "refer_note": [{"note": "18023", "noteTitle": "18023 - Jobs EU_INIT, EU_REORG, EU_PUT", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>What are the jobs EU_INIT (program SAPRSEUC), EU_REORG (program SAPRSLOG), and EU_PUT (program SAPRSEUT), which are automatically scheduled by the ABAP Workbench, used for?</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SE80, Object Navigator, Repository Browser, Repository Information System</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The EU jobs are used to reconstruct or update the indexes (where-used lists, navigation indexes, object lists) that are important for the ABAP Workbench.<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>When you start transaction SE80 (Repository Browser) for the first time, the three EU jobs are automatically created and, if the user has sufficient authorizations, released: EU_INIT (single start), EU_REORG (periodically each night), and EU_PUT (periodically each night). Alternatively, You can also schedule the three jobs by manually executing program SAPRSEUJ.<br/>If the user does not have a release authorization, the jobs remain in the status \"scheduled\". In this state, they are not executed until they are eventually manually released. Therefore, in this case it is required that a system administrator interferes.<br/><br/>Short description of the individual jobs:<br/></p> <ul><li>EU_INIT:</li></ul> <p>           EU_INIT is used to completely rebuild the indexes and therefore has a correspondingly long runtime. It starts program SAPRSEUI. All customer-defined programs (selection according to the naming convention) are analyzed and indexes are created that are used in the ABAP Workbench for the where-used lists of function modules, error messages, reports, and so on. These indexes are automatically updated in dialog mode.<br/>The job can be repeated at any time. After a termination, the job is automatically scheduled for the next day; it then starts at the point of termination. (EU_INIT can therefore be terminated deliberately, if it disturbs other activities in the system. See also Note 759407.)</p> <ul><li>EU_REORG:</li></ul> <p>           As mentioned above, the indexes are automatically updated online by the ABAP Workbench tools. To keep the effort for updating these indexes as low as possible, only the changes are logged, which means a reorganization of the entire index for each program is required from time to time. So that this reorganization does not interfere with the online system, the EU_REORG job runs every night and performs this task. If the EU_REORG job did not run one night, this simply means that the reorganization takes place more often online.</p> <ul><li>EU_PUT:</li></ul> <p>           The EU_PUT job also runs every night. It starts program SAPRSEUT. This program checks whether customer-defined development objects have been transported into the SAP system with the SAP transport system, and generates or updates the indexes described above whenever required.<br/>           <br/>To create the indexes, the EU jobs analyze the program sources of the development objects. Faulty ABAP programs (sources with grave syntax errors, for example, a literal that is too long because a concluding inverted comma is missing) are skipped. The relevant job continued the analysis with the next program and issues the names of all programs with errors in a list.<br/>After correcting the faulty programs, you can update the object lists of the relevant programs in the Repository Browser. To do this, proceed as follows:</p> <ul><ul><li>Up to Release 6.10: Choose \"Update\".</li></ul></ul> <ul><ul><li>As of Release 6.20: In the tree display for the object list, go to the context menu \"Other functions\"-&gt;\"Rebuild object list\".</li></ul></ul> <ul><ul><li></li></ul></ul> <ul><ul><li></li></ul></ul></div>", "noteVersion": 10}, {"note": "401389", "noteTitle": "401389 - Where-used list in the Workbench as of Release 6.10", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The where-used list for Dictionary objects and object types returns an incomplete result or no result at all.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Editor, index, SAPRSEUB, where-used list, use, EU_INIT, Repository Information System, SE11, ABAP Workbench</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>A new where-used list for types was delivered with Basis Release 6.10. This list is based on its own index. You must first set up the initial index.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Schedule a job once that executes the report SAPRSEUB. This program creates the index for the where-used list. Check if the job was executed properly. After an upgrade, you must run the job SAPRSEUB again. For more information, see SAP Note 28022.</p>\n<p>The full index creation requires different resources, depending on the number of programs and Dictionary objects. For more information, see SAP Notes 28022 and 2039618. For customer objects and programs, the index is set up automatically the program SAPRSEUC runs. You do not normally have to start this program specifically. For more information, see SAP Note 18023. The index for customer programs requires fewer resources.</p></div>", "noteVersion": 11}, {"note": "788218", "noteTitle": "788218 - Add. info. on upgrading to SAP ERP Central Component 5.0 SR1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Errors in the upgrade or update procedure or in the upgrade guides; preparations for the upgrade or update; additional information to the upgrade guide.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Update, upgrade, SUM, Software Update Manager, SAP ERP Central Component 5.00 Support Release 1</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>*</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><br/><strong>CAUTION:</strong><br/>This note is updated regularly!<br/>Therefore, you should read it again immediately before starting the upgrade.<br/><br/></p>\n<p><strong>What information can I expect from this note?</strong></p>\n<p>This note describes problems that may occur during the system upgrade and provides information on how to solve them. This usually takes the form of references to other notes.<br/>The main purpose of this note is to prevent data loss, upgrade shutdowns, and long runtimes.<br/>It deals with database-independent problems only.<br/><br/></p>\n<p><strong>Which additional notes do I require in preparation for the upgrade?</strong></p>\n<p>This depends on the functions that you are using. You will need one or several of the following notes:<br/><br/>Short text................................................ Note number<br/>_____________________________________________________________________<br/>Additional information on upgrading to WAS 6.40 MaxDB .........669656<br/>Additional Information: Upgrade to SAP Web AS 6.40.............661569<br/>DB6: Additions to upgrade (based) on SAP Web AS 6.40...........662191<br/>DB2-z/OS: Additions upgrade to Basis 6.40......................661252<br/>Add. info. on upgrade to SCM41 SRM40 ECC50 MSSQL...............737115<br/>Add. info. on upgrading to SAP Web 6.40 ORACLE.................662219<br/>Additional information on the upgrade to ECC 5.0...............700675<br/>_____________________________________________________________________<br/>Repairs for upgrade to Basis 640 ............................. 663240<br/>Corrections for for R3up Version 640 ......................... 663258<br/>_____________________________________________________________________<br/>OCS: Known problems with Supp.Packages in Basis Rel.6.40 ......672651<br/>_____________________________________________________________________</p>\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP ERP Central Component 5.0 SR1. You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p> </p>\n<p> </p>\n<p><strong>Contents</strong></p>\n<p>I/ ...... Keyword<br/>II/ ..... Important General Information<br/>III/ .... Corrections to the Guide<br/>IV/ ..... Errors on the CD-ROM<br/>V/ ...... Preventing Data Loss, Upgrade Shutdown, and Long Runtimes<br/>VI/ ..... Preparing the Upgrade<br/>VII/..... Problems During the PREPARE and Upgrade Phases<br/>VIII/ ... Problems After the Upgrade<br/>IX/ ..... Chronological Summary<br/><br/></p>\n<p><strong>I/ keyword</strong></p>\n<p>Keywords are no longer applicable.<br/>(For information only: The R3up keyword was 160421.)</p>\n<p> </p>\n<p><strong>II/ Important General Information</strong></p>\n<p><strong>**************************************************</strong></p>\n<p>The former tool R3up was replaced by the Software Update Manager (SUM). See also SAP Note 1589311 and the blog:<br/><a href=\"https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/\" target=\"_blank\">https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/</a>.</p>\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP ERP Central Component 5.0 SR1.</p>\n<p>You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p><strong>**************************************************</strong></p>\n<p> </p>\n<p><br/>-----------------------&lt; D025323 MAY/24/02 &gt;--------------------<br/><strong>Corrections and Repairs for the Upgrade</strong><br/>Before the upgrade, you must check whether a new version of the R3up exists for your specific upgrade.<br/>For more information, see <strong>Note 663258</strong>.<br/><br/>-----------------------&lt; D021371 NOV/29/05 &gt;--------------------<br/><strong>Upgrade on Linux x86_64: Correct R3up Version</strong><br/>For SAP systems installed on Linux x86_64, there is no 64Bit R3up version. You need to use R3up for Linux x86 32-Bit.<br/>For more information on how to use the R3up version in combination with the Kernel DVD, see Note <strong>893352</strong>.<br/><br/>-----------------------&lt; D034302 DEZ/03/03 &gt;--------------------<br/><strong>Windows only: Execute program R3dllins.exe</strong><br/>The 6.40 kernel is compiled with the new version of MS compiler and requires additional libraries for operation. To prevent problems during and after the upgrade, you must execute program R3dllins.exe on your central host, all application hosts, and on the remote shadow host, if you are planning to use one.<br/>You can find the program on the Upgrade Master CD in the NT\\I386\\NTPATCH folder. It must be executed before you start PREPARE and directly from the NTPATCH folder (it can be shared). Copying and executing the program<br/>will not work!<br/><br/>-----------------------&lt; D028310 JUL/19/02 &gt;---------------------<br/><strong>Problems with the Shadow Instance.</strong><br/>The following Notes contain information about problems with the shadow instance:</p>\n<ul>\n<li><strong>525677</strong>: Problems when starting the shadow instance</li>\n</ul>\n<ul>\n<li><strong>430318</strong>: Remote shadow instance on a different operating system</li>\n</ul>\n<p><br/>------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br/><strong>LSWM now part of SAP_BASIS</strong><br/>As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW and your source release is based on SAP Web AS 6.10 or lower, do not implement LSMW after the upgrade.<br/>For more information, see <strong>Note 673066</strong>.<br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>III/ Corrections to the Guides</strong></p>\n<p><br/>-----------------------&lt; D033903 24/SEP/08 &gt;--------------------------<br/><strong>SDK Version 1.4.x for Upgrade Assistant</strong><br/>The upgrade assistant only supports Java Software Development Kit (SDK) 1.4.x. It does not support version 1.5 or higher.<br/><br/>------------------------&lt; D035220 APR/23/07 &gt;--------------------------<br/><strong>Migrate data from table TVARV to TVARVC</strong><br/>As of SAP Basis 6.10, the client-specific table TVARVC is used instead of cross-client table TVARV. If you want to migrate entries from table TVARV to the new table, you can use report RSTVARVCLIENTDEPENDENT.<br/>For more information, see Note <strong>557314</strong>.<br/><br/>------------------------&lt; D024991 OCT/19/06 &gt;-------------------------<br/><strong>Data Management Planning - Link to SAP Service Marketplace</strong><br/>Site service.sap.com/dao on SAP Service Marketplace is temporarily underconstruction. Please refer to the following site and document instead:<br/>service.sap.com/data-archiving -&gt; Media Library -&gt; Literature &amp; Brochure-&gt; Data Management Guide<br/><br/>------------------------&lt; D022030 JUN/21/06 &gt;-------------------------<br/><strong>Section: Making Entries for the Parameter Input Module</strong><br/>The maximum length of the mount directory path is <strong>50</strong> characters.<br/>It may not contain any blanks or special characters.<br/><br/>------------------------&lt; D022030 16/MAY/06 &gt;-------------------------<br/><strong>AIX: Isolating the Central Instance</strong><br/>The values stated in the AIX part of section \"Isolating the Central Instance\" are minimum values.<br/><br/>------------------------&lt; D022030 31/JAN/06 &gt;-------------------------<br/><strong>Windows: Space Requirements for MS SQL Database</strong><br/>In sections \"Upgrade - Step by Step\" and \"Checking the Hardware Requirements\", you are referred to Note 689574 for space requirements with MS SQL database.<br/>This reference is wrong.<br/>The PREPARE program issues the correct space requirements.<br/><br/>------------------------&lt; D030328 09/AUG/05 &gt;-------------------------<br/><strong>Optional Follow-Up Activity: Where-used list in the Workbench</strong><br/>As of release 6.10, the where-used list for Dictionary objects has changed. If you need a proper display of the list, you need to run report SAPRSEUB after the upgrade.<br/>As the runtime of the report may be quite long, we recommend that you run it in the development system only.<br/>For more information, see Notes <strong>401389</strong> and <strong>28022</strong>.<br/><br/><br/>------------------------&lt; D022030 APR/06/05 &gt;-------------------------<br/><strong>Names of DVDs needed - Section \"Upgrade - Step by Step\"</strong><br/>The names of the DVDs needed as they are described in section \"Upgrade -Step by Step\" do not fully correspond to the titles of the actual DVDs.<br/>For the correct DVD titles, see the documentation \"mySAP ERP Master Guide\" for mySAP ERP 2004 on SAP Service Marketplace under Quick Link \"instguides\" -&gt; mySAP ERP -&gt; mySAP ERP 2004.<br/><br/>------------------------&lt; D022030 MAR/18/05 &gt;-------------------------<br/><strong>Documentation \"SAP Software on UNIX - OS Dependencies\"</strong><br/>The information formerly contained in the documentation \"SAP Software on UNIX - OS Dependencies\" has been moved to the documentation \"Component Installation Guide &lt;your SAP component system combination&gt;, Part I.<br/><br/>------------------------&lt; I002675 MAR/09/05 &gt;-------------------------<br/><strong>Windows Guide Section on Database-Specific Parameters</strong><br/>In the Guide for the Upgrade on Windows, section \"Checking the Database- specific requirements for PREPARE\", you are asked to check the profile parameters for MS SQL server. As some of the parameter names have changed, SAP system tools may not recognize the parameters.<br/>For more information, see <strong>Note 826528</strong>.<br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>IV/ Errors on the CD-ROM</strong></p>\n<p><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>V/ Preventing Data Loss, Upgrade Shutdown, and Long Runtimes</strong></p>\n<p><br/>-----------------------&lt; D000706 28/NOV/06 &gt;--------------------------<br/><strong>Modification Adjustment Planning and Unicode Conversion</strong><br/>You cannot import transport requests created in a Unicode SAP system into a non-Unicode SAP system. If you want to perform a Unicode conversion of your SAP system, create the transport request <strong>before</strong> the conversion.<br/><br/>-----------------------&lt; D028597 15/SEP/06 &gt;--------------------------<br/><strong>Support Package SAPKB64018 - use corrected version</strong><br/>If you include Support Package SAPKB70009 in the upgrade, make sure to use the corrected version, indicated by EPS file name CSN0120061532_0024351.PAT.<br/>If you use the old version, phase XPRAS_UPG returns an error on the after import method SRM_FILL_KC_TABLES_AFTER_IMP. In this case, proceed as described in Note <strong>967821</strong>.<br/>For more information about the problem in general, see Note <strong>672651 </strong>.<br/><br/>------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br/><strong>Back up customer-specific entries in table EDIFCT</strong><br/>If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br/>For more information, see Note <strong>865142</strong>.<br/><br/>------------------------&lt; D023890 16/AUG/05 &gt;-------------------------<br/><strong>Add-On strategy for ST-PI: Do not use Delete</strong></p>\n<ul>\n<li>Phase IS_SELECT:</li>\n</ul>\n<p>           Do not use the option \"DELETE\" for ST-PI in phase IS_SELECT. <br/>           If the option \"INST/UPG WITH STD CD\" does not work, download the latest version of ST-PI at http://service.sap.com/installations -&gt; Installations and Upgrades -&gt; Plug-Ins -&gt; SAP Solution Tools -&gt; ST-PI -&gt; ST-PI 2005_1_640 -&gt; Supplementary Upgrade</p>\n<ul>\n<li>Phase BIND_PATCH:</li>\n</ul>\n<p>           Additionally you should include (at least) 2 Support Packages at http://service.sap.com/patches -&gt; Plug-Ins -&gt; SAP Solution Tools -&gt; ST-PI -&gt; ST-PI 2005_1_640<br/>           You can ignore the warning: ST-PI - selected package level is 2, equivalence level is 3<br/>           Do not ignore other warnings!<br/>           If you are using a different release than ST-PI 2005_1_640 include all available Support Packages for this release.<br/>For more information about the release strategy of ST-PI, see SAP notes <strong>539977</strong> and <strong>606041</strong>.<br/>If you have decided to delete ST-PI, this will lead to an error in Phase TR_PATCH_STATUS_UPGRADE: ...failed with RFC_ERROR_SYSTEM_FAILURE.<br/><br/>------------------------&lt; D030559 14/JUL/05 &gt;-------------------------<br/><strong>Do not include ABA Support Package 12 in the Upgrade</strong><br/>If you included Support Package SAPKA64012 in the upgrade, the upgrade fails in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br/>To prevent the failure, only include the Suport Package in the upgrade if you can also include at least Support Package <strong>SAPKA64013</strong> as the problem will be fixed with SP 13.<br/>For more information - also on the procedure in case of an upgrade failure, see <strong>note 849925</strong>.<br/><br/>------------------------&lt; D031049 14/OCT/04 &gt;-------------------------<br/><strong>Source Rel. 4.0B: Project-Related Incoming Orders</strong><br/>If you are using the preliminary solution for project-related incoming orders published with Note 118780, you have to modify data elements before the upgrade.<br/>For more information, see <strong>note 369542</strong>.<br/><br/>------------------------&lt; D024329 14/OCT/04 &gt;-------------------------<br/><strong>Component PS-ST-WBS: Preliminary Inclusion of Field KIMSK</strong><br/>If you do not want the upgrade to overwrite field KIMSK, you can include the field in the table of modifiable fields before the upgrade.<br/>For more information, see <strong>note 185315</strong>.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>VI/ Preparing the Upgrade</strong></p>\n<p><br/>------------------------&lt; D044675 17/JAN/08 &gt;--------------------------<br/><strong>Upgrade from Source Release 4.6C: Including a minimum SP level</strong><br/>If your source release system includes SAP_HR 46C Support Package level D1, data loss may occur during the upgrade. To prevent any data loss, include at least Support Package 43 of the target release into the upgrade, although the upgrade program does not request it.<br/>Alternatively, update your system to SAP_HR 46C Support Package level D2 before the upgrade.<br/><br/>-----------------------&lt; D001330 27/APR/06 &gt;--------------------------<br/><strong>Handling of customer translation in the upgrade</strong><br/>Z languages or customer translations on system texts with transaction SE63 are not considered as modifications to the system by the upgrade and are therefore lost during the upgrade. As the SAP system may change considerably from one release to the next, it may not be worth saving the translations.<br/>If you think that it is worth saving your translations or languages, seeNote <strong>485741</strong> for more information.<br/><br/>--------------------------&lt; D037027 DEC/12/05 &gt;-----------------------<br/><strong>SD Texts: Check Text Customizing on Source Release</strong><br/>Before the upgrade, check your text Customizing in transaction VOTX as described in Note <strong>900607</strong>.<br/><br/>--------------------------&lt; D001330 10/OCT/05 &gt;-----------------------<br/><strong>Apply the Latest Upgrade Repairs for Correct Language Import</strong><br/>If you do not apply the latest repairs for the upgrade as described in Note 663240, the language import from the data dictionary will be incomplete.<br/>For more information, see Note <strong>885955</strong>.<br/><br/>-----------------------&lt; D028310 NOV/03/04 &gt;--------------------<br/><strong>Phase CONFCHK_IMP on Distributed Systems</strong><br/>Phase CONFCHK_IMP offers you a list of operating systems to select from. This list only contains one entry \"Linux\" which is valid for both Linux and Linux IA64.<br/><br/>------------------------&lt; D030022 14/OCT/04 &gt;-------------------------<br/><strong>Source Rel. 3.1I: Loss of Address Data</strong><br/>If you are using component \"Plant Maintenance\" (PM-WOC-MN) and convert the addresses with report  RSXADR05 before the upgrade from Source Release 3.1I, make sure to read <strong>note 360929</strong>.<br/>Otherwise you may lose address data during the upgrade.<br/><br/>----------------------&lt; D033898 06/OCT/04 &gt;---------------------------<br/><strong>Source Release 4.6C and below: SMODILOG Entries</strong><br/>In Releases 4.6C and below, when you created customer-specific parameter effectivities, the system did not make SMODILOG entries. In order not to lose these customer-specific parameter effectivities during the upgrade, proceed as described in <strong>note 741280</strong>.<br/><br/>----------------------&lt; D038245 09/SEP/04 &gt;---------------------------<br/><strong>Source Release Extension Set 1.10: Exchange containers</strong><br/>If your system was installed with SAP R/3 Enterprise Ext. Set 1.10 (based on SAP Web AS 6.20) and you are using a database that uses different containers for saving data (Oracle, Informix and DB2 UDB for UNIX and Windows), refer to <strong>note 674070</strong> before the upgrade.<br/>Otherwise, the exchange containers (tablespaces/dbspaces) cannot be emptied during the upgrade and cannot be deleted after the upgrade.<br/><br/>------------------------&lt; D038245 19/APR/04 &gt;------------------------<br/><strong>Unicode Systems: Downward Compatible Kernel 6.40</strong><br/>If you are using the normal kernel for Release 6.20 with your Unicode system, PREPARE issues the error message: Could not open the ICU common library.<br/>Before you start PREPARE, install the Downward Compatible Kernel for Release 6.40. Until this kernel is available, proceed as described in <strong>Note 716378</strong>.<br/><br/>------------------------&lt; D032986 27/JAN/04 &gt;------------------------<br/><strong>Upgrading with reintegrated add-ons (retrofit)</strong><br/>For SAP ERP Core Component 5.0 (ECC 5.0), more add-ons were reintegrated into the ECC or the extension set.<br/><strong>Note 700778</strong> contains additional information on processing these add-ons before, during and after the upgrade.<br/><br/>--------------------------&lt; D032986 27/JAN/04 &gt;-----------------------<br/><strong>Upgrading with PI/PI-A</strong><br/>For information on how to upgrade your system with PI/PI-A plug-ins, see <strong>Note 700779</strong>.<br/><br/>--------------------------&lt; D025323 24/APR/03 &gt;-----------------------<br/><strong>Upgrade on AIX: saposcol</strong><br/>Refer to Note <strong>526694</strong> before the upgrade.<br/><br/>--------------------------&lt; D019926 DEC/10/02 &gt;-----------------------<br/><strong>Upgrading with AIX 5.1</strong><br/>If you want to upgrade with AIX 5.1, see <strong>Note 502532</strong> before starting PREPARE.<br/><br/>-----------------------&lt; D025323 FEB/20/02 &gt;--------------------------<br/><strong>Source Releases on UNIX 32-bit or AIX 64-bit</strong><br/>In some cases, you may have to upgrade the operating system to 64-bit before the actual upgrade.<br/>When you upgrade from AIX 4.3 64-bit, you must perform some additional actions before upgrading.<br/>For more information, see <strong>Notes 496963</strong> and <strong>499708</strong>.<br/><br/>-----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>VII/ Problems During the PREPARE and Upgrade Phases</strong></p>\n<p><br/>This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.</p>\n<p><strong>Problems During the PREPARE Phases</strong></p>\n<p><br/>------------------------&lt; D038245 05/SEP/05 &gt;------------------------<br/><strong>Problem with DVD mount paths</strong><br/>When you enter mount points in PREPARE, you may get the error message: SEVERE ERROR: unable to find directory<br/>In this case, check whether you mount point is longer than 94 characters or contains blanks and special characters. In this case, shorten the name and remove blanks or special characters.<br/><br/>-----------------------&lt; D038245 20/APR/05 &gt;---------------------------<br/><strong>Phase RFCCHK_INI: Name or Password is Incorrect</strong><br/>In phase RFCCHK_INI you may get the error message \"Name or Password is Incorrect\".<br/>If you replaced R3up after phase RFCCHK_INI with the latest version from SAP Service Marketplace, this error may also come up in other phases that connect to the system using RFC.<br/>For more information, see Note <strong>792850</strong>. You may have to replace disp+work and restart the system.<br/><br/>-----------------------&lt; D028310 JUL/26/04 &gt;----------------------<br/><strong>Source Release 3.1I and 4.0B: Error in Phase TR_CMDIMPORT_FDTASKS </strong><br/>If there are nametab entries with a non-unique UUID, you get an error message in phase TR_CMDIMPORT_FDTASKS. Proceed as described in <strong>note 705485</strong><br/><br/>-----------------------&lt; D038245 APR/11/02 &gt;--------------------<br/><strong>Termination in the TOOLIMPD3 phase</strong><br/>The TOOLIMPD3 phase terminates in the PREPARE module import. The following message appears in the log file: ABAP runtime error CALL_FUNCTION_NO_RECEIVER<br/>Receiving data for unknown CPIC link XXXXXX.<br/>Repeat the phase and continue with the upgrade.<br/><br/>-----------------------&lt; D022256 SEP/04/00 &gt;--------------------<br/><strong>For Windows NT 4.0 only</strong><br/>During PREPARE, a dialog box with the following error message may appear: 'The procedure entry point ... could not be located in the dynamic link library ... .',<br/>In this case, import the latest DLLs using the R3DLLINS.EXE program. The program is available on the CD SAP Kernel in directory \\NT\\&lt;Processor type&gt;\\NTPATCH.<br/>Reboot your machine and restart PREPARE with 'PREPARE repeat'.<br/><br/>-----------------------------------------------------------------------<br/><br/><br/></p>\n<p><strong>Problems During the Upgrade Phases</strong></p>\n<p><br/>------------------------&lt; D028310 20/AUG/04 &gt;--------------------------<br/>Phase: DIFFEXPDDIV<br/>Note: 766379<br/>Description:<br/>Error message in log file DIFFEXPD.ELG (directory &lt;DIR_PUT&gt;/log):<br/>INACTIVE DDIC VERSIONS-Export ERRORS and RETURN CODE in SAPEDDD622.QO1<br/>~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br/>2EETW190 \"TABT\" \"TESCL                        \" has no active version.<br/>...<br/><br/>-----------------------------------------------------------------------<br/>Phase: PARDIST_SHD<br/>Note: 73999<br/>Description: PCON_640 or PARDIST_SHD upgrade phases: TG450 to TG453<br/><br/>-------------------------&lt; D025988 JUN/16/00 &gt;----------------------<br/>Phase: PARDIST_SHD<br/>Description: For Windows NT only<br/>The upgrade is terminated during the PARDIST_SHD phase. The PCONUPG.ELG log file contains an incomplete error text which was extracted from the DS&lt;date&gt;.&lt;SID&gt; log file. Repeat the phase.<br/><br/>--------------------------&lt; I808985 22/FEB/06 &gt;-----------------------<br/>Phase: TABIM_UPG<br/>Description: You may get a duplicate key error with the following text: 2EETW000 Update and insert sequence failed due to an error in DBI<br/>In this case, delete the primary index of the respective table and recreate it. Repeat the upgrade phase.<br/>For more information on duplicate keys, see Note 626915.<br/><br/>--------------------------&lt; D019416 19/MAR/04 &gt;-----------------------<br/>Phase: TABIM_POST<br/>Note: 718912<br/>Description: Upgrade stops with error DI829. In this case, implement the above note and continue with the upgrade. After the upgrade, you have to revert the modification.<br/><br/>--------------------------&lt; C5010299 JAN/21/05 &gt;-----------------------<br/>Phase: XPRAS_UPG<br/>Note: 778198<br/>Description: Error message S&gt;801 in log file LONGPOST.LOG. For more<br/>information, see the note above.<br/><br/>--------------------------&lt; D022030 JAN/26/06 &gt;----------------------<br/>Phase: XPRAS_UPG<br/>Note: 534826<br/>Description: LONGPOST.LOG: The XPRA RMCSXP03 reports error M2392, M2108 or M2685. The note mentioned above contains more information on problems with update programs in addition to the solution to the error.<br/><br/>--------------------------&lt; D021867 AUG/10/04 &gt;-----------------------<br/>Phase: JOB_RDDNTPUR<br/>Description: In the longpost-log file, you may get the error message: 3PETG447 Table and runtime object \"TA22EQU_WAO\" exist without DDIC reference.<br/>You can ignore this message.<br/><br/>-----------------------------------------------------------------------<br/>Phase JOB_RSTLANUPG<br/>Note: 626272<br/>Description: Termination; the log file for job RSTLAN_UPGRADE contains the following error message:<br/>\"..Job terminated after system exception ERROR_MESSAGE.\"<br/><br/>--------------------------&lt; D025323 MAY/23/02 &gt;-----------------<br/>Phase: CHK_POSTUP<br/>Note: 197886<br/>Description: If you have imported Notes 178631, 116095 or 69455 into your system before the upgrade, error messages for objects without DDIC reference appear in the log LONGPOST.LOG. Proceed as described in the Note.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>VIII/ Problems After the Upgrade</strong></p>\n<p><br/>This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br/><br/>-----------------------&lt; D030731 15/SEP/05 &gt;------------------------<br/><strong>Run Report Z_873466_REPAIR_AFTER_UPGRADE</strong><br/>Table COKEY2 has been extended with two fields that cannot be initialized by the upgrade. Therefore, you need to run report Z_873466_REPAIR_AFTER_UPGRADE directly after the upgrade.<br/>For more information, see Note <strong>873466</strong>.<br/><br/>-----------------------&lt; D023890 11/MAR/05 &gt;------------------------<br/><strong>Ignore or delete view VFAS</strong><br/>If after the upgrade the \"VFAS\" view is displayed as incorrect in transaction DB02, you can ignore this or delete the view.<br/><br/>-----------------------&lt; D039516 15/FEB/05 &gt;-------------------------<br/><strong>Non-IS-BEV Users: Deactivate Beverage Solution Pushbutton</strong><br/>If you are <strong>not using IS Beverage solution</strong>, you have to deactivate the Beverage Solution pushbutton after the upgrade.<br/>For more information, see <strong>Note 781111</strong>.<br/><br/>-----------------------&lt; D000434 28/JAN/04 &gt;-------------------------<br/><strong>Source Release 6.20: Missing DB View STWB_INFO</strong><br/>If your source release is based on SAP_BASIS 6.20 SP 48 or lower, transaction DB02 might show DB view STWB_INFO missing after the upgrade. <strong>This</strong> view is not needed in the system and requires no further actions.<br/><br/>-----------------------&lt; D035318 08/JUL/04 &gt;-------------------------<br/><strong>Source Release lower than Basis 6.10: Codepage Conversion</strong><br/>In Release 6.10, the codepage administration has changed considerably. If you want to continue using the customer-defined codepages that start with \"9\" after the upgrade, you have to convert the codepages using report RSCP0126 after the upgrade.<br/>For more information, see <strong>Notes 485455</strong> and <strong>511732</strong>.<br/><br/>------------------------&lt; D020815 AUG/23/02 &gt;-------------------<br/><strong>SPAU: Names of interface methods are truncated</strong><br/>Some methods (ABAP objects) that were modified and overwritten by the upgrade can be displayed in transaction SPAU with their names shortened to 30 characters.<br/>As a result, the system may also incorrectly sort methods in SPAU under \"Deleted objects\".<br/>Caution: Deleted objects are not displayed in the standard selection in SPAU. It is easily possible to overlook these!<br/>For more information about the correction, see <strong>Note 547773</strong>.<br/><br/>----------------------------------------------------------------------<br/><strong>Linux: Importing the new saposcol version</strong><br/>For more information, see <strong>Note 19227.</strong><br/><br/>----------------------------------------------------------------------<br/><strong>ReliantUNIX: saposcol version 32-bit or 64-bit</strong><br/>For more information, see <strong>Note 148926.</strong><br/><br/>----------------------------------------------------------------------<br/><strong>Solaris: saposcol version 32-bit or 64- bit</strong><br/>For more information, see <strong>Note 162980.</strong><br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>IX/ Chronological Summary</strong></p>\n<p><br/>Date.....Topic..Short description<br/>-----------------------------------------------------------------------<br/>SEP/24/08..III..SDK Version 1.4.x for Upgrade Assistant<br/>JAN/17/08...VI..Source Release SAP R/3 4.6C: Including a min. SP level<br/>APR/23/07..III..Migrate data from table TVARV to TVARVC<br/>MAR/02/07..VII..Phase JOB_RSTLANUPG: Termination<br/>NOV/28/06....V..Modification Adjustment Planning and Unicode Conversion<br/>OCT/19/06..III..Data Management Planning - Link to SMP<br/>SEP/15/06....V..Support Package SAPKB64018 - use corrected version<br/>JUN/21/06..III..Entries for Parameter Input Module: Path Length<br/>MAY/16/06..III..AIX: Isolating the Central Instance<br/>27/APR/06...VI..Handling of customer translation in the upgrade<br/>JAN/31/06..III..Windows: Space Requirements for MS SQL Database<br/>JAN/26/06..VII..Phase XPRAS_UPG: LONGPOST.LOG -  XPRA RMCSXP03<br/>DEC/12/05...VI..SD Texts: Check Text Customizing on Source Release<br/>NOV/29/05...II..Upgrade on Linux x86_64: Correct R3up Version<br/>OCT/10/05...VI..Latest Upgrade Repairs for Correct Language Import<br/>15/SEP/05....V..Back up customer-specific entries in table EDIFCT<br/>15/SEP/05.VIII..Run Report Z_873466_REPAIR_AFTER_UPGRADE<br/>05/SEP/05..VII..Problem with DVD mount paths<br/>AUG/16/05....V..Add-On strategy for ST-PI: Do not use Delete<br/>09/AUG/05..III..Opt. Follow-Up: Where-Used List<br/>JUN/02/05....V..Do not include ABA Support Package 12 in the Upgrade<br/>APR/20/05..VII..Phase RFCCHK_INI: Name or Password is Incorrect<br/>APR/06/05..III..Names of DVDs needed - Section \"Upgrade - Step by Step\"<br/>MAR/18/05..III..Documentation: SAP Software on UNIX - OS Dependencies<br/>MAR/11/05.VIII..Ignore or delete view VFAS<br/>MAR/09/05..III..Windows Guide Section on Database-Specific Parameters<br/>FEB/15/05.VIII..Non-IS-BEV Users: Deactivate Beverage Solution<br/>FEB/02/05...II..LSWM now part of SAP_BASIS<br/>JAN/28/05.VIII..Source Release 6.20: Missing DB View STWB_INFO<br/>JAN/21/05..VII..Phase XPRAS_UPG: Error S&gt;801<br/>14/OCT/04....V..Component PS-ST-WBS: Prel. Inclusion of Field KIMSK<br/>14/OCT/04....V..Source Rel. 4.0B: Project-Related Incoming Orders<br/>14/OCT/04...VI..Source Rel. 3.1I: Loss of Address Data<br/>06/OCT/04...VI..Source Release 4.6C and below: SMODILOG Entries<br/>SEP/09/04...VI..Source Release Extension Set 1.10: Exchange containers<br/>AUG/20/04..VII..Phase: DIFFEXPDDIV<br/>AUG/10/04..VII..Phase JOB_RDDNTPUR: TA22EQU_WAO without reference<br/>JUL/26/04..VII..Start-Rel. 3.1I / 4.0B: Phase TR_CMDIMPORT_FDTASKS<br/>JUL/08/04.VIII..Source Release lower than 6.10: Codepage conversion<br/>APR/19/04...VI..Unicode Systems: Downward Compatible Kernel 6.40<br/>19/MAR/04..VII..Phase TABIM_POST: Error DI829<br/>JAN/27/04...VI..Upgrading with reintegrated add-ons (retrofit)<br/>JAN/27/04...VI..Upgrading with PI/PI-A<br/>DEZ/03/03...II..Windows only: Execute program R3dllins.exe<br/>SEP/17/03....I..R3up keyword<br/>APR/24/03...VI..Upgrade on AIX: saposcol<br/>DEC/10/02...VI..Upgrading with AIX 5.1<br/>AUG/23/02.VIII..SPAU: Names of interface methods are truncated<br/>JUL/23/02 ...I..Windows: Windows XP is not supported<br/>JUL/19/02...II..Problems with the shadow instance<br/>MAY/24/02...II..Corrections and repairs for the upgrade<br/>MAY/23/02..VII..Phase CHK_POSTUP - objects without DDIC reference<br/>APR/11/02..VII..Termination in the TOOLIMPD3 phase<br/>APR/08/02..III..Preparation for reading the upgrade CDs<br/>FEB/20/02...VI..Source releases on UNIX 32-bit or AIX 64-bit<br/>OCT/19/00.VIII..Linux:  Importing the new saposcol version<br/>SEP/04/00..VII..Windows NT: Error message during PREPARE<br/>FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Reliant UNIX<br/>FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Solaris<br/>----------------------------------------------------------------------</p></div>", "noteVersion": 40}, {"note": "401717", "noteTitle": "401717 - Additional info on upgrading to SAP Web AS 6.10 (Basis)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>There are errors in the upgrade procedure or in the upgrade guide; you are preparing to upgrade.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Upgrade, migration, release update, release, maintenance level, R3up, PREPARE</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>*</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><b>CAUTION:</b><b> </b><b>This note is updated on a regular basis.</b><br/><b>Therefore, read it again immediately before the upgrade.</b><br/></p> <b>What can I expect from this note?</b><br/> <p>The note describes problems that may occur during the system upgrade and gives you information on how to solve them. This usually takes the form of references to other notes.<br/>This note focuses on preventing data loss, upgrade shutdowns and long runtimes.<br/>Only database-independent problems are described here.<br/></p> <b>What should I not expect from this note?</b><br/> <p>Problems occurring after the upgrade are only dealt with if they are directly caused by the upgrade tools.<br/></p> <b>Which notes do I also need in preparation for the upgrade?</b><br/> <p>This depends on the functions that you are using. You will probably need one or several of the following notes:<br/><br/>Short text.......................................... Note number<br/>_____________________________________________________________________<br/>Additional information on upgrading to Enterprise Buyer 3.0/CRM 3.0 .....................................................407453<br/>_____________________________________________________________________<br/>Additional information on upgrading to SAP Web AS 6.10 SAP DB ...........................................................398332<br/>Additional information on upgrading to SAP Web AS 6.10 iSeries .........................................................413107<br/>Additional information on upgrading to SAP Web AS 6.10 DB6 ..............................................................401849<br/>Additional information on upgrading to SAP Web AS 6.10 DB2 UDB for OS/390 ...............................................407283<br/>Additional information on upgrading to SAP Web AS 6.10 MS SQL Server ....................................................427307<br/>Additional information on upgrading to SAP Web AS 6.10 Oracle ...........................................................401721<br/>Additional information on the upgrade - general Informix note ...........................................................327030<br/>_____________________________________________________________________<br/>Current note on the language import 6.10 ............394109<br/>Language import and Support Package .................352941<br/>_____________________________________________________________________<br/>Information and corrections for upgrade to systems with Basis 6.10 .....................................................438266<br/>Repairs for upgrade to Basis 6.10....................438204<br/><br/>---------------------------------------------------------------------<br/></p> <b>Contents</b><br/> <p>I/ ......R3up keyword<br/>II/ .....Important general information<br/>III/ ....Corrections to the guide<br/>IV/ .....Errors on the CD-ROM<br/>V/ ......Avoiding loss of data, upgrade shutdown, and long<br/>         runtimes<br/>VI/ .....Preparing for the upgrade<br/>VII/ ....Problems in the PREPARE and upgrade phases<br/>VIII/ ...Problems after the upgrade<br/>IX/ .....Chronological summary<br/></p> <b>I/ R3up keyword</b><br/> <p><br/>-----------------------&lt; D023536 MAY/08/01 &gt;--------------------<br/>The R3up keyword is:     135999<br/>You must enter this keyword the first time you call R3up.<br/>----------------------------------------------------------------------<br/><br/><br/><br/></p> <b>II/ Important general information</b><br/> <b></b><br/> <p>-----------------------&lt; D025323 SEP/20/02 &gt;-------------------------<br/><b>Windows NT only:</b><b> </b><b>checking tp version</b><br/>Make sure that there is no tp Version 305.12.58 (tp for the source release, for source release 4.6x) in the directory \\usr\\sap\\&lt;sid&gt;\\sys\\exe\\run.<br/>Use either a more recent version or the previous version, otherwise errors occur during the upgrade.<br/>For more information, see <b>Note 537347</b>.<br/><br/>-------------------&lt; Amended by D025323 MAY/28/02 &gt;-----------<br/>-----------------------&lt; D022030 OCT/02/01 &gt;--------------------<br/><b>Corrections and repairs for the upgrade</b><br/>Before performing the upgrade, you must check the following two points for your particular upgrade</p> <ul><li>Is there a new version of R3up? For details, see <b>Note 438266.</b></li></ul> <ul><li>Are there repairs to the upgrade ABAP programs? For details, see <b>Note 438204</b>.</li></ul> <p><br/>-----------------------&lt; D025323 APR/23/01 &gt;--------------------<br/><b>Using the correct versions of tp and R3trans</b><br/>It is absolutely essential that you use the versions of the programs tp and R3trans that are required by R3up. Using an older version may require extensive reworking in the system.<br/><br/>------------------------&lt; D022030 APR/10/02 &gt;-------------------<br/>------------------------&lt; D030326 MAY/07/02 &gt;-------------------<br/><b>Importing the latest SPAM version:</b><br/>Before the PREPARE, always import the latest SPAM version for your source release. The reasons for this include the following:</p> <ul><li>In Source Release 4.x, SPAM 23 Version 31 causes a termination in the phase BIND_PATCH. To prevent this termination, you require at least one version higher.</li></ul> <ul><li>As of Source Release 4.0B, the queue calculator of the Support Package Manager (SPAM) is called to import support packages. To ensure that the current version of the queue calculator exists in your system, import the latest SPAM version for your source release before starting the PREPARE.</li></ul> <p>Importing a SPAM update during the PREPARE phase may cause terminations during the upgrade. If you have already started the PREPARE phase, reset it and restart the PREPARE phase.<br/><br/>-----------------------&lt; D000706 JUL/01/02 &gt;--------------------<br/><b>Changing to DCK 6.20 after the upgrade</b><br/>After the upgrade to Web AS 6.10, it may be a good idea to change to the downward-compatible kernel for Release 6.20.<br/>Further information is available in <b>Note 502999.</b><br/><br/>-----------------------&lt; D025323 JAN/09/02 &gt;--------------------<br/><b>Importing Basis Support Package 10</b><br/>You can only import this Support Package into the upgrade if you import the most recent repairs for the upgrade (see above).<br/><br/>------------------------&lt; D034624 MAR/04/02 &gt;-------------------<br/><b>Error in qRFC Version 6.10.043</b><br/>When you load transaction data from an SAP system into a BW system in the delta mode, data inconsistencies may occur with qRFC Version 6.10.043. If you are using this function, you must be above/below a certain Support Package level during a source system upgrade. If you use this function, you must exceed or fall short of a specific Support Package level when you upgrade the source system.<br/>For more information, see <b>Note 498484.</b><br/><br/>-----------------------&lt; D022030 OCT/02/01 &gt;--------------------<br/><b>Replacing the tool tp</b><br/>Refer to the points in section V.<br/><br/>-----------------------&lt; D028310 JUL/19/02 &gt;--------------------<br/><b>Problems with the shadow instance</b><br/>The following notes contain information about problems with the shadow instance:</p> <ul><li><b>525677</b>: Problems when starting the shadow instance</li></ul> <ul><li><b>500181</b>: Strategy change from resource-minimized to downtime-minimized</li></ul> <ul><li><b>430318</b>: Remote shadow instance on other operating systems</li></ul> <p><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p> <b>III/ Corrections to the guides</b><br/> <p><br/>------------------------&lt; D030328 AUG/09/05 &gt;-------------------------<br/><b>Optional postprocessing: Where-used list in the workbench</b><br/>As of Release 6.10, the where-used list for dictionary objects changes. If you require a precise list display, start the report SAPRSEUB after the upgrade.<br/>Since the report may run for a very long time, we recommend that you run it in the development system only.<br/>For further information, see Notes <b>401389</b> and <b>28022</b>.<br/><br/>------------------------&lt; D001330 OCT/20/04 &gt;-------------------------<br/><b>Section:</b><b> </b><b>Phase JOB_RSVBCHCK2</b><br/>The text should be:<br/>\"If errors are reported for this phase and you are <b>still</b> in production operation, you can bypass these errors without a password by using \"Ignore\".<br/><br/>-----------------------&lt; D022030 JUL/06/04 &gt;-------------------------<br/><b>Phase INITSHD: Instance number of the shadow instance</b><br/>You will find further information about selecting the shadow instance number in <b>Note 29972</b>.<br/><br/>-----------------------&lt; D022030 JAN/09/02 &gt;--------------------<br/><b>Section:</b><b> </b><b>Using another upgrade directory</b><br/>The restriction of the directory name to a length of no more than 18 characters, which is described in the chapter \"Upgrade administration\" in the section \"Other upgrade- use directory\", has been removed. The directory path can now be a maximum of 94 characters in length and individual file names can be a maximum of 40 characters in length.<br/><br/><br/>-----------------------&lt; D023536 OCT/17/01 &gt;--------------------<br/><b>For UNIX only:</b><b> </b><b>Shadow instance on another UNIX system </b><br/>If you select the option of operating the shadow instance on another application server (remote) in the phase INITSHD in the PREPARE module installation, and you are using a different (UNIX) operating system on this server, refer to <b>Note 430318</b>.<br/><br/>-----------------------&lt; D022030 AUG/28/01 &gt;--------------------<br/><b>For deliveries up to and including August 31, 2001</b><br/>Section \"Evaluate ELG log files\" under \"General upgrade sequence\" -&gt; \"Problems and solutions during the upgrade\":<br/>Change the command for unlocking and locking the system. To do this, proceed as follows:<br/>1. Unlock the SAP system using the following commands, where &lt;transport profile&gt; is the transport profile of the transport domain to which the system is connected:<br/> cd /&lt;upgrade directory&gt;/bin<br/> ..exe/tp unlocksys &lt;SAPSID&gt; pf=&lt;transport profile&gt;<br/> ..exe/tp unlock_eu &lt;SAPSID&gt; pf=&lt;transport profile&gt;<br/>2. Log onto the SAP system and eliminate the errors.<br/>3. Lock the SAP system again with the following commands:<br/> cd /&lt;upgrade directory&gt;/bin<br/> ..exe/tp locksys &lt;SAPSID&gt; pf=&lt;transport profile&gt;<br/> ../exe/tp lock_eu &lt;SAPSID&gt; pf=&lt;transport profile&gt;<br/><br/><br/>-----------------------&lt; D022030 AUG/28/01 &gt;--------------------<br/><b>For deliveries up to and including August 31, 2001</b><br/>Section \"Upgrade in an MCOD environment\" under \"General upgrade sequence\" -&gt; \"Planning the upgrade\".<br/>Up to the insert for SAP DB, replace the procedure with:<br/>When you upgrade an MCOD system, you must observe the following points:</p> <ul><li>The names of the database user as well as the tablespaces, dbspaces or Devspaces of an MCOD system differ from the names of an MCOD system without MCOD layout or a non-MCOD system. The names to be entered for systems with normal or MCOD layout are listed at appropriate points in the text.</li></ul> <ul><li>Archiving should not be deactivated during the upgrade in an MCOD system landscape. If you need to reset the upgrade, all SAP systems on the database are affected. At the start of the upgrade, the PREPARE asks you whether more than one SAP system is installed in the database. If this is the case, you should not deactivate the archiving during the upgrade.</li></ul> <p><br/><br/>-----------------------&lt; D022030 JUL/31/01 &gt;-------------------------<br/><b>For deliveries prior to August 31, 2001 and as of Source Release 4.0B</b><br/>Section \"Import latest SPAM update\" under \"General upgrade sequence\" -&gt; \"Preparing for the upgrade\":<br/>Before starting the PREPARE, import the latest SPAM update for your <b> source</b>release.<br/>----------------------------------------------------------------------<br/><br/><br/><br/></p> <b>IV/ Errors on the CD-ROM</b><br/> <p><br/><br/><br/><br/><br/></p> <b>V/ Preventing data loss, upgrade shutdowns and long runtimes</b><br/> <p><br/>------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br/><b>Saving customer-specific entries in the table </b><b>EDIFCT</b><br/>If you have made customer-specific entries in the table EDIFCT, they may disappear during the upgrade. If you would like to keep the entries, export them before the upgrade and import them into the system again after the upgrade.<br/>For further information, see Note <b>865142</b>.<br/><br/>------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br/><b>Saving customer-specific entries in the table </b><b>EDIFCT</b><br/>If you have made customer-specific entries in the table EDIFCT, they may disappear during the upgrade. If you would like to keep the entries, export them before the upgrade and import them into the system again after the upgrade.<br/>For further information, see Note <b>865142</b>.<br/><br/>------------------------&lt; D001330 20/OKT/04 &gt;-------------------------<br/><b>Function groups in the customer namespace</b><br/>If you have created function groups in the customer namespace, the upgrade can result in loss of data.<br/>More information is available in Note <b>783308.</b><br/><br/>-----------------------&lt; D020815 13/FEB/04 &gt;--------------------------<br/><b>SPDD - Resetting to standard</b><br/>When you select \"Reset to standard\" in Transaction SPDD for data elements that have already been adjusted, activation starts instead. If you want to reset adjusted data elements, follow the steps described in<b> Note 705943</b>.<br/><br/>-----------------------&lt; D030326 NOV/21/02 &gt;--------------------------<br/><b>Maintaining technical settings in the phase ACT_&lt;Rel&gt;</b><br/>If you log on to the shadow system during the phase ACT_620 to perform the modification adjustment or to eliminate activation errors, make sure that you &lt;UK Char&gt;only save and do not activate the settings when you maintain the technical settings of a table.<br/>Since the activation of technical settings updates the DDIC runtime object, you may not be able to execute programs that reference this table following activation. This can result in the shadow system being destroyed and the upgrade having to be reset.<br/><br/>-----------------------&lt; D019989 APR/10/02 &gt;--------------------<br/><b>Prefix namespace with namespace role C</b><br/>If you have a reserved namespace with namespace role 'C', you must change the namespace roles of the objects for the duration of the upgrade in order to prevent objects from being lost.<br/>For more information, see <b>Note 509331</b>.<br/><br/>------------------------&lt; D001330 MAR/21/02 &gt;-------------------<br/>------------------------&lt; D030326 MAY/07/02 &gt;-------------------<br/><b>Importing Support Packages and SPAM updates after or during the PREPARE phase</b><br/>If you must import additional Support Packages after or during the PREPARE phase into the system (for example, due to PREPARE information), you MUST reset and repeat the PREPARE phase after importing.<br/>For more information, see <b>Note 505754</b>.<br/>The same also applies when you import SPAM updates during the PREPARE phase. Here, you MUST also reset and repeat the PREPARE phase.<br/><br/>------------------------&lt; D025323 DEC/20/01 &gt;-------------------<br/>------------------------&lt; D025323 NOV/30/01 &gt;-------------------<br/><b>Replace tp</b><br/>Replace the tp in the directory &lt;Dir_Put&gt;/exe <b>after the PREPARE</b> and before the upgrade. By doing so, you avoid the following problems:</p> <ul><li>With deliveries that have a tp with a version number lower than 310.56.27, system performance can be significantly impaired due to an incorrectly set indicator. For more information, see <b>Note 456521</b>.</li></ul> <ul><li>With deliveries that have a tp with a version number lower than 310.56.21 (BW 3.0A FCS / EBP-CRM 3.0 FCS and GA), terminations may occur in a DIFFEXP* phase. For more information, see <b>Note 446828</b>.</li></ul> <p><br/>------------------------&lt; D024828 DEC/11/01 &gt;-------------------<br/><b>No ICNV during the upgrade</b><br/>ICNV should not be executed during the upgrade to SAP Web AS 6.10. You should ignore the prompt by R3up to start the transaction in the phase ICNV_REQ.<br/><br/>-----------------------&lt; D000706 JUN/23/04 &gt;-------------------------<br/>-----------------------&lt; D025323 NOV/27/01 &gt;-------------------------<br/>-----------------------&lt; D025907 24/OCT/01 &gt;-------------------------<br/>-----------------------&lt; D025323 25/SEP/01 &gt;-------------------------<br/><b>Import the latest R3trans patch</b><br/>After the PREPARE and <b>before you start the upgrade,</b> download the latest R3trans for Release 6.10 from the SAP Service Marketplace and replace the delivered R3trans with it in the directory &lt;dir_put&gt;/exe.<br/>By doing this, you avoid the following problems:</p> <ul><li>Upgrade phase XTERN_CNV: Entries from client-specific tables are not transported. For more information, see <b>Note 746476</b>.</li></ul> <ul><li>Join conditions do not disappear during the upgrade. For more information, see <b>Note 444045.</b></li></ul> <ul><li>Termination in the phase Shadow_Import_ALL. For more information, see <b>Note 442193.</b></li></ul> <ul><li>'duplicate key errors' during the import. For more information, see <b> Note 429474</b>.</li></ul> <p><br/>-----------------------&lt; D025323 SEP/28/01 &gt;--------------------<br/><b>EBP/CRM 3.0 and BW 3.0A with customer development classes only:</b><b></b><br/><b></b>If you have customized development classes in the customer namespace in your system, these must be copied in the phase DIFFEXPCUST.<br/>For more information on the parameter changes, see<b> Note 438946.</b><b></b><br/><br/><b>-----------------------&lt; D022030 SEP/21/01 &gt;-------------------------</b> <b></b><br/><b>For SUN-OS only, 32-bit:</b><br/>tp versions lower than 310.56.08 end up in an endless loop in the phase KX_SWITCH. For more information, see <b>Note 436599</b> (see also Section VII).<br/>----------------------------------------------------------------------<br/><br/><br/></p> <b>VI/ Preparing for the upgrade</b><br/> <p><br/>-----------------------&lt; D022030 DEC/06/01 &gt;--------------------<br/><b>For all databases:</b><b> </b><b>OS/DB Version before PREPARE</b><br/>Before starting PREPARE, the system must be upgraded to the operating system and database version required for the target release.<br/><br/>-----------------------&lt; D021371 OCT/12/01 &gt;--------------------<br/><b>For AIX only:</b><br/>For an upgrade under AIX, you require at least the AIX Application Runtime Version *******.<br/>Use the following command: lslpp -l xlC.rte<br/>to check whether the C set ++ is installed.<br/>If it is not installed or if the incorrect version is installed, download it from the country-specific version of the IBM AIX Fix Distribution Internet page or from the United States page http://service.software.ibm.com/support/rs6000<br/>Install it using smit (-&gt; Software) as root user.<br/>If you also need the \"Base Level Fileset\" (xlC.rte.5.0.0.0), download the file vacpp5_runtime.tar.Z from the page ftp://ftp.software.ibm.com/aix/products/ccpp/<br/><br/><br/>-----------------------&lt; D022030 JUL/31/01 &gt;-------------------------<br/><b>See also the entry under III:</b><br/><b>For deliveries before August 31, 2001 and as of Source Release 4.0B </b><br/>Guide section \"Import the latest SPAM update\" under \"General upgrade sequence\" -&gt; \"Preparing for the upgrade\": Before starting the PREPARE, import the latest SPAM update for your <b> source</b> release.<br/><br/>------------------------&lt; D020847 JUN/27/00 &gt;-------------------<br/><b>See also the entry under VII:</b><br/><b>For Windows NT only:</b><br/>For Source Release 4.6A/4.6B, you require at least kernel patch level 103.<br/><br/>----------------------------------------------------------------------<br/><br/><br/></p> <b>VII/ Problems in the PREPARE and upgrade phases</b><br/> <b></b><br/> <p>This section contains recognized problems that cannot be avoided by preventive measures. These problems can only occur under very specific conditions or preconditions.<br/></p> <b>Problems in the PREPARE phases</b><br/> <p><br/>------------------------&lt; D020847 SEP/04/00 &gt;-------------------------<br/><b>For Windows NT 4.0 only</b><br/>If you get a dialog box with the following error message during the PREPARE: 'The procedure entry point ... could not be located in the<br/> dynamic link library ...  .'<br/>use the program R3DLLINS.EXE to import the latest DLLs. The program is in the directory \\NT\\&lt;processor type&gt;\\NTPATCH on the SAP Kernel CD.<br/>Reboot your machine and restart PREPARE with 'PREPARE repeat' again.<br/><br/>-----------------------------------------------------------------------<br/><b>For RELIANT UNIX only:</b><br/>SAPCAR does not start in the PREPARE if the RELIANT UNIX version is lower than 5.44 and the version of the C++ runtime environment CDS++RTS is lower than 2.0. For more information, see <b>Note 304809</b>.<br/><br/>------------------------&lt; D020847 JUN/27/00 &gt;-------------------<br/><b>For Windows NT only:</b><br/>With Source Release 4.6A/4.6B, you require at least kernel patch level 103. Otherwise you get the error \"Time limit exceeded\". In this case, import a kernel with a higher patch level and repeat the phase.<br/><br/>-----------------------------------------------------------------------<br/><b>As of Source Release 4.0A:</b><br/>Termination in the phases TOOLIMPD1 or TOOLIMPD2. For more information, see <b>Note 98198</b>.<br/>-----------------------------------------------------------------------<br/><br/><br/></p> <b>Problems in the upgrade phases</b><br/> <p><br/>------------------------&lt; D020815 MAY/15/02 &gt;-------------------<br/>Phase: ACT_610:<br/>Note: 519572<br/>Description: SPDD does not display all objects.<br/>When you carry out an upgrade from BW 3.0A, CRM 3.0 or CRM 3.0 SR1, start the correction report before you start the SPDD.<br/><br/>------------------------&lt; D001330 MAR/21/02 &gt;-------------------<br/>Phase: ACT_610:<br/>Note: 504892<br/>Description: Error during the SPDD comparison request<br/>When you save the SPDD order, error message TR067 appears, \"Enhanced transport control is not active: Target client cannot be specified.\"<br/><br/>-----------------------------------------------------------------------<br/>Phase: PARDIST_SHD<br/>Note: 73999<br/>Description: Upgrade phase PCON_610 or PARDIST_SHD: TG450 to TG453<br/><br/>------------------------&lt; D025988 JUN/16/00 &gt;-------------------<br/>Phase: PARDIST_SHD<br/><b>For Windows NT only:</b><br/>Upgrade termination in the phase PARDIST_SHD; an incomplete error text that was extracted from the log file DS&lt;date&gt;.&lt;SID&gt; is generated in the log file Pconupg.elg. Repeat the phase.<br/><br/>---------------------------------------------------------------------<br/>Phase: KX_SWITCH<br/>Description: <b>For SUN-OS only, 32-bit:</b><br/>tp versions lower than 310.56.08 end up in an endless loop in the KX_SWITCH phase. For more information, see <b>Note </b><b>436599 </b>(see also Section V).<br/><br/>------------------------&lt; D030326 JAN/15/02 &gt;-------------------<br/>Phase: RUN_RSCPFROM<br/>Description: Termination in phase RUN_RSCPFROM (\"RFC_ERROR_SYSTEM_FAILURE\") if you also include Basis Support Package 10 (SAPKB61010) in the upgrade. For more information, see <b>Note 486321</b>.<br/><br/>--------------------------&lt; D019416 MAR/19/04 &gt;------------------------<br/>Phase: TABIM_POST<br/>Note: 718912<br/>Description: The phase terminates with error message DI829. In this case, import the modification from the note and continue with the upgrade. You must undo the modification again after the upgrade.<br/><br/>--------------------------&lt; D025323 MAY/23/02 &gt;-----------------<br/>Phase: CHK_POSTUP<br/>Note: 197886<br/>Description: If you have imported Notes 178631, 116095 or 69455 into your system before the upgrade, error messages will appear in the log LONGPOST.LOG for objects without a DDIC reference. Proceed as described in the note.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p> <b>VIII/ Problems after the upgrade</b><br/> <b></b><br/> <p>This section contains recognized problems that cannot be avoided by preventive measures. These problems only occur under very specific conditions or preconditions.<br/><br/>-----------------------&lt; D035318 JUL/08/04 &gt;-------------------------<br/><b>Start release lower than Basis 6.10:</b><b> </b><b>Conversion of code pages</b><br/>The code page administration was extended for Basis Release 6.10. To be able to continue to use customer-designed (print-) code pages that begin with \"9\" after the upgrade, you must convert these after the upgrade using report RSCP0126.<br/>You will find further information in <b>Notes 485455</b> and <b>511732.</b><br/><br/>------------------------&lt; D020815 AUG/23/02 &gt;-------------------<br/><b>SPAU: Names of interface methods are truncated</b><br/>Some methods (ABAP objects) that were modified and overwritten by the upgrade may be displayed, shortened to 30 characters, in Transaction SPAU. As a result, the system may inadvertently sort methods in SPAU under \"Deleted objects\".<br/>Caution: Deleted objects are not displayed in Transaction SPAU when you use the standard selection. It is possible to overlook these deleted objects.<br/>For information about the correction, refer to<b> Note 547773.</b><br/><br/>-----------------------&lt; D025323 JUN/01/01 &gt;--------------------<br/><b>Before calling SE11:</b><b> </b><b>start report</b><br/>Before you can edit customer tables with Transaction SE11, you must start a postprocessing report. Call Transaction SE38 and start the report RADSETMNT. Start the online report and accept the default settings.<br/><br/>------------------------&lt; D022860 JAN/09/01 &gt;-------------------<br/><b>Incomplete path in .sapenv_&lt;hostname&gt;.sh</b><br/>For UNIX only: If you use the born or korn shell as the login shell for &lt;sapsid&gt;adm and the system can no longer start or stop, this may be due to an incomplete path specification in $HOME/.sapenv_&lt;hostname&gt;.sh.<br/>In the file $HOME/.sapenv_&lt;hostname&gt;.sh, change the row:<br/>           for d in /usr/sap/&lt;SAPSID&gt;/SYS/exe/run; do<br/>to<br/>           for d in /usr/sap/&lt;SAPSID&gt;/SYS/exe/run $HOME .; do<br/><br/>----------------------------------------------------------------------<br/><b>Linux:</b><b> </b><b>importing a new saposcol version</b><br/>For more information, see <b>Note </b><b>19227.</b><br/><br/>----------------------------------------------------------------------<br/><b>ReliantUNIX:</b><b> </b><b>saposcol version 32-bit or 64-bit</b><br/>For more information, see <b>Note 1</b><b>48926.</b><br/><br/>----------------------------------------------------------------------<br/><b>Solaris:</b><b> </b><b>saposcol version 32-bit or 64-bit</b><br/>For more information, see <b>Note</b> <b>162980.</b><br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p> <b>IX/ Chronological Summary</b><br/> <p><br/>Date.....Section.....Short description<br/>-----------------------------------------------------------------------<br/>SEP/15/05....V.......Saving customer-specific entries in table EDIFCT<br/>SEP/15/05....V.......Saving customer-specific entries in table EDIFCT<br/>09/AUG/05..III.......Opt. Postprocessing: Where-used list<br/>OCT/20/04..III.......Section: Phase JOB_RSVBCHCK2<br/>OCT/20/04....V.......Function groups in the customer namespace<br/>JUL/07/04.VIII.......Source release lower than 6.10: Conversion of code pages<br/>JUL/06/04..III.......Phase INITSHD: Instance number of the shadow instance<br/>JUN/23/04....V.......Import the latest R3trans<br/>MAR/19/04..VII.......Phase TABIM_POST: Error DI829<br/>FEB/13/04....V.......SPDD - Resetting to standard<br/>NOV/21/02....V.......Maintaining technical settings in the phase ACT_&lt;Rel&gt;<br/>SEP/20/02...II.......Windows NT only: checking tp version<br/>AUG/23/02.VIII.......SPAU: Names of interface methods are truncated<br/>JUL/19/02...II.......Problems with the shadow instance<br/>JUL/01/02...II.......Changing to DCK 6.20 after the upgrade (if necessary)<br/>MAY/28/02...II.......Corrections and repairs for the upgrade<br/>MAY/23/02..VII.......Phase CHK_POSTUP - objects without a DDIC reference<br/>MAY/15/02..VII.......SPDD does not display all objects<br/>MAY/07/02....V.......Importing Support Packages after/during the PREPARE phase<br/>APR/23/02...II.......Using correct tp and R3trans versions<br/>APR/15/02....V.......Do not include Basis Support Package 14<br/>APR/10/02...II.......Importing the latest SPAM update before PREPARE<br/>APR/10/02....V.......Prefix namespace with namespace role C<br/>MAR/21/02..VII.......Error during the SPDD comparison request<br/>MAR/21/02....V.......Importing Support Packages after PREPARE<br/>MAR/04/02...II.......Error in qRFC Version 6.10.043<br/>JAN/09/02..III.......Section: Using another upgrade directory<br/>DEC/20/01....V.......Replacing tp<br/>DEC/11/01....V.......No ICNV during the upgrade<br/>DEC/06/01...VI.......For all databases: OS/DB Version before PREPARE<br/>NOV/30/01....V.......For deliveries before December 2001: replace tp<br/>NOV/27/01....V.......Import R3trans patch<br/>OCT/24/01....V.......Import latest R3trans<br/>OCT/17/01..III.......For UNIX only: Shadow instance on the other UNIX system<br/>OCT/12/01...VI.......AIX Application Runtime Version *******.<br/>SEP/28/01....V.......EBP/CRM 3.0/BW 3.0A: customer development classes<br/>SEP/25/01....V.......Delivery before 22.08.01: R3trans incorrect<br/>SEP/21/01....V.......For SUN-OS only, 32-bit: incorrect tp version<br/>AUG/28/01..III.......Guide amendment: evaluate ELG log files<br/>AUG/28/01..III.......Guide amendment: Upgrade in a MCOD environment<br/>JUL/31/01..III.......As of Source Release 4.0B: SPAM update for source release<br/>JUN/01/01.VIII.......Before calling SE11: start report<br/>JAN/09/01.VIII.......Incomplete path in .sapenv_&lt;hostname&gt;.sh<br/>MAY/08/01....I.......R3up Keyword<br/>OCT/19/00.VIII.......Linux:  importing a new saposcol version<br/>SEP/04/00..VII.......Windows NT: error message in the PREPARE<br/>JUN/27/00..VII.......Windows NT/Source Release 4.6A/4.6B: min. kernel PL 103<br/>FEB/16/00.VIII.......saposcol Version 32-bit or 64-bit on ReliantUNIX<br/>FEB/16/00.VIII.......saposcol Version 32-bit or 64-bit on Solaris<br/>FEB/16/00..VII.......PREPARE/TOOLIMPD2: Termination with distribution error<br/>----------------------------------------------------------------------<br/></p></div>", "noteVersion": 81}, {"note": "661640", "noteTitle": "661640 - Add. info. on upgrading to SAP Web AS 6.40", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Errors in the upgrade procedure or in the upgrade guide; preparations for the upgrade</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Update, migration, upgrade, release, maintenance level, R3up, PREPARE<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>*</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/><b>CAUTION:</b><br/>This note is updated regularly!<br/>Therefore, you should read it again immediately before starting the upgrade.<br/><br/><br/></p> <b>What information can I expect from this note?</b><br/> <p>This note describes problems that may occur during the system upgrade and provides information on how to solve them. This usually takes the form of references to other notes.<br/>The main purpose of this note is to prevent data loss, upgrade shutdowns, and long runtimes.<br/>It deals with database-independent problems only.<br/><br/></p> <b>Which additional notes do I require in preparation for the upgrade?</b><br/> <p>This depends on the functions that you are using. You will need one or several of the following notes:<br/><br/>Short text................................................ Note number<br/>_____________________________________________________________________<br/>Additional information on upgrade to WAS 6.40 MaxDB........... 669656<br/>Additional Information: Upgrade to SAP Web AS 6.40............ 661569<br/>DB5: Additions to upgrade (based) on SAP Web AS 6.40.......... 662191<br/>DB2-z/OS: Additions upgrade to Basis 6.40 .................... 661252<br/>Enhancements to upgradeto WEBAS 6.40 with MS SQL.............. 669236<br/>Add. info. on upgrading to Web SAP AS 6.40 ORACLE ............ 662219<br/>Additional information for upgrading to Basis................. 647130<br/>_____________________________________________________________________<br/>Repairs for upgrade to Basis 640 ............................. 663240<br/>Corrections for for R3up Release 640 ......................... 663258<br/>_____________________________________________________________________<br/>OCS: Known problems with Supp.Packages in Basis Rel.6.40 ......672651<br/>_____________________________________________________________________<br/><br/></p> <b>Contents</b><br/> <p>I/ ...... R3up Keyword<br/>II/ ..... Important General Information<br/>III/ .... Corrections to the Guide<br/>IV/ ..... Errors on the CD-ROM<br/>V/ ...... Preventing Data Loss, Upgrade Shutdown, and Long Runtimes<br/>VI/ ..... Preparing the Upgrade<br/>VII/..... Problems During the PREPARE and Upgrade Phases<br/>VIII/ ... Problems After the Upgrade<br/>IX/ ..... Chronological Summary<br/><br/></p> <b>I/ R3up keyword</b><br/> <p><br/>-----------------------&lt; D023536 FEB/20/02 &gt;--------------------<br/>The R3up keyword is: 160421<br/>This must be entered the first time you call R3up.<br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p> <b>II/ Important General Information</b><br/> <p><br/><br/>-----------------------&lt; D025323 MAY/24/02 &gt;--------------------<br/><b>Corrections and Repairs for the Upgrade</b><br/>Before the upgrade, you must check whether a new release and version of the R3up exists for your specific upgrade.<br/>For more information, see <b>Note 663258</b>.<br/><br/>-----------------------&lt; D021371 NOV/29/05 &gt;--------------------<br/><b>Upgrade on Linux x86_64: Correct R3up Version</b><br/>For SAP systems installed on Linux x86_64, there is no 64Bit R3up version. You need to use R3up for Linux x86 32-Bit.<br/>For more information on how to use the R3up version in combination with the Kernel DVD, see Note <b>893352</b>.<br/><br/>-----------------------&lt; D034302 DEZ/03/03 &gt;--------------------<br/><b>Windows only: Execute program R3dllins.exe</b><br/>The 6.40 kernel is compiled with the new version of MS compiler and requires additional libraries for operation. To prevent problems during and after the upgrade, you must execute program R3dllins.exe on your central host, all application hosts, and on the remote shadow host, if you are planning to use one.<br/>You can find the program on the Upgrade Master CD in the NT\\I386\\NTPATCH folder. It must be executed before you start prepare and directly from the NTPATCH folder (it can be shared). Copying and executing the program<br/>will not work!<br/><br/>-----------------------&lt; D028310 JUL/19/02 &gt;--------------------<br/><b>Problems with the Shadow Instance.</b><br/>The following Notes contain information about problems with the shadow instance:</p> <ul><li><b>525677</b>: Problems when starting the shadow instance</li></ul> <ul><li><b>430318</b>: Remote shadow instance on a different operating system</li></ul> <p><br/>------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br/><b>LSMW now part of SAP_BASIS</b><br/>As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW and your source release is based on SAP Web AS 6.10 or lower, do not implement LSMW after the upgrade.<br/>For more information, see <b>Note 673066</b>.<br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p> <b>III/ Corrections to the Guides</b><br/> <p><br/>-----------------------&lt; D033903 15/SEP/08 &gt;--------------------------<br/><b>SDK Version 1.4.x for Upgrade Assistant</b><br/>The upgrade assistant only supports Java Software Development Kit (SDK) 1.4.x. It does not support version 1.5 or higher.<br/><br/>------------------------&lt; D030328 09/AUG/05 &gt;-------------------------<br/><b>Optional Follow-Up Activity: Where-used list in the Workbench</b><br/>As of release 6.10, the where-used list for Dictionary objects has changed. If you need a proper display of the list, you need to run report SAPRSEUB after the upgrade.<br/>As the runtime of the report may be quite long, we recommend that you run it in the development system only.<br/>For more information, see Notes <b>401389</b> and <b>28022</b>.<br/><br/>------------------------&lt; D028310 25/FEB/05 &gt;-------------------------<br/><b>Section \"SAP NW AS: J2EE Engine Installation Planning\"</b><br/>Please ignore this section! After the upgrade, proceed as described in section \"Installing the J2EE Engine\".<br/><br/>------------------------&lt; D028310 09/FEB/05 &gt;-------------------------<br/><b>Section: Making Entries for the Extension Module</b><br/>In subsection \"Phase IS_SELECT\" the following applies to add-ons with status \"Undecided\":<br/>\"Your software vendor has predefined the strategy to choose for each add-on.<br/>For more information, see the SAP Note displayed by R3up or contact your software vendor.\"<br/><br/>------------------------&lt; D001330 20/OCT/04 &gt;-------------------------<br/><b>Section: Phase JOB_RSVBCHCK2</b><br/>The text should say:<br/>\"If errors occur in this phase and you <b>are still in</b> production operation, you can skip these errors with ignore without entering a password\".<br/><br/>-----------------------&lt; D022030 JUL/06/04 &gt;--------------------<br/><b>Phase INITSHD: Instance Number of the Shadow Instance</b><br/>For more information on choosing the instance number for the shadow instance, see <b>note 29972</b>.<br/><br/>-----------------------&lt; D022030 MAY/07/04 &gt;--------------------<br/><b>Implementing the Integrated SAP ITS</b><br/>The text \"Meeting the Requirements for the SAP Internet Solutions\" does not contain a procedure for implementing the integrated ITS.<br/>If you want to use it, proceed as follows:</p> <ol>1. After the upgrade of the system, configure the integrated ITS as described in the online documentation under Application Platform (SAP Web Application Server) -&gt; ABAP Technology -&gt; UI Technology -&gt; Web UI Technology -&gt; ITS/SAP@Web Studio -&gt; SAP ITS in the SAP Web Application Server -&gt; Configuration.</ol> <ol>2. Migrate the applications as described in the document \"Migration of Existing ITS Services\" on SAP Service Marketplace at service.sap.com\\sap-its -&gt; Media Library -&gt; Literature.</ol> <p><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p> <b>IV/ Errors on the CD-ROM</b><br/> <p><br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p> <b>V/ Preventing Data Loss, Upgrade Shutdown, and Long Runtimes</b><br/> <p><br/>-----------------------&lt; D028597 15/SEP/06 &gt;--------------------------<br/><b>Support Package SAPKB64018 - use corrected version</b><br/>If you include Support Package SAPKB70009 in the upgrade, make sure to use the corrected version, indicated by EPS file name CSN0120061532_0024351.PAT.<br/>If you use the old version, phase XPRAS_UPG returns an error on the after import method SRM_FILL_KC_TABLES_AFTER_IMP. In this case, proceed as described in Note <b>967821</b>.<br/>For more information about the problem in general, see Note <b>672651 </b>.<br/><br/>------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br/><b>Back up customer-specific entries in table EDIFCT</b><br/>If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br/>For more information, see Note <b>865142</b>.<br/><br/>------------------------&lt; D030559 02/JUN/05 &gt;-------------------------<br/><b>Do not include ABA Support Package 12 in the Upgrade</b><br/>If you included Support Package SAPKA64012 in the upgrade, the upgrade fails in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br/>To prevent the failure, only include the Suport Package in the upgrade if you can also include at least Support Package <b>SAPKA64013</b> as the problem will be fixed with SP 13.<br/>For more information - also on the procedure in case of an upgrade failure, see <b>note 849925</b>.<br/><br/>------------------------&lt; D001330 20/OCT/04 &gt;-------------------------<br/><b>Function Groups in the Customer Name Space</b><br/>If you have created function groups in the customer namespace, you may lose data during the upgrade.<br/>For more information, see Note <b>783308</b>.<br/><br/>-----------------------&lt; D020815 13/FEB/04 &gt;--------------------------<br/><b>SPDD - Return to Standard</b><br/>If you choose \"return to standard\" in transaction SPDD for data elements for which you have already performed the modification adjustment, the activation of the data elements starts. If you want to return adjusted data elements to the standard, proceed as described in <b>Note 705943 </b>.<br/><br/>--------------------------&lt; D030559 27/JAN/04 &gt;-----------------------<br/><b>Import of Asian Languages: Change System Language</b><br/>If you are importing Asian languages during the upgrade, you may get an error message during phase STARTSAP_TRANS or XPRAS_UPG (program RADBTLOG).<br/>In order to prevent this problem, change your system language to \"English\" <b>before the upgrade</b>: Set instance parameter zcsa/system_language = E<br/>and restart the SAP system.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p> <b>VI/ Preparing the Upgrade</b><br/> <p><br/>-----------------------&lt; D001330 27/APR/06 &gt;--------------------------<br/><b>Handling of customer translation in the upgrade</b><br/>Z languages or customer translations on system texts with transaction SE63 are not considered as modifications to the system by the upgrade and are therefore lost during the upgrade. As the SAP system may change considerably from one release to the next, it may not be worth saving the translations.<br/>If you think that it is worth saving your translations or languages, see Note <b>485741</b> for more information.<br/><br/>--------------------------&lt; D001330 10/OCT/05 &gt;-----------------------<br/><b>Apply the Latest Upgrade Repairs for Correct Language Import</b><br/>If you do not apply the latest repairs for the upgrade as described in Note 663240, the language import from the data dictionary will be incomplete.<br/>For more information, see Note <b>885955</b>.<br/><br/>------------------------&lt; D034302 09/JUL/04 &gt;------------------------<br/><b>Windows only: Shut Down IGS Before Starting PREPARE</b><br/>When you upgrade your system from Source Release 6.20, you have to make sure that the IGS is not running when you start PREPARE. Otherwise you will get the following error message in module General Checks: The LIBRFC32.DLL cannot be updated<br/>To shut down the IGS, proceed as follows:</p> <ul><li>Non-cluster installations: Kill the process as described in <b>Note 737099</b>.</li></ul> <ul><li>Windows Cluster installations:</li></ul> <ol><ol>a) Turn the following lines in you start profile into a comment:</ol></ol> <p>                       _IGS=igswd.exe Start_Program_06=local $(DIR_INSTANCE)\\igs\\bin\\$(_IGS) (this is one line) <p>                       -dir=$(DIR_INSTANCE)\\igs -mode=all <ol><ol>b) Restart the system. Restarting the system cannot be avoided in this case!</ol></ol> <p><br/>------------------------&lt; D038245 09/JUL/04 &gt;------------------------<br/><b>Adjust Profile Parameter \"rsts/ccc/cachesize\"</b><br/>This error has been fixed with the latest R3up version!<br/>Check whether profile parameter \"rsts/ccc/cachesize\" has been set. If yes, either delete the parameter or set it to 6.000.000 bytes as described in <b>note 5470</b>.<br/>Otherwise, you get the follwoing errors in several of the upgrade phases (e.g. PARCONV_UPG, XPREAS_UPG): Runtime error \"IMPORT_INIT_CONVERSATION_FAILED\"<br/>The sys log contains the message: \"E 14 Reorganized Buffer RSCPCCC with Length xxxxx (Overflow)\"<br/>The error also occurs in phases UVERS_CHK and START_SHDI_FIRST. Here you get a segmantation fault or Dr. Watson dump.<br/><br/>------------------------&lt; D038245 19/APR/04 &gt;------------------------<br/><b>Unicode Systems: Downward Compatible Kernel 6.40</b><br/>When you are using the normal kernel for Release 6.20 with your Unicode system, PREPARE issues the error message: Could not open the ICU common library.<br/>Before you start PREPARE, install the Downward Compatible Kernel for Release 6.40. Until this kernel is available, proceed as described in <b>Note 716378</b>.<br/><br/>------------------------&lt; D035318 04/FEB/04 &gt;------------------------<br/><b>Unicode Systems: Report RUTTTYPACT</b><br/>If your system is a unicode system, check whether you have run report RUTTTYPACT in your system after the installation. If you have not run the report, do so before you start PREPARE. To run the report, proceed as described in <b>note 544623</b>.<br/><br/>--------------------------&lt; D034302 16/JAN/04 &gt;-----------------------<br/><b>Java Application Server</b><br/>If you have Java Application Server installed and running together with your ABAP system, problems can occur when the upgrade program tries to exchange executables and libraries. The problems can occur during PREPARE module General Checks as well as during upgrade phase KX_SWITCH.<br/>To prevent these problems, shut down the Java Application Server during the upgrade.<br/><br/>--------------------------&lt; D022030 22/MAR/04 &gt;-----------------------<br/><b>Database Archives During the Upgrade</b><br/>Knowing the size of the archives written during the upgrade may help you select an upgrade or archiving strategy. The first of the two numbers specifies the size of the archives up to phase MODPROF_TRANS, while the second specifies the size of the archives up to the end of the upgrade.<br/>DB2 UDB for UNIX/Windows: 6 GB / 8 GB<br/>Informix: 8 GB / 10 GB<br/>MS SQL Server: 7 GB / 8.5 GB<br/>Oracle: 9.5 GB / 10.5 GB<br/>MaxDB: 5 GB / 6 GB<br/>These sizes are based on <b>sample data</b>.<br/><br/>--------------------------&lt; D022030 22/MAR/04 &gt;-----------------------<br/><b>Space Requirements in the Database</b><br/>You must enhance the database both during and because of the upgrade. The numbers specify the enhancement required temporarily during the upgrade.<br/>DB2 UDB for UNIX/Windows: 17 GB<br/>Informix: 18 GB<br/>iSeries: 20 GB*<br/>MS SQL Server: 9 GB<br/>Oracle: 17 GB<br/>MaxDB: 5.5 GB<br/>* largely depends on the upgrade and archiving strategy because when using upgrade strategy archiving_on, a rather big number of journal receivers may arise during the upgrade.<br/>These sizes are based on sample data. Additional freespace requirements (depending on your system) are calculated during the PREPARE.<br/>To determine realistic freespace requirements, you should execute PREPARE.<br/>After the upgrade, you can free space by deleting substitution tablespaces or removing superfluous database objects. For more information on how to proceed, see the Upgrade Guide under \"Post-Upgrade Activities\".<br/><br/>--------------------------&lt; D025323 24/APR/03 &gt;-----------------------<br/><b>Upgrade on AIX: saposcol</b><br/>Refer to Note <b>526694</b> before the upgrade.<br/><br/>--------------------------&lt; D019926 DEC/10/02 &gt;-----------------------<br/><b>Upgrading with AIX 5.1</b><br/>If you want to upgrade with AIX 5.1, see <b>Note 502532</b> before starting PREPARE.<br/><br/>-----------------------&lt; D025323 FEB/20/02 &gt;--------------------------<br/><b>Source Releases on UNIX 32-bit or AIX 64-bit</b><br/>In some cases, you may have to upgrade the operating system to 64-bit before the actual upgrade.<br/>When you upgrade from AIX 4.3 64-bit, you must perform some additional actions before upgrading.<br/>For more information, see <b>Notes 496963</b> and <b>499708</b>.<br/><br/>-----------------------------------------------------------------------<br/><br/><br/><br/></p> <b>VII/ Problems During the PREPARE and Upgrade Phases</b><br/> <p><br/>This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.<br/></p> <b>Problems During the PREPARE Phases</b><br/> <p><br/>-----------------------&lt; D038245 20/APR/05 &gt;---------------------------<br/><b>Phase RFCCHK_INI: Name or Password is Incorrect</b><br/>In phase RFCCHK_INI you may get the error message \"Name or Password is Incorrect\".<br/>If you replaced R3up after phase RFCCHK_INI with the latest version from SAP Service Marketplace, this error may also come up in other phases that connect to the system using RFC.<br/>For more informationen, see Note <b>792850</b>. You may have to replace disp+work and restart the system.<br/><br/>-----------------------&lt; D028310 NOV/03/04 &gt;--------------------<br/><b>Phase CONFCHK_IMP on Distributed Systems</b><br/>Phase CONFCHK_IMP offers you a list of operating systems to select from. This list only contains one entry \"Linux\" which is valid for both Linux and Linux IA64.<br/><br/>-----------------------&lt; D038245 APR/11/02 &gt;--------------------<br/><b>Termination in the TOOLIMPD3 phase</b><br/>The TOOLIMPD3 phase terminates in the PREPARE module import. The following message appears in the log file: ABAP runtime error CALL_FUNCTION_NO_RECEIVER<br/> Receiving data for unknown CPIC link XXXXXX.<br/>Repeat the phase and continue with the upgrade.<br/><br/>-----------------------&lt; D022256 SEP/04/00 &gt;--------------------<br/><b>For Windows NT 4.0 only</b><br/>During PREPARE, a dialog box with the following error message may appear: 'The procedure entry point ... could not be located in the dynamic link library ... .',<br/>In this case, import the latest DLLs using the R3DLLINS.EXE program. The program is available on the CD SAP Kernel in directory \\NT\\&lt;Processor type&gt;\\NTPATCH.<br/>Reboot your machine and restart PREPARE with 'PREPARE repeat'.<br/><br/>-----------------------------------------------------------------------<br/><br/><br/></p> <b>Problems During the Upgrade Phases</b><br/> <p><br/>------------------------&lt; D028310 20/AUG/04 &gt;--------------------------<br/>Phase: DIFFEXPDDIV<br/>Note: 766379<br/>Description:<br/>Error message in log file DIFFEXPD.ELG (directory &lt;DIR_PUT&gt;/log):<br/>INACTIVE DDIC VERSIONS-Export ERRORS and RETURN CODE in SAPEDDD622.QO1<br/>~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br/>2EETW190 \"TABT\" \"TESCL                        \" has no active version.<br/>...<br/><br/>------------------------&lt; D025988 JUN/16/00 &gt;-------------------<br/>Phase: PARDIST_SHD<br/>Description: For Windows NT only<br/>The upgrade is terminated during the PARDIST_SHD phase. The PCONUPG.ELG log file contains an incomplete error text which was extracted from the DS&lt;date&gt;.&lt;SID&gt; log file. Repeat the phase.<br/><br/>--------------------&lt;changed D026178 JUN/25/04 &gt;--------------------<br/>-------------------------&lt; D026178 JUN/07/04 &gt;----------------------<br/>Phase: ACT_640<br/>Description: If you have included Basis Support Package 03 in the upgrade, you may get the following error message: SHADOW IMPORT ERRORS and RETURN CODE in SAPKGPAC01.QO1<br/> 1EEDO519 \"Table\" \"TPPROFILES_TYPE_PROFILE\" could not be activated.<br/>The same error may appear for table \"TPPROFILES_TYPE_PROFILE_INFO\".<br/>You can ignore the error. Repeat the phase to continue with the upgrade.<br/><br/>----------------------------------------------------------------------<br/>Phase: PARCONV_UPG<br/>Notes: 705724, 705733<br/>Description: If you have not included SP02 in the upgrade, you will receive the error \"CONV ENTRY TBATG TABLMESYBODY - Unable to interpret \"000A \" as a number.\"<br/>If you do not use Mobile Infrastructure, you can ignore this error.<br/><br/>--------------------------&lt; D019416 19/MAR/04 &gt;-----------------------<br/>Phase: TABIM_POST<br/>Note: 718912<br/>Description: Upgrade stops with error DI829. In this case, implement<br/>the above note and continue with the upgrade. After the upgrade, you have to revert the modification.<br/><br/>--------------------------&lt; C5010299 JAN/21/05 &gt;-----------------------<br/>Phase: XPRAS_UPG<br/>Note: 778198<br/>Description: Error message S&gt;801 in log file LONGPOST.LOG. For more information, see the note above.<br/><br/>----------------------------------------------------------------------<br/>Phase: JOB_RDDNTPUR<br/>Note: 699458<br/>Description: If you get the error message<br/>3PETG447 Table and runtime object \"UMG_TEST_A\" exist without DDIC reference (\"Log. pool\")<br/>you can ignore it. For more information and a list of further tables for which you can ignore this error message, see the note above.<br/><br/>--------------------------&lt; D025323 MAY/23/02 &gt;-----------------<br/>Phase: CHK_POSTUP<br/>Note: 197886<br/>Description: If you have imported Notes 178631, 116095 or 69455 into your system before the upgrade, error messages for objects without DDIC reference appear in the log LONGPOST.LOG. Proceed as described in the Note.<br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p> <b>VIII/ Problems After the Upgrade</b><br/> <p><br/>This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br/><br/>-----------------------&lt; D000434 28/JAN/04 &gt;-------------------------<br/><b>Source Release 6.20: Missing DB View STWB_INFO</b><br/>If your source release is based on SAP_BASIS 6.20 SP 48 or lower, transaction DB02 might show DB view STWB_INFO missing after the upgrade.<b>This</b> view is not needed in the system and requires no further actions.<br/><br/>-----------------------&lt; D035318 08/JUL/04 &gt;-------------------------<br/><b>Source Release lower than Basis 6.10: Codepage Conversion</b><br/>In Release 6.10, the codepage administration has changed considerably. If you want to continue using the customer-defined codepages that start with \"9\" after the upgrade, you have to convert the codepages using report RSCP0126 after the upgrade.<br/>For more information, see <b>Notes 485455</b> and <b>511732</b>.<br/><br/>-----------------------&lt; D035318 04/FEB/04----------------------------<br/><b>Unicode Systems: Run Report RUTTTYPACT</b><br/>If your system is a unicode system, you must run report RUTTTYPACT after the upgrade. To run the report, proceed as described in <b>note 544623</b>.<br/><br/>------------------------&lt; D020815 AUG/23/02 &gt;------------------------<br/><b>SPAU: Names of interface methods are truncated</b><br/>Some methods (ABAP objects) that were modified and overwritten by the upgrade can be displayedin transaction SPAU with their names shortened to 30 characters.<br/>As a result, the system may also incorrectly sort methods in SPAU under \"Deleted objects\".<br/>Caution: Deleted objects are not displayed in the standard selection in SPAU. It is easily possible to overlook these!<br/>For more information about the correction, see <b>Note 547773</b>.<br/><br/>----------------------------------------------------------------------<br/><b>Linux: Importing the new saposcol version</b><br/>For more information, see <b>Note 19227.</b><br/><br/>----------------------------------------------------------------------<br/><b>ReliantUNIX: saposcol version 32-bit or 64-bit</b><br/>For more information, see <b>Note 148926.</b><br/><br/>----------------------------------------------------------------------<br/><b>Solaris: saposcol version 32-bit or 64- bit</b><br/>For more information, see <b>Note 162980.</b><br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p> <b>IX/ Chronological Summary</b><br/> <p><br/>Date.....Topic..Short description<br/>-----------------------------------------------------------------------<br/>SEP/15/08..III..SDK Version 1.4.x for Upgrade Assistant<br/>SEP/15/06....V..Support Package SAPKB64018 - use corrected version<br/>27/APR/06...VI..Handling of customer translation in the upgrade<br/>NOV/29/05...II..Upgrade on Linux x86_64: Correct R3up Version<br/>OCT/10/05...VI..Latest Upgrade Repairs for Correct Language Import<br/>15/SEP/05....V..Back up customer-specific entries in table EDIFCT<br/>09/AUG/05..III..Opt. Follow-Up: Where-Used List<br/>JUN/02/05....V..Do not include ABA Support Package 12 in the Upgrade<br/>APR/20/05..VII..Phase RFCCHK_INI: Name or Password is Incorrect<br/>FEB/25/05..III..Section \"SAP NW AS: J2EE Engine Installation Planning\"<br/>FEB/09/05..III..Section: Making Entries for the Extension Module<br/>FEB/02/05...II..LSMW now part of SAP_BASIS<br/>JAN/28/05.VIII..Source Release 6.20: Missing DB View STWB_INFO<br/>JAN/21/05..VII..Phase XPRAS_UPG: Error S&gt;801<br/>DEC/13/04...VI..Windows only: Shut Down IGS Before Starting PREPARE<br/>NOV/03/04..VII..Phase CONFCHK_IMP on Distributed Systems<br/>AUG/20/04..VII..Phase: DIFFEXPDDIV<br/>JUL/13/04...VI..Adjust Profile Parameter \"rsts/ccc/cachesize\"<br/>JUL/08/04.VIII..Source Release lower than 6.10: Codepage conversion<br/>JUL/06/04..III..Phase INITSHD: Instance Number of the Shadow Instance<br/>JUN/07/04..VII..Phase ACT_640: SHADOW IMPORT ERRORS with Basis SP03<br/>MAY/07/04..III..Implementing the Integrated SAP ITS<br/>APR/19/04...VI..Unicode Systems: Downward Compatible Kernel 6.40<br/>MAR/04/04..VII..Phase TABIM_POST: Error DI829<br/>FEB/13/04..VII..Phase PARCONV_UPG: Table MESYBODY<br/>FEB/13/04....V..SPDD - Return to Standard<br/>FEB/04/04.VIII..Unicode Systems: Run Report RUTTTYPACT<br/>FEB/04/04...VI..Unicode Systems: Report RUTTTYPACT<br/>JAN/27/04....V..Import of Asian Languages: Change System Language<br/>JAN/22/04..VII..Phase JOB_RDDNTPUR: Error 3PETG447<br/>JAN/16/04...VI..Java Application Server<br/>DEZ/03/03...II..Windows only: Execute program R3dllins.exe<br/>SEP/23/03...VI..Database Archives During the Upgrade<br/>SEP/23/03...VI..Space Requirements in the Database<br/>SEP/17/03....I..R3up keyword<br/>APR/24/03...VI..Upgrade on AIX: saposcol<br/>DEC/10/02...VI..Upgrading with AIX 5.1<br/>AUG/23/02.VIII..SPAU: Names of interface methods are truncated<br/>JUL/23/02 ...I..Windows: Windows XP is not supported<br/>JUL/19/02...II..Problems with the shadow instance<br/>MAY/24/02...II..Corrections and repairs for the upgrade<br/>MAY/23/02..VII..Phase CHK_POSTUP - objects without DDIC reference<br/>APR/11/02..VII..Termination in the TOOLIMPD3 phase<br/>APR/08/02..III..Preparation for reading the upgrade CDs<br/>FEB/20/02...VI..Source releases on UNIX 32-bit or AIX 64-bit<br/>OCT/19/00.VIII..Linux:  Importing the new saposcol version<br/>SEP/04/00..VII..Windows NT: Error message during PREPARE<br/>FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Reliant UNIX<br/>FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Solaris<br/>----------------------------------------------------------------------<br/></p></p></p></div>", "noteVersion": 38}, {"note": "1923499", "noteTitle": "1923499 - STATIC or Semi-dynamic TBOM has a very small content", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You record static or semi-dynamic TBOM in transaction SOLAR01/02 or via the report of mass TBOM generation 'AGS_BPCA_TBOM_STATIC_GEN', the generated TBOMs have very small amounts of objects, even with a high branching level.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Solution manager,<br/>BPCA,<br/>Business Process Change Analyzer,<br/>Technical Bill of Material,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The STATIC TBOM recording is using the environment data of the where used functionality, and this data is not up to date.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Update the where-used index in your managed system as explained in the note 28022,</p>\n<p>You can find additional information about the where-used index in the two following notes:</p>\n<ul>\n<li>2039618 - Size of table WBCROSSGT</li>\n<li>1917231 - Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT</li>\n</ul></div>", "noteVersion": 4}, {"note": "688720", "noteTitle": "688720 - Where-used list for transactions in area menus", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The where-used list for transactions in area menus is incorrect. Various differences can arise:</p> <ol>1. The where-used list is incomplete.</ol> <ol>2. After you change an area menu, all of the area menu transactions disappear from the where-used list.</ol> <ol>3. The where-used list displays too many hits.<br/></ol> <p>Explanation: Here, area menu denotes the area menu provided using transaction SE43 up to Basis Release 4.6B, and not the Easy Access menus used as of Release 4.6C.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>se43</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There are several reasons for the differences.<br/>A programming error is the cause of the gaps in the where-used list and the disappearance of entries that previously existed when you save or generate an area menu.<br/>Unnecessary entries in the where-used list are caused by the standard processing of interface parts in transactions SE41 (Menu Painter) and SE43  (Area Menu Editor). If a transaction is included in a menu branch of an area menu, nevertheless, it remains in the list of functions. However, if the transaction is deleted again from the menu branch, it remains in the list of the functions because it is probably used in another location in the area menu. Since the where-used list takes into account the list of functions, it also displays the transaction deleted from the menu branch for as long as it is not removed from the list of the functions.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the specified Support Package (in Basis Release 4.6B, 4.6C or 4.6D) or implement the note correction (as of Basis Release 4.0B).<br/>You can then once again fill any gaps that exist in the where-used list either</p> <ul><li>System-wide</li></ul> <p>           by executing the SAPRSEUB report (with a runtime of several hours) in accordance with note 28022 or</p> <ul><li>for an individual area menu</li></ul> <p>           by executing the RS_CUA_TCODE_INDEX function module in test mode (F8) with the OBJECT NAME = &lt;area_menu_name&gt;' and OBJ_TYPE = space parameters.<br/><br/>However, the Support Package and note correction do not correct the unnecessary entries in the where-used list. These can only be removed separately in each case for an area menu by postprocessing in SE43. This occurs in the following steps.</p> <ol>1. Call the SE43 area menu editor for &lt;area_menu_name&gt; and press \"Change\".</ol> <ol>2. Enter \"=NREF\" in the OK code field.</ol> <p>           This makes available the known \"Utilities--&gt; Unused objects\" menu function in the Menu Painter.</p> <ul><li>Select all functions that appear in the list of unused functions.</li></ul> <ul><li>Delete the selected functions using the Delete pushbutton (\"Delete selected subobjects (Ctrl F2)\")</li></ul> <ul><li>Save and generated your changes.</li></ul></div>", "noteVersion": 4}, {"note": "1527757", "noteTitle": "1527757 - Deleting multiple-use includes: Where-used list", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>If an include that is included in several supporting programs is deleted from a source, the system does not update the where-used list correctly. You must use an additional include to make this include part of several supporting programs. The use for only one supporting program is deleted; the use for additional supporting programs is not deleted. Therefore, the system still displays the use after the deletion.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Where-used list, delete include, update navigation index</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This error is relevant only in development systems and occurs only for includes that are used several times. There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package or implement the advance correction. After you implement the advance correction, you must rebuild the entire index. Execute the program SAPRSEUB in the development system. See Notes 18023 and 28022.</p></div>", "noteVersion": 1}, {"note": "501868", "noteTitle": "501868 - Forward navigation does not work", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p><br/>Double-clicking on a text in the front-end editor does not execute forward navigation. The navigation works in the back-end editor.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/>se80, se24, se37, se38, where-used list<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br/>The program text contains hidden control characters (0x0B) that are displayed as line breaks in the front-end editor. These characters are ignored in the back-end editor and in the navigation. As a result, the line counts of the front-end editor and navigation are different and thus the incorrect text location is transferred to the navigation.<br/>The control characters may have entered the program text by means of \"Cut&amp;Paste\" or by pressing Shift+Return. You can prevent the latter cause by implement the latest front-end patch (note 428254).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/>Find the location of the control characters by comparing the line numbers in the front-end and back-end editor and delete the blank lines in the front-end editor.</p></div>", "noteVersion": 2}, {"note": "587896", "noteTitle": "587896 - Add. info on upgrade to SAP R/3 Enterprise Core 4.70 SR1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Errors in the upgrade or update procedure or in the upgrade guides; preparations for the upgrade or update; additional information to the upgrade guide.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Update, upgrade, SUM, Software Update Manager, SAP R/3 Enterprise</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>*</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>CAUTION: This note is updated continually.</strong><br/><strong>You should therefore read it again immediately before the upgrade.</strong><br/><strong>CAUTION: This note only applies to R/3 Enterprise Core 4.70 Support Release 1.</strong><br/><br/></p>\n<p><strong>What can you expect of this note?</strong></p>\n<p>This note describes problems that may occur when you upgrade the system and provides you with information about how to solve them. In most cases you are referred to other notes.<br/>The main aim of this note is to prevent data loss, upgrade standstill and long runtimes.<br/>This note only deals with database-independent problems.</p>\n<p><strong>What can you not expect of this note?</strong></p>\n<p>Problems after the upgrade are only dealt with if they are caused directly by the upgrade tools.</p>\n<p><strong>Which notes do I need in preparation for the upgrade?</strong></p>\n<p>That depends on the functions you are using. You need one or several of the following notes:<br/><br/>Short text.......................................... Note number<br/>_____________________________________________________________________<br/>Additional information about upgrading to SAP Web AS 6.20 SAP DB......................................................... 490325<br/>Additions to the upgrade SAP R/3 Enterprise 4.7 SR1 Ext.1.10.......... 597256<br/>Additional information about upgrading to SAP Web AS ........... 490095<br/>DB2/390: Additions upgrade to R/3 Enterprise 4.7.............. 517267<br/>Add. information on upgrading to SAP R/3 Enterprise Core 4.70... 517070<br/>Add. information on upgrading to SAP Web AS 6.20 Oracle .............. 575280<br/>Additional information on upgrading to Basis 6.20 - INFORMIX 486274<br/>_____________________________________________________________________<br/>Current note on the language transport in Release 6.20 .. 482462<br/>Consultation: Languages and Support Packages ............ 352941<br/>_____________________________________________________________________<br/>Corrections for the upgrade to systems with Basis 620 ... 517731<br/>Repairs for upgrade to Basis 620 ........................ 522711<br/>_____________________________________________________________________<br/>OCS: Known problems with Supp. Packages in Basis Rel.6.20.........447925 447925<br/><br/>Use the Software Update Manager 1.0 for an update or upgrade to SAP R/3 Enterprise Core 4.70 SR1. You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p> </p>\n<p><br/>---------------------------------------------------------------------</p>\n<p><strong>Contents</strong></p>\n<p>I/  ...... Keyword<br/>II/ ..... Important general information<br/>III/ .... Corrections to the guides<br/>IV/ ..... Errors on the CD-ROM<br/>V/ ...... Preventing loss of data, upgrade shutdowns and long<br/>          runtimes<br/>VI/ ..... You are preparing for an upgrade.<br/>VII/ .... Problems in the PREPARE and upgrade phases<br/>VIII/ ... Problems after the upgrade<br/>IX/ ..... Temporal specification of contents</p>\n<p><strong>I/ Keyword</strong></p>\n<p>Keywords are no longer applicable.<br/>(For information only: The R3up keyword was 83478.)</p>\n<p><strong>II/ Important general remarks</strong></p>\n<p><strong>**************************************************</strong></p>\n<p>The former tool R3up was replaced by the Software Update Manager (SUM). See also SAP Note 1589311 and the blog:<br/><a href=\"https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/\" target=\"_blank\">https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/</a>.</p>\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP R/3 Enterprise Core 4.70 SR1.</p>\n<p>You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p><strong>**************************************************</strong></p>\n<p> </p>\n<p>-------------------&lt; changed D030559 SEP/16/05 &gt;------------------------<br/>---------------------&lt; D025323 MAY/24/02 &gt;----------------------<br/><strong>Corrections and repairs for the upgrade</strong><br/>Before the upgrade, you must import the <strong>valid R3up version</strong> for your specific upgrade.<br/>For more information, see <strong>Note 517731</strong>.<br/>In addition, you must check whether there are corrections to the ABAP programs of the upgrade.<br/>For more information, see <strong>Note 522711</strong>.<br/><strong>CAUTION: It is ESSENTIAL that you apply these two notes.</strong><br/><br/>--------------------&lt;changed D0323540 NOV/18/05&gt;---------------------<br/>-----------------------&lt; D028310 16/NOV/05&gt;---------------------------<br/><strong>Z1&gt;Do not include Support Package SAP_BASIS 56 in the upgrade</strong><br/>Do NOT include Basis Support Package 56 (SAPKB62056) in the upgrade. If you do include the Support Package, the upgrade will terminate in phase JOB_RSWBOUP1.<br/>If you have included the Support Package, follow the instructions in Note <strong>898921</strong>.<br/><br/>-----------------------&lt; D028310 AUG/16/04&gt;---------------------------<br/><strong>Including Support Package SAP_BASIS 6.20 SP42</strong><br/>If you want to include SAP_BASIS 6.20 SP42 in the upgrade, make sure that you have at least Version 15.90 of R3up. If you do not have this R3up version, errors may occur in the XPRAS phase.<br/><br/>-------------------&lt;changed D000706 JUN/23/04 &gt;----------------------<br/>-----------------------&lt; D025907 DEC/01/03 &gt;-------------------------<br/><strong>Use the current version of R3trans</strong><br/>Before the upgrade, make sure that the R3trans for the target release reports a minimum of Version number <strong>15.06.04</strong>.<br/>If this is not the case, import the current R3trans for the target release. Place R3trans into the &lt;DIR_PUT&gt;/exe directory after the PREPARE phase and prior to the upgrade.<br/>For more information, see <strong>Note 746476</strong>.<br/><br/>--------------------&lt; Changed D025323 JUN/27/03 &gt;---------------------<br/>-----------------------&lt; D025323 JUN/23/03 &gt;--------------------------<br/><strong>Source Release 3.1: Integrate Fix Pack 22</strong><br/>For Source Release 3.1, you must integrate at least <strong>Fix Pack Version 22</strong> into PREPARE. Otherwise, you risk data loss.<br/>If you have already started PREPARE with an older Fix Pack, repeat the PREPARE module IMPORT and all subsequent modules.<br/>For more information, see <strong>Note 522711</strong>.<br/><br/>-----------------------&lt; D028310 JUL/19/02 &gt;--------------------<br/><strong>Problems with the shadow instance</strong><br/>The following notes contain information about problems with the shadow instance:</p>\n<ul>\n<li><strong>525677</strong>: Problems when starting the shadow instance</li>\n</ul>\n<ul>\n<li><strong>430318</strong>: Remote shadow instance on another operating system</li>\n</ul>\n<p><br/>-----------------------&lt; D022030 MAY/08/03 &gt;----------------------------<br/><strong>Support Package levels in SAP R/3 Enterprise 4.70 SR1</strong><br/>SAP R/3 Enterprise 4.70 Support Release 1 is based on the following Support Package levels:<br/>Main components:</p>\n<ul>\n<li>SAP_BASIS 620 Support Package 17</li>\n</ul>\n<ul>\n<li>SAP_ABA 620 Support Package 17</li>\n</ul>\n<ul>\n<li>SAP_HR 470 Support Package 10</li>\n</ul>\n<ul>\n<li>SAP_APPL 470 Support Package 6</li>\n</ul>\n<p>Add-ons</p>\n<ul>\n<li>EA-HR 110 Support Package 10</li>\n</ul>\n<ul>\n<li>EA-APPL 110 Support Package 6</li>\n</ul>\n<ul>\n<li>EA-PS 110 Support Package 6</li>\n</ul>\n<ul>\n<li>EA-IPPE 110 Support Package 6</li>\n</ul>\n<ul>\n<li>EA-FINSERV 110 Support Package 6</li>\n</ul>\n<ul>\n<li>EA-RETAIL 110 Support Package 6</li>\n</ul>\n<ul>\n<li>EA-GLTRADE 110 Support Package 6</li>\n</ul>\n<p>Plug-ins</p>\n<ul>\n<li>PI_BASIS 2002_1_620 Support Package 6</li>\n</ul>\n<ul>\n<li>PI 2002_1_470 Support Package 3</li>\n</ul>\n<p><br/>---------------------------------------------------------------------<br/><strong>Installation of the SAP J2EE Engine</strong><br/>The SAP J2EE Engine is not required for SAP R/3 Enterprise 4.70.<br/>To ensure that you make optimal use of your live environment's system resources, the SAP J2EE Engine is not installed automatically with R/3 Enterprise.<br/>If you would like to install the SAP J2EE Engine, you can install it as described in <strong>Note 545422</strong><br/><br/>-------------------------&lt; D029035 SEP/05/02 &gt;------------------------<br/><strong>Upgrade and Unicode conversion</strong><br/>At present, SAP does not support an upgrade with simultaneous Unicode conversion. To obtain a Unicode system, you must first carry out an upgrade of the non-Unicode system of release versions earlier than 4.70 (SAP Web AS version earlier than 6.20) to Release 4.70. You can then perform the Unicode conversion.<br/>For more information, see <strong>Note 548016</strong>.<br/><br/>---------------------------------------------------------------------<br/><strong>Retrofit add-ons</strong><br/>For more information about dealing with add-ons that underwent a retrofit for R/3 Enterprise 4.70, see <strong>Note 533348</strong>.<br/><br/>---------------------&lt; D025323 SEP/20/02 &gt;----------------------<br/><strong>Windows NT only: Check the tp version</strong><br/>Make sure that the \\usr\\sap\\&lt;sid&gt;\\sys\\exe\\run directory does not contain tp Version 305.12.58 (tp for the source release, when Source Release is 4.6x)<br/>Use either a more recent version or the previous version, otherwise errors will occur during the upgrade.<br/>For more information, see <strong>Note 537347</strong>.<br/><br/>------------------------&lt; D042621 02/FEB/05 &gt;-------------------------<br/><strong>LSMW is now part of SAP_BASIS</strong><br/>As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you use LSMW with a source release based on SAP Web AS 6.10 or lower, do not import LSMW again after the upgrade.<br/>For more information, see <strong>Note 673066</strong>.<br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/><br/><br/><br/></p>\n<p><strong>III/ Corrections to the guide</strong></p>\n<p><br/>-------------------------&lt; D022030 21/JUN/06 &gt;------------------<br/><strong>Section: Making Entries for the Parameter Input Module</strong><br/>The maximum length of the mount directory path is <strong>50</strong> characters.<br/>The path may not contain any blank spaces or special characters.<br/><br/>------------------------&lt; D030328 AUG/09/05 &gt;-------------------------<br/><strong>Optional postprocessing: where-used list in the Workbench</strong><br/>As of Release 6.10, the where-used list for ABAP Dictionary objects has changed. If you require an exact display of the list, start the report SAPRSEUB after the upgrade.<br/>As the report can have very long runtimes, we recommend that you only run this report in a development system.<br/>For further information, see Notes <strong>401389</strong> and <strong>28022</strong>.<br/><br/>------------------------&lt; I002675 MAR/11/05 &gt;---------------------------<br/><strong>Windows manual: section on database-specific parameters</strong><br/>In the upgrade manual for Windows, section \"Checking the Database- specific requirements for PREPARE\", you are asked to check the profile parameters for the MS SQL server. Since some of the parameters have changed, they are no longer recognized by SAP tools to a certain extent.<br/>For more information, see <strong>Note 826528</strong>.<br/><br/>------------------------&lt; D028310 FEB/09/05 &gt;---------------------------<br/><strong>Section: Making Entries for the Extension Module</strong><br/>In the subsection Phase IS_SELECT, the following applies to add-ons that have the status \"undecided\":<br/>\"The software manufacturer of the individual add-ons has predefined the strategy that you must choose for the add-on.<br/>For further information, see the displayed SAP note or contact the software manufacturer of your add-on.\"<br/><br/>-----------------------&lt; D001330 OCT/20/04 &gt;-------------------------<br/><strong>Section: Phase JOB_RSVBCHCK2</strong><br/>The text should read:<br/>\"If errors are reported in this Phase, and you are <strong>still</strong> in production operation, you can bypass this error with 'ignore' without a password\".<br/><br/>-----------------------&lt; D022030 JUL/06/04 &gt;-------------------------<br/><strong>Phase INITSHD: Instance number of the shadow instance</strong><br/>For more information about the shadow instance number selection, see <strong>Note 29972.</strong><br/><br/>-------------------------&lt; D038245 FEB/26/04 &gt;--------------------------<br/><strong>DDIC user needs SAP_ALL authorization</strong><br/>In section \"Checking the User for the Upgrade\", the following phrase is missing:<br/>To carry out all actions required for the upgrade, the DDIC user needs SAP_ALL authorization.<br/><br/>------------------------&lt; D033327 JUL/02/03 &gt;-------------------------<br/><strong>Windows only: Wrong Note number in postprocessing work for MSCS </strong><br/>Sections \"Upgrade - Step by Step\" and \"Post-Upgrade Activities for the Microsoft Cluster Server\" refer to <strong>Note 144031</strong> during the postprocessing work for MSCS. This is incorrect. The correct reference is <strong>Note 544988</strong>.<br/><br/>------------------&lt; D000706 JUN/26/03 &gt;-------------------------<br/><strong>Date of the R3trans required for the source release</strong><br/>Section \"Checking SAP Programs\" asks for an R3trans of APR/29/02 or later for the source release.<br/>This date refers to the date of the file. The actual R3trans displays the earlier date of March 04, 2002.<br/><br/>-------------------------&lt; D022030 JUN/23/03 &gt;------------------------<br/><strong>Parameter rdisp contains a typographical error</strong>:<br/>The parameters rdisp/mshost and rdisp/wp_no_vb have been written incorrectly in some guides. The correct name is \"rdisp\", not \"rsdisp\".<br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>IV/ Errors on the CD-ROM</strong></p>\n<p><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>V/ Avoiding data loss, upgrade shutdown and long run times</strong></p>\n<p><br/>--------------------------&lt; d000706 28/NOV/06 &gt;------------------------<br/><strong>Modification adjustment planning and Unicode conversion</strong><br/>You cannot import any transport requests that were created in a Unicode SAP system into a non-Unicode SAP system.<br/>If you want to convert your SAP system to unicode, create the transport request <strong>before</strong> the Unicode conversion.<br/><br/>------------------------&lt; D003327 SEP/15/05 &gt;--------------------------<br/><strong>Saving customer-specific entries in the EDIFCT table</strong><br/>If you have made customer-specific entries in the EDIFCT table, these may be lost during the upgrade. If you want to keep the entries, you must export them before the upgrade and then import them again into the system after the upgrade.<br/>For more information, see <strong>Note 865142</strong>.<br/><br/>------------------------&lt; D030559 02/JUN/05 &gt;-------------------------<br/><strong>Do not include ABA Support Package 51 in the upgrade</strong><br/>If you include the Support Package SAPKA62051 in the upgrade, the upgrade is terminated in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br/>To avoid this termination, you should only include this Support Package in the upgrade, if you can include Support Package <strong>SAPKA62053</strong> at the same time, as this Support Package<br/>fixes the problem.<br/>For further information, also regarding how to proceed if errors occur, see <strong>Note 849925</strong>.<br/><br/>-----------------------&lt; D001330 OCT/20/04 &gt;-------------------------<br/><strong>Function groups in the customer namespace</strong><br/>If you have created function groups in the customer namespace, the upgrade may lead to loss of data.<br/>For more information, see <strong>Note 783308</strong>.<br/><br/>------------------------&lt; D031049 OCT/14/04 &gt;---------------------------<br/><strong>Source Release. 4.0B: Project-Related Incoming Orders</strong><br/>If you apply a published advance correction to a project-related incoming order using Note 118780, you have to modify data elements before you upgrade.<br/>For more information, see <strong>Note 369542</strong>.<br/><br/>------------------------&lt; D024329 OCT/14/04 &gt;---------------------------<br/><strong>Component PS-ST-WBS: Preliminary Admission Field KIMSK</strong><br/>You can transfer field KIMSK to the table of modifiable fields before the upgrade, so that it will not be overwritten during the upgrade.<br/>For more information, see <strong>Note 185315</strong>.<br/><br/>-----------------&lt;changed D038245 DEC/27/04 &gt;---------------------------<br/>-----------------&lt;changed D038245 OCT/01/04 &gt;---------------------------<br/>---------------------&lt; D030326 AUG/08/02 &gt;------------------------------<br/><strong>Adjusting profile parameters</strong><br/>Check whether the profile parameter \"rsts/ccc/cachesize\" is set. If it is, remove it or extend it to 6,000,000 bytes, as described in <strong>Note 5470</strong>.<br/>The following are possible error messages:</p>\n<ul>\n<li>\"segmentation fault\" or Dr Watson Dump of the R3up in RFC or \"JOB\" phases</li>\n</ul>\n<p>           In this case, you just need to change the profile file and repeat this step. You do not have to restart the system.</p>\n<ul>\n<li>Runtime error \"IMPORT_INIT_CONVERSATION_FAILED\"</li>\n</ul>\n<p>           This following message is displayed in the system log:<br/>           \"E 14 Reorganized Buffer RSCPCCC with Length xxxxx (Overflow)\"<br/>           In this case, you need to change the profile file and restart the system before you can proceed with the upgrade.</p>\n<ul>\n<li>After a termination in the RFCCHK_INI phase:</li>\n</ul>\n<p>           RFC logon to .... no success or failure at all. file from former upgrade has more entries.<br/>           In this case you need to change the profile file and restart the system before you can proceed with the PREPARE.<br/><br/>-----------------&lt; changed  D035061 MAR/18/04 &gt;-----------------------<br/>-----------------------&lt; D035061 MAR/04/04 &gt;--------------------------<br/><strong>Basis Support Package 37</strong><br/>If you include Basis Support Package 37 in the upgrade, a termination occurs in the TABIM_UPG phase.<br/>For information about how to correct the error, see <strong>Note 718985</strong>.<br/>The problem is solved with Basis Support Package 38.<br/><br/>-----------------------&lt; D020815 FEB/13/04 &gt;--------------------------<br/><strong>SPDD - Reset to standard</strong><br/>When you choose \"Reset to standard\" in Transaction SPDD, for data elements already adjusted, activation starts instead. If you want to reset data elements that have already been adjusted, proceed as described in <strong>Note 705943</strong>.<br/><br/>-----------------------&lt; D025323 JAN/27/04 &gt;--------------------------<br/><strong>Problems due to including Basis Support Package 35</strong><br/>When you include Support Package SAPKB62035 in the upgrade, an error message is displayed in the PARCONV_UPG phase when activating a table contained in the Support Package.<br/>For information about how to prevent the error, see <strong>Note 694062</strong>.<br/><br/>----------------------&lt; D039228 DEC/05/03 &gt;---------------------------<br/><strong>Source Release 4.6B/4.6C: Problems with Table kpp1loio</strong><br/>If you have installed Support Package SAPKH46B53 (4.6B) or SAPKH46C44 (4.6C) in the source release, a termination occurs in phase TABIM_UPG.<br/>To prevent this, proceed as described in <strong>Note 666787</strong> before you carry out the upgrade.<br/><br/>-----------------------&lt; D002676 NOV/11/03 &gt;--------------------------<br/><strong>Data loss in Table PA0616</strong><br/>Due to the upgrade, data may be lost in Table PA0616. To prevent this, install the current correction package as described in <strong>Note 522711</strong>.<br/><br/>------------------&lt; changed D035061 MAY/09/03 &gt;----------------------<br/>-----------------------&lt; D035061 MAY/08/03 &gt;--------------------------<br/><strong>Inclusion of certain Support Package statuses</strong><br/>If you include the following Support Packages in the upgrade, include to at least the following level:<br/>SAP_APPL 470 ... Support Package 0010*<br/>EA-APPL 110 .... Support Package 0004<br/>* Until this Support Package is released, proceed as follows:</p>\n<ul>\n<li>Customers without EA_GLTRADE:</li>\n</ul>\n<p>           Include SAP_APPL at least until Support Package 0008. In this case you can ignore the error message that occurs in the ACT_620 phase for the views h_tmfk, v_tb2bt and v_tb2bu. For more information, see <strong>Note 610295</strong>.</p>\n<ul>\n<li>Customers with EA_GLTRADE:</li>\n</ul>\n<p>           Import the EA_GLTRADE Support Packages to at least Support Package 0007 during the upgrade. For more information, see <strong>Note 615187</strong>.<br/><br/>-----------------------&lt; D002676 02/MAY/03 &gt;--------------------------<br/><strong>Loss of Customer Matchcode IDs</strong><br/>Customer-specific matchcode IDs are lost when you upgrade to SAP R/3 Enterprise. The error is eliminated with the upgrade correction from <strong>Note 522711</strong>.<br/><br/>------------------------&lt;I023086 APR/08/03 &gt;--------------------<br/><strong>Archiving Work items for reducing the conversion time</strong><br/>During the transfer to 46A, corrections were made in table SWWWIHEAD to improve performance. These corrections require an (automatic) table conversion on the database.<br/>To minimize the conversion time, we urgently recommend that you archive or delete entries in these tables that are no longer required (work items in an end status) before the actual upgrade.<br/>To archive/delete the data, see <strong>Notes: 49545, 145291, 153205, 159065</strong>.<br/><br/>-----------------------&lt; D030326 MAR/28/03 &gt;--------------------<br/><strong>Missing contents for proxy generation of the Exchange Infrastructure</strong><br/>As of Basis Release 6.20, more extensive information about DDIC objects, classes and interfaces is delivered, which is necessary if you use the SAP Exchange Infrastructure (SAP XI). The DDIC object information is not imported during the upgrade.<br/>The error is eliminated with the correction for the upgrade procedure <strong>Note 522711</strong>.<br/>If you have already performed the upgrade to Basis Release 6.20, you will find information about the subsequent importing of the missing data in <strong>Note 439915</strong> about the add-on \"Exchange Infrastructure - Integration Server\" (APPINT).<br/><br/>--------------------&lt; D030326 SEP/17/02 &gt;-----------------------<br/><strong>DDIC password</strong><br/>Once you have started the upgrade, you must not change the DDIC password either in the original system or in the shadow system. Otherwise you can no longer logon to the system during the upgrade.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>VI/ Preparations for the upgrade</strong></p>\n<p><br/>-----------------------&lt; D001330 27/APR/06 &gt;-------------------------- <strong>Treatment of customer translations in the upgrade</strong><br/>Z-languages or customer-specific translations of system texts (performed using Transaction SE63) are not recognized as modifications to the system, and they are therefore lost after the upgrade. Since the SAP System often changes considerably from one release to the next, it is often not worth while recovering these translations.<br/>If you you wish to recover the languages or texts, refer to Note <strong>485741</strong> for more information about the necessary procedure.<br/><br/>------------------------&lt; D030022 OCT/14/04 &gt;---------------------------<br/><strong>Source Release 3.1I: Loss of Address Data</strong><br/>If you install component \"Maintenance\" (PM-WOC-MN) and convert addresses using report RSXADR05 before the upgrade from Source Release 3.1I, see the information in <strong>Note 360929</strong>.<br/>Otherwise address data may be lost during the upgrade.<br/><br/>----------------------&lt; D033898 OCT/06/04 &gt;---------------------------<br/><strong>Source Release 4.6C and lower: SMODILOG entries</strong><br/>In Releases 4.6C and lower, no SMODILOG entries were written when creating a customer-specific parameter effectivity. To avoid losing the customer-specific parameter effectivities, follow the instructions in <strong>Note 741280.</strong><br/><br/>----------------------&lt; D019011 DEC/17/03 &gt;---------------------------<br/><strong>Delete old application logs</strong><br/>As of Release 4.6C, the application logs are stored in a new format. To read old application logs, the R/3 system needs to convert them to the new format. To avoid performance problems during conversion, you can delete application logs from Releases prior to 4.6C which are no longer needed before carrying out the upgrade.<br/>For more information, see <strong>Note 195157</strong>.<br/><br/>-----------------------&lt; D003551 DEC/11/03 &gt;--------------------------<br/><strong>Use of the EXT Kernel during the upgrade</strong><br/>If you use the EXT Kernel in the source release, see <strong>Note 643409</strong> before the upgrade.<br/><br/>--------------------------&lt; D022188 AUG/27/03 &gt;------------------------<br/><strong>Adjusting application-specific data</strong><br/>To avoid problems when adjusting application-specific data (in particular report variants), see <strong>Notes 623723</strong> and <strong>626408.</strong><br/>The notes also contain information for upgrade postprocessing.<br/><br/>----------------------&lt; D025323 APR/24/03 &gt;---------------------<br/><strong>Upgrading under AIX: saposcol</strong><br/>Before the upgrade, see <strong>Notes 526694</strong> and <strong>569015</strong>.<br/><br/>--------------------------&lt; D030326 AUG/08/02 &gt;-----------------<br/><strong>Adjusting profile parameters</strong><br/>Check whether the profile parameter \"rsts/ccc/cachesize\" is set.<br/>If it is, remove it or extend it to 6,000,000 bytes, as described in Note 5470.<br/>For more information about these add-ons, refer to note <strong>533348</strong>.<br/><br/>----------------------&lt; D025323 JUL/08/02 &gt;----------------<br/><strong>The following message appears in the syslog:</strong><br/>\"E 14 Reorganized Buffer RSCPCCC with Length xxxxx (Overflow)\" The error also occurs in phase UVERS_CHK.<br/>Here you get a \"segmentation fault\" or Dr. Watson dump. -------------------------&lt; D032986 JUL/09/02 &gt;------------------<br/><strong>Before the PREPARE</strong> switch to a 64-bit version, preferably the 64-bit version specified for the target release.<br/>If you are using AIX 4.3 64-bit, you must also perform other actions before the upgrade.<br/>For more information on processing these add-ons, see <strong>Notes 496963</strong> and <strong>499708</strong>.<br/><br/>-----------------------&lt; D021371 OCT/12/01 &gt;--------------------<br/><strong>Only for AIX: Application Runtime</strong><br/>For an upgrade on AIX, you require at least AIX Application Runtime Version *******.<br/>Use the command<br/>lslpp -l xlC.rte<br/>to check whether the C set ++ has been installed.<br/>If it has not been installed or if the incorrect version was installed, download this version from your country version of the IBM AIX Fix Distribution web page or from the American web page<br/>http://service.software.ibm.com/support/rs6000<br/>Install it using smit (-&gt; software) as user root.<br/>If you require the \"Base Level Fileset\" (xlC.rte.5.0.0.0), download the vacpp5_runtime.tar.Z file from the following Web page:<br/>http://service.software.ibm.com/support/rs6000<br/>Install it using smit (-&gt; software) as the user root.<br/><br/>ftp://ftp.software.ibm.com/aix/products/ccpp/ --------------------&lt; added D032986 FEB/05/04 &gt;---------------------<br/>--------------------&lt; added D032986 JUL/05/02 &gt;----------<br/>-------------------------&lt; D028310 JUL/02/02 &gt;------------------<br/><strong>Upgrade with add-on PI, PI-A or their predecessors BW-BCT, APO-CIF, B2B-PRO-PI and CRM-R3A</strong><br/>PI Release 2002_1_470 is part of the R/3 Enterprise delivery (R/3 Enterprise Core 4.70, R/3 Enterprise Extension 1.10).<br/>If you have already installed PI, PI-A or their predecessors in your R/3 source release, proceed as follows:</p>\n<ul>\n<li>Before starting the PREPARE, you need to have at least PI/PI-A Release level 2001.2 (technically for example 2001_2_40B) installed. For more information on processing these add-ons, see <strong>Notes 533568</strong> and <strong>214503</strong>.</li>\n</ul>\n<ul>\n<li>Source release 3.1I:</li>\n</ul>\n<p>           If the add-on PI with a Release up to or including 2002_1_31I is installed in your 3.1I system, select \"Passive deletion\" in the phase IS_READ (that is, neither \"Supplement CD\" nor \"Received\"). As the add-on PI is part of the export when upgrading to R/3 Enterprise 4.70, you automatically receive the current version with the upgrade.<br/>           If the PI or PI-A version in your 3. 1I system is higher than 2002_1_31I, you must include a supplement CD in phase IS_READ to avoid data loss.</p>\n<ul>\n<li>Source releases higher than 3.1I:</li>\n</ul>\n<p>           If the PI or PI-A version in your R/3 system is higher than 2002_1, you must include a supplement CD in phase IS_SELECT to avoid data loss.<br/>           For more information on processing these add-ons, see <strong>Notes 533568</strong> and <strong>214503</strong>. Detailed information about the R/3 plug-in (Contents, releases, dates, installation and upgrade strategy, maintenance) is provided on the SAP Service Marketplace under the alias R3-PLUG-IN.<br/><br/>If you have not installed PI or PI-A or their predecessors in your R/3 source release, you can carry out the upgrade to R/3 Enterprise without installing PI or PI-A in the R/3 source release.<br/><br/>-------------------------&lt; D022030 JUN/24/02 &gt;------------------<br/><strong>Database archives</strong><br/>The archives used during the upgrade help you select an upgrade or archiving strategy. The first of the two digits specifies the size of the archives up to phase MODPROF_TRANS, the second specifies the size of the archives up to the end of the upgrade.<br/>DB2 UDB for UNIX/Windows: 26 GB/31 GB<br/>Informix: 22 GB/27 GB<br/>MS SQL server: 24 GB/30 GB<br/>Oracle: 26 GB/31 GB<br/>SAP DB: 15 GB/20 GB<br/>The sizes are based on sample data.<br/><br/>-------------------------&lt; D022030 JUN/24/02 &gt;------------------<br/><strong>Storage requirement in the database</strong><br/>The database must be enhanced during and through the upgrade. The first of the two digits specifies the temporarily required enhancement during the upgrade, the second specifies the permanent enhancement of the database.<br/>DB2 UDB for UNIX/Windows: 18 GB/15 GB<br/>Informix: -- GB / -- GB<br/>iSeries: 25 GB/16 GB*<br/>MS SQL server: 15 GB/-- GB<br/>Oracle: 18 GB/15 GB<br/>SAP DB: 14 GB/8 GB<br/>The sizes are based on sample data.<br/>* depends predominately on the upgrade and archiving strategy as a number of journal receivers may arise during archiving.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>VII/ Problems in the PREPARE- and upgrade phases</strong><br/><br/></p>\n<p>In this column you can find known problems that you cannot avoid with preventive measures. These problems can only occur under certain conditions or prerequisites.</p>\n<p><strong>Problems in the PREPARE phases</strong></p>\n<p><br/>----------------------&lt; D038245 APR/20/05 &gt;-----------------------------<br/><strong>Phase RFCCHK_INI: Name or Password is Incorrect</strong><br/>In the phase RFCCHK_INI, the system issues the error \"Name or Password is Incorrect.\"<br/>If you do not replace R3up with the latest version from the SAP Service Marketplace until after the phase RFCCHK_INI, this error may also occur in other phases that are connected to the system by RFC.<br/>For more information, see <strong>Note 792850</strong>. If necessary, replace disp+work and restart your system.<br/><br/>-----------------------&lt; D028310 JUL/26/O4 &gt;---------------------------<br/><strong>Start-Release 3.1I and 4.0B: error in phase TR_CMDIMPORT_FDTASKS </strong><br/>If nametab entries exist without a unique UUID an error occurs in this phase. Proceed as described in <strong>Note 705485</strong><br/><br/>-----------------------&lt; D038245 JUL/30/03 &gt;--------------------<br/><strong>Exchanging the target release kernel</strong><br/>After the \"General checks\" module, you may receive the following error message in the CHECKS.LOG log file:<br/>There are dependencies between Basis Support Packages included in phase BIND_PATCH and the patch level of the target release kernel in directory /usr/sap/put/exe.<br/>In this case, a Support Package included in the upgrade needs another kernel patch level.<br/>Proceed as described in <strong>Note 211077</strong>.<br/><br/>-----------------------&lt; D022256 SEP/04/00 &gt;--------------------<br/><strong>For Windows NT 4.0 only</strong><br/>If the system displays a dialog box with the following error message during the PREPARE:<br/>\"The procedure entry point ... could not be located in the dynamic link library ...  .'<br/>use the R3DLLINS.EXE program to import the latest DLLs. The program is located on the CD SAP kernel in the \\NT\\&lt;processor type&gt;\\NTPATCH directory.<br/>Then reboot your machine and restart PREPARE using \"PREPARE repeat\".<br/><br/>-----------------------------------------------------------------------<br/><strong>As of Source Release 4.0A:</strong><br/>Termination in the phases TOOLIMPD1 or TOOLIMPD2. For more information, see <strong>Note 98198</strong>.<br/><br/>-----------------------------------------------------------------------<br/><br/></p>\n<p><strong>Problems in the upgrade phases</strong></p>\n<p><br/>-----------------------------------------------------------------------<br/>Phase: PARDIST_SHD<br/>Note: 73999<br/>Description: PCON_620 and PARDIST_SHD upgrade phase: TG450 to TG453<br/><br/>-----------------------&lt; ******** AUG/25/03 &gt;--------------------------<br/>Phase: PARDIST_SHD<br/>Description: Error in ALTER TABLE generation for table \"DVPOOLTEXT\"<br/>The primary indexes of Tables GLS2CLUS and DVPOOLTEXT do not meet any valid namespace convention for indexes.<br/>The primary index of Table GLS2CLUS is GLS2CLU0, the primary index of Table DVPOOLTEXT is DVPOOLT0. Make the following corrections in the <strong>Original system</strong> (not in the shadow system):</p>\n<ol>1. Drop the two indexes GLS2CLU0 and DVPOOLT0 from the database.</ol><ol>2. Create the indexes again using Transaction SE14.</ol><ol>3. Repeat the upgrade phase with option \"Repeat\".</ol>\n<p><br/>-----------------------------------------------------------------------<br/>Phase: STARTR3_NBAS<br/>Description: Only for MS SQL and MSCS - termination<br/>A termination may occur when you combine MS SQL Servers and MSCS Cluster if disp/sna_gw_service = sapgw&lt;System-ID&gt; is not set in the profile<br/>.<br/>Set the parameter and repeat the phase.<br/><br/>-----------------------------------------------------------------------<br/>Phase: TABIM_UPG<br/>Note: 666787<br/>Description: Phase terminates with error message 2EETW000 Update and insert sequence failed due to conflicting unique indexes on table KPP1LOIO (see Note 626915 for details).<br/>Follow the steps described in the note.<br/><br/>--------------------------&lt; D019416 MAR/19/04 &gt;-----------------------<br/>Phase: TABIM_POST<br/>Note: 718912<br/>Description: Phase terminates with Error message DI829. In this case, import the changes from the note and continue with the upgrade. The changes must be reversed after the upgrade.<br/><br/>-----------------------------------------------------------------------<br/>Phase: PARCONV_UPG ***<br/>Note: 339126<br/>Description: SQL error in the database when accessing a table. Call report RADUTMST for the tables involved and then continue with Repeat.<br/><br/>-----------------------------------------------------------------------<br/>Phase: STARTR3_IMP<br/>Note: 525006<br/>Description: <strong>Only if you are using AIX and the Upgrade Assistant</strong><br/>The program R3up cannot start the system and terminates.<br/><br/>-------------------------&lt; D032354 AUG/08/06 &gt;------------------------<br/>Phase: XPRAS_UPG<br/>Note: 928035<br/>Description: If you did not include Support Package SAPKB62060 in the upgrade, the phase may terminate with a short dump and a syntax error in the SAPLSCPRPS program (The field UPD_SCPRPPRL is unknown).<br/>Import the above note and repeat the phase.<br/><br/>-----------------------------------------------------------------------<br/>Phase: XPRAS_UPG<br/>Note: 648372<br/>Description: There is an error when activating the WFTS BOR object type. In many cases, you can ignore this error (see Note), and it is solved using Support Package SAPKB62029.<br/><br/>--------------------&lt; changed D038245 FEB/26/04 &gt;---------------------<br/>-------------------------&lt; D038245 MAR/06/03 &gt;--------------------------<br/>Phase: XPRAS_UPG<br/>Description: If a database error occurs in this phase (for example, overflowing tablespace), R3up automatically repeats the phase. In some cases, R3up may continue to run, but posting is deactivated in the system. The following entry appears in the system log:<br/>The update task was deactivated.<br/>In this case, solve the database problem (for example, extend the tablespace), call Transaction SM14 and reactivate the update.<br/><br/>-------------------------&lt; D022030 JUL/09/02 &gt;------------------<br/>Phase: XPRAS_UPG<br/>Note: 534826<br/>Description: LONGPOST.LOG: The XPRA RMCSXP03 reports the M2392, M2108 or M2685 error. The note mentioned above contains more information on problems with update programs in addition to the solution to the error.<br/><br/>-----------------&lt;changed D028886 JUN/10/02 &gt;-----------------<br/>Phase: XPRAS_UPG<br/>Description: LONGPOST.LOG: You can ignore the following XPRA RMCSXP04 (SAPK46AXP5) entries:<br/>      3PEM2579 Client \"xxx\":<br/>      Problem adapting \" \"/\" \"<br/>      to updating IS \"S216\" (TMC2F)<br/>where xxx is a client in your system.  The same applies to \"S217\" and \"S218\".<br/><br/>-------------------&lt; changed D030326 JUN/21/02 &gt;------------------------<br/>Phase: JOB_RSTLANUPG<br/>Note: If necessary 196113<br/>Description: Under certain circumstances, the report RSTLAN_UPGRADE called in this phase may terminate with an ABAP dump message<br/>      ABAP/4 runtime error (short dump) in SAPLSBAL_DB_INTERNAL<br/>      program            SAPLSBAL_DB_INTERNAL<br/>      include          LSBAL_DB_INTERNALU02<br/>      000540  IF NOT i_s_db_tables-balhdr_i IS INITIAL.<br/>      =====&gt;     INSERT balhdr CLIENT SPECIFIED FROM TABLE<br/>terminated If this happens, repeat the phase again. If the error persists, implement Note 196113.<br/><br/>-----------------------------------------------------------------------<br/>Phase: JOB_RDDNTPUR<br/>Description: For Source Release 4.6B, the following error message may appear in the log LONGPOST.LOG:<br/>Table and runtime object \"TSTCCLASS\" exist without DDIC reference<br/>You can ignore this error message.<br/><br/>-----------------------------------------------------------------------<br/>Phase: CHK_POSTUP<br/>Note: 197886<br/>Description: If you have implemented Notes 178631, 116095 or 69455 in your system before the upgrade, the LONGPOST.LOG log contains error messages for objects that lack a DDIC reference.<br/>Follow the steps described in the note.<br/><br/>----------------------------------------------------------------------<br/>Phase: CHK_POSTUP<br/>Description: For Source Release 4.6 B, tables SPRTL1, SPRTL3 and SPRTL9 may appear with the following error message in the log LONGPOST.LOG:<br/>Table and runtime object \"SPRTL1\" exist without DDIC reference<br/>You can ignore this error message.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/><br/></p>\n<p><strong>VIII/  Problems after the upgrade</strong><br/><br/></p>\n<p>In this column you can find known problems that you cannot avoid with preventive measures. These problems only occur under very specific conditions.<br/><br/>------------------------&lt; D035318 JUN/08/04 &gt;-------------------------<br/><strong>Source Release Basis 6.10 and lower: Conversion of code pages</strong><br/>The code page administration is enhanced with Basis Release 6.10. In order that customer defined (printer) code pages beginning with 9 can continue to be used after the upgrade, you need to convert them after the upgrade using report RSCP0126.<br/>For more information on processing these add-ons, see <strong>Notes 485455</strong> and <strong>511732</strong>.<br/><br/>------------------------&lt; D038330 MAR/16/04 &gt;-------------------------<br/><strong>Source Release 4.0B SP C4 and higher: Terminations during settlement</strong><br/>If you use source release 4.0B with Support Package level SAPKE40BC4 or higher and you did not use conversion report UMSETZEN_PCL2_RD, you must see <strong>Note 700124</strong>.<br/>Otherwise, terminations may occur during settlement or when displaying settlement results<br/>.<br/><br/>-------------------------&lt; I019889 JUL/21/03 &gt;----------------------<br/><strong>Payroll Great Britain: Syntax error in Report RPCALCG0</strong><br/>When you get syntax error 'The FORM \"FUXDEC\" does not exist' in Report RPCALCG0, proceed as described in Note <strong>642937</strong>.<br/>The problem has been solved with HR Support Package 18.<br/><br/>-------------------------&lt; D020815 MAR/25/03 &gt;------------------<br/><strong>User exits in SPAU</strong><br/>Due to the changed procedure for the modification adjustment during the upgrade, users exits that were delivered with earlier releases and used by customers are now displayed as modifications and must be adjusted.<br/>For more information, see <strong>Note 607893</strong>.<br/><br/>--------------------&lt; enhanced D027999 OCT/07/02 &gt;----------------------<br/>-------------------------&lt; D027999 SEP/27/02 &gt;------------------<br/><strong>Problems with user exits and customer enhancements</strong><br/>With the SAP Web application server, new and intensified rules for the (Unicode-capable) ABAP syntax and runtime were introduced (which apply to both Unicode and non-Unicode installations). These stricter checks may result in syntax error messages for user exits and customer enhancements.</p>\n<ul>\n<li>For information about eliminating possible errors with <strong>user exits</strong>, see <strong>Note 553110</strong>.</li>\n</ul>\n<ul>\n<li>For information about eliminating possible errors with <strong>customer enhancements</strong>, see <strong>Note 493387.</strong></li>\n</ul>\n<p><br/>------------------------&lt; D020815 AUG/23/02 &gt;-------------------<br/><strong>SPAU: Names of interface methods are truncated</strong><br/>In Transaction SPAU, some methods that were modified and overwritten by the upgrade (ABAP objects) may be displayed in abbreviated form (truncated to 30 characters). As a result, methods in Transaction SPAU may also be sorted incorrectly under \"Deleted objects\".<br/>Note: Deleted objects are not displayed for the standard selection in Transaction SPAU. It is very easy to overlook these.<br/>For more information about the correction, see<strong> Note 547773</strong>.<br/><br/>--------------------&lt; changed D032781 MAY/06/03 &gt;---------------------<br/>-------------------------&lt; D020847 AUG/13/02 &gt;-------------------------<br/><strong>Windows/Microsoft Cluster Server (MSCS)</strong><br/>If you perform an upgrade under MSCS, you must carry out the post-upgrade activities described in <strong>Note 544988</strong>. To do this, you require a special installation CD which you must order separately.<br/><br/>----------------------------------------------------------------------<br/><strong>Linux:  Importing the new saposcol version</strong><br/>For more information, see <strong>Note 19227.</strong><br/><br/>----------------------------------------------------------------------<br/><strong>Solaris: saposcol version 32 or 64-bit</strong><br/>For more information, see <strong>Note 162980.</strong><br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>IX/ Contents in chronological order</strong></p>\n<p><br/>Date.......Topic.....Short description<br/>-----------------------------------------------------------------------<br/>NOV/28/06.....V...Modification adjustment planning/Unicode conversion<br/>08/AUG/06...VII...Phase XPRAS_UPG - Program SAPLSCPRPS<br/>JUN/21/06...III...Entries for Parameter Input Module: Path length<br/>27/APR/06....VI...Treatment of customer-specific translations in the upgrade<br/>NOV 16 05....II...Do not include SP SAP_BASIS 56 in the upgrade<br/>SEP/15/05.....V...Saving customer-specific entries in the EDIFCT table<br/>09/AUG/05...III...Opt. Postprocessing Where-used list<br/>JUN/02/05.....V....Do not include ABA Support Package 51 in the upgrade<br/>APR/20/05...VII...Phase RFCCHK_INI: Name or Password is Incorrect<br/>11/MAR/05...III...Windows manual section: Database-Spec. Parameters.<br/>FEB 09 05...III...Section: Making Entries for the Extension Module<br/>FEB/02/05....II...LSMW now part of SAP_BASIS<br/>OCT/20/04...III...Section: Phase JOB_RSVBCHCK2<br/>OCT/20/04.....V...Function group in the customer namespace<br/>OCT 14 04....V....Source Release 4.0B: Project-Related Incoming Orders<br/>OCT 14 04....V....Component PS-ST-WBS: Preliminary Admission Field KIMSK<br/>OCT 14 04....VI...Source Release 3.1I: Loss of Address Data<br/>OCT 06 04....VI.. Source Release 4.6C and lower: SMODILOG entries<br/>16/AUG/04...II...Including SAP_BASIS 6.20 SP42<br/>JUL 26 04...VII.. Source Release 3.1I / 4.0B: Phase TR_CMDIMPORT_FDTASKS<br/>JUL 08 04..VIII.. Source Release lower than 6.10: Conversion of code pages<br/>JUL/06/04...III.. Phase INITSHD: Instance number of the shadow instance<br/>23/JUN/04....II.. Use the current version of R3trans<br/>MAR 19 04...VII.. Phase TABIM_POST: Error DI829<br/>MAR 16 04..VIII.. Source Rel. 4.0B: Terminations during settlement<br/>MAR 04 04.....V.. Basis Support Package 37<br/>FEB 26 04...III.. DDIC user needs SAP_ALL authorization<br/>FEB 13 04.....V.. SPDD - Reset to standard<br/>JAN 27 04.....V.. Including Basis Support Package 35<br/>DEC 17 03....VI.. Delete old application logs<br/>DEC 11 03....VI.. Use of the EXT Kernel during the upgrade<br/>05/DEC/03....II.. Source Rel. 4.6B/4.6C: Problems w. Table kpp1loio<br/>05/DEC/03...VII.. Phase TABIM_UPG - Termination<br/>01/DEC/03....II.. Use the current version of R3trans<br/>NOV 11 03.....V.. Data loss in Table PA0616<br/>SEP 19 03...VII.. Phase XPRAS_UPG: Activating the BOR object type WFTS<br/>AUG 27 03....VI.. Adjusting application-specific data<br/>25/AUG/03...VII.. Phase: PARDIST_SHD Error in ALTER TABLE generation<br/>30/JUL/03...VII...Exchanging the target release kernel<br/>21/JUL/03..VIII.. Payroll GB: Syntax error in Report RPCALCG0<br/>02/JUL/03...III...Only for Windows: Wrong note for MSCS postprocessing<br/>27/JUN/03....II.. Source Release 3.1: Integrate Fix Pack 22<br/>26/JUN/03...III...Date of the R3trans required for the source release<br/>23/JUN/03...III.. Error in parameter rdisp<br/>08/MAY/03....II.. Support Package statuses in SAP R/3 Enterprise 4.70 Support Release 1<br/>08/MAY/03.....V.. Including certain Support Package levels<br/>02/MAY/03.....V.. Loss of customer matchcode IDs<br/>APR/24/03....VI...Upgrade on AIX: saposcol<br/>APR 08 03.....V.. Archiving of work items<br/>28/MAR/03.....V.. Missing contents for proxy generation of the XI<br/>MAR 25 03...VIII..User exits in SPAU<br/>06/MAR/03...VII.. Phase: XPRAS_UPG - Update<br/>FEB 25 03...VII.. Phase: JOB_RDDNTPUR - TSTCCLASS<br/>FEB 24 03...VII.. Phase: Phase: CHK_POSTUP - Tables SPRTL1, SPRTL3, SPRTL9<br/>SEP/27/02..VIII...Problems with user exits<br/>20/SEP/02....II...Windows NT only: Check the tp version<br/>SEP/17/02.....V...DDIC password<br/>SEP/13/02...VII...MS SQL with MSCS only: STARTR3_NBAS - Termination<br/>SEP 05 02...II... Upgrade and Unicode conversion<br/>02/SEP/02...II... SAP J2EE Engine no longer needed<br/>23/AUG/02..VIII...SPAU: Interface method names are truncated<br/>AUG/13/02..VIII...Windows/MSCS: Follow-up procedure<br/>AUG/09/02...VII...PARCONV_UPG phase: SQL error<br/>08/AUG/02...V.....Adjust profile parameters<br/>AUG/07/02...VII...SHADOW_IMPORT_ALL phase: Duplicate key error<br/>07/AUG/02...II... Retrofit add-ons<br/>JUL/19/02....II...Problems with the shadow instance<br/>09/JUL/02...VI... Upgrade with reintegrated add-ons<br/>JUL/09/02...VII...Phase XPRAS_UPG: LONGPOST.LOG: XPRA RMCSXP03<br/>JUL/08/02....VI...Upgrade with 32-bit operating systems<br/>JUL 02 02...VI... Upgrade with add-on PI<br/>24/JUN/02...VI... Database archive used<br/>24/JUN/02...VI... Storage requirements in the database<br/>JUN 21 02..VII... Phase JOB_RSTLANUPG - termination<br/>MAY 23 02 ..VII.. XPRAS_UPG: LONGPOST.LOG: XPRA RMCSXP04 (SAPK46AXP5)<br/>JUN 04 02.VII.... Phase STARTR3_IMP with AIX - system does not start<br/>MAY/24/02....II...Corrections and repairs for the upgrade<br/>MAY 23 02..VII... Phase CHK_POSTUP - objects without DDIC reference<br/>OCT/12/01....VI...AIX Application Runtime Version *******.<br/>FEB/20/02.....I...R3up keyword<br/>OCT/19/00..VIII...Linux:  Importing the new saposcol version<br/>SEP/04/00...VII...Windows NT: Error message in the PREPARE<br/>FEB/16/00..VIII...saposcol Version 32 or 64-bit on Solaris<br/><br/><br/>-----------------------------------------------------------------------</p></div>", "noteVersion": 79}, {"note": "759407", "noteTitle": "759407 - EU_INIT job is started repeatedly/you want to stop this job", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The EU_INIT background job is started repeatedly, and therefore runs in parallel. This causes deadlocks on the database for the tables WBCROSSI, CROSS, and WBCROSSGT.<br/>The system schedules the EU_INIT job again and again even though you do not request it.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Where-used list, SE84, navigation index</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is due to a program error, incorrect job scheduling, and the job has a long<br/>runtime and may only be started once.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement advance corrections or hot package.<br/><br/>Information on EU_INIT:<br/>Either the program SAPRSEUC or SAPRSEUB runs behind the EU_INIT job. For more information about this, see Note 18023. The job is based on the complete where-used list index in the system and therefore has a very long runtime. Generally, the job must run only in the systems in which you require a where-used list of objects. This generally applies only to the development systems. The job may only run once. Due to its long runtime, you frequently cannot end the job in a day or cannot stop it for other reasons (for example, system resources). This is not a problem because the job has an automatic restart mechanism.<br/>The procedure is as follows: If the job has completely finished once, an end indicator is set in the system. The job EU_PUT that runs daily checks whether this end indicator is set or whether EU_INIT is still running.  If the EU_INIT is not currently running and the end indicator is not set, EU_PUT reschedules the EU_INIT for 20:00.  The EU_INIT job continues working at the point at which it was stopped.<br/>If the job EU_INIT prevents daily operation, you can stop it without any problems because it automatically restarts in the evening.<br/><br/>If you do not require the job EU_INIT to be scheduled or restarted, you can achieve this by executing the program SAPRSEUB_STOP or the equivalent program ZSAPRSEUB_STOP.<br/>Which of the two programs is now available or can be made available depends on the Basis Release level:</p> <ul><li> Releases 6.10 to 6.40</li></ul> <p>           Create the customer-specific program ZSAPRSEUB_STOP. You will find the source code for this in correction instruction 875591 of this note.<br/>           Note: The source code is valid for Release 6.10 to 6.40 only.</p> <ul><li>7.0x releases</li></ul> <p>           Use the Note Assistant (transaction SNOTE) to implement correction instruction 838097 of this note. As a result, the report SAPRSEUB_STOP is created.</p> <ul><li>As of Release 7.10</li></ul> <p>           The report SAPRSEUB_STOP is contained in the standard system. <br/>           <br/>Execute one of the two programs once. This prevents the system from rescheduling EU_INIT after it is terminated or stopped.<br/>EU_INIT must not be active when you execute the program.<br/><br/>The solution is also planned for productive systems in which the job was inadvertently or automatically started, and the run is not requested.<br/><br/>The restart mechanism remains switched off until the EU_INIT job is directly restarted by a user.<br/>Note that supressing the EU_INIT job stops the setup of indexes for the where-used list. You can no longer guarantee a correct function of the where-used list in this system.</p></div>", "noteVersion": 8}, {"note": "737696", "noteTitle": "737696 - Add. info on upgrade to SAP R/3 Enterprise 4.70 Ext. 2 SR1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Errors in the upgrade or update procedure or in the upgrade guides; preparations for the upgrade or update; additional information to the upgrade guide.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Update, upgrade, SUM, Software Update Manager, SAP R/3 Enterprise</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>*</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>CAUTION: This note is updated constantly</strong><br/><strong>Therefore, you should read it again immediately before you perform the upgrade.</strong><br/><strong>CAUTION: This note only applies to the upgrade to SAP R/3 Enterprise Core 4.70 with Extension Set 2.00 Support Release 1.</strong></p>\n<p><strong>What can you expect from this note?</strong></p>\n<p>This note specifies problems that may occur when you upgrade the system and provides you with solutions. In most cases, it refers to other notes for the solutions.<br/>The main purpose of this note is to prevent data loss, upgrade shutdowns and long runtimes.<br/>It deals with database-independent problems only.</p>\n<p><strong>What can you not expect from this note?</strong></p>\n<p>Problems after the upgrade are only dealt with if they are caused directly by the upgrade tools.</p>\n<p><strong>Which notes do you also need to prepare for the upgrade?</strong></p>\n<p>This depends on the functions that you are using. You require one or more of the following notes:<br/><br/>Short text .............................................. Note number<br/>_____________________________________________________________________<br/>Additional info on upgrade to SAP Web AS 6.20 SAP........... 490325<br/>Additions upg. SAP R/3 Enterprise Core 4.70 Ext. 2:......... 640897<br/>DB6: Additional information on upgrading to SAP Web......... 490095<br/>DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00.......... 640516<br/>Enhancements to upgrading to SAP R/3 Enterprise 4.70........ 517070<br/>Add info about upgrade to SAP Web As 6.20 ORACLE............ 575280<br/>Add. info on upgrading Enterprise 4.7 Extension Set......... 639099<br/>_____________________________________________________________________<br/>Current note on the language import in Release 6.20 ........ 482462<br/>Language import and Support Packages . ..................... 352941<br/>_____________________________________________________________________<br/>Corrections for the upgrade to systems with Basis 620 ...... 517731<br/>Repairs for upgrade to Basis 620 .................. ........ 522711<br/>_____________________________________________________________________<br/>Known problems with Support Packages in Basis Release 6.20 . 447925<br/>_____________________________________________________________________<br/><br/>Use the Software Update Manager 1.0 for an update or upgrade to SAP R/3 Enterprise 4.70 Ext. 2 SR1. You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p><br/><br/><br/>---------------------------------------------------------------------</p>\n<p><strong>Contents</strong></p>\n<p>I/ ..... Keyword<br/>II/ .... Important general remarks<br/>III/ ... Corrections to the guides<br/>IV/ .... Errors on the CD ROM<br/>V/ ......Avoiding data loss, upgrade shutdowns and long runtimes<br/>VI/ .... Preparing for the upgrade<br/>VII/ ... Problems in the PREPARE and upgrade phases<br/>VIII/ .. Problems after the upgrade<br/>IX/ .... Chronological summary</p>\n<p><strong>I/ Keyword</strong><br/><br/>Keywords are no longer applicable.<br/>(For information only: The R3up keyword was 83478.)</p>\n<p><strong><br/>II/ Important general information</strong></p>\n<p><strong>**************************************************</strong></p>\n<p>The former tool R3up was replaced by the Software Update Manager (SUM). See also SAP Note 1589311 and the blog:<br/><a href=\"https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/\" target=\"_blank\">https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/</a>.</p>\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP R/3 Enterprise 4.70 Ext. 2 SR1.</p>\n<p>You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p><strong>**************************************************</strong></p>\n<p> </p>\n<p> </p>\n<p>--------------------&lt;changed D030559 16/SEP/05 &gt;----------------------<br/>-----------------------&lt; D025323 MAY/24/02 &gt;--------------------------<br/><strong>Corrections and repairs for the upgrade</strong><br/>Before the upgrade, you must replace the R3up with the <strong>latest valid R3up version</strong> for your specific upgrade.<br/>For more information, <strong>see note 517731</strong>.<br/>Furthermore, check if repairs have been made to the ABAP programs of the upgrade for your specific upgrade.<br/>For more information, see <strong>note 522711.</strong><br/><strong>You must use these two notes in order to avoid serious problems during the upgrade!</strong><br/><br/>-----------------------&lt; D028310 JUL/19/02 &gt;--------------------------<br/><strong>Problems with the shadow instance</strong><br/>The following notes contain information on problems with the shadow instance:</p>\n<ul>\n<li><strong>525677</strong>: Problems starting the shadow instance</li>\n</ul>\n<ul>\n<li><strong>430318</strong>: Remote shadow instance on another operating system</li>\n</ul>\n<p><br/>-----------------------&lt; D028310 AUG/16/04 &gt;---------------------------<br/><strong>Including Support Package SAP_BASIS 6.20 SP42</strong><br/>If you want to include SAP_BASIS 6.20 SP42 into the upgrade, make sure that R3up has version 15.90 or higher. If R3up does not have this version, an error in the XPRA phase can occur.<br/><br/>-----------------------&lt; D039055 AUG/10/04 &gt;--------------------------<br/><strong>Exchange Kernel after the Upgrade</strong><br/>After the upgrade, exchange the target release Kernel for the current Kernel from SAP Service Marketplace.<br/><br/>-------------------&lt;Changed D022030 NOV/18/04 &gt;-----------------------<br/>-------------------&lt;Changed D022030 DEC/08/03 &gt;-----------------------<br/>-----------------------&lt; D022030 JUL/23/03 &gt;--------------------------<br/><strong>Support Package versions in SAP Enterprise 4.70 Ext. Set 2.00 Support Release 1</strong><br/>The \"SAP R/3 Enterprise Core 4.70 with SAP R/3 Enterprise Extension Set 2.0 Support Release 1\", which is on the delivered upgrade CDs, contains the following Support Package versions:<br/>Main components:</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP_BASIS 620</td>\n<td>Support Package 41</td>\n</tr>\n<tr>\n<td>SAP_ABA 620</td>\n<td>Support Package 41</td>\n</tr>\n<tr>\n<td>SAP_HR 470</td>\n<td>Support Package 30</td>\n</tr>\n<tr>\n<td>SAP_APPL 470</td>\n<td>Support Package 21</td>\n</tr>\n</tbody>\n</table></div>\n<p>Add-Ons</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>EA-HR 200</td>\n<td>Support Package 11</td>\n</tr>\n<tr>\n<td>EA-APPL 200</td>\n<td>Support Package 07</td>\n</tr>\n<tr>\n<td>EA-PS 200</td>\n<td>Support Package 07</td>\n</tr>\n<tr>\n<td>EA-IPPE 200</td>\n<td>Support Package 13</td>\n</tr>\n<tr>\n<td>EA-FINSERV 200</td>\n<td>Support Package 07</td>\n</tr>\n<tr>\n<td>EA-RETAIL 200</td>\n<td>Support Package 07</td>\n</tr>\n<tr>\n<td>EA-GLTRADE 200</td>\n<td>Support Package 07</td>\n</tr>\n<tr>\n<td>EA-DFPS 200</td>\n<td>Support Package 07</td>\n</tr>\n<tr>\n<td>ABA_PLUS 100</td>\n<td>Support Package 09</td>\n</tr>\n</tbody>\n</table></div>\n<p>Plug-Ins</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>PI_BASIS 2004_1_620 Support Package 03</td>\n</tr>\n<tr>\n<td>PI 2004_1_470</td>\n<td>Support Package 01</td>\n</tr>\n</tbody>\n</table></div>\n<p><br/>-------------------------&lt; D032986 JUL/09/02 &gt;------------------------<br/><strong>Upgrading with reintegrated add-ons (retrofit)</strong><br/>For R/3 Enterprise 4.70, more add-ons were reintegrated into the SAP R/3 Enterprise Core or the Extension Set.<br/>Note <strong>625318</strong> contains additional information on processing these add-ons before, during, and after the upgrade.<br/><br/>-------------------------&lt; D029035 SEP/05/02 &gt;------------------------<br/><strong>Upgrade and Unicode conversion</strong><br/>Currently SAP does not support any upgrade with simultaneous Unicode conversion. To obtain a Unicode system, you must first perform an upgrade from the non-Unicode system with a release version lower than 4.70 (SAP Web AS version lower than 6.20) to Release 4.70. You can then perform the Unicode conversion.<br/>For more information, see <strong>note 548016</strong>.<br/><br/>------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br/><strong>LSMW now part of SAP_BASIS</strong><br/>As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW and your source release is based on SAP Web AS 6.10 or lower, do not implement LSMW after the upgrade.<br/>For more information, see <strong>Note 673066</strong>.<br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>III/ Corrections to the guides</strong></p>\n<p><br/>------------------------&lt; D024991 OCT/19/06 &gt;-------------------------<br/><strong>Data Management Planning - Link to SAP Service Marketplace</strong><br/>Site service.sap.com/dao on SAP Service Marketplace is temporarily underconstruction. Please refer to the following site and document instead:<br/>service.sap.com/data-archiving -&gt; Media Library -&gt; Literature &amp; Brochure-&gt; Data Management Guide<br/><br/>------------------------&lt; D022030 JUN/21/06 &gt;-------------------------<br/><strong>Section: Making Entries for the Parameter Input Module</strong><br/>The maximum length of the mount directory path is <strong>50</strong> characters.<br/>It may not contain any blanks or special characters.<br/><br/>------------------------&lt; D030328 09/AUG/05 &gt;-------------------------<br/><strong>Optional Follow-Up Activity: Where-used list in the Workbench</strong><br/>As of release 6.10, the where-used list for Dictionary objects has changed. If you need a proper display of the list, you need to run report SAPRSEUB after the upgrade.<br/>As the runtime of the report may be quite long, we recommend that you run it in the development system only.<br/>For more information, see Notes <strong>401389</strong> and <strong>28022</strong>.<br/><br/>------------------------&lt; I002675 MAR/11/05 &gt;-------------------------<br/><strong>Windows Guide Section on Database-Specific Parameters</strong><br/>In the Guide for the Upgrade on Windows, section \"Checking the Database-specific requirements for PREPARE\", you are asked to check the profile parameters for MS SQL server. As some of the parameter names have changed, SAP system tools may not recognize the parameters.<br/>For more information, see <strong>Note 826528</strong>.<br/><br/>------------------------&lt; D028310 09/FEB/05 &gt;-------------------------<br/><strong>Section: Making Entries for the Extension Module</strong><br/>In subsection \"Phase IS_SELECT\" the following applies to add-ons with status \"Undecided\":<br/>\"Your software vendor has predefined the strategy to choose for each add-on.<br/>For more information, see the SAP Note displayed by R3up or contact your software vendor.\"<br/><br/>------------------------&lt; D022030 NOV/25/04 &gt;-------------------------<br/><strong>Number of Upgrade CDs Containing Transport Requests</strong><br/>The upgrade package contains 13 Upgrade CDs with transport requests,<br/>not 11 as stated in the UNIX and Windows guides.<br/><br/>------------------------&lt; D023890 OCT/29/04 &gt;-------------------------<br/><strong>Section Phase ACT_REL: More information on SPDD</strong><br/>For detailed documentation on modification adjustment during SPDD and a list of FAQs, see SAP Service Marketplace at service.sap.com/SPAU.<br/><br/>------------------------&lt; D023890 OCT/29/04 &gt;-------------------------<br/><strong>Section Adjusting Repository Objects: More information on SPAU</strong><br/>For detailed documentation on modification adjustment during SPAU and a list of FAQs, see SAP Service Marketplace at service.sap.com/SPAU.<br/><br/>------------------------&lt; D001330 OCT/20/04 &gt;-------------------------<br/><strong>Section: Phase JOB_RSVBCHCK2</strong><br/>The text should say:<br/>\"If errors occur in this phase and you <strong>are still in</strong> production operation, you can skip these errors with ignore without entering a password\".<br/>---------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>IV/ Errors on the CD ROM</strong></p>\n<p><br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>V/ Preventing loss of data, upgrade shutdown and long runtimes</strong></p>\n<p><br/>-----------------------&lt; D000706 28/NOV/06 &gt;--------------------------<br/><strong>Modification Adjustment Planning and Unicode Conversion</strong><br/>You cannot import transport requests created in a Unicode SAP system into a non-Unicode SAP system. If you want to perform a Unicode conversion of your SAP system, create the transport request <strong>before </strong>the conversion.<br/><br/>------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br/><strong>Back up customer-specific entries in table EDIFCT</strong><br/>If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br/>For more information, see Note <strong>865142</strong>.<br/><br/>------------------------&lt; D030559 02/JUN/05 &gt;-------------------------<br/><strong>Do not include ABA Support Package 51 in the Upgrade</strong><br/>If you included Support Package SAPKA62051 in the upgrade, the upgrade fails in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br/>To prevent the failure, only include the Suport Package in the upgrade if you can also include at least Support Package <strong>SAPKA62053</strong> as the problem will be fixed with SP 53.<br/>For more information - also on the procedure in case of an upgrade failure, see <strong>note 849925</strong>.<br/><br/>------------------------&lt; D003551 JAN/14/05 &gt;-------------------------<br/><strong>Use Upgrade Correction from Note 522711</strong><br/>You must always use the latest correction for the upgrade tools as described in section II.<br/>For <strong>Source Release SAP R/3 Enterprise 4.70 Ext. Set 1.00</strong>, it is extremely important that you use the correction from <strong>note 522711</strong>, otherwise you will lose data during the upgrade.<br/><br/>------------------------&lt; D001330 OCT/20/04 &gt;-------------------------<br/><strong>Function Groups in the Customer Name Space</strong><br/>If you have created function groups in the customer namespace, you may lose data during the upgrade.<br/>For more information, see Note <strong>783308</strong>.<br/><br/>------------------------&lt; D031049 14/OCT/04 &gt;-------------------------<br/><strong>Source Rel. 4.0B: Project-Related Incoming Orders</strong><br/>If you are using the preliminary solution for project-related incoming orders published with Note 118780, you have to modify data elements before the upgrade.<br/>For more information, see <strong>note 369542</strong>.<br/><br/>------------------------&lt; D024329 14/OCT/04 &gt;-------------------------<br/><strong>Component PS-ST-WBS: Preliminary Inclusion of Field KIMSK</strong><br/>If you do not want the upgrade to overwrite field KIMSK, you can include the field in the table of modifiable fields before the upgrade.<br/>For more information, see <strong>note 185315</strong>.<br/><br/>---------------------&lt;changedt D038245 01/OCT/04 &gt;--------------------<br/>----------------------&lt; D038245 09/JUL/04 &gt;---------------------------<br/><strong>Adjusting Profile Parameter \"rsts/ccc/cachesize\"</strong><br/>Check whether the profileparameter \"rsts/ccc/cachesize\" has been set. If yes, delete it or set it to at least 6.000.000 Bytes as described in <strong>note 5470</strong>.<br/>Possible error messages are:</p>\n<ul>\n<li>\"segmentation fault\" or Dr. Watson Dump of the R3up in RFC- or \"JOB\"-Phases</li>\n</ul>\n<p>           In this case, you simply have to adjust the profile file and repeat the phase. You do not have to restart the system.</p>\n<ul>\n<li>Runtime error \"IMPORT_INIT_CONVERSATION_FAILED\"</li>\n</ul>\n<p>           The syslog contains the message:<br/>           \"E 14 Reorganized Buffer RSCPCCC with Length xxxxx (Overflow)\"<br/>           In this case, you have to adjust the profile file and restart the system before you can continue with the upgrade.<br/><br/>-----------------------&lt; D020815 FEB/13/04 &gt;--------------------------<br/><strong>SPDD - Reset on standard</strong><br/>If you select \"Reset to standard\" for all data elements that have already been adjusted, this starts an activation instead. If you want to reset adjusted data elements, proceed as described in <strong>note 705943</strong>.<br/><br/>------------------------&lt;C5020250 JUL/30/03 &gt;-------------------------<br/><strong>EH&amp;S: Reducing the number range interval of EHS_NEWPER</strong><br/>During the upgrade to \"Environment, Health and Safety (EHS)\" in \"SAP R/3 PLM Extension 2.00\", errors may occur with the \"EHS_NEWPER\" number range object.<br/>To avoid these errors, proceed as described in <strong>note 643431</strong>.<br/><br/>------------------------&lt;I023086 APR/08/03 &gt;--------------------------<br/><strong>Archiving work items to shorten the conversion time</strong><br/>With the transition to 46A, corrections were made in the SWWWIHEAD table to improve performance. These corrections require an (automatic) table conversion on the database.<br/>To minimize the conversion time, we urgently recommend that you archive and/or delete entries in these tables that are no longer required (work items in an end status) before the actual upgrade.<br/>To archive/delete the data, see the following <strong>notes: 49545, 145291, 153205, 159065.</strong><br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>VI/ Preparing the upgrade</strong></p>\n<p><br/>------------------------&lt; D044675 17/JAN/08 &gt;--------------------------<br/><strong>Upgrade from Source Release 4.6C: Including a minimum SP level</strong><br/>If your source release system includes SAP_HR 46C Support Package level D1, data loss may occur during the upgrade. To prevent any data loss, include at least Support Package 77 of the target release into the upgrade, although the upgrade program does not request it.<br/>Alternatively, update your system to SAP_HR 46C Support Package level D2 before the upgrade.<br/><br/>-----------------------&lt; D001330 27/APR/06 &gt;--------------------------<br/><strong>Handling of customer translation in the upgrade</strong><br/>Z languages or customer translations on system texts with transaction SE63 are not considered as modifications to the system by the upgrade and are therefore lost during the upgrade. As the SAP system may change considerably from one release to the next, it may not be worth saving the translations.<br/>If you think that it is worth saving your translations or languages, seeNote <strong>485741</strong> for more information.<br/><br/>------------------------&lt; D030022 DEC/12/05 &gt;-------------------------<br/><strong>SD Texts: Check Text Procedures in Transaction VOTX</strong><br/>Source Release 4.6C and lower: Before the upgrade, check your text procedures as described in Note <strong>751690</strong>.<br/><br/>------------------------&lt; D030022 14/OCT/04 &gt;-------------------------<br/><strong>Source Rel. 3.1I: Loss of Address Data</strong><br/>If you are using component \"Plant Maintenance\" (PM-WOC-MN) and convert the addresses with report  RSXADR05 before the upgrade from Source Release 3.1I, make sure to read <strong>note 360929 </strong>.<br/>Otherwise you may lose address data during the upgrade.<br/><br/>----------------------&lt; D033898 06/OCT/04 &gt;---------------------------<br/><strong>Source Release 4.6C and below: SMODILOG Entries</strong><br/>In Releases 4.6C and below, when you created customer-specific parametereffectivities, the system did not make SMODILOG entries. In order not tolose these customer-specific parameter effectivities during the upgrade,proceed as described in <strong>note 741280</strong>.<br/><br/>----------------------&lt; D019011 DEC/17/03 &gt;---------------------------<br/><strong>Deleting old application logs</strong><br/>As of Release 4.6C, the application logs are stored in a new format. To be able to continue reading old application logs, the R/3 system must convert these into the new format. To avoid performance problems with conversion, you can no longer delete required application logs from releases lower than 4.6C.<br/>For more information, see <strong>note 195157</strong>.<br/><br/>--------------------------&lt; D038245 OCT/28/03 &gt;-----------------------<br/><strong>Source Release Extension Set 1.10: Exchange containers</strong><br/>If your system was installed with SAP R/3 Enterprise Ext. Set 1.10 (based on SAP Web AS 6.20) and you are using a database that uses different containers for saving data (Oracle, Informix and DB2 UDB for UNIX and Windows), refer to <strong>note 674070</strong> before the upgrade.<br/>Otherwise, the exchange containers (tablespaces/dbspaces) cannot be emptied during the upgrade and cannot be deleted after the upgrade.<br/><br/>--------------------------&lt; D022188 AUG/27/03 &gt;-----------------------<br/><strong>Adjusting application-specific data</strong><br/>To avoid problems when adjusting application-specific data, (especially report variants), refer to <strong>notes 623723</strong> and <strong>626408</strong>.<br/>The notes also contain information about postprocessing the upgrade.<br/><br/>--------------------------&lt; D025323 APR/24/03 &gt;-----------------------<br/><strong>Upgrading with AIX: saposcol</strong><br/>Before the upgrade, see notes <strong>526694</strong> and <strong>569015</strong>.<br/><br/>-------------------------&lt; D025323 JUL/08/02 &gt;------------------------<br/><strong>Upgrade with 32-bit operating systems</strong><br/>If your source release system is on operating system version AIX 32-bit, HP-UX 32-bit or SUN-OS 32-bit, switch to a 64-bit version <strong>before the PREPARE</strong>, preferably directly to the 64-bit version prescribed for the target release.<br/>If you are using AIX 4.3 64-bit, other actions are required before the upgrade.<br/>For more information, see <strong>notes 496963</strong> and <strong>499708</strong>.<br/><br/>-----------------------&lt; D021371 OCT/12/01 &gt;--------------------------<br/><strong>Only for AIX: Application Runtime</strong><br/>For an upgrade with AIX, you require at least AIX Application Runtime Version *******.<br/>Use the command lslpp -l xlC.rte<br/>to check whether C set ++ is installed.<br/>If it is not installed, or if the incorrect version is installed, download it from your country version of the IBM AIX Fix Distribution Web page or from the US Web page: http://service.software.ibm.com/support/rs6000<br/>As user root, install the version using smit (-&gt; software).<br/>If you also require the \"Base Level Fileset\" (xlC.rte.5.0.0.0), download the vacpp5_runtime.tar.Z file from: ftp://ftp.software.ibm.com/aix/products/ccpp/<br/><br/>--------------------&lt; Added by D032986 JUL/05/02 &gt;--------------------<br/>-------------------------&lt; D028310 JUL/02/02 &gt;------------------------<br/><strong>Upgrade with PI or PI-A add-on or their predecessors BW-BCT, APO-CIF , B2B-PRO-PI and CRM-R3A</strong><br/>PI Release 2004_1_470 is part of the R/3 Enterprise delivery (R/3 Enterprise Core 4.70, R/3 Enterprise Extension 2.00 SR1).<br/>If you have already installed PI or PI-A or their predecessors in your R/3 source release, proceed as follows:</p>\n<ul>\n<li>Before you begin with PREPARE, you must have installed at least the PI-/PI-A- Release 2002.1 (technically 2002_1_40B, for instance). For more information, see notes <strong>748759</strong> and <strong>214503</strong>.</li>\n</ul>\n<ul>\n<li>Source Release 3.1I:</li>\n</ul>\n<p>           If your 3. 1I system contains the PI add-on with Release 2004_1_31I or lower, select \"Passive deletion\" (and not \"Supplement CD\" or \"Received\") in the IS_READ phase. Since the PI add-on is part of the export when you upgrade to R/3 Enterprise 4.70, you automatically receive the current version with the upgrade.<br/>           If the version in your 3. 1I system is greater than 2003_1_31I, you must import a Supplement CD in the IS_READ phase to prevent data loss.</p>\n<ul>\n<li>Source release greater than 3.1I:</li>\n</ul>\n<p>           If the PI or PI-A version in your R/3 system is greater than 2004_1, you must import a Supplement CD in the IS_SELECT phase to prevent data loss.<br/>           You will find more information in <strong>notes 748759</strong> and <strong>214503</strong>. You will find detailed information on the R/3 plug-in (contents, releases, dates, installation and upgrade strategy, maintenance) on the SAP Service Marketplace under the R3-PLUG-IN alias.<br/>If you have not installed PI or PI-A or their predecessors on your R/3 source release, you can perform the upgrade to R/3 Enterprise without installing PI or PI-A on the R/3 source release.<br/><br/>-------------------------&lt; D022030 SEP/10/03 &gt;------------------------<br/><strong>Relevant database archives</strong><br/>The archives used during the upgrade help you select an upgrade or archiving strategy. The first number of two indicates the size of the archives up to phase MODPROF_TRANS, while the second specifies the size of the archives up to the end of the upgrade.<br/>DB2 UDB for UNIX/Windows: 15 GB/20 GB<br/>Informix: 22 GB/27 GB<br/>MS SQL Server: 24 GB/30 GB<br/>Oracle: 30 GB/36 GB<br/>SAP DB: 15 GB/20 GB<br/>These sizes are based on sample data.<br/><br/>-------------------------&lt; D022030 SEP/10/03 &gt;------------------------<br/><strong>Space required on the database</strong><br/>You must enhance the database both during and throughout the upgrade. The first number of two specifies the enhancement required temporarily during the upgrade, whereas the second specifies the permanent database enhancement.<br/>DB2 UDB for UNIX/Windows: 23 GB/19 GB<br/>Informix: 30 GB/no specification<br/>iSeries: 25 GB/16 GB*<br/>MS SQL Server: 15 GB/no specification<br/>Oracle: 24 GB/20 GB<br/>SAP DB: 17 GB/10 GB<br/>* largely depends on the upgrade and archiving strategy because a number of journal receivers may arise during archiving.<br/>These sizes are based on sample data. Additional freespace requirements (depending on your system) are calculated during the PREPARE.<br/>To determine realistic freespace requirements, you should execute PREPARE.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>VII/ Problems in the PREPARE and Upgrade Phases</strong></p>\n<p><br/>This section describes problems that you cannot prevent using preventive measures. These problems can only occur in specific cases or if particular prerequisites apply.</p>\n<p><strong>Problems in the PREPARE phases</strong></p>\n<p><br/>-----------------------&lt; D038245 20/APR/05 &gt;---------------------------<br/><strong>Phase RFCCHK_INI: Name or Password is Incorrect</strong><br/>In phase RFCCHK_INI you may get the error message \"Name or Password is Incorrect\".<br/>If you replaced R3up after phase RFCCHK_INI with the latest version fromSAP Service Marketplace, this error may also come up in other phases that connect to the system using RFC.<br/>For more information, see Note <strong>792850</strong>. You may have to replace disp+work and restart the system.<br/><br/>-----------------------&lt; D022256 SEP/04/00 &gt;--------------------------<br/><strong>For Windows NT 4.0 only</strong><br/>If a dialog box containing the following error message appears during PREPARE: 'The procedure entry point ... could not be located in the dynamic link library ... .install the latest DLLs with the R3DLLINS.EXE program. This program is available on the SAP kernel CD in the \\NT\\&lt;processor type&gt;\\NTPATCH directory.<br/>Reboot your machine and use 'PREPARE repeat' to restart PREPARE.<br/><br/>-----------------------------------------------------------------------<br/><strong>As of Source Release 4.0A:</strong><br/>Termination in the TOOLIMPD1 or TOOLIMPD2 phases. For more information, see <strong>note 98198</strong>.<br/><br/>-----------------------------------------------------------------------<br/><br/></p>\n<p><strong>Problems in the Upgrade Phases</strong></p>\n<p><br/>------------------------&lt; D028310 20/AUG/04 &gt;--------------------------<br/>Phase: DIFFEXPDDIV<br/>Note: 766379<br/>Description:<br/>Error message in log file DIFFEXPD.ELG (directory &lt;DIR_PUT&gt;/log):<br/>INACTIVE DDIC VERSIONS-Export ERRORS and RETURN CODE in SAPEDDD622.QO1<br/>~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br/>2EETW190 \"TABT\" \"TESCL                        \" has no active version.<br/>...<br/><br/>-----------------------------------------------------------------------<br/>Phase: STARTR3_NBAS<br/>Description: Termination with MS SQL and MSCS only<br/>If you use MS SQL Server in conjunction with MSCS Cluster, a termination may occur if the disp/sna_gw_service = sapgw&lt;system ID&gt; parameter is not set in the profile. Set this parameter and repeat the phase.<br/><br/>--------------------------&lt; D019416 19/MAR/04 &gt;-----------------------<br/>Phase: TABIM_POST<br/>Note: 718912<br/>Description: termination of the phase with error message DI829. Implement the modification from the note and continue with the upgrade. The modification must be undone again after the upgrade.<br/><br/>-------------------------&lt; D032354 AUG/08/06 &gt;------------------------<br/>Phase: XPRAS_UPG<br/>Note: 928035<br/>Description: If you have not included Support Package SAPKB62060 in the upgrade, the phase may stop with a short dump and a syntax error in program SAPLSCPRPS (field UPD_SCPRPPRL unknown).<br/>Apply the above note and repeat the phase.<br/><br/>-------------------------&lt; D022030 JUL/09/02 &gt;------------------------<br/>Phase: XPRAS_UPG<br/>Note: 534826<br/>Description: LONGPOST.LOG: The XPRA RMCSXP03 reports the M2392, M2108 or M2685 error. In addition to the error solution, the note mentioned above also contains additional information about problems with update programs.<br/><br/>-------------------&lt; changed D030326 JUN/21/02 &gt;----------------------<br/>Phase: XPRAS_UPG<br/>Description: LONGPOST.LOG: You can ignore the following entries from XPRA RMCSXP04 (SAPK46AXP5): 3PEM2579 Client \"xxx\":<br/>Problem adapting \" \" / \" \"<br/>to updating IS \"S216\" (TMC2F)<br/>In this case, xxx is a client in your system.  The same applies to \"S217\" and \"S218\".<br/><br/>--------------------&lt; I010412/D036047 FEB/22/06 &gt;--------------------<br/>Phase: XPRAS_UPG<br/>Description: Phase stops with a short dump and the error message states that a syntax error has occured in program SAPLSCPRPS because there is no field UPD_SCPRPPRL.<br/>Solution: Within include FSCPRPPRLCDC, form CD_ALL_SCPRPPRL, turn several lines into a comment as follows: FORM CD_CALL_SCPRPPRL       .<br/>* not in use any more 18.04.2005<br/>*   IF   ( UPD_SCPRPPRL                       NE SPACE )<br/>*   .<br/>*     CALL FUNCTION 'SCPRPPRL_WRITE_DOCUMENT       '<br/>*        EXPORTING OBJECTID              = OBJECTID<br/>*                  TCODE                 = TCODE<br/>*                  UTIME                 = UTIME<br/>*                  UDATE                 = UDATE<br/>*                  USERNAME              = USERNAME<br/>*                  PLANNED_CHANGE_NUMBER = PLANNED_CHANGE_NUMBER<br/>*                  OBJECT_CHANGE_INDICATOR = CDOC_UPD_OBJECT<br/>*                  PLANNED_OR_REAL_CHANGES = CDOC_PLANNED_OR_REAL<br/>*                  NO_CHANGE_POINTERS = CDOC_NO_CHANGE_POINTERS<br/>*                  UPD_SCPRPPRL *                      = UPD_SCPRPPRL<br/>*          TABLES  XSCPRPPRL<br/>*                      = XSCPRPPRL<br/>*                  YSCPRPPRL<br/>*                      = YSCPRPPRL<br/>*     .<br/>*   ENDIF.<br/>*   CLEAR PLANNED_CHANGE_NUMBER.<br/>ENDFORM.<br/><br/><br/>-------------------&lt;Changed by D030326 on JUN/21/02 &gt;-----------------<br/>Phase: JOB_RSTLANUPG<br/>Note: 196113 (if required)<br/>Description: In certain cases, the RSTLAN_UPGRADE report called in this phase may terminate with an ABAP dump message:       ABAP/4 runtime error (short dump) in SAPLSBAL_DB_INTERNAL<br/>      Program           SAPLSBAL_DB_INTERNAL<br/>      Include           LSBAL_DB_INTERNALU02<br/>      000540   IF NOT i_s_db_tables-balhdr_i IS INITIAL.<br/>      =====&gt;     INSERT balhdr CLIENT SPECIFIED FROM TABLE<br/>If this occurs, repeat the phase. If the error persists, implement note 196113.<br/><br/>-------------------------&lt; D021211 03/MAR/04 &gt;-------------------------<br/>Phase: JOB_RDDNTPUR<br/>Description: The following error message may appear in the LONGPOST.LOG for the S214BIW1, S214BIW2, S219BIW2 and TA22EQU_WAO tables: Table and runtime object \"S214BIW1\" exist without DDIC reference<br/>You can ignore this error message.<br/><br/>-------------------------&lt; D028323 FEB/25/03 &gt;------------------------<br/>Phase: JOB_RDDNTPUR<br/>Description: With Source Release 4.6B, the following error message may appear in the LONGPOST.LOG log: Table and runtime object \"TSTCCLASS\" exist without DDIC reference<br/>You can ignore this error message.<br/><br/>-----------------------------------------------------------------------<br/>Phase JOB_RSTLANUPG<br/>Note: 626272<br/>Description: Termination; the log file for job RSTLAN_UPGRADE contains the following error message:<br/>\"..Job terminated after system exception ERROR_MESSAGE.\"<br/><br/>-----------------------------------------------------------------------<br/>Phase CHK_POSTUP<br/>Note: 197886<br/>Description: If you have implemented notes 178631, 116095 or 69455 in your system before the upgrade, error messages appear for objects without DDIC references in the LONGPOST.LOG log.<br/>Proceed as described in the above note.<br/><br/>----------------------------------------------------------------------<br/>Phase: CHK_POSTUP<br/>Description: With Source Release 4.6B, the SPRTL1, SPRTL3 and SPRTL9 tables may appear in the LONGPOST.LOG log with the following error message: Table and runtime object \"SPRTL1\" exist without DDIC reference<br/>You can ignore this error message.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/><br/></p>\n<p><strong>VII/ Problems after the upgrade</strong><br/><br/></p>\n<p>This section describes problems that you cannot prevent using preventive measures. These problems only occur in very specific cases or if certain prerequisites apply.<br/><br/>------------------------&lt; D034907 15/JUN/04 &gt;-------------------------<br/><strong>Syntax Errors in Function group FIN1 or FIN2</strong><br/>After the upgrade, apply <strong>note 745429</strong>. Otherwise, you will receive syntax errors in the function groups FIN1 or FIN2.<br/>For more information, see <strong>note 745429</strong>.<br/><br/>------------------------&lt; D038330 16/MAR/04 &gt;-------------------------<br/><strong>Source Release 4.0B SP C4 and higher: Terminations with payroll</strong><br/>***Only relevant for customers who use german payroll (component PY-DE)***<br/>If your source release is 4.0B with Support Package level SAPKE40BC4 or higher and you have not used the UMSETZEN_PCL2_RD conversion report, you must proceed as described in <strong>note 700124</strong>.<br/>Otherwise, terminations occur with payroll or with the display of payroll results.<br/><br/>-------------------------&lt; D019419 DEC/02/03 &gt;------------------------<br/><strong>Only for Add-On \"Cable Solution\": Transactions CD, VAS, YM: Using Standard Icons</strong><br/>After the upgrade, the standard icons are missing in the monitor transactions CD, VAS, YM.<br/>To use these, proceed as described in <strong>note 683657</strong>.<br/><br/>-------------------------&lt; I014389 OCT/07/03 &gt;------------------------<br/><strong>EA-PS 1.10: Postprocessing for XPRA RFMXPR49</strong><br/>If you are using the derivation tool that uses the 'DRULE' rule type, you receive an error message.<br/>Proceed as described in <strong>note 657229.</strong><br/><br/>-------------------------&lt; D032986 SEP/19/03 &gt;------------------------<br/><strong>PI 2003_1_470: Postprocessing for XPRA RMCSBWXP_COM</strong><br/>To ensure that no Business Content of the Logistics BW extraction is lost, you must implement <strong>notes 648565</strong> and <strong>651522</strong> after the upgrade.<br/>For more information, see <strong>note 748759.</strong><br/><br/>-------------------------&lt; D021211 SEP/09/03 &gt;------------------------<br/><strong>Ignore or delete views</strong><br/>If, after the upgrade, the \"BIW_ASSET_VIEW\" and \"ROOSOURCE_V\" views are displayed as incorrect in transaction DB02, you can ignore this or delete the views.<br/><br/>-------------------------&lt; C5020250 JUL/08/03 &gt;-----------------------<br/><strong>EH&amp;S: Postprocessing</strong><br/>Source Release 4.70 Ext. Set 1.10: For the area of Occupational Health, the legacy data must be converted to the new format <strong>before the first call</strong> of the transactions for Occupational Health.<br/>To do so, start the REHSH_X_BASIC_DATA_UPDATE (conversion of OH basic data) and REHSH_X_CONVERT_PERSON_ID (conversion of the PERSON_ID field to T7EHS00_MAPPERNR) reports.<br/>For more information, see <strong>notes 636841</strong> and <strong>636848</strong>.<br/><br/>-------------------------&lt; D020815 MAR/25/03 &gt;------------------------<br/><strong>User exits in SPAU</strong><br/>Due to the changed procedure for the modification adjustment during the upgrade, user exits that were delivered with earlier releases and used by customers are now displayed as modifications and must be adjusted.<br/>For more information, see <strong>note 607893</strong>.<br/><br/>--------------------&lt; D027999 OCT/07/02 &gt;-----------------------------<br/>--------------------&lt; D027999 SEP/27/02 &gt;-----------------------------<br/><br/><strong>Problems with user exits and customer enhancements</strong><br/>New and stricter rules for (Unicode-enabled) ABAP syntax and runtime have been introduced with the SAP Web Application Server, and these apply to both Unicode and non-Unicode installations. Due to the stricter checks, syntax errors may occur for exits and customer enhancements.</p>\n<ul>\n<li>For information on eliminating possible errors with <strong>user exits</strong>, see <strong>note 553110</strong>.</li>\n</ul>\n<ul>\n<li>For information on eliminating possible errors with <strong>customer enhancements, see <strong>note 493387</strong>.</strong></li>\n</ul>\n<p><br/>--------------------&lt; changed D032781 MAY/06/03 &gt;---------------------<br/>-------------------------&lt; D020847 AUG/13/02 &gt;------------------------<br/><strong>Windows/Microsoft Cluster Server (MSCS)</strong><br/>If you perform an upgrade on MSCS, you must perform the post-upgrade activities described in <strong>SAP note 544988</strong>.<br/><br/>----------------------------------------------------------------------<br/><strong>Linux: Import new saposcol version</strong><br/>For more information, see note <strong>19227</strong>.<br/><br/>----------------------------------------------------------------------<br/><strong>Solaris: saposcol version 32-bit or 64-bit</strong><br/>For more information, see note <strong>162980</strong>.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>IX/ Chronological summary</strong></p>\n<p><br/>Date.......Topic..Short Description<br/>-----------------------------------------------------------------------<br/>JAN/17/08....VI...Source Release SAP R/3 4.6C: Including a min. SP level<br/>MAR/02/07...VII...Phase JOB_RSTLANUPG: Termination<br/>NOV/28/06.....V...Modification Adjustment Planning/Unicode Conversion<br/>OCT/19/06...III...Data Management Planning - Link to SMP<br/>AUG/08/06...VII...Phase XPRAS_UPG - program SAPLSCPRPS<br/>JUN/21/06...III...Entries for Parameter Input Module: Path Length<br/>27/APR/06....VI...Handling of customer translation in the upgrade<br/>FEB/22/06...VII...Phase XPRAS_UPG - short dump with program SAPLSCPRPS<br/>DEC/12/05....VI...SD Texts: Check Text Procedures in Transaction VOTX<br/>15/SEP/05.....V...Back up customer-specific entries in table EDIFCT<br/>09/AUG/05...III...Opt. Follow-Up: Where-Used List<br/>02/JUN/05.....V...Do not include ABA Support Package 51 in the Upgrade<br/>APR/20/05...VII...Phase RFCCHK_INI: Name or Password is Incorrect<br/>11/MAR/05...III...Windows Guide Section on Database-Specific Parameters<br/>09/FEB/05...III...Section: Making Entries for the Extension Module<br/>02/FEB/05....II...LSMW now part of SAP_BASIS<br/>14/JAN/05.....V...Use Upgrade Correction from Note 522711<br/>25/NOV/04...III...Number of Upgrade CDs Containing Transport Requests<br/>29/OCT/04...III...Section Phase ACT_REL: More information on SPDD<br/>29/OCT/04...III...Section Adjusting Repository Obj.: More info on SPAU<br/>20/OCT/04...III...Section: Phase JOB_RSVBCHCK2<br/>20/OCT/04.....V...Function Groups in Customer Namespace<br/>14/OCT/04.....V...Source Rel. 4.0B: Project-Related Incoming Orders<br/>14/OCT/04.....V...Component PS-ST-WBS: Prel. Inclusion of Field KIMSK<br/>14/OCT/04....VI...Source Rel. 3.1I: Loss of Address Data<br/>06/OCT/04....VI...Source Release 4.6C and below: SMODILOG Entries<br/>AUG/20/04...VII...Phase: DIFFEXPDDIV<br/>AUG/16/04....II...Including SAP_BASIS 6.20 SP42<br/>AUG/10/04....II.. Exchange Kernel after the Upgrade<br/>JUL/09/04.....V.. Adjust Profile Parameter \"rsts/ccc/cachesize\"<br/>JUN/15/04...VII.. Syntax Errors in Function group FIN1 or FIN2<br/>MAR/19/04...VII.. Phase TABIM_POST: Error DI829<br/>MAR/16/04..VIII.. Source Release 4.0B: Terminations with payroll<br/>MAR/03/04...VII.. JOB_RDDNTPUR phase: Entries in LONGPOST.LOG<br/>FEB/13/04.....V.. SPDD - Reset on standard<br/>DEC/17/03....VI.. Deleting old application logs<br/>DEC/02/03..VIII.. CD, VAS, YM transactions: Standard icons<br/>OCT/28/03....VI.. Source Release Ext. Set 1.10: Exchange containers<br/>OCT/07/03..VIII.. EA-PS 1.10: Postprocessing for XPRA RFMXPR49<br/>SEP/19/03..VIII.. PI 2003_1_470: Postprocessing for XPRA RMCSBWXP_COM<br/>SEP/10/03....VI.. Space required in the database<br/>SEP/10/03....VI.. Relevant database archive<br/>SEP/09/03..VIII.. Ignore or delete views<br/>AUG/27/03....VI.. Adjusting application-specific data<br/>JUL/30/03.....V.. Shortening the EHS_NEWPER number range interval<br/>JUL/23/03....II.. Support Packages in SAP R/3 Enterprise 4.70 SR1<br/>JUL/08/03..VIII.. EH&amp;S: Postprocessing<br/>APR/24/03....VI.. Upgrade on AIX: saposcol<br/>APR/08/03.....V.. Archiving work items<br/>MAR/25/03..VIII.. User Exits in SPAU<br/>FEB/25/03...VII.. Phase: JOB_RDDNTPUR - TSTCCLASS<br/>FEB/24/03...VII.. Phase: CHK_POSTUP - SPRTL1, SPRTL3 and SPRTL9 tables<br/>SEP/27/02..VIII.. Problems with user exits<br/>SEP/13/02...VII.. MS SQL with MSCS only: STARTR3_NBAS - termination<br/>SEP/05/02....II.. Upgrade and Unicode conversion<br/>AUG/13/02..VIII.. Windows/MSCS: Postprocessing<br/>AUG/07/02...VII.. SHADOW_IMPORT_ALL phase: Duplicate key error<br/>JUL/19/02....II.. Problems with the shadow instance<br/>JUL/09/02....II.. Upgrade with reintegrated add-ons (retrofit)<br/>JUL/09/02...VII.. XPRAS_UPG phase: LONGPOST.LOG: XPRA RMCSXP03<br/>JUL/08/02....VI.. Upgrade with 32-bit operating systems<br/>JUL/02/02....VI.. Upgrade with the PI add-on<br/>JUN/21/02...VII.. JOB_RSTLANUPG phase- termination<br/>JUN/10/02 ..VII.. XPRAS_UPG: LONGPOST.LOG: XPRA RMCSXP04 (SAPK46AXP5)<br/>MAY/24/02....II.. Corrections and repairs for the upgrade<br/>MAY/23/02...VII.. Phase CHK_POSTUP - objects without DDIC reference<br/>OCT/12/01....VI.. AIX Application Runtime Version *******.<br/>FEB/20/02.....I.. R3up Keyword<br/>OCT/19/00..VIII.. Linux:  Import new saposcol version<br/>SEP/04/00...VII.. Windows NT: Error message in the PREPARE<br/>FEB/16/00..VIII.. saposcol Version 32 or 64-bit on Solaris<br/><br/><br/>-----------------------------------------------------------------------</p></div>", "noteVersion": 42}, {"note": "663522", "noteTitle": "663522 - Where-used list: Space required, DBIF_RSQL_SQL_ERROR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The jobs for setting up the EU_INIT or EU_REORG where-used list indexes terminate with the DBIF_RSQL_SQL_ERROR error. An error is not displayed in the job management of the job and the database error only appears in the dump analysis. Five hundred (500) terminations appear in the dump analysis.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SAPRSEUB, EU_REORG, EU_INIT, tablespace, CX_SY_OPEN_SQL_DB, ORA-01653</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>As of Release 610, the index for the where-used list needs considerably more storage space (more than 1 GB). The termination is caused by a lack of memory space.<br/>The reason for the number of terminations (500) is an automatic restart mechanism in the program. This automatic restart has been identified from the following reason:<br/>For programs that have difficult syntactic errors, a where-used list cannot be determined. As a result, the program can terminate. If this happens, the job is automatically started again and continues with the next program. Consequently, the index is created and all incorrect programs are filtered. Otherwise, the index setup would always terminate for the first incorrect program and would never finish.<br/>In the case of the error mentioned here, the restart is not required however it cannot be avoided. You cannot determine whether this type of error is a database error or a serious syntax error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Make enough space available in your system and start the job again. In the dump analysis, check that the job has run without database errors.</p></div>", "noteVersion": 3}, {"note": "920177", "noteTitle": "920177 - Where-used list incorrect for includes", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The where-used list for includes does not find all usage locations.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>WBCROSSI, SE38, where-used list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance correction or import the specified Support Package.<br/>To generate the where-used list index, you must rerun the SAPRSEUB report after you implement the advance correction. Schedule the report in the background. See Note 28022.<br/><br/><strong>For Release 6.20, note the following procedure for implementing the advance correction:</strong><br/>For Support Package level 55 and higher, class attributes and method interfaces are implemented automatically by the Note Assistant. For Support Packages lower than 55, class attributes and method interfaces must be created or changed in the class builder before transferring the sources. To do this, proceed as follows:</p> <ol>1. Implement the advance correction, but do not activate it.</ol> <ol>2. Proceed as follows: Call transaction SE24 (Class Builder) and change the following objects:</ol> <ul><li>Go to the 'Attributes' tab page.</li></ul> <ul><ul><li>Create the following class attributes:</li></ul></ul> <p>                    TABLE_WBCROSSI_RAHMEN Static Attribute Private Type WBCROSST<br/>                    TABLE_WBCROSSI_INCLUDES Static Attribute Private Type WBCROSST<br/>                    INSERT_INCLUDE Static Attribute Private Type CHAR1<br/>                    ALL_PROGRAMS Static Attribute Private Type PROGRAMTAB<br/>                    Change the reference type of the attribute TABLE_WBCROSSI to TY_WBCROSSI_TAB.<br/></p> <ul><li>Navigate to the 'Methods' tab page</li></ul> <ul><ul><li>Create the following methods with the relevant interfaces. The method type is 'Static Method' and visibility is 'Private'.</li></ul></ul> <p>                    Method CHECK_DELETE_WBCROSSI<br/>                    Import parameters P_DELETE_WA type WBCROSSI and Changingparameter P_DELETE_TABLE type WBCROSST .<br/>                    Method SELECT_WBCROSSI_INCLUDES (no parameters)<br/>                    Method SELECT_WBCROSSI_MASTER<br/>                    Import parameter P_PROGRAM_TAB Type PROGRAMTAB</p> <ul><ul><li>Change the typing of the P_WBCROSSI parameter of the GET_INDEX to TY_WBCROSSI_TAB method.</li></ul></ul> <ol>3. Save and activate the CL_WB_CROSSREFERENCE class.</ol> <p><br/><strong>For Release 6.40, proceed as follows to implement the advance correction:</strong></p> <ol>1. First of all, create the WBACTINDEX table in the system. To do this, call transaction SE11 and enter the 'WBACTINDEX' name in the 'Database table' field. Choose the 'Create' function.</ol> <p>           Enter the following values:<br/>           Package: SEUIX<br/>           Delivery class of the table:  'S' = 'System table, maint only by SAP, change = modification'.<br/>           Data Browser/Table View Maint:  Data Browser/Table View Maint:<br/>           'Display/Maintenance Allowed with Restrictions'.<br/>           Data class: SSRC<br/>           Size category: 0<br/>           Buffering not allowed<br/>           Enhancement Category: Can be enhanced (character-type)<br/>           The short description of the table is 'Update Programs for Navigation Index'. Create the PROGNAME field with the corresponding PROGNAME data element. Select the 'Key' and 'Initial Values' checkboxes for this field.</p> <ol>2. Activate the table.</ol> <ol>3. Now implement the advance corrections for Release 6.40.</ol> <p>           Important: Do not activate all objects yet. First of all, activate the function module: REPS_OBJECT_ACTIVATE separately. To do this, proceed as follows: If the dialog box for activating all objects is displayed when you implement the note, enter a parallel mode and activate the REPS_OBJECT_ACTIVATE function module. Confirm the activation when you implement the note.<br/>           <br/><strong>For Release 7.00, note the following procedure for implementing the advance correction:</strong><br/>Copy the advance correction for Release 7.00.<br/>Important: Do not activate all objects yet. First of all, activate the function module: REPS_OBJECT_ACTIVATE separately. After you have successfully activated the function module, you can activate all the other objects.<br/><br/><br/><br/><br/><br/><br/></p></div>", "noteVersion": 38}, {"note": "684406", "noteTitle": "684406 - Additional information on upgrading to SAP SCM 4.1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Errors in the upgrade or update procedure or in the upgrade guides; preparations for the upgrade or update; additional information to the upgrade guide.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Update, upgrade, SUM, Software Update Manager, SAP SCM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>*</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>CAUTION: This note is updated regularly.</strong><br/><strong>Therefore, you should read it again immediately before the upgrade. </strong></p>\n<p><strong>What can I expect from this note?</strong></p>\n<p>This note specifies problems that may occur when you upgrade the system and specifies notes on their solutions. As a result, most of this note refers to other notes.<br/>This note is mainly concerned with helping you to avoid loss of data, upgrade shutdowns and long runtimes.<br/>It only discusses database-independent problems.</p>\n<p><strong>What can I not expect from this note?</strong></p>\n<p>Problems after the upgrade are only discussed if they are directly caused by the upgrade tools.</p>\n<p><strong>Which other notes do I need to prepare for the upgrade?</strong></p>\n<p>The answer depends on the functions you use. To be more precise, you need one of more of the following notes:<br/><br/>Short text................................................   Note number<br/>___________________________________________________________<br/>Additional information on upgrade to WAS 6.40 MaxDB............ 669656<br/>Additional Information: Upgrade to SAP Web AS 6.40............. 661569<br/>DB6: Additions to upgrade (based) on SAP Web AS 6.40........... 662191<br/>DB2-z/OS: Additions upgrade to Basis 6.40 ..................... 661252<br/>Add. Info on upgrade to SCM41 SRM40 ECC50 MSQL................. 737115<br/>Add. info on upgrading to SAP Web AS 6.40 ORACLE............... 662219<br/>Additional information on the upgrade to ECC 5.0............... 700675<br/>_____________________________________________________________________<br/>Current note on the language transport in Release 6.40 . ...... 482462<br/>Language import and Support Packages ................... ...... 352941<br/>_____________________________________________________________________<br/>Corrections for the upgrade to Basis 640 .............. ....... 663240<br/>Repairs for upgrade to Basis 640 .............................. 663240<br/>_____________________________________________________________________<br/>OCS: Known problems with Support Packages in Basis Rel. 6.40... 672651<br/>_____________________________________________________________________<br/>Additional information on upgrading to liveCache 7.4.3 .........<br/>Prerequisites for upgrading to SCM 4.X ........................ 728545</p>\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP SCM 4.1. You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p><br/>---------------------------------------------------------------------<br/><strong>Contents</strong></p>\n<p>I/ ...... Keyword<br/>II/ ......Important general information<br/>III/ .....Corrections to the guides<br/>IV/ ......Errors on the CD-ROM<br/>V/ ... ...Preventing data loss, upgrade shutdown and long runtimes<br/>VI/ ..... Preparing for the upgrade<br/>VII/ .... Problems in the PREPARE and upgrade phases<br/>VIII/ ... Problems after the upgrade<br/>IX/ ..... Chronological summary</p>\n<p><strong>I/ Keyword</strong></p>\n<p>Keywords are no longer applicable.<br/>(For information only: The R3up keyword was 160421.)</p>\n<p><strong>II/ Important general information</strong></p>\n<p><strong>**************************************************</strong></p>\n<p>The former tool R3up was replaced by the Software Update Manager (SUM). See also SAP Note 1589311 and the blog:<br/><a href=\"https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/\" target=\"_blank\">https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/</a>.</p>\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP SCM 4.1.</p>\n<p>You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a href=\"http://support.sap.com/sltoolset\" target=\"_blank\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\n<p><strong>**************************************************</strong></p>\n<p> </p>\n<p> </p>\n<p>-----------------------&lt; D021371 NOV/29/05 &gt;--------------------<br/><strong>Upgrade on Linux x86_64: Correct R3up Version</strong><br/>For SAP systems installed on Linux x86_64, there is no 64Bit R3up version. You need to use R3up for Linux x86 32-Bit.<br/>For more information on how to use the R3up version in combination with the Kernel DVD, see Note <strong>893352</strong>.<br/><br/>---------------------&lt; D041268 FEB/08/05 &gt;----------------------------<br/><strong>Integrate Add-On BI_CONT 3.5.3 SP1 (\"fulltask\") in the upgrade</strong><br/>During the upgrade, update add-on BI_CONT to at least version<br/>BI_CONT 3.5.3 Support Package level 1 (\"fulltask\" version).<br/>For more information, see <strong>note 774933</strong>.<br/><br/>---------------------&lt; D020876 SEP/06/04 &gt;----------------------------<br/>Different problems can occur during the upgrade from APO 3.0 or 3.1 or SCM 4.0 to SCM 4.1. Note 770673 summarizes the existing notes to date. Note that some notes must also be implemented or considered before the upgrade.<br/><br/>---------------------&lt; D025323 MAY/24/02 &gt;----------------------------<br/><strong>Corrections and repairs for the upgrade</strong><br/>Before the upgrade, you MUST check whether:</p>\n<ul>\n<li>there is a new version of the R3up for your specific upgrade. For more information, see note 663240.</li>\n</ul>\n<ul>\n<li>there are repairs to the ABAP programs of the upgrade. For more information, see note 705485.</li>\n</ul>\n<p><strong>It is essential that you apply these two notes.</strong><br/><br/>---------------------&lt; D028310 JUL/19/02 &gt;----------------------------<br/><strong>Problems with the shadow instance</strong><br/>The following notes contain information on problems with the shadow instance:</p>\n<ul>\n<li><strong>525677</strong>: Problems starting the shadow instance</li>\n</ul>\n<ul>\n<li><strong>430318</strong>: Remote shadow instance on other operating system</li>\n</ul>\n<p><br/>------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br/><strong>LSMW now part of SAP_BASIS</strong><br/>As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW and your source release is based on SAP Web AS 6.10 or lower, do not implement LSMW after the upgrade.<br/>For more information, see <strong>Note 673066</strong>.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>III/ Corrections to the guides</strong></p>\n<p><br/>------------------------&lt; D022030 JUN/21/06 &gt;-------------------------<br/><strong>Section: Adjusting Customer Developments</strong><br/>In the SAP Notes table, SAP Note 689951 \"Release upgrade from 6.20 to 6.40 for customer programs\" is missing.<br/><br/>------------------------&lt; D022030 JUN/21/06 &gt;-------------------------<br/><strong>Section: Making Entries for the Parameter Input Module</strong><br/>The maximum length of the mount directory path is <strong>50</strong> characters.<br/>It may not contain any blanks or special characters.<br/><br/>------------------------&lt; D022030 MAY/26/06 &gt;-------------------------<br/><strong>SAP SCM Post-Upgrade Activities</strong><br/>The following post-upgrade activities only need to be performed for source release 3.x:</p>\n<ul>\n<li>Retransfer of Classes Master Data</li>\n</ul>\n<ul>\n<li>Adjusting Parameter in Customer Exit APOCF001</li>\n</ul>\n<ul>\n<li>Adjusting SNP Optimizer Profile</li>\n</ul>\n<ul>\n<li>Adjusting Initial Menu</li>\n</ul>\n<p>Post-upgrade activity \"Adjusting Initial Menu\" does not have to be performed at all.<br/><br/>------------------------&lt; D022030 MAR/09/06 &gt;-------------------------<br/><strong>Preparations BW-specific objects: Checking number ranges</strong><br/>In section \"Making Preparations for SAP BW-specific Objects\", subsection\"Checking Number Ranges\", the procedure needs to be separated depending source release. At the moment, the description only applies to source releases higher than SAP APO 3.1. For SAP APO 3.1 and lower, proceed as follows:</p>\n<ol>1. Call transaction RSRV.</ol><ol>2. Choose tab page Char.</ol><ol>3. Select Comparison of Number Range and Max. SID (all Characteristics).</ol><ol>4. Choose button Analysis.</ol><ol>5. If a red bullet is displayed in the column Result, inconsistencies exist. Choose Correct error.</ol>\n<p><br/>------------------------&lt; D022030 19/OCT/05 &gt;-------------------------<br/><strong>Minimum Support Package Level for BW 2.0B</strong><br/>If your source release is SAP APO 3.0, make sure that the SAP_BW part ofyour source release system has at least Support Package level 26 before you start the upgrade.<br/><br/>------------------------&lt; D030328 12/AUG/05 &gt;-------------------------<br/><strong>Optional Follow-Up Activity: Where-used list in the Workbench</strong><br/>As of release 6.10, the where-used list for Dictionary objects has changed. If you need a proper display of the list, you need to run report SAPRSEUB after the upgrade.<br/>As the runtime of the report may be quite long, we recommend that you run it in the development system only.<br/>For more information, see Notes <strong>401389</strong> and <strong>28022</strong>.<br/><br/>------------------------&lt; D022030 28/APR/05 &gt;-------------------------<br/><strong>Section: Making Preparations for SAP BW-specific Objects</strong><br/>In subsection \"Checking number ranges\", the path to the Master Data in<br/>transaction RSRV only applies to source release 4.0.<br/>For older source releases, the path is RSRV -&gt; TAb page Char.<br/><br/>------------------------&lt; I002675 MAR/11/05 &gt;-------------------------<br/><strong>Windows Guide Section on Database-Specific Parameters</strong><br/>In the Guide for the Upgrade on Windows, section \"Checking the Database-specific requirements for PREPARE\", you are asked to check the profile parameters for MS SQL server. As some of the parameter names have changed, SAP system tools may not recognize the parameters.<br/>For more information, see <strong>Note 826528</strong>.<br/><br/>------------------------&lt; D028310 09/FEB/05 &gt;-------------------------<br/><strong>Section: Making Entries for the Extension Module</strong><br/>In subsection \"Phase IS_SELECT\" the following applies to add-ons with status \"Undecided\":<br/>\"Your software vendor has predefined the strategy to choose for each add-on.<br/>For more information, see the SAP Note displayed by R3up or contact your software vendor.\"<br/><br/>------------------------&lt; D001330 AUG/11/04 &gt;--------------------------<br/><strong>Phase REQ_APOUPG</strong><br/>The upgrade phase in which the upgrade stops, and asks you to make the preparations for the SAP liveCache upgrade differs depending on the upgrade strategy you have choosen:</p>\n<ul>\n<li>Upgrade strategy resource-minimized: The upgrade stops in phase REQ_ APOUPG1 or REQ_APOUPG2</li>\n</ul>\n<ul>\n<li>Upgrade strategy downtime minimized: The upgrade stops in phase REQ_APOUPG3</li>\n</ul>\n<ul>\n<li>If you have changed the upgrade strategy during the upgrade, the upgrade stops in phase REQ_APOUPGSW.</li>\n</ul>\n<p>-----------------------&lt; D022030 JUL/06/04 &gt;-------------------- <strong>Phase INITSHD: Instance Number of the Shadow Instance</strong> For more information on choosing the instance number for the shadow instance, see <strong>note 29972</strong>.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>IV/ Errors on the CD-ROM</strong></p>\n<p><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>V/ Preventing data loss, upgrade shutdowns and long runtimes</strong></p>\n<p><br/>-----------------------&lt; D000706 28/NOV/06 &gt;--------------------------<br/><strong>Modification Adjustment Planning and Unicode Conversion</strong><br/>You cannot import transport requests created in a Unicode SAP system into a non-Unicode SAP system. If you want to perform a Unicode conversion of your SAP system, create the transport request <strong>before </strong>the conversion.<br/><br/>-------------------------------------------------------------------<br/><strong>Do not include ABA Support Package 12 in the Upgrade</strong><br/>If you included Support Package SAPKA64012 in the upgrade, the upgrade fails in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br/>To prevent the failure, only include the Suport Package in the upgrade if you can also include at least Support Package <strong>SAPKA64013</strong> as the problem will be fixed with SP 13.<br/>For more information - also on the procedure in case of an upgrade failure, see <strong>note 849925</strong>.<br/><br/>-----------------------&lt; D020876 13/SEP/04 &gt;--------------------------<br/><strong>Use newer LCA build</strong><br/>The LCA build contained on the CD is too low for the upgrade. To avoid problems and errors in the upgrade, instead install the LCA Build 03 PL 002 as described in <strong>note 762949</strong>.<br/><br/>-----------------------&lt; D020815 13/FEB/04 &gt;-------------------------- <strong>SPDD - Return to Standard</strong> If you choose \"return to standard\" in transaction SPDD for data elementswhich you have already performed the modification adjustment, the activaof the data elements starts.<br/>If you want to return adjusted data elements to the standard, proceed as, proceed as described in <strong>Note 705943</strong>.<br/><br/>--------------------------&lt; D030559 27/JAN/04 &gt;-----------------------<br/><strong>Import of Asian Languages: Change System Language</strong><br/>If you are importing Asian languages during the upgrade, you may get an error message during phase STARTSAP_TRANS or XPRAS_UPG (program<br/>RADBTLOG). In order to prevent this problem, change your system languageto \"English\" <strong>before</strong> the upgrade: Set instance parameter zcsa/system_language = E<br/>and restart the SAP system.<br/><br/>--------------------&lt; D030326 SEP/17/02 &gt;-----------------------------<br/><strong>DDIC password</strong><br/>After you start the upgrade, the DDIC password must not be changed in the original system or the shadow system. Otherwise, you will no longer be able to log onto the system during the upgrade.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>VI/ Preparing for the upgrade</strong></p>\n<p><br/>-----------------------&lt; D001330 27/APR/06 &gt;--------------------------<br/><strong>Handling of customer translation in the upgrade</strong><br/>Z languages or customer translations on system texts with transaction SE63 are not considered as modifications to the system by the upgrade and are therefore lost during the upgrade. As the SAP system may change considerably from one release to the next, it may not be worth saving the translations.<br/>If you think that it is worth saving your translations or languages, see Note <strong>485741</strong> for more information.<br/><br/>----------------------&lt; changed D033882 17/OCT/06 &gt;-------------------<br/>--------------------------&lt; D020876 24/NOV/05 &gt;-----------------------<br/><strong>Source Release 3.x: Delete Planning Version 000 in Client 000</strong><br/>If your source release is APO 3.x, client 000 contains planning version 000. This planning version is not needed and you can delete it before the upgrade to avoid inconsistencies in the target release.<br/>Caution: Before you delete the planning version, make sure that you apply Note <strong>924697</strong>!<br/><br/>--------------------------&lt; D001330 10/OCT/05 &gt;-----------------------<br/><strong>Apply the Latest Upgrade Repairs for Correct Language Import</strong><br/>If you do not apply the latest repairs for the upgrade as described in Note 663240, the language import from the data dictionary will be incomplete.<br/>For more information, see Note <strong>885955</strong>.<br/><br/>---------------------&lt; D038245 19/APR/04 &gt;---------------------------- <strong>Unicode Systems: Downward Compatible Kernel 6.40</strong> When you are using the normal kernel for Release 6.20 with your Unicode system, PREPARE issues the error message: Could not open the ICU common library. Before you start PREPARE, install the Downward Compatible Kernel for Release 6.40. Until this kernel is available, proceed as described in <strong>Note 716378</strong>.<br/><br/>---------------------&lt; D021160 FEB/19/04 &gt;----------------------------<br/><strong>Deleting planning versions</strong><br/>Before you delete the planning versions (step A1 of the /SAPAPO/OM_LC_UPGRADE_41 report), consult the user department to determine which planning versions are no longer required. Otherwise, too many planning versions may be deleted.<br/><br/>-----------------------&lt; D035318 04/FEB/04----------------------------<br/><strong>Unicode Systems: Report RUTTTYPACT</strong><br/>If your system is a unicode system, check whether you have run report RUTTTYPACT in your system after the installation. If you have not run the report, do so before you start PREPARE.<br/>To run the report, proceed as described in <strong>note 544623</strong>.<br/><br/>-----------------------&lt; D025440 OCT/17/03 &gt;--------------------------<br/><strong>Demand Planning:</strong><strong>Information on the upgrade</strong><br/>For more information on handling Demand Planning during the upgrade, see note 588986.<br/><br/>--------------------------&lt; D022188 AUG/27/03 &gt;-----------------------<br/><strong>Adjusting application-specific data</strong><br/>To avoid problems when adjusting application-specific data, (especially report variants), refer to <strong>notes 623723</strong> and <strong>626408</strong>.<br/>The notes also contain information about postprocessing the upgrade.<br/><br/>---------------------&lt; ******** JUN/27/03 &gt;---------------------------<br/><strong>Correct setting of the classification system</strong><br/>If you have already performed an upgrade in your system from APO 2.0 to APO 3.0, ensure that the classification system is set correctly in the Customizing.<br/>Use the 'CDP' setting if you are using the CDP classification system or if you are not using any classification system. Use 'IBASE and R/3 characteristics' if you are using IBASE or runtime object with configuration.<br/><br/>----------------------&lt; D025323 APR/24/03 &gt;---------------------------<br/><strong>Upgrade with AIX:</strong><strong>saposcol</strong><br/>Before the upgrade, see notes 526694 and 569015.<br/><br/>----------------------&lt; D025323 JUL/08/02 &gt;---------------------------<br/><strong>Upgrade with 32-bit operating systems</strong><br/>If your <strong>start release system</strong> is at the operating system version AIX 32-bit, HP-UX 32-bit or SUN-OS 32-bit, switch to a 64-bit version before the PREPARE, preferably directly to the 64-bit version prescribed for the target release.<br/>If you are using AIX 4.3 64-bit, additional actions are required before the upgrade.<br/>For more information, see notes 496963 <strong>and </strong>499708.<br/><br/>----------------------&lt; D022030 JUN/24/02 &gt;---------------------------<br/><strong>Relevant database archives</strong><br/>You are assisted in choosing the upgrade and archiving strategy by the archives used during the upgrade, for example. The first number (of the two) specifies the size of the archives up to the MODPROF_TRANS phase, the second number (of the two) specifies the size of the archives up to the end of the upgrade.<br/>DB2 UDB for UNIX/Windows: 9 GB/12GB<br/>Informix: 10 GB/12 GB<br/>MS SQL Server: 7,5 GB/10 GB<br/>Oracle: 11 GB/14 GB<br/>SAP DB: 6 GB/7 GB<br/>These sizes are based on sample data.<br/><br/>----------------------&lt; D022030 JUN/24/02 &gt;---------------------------<br/><strong>Storage requirement in the database</strong><br/>You have to enhance the database both during and after the upgrade. The first number (of the two) specifies the temporary enhancement required during the upgrade, the second number (of the two) specifies the permanent database enhancement.<br/>DB2 UDB for UNIX/Windows: 9 GB/6 GB<br/>Informix: 10 GB/8 GB<br/>MS SQL Server: 10 GB/6 GB<br/>Oracle: 10 GB/7 GB<br/>SAP DB: 8 GB/5 GB<br/>These sizes are based on sample data.<br/><br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p>\n<p><strong>VII/ Problems in the PREPARE and upgrade phases</strong></p>\n<p><br/>In this section, you will find known problems that you cannot prevent with preventive measures. These problems may only occur under very specific conditions.</p>\n<p><strong>Problems During the PREPARE phases</strong></p>\n<p><br/>-----------------------&lt; D038245 20/APR/05 &gt;---------------------------<br/><strong>Phase RFCCHK_INI: Name or Password is Incorrect</strong><br/>In phase RFCCHK_INI you may get the error message \"Name or Password is Incorrect\".<br/>If you replaced R3up after phase RFCCHK_INI with the latest version from SAP Service Marketplace, this error may also come up in other phases that connect to the system using RFC.<br/>For more information, see Note <strong>792850</strong>. You may have to replace disp+work and restart the system.<br/><br/>-----------------------&lt; D028310 NOV/03/04 &gt;--------------------<br/><strong>Phase CONFCHK_IMP on Distributed Systems</strong><br/>Phase CONFCHK_IMP offers you a list of operating systems to select from.<br/>This list only contains one entry \"Linux\" which is valid for both Linux and Linux IA64.<br/><br/>-------------------&lt; D038245 MAY/10/04 &gt;------------------------------ Phase SAVELOGS<br/>If you have a large number of add-ons in your system, R3up possible aborts in the SAVELOGS phase.<br/>If this problem occurs, then download and install at least R3up version 21.017 and repeat the phase.<br/><br/>-------------------&lt; D022256 SEP/04/00 &gt;------------------------------<br/><strong>Windows NT 4.0 only</strong><br/>If a dialog box with the error message:<br/>'The procedure entry point ... could not be located in the dynamic link library ..<br/>' appears during the PREPARE, use the R3DLLINS.EXE program to install the latest DLLS. The program is on the SAP kernel CD in the \\&lt;Processor type&gt;\\NTPATCH directory.<br/>Then reboot your machine and use 'PREPARE repeat' to restart PREPARE.<br/><br/>-----------------------------------------------------------<br/>Termination in the TOOLIMPD1 or TOOLIMPD2 phases. For more information, see note 98198.<br/><br/>----------------------------------------------------------------------<br/><br/></p>\n<p><strong>Problems in the upgrade phases</strong></p>\n<p><br/>------------------------&lt; D028310 20/AUG/04 &gt;--------------------------<br/>Phase: DIFFEXPDDIV<br/>Note: 766379<br/>Description:<br/>Error message in log file DIFFEXPD.ELG (directory &lt;DIR_PUT&gt;/log):<br/>INACTIVE DDIC VERSIONS-Export ERRORS and RETURN CODE in SAPEDDD622.QO1<br/>~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br/>2EETW190 \"TABT\" \"TESCL                        \" has no active version.<br/>...<br/><br/>------------------&lt;changed D026178 JUN/25/04&gt;------------------------<br/>---------------------&lt; D026178 JUN/07/04&gt;---------------------------- Phase: ACT_640<br/>Description: If you have included Basis Support Package 03 in the upgrade, you may get the following error message: SHADOW IMPORT ERRORS and RETURN CODE in SAPKGPAC01.QO1 1EEDO519 \"Table\" \"TPPROFILES_TYPE_PROFILE\" could not be activated. The same error may appear for table \"TPPROFILES_TYPE_PROFILE_INFO\". You can ignore the error. Repeat the phase to continue with the upgrade.<br/><br/>---------------------&lt; D019416 19/MAR/04 &gt;----------------------------<br/>Phase: TABIM_POST<br/>Note: 718912<br/>Description: Upgrade stops with error DI829. In this case, implement the above note and continue with the upgrade. After the upgrade, you have to revert the modification.<br/><br/>---------------------&lt; D000325 AUG/25/03 &gt;----------------------------<br/>Phase: XPRAS_UPG<br/>Description: BASIS-XPRA termination<br/>During the upgrade from SCM 3.X to 4.0, you can now include several Support Packages. However, if you include BASIS Support Package 27 or 28 during the upgrade, this causes a termination in the XPRA phase. The log files contain the following messages for this termination:<br/>2EEOL555 Object type '\"WFTS\"' could not be generated<br/>2EEOL555 Object type '\"DRB\"' could not be generated<br/>2EEPU133 Errors occurred during post-handling<br/>\"SWO_OBJTYPE_AFTER_IMPORT\" for \"SOBJ\" \"L\"<br/><br/>You can ignore these terminations.<br/>For more information, read the following notes:<br/>648372 WFTS object type: Errors during upgrade to 4.7 Enterprise<br/>648655 \"Object type DRB could not be generated\" during upgrade<br/>650249 Reference fields from complex structures in parameters<br/><br/>-----------------------------------------------------------<br/>Phase: PARCONV_UPG ***<br/>Note: 339126<br/>Description: An SQL error occurs in the database when a table is accessed. Call the RADUTMST report for the tables involved and then continue with Repeat.<br/><br/>-----------------------------------------------------------<br/>Phase: STARTR3_IMP<br/>Note: 525006<br/>Description: <strong>This problem only occurs with AIX if you use the Upgrade Assistant</strong><br/>The R3up program cannot start the system and terminates.<br/><br/>Phase: XPRAS_UPG<br/>Note: 648372<br/>Description: Error when activating the WFTS BOR object type. The error can be ignored in many cases (see note) and is eliminated with Support Package SAPKB62029.<br/><br/>--------------------------&lt; C5010299 JAN/21/05 &gt;-----------------------<br/>Phase: XPRAS_UPG<br/>Note: 778198<br/>Description: Error message S&gt;801 in log file LONGPOST.LOG. For more information, see the note above.<br/><br/>----------------------&lt; D003550 MAY/07/03 &gt;----------------<br/>Phase: XPRAS_UPG<br/>Description: The following error occurs for one or more tables:<br/>Activate dependent table &lt;tabname&gt;.<br/>Table &lt;tabname&gt;: Key length &gt; in 120 (limited functions)<br/>DB length of table key &lt;tabname&gt; is too long (&gt;255).<br/>Dependent table &lt;tabname&gt; could not be activated.<br/>If these are the only errors in the XPRAS_UPG phase, you can ignore them with \"ignore -&gt; repair severe errors\". The upgrade then continues.<br/>The error occurs with tables for ODS objects that you can activate in SAP BW with transaction RSDODS:<br/>Table name /BIC/AXYZ1n -&gt; ODS object name is XYZ (n = number)<br/>Table name /BI0/AXYZ1n -&gt; ODS object name is 0XYZ (n = number)<br/>After the upgrade, activate all ODS objects with tables for which errors were issued.<br/><br/>---------------------&lt; D019430 APR/11/03 &gt;-----------------<br/><strong>Only for Start-Release APO 3.1</strong><br/>Phase: XPRAS_UPG<br/>Description: If you are using procedures that refer to customer-specific algorithms (function modules) for the admissibility check and/or feasibility check in the CMDS, the relevant control profiles cannot be converted by the XPRA.<br/>In this case, manual postprocessing is required before you can use the system productively again after the upgrade. The procedure is described in the LONGPOST.LOG file.<br/><br/>-----------------------------------------------------------<br/>Phase CHK_POSTUP<br/>Note: 197886<br/>Description: If you have implemented notes 178631, 116095 or 69455 in your system before the upgrade, error messages for objects without a DDIC reference appear in the LONGPOST.LOG log.<br/>Follow the steps described in the note.<br/><br/>-----------------------------------------------------------<br/><br/><br/><br/><br/><br/></p>\n<p><strong>VIII/ Problems after the upgrade</strong><br/><br/></p>\n<p>In this section, you will find known problems that you cannot prevent with preventive measures. These problems only occur under very specific conditions.<br/><br/>-----------------------&lt; D000434 28/JAN/04 &gt;-------------------------<br/><strong>Source Release 6.20: Missing DB View STWB_INFO</strong><br/>If your source release is based on SAP_BASIS 6.20 SP 48 or lower, transaction DB02 might show DB view STWB_INFO missing after the upgrade.<strong>This</strong> view is not needed in the system and requires no further actions.<br/><br/>---------------------&lt; D035318 08/JUL/04 &gt;-----------------<br/><strong>Source Release lower than Basis 6.10: Codepage Conversion</strong><br/>In Release 6.10, the codepage administration has changed considerably.<br/>If you want to continue using the customer-defined codepages that start with \"9\" after the upgrade, you have to convert the codepages using report RSCP0126 after the upgrade.<br/>For more information, see <strong>Notes 485455</strong> and <strong>511732</strong>.<br/><br/>---------------------&lt; D034064 05/MAR/04 &gt;-----------------<br/><strong>TLB profile maintenance</strong><br/>After the upgrade from APO 3.0, an incorrect screen is displayed in TLB profile maintenance.<br/>For more information, see note 708197.<br/><br/>---------------------&lt; D023952 AUG/12/03 &gt;-----------------<br/><strong>Only for Start-Release APO 3.1</strong><br/><strong>Condition technique for RBA Customizing is missing</strong><br/>If you have not included the FIX_SCM401 toolfix in the upgrade, the condition technique required for the RBA Customizing is missing after the upgrade. In this case, proceed as described in note 650291.<br/><br/>---------------------&lt; D027999 SEP/27/02 &gt;-----------------<br/><strong>New initial menu</strong><br/>If you start the system after the upgrade and the initial menu is the \"SAP Easy Access Advanced Planning and Optimization\" menu, you must convert this to the new SCM initial menu \"SAP Easy Access mySAP Supply Chain Management\".<br/>Proceed as described under \"Performing Post-Upgrade Activities for the Applications\" in the Component Upgrade Guide.<br/><br/>-----------------&lt; added D027999 OCT/07/02 &gt;---------------<br/>---------------------&lt; D027999 SEP/27/02 &gt;-----------------<br/><strong>Problems with user exits and customer enhancements</strong><br/>With the SAP Web Application Server, new and intensified rules were introduced for the (Unicode-enabled) ABAP syntax and runtime that are valid for both Unicode and non-Unicode installations. In some cases, these stricter checks may therefore result in error messages with user exits and customer enhancements.</p>\n<ul>\n<li>For information on eliminating possible errors <strong>with</strong><strong> user</strong> exits, see note 553110.</li>\n</ul>\n<ul>\n<li>For information on eliminating possible errors <strong>with</strong> customer enhancements, see <strong>note 493387.</strong></li>\n</ul>\n<p><br/>---------------------&lt; D020815 AUG/23/02 &gt;-----------------<br/><strong>SPAU:</strong><strong>Names of interface methods are truncated</strong><br/>Some modified methods and methods overwritten by the upgrade (ABAP objects) may be shortened to 30 characters in the display in transaction SPAU. Consequently, methods in SPAU may also be sorted incorrectly under \"Deleted Objects\".<br/>Caution: Deleted objects are not displayed in the SPAU with a standard selection. These may be easily overlooked.<br/>For information on the correction, see <strong>note 547773.</strong><br/><br/>---------------------&lt; D020847 AUG/13/02 &gt;-----------------<br/><strong>Windows/Microsoft Cluster Server (MSCS)</strong><br/>If you perform an upgrade with MSCS, you must perform the post-upgrade steps described <strong>in</strong><strong> SAP note</strong> 544988.<br/><br/>-----------------------------------------------------------<br/><strong>Linux:</strong><strong>Importing new saposcol version</strong><br/>For more information, see note <strong>19227.</strong><br/><br/>-----------------------------------------------------------<br/><strong>Solaris:</strong><strong>saposcol version 32-bit or 64-bit</strong><br/>For more information, see note <strong>162980.</strong><br/><br/>-----------------------------------------------------------<br/><br/><br/><br/><br/></p>\n<p><strong>IX/ Chronological Summary</strong></p>\n<p><br/>Date.......Topic..Short description<br/>-----------------------------------------------------------<br/>NOV/28/06.....V...Modification Adjustment Planning/Unicode Conversion<br/>JUL/17/06...III...Section: Adjusting Customer Developments<br/>JUN/21/06...III...Entries for Parameter Input Module: Path Length<br/>MAY/29/05...III...SAP SCM Post-Upgrade Activities<br/>27/APR/06....VI...Handling of customer translation in the upgrade<br/>MAR/09/06...III...Prep. BW-specific objects: Checking number ranges<br/>NOV/29/05....II...Upgrade on Linux x86_64: Correct R3up Version<br/>NOV/24/05....VI...Source Rel. 3.x: Planning Version 000 in Client 000<br/>OCT/19/05...III...Minimum Support Package Level for BW 2.0B<br/>OCT/10/05....VI...Latest Upgrade Repairs for Correct Language Import<br/>12/AUG/05...III...Opt. Follow-Up: Where-Used List<br/>JUN/02/05.....V...Do not include ABA Support Package 12 in the Upgrade<br/>APR/28/05...III...Section: Making Preparations for SAP BW-specific Obj.<br/>APR/20/05...VII...Phase RFCCHK_INI: Name or Password is Incorrect<br/>MAR/11/05...III...Windows Guide Section on Database-Specific Parameters<br/>FEB/09/05...III...Section: Making Entries for the Extension Module<br/>FEB/02/08....II...Integrate Add-On BI_CONT 3.5.3 SP1 in the upgrade<br/>FEB/02/05....II...LSMW now part of SAP_BASIS<br/>JAN/28/05..VIII...Source Release 6.20: Missing DB View STWB_INFO<br/>JAN/21/05...VII.. Phase XPRAS_UPG: Error S&gt;801<br/>NOV/03/04.. VII.. Phase CONFCHK_IMP on Distributed Systems<br/>AUG/20/04...VII.. Phase: DIFFEXPDDIV<br/>AUG/11/04...III.. Phase REQ_APOUPG<br/>JUL/08/04. VIII.. Source Release lower than 6.10: Codepage conversion<br/>JUL/06/04...III.. Phase INITSHD: Instance Number of the Shadow Instance<br/>JUN/07/04...VII.. Phase ACT_640: SHADOW IMPORT ERRORS with Basis SP03<br/>MAY/10/04...VII.. Abortion in SAVELOGS phase<br/>APR/19/04... VI.. Unicode Systems: Downward Compatible Kernel 6.40<br/>MAR/19/04...VII.. Phase TABIM_POST: Error DI829<br/>MAR/05/04..VIII.. TLB profile maintenance<br/>FEB/19/04....VI.. Deleting planning versions<br/>FEB/13/04.... V.. SPDD - Return to Standard<br/>JAN/04/04....VI.. Unicode Systems: Report RUTTTYPACT<br/>JAN/27/04.....V.. Import of Asian Languages: Change System Language<br/>SEP/19/03...VII.. Phase XPRAS_UPG: Activate the WFTS BOR object type<br/>AUG/27/03.....V.. Adjusting application-specific data<br/>AUG/12/03..VIII...Start-Release 3.1: Condition technique missing for                   RBA Customizing<br/>JUL/02/03....II...Section A of the /SAPAPO/OM_LC_UPGGRADE_41 report<br/>JUN/27/03....VI...Correct setting of the classification system<br/>JUN/26/03..VIII...Renaming COM objects in lC Apps<br/>MAY/07/03...VII...Phase XPRAS_UPG: ODS object activation<br/>APR/24/03....VI...Upgrade on AIX: saposcol<br/>APR/23/03..VIII...New initial menu<br/>APR/11/03...VII...Start-Release 3.1: Phase XPRAS_UPG: Customer-specific<br/>                  algorithms<br/>SEP/27/02..VIII...Problems with user exits<br/>SEP/17/02.....V...DDIC password<br/>AUG/23/02..VIII...SPAU: Names of interface methods are truncated<br/>AUG/13/02..VIII...Windows/MSCS: Postprocessing<br/>AUG/09/02...VII...Phase PARCONV_UPG: SQL error<br/>AUG/07/02...VII...Phase SHADOW_IMPORT_ALL: Duplicate key error<br/>JUL/19/02....II...Problems with the shadow instance<br/>JUL/08/02....VI...Upgrade with 32-bit operating systems<br/>JUN/24/02....VI...Relevant database archive<br/>JUN/24/02....VI...Space requirements in the database<br/>JUN/04/02 ..VII.. STARTR3_IMP phase with AIX - system does not start<br/>MAY/24/02....II...Corrections and repairs for the upgrade<br/>FEB/20/02.....I...R3up Keyword<br/>OCT/19/00..VIII...Linux:  Importing new saposcol version<br/>SEP/04/00...VII...Windows NT: Error message in PREPARE<br/>FEB/16/00..VIII...saposcol Version 32-bit or 64-bit on Solaris<br/><br/><br/>----------------------------------------------------------------------</p></div>", "noteVersion": 42}, {"note": "785307", "noteTitle": "785307 - SAP Query: Where-used list and generated reports", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The where-used list is incomplete for reports generated by the SAP Query Tool (AQ...).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SAP Query, where-used list, generated report</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You add the generated reports using the ABAP command 'Insert report'. However, no where-used list is created.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This behavior is intentional: A where-used list based on the generated programs of a query is not reliable because the generated programs may be out of date or may only be created when the query is next started.<br/>If a where-used list is created according to Note 18023 or 28022, the where-used list for the SAP Query programs is incomplete because the system ignores the generated includes in the namespace /1BCDWB/ (see Note 1180964).<br/></p></div>", "noteVersion": 7}, {"note": "735560", "noteTitle": "735560 - Errors in where-used list for type groups and includes", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>There are errors in the where-used list for type groups and includes. Usage locations are not found.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SE11, SE84</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package or implement the advance correction. After the correction, you must set up the index again. For this, schedule the SAPRSEUB program in the background in accordance with note 28022.</p></div>", "noteVersion": 5}, {"note": "79084", "noteTitle": "79084 - Syslog: Error when writing lock handler file", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>1. There are error messages in the system log of the central lock handler:<br/> GEM K Error writing to a lock handler file.<br/> GEL K Error reading a lock handler file.<br/> GZZ K &gt; ...\\SYS\\global\\ENQLIS...<br/> GZZ K &gt; Invalid argument<br/> GEA K Internal error in lock management.<br/> GZZ K &gt; LstActualFile(); rtc=8<br/>There are error messages in the system log of the local application server:<br/> GI0 K Error calling the central lock handler<br/> <br/> GI7 K &gt; Request to update the report file<br/>       failed.<br/><br/> GZZ K &gt; EnqueOpMem(); Illegal OpCode A<br/> GEA K Internal lock administration error<br/> GEH K Enqueue: Transmission error when reading lock entries<br/><br/>2. From kernel Release 46D, the system can also issue the following error messages:<br/>....<br/>E  *** ERROR =&gt; EnqServer(). Inconsistent Request Length. [enctrl.c<br/>E  *** ERROR =&gt; Input Buffer LenTh = 592, LenRec = 29616 [enctrl.c<br/>E  *** ERROR =&gt; EnqueQueryRemote: rtc = 8 [enxxhead.c   3673]<br/>or<br/>E  *** ERROR =&gt; EnqueSendQueryRequest: MaxResponseSize(...) !=<br/>                              ComBufferSize(...) [enxxhead.c  ...]<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>enqueue enque lock lock handler reporting file report file batch input SAPMSBDC ENQUE_REPORT ENQUEUE_REPORT ENQUE_READ ENQUEUE_READ enxxhead RSBDCBTC EnqueOpMem Illegal OpCode A<br/>EnqueQueryRemote EnqueQueryServer<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><ol>1. There are invalid calls of the function modules ENQUE_REPORT and ENQUE_READ outside the lock server programs.</ol> <span>              </span>The ENQUE_REPORT and ENQUE_READ functions are only intended for execution on the enqueue server. Application programs must use the ENQUEUE_READ or ENQUEUE_REPORT functions instead. <span>              </span>If you use the function module ENQUE_REPORT_TEST, which has not been released, the system can also issue the above system log entries. <ol>2. The profile parameter enque/process_location is set in the enqueue instance on REMOTE_TASKHANDLER. You should not set enque/process_location explicitly; if you do, the default value has no effect.</ol> <span>              </span>Exception: You should only set this parameter in a high-availability configuration. In this special case, be sure to note the specific configuration guidelines of the high-availability configuration. <p></p> <ol>3. The instances of the system have different kernel versions.</ol> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>1. Invalid call of ENQUE_REPORT or ENQUE_READ<br/><br/>In the programs RSBDCBTC and SAPMSBDC, replace the calls of ENQUE_REPORT with calls of ENQUEUE_REPORT. The parameters remain the same. The errors in these programs are corrected in Release 4.6x and later.<br/><br/>Other programs may also contain ENQUE_READ or ENQUE_REPORT calls, which will cause the same error.<br/>We have frequently observed invalid calls in customer modifications, as well - usually in programs whose names start with X, Y, or Z.<br/><br/>We recommend using the where-used list to search for incorrect calls in other programs.<br/>SE37, ENQUE_READ -&gt; Utilities -&gt; Where-Used List<br/>SE37, ENQUE_REPORT -&gt; Utilities -&gt; Where-Used List<br/>SE37, ENQUE_REPORT_TEST -&gt; Utilities -&gt; Where-Used List<br/><br/>Note that the where-used list is not updated automatically for SAP objects. Therefore, first proceed as described in SAP Note 28022.<br/><br/>The only legal calls of the ENQUE_READ and ENQUE_REPORT functions are contained in enqueue programs such as RSENQRR2, RSENQTSx, and LSENTUxx. Calls of ENQUE_REPORT_TEST are NOT allowed.<br/><br/>Do not let the possible presence of comment lines such as<br/>23.07.97 vh Funktionsbaustein ENQUE_REPORT durch ENQUEUE_REPORT<br/>         vh ersetzt, Löschen von Sperren<br/><br/>prevent you from replacing all occurrences of function calls for ENQUE_READ and ENQUE_REPORT with function calls for ENQUEUE_READ and ENQUEUE_REPORT, respectively. The programs RSENQRR2, RSENQTS*, and LSENTU* are the only exceptions.<br/>IMPORTANT: Do NOT replace the calls in the programs RSENQRR2, RSENQTS*, and LSENTU*.<br/><br/>The following occurrences of the \"incorrect\" calls are currently known:<br/><br/>  RPIEWT04<br/>  RPWI2000<br/>  RSBDCBTC<br/>  SAPMSBDC<br/>  RSBDCTL3<br/>  RSBDCTL4<br/>  RSBDCTL6<br/>  RSBDCTL7<br/>  LZWABUU01<br/>  LZWABUU02<br/>  LZWABUU03<br/>  LZWABUU04<br/>  Z3C0M007<br/>  Z3C0M022<br/>  /SSA/ABS (corrected with SAP Note 1920219)<br/><br/>Occurrences in comment lines do not result in the error, but you can replace them anyway.<br/><br/>2. Invalid profile parameter enque/process_location: Remove the profile parameter enque/process_location from the default profile, DEFAULT.PFL, and from instance profiles if your system does not have a high-availability configuration.<br/><br/>3. Call transaction SM51 and check whether all the system instances<br/>have the same patch level. All instances of a system must use the same kernel patch level.<br/> <br/></p></div>", "noteVersion": 30}, {"note": "1346740", "noteTitle": "1346740 - WDA: Where-used list for component configurations", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The where-used list for component configurations is not up-to-date.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>WDR_CFG_ADJUST_WUL, WDY_CFG_CMP_WUL</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The system updates the where-used list when you save a component configuration or an application configuration. In addition, the where-used list is updated by the jobs EU_INIT, EU_REORG, or EU_PUT, which are scheduled by the ABAP Workbench. For more information about this, also see Note 18023 or 28022.<br/>If the where-used list is not up-to-date, you can start the report WDR_CFG_ADJUST_WUL (in the background).</p></div>", "noteVersion": 3}, {"note": "775047", "noteTitle": "775047 - Add. info. on upgrading to SAP Web AS 6.40 SR1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Errors in the upgrade procedure or in the upgrade guide; preparations for the upgrade</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Update, migration, upgrade, release, maintenance level, R3up, PREPARE<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>*</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/><b>CAUTION:</b><br/>This note is updated regularly!<br/>Therefore, you should read it again immediately before starting the upgrade.<br/><br/></p> <b>What information can I expect from this note?</b><br/> <p>This note describes problems that may occur during the system upgrade and provides information on how to solve them. This usually takes the form of references to other notes.<br/>The main purpose of this note is to prevent data loss, upgrade shutdowns, and long runtimes.<br/>It deals with database-independent problems only.<br/><br/></p> <b>Which additional notes do I require in preparation for the upgrade?</b><br/> <p>This depends on the functions that you are using. You will need one or several of the following notes:<br/><br/>Short text................................................ Note number<br/>_____________________________________________________________________<br/>Additional information on upgrade to WAS 6.40 MaxDB............669656<br/>Additional information: Upgrade to SAP Web AS 6.40.............661569<br/>DB6: Additions to upgrade (based) on SAP Web AS 6.40...........662191<br/>DB2-z/OS: Additions upgrade ot Basis 6.40......................661252<br/>Enhancements tu upgrade to WEB As 6.40 with MS SQL.............669236<br/>Add. info. on upgrading to SAP Web AS 6.40 ORACLE..............662219<br/>Additional information for upgrading to Basis..................647130<br/>_____________________________________________________________________<br/>Repairs for upgrade to Basis 640 ............................. 663240<br/>Corrections for for R3up Version 640 ......................... 663258<br/>_____________________________________________________________________<br/>OCS: Known problems with Supp.Packages in Basis Rel.6.40 ......672651<br/>_____________________________________________________________________<br/><br/></p> <b>Contents</b><br/> <p>I/ ...... R3up Keyword<br/>II/ ..... Important General Information<br/>III/ .... Corrections to the Guide<br/>IV/ ..... Errors on the CD-ROM<br/>V/ ...... Preventing Data Loss, Upgrade Shutdown, and Long Runtimes<br/>VI/ ..... Preparing the Upgrade<br/>VII/..... Problems During the PREPARE and Upgrade Phases<br/>VIII/ ... Problems After the Upgrade<br/>IX/ ..... Chronological Summary<br/><br/></p> <b>I/ R3up keyword</b><br/> <p><br/>-----------------------&lt; D023536 FEB/20/02 &gt;--------------------<br/>The R3up keyword is: 160421<br/>This must be entered the first time you call R3up.<br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p> <b>II/ Important General Information</b><br/> <p><br/><br/>-----------------------&lt; D025323 MAY/24/02 &gt;--------------------<br/><b>Corrections and Repairs for the Upgrade</b><br/>Before the upgrade, you must check whether a new version of the R3up exists for your specific upgrade.<br/>For more information, see <b>Note 663258</b>.<br/><br/>-----------------------&lt; D021371 NOV/29/05 &gt;--------------------<br/><b>Upgrade on Linux x86_64: Correct R3up Version</b><br/>For SAP systems installed on Linux x86_64, there is no 64Bit R3up version. You need to use R3up for Linux x86 32-Bit.<br/>For more information on how to use the R3up version in combination with the Kernel DVD, see Note <b>893352</b>.<br/><br/>-----------------------&lt; D034302 DEZ/03/03 &gt;--------------------<br/><b>Windows only: Execute program R3dllins.exe</b><br/>The 6.40 kernel is compiled with the new version of MS compiler and requires additional libraries for operation. To prevent problems during and after the upgrade, you must execute program R3dllins.exe on your central host, all application hosts, and on the remote shadow host, if you are planning to use one.<br/>You can find the program on the Upgrade Master CD in the NT\\I386\\NTPATCH folder. It must be executed before you start prepare and directly from the NTPATCH folder (it can be shared). Copying and executing the program<br/>will not work!<br/><br/>-----------------------&lt; D028310 JUL/19/02 &gt;--------------------<br/><b>Problems with the Shadow Instance.</b><br/>The following Notes contain information about problems with the shadow instance:</p> <ul><li><b>525677</b>: Problems when starting the shadow instance</li></ul> <ul><li><b>430318</b>: Remote shadow instance on a different operating system</li></ul> <p><br/>------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br/><b>LSMW now part of SAP_BASIS</b><br/>As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW<br/>and your source release is based on SAP Web AS 6.10 or lower, do not<br/>implement LSMW after the upgrade.<br/>For more information, see <b>Note 673066</b>.<br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p> <b>III/ Corrections to the Guides</b><br/> <p><br/>-----------------------&lt; D033903 15/SEP/08 &gt;--------------------------<br/><b>SDK Version 1.4.x for Upgrade Assistant</b><br/>The upgrade assistant only supports Java Software Development Kit (SDK) 1.4.x. It does not support version 1.5 or higher.<br/><br/>------------------------&lt; D035220 APR/23/07 &gt;--------------------------<br/><b>Migrate data from table TVARV to TVARVC</b><br/>As of SAP Basis 6.10, the client-specific table TVARVC is used instead of cross-client table TVARV. If you want to migrate entries from table TVARV to the new table, you can use report RSTVARVCLIENTDEPENDENT.<br/>For more information, see Note <b>557314</b>.<br/><br/>------------------------&lt; D024991 OCT/19/06 &gt;-------------------------<br/><b>Data Management Planning - Link to SAP Service Marketplace</b><br/>Site service.sap.com/dao on SAP Service Marketplace is temporarily underconstruction. Please refer to the following site and document instead:<br/>service.sap.com/data-archiving -&gt; Media Library -&gt; Literature &amp; Brochure-&gt; Data Management Guide<br/><br/>------------------------&lt; D022030 JUN/21/06 &gt;-------------------------<br/><b>Section: Making Entries for the Parameter Input Module</b><br/>The maximum length of the mount directory path is <b>50</b> characters.<br/>It may not contain any blanks or special characters.<br/><br/>------------------------&lt; D030328 09/AUG/05 &gt;-------------------------<br/><b>Optional Follow-Up Activity: Where-used list in the Workbench</b><br/>As of release 6.10, the where-used list for Dictionary objects has changed. If you need a proper display of the list, you need to run report SAPRSEUB after the upgrade.<br/>As the runtime of the report may be quite long, we recommend that you run it in the development system only.<br/>For more information, see Notes <b>401389</b> and <b>28022</b>.<br/><br/>------------------------&lt; D022030 MAR/18/05 &gt;-------------------------<br/><b>Documentation \"SAP Software on UNIX - OS Dependencies\"</b><br/>The information formerly contained in the documentation \"SAP Software onUNIX - OS Dependencies\" has been moved to the documentation \"Component Installation Guide &lt;your SAP component system combination&gt;, Part I.<br/><br/>------------------------&lt; I002675 MAR/11/05 &gt;-------------------------<br/><b>Windows Guide Section on Database-Specific Parameters</b><br/>In the Guide for the Upgrade on Windows, section \"Checking the Database-specific requirements for PREPARE\", you are asked to check the profile parameters for MS SQL server. As some of the parameter names have changed, SAP system tools may not recognize the parameters.<br/>For more information, see <b>Note 826528</b>.<br/><br/>------------------------&lt; D028310 25/FEB/05 &gt;-------------------------<br/><b>Section \"SAP NW AS: J2EE Engine Installation Planning\"</b><br/>Please ignore this section! After the upgrade, proceed as described in section \"Installing the J2EE Engine\".<br/><br/>------------------------&lt; D028310 09/FEB/05 &gt;-------------------------<br/><b>Section: Making Entries for the Extension Module</b><br/>In subsection \"Phase IS_SELECT\" the following applies to add-ons with status \"Undecided\":<br/>\"Your software vendor has predefined the strategy to choose for each add-on.<br/>For more information, see the SAP Note displayed by R3up or contact your software vendor.\"<br/><br/>-----------------------&lt; D022030 JUL/06/04 &gt;--------------------<br/><b>Phase INITSHD: Instance Number of the Shadow Instance</b><br/>For more information on choosing the instance number for the shadow instance, see <b>note 29972</b>.<br/><br/>-----------------------&lt; D022030 MAY/07/04 &gt;--------------------<br/><b>Implementing the Integrated SAP ITS</b><br/>The text \"Meeting the Requirements for the SAP Internet Solutions\" does not contain a procedure for implementing the integrated ITS.<br/>If you want to use it, proceed as follows:</p> <ol>1. After the upgrade of the system, configure the integrated ITS as described in the online documentation under Application Platform (SAP Web Application Server) -&gt; ABAP Technology -&gt; UI Technology -&gt; Web UI Technology -&gt; ITS/SAP@Web Studio -&gt; SAP ITS in the SAP Web Application Server -&gt; Configuration.</ol> <ol>2. Migrate the applications as described in the document \"Migration of Existing ITS Services\" on SAP Service Marketplace at service.sap.com\\sap-its -&gt; Media Library -&gt; Literature.</ol> <p><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p> <b>IV/ Errors on the CD-ROM</b><br/> <p><br/><br/>---------------------------------------------------------------------<br/><br/><br/><br/></p> <b>V/ Preventing Data Loss, Upgrade Shutdown, and Long Runtimes</b><br/> <p><br/>-----------------------&lt; D000706 28/NOV/06 &gt;--------------------------<br/><b>Modification Adjustment Planning and Unicode Conversion</b><br/>You cannot import transport requests created in a Unicode SAP system into a non-Unicode SAP system. If you want to perform a Unicode conversion of your SAP system, create the transport request <b>before </b> the conversion.<br/><br/>-----------------------&lt; D028597 15/SEP/06 &gt;--------------------------<br/><b>Support Package SAPKB64018 - use corrected version</b><br/>If you include Support Package SAPKB70009 in the upgrade, make sure to use the corrected version, indicated by EPS file name CSN0120061532_0024351.PAT.<br/>If you use the old version, phase XPRAS_UPG returns an error on the after import method SRM_FILL_KC_TABLES_AFTER_IMP. In this case, proceed as described in Note <b>967821</b>.<br/>For more information about the problem in general, see Note <b>672651 </b>.<br/><br/>------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br/><b>Back up customer-specific entries in table EDIFCT</b><br/>If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br/>For more information, see Note <b>865142</b>.<br/><br/>----------------------------------------------------------------<br/><b>Do not include ABA Support Package 12 in the Upgrade</b><br/>If you included Support Package SAPKA64012 in the upgrade, the upgrade fails in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br/>To prevent the failure, only include the Suport Package in the upgrade if you can also include at least Support Package <b>SAPKA64013</b> as the problem will be fixed with SP 13.<br/>For more information - also on the procedure in case of an upgrade failure, see <b>note 849925</b>.<br/><br/>------------------------&lt; D001330 20/OCT/04 &gt;-------------------------<br/><b>Function Groups in the Customer Name Space</b><br/>If you have created function groups in the customer namespace, you may lose data during the upgrade.<br/>For more information, see Note <b>783308</b>.<br/><br/>-----------------------&lt; D020815 13/FEB/04 &gt;--------------------------<br/><b>SPDD - Return to Standard</b><br/>If you choose \"return to standard\" in transaction SPDD for data elements for which you have already performed the modification adjustment, the activation of the data elements starts. If you want to return adjusted data elements to the standard, proceed as described in <b>Note 705943 </b>.<br/><br/>--------------------------&lt; D030559 27/JAN/04 &gt;-----------------------<br/><b>Import of Asian Languages: Change System Language</b><br/>If you are importing Asian languages during the upgrade, you may get an error message during phase STARTSAP_TRANS or XPRAS_UPG (program RADBTLOG).<br/>In order to prevent this problem, change your system language to \"English\" <b>before the upgrade</b>: Set instance parameter zcsa/system_language = E<br/>and restart the SAP system.<br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/></p> <b>VI/ Preparing the Upgrade</b><br/> <p><br/>-----------------------&lt; D001330 27/APR/06 &gt;--------------------------<br/><b>Handling of customer translation in the upgrade</b><br/>Z languages or customer translations on system texts with transaction SE63 are not considered as modifications to the system by the upgrade and are therefore lost during the upgrade. As the SAP system may change considerably from one release to the next, it may not be worth saving the translations.<br/>If you think that it is worth saving your translations or languages, seeNote <b>485741</b> for more information.<br/><br/><br/>--------------------------&lt; D001330 10/OCT/05 &gt;-----------------------<br/><b>Apply the Latest Upgrade Repairs for Correct Language Import</b><br/>If you do not apply the latest repairs for the upgrade as described in Note 663240, the language import from the data dictionary will be incomplete.<br/>For more information, see Note <b>885955</b>.<br/><br/>------------------------&lt; D034302 13/DEC/04 &gt;------------------------<br/><b>Windows only: Shut Down IGS Before Starting PREPARE</b><br/>When you upgrade your system from Source Release 6.20, you have to make sure that the IGS is not running when you start PREPARE. Otherwise you will get the following error message in module General Checks: The LIBRFC32.DLL cannot be updated<br/>To shut down the IGS, proceed as follows:</p> <ul><li>Non-cluster installations: Kill the process as described in <b>Note 737099</b>.</li></ul> <ul><li>Windows Cluster installations:</li></ul> <ol><ol>a) Turn the following lines in you start profile into a comment:</ol></ol> <p>                       _IGS=igswd.exe Start_Program_06=local $(DIR_INSTANCE)\\igs\\bin\\$(_IGS) (this is one line) <p>                       -dir=$(DIR_INSTANCE)\\igs -mode=all <ol><ol>b) Restart the system. Restarting the system cannot be avoided in this case!</ol></ol> <p><br/>------------------------&lt; D038245 09/JUL/04 &gt;------------------------<br/><b>Adjust Profile Parameter \"rsts/ccc/cachesize\"</b><br/>Check whether profile parameter \"rsts/ccc/cachesize\" has been set. If yes, either delete the parameter or set it to 6.000.000 bytes as described in <b>note 5470</b>.<br/>Otherwise, you get the follwoing errors in several of the upgrade phases (e.g. PARCONV_UPG, XPREAS_UPG): Runtime error \"IMPORT_INIT_CONVERSATION_FAILED\"<br/>The sys log contains the message: \"E 14 Reorganized Buffer RSCPCCC with Length xxxxx (Overflow)\"<br/>The error also occurs in phases UVERS_CHK and START_SHDI_FIRST. Here you get a segmantation fault or Dr. Watson dump.<br/><br/>------------------------&lt; D038245 19/APR/04 &gt;------------------------<br/><b>Unicode Systems: Downward Compatible Kernel 6.40</b><br/>When you are using the normal kernel for Release 6.20 with your Unicode system, PREPARE issues the error message: Could not open the ICU common library.<br/>Before you start PREPARE, install the Downward Compatible Kernel for Release 6.40. Until this kernel is available, proceed as described in <b>Note 716378</b>.<br/><br/>------------------------&lt; D035318 04/FEB/04 &gt;------------------------<br/><b>Unicode Systems: Report RUTTTYPACT</b><br/>If your system is a unicode system, check whether you have run report RUTTTYPACT in your system after the installation. If you have not run the report, do so before you start PREPARE. To run the report, proceed as described in <b>note 544623</b>.<br/><br/>--------------------------&lt; D034302 16/JAN/04 &gt;-----------------------<br/><b>Java Application Server</b><br/>If you have Java Application Server installed and running together with your ABAP system, problems can occur when the upgrade program tries to exchange executables and libraries. The problems can occur during PREPARE module General Checks as well as during upgrade phase KX_SWITCH.<br/>To prevent these problems, shut down the Java Application Server during the upgrade.<br/><br/>--------------------------&lt; D022030 22/MAR/04 &gt;-----------------------<br/><b>Database Archives During the Upgrade</b><br/>Knowing the size of the archives written during the upgrade may help you select an upgrade or archiving strategy. The first of the two numbers specifies the size of the archives up to phase MODPROF_TRANS, while the second specifies the size of the archives up to the end of the upgrade.<br/>DB2 UDB for UNIX/Windows: 6 GB / 8 GB<br/>Informix: 8 GB / 10 GB<br/>MS SQL Server: 7 GB / 8.5 GB<br/>Oracle: 9.5 GB / 10.5 GB<br/>MaxDB: 5 GB / 6 GB<br/>These sizes are based on <b>sample data</b>.<br/><br/>--------------------------&lt; D022030 22/MAR/04 &gt;-----------------------<br/><b>Space Requirements in the Database</b><br/>You must enhance the database both during and because of the upgrade. The numbers specify the enhancement required temporarily during the upgrade.<br/>DB2 UDB for UNIX/Windows: 17 GB<br/>Informix: 18 GB<br/>iSeries: 20 GB*<br/>MS SQL Server: 9 GB<br/>Oracle: 17 GB<br/>MaxDB: 5.5 GB<br/>* largely depends on the upgrade and archiving strategy because when using upgrade strategy archiving_on, a rather big number of journal receivers may arise during the upgrade.<br/>These sizes are based on sample data. Additional freespace requirements (depending on your system) are calculated during the PREPARE.<br/>To determine realistic freespace requirements, you should execute PREPARE.<br/>After the upgrade, you can free space by deleting substitution tablespaces or removing superfluous database objects. For more information on how to proceed, see the Upgrade Guide under \"Post-Upgrade Activities\".<br/><br/>--------------------------&lt; D025323 24/APR/03 &gt;-----------------------<br/><b>Upgrade on AIX: saposcol</b><br/>Refer to Note <b>526694</b> before the upgrade.<br/><br/>--------------------------&lt; D019926 DEC/10/02 &gt;-----------------------<br/><b>Upgrading with AIX 5.1</b><br/>If you want to upgrade with AIX 5.1, see <b>Note 502532</b> before starting PREPARE.<br/><br/>-----------------------&lt; D025323 FEB/20/02 &gt;--------------------------<br/><b>Source Releases on UNIX 32-bit or AIX 64-bit</b><br/>In some cases, you may have to upgrade the operating system to 64-bit before the actual upgrade.<br/>When you upgrade from AIX 4.3 64-bit, you must perform some additional actions before upgrading.<br/>For more information, see <b>Notes 496963</b> and <b>499708</b>.<br/><br/>-----------------------------------------------------------------------<br/><br/><br/><br/></p> <b>VII/ Problems During the PREPARE and Upgrade Phases</b><br/> <p><br/>This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.<br/></p> <b>Problems During the PREPARE Phases</b><br/> <p><br/>------------------------&lt; D038245 05/SEP/05 &gt;------------------------<br/><b>Problem with DVD mount paths</b><br/>When you enter mount points in PREPARE, you may get the error message: SEVERE ERROR: unable to find directory<br/>In this case, check whether you mount point is longer than 94 characters or contains blanks and special characters. In this case, shorten the name and remove blanks or special characters.<br/><br/>-----------------------&lt; D038245 20/APR/05 &gt;---------------------------<br/><b>Phase RFCCHK_INI: Name or Password is Incorrect</b><br/>In phase RFCCHK_INI you may get the error message \"Name or Password is Incorrect\".<br/>If you replaced R3up after phase RFCCHK_INI with the latest version from SAP Service Marketplace, this error may also come up in other phases that connect to the system using RFC.<br/>For more information, see Note <b>792850</b>. You may have to replace disp+work and restart the system.<br/><br/>-----------------------&lt; D028310 NOV/03/04 &gt;--------------------<br/><b>Phase CONFCHK_IMP on Distributed Systems</b><br/>Phase CONFCHK_IMP offers you a list of operating systems to select from<br/>This list only contains one entry \"Linux\" which is valid for both Linux and Linux IA64.<br/><br/>-----------------------&lt; D038245 APR/11/02 &gt;--------------------<br/><b>Termination in the TOOLIMPD3 phase</b><br/>The TOOLIMPD3 phase terminates in the PREPARE module import. The following message appears in the log file: ABAP runtime error CALL_FUNCTION_NO_RECEIVER<br/> Receiving data for unknown CPIC link XXXXXX.<br/>Repeat the phase and continue with the upgrade.<br/><br/>-----------------------&lt; D022256 SEP/04/00 &gt;--------------------<br/><b>For Windows NT 4.0 only</b><br/>During PREPARE, a dialog box with the following error message may appear: 'The procedure entry point ... could not be located in the dynamic link library ... .',<br/>In this case, import the latest DLLs using the R3DLLINS.EXE program. The program is available on the CD SAP Kernel in directory \\NT\\&lt;Processor type&gt;\\NTPATCH.<br/>Reboot your machine and restart PREPARE with 'PREPARE repeat'.<br/><br/>-----------------------------------------------------------------------<br/><br/><br/></p> <b>Problems During the Upgrade Phases</b><br/> <p><br/>------------------------&lt; D028310 20/AUG/04 &gt;--------------------------<br/>Phase: DIFFEXPDDIV<br/>Note: 766379<br/>Description:<br/>Error message in log file DIFFEXPD.ELG (directory &lt;DIR_PUT&gt;/log):<br/>INACTIVE DDIC VERSIONS-Export ERRORS and RETURN CODE in SAPEDDD622.QO1<br/>~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br/>2EETW190 \"TABT\" \"TESCL                        \" has no active version.<br/>...<br/><br/>------------------------&lt; D025988 JUN/16/00 &gt;-------------------<br/>Phase: PARDIST_SHD<br/>Description: For Windows NT only<br/>The upgrade is terminated during the PARDIST_SHD phase. The PCONUPG.ELG log file contains an incomplete error text which was extracted from the DS&lt;date&gt;.&lt;SID&gt; log file. Repeat the phase.<br/><br/>--------------------&lt;changed D026178 JUN/25/04 &gt;--------------------<br/>-------------------------&lt; D026178 JUN/07/04 &gt;----------------------<br/>Phase: ACT_640<br/>Description: If you have included Basis Support Package 03 in the upgrade, you may get the following error message: SHADOW IMPORT ERRORS and RETURN CODE in SAPKGPAC01.QO1<br/> 1EEDO519 \"Table\" \"TPPROFILES_TYPE_PROFILE\" could not be activated.<br/>The same error may appear for table \"TPPROFILES_TYPE_PROFILE_INFO\".<br/>You can ignore the error. Repeat the phase to continue with the upgrade.<br/><br/>------------------------&lt; D001330 MAR/19/02 &gt;-------------------<br/>Phase: ACT_640<br/>Note: 504892<br/>Description: Errors in the SPDD comparison request<br/>When you save the SPDD request, the system issues an TR067 error message: \"Extended transport control is not active -&gt; Only target systems possible.\"<br/><br/>----------------------------------------------------------------------<br/>Phase: PARCONV_UPG<br/>Notes: 705724, 705733<br/>Description: If you have not included SP02 in the upgrade, you will receive the error \"CONV ENTRY TBATG TABLMESYBODY - Unable to interpret \"000A \" as a number.\"<br/>If you do not use Mobile Infrastructure, you can ignore this error.<br/><br/>----------------------------------------------------------------------<br/>Phase: IMPORT<br/>Note: 88656<br/>Description: You have included Suport Package SAPKB64012 and receive the following error message:<br/>2EETW000 Table RSMPTEXTS~: Duplicate record during array insert occured.<br/>Proceed as described in Note 88656, entry number 29.<br/><br/>--------------------------&lt; D019416 19/MAR/04 &gt;-----------------------<br/>Phase: TABIM_POST<br/>Note: 718912<br/>Description: Upgrade stops with error DI829. In this case, implement<br/>the above note and continue with the upgrade. After the upgrade, you have to revert the modification.<br/><br/>----------------------------------------------------------------------<br/>Phase: XPRAS_UPG<br/>Note: 778198<br/>Description: Error message S&gt;801 in log file LONGPOST.LOG. For more<br/>information, see the note above.<br/><br/>----------------------------------------------------------------------<br/>Phase: JOB_RDDNTPUR<br/>Note: 699458<br/>Description: If you get the error message<br/>3PETG447 Table and runtime object \"UMG_TEST_A\" exist without DDIC reference (\"Log. pool\")<br/>you can ignore it. For more information and a list of further tables for which you can ignore this error message, see the note above.<br/><br/>--------------------------&lt; D025323 MAY/23/02 &gt;-----------------<br/>Phase: CHK_POSTUP<br/>Note: 197886<br/>Description: If you have imported Notes 178631, 116095 or 69455 into your system before the upgrade, error messages for objects without DDIC reference appear in the log LONGPOST.LOG. Proceed as described in the Note.<br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p> <b>VIII/ Problems After the Upgrade</b><br/> <p><br/>This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br/><br/>-----------------------&lt; D035318 08/JUL/04 &gt;-------------------------<br/><b>Source Release lower than Basis 6.10: Codepage Conversion</b><br/>In Release 6.10, the codepage administration has changed considerably. If you want to continue using the customer-defined codepages that start with \"9\" after the upgrade, you have to convert the codepages using report RSCP0126 after the upgrade.<br/>For more information, see <b>Notes 485455</b> and <b>511732</b>.<br/><br/>-----------------------&lt; D035318 04/FEB/04----------------------------<br/><b>Unicode Systems: Run Report RUTTTYPACT</b><br/>If your system is a unicode system, you must run report RUTTTYPACT after the upgrade. To run the report, proceed as described in <b>note 544623 </b>.<br/><br/>------------------------&lt; D020815 AUG/23/02 &gt;------------------------<br/><b>SPAU: Names of interface methods are truncated</b><br/>Some methods (ABAP objects) that were modified and overwritten by the upgrade can be displayedin transaction SPAU with their names shortened to 30 characters.<br/>As a result, the system may also incorrectly sort methods in SPAU under \"Deleted objects\".<br/>Caution: Deleted objects are not displayed in the standard selection in SPAU. It is easily possible to overlook these!<br/>For more information about the correction, see <b>Note 547773</b>.<br/><br/>----------------------------------------------------------------------<br/><b>Linux: Importing the new saposcol version</b><br/>For more information, see <b>Note 19227.</b><br/><br/>----------------------------------------------------------------------<br/><b>ReliantUNIX: saposcol version 32-bit or 64-bit</b><br/>For more information, see <b>Note 148926.</b><br/><br/>----------------------------------------------------------------------<br/><b>Solaris: saposcol version 32-bit or 64- bit</b><br/>For more information, see <b>Note 162980.</b><br/><br/>----------------------------------------------------------------------<br/><br/><br/><br/><br/></p> <b>IX/ Chronological Summary</b><br/> <p><br/>Date.....Topic..Short description<br/>-----------------------------------------------------------------------<br/>SEP/15/08..III..SDK Version 1.4.x for Upgrade Assistant<br/>APR/23/07..III..Migrate data from table TVARV to TVARVC<br/>MAR/02/07..VII..Phase RFCCHK_INI: Table RSMPTEXTS~: Duplicate record<br/>NOV/28/06....V..Modification Adjustment Planning and Unicode Conversion<br/>OCT/19/06..III..Data Management Planning - Link to SMP<br/>SEP/15/06....V..Support Package SAPKB64018 - use corrected version<br/>JUN/21/06..III..Entries for Parameter Input Module: Path Length<br/>27/APR/06...VI..Handling of customer translation in the upgrade<br/>NOV/29/05...II..Upgrade on Linux x86_64: Correct R3up Version<br/>OCT/10/05...VI..Latest Upgrade Repairs for Correct Language Import<br/>15/SEP/05....V..Back up customer-specific entries in table EDIFCT<br/>05/SEP/05..VII..Problem with DVD mount paths<br/>09/AUG/05..III..Opt. Follow-Up: Where-Used List<br/>JUN/02/05....V..Do not include ABA Support Package 12 in the Upgrade<br/>APR/20/05..VII..Phase RFCCHK_INI: Name or Password is Incorrect<br/>MAR/18/05..III..Documentation: SAP Software on UNIX - OS Dependencies<br/>MAR/11/05..III..Windows Guide Section on Database-Specific Parameters<br/>FEB/25/05..III..Section \"SAP NW AS: J2EE Engine Installation Planning\"<br/>FEB/09/05..III..Section: Making Entries for the Extension Module<br/>FEB/02/05...II..LSMW now part of SAP_BASIS<br/>JAN/21/05..VII..Phase XPRAS_UPG: Error S&gt;801<br/>DEC/13/04...VI..Windows only: Shut Down IGS Before Starting PREPARE<br/>NOV/03/04..VII..Phase CONFCHK_IMP on Distributed Systems<br/>OCT/20/04....V..Function Groups in the Customer Name Space<br/>OCT/20/04..III..Section: Phase JOB_RSVBCHCK2<br/>AUG/20/04..VII..Phase: DIFFEXPDDIV<br/>JUL/13/04...VI..Adjust Profile Parameter \"rsts/ccc/cachesize\"<br/>JUL/08/04.VIII..Source Release lower than 6.10: Codepage conversion<br/>JUL/06/04..III..Phase INITSHD: Instance Number of the Shadow Instance<br/>JUN/07/04..VII..Phase ACT_640: SHADOW IMPORT ERRORS with Basis SP03<br/>MAY/07/04..III..Implementing the Integrated SAP ITS<br/>APR/19/04...VI..Unicode Systems: Downward Compatible Kernel 6.40<br/>MAR/04/04..VII..Phase TABIM_POST: Error DI829<br/>FEB/13/04..VII..Phase PARCONV_UPG: Table MESYBODY<br/>FEB/13/04....V..SPDD - Return to Standard<br/>FEB/04/04.VIII..Unicode Systems: Run Report RUTTTYPACT<br/>FEB/04/04...VI..Unicode Systems: Report RUTTTYPACT<br/>JAN/27/04....V..Import of Asian Languages: Change System Language<br/>JAN/22/04..VII..Phase JOB_RDDNTPUR: Error 3PETG447<br/>JAN/16/04...VI..Java Application Server<br/>DEZ/03/03...II..Windows only: Execute program R3dllins.exe<br/>SEP/23/03...VI..Database Archives During the Upgrade<br/>SEP/23/03...VI..Space Requirements in the Database<br/>SEP/17/03....I..R3up keyword<br/>APR/24/03...VI..Upgrade on AIX: saposcol<br/>DEC/10/02...VI..Upgrading with AIX 5.1<br/>AUG/23/02.VIII..SPAU: Names of interface methods are truncated<br/>JUL/23/02 ...I..Windows: Windows XP is not supported<br/>JUL/19/02...II..Problems with the shadow instance<br/>MAY/24/02...II..Corrections and repairs for the upgrade<br/>MAY/23/02..VII..Phase CHK_POSTUP - objects without DDIC reference<br/>APR/11/02..VII..Termination in the TOOLIMPD3 phase<br/>APR/08/02..III..Preparation for reading the upgrade CDs<br/>MAR/19/02..VII..Phase ACT_620: Errors in the SPDD comparison request<br/>FEB/20/02...VI..Source releases on UNIX 32-bit or AIX 64-bit<br/>OCT/19/00.VIII..Linux:  Importing the new saposcol version<br/>SEP/04/00..VII..Windows NT: Error message during PREPARE<br/>FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Reliant UNIX<br/>FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Solaris<br/>----------------------------------------------------------------------<br/></p></p></p></div>", "noteVersion": 26}]}, {"note": "2197392", "noteTitle": "2197392 - Resolve findings of core ERP MM-IM S/4HANA pre checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You executed the MM-IM prechecks to verify what needs to be done to install SAP S/4HANA and got a list of messages to work with.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition, S4HANA, <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">CL_S4_CHECKS_MM_IM, CLS4SIC_MM_IM_SI1, Precheck</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>During the migration, the content of the current database tables is transferred into the simplified data model and requires a certain level of data consistency in the current database tables which got simplified.<br/>SAP S/4HANA uses a simplified data model which may require special adjustments of customer development or modifications besides the usual SPAU or SPDD transactions.<br/>You have customer enhancements in the area of inventory management or material valuation (component MM-IM) which were built for SAP ERP 6.0.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The simplified data model replaces several stock tables with the new MATDOC table and makes quantity aggregate fields obsolete in master data tables (e.g. MARC). Tables with those adjustments will be called “simplified stock tables” in this document and comprise the following table names: MKPF, MSEG, MARC, MARD, MCHB, MKOL, MSKA, MSPR, MSKU, MSLB, MSSA, MSSL, MSSQ, MARCH, MARDH, MCHBH, MKOLH, MSKAH, MSKUH, MSLBH, MSPRH, MSSAH and MSSQH. On newer SAP ERP releases it furthermore comprises the stock-in-transit tables: MSTB, MSTE, MSTQ, MSTBH, MSTEH and MSTQH.<br/>The tables MARC, MARD, MCHB, MKOL, MSKA, MSKU, MSLB and MSPR are called “hybrid” tables because the master data parts of those tables remain but the transactional parts are deprecated and are calculated on the fly using the new MATDOC table. <br/>The remaining tables MKPF, MSEG, MSSA, MSSL, MSSQ, MARCH, MARDH, MCHBH, MKOLH, MSKAH, MSKUH, MSLBH, MSPRH, MSSAH, MSSQH, MSTB, MSTE, MSTQ, MSTBH, MSTEH and MSTQH are called “replaced aggregation or document tables”.<br/>In the area of Material Valuation, the following tables have been “simplified”: EBEW, EBEWH, MBEW, MBEWH, OBEW, OBEWH, QBEW and QBEWH.</p>\n<p><strong>How to find the relevant information</strong><br/>Consider you have an error message like: Check ID \"ALL_MATNR_IN_MARA' (Application component MM-IM) , return code '8' Check info:MANDT:123, MATNR MY_MATNR of the table MARDH does not exist in the table MARA - See note :2197392<br/>Now take the Check ID \"ALL_MATNR_IN_MARA\" and search for it in this note. You will then find the chapter dedicated to this specific Check ID. <br/>Proceed like this for all error messages that are reported from the precheck.</p>\n<p><br/><span><strong>Data consistency</strong></span><br/><strong>Material Number (MATNR) must exist in simplified MM-IM tables</strong><br/>Check ID: ALL_MATNR_IN_MARA<br/>All simplified stock tables (except MKPF) have a field MATNR. Each distinct MATNR entry must have a corresponding entry in table MARA which contains all materials.<br/>This error might come up if material documents are differently archived than the material master data. In this case, it is recommended to archive those material documents for which the material master does no longer exist in MARA using the archiving object MM_MATBEL and/or MM_SPSTOCK.<br/>If this error is reported for history tables like MARDH you might consider executing report MBARCHHIST to delete orphaned entries (entries that only exist in the history table but not in the corresponding non-history table).</p>\n<p><strong>BUKRS values of MSEG must exist in T001</strong><br/>Check ID: BUKRS_IN_MSEG<br/>Table MSEG contains a field BUKRS for which a corresponding entry must exist in table T001 for each BUKRS value of MSEG.<br/>It is recommended to archive the simplified table entries that caused the conflict in case the referenced company code is no longer valid in the system.</p>\n<p><strong>BUKRS values of MSEG must match those in T001K</strong><br/>Check ID: MSEG_VS_T001K_BUKRS<br/>Table MSEG contains the fields BUKRS and WERKS. For the WERKS field, there must be an entry in T001W and based on the BWKEY field an entry must exist in T001K. The WERKS+BUKRS combination in those two tables must match the combination of the MSEG entries. If they do not match, the company - plant assignments had changed in the past.<br/>It is recommended to archive the MSEG table entries that caused the conflict.</p>\n<p><strong>WERKS to BUKRS foreign key chain</strong><br/>The foreign key chain is checked for this field / table combination:<br/>For each distinct value of WERKS in each of the simplified stock tables (except MKPF and MSEG), there must be a corresponding entry in table T001W. (Check ID: ALL_WERKS_IN_T001W)<br/>For each of those entries in T001W, there must be an entry in table T001K based on the BWKEY. (Check ID: ALL_BWKEY_IN_T001K)<br/>For each of those entries in T001K, there must be an entry in MARV based on the BUKRS. (Check ID: ALL_BUKRS_IN_MARV)<br/>For all of those entries in T001K, there must be an entry in T001 based on the BUKRS. (Check ID: ALL_BUKRS_IN_T001)<br/>If this error occurred, the archiving might not have been completed correctly. It is recommended to archive the remaining simplified stock table entries that caused the conflict.</p>\n<p><strong>Periods must be valid<br/></strong>Check ID: MONTH_YEAR_PERIOD<br/>Simplified stock tables with fields LFGJA and LFMON must not have initial period values to avoid migration errors. No posting date can be determined for the last day of an initial period. For table MARC, MARD or MCHB (new with note <a href=\"/notes/2271461\" target=\"_blank\">2271461</a> and version 10 of precheck implementation note 2194618) and initial stock values, no errors will show up as they can be ignored during migration. If there are any other instances with such an inconsistency you need to check how they could be corrected. There is no general guidance how this can be achieved.</p>\n<p>Check ID: PERIOD_CHECK<br/>This check validates any LFGJA and LFMON fields with its corresponding PERIV field against tables T009 and T009B. If this error occurred, check why the period is not maintained in table T009B or if this a an inconsistent entry in any of the checked MM-IM database tables. <br/>For releases up to (and including) 1610, you can run this check manually with report MMIM_S4_PRECHECK_RESULT to get also the tablename for which the error occurs. From release 1709 onwards, the report MMIM_S4_PRECHECK_RESULT is no longer required.</p>\n<p><strong>MKPF and MSEG consistency<br/></strong>Check ID: ALL_MKPF_EXIST<br/>All entries in table MSEG must refer to a single entry in table MKPF. This check will find any MSEG entries without a corresponding MKPF header entry.<br/>Such entries will not be converted to the new data model! So check in advance whether the entries are still needed or not. If they are needed, correct the inconsistency.</p>\n<p>Check ID: ALL_MKPF_WITH_MSEG<br/>All entries in table MKPF shall have at least one entry in table MSEG to reflect a material movement. This check will find any MKPF entries without a corresponding MSEG item entry. <br/>Such entries will not be converted to the new data model (MATDOC table)! So check in advance whether the entries are still needed or not. If they are needed, correct the inconsistency.</p>\n<p>Check ID: NO_MKPF_POSTING_DATE<br/>All material documents are posted on a certain date. If that date does not exist, it is an inconsistency that needs to be corrected. If you find such inconsistencies it might be helpful to create an incident on MM-IM-GF-INC to analyze this in detail.</p>\n<p>Check ID: BWART_IN_MSEG<br/>All material documents must have a valid stock movement type. If a stock movement type used in the material document item does not exsist in T156, it is an inconsistency that needs to be corrected: Either the material document that use such stock movement types have to be archived before the conversion or you need to re-create the missing stock movement type.</p>\n<p><strong>Quantity aggregation consistency<br/></strong>Check ID: QTY_AGGR_CHECK<br/>Quanties between certain pair of dependant tables need to be aggregated correctly. This check will find discrepancies between these paired tables.<br/>These pairs of tables are: MCHB/MARD, MKOL/MARD, MSKA/MSSA, MSLB/MSSL, MSPR/MSSQ, MSTB/MARC, MSTE/MSSA, MSTQ/MSSQ, MCSD/MCSS, MSCD/MSCS, MSFD/MSFS, MSID/MSIS and MSRD/MSRS.<br/>In case errors occur, they can be analyzed further with transaction MB5K. Alternatively, if the erroneous records are old and they are not needed any more, they can be archived.</p>\n<p>Check ID: QTY_AGGR_CHECK_NON_SIT<br/>This check validatesthat there are no quantities in stock in transit for batch managed materials without an active business function 'stock in transit (SIT)'. In this case the quantities are only stored in field BWESB of table MARC. In S/4HANA the quantities must be additionally stored in SIT-tables like e.g. MSTB, etc. as the respective business function is always set active in S/4HANA. <br/>Solution: Remove the respective stock quantities from table MARC in the ECC-system via transaction MIGO using movement type 109 (batch assignment might be required) and memorize the new created material documents. After the successful conversion reverse these memorized documents again to rebuild the quantities in stock in transit. The described procedure ensures that all relevant stock tables are updated and the surrounding documents (purchase orders, deliveries) are in an consistent state.</p>\n<p><strong>Valuation consistency<br/></strong>Check ID: KALNR_CHECK<br/><span>Precondition</span>: Split valuation is configured for the material (and company code). In addition the valuation by sales order or project (KZBWS = M) or by supplier (XOBEW =X) is active.</p>\n<p>The check lists inconsistent stock quantities comparing two tables: First table (e.g. MSSL) keeps the quantity on an aggregated level across valuation types (BWTAR). The second table (e.g. MSLB) keeps the quantities per valuation type (BWTAR). Both tables must match. Also the periods of relevant history tables are checked.<br/>For each inconsistent data record, the check result displays the names of the aggregated stock table, the name of the detailed stock table, and the key fields. All these errors can be analyzed further with transaction MB5K (see note <a href=\"/notes/34440\" target=\"_blank\">34440</a>). Alternatively, if the listed records are old and they are not needed any more, they can be archived.</p>\n<p>Check ID: KALNR_VAL_STOCK<br/>This check ensures that the relevant valuation data exists for all stock tables (like MARC, MARD, ...). The warnings always show the name of the stock table followed by the key fields of the inconsistent entry and the name of the valuation table (like MBEW) where the corresponding entry is missing.</p>\n<p>All these findings can be analyzed further with transaction MB5K (see note <a href=\"/notes/34440\" target=\"_blank\">34440</a>). Alternatively, if the listed stock table records are old and they are not needed any more, they can be archived. Also the valuation settings (WERTU in material type definition, or KZBWS in project/sales order) can be changed. To do this, all stock has to be posted out temporarily, then the settings are changed and finally the stock is posted back in.</p>\n<p>Remark: this warning also occurs for material master records with valuation relevant material type where the accounting view is missing. In case such materials have never been used for goods movements, this inconsistency was never discovered. The problem is simply fixed by creating the missing accounting view or by deleting unused master data records.</p>\n<p>Check ID: KALNR_VAL_DOC<br/>This check ensures that the relevant valuation data exists for all material document items. The findings always show the name of the material document item table (MSEG) followed by the key fields of the inconsistent entry and the name of the valuation table (like MBEW) where the corresponding entry is missing.</p>\n<p>If the listed entries are old and they are not needed any more, they can be archived.</p>\n<p><strong><span>Customer enhancements or modifications</span></strong><br/><strong>Views on simplified tables</strong><br/>Check ID: VIEW_ON_REDIRECT<br/>There shall be no customer views on any of the simplified tables. If there are any findings, see note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> on how to adjust views on simplified stock tables or note <a href=\"/notes/2217299\" target=\"_blank\">2217299</a> for material valuation tables.</p>\n<p><strong>View-appends on redirected views</strong><br/>Check ID: APPEND_ON_REDIRECTED_VIEW<br/>There shall be no appends on any of the redirected views delivered by SAP as appends are technically not supported on redirected views. If there are any findings, see note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> on how to adjust view appends on redirected tables or note <a href=\"/notes/2217299\" target=\"_blank\">2217299</a> for material valuation tables.</p>\n<p><strong>Append-field conflicts of MKPF and MSEG</strong><br/>Check ID: APPEND_DUPL<br/>If there are appends on MKPF and MSEG which both have fields with identical names, this will cause a naming conflict as both tables are migrated into one denormalized target table. If the field contains identical content at MKPF and MSEG, there is nothing which needs to be done. If the field contains different content, an unspecified state will exist in the target table! For such conflicts see note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> on how to solve append-field conflicts of MKPF and MSEG.</p>\n<p><strong>Append-fields on replaced aggregation tables</strong><br/>Check ID: APPEND_ON_FULLY_REPLACED_TAB<br/>Appends of aggregation tables which have been replaced by on the fly aggregations cannot be migrated into MATDOC because of the different cardinality. <br/>Appends on table MKPF and MSEG can be migrated to table MATDOC.<br/>If there are any findings, see note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> on how to adjust appends on replaced aggregation tables or note <a href=\"/notes/2217299\" target=\"_blank\">2217299</a> for material valuation tables.</p>\n<p><strong>Append-fields on hybrid tables<br/></strong>Check ID: APPEND_ON_PARTIALLY_REPLACED_TAB<br/>See note <a href=\"/notes/2217299\" target=\"_blank\">2217299</a> on how to migrate appends on material valuation tables using the extension mechanisms of CDS views.</p>\n<p>Check ID: APPEND_BETWEEN_SAP_FIELDS<br/>Append fields which are not at the end of a table structure but between SAP fields cannot be migrated using the extension mechanisms of CDS views.<br/>See note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> on how to resolve such issues on hybrid tables or note <a href=\"/notes/2217299\" target=\"_blank\">2217299</a> for material valuation tables.</p>\n<p><strong>Potential aggregates and hybrid tables</strong><br/>Check ID: QUANTITY_IN_APPEND<br/>The precheck will return warnings for quantity fields on hybrid tables as potential aggregate fields. Double-check whether this is an aggregate or a master data field. If the append fields are master data fields, there is nothing which needs to be done. <br/>Any transactional fields in master data tables need to be reworked depending on the scenario they were used for. SAP cannot give a guidance on how this shall be done.<br/>You may also check note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> on how to adjust appends on hybrid tables or note <a href=\"/notes/2217299\" target=\"_blank\">2217299</a> for material valuation tables.</p>\n<p><strong>Customizing including CI_COBL</strong><br/>Check ID: CODING_BLOCK_CUSTOMIZING<br/>This warning is only relevant if you are targeting S/4HANA version 1511 SP00! For all later service packs and releases, this check is obsolete.<br/>See note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> and <a href=\"/notes/2242679\" target=\"_blank\">2242679</a>.</p>\n<p><strong>Other customizing includes</strong><br/>Check ID: CUSTOM_INCLUDE<br/>If this warning is issued for MKPF or MSEG, add the include to the corresponding include structure of table MATDOC i.e. NSDM_S_HEADER and NSDM_S_ITEM respectively (for details, please refer to note <a href=\"/notes/2206980\" target=\"_blank\">2206980</a> section 1.1)<br/>If this warning is issued for a replaced aggregation table, there is nothing you can do about this similarly to check id: APPEND_ON_FULLY_REPLACED_TAB.</p>\n<p><strong><span>Customizing</span></strong><br/><strong>Active LIS and active Late Lock strategy</strong><br/>Check ID: LIS_CUSTOMIZING_CHECK<br/>See note <a href=\"/notes/2319579\" target=\"_blank\">2319579</a></p>", "noteVersion": 28, "refer_note": [{"note": "2539490", "noteTitle": "2539490 - S4TC Correction for 2503309 - Enhancement for Pre-check (new framework): wrong quantity aggregation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Precheck for S/4HANA conversion is not checking that quantities between certain pairs of dependant tables are aggregated correctly.</p>\n<p>These pairs are:<br/>MSLB  and MSSL<br/>MSCD and MSCS<br/>MSFD and MSFS<br/>MSID and MSIS<br/>MSRD and MSRS<br/>MSKA and MSSA (this check is introduced with note <a href=\"/notes/2581131\" target=\"_blank\">2581131</a>)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition, pre-check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note enhances the exisiting pre-check.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Check ID QTY_AGGR_CHECK is introduced in note <a href=\"/notes/2197392\" target=\"_blank\">2197392</a>.</p>\n<p>Please implement the included correction instructions.</p>", "noteVersion": 5}, {"note": "2271461", "noteTitle": "2271461 - S/4HANA MM-IM migration: MCHB with initial LFGJA LFMON", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You executed the MM-IM prechecks and get errors 'Periods must be valid' with check ID  MONTH_YEAR_PERIOD for records in table MCHB (see note <a href=\"/notes/2197392\" target=\"_blank\">2197392</a>).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, S4HANA,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Simplified stock tables with fields LFGJA and LFMON must not have initial period values. Initial LFGJA and LFMON causes the error NSDM_MM_MIG 021 during aggregate migration as no posting date BUDAT with the last day of the initial period can be determined for a new MIG_DELTA record as described in note <a href=\"/notes/2238690\" target=\"_blank\">2238690</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With assigned correction MCHB records with initial LFGJA and LFMON are ignored during aggregate migration if they have also initial stock values. That was implemented for MARC and MARD already. The pre-check implementation note 2194618 is adopted accordingly with version 10 and the resolve findings note <a href=\"/notes/2197392\" target=\"_blank\">2197392</a> with version 7.</p>\n<p>Implement assigned correction instruction and the new pre-check note versions.</p>", "noteVersion": 1}, {"note": "2523644", "noteTitle": "2523644 - S4TC Correction for 2503309 - Precheck emhancement: Check that movement types used by material documents exist", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Precheck for S/4HANA conversion is not checking the existence of the movement types used by material documents.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MKPF MSEG-BWART T156 BWART_IN_MSEG</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Check ID BWART_IN_MSEG is introduced in note <a href=\"/notes/2197392\" target=\"_blank\">2197392</a> to check that a movement type used by a material document exists in T156.</p>\n<p>Apply the correction instruction.</p>", "noteVersion": 3}, {"note": "2319579", "noteTitle": "2319579 - S4TWL - Performance optimizations within Material Document Processing - lock behavior and stock underrun protection", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><strong>1. General</strong></p>\n<p>With the introduction of the new MM-IM data model there are opportunities and drawbacks. The opportunities will offer new process modes whereas the drawbacks require changes of the process behavior. Both will have effects on follow-on processes or side processes.</p>\n<p><strong>2. Opportunities</strong></p>\n<p><strong>2.1 Locks to protect stock</strong></p>\n<p>With the introduction of the new MM-IM data model in S/4HANA OP1511 there are no UPDATE processes on the actual stock quantity fields in the material master and stock aggregate tables anymore. Instead of the UPDATEs only INSERTs into the new material document table will be done. Hence from a data base perspective there are no locks anymore on DB level. That enables - from a DB perspective - parallel execution of material document processing. Anyway, from a business perspective there are still logical locks required because</p>\n<ol>\n<li>Consistency with material master data (i.e. prevent parallel change of master data attributes). This can be ensured already with a shared logical lock because with such a lock a change of material master is protected.</li>\n<li>Defined sequence of material documents (e.g. for moving average price).</li>\n<li>Consistency with stock quantity (check availability if negative stock is not allowed).</li>\n</ol>\n<p>The consistency with master data and with stock quantity (1 + 3) is necessary in any case. The defined sequence of material documents (2) is ensured only in the case that exclusive lock is chosen in the customizing. Regarding the lock of materials to enable a consistent quantity check (lock reason 3) there are currently two different kinds of lock strategies: early exclusive quantity lock and late exclusive quantity lock.</p>\n<p><strong>2.2 Early lock strategy</strong></p>\n<p>In the early exclusive lock strategy, all material master data and quantities are read in an array fetch before the item processing (except for special stocks) and exclusive locks are set during the entire processing of the material document. As this implies that valid quantities are read from the buffer, this option has the best performance regarding process runtime, but on the other hand it has a worst performance regarding process throughput in scenarios with many equal material items in parallel processes.</p>\n<p><strong>2.3 Late lock strategy</strong></p>\n<p>In the late exclusive lock strategy, the material master data and quantities are read also in an array fetch before the item processing but no exclusive locks are set. An exclusive lock will be set only for a short period of time during the item processing for each specific item. This exclusive lock will be converted into a shared lock after checks for stock underrun.</p>\n<p>As the new MM-IM data model changes the old key figure model to an account model this enables a more finer granular locking for the late quantity lock case starting with S/4HANA OP1610 and CE1608. For the key figure model the locks have been set on material and plant level in S/4HANA OP1511. With the account model the locks can be set on material, plant, storage location, stock identifier, special stock identifier level (in principle what defines the stock in the new model) starting with S/4HANA OP1610 and CE1608. Hence, even in parallel processes the probability for a lock collision is rather likely. Furthermore, by changing the sequence of sub-process steps under the hood of the exclusive look in the late lock strategy the duration of this exclusive lock can be shortened.</p>\n<p>With S/4HANA OP1610 and CE1608 a further option to increase throughput will be introduced. This is the option to define in the customizing that, for the late lock strategy, no exclusive lock shall be set. In this case only late shared locks will be set which really enables parallel material document processing (stock underrun checks are still possible because in this case all processes communicate their withdrawals to the lock server who in this case serves as second storage for stock changes (first storage is the DB) and the calculation operates on data from the first and the second storage).</p>\n<p><strong>2.4. Locks on batches</strong></p>\n<p>Locks on batch materials will be set in the current process to protect parallel changes of the batch master. In S/4HANA OP1511 those locks will be set without any conditions, means they will be set even if the material document posting does not change any of the batch master data. Starting with S/4HANA OP1610 and CE1608, to reduce the collision rate of parallel material document postings for batch material line items those batch locks will be set only if the batch master data like production date or best before date are entered during the MM-IM document processing and these data differ from the data on the DB.</p>\n<p><strong>3. Overcome drawbacks</strong></p>\n<p>With the introduction of the new MM-IM data model for S/4HANA OP1511 the material stock quantity data are not stored anymore as pre aggregated data in the old hybrid (containing material master and material actual stock quantity data) or aggregate (containing only material actual stock quantity data) tables. Instead the material stock quantity data will be calculated on-the-fly from the material document table. For applications this is transparent because the calculation of stock data as well as the determination of the material master data will be done via CDS-compatibility views which are assigned as proxy objects to those tables with hybrid or aggregate character and each fetch to the tables get redirected in the DBI layer to the assigned proxy view. According to the on-the-fly calculation, read accesses to those hybrid and aggregate tables are much slower than in Suite on HANA where the aggregated quantity changes have been stored in these tables as actual stock quantity data.</p>\n<p>During material document processing, material data is read from the database in general in an array fetch at the beginning of the processing. A second access to the database to read material data will be done in case of the late exclusive lock during the item processing. In both cases – due to the compatibility view – the material master as well as the material stock quantity data will be read whereas in the prefetch only the master data is required and during the late exclusive lock phase only the material stock quantity data is required.</p>\n<p>To turn the drawback of on-the-fly calculation into an advantage the following will be introdcued with S/4HANA OP1610 and CE1608:</p>\n<ul>\n<li>during the prefetch phase only the material master data will be read into internal buffers by using the pure material master data views to retrieve the data from DB</li>\n<li>during the item processing the stock quantity data will be read in the account model from the data base, and this will be done only in case of stock decreasing processes where negative stocks are prohibited</li>\n</ul>\n<p>Because it is known that HANA can process a single array fetch faster than many single fetches, the stock quantity read and stock underrun check mentioned in the last bullet point can also be done outside of the item processing. This array operation, which does also the lock operation, is executed directly in the function module MB_CREATE_GOODS_MOVEMENT after the item processing therein has been finished. The decision whether the read and check operations are done during or after item processing is done at the beginning of the processing. The new BAdI BADI_NSDM_READ_STOCK can be used by customers to overrule the decision.</p>\n<p>This changes to turn the drawbacks have been done irrespective of the lock strategy. It is effective in early as well as late lock strategy.</p>\n<p>Hence for S/4HANA OP1610 and CE1608, the adaptation done for the late lock strategy allows parallel execution of material document processes whereas the stock quantity read from DB and stock underrun check reduces the processing time of a single material document process. Overall it increases the performance of material document processing in S/4HANA OP1610 and CE1608 compared to S/4HANA OP1511 and Suite on HANA.</p>\n<p><strong>4. Permission of negative stocks</strong></p>\n<p>It is possible to allow on plant level negative stocks. Also on storage location level negative stocks can be permitted. And for the material itself a negative stock can be enabled. All this was possible to configure already in S/4HANA OP1511 and also in Suite on HANA. If on all 3 level the permission on negative stock has been granted then in S/4HANA OP1610 and CE1608</p>\n<ol>\n<li>no locks at all will be set in the late lock strategy case.</li>\n<li>no stock determination from the data base will be done and no stock underrun check will be executed (in both lock strategies).</li>\n</ol>\n<p>Hence for such a configuration and setting of material master data the runtime and the throughput may increase further.</p>\n<p><strong>5. Business process related information</strong></p>\n<p><strong>5.1 Modifications in the coding of Material Document Processing</strong></p>\n<p>As already described in the Overcome Drawback section, stock quantity data will be read and calculated only for stock decreasing processes where negative stocks are prohibited. And this will be done, if necessary, in the new account model. Hence, the actual stock quantity fields in the internal tables XMabc tables used and routed internally are always zero and cannot be used for any subsequent calculation incorporated in the material document processing. There is no option to populate these actual stock quantity fields in the internal tables XMabc tables anymore! Thus, customer coding modifications using stock quantity data from those XMabc internal tables must be adjusted. Stock change information is available via it_stock_change = cl_nsdm_mmim_read_stock=&gt;get_instance( )-&gt;get_stock_change_for_bte( ).</p>\n<p><strong>5.2 Logistics Information System (LIS)</strong></p>\n<p>LIS processing during any document processing means that document data will be prepared (transformed) into LIS structures and those data will be finally written into so called LIS information structures. Customers can enrich those data structures as well as define their own information structures. LIS processing means that the document processing time gets enlarged, increases significantly the memory footprint due to the LIS information structures and forces (at least for MM-IM) a serialized processing in case of V1 mode. Data in those LIS information structures will be used for special reporting by customers.</p>\n<p>A forced serialization of MM-IM document processing due to active LIS and a customizing of late lock strategy to enable parallel processing for MM-IM does not make sense at all. Furthermore several LIS key figures like # of times a zero stock would be reached by a posting, cannot be determined reliable in parallel processes. Hence, active LIS and late lock mode is defined now as an inconsistent system state starting with S/4HANA OP1610 and CE1608!</p>\n<p>Furthermode, because several fields in internal structures (exactly those stock quanitity fields of the former key figure model like field LABST in the internal structure XMARD) are not populated with data anymore with the active optimization, absolut stock quanities can not be send via the INVCON0x IDOC (message type INVCON).</p>\n<p>If an On-Premise system is configured for early lock strategy then LIS and the INVCON message type can be used. In this case the processing will be internally switched into the legacy mode. That means that none of the above described optimizations will be executed. This is necessary just because the optimized coding does not fill the internal XMabc tables with stock data in the key figure model anymore (just because in many cases stock data are not calculated at all) and the data in those internal tables are required for LIS processing and the INVCON message type.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MM-IM; BAPI_GOODSMVT_CREATE; MB_CREATE_GOODS_MOVEMENT; MIGO; Performance; Locks; LIS; INVCON; APO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Introduction of new MM-IM data model enables improvements in lock behavior and reduction of processing time and increase of throughput.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Switching the lock strategies</strong></p>\n<p>The toggling between early lock and late lock strategy can be done via customizing transaction OMJI. Marking the flag \"Exclusive block\" means early lock strategy whereas marking \"Late exclusive block\" means late lock strategy. Toggling of these two lock modes is possible. The setting of \"no exclusive lock\" for the late lock strategy has to be done by executing program SAPRCKM_NO_EXCLUSIVELY_LOCKING. This change can not be undone. For this setting please refer especially to note 2267835.</p>\n<p>The strategy late lock/non-exclusive seems to be a good strategy if - by business means - it is ensured that goods receipt postings are always be processed before goods issue or transfer postings are done.</p>\n<p><strong>LIS</strong></p>\n<p>LIS settings can be done by executing the customizing transaction OMO9 and LBW1 but only for application \"Inventory Controlling)). LIS update processing is only possible for early lock strategy. For late lock strategies LIS has to be deactivated by setting \"No updating\" for all LIS info structures (in transaction OMO9 or LBW1 (application \"Inventory Controlling) do a double click on an info structure; the Parameters dialog box appears and in this dialog mark \"No updating\"; do that for each info structure). Toggling on/off of LIS is not that easy as for the lock strategies. Toggling LIS from off to on requires the re-generation of the content of the LIS info structures.</p>\n<p><strong>Permission of negative stocks</strong></p>\n<p>At first the permission has to be granted on plant level. Go to SAP IMG --&gt; Materials Management --&gt; Inventory Management and Physical Inventory --&gt; Plant Parameters. Here you can grant negative stocks on special stock types.</p>\n<p>At second the permission has to be granted on storage location level. Go to SAP IMG (or use customizing transaction OMJ1) --&gt; Materials Management --&gt; Inventory Management and Physical Inventory --&gt; Goods Issue / Transfer Postings --&gt; Allow negative stocks. Here you can grant negative stocks for storage locations in a plant assigned to a valuation area.</p>\n<p>At third the permission has to be granted for the material itself. Execute material maintenance (e.g. transaction MM02) and enter the tab \"Plant data / stor. 2\". Mark flag \"Neg. stocks in plant\"</p>\n<p><strong>Customer modifications</strong></p>\n<p>Real modification of SAP coding by customers in the area of function group MBWL or the MIGO main program SAPMM07M and its includes using stock aggregate data from the internal tables XMabc must be refactored. Implemented BAdI methods are not affected by this.</p>\n<p> </p>", "noteVersion": 4}]}, {"note": "2378796", "noteTitle": "2378796 - Material classification: Change in data model in SAP S/4HANA 1610", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP S/4HANA 1610 or higher and need additional information how to adjust your customer enhancements, modifications or own functionalities to the new, simplified data model of SAP S/4HANA material classification with commodity codes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Classification with commodity codes, International Trade, STAWN, EXPME, Custom Code, V_MARC_MD.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have customer enhancements, modifications or own functionalities in the area of \"material classification with commodity codes\" which were built for SAP ECC or SAP S/4HANA 1511.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Any database view built (for e.g. V_MARC_MD)  built on database table \"MARC\" shall not be used to read fields STAWN and EXPME<span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">.   </span></p>\n<p>Instead class /SAPSLL/CL_MM_CLS_SERVICE (method GET_COMMODITY_CODE_CLS) shall be used to access commodity code and</p>\n<p>            class /SAPSLL/CL_MM_CLS_SERVICE (method GET_COMMODITY_CODE_DETAILS) shall be used to access Unit of measure for commodity code.</p>", "noteVersion": 5}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": "Decide which locking methods to be used for goods movements"}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Technical System Configuration", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}