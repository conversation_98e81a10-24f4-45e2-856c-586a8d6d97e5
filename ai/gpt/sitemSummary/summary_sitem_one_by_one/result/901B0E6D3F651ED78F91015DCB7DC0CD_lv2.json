{"guid": "901B0E6D3F651ED78F91015DCB7DC0CD", "sitemId": "SI06: OGSD_COLLECTIVE_ORDERS", "sitemTitle": "S4TWL - OGSD - Collective Orders", "note": 2490624, "noteTitle": "2490624 - S4TWL - OGSD - Collective Orders", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert a system with Add-on OGSD to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Simplification Item, System Conversion to SAP S/4HANA, Transactions /ICO/MO_CO01, /ICO/MO_CO, /ICO/MO_H1,/ICO/MO_H3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are using the OGSD application Collective Orders</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description:</strong></p>\n<p>Collective Orders is an OGSD application that involves customers ordering materials for themselves and for other customers in the surrounding area, for example, to obtain better conditions from the supplier.</p>\n<p><strong>Business related information:</strong></p>\n<p>The business advantages of collective orders include more efficient use of means of transport and more options for pricing, due to lower shipment costs and the fostering of relationships between the customer and the company. The ordering parties for a collective order are clearly mapped in a customer hierarchy.</p>\n<p><span><strong>Please note:</strong></span></p>\n<ol>\n<li><strong>OGSD application \"Collective Orders\" is discontinued</strong> when converting to SAP S/4HANA and using <strong>S4SCSD Release 1.0</strong>. In this case <strong>all its program- and DDIC-objects are lost after a conversion</strong> to SAP S/4HANA.</li>\n<li><strong>But this application is again available</strong> when converting to SAP S/4HANA and using <strong>S4SCSD Release 2.0</strong>. In this case <strong>all its program- and DDIC-objects are kept and usable after a conversion</strong> to SAP S/4HANA.</li>\n</ol>\n<p>For further information about Collective Orders see:</p>\n<p><a href=\"https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/4f330f54366b2e18e10000000a174cb4.html\" target=\"_blank\">https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/4f330f54366b2e18e10000000a174cb4.html</a></p>\n<p><strong>Required and recommended action(s):</strong></p>\n<p><strong>A) After conversion to S/4HANA and S4SCSD 1.0:</strong></p>\n<ol>\n<li>There is no such function in S4SCSD 1.0. If you want to continue using a functionality in SAP S/4HANA that is provided at present by Collective Orders in OGSD for the Business Suite, you need to switch to other SAP- or third-party-software.</li>\n<li>You need to consider this as a project. All possible adaptations need to be executed manually.</li>\n<li>In case of switching to a new software you need to organize knowledge transfer to all users working with Collective Orders as their pattern of work will change when working with new software. Users will have to use new applications for creating, changing, displaying  and processing new documents.</li>\n</ol>\n<p><strong>B) After conversion to S/4HANA and S4SCSD 2.0:</strong></p>\n<ol>\n<li>No action is required, as this application does exist in S4SCSD 2.0.</li>\n</ol>\n<div></div>", "noteVersion": 4, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "Collective order functionality is not available in SAP S/4HANA 1610 and 1709 with add-on S4SCSD 1.0, but with SAP S/4HANA 1809 and add-on S4SCSD 2.0. Decide if SAP S/4HANA 1809 and S4SCSD 2.0 can be used as target. With SAP S/4HANA 1809 no further activities are required."}]}