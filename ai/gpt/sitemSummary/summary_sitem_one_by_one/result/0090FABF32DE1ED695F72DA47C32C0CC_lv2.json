{"guid": "0090FABF32DE1ED695F72DA47C32C0CC", "sitemId": "SI8: FIN_CO", "sitemTitle": "S4TWL - Reporting/Analytics in Controlling", "note": 2349297, "noteTitle": "2349297 - S4TWL - Reporting/Analytics in Controlling", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p dir=\"ltr\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p dir=\"ltr\">This note is relevant if you are doing a conversion from ERP ECC 6.0 (EHP 7 or higher)  to S/4 HANA.</p>\n<p dir=\"ltr\"><strong>Description</strong></p>\n<p dir=\"ltr\">You want to use the new reporting tools and the universal journal for your reporting tasks<strong>, </strong>but need to understand which controlling reports are not yet covered in SAP S/4HANA. You also need to understand where the plan data for reporting resides.</p>\n<p dir=\"ltr\">The universal journal brings together the account assignments traditionally considered to be part of Controlling (cost center, order, WBS element, and so on), Profitability Analysis (products, product groups, customers, customer groups, regions, and so on) and Enterprise Controlling (profit center). This provides a profit and loss statement with a drill-down to the above reporting dimensions and dedicated reports by profit center, functional area, cost center, internal order, project, and market segment. The merge of accounts and cost elements means that you also effectively see CO postings in the trial balance and related reports.</p>\n<p dir=\"ltr\">From a reporting point of view this means that the conventional component barriers, no longer hold. Where a drill-down report in the general ledger could previously drill down by any dimension in FAGLFLEXT but not to the CO account assignments, with SAP S/4HANA you have more flexibility and can report on any of the dimensions listed above. Nevertheless if you continue to use a classic drill-down report (built using transaction FGI1), you will only see the reporting dimensions that were previously available in the SAP General Ledger, because you are technically using a compatibility view. This means that the relevant data has to be aggregated on the fly from the universal journal for the fields in the compatability view. These reports also currently show planning data created for the GL dimensions using transaction GP12N. Reports in Controlling also work with compatibility views. This means that they continue to display cost elements and cost element groups rather than accounts and account groups and have to aggregate the relevant fields in the universal journal on the fly. The classic reports also read planning data created using transactions KP06 (cost centers), KPF6 (internal orders) and CJR2 (projects).</p>\n<p dir=\"ltr\">To use the <strong>new</strong> reports, you will have to activate the relevant Fiori apps. There are Fiori apps for reporting on cost centers, internal orders, projects, and sales order items. The account structure for these reports can be based on the financial statement version or on the cost element groups. For cost centers, internal orders, and projects you can distinguish between real and statistical postings in reporting. A new field has been introduced to the universal journal that represents the old CO object number and works using a combination of object type = ACCASSTY (KS for cost center, OR for order, and so on) and account assignment = ACCASS (the cost center number, order number and so on). This provides the means to replace the reports formerly delivered under the Cost and Revenue Accounting InfoSystem. Nonetheless, you need to be aware that plan/actual reporting is based on SAP BPC for S/4 HANA Finance or on SAP Analytics Cloud for Planning and you will need to activate the appropriate planning applications. Planning functions exist that transfer data from the classic planning transactions to the new data structures.</p>\n<p dir=\"ltr\">The first Fiori app for reporting on <strong>target</strong> and actual costs on production orders is the Production Cost Analysis app from 1610 and can be used as an alternative to the classic target/cost analyses in ABAP list viewer. In 1809 this app was extended to provide access to the detailed costs by work center and operation and to calculate target costs on the fly for these entities. For the reporting of the variance categories on production orders (stored in table COSB) you should continue to use the classic reports or move to the event-based approach delivered in combination with Universal Parallel Accounting.</p>\n<p dir=\"ltr\">The first Fiori apps for reporting on <strong>commitments</strong> by project and cost center have been delivered in the cloud using the predictive accounting approach where commitments are stored in an extension ledger rather than the dedicated CO table (COOI). From 1809 you can switch to Fiori apps to display commitments for projects and cost centers and to report budget usage by project. The predictive accounting approach is also used to update the values from incoming sales orders to an extension ledger and to store statistical sales conditions by market segment.</p>\n<p dir=\"ltr\">For the cost centers there are not yet Fiori apps for reporting on target costs, variance categories, and intercompany eliminations. For the internal orders and projects, there are not yet Fiori apps for reporting on work in process or results analysis. For the sales orders, there are not yet Fiori apps for reporting on cost estimates or results analysis. For these reporting tasks, you will need to continue to use Report Writer/Painter or drill-down reports as appropriate. You can identify the need for such reports by checking whether entries for WIP and variances are shown in table COSB. Target costs and intercompany eliminations are identified by the value type WRTYP in table COEP. There are not yet Fiori apps to display statistical key figures on profit centers, cost centers, orders and WBS elements.</p>\n<p dir=\"ltr\">If you choose to use a mixture of new applications and classic tools, you should be aware that it is possible to retract planning data from SAP BPC to cost center planning, order planning and project planning and to fill the general ledger and profit center plan via plan integration in order to provide the appropriate plan/actual reports by cost element and that similar functions are available if you plan using SAP SAC.</p>\n<p dir=\"ltr\">There are Fiori apps for reporting on market segments based on the account-based approach. Plan/actual reporting here is also based on SAP BPC and you will need to activate the appropriate planning applications and BW content. Alternatively if you use SAP SAC for planning, plan/actual reports are built directly on the ACDOCP planning table. SAP S/4HANA includes new functions allowing you to derive the market segments at the time of postings to cost centers, orders, and projects. Such market segment information is only available in the new reporting apps.</p>\n<p dir=\"ltr\">Traditional drill-down reports, built using transaction KE30, and the line item report (KE24) will continue to work, but will only show revenues and costs where the account assignment is a CO-PA characteristic (ie values are assigned to object type EO). To see CO-PA characteristics that were derived from the account assignments in Controlling use transaction KE24N. Costing-based CO-PA continues to be supported.</p>\n<p dir=\"ltr\">The existing extractors to SAP BW will continue to work. You should also consider using the new datasource 0FI_ACDOCA_10 <span>to extract the complete universal journal to SAP BW, where BW runs as a dedicated data warehouse, rather than in embedded mode.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p dir=\"ltr\"><strong>Business Process related information</strong></p>\n<p dir=\"ltr\">Analyze existing roles to determine which reporting applications are used by which business users. Check against Fiori business catalogs to determine which reporting apps can be offered to which user groups.</p>\n<p dir=\"ltr\">Analyze existing roles in SAP BW to determine whether some operational reporting applications can be moved back into SAP S/4HANA.</p>\n<p dir=\"ltr\">Check existing planning process and determine whether it is possible to move some (or even all) planning activities to SAP BPC or SAP SAC.　</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td>\n<p>All transactions available, but are making use of compatibility views</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p dir=\"ltr\"><strong>Required and Recommended Action(s)</strong></p>\n<p dir=\"ltr\">Most existing reports in Report Writer/Painter, drill-down reporting and ABAP List Views (ALV) will continue to work using compatibility views which will provide access to all fields available in the original report but will <strong>not</strong> provide insight over and above what was already available in SAP ERP. The merge of the cost elements and the accounts mean that only the account is stored in the universal journal but that you can use compatibility views to convert the account view back to the cost element view.</p>\n<p dir=\"ltr\">The exception are those Report Writer/Painter reports in library 5A1 (Cost Element Accounting) which read from table COFIT. The reconciliation ledger is no longer filled after the conversion to SAP S/4HANA. Here you will have to use the new reports to show the costs and revenues by G/L account and account assignment. If you select the new fields ACCASS and ACCASSTY as drill-downs you can show all postings to cost center, order, and so on as in the classic cost element reports.</p>\n<p dir=\"ltr\">Please ensure that the transactions such as CK11N (create standard cost estimate), CO01-CO03 (create/change/display production order) and CR01-CR03 (create/change/display process order) are calling ABAP List Views rather than the old Report Writer reports. To do this, choose transaction OKN0, select the tab \"Report Selection\" and ensure that the flags \"Flexible itemization\", \"Flexible cost component report\" and \"Flexible cost display\" are active.</p>\n<p dir=\"ltr\">Activate appropriate Fiori reporting apps and test with end users. Check existing planning process and determine whether it is possible to move some (or even all) planning activities to SAP BPC or SAP SAC.　</p>", "noteVersion": 12, "refer_note": [{"note": "2579584", "noteTitle": "2579584 - Recommendations for Usage of Reports in Financial Reporting in S/4 HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With SAP S/4 HANA, SAP changed the data model in Finance to bring together data from several application components into one item table (the universal journal). This means that all report users are looking at different aggregations of the same dataset (single source of truth) and there is no need to pre-aggregate data or reconcile different data sources. The reporting applications are being rebuilt to take advantage of the new data model and extended to include, for example, additional currencies or additional fields and to provide a modern user experience (SAP Fiori).</p>\n<p>SAP delivers many reporting applications that are designed to cover the main reporting requirements and offers a variety to tools that allow customers to enhance the reports delivered by SAP or build their own reports based on the CDS views delivered by SAP.</p>\n<p>This note explains how to use the different reporting tools and describes the reporting apps delivered for CO and FI in SAP S/4HANA  (from  1610 OP).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>REPORTPAINTER, REPORTWRITER, CDS, Embedded Analytics, Plan-Actual-Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For the areas CO-OM, CO-PC, CO-PA, FI-GL, EC-PCA, and FI-AA SAP delivers:</p>\n<ul>\n<li>Fiori apps for financial reporting</li>\n<li>Fiori based key user tools that allow the creation of customer-specific CDS query based reports and allow to enhance SAP delivered reports. <br/>These CDS queries can also be used in a variety of analytical applications outside of S/4HANA like SAP Analysis for Microsoft Office or SAP Analytics Cloud.</li>\n</ul>\n<p>Alongside these new reporting capabilities, existing reporting tools are still supported to ensure that upgrade projects can make the transition over time and continue to take advantage of their earlier investments:</p>\n<ul>\n<li>Report Writer, Report Painter, Drill-Down Reporting</li>\n<li>SAPGUI Applications for line items</li>\n</ul>\n<p>The existing tools have been adapted and optimized to work with the new HANA based architecture, but cannot leverage the HANA architecture completely due to the constraints in the original tool architecture.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP recommends a gradual switch to the new CDS based reporting with the FIORI user interface.</p>\n<p>Please find here more details and links to further information on the following topics:</p>\n<ol>\n<li><a href=\"#Fiori_apps\" target=\"_self\">Fiori apps for reporting</a></li>\n<li><a href=\"#Key_user_tools\" target=\"_self\">Fiori based key user tools that allow the creation of own CDS based reports</a></li>\n<li><a href=\"#Advantages\" target=\"_self\">Advantages of CDS based reporting</a></li>\n<li><a href=\"#ReportWriter\" target=\"_self\">Report Writer, Report Painter, Drilldown Reporting and SAPGUI Applications for line items</a></li>\n</ol>\n<p><br/><strong><a name=\"Fiori_apps\" target=\"_blank\"></a></strong>﻿<strong>1. Fiori apps for reporting</strong></p>\n<p>You can find detailed information on the new reports in the Fiori Apps Library by searching for the roles General Ledger Accountant, Controller, Inventory Accountant, Asset Accountant, and so on: <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/home\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/home</a></p>\n<p>To gain an impression of the new options, use this video to show how to display and compare G/L account balances: <a href=\"https://help.sap.com/viewer/0fc4f335190447c2992391835eb508cd/3.6/en-US/0e2382534c07ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/0fc4f335190447c2992391835eb508cd/3.6/en-US/0e2382534c07ff4fe10000000a44176d.html</a></p>\n<p>Further examples of the new reporting apps are:</p>\n<ul>\n<li>“Display Financial Statement” <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0708')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0708')/S9OP</a></li>\n<li>“Trial Balance”  <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0955')/W11\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0955')/W11</a></li>\n<ul>\n<li>Video: SAP S/4HANA Analyze Financial Balance Sheet - Profit and Loss <a href=\"https://www.youtube.com/watch?v=YIfHsp2AUio&amp;feature=youtu.be\" target=\"_blank\">https://www.youtube.com/watch?v=YIfHsp2AUio&amp;feature=youtu.be</a></li>\n</ul>\n<li>“Display G/L Account Line Items”  <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F2217')/S18\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F2217')/S18</a></li>\n<li>“Display G/L Account Balances”  <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0707')/S7OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0707')/S7OP</a></li>\n<li>“Cost Centers - Plan/Actual” <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0949A')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0949A')/S9OP</a></li>\n<li>“Internal Orders - Plan/Actual YTD”   <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0929A')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0929A')/S9OP</a></li>\n<li>“Profit Centers - Plan/Actual with Currency Translation” <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0938A')/S9OP\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0938A')/S9OP</a></li>\n<li>Blog: SAP S/4HANA Cloud for Finance: Balance Reporting – Part 2: Standard reports and standard API for balance reporting <a href=\"https://blogs.sap.com/2019/02/19/sap-s4hana-cloud-for-finance-balance-reporting-part-2-standard-reports-and-standard-api-for-balance-reporting/\" target=\"_blank\">https://blogs.sap.com/2019/02/19/sap-s4hana-cloud-for-finance-balance-reporting-part-2-standard-reports-and-standard-api-for-balance-reporting/</a></li>\n</ul>\n<p><strong><a name=\"Key_user_tools\" target=\"_blank\"></a></strong>﻿<strong>2. Fiori based key user tools that allow the creation of own CDS based reports</strong></p>\n<p>The term “S/4HANA Embedded Analytics” is used to describe the new CDS based reporting capabilities within S/4HANA. This blog provides more information about the basic ideas: <a href=\"https://blogs.sap.com/2016/05/27/getting-started-with-s4-hana-embedded-analytics/\" target=\"_blank\">https://blogs.sap.com/2016/05/27/getting-started-with-s4-hana-embedded-analytics/</a></p>\n<p>The term “key user tools” is used to describe the toolsets that will ultimately replace Report Writer, Report Painter, Drill-Down, and so on. This blog provides an introduction to the key user tools. <a href=\"https://blogs.sap.com/tag/key-user-tools/\" target=\"_blank\">https://blogs.sap.com/tag/key-user-tools/</a></p>\n<p>If you want to create your own reports, use the app “Custom Analytical Query” to define a CDS query. In this blog we explain how to use this tool to create a cost center report with hierarchies for cost centers and cost elements (accounts) and information on plan values and actual values: <a href=\"https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/\" target=\"_blank\">https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/</a></p>\n<p>You can use the CDS query that you have defined using the “Custom Analytical Query” app in different user interfaces. For a simple grid based application, embed the query in the  “Design Studio” as described here: <a href=\"https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/\" target=\"_blank\">https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/</a></p>\n<p>Information how you can create your own balance report can be found here: <a href=\"https://blogs.sap.com/2019/03/15/sap-s4hana-cloud-for-finance-balance-reporting-part-3-custom-report-creation-for-balance-reporting/\" target=\"_blank\">https://blogs.sap.com/2019/03/15/sap-s4hana-cloud-for-finance-balance-reporting-part-3-custom-report-creation-for-balance-reporting/</a></p>\n<p>The same CDS query also can be used to create an “Analytical List Page” (ALP), a graphic in an “Overview Page” or a “Smart Business” analytical application. Info on ALP is provided in this blog: <a href=\"https://blogs.sap.com/2016/11/03/sap-fiori-2.0-a-primer-on-embedded-analytics/\" target=\"_blank\">https://blogs.sap.com/2016/11/03/sap-fiori-2.0-a-primer-on-embedded-analytics/</a>.</p>\n<p>Alternatively, if you want to use this CDS query in an Excel based user interface, you can also use “SAP Analysis for Microsoft Office” to display this query.</p>\n<p>Configuration guides that describes the necessary steps to define own Fiori based analytical applications can be found in the “SAP Best Practices for analytics with SAP S/4HANA”: <a href=\"https://rapid.sap.com/bp/#/BP_S4H_ANA\" target=\"_blank\">https://rapid.sap.com/bp/#/BP_S4H_ANA</a><br/>Detailed steps for the key user tools are described in this file: <a href=\"https://support.sap.com/content/dam/SAAP/Sol_Pack/Library/Configuration/2PJ_S4CLD1711_BB_ConfigGuide_EN_XX.docx\" target=\"_blank\">https://support.sap.com/content/dam/SAAP/Sol_Pack/Library/Configuration/2PJ_S4CLD1711_BB_ConfigGuide_EN_XX.docx</a></p>\n<p><strong><a name=\"Advantages\" target=\"_blank\"></a>﻿﻿3. Advantages of CDS based reporting</strong></p>\n<p><span>More detail in the reports:</span><br/>The universal journal in SAP S/4HANA contains all reporting dimensions (account, profit center, company code, cost center, and so on). The line items cannot be consumed directly in a report but must be accessed via CDS views that allow you to link the transactional data with the related master data information, including texts (available in multiple languages), attributes (e.g. the name of the cost center manager) and hierarchies. SAP delivers CDS views for the transactional data and the master data that you can directly use to create your own CDS queries.</p>\n<p><span>Customer enhancements in master data automatically available in reporting:</span><br/>CDS based reporting supports the usage of custom fields in SAP delivered or customer defined reports. Here you can find an example for the master data extensibility: <a href=\"https://blogs.sap.com/2017/07/12/new-video-extending-master-data-for-fixed-assets-in-sap-s4hana-cloud/\" target=\"_blank\">https://blogs.sap.com/2017/07/12/new-video-extending-master-data-for-fixed-assets-in-sap-s4hana-cloud/</a></p>\n<p><span>Definition of CDS queries allows usage in multiple analytic application:</span><br/>Once you have defined a CDS query, you might choose from a variety of user interfaces according to the use case and the expectations of the user. The same CDS query can be used for detailed drill down analysis with a multi-dimensional grid and to define a traffic light or a graphic in the “Smart Business” framework. Other usages are “Analytical List Page” (ALP), SAP Analysis for Office or even SAP Analytics Cloud.</p>\n<p><strong><a name=\"ReportWriter\" target=\"_blank\"></a>﻿4. Report Writer, Report Painter and SAPGUI Applications for line items</strong></p>\n<p>Report Writer, Report Painter, Drill-Down Reporting, and the SAPGUI Applications for line items are still available in the S/4HANA System. These reports are still supported and have been technically adapted and optimized for the new architecture.</p>\n<p>For technical reasons, these classic reporting tools can only partial benefit from the new HANA capabilities. <br/>- The advantages of more detailed reporting with the usage of line item information is not available for Report Painter and Report Writer, since the report definition continues to be based on the old data structures.<br/>- Report Painter and Report Writer Reports cannot benefit from the new extensibility concept.<br/>- CDS based queries uses an architecture that is designed specifically to work with HANA. Therefore CDS based reporting might in many cases offer superior performance compared to Report Painter or Report Writer.</p>\n<p>Therefore going forward we recommend to create new reports using the CDS based reporting tools instead of using Report Painter or Report Writer.</p>", "noteVersion": 6, "refer_note": [{"note": "2623507", "noteTitle": "2623507 - Fiori Multidimensional Reporting in S/4 HANA onPremise using custom analytical queries", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP delivers in S/4 HANA various Fiori Multidimensional Reports which allow analyzing the data in a flexible way (e.g. by easily adding dimensions and measures to the rows or columns).</p>\n<p>Customers and Partners can also create Fiori Multidimensional Reports based on an Analytcial Query and visualize them in the Fiori Launchpad. SAP offers for this puppose in S/4 HANA on Premise 2 generic player applications which require mainly the Analytical Query as parameter: a Web Dynpro based reporting application and a SAPUI5 based reporting application (this reporting tool is obsolete since S/4 HANA on Premise 2021). The following describes how to configure these 2 reporting applications in Fiori Launchpad (FLP). (Please note: The Analytical Query can be either a ABAP CDS View - exposed as anyltical query - or a BW Query.)</p>\n<p> </p>\n<p><strong>1. Web Dynpro based reporting application for S/4 HANA (\"Web Dynpro Data Grid\")</strong></p>\n<p>(To be used as default reporting tool in S/4 HANA)</p>\n<p><strong>a) FLP Target Mapping</strong></p>\n<p>Semantic Object: &lt;choose a valid Semantic Object&gt;</p>\n<p>Action: &lt;arbitary action&gt;</p>\n<p>Application Type: Web Dynpro</p>\n<p>Title: &lt;arbitary title&gt;</p>\n<p>Application: FPM_BICS_OVP</p>\n<p>Configutation: FPM_BICS_OVP</p>\n<p>System Alias: &lt;RFC Connection to the backend, e.g. S4FIN; can be maintained via IMG (<em>Manage RFC Destinations</em>) or via the transaction SM59&gt;</p>\n<p>Device Types: Desktop</p>\n<p>Parameters:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Name</strong></td>\n<td><strong>Mandatory</strong></td>\n<td><strong>Value</strong></td>\n<td><strong>Default Value</strong></td>\n</tr>\n<tr>\n<td>bsa_query</td>\n<td>false</td>\n<td> </td>\n<td>&lt;ID of the custom analytical query&gt; (Please note: if an ABAP CDS View is exposed as anayltical query then you should use for the ID of the query the syntax 2C&lt;SQL View Name of the CDS View&gt;; e.g. the ID of the query is 2CCFITRIALBALQ0001 if the SQL View Name is CFITRIALBALQ0001)</td>\n</tr>\n<tr>\n<td>sap-ui-tech-hint</td>\n<td>true</td>\n<td>WDA</td>\n<td> </td>\n</tr>\n<tr>\n<td>sap-ushell-next-navmode</td>\n<td>false</td>\n<td> </td>\n<td>explace</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>b) FLP Tile Configuration</strong></p>\n<p>Title: &lt;use the same Title as defined in the Target Mapping&gt;</p>\n<p>Information: &lt;Accessible&gt; (recommended setting)</p>\n<p>Semantic Object: &lt;use the same Semantic Object as defined in the Target Mapping&gt;</p>\n<p>Action: &lt;use the same Action as defined in the Target Mapping&gt;</p>\n<p>Parameters: sap-ui-tech-hint=WDA</p>\n<p> </p>\n<p><strong>2. SAPUI5 based generic reporting application for S/4 HANA (\"Design Studio Data Grid\") - available since SAPUI5 1.48 (S/4 HANA on Premise 1709)</strong></p>\n<p>(This reporting tool is obsolete since S/4 HANA on Premise 2021)</p>\n<p><strong>a) FLP Target Mapping</strong></p>\n<p>Semantic Object: &lt;choose a valid Semantic Object&gt;</p>\n<p>Action: &lt;arbitary action&gt;</p>\n<p>Application Type: SAPUI5 Fiori App</p>\n<p>Title: &lt;arbitary title&gt;</p>\n<p>URL: /sap/bc/ui5_ui5/sap/FIN_DS_ANALYZE</p>\n<p>ID: fin.acc.query.analyze</p>\n<p>Device Types: Desktop</p>\n<p>Parameters:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Name</strong></td>\n<td><strong>Default Value</strong></td>\n</tr>\n<tr>\n<td>XQUERY</td>\n<td>&lt;ID of the custom analytical query&gt; (Please note: if an ABAP CDS View is exposed as anayltical query then you should use for the ID of the query the syntax 2C&lt;SQL View Name of the CDS View&gt;; e.g. the ID of the query is 2CCFITRIALBALQ0001 if the SQL View Name is CFITRIALBALQ0001)</td>\n</tr>\n<tr>\n<td>XSEMANTIC_OBJECTS</td>\n<td>*</td>\n</tr>\n<tr>\n<td>XSYSTEM</td>\n<td>&lt;SAP System Alias (to the backend system), e.g. S4FIN; can be maintained via IMG (<em>Manage SAP System Aliases</em>) or via the transaction SM30 (View /IWFND/V_DFSYAL)&gt;</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>b) FLP Tile Configuration</strong></p>\n<p>Title: &lt;use the same Title as defined in the Target Mapping&gt;</p>\n<p>Semantic Object: &lt;use the same Semantic Object as defined in the Target Mapping&gt;</p>\n<p>Action: &lt;use the same Action as defined in the Target Mapping&gt;</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>(No correction required.)</p>", "noteVersion": 4}, {"note": "1976487", "noteTitle": "1976487 - Information about adjusting customer-specific programs to the simplified data model in SAP Simple Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have installed SAP Simple Finance, on-premise edition or plan to do so. You require further information about how you can adjust your customer-specific programs or modifications to the new simplified data model in SAP Simple Finance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Migration, 1402, 1503</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have developed customer-specific accounting developments (in particular in the application components AC, FI, CO, FIN) that were developed for SAP ERP 6.0.</p>\n<p>SAP Simple Finance offers a simplified data model. In addition to the usual SPAU/SPDD activities, this might require special adjustments to customer-specific developments or modifications.</p>\n<p><strong>Task</strong>: Check whether the customer uses the affected tables or views in their own source code.</p>\n<p><strong>Procedure</strong>: See chapter 1.5</p>\n<p><strong>Rating</strong>:</p>\n<p>At least one of the tables or views is used in the customer-specific source code -&gt; YELLOW</p>\n<p>None of the tables or views is used in the customer-specific source code -&gt; GREEN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>1. Adjustment of customer-specific development objects </strong></p>\n<p><strong>1.1. Write access to totals tables and index tables in FI and CO</strong></p>\n<p>In SAP Simple Finance, the following tables have been replaced by views of the same name (Some views contain additional fields compared to the replaced tables, but no fields are missing.):</p>\n<p>COSP<br/>COSS<br/>BSIS<br/>BSAS<br/>FAGLBSIS<br/>FAGLBSAS<br/>BSIK<br/>BSAK<br/>BSID<br/>BSAD<br/>FAGLFLEXT<br/>LFC1<br/>LFC3<br/>KNC1<br/>KNC3<br/>GLT0</p>\n<p>These views of the same name select data from the line item tables and convert this into the format of the replaced tables so that all read accesses still work.</p>\n<p>However, no write accesses may take place - the views are read-only. Remove all write accesses to these objects from your source code (operations INSERT, UPDATE, DELETE, and MODIFY).</p>\n<p><strong><strong>1.2 Read accesses to totals tables and index tables in FI and CO</strong></strong></p>\n<p>See the following SAP Notes:</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><span class=\"urTxtStd urVt1\">2219527</span></td>\n<td> </td>\n<td>Notes about using views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS in customer-defined programs in SAP S/4HANA Finance<a class=\"urLnk urVt1\" href=\"/notes/2219527\" id=\"CORR_GR_C_wu_2219527\" target=\"_blank\"></a></td>\n</tr>\n<tr>\n<td><span class=\"urTxtStd urVt1\">2221298</span></td>\n<td> </td>\n<td>Notes about using views GLT0, FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, and JVGLFLEXT in custom programs in SAP S/4HANA Finance<a class=\"urLnk urVt1\" href=\"/notes/2221298\" id=\"CORR_GR_C_wu_2221298\" target=\"_blank\"></a></td>\n</tr>\n<tr>\n<td><span class=\"urTxtStd urVt1\">2185026</span></td>\n<td> </td>\n<td>Compatibility views COSP, COSS, COEP, and COVP. How can their use be optimized?<a class=\"urLnk urVt1\" href=\"/notes/2105948\" id=\"CORR_GR_C_wu_2105948\" target=\"_blank\"></a></td>\n</tr>\n</tbody>\n</table></div>\n<p>SELECTs from views of the same name with the addition ORDER BY PRIMARY KEY result in a syntax error in Release S/4H Finance 1503. These must be replaced by an explicit field list, for example, ORDER BY BUKRS HKONT AUGDT AUGBL ZUONR GJAHR BELNR BUZEI. In later releases, you can implement SAP Note 2489438 so that ORDER BY PRIMARY KEYs no longer result in a syntax error. For the view FAGLFLEXT, however, an explicit field list must still be used. See also SAP Note 2629757.</p>\n<p><strong>1.3 Views for totals tables and index tables</strong></p>\n<p>If, in the ABAP Dictionary, you have defined database views for one of the tables that has been eliminated in SAP Simple Finance, you must remove these views. The ABAP Dictionary does not support database views based on other views.</p>\n<p>Example:</p>\n<p>You have created a database view called Z_BKPF_BSIS with the base tables BKPF and BSIS. In SAP Simple Finance, BSIS is a view. As a result, Z_BKPF_BSIS would be based on the BSIS view. This is not supported.</p>\n<p>Proceed as follows to find affected views in your namespace.</p>\n<ol>\n<li>Use the where-used list in transaction SE11. One after the other, enter the affected objects (COSP, COSS, BSIS, and so on) into the table field.</li>\n<li>Call the where-used list (Ctrl+Shift+F3) and set the \"Views\" indicator only.</li>\n<li>Execute the where-used list.</li>\n</ol>\n<p>You must remove these database views from the ABAP Dictionary and replace the selection from the views by an Open SQL SELECT or a read module call.</p>\n<p><strong>1.4 Aging</strong></p>\n<p>If you actively use SAP data aging for the FI document, you have to make further adjustments. See SAP Note 2076652.</p>\n<p><strong>1.5 Finding the affected parts of the code</strong></p>\n<p>You can use the Code Inspector to find all affected parts of the code. Proceed as follows:</p>\n<ol>\n<li>Call transaction SCI and define a new variant. In the definition of the variant, set the indicator for searching for DB operations in \"Search Functs.\". Enter the list of affected objects (COSP, COSS, BSIS etc.).</li>\n<li>Create an object set that contains all of your programs, classes, function groups, and so on.</li>\n<li>Create a new inspection and execute it. The result is a list of all database accesses to the totals tables and index tables removed in Simple Finance.</li>\n</ol>\n<p>Remember that generic database accesses that cannot be found using standard methods are possible too.</p>\n<p>Example: UPDATE (lv_tabname) FROM lt_index.</p>\n<p><strong>1.6 SAP Simple Finance, on-premise edition 1503 </strong></p>\n<p>Additional compatibility views were implemented in SAP Simple Finance, on-premise edition 1503. The technology is different than in the previous release. The views do not have the same name as the original table. Instead, in the database interface (DBI), all SELECTs are redirected to compatibility views that receive data in the same structure from the new data model (mainly from the new table ACDOCA). The process of redirecting SELECTs is also called REDIRECT.</p>\n<p>Table -&gt; View</p>\n<p>ANEA -&gt; FAAV_ANEA<br/>ANEK -&gt; FAAV_ANEK<br/>ANEP -&gt; FAAV_ANEP<br/>ANLC -&gt; FAAV_ANLC<br/>ANLP -&gt; FAAV_ANLP<br/>BSIM -&gt; V_BSIM<br/>CKMI1 -&gt; V_CKMI1<br/>COEP -&gt; V_COEP<br/>FAGLFLEXA -&gt; FGLV_FAGLFLEXA<br/>MLCD -&gt; V_MLCD<br/>MLCR -&gt; V_MLCR<br/>MLHD -&gt; V_MLHD<br/>MLIT -&gt; V_MLIT<br/>MLPP -&gt; V_MLPP<br/>T012K -&gt; V_T012K_BAM<br/>T012T -&gt; V_T012T_BAM<br/>FMGLFLEXA -&gt; FGLV_FMGLFLEXA<br/>FMGLFLEXT -&gt; FGLV_FMGLFLEXT<br/>PSGLFLEXA -&gt; FGLV_PSGLFLEXA<br/>PSGLFLEXT -&gt; FGLV_PSGLFLEXT<br/>JVGLFLEXA -&gt; FGLV_JVGLFLEXA<br/>JVGLFLEXT -&gt; FGLV_JVGLFLEXT<br/>ZZ&lt;CUST&gt;A -&gt; ZFGLV_GLSI_C&lt;number&gt;<br/>ZZ&lt;CUST&gt;T -&gt; ZFGLV_GLTT_C&lt;number&gt;</p>\n<p><em>The ZZ* tables are General Ledger Accounting (new) line item tables and totals tables in the customer namespace. They can be identified with a SELECT on the table T800A with the restriction TTYPE IN ( 'TT', 'SI' ) AND GLFLEX = 'X' AND INACTIVE = ''.</em></p>\n<p>The views select data from the table ACDOCA and other tables and convert it into the format of the original tables so that all read accesses continue to work.</p>\n<p>The write accesses (INSERT, UPDATE, DELETE, and MODIFY operations) must be converted as described in the following sections.</p>\n<p><em><strong>1.6.1 General ledger</strong></em></p>\n<p>Write accesses to the line item tables FAGLFLEXA, FMGLFLEXA, PSGLFLEXA, JVGLFLEXA, and ZZ&lt;CUST&gt;A must be converted to the table ACDOCA. Most of the fields in the table ACDOCA match the fields in the General Ledger Accounting (new) line item tables. However, there are a few exceptions:</p>\n<ul>\n<li>ACTIV is BTTYPE in ACDOCA.</li>\n<li>RVERS is obsolete.</li>\n<li>DOCNR is obsolete.</li>\n<li>COST_ELEM is obsolete. There is only one field for the account: RACCT.</li>\n<li>LOGSYS is AWSYS in ACDOCA. </li>\n<li>Optional fields (for example, ZZHOART) have a different name in the table ACDOCA. The mapping is implemented in the method GET_ACTIVE_OPT_FIELDS of the class CL_FINS_FI_UTILITY.</li>\n</ul>\n<p>We advise against converting write accesses directly to the table ACDOCA. Instead, use the new ABAP class CL_FINS_ACDOC_CHANGE for all write accesses to ACDOCA.</p>\n<p>Write accesses to the totals tables FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, JVGLFLEXT, and ZZ&lt;CUST&gt;T must be removed without being replaced.</p>\n<p>Assignments using the group names PERIOD_DATA, TSL, HSL, KSL, OSL, MSL, and FIX must be reprogrammed.<br/>Example: The following code results in SY-SUBRC 4 in SAP Simple Finance.<br/>FIELD-SYMBOLS &lt;ls&gt; TYPE faglflext.<br/>ASSIGN COMPONENT 'HSL' OF STRUCTURE &lt;ls&gt; TO FIELD-SYMBOL(&lt;ls_hsl&gt;).</p>\n<p>In SAP Simple Finance, compatibility views for the tables FAGLFLEXA/T, FMGLFLEXA/T, PSGLFLEXA/T, JVGLFLEXA/T, and ZZ&lt;CUST&gt;A/T return data for all general ledgers. Make sure that all read accesses restrict the field RLDNR. <br/>Example:<br/>Before the upgrade to SAP Simple Finance, you had two ledgers, 0L and 1N, persisted in separate tables (0L in FAGLFLEXA/T and 1N in ZZFLEXA/T). Following an upgrade, a SELECT from FAGLFLEXA/T returns data for 0L <span>and</span> 1N. The same is true for ZZFLEXA/T.</p>\n<p>For certain posting documents, there is no entry view in transactions FB03 and FB03L. For these documents, there are only entries in BKPF/ACDOCA but not in BSEG/BSEG_ADD (see SAP Note</p>\n<p>2297729).</p>\n<p>1. Intracompany CO processes</p>\n<p>2. Foreign currency valuation (FAGL_FCV)</p>\n<p>3. GL allocations</p>\n<p>4. SAP Fiori app for foreign currency valuation</p>\n<p><strong>1.6.2 COEP</strong></p>\n<p>Use the new ABAP class CL_FCO_COEP_UPDATE for all write accesses to COEP outside the software component SAP_FIN. This ensures that actual postings in COEP are no longer updated. Important: This class does not transfer the alternative posting to the table ACDOCA.</p>\n<p>You must therefore remove write accesses to the line item tables COEP if you previously changed document line items of the value type 04 (\"Actual postings\"): Actual postings of the value type 04 are no longer supported in the table COEP. (In other words, the compatibility views COEP and COVP no longer read actual postings from COEP. Instead, they are calculated from ACDOCA.) Write accesses for value types other than \"04\" are still possible.</p>\n<p>We strongly advise against converting write accesses directly to the table ACDOCA because most of the field names in COEP differ from those in ACDOCA and, to some extent, cannot be mapped on a 1:1 basis. In addition, the new posting logic in ACDOCA differs significantly from the posting logic in COEP. Therefore, a customer-specific program cannot simply be converted to the table ACDOCA.</p>\n<p><strong>1.6.3 Asset Accounting</strong></p>\n<p>You must remove write accesses to the tables ANEA, ANEP, ANEK, ANLC, ANLP without replacing them.</p>\n<p><strong>1.6.4 Material ledger</strong></p>\n<p>You must remove write accesses to the ML document tables (MLHD, MLIT, MLPP, MLCR, MLCD) if you previously changed documents of the transaction type \"UP\" (\"Update\"). Write accesses for other transaction types are still possible.</p>\n<p>The compatibility views from ACDOCA are used to calculate update documents. We strongly advise against converting write accesses directly to the table ACDOCA because most of the field names in the ML document tables differ from those in ACDOCA and, to some extent, cannot be mapped on a 1:1 basis. Therefore, a customer-specific program cannot simply be converted to the table ACDOCA.</p>\n<p>Write accesses to the table CKMI1 must be removed. Data is calculated from ACDOCA using a compatibility view. We strongly advise against switching write accesses directly to the table ACDOCA since most of the field names of the index table differ from those of ACDOCA and sometimes cannot be mapped on a 1:1 basis. Therefore, a customer-specific program cannot simply be converted to the table ACDOCA.</p>\n<p><strong>1.6.5 T012K/T012T</strong></p>\n<p>Write accesses to the tables T012T and T012K must be converted to the tables FCLM_BAM_AMD, FCLM_BAM_AMD_CUR, FCLM_BAM_AMT_T, and FCLM_BAM_ACLINK2.<br/>We recommend that you perform write accesses via the BOR object FCLM_CR only. Otherwise, the approval processes configured in bank account management are not taken into account.</p>\n<p>The manner in which the IBAN of the house bank account is stored has been changed. It is no longer in the table TIBAN, but in the IBAN field of the table FCLM_BAM_AMD. As a result, accesses using the function modules</p>\n<p>READ_IBAN_EXT<br/>READ_IBAN_FROM_DB<br/>READ_IBAN<br/>READ_IBAN_INT<br/>SEARCH_FOR_IBAN</p>\n<p>no longer work. To read the IBAN of a house bank account, you must replace these modules as follows (old name with the attached _HBA):</p>\n<p>READ_IBAN_EXT_HBA<br/>READ_IBAN_FROM_DB_HBA<br/>READ_IBAN_HBA<br/>READ_IBAN_INT_HBA<br/>SEARCH_FOR_IBAN_HBA</p>\n<p><strong>1.6.6 Views on redirected tables</strong></p>\n<p>If, in the ABAP Dictionary, you have defined database views for one of the tables listed in Section 1.6, you must remove these views. Otherwise, you will access data in decommissioned tables.</p>\n<p>You must remove these database views from the ABAP Dictionary and replace the selection from the views by an Open SQL SELECT or a read module call. Alternatively, you can create new CDS views.</p>\n<p>The attached file lists all views that have been replaced in the standard system with equivalent ABAP source code or a new CDS view. REDIRECT technology is used to redirect the SELECTs to the new CDS views. The REDIRECT procedure cannot be used for customer-defined views.</p>\n<p>Example: The compatibility view V_COVP was created for the view COVP. In the case of SELECT FROM COVP, the SELECTs are redirected to V_COVP.</p>\n<p><strong>1.7 S/4HANA readiness checks</strong></p>\n<p>Hits from the S/4HANA readiness check \"S/4HANA: Search for ABAP Dictionary enhancements\" that reference this SAP Note can be ignored and can be suppressed by implementing correction note 2522926.</p>", "noteVersion": 21}, {"note": "2535903", "noteTitle": "2535903 - How to create your custom CDS Queries and leverage existing CDS Reporting Queries in S/4HANA Cloud and On Premise 1709 and subsequent OP releases", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to create your own Custom CDS queries to fullfil customer requierements or want to leverage existing CDS queries as a tile on the Fiori launchpad. This note is relevant for S/4HANA Cloud and S/4HANA OnPremise 1709 and subsequent OP releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Core Data Services</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Information</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span>One simple option to leverage an existing CDS query for reporting is to create a new tile in the Fiori Launchpad Designer following note ##2623507. The list of existing CDS queries can be found below.</span></p>\n<p><span>A different option is to create your own custom CDS query that can be accessed via a tile on the Fiori launchpad, follow the guide in the attachment or link </span><a href=\"https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/\" target=\"_blank\">https://blogs.sap.com/2017/05/31/custom-analytical-query-how-to-create-a-custom-analytical-query-and-display-in-a-grid-based-application/</a><span> Instead of using a custom CDS view as described in the guide, you use the following CDS Cubes as Data Views:</span><br/><span>Actuals: I_JournalEntryItemCube</span><br/><span>Plan/Actuals: I_ActualPlanJrnlEntryItemCube</span><br/><span>If you want to leverage an existing CDS Queries, you can mark an existing CDS Query and click \"Copy\" instead of \"Create\" in the Query Designer (--&gt; tile \"Custom Analytical Queries\") as described in step 2 of the guide linked above.</span></p>\n<p>The following CDS Queries are available:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Report Title</strong></td>\n<td><strong>Subtitle</strong></td>\n<td><strong>CDS Query</strong></td>\n</tr>\n<tr>\n<td>Cost Centers</td>\n<td>Actuals</td>\n<td>C_CostCenterQ2001</td>\n</tr>\n<tr>\n<td>Cost Centers</td>\n<td>Plan/Actual</td>\n<td>C_CostCenterPlanActQ2001</td>\n</tr>\n<tr>\n<td>Projects</td>\n<td>Actuals</td>\n<td>C_ProjectQ2201</td>\n</tr>\n<tr>\n<td>Projects</td>\n<td>Plan/Actual</td>\n<td>C_ProjectPlanActQ2201</td>\n</tr>\n<tr>\n<td>Projects</td>\n<td>Baseline/EAC/Ongoing</td>\n<td>C_ProjectPlanActQ2203</td>\n</tr>\n<tr>\n<td>Internal Orders</td>\n<td>Plan/Actual</td>\n<td>C_InternalOrderPlanActQ2101</td>\n</tr>\n<tr>\n<td>Market Segments</td>\n<td>Actuals</td>\n<td>C_MarketSegmentQ2501</td>\n</tr>\n<tr>\n<td>Market Segments</td>\n<td>Plan/Actual</td>\n<td>C_MarketSegmentPlanActQ2501</td>\n</tr>\n<tr>\n<td>Profit Centers</td>\n<td>Actuals</td>\n<td>C_ProfitCenterQ2701</td>\n</tr>\n<tr>\n<td>Profit Centers</td>\n<td>Plan/Actual</td>\n<td>C_ProfitCenterPlanActQ2701</td>\n</tr>\n<tr>\n<td>P&amp;L</td>\n<td>Actuals</td>\n<td>C_ProfitAndLossQ2901</td>\n</tr>\n<tr>\n<td>P&amp;L</td>\n<td>Plan/Actual</td>\n<td>C_ProfitAndLossPlanActQ2903</td>\n</tr>\n<tr>\n<td>Functional Areas</td>\n<td>Actual</td>\n<td>C_FunctionalAreaQ2801</td>\n</tr>\n<tr>\n<td>Functional Areas</td>\n<td>Plan/Actual</td>\n<td>C_FunctionalAreaPlanActQ2801</td>\n</tr>\n<tr>\n<td>Sales Orders</td>\n<td>Actuals</td>\n<td>C_SalesOrderQ2301</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that for P&amp;L Plan/Actual (CDS query C_ProfitAndLossPlanActQ2903) SAP delivers a Fiori app tile starting with OP1909.</p>", "noteVersion": 6}]}, {"note": "1972819", "noteTitle": "1972819 - Setup SAP BPC optimized for S/4 HANA Finance and Embedded BW Reporting (aka Integrated Business Planning for Finance)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are trying to set up SAP BPC for S/4 HANA Finance and Embedded BW Reporting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Financials add-on for SAP Business Suite powered by SAP HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Reduction of setup time.</p>\n<p>Minimized risk of choosing inappropriate parameters.</p>\n<p>Note 2351974 is implemented.</p>\n<p>SAP Note 2025364 is implemented (in Release SAP HANA 1.0, not relevant from NW750).</p>\n<p>SAP Note 1959332 or SP7 of SAP_BW 7.40 is implemented (not relevant from NW750).</p>\n<p>SAP Note 2136950 or SP11 of SAP_BW 7.40 is implemented (not relevant from NW750).</p>\n<p>SAP Note 2162108 or SP12 of SAP_BW 7.40 is implemented (not relevant from NW750).</p>\n<p>SAP Note 2207378 or SP1 of SAP_BW 7.50 is implemented (relevant only for S4Hana OP 1511 SP0).</p>\n<p>SAP Note 2213891 or SP1 of SAP_BW 7.50 is implemented (relevant only for S4Hana OP 1511 SP0).</p>\n<p>SAP Note 2290303 or SP4 of SAP_BW 7.50 is implemented (relevant only for S4Hana OP 1511 SP0).</p>\n<p>SAP Note 1637199 is taken into consideration (relevant only for planning, starting from SAP_BW 7.30, SP5).</p>\n<p>SAP Note 1919631 is taken into consideration (relevant only for planning, starting from SAP_BW 7.40, SP5).</p>\n<p>The role SAP_BW_SETUP has been generated.</p>\n<p>Your user has been assigned to the role SAP_BW_SETUP.</p>\n<p>SAP Note 2074801 has been considered.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Follow the manual correction instructions.</p>", "noteVersion": 54, "refer_note": [{"note": "2162108", "noteTitle": "2162108 - BI Content activation of data slice encounters SAPSQL_ARRAY_INSERT_DUPREC", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The activation of a data slice from BI Content is terminated with the following error messages:</p>\n<ul>\n<li>\"The system tried to insert a data record, even though a data record with the same primary key already exists\" (message number: RS_EXCEPTION000)</li>\n<li>\"An exception with the type CX_SY_OPEN_SQL_DB occurred, but was neither handled locally, nor declared in a RAISING clause.\" (message number: RS_EXCEPTION000)</li>\n<li>\"CL_RSPLS_PLDS &gt;&gt; Row:  Inc: 84 Prog: _SAVE_TO_DB\" (message number: RS_EXCEPTION301)</li>\n<li>Exception \"An exception with the type CX_SY_OPEN_SQL_DB occur' in progrmm 'CL_RSPLS_PLDS=================CP,CL_RSPLS_PLDS====' when passing type 'Planning: Data Slices' PLDS\" (message number: RSO 323)</li>\n</ul>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSA1, PLDS, data slice, SAPSQL_ARRAY_INSERT_DUPREC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There has been a program error since you implemented SAP Note 2067477.</p>\n<p>During the activation of a data slice, the following happens: First, in the method _DELETE_SUBOBJECTS_FROM_DB, all entries of the table RSPLS_DST for the InfoProvider with active object version are deleted, so that new entries with active object version can be added in the method _SAVE_TO_DB. Due to the corrections from SAP Note 2067477, entries with inactive object version for the INSERT are also read into the database during the BI Content activation. The INSERT fails because these entries are already contained in the database table and were not deleted previously in the method _DELETE_SUBOBJECTS_FROM_DB.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li>SAP BW 7.30<br/><br/>Import Support Package 14 for SAP BW 7.30 (SAPKW73014) into your BW system. The Support Package is available once <strong>SAP Note 2132939</strong> (\"SAPBWNews BW 7.30 ABAP SP14\"), which describes this SP in more detail, has been released for customers.</li>\n<li>SAP BW 7.31 (SAP NW BW 7.3 EHP 1)<br/><br/>Import Support Package 17 for SAP BW 7.31 (SAPKW73117) into your BW system. The Support Package is available once <strong>SAP Note 2139356</strong> (\"SAPBWNews BW 7.31/7.03 ABAP SP17\"), which describes this SP in more detail, has been released for customers.</li>\n<li>SAP BW 7.40<br/><br/>Import Support Package 12 for SAP BW 7.40 (SAPKW74012) into your BW system. The Support Package is available once <strong>SAP Note</strong><strong><strong> 2143705</strong></strong> (\"SAPBWNews BW 7.4 ABAP SP 12\"), which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<p>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><strong>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</strong><br/><br/>To provide information in advance, the SAP Note(s) specified above may already be available before the Support Package is released. In this case, the short text of the SAP Note will still contain the words \"Preliminary version\".</p>", "noteVersion": 1}, {"note": "2136950", "noteTitle": "2136950 - BW-IP: Content activation of a planning application causes CL_RSPLS_ALVL CX_RSD_INFOPROV_NOT_FOUND", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the activation of content that contains a planning application, a termination occurs with the exception CX_RSD_INFOPROV_NOT_FOUND in CL_RSPLS_ALVL.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Integrated planning content activation BSANLY_BI_ACTIVATION  /ERP/SFIN_PLANNING  /ERP/FCOM_PLANNING</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li>SAP BW 7.40<br/><br/>Import Support Package 11 for SAP BW 7.40 (SAPKW74011) into your BW system. The Support Package is available once <strong>SAP Note </strong><strong><strong>2100895 </strong></strong>\"SAPBWNews NW BW 7.4 ABAP SP11\", which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<p>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><strong>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</strong><br/><br/>To provide information in advance, the SAP Note(s) specified above may already be available before the Support Package is released. In this case, the short text of the SAP Note will still contain the words \"Preliminary version\".</p>", "noteVersion": 1}, {"note": "1637199", "noteTitle": "1637199 - Using the planning applications KIT (PAK)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Starting with SAP BW 7.30 SP5 we have introduced the Planning Applications KIT <strong>(PAK)</strong> which allows execution of BW-IP standard functions in HANA. The following needs to be considered when using the ABAP Planning Applications KIT:</p>\n<ul>\n<li>The use of the Planning Applications KIT requires the following license: 'SAP BusinessObjects Planning and Consolidation, version for SAP NetWeaver'. If you do not have acquired this license, please contact your account executive for further information.</li>\n</ul>\n<ul>\n<li>Some standard BW-IP functions are not accelerated with the ABAP Planning Applications KIT or lead to errors when executed in HANA.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Hana optimized Planning, planning applications KIT, disable, deactivate, unable, PAK, HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have acquired the license mentioned above and want to leverage BW-IP standard functions in HANA. Certain features available today in BW-IP (and PAK) are only supported in ABAP and might lead to additional data exchanges between the ABAP layer and the HANA database which can have a bad impact on the system performance. Also some ABAP specific features are not supported in the HANA optimized execution (see below for a list of all these features). Some remodeling might be required to leverage full HANA optimization when starting from an existing scenario.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Following switches exist to de-/activate ABAP Planning Applications KIT:</p>\n<ul>\n<li>Main switch to generally activate the Planning Applications KIT (license required): table view RSPLS_HDB_ACT in SM30. If not yet existing, create a new entry for \"Deep HANA Integration Active\" (HANA_ACT) and mark the checkbox 'Function Active' in order to activate the ABAP Planning Applications KIT.</li>\n</ul>\n<ul>\n<li>In rare cases some scenarios might cause problems when executed in HANA. Thus the table view RSPLS_HDB_ACT_IP in SM30 can be used to deactivate the ABAP Planning Applications KIT for individual InfoProviders. Choose the real-time InfoCube, direct update DSO or aDSO you do not want to use for HANA planning. This feature is available in order to guarantee a smooth transition from existing BW-IP scenarios and to avoid additional roundtrips due to the restrictions mentioned below. <br/><br/>Every real-time InfoCube must be HANA optimized to run in HANA. In case of a MultiProvider or CompositeProvider all planning enabled part providers must be HANA enabled (not de -activated in the table mentioned above) and also HANA optimized. One disabled or not HANA optimized InfoProvider disables the entire Multi- or CompositeProvider for HANA processing. By default all InfoProviders not mentioned in the table view RSPLS_HDB_ACT_IP are HANA enabled. It is only necessary to add an InfoProvider when it should be HANA disabled. This can be done by adding the InfoProvider to the table and de-selecting the ‘Functn. Active’ checkbox.</li>\n</ul>\n<ul>\n<li>There is a global “emergency exit switch” that can be set using report SAP_RSADMIN_MAINTAIN with parameter object RSPLS_HDB_PROCESSING_OFF. This switch should only be used in rare special circumstances.<br/>Possible values are:</li>\n</ul>\n<ul>\n<ul>\n<li>'F': Force Off         No HANA optimized processing, cannot be overruled any user</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>'X': Off           No HANA optimized processing, but can be overruled for single users for special purposes</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Not set: switch inactive (default and usual value) <br/>HANA processing will run on the intended planning function etc., but can be switched off for single users (traces, comparison etc.)</li>\n</ul>\n</ul>\n<ul>\n<li>For project studies/PoC or special tasks like special process chains one can set a user specific PAK activation switch: Set/Get-Parameter RSPLS_HDB_SUPPORT (set in transaction SU01)<br/>Possible Values are:</li>\n</ul>\n<ul>\n<ul>\n<li>'HDB_ON': HANA optimized processing when possible also if global switch set   'X' (OFF).</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>'HDB_OFF': HANA optimized processing disabled independently from the global setting.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Not set: inactive, general system settings are used (including the global switch)</li>\n</ul>\n</ul>\n<ul>\n<li>Trace information like whether a planning function is executed HANA optimized or the information about changed and created records are only displayed and written to the log when the user parameter RS_DEBUGLEVEL is set to 2 (or higher).</li>\n<li>If you use transaction RSPLAN and run a step in a planning sequence by pressing 'Execute Step with Trace' (as opposed to 'Execute Step') the function currently still runs in the IP (ABAP) runtime since we need to retrieve information for a detailed display. See knowledge base article 1911914 and 2)</li>\n</ul>\n<p><br/> <br/>Below we provide a list of features which are supported to run fully in HANA and also a list of features which are still executed in ABAP application server. Those will cause an additional data round trip and might harm the performance.</p>\n<p>Also a list of functions is added which are not supported on HANA. We have been working on enabling further functions in HANA (and continue to do so) so this list is subject to change. Some of the topics refer to a comment at the end of the note indicating the potential to eliminate the restrictions over time. In order to check whether a scenario in principle can run HANA optimized we provide the report RSPLS_PLANNING_ON_HDB_ANALYSIS (note 1824516 or 7.30 SP 10/ 7.31 SP8 and note 2336691 for better FOX support). In 7.4 SP5 and follow up releases they are supported as well if not mention explicit.</p>\n<p>1. List of features which run fully in HANA</p>\n<ul>\n<li>Following Planning Functions are executed in HANA:\r\n<ul>\n<li>Planning Function Type: 0RSPL_COPY                        Copy<br/>  </li>\n<li>Planning Function Type: 0RSPL_COPY_NO_ZEROS      Copy ( without records where all keyfigures are zero ) <br/><br/></li>\n<li>Planning Function Type: 0RSPL_CREATE_CR               Generate Combinations<br/>  </li>\n<li>Planning Function Type: 0RSPL_CURR_CONV              Currency Translation<br/>                                   requires BW 7.4 SP8 or note 2076956 for BW 7.3/7.31 and Hana Revision &gt;= *********</li>\n<li>Planning Function Type: 0RSPL_DELETE                     Delete<br/><br/></li>\n<li>Planning Function Type: 0RSPL_DELETE_DSO             Delete DSO data physically<br/>                                   requires note  1855154 or 7.30 SP10/7.31 SP9/7.40 SP5 <br/>  </li>\n<li>Planning Function Type: 0RSPL_DELETE_CR                Deletion of Invalid Combinations<br/>                                   requires note  1778939 or 7.30 SP9/7.31 SP7<br/>  </li>\n<li>Planning Function Type: 0RSPL_DELETE_CR_DSO        Physical Deletion of Invalid Combinations in DSOs<br/>                                   requires note  1855154 or 7.30 SP10/7.31 SP9/7.40 SP5<br/><br/></li>\n<li>Planning Function Type: 0RSPL_DISTR_KEY                 Distribution with Keys<br/>                                   requires BW 7.4 SP8 and HANA revision &gt;= 1.00.73<br/>                                   or only note 1821899 or 7.30 SP10/7.31 SP9/7.4 SP5 for distribution type 'Distribute Not-Assigned (#)'. <br/><br/></li>\n<li>Planning Function Type: 0RSPL_DISTR_REFDATA          Distribution by Reference Data<br/>  </li>\n<li>Planning Function Type: 0RSPL_FORMULA                    Formula<br/>                                   (see restrictions below)<br/><br/></li>\n<li>Planning Function Type: 0RSPL_REPOST                       Repost<br/>   </li>\n<li>Planning Function Type: 0RSPL_REPOST_DSO               Repost Data and Delete DSO Data physically<br/>                                   requires note  1912307 or 7.30 SP11/7.31 SP10/7.40 SP5 <br/>  </li>\n<li>Planning Function Type: 0RSPL_REPOST_CR                  Repost on Basis of Characteristic Relationship<br/>                                   requires note 1855154 or 7.30 SP10/7.31 SP9<br/>  </li>\n<li>Planning Function Type: 0RSPL_REPOST_CR_DSO          Repost DSO Data on Basis of Characteristic Relationships<br/>                                   requires note  1855154 or 7.30 SP10/7.31 SP9/7.40 SP5 </li>\n<li>Planning Function Type: 0RSPL_REVALUATION               Revaluation<br/><br/></li>\n<li>Planning Function Type: 0RSPL_SET_VALUES                 Set Key Figure Values<br/><br/></li>\n<li>Planning Function Type: 0RSPL_UNIT_CONV                  Unit Conversion<br/>                                   requires BW 7.4 SP8, or note 2076956 for BW 7.3/7.31 and Hana Revision &gt;= *********</li>\n</ul>\n</li>\n</ul>\n<p> </p>\n<ul>\n<li>delta mode flag switched on in planning functions is supported. However, the determination of the filter is done in ABAP and can cause more time then executing all records in HANA. It should therefore be only used for functional reasons and not due to better performance as in the classic IP case</li>\n<li>Disaggregation in the Query is executed in HANA if</li>\n<ul>\n<li>the query is modeled directly on the aggregation level</li>\n<li>for all restricting characteristics all contributing key figures are only restricted to one single value <br/> </li>\n</ul>\n<li>Following characteristic relationships are supported to be fully checked in HANA</li>\n<ul>\n<li>Characteristic relationship of type attribute</li>\n<li>Characteristic relationship of type DSO</li>\n<li>Characteristic relationships of type hierarchy is supported with 7.30 SP12 or 7.31 SP12 or note 1984344 </li>\n<li>Characteristic relationship of type transient. This is only available through the ABAP BICS interface<br/> </li>\n</ul>\n<li>Following data slices are supported to be fully checked in HANA</li>\n<ul>\n<li>Data slices of type selection with 7.30 SP9 or 7.31 SP7 or note 1803016<br/> </li>\n</ul>\n<li>Logging BADI requires 7.30 SP9 or 7.31 SP7 or note 1802658<br/> </li>\n<li>Key-figures in the InfoCube or aDSO with aggregate ‘SUM’. For direct update DSO we also allow ‘NO2’. The usage of MIN/MAX in read only key figure like in classic BW-IP requires BW 7.4 SP8.</li>\n<li>The new DSO of type planning (see note 1735590) is released for HANA optimized planning with 7.30 SP9 or 7.31 SP7 or note 1799168. Character like key figures which were introduced with BW 7.4 SP8 are available in PAK with note 2196138<br/> <br/>In this case where the model allows the algorithm to fully execute in HANA also the data of the planning buffer only resists in HANA (see above). Then also the save of plan buffers is fully executed within HANA only.</li>\n</ul>\n<p>2. List of features still executed in ABAP application server with data round trip</p>\n<ul>\n<li>Planning:</li>\n<ul>\n<li>Own planning function type: any planning function exit as own type as long as no interface IF_RSPLFA_SRVTYPE_TREX_EXEC or IF_RSPLFA_SRVTYPE_TREX_EXEC_R is implemented leveraging SQL Script for HANA processing. See Standard BW Documentation or note 1870369.</li>\n<li>Planning function type: 0RSPL_FORECASTING   Forecast<br/>As workaround one can use a SQL Script based implementation (see footnote 1) which includes some PAL functions<br/>(See <a href=\"http://help.sap.com/hana/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf\" target=\"_blank\">http://help.sap.com/HANA/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf</a> and <a href=\"http://www.saphana.com/community/hana-academy/#predictive-analytics-library\" target=\"_blank\">http://www.sapHANA.com/community/HANA-academy/#predictive-analytics-library</a>)<br/>  </li>\n</ul>\n<li>Planning function type 'FOX' does not support the following commands and is in those cases executed in the application server</li>\n<ul>\n<li>CALL FUNCTION (1)</li>\n<li>ABAP BREAK-POINT command is ignored</li>\n<li>FOX key words are not supported for other usage like variable names. E.g. DATA I TYPE I is not allowed but DATA J TYPE I is allowed.</li>\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on standard time characteristics like 0CALYEAR or characteristic referencing those</li>\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on characteristics which don't have a master data table, which is a BW object. But HANA View based info objects are supported ATRV in FOX on HANA view solved with note 2644984 and Revision 2.00.030.01</li>\n<li>Using a hierarchy node variable in VARC or similar statements</li>\n<li>Using compounding in variables before revision 122.05. It work from revision 122.05 onwards when applying note 2386434.</li>\n<li>Using InfoObjects outside the aggregation level (e.g. in DATA statements)</li>\n<li>The underlying aggregation level contains a float key figure you need at least note 2644984 and for HANA 1.0 revision 1.00.122.17 or for HANA 2.0 2.00.030.01. See also notes 2449817 and 460652 and the general advice to avoid float in BW. </li>\n<li>New FOX features introduced with BW 7.4 SP8 like reading from external aggregation levels which is supported for PAK with revision 102.4 incl. note 2234156 and 2241643.  Internal tables are also supported in PAK with HANA revision 97.02 and note 2145611</li>\n<li>The new FOX features ‘modularization’ or ‘forms’ introduced with BW 7.5 SP0 are currently not yet supported in PAK and lead to the processing in ABAP application server.  </li>\n<li>The new FOX feature ‘directly reading from a non-input enabled DSO ‘ (introduced in BW 7.5 SP0) is  supported in PAK with SAP BW 7.52. In case the DSO contains non-key fields the the execution however is still on the ABAP stack.</li>\n<li>The new feature of transient attributes coming with BW 7.4 SP4 will not be supported to run in HANA for time dependent attributes in FOX (e.g. ATRV). Then the FOX is processed on ABAP side.</li>\n<li>Key fields in internal tables of type KEYFIGURE_NAME.</li>\n<li>Access to external InfoProviders with variables of type KEYFIGURE_NAME.</li>\n</ul>\n</ul>\n<p>     Note 2336691 includes the HANA check of FOX in Fox Editor</p>\n<ul>\n<li>If conditions exist on a planning function and</li>\n<ul>\n<li>More on has more than 1 condition on this planning function or</li>\n<li>The global filter contains selection on attributes, but the condition contains selection on the basic characteristics or</li>\n<li>The filter contains selections on hierarchy nodes and the condition has selections on the characteristic the hierarchy is defined on or</li>\n<li>The selection contains selections which are not single values restriction and those selections are not one by the same in the global filter.<br/> </li>\n</ul>\n<li>Dis-aggregations in the Query are executed not in the database if</li>\n<ul>\n<li>Planning model uses MultiProvider on top of an aggregation level</li>\n<li>A formula is used as reference key figure for disaggregation</li>\n<li>The key figure is restricted to multiple values for a given characteristic except several single values. E.g. intervals, or hierarchy nodes in the restriction lead to execution in the application server.</li>\n<li>You use disaggregation with reference to a further structure element that contains a constant selection for a characteristic.</li>\n</ul>\n</ul>\n<p>          Here the additional impact of a round trip is not as considerable as for planning functions. Only the generated data has to be pushed to the database.</p>\n<p> </p>\n<p>3. List of features which cannot be executed with the ABAP applications planning KIT in HANA and leads to switch to BW-IP (for the entire model, not a single step)</p>\n<ul>\n<li>TIMS &amp; DATS key-figures in the InfoCube. We recommend to use data type DEC and instead of TIMS or DATS</li>\n</ul>\n<ul>\n<li>Master data access of type Own Implementation or Remote (Direct Access). With note 1929130 we allow (as exemption) characteristics referencing to standard BW time characteristics, source system (0SOURSYSTEM) or InfoProvider (0INFOPROV) when using their standard implementations.</li>\n</ul>\n<ul>\n<li>Virtual master data, including hierarchies and technical master data which are not persisted in tables. This also affects compounded characteristics leveraging those master data. Examples are InfoObjects that exist in BW but without stored data in the InfoObject tables. Exceptions are time and system characteristics which also work in the HANA optimized case.</li>\n</ul>\n<ul>\n<li>Virtual and referencing key-figures</li>\n</ul>\n<ul>\n<li>Transient characteristics</li>\n</ul>\n<ul>\n<li>Certain constraints by characteristic relationship cannot be checked in HANA yet. This includes</li>\n</ul>\n<ul>\n<ul>\n<li>characteristic relationships of type EXIT as long as the interface IF_RSPLS_CR_EXIT_HDB is not implemented. One can either leveraging SQL Script for HANA processing (see notes 1877182, 1874412 and 1929130(1)) or use ABAP as fall back (see note 1956085 - BW-IP (PAK): ABAP as fallback for exit characteristic relations and data slices on SAP HANA)</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li>Certain constraints by data slices cannot be checked in HANA yet. This includes</li>\n</ul>\n<ul>\n<ul>\n<li>data slices of type EXIT as long as the interface IF_RSPLS_DS_EXIT_HDB is not implemented leveraging SQL Script for HANA processing. See notes 1877182, 1874412 and 1929130. See footnote (1)</li>\n</ul>\n</ul>\n<ul>\n<li>CP-Problem as explained in the long text of message BRAIN 313 (see note 2095418) is not working before BW 7.4 SP11 or note 2111189.</li>\n<li>HANA views with placeholder in virtual providers or as part providers in a CompositeProvider</li>\n<li>numeric ADSO fields, e.g. integers, without aggregation, except those of type NUMC. It is recommended to use NUMC instead.</li>\n</ul>\n<p> </p>\n<p>Potential to overcome current limitation<br/>(this list reflects development plans and is no commitment for dates and or functionality)</p>\n<ul>\n<ul>\n<li>(1) By conception, we will not be able to support the full flexibility of an ABAP EXIT within HANA, in particular, when other ABAP sources are leveraged in the implementation. However, there are EXIT implementations using SQL script that can be executed in HANA.  Prerequisites are HANA 1.0 SP6 Revision 67 and NW 7.30 SP10/7.31 SP9. For BW one can alternatively apply notes 1861395, 1870342, 1877182, 1870369, 1929130 and 1956085.</li>\n</ul>\n</ul></div>", "noteVersion": 70}, {"note": "1919631", "noteTitle": "1919631 - Activating the BPC embedded", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With the installation of SAP BW 7.40 SP05, the BPC ‘embedded’ can be used without further software components. It integrates</p>\n<ol>1. Business Planning and Consolidation (BPC)</ol><ol>2. Planning Application Kit (PAK).</ol>\n<p>However the BPC ‘embedded’ belongs to the BPC NW product which requires a dedicated license. This is why it needs to be activated specifically. The following prerequisites need to be considered in order to use the BPC ‘embedded’:</p>\n<ul>\n<li>A license for 'SAP BusinessObjects Planning and Consolidation, version for SAP NetWeaver' is required</li>\n</ul>\n<ul>\n<li>Planning Application Kit should be activated (See Note 1637199)</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Hana optimized planning</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To activate the BPC-PAK embedded perform the following steps:</p>\n<ul>\n<li>Start transaction SM30 and enter table view RSPLS_HDB_ACT.</li>\n</ul>\n<ul>\n<li>Add and activate the parameter BPC_ACT 'BPC Embedded Model Active'.</li>\n</ul></div>", "noteVersion": 2}, {"note": "2213891", "noteTitle": "2213891 - Infoobject 0HIER_VERS is not active after installation of NW 740 SP08", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Activation of technical content from the transaction RSTCO_ADMIN is still running or has been terminated with following dump</p>\n<p>\"MESSAGE_TYPE_X\"<br/>\"SAPLRSDG_IOBJ_DB_READ\" bzw. LRSDG_IOBJ_DB_READF40<br/>\"REGULAR_IOBJ_GET_FOR_META\"</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Technical content activation, RSTCO_ADMIN, NW 740, LRSDG_IOBJ_DB_READF40, MESSAGE_TYPE_X, 0HIER_VERS, 0PPM_VC1, 0BWTCT_PLAN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error.</p>\n<p>The dump happens during the activation of cube 0PPM_VC1 that checks for the infoobject 0HIER_VERS status.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To solve the issue, apply the following Support Packages:</p>\n<ul>\n<li>SAP BW 7.40<br/><br/>Import Support Package 14 for SAP BW 7.40 (SAPKW74014) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2207824 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.4 ABAP SP14\", which describes this Support Package in more detail, is released for customers</li>\n<li>SAP BW 7.50<br/><br/>Import Support Package 1 for SAP BW 7.50 (SAPK-75001INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2192427 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.50 ABAP SP1\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<p><br/>Or you can use the correction instructions to reslove the issue.</p>\n<p><br/>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882</strong> for transaction SNOTE.<br/><br/>This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".</p>\n<p> </p>", "noteVersion": 4}, {"note": "2074801", "noteTitle": "2074801 - Issues with special BW InfoObjects like 0<PERSON><PERSON><PERSON><PERSON><PERSON>, 0CALMONTH, 0INFOPROV, 0IOBJNM....", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For certain BW content and/or technical characteristics such as 0CALMONTH, 0FISCPER, 0FISCYEAR, 0INFOPROV, 0IOBJNM errors occur in different contexts.</p>\n<p>Sometimes these error are only noticed after an upgrade of the system to a higher BW release.</p>\n<p>Also, these error may come up in different areas and contexts such as:</p>\n<ul>\n<li>RSRT: Query Generation or Execution</li>\n<ul>\n<li>Name T009 is not in the namespace for generated BW meta objects (Message no. R7019).</li>\n</ul>\n<li>F4/ LOV/ variables</li>\n<ul>\n<li>Dumps with these key words occur when processing master data: \"PERFORM_NOT_FOUND\" \"CX_SY_DYN_CALL_ILLEGAL_FORM\" \"SAPLRRSI\" \"LRRSIF02\" \"CHKTAB_TO_SIDTAB_SPEC\".</li>\n<li>BRAIN 299: CL_RSMD_RS_BUILD_QUERY ADD_TABLE_FIELD-02-.</li>\n<li>Virtual time hierarchies (e.g. 0WEEK_DAY, 0YEA_MON_DAY, 0YEA_QUA_DAY, 0YEA_QUA_MON_DAY, ...) of transaction SPRO/ RSRHIERARCHYVIRT are not working as expected.</li>\n<li>Texts/Description are not displayed as expected.</li>\n<li>Value help or F4 help in BEx or LOV/prompts in Webi or BO tools are displaying too few/many values (the definition according to KBA <a href=\"/notes/1565809\" target=\"_blank\">1565809</a> does not work).</li>\n<li>ORA-904 occurs when processing F4-help.</li>\n<li>System error: \"Subroutine call failed. Could not find form NEW_VALUES_GET\" - Exception CX_SY_DYN_CALL_ILLEGAL_FORM.</li>\n<li>BRAIN_DEV 643 'Value &lt;&gt; is invalid for &lt;&gt;'.</li>\n</ul>\n<li>Planning</li>\n<ul>\n<li>Dumps with these key words occur in planning context: \"UNCAUGHT_EXCEPTION\" \"CX_RSR_PROPAGATE_X\" \"CL_RSPLS_HDB_SWITCH\" \"CX_RSR_X_MESSAGE\" \"CL_RSR_TIME_RELATION_HDB\".</li>\n<li>System error in \"FILL_SIDS\" \"CL_RSR_TIME_RELATION_HDB\".</li>\n<li>BRAIN (299): System error in program CL_RSMD_RS_SPECIAL and form _GENERATE_RETURN_STRUCTURE-01-.</li>\n</ul>\n<li>INA/BICS executions via frontends SAC, Design Studio, Lumira...</li>\n<ul>\n<li>\"Text\":\"System error in program CL_RSR_OLAP_VAR and form BAD STATE\", \"ExceptionClass\":\"CX_RSR_X_MESSAGE\".</li>\n</ul>\n<li>MDX</li>\n<ul>\n<li>MDX syntax errors when using properties/attributes of special and time characteristics: BRAINOLAPAPI 100 Invalid MDX command.</li>\n<li>Value help or F4 help in BEx or LOV/prompts in Webi or BO tools are displaying too few/many values (the definition according to KBA <a href=\"/notes/1565809\" target=\"_blank\">1565809</a> does not work).</li>\n</ul>\n<li>Data Loading</li>\n<ul>\n<li>RSAU499 error during data load with InfoPackage into InfoCube (also see KBA <a href=\"/notes/1937155\" target=\"_blank\">1937155</a>).</li>\n</ul>\n<li>Generation of external HANA view on various objects like Infocube, DSO, aDSO, Infoobject</li>\n<ul>\n<li>OBJECTS_OBJREF_NOT_ASSIGNED_NO dump in CL_RS2HANA_VIEW_LOGICAL_OBJECT Method _GET_TXT_TABLES.</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Environment\">Environment</h3>\n<ul>\n<li>SAP NetWeaver - All versions</li>\n<li>SAP BW/4HANA - All versions</li>\n<li>SAP S/4HANA - Embedded BW</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Cause\">Cause</h3>\n<h3 data-toc-skip=\"\">Overview</h3>\n<p>For technical BW characteristics, special tables are processed and/or special logic applies. Therefore, these objects have their own master data read classes implemented which are used to deliver the master data and texts(in releases older than BW7x this was not the case).</p>\n<p>If the active and the content version of these InfoObjects are not matching, symptoms such as mentioned above may occur. In particular, the active version needs to use the correct master data read class (a complete list can be found in SAP note <a href=\"/notes/1387166\" target=\"_blank\">1387166</a>).</p>\n<p>Review the following Wiki page where you can find detailed information to all time charatceristics: <a href=\"https://wiki.scn.sap.com/wiki/x/TYCrGQ\" target=\"_blank\">Time and Technical Characteristics</a></p>\n<h3 data-toc-skip=\"\">Example</h3>\n<p>See for example below screenshot for characteristic 0CALMONTH demonstrating different settings between D- and A-version in transaction RSD1 (InfoObject maintainance).</p>\n<p></p>\n<p>You can also check the difference in transaction RSD1-&gt; display &lt;InfoObject&gt; -&gt; Version comparison:</p>\n<p></p>\n<p>It will list the differences:</p>\n<p></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Resolution\">Resolution</h3>\n<ol>\n<li>Check all special InfoObjects listed in SAP note <a href=\"/notes/1387166\" target=\"_blank\">1387166</a> and make sure the active version matches the content version. If not, reactivate the InfoObject from Business Content by choosing 'Copy' option in RSA1-&gt;BI_CONT. See <a href=\"https://wiki.scn.sap.com/wiki/x/jYaZGg\" target=\"_blank\">How to setup Technical InfoObjects for Embedded Analytics</a> for details.<br/><br/></li>\n<li>Starting with releases BW730 and higher, you can use check report <em>RSD_TIME_TCT_MREADCLASS_CHECK</em> delivered with SAP note <a href=\"/notes/2220750\" target=\"_blank\">2194279</a> and the enhancement of SAP note <a href=\"/notes/2220750\" target=\"_blank\">2220750</a> to check the system for this type of InfoObjects. <br/>The report displays a list with all difference found in the check and which need to be fixed by reactivating the infoobjects. E.g. it looks like the following:<br/><br/><br/><br/></li>\n</ol>\n<p>In some cases (especially before SAP note <a href=\"/notes/2104257\" target=\"_blank\">2104257</a> got included in the system), after reactivating InfoObjects and setting the correct master data read class, it is recommended to run report <em>RSR_MULTIPROV_CHECK</em> in transactionSE38 to make sure that all runtime objects and metadata of InfoProviders (such as Multiproviders, Aggregation levels etc.) are updated as well.</p>\n<ul>\n<li>Transaction SE38: RSR_MULTIPROV_CHECK</li>\n<li>Scroll the bottom and flag 'All InfoProviders'</li>\n<li>Press Execute<br/><br/></li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"See Also\">See Also</h3>\n<p>SAP Online Documentation</p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/2.0.1/en-US/4bdd81ec44f540678ad963ca9a86b422.html\" target=\"_blank\">Navigation Attributes for Time Characteristics</a></li>\n</ul>\n<p>SDN Wiki</p>\n<ul>\n<li><a href=\"https://wiki.scn.sap.com/wiki/x/TYCrGQ\" target=\"_blank\">Time and Technical Characteristics</a></li>\n<li><a href=\"https://wiki.scn.sap.com/wiki/x/jYaZGg\" target=\"_blank\">How to setup Technical InfoObjects for Embedded Analytics</a></li>\n<li><a href=\"https://wiki.scn.sap.com/wiki/x/dwC-Dg\" target=\"_blank\">OLAP Technology</a></li>\n</ul>\n<p>SAP Notes</p>\n<ul>\n<li><a href=\"/notes/2194279\" target=\"_blank\">2194279</a> Check report for technical characteristics and time characteristics</li>\n<li><a href=\"/notes/2220750\" target=\"_blank\">2220750</a> Enhancement of check report RSD_TIME_TCT_MREADCLASS_CHECK</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Keywords\">Keywords</h3>\n<p>0BUCKET, 0CALMONTH, 0CALMONTH2, 0CALQUART1, 0CALQUARTER, 0CALWEEK, 0CALYEAR, 0FISCPER, 0FISCPER3, 0FISCVARNT, 0FISCYEAR, 0HIENM, 0HIER_HIEID, 0HIER_NODE, 0HIER_VERS, 0INFOPROV, 0IOBJNM, 0LANGU, 0LOGSYS, 0RATE_TYPE, 0RTYPE, 0SOURSYSTEM, 0WEEKDAY1, NEW_VALUES_GET, PERFORM_NOT_FOUND, ADD_TABLE_FIELD, CHKTAB_TO_SIDTAB_SPEC, 0WEEK_DAY, 0YEA_MON_DAY, 0YEA_QUA_0YEA_QUA_MON_DAY, ORA-904, BRAIN_DEV 643, BRAIN_DEV643, RSRT_BW:021, UNCAUGHT_EXCEPTION CX_RSR_PROPAGATE_X CL_RSPLS_HDB_SWITCH, FILL_SIDS CL_RSR_TIME_RELATION_HDB, CL_RS2HANA_VIEW_LOGICAL_OBJECTCP, SAP_BW_SETUP_INITIAL_S4HANA, _FILL_TIME_MD_TABLES_FPER</p>", "noteVersion": 18}, {"note": "2207378", "noteTitle": "2207378 - rstco_admin run shows runs differently in different systems", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the initial content installation after installing a BW system via transaction rstco_admin the error messages are depending on the order of the returned package names of the select statement.</p>\n<p>RSCONTENT_ACTIVATE: depending on Package order 0UNIT is required.<br/>As the package order is not determined by the select we add<br/>a order criteria by name descending to force always the same<br/>activition order in terms of packages.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW, TCO Content, Initial Setup</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As there is a order by missing in the select statement the order is depending on the database. Now in the first step the objects of the _IM Packages in descending order are installed and later on the _OM Packages also in descending order.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p></p>\n<p>To solve the issue, apply the following Support Packages:</p>\n<ul>\n<li>SAP BW 7.50<br/><br/>Import Support Package 1 for SAP BW 7.50 (SAPK-75001INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2192427 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.50 ABAP SP1\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<ul></ul>\n<p><br/><br/>In urgent cases you can use the correction instructions.<br/><br/>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882</strong> for transaction SNOTE.</p>", "noteVersion": 1}, {"note": "1959332", "noteTitle": "1959332 - Transport check fails in IF_RSO_TLOGO_TRANSPORTABLE->IS_TRANSPORTABLE", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The transport of BW entities fails because of the check in IF_RSO_TLOGO_TRANSPORTABLE-&gt;IS_TRANSPORTABLE( ). This interface was introduced to provide an option at instance level for deciding whether the object was part of the transport or not. The check fails because of an access to object version A, which is not yet created, or because of missing DTA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Business Information Warehouse, BW, integrated planning, IP, BW-IP, Planning, Workspace, local provider, transport</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This problem is caused by a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li>SAP NetWeaver BW 7.40<br/><br/>Import Support Package 7 for SAP NetWeaver BW 7.40 (SAPKW74007) into your BW system. The Support Package is available when <strong>SAP Note</strong><strong><strong> 1955499</strong></strong> \"SAPBWNews NW BW 7.4/ABAP SP04\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<p>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><strong>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</strong><br/><br/>To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note still contains the words \"Preliminary version\".</p>", "noteVersion": 1}, {"note": "2290303", "noteTitle": "2290303 - Infoobject 0TXTSH does not exist in version A", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The installation from RSTCO_ADMIN may end with an error for infoobject 0TCAIFAREA. This is because the dependent infoobject 0TXTSH is not active.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSTCO_ADMIN, BI_TCO_ACTIVATION, 0TCAIFAREA, 0TXTSH, CX_RSD_IOBJ_NOT_EXIST R7245</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To solve the issue, apply the following Support Packages:</p>\n<ul>\n<li>SAP BW 7.40<br/>Import Support Package 15 for SAP BW 7.40 (SAPKW74015) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2252375 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.4 ABAP SP15\", which describes this Support Package in more detail, is released for customers</li>\n<li>SAP BW 7.50<br/><br/>Import Support Package 4 for SAP BW 7.50 (SAPK-75004INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2280438 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.50 ABAP SP4\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<p><br/><br/>In urgent cases you can use the correction instructions.<br/><br/>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882 and SAP Note 2248091</strong> for transaction SNOTE.<br/><br/>This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".</p>\n<p> </p>", "noteVersion": 2}, {"note": "2025364", "noteTitle": "2025364 - Error when accessing Hierarchies for Partner InfoObjects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use a query based on an InfoProvider which references partner InfoObjects (partner cost center, partner order, partner WBS element, partner profit center) like the InfoProvider /ERP/SFIN_V01. The report does not come up but signals an error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>InfoObjects /ERP/PCOSTCTR, /ERP/PCOORDER, /ERP/PWBSELMT, /ERP/PPROFCTR; class CL_FCOM_IP_HRY_READER, method GET_DATASRC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Replace the code in class CL_FCOM_IP_HRY_READER, method GET_DATASRC by the version attached.</p>", "noteVersion": 1}]}, {"note": "2081400", "noteTitle": "2081400 - SAP BPC Optimized for S/4 HANA Finance (aka: Integrated Business Planning for Finance): Compilation of Information", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the SAP BPC Optimized for S/4 HANA Finance (also known as \"Integrated Business Planning for Finance\" (IBPF)) solution and you are looking for a central point to access all relevant information regarding this application.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Financial Planning, SAP Simple Finance, KP06, Simplified Financials, Smart Financials, Integrated Business Planning for Finance, IBP, IBPF, Financials add-on for SAP Business Suite powered by SAP HANA, sFIN, SAP BPC for S/4 HANA Finance , ...</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Information is located in several different locations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>General Application Documentation and Help</strong>:<br/>The documentation for the \"SAP BPC for S/4 HANA Finance\" solution can be found here:</p>\n<p>Open the Help Portal at: <a href=\"http://help.sap.com/\" target=\"_blank\">http://help.sap.com</a>  and enter \"BPC optimized for S/4Hana\" into the search field. Press the \"search\" icon. <br/>... or directly at:  https://help.sap.com/viewer/74785309615c47a1b7497b1f8942ebd1/1709%20000/en-US/c95ad35225d10026e10000000a4450e5.html</p>\n<p>Additional information about the use of \"Analysis Office\" can be found here:</p>\n<p>Open the Help Portal at: <a href=\"http://help.sap.com/\" target=\"_blank\">http://help.sap.com</a> and enter \"SAP Analysis for Microsoft Office\"  into the search field. Press the \"search\" icon. <br/>... or directly at: https://help.sap.com/viewer/p/SAP_BUSINESSOBJECTS_ANALYSIS_OFFICE</p>\n<p><strong>Content: </strong><br/>Please bear in mind, that the content provided must be considered as sample content only. The goal is to shorten implementation times. The content can be used<br/>a) as-is. Feel free to use the content productively<br/>b) in an adaptation. You can copy &amp; enhance the content to meet your requirements in a better way. It ist obvious, that not all planning scenarios can be covered by tzhe reports &amp; workbooks that we provide<br/>c) not at all. Instead you can build your own content from scratch next to our sample content. This is possible because \"SAP BPC Optimized\" contains a full blown \"SAP BPC Embedded Model\" environment.</p>\n<p>Please check the sample content if it meets your requirements before starting your implementation project. <br/><em><span>Example 1</span>:</em> Our planning workbooks and reports are focused on working with the Controlling Area Currency. If you intend to use the Company Code Currency instead, you have to foresee the related adoption effort. <br/><em><span>Example 2:</span></em> Data locking may hinder planners from planning if they all want to do cost center planning at the same time - independently of the cost center used. If you want to avoid this, you may consider including another dimension in your planning workbooks.<br/><em><span>Example 3:</span></em> Our default setting for storing plan data (you have two options: cube \"R01\" or transparent table ACDOCP \"V20\") points to the cube. If you want to change this, see attached document \"Setting the default for the plan data persistency\"and the documentation.</p>\n<p><strong>Training:</strong><br/>The related SAP standard training course is: S4F80 \"SAP BPC Optimized for S/4 HANA\".</p>\n<p><strong>Presentations:<br/></strong>Customers and partners that are entiteled to access EKT (Early Knowledge Transfer) materials will find a general introduction to the topic \"SAP BPC optimized for S/4 HANA Finance\" after registration by following theses steps:</p>\n<p>1. Log in to <span><a href=\"http://training.sap.com/\" target=\"_blank\">http://training.sap.com</a></span>  with your S user ID Sxxxxxxxxxx and your password.<br/>2. Find your Learning Hub subscription at the upper right of the window:  My Account &gt; My Training.<br/>3. Press the button “Go to Learning Hub”<br/>4. In SAP Learning Hub choose “Learning Content” from the “Home” menu.<br/>5. Search for the course ID (eg. EKT_SFIN20):</p>\n<p><strong>Installation Guides:</strong><br/>Documents that help you in setting-up the SAP installation from a technical point of view can be found here:<br/><a href=\"http://service.sap.com/instguides\" target=\"_blank\">http://</a><a href=\"http://service.sap.com/instguides\" target=\"_blank\">service.sap.com/instguides</a></p>\n<p>or directly at:  https://help.sap.com/doc/6b11678926d3409bbfea8897cb34d10f/1709%20001/en-US/INST_OP1709_FPS01.pdf</p>\n<p><strong>     Installation Notes: </strong><br/>           In order to install &amp; activate the solution in your system, please consider the following notes:</p>\n<p><strong>           1972819 - </strong>Setup Integrated Business Planning for Finance<br/>                            This note must be considered after each upgrade !<br/><strong>           2061419</strong> - Integrated Business Planning: <br/>                              Copy Plan Data to Classical ERP Planning &amp; Reporting</p>\n<p><strong> </strong></p>\n<p><strong>How to download the Analysis Office MS Excel Add-On'<br/></strong>Go to the Software Download Center    <span><a href=\"http://support.sap.com/swdc\" target=\"_blank\">http</a></span><span><a href=\"http://support.sap.com/swdc\" target=\"_blank\">://</a></span><span><a href=\"http://support.sap.com/swdc\" target=\"_blank\">support.sap.com/swdc</a></span>   and navigate to:<br/><br/>==&gt; Installations and Upgrades<br/>==&gt; Browse download catalog <br/>==&gt; Analytics Solutions <br/>==&gt; SBOP ANALYSIS MS OFFICE  <br/>==&gt; e.g. SBOP ANALYSIS MS OFFICE 2.6 or later</p>\n<p> </p>\n<p><strong>How-To Guides:<br/></strong>We have created How-To Guides for some functions. These guides are located in the SAP Community Network (SCN).  Open the SCN Portal at: <a href=\"http://scn.sap.com/\" target=\"_blank\">http://scn.sap.com</a></p>\n<p>Then follow the navigation: Click on \"Products\" (top of the screen) and then select the tab named \"Analytics\". Here \"Data Warehousing\" &gt; \"Business Planning\" takes you to the page where all BW planning related How-To documents are located. Access the entire list of documents: click \"SAP How-To Guides for ...\" on the left side of the page. You can access the two documents that have been created within the IBP development directly at:</p>\n<p>Enhancing an Input-Ready Query with a Comments Column<br/><a href=\"http://go.sap.com/docs/download/2014/10/064ef6df-537c-0010-82c7-eda71af511fa.pdf\" target=\"_blank\">http://go.sap.com/docs/download/2014/10/064ef6df-537c-0010-82c7-eda71af511fa.pdf</a></p>\n<p>Provisional Master Data<br/><a href=\"http://go.sap.com/docs/download/2014/10/ac166ee0-537c-0010-82c7-eda71af511fa.pdf\" target=\"_blank\">http://go.sap.com/docs/download/2014/10/ac166ee0-537c-0010-82c7-eda71af511fa.pdf</a></p>\n<p>Process Orchestration with BPF<br/><a href=\"http://go.sap.com/documents/2016/08/a0caf406-817c-0010-82c7-eda71af511fa.html\" target=\"_blank\">http://go.sap.com/documents/2016/08/a0caf406-817c-0010-82c7-eda71af511fa.html</a></p>\n<p>Plan Data Retraction from Standalone BPC to BPC Optimized for S/4<br/><a href=\"http://go.sap.com/documents/2016/06/1800637d-767c-0010-82c7-eda71af511fa.html\" target=\"_blank\">http://go.sap.com/documents/2016/06/1800637d-767c-0010-82c7-eda71af511fa.html</a></p>\n<p>Embedded BW<br/><a href=\"http://go.sap.com/docs/download/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.pdf\" target=\"_blank\">http://go.sap.com/docs/download/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.pdf</a></p>\n<p>Adapting the Business Content (Extensibility) in SAP BPC Optimized for S/4 HANA Finance<br/><a href=\"http://go.sap.com/documents/2016/06/14e6eeba-757c-0010-82c7-eda71af511fa.html\" target=\"_blank\">http://go.sap.com/documents/2016/06/14e6eeba-757c-0010-82c7-eda71af511fa.html</a></p>\n<p>BRFplus Applications in BPC Optimized for S/4 HANA Finance<br/><a href=\"http://go.sap.com/documents/2016/06/c6e7c81d-767c-0010-82c7-eda71af511fa.html\" target=\"_blank\">http://go.sap.com/documents/2016/06/c6e7c81d-767c-0010-82c7-eda71af511fa.html</a></p>\n<p>Import Financial Plan Data<br/><a href=\"https://www.sap.com/documents/2018/05/76f26f2f-077d-0010-87a3-c30de2ffd8ff.html\" target=\"_blank\">https://www.sap.com/documents/2018/05/76f26f2f-077d-0010-87a3-c30de2ffd8ff.html</a></p>\n<p>HDI Migration for SAP BPC Optimized for SAP S/4HANA<br/><a href=\"/notes/2649528\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2649528</a></p>\n<p><strong>Troubleshooting Guide:<br/></strong>If your project is facing issues with Fiori apps, like missing tiles, apps that don’t start, etc. or if you need tools for error analysis then check out the SAP Fiori – Troubleshooting Guide for SAP S/4HANA Finance, On-Premise Edition which can be found here:</p>\n<p><a href=\"https://blogs.sap.com/2016/02/02/fiori-problems-to-get-apps-up-and-running-checkout-the-troubleshooting-guide/\" target=\"_blank\">https://blogs.sap.com/2016/02/02/fiori-problems-to-get-apps-up-and-running-checkout-the-troubleshooting-guide/</a></p>\n<p><strong>Release Information Notes (RIN):<br/></strong><strong>Release              Note / Hint<br/></strong>1.1                     2039267<br/>1.3                     2061419<br/>2.0                     2127742<br/>2.2                     2165911 (1503 SPS 1508)   aka sFIN 2.2<br/>2.3                     2203831 (1511 on premise) aka sFIN 2.3 <br/>                          The app \"Set Controlling Area\" has been replaced with a similar functionality located in the \"User Preferences\" menu within the Fiori launchpad. <br/>S/4HANA 1610     Analysis Office Excel Add-In: The workbooks must be opend using Version 2.2 SP3 minimum.<br/>                          The app \"Set Controlling Area\" has been replaced with a similar functionality located in the \"User Preferences\" menu within the Fiori launchpad.<br/>                          1610 Help: <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/4215dc52f2d2a91ae10000000a44538d/1610%20002/en-US/frameset.htm\" target=\"_blank\">Online Help<br/></a>S/4HANA 1709     Central Release Note: <a href=\"/notes/2493348\" target=\"_blank\">2493348<br/></a>                          1709 Help:  <a href=\"https://help.sap.com/viewer/74785309615c47a1b7497b1f8942ebd1/1709%20001/en-US/c95ad35225d10026e10000000a4450e5.html\" target=\"_blank\">Online Help</a></p>\n<p> </p>\n<p><strong>Additional Notes:<br/></strong>If you have just upgraded to release 1503 SPS 1508 (aka sFIN 2.0 SP2), you have to implement also the following notes (list may not be complete):</p>\n<p>2165500: Activation of Analysis Office object from Business Content Bundle fails<br/>2165417: Analytical GUIBB - Error while mapping variables for PS<br/>2164000: FPM BICS Search: Values of hierarchical compound variables not valid<br/>2165106: Analytical GUIBB-Planning with compound characteristics<br/>2074801: Dumps and Issues with special InfoObjects like 0FISCYEAR, 0CALMONTH...</p>\n<p> </p>\n<p><strong>Remark Regarding Amount Fields<br/></strong>Many transactional tables in S/4HANA have been extendet to handle very large amounts (23 digits). This enhancement has not been done for classic CO plan data tables. For this reason, plan data processed with SAP BPC for S/4HANA can still not exceed the traditional length of 15+2 decimals.</p>\n<p><strong>Cookbooks</strong><br/><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" de;=\"\" en-us;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">SAP S/4HANA: What is SAP S/4HANA: <a href=\"http://scn.sap.com/docs/DOC-64980\" target=\"_blank\">http://scn.sap.com/docs/DOC-64980</a><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" de;=\"\" en-us;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><br/>Blog: </span></span><a href=\"http://scn.sap.com/community/s4hana/blog/2015/07/09/sap-s4hana-cookbook-guiding-you-through-an-sap-s4hana-implementation\" target=\"_blank\">http://scn.sap.com/community/s4hana/blog/2015/07/09/sap-s4hana-cookbook-guiding-you-through-an-sap-s4hana-implementation</a></p>\n<p> </p>\n<p>END of Note</p>", "noteVersion": 27}, {"note": "2575530", "noteTitle": "2575530 - Performance after Upgrade to S/4 HANA in Accounting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You recently upgraded from ERP or SuiteOnHANA to S/4 HANA and you experience a decrease in performance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Universal Journal, ACDOCA, READ_OPTI, T811FLAGS, Report Painter, Report Writer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable the Finance solution to be enhanced with new capabilities and efficiently utilize new technologies such as in-memory, we have developed a fundamentally new architecture.</p>\n<p>First we redesigned the data model and provided various applications with a common data basis (Universal Journal), which enables much of the detailed information from the business processes to be stored in journal entries. Furthermore, wherever possible we dispensed with redundancies or even eliminated existing ones. All this has enabled radically new insights into financial data with unprecedented levels of detail.</p>\n<p>These and other changes form the basis for significant new capabilities (such as parallel currencies, parallel ledgers, and extensibility) which we have developed and will continue to develop. Step by step, we are enhancing our applications with these capabilities and gradually adapting them to take full advantage of the potentials inherent in the new architecture. We have already converted many applications. These approaches have opened up completely new perspectives such as simulation and prediction.</p>\n<p>Despite all these changes, we have not neglected the needs of our customers who have been using our solutions before. In order to smooth the path for them to transition to S/4HANA, we are employing the technology of compatibility views which compute the legacy data format at runtime. In some cases, this can lead to performance differences against ERP. To ensure that our customers can profit from enhanced insights into their data and increased performance, we recommend that customer programs be adapted to the new data basis.</p>\n<p>In view of the above developments and enhancements, in some cases (often depending on the actual constellation of customer data) the performance of some of our applications cannot be compared directly with that of ERP. For a list of adjustments that have been made up to now, see the attachment to this Note.</p>\n<p><br/><br/></p>", "noteVersion": 3, "refer_note": [{"note": "2185026", "noteTitle": "2185026 - Compatibility views COSP, COSS, COEP, COVP: How do you optimize their use?", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In your customer-specific programs, you use the compatibility views COSP, COSS, COEP, and COVP to determine CO-relevant documents or totals records.</p>\n<p>The performance of the compatibility views is insufficient.</p>\n<p>You want to optimize these accesses.</p>\n<p>You want to understand the data model behind the compatibility views.</p>\n<p>They terminate when you use them in transaction SE16 if datasets are large.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>COSSA, COSPA, SELECT, ABAP, SQL error 2048</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For sFIN 1503 (SAP_FIN 720), the controlling document was transferred to the new table ACDOCA.</p>\n<p>For sFIN 1.0 (SAP_FIN 700), the totals tables COSP and COSS were replaced by views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In principle, you can optimize selection using compatibility views in four ways:</p>\n<ol>\n<li>By optimizing the selection conditions when the compatibility views are used in the program.</li>\n<li>By replacing the selection of compatibility views with adequate replacement CDS views that, for example, have the same structure (identical field string) as the compatibility views but sometimes have a reduced scope of functionality. This means that they are faster than the delivered compatibility views.</li>\n<li>By replacing the selection for the CO-relevant actual postings with direct access to the new line item table ACDOCA. Reading the other data from the relevant tables.</li>\n<li>By replacing the selection of compatibility views with a standard ABAP class delivered by SAP.</li>\n</ol>\n<p>The third option is recommended long term - especially for actual postings from ACDOCA.</p>\n<p> </p>\n<p><span>Usage is not suitable in SE16:</span></p>\n<p>The compatibility views are generally <span>not suitable</span> for the selection of large datasets, top N, or similar queries using transaction SE16. This can cause a memory overflow, among other issues. Depending on the information you need, use the tables ACDOCA, COSP_BAK, or COSS_BAK or the view V_COEP_ORI instead in SE16, with the suitable selection criteria described here. The following information about the selection criteria you should avoid when using the compatibility views in ABAP source code generally also applies to their use in SE16.</p>\n<p> </p>\n<p><span>Change as of SAP_FIN 730 and S4CORE 101:</span></p>\n<ul>\n<li>As of this release, the compatibility views also support actual versions &lt;&gt; '000'. Background: Transfer prices are now supported in Controlling.</li>\n<li>Customizing table FINSC_CMP_VERSNC for linking the version and ledger replaces the table FINSC_LD_CMP. Important: For data selections in Version \"000\", you can simply replace the table. For data selections with actual version &lt;&gt; '000', this does not suffice.</li>\n</ul>\n<p>See the section entitled \"Actual versions &lt;&gt; '000'\".</p>\n<p> </p>\n<p> </p>\n<p>First, the data model changed in SFIN will be described below.</p>\n<p><span><strong>Data model of the compatibility views in sFIN</strong></span></p>\n<p><strong>The compatibility views replace the tables COSP and COSS and are used to redirect read accesses for the table COEP and the ABAP Dictionary view COVP. This ensures that <span>all</span> programs that access COSP, COSS, COEP, and COVP in read mode continue to work without losing any functions.</strong></p>\n<p>Compatibility views are built as CDS views (DDL views).</p>\n<p>Tip: The compatibility views and their implementation as a hierarchy of subviews can be completely viewed only using the workbench \"ABAP in Eclipse\". You can find the compatibility views in the packages KACC_ERP50 (for COSP, COSS, COEP) and KACC (for COVP). Only a restricted display is possible in transaction SE11.</p>\n<p><strong>SAP_FIN 700</strong></p>\n<p>For SAP_FIN 700, the former totals tables COSP and COSS were already replaced by CDS views of the same name (DDL views) - also called compatibility views. During the installation/upgrade, the system automatically transfers previous data from COSP and COSS to the relevant, new tables COSP_BAK and COSS_BAK.</p>\n<p>These compatibility views COSP and COSS calculate the actual postings from the line item table COEP ad hoc and read the other data from the new tables COSP_BAK and COSS_BAK.</p>\n<p>Note for posting:</p>\n<ul>\n<li>All of the data with the value type &lt; &gt; 04 and &lt; &gt; 11 is still written to the tables COSP_BAK and COSS_BAK.</li>\n<li>You are not allowed to directly add, change, or delete data with a value type = 04 and = 11.</li>\n<li>SQL operations for data manipulation in COSP and COSS are no longer possible. They lead to a syntax error.</li>\n</ul>\n<p>This is ensured by using the classes CL_FCO_COSP_UPDATE and CL_FCO_COSS_UPDATE instead of direct INSERT, UPDATE, DELETE, MODIFY commands using SQL on the previous tables COSP and COSS. Important: These classes do not ensure that the actual costs of the value type 04 and 11 are updated to the table COEP (or ACDOCA as of SAP_FIN 720). Use the available interfaces (such as BAPIs) for your customer-specific update of these actual costs.</p>\n<p><strong>Data source for COSP and COSS (700):</strong></p>\n<ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = 04 (current value) </li>\n<li>WRTTP = 11 (statistical current value)</li>\n</ul>\n<li>From the tables COSP_BAK or COSS_BAK:</li>\n<ul>\n<li>All other WRTTPs</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>SAP_FIN 720 and higher</strong></p>\n<p>As of SAP_FIN 720, the previous actual postings from the table COEP are transferred to the table ACDOCA.</p>\n<p>The table COEP is <span>automatically</span> redirected to the view V_COEP only for <span>read accesses (ABAP Open SQL statement SELECT)</span>. In the same way, the ABAP Dictionary view COVP is automatically redirected to the CDS view V_COVP.</p>\n<p>As such, the table COEP remains intact.</p>\n<p>Important tips for the redirection:</p>\n<ul>\n<li>This redirection is visible in transaction SE11 in the menu under \"Utilities-&gt; Runtime Object-&gt;Display\": In the table \"Header of active runtime object\", you will find the view that is redirected to in the column \"Physical table\".</li>\n<li>The <span>redirection</span> is <span>not active</span> in SAP HANA objects such as CDS views, calculation views, analytical views, and attribute views. A selection in COEP is not redirected in SAP HANA views but selects the data from the original table. </li>\n<li>You <span>must</span> activate the redirection in SAP NetWeaver 7.40 as a step in the migration or in the basic Customizing of the general ledger. Otherwise, no postings can be made in the system.</li>\n</ul>\n<p>The compatibility view V_COEP calculates the actual postings from the line item table ACDOCA ad hoc and reads the other data that was previously posted and continues to be posted in COEP from the table COEP. The compatibility view V_COVP calculates the actual postings from the line item table ACDOCA ad hoc and reads the other data from the tables COEP and COBK. This ensures that <span>all</span> programs that use a SELECT operation in COEP and COVP continue to work with no functional compromises.</p>\n<p>If you want to know which data is in the physical table COEP, use the CDS view V_COEP_ORI. This view selects the data directly from the table COEP. (Reason: Generally, the redirection, as mentioned above, is not active within CDS views.)</p>\n<p><span>Important:</span> Change of COSP or COSS in SAP_FIN 720: Due to the transfer of the CO actual postings to ACDOCA, since SAP_FIN 720 the views COSP/COSS have read the actual postings of a value type = 04 from ACDOCA and not from COEP accordingly (as in SAP_FIN 700, see above).</p>\n<p>Note for posting:</p>\n<ul>\n<li>The actual postings (value type 04 and relevant statistical line items of the value type 11) from the table COEP are written in one shared line item to the table ACDOCA.</li>\n<li>All of the data with the value type &lt; &gt; 04 is still written to the table COEP.</li>\n<li>The statistical actual postings (value type 11) are written to both ACDOCA and COEP. The CO compatibility views always select this data from COEP for performance reasons. The new report (VDM, SAP Fiori) reads this data from ACDOCA.</li>\n<li>You are not allowed to directly add, change, or delete data with a value type = 04 in COEP.</li>\n<li>The table for the document header, COBK, is still updated for <span>all</span> CO-relevant actual postings.</li>\n<li>CO-relevant postings in ACDOCA that are entered in SAP_FIN 720 always have a document header in BKPF as well.</li>\n<li>Therefore, migrated CO documents from the legacy system have only one document header in BKPF if a connection to the FI document was found in the migration. As mentioned, the COBK entry is <span>always</span> available.</li>\n</ul>\n<p><strong><strong><strong>Data source for</strong> COSP and COSS (720 et seq.)</strong>:</strong></p>\n<ul>\n<li>From the table ACDOCA:</li>\n<ul>\n<li>WRTTP = 04 (Actual) </li>\n</ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = 11 (Statistical Actual)</li>\n</ul>\n<li>From the tables COSP_BAK and COSS_BAK:</li>\n<ul>\n<li>All other WRTTPs</li>\n</ul>\n</ul>\n<p><strong><strong>Data source for</strong> COEP (720 et seq):</strong></p>\n<ul>\n<li>From the table ACDOCA:</li>\n<ul>\n<li>WRTTP = 04 (Actual) </li>\n</ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = '11' (statistical actuals); note: For technical performance reasons, this data is read from COEP and not from ACDOCA.</li>\n<li>All other WRTTPs</li>\n</ul>\n</ul>\n<p><strong><strong>Data source for</strong> COVP (720 et seq):</strong></p>\n<ul>\n<li>From the table ACDOCA:</li>\n<ul>\n<li>WRTTP = 04 (Actual) </li>\n</ul>\n<li>From the table COEP:</li>\n<ul>\n<li>WRTTP = '11' (statistical actuals); note: For performance reasons, this data is read from COEP and not from ACDOCA, where it should actually also be available.</li>\n<li>All other WRTTPs</li>\n</ul>\n<li>The document header is still from the table COBK.</li>\n</ul>\n<p> </p>\n<p> </p>\n<p>The three ways of optimizing the selection are listed below:</p>\n<p><span><strong>1</strong></span><strong><span>st way: Optimize the selection conditions</span> </strong></p>\n<p>A general recommendation cannot be given because the performance of the SELECT command depends on the system properties, the system settings, and the data volume in the tables ACDOCA, COEP, COBK, COSP_BAK, COSS_BAK, and all other tables involved. Test these alternatives before you use them in your production system.</p>\n<ul>\n<li>Avoid the JOIN operation of large tables (MARA, CE4xxxx, CKIS, CSKS, for example) with compatibility views.</li>\n<li>Avoid SELECT * ... . Instead, use specific, restricted field lists in the SELECT statement.</li>\n<li>Avoid a large number of SELECT SINGLE or SELECT commands with very detailed selection conditions on the one hand (only for one CO object (OBJNR) or only one cost element (KSTAR), for example). Package the SELECT command. Package sizes of 50 - 1000 CO objects or cost elements have proved to be sensible. Proceed in the same way for other selection criteria - except for the value type (WRTTP). The optimal package size depends on the conditions mentioned above.</li>\n<li>Avoid a SELECT command with very large WHERE conditions on the other hand (more than 1000 CO objects or more than 1000 cost elements, for example). Package here as described in the previous point.</li>\n<li>The usage of RANGES in the WHERE condition appears better in performance measurements than the usage of the SELECT in a LOOP.</li>\n<li>Avoid the FOR ALL ENTRIES command. In particular, FOR ALL ENTRIES with an internal table from which more than one field value is queried in the WHERE condition is extremely performance critical. Package here as described in the previous point.</li>\n<ul>\n<li>Example: SELECT ... FOR ALL ENTRIES in LT_ABC WHERE objnr = LT_ABC-objnr AND vrgng = LT_ABC-vrgng. Avoid conditions of this kind. Instead, use separate RANGES tables for OBJNR and VRGNG in the WHERE condition.</li>\n<li>If you have programmed a selection for a compatibility view with several value types, split this selection into several selections - split according to value type (field WRTTP) - and then merge the results in the program. That is sometimes faster than a selection using the compatibility view with all value types. Particularly split after:</li>\n<li>Value type 04, separate</li>\n<li>Value type 11, separate</li>\n<li>All other value types together, separate</li>\n</ul>\n<li>Avoid using selections such as \"SELECT objnr INTO ld_objnr UP TO 1 ROWS FROM COEP\" WHERE OBJNR = I_OBJNR or similar to determine whether one entry or no entry is delivered.</li>\n<ul>\n<li>Perform these queries for data of the value type 04 directly on ACDOCA. <span>Never</span> read data of the value type 04 from COEP, COSP_BAK, or COSS_BAK.</li>\n<li>For the <span>other value types</span>, select directly from the relevant original tables:</li>\n<ul>\n<li>COEP via view V_COEP_ORI, (not for 04)</li>\n<li>COBK (not for 04)</li>\n<li>COSP_BAK (not for 04, 11)</li>\n<li>COSS_BAK (not for 04, 11)</li>\n</ul>\n<li>Example: See the function module K_OBJECT_DATA_EXIST following the implementation of SAP Note 2160629.</li>\n</ul>\n<li> Avoid selections without the specification of the fiscal year and period. Wherever possible, restrict the period.</li>\n</ul>\n<p> </p>\n<p><span><strong>2nd way: Selection with replacement views </strong></span></p>\n<p>Until now, SAP has only delivered replacement views with a reduced scope of functionality for COVP.</p>\n<p>Instead of COVP, you can use optimized replacement views that have the same structure as COVP but have better performance because some technical fields are not calculated - SAP Note 2159922 (\"sFIN: Performance of COVP\"):</p>\n<ul>\n<li>If you do not need the values of the fields OBJNR_N1, OBJNR_N2, OBJNR_N3, and TIMESTMP of COVP for real actual postings, use V_COVP_KAEP instead of COVP.</li>\n<li>See SAP Note 2178343 for an example implementation of the alternative use of these views in the standard program of the line item display.</li>\n</ul>\n<p> </p>\n<p>The optimized views V_COSP_R and V_COSS_R are available for COSP and COSS. They have identical scopes of functionality that provide improved performance in the case of a large data volume. Please see SAP Note 2222535 in this regard.</p>\n<p>For small and medium-sized data volumes, access via COSP and COSS provides better performance.</p>\n<p> </p>\n<p><span><strong>3</strong><strong>rd way: Selection from ACDOCA</strong></span></p>\n<p>Note that only the CO-relevant actual postings from the table ACDOCA are to be determined. This corresponds to the data that earlier was saved in the old tables COEP, COSP, and COSS under value type 04 and 11. Data of the value type 11 is also written in the table COEP for performance reasons.</p>\n<p>a) If you change your selection to ACDOCA, but still further process this data in your program using old structures (COEP, COSSA, COSPA, and so on), you need the mapping of the field names of the CO-relevant fields from ACDOCA with the fields from the previous line item table COEP. You can find this mapping in SAP Note 2156822 (\"sFIN: Mapping of ACDOCA fields to old FI/CO tables\"). This SAP Note provides information about the assignment of the partially different field names in the tables.</p>\n<ul>\n<li>Example: COEP-KSTAR (\"Cost Element\") corresponds 1:1 to the ACDOCA-RACCT (\"Account Number\") </li>\n</ul>\n<p>b) However, some fields of the compatibility views are not transferred 1:1 from a field of ACDOCA, but are calculated depending on various fields.</p>\n<ul>\n<li>Example: \"Total Value in Object currency\" COEP-WOGBTR generally corresponds to the field \"Value in Local Currency\" ACDOCA-HSL. Exception: If ACDOCA-RCO_OCUR is filled, the field COEP-WOGBTR is adopted from the \"Value in CO Object Currency\" field ACDOCA-CO_OSL.</li>\n</ul>\n<p> </p>\n<p><strong>Calculating COEP for value type 04 from ACDOCA</strong> (for Version '000'):</p>\n<ul>\n<li>To generate the COEP line items from ACDOCA, refer to</li>\n<ul>\n<li>SAP Note 2156822 (\"sFIN: Mapping of ACDOCA fields to old FI/CO tables\") (-&gt; for a)) and</li>\n<li>to the CDS view V_COEP_ACDOCA \"Mapping from ACDOCA to COEP\", which calculates most of the fields of the table COEP ( -&gt; for b)). This view is also used in the view hierarchy of V_COEP. You can determine everything apart from the calculation of the fields OBJNR_N1, OBJNR_N2, OBJNR_N3, and TIMESTMP here.</li>\n</ul>\n<li>To determine the CO-relevant document lines from ACDOCA, use the JOIN operation from the view V_COEP_ACDOCA or from the example below.</li>\n<ul>\n<li>Primarily, you set filters:</li>\n<ul>\n<li>For the fields CO_BELNR and CO_BUZEI. Both must be filled.</li>\n<li>For the CO-relevant ledger (ACDOCA-RLDNR). Your determine the relevant ledger per company code and CO version (normally version '000') from the customizing table FINSC_LD_CMP. <span>Important:</span> As of SAP_FIN 730 and S4CORE 101, you can no longer use the table FINSC_LD_CMP. In these releases, you must use the new table FINSC_CMP_VERSNC.</li>\n</ul>\n<li>If ACCASTY is filled, a posting that corresponds to the value type 04 is concerned (Warning: ACCASTY = space would be a posting of the cost element type 90. These are purely statistical). Use Customizing table FINSC_LD_CMP (as of SAP_FIN 730 or S4CORE 101: use the table FINSC_CMP_VERSNC) to determine the CO-relevant ledger (CO Version '000' is assigned to this ledger; see the migration IMG: \"Preparations and Migration of Customizing -&gt; Preparations and Migration of Customizing for General Ledger -&gt; Define Ledger for CO Version\")</li>\n</ul>\n<li>Recommendation for COEP-TIMESTMP (Format: Number of seconds since January 1, 1990 * 10000): In the customer program, use ACDOCA-TIMESTAMP (format: JJJJMMTThhmmss) instead, which uses one different format but is otherwise the same.</li>\n<li>In CO, you have the option of updating an object currency that is different than the posting currency. If you use this function, these values are in the field ACDOCA-CO_OSL. The relevant currency key is in ACDOCA-RCO_OCUR. If you do not use this free object currency function, you will find the so-called \"object currency\" (posted formerly in COEP-WOGBTR) in the field ACDOCA-HSL.</li>\n</ul>\n<p>Example of the filtering of CO-relevant document line items from the table ACDOCA for value type 04:</p>\n<p><strong>Data: </strong>lt_acdoca <strong>type table of </strong>acdoca.</p>\n<p>select * <strong>from </strong>acdoca <strong>as</strong> a</p>\n<p><strong>inner join</strong> finsc_ld_cmp <strong>as</strong> v <strong>on</strong> a<strong>~</strong>rbukrs <strong>=</strong> v<strong>~</strong>bukrs <strong>and</strong> a<strong>~</strong>rldnr <strong>=</strong> v<strong>~</strong>rldnr <strong>and </strong>v<strong>~</strong>versn <strong>ne space </strong>\"Important: or table FINSC_CMP_VERSNC</p>\n<p><strong>into corresponding fields of table </strong>lt_acdoca</p>\n<p><strong>where</strong> a<strong>~</strong>co_belnr <strong>ne space and</strong> a<strong>~</strong>co_buzei <strong>ne </strong>'000' <strong>and not</strong> a<strong>~</strong>accasty <strong>= space. </strong></p>\n<p> </p>\n<p><strong>Calculating COEP for value type 11 from ACDOCA</strong> (for Version '000'):</p>\n<p>This calculation is very complicated. Furthermore, it is no longer possible to determine an object currency of the statistical CO objects that differs from the real posting from the table ACDOCA.</p>\n<p>Recommendation: Therefore, directly use the table COEP to evaluate data of the value type 11. To do this, directly use the view V_COEP_ORI in ABAP.</p>\n<p><strong>Calculating COEP for additional value types:</strong></p>\n<p>Recommendation: To do this, directly use the view V_COEP_ORI in ABAP.</p>\n<p> </p>\n<p><strong>Calculating COSP/COSS for value type '04' from ACDOCA</strong> (for Version '000'):</p>\n<p>At this point, the determination of the diverse amounts and quantities (controlling area currency, transaction currency, object currency, and so on) should not be reproduced for the periods 1-16 and their aggregation. If you require it, refer to the programming of the view hierarchy of COSP and COSS.</p>\n<p><span>Recommendation:</span></p>\n<ol>\n<li>The on-the-fly calculations and aggregations of the line items are often an important factor in the insufficient performance of COSP and COSS. For customer programs, therefore, it is generally recommended that you <span>no longer</span> <span>use</span> the structures <span>COSP and COSS</span> and the data that must be aggregated for them for performance reasons. Instead, you should further process the data of the line items directly from ACDOCA.</li>\n<li>The example below shows which complex selection from ACDOCA (different from when actual line items are generated) may be necessary to determine the data foundation of the actual costs for the totals. If you cannot avoid using the structures COSP/COSS, thoroughly test your converted program with regards to its functionality and performance before you use it productively. </li>\n</ol>\n<p>To determine the data of the value type 04 that is relevant for COSP and COSS from ACDOCA, you must also read the adjustment postings from the \"Migration of Balances\" (see Customizing), except for the selection in accordance with \"Calculating the values of COEP of value type 04 from the values of ACDOCA\". These adjustment postings are created during the migration if, for example, you had already archived COEP line items using the archiving object CO_ITEM but had not archived the relevant totals. Therefore, these adjustment postings are relevant only if you have carried out a migration.</p>\n<p><span>Calculating the former lines from COSP/COSS for value type 04 from ACDOCA (for Version '000'):</span></p>\n<ul>\n<li>The balance adjustments do not have a CO document number in the field CO_BELNR and do not have a document header in COBK and BKPF because they are pure adjustment postings.</li>\n<li>To determine the CO-relevant adjustment postings from ACDOCA, follow the example below:</li>\n<ul>\n<li>The CO-relevant balance adjustments have the value ACDOCA-BSTAT = C and ACDOCA-MIG_SOURCE = C.</li>\n<li>The balance adjustments that are relevant for COSP and for COSS depend on the business transaction ACDOCA-VRGNG (JOIN with the table TJ01 and check of the indicator TJ01-COSP or TJ01-COSS for the relevant results analysis of the transaction for COSP or COSS).</li>\n<li>As a rule, the logical system of a posting in ACDOCA must correspond to the logical system of the client and controlling area (JOIN with TKA01 and T000).</li>\n</ul>\n<li>The filter criteria of the CO line items correspond to those from the point \"Calculating the values of COEP of value type 04 from the values of ACDOCA\".</li>\n</ul>\n<p> </p>\n<p>In the following example, the syntactically correct selection of the CO-relevant primary costs and secondary costs in <span>one</span> SELECT statement is illustrated.</p>\n<p>Example of the filtering of the necessary CO-relevant balance adjustments and the necessary CO relevant line items from ACDOCA for <span>value type 04 and COSP</span>:</p>\n<p><strong>Data: </strong>lt_acdoca <strong>type table of </strong>acdoca.</p>\n<p>select * <strong>from </strong>acdoca <strong>as</strong> a</p>\n<p><strong>inner join</strong> finsc_ld_cmp <strong>as</strong> v <strong>on</strong> a<strong>~</strong>rbukrs <strong>=</strong> v<strong>~</strong>bukrs <strong>and</strong> a<strong>~</strong>rldnr <strong>=</strong> v<strong>~</strong>rldnr <strong>and </strong>v<strong>~</strong>versn <strong>= </strong>'000' \"Important: or table FINSC_CMP_VERSNC</p>\n<p><strong>inner join</strong> tj01 <strong>as</strong> t <strong>on</strong> t<strong>~</strong>vrgng <strong>=</strong> a<strong>~</strong>vrgng <strong>and</strong> t<strong>~</strong>xcosp <strong>= </strong>'X' <strong>and</strong> t<strong>~</strong>xcoss = ' '</p>\n<p><strong>inner join</strong> tka01 <strong>as</strong> tk<strong> on</strong> tk~kokrs = a~kokrs</p>\n<p><strong>inner join</strong> t000 <strong>as</strong> s <strong>on</strong> ( s<strong>~</strong>logsys <strong>=</strong> a<strong>~</strong>logsyso <strong>or</strong> a<strong>~</strong>logsyso <strong>= </strong>' ' <strong>or</strong></p>\n<p><strong></strong><span><span>( </span></span><span><span>(</span></span><strong><strong><span><strong><span> </span></strong></span></strong><span></span></strong>a~accasty = 'KS' <strong>or</strong> a~accasty = 'KL' ) <strong>and</strong> ( s~logsys = tk~logsystem <strong>or</strong> tk~logsystem = ' ' ) )</p>\n<p>)</p>\n<p><strong>into corresponding fields of table @</strong>lt_acdoca</p>\n<p><strong><strong>where</strong></strong>(( a~co_belnr <strong><strong>ne </strong></strong>' '<strong><strong> and</strong></strong>a~co_buzei<strong> <strong>ne </strong></strong>'000'<strong> <strong>and not</strong></strong>a~accasty = ' ')<strong><strong> or </strong></strong></p>\n<p>( a<strong>~</strong>bstat <strong>= </strong>'C' <strong>and</strong> a<strong>~</strong>mig_source <strong>= </strong>'C' ))</p>\n<p><strong>and </strong>s~mandt = @sy-mandt.</p>\n<p> </p>\n<p>Example of the filtering of the necessary CO relevant balance adjustments and the necessary CO relevant line items (together) from ACDOCA for the <span>value type 04 and COSS</span>:</p>\n<p><strong>Data: </strong>lt_acdoca <strong>type table of </strong>acdoca.</p>\n<p>select * <strong>from </strong>acdoca <strong>as</strong> a</p>\n<p><strong>inner join</strong> finsc_ld_cmp <strong>as</strong> v <strong>on</strong> a<strong>~</strong>rbukrs <strong>=</strong> v<strong>~</strong>bukrs <strong>and</strong> a<strong>~</strong>rldnr <strong>=</strong> v<strong>~</strong>rldnr <strong>and </strong>v<strong>~</strong>versn <strong>= </strong>'000' \"Important: or table FINSC_CMP_VERSNC</p>\n<p><strong>inner join</strong> tj01 <strong>as</strong> t <strong>on</strong> t<strong>~</strong>vrgng <strong>=</strong> a<strong>~</strong>vrgng <strong>and</strong> t<strong>~</strong>xcosp <strong>= </strong>' ' <strong>and</strong> t<strong>~</strong>xcoss = 'X'</p>\n<p><strong>inner join</strong> tka01 <strong>as</strong> tk<strong> on</strong> tk~kokrs = a~kokrs</p>\n<p><strong>inner join</strong> t000 <strong>as</strong> s <strong>on</strong> ( s<strong>~</strong>logsys <strong>=</strong> a<strong>~</strong>logsyso <strong>or</strong> a<strong>~</strong>logsyso <strong>= </strong>' ' <strong>or</strong></p>\n<p><strong></strong><span><span>( </span></span><span><span>(</span></span><strong><strong><span><strong><span> </span></strong></span></strong><span></span></strong>a~accasty = 'KS' <strong>or</strong> a~accasty = 'KL' ) <strong>and</strong> ( s~logsys = tk~logsystem <strong>or</strong> tk~logsystem = ' ' ) )</p>\n<p>)</p>\n<p><strong>into corresponding fields of table @</strong>lt_acdoca</p>\n<p><strong><strong>where</strong></strong>(( a~co_belnr <strong><strong>ne </strong></strong>' '<strong><strong> and</strong></strong>a~co_buzei<strong> <strong>ne </strong></strong>'000'<strong> <strong>and not</strong></strong>a~accasty = ' ')<strong><strong> or </strong></strong></p>\n<p>( a<strong>~</strong>bstat <strong>= </strong>'C' <strong>and</strong> a<strong>~</strong>mig_source <strong>= </strong>'C' ))</p>\n<p><strong>and </strong>s~mandt = @sy-mandt.</p>\n<p> </p>\n<p>Tips for possible performance optimizations in the determination of CO-relevant data from ACDOCA in the form of the earlier totals structures:</p>\n<p>Sometimes, it is better to split the JOIN conditions with the tables T000, TKA01, TJ01, and FINSC_LD_CMP (or FINSC_CMP_VERSNC) into separate SELECTS and to use the results in the WHERE condition of the SELECT * FROM ACDOCA. Please check these options.</p>\n<ul>\n<li>Drag the selection of the T000-LOGSYS (with TKA01) in ABAP before the SELECT on ACDOCA and use LOGSYS in the WHERE condition of the SELECT on ACDOCA.</li>\n<li>If you must generate data for \"COSS\" and \"COSP\" but the distinction between primary and secondary costs is irrelevant, you can refrain from the JOIN with the table TJ01, which only controls which data (ACDOCA-VRGNG) belongs in COSP and in COSS.</li>\n<li>If you use just one or only a few company codes, you can place the determination of the CO-relevant ledger from FINSC_LD_CMP (or table FINSC_CMP_VERSNC) into a separate SELECT earlier on and specify the result in the WHERE condition of the SELECT * FROM ACDOCA.</li>\n</ul>\n<p> </p>\n<p><strong>Actual version</strong><strong>s &lt;&gt; '000' </strong>(as of SAP_FIN 730 and S4CORE 101)</p>\n<ul>\n<li>In principle, as of this release, the compatibility views also support actual versions &lt;&gt; '000'.</li>\n<li>In the table FINSC_LD_CMP, the version is no longer assigned to the ledger. This is now configured in the Customizing table FINSC_CMP_VERSNC. If you use the earlier table FINSC_LD_CMP for this purpose, a syntax error occurs. </li>\n<li>For data selections in Version \"000\", you can simply replace the previous table FINSC_LD_CMP with FINSC_CMP_VERSNC.</li>\n<li>For data selections with actual version &lt;&gt; '000', this does not suffice: In the table FINSC_CMP_VERSNC system, the assignment of ACDOCA value columns to the COSP, COSS, and COEP value columns is now also defined for the data in CO version &lt;&gt; '000’.</li>\n</ul>\n<p>Problem with full values in ACDOCA and earlier delta values in CO* tables:</p>\n<p>Note the following: Previously, the values in actual versions &lt;&gt; '000’ were saved in the former CO* tables as delta values (not full values). The actual full value in an actual version 'nnn' is always determined from the sum of the relevant database value in Version '000' and the database value in Version 'nnn'. This means that database values in the actual version 'nnn' must never be considered on their own.</p>\n<p>In the table ACDOCA, the data in different actual versions is always saved as a full value, either</p>\n<ul>\n<li>in the same ledger but in other value columns within ACDOCA</li>\n</ul>\n<p>or</p>\n<ul>\n<li>in separate ledgers (for each version).</li>\n</ul>\n<p>As mentioned above, the assignment of ACDOCA value columns to the COSP, COSS, and COEP value columns is configured in the Customizing table FINSC_CMP_VERSNC.</p>\n<p>The compatibility views use a complex logic for actual versions &lt;&gt; '000' to calculate delta values again - while using the Customizing table FINSC_CMP_VERSNC.</p>\n<p> </p>\n<p>For these reasons, we advise against calculating the CO delta values on the basis of the type of compatibility view:</p>\n<ul>\n<li>Instead, use the full values directly from ACDOCA while taking the Customizing table FINSC_CMP_VERSNC into consideration, which informs you which ACDOCA value column contains the appropriate value.</li>\n<li>Alternatively: SAP Note 2388871 also makes the access classes from SAP Note 2261720 available for actual versions &lt;&gt; '000'.</li>\n</ul>\n<p> </p>\n<p><span><strong>4th way: Alternative access classes</strong></span></p>\n<p>SAP Note <strong>2261720</strong> introduces new access classes that can be used as a replacement for the selection of COEP, COSS, and COSP. In some circumstances, this is easier than programming the corresponding SELECTS themselves. The advantage associated with using these access classes is that they output data in the structure COEP, COSS, and COSP. However, from a performance and technical perspective, it is still more favorable to make your specific selections directly in ACDOCA and avoid conversions to the structures COEP, COSS, and COSP. Of course, this requires that your programs forgo the structures COEP, COSS, and COSP.</p>\n<p>SAP Note 2388871 also makes the access classes from SAP Note 2261720 available for actual versions &lt;&gt; '000'.</p>", "noteVersion": 11}, {"note": "2219527", "noteTitle": "2219527 - Notes about using views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS in customer-defined programs in SAP S/4HANA Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In your customer-defined programs, you use the compatibility views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS to determine FI line items.</p>\n<p>The performance of the compatibility views is not sufficient.</p>\n<p>You want to optimize these accesses.</p>\n<p>You want to understand the data model behind the compatibility views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Compatibility views, SAP Simple Finance, SFIN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP_FIN 700 replaced the index tables BSID, BSAD, BSIK, BSAK, BSIS, and BSAS with views. The views are slower than the replaced tables (partly due to various factors).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Data model of compatibility views</strong></p>\n<p>In SAP S/4HANA Finance, the partially redundant tables BSID, BSAD, BSIK, BSAK, BSIS, and BSAS were replaced with views of the same name. This ensures that all programs that execute read accesses on BSID, BSAD, BSIK, BSAK, BSIS, and BSAS continue to work without any loss of function.</p>\n<p>The views obtain your data from the tables BKPF and BSEG. With SAP Note 2207950, a LEFT OUTER JOIN is used in the views instead of an INNER JOIN. Consequently, in the case of read accesses where only fields from the table BSEG are requested, the database does not have to execute the JOIN, thus improving performance.</p>\n<p>In addition, the views contain a UNION ALL with the relevant BCK table (for example, BSAD_BCK) for reading partially archived documents. In the case of BSID and BSIK, the UNION ALL is required for technical reasons only (so that the views in the ABAP Dictionary obtain the correct data elements). With SAP Note 2207950, the UNION ALL for the views BSIK and BSID is removed in SAP_FIN 730 (SAP S/4HANA Finance 1605) and S4CORE 100 (SAP S/4HANA on-Premise). This improves the performance of both views. In SAP_FIN 720 (SAP S/4HANA Finance 1503), the UNION ALL for the views BSIK or BSID cannot be removed because, at the very least, NetWeaver 750 is required.</p>\n<p><strong>Tips for using the compatibility views</strong></p>\n<ul>\n<li>In most cases, read accesses from the compatibility views can remain unchanged. If, however, the performance of your programs is worse than it was before the upgrade, note the following:</li>\n<li>You can rewrite the SELECTs directly on BSEG or BKPF/BSEG. This is useful in the following cases:</li>\n</ul>\n<ol><ol>\n<li>In SAP_FIN 720, the UNION ALL consumes additional runtime for the views BSID and BSIK. A direct SELECT on the join BSEG/BKPF is more efficient.</li>\n<li>You do not use archiving. Then, the UNION ALL with the BCK table is useless and consumes only runtime.</li>\n<li>You require data from the tables BSID and BSAD (or BSIK/BSAK).</li>\n</ol></ol>\n<ul>\n<li>Avoid a large number of small SELECTs. Preferably, all data should be read with a single access. To prevent a memory overflow in the case of large datasets, you can use the cursor technique (OPEN CURSOR ... FOR SELECT ...).</li>\n<li>Enter a field list. Avoid SELECT *. In particular, this applies if the field list is restricted to the fields in the table BSEG. For more information, see SAP Note 2207950.</li>\n</ul>\n<p><strong>Example</strong></p>\n<p>The attachment contains a code snippet that clarifies the tips described above.</p>", "noteVersion": 2}, {"note": "2261720", "noteTitle": "2261720 - Access classes for COEP, COSS, and COSP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have performance problems in customer-defined programs when accessing CO single records or totals records via K_XXX_READ_MULTI modules or compatibility views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, COEP, COSS, COSP, CL_FCO_COEP_READ, CL_FCO_COSP_READ, CL_FCO_COSS_READ, K_COSSA_READ_MULTI, K_COSPA_READ_MULTI, K_COEP_READ_MULTI</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement the SAP Note. A new interface is then available to you.</p>\n<p><strong><span>Explanation</span></strong></p>\n<p>The classes are suitable for data retrieval in customer programs, in particular if the K_xxx_READ_MULTI function modules have been used until now. They have the benefit that the tables are wrapped and the data fields conform to the CO conventions.</p>\n<p>The optimization in the classes works if you select exactly with one value type. In particular for the value types 04 and 11, extremely favorable accesses can be carried out in some cases. Despite this, it is important that the selection is as restricted as possible - ideally the period, too.</p>\n<p>There are three methods for the direct replacement of the aforementioned function modules: CL_FCO_COEP_READ=&gt;READ_COEP, CL_FCO_COSP_READ=&gt;READ_COSPA, and CL_FCO_COSS_READ=&gt;READ_COSSA. These methods are a little slower than the other methods (see below) but have the benefit of offering the same interface as the function modules.</p>\n<ol>\n<li>Class CL_FCO_COEP_READ</li>\n<ol>\n<li>GET_FILTER_FROM_COSEL: Converts a table from the line type COSEL to the filter structure of the type TY_FILTER as is required by the other methods.</li>\n<li>READ_COEP: Reading the data of the view COEP (with optimization for when just one value type is requested) - this method is compatible with the aforementioned function modules - requires more main memory.</li>\n<li>READ_04_AS_COEP: Reading the data of the view COEP for the value type 4, return in COEP format - requires more main memory</li>\n<li>READ_04_AS_COEPX: Reading the data of the view COEP for the value type 4, return in the internal format T_COEPXL - saves main memory</li>\n<li>READ_11_AS_COEP: Reading the data of the view COEP for the value type 11, return in COEP format</li>\n<li>READ_NN_AS_COEP: Reading the data of the view COEP for value types other than 4 and 11, return in COEP format</li>\n</ol>\n<li>Class CL_FCO_COSP_READ</li>\n<ol>\n<li>GET_FILTER_FROM_COSEL: Converts a table from the line type COSEL to the filter structure of the type TY_FILTER as is required by the other methods.</li>\n<li>READ_COSPA: Reading the data of the view COSP (with optimization for when just one value type is requested) - this method is compatible with the aforementioned function modules.</li>\n<li>READ_04_AS_COSPA: Reading the data of the view COSP for the value type 4, return in COSPA format - requires more main memory</li>\n<li>READ_04_AS_COEPX: Reading the data of the view COSP for the value type 4, return in TT_COEPX format - is significantly faster and requires less main memory</li>\n<li>READ_11_AS_COSPA: Reading the data of the view COSP for the value type 11, return in COSPA format</li>\n<li>READ_NN_AS_COSPA: Reading the data of the view COSP for value types other than 4 and 11, return in COSPA format</li>\n</ol>\n<li>Class CL_FCO_COSS_READ</li>\n<ol>\n<li>GET_FILTER_FROM_COSEL: Converts a table from the line type COSEL to the filter structure of the type TY_FILTER as is required by the other methods.</li>\n<li>READ_COSPA: Reading the data of the view COSP (with optimization for when just one value type is requested) - this method is compatible with the aforementioned function modules.</li>\n<li>READ_04_AS_COSPA: Reading the data of the view COSP for the value type 4, return in COSSA format - requires more main memory</li>\n<li>READ_04_AS_COEPX: Reading the data of the view COSP for the value type 4, return in TT_COEPX format - is significantly faster and requires less main memory</li>\n<li>READ_11_AS_COSPA: Reading the data of the view COSP for the value type 11, return in COSSA format</li>\n<li>READ_NN_AS_COSPA: Reading the data of the view COSP for value types other than 4 and 11, return in COSSA format</li>\n</ol></ol>\n<p><span><strong>Restrictions</strong></span></p>\n<ol>\n<li>Only version 000 is supported.</li>\n<li>Transfer prices and parallel valuation are not supported.</li>\n<li>Customer-defined fields in the view COEP are not supported.</li>\n<li>Packaging is not supported.</li>\n<li>The class CL_FCO_COEP_READ requires a lot of main memory for the value type 04. Restrict the selection to the required data records.</li>\n<li>The addition of certain data fields from the table COKEY using the field HRKFT is not always performed.</li>\n</ol>", "noteVersion": 3}, {"note": "2076652", "noteTitle": "2076652 - SAP Simple Finance: SAP Note for adjustment of customer-specific programs for aging", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have installed SAP Simple Finance and want to use SAP Data Aging for the FI document. To do so, you must adjust customer-specific programs or modifications.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, SAP simple Finance, data aging</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use SAP Data Aging for the FI document.</p>\n<p>You have implemented customer-specific accounting developments (in particular in the application components AC, FI, CO, and FIN) that were developed for SAP ERP 6.0.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Adjustments are required in the case of access to totals in FI and direct archive accesses.</p>\n<p><strong>1. Read accesses to totals in FI</strong></p>\n<p> The following FI totals tables have been replaced by views of the same name:</p>\n<p> GLT0, <PERSON><PERSON><PERSON><PERSON>XT, KNC1, KNC3, LFC1, LFC3</p>\n<p>These views calculate the total from the line items that might already be located in the \"cold\" area of the database and are not selected by default. For this reason, a control of the access to the cold area of the database must be implemented for accesses to the old data in customer-specific programs.</p>\n<p>To access the cold area of the database, you must first specify the temperature on the basis of the selection conditions and pass these to the database by means of a kernel call.</p>\n<p><strong>1.1 Determination of places to be adjusted</strong></p>\n<p>You can use the Code Inspector to find all affected parts of the code. Proceed as follows:</p>\n<ol>\n<li>Call transaction SCI and define a new variant. In the definition of the variant, set the indicator for searching for DB operations in the search functions and enter the list of affected objects (GLT0 and so on).</li>\n<li>Create an object set that contains all of your programs, classes, function groups, and so on.</li>\n<li> Create a new inspection and execute it. The result is a list of all database accesses to the totals tables and index tables removed in Smart Financials.</li>\n</ol>\n<p>Remember that generic database accesses that cannot be found using standard methods are possible too.</p>\n<p>Example: SELECT * FROM (lv_tabname) INTO TABLE lt_table.</p>\n<p><strong> 1.2 Determination of temperature</strong></p>\n<p>You can derive a lower estimation of the temperature from the selection conditions for the fiscal year. To take a deviating fiscal year into account, you can also include the ledger and company code (otherwise, the least favorable situation is assumed).</p>\n<p> To do so, call the static method GET_TEMPERATURE_DOCUMENT of the class CL_FINS_FI_AGING_UTILITY.</p>\n<p><strong> 1.3 Setting the temperature </strong></p>\n<p>Depending on the situation, you set the temperature on the basis of the class CL_ABAP_SESSION_TEMPERATURE or CL_ABAP_STACK_TEMPERATURE.</p>\n<p><strong> 1.3.1 Stack temperature</strong></p>\n<p>You use this method if you call a self-written routine in your program (form routine, function module, method...) in which the call of the earlier totals table is located. The set temperature is valid until you leave the routine and is then reset to the previous value.</p>\n<p>Call the static method SET_TEMPERATURE of the class CL_ABAP_STACK_TEMPERATURE and pass the temperature calculated in 1.2.</p>\n<p><strong> 1.3.2 Session temperature</strong></p>\n<p>The use of this method is required if you are not in a routine (that is, in the main program of a report). This method also makes sense if you want to set the temperature for the entire program directly after START-OF-SECLECTION on the basis of the selection conditions.</p>\n<p>First, obtain an instance of the class using the static method GET_SESSION_HANDLE of the class CL_ABAP_SESSION_TEMPERATURE. You are only allowed to call this method once in a session. You can then call the method SET_TEMPERATURE of the instance and pass the temperature calculated in 1.2.</p>\n<p><strong> 2. Archive accesses</strong></p>\n<p>With the FI document in SAP Simple Finance, archiving was replaced by aging; however, access to archived files is still supported. For this reason, old data can be located in the archive or in the cold area of the database. In all places where you previously accessed the archive, you must now access the cold area of the database. If you use standard SAP function modules for this, no adjustment is required.</p>\n<p>You can find the relevant source code sections by carrying out a text search:</p>\n<ul>\n<li>Name of the archiving object FI_DOCUMNT</li>\n<li>Name of the field catalog of the archiving object FI_DOCUMNT: You can find the defined field catalogs by calling transaction SARJ and choosing \"Environment -&gt; Field Catalogs\".</li>\n<li>Name of the archive information structures of the archiving object: Transaktion SARJ</li>\n</ul>\n<p>Alternatively, use a where-used list:</p>\n<ul>\n<li>Use access for the unction module AS_API_READ (based on the field catalogs)</li>\n<li>Use access for the function module AS_API_SYSTEM_SELECT (based on the archive index)</li>\n<li>Use access for the function module ARCHIVE_OPEN_FOR_READ (based on the archiving object)</li>\n</ul>\n<p>Note that there are other archive access possibilities (not recommended by SAP). A use access for the module ARCHIVE_READ_OBJECT or ARCHIVE_READ_OBJECT_BY_OFFSET gives a full picture but without the possibility of a simple filter for relevant places.</p>\n<p>Depending on the situation, both of the methods mentioned in 1.3 can be used: The previous SELECT on the database is adjusted at the start of the selections so that it also reads cold data OR - in addition to the archive access - you access the cold area by means of Open SQL and the same selection conditions, ignoring hot data. Depending on whether the SELECTs or archive access take place in the main program or in a routine (or depending on where you want to make the adjustment), you use CL_ABAP_SESSION_TEMPERATURE or CL_ABAP_STACK_TEMPERATURE. Particularly if you have written your own archiving read modules, it is probably most sensible to directly adjust these modules.</p>\n<p> To calculate the temperature, you use the method GET_TEMPERATURE_DOCUMENT of the class CL_FINS_AGING_UTILITY (as described in 1.2) if there is a selection condition for the fiscal year. If there is a condition for the posting date or clearing date, you can use this to determine the temperature more precisely (Note that a condition such as \"open items on the key date\" can be interpreted as a condition for the clearing date.) In particular, this is useful if there is no condition for the fiscal year. To do this, use the method GET_TEMPERATURE_DOCUMENT_MIX of the same class. If no condition exists, you can also use the method SET_COLD of one of the two kernel methods, with which you take all data into account. This should be avoided but is acceptable because the user has explicitly requested old data.</p>", "noteVersion": 2}, {"note": "2221298", "noteTitle": "2221298 - Notes about using views GLT0, FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, and JVGLFLEXT in custom programs in SAP S/4HANA Finance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In your custom programs, you use the compatibility views GLT0, FAGLFLEXT, FMG<PERSON>LEXT, PSGLFLEXT, and JVGLFLEXT to determine balances in the general ledger.</p>\n<p>The performance of the compatibility views is not sufficient.</p>\n<p>You want to optimize these accesses.</p>\n<p>You want to understand the data model behind the compatibility views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Compatibility views, SAP Simple Finance, SFIN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>With SAP_FIN 700, the totals tables FAGLFLEXT and GLT0 were replaced by views of the same name.</p>\n<p>In SAP_FIN 720, further totals tables were replaced in G/L by compatibility views:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Table</td>\n<td>View</td>\n</tr>\n<tr>\n<td>FMGLFLEXT </td>\n<td>FGLV_FMGLFLEXT</td>\n</tr>\n<tr>\n<td>PSGLFLEXT</td>\n<td>FGLV_PSGLFLEXT</td>\n</tr>\n<tr>\n<td>JVGLFLEXT</td>\n<td>FGLV_JVGLFLEXT</td>\n</tr>\n<tr>\n<td>ZZ&lt;CUST&gt;T</td>\n<td>ZFGLV_GLTT_C&lt;number&gt;</td>\n</tr>\n</tbody>\n</table></div>\n<p>The views are slower than the replaced tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Data model of compatibility views</strong></p>\n<p>The partially redundant totals tables GLT0, FAGLFLEXT, FMGLFLEXT, PSGLFLEXT, and JVGLFLEXT and custom totals tables in the general ledger were replaced by compatibility views in <em>SAP S/4HANA Finance</em>. This ensures that all programs that execute read accesses on the totals tables mostly continue to work without any drop in function.</p>\n<p>The fields OBJNR00, OBJNR01, and so on (the object number) are set to zero in the compatibility views for performance reasons. They do not correspond to the original content of the totals table.</p>\n<p>The views acquire their data from the journal entry table ACDOCA using a hierarchy of views. Here, for example, is the view FAGLFLEXT and underlying views:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Table/view</td>\n<td>Read from</td>\n<td>DDL source</td>\n</tr>\n<tr>\n<td>\n<p>View FAGLFLEXT</p>\n</td>\n<td>\n<p>FGLV_FAGLFLEXT</p>\n</td>\n<td>\n<p>V_FAGLFLEXT_DDL</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_FAGLFLEXT</p>\n</td>\n<td>\n<p>FGLV_GLTT2</p>\n</td>\n<td>\n<p>FGL_FAGLFLEXT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLTT2</p>\n</td>\n<td>\n<p>FGLV_GLTT1</p>\n</td>\n<td>\n<p>FGL_GLTT2</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLTT1</p>\n</td>\n<td>\n<p>FGLV_GLTT0</p>\n</td>\n<td>\n<p>FGL_GLTT1</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLTT0</p>\n</td>\n<td>\n<p>FGLV_GLSI_ACD</p>\n</td>\n<td>\n<p>FGL_GLTT0</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>View FGLV_GLSI_ACD</p>\n</td>\n<td>Tables ACDOCA,<br/>FINSC_LEDGER_REP,<br/>FINSC_LD_CMP</td>\n<td>FGL_GLSI_ACD</td>\n</tr>\n</tbody>\n</table></div>\n<p>The tables FINSC_LEDGER_REP and FINSC_LD_CMP reproduce the functions of the extension ledger.</p>\n<p>In SAP_FIN 720, the DDL source V_xxx_DDL (for example, V_FAGLFLEXT_DDL) also contains a UNION ALL with the corresponding BCK table (for example, FAGLFLEXT_BCK). This dummy UNION is required in SAP NetWeaver 740 to obtain the correct data elements in the compatibility view.</p>\n<p>The compatibility view FAGLFLEXT was already introduced in SAP_FIN 700. During the upgrade to SAP S/4HANA Finance. the table FAGLFLEXT is renamed as FAGLFLEXT_BCK, thus freeing the namespace for the view FAGLFLEXT. The table FAGLFLEXT_BCK is read during the migration and is then obsolete. In the same way, this applies to the view GLT0, too.</p>\n<p>In SAP_FIN 720, compatibility views were added for the other totals tables in the general ledger. These views have a different name (for example, the table FMGLFLEXT and the corresponding proxy view FGLV_FMGLFLEXT). Each Open SQL SELECT from ABAP is forwarded to the compatibility view (proxy view) in the database interface. The link from the table to the corresponding compatibility view (also called REDIRECT) is established during the data migration (IMG activity <em>Migration from SAP ERP to SAP Accounting powered by SAP HANA -&gt; Migration -&gt; Regenerate CDS Views and Field Mapping</em>).</p>\n<p>The view names for totals tables in the customer namespace are numbered in accordance with the pattern ZFGLV_GLTT_C&lt;number&gt;. The exact name is in the column VIEWREF of the table DD02L.</p>\n<p><strong>Tips for adjustment of custom programs</strong></p>\n<p>If you actively use SAP Data Aging for the FI document, read access adjustments are required. See SAP Note 2076652.</p>\n<p>Assignments using the group names PERIOD_DATA, TSL, HSL, KSL, OSL, MSL, and FIX must be reprogrammed. See SAP Note 1976487.</p>\n<p>The view hierarchies of the compatibility views are all structured similarly. The line items from the table ACDOCA are aggregated, the posting periods are switched from rows to columns (for example, TSL to TSLVT, TSL01, TSL02 etc.), and the column RPMAX is calculated. This transformation takes a relatively large amount of runtime, so we recommend that you select the data directly from the table ACDOCA. We therefore recommend that you read general ledger balances via the function module FINS_ACDOCA_BW_EXTRACT_DATA to achieve optimum performance when accessing the database.</p>\n<p>The compatibility views for the journal entry tables FAGLFLEXA, FMGLFLEXA, PSGLFLEXA, and JVGLFLEXA and for custom journal entry tables in the general ledger are, however, problem-free from a performance point of view and can still be used in custom programs.</p>\n<p><strong>Example</strong></p>\n<p>The attachment ZFINS_SEL_GLT0_FROM_ACDOCA.txt contains the example program ZFINS_SEL_GLT0_FROM_ACDOCA to illustrate the use of the function module FINS_ACDOCA_BW_EXTRACT_DATA to select ACDOCA data in the format of the table GLT0 (with period field POPER). Adjust this accordingly.</p>\n<p> </p>", "noteVersion": 6}, {"note": "2570011", "noteTitle": "2570011 - Further Performance Improvements New Read-API", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the new read API CL_FCO_COSP_READ or CL_FCO_COSS_READ and you experience long runtimes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>T811FLAGS, READ_OPTI, READ_OPTI_NO_CDS, RK_COMPARE_READ_APIS, CL_FCO_COSP_READ, CL_FCO_COSS_READ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement this SAP note.</p>\n<p> </p>", "noteVersion": 4}, {"note": "2314542", "noteTitle": "2314542 - Performance Optimization for K_COSPA_READ_MULTI and K_COSSA_READ_MULTI", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In some cases the performance of function modules K_COSPA_READ_MULTI and K_COSSA_READ_MULTI can be enhanced.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, READ_OPTI, CL_FCO_COSP_READ, CL_FCO_COSS_READ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>When you use Transfer Prices / Parallel Valuation you must implement 2388871 and 2570011 before turning on the optimized read. Otherwise you will receive wrong results.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement the following SAP notes: 2462432, 2432089, 2388871, 2261720, 2299870 and 2275731, as well as 2544417 and 2544926 (if valid for your release) and 2570011. This will add a read optimization for some cases.</p>\n<p>Please use transaction SE16 to add the following entry to table T811FLAGS: <br/>tab=KARS<br/>field=READ_OPTI<br/>valmin=X</p>\n<p>Please note the following restrictions:</p>\n<ol>\n<li>The optimization may change the order of datasets returned. This should not affect SAP programs.</li>\n<li>The optimization needs slightly more memory.</li>\n<li>The optimization does not work when you pass the T_HDB_FIELDS parameter.</li>\n</ol>\n<p>If the above topics cause problems, please delete the above entry from T811FLAGS.</p>\n<p>If you use Transfer Prices / Parallel Valuation you have to implement at least SAP notes 238871, 2544417 (S4CORE 101 only), 2544926 (others than S4CORE 101). It is strongly recommended to implement the latest SAP notes affecting the classes CL_FCO_READ_HELPER, CL_FCO_COSP_READ and CL_FCO_COSS_READ.</p>", "noteVersion": 9}]}], "activities": [{"Activity": "Business Decision", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Decision on the relevant Fiori apps to be used for reporting and planning using embeeded BPC or to reactivate old transactions"}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you want to reactivate, act according to steps mentioned in this simplification item"}, {"Activity": "Fiori Implementation", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "In case you want use Fiori tiles => choose them and configure them"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}