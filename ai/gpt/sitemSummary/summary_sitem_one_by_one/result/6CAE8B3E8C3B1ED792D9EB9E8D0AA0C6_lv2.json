{"guid": "6CAE8B3E8C3B1ED792D9EB9E8D0AA0C6", "sitemId": "BW131: InfoPackages (PSA)", "sitemTitle": "BW4SL - InfoPackages and Persistent Staging Areas", "note": 2464367, "noteTitle": "2464367 - BW4SL - InfoPackages and Persistent Staging Areas", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The persistent staging area service (PSA) and InfoPackages are not available in SAP BW/4HANA. Therefore, it is no longer possible to load data via InfoPackage.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ISIP, SHIP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available or can be converted to SAP BW/4HANA.</p>\n<p>See node: Manual Housekeeping --&gt; Delete PSA</p>\n<p>See node: Supported by Transfer Tool --&gt; 3.x Migration</p>\n<p>See node: Manual Redesign --&gt; BW Object Type</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In SAP BW/4HANA, a data transfer process (DTP) is the only object type to load data into the system. It can access all supported source system types directly without using a PSA. If the PSA has been used in SAP BW to distribute data to several target objects or for a sorted access to the data, or to have a SAP HANA transformation based on the data, then a write-optimized DataStore object (advanced) must be put in between the DataSource and the target DataStore object (advanced). Also, it is required to create an aDSO from the PSA if you have Full-InfoPackages with different filter conditions or file names to be extracted.</p>\n<p>The Transfer Tool will allow you to either skip the InfoPackage and create the DTP directly on the DataSource, or transfer the PSA to an DataStore object (advanced) and replace the InfoPackage by a DTP.  If no aDSO is created from the PSA, there must be only one Full-InfoPackage, which will then be merged into the Delta-DTP.</p>\n<p>If an ADSO is created from the PSA, then the different InfoPackages will then be transferred to different Full-DTP's, and Delta can be extracted from the PSA-aDSO using a Delta-DTP.</p>\n<ul>\n<li>The ID of the Full DTPI_s is generated by replacing the first 5 characters of the Info Package ID with the prefix 'DTPI_'.</li>\n<li>The conversion will also create one delta DTPI, for each data source which had delta (initialization) ISIPs that were executed and are still active. The ID following the prefix of the delta DTPI_...s is a hash generated from the name of the Data Source and the Source System, as they are before conversion. The selection conditions of this delta DTPI_ are computed in such a manner that they correspond to all active ISIP delta initializations. The DTP is generated with this selection condition as filter during meta data conversion, in an inplace transfer. In the case of the remote transfer, this filter setting is done during the request conversion of the target of the DTP.</li>\n</ul>\n<p>For <strong>hierarchy data sources</strong>, no ADSO can be generated to replace the PSA. If you have only one InfoPackage, then it's settings (hierarchy selection conditions) will be automatically transferred to the existing DTPs extracting from the data source. If you have more than one InfoPackage with different settings (hierarchy selection conditions), than you can proceed as follows:</p>\n<ul>\n<li>In any case please implement SAP Note '3254923 - P26:DTP:TT:Hierarchy from Ipak Dataflow' as a prerequisite for the next steps.<br/><br/></li>\n<li>in the case of an inplace transfer: please make sure that the latest version of note 3044301 is applied in the system. Thus the report <strong>RSB4H_CONV_HIER_ISIPS_TO_DTP</strong> will be present in your system. Please execute the report <strong>before </strong>you include the data source in the scope of a transfer tool. When executing the report, you will have to specify the data source name. Start by executing the report with the simulation flag on. It will list all the InfoPackages with their hierarchy selection conditions, and also the existing DTPs extracting from the (PSA) of the data source, together with the name of the InfoObjects for which the hierarchies are loaded. If you switch the simulation flag off, you have the possibility to create DTPs replacing the ISIPs, namely, per ISIP, one for each of the InfoObject(Hierarchies) already connected to the data source by older DTPs. You can also choose to create DTPs replacing the ISIPs, having as target the hierarchies of other InfoObjects, provided that a transformation from the data source, to the said hierarchy is previously created.</li>\n</ul>\n<p><span>The report offers you the possibility to navigate per double click, in the ISIP and DTP maintenance, and from there in the Process Chain maintenance. Please don't forget to adjust the process chains. DO NOT DELTE the ISIPs. Afterwards you can start the transfer tool run on the data source. Then the DTPs produced with the report will be converted only on the data source side, if necessary. </span></p>\n<ul>\n<li>in the case of a remote or shell transfer: please make sure that the latest version of note 3044301 is applied in the target system.  Please execute the report <strong>after </strong>the data source, transformations and InfoObjects were imported in the target system. The parameter needed in this case, is the data source and the logical system as they were created in the target system. The function of the report is the same, the only difference is that no display of the InfoPackage is directly possible (you can do it manually, in the sender system).</li>\n</ul>\n<p>The following InfoPackages cannot be handled by the Transfer Tool:</p>\n<ul>\n<li>InfoPackages for 3.x DataSources<br/>Migrate the 3.x DataSources and InfoPackage to 7.x DataSources (using transaction RSMIGRATE)</li>\n<li>InfoPackages for Read-time Data Acquisition<br/>See SAP Note <a href=\"/notes/2447916\" target=\"_blank\">2447916</a> BW4SL - Real-Time Data Acquisition (RDA)</li>\n<li>Push InfoPackage for Web Service Source Systems <br/>See SAP Note <a href=\"/notes/2441826\" target=\"_blank\">2441826</a> BW4SL - Web Service Source Systems</li>\n<li>Multiple Delta InfoPackages for File Source Systems which read different files for same DataSource (or have different routines to determine the filename)</li>\n</ul>\n<p>If a process variant for the deletion of old requests from PSAs is used, please note that there are restrictions on the corresponding process variant for the deletion of old requests from DataStore objects (advanced). See SAP Note <a href=\"/notes/2448086\" target=\"_blank\">2448086</a> for details.</p>\n<p><strong>Related Information</strong></p>\n<p>See SAP Note <a href=\"/notes/2464541\" target=\"_blank\">2464541</a> - BW4SL - Data Transfer Process / Error Stack</p>\n<p>See the documentation for more information:</p>\n<p><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.5.7/en-US/4403a25c0bbe025ce10000000a1553f7.html\" target=\"_blank\">InfoPackage --&gt; Data Transfer Process</a></p>\n<p><a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.5.7/en-US/14e01017-1047-488a-ad85-18d1e5bc5cfe.html\" target=\"_blank\">Persistent Staging Area (PSA)</a></p>\n<p><a href=\"https://help.sap.com/viewer/2e90b26cf7484203a523bf0f4b1bc137/7.5.7/en-US/4a2c746ac16d47dbe10000000a42189c.html\" target=\"_blank\">InfoPackage</a></p>\n<p><a href=\"https://uacp2.hana.ondemand.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/49996aca00002774e10000000a42189b.html\" target=\"_blank\">Data Transfer Process</a> in SAP BW/4HANA</p>\n<p><a href=\"https://uacp2.hana.ondemand.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/aefee5c9097e4b1480ef3d0df80dc336.html\" target=\"_blank\">Working with Source Systems</a> in SAP BW/4HANA</p>", "noteVersion": 12, "refer_note": [{"note": "2441826", "noteTitle": "2441826 - BW4SL & BWbridgeSL - Web Service Source Systems", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the source system of type \"Web Service\" and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LSYS, RSDS, XML, SOAP, OData, CPI, SAP NetWeaver PI</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_RC to determine which objects are supported with or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<p>See node: Automated Cleanup --&gt; Deletions</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>Target SAP BW/4HANA:</span></strong></p>\n<p>Source system of type \"Web Service\" are not available in SAP BW/4HANA. With SAP BW/4HANA 2.0, there is a new capability \"Write Interface\" for DataStore Objects allowing to push data into inbound queue tables of Staging DataStore Objects and Standard DataStore Objects. This capability replaces the push capability of PSA tables of DataSources of Web Service Source Systems.</p>\n<p>SAP Cloud Platform Integration and SAP NetWeaver Process Integration equal or higher SAP NetWeaver 7.50 Process Integration SP15 (see SAP Note <a href=\"/notes/2757524\" target=\"_blank\">2757524</a>) can use this new http (REST) interface for data integration to SAP BW/4HANA 2.0. Please find more information in the documentation <a href=\"https://help.sap.com/viewer/DRAFT/107a6e8a38b74ede94c833ca3b7b6f51/2.0.0/en-US/8127edbcf2fa488ba04dfc1751d5261d.html\" target=\"_blank\">DataStore Object with Write Interface</a>, in the <a href=\"https://www.sap.com/documents/2019/02/b079bb3f-3a7d-0010-87a3-c30de2ffd8ff.html\" target=\"_blank\">What's new with SAP BW/4HANA 2.0</a> (page 24) and in the <a href=\"https://www.sap.com/community/topic/bw4hana.html\" target=\"_blank\">SAP BW/4HANA Community</a>.</p>\n<p>Please note that there is no full automated conversion path from DataSources of Web Service Source Systems in SAP BW to DataStore Objects with \"Write Interface\" in SAP BW/4HANA 2.0.</p>\n<p>For a manual conversion, we recommend the following approach:</p>\n<ul>\n<li>In SAP BW, create a Staging or Standard DataStore Object with an existing Web Service Data Source as template. Integrate this new object as additional source object to the data flows on top of the respective Web Service DataSource. Replicate transformation logic if necessary.</li>\n<li>Before conversion, please make sure that all data from the PSA table of your Web Service DataSource is distributed to further objects along that data flow.</li>\n<li>In conversion, any Web Service Source Systems left, when executing the switch to \"Ready for conversion mode\", will be automatically deleted.</li>\n<li>After conversion to SAP BW/4HANA 2.0, please add the property \"Write Interface enabled\" to the new DataStore Object and activate it.</li>\n<li>After activation, the URIs to be used in SAP Cloud Platform Integration or SAP NetWeaver PI for sending data to SAP BW/4HANA are displayed in the properties section of the DataStore Object editor in the BW Modeling Tools under\"Template URIs\".</li>\n</ul>\n<p>For a semi automated conversion, we recommend the following approach:</p>\n<ul>\n<li>In SAP BW, execute task list SAP_BW4_TRANSFER_INPLACE with the Web Service DataSource as starting object.</li>\n<li>The task list will create a standard ADSO as replacement for the Web Service DataSource</li>\n<li>After conversion to SAP BW/4HANA 2.0, please add the property \"Write Interface enabled\" to the new DataStore Object and activate it.</li>\n<li>After activation, the URIs to be used in SAP Cloud Platform Integration or SAP NetWeaver PI for sending data to SAP BW/4HANA are displayed in the properties section of the DataStore Object editor in the BW Modeling Tools under\"Template URIs\".</li>\n<li><strong>Please note, if you use this approach, you can NOT use this scenario anymore until you are on SAP BW/4 and the after conversion steps are done for the write interface ADSO.</strong></li>\n</ul>\n<p>In a Remote- or Shell-Transfer:</p>\n<ul>\n<li>In SAP BW, execute task list SAP_BW4_TRANSFER_REMOTE_PREPARE/SHELL with the Web Service DataSource as starting object.</li>\n<li>In a SAP BW/4HANA 2.0, the transfer will create an ADSO with the property \"Write Interface enabled\" for the Web Service DataSource.</li>\n<ul>\n<li>After activation, the URIs to be used in SAP Cloud Platform Integration or SAP NetWeaver PI for sending data to SAP BW/4HANA are displayed in the properties section of the DataStore Object editor in the BW Modeling Tools under\"Template URIs\".</li>\n</ul>\n<li>In a SAP BW/4HANA 1.0, the transfer will create a standard ADSO as replacement for the Web Service DataSource</li>\n</ul>\n<p>As the \"Write Interface\" capability is not available in SAP BW/4HANA 1.0 the following alternatives apply:</p>\n<ul>\n<li>the <a href=\"https://help.sap.com/viewer/7952ef28a6914997abc01745fef1b607/2.0_SPS02/en-US/d9f0a3b09e0f4b3eb010be8bd36871e5.html\" target=\"_blank\">SAP HANA Smart Data Integration (SDI) OData Adapter</a> and the SAP HANA Source System in SAP BW/4HANA to connect to OData APIs in case of a cloud application integration scenario. Please check, if the cloud application provides an OData API for data extraction of the business data you want to integrate to SAP BW/4HANA.</li>\n<li>the <a href=\"https://help.sap.com/viewer/7952ef28a6914997abc01745fef1b607/2.0_SPS02/en-US/c172e4285b884e0f96be9cd03f3bc3af.html\" target=\"_blank\">SAP HANA Smart Data Integration (SDI) SOAP Adapter</a> to load data from SOAP based web services into native SAP HANA tables residing on a local HANA schema of your SAP BW/4HANA instance. As of technical restrictions, the SOAP Adapter can't get directly used within the SAP HANA Source System provided in SAP BW/4HANA and has to be consumed using HANA native data provisioning capabilities.</li>\n<li>SAP NetWeaver Process Integration or SAP Cloud Platform Integration to write data from a web service into native SAP HANA tables residing on a local HANA schema of your SAP BW/4HANA instance.</li>\n</ul>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/saphelp_nw75/helpdata/en/4a/1dfbbe5c171b40e10000000a42189c/frameset.htm\" target=\"_blank\">Data Transfer Using Web Services</a></li>\n</ul>\n<p> </p>\n<p><span><strong><span>Target SAP Datasphere, SAP BW bridge:</span></strong></span></p>\n<p><span>Source system of type \"Web Service\" are not available in SAP Datasphere, SAP BW bridge. With SAP Datasphere, SAP BW bridge, there is a new capability \"Write Interface\" for DataStore Objects allowing to push data into inbound queue tables of Staging DataStore Objects and Standard DataStore Objects. This capability replaces the push capability of PSA tables of DataSources of Web Service Source Systems.</span></p>\n<p>SAP Cloud Platform Integration and SAP NetWeaver Process Integration equal or higher SAP NetWeaver 7.50 Process Integration SP15 (see SAP Note <a href=\"/notes/2757524\" target=\"_blank\">2757524</a>) can use this new http (REST) interface for data integration to SAP Datasphere, SAP BW bridge. Please find more information in the documentation <a href=\"https://help.sap.com/docs/SAP_BW_BRIDGE/107a6e8a38b74ede94c833ca3b7b6f51/8127edbcf2fa488ba04dfc1751d5261d.html\" target=\"_blank\">DataStore Object with Write Interface</a>.</p>\n<p>Please note that there is no full automated conversion path from DataSources of Web Service Source Systems in SAP BW to DataStore Objects with \"Write Interface\" in SAP Datasphere, SAP BW bridge.</p>\n<p>For a Remote- or Shell-Transfer:</p>\n<ul>\n<li>Follow the prerequisites and set up the communication arrangement to use DataStore Objects with write interface-enabled in SAP Datasphere, SAP BW bridge. (<a href=\"https://help.sap.com/docs/SAP_BW_BRIDGE/107a6e8a38b74ede94c833ca3b7b6f51/2ccfa14e91404572ad039251920e890e.html\" target=\"_blank\">DataStore Objects with write interface-enabled in SAP Datasphere, SAP BW bridge</a>)</li>\n<li>In SAP BW, execute task list SAP_BW4_TRANSFER_CLOUD_REMOTE/SHELL with the Web Service DataSource as starting object.</li>\n<li>In a Datasphere, SAP BW bridge, the transfer will create an aDSO with the property \"Write Interface enabled\" for the Web Service DataSource.</li>\n<ul>\n<li>After activation, the URIs to be used in SAP Cloud Platform Integration or SAP NetWeaver PI for sending data to SAP Datasphere, SAP BW bridge are display in the properties section of the DataStore Object editor in the BW Modeling Tools under \"Template URIs\".</li>\n</ul>\n</ul>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/saphelp_nw75/helpdata/en/4a/1dfbbe5c171b40e10000000a42189c/frameset.htm\" target=\"_blank\">Data Transfer Using Web Services</a></li>\n<li><a href=\"https://help.sap.com/docs/SAP_BW_BRIDGE/107a6e8a38b74ede94c833ca3b7b6f51/8127edbcf2fa488ba04dfc1751d5261d.html\" target=\"_blank\">DataStore Object with Write Interface</a></li>\n<li><a href=\"https://help.sap.com/docs/SAP_BW_BRIDGE/107a6e8a38b74ede94c833ca3b7b6f51/2ccfa14e91404572ad039251920e890e.html\" target=\"_blank\">Use DataStore Object with Write Interface in SAP Datasphere, SAP BW bridge</a></li>\n</ul>\n<p> </p>\n<p><span><strong>Additional Information regarding RSPM handling for Write Interface DataStore Objects</strong></span></p>\n<p>The concept of writing into an open Request which is automatically maintained by the BW System does not exist anymore.</p>\n<p>(There are no threshold values, e.g. for the package size maintainable and no daemon exists. However, it is possible to have several open RSPM Request and even Push and DTP in parallel, so there is no limitation to just one \"Push Request\" anyway).</p>\n<p>Instead there are these 2 loading scenario supported via the Write Interface:</p>\n<ol>\n<li>Each data call results in a RSPM Request (\"One-step\"), i.e. the BW opens and closes an RSPM Request internally around the transferred records. The throughput is limited in this case and also the degree of parallel loading into the same target is limited.</li>\n<li>The external system adminstrates the RSPM Request explicitely and sends Data Packages into this Request (\"Write into Request\"): in this case the external system opens the RSPM Request and is also responsible for closing it after sending all Data Packages (e.g. after the source has \"no more data\" for the time being or periodically). Parallel loading of Data Packages is possible though.</li>\n</ol>\n<p><span> </span></p>", "noteVersion": 20}, {"note": "2464541", "noteTitle": "2464541 - BW4SL - Data Transfer Processes", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Please note the following limitations on Data Transfer Processes (DTP) in SAP BW/4HANA:</p>\n<ol>\n<li>The persistent staging area service (PSA) is no longer available in SAP BW/4HANA. As of that, the following limitations apply:</li>\n<ol>\n<li>Data Transfer Processes that use error handling are not available (no error stacks, no error stack maintenance and no error DTPs)</li>\n<li>No distribution of data extracted from source systems to the PSA and from there to several target objects (extract once – deploy many)</li>\n<li>Potential for out of memory situations when using the DTP feature \"Semantic Groups\" (semantically grouped extraction of data) in combination with ODP DataSources as source objects (the same limitation applies in SAP BW when using the DTP data extraction setting \"Directly from source system. PSA not used\")</li>\n<li>\"SAP HANA Execution\" is not possible for DTPs and Transformations that use ODP DataSources as source objects (the same limitation applies in SAP BW when using the DTP data extraction setting \"Directly from source system. PSA not used\")</li>\n</ol>\n<li>DTPs of type \"Real-time Data Acquisition\" or \"Direct Access\" are not available in SAP BW/4HANA.</li>\n<li>DTPs using error handling and SAP HANA runtime for transformations are not available in SAP BW/4HANA 1.0</li>\n<li>DTPs are loading data between SEM-BCS objects and SAP BW objects.</li>\n<li>DTP form based routines that directly modify TABLES parameter without a workarea cannot be transferred to BW/4 HANA Cloud as they cannot be migrated to Class based routines.</li>\n<li>DTP filters based on BEx Variables are not supported in BW/4 HANA Cloud.</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>DTPA, DTPD</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available or can be converted to SAP BW/4HANA.</p>\n<p>See node: Manual Housekeeping --&gt; Clean-up Error Stacks</p>\n<p>See node: Manual Redesign --&gt; BW Object Type</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For 1 a): Since SAP BW/4HANA Support Package 04 the new technology \"Data Transfer Intermediate Storage (DTIS)\" is provided, which covers the DTP error stack functionality (excluding the maintenance of the error stack – see the SAP Knowledge Base Article <a href=\"/notes/2494151/E\" target=\"_blank\">2494151</a> – “Display and Edit records of Error DTP is not available (BW/4HANA)”).</p>\n<p>As no transfer of open error requests into the new DTIS technology is possible, the current error stack tables must be cleared prior to the system conversion, either by deleting the corresponding requests or by processing them with an error DTP.</p>\n<p>In addition, the system checks for error DTPs, which have no standard DTP or a standard DTP, which has error handling switched off. These are marked to be deleted in the transfer tool and removed automatically.</p>\n<p>For 1 b): Currently, an Advanced DataStore Object (ADSO) must be used as replacement for the Persistent Staging Area (PSA) to allow for “extract once – deploy many” scenarios. The SAP BW/4HANA conversion tools do support this conversion step (See SAP Note <a href=\"/notes/2464367\" target=\"_blank\">2464367</a> – “BW4SL - InfoPackages and Persistent Staging Areas)”.</p>\n<p>For 1 c): With SAP Note <a href=\"/notes/2497506\" target=\"_blank\">2497506</a> – “DTP load from ODP source with semantic grouping” packaging of data is introduced for DTPs based on ODP DataSources that use “Semantic Groups”. This correction will eliminate out of memory situations on source system level. Memory shortages may however still occur in the SAP BW/4HANA target system.</p>\n<p>Alternatively, an Advanced DataStore Object (ADSO) can be used as source object for DTPs that use “Semantic Groups” and would hence replace the Persistent Staging Area (PSA). The SAP BW/4HANA conversion tools do support this conversion step (See SAP Note <a href=\"/notes/2464367\" target=\"_blank\">2464367</a> – “BW4SL - InfoPackages and Persistent Staging Areas”).</p>\n<p>For 1 d): Currently, an Advanced DataStore Object (ADSO) must be used as replacement for the Persistent Staging Area (PSA) to allow for “SAP HANA Execution” on top of data from ODP DataSources. The SAP BW/4HANA conversion tools do support this conversion step (See SAP Note <a href=\"/notes/2464367\" target=\"_blank\">2464367</a> – “BW4SL - InfoPackages and Persistent Staging Areas”).</p>\n<p>For 2): DTPs for \"Real-time Data Acquisition\" and \"Direct Access\" need to be redesigned. It's recommended to use streaming process chains and Open ODS Views instead. See also See SAP Note <a href=\"/notes/2447916\" target=\"_blank\">2447916</a> – “BW4SL - Real-time Data Acquisition (RDA)” for further details.</p>\n<p>For 3): It's not possible to convert such DTPs automatically for SAP BW/4HANA 1.0. We plan to support DTPs using error handling and SAP HANA runtime for transformations in SAP BW/4HANA 2.0. For details, see SAP Note <a href=\"/notes/2580109\" target=\"_blank\">2580109</a>.</p>\n<p>For 4): Please check note <a href=\"/notes/3018387\" target=\"_blank\">3018387</a>.</p>\n<p>For 5): Please change the field routines to modify/append Tables parameter \"l_t_range\" using a work area.</p>\n<p>For 6): Please use routines instead of BEx variables.</p>\n<p><strong>Related Information</strong></p>\n<p>SAP Note <a href=\"/notes/2464367\" target=\"_blank\">2464367</a> - BW4SL - InfoPackages and Persistent Staging Areas</p>\n<p>SAP Note <a href=\"/notes/2447916\" target=\"_blank\">2447916</a> - BW4SL - Real-time Data Acquisition (RDA)</p>\n<p>SAP Knowledge Base Article <a href=\"/notes/2494151/E\" target=\"_blank\">2494151</a> – Display and Edit records of Error DTP is not available (BW/4HANA)</p>\n<p>SAP Help Documentation: <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/ac029de05e164a12ac1ce08d16180f05.html\" target=\"_blank\">Process Chains for Streaming</a></p>", "noteVersion": 13, "refer_note": [{"note": "2494151", "noteTitle": "2494151 - Display and Edit records of Error DTP is not available (BW/4HANA)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Error stack of DTP showing records, but displaying and editing is not available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Environment\">Environment</h3>\n<ul>\n<li>SAP BW/4HANA 1.0</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\n<p>1. Open DTP and its showing available records in error stack.</p>\n<p></p>\n<p>2. Click on Display Error Stack (F7)</p>\n<p> - Case 1: you get a message: \"List does not contain any data\"</p>\n<p></p>\n<p>Follow solution 1</p>\n<p>- Case 2: you get no message</p>\n<p></p>\n<p>Follow solution 2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Cause\">Cause</h3>\n<p>Displaying and editing of records in the error DTP is not yet available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Resolution\">Resolution</h3>\n<p>Solution 1:</p>\n<p>As workaround find the generated DTIS table via tables RSDTIS and RSDTISREQUEST. <br/>The name of the table is in column TABNAME.</p>\n<p>Afterwards you can check the records directly in SE16 for this generated table.</p>\n<p>Additional information:<br/>Use an Error-DTP to try the records in the error stack later again. <br/>One typical scenario is that some records fail in the normal DTP because of missing master data. <br/>Once the master data are up to date you retry these records with the Error-DTP.</p>\n<p>We strongly recommend to update to BW/4HANA 1.0 SP8 as this provides standard functionality for editing data records in the error stack. Please see the following documentation links for more information:</p>\n<p><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.8/en-US/42fa1acfcf2c1aa2e10000000a422035.html\" target=\"_blank\">Data Transfer Intermediate Storage (DTIS)</a> <br/><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.latest/en-US/035b81dc2edf4ab3b75a481090596010.html\" target=\"_blank\">Specifying How Data Records Are Handled</a></p>\n<p>Solution 2:</p>\n<ul>\n<li>BW/4HANA 1.0 SP09 and smaller: Authorization object \"S_RS_DTP - Data Warehouse Modeling - Data Transfer Process\" needs to be altered. Permitted activities must be set to “Full authorization”.</li>\n</ul>\n<p>        </p>\n<ul>\n<li>BW/4HANA 1.0 SP010: Authorization object \"S_RS_DTP - Data Warehouse Modeling - Data Transfer Process needs\" to be altered. Within permitted activities “36 - Extended maintenance” must be added.</li>\n</ul>\n<p><span>        </span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Keywords\">Keywords</h3>\n<p>Business Warehouse, BW/4HANA , error stack</p>", "noteVersion": 6}, {"note": "2497506", "noteTitle": "2497506 - DTP load from ODP source with semantic grouping", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A DTP load from ODP source with semantic grouping must extract all data in one DTP package. This is necessary because ODP does not support sorting of results. But in some situations the ODP source ignores this requirement and returns more than one data package.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ODP DTP error handler</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li><strong><span>SAP BW 7.40</span></strong><br/>Implement Support Package 18 for SAP BW 7.40 (SAPKW74018) into your BW system. The Support Package will be available as soon as <strong>SAP Note </strong><strong>2445563 </strong>with the short text \"SAPBWNews 7.40 BW ABAP SP18\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<ul>\n<li><strong><span>SAP BW 7.50</span></strong><br/>Implement Support Package 9 for SAP BW 7.50 (SAPK-75009INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2467627 </strong>with the short text \"SAPBWNews 7.50 BW ABAP SP9\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<ul>\n<li><span><strong>SAP BW 7.51</strong></span><br/>Implement Support Package 4 for SAP BW 7.51 (SAPK-75104INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2467673 </strong>with the short text \"SAPBWNews 7.51 BW ABAP SP4\", which describes this Support Package in more detail, is released for customers.</li>\n<li><span><strong>SAP BW 7.52</strong></span><br/>Implement Support Package 1 for SAP BW 7.52 (SAPK-75201INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2487153 </strong>with the short text \"SAPBWNews 7.52 BW ABAP SP1\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<ul>\n<li><span><strong>SAP BW/4HANA 1.0</strong></span><br/>Implement Support Package 6 for SAP BW/4HANA 1.0 (SAPK-10006INDW4CORE) into your SAP BW/4HANA system. The Support Package will be available as soon as <strong>SAP Note 2491835</strong><strong> </strong>with the short text \"SAPBWNews SAP BW/4HANA 1.0 SP06\", which describes this Support Package in more detail, is released for customers. </li>\n</ul>\n<p>In urgent cases you can use the correction instructions.</p>\n<p>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882 and SAP Note 2248091</strong> for transaction SNOTE.</p>\n<p>This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".</p>", "noteVersion": 2}, {"note": "3018387", "noteTitle": "3018387 - SAP BW/4 Inplace Conversion with dataloads between BCS and BW objects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In the Pre-Check report or during the mode change from \"Compatibility mode\" to \"B4H\" mode you get messages like:</p>\n<p>\"Request must be transfered to RSPM (Target/Source only).</p>\n<p>The target or the source is BCS object like CUBE, DSO,..</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span>Inplace, Conversion, SEM BCS, BCS4HANA, BW/4 conversion</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>BCS conversion are normaly done during the mode change from \"B4H\" to \"Ready4Conversion\". Please find some information in note <a href=\"/notes/2651241\" target=\"_blank\">2651241</a>. But in your scenario, you are using BCS objects as source or target for other BW objects like Cubes, DSOs.</p>\n<p>A mode change to \"B4H\" isn't possible in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Here are 2 solutions possible to solve the problem and change the mode to \"B4H\"</p>\n<ol>\n<li>If possible, you can delete the TRFNs and DTPs between BCS objects and BW objects. Then it is possible to change the mode to \"B4H\" and during the mode change to \"Ready4Conversion\", the BCS stuff will be converted.</li>\n<li>If it is not possible to delete the TRFNs and DTPs between BCS objects and BW objects, than you can execute the tasklist \"SAP_BW4_TRANSFER_SEM\" in transaction STC01. Please select the BCS objects like Cube, DSO, Multiprovider and execute the list to transfer the BCS objects. Please note, BCS reporting isn't possible afterwards until the system is technical converted to SAP BW/4.</li>\n</ol>", "noteVersion": 2}, {"note": "2580109", "noteTitle": "2580109 - Error handling in BW transformations and data transfer processes in SAP HANA runtime", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Incorrect records should be filtered out instead of being written to the data target. These records should be corrected manually or automatically using routines or should be written to the data target again later on once the prerequisites for a valid update are again met.</p>\n<p>This SAP note describes the general procedure for dealing with incorrect records as well as providing general information about the correct modeling of error handling situations. In addition, differences between the SAP HANA and the ABAP runtime are explained.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Error handling, error, error records, incorrect records, transformation, DTP, error DTP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The handling of incorrect records should be modeled in the BW system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>SAP Business Warehouse (SAP BW) 7.x and </span></strong><span><strong>SAP BW/4HANA</strong></span></p>\n<p>There are essentially two modeling approaches for the handling of incorrect data records. In one scenario, you benefit from certain automatic processes that are made available by the BW system but that do not allow flexible further processing. In a second scenario, more modeling work is required, but you can work flexibly with incorrect records. In particular, you can use routines to implement automatic corrections or use a separate user interface to enable flexible change possibilities and checks.</p>\n<p><strong>Note the following: </strong></p>\n<p><strong>Use scenario 1 in exceptional situations only in BW 7.x. In the case of complex scenarios, the performance restrictions mentioned below can occur in the SAP HANA runtime. Problems can also occur with regard to source record determination. In general, scenario 2 is therefore recommended. Scenario 1 can be used for the SAP HANA runtime only if extensive tests have been carried out first to verify performance and the correct determination of the error source records. </strong></p>\n<p><strong>With BW/4HANA 1.0, scenario 1 is not supported for the SAP HANA runtime. The error handling cannot be used for the SAP HANA runtime.</strong></p>\n<p><strong>With BW/4HANA 2.0, the error handling has been fundamentally revised for the SAP HANA runtime in order to ensure improved stability in complex scenarios. This means that the correct source record determination can be ensured with scenario 1, too. However, even in BW/4HANA 2.0, the performance must be checked for each data flow for the reasons specified below before the scenario can be used productively.</strong></p>\n<p><span>Scenario 1: You use DTP error handling for temporary errors or manual error correction in the error stack.</span></p>\n<p>In this scenario, the DTP error handler is used.</p>\n<p>DTP error handling is described in the <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.7/de-DE/42fbd598481e1a61e10000000a422035.html\" target=\"_blank\">Handling Data Records with Errors</a> section of the SAP BW/4HANA documentation.</p>\n<p>In the DTP, fields that act as keys for error handling are defined in the 'Extraction grouped by' (BW/4HANA) or 'Semantic Grouping' (BW 7.50) area. At runtime, the incorrect records are flagged in the transformations and are written to the error stack. All other records that have the same key values as a record in the error stack are also written to the error stack. Only the correct data records are updated. You can manually correct the incorrect records or update them again after a certain amount of time. For example, data records are classified as incorrect because the referential integrity of master data is violated since the master data has not yet updated in the system. Once the master data load has been completed successfully, you can update the incorrect records without problems.</p>\n<p><span>Scenario 2: Automatic correction of error records/own user interfaces for manual error correction</span></p>\n<p>This scenario fundamentally differs from the first scenario with regard to its modeling. The focus is here on the flexible further processing of the incorrect records.</p>\n<p>A field is included in the target DataStore object, allowing a record to be selected as incorrect in the transformation using a routine, for example. It might also be possible to enter the type of error there in order to simplify further processing of the incorrect record. This means that, in principle, all records are updated to the target object; however, the incorrect records are flagged. This initially also enables reporting for the incorrect records, which can be added or removed by means of the selection of the error field in the query.</p>\n<p>To further process the incorrect records, you can use a transformation that writes to the target object itself and makes automatic error corrections. Alternatively, the data can be written to another DataStore object (for example, a direct access DataStore object) in order to enable the processing of the data with a separate UI and the provided DataStore APIs. From this DataStore object, the corrected data can then be written back to the actual DataStore object or processed elsewhere. (APIs for reading/writing: RSDRI_INFOPROV_READ/RSDSO_DU_WRITE_API)</p>\n<p><span>Use of DTP error handling for transformation on basis of SAP HANA runtime</span></p>\n<p>If error handling is to be used for the SAP HANA runtime and SAP HANA transformations, you must take into account the following points:</p>\n<p>Generally, major performance restrictions should be expected if error handling is to be used in the SAP HANA runtime. This can result in a longer loading time in some cases than with the ABAP runtime:</p>\n<ul>\n<li>The SAP HANA runtime must be executed at least twice to update the incorrect records separately from the correct records.</li>\n<li><strong>For each single field routine in the SAP HANA script, the entire transformation with all data must be executed an additional time. This means that a runtime extension with a factor of n+2 occurs for n used routines. For this reason, you should always avoid using single field routines via SAP HANA script in transformations of this kind. In any case, use an end routine or use ABAP routines and thus the ABAP runtime.</strong></li>\n<li>In general, scenario 2 is to be recommended. The use of scenario 1 must be tested extremely carefully, particularly with regard to the number of data records and whether it is possible to determine the correct source records for the target records in the error message transformation. If not, scenario 2 must be used.</li>\n</ul>\n<p>Note: When you use error handling for SAP HANA runtime and SAP HANA transformations and certain errors occur, namely</p>\n<ul>\n<li>missing master data (there are characteristics in the loaded data for which no master data exists),</li>\n<li>errors during time consistency check,</li>\n<li>attempts to write to an archived partition,</li>\n</ul>\n<p>the incorrect data records are not updated to the error stack, but are updated together with all other data records in the data target, even though the request is set to error status due to these incorrect records.</p>", "noteVersion": 10}, {"note": "2464367", "noteTitle": "2464367 - BW4SL - InfoPackages and Persistent Staging Areas", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The persistent staging area service (PSA) and InfoPackages are not available in SAP BW/4HANA. Therefore, it is no longer possible to load data via InfoPackage.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ISIP, SHIP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available or can be converted to SAP BW/4HANA.</p>\n<p>See node: Manual Housekeeping --&gt; Delete PSA</p>\n<p>See node: Supported by Transfer Tool --&gt; 3.x Migration</p>\n<p>See node: Manual Redesign --&gt; BW Object Type</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In SAP BW/4HANA, a data transfer process (DTP) is the only object type to load data into the system. It can access all supported source system types directly without using a PSA. If the PSA has been used in SAP BW to distribute data to several target objects or for a sorted access to the data, or to have a SAP HANA transformation based on the data, then a write-optimized DataStore object (advanced) must be put in between the DataSource and the target DataStore object (advanced). Also, it is required to create an aDSO from the PSA if you have Full-InfoPackages with different filter conditions or file names to be extracted.</p>\n<p>The Transfer Tool will allow you to either skip the InfoPackage and create the DTP directly on the DataSource, or transfer the PSA to an DataStore object (advanced) and replace the InfoPackage by a DTP.  If no aDSO is created from the PSA, there must be only one Full-InfoPackage, which will then be merged into the Delta-DTP.</p>\n<p>If an ADSO is created from the PSA, then the different InfoPackages will then be transferred to different Full-DTP's, and Delta can be extracted from the PSA-aDSO using a Delta-DTP.</p>\n<ul>\n<li>The ID of the Full DTPI_s is generated by replacing the first 5 characters of the Info Package ID with the prefix 'DTPI_'.</li>\n<li>The conversion will also create one delta DTPI, for each data source which had delta (initialization) ISIPs that were executed and are still active. The ID following the prefix of the delta DTPI_...s is a hash generated from the name of the Data Source and the Source System, as they are before conversion. The selection conditions of this delta DTPI_ are computed in such a manner that they correspond to all active ISIP delta initializations. The DTP is generated with this selection condition as filter during meta data conversion, in an inplace transfer. In the case of the remote transfer, this filter setting is done during the request conversion of the target of the DTP.</li>\n</ul>\n<p>For <strong>hierarchy data sources</strong>, no ADSO can be generated to replace the PSA. If you have only one InfoPackage, then it's settings (hierarchy selection conditions) will be automatically transferred to the existing DTPs extracting from the data source. If you have more than one InfoPackage with different settings (hierarchy selection conditions), than you can proceed as follows:</p>\n<ul>\n<li>In any case please implement SAP Note '3254923 - P26:DTP:TT:Hierarchy from Ipak Dataflow' as a prerequisite for the next steps.<br/><br/></li>\n<li>in the case of an inplace transfer: please make sure that the latest version of note 3044301 is applied in the system. Thus the report <strong>RSB4H_CONV_HIER_ISIPS_TO_DTP</strong> will be present in your system. Please execute the report <strong>before </strong>you include the data source in the scope of a transfer tool. When executing the report, you will have to specify the data source name. Start by executing the report with the simulation flag on. It will list all the InfoPackages with their hierarchy selection conditions, and also the existing DTPs extracting from the (PSA) of the data source, together with the name of the InfoObjects for which the hierarchies are loaded. If you switch the simulation flag off, you have the possibility to create DTPs replacing the ISIPs, namely, per ISIP, one for each of the InfoObject(Hierarchies) already connected to the data source by older DTPs. You can also choose to create DTPs replacing the ISIPs, having as target the hierarchies of other InfoObjects, provided that a transformation from the data source, to the said hierarchy is previously created.</li>\n</ul>\n<p><span>The report offers you the possibility to navigate per double click, in the ISIP and DTP maintenance, and from there in the Process Chain maintenance. Please don't forget to adjust the process chains. DO NOT DELTE the ISIPs. Afterwards you can start the transfer tool run on the data source. Then the DTPs produced with the report will be converted only on the data source side, if necessary. </span></p>\n<ul>\n<li>in the case of a remote or shell transfer: please make sure that the latest version of note 3044301 is applied in the target system.  Please execute the report <strong>after </strong>the data source, transformations and InfoObjects were imported in the target system. The parameter needed in this case, is the data source and the logical system as they were created in the target system. The function of the report is the same, the only difference is that no display of the InfoPackage is directly possible (you can do it manually, in the sender system).</li>\n</ul>\n<p>The following InfoPackages cannot be handled by the Transfer Tool:</p>\n<ul>\n<li>InfoPackages for 3.x DataSources<br/>Migrate the 3.x DataSources and InfoPackage to 7.x DataSources (using transaction RSMIGRATE)</li>\n<li>InfoPackages for Read-time Data Acquisition<br/>See SAP Note <a href=\"/notes/2447916\" target=\"_blank\">2447916</a> BW4SL - Real-Time Data Acquisition (RDA)</li>\n<li>Push InfoPackage for Web Service Source Systems <br/>See SAP Note <a href=\"/notes/2441826\" target=\"_blank\">2441826</a> BW4SL - Web Service Source Systems</li>\n<li>Multiple Delta InfoPackages for File Source Systems which read different files for same DataSource (or have different routines to determine the filename)</li>\n</ul>\n<p>If a process variant for the deletion of old requests from PSAs is used, please note that there are restrictions on the corresponding process variant for the deletion of old requests from DataStore objects (advanced). See SAP Note <a href=\"/notes/2448086\" target=\"_blank\">2448086</a> for details.</p>\n<p><strong>Related Information</strong></p>\n<p>See SAP Note <a href=\"/notes/2464541\" target=\"_blank\">2464541</a> - BW4SL - Data Transfer Process / Error Stack</p>\n<p>See the documentation for more information:</p>\n<p><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.5.7/en-US/4403a25c0bbe025ce10000000a1553f7.html\" target=\"_blank\">InfoPackage --&gt; Data Transfer Process</a></p>\n<p><a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.5.7/en-US/14e01017-1047-488a-ad85-18d1e5bc5cfe.html\" target=\"_blank\">Persistent Staging Area (PSA)</a></p>\n<p><a href=\"https://help.sap.com/viewer/2e90b26cf7484203a523bf0f4b1bc137/7.5.7/en-US/4a2c746ac16d47dbe10000000a42189c.html\" target=\"_blank\">InfoPackage</a></p>\n<p><a href=\"https://uacp2.hana.ondemand.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/49996aca00002774e10000000a42189b.html\" target=\"_blank\">Data Transfer Process</a> in SAP BW/4HANA</p>\n<p><a href=\"https://uacp2.hana.ondemand.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/aefee5c9097e4b1480ef3d0df80dc336.html\" target=\"_blank\">Working with Source Systems</a> in SAP BW/4HANA</p>", "noteVersion": 12}]}, {"note": "2447916", "noteTitle": "2447916 - BW4SL - Real-Time Data Acquisition (RDA)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Real-Time Data Acquisition (RDA) is not available in SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$RDA, RDAC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are supported or can be converted to SAP BW/4HANA.</p>\n<p>See node: Manual Redesign --&gt; BW Object Type</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In BW/4HANA, Real-time Data Acquisition is not available, but instead streaming process chains have to be used. Currently, there is no automatic transfer available yet, which replaces RDA settings with streaming process chains, so please proceed manually as follows. During the procedure, you will not be able to load data in RDA mode.</p>\n<p>The first step is to remove and disable RDA settings in the original BW and replace them with standard BW loading. To do this, proceed as follows: The first step is to remove and disable RDA settings in the original BW and replace them with standard BW loading. To do this, proceed as follows:</p>\n<ul>\n<li>RDA using Service API (S-API)</li>\n<ul>\n<li>Unassign the RDA daemon in the RDA monitor.</li>\n<li>Close the open RDA requests in the RDA monitor if needed.</li>\n<li>Delete all processes referring to the RDA InfoPackage. This can be processes for closing RDA requests (RDA_CLOSE), for stopping RDA load processes (RDA_RESET), or for starting RDA load processes (RDA_START). There is no direct replacement for these process types in SAP BW/4HANA as they are considered obsolete when streaming data.</li>\n<li>Create a new process chain to replace the RDA process logic which is defined by (a) the RDA InfoPackage, (b) the DTPs assigned to the corresponding DataSource in the RDA monitor, and (c) the subsequent process chains assigned to these DTPs in the RDA monitor.</li>\n<li>Create a standard delta InfoPackage to replace the RDA InfoPackage (a) and add it to the process chain.</li>\n<li>Switch all assigned DTPs (b) to standard mode and add them to the process chain after the InfoPackage.</li>\n<li>Create processes for activating data in the InfoProvider or InfoObject if applicable and add them to the process chain after the corresponding DTP. </li>\n<li>Add the assigned subsequent process chains (c) to the process chain after the corresponding data-activation process or DTP.</li>\n<li>Delete the RDA InfoPackage.</li>\n</ul>\n<li>RDA using Web Service Push</li>\n<ul>\n<li>Refer to SAP Note <a href=\"/notes/2441826\" target=\"_blank\">2441826</a> on how to deal with source system for Web Service Push.</li>\n</ul>\n<li>RDA using ODP Data Replication API</li>\n<ul>\n<li>Unassign the RDA daemon in the RDA monitor.</li>\n<li>Close the open RDA requests in the RDA monitor if needed.</li>\n<li>Delete all processes referring to DTPs for RDA. This can be processes for closing RDA requests (RDA_CLOSE), for stopping RDA load processes (RDA_RESET), or for starting RDA load processes (RDA_START). There is no direct replacement for these process types in BW/4HANA as they are considered obsolete when streaming data.</li>\n<li>Create a new process chain to replace the RDA process logic which is defined by (d) the DTP for RDA, and (e) the subsequent process chains assigned to this DTP in the RDA monitor.</li>\n<li>Switch the DTP for RDA (d) to standard mode and add it to the process chain.</li>\n<li>Create a process for activating data in the InfoProvider or InfoObject if applicable and add it to the process chain.</li>\n<li>Add the assigned subsequent process chains (e) to the process chain after the data-activation process or DTP.</li>\n</ul>\n</ul>\n<p>You now have a standard process chain which loads delta data. You can use this process chain, but you should not load it with high frequency. To transfer it to streaming, please do the following:</p>\n<p>Run the transfer tool either in In-Place or Remote scenario for your process chain. This will transfer your InfoPackage to DTP (if required) and the SAPI DataSources to ODP DataSources. The resulting chain then can be switched to streaming mode and scheduled accordingly.</p>\n<p><strong>Related Information</strong></p>\n<p>Refer to <a href=\"https://help.sap.com/viewer/2e90b26cf7484203a523bf0f4b1bc137/7.5.6/en-US/ac029de05e164a12ac1ce08d16180f05.html\" target=\"_blank\">Process Chains for Streaming</a> for more details.</p>", "noteVersion": 4}, {"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}]}