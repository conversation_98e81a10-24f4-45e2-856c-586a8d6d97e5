{"guid": "901B0E6C72791ED78EE9792FA8B740CC", "sitemId": "SI04: CPD_GANTT_CPI", "sitemTitle": "S4TWL - Launchpads of Gantt Chart and Commercial Project Inception", "note": 2318728, "noteTitle": "2318728 - S4TWL – Launchpad of Gantt Chart and Commercial Project Inception", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP Commercial Project Management for SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Commercial Project Management, Gantt Chart, launchpad, launch, CA-CPD, S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The Gantt chart launch option is dependent on the business function switche OPS_PS_HLP_2 which is not available in SAP S/4HANA, on-premise edition.</p>\n<p>The Commercial Project Inception launch option is dependent on the business function switche OPS_PS_HLP_2 which is not available in Initial SAP S/4HANA, on-premise edition.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With <em>SAP Commercial Project Management for SAP S/4HANA</em>, the launchpads of the following functions are not available in SAP S/4HANA, on-premise edition:</p>\n<ul>\n<li>Gantt Chart (to launch master project structures of PS projects in Gantt charts)</li>\n<li>Commercial Project Inception</li>\n</ul>\n<p><strong>Required and Recommended Actions</strong></p>\n<ul>\n<li>Inform key users and end users.</li>\n</ul>", "noteVersion": 6, "refer_note": [{"note": "2267333", "noteTitle": "2267333 - S4TWL - Selected PS Business Function Capabilities", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Compatibility Scope, System Conversion, Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Selected PS Business Function capabilities are part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, selected PS Business Function capabilities can be found under the ID 468.</p>\n<p>As of SAP S/4HANA 1511 - FPS1 the following business functions are switchable but obsolete:</p>\n<ul>\n<li>OPS_PS_CI_3</li>\n<li>LOG_XPD_EXT_1 </li>\n</ul>\n<p>The following PS business functions are part of the compatibilty scope</p>\n<ul>\n<li>OPS_PS_CI_1</li>\n<li>OPS_PS_CI_2</li>\n</ul>\n<p> The following transactions are hence categorized not to be the target architecture:</p>\n<ul>\n<li>CN08CI Config. of Commercial Proj Inception</li>\n<li>CNACLD PS ACL Deletion Program transaction</li>\n<li>CNSKFDEF SKF defaults for project elements</li>\n</ul>\n<p> </p>\n<p><strong><em>Basic Information related to Business Functions</em></strong></p>\n<p><em>If a business function was switched “ON” in the Business Suite start release system, but defined as “always_off” in the SAP S/4HANA, on-premise edition target release, then a system conversion is not possible with this release. See SAP Note 2240359 - SAP S/4HANA, on-premise edition 1511: Always-Off Business Functions. If a business function is defined as \"customer_switchable\" in the target release (SAP S/4HANA, on-premise edition 1511 – FPS1), then the business function status remains unchanged from the status on start release. Business Functions defined as “obsolete” cannot be switched “ON”</em></p>\n<p><strong>Business Process related information</strong></p>\n<p>Customer having one or all of these business functions switched “ON” in Business Suite start release can execute the system conversion. Customers having none of these business functions switched “ON” in Business Suite start release cannot activate the obsolete business functions after the system conversion to SAP S/4 HANA 1511.</p>\n<p>The Business Function LOG_XPD_EXT_1 was flagged as obsolete as of SAP S/4HANA 1511 - FPS1, the flag got removed with SAP S/4HANA 2022 FPS2. The functionality covered in the business function has been modernized along with Progress Tracking in SAP S/4HANA 2022. With this modernization, the functionality becomes part of the perpetual scope of SAP S/4HANA.</p>\n<p>The Business Functions OPS_PS_CI_1 and OPS_PS_CI_2 are available and selectable with SAP S/4HANA 1511.</p>\n<p><span> </span></p>\n<p><strong>Modernized topics</strong></p>\n<p><strong>Project Builder enhancements</strong></p>\n<p>Project Builder enhancements consist of</p>\n<ul>\n<li>Display of archived projects (OPS_PS_CI_1)</li>\n<li>Intermediate save (OPS_PS_CI_1)</li>\n<li>Display of more than 5 projects in work list (OPS_PS_CI_1)</li>\n<li>Performance Improvements in the Project Builder and Network Transactions (OPS_PS_CI_2)</li>\n<li>Collective purchase requisitions (OPS_PS_CI_1)</li>\n<li>Multilanguage short texts (OPS_PS_CI_1)</li>\n</ul>\n<div><span>The topic of Project Builder enhancements has been modernized by</span></div>\n<div>\n<ul>\n<li><span>Display of archived multilanguage short texts in Project Builder (SAP S/4HANA 2022 FPS0)</span></li>\n<li><span>Support of multilanguage short texts in Fiori apps (SAP S/4HANA 2021 FPS2)</span></li>\n<li><span>Support of a new processing layer for creating purchase requisitions from network activities (SAP S/4HANA 2022 FPS1)</span></li>\n</ul>\n<span>All of the above features are carved out of the given business function as of SAP S/4HANA 2022 FPS1 and with the modernization are part of the perpetual scope of SAP S/4HANA.</span></div>\n<p><strong>Project-Oriented Procurement (ProMan) enhancements</strong></p>\n<p>With SAP S/4HANA 2021 Project-Oriented Procurement is enhanced by support for multiple business partner adresses as part of the purchasing integration. The enhanced Project-Oriented Procurement is available as part of SAP S/4HANA perpetual scope. This includes also the enhancements to ProMan included in Business Function OPS_PS_CI_1. See also Note <a href=\"/notes/2267384\" target=\"_blank\">2267384</a>.</p>\n<p><span><strong>Progress Tracking and Progress Analysis enhancements</strong></span></p>\n<ul>\n<li>Progress Tracking enhancements</li>\n<li>Progress Analysis Workbench enhancements</li>\n</ul>\n<p><span>With SAP S/4HANA 2022 FPS0 Progress Tracking and Progress Analysis Workbench are enhanced by support of key-user extensibility. The enhanced functionality is available as part of SAP S/4HANA perpetual scope. This includes also the enhancements included in Business Function OPS_PS_CI_1 and LOG_XPD_EXT_1. See also Note <a href=\"/notes/2267190\" target=\"_blank\">2267190</a>. </span></p>\n<p><span><strong>Cost Calculation</strong></span></p>\n<ul>\n<li>Easy Cost Planning for Network Activities</li>\n<li>Business Add-Ins for Calculation in the Project System</li>\n</ul>\n<p><span>With SAP S/4HANA 2022 FPS0 Cost Calculation is modernized by support of ACDOCP. The enhanced cost calculation is available as part of SAP S/4HANA perpetual scope. This includes also the enhancements included in Business Function OPS_PS_CI_1. See also Note <a href=\"/notes/3033658\" target=\"_blank\">3033658</a>.</span></p>\n<p><strong>New extractors for BI content</strong></p>\n<ul>\n<li>0PROJECT_CUST_ATTR Customer Fields for Project Def. (ci_proj) </li>\n<li>0WBS_ELEMT_CUST_ATTR Customer Fields for WBS Element </li>\n<li>0NETWORK_CUST_ATTR Customer Fields for network </li>\n<li>0ACTIVITY_CUST_ATTR Customer Fields for Network Activity </li>\n<li>0ACTY_ELEMT_CUST_ATTR Customer Fields for Activity Element<br/>0WBS_ELEMT_USR_ATTR User Fields for WBS Element </li>\n<li>0ACTIVITY_USR_ATTR User Fields for Network Activity </li>\n<li>0ACTY_ELEMT_USR_ATTR User Fields for Activity Element </li>\n<li>0PS_CLM_ECP ECP Data for Claim </li>\n<li>0PS_WBS_ECP ECP for WBS-element </li>\n<li>0PS_NWA_ECP ECP for network activities </li>\n<li>0PS_NAE_ECP ECP for activity elements</li>\n<li>0PROJECT_TEXT Project Definition </li>\n<li>0WBS_ELEMT_TEXT Work Breakdown Structure Element </li>\n<li>0NETWORK_TEXT Network Number <br/>the above extractors are modernized by modernization of the related topic, i.e. </li>\n<ul>\n<li>Support of key-user extensibility for projects (SAP S/4HANA 2020 FPS2)</li>\n<li>Modernization of user-field display (SAP S/4HANA 2020 FPS0)</li>\n<li>Modernization of Easy Cost Planning (SAP S/4HANA 2022 FPS0)</li>\n<li>Modernization of multi-languge texts (SAP S/4HANA 2022 FPS0)</li>\n</ul>\n</ul>\n<p><span>with these modernizations, the extractors above are part of the perpetual scope of SAP S/4HANA.</span></p>\n<p><span><strong>OPS_PS_CI_1</strong>:</span></p>\n<ul>\n<li>Change Inherited Settlement Rules<br/>the topic of settlement rule is modernized by substitution and validation of WBS element settlement rule parameters (SAP S/4HANA 2022 FPS1). With the modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\n<li>Project System Material Component enhancements<br/>the topic of project system material components is modernized by support of a new processing layer for creating purchase requisitions from network activities (SAP S/4HANA 2022 FPS1) as well as by the modernization of the Open Catalog Interface (SAP S/4HANA 2022 FPS1). With these modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\n<li>Determine WBS Account Assignment at MRP Area Level<br/>the topic of WBS Account Assignment determination at MRP Area Level is modernized by the usage in enhanced Project Manufacturing Management and Optimization (PMMO) scenarios (SAP S/4HANA 2020). With these modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\n<li>Performance improvements through allocation of status combinations<br/>modernized by support of reading via status combination codes in the OData APIs for Project and Project Network. With this modernization in SAP S/4HANA 2023 the feature becomes part of the perpetual scope.</li>\n</ul>\n<p><strong>OPS_PS_CI_2</strong>:</p>\n<ul>\n<li>Display of Nonarchived and Archived Line Items<br/>the topic of line item reports is moderined by the new Fiori app Project Cost Line Items (SAP S/4HANA 2022 FPS1), see also note <a href=\"/notes/2267286\" target=\"_blank\">2267286</a>. With the modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\n<li>Option for the Down Payment Clearing of Billing Plans and Invoicing Plan<br/>modernized with SAP S/4HANA 2023 by the modernization of Project Cash Management, see also <a href=\"/notes/2267182\" target=\"_blank\">2267182</a>. With this modernization the feature becomes part of the perpetual scope of S/4HANA. </li>\n</ul>\n<p><span><strong>Topics that stay in compatibility scope</strong></span></p>\n<p>The following features can be used as part of the compatibility scope. There are no plans to modernize the functionality, hence usage rights expire with the expiry date of the compatibility scope.</p>\n<p><span>OPS_PS_CI_1:</span></p>\n<ul>\n<li><span>Forecast Workbench</span></li>\n<li><span>Access Control Lists</span></li>\n<li><span>Display of statistical key figures on an additional tab page</span></li>\n<li><span>New extractors for BI content</span></li>\n<ul>\n<li><span>Network: Statistical Key Figure</span></li>\n<li><span>Network Activity: Statistical Key Figure</span></li>\n<li><span>Netw. Act. Element: Statistical Key Figures</span></li>\n</ul>\n</ul>\n<p><span>OPS_PS_CI_3: </span></p>\n<p><span>The business function provides an optimization for Resource-Related Billing (RRB) for the source Actual costs - line items. As an alternative, consider using the optimization provided with SAP Note 2677564 or evaluate the usage of the new source Actuals Cost from Univ. Jrnl (Actuals Cost from Universal Journal) available as of SAP S/4HANA 2022.<em><br/></em></span></p>\n<p><span> </span></p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>For the modernized topics, upgrade to the relevant SAP S/4HANA release before expiry date of the compatibility scope.</p>", "noteVersion": 13}], "activities": [{"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "With SAP Commercial Project Management for SAP S/4HANA, the launchpads of the following functions are not available in SAP S/4HANA, on-premise edition: (1) Gantt Chart (to launch master project structures of PS projects in Gantt charts), (2) Commercial Project Inception. Please, inform key users and end users. See also SAP Note 2318728."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Inform about obsolete launchpads."}]}